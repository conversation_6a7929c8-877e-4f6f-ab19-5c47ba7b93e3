<template>
  <div class="PurchaseEightDisciplinesHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="detail"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {
    // BUTTON_SAVE,
    // BUTTON_PUBLISH,
    // BUTTON_SUBMIT,
    BUTTON_BACK
} from '@/utils/constant.js'

export default {
    name: 'ElsBarcodeRuleHeadDetail',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            requestData: {
                detail: {
                    url: '/base/barcode/elsBarcodeRuleHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {},
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            url: {
                submit: '/base/barcode/elsBarcodeRuleHead/edit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_barcodeRule_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        // 处理发布回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            const { elsBarcodeRuleItemList } = args.allData || {}
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!elsBarcodeRuleItemList || !elsBarcodeRuleItemList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cVHxOLV_c6f5290a`, '行信息不能为空！'))
                    reject(args)
                } else {
                    resolve(args)
                }
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            console.log('handleSaveAfter', args)
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        // 处理保存回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handleSubmitBefore (args) {
            return new Promise(resolve => {
                // 在这里处理逻辑
                args.allData.buttonType = '2'
                return resolve(args)
            })
        },
        goBack () {
            this.$parent.hideEditPage()
        },
        //新增行
        businessGridAdd ({ Vue, pageConfig, btn, groupCode }) {
            const { allData = {} } = this.getBusinessExtendData(this.businessRefName)
            console.log('groupCode', groupCode)
            let itemGrid = this.getItemGridRef(groupCode)
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)

            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {

                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            // if (allData.sampleNumber!='' || row.sampleNumber!=''){

            // }
            itemGrid.insertAt([row], -1)
        },
        //新增弹窗
        businessGridAddPopup ({ Vue, pageConfig, btn, groupCode }) {
            this.curGroupCode = groupCode
            this.fieldSelectType = 'material'
            let url = '/base/barcode/elsBarcodeAttribute/enableList'
            let columns = [
                { field: 'businessType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm132_busDocType`, '业务单据类型') },
                { field: 'businessField', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_EStFJO_fa24b7fe`, '业务单据字段') },
                { field: 'businessFieldName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_tFJORL_d790c17a`, '单据字段名称') },
                { field: 'attributeType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WcAc_2b753bb9`, '属性类型') }
            ]
            this.$refs.fieldSelectModal.open(url, {}, columns, 'multiple')
        },
        checkItemSelectOk (data) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            let itemGrid = this.getItemGridRef('elsBarcodeRuleItemList')
            data.forEach(item => {
                item['businessType'] = item['businessType_dictText']
                item['attributeType'] = item['attributeType_dictText']
                item['ruleNumber'] = allData['ruleNumber']
                item['id'] = null
            })
            itemGrid.insertAt(data, -1)
        }
    }
}
</script>