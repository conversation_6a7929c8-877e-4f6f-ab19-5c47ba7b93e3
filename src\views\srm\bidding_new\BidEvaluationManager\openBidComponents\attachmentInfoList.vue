<template>
  <div>
    <list-table
      v-if="saleAttachmentDTOList.length > 0"
      ref="saleAttachmentDTOList"
      :externalToolBar="externalToolBar"
      :statictableColumns="statictableColumns"
      :pageData="pageData"
      :pageStatus="pageStatus"
      setGridHeight="500"
      :fromSourceData="saleAttachmentDTOList"
      :showTablePage="false"
    >
    </list-table>
  </div>
</template>
<script>
import listTable from '@/views/srm/bidding_new/BiddingHall/components/listTable'
import {getAction} from '@/api/manage'
// import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    // mixins: [baseMixins],
    props: {
        // groupCode: {
        //     default: '',
        //     type: String
        // },
        // fromSourceData: {
        //     default () {
        //         return []
        //     },
        //     type: Array
        // },
        pageStatus: {
            default: 'detail',
            type: String 
        },
        clarifyRow: {
            default () {
                return {}
            },
            type: Object 
        }
    },
    // watch: {
    //     fromSourceData: {
    //         immediate: true,
    //         deep: true,
    //         handler (val){
    //             console.log(val)
    //             this.saleAttachmentDTOList = val
    //         }
    //     }
    // },
    computed: {
        statictableColumns () {
            let attachmentFileType = ''
            if (this.clarifyRow.checkType == '0') {
                attachmentFileType = 'preSaleDocumentSubmitFileType'
            } else {
                if (this.clarifyRow.processType == '1') {
                    attachmentFileType = 'resultSaleDocumentSubmitFileType '
                } else {
                    attachmentFileType = 'saleDocumentSubmitFileType'
                }
            }
            let columns = [
                { type: 'checkbox', width: 40, fixed: 'left' }, 
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                {
                    groupCode: 'saleAttachmentDTOList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                    fieldLabelI18nKey: '',
                    field: 'fileType',
                    fieldType: 'select',
                    dictCode: attachmentFileType,
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    enabled: false
                },
                {
                    groupCode: 'saleAttachmentDTOList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称'),
                    fieldLabelI18nKey: '',
                    field: 'fileName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'saleAttachmentDTOList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                    fieldLabelI18nKey: '',
                    field: 'uploadTime',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'saleAttachmentDTOList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人'),
                    fieldLabelI18nKey: '',
                    fieldType: 'number',
                    field: 'updateBy',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    enabled: false
                },
                {
                    fixed: 'right',
                    groupCode: 'saleAttachmentDTOList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    field: 'title',
                    width: 100,
                    slots: { default: 'grid_opration' }
                }
            ]
            return columns
        } 
        
    },
    components: {
        listTable
    },
    data () {
        return {
            saleAttachmentDTOList: [],
            externalToolBar: [
            ],
            pageData: {
                optColumnList: [
                    {
                        key: 'download',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                        clickFn: this.downloadEvent
                    },
                    {
                        key: 'preView',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        clickFn: this.preViewEvent
                    }
                ]
            }
        }
    },
    methods: {
        dealLabel (str) {
            // if (this.checkType !== null && this.processType !== null && !this.currentStep) this.getNodeParams()
            let prefix = ''
            switch (this.clarifyRow.checkType) {
            case '0':
                prefix = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UU_12d39d`, '预审')
                break
            case '1':
                switch (this.clarifyRow.processType) {
                case '0':
                    prefix = ''
                    break
                case '1':
                    switch (this.clarifyRow.currentStep) {
                    case '0':
                        prefix = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nIx_1d83d91`, '第一步')
                        break
                    case '1':
                        prefix = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nxx_1d84e85`, '第二步')
                        break
                    default:
                        prefix = ''
                    }
                    break
                default:
                    prefix = ''
                }
                break
            default:
                prefix = ''
            }
            return `${prefix}${str}`
        },
        preViewEvent (row) {
            row.subpackageId = this.clarifyRow.subpackageId
            let preViewFile = {}
            if (row && typeof row == 'string' ) {
                preViewFile = JSON.parse(row) || ''
            } else {
                preViewFile = row
            }
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (row, config) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.clarifyRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.$refs.saleAttachmentDTOList.loading=true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                this.$refs.saleAttachmentDTOList.loading=false
            })
        }
    },
    mounted () {
        console.log('文件this.fromSourceData', this.fromSourceData)
        console.log('文件this.saleAttachmentDTOList', this.saleAttachmentDTOList)
        // this.saleAttachmentDTOList = this.fromSourceData
        // if (['1', '2'].includes(this.fromSourceData.status) || this.pageStatus == 'detail') this.externalToolBar = []
    }
}
</script>
<style lang="less" scoped>

</style>