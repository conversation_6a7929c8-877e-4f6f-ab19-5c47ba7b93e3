<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      modelLayout="collapse"
      :page-data="pageData"
      :url="url" />
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
    v-drag    
      v-model="auditVisible"
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pprovalOpinion`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <remote-js
      :src="fileSrc"
      v-if="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {  getAction, httpAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
export default {
    name: 'EcnAuditDetail',
    components: {
        flowViewModal
    },
    mixins: [DetailMixin],
    data () {
        return {
            fileSrc: '',
            flowId: '',
            auditVisible: false,
            opinion: '',
            showRemote: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            currentRow: {},
            currentUrl: '',
            flowView: false,
            cancelAuditShow: false,
            currentBasePath: this.$variateConfig['domainURL'],
            previewContent: '',
            confirmLoading: false,
            previewModal: false,
            pageData: {
                form: {

                },
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_ItRH_40bee5ab`, '订单明细'), groupCode: 'orderDetails', type: 'grid', custom: {
                        ref: 'orderDetails',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierCode`, '供应商编码'), width: 120 },
                            { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 120 },
                            { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 120 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                            { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lineStatus`, '行状态'), width: 120 },
                            { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                            { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 120 },
                            { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 120 },
                            { field: 'receiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantity`, '收货数量'), width: 120 },
                            { field: 'onWayQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_onWayQuantity`, '在途数量'), width: 120 },
                            { field: 'notDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`, '未交货数量'), width: 120 },
                            { field: 'orderCreateBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreator`, '订单创建人'), width: 120 },
                            { field: 'orderCreateTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreatTime`, '订单创建时间'), width: 120 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_vendorPushList`, '供应商推送列表'), groupCode: 'ecnSupplierListList', type: 'grid', custom: {
                        ref: 'ecnSupplierListList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 120 },
                            { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 120 },
                            { field: 'viewStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_hasItBeenRead`, '是否已读'), width: 120 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_internalRemindeLlist`, '内部提醒列表'), groupCode: 'ecnBuyerListList', type: 'grid', custom: {
                        ref: 'ecnBuyerListList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 120 },
                            { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), width: 120 },
                            { field: 'department', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_department`, '部门'), width: 120 },
                            { field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), width: 200 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'attachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'), type: 'primary', click: this.auditPass, showCondition: this.showAuditBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'), type: '', click: this.auditReject, showCondition: this.showAuditBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'rollBack',  click: this.goBackAudit}
                ]
            },
            url: {
                detail: '/ecn/purchaseEcn/queryById'
            }
        }
    },
    computed: {
        // getFileSrc () 
    },
    created () {
        this.getFileSrc()
    },
    mounted () {
    },
    methods: {
        getFileSrc () {
            let elsAccount = this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            const param = {id: this.currentEditRow.businessId}
            getAction('/ecn/purchaseEcn/list', param).then(res => {
                const type = res.success ? 'success' : 'error'
                console.log(res)
                if(type==='success' && res.result && res.result.records){
                    console.log(res.result[0])
                    let templateNumber = res.result.records[0].templateNumber
                    let templateVersion = res.result.records[0].templateVersion
                    if(res.result.records[0].templateAccount){
                        elsAccount = res.result.records[0].templateAccount
                    }
                    this.fileSrc = `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_aduit_ecn_${templateNumber}_${templateVersion}.js?t=`+time
                }
            })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id, this.getItemNumberOptions)
            }
        },
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow (){
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        preViewEvent ( row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },

        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBackAudit()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {

            })
        }
    }
}
</script>