<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @loadSuccess="handleLoadSuccess"
    />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"
    />
  </div>
</template>
<script>
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'
import { httpAction, getAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
export default {
    mixins: [DetailMixin],
    components: {
        flowViewModal
    },
    data () {
        return {
            showRemote: false,
            flowView: false,
            flowId: '',
            currentBasePath: this.$variateConfig['domainURL'],
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineInformation`, '行信息'), groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                            ref: 'purchaseQualityCheckItemList',
                            columns: []
                        }
                    }
                    ,
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                            ref: 'purchaseAttachmentList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                // { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 320 },
                                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 140 },
                                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                                { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }

                            ]
                        }
                    }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocationApproval`, '审批撤销'), type: '', click: this.auditCancel, showCondition: this.auditCancelConditionBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow, showCondition: this.showFlowConditionBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/quality/purchaseQualityCheckHead/queryById',
                cancel: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_qualityCheck_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, { id: this.$route.query.id }).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        } else {
            this.showRemote = true
        }
    },
    methods: {
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        handleLoadSuccess (res) {
            // this.currentEditRow = res.res.result
            this.showRemote = true
        },
        auditCancelConditionBtn () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let auditStatus = params.auditStatus
            if (auditStatus == '1') {
                return true
            } else {
                return false
            }
        },
        showFlowConditionBtn () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let auditStatus = params.auditStatus
            if (auditStatus == '1' || auditStatus == '2' || auditStatus == '3') {
                return true
            } else {
                return false
            }
        },
        showFlow () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            this.flowId = params.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditCancel () {
            let form = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let param = {}
            param['businessType'] = 'publishQualityCheck'
            param['businessId'] = form.id
            param['rootProcessInstanceId'] = form.flowId
            this.confirmLoading = true
            httpAction(this.url.cancel, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.cancelAuditCallBack(form)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    }
}
</script>