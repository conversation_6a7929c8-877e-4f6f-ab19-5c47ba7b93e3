<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :extraDetailConfig="extraDetailConfig"
      @activeKeyChange="activeKeyChange"
      :url="url"
      :reloadData="handleReloadData"
      :class="{'supplierInfo-cell':activeKey=='supplierInfo'}"
    />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
    <!-- <a-modal
        v-drag
          centered
          :width="960"
          :maskClosable="false"
          :visible="flowView"
          @ok="closeFlowView"
          @cancel="closeFlowView">
          <iframe
            style="width:100%;height:560px"
            title=""
            :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
            frameborder="0"></iframe>
        </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <field-select-modal ref="fieldSelectModal"/>
    <!-- 确认项 预制选项 -->
    <set-confirm-item-modal
      ref="setConfirmItemModal"
      view
    />
    <!-- 定价 -->
    <submit-priced ref="submitPriced"/>
    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="templateVisible"
      @ok="selectedDeliveryTemplate">
      <template slot="footer">
        <a-button
          key="back"
          @click="handleTemplateCancel">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedDeliveryTemplate">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"/>
    </a-modal>
    <a-modal
      v-drag
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果')"
      :visible="riskShow"
      @ok="confirm"
      @cancel="confirm"
    >
      <div
        v-for="(data, index) in riskList"
        :key="data">
        <p
          :key="index"
          v-if="data.type==='0'">{{ index + 1 }}、{{ data.sourceName }}
          {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}
          {{ $srmI18n(`${$getLangAccount()}#i18n_field_KCbVeMKdeyR_3727867`, '在股权穿透存在相同结果') }}：{{
            data.result
          }}</p>
        <p
          :key="index"
          v-if="data.type==='1'">{{ index + 1 }}、{{ data.sourceName }}
          {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{
            data.toName
          }}{{
            $srmI18n(`${$getLangAccount()}#i18n_field_KeslILMKdeyR_85d14c24`, '在最终受益人存在相同结果')
          }}：{{
            data.result
          }}</p>
        <p
          :key="index"
          v-if="data.type==='3'">{{ index + 1 }}、{{ data.sourceName }}
          {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{
            data.toName
          }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_KKVLMKdeyR_46ebba36`, '在实控人存在相同结果') }}：{{
            data.result
          }}</p>
        <p
          :key="index"
          v-if="data.type==='4'">{{ index + 1 }}、{{ data.sourceName }}
          {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{
            data.toName
          }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_KsuWWMKdeyR_17c9510e`, '在报价ip存在相同结果') }}：{{
            data.result
          }}</p>
        <p
          v-if="data.type === '5' || data.type === '6' || data.type === '7' || data.type === '8'">{{
                                                                                                  index + 1
                                                                                                }}、{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }}
          {{ data.toName }}{{
            $srmI18n(`${$getLangAccount()}#i18n_field_MKW_162724a`, '存在：')
          }}{{ data.result }}{{
            $srmI18n(`${$getLangAccount()}#i18n_field_RH_a59e8`, '关系')
          }}</p>
      </div>
    </a-modal>

     
    <a-modal
      v-drag
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_more`, '更多')"
      :visible="priceModelShow"
      @ok="priceModelShow=false"
      @cancel="priceModelShow=false"
    >
    
      <!-- 打包 -->
      <vxe-grid
        v-if="$refs.detailPage&&$refs.detailPage.form.ebiddingWay === '0'"
        ref="quotaGrid"
        :height="437+resizeHeight"
        show-overflow
        v-bind="quotaPackGridOptions"
      >
        <!-- v-on="quotaGridEvents" -->
        <template slot="empty">
          <a-empty />
        </template>
       
          
        <template #totalAmount_default="{ row, rowIndex }">
          <div>
            
            <span>{{ row.totalAmount }}</span>
          </div>
        </template>
        <template #bidNumber_default="{ row }">
          <div>
            
            <span>{{ row.bidNumber }}</span>
          </div>
        </template>
        <template #enterTaxCode_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.taxCode }}</span>
          </div>
        </template>
        <template #enterTaxRate_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.taxRate }}</span>
          </div>
        </template>
        <template #netTotalAmount_default="{ row, rowIndex }">
          <div>
            
            <span >{{ row.netTotalAmount }}</span>
          </div>
        </template>
      </vxe-grid>
      <!-- 逐条 -->
      <vxe-grid
        v-else
        ref="quotaGrid"
        height="437"
        show-overflow
        v-bind="quotaGridDeOptions"
      >
        <!-- v-on="quotaGridEvents" -->
        <template slot="empty">
          <a-empty />
        </template>
       
        <template #price_header="{ column }">
          <i class="vxe-icon--edit-outline"></i>
          <span>{{ column.title }}</span>
         
        </template>
          
        <template #price_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.price }}</span>
          </div>
        </template>
        <template #enterTaxCode_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.taxCode }}</span>
          </div>
        </template>
        <template #enterTaxRate_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.taxRate }}</span>
          </div>
        </template>
        <template #netPrice_default="{ row, rowIndex }">
          <div>
           
            <span >{{ row.netPrice }}</span>
          </div>
        </template>
        <template #bidNumber_default="{ row }">
          <div>
          
            <span >{{ row.bidNumber }}</span>
          </div>
        </template>
       
       
      </vxe-grid>

    </a-modal>  


  </div>
</template>
<script lang="jsx">
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {postAction, getAction, httpAction} from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import flowViewModal from '@comp/flowView/flowView'
import SetConfirmItemModal from './SetConfirmItemModal'
import SubmitPriced from '../components/SubmitPriced'
import {removeRepet} from '@/utils/util'
import {ajaxFindDictItems} from '@/api/api'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {quotaGridDeOptions, quotaPackGridDeOptions} from '../gridConfig/purchase/indexDutch'

export default {
    mixins: [DetailMixin],
    components: {
        flowViewModal,
        fieldSelectModal,
        SubmitPriced,
        SetConfirmItemModal
    },
    data () {
        return {
            businessType: '',
            submitLoading: false,
            templateVisible: false,
            nextOpt: true,
            currentRow: {},
            templateNumber: undefined,
            templateOpts: [],
            stageTypeData: [],
            riskShow: false,
            selectType: '',
            showRemote: false,
            flowView: false,
            flowId: '',
            currentBasePath: this.$variateConfig['domainURL'],
            quotaGridDeOptions,
            quotaPackGridDeOptions,
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseEbiddingItemList',
                            columns: [],
                            buttons: [{
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplementaryMaterialCode`, '补充物料编码'),
                                click: this.replenishMaterialNumber,
                                showCondition: this.showReplenishBtn,
                                authorityCode: 'ebidding#purchaseEbiddingHead:replenishMaterialNumber'
                            }]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'),
                        groupCode: 'supplierInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseEbiddingSupplierQuoteList',
                            expandColumnsMethod: this.expandColumnsMethod, //供应商管理-打包未税金额
                            columns: [
                                // { type: 'checkbox', width: 40 },
                                {
                                    type: 'seq',
                                    width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'),
                                    field: 'toElsAccount',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'),
                                    field: 'supplierCode',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                                    field: 'supplierName',
                                    width: 200
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'),
                                    field: 'needCoordination',
                                    width: 200,
                                    dictCode: 'srmSupplierCoordinationWay'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_binddingStatus`, '应标状态'),
                                    field: 'replyStatus_dictText',
                                    width: 100
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLd_1d59643`, '确认项'),
                        groupCode: 'confirmItem',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseEbiddingConfirmList',
                            columns: [
                                {type: 'checkbox', width: 40 },
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'),
                                    field: 'toElsAccount',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                                    field: 'supplierName',
                                    width: 200
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_binddingStatus`, '应标状态'),
                                    field: 'status_dictText',
                                    width: 100,
                                    dictCode: 'srmEbiddingConfirmStatus'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLPCMW_713efd4f`, '确认要点描述'),
                                    field: 'confirmDesc',
                                    width: 220
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'),
                                    field: 'must_dictText',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMAc_2973057e`, '填写类型'),
                                    field: 'writeType_dictText',
                                    width: 120,
                                    dictCode: 'inspection_item_write_type'
                                },
                                {
                                    field: 'confirmItemList',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_URid_469b0f42`, '预制选项'),
                                    width: 120,
                                    slots: {
                                        default: ({row}) =>{
                                            if (row && ['0', '1'].includes(row.writeType)) {
                                                let label =this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notSet`, '未设置')
                                                if (row.confirmItemList && row.confirmItemList.length > 0) {
                                                    label = row.content
                                                }
                                                return [
                                                    <span>{label}</span>
                                                ]
                                            } else {
                                                return ''
                                            }
                                        }
                                    }
                                },
                                {
                                    field: 'purchaseRemark',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRemarks`, '采购备注')
                                },
                                {
                                    field: 'supplierRemark',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supRemark`, '供应商备注')
                                }
                            ],
                            buttons: [
                                {
                                    type: 'primary',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                                    click: this.itemConfirmEvent,
                                    showCondition: this.showConfirmBtn
                                },
                                {
                                    type: 'danger',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'),
                                    click: this.itemRefuseEvent,
                                    showCondition: this.showConfirmBtn
                                },
                                {
                                    type: 'warning',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_VVMB_43cf96b2`, '重新回复'),
                                    click: this.itemReplyAgainEvent,
                                    showCondition: this.showConfirmBtn
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'),
                        groupCode: 'fileDemandInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachmentDemandList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                                    width: 120
                                },
                                {
                                    field: 'stageType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'),
                                    width: 120
                                },
                                {
                                    field: 'required_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'),
                                    width: 120
                                },
                                {
                                    field: 'remark',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    width: 220
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachmentList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                                    width: 120
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 120
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'uploadSubAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                                    width: 120
                                },
                                {
                                    field: 'itemNumber',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                                    width: 120
                                },
                                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120 },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 140,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    clickFn: this.downloadEvent
                                },
                                {
                                    type: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preViewEvent
                                },
                                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent, showCondition: this.deleteFileConditionBtn }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBII_2ed15c51`, '授标意见'),
                        groupCode: 'awardOpinion',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAwardOpinionList',
                            columns: [
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'awardOpinion',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBII_2ed15c51`, '授标意见'),
                                    width: 150
                                },
                                {
                                    field: 'createTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                                    width: 150
                                },
                                {
                                    field: 'createBy',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'),
                                    width: 120
                                }
                            ],
                            showOptColumn: false
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJitH_4a08df61`, '围标探测记录'),
                        groupCode: 'probeResultList',
                        type: 'grid',
                        custom: {
                            ref: 'probeResultList',
                            columns: [
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'supplierName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                                    width: 120
                                },
                                {
                                    field: 'probeResult',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果'),
                                    width: 120
                                },
                                {
                                    field: 'createTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                                    width: 150
                                },
                                {
                                    field: 'createBy',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'),
                                    width: 120
                                }
                            ],
                            showOptColumn: false
                        }
                    }
                ],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_LBJi_2919d4bc`, '围标探测'),
                        type: 'primary',
                        click: this.checkRisk,
                        showCondition: this.checkRiskCondition,
                        icon: 'question-circle-o',
                        helpText: `1、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yVLIxDjWVOSmhyR_c7532315`, '接口为异步调用，请静候查询结果')}<br> 2、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lnICdjWRWefRdXWRxOfUWWu_f2d40ee9`, '受第三方应用限制，最大供应商数量不能大于20家')}`,
                        authorityCode: 'ebidding#purchaseEbiddingHead:probe'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocationApproval`, '审批撤销'),
                        type: '',
                        click: this.auditCancel,
                        showCondition: this.auditCancelConditionBtn
                    },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_bLne_36b82195`, '生成合同'), type: 'primary', click: this.generateContract, showCondition: this.generateContractlConditionBtn},
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                        type: '',
                        click: this.showFlow,
                        showCondition: this.showFlowConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                        type: 'primary',
                        click: this.submit,
                        showCondition: this.showSubmitCondition
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_DJIu_2e91e671`, '提交定价'),
                        type: 'primary',
                        click: this.showSubmitPrice,
                        showCondition: this.confirmEbiddingCondition,
                        authorityCode: 'ebidding#purchaseEbiddingHead:confirmBid'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_OufY_390dbcd7`, '竞价大厅'),
                        click: this.openLobby,
                        showCondition: this.showOpenLobby
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatPriceRecord`, '生成价格记录'),
                        click: this.generatePriceRecord,
                        showCondition: this.generatePriceRecordCondition,
                        authorityCode: 'ebidding#purchaseEbiddingHead:generatePriceRecord'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_releaseNewSupplier`, '发布新供应商'),
                        type: 'primary',
                        click: this.showNewSupplier,
                        showCondition: this.showNewSupplierBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QB_da2e6`, '流标'),
                        click: this.failedBidding,
                        showCondition: this.showFailedBiddingBtn,
                        authorityCode: 'ebidding#purchaseEbiddingHead:failed'
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            
            extraDetailConfig: {
                editConfig: {
                    trigger: 'click',
                    mode: 'cell'
                }
            },
            url: {
                detail: '/ebidding/purchaseEbiddingHead/queryById',
                confirm: '/ebidding/purchaseEbiddingHead/confirmBid',
                generateContract: '/ebidding/purchaseEbiddingHead/generateContract',
                submit: '/a1bpmn/audit/api/submit',
                cancel: '/a1bpmn/audit/api/cancel',
                generate: '/ebidding/purchaseEbiddingHead/generatePriceRecord',
                replenish: '/ebidding/purchaseEbiddingHead/replenishMaterialNumber',
                publishNewSupplier: '/ebidding/purchaseEbiddingHead/publishNewSupplier',
                itemConfirm: '/ebidding/PurchaseEbiddingConfirm/confirm',
                itemRefuse: '/ebidding/PurchaseEbiddingConfirm/refuse',
                itemReplyAgain: '/ebidding/PurchaseEbiddingConfirm/replyAgain',
                failed: '/ebidding/purchaseEbiddingHead/failed',
                probe: '/ebidding/purchaseEbiddingHead/probe'
            },
            riskList: [],
            activeKey: '',
            priceModelShow: false,
            priceData: []
        }
    },
    computed: {
        fileSrc () {
            this.getStageTypeData()
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_ebidding_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
        deleteFileConditionBtn (row) {
            let user = this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            // 当前登录用户账号、子账号为当前附件上传人，且 （(竞价结束，且结果审批 审批拒绝状态) 或 重新比价已悔标）可删除
            return user === row.uploadElsAccount && row.createBy === subAccount
                && ((this.currentEditRow.ebiddingStatus === '5' && this.currentEditRow.resultAuditStatus === '3') || this.currentEditRow.ebiddingStatus === '12')
        },
        deleteFilesEvent (row) {
            let user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            const fileGrid = this.$refs.detailPage.$refs.purchaseAttachmentList[0]
            //如果删除的数据有和登录人账号不一致的
            if(user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
                return
            }
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        activeKeyChange (val){
            this.activeKey=val
        },
        async getStageTypeData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEbiddingStageType'
            }
            const res = await ajaxFindDictItems(postData)
            if (res.success) {
                this.stageTypeData = res.result
            }
        },
        beforeHandleData (pageData){
            let _this=this
        
            for(let clm of pageData.itemColumns){
                                  
                if(clm.field=='price'){
                         
                    clm.slots ={
                        default: ({row}) => {  
                            let handleClick= function (row){
                                console.log(row)
                                _this.priceModelShow=true

                                if(_this.$refs.detailPage&&_this.$refs.detailPage.form.ebiddingWay=='0'){
                                    _this.quotaPackGridDeOptions.data=row.purchaseEbiddingItemHis
                                }else{
                                    _this.quotaGridDeOptions.data=row.purchaseEbiddingItemHis
                                }
                                
                            }
                            let isMore=row.purchaseEbiddingItemHis&&row.purchaseEbiddingItemHis.length>0?<span onClick={handleClick.bind(_this, row)} style="color:#1890ff;float:right">更多</span>:''
                            let  El = (<div><span>{row.price}</span>{isMore}</div>)
                               
                            return   El
                              
                            
                        }
                    }
               
             
                       
                }

                // break    
            }
            
        },
        afterHandleData (pageData) {
            if (this.currentEditRow?.detectionRequire != '1') {
                pageData.groups = pageData.groups.filter(v=>v.groupCode !== 'probeResultList')
            }

            console.log('afterHandleData,form', pageData)     
            // let {
            //     ebiddingWay='0',
            //     allowModifyQuantity='0',
            //     ebiddingMethod='0',
            //     quotaWay='0',
            //     ebiddingStatus=0
            // } = this.$refs.detailPage && this.$refs.detailPage.form || {}
            // const isAllowModifyQuantity=ebiddingWay=='1'&&allowModifyQuantity=='1'&&quotaWay=='1'&&ebiddingMethod=='2'&&ebiddingStatus=='5'
            // if(isAllowModifyQuantity){
                
            //     for(let group of pageData.groups){
            //         if(group.groupCode=='itemInfo'){
            //             for(let clm of group.custom.formFields){
                            
            //                 if(clm.fieldName=='price'){
                             
            //                     clm.slot={
            //                         default: ({row}) => {  
            //                             let  El = (<div><span>{row.price}</span> <span>更多</span></div>)
                               
            //                             return [
            //                                 El
            //                             ]
            //                         }
            //                     }
            //                     break
            //                 }

            //             }

            //             break
            //         }
            //     }

            // }
            
        },
        handleReloadData (res) {
            res.result.ebiddingAmount = res.result.ebiddingAmount ? res.result.ebiddingAmount.toFixed(2) : ''
            res.result.savingAmount = res.result.savingAmount ? res.result.savingAmount.toFixed(2) : ''
            res.result.savingRate = res.result.savingRate ? res.result.savingRate.toFixed(2) : ''
            if (this.stageTypeData && this.stageTypeData.length > 0) {
                res.result.purchaseAttachmentDemandList = res.result.purchaseAttachmentDemandList.map(i => {
                    i.stageType_dictText = this.stageTypeData.find(item => item.value === i.stageType).text
                    return i
                })
            }
            return res
        },
        generateContract () {
            this.nextOpt = true
            this.serachTemplate('contract')
        },
        serachTemplate (businessType) {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            if (this.nextOpt){
                this.pageData.businessType = businessType
                this.openModal(params.elsAccount)
            }
        },
        queryTemplateList (elsAccount) {
            let params = {
                pageSize: 100,
                elsAccount: elsAccount,
                templateStatus: '1',
                businessType: this.pageData.businessType,
                pageNo: 1
            }
            return getAction('/template/templateHead/getListByType', params)
        },
        openModal (elsAccount) {
            this.queryTemplateList(elsAccount).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.templateNumber,
                                title: item.templateName,
                                version: item.templateVersion,
                                account: item.elsAccount
                            }
                        })
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length===1) {
                            this.templateNumber = this.templateOpts[0].value
                            this.selectedDeliveryTemplate()
                        } else {
                            // 有多个模板先选择在新建
                            this.templateVisible = true
                        }
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        generateContractlConditionBtn (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let ebiddingStatus = params.ebiddingStatus
            let generateContract = params.generateContract
            return ebiddingStatus == '6'&& generateContract=='0'
        },
        handleTemplateCancel () {
            this.templateVisible = false
        },
        selectedDeliveryTemplate () {
            if(this.templateNumber) {
                const that = this
                let param = this.$refs.detailPage && this.$refs.detailPage.form || {}
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version,
                    templateAccount: template[0].account,
                    id: param.id
                }
                that.templateVisible = false
                that.submitLoading = false
                if (this.url.generateContract==''){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
                    return
                }
                that.postUpdateData(this.url.generateContract, params)
            }
        },
        postUpdateData (url, row){
            this.$refs.detailPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    if (res.result && res.result?.length == 1) { // 直接跳转到范式合同管理列表页面
                        const query = {
                            source: 'demand-pool',
                            result: res.result
                        }
                        this.$router.push({ path: '/srm/contract/purchase/PurchaseContractHeadList', query})
                    } else {
                        this.$info({
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                            content: res.message,
                            onOk: () =>{
                            // 更新vuex 当前行数据
                                this.refresh()
                            }
                        })
                    }
                } else { 
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        },
        refresh () {
            this.init()
        },
        confirm () {
            this.riskShow = false
        },
        // 围标检测
        checkRisk () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let headId = params.id
            this.confirmLoading = true
            getAction(this.url.probe, {headId: headId})
                .then(res => {
                    if (res && res.success) {
                        if (res.result && res.result.length) {
                            this.riskList = res.result
                            this.riskShow = true
                        } else {
                            this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IiRCISReWF_3b256afc`, '检测公司间无共同数据'))
                        }
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        failedBidding () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let headId = params.id
            const that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLQBW_ec0087ec`, '是否确认流标?'),
                onOk: () => {
                    that.$refs.detailPage.showLoading()
                    getAction(that.url.failed, {headId: headId}).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.init()
                        } else {
                            that.$message.warning(res.message)
                        }
                        that.$refs.detailPage.hideLoading()
                    })
                }
            })
        },
        // 待应标、竞价结束 可流标操作
        showFailedBiddingBtn () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let {ebiddingStatus} = params
            return (ebiddingStatus === '1' || ebiddingStatus === '5') && this.$hasOptAuth('ebidding#purchaseEbiddingHead:failed')
        },
        showReplenishBtn () {
            return this.$hasOptAuth('ebidding#purchaseEbiddingHead:replenishMaterialNumber')
        },
        showConfirmBtn () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let {ebiddingStatus} = params
            return ebiddingStatus === '1'
        },
        // 提交定价
        showSubmitPrice () {
            const _this = this
            const params = this.$refs.detailPage && this.$refs.detailPage.form
            let content=this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_DJIu_2e91e671`, '提交定价')
            //ebiddingWay (0: 打包, 1: 逐条, 2: 批量)
            //quotaWay  0: 百分比, 1: 数值
            if(params.ebiddingWay=='0'||params.ebiddingWay=='2'){
                let ebiddingWayObj={
                    '0': this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_fs_c3b12`, '打包'),
                    '2': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zR_c7e76`, '批量') 
                }
                let quotaWayObj={
                    '0': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_percentage`, '百分比'),
                    '1': this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_WR_c98cc`, '数值') 
                }
                content=`${ebiddingWayObj[params.ebiddingWay]}${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_xRuvz_7b5f46bf`, '不支持拆分')}${quotaWayObj[params.quotaWay]}`
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content,
                onOk () {
                    _this.$refs.submitPriced.open(_this.currentEditRow.id, params)
                }
            })
        },
        // 确认项 - 确认
        itemConfirmEvent () {
            let records = this.$refs.detailPage.$refs.purchaseEbiddingConfirmList[0].getCheckboxRecords() || []
            if (records.length === 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectLeastOperate`, '请至少选择一行进行操作！'))
                return
            }
            const that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isSure`, '是否确认?'),
                onOk: () => {
                    that.$refs.detailPage.showLoading()
                    let params = JSON.parse(JSON.stringify(records))
                    // params = params.map(i => {
                    //     i.content = i.writeType === '1' ? (i.content ? i.content.split(',') : '') : (i.content || '')
                    //     return i
                    // })
                    postAction(that.url.itemConfirm, params).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.init()
                        } else {
                            that.$message.warning(res.message)
                        }
                        that.$refs.detailPage.hideLoading()
                    })
                }
            })
        },
        // 确认项 - 拒绝
        itemRefuseEvent () {
            let records = this.$refs.detailPage.$refs.purchaseEbiddingConfirmList[0].getCheckboxRecords() || []
            if (records.length === 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectLeastOperate`, '请至少选择一行进行操作！'))
                return
            }
            const that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isRefer`, '是否拒绝？'),
                onOk: () => {
                    that.$refs.detailPage.showLoading()
                    let params = JSON.parse(JSON.stringify(records))
                    // params = params.map(i => {
                    //     i.content = i.writeType === '1' ? (i.content ? i.content.split(',') : '') : (i.content || '')
                    //     return i
                    // })
                    postAction(that.url.itemRefuse, params).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.init()
                        } else {
                            that.$message.warning(res.message)
                        }
                        that.$refs.detailPage.hideLoading()
                    })
                }
            })
        },
        // 确认项 - 重新回复
        itemReplyAgainEvent () {
            let records = this.$refs.detailPage.$refs.purchaseEbiddingConfirmList[0].getCheckboxRecords() || []
            if (records.length === 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectLeastOperate`, '请至少选择一行进行操作！'))
                return
            }
            const that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQVVMBW_3ff05a56`, '是否重新回复？'),
                onOk: () => {
                    that.$refs.detailPage.showLoading()
                    let params = JSON.parse(JSON.stringify(records))
                    // params = params.map(i => {
                    //     i.content = i.writeType === '1' ? (i.content ? i.content.split(',') : '') : (i.content || '')
                    //     return i
                    // })
                    postAction(that.url.itemReplyAgain, params).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.init()
                        } else {
                            that.$message.warning(res.message)
                        }
                        that.$refs.detailPage.hideLoading()
                    })
                }
            })
        },
        setConfrimItem (row) {
            this.currentRow = row
            this.$refs.setConfirmItemModal.open(row)
        },
        showNewSupplierBtn () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let {ebiddingStatus, publishNewSupplier} = params
            return (ebiddingStatus == '1' || ebiddingStatus == '3') && publishNewSupplier === '1'
        },
        showNewSupplier () {
            this.selectType = 'supplier'
            let url = '/supplier/supplierMaster/list'
            let columns = [
                {
                    field: 'toElsAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                    width: 150
                },
                {
                    field: 'supplierCode',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
                    width: 150
                },
                {
                    field: 'supplierName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                    width: 200
                },
                {
                    field: 'supplierStatus_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'),
                    width: 200
                },
                {
                    field: 'cateName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'),
                    width: 200
                },
                {
                    field: 'needCoordination_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'),
                    width: 200
                }
            ]
            // 获取供应商范围参数
            const form = this.$refs.detailPage && this.$refs.detailPage.form || {}
            const {supplierScope = ''} = form
            this.$refs.fieldSelectModal.open(url, {
                supplierStatus: supplierScope,
                frozenFunctionValue: '2'
            }, columns, 'multiple')
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        },
        replenishMaterialNumber () {
            this.selectType = 'material'
            let records = this.$refs.detailPage.$refs.purchaseEbiddingItemList[0].getCheckboxRecords() || []
            if (records.length !== 1) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectARowDataOperate`, '请选择一条行数据进行操作'))
                return
            }
            let row = JSON.parse(JSON.stringify(records[0]))
            if (row.materialNumber) {
                this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_material`, '物料')}：` + row.materialDesc + `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_codeAlreadyExistsNoNeedSupplement`, '已存在编码，无需补充！')}`)
                return
            }
            let columns = [
                {
                    field: 'materialNumber',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                    width: 150
                },
                {
                    field: 'materialName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
                    width: 150
                },
                {
                    field: 'materialDesc',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                    width: 200
                },
                {
                    field: 'brand',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialBrand`, '物料品牌'),
                    width: 200
                },
                {
                    field: 'purchaseCycle',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'),
                    width: 200
                },
                {
                    field: 'purchaseType_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseType`, '采购类型'),
                    width: 200
                },
                {
                    field: 'checkWay_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkWay`, '检验方式'),
                    width: 200
                },
                {
                    field: 'purchaseUnit',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingUnit`, '采购单位'),
                    width: 200
                },
                {
                    field: 'materialModel',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialModel`, '物料型号'),
                    width: 200
                },
                {
                    field: 'materialSpec',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),
                    width: 200
                }
            ]
            this.$refs.fieldSelectModal.open('/material/purchaseMaterialHead/list', {}, columns, 'single')
        },
        fieldSelectOk (data) {
            if (this.selectType === 'material') {
                let records = this.$refs.detailPage.$refs.purchaseEbiddingItemList[0].getCheckboxRecords()
                let row = JSON.parse(JSON.stringify(records[0]))
                const _this = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                    content: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, '是否确认将物料')}：` + row.materialDesc + `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_encodingUpdatedTo`, '的编码更新为')}：` + data[0].materialNumber,
                    onOk () {
                        _this.$refs.detailPage.showLoading()
                        row.materialNumber = data[0].materialNumber
                        row.materialDesc = data[0].materialDesc
                        row.materialGroup = data[0].materialGroup
                        row.materialGroupName = data[0].materialGroupName
                        row.materialModel = data[0].materialModel
                        row.materialSpec = data[0].materialSpec
                        postAction(_this.url.replenish, row).then((res) => {
                            if (res.success) {
                                _this.$message.success(res.message)
                                _this.init()
                            } else {
                                _this.$message.warning(res.message)
                            }
                        }).finally(() => {
                            _this.$refs.detailPage.hideLoading()
                        })
                    }
                })
            } else if (this.selectType === 'supplier') {
                let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
                params['purchaseEbiddingSupplierList'] = data
                this.$refs.detailPage.confirmLoading = true
                let url = this.url.publishNewSupplier
                postAction(url, params).then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.init()
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.$refs.detailPage.confirmLoading = false
                })
            }
        },
        openLobby () {
            // const {
            //     ebiddingStatus = '',
            //     ebiddingMethod = '0',
            //     ebiddingNumber,
            //     currentItemNumber
            // } = this.$refs.detailPage && this.$refs.detailPage.form || {}
            // if (ebiddingStatus === '0') {
            //     this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_BbddingDocumentsNewStatusAllowedEnteBiddingHall`, '新建状态竞价单不允许进入竞价大厅'))
            //     return
            // }
            // this.$store.dispatch('SetTabConfirm', false)
            // // 竞价方式 - ebiddingMethod 0 || null 英式，1 日式，2 荷式
            // if (ebiddingMethod === '0') {
            //     this.$router.push({
            //         path: '/ebidding/buyLobbyNew',
            //         query: {
            //             id: this.currentEditRow.id,
            //             ebiddingNumber,
            //             currentItemNumber
            //         }
            //     })
            // } else {
            //     this.$router.push({
            //         path: '/ebidding/buyLobbyNewJap',
            //         query: {
            //             id: this.currentEditRow.id,
            //             ebiddingNumber,
            //             currentItemNumber
            //         }
            //     })
            // }
            const {
                ebiddingMethod = '0', ebiddingWay,
                ebiddingNumber,
                currentItemNumber,
                id
            } = this.$refs.detailPage && this.$refs.detailPage.form || {}
            this.$store.dispatch('SetTabConfirm', false)
            // 竞价方式 - ebiddingMethod '0' || null 英式，'1' 日式，'2' 荷式
            if (ebiddingMethod === '0') {
                window.open(`${window.origin}/ebidding/buyLobbyNew?id=${id}&ebiddingNumber=${ebiddingNumber}&currentItemNumber=${currentItemNumber}`, '_blank')
            } else if (ebiddingMethod === '1' || ebiddingWay === '3') { // 英式 或 一次性
                window.open(`${window.origin}/ebidding/buyLobbyNewJap?id=${id}&ebiddingNumber=${ebiddingNumber}&currentItemNumber=${currentItemNumber}`, '_blank')
            } else if (ebiddingMethod === '2') {
                window.open(`${window.origin}/ebidding/buyLobbyNewDutch?id=${id}&ebiddingNumber=${ebiddingNumber}&currentItemNumber=${currentItemNumber}`, '_blank')
            }
        },
        // 供应商列表扩展-定价 / 供应商管理-打包未税金额
        expandColumnsMethod () {
            // 报价方式, prop: ebiddingWay, 0: 打包, 1: 逐条, 3 一次性
            // 拆分方式, prop: quotaWay, 0: 百分比, 1: 数值
            let {
                ebiddingWay = '0',
                purchaseEbiddingItemList = [],
                allowModifyQuantity='0',
                ebiddingMethod='0',
                quotaWay='0',
                quoteType='0',
                hasQuote='0'
            } = this.$refs.detailPage && this.$refs.detailPage.form || {}
            const isEbiddingWay1 = ebiddingWay === '1' || ebiddingWay === '3'
            const title =
                quotaWay === '0' ?
                    this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitRatio`, '拆分比例%') :
                    this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzWR_2e27225f`, '拆分数量')
            const wonTheBid = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_wonTheBid`, '已中标')
            const notWonTheBid = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notWonTheBid`, '未中标')
            const isAllowModifyQuantity = hasQuote=='1' && allowModifyQuantity=='1' && ebiddingMethod=='2' && (ebiddingWay == '1' || ebiddingWay == '3')
            purchaseEbiddingItemList = removeRepet(purchaseEbiddingItemList, 'itemNumber')

            if (isEbiddingWay1) {
                return purchaseEbiddingItemList.map((item, index) => {
                    return {
                        title: item.materialDesc, //物料名称
                        children: [
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_offer`, '报价'),
                                field: `price${index}`,
                                width: 120,
                                slots: {
                                    default: ({row}) => {
                                        const price = row.purchaseEbiddingItemList[index] && row.purchaseEbiddingItemList[index].price
                                        let  El = (<span>{price}</span>)

                                        if(isAllowModifyQuantity&&row.purchaseEbiddingItemHis){
                                            let spanEl=[]
                                            if(row.purchaseEbiddingItemHis&&row.purchaseEbiddingItemHis.length>0){
                                                for(let i of row.purchaseEbiddingItemHis){
                                                    if(item.materialNumber==i.materialNumber)
                                                        spanEl.push(<span style="display:block">{quoteType=='0'?i.price:i.netPrice}</span>)
                                                }
                                            }
                                            El=(spanEl)
                                        }
                                        return [
                                            El
                                        ]
                                    }
                                }
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractAward`, '授标'),
                                field: `itemStatus${index}`,
                                width: 120,
                                editRender: {
                                    enabled: true
                                },
                                slots: {
                                    default: ({row}) => {
                                        const itemStatus = row.purchaseEbiddingItemList[index] && row.purchaseEbiddingItemList[index].itemStatus
                                        let El = (<span>{itemStatus === '9' ? wonTheBid : notWonTheBid}</span>)
                                        console.log('***********', isAllowModifyQuantity)      
                                        if(isAllowModifyQuantity&&row.purchaseEbiddingItemHis){
                                            let spanEl=[]
                                            row.purchaseEbiddingItemHis.forEach((val)=>{
                                                
                                                if( val.itemStatus!='9'&&val.itemStatus!='8') val.itemStatus='9'
                                            })
                                            if(row.purchaseEbiddingItemHis&&row.purchaseEbiddingItemHis.length>0){
                                                for(let i of row.purchaseEbiddingItemHis){
                                                    if(item.materialNumber==i.materialNumber)
                                                        spanEl.push(<span style="display:block">{i.itemStatus === '9' ? wonTheBid : notWonTheBid}</span>)
                                                    else   spanEl.push(<span style="display:block">{ notWonTheBid}</span>)
                                                }  
                                            }
                                            El=(spanEl)
                                        }
                                        return [
                                            El
                                        ]
                                    },
                                    edit: (({row, column}) => {
                                        const disabled = !(row.purchaseEbiddingItemList[index]&&row.purchaseEbiddingItemList[index].price)
                                        const optsArr = [{value: '9', label: wonTheBid}, {
                                            value: '8',
                                            label: notWonTheBid
                                        }]
                                        const opts = optsArr.map(n => {
                                            return (<vxe-option value={n.value} label={n.label}></vxe-option>)
                                        })
                                        const props = {
                                            value: row[column.property],
                                            disabled
                                        }
                                        const on = {
                                            change: ({value}) => {
                                                row[`itemStatus${index}`] = value
                                                row.purchaseEbiddingItemList[index].itemStatus = value
                                            }
                                        }
                                   

                                        let  El = (
                                            <vxe-select {...{props, on: on}}>
                                                {opts}
                                            </vxe-select>
                                        )

                                        if(isAllowModifyQuantity&&row.purchaseEbiddingItemHis){
                                          
                                            let selectEl=[]
                                            if(row.purchaseEbiddingItemHis&&row.purchaseEbiddingItemHis.length>0){
                                                for(let i of row.purchaseEbiddingItemHis){
                                                    if(item.materialNumber==i.materialNumber){
                                                        let disabled=!(i.price)
                                                        let props={
                                                            value: i.itemStatus === '9' ? wonTheBid : notWonTheBid,
                                                            disabled
                                                        }
                                                        let on = {
                                                            change: ({value}) => {
                                                                i.itemStatus = value
                                                             
                                                            }
                                                        }
                                                        selectEl.push(<span style="display:block"> <vxe-select {...{props, on: on}}>
                                                            {opts}
                                                        </vxe-select></span>)
                                                    }
                                                }

                                            }


                                            El=selectEl
                                        }
                                        return [
                                            El
                                        ]
                                    })
                                }
                            },
                            {
                                title: title,
                                field: `quota${index}`,
                                width: 120,
                                editRender: {
                                    enabled: true,
                                    autofocus: '.custom-cell-input input'
                                },
                                slots: {
                                    default: ({row}) => {
                                        let quota = row.purchaseEbiddingItemList[index] && row.purchaseEbiddingItemList[index].quota
                                 
                                        let El = (<span>{quota}</span>)
                                        if(isAllowModifyQuantity&&row.purchaseEbiddingItemHis){
                                            let spanEl=[]
                                            if(row.purchaseEbiddingItemHis&&row.purchaseEbiddingItemHis.length>0){
                                                for(let i of row.purchaseEbiddingItemHis){
                                                    if(item.materialNumber==i.materialNumber)
                                                        spanEl.push(<span style="display:block">{i.bidNumber}</span>)
                                                }  
                                            }
                                            El=(spanEl)
                                        }

                                        return [
                                            El
                                        ]
                                    },
                                    edit: (({row, column}) => {
                                        let disabled = !(row.purchaseEbiddingItemList[index]&&row.purchaseEbiddingItemList[index].price)
                                        const props = {
                                            value: row[column.property],
                                            disabled
                                        }
                                        const on = {
                                            change: ({value}) => {
                                                row[`quota${index}`] = value
                                                row.purchaseEbiddingItemList[index].quota = value
                                                console.log(column.property)
                                                if (value && value != 0) {
                                                    row.purchaseEbiddingItemList[index].itemStatus = '9'
                                                    row[`itemStatus${index}`] = '9'
                                                } else {
                                                    row.purchaseEbiddingItemList[index].itemStatus = '8'
                                                    row[`itemStatus${index}`] = '8'
                                                }
                                            },
                                            blur: ({value}) => {
                                                row[`quota${index}`] = value
                                                row.purchaseEbiddingItemList[index].quota = value
                                                console.log(column.property)
                                                if (value && value != 0) {
                                                    row.purchaseEbiddingItemList[index].itemStatus = '9'
                                                    row[`itemStatus${index}`] = '9'
                                                } else {
                                                    row.purchaseEbiddingItemList[index].itemStatus = '8'
                                                    row[`itemStatus${index}`] = '8'
                                                }
                                            }
                                        }
                                        let El = (
                                            <vxe-input class="custom-cell-input" {...{props, on: on}} />
                                        )

                                        if(isAllowModifyQuantity&&row.purchaseEbiddingItemHis){
                                          
                                            let inputEl=[]
                                            if(row.purchaseEbiddingItemHis&&row.purchaseEbiddingItemHis.length>0){
                                                for(let i of row.purchaseEbiddingItemHis){
                                                    if(item.materialNumber==i.materialNumber){
                                                        let disabled=!(i.price)
                                                        let  props = {
                                                            value: i.bidNumber,
                                                            disabled
                                                        }
                                                        let on = {
                                                            change: ({value}) => {
                                                                i.bidNumber=value
                                                         
                                                                if (value && value != 0) {
                                                              
                                                                    i.itemStatus= '9'
                                                                } else {
                                                                   
                                                                    i.itemStatus = '8'
                                                                }
                                                            },
                                                            blur: ({value}) => {
                                                                i.bidNumber=value
                                                                if (value && value != 0) {
                                                                    i.itemStatus = '9'
                                                                } else {
                                                                    i.itemStatus = '8'
                                                                }
                                                            }
                                                        }
                                                        inputEl.push(<span style="display:block">  <vxe-input class="custom-cell-input" {...{props, on: on}} /> </span>)
                                                    }
                                                }

                                            }


                                            El=inputEl
                                        }

                                        return [
                                            El
                                        ]
                                    })
                                }
                            }
                        ]
                    }
                })
            } else {
                let Headtitle = ebiddingWay != '1' && ebiddingWay != '3' && quoteType == '0' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额') : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未税金额')
                return [
                    {
                        title: Headtitle,
                        children: [
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_offer`, '报价'),
                                field: 'totalAmount',
                                width: 120,
                                slots: {
                                    default: ({row}) => {
                                        const totalAmount = row.purchaseEbiddingItemList.length > 0 ? row.purchaseEbiddingItemList[0].totalAmount : ''
                                        const netTotalAmount = row.purchaseEbiddingItemList.length > 0 ? row.purchaseEbiddingItemList[0].netTotalAmount : ''
                                        const El = (<span>{ ebiddingWay != '1' && ebiddingWay != '3' && quoteType == '0' ? totalAmount : netTotalAmount}</span>)
                                        return [
                                            El
                                        ]
                                    }
                                }
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractAward`, '授标'),
                                field: 'itemStatus',
                                width: 120,
                                editRender: {
                                    enabled: true
                                },
                                slots: {
                                    default: ({row}) => {
                                        const itemStatus = row.purchaseEbiddingItemList.length > 0 ? row.purchaseEbiddingItemList[0].itemStatus : ''
                                        const El = (<span>{itemStatus === '9' ? wonTheBid : notWonTheBid}</span>)
                                        return [
                                            El
                                        ]
                                    },
                                    edit: (({row, column}) => {
                                        let disabled = !(row.purchaseEbiddingItemList[0].totalAmount)
                                        const optsArr = [{value: '9', label: wonTheBid}, {
                                            value: '8',
                                            label: notWonTheBid
                                        }]
                                        const opts = optsArr.map(n => {
                                            return (<vxe-option value={n.value} label={n.label}></vxe-option>)
                                        })
                                        const props = {
                                            value: row[column.property],
                                            disabled
                                        }
                                        const on = {
                                            change: ({value}) => {
                                                row.itemStatus = value
                                                row.purchaseEbiddingItemList[0].itemStatus = value
                                            }
                                        }
                                        const El = (
                                            <vxe-select {...{props, on: on}}>
                                                {opts}
                                            </vxe-select>
                                        )
                                        return [
                                            El
                                        ]
                                    })
                                }
                            },
                            {
                                title: title,
                                field: 'quota',
                                width: 120,
                                editRender: {
                                    enabled: true,
                                    autofocus: '.custom-cell-input input'
                                },
                                slots: {
                                    default: ({row}) => {
                                        let quota = row.purchaseEbiddingItemList.length > 0 ? row.purchaseEbiddingItemList[0].quota : ''
                                        const El = (<span>{quota}</span>)
                                        return [
                                            El
                                        ]
                                    },
                                    edit: (({row, column}) => {
                                        let disabled = !(row.purchaseEbiddingItemList[0].totalAmount)
                                        const props = {
                                            value: row[column.property],
                                            disabled
                                        }
                                        const on = {
                                            change: ({value}) => {
                                                row.quota = value
                                                row.purchaseEbiddingItemList[0].quota = value
                                                console.log(column.property)
                                                if (value && value != 0) {
                                                    row.purchaseEbiddingItemList[0].itemStatus = '9'
                                                    row.itemStatus = '9'
                                                } else {
                                                    row.purchaseEbiddingItemList[0].itemStatus = '8'
                                                    row.itemStatus = '8'
                                                }
                                            },
                                            blur: ({value}) => {
                                                row.quota = value
                                                row.purchaseEbiddingItemList[0].quota = value
                                                if (value && value != 0) {
                                                    row.purchaseEbiddingItemList[0].itemStatus = '9'
                                                    row.itemStatus = '9'
                                                } else {
                                                    row.purchaseEbiddingItemList[0].itemStatus = '8'
                                                    row.itemStatus = '8'
                                                }
                                            }
                                        }
                                        const El = (
                                            <vxe-input class="custom-cell-input" {...{props, on: on}} />
                                        )
                                        return [
                                            El
                                        ]
                                    })
                                }
                            }
                        ]
                    }
                ]
            }
        },
        auditCancelConditionBtn () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let auditStatus = params.auditStatus
            return auditStatus == '1'
        },
        showSubmitCondition () { // 提交审批按钮显隐判断
            // let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            // let auditStatus = params.auditStatus
            // return auditStatus =='1'
            let headData = this.$refs.detailPage && this.$refs.detailPage.form || {}
            const {resultAuditStatus, resultAudit} = headData
            if (resultAudit == '0') {
                return false
            }
            if (resultAuditStatus !== '0' && resultAuditStatus !== '3') {
                return false
            }
            let itemList = this.$refs.detailPage.$refs.purchaseEbiddingItemList[0].getTableData().fullData || []
            for (let i in itemList) {
                let itemStatus = itemList[i].itemStatus
                if (itemStatus !== '9' && itemStatus !== '8') {
                    return false
                }
            }
        },
        showOpenLobby () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            const { ebiddingStatus } = params
            return ebiddingStatus !=='0'
        },
        generatePriceRecordCondition () {
            return this.$hasOptAuth('ebidding#purchaseEbiddingHead:generatePriceRecord')
        },
        checkRiskCondition () {
            return this.$hasOptAuth('ebidding#purchaseEbiddingHead:probe')
        },
        // 定价按钮显隐判断
        confirmEbiddingCondition () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let ebiddingStatus = params.ebiddingStatus
            return (ebiddingStatus == '5' || ebiddingStatus == '12') && this.$hasOptAuth('ebidding#purchaseEbiddingHead:confirmBid')
        },
        showFlowConditionBtn () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let { auditStatus, resultAudit } = params
            return auditStatus == '1' || auditStatus == '2' || auditStatus == '3' || resultAudit == '1'
        },
        showFlow () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            this.flowId = params.resultFlowId || params.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditCancel () {
            let form = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let param = {}
            param['businessType'] = 'publishEbidding'
            param['businessId'] = form.id
            param['rootProcessInstanceId'] = form.flowId
            this.confirmLoading = true
            postAction(this.url.cancel, param).then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    if (res?.result?.auditStatus == '0') {
                        this.$parent.cancelAuditCallBack(form)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        //定价
        confirmEbidding () {
            const that = this
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLIuW_ebbb3b15`, '是否确认定价?'),
                onOk: () => {
                    that.$refs.detailPage.showLoading()
                    postAction(that.url.confirm, params).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                        } else {
                            that.$message.warning(res.message)
                        }
                        that.init()
                        that.$refs.detailPage.hideLoading()
                    })
                }
            })
        },
        submit () {
            let headData = this.$refs.detailPage && this.$refs.detailPage.form || {}
            const {resultAuditStatus, resultAudit} = headData
            if (resultAudit == '0') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_STDJUzW_6e48a495`, '无需提交审批！'))
                return
            }
            if (resultAuditStatus !== '0' && resultAuditStatus !== '3') {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APzExqDJUzW_9316aa0e`, '当前状态不可提交审批!'))
                return
            }
            let itemList = this.$refs.detailPage.$refs.purchaseEbiddingItemList[0].getTableData().fullData || []
            for (let i in itemList) {
                let itemStatus = itemList[i].itemStatus
                if (itemStatus !== '9' && itemStatus !== '8') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_djcdIIIujtFnqDJUz_2e8385d4`, '所有行项目已定价的单据才可提交审批'))
                    return
                }
            }
            const param = {
                businessId: headData.id,
                businessType: 'resultEbidding',
                auditSubject: `竞价结果审批，单号：${headData.ebiddingNumber}`,
                params: JSON.stringify(headData)
            }
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLDJUz_8f71e759`, '是否确认提交审批？'),
                onOk: () => {
                    that.$refs.detailPage.showLoading()
                    postAction(that.url.submit, param).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                        } else {
                            that.$message.warning(res.message)
                        }
                        that.$refs.detailPage.hideLoading()
                    })
                }
            })
        },
        generatePriceRecord () {
            const that = this
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            const {ebiddingStatus = ''} = params
            if (ebiddingStatus !== '6') {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RiTtkOutzELIlBntF_bae88cf`, '只允许操作竞价单状态为已授标的单据'))
                return
            }
            let headId = params.id
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatPriceInfoTips`, '是否确认生成价格信息记录？'),
                onOk: () => {
                    that.$refs.detailPage.showLoading()
                    getAction(that.url.generate, {headId: headId}).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                        } else {
                            that.$message.warning(res.message)
                        }
                        that.$refs.detailPage.hideLoading()
                    })
                }
            })
        }
    }
}
</script>


<style lang="scss" scoped>

.supplierInfo-cell{
   
  :deep(.vxe-cell){
    max-height: 3000px !important;
  }
}


</style>