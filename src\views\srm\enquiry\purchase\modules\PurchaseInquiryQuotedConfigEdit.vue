<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        pageStatus="edit"
        v-on="businessHandler"
      >
        <template #title-page>
          <h1 style="font-weight: 600;font-size: 20px;">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_LvumIrERAt_1a575aa8`, '成本价格模板配置-编辑') }}</h1>
        </template>
      </business-layout>
    </a-spin>

    <purchase-edit-cost
      ref="costform"
      :current-edit-row="costEditRow"
    />
    <view-ladder-price-modal ref="ladderPage"></view-ladder-price-modal>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {createPromise} from '@/utils/util.js'
import ViewLadderPriceModal from '../../modules/ViewLadderPriceModal'
import PurchaseEditCost from './PurchaseEditCost'
import { isArray, isObject, isFunction } from 'lodash'


export default {
    name: 'PurchaseInquiryQuotedConfigEdit',
    components: {
        BusinessLayout,
        ViewLadderPriceModal,
        PurchaseEditCost
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            costEditRow: {},
            requestData: {
                detail: {
                    url: '/enquiry/purchaseInquiryQuotedConfig/queryById', args: (that) => {
                        return {id: that.currentEditRow.id}
                    }
                }
            },

            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/enquiry/purchaseInquiryQuotedConfig/edit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save',
                    click: this.handleSave,
                    handleBefore: this.saveBefore,
                    showMessage: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                add: '/enquiry/purchaseInquiryQuotedConfig/add',
                edit: '/enquiry/purchaseInquiryQuotedConfig/edit',
                detail: '/enquiry/purchaseInquiryQuotedConfig/queryById'
            }
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
        },

        isJson (str) {
            if (typeof str == 'string') {
                try {
                    let obj = JSON.parse(str)
                    if (typeof obj == 'object' && obj) {
                        return true
                    }
                    return false
                } catch {
                    return false
                }
            }
        },

        saveBefore (args) {
            const that = this
            let fn = (resolve, reject) => {
                const {allData = {}} = args || {}
                if (!allData.configItem || allData.configItem === '') {
                    that.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, ' 配置项不能为空！'))
                    reject(args)
                    return
                }
                if (!allData.configValue || allData.configValue === '') {
                    that.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '配置项值编码不能为空！'))
                    reject(args)
                    return
                }
                if (!allData.quotePriceWay || allData.quotePriceWay === '') {
                    that.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '报价方式不能为空！'))
                    reject(args)
                    return
                }
                if (allData.quotePriceWay === '1' && (allData.ladderPriceJson === '' || allData.ladderPriceJson === undefined || allData.ladderPriceJson === null)) {
                    that.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '阶梯报价JSON不能为空！'))
                    reject(args)
                    return
                }
                if (allData.quotePriceWay === '2' && (allData.costFormJson === '' || allData.costFormJson === undefined || allData.costFormJson === null)) {
                    that.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '成本组成JSON不能为空！'))
                    reject(args)
                    return
                }
                resolve({
                    ...args,
                    allData: allData
                })
            }
            return createPromise(fn)
        },

        beforeHandleData (data) {
            data.itemColumns.forEach(item => {
                if (item.field == 'ladderPriceJson') {
                    item.slots = this.ladderSlots
                }
                if (item.field == 'costFormJson') {
                    item.slots = this.costSlots
                }
            })
        },

        // setLadder(row) {
        //     this.$refs.ladderPage.open(row)
        // },
        // openCost(row) {
        //     let costJson = row.costFormJson ? JSON.parse(row.costFormJson) : {}
        //     this.costEditRow = costJson
        //     let data = costJson['data'] || {}
        //     this.$refs.costform.open(data, 'detail')
        // },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '配置项'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请选择配置项'),
                        fieldName: 'configItem',
                        dictCode: 'quotedConfigConfigItem',
                        required: '1',
                        bindFunction: function (_self, formRef, pageConfig, groupData, value, field, formModel, tpmRootRef) {
                            let setDisableExtendByField = function (fieldName, formFields, index) {
                                for (let sub of formFields) {
                                    if (sub.fieldName === fieldName) {
                                        sub.disabled = false
                                        sub.extend = extendData[index]
                                        break
                                    }
                                }
                            }
                            var setDisableByField = function (fieldName, formFields) {
                                for (let sub of formFields) {
                                    if (sub.fieldName === fieldName) {
                                        sub.disabled = true
                                        break
                                    }
                                }
                            }

                            if ('' === value || null === value || undefined === value) {
                                setDisableByField('configValue', groupData.formFields)
                                groupData.formModel.configValue = ''
                                groupData.formModel.configName = ''
                                return
                            }
                            let items = ['materialNumber', 'materialCate', 'materialGroup']
                            let index = items.findIndex(rs => rs === value)
                            let extendData = [
                                {
                                    selectModel: 'single',
                                    modalColumns: [
                                        {
                                            field: 'materialNumber',
                                            title: '物料编码',
                                            fieldLabelI18nKey: 'i18n_field_materialNumber',
                                            with: 150
                                        },
                                        {
                                            field: 'materialDesc',
                                            title: '物料描述',
                                            fieldLabelI18nKey: 'i18n_field_materialDesc',
                                            with: 150
                                        },
                                        {
                                            field: 'materialName',
                                            title: '物料名称',
                                            fieldLabelI18nKey: 'i18n_field_materialName',
                                            with: 150
                                        },
                                        {
                                            field: 'materialSpec',
                                            title: '物料规格',
                                            fieldLabelI18nKey: 'i18n_field_materialSpec',
                                            with: 150
                                        }
                                    ],
                                    modalUrl: '/material/purchaseMaterialHead/list',
                                    modalParams: {
                                        blocDel: '0'
                                    },
                                    afterClearCallBack: function (form, pageData, column) {
                                        form.configValue = ''
                                        form.configName = ''
                                    }
                                },
                                {
                                    selectModel: 'single',
                                    'modalColumns': [
                                        {
                                            field: 'cateCode',
                                            title: '编码',
                                            fieldLabelI18nKey: 'i18n_field_cateCode',
                                            treeNode: true
                                        },
                                        {
                                            field: 'title',
                                            title: '分类名称',
                                            fieldLabelI18nKey: 'i18n_title_classificationName',
                                            width: 180
                                        },
                                        {
                                            field: 'value',
                                            title: '分类编码',
                                            fieldLabelI18nKey: 'i18n_title_classificationCode',
                                            width: 180
                                        }
                                    ],
                                    modalUrl: '/material/purchaseMaterialCode/pageMaterialCodeTree',
                                    customType: {
                                        pagerConfig: {},
                                        resultKey: 'res.result.records'
                                    },
                                    modalParams: {
                                        cateStatus: '1'
                                    },
                                    isTree: true,
                                    treeConfig: {},
                                    afterClearCallBack: function (form, pageData, column) {
                                        form.configValue = ''
                                        form.configName = ''
                                    }
                                },
                                {
                                    selectModel: 'single',
                                    'modalColumns': [
                                        {
                                            field: 'value',
                                            title: '物料组编码',
                                            fieldLabelI18nKey: 'i18n_field_cateCode',
                                            treeNode: true
                                        },
                                        {
                                            field: 'title',
                                            title: '物料组名称',
                                            fieldLabelI18nKey: 'i18n_title_classificationName',
                                            width: 180
                                        }
                                    ],
                                    modalUrl: '/base/dict/getFindDictItems',
                                    modalParams: function (that, form, row) {
                                        return {dictCode: 'MaterialGroup', elsAccount: that.$ls.get('Login_elsAccount')}
                                    },
                                    isTree: true,
                                    afterClearCallBack: function (form, pageData, column) {
                                        form.configValue = ''
                                        form.configName = ''
                                    }
                                }
                            ]
                            setDisableExtendByField('configValue', groupData.formFields, index)
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'remoteSelect',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '配置项值编码'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请选择配置项值编码'),
                        fieldName: 'configValue',
                        disabled: true,
                        required: '1',
                        bindFunction: (Vue, _data) =>{
                            if (Vue.group.formModel.configItem === 'materialCate') {
                                Vue.group.formModel.configValue = _data[0].value || _data[0].key
                                Vue.group.formModel.configName = _data[0].title
                            }
                            if (Vue.group.formModel.configItem === 'materialGroup') {
                                Vue.group.formModel.configValue = _data[0].value
                                Vue.group.formModel.configName = _data[0].title
                            }

                            if (Vue.group.formModel.configItem === 'materialNumber') {
                                Vue.group.formModel.configValue = _data[0].materialNumber
                                Vue.group.formModel.configName = _data[0].materialName
                            }

                        },
                        extend: {}
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '配置项值名称'),
                        fieldName: 'configName',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '公司'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请选择公司'),
                        fieldName: 'company',
                        dictCode: 'purchase_organization_info#concat(org_code,\'_\',org_name)#org_code#org_category_code="companyCode" && status="1"'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '工厂'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请选择工厂'),
                        fieldName: 'factory',
                        dictCode: 'purchase_organization_info#concat(org_code,\'_\',org_name)#org_code#org_category_code="factory" && status="1"'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '采购组织'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请选择采购组织'),
                        fieldName: 'purchaseOrg',
                        dictCode: 'purchase_organization_info#concat(org_code,\'_\',org_name)#org_code#org_category_code="purchaseOrganization" && status="1"'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '报价方式'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请选择报价方式'),
                        fieldName: 'quotePriceWay',
                        dictCode: 'srmQuotePriceWay',
                        filterSelectList: ['0'],
                        required: '1',
                        bindFunction: function (_self, formRef, pageConfig, groupData, value, field, formModel, tpmRootRef) {
                            let setDisableByField = function (fieldName, formFields, flag) {
                                for (let sub of formFields) {
                                    if (sub.fieldName === fieldName) {
                                        sub.disabled = flag
                                        break
                                    }
                                }
                            }

                            if ('' === value || undefined === 'value' || null === value) {
                                setDisableByField('costFormJson', groupData.formFields, true)
                                setDisableByField('ladderPriceJson', groupData.formFields, true)
                            }

                            if (value === '1') {
                                setDisableByField('costFormJson', groupData.formFields, true)
                                setDisableByField('ladderPriceJson', groupData.formFields, false)
                                groupData.formModel.costFormJson = ''
                            }
                            if (value === '2') {
                                setDisableByField('costFormJson', groupData.formFields, false)
                                setDisableByField('ladderPriceJson', groupData.formFields, true)
                                groupData.formModel.ladderPriceJson = ''
                            }
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '成本组成JSON'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请输入成本组成JSON'),
                        fieldName: 'costFormJson',
                        disabled: true,
                        bindFunction: (Vue, data) =>{
                            if ('' !== data && undefined !== data && null!==data) {
                                Vue.group.formModel.costFormJson = JSON.stringify(data)
                            }
                        },
                        extend: {
                            selectModel: 'single',
                            modalColumns: [
                                {
                                    field: 'templateName',
                                    title: '模板名称',
                                    fieldLabelI18nKey: 'i18n_title_templateName',
                                    with: 150
                                },
                                {
                                    field: 'templateNumber',
                                    title: '模板编号',
                                    fieldLabelI18nKey: 'i18n_title_templateNumber',
                                    with: 150
                                },
                                {
                                    field: 'createBy_dictText',
                                    title: '创建人',
                                    fieldLabelI18nKey: 'i18n_field_createBy',
                                    with: 150
                                }
                            ],
                            isTree: true,
                            modalUrl: '/template/templateHead/getListByType',
                            modalParams: function (that, form, row) {
                                return {businessType: 'costForm', elsAccount: that.$ls.get('Login_elsAccount')}
                            },
                            afterClearCallBack: function (form, pageData, column) {
                                form.costFormJson = ''
                            }
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '6',
                        fieldType: 'ladderPrice',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '阶梯报价JSON'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请输入阶梯报价JSON'),
                        fieldName: 'ladderPriceJson',
                        disabled: true,
                        bindFunction: (Vue, data) =>{
                            if ('' !== data && undefined !== data && null!=data && isArray(data)) {
                                Vue.group.formModel.ladderPriceJson = JSON.stringify(data)
                            }
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '6',
                        fieldType: 'input',
                        dictCode: 'yn',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#`, '备注'),
                        fieldName: 'remark'
                    }
                ]
            }
        }
    }
}
</script>
