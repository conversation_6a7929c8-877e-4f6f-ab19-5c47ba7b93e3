<template>
  <div
    class="els-page-comtainer"
    style="background-color: white;">
    <a-spin :spinning="confirmLoading">
      <a-page-header
        :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
        :title="pageData.title"
      >
        <template
          v-if="taskInfo.taskId"
          slot="extra">
          <taskBtn
            :currentEditRow="currentEditRow"
            :pageHeaderButtons="pageData.publicBtn"/>
        </template>
        <template
          slot="extra"
          v-else>
          <a-button
            v-for="(item, key) in publicBtn"
            v-show="item.showCondition ? item.showCondition(item.id) : true"
            :type="item.type || 'default'"
            style="margin-left:8px"
            slot="tabBarExtraContent"
            @click="item.clickFn"
            :key="'public_btn_' + key">{{ item.title }}</a-button>
        </template>
      </a-page-header>
      <a-collapse
        v-model="activeKey"
        expandIconPosition="right">
        <a-collapse-panel
          v-for="(panel, index) in pageData.panels"
          :key="'collapse_panel_'+index"
          :header="panel.title"
        >
          <!-- //预警配置通知对象 -->
          <div style="display:flex; justify-content:space-around">
            <div
              v-for="(item, value) in panel.content.plainOptions"
              :key="value">
              <div
                v-if="item.style"
                style="width:5px;height:100%;background-color: #000000;border: 1px solid #000000;border-radius:10px;"></div>
              <div
                style="display:flex;align-content:space-between;"
                v-if="panel.content.ref=='timerForm' &&!item.style" >
                <div style="margin-right:50px"><b
                  v-if="item.ordata"
                  style="font-size:16px;margin-right:7px">{{ item.ordata }}</b>{{ item.purchasetitle }}</div>
                <div style="display: grid;margin: 0 auto;">
                  <div
                    style="margin-left: 2px;margin-top:6px;display:flex;justify-content:space-between;"
                    v-for="(item, value) in item.list"
                    :key="value">
                    <div>
                      <span style="padding-right:0px;">{{ item.title }}</span>
                      <a-tooltip
                        v-if="item.helpText"
                        :title="item.helpText">
                        <a-icon type="question-circle-o" />
                      </a-tooltip>
                    </div>
                    <a-form-model-item
                      style="display:flex;margin:-7px 0px 5px 5px;width:400px;"
                      :prop="item.fieldName"
                      v-if="item.type==='select'">
                      <m-select
                        style="width:400px;"
                        :getPopupContainer="triggerNode=>{ return $refs.els_page_comtainer || document.body}"
                        :returnTitle="true"
                        :mode="item.mode"
                        :disabled="item.disabled"
                        v-model="formData[item.fieldName]"
                        :placeholder="item.placeholder"
                        :show-opt-value="item.showOptValue"
                        :options="item.options"
                        :source-url="item.sourceUrl"
                        :source-map="item.sourceMap"
                        :value-map="item.valueMap"
                        :title-map="item.titleMap"
                        :configData="pageData"
                        :dict-code="item.dictCode"
                        :noEmptyOpt="item.noEmptyOpt"
                        :fieldList="panel.content.list"
                        :formModel="formData"
                        :relationData="item.relationData"
                        @change="item.changeEvent"/>
                    </a-form-model-item>
                    <a-form-model-item
                      style="display:flex;margin:-7px 0px 5px 5px;width:400px;"
                      v-else-if="item.type=='selectModal'"
                      :prop="item.fieldName">
                      <!-- <a-tooltip
                                slot="label"
                                v-if="item.label"
                                :title="item.label">
                                {{ item.label }}
                                </a-tooltip> -->
                      <m-select-modal
                        isFromTileEditPage
                        v-model="formData[item.fieldName]"
                        :disabled="item.disabled || false"
                        :config="item"
                        style="width:400px"
                        :pageData="pageData"
                        :form="formData"
                        :currentStep="index"
                        @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                      />
                    </a-form-model-item>
                    <a-form-model-item
                      style="width:400px;margin:-7px 0px 5px 5px;"
                      v-if="item.type == 'codeEditorModel'"
                      :prop="item.fieldName">
                      <code-editor-model
                        :defaultValue="item.defaultValue"
                        :disabled="item.disabled"
                        style="width:100%"
                        v-bind="item.extend || {}"
                        v-model="formData[item.fieldName]"
                        @handleSureClick="(content)=> {formData[item.fieldName] = content}"></code-editor-model>
                    </a-form-model-item>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <a-row
            v-if="panel.content.type == 'form'"
            style="margin-bottom:-12px;color:#222"
            :gutter="24">
            <a-col
              v-for="(item,i) in panel.content.list"
              :key="i"
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">{{ item.label }}:
                  <a-tooltip
                    placement="topLeft"
                    :title="item.helpText">
                    <a-icon
                      type="question-circle-o"
                      v-if="item.helpText" />
                  </a-tooltip>
                </a-col>
                <a-col :span="15">{{ item.isBoolean ? (formData[item.fieldName] ? '是':'否') : formData[item.fieldName] }}</a-col>
              </a-row>
            </a-col>
          </a-row>
          <div v-else-if="panel.content.type == 'table'">
            <vxe-grid
              border
              auto-resize 
              resizable
              highlight-hover-row
              show-overflow
              :ref="panel.content.ref"
              size="mini"
              :columns="panel.content.columns"
              height="300"
              :edit-config="{trigger: 'click', mode: 'cell'}"
            >
              <template slot="empty">
                <a-empty />
              </template>
              <template #cell_edit_modal="{ row, column }">
                <a
                  @click="openCellEditModal(row, column)"
                  v-html="row[column.property]"></a>
              </template>
            </vxe-grid>
          </div>
          <upload-list
            :ref="panel.content.ref"
            v-else-if="panel.content.type == 'upload'"></upload-list>
        </a-collapse-panel>
      </a-collapse>
    </a-spin>
  </div>
</template>
<script>
import { Empty } from 'ant-design-vue'
import { getAction } from '@/api/manage'
// import { postAction } from '@/api/manage'
import { areas } from '@/store/area'
import uploadList from '@comp/uploadList/uploadList'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import {ajaxFindDictItems} from '@/api/api'
import { mapGetters } from 'vuex'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
export default {
    name: 'DetailPage',
    props: {
        pageData: {
            type: Object,
            default: null
        },
        currentEditRow: {
            type: Object,
            default: null
        },
        afterHandleData: {
            type: Function,
            default: ()=>{}

        }
    },
    components: {
        AEmpty: Empty,
        uploadList,
        taskBtn
    },
    computed: {
        ...mapGetters([
            'taskInfo'
        ]),
        provinceList () {
            let list = areas.map(item => {
                return {value: item.value, label: item.label}
            })
            return list
        },
        cityList () {
            let list = []
            areas.forEach(item => {
                if(item.children && item.children.length) {
                    list = list.concat(item.children)
                }
            })
            list = list.map(item => {
                return {value: item.value, label: item.label}
            })
            return list
        },
        areaList () {
            let list = []
            areas.forEach(item => {
                if(item.children && item.children.length) {
                    item.children.forEach(item => {
                        list = list.concat(item.children)
                    })
                }
            })
            list = list.map(item => {
                return {value: item.value, label: item.label}
            })
            return list
        },
        publicBtn () {
            if(this.pageData.publicBtn && this.pageData.publicBtn.length) {
                return this.pageData.publicBtn
            }else {
                return [{type: 'rollBack', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack}]
            }
        }
    },
    data () {
        return {
            fixPageHeader: false,
            formData: {},
            confirmLoading: false,
            pageDetail: {}
        }
    },
    created () {
        this.computedKey()
        this.getPageDetail()
    },
    methods: {
        goBack () {
            this.$parent.hide()
        },
        computedKey () {
            let keyList = []
            for(let i = 0; i < this.pageData.panels.length; i++) {
                keyList.push('collapse_panel_'+i)
            }
            this.activeKey = keyList
        },
        gettimerFormdata (data, panel){
            let that = this         
            panel.content.plainOptions.forEach(item2 => {
                item2.list.forEach(item=>{
                    that.formData[item.fieldName] = data[item.fieldName]
                    if(item.fieldName=='purchaseAssignee'){
                        that.formData[item.fieldName]=data[item.fieldName+'_dictText']
                    }
                })
            })
        },
        getPageDetail () {
            let that = this
            this.confirmLoading = true
            let params = {id: this.currentEditRow.id}
            getAction(this.pageData.url.detail, params).then(res => {
                this.confirmLoading = false
                if(res.success) {
                    if (res.result.accountValidityDate) {
                        let time = res.result.accountValidityDate
                        let dateTime = new Date(time)     
                        res.result.accountValidityDate = dateTime.toLocaleDateString().replace(/\//g, '-')
                    }
                    let data = res.result
                    this.pageDetail = data
                    this.afterHandleData(this.pageDetail)
                    that.pageData.panels.forEach(panel => {
                        if(panel.content.ref == 'timerForm'){that.gettimerFormdata(data, panel)}
                        if(panel.content.type == 'form') {
                            panel.content.list.forEach(item => {
                                that.formData[item.fieldName] = data[item.fieldName]
                                if(item.type == 'addressCascader') {
                                    if(data[item.state]) {
                                        let cityCode = data[item.city] || ''
                                        let areaCode = data[item.area] || ''
                                        let province = that.getTitleByValue(that.provinceList, data[item.state])
                                        let city = that.getTitleByValue(that.cityList, cityCode)
                                        let area = that.getTitleByValue(that.areaList, areaCode)
                                        that.formData[item.fieldName] = province +( city ? '/' : '') + city + (area ? '/' : '') + area
                                    }
                                }
                                if(item.fieldName == 'organizationType') {
                                    // item.fieldName+= '_dictText';
                                    //根据字典Code, 初始化字典数组
                                    let code = this.pageData
                                    let postData = {
                                        busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                                        dictCode: 'orgCategoryCode'
                                    }
                                    ajaxFindDictItems(postData).then((res) => {
                                        if (res.success) {
                                            for(let i = 0; i < res.result.length; i++) {
                                                if(res.result[i].value == that.formData[item.fieldName]){
                                                    that.formData[item.fieldName] = res.result[i].text
                                                    that.$forceUpdate()
                                                }
                                            }
                                        }
                                    })
                                }
                                if(item.type == 'message') {
                                    let value =  item.fieldName.indexOf('_dictText')> -1 ?  item.fieldName.substring(0, item.fieldName.indexOf('_dictText')) : ''
                                    //根据字典Code, 初始化字典数组
                                    let postData = {
                                        busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                                        dictCode: item.dictCode|| item.type+'_'+ data.businessType
                                    }
                                    ajaxFindDictItems(postData).then((res) => {
                                        if (res.success&&res.result.length>0) {
                                            const dictLabelArr=res.result.filter(v=>{  return  v.value==data[value]})
                                            that.formData[item.fieldName] =dictLabelArr.length!=0 ? dictLabelArr[0].text : ''
                                            console.log(dictLabelArr[0].text)
                                            this.$forceUpdate()
                                        }
                                    })
                                }
                                if(item.type == 'dateRange') {
                                    if(data[item.start] && data[item.end]) {
                                        that.formData[item.fieldName] = data[item.start] + '~' + data[item.end]
                                    }
                                }
                            })
                        }else if(panel.content.type == 'table') {
                            that.$refs[panel.content.ref][0].loadData(data[panel.content.ref])
                        }else if(panel.content.type == 'upload') {
                            panel.content.relatedId = data[panel.content.relatedIdMap]
                        }
                    })
                    this.getUploadListData()
                    this.$forceUpdate()
                }
            })  
        },
        getUploadListData () {
            let that = this
            let uploadPanel = this.pageData.panels.filter(panel => {
                return panel.content.type == 'upload'
            })
            if(uploadPanel.length) {
                uploadPanel.forEach(item => {
                    that.$refs[item.content.ref][0].getFileList(item.content.relatedId, item.content.relatedType, item.content.roleName)
                })
            }
        },
        //打开行明细弹窗
        openCellEditModal (row, column) {
            this.$parent['edit_'+column.property](row)
        },
        getTitleByValue (list, value) {
            let title = ''
            if(value) {
                list.forEach(item => {
                    if(item.value == value) {
                        title = item.label
                    }
                })
            }
            return title
        }
    }
}
</script>
<style scoped>
    .ant-page-header {
       padding: 8px 16px;
    }
</style>