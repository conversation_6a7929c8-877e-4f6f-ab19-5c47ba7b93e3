<template>
  <div>
    <a-modal
      v-drag
      v-model="timeVisible"
      :title="title"
      @ok="confirm">
      <a-spin :spinning="loading">
        <a-date-picker
          style="width:100%"
          :show-time="true"
          :valueFormat="'YYYY-MM-DD HH:mm:ss'"
          v-model="newEndTime" />
      </a-spin>
    </a-modal>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import { postAction } from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
export default {
    name: 'PricingNotice',
    components: {
        fieldSelectModal
    },
    data () {
        return {
            changeRow: {},
            loading: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterNewReportPriceDeline`, '请输入新的报价截止时间'),
            newEndTime: undefined,
            timeVisible: false,
            packageOpt: false,
            headId: undefined,
            reQuoteWay: undefined,
            purchaseEnquiryItemList: undefined,
            enquirySupplierListList: undefined,
            selectFlag: undefined,
            optionData: []
        }
    },
    methods: {
        openUser (headId, chooseList, packageOpt, row) {
            this.changeRow = row
            this.headId = headId
            if (!chooseList.length) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectRowDataBeOperated`, '请选择需要操作的行数据'))
                return
            }
            this.reQuoteWay = 'user'
            this.purchaseEnquiryItemList = chooseList
            this.packageOpt = packageOpt
            this.timeVisible = true
        },
        openMaterial (headId){
            this.headId = headId
            this.reQuoteWay = 'material'
            let columns = [
                {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150},
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150},
                {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200},
                {field: 'purchaseCycle', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), width: 200},
                {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料规格'), width: 200}
            ]
            this.selectFlag = 'material'
            this.$refs.fieldSelectModal.open('/enquiry/purchaseEnquiryHead/reQuoteMaterialList', {headId: headId}, columns, 'multiple')
        },
        openSupplier (headId){
            this.headId = headId
            this.reQuoteWay = 'supplier'
            let columns = [
                {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150},
                {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200},
                {field: 'needCoordination_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), width: 200}
            ]
            this.selectFlag = 'supplier'
            this.$refs.fieldSelectModal.open('/enquiry/purchaseEnquiryHead/reQuoteSupplierList', {headId: headId}, columns, 'multiple')
        },
        fieldSelectOk (data){
            if(this.selectFlag === 'material'){
                this.purchaseEnquiryItemList = data
            }else{
                this.enquirySupplierListList = data
            }
            this.timeVisible = true
        },
        openAll (headId){
            this.headId = headId
            this.reQuoteWay = 'all'
            this.timeVisible = true
        },
        confirm (){
            if(!this.newEndTime){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationDeadlineCannotBeBlank`, '报价截止时间不能为空'))
                return
            }
            let param = {}
            param['id'] = this.headId
            param['quoteEndTime'] = this.newEndTime
            param['reQuoteWay'] = this.reQuoteWay
            param['packageOpt'] = this.packageOpt
            param['purchaseEnquiryItemList'] = this.purchaseEnquiryItemList
            param['enquirySupplierListList'] = this.enquirySupplierListList
            this.loading = true
            postAction('/enquiry/purchaseEnquiryHead/reQuote', param).then(res => {
                if(res.success === true){
                    this.$message.success(res.message)
                    if (this.reQuoteWay === 'user') {
                        this.$parent.getItemData('option', this.changeRow)
                        this.$parent.refreshEnguiryDetailTab()
                        return
                    }
                    this.$parent.refresh()
                }else{
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.loading = false
                this.timeVisible = false
            })
        }
    }
}
</script>