<template>
  <div
    bordered
    class="description">
    <template v-if="tab.custom.formFields && tab.custom.formFields.length">
      <a-descriptions
        bordered
        size="small">
        <a-descriptions-item
          v-for="field in formFields"
          :key="field.fieldName"
          :span="descriptionsSpan(field)">
          <span slot="label">
            {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
            <a-tooltip
              v-if="field.helpText"
              :title="field.helpText">
              <a-icon type="question-circle-o" />
            </a-tooltip>
            <span class="item-required-icon" v-if="field.required==='1'">*</span>
          </span>
          <template>
            <div
              class="item-content"
              :class="{'item-required-text': field.required==='1' }"
              v-if="!tab.custom.parentObj">
              <!-- 超链接 -->
              <template v-if="field.extend && field.extend.linkConfig">
                <!--  外链  -->
                <a
                  v-if="field.extend && field.extend.exLink"
                  :href="form[field.fieldName]"
                  target="_blank">
                  <span>{{ $srmI18n(`${$getLangAccount()}#${field.extend.linkConfig.titleI18nKey}`,field.extend.linkConfig.title) }}</span>
                </a>
                <!--  路由跳转  -->
                <span
                  v-else
                  :style="{ 'color': '#0000FF', 'cursor': 'pointer' }"
                  @click="getNewRouter(form[field.fieldName], field)">
                  {{ form[field.fieldName] }}
                </span>
              </template>
              <span
                :style="{color: mapFieldColor(field, form)}"
                v-else-if="field.fieldType == 'switch'">
                {{
                  ['1', 'Y'].includes(form[field.fieldName])
                    ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                    : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                }}
              </span>
              <div v-else-if="field.fieldType == 'image'"> 
                <template v-if="previewPictureText(form[field.fieldName])">
                  <span
                    class="preview"
                    :key="idx"
                    v-for="(el, idx) of previewPictureText(form[field.fieldName])"
                    @click.stop="previewPicture(el)">
                    <img
                      :key="idx"
                      :src="el"
                      alt=""/>
                  </span>
                </template>
              </div>
              <div v-else-if="field.fieldType === 'fileUpload' && field.fieldName==='fbk10'">
                <div>
                  <p
                    v-for="attachment in getAttachments(form[field.fieldName])"
                    :key="attachment.id">
                    <a>{{ attachment.fileName }}</a>
                    <a
                      style="margin-left: 10px"
                      @click="()=> { downloadtWeekReportFile(attachment) }">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }} </a>
                    <a
                      @click="()=> { handlePreview(attachment) }"> {{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                  </p>
                </div>
              </div>
              <template v-else-if="field.fieldType == 'richEditorModel'">
                <!-- <Popover :content="form[field.fieldName]" class="rich-editor-display-box p" /> -->
                <renderHtmlModal :content="form[field.fieldName]"/> 
              </template>
              <!-- 专家专业后台没法翻译-->
              <template v-else-if="field.fieldType == 'treeSelect' && field.fieldName == 'specialized'">
                <m-tree-select
                  v-model="form[field.fieldName]"
                  allowClear
                  :configData="field"
                  disabled
                  :multiple="(field.extend && field.extend.multiple) || false"
                  :maxTagCount="(field.extend && field.extend.maxTagCount) || 1"
                  :sourceUrl="field.dictCode"
                  :sourceMap="field.sourceMap"
                  :sourceData="field.extend && field.extend.sourceData"
                  :showEmptyNode="field.showEmptyNode"
                  :treeNodeFilterProp="field.extend && field.extend.treeNodeFilterProp"
                  :treeDefaultExpandAll="field.extend && field.extend.treeDefaultExpandAll"
                  :parentNodeSelectable="field.extend && (field.extend.parentNodeSelectable || true)"
                />
              </template>
              <template v-else-if="field.fieldType == 'treeSelect'">
                <span>{{ form[field.fieldName+'_dictText'] }}</span>
              </template>
              <!-- 阶梯价格 -->
              <template v-else-if="field.fieldName === 'ladderPriceJson'">
                <a-tooltip
                  placement="top"
                  v-if="form['ladderPriceJson']"
                  overlayClassName="tip-overlay-class">
                  <template slot="title">
                    <vxe-table
                      auto-resize
                      border
                      size="mini"
                      :data="initRowLadderJson(form['ladderPriceJson'])">
                      <vxe-table-column
                        type="seq"
                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_details`, '序号')}`"
                        width="80">
                      </vxe-table-column>
                      <vxe-table-column
                        field="ladderQuantity"
                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_ladderQuantity`, '阶梯数量')}`"
                        width="140">
                      </vxe-table-column>
                      <vxe-table-column
                        field="price"
                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_price`, '含税价')}`"
                        width="140">
                      </vxe-table-column>
                      <vxe-table-column
                        field="netPrice"
                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_netPrice`, '不含税价')}`"
                        width="140">
                      </vxe-table-column>
                    </vxe-table>
                  </template>
                  <div class="json-box"><a href="javascript: void(0)">{{
                    defaultRowLadderJson(form['ladderPriceJson'])
                  }}</a></div>
                </a-tooltip>
              </template>
              <template v-else-if="field.fieldName === 'frozenFunction' && field.fieldType === 'customSelectModal'">
                <span
                  @click="$emit('customSelect', form)"
                  style="color: blue; cursor:pointer;">{{
                    `${$srmI18n(`${$getLangAccount()}#i18n_title_frozenFunction`, '冻结功能')}`
                  }}</span>
              </template>
              <div v-else-if="field.fieldType == 'cascader'">
                {{ form[field.fieldName + '_dictText'] }}
              </div>
              <span v-else-if="field.fieldType == 'currency'">
                {{ formatCurrencyValue(form[field.fieldName], field) }}
              </span>
              <span v-else-if="field.fieldType == 'float'">
                {{ formatFloat(form[field.fieldName], field.dataFormat) }} 
              </span>
              <span
                :style="{color: mapFieldColor(field, form)}"
                v-else>
                {{ field.fieldType == 'select' || field.fieldType == 'multiple' ?
                  form[field.fieldName + '_dictText'] : form[field.fieldName]
                }}
              </span>
            </div>
            <div
              class="item-content"
              :class="{'item-required-text': field.required==='1' }"
              v-else-if="tab.custom.parentObj && form[tab.custom.parentObj]">
              <span
                :style="{color: mapFieldColor(field, form[tab.custom.parentObj])}"
                v-if="field.fieldType == 'switch'">
                {{
                  ['1', 'Y'].includes(form[tab.custom.parentObj][field.fieldName])
                    ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                    : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                }}
              </span>
              <a
                v-if="field.fieldType === 'link' && field.extend && field.extend.exLink"
                :href="form[item.fieldName]"
                target="_blank">
                <span>{{ $srmI18n(`${busAccount}#${item.extend.linkConfig.titleI18nKey}`,item.extend.linkConfig.title) }}</span>
              </a>
              <a
                v-if="item.fieldType === 'link' && field.extend && !field.extend.exLink"
                @click="getNewRouter(form[item.fieldName], item)">
                {{ getNewRouter(form[item.fieldName]) }}
              </a>
              <div v-else-if="field.fieldType == 'cascader'">
                {{ form[field.fieldName + '_dictText'] }}
              </div>
              <span
                :style="{color: mapFieldColor(field, form[tab.custom.parentObj])}"
                v-else>
                {{ field.fieldType == 'select' || field.fieldType == 'multiple' ?
                  form[tab.custom.parentObj][field.fieldName + '_dictText'] :
                  form[tab.custom.parentObj][field.fieldName]
                }}
              </span>
            </div>
          </template>
        </a-descriptions-item>
      </a-descriptions>
    </template>
  </div>
</template>
  
<script>
import { currency, formatFloat} from '@/filters'
import MTreeSelect from '@comp/treeSelect/mTreeSelect'
  
export default {
    components: {
        Popover: () => import('@comp/Popover/index.vue'),
        MTreeSelect
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        tab: {
            type: Object,
            default: () => { }
        },
        form: {
            type: Object,
            default: () => { 
                return {}
            }
        },
        busAccount: {
            type: String,
            default: ''
        }
    },
    computed: {
        formFields () {
            let hideFields = ['hiddenField']
            let rs = this.tab.custom.formFields.filter(rs => !hideFields.includes(rs.fieldType))
            return rs
        }
    },
    methods: {
        formatFloat,
        previewPictureText (value) {
            let result = ''
            if (value) {
                result = value.split(',')
            } 
            return result
        },
        // 附件列表
        getAttachments (attachments) {
            let arr = []
            if (attachments) {
                let list = JSON.parse(attachments)
                console.log(list)
                arr= list
            }
            return arr
        },
        // 下载周报
        downloadtWeekReportFile (file) {
            const params = file
            let url= this.$variateConfig['domainURL'] + '/attachment/purchaseAttachment/download?id='+ params.id +'&token=' + this.$ls.get('Access-Token')
            window.open(url, '_blank')
        },
        // 预览
        handlePreview (file) {
            let params = file
            this.$previewFile.open({params: params })
        },
        formatCurrencyValue (val, config) {
            let extend = config.extend || {}
            let symbol = extend && extend.symol || ''
            let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
            return currency(val, symbol, decimals)
        },
        //调整Descriptions.Item的span的数量
        descriptionsSpan (fields){
            if(fields.fieldType=='textArea') return 3
            let otherFields=this.formFields.filter((n) =>n.fieldType!=='textArea')
            if(fields.fieldName===otherFields[otherFields.length-1].fieldName){
                //最后一个字段填满该行
                return 3
            }
            return 1      
        },
        // 适配列自定义颜色配置
        mapFieldColor (field, form) {
            let color = ''        
            if (field && field.dictCode) {            
                if (this.currentEditRow && this.currentEditRow[`${field.fieldName}_dictText_fieldColors`] && this.currentEditRow[`${field.fieldName}_dictText_fieldColors`].length) {
                    let fieldColors = this.currentEditRow[`${field.fieldName}_dictText_fieldColors`]                
                    fieldColors.forEach((item)=> {
                        if (item.fieldName === form[field.fieldName]) {
                            color= item.fieldColor
                        }
                    })
                }
            }
            return color
        },
        initRowLadderJson (jsonData) {
            let arr = []
            if (jsonData) {
                arr = JSON.parse(jsonData)
            }
            return arr
        },
        // 阶梯报价默认显示
        defaultRowLadderJson (jsonData) {
            let arrString = ''
            if (jsonData) {
                let arr = JSON.parse(jsonData)
                arr.forEach((item, index) => {
                    let ladderQuantity = item.ladderQuantity
                    let price = item.price
                    let netPrice = item.netPrice
                    let str = `${ladderQuantity} ${price} ${netPrice} `
                    let separator = index === arr.length - 1 ? '' : ','
                    arrString += str + separator
                })
            }
            return arrString
        },
        getNewRouter (value, item) {
            this.$emit('getNewRouter', ...arguments)
        },
        previewPicture (data) {
            this.$emit('previewPicture', ...arguments)
        }
    }
}
</script>
  
  <style lang="less" scoped>
  :deep(.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-content){
      padding: 0px 0px !important;
      color: #798087;
  }
  .preview{
      cursor: pointer;
      display: inline-block;
      width: 80px;
      height: 80px;
      margin-right: 10px;
      img{
        display: block;
        width: 100%;
        height: 100%;
      }
  }
  .item-content {    
      // padding: 8px 16px;
      // min-height: 44px;
      // line-height: 28px;
      padding-left: 4px;
  }
  .item-required-icon {
      color: #f5222d;
      font-size: 14px;
  }
  .item-required-text {
      background-color: #fff9f7;
      border: 1px solid #fdaf96;
      border-radius: 2px;
      color: #454f59;
      // margin: 8px 8px;
      // line-height: 13px;
      // padding: 8px 8px;
      // min-height: 28px;
  }
  :deep(.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-label){
    width: 16.66%;
  }
  </style>
  