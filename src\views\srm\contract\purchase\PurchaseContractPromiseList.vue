<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage&& !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"/>
    <View-Purcase-Contract-Promise-Modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetailPage"
    />
    <Purcase-Contract-Promise-Modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import PurcaseContractPromiseModal from './modules/PurcaseContractPromiseModal'
import ViewPurcaseContractPromiseModal from './modules/ViewPurcaseContractPromiseModal'
import {httpAction} from '@/api/manage'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        'Purcase-Contract-Promise-Modal': PurcaseContractPromiseModal,
        'View-Purcase-Contract-Promise-Modal': ViewPurcaseContractPromiseModal
    },
    data () {
        return {
            showEditPage: false,
            currentEditRow: {},
            pageData: {
                businessType: 'contractPromise',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    }
                ],
                button: [
                    //{label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        folded: true,
                        clickFn: this.settingColumns
                    }
                ],
                form: {
                    keyWord: ''
                },
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        authorityCode: 'contractPromise#purchaseContractPromise:view',
                        clickFn: this.handleView
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        authorityCode: 'contractPromise#purchaseContractPromise:edit',
                        clickFn: this.handleEdit,
                        allow: this.showEditCondition
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZUzRL_ce0f144f`, '履约审批确认'),
                        clickFn: this.handleEdit,
                        allow: this.showConfirmedCondition
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        authorityCode: 'contractPromise#purchaseContractPromise:delete',
                        clickFn: this.handleDelete,
                        allow: this.allowDelete
                    },
                    {
                        type: 'close-circle',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                        authorityCode: 'contractPromise#purchaseContractPromise:cancel',
                        clickFn: this.cancel,
                        allow: this.showCancelCondition
                    },
                    {
                        type: 'chat',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'),
                        clickFn: this.handleChat,
                        allow: this.allowChat
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ]
            },
            url: {
                list: '/contract/purchaseContractPromise/list',
                add: '/contract/purchaseContractPromise/add',
                delete: '/contract/purchaseContractPromise/delete',
                cancel: '/contract/purchaseContractPromise/cancel',
                exportXlsUrl: 'contract/purchaseContractPromise/exportXls',
                columns: 'purchaseContractPromiseList'
            }
        }
    },
    methods: {
        handleChat (row) {
            let {id} = row
            let recordNumber = row.promiseNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'PurchaseContractPromise', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if (row.promiseStatus != '0') {
                return false
            } else {
                return true
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_salesContractHeader`, '采购合同履约头'))
        },
        hideDetailPage () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            } else {
                this.showDetailPage = false
            }
        },
        submitCallBack () {
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelCallBack () {
            this.showEditPage = true
            this.showDetailPage = false
            this.searchEvent()
        },
        showEditCondition (row) {
            if (((row.promiseStatus == '0' || row.promiseStatus == '6') && row.busAccount == row.createAccount && (row.auditStatus == '0' || row.auditStatus == '4' || row.auditStatus == '3')) || (row.busAccount == row.createAccount && row.promiseStatus == '4')) {
                return false
            } else {
                return true
            }
        },
        showConfirmedCondition (row) {
            if (row.promiseStatus == '1' && row.busAccount != row.createAccount && (row.auditStatus == '0' || row.auditStatus == '4' || row.auditStatus == '3')) {
                return false
            } else {
                return true
            }
        },
        showCancelCondition (row) {
            if (row.promiseStatus == '1' || row.promiseStatus == '2' || row.promiseStatus == '3' || row.promiseStatus == '4') {
                return false
            } else {
                return true
            }
        },
        allowDelete (row) {
            if (row.promiseStatus == '0' && (row.auditStatus == '0' || row.auditStatus == '4' || row.auditStatus == '3')) {
                return false
            } else {
                return true
            }
        },
        cancel (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        postUpdateData (url, row) {
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        }

    }
}
</script>