<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showHisPage && !showEsignPage && !showContractLockPage && !showEsignV3Page && !showFadadaPage && !showFadadaOnePage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <purchaseContractHead-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 表单区域 -->
    <viewPurchaseContractHead-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 表单区域 -->
    <purchaseContractHeadHis-modal
      v-if="showHisPage"
      :current-edit-row="currentEditRow"
      @hide="hideHisPage"/>
    <!-- 发起签章 -->
    <EsignFlowEdit
      v-if="showEsignPage"
      :current-edit-row="currentEditRow"
      @hide="hideEsignPage"/>
    <PurchaseEsignV3FlowEdit
      v-if="showEsignV3Page"
      :current-edit-row="currentEditRow"
      @hide="showEsignV3Page = false" />
    <!-- 查看签署流程 -->
    <purchase-order-e-sign-select-modal
      ref="viewESignList"
      :columns="viewEsignColumns"
      :title="viewEsignTitle"
    />
    <!-- 归档文件下载 -->
    <purchase-order-e-sign-download-modal
      ref="downloadESignList"
      :columns="downloadEsignColumns"
      :title="downloadEsignTitle"
    />
    <ClContractEdit
      v-if="showContractLockPage"
      :current-edit-row="currentEditRow"
      @hide="hideContractLockPage"/>
    <EditFadadaSignTaskPurchaseModal
      v-if="showFadadaPage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideFadadaPage"/>
    <EditFadadaSignTaskPurchaseOneModal
      v-if="showFadadaOnePage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideFadadaPage"/>
    <!--弹窗选择-->
    <field-select-modal
      ref="fieldSelectModal"/>
  </div>
</template>
<script>
import PurchaseEsignV3FlowEdit from '@/views/srm/esignV3/purchase/modules/PurchaseEsignV3FlowEdit'
import PurchaseContractHeadModalSimple from './modules/EditPurchaseContractHeadModalSimple'
import ViewPurchaseContractHeadModal from './modules/ViewPurchaseContractHeadModal'
import PurchaseContractHeadHisList from './PurchaseContractHeadHisList'
import EsignFlowEdit from '@/views/srm/esign/modules/EsignFlowEdit'
import PurchaseOrderESignSelectModal from '@/views/srm/order/purchase/PurchaseOrderESignSelectModal'
import PurchaseOrderESignDownloadModal from '@/views/srm/order/purchase/PurchaseOrderESignDownloadModal'
import EditFadadaSignTaskPurchaseModal from '@/views/srm/fadadaFasc/purchase/modules/EditFadadaSignTaskPurchaseModal'
import EditFadadaSignTaskPurchaseOneModal from '@/views/srm/fadadaFasc/purchase/modules/EditFadadaSignTaskPurchaseOneModal'
import fieldSelectModal from '@comp/template/fieldSelectModal'

import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, httpAction, postAction} from '@/api/manage'
import Vue from 'vue'
import {USER_COMPANYSET} from '@/store/mutation-types'
import ClContractEdit from '@/views/srm/contractLock/modules/ClContractEdit'
import { REPORT_ADDRESS } from '@/utils/const.js'


export default {
    mixins: [ListMixin],
    components: {
        'purchaseContractHead-modal': PurchaseContractHeadModalSimple,
        'viewPurchaseContractHead-modal': ViewPurchaseContractHeadModal,
        'purchaseContractHeadHis-modal': PurchaseContractHeadHisList,
        EsignFlowEdit,
        PurchaseOrderESignSelectModal,
        PurchaseOrderESignDownloadModal,
        ClContractEdit,
        fieldSelectModal,
        PurchaseEsignV3FlowEdit,
        EditFadadaSignTaskPurchaseModal,
        EditFadadaSignTaskPurchaseOneModal
    },
    data () {
        return {
            showEsignV3Page: false,
            showHisPage: false,
            showEsignPage: false,
            showContractLockPage: false,
            showFadadaOnePage: false,
            showFadadaPage: false,
            pageData: {
                businessType: 'contractSimple',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterContractCodeOrName`, '请输入合同编码或合同名称')
                    }
                ],
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'contract#purchaseContractHead:add',
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
                        authorityCode: 'contract#purchaseContractHead:export',
                        icon: 'download',
                        clickFn: this.handleExportXls
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                form: {
                    keyWord: '',
                    contractStatus: ''
                },
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        authorityCode: 'contract#purchaseContractHead:view',
                        clickFn: this.handleView
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        authorityCode: 'contract#purchaseContractHead:edit',
                        clickFn: this.handleEdit,
                        allow: this.showEditCondition
                    },
                    {
                        type: 'copy',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_updateVersion`, '更新版本'),
                        authorityCode: 'contract#purchaseContractHead:upgradeVersion',
                        clickFn: this.changeVersion,
                        allow: this.showChangeVersionCondition
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        authorityCode: 'contract#purchaseContractHead:delete',
                        clickFn: this.handleDelete,
                        allow: this.showDelCondition
                    },
                    {
                        type: '',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办'),
                        authorityCode: 'contract#purchaseContractHead:editHead',
                        clickFn: this.updateItem
                    },
                    {
                        type: 'history',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_numberOfHistory`, '历史记录'),
                        clickFn: this.handleHis,
                        allow: this.showHisCondition
                    },
                    {
                        type: 'database',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_initSignature`, '发起签署'),
                        clickFn: this.esignBtn,
                        allow: this.showSignCondition,
                        showCondition: this.showEsignBtn
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQL_1184c464`, '查看签署流程'),
                        clickFn: this.handleESignView,
                        allow: this.allowViewESign,
                        showCondition: this.showEsignBtn
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IKCPLAQI_7c722b1b`, '下载电签归档文件'),
                        clickFn: this.flowFileDownload,
                        allow: this.allowDownloadESign,
                        showCondition: this.showEsignBtn
                    },
                    {
                        type: 'database',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_LAWuCPW_d31d6579`, '归档（非电签）'),
                        authorityCode: 'contract#purchaseContractHead:archive',
                        clickFn: this.archive,
                        allow: this.showArchiveCondition
                    },
                    // {
                    //     type: 'close-circle',
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                    //     authorityCode: 'contract#purchaseContractHead:cancel',
                    //     clickFn: this.cancel,
                    //     allow: this.showCancelCondition
                    // },
                    {
                        type: 'copy',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'),
                        authorityCode: 'contract#purchaseContractHead:copy',
                        clickFn: this.handleCopy
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ],
                optColumnWidth: 300
            },
            url: {
                list: '/contract/purchaseContractHead/listSimple',
                add: '/contract/purchaseContractHead/add',
                delete: '/contract/purchaseContractHead/delete',
                editHead: '/contract/purchaseContractHead/editHead',
                copy: '/contract/purchaseContractHead/copy',
                deleteBatch: '/contract/purchaseContractHead/deleteBatch',
                importExcelUrl: 'contract/purchaseContractHead/importExcel',
                changeVersion: '/contract/purchaseContractHead/upgradeVersion',
                archive: '/contract/purchaseContractHead/archive',
                cancel: '/contract/purchaseContractHead/cancel',
                columns: 'purchaseContractHeadList',
                exportXlsUrl: '/contract/purchaseContractHead/exportXls?dataType=simple',
                flowFileDownload: '/esign/elsEsign/busSignFileDownload',
                clFlowFileDownload: '/contractLock/elsClContract/downloadArchiveByRelationId'
            },
            tabsList: [],
            viewEsignColumns: [
                {type: 'checkbox', width: 40, align: 'center'},
                {
                    type: 'seq',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),
                    width: 60,
                    align: 'center'
                },
                {
                    field: 'createTime',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAKI_2839c904`, '发起时间'),
                    align: 'center'
                },
                {
                    field: 'filesName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                    align: 'center'
                },
                {
                    field: 'sendStatus_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sendStatus`, '发送状态'),
                    align: 'center'
                },
                {
                    field: 'signerVindicateStatus_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSignatoryMaintainsTheState`, '签署人维护状态'),
                    align: 'center'
                },
                {
                    field: 'initiate_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_initiate`, '是否开启'),
                    align: 'center'
                },
                {
                    field: 'purchaseEsignStatus_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseEsignStatus`, '采方签署状态'),
                    align: 'center'
                },
                {
                    field: 'saleEsignStatus_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_saleEsignStatus`, '供方签署状态'),
                    align: 'center'
                },
                {
                    field: 'archiving_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_archiving`, '是否归档'),
                    align: 'center'
                },
                {
                    field: 'esignStatus_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_esignStatus`, '签署状态'),
                    align: 'center'
                },
                {
                    field: 'sendBack_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sendBack`, '是否退回'),
                    align: 'center'
                },
                {
                    field: 'grid_opration',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 120,
                    align: 'center',
                    slots: {default: 'grid_opration'}
                }
            ],
            viewEsignTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQL_1184c464`, '查看签署流程'),
            downloadEsignColumns: [
                {type: 'checkbox', width: 40, align: 'center'},
                {
                    type: 'seq',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),
                    width: 60,
                    align: 'center'
                },
                {
                    field: 'filesName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                    align: 'center'
                },
                {field: 'updateTime', title: this.$srmI18n(`${this.$getLangAccount()}#`, '归档日期'), align: 'center'},
                {
                    field: 'grid_opration',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 120,
                    align: 'center',
                    slots: {default: 'grid_opration'}
                }
            ],
            downloadEsignTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IKLAQI_3fb14732`, '下载归档文件')
        }
    },
    mounted () {
        // this.serachTabs('srmContractStatus', 'contractStatus')
        this.serachCountTabs('/contract/purchaseContractHead/countsimple')
    },
    methods: {
        formatTableData(data) {
            console.log('請求接口後格式化列表數據', data)
            data = data.map((item) => {
                if (item.totalTaxAmount === 0 || Math.abs(item.totalTaxAmount) > 0) {
                    item.totalTaxAmount = Number(item.totalTaxAmount).toFixed(2)
                }
                return item
            })
            return data;
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserContractHeader`, '采购方合同头'))
        },
        hideContractLockPage () {
            this.showContractLockPage = false
        },
        // 复制功能按钮
        handleCopy (row) {
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(row)
                }
            })
        },
        updateItem (row) {
            let item = {
                sourceUrl: '/account/elsSubAccount/list',
                params: {},
                columns: [
                    {
                        field: 'subAccount',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                        with: 150
                    },
                    {
                        field: 'realname',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'),
                        with: 150
                    }
                ]
            }
            this.currentRow = row
            this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        },
        fieldSelectOk (data) {
            let that = this
            let elsBusinessTransferHis = {
                businessType: 'contract',
                businessTransferNumber: that.currentRow.contractNumber,
                businessTransferName: '综合采购合同' + that.currentRow.contractNumber + ':' + that.currentRow.contractName,
                transferEntity: (that.currentRow.purchasePrincipal ? that.currentRow.purchasePrincipal : '') + '->' + data[0].realname
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办'),
                content: this.$srmI18n(`${this.$getLangAccount()}#`, '确认将此单据由 ' + that.currentRow.purchasePrincipal + ' 转办为') + ':' + data[0].subAccount + '_' + data[0].realname,
                onOk: function () {
                    debugger
                    that.currentRow.purchasePrincipal = data[0].subAccount + '_' + data[0].realname
                    that.$set(that.currentRow, 'elsBusinessTransferHis', elsBusinessTransferHis)
                    that.postUpdateData(that.url.editHead, that.currentRow)
                }
            })
        },
        // 复制数据请求接口
        copyData (row) {
            this.confirmLoading = true
            let param = {id: row.id}
            getAction(this.url.copy, param).then(res => {
                if (res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功！'))
                    this.searchEvent()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        showHisCondition (row) {
            if (row.contractStatus === '1') {
                return true
            } else {
                if (row.contractVersion <= 1) {
                    return true
                } else {
                    return false
                }
            }

        },
        showChangeVersionCondition (row) {
            if (row.startSign == '1' && row.endSign != '2') {
                return true
            }
            if (row.contractStatus == '4') {
                return false
            } else {
                return true
            }
        },
        showCancelCondition (row) {

            if (row.contractStatus === '5' && row.auditStatus === '2') {
                return false
            }
            if (row.contractStatus == '4' || row.contractStatus === '2') {
                return false
            } else {
                return true
            }
        },
        handleESignView (row) {
            let signType = row.signType
            if (signType == 'contractLock') {
                getAction('contractLock/elsClContract/list', {relationId: row.id}).then((res) => {
                    if (res.success) {
                        this.$router.push({
                            path: '/srm/contractLock/ClContractList',
                            query: {id: res.result.records[0].id, pageShow: 'true'}
                        })
                        this.visible = false
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }else if (signType == 'esignV3') {
                getAction('/esignv3/elsEsignV3Flow/list', {relationId: row.id}).then((res) => {
                    if (res.success) {
                        console.log('res', res.result.records[0].id)
                        this.$router.push({
                            path: '/srm/esignV3/purchase/PurchaseEsignV3List',
                            query: {id: res.result.records[0].id, pageShow: 'true'}
                        })
                        this.visible = false
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }else if (signType == 'fadada') {
                getAction('/electronsign/fadada/fadadaSignTaskPurchase/list', {busId: row.id}).then((res) => {
                    if (res.success) {
                        console.log('res', res.result.records[0].id)
                        this.$router.push({
                            path: '/srm/fadadaFasc/purchase/FadadaSignTaskPurchaseList',
                            query: {id: res.result.records[0].id, pageShow: 'true'}
                        })
                        this.visible = false
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            } else {
                getAction('esign/elsEsign/list', {relationId: row.id}).then((res) => {
                    if (res.success) {
                        this.$router.push({
                            path: '/srm/esign/EsignFlow',
                            query: {id: res.result.records[0].id, pageShow: 'true'}
                        })
                        this.visible = false
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }
        },
        showArchiveCondition (row) {
            if (row.contractStatus == '3' && row.sign !== '1') {
                return false
            } else {
                return true
            }
        },
        allowDownloadESign (row) {
            //是否启用电子签章为“是”，而且合同状态为“已归档”才可点击
            if (row.sign == '1' && row.contractStatus == '6') {
                return false
            }
            return true
        },
        showEsignBtn () {
            let companySet = Vue.ls.get(USER_COMPANYSET) || {}
            let arr = companySet.electronicSignature?.split(',') || []
            for (let r of arr) {
                if (r == 'contract') {
                    return true
                }
            }
            return false
        },
        flowFileDownload (row) {
            let signType = row.signType
            if (signType == 'contractLock') {
                getAction(this.url.clFlowFileDownload, {id: row.id, busType: 'contract'}).then(res => {
                    if (res.success) {
                        if (!res.result.archiving || res.result.archiving == '0') {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQLLLA_5e681f9d`, '签署流程未归档'))
                            return
                        }
                        getAction(res.result.remoteFilePath, {}, {
                            responseType: 'blob'
                        }).then(ref => {
                            let url = window.URL.createObjectURL(new Blob([ref]))
                            let link = document.createElement('a')
                            link.style.display = 'none'
                            link.href = url
                            link.setAttribute('download', res.result.documentName)
                            document.body.appendChild(link)
                            link.click()
                            document.body.removeChild(link) //下载完成移除元素
                            window.URL.revokeObjectURL(url) //释放掉blob对象
                        })
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }else if(signType == 'esignV3'){
                getAction('/esignv3/elsEsignV3Flow/signFileDownloadByRelationId', {id: row.id}).then(res => {
                    if(res.success){
                        console.log(res.result)
                        const result = res.result
                        const files = result.files[0]
                        getAction(files.downloadUrl, {}, {
                            responseType: 'blob'
                        }).then(res => {
                            let url = window.URL.createObjectURL(new Blob([res]))
                            let link = document.createElement('a')
                            link.style.display = 'none'
                            link.href = url
                            link.setAttribute('download', files.fileName)
                            document.body.appendChild(link)
                            link.click()
                            document.body.removeChild(link) //下载完成移除元素
                            window.URL.revokeObjectURL(url) //释放掉blob对象
                        })
                    }else{
                        this.$message.warning(res.message)
                    }
                })
            }else if(signType == 'fadada'){
                getAction('/electronsign/fadada/fadadaSignTaskPurchase/downloadArchiveByRelationId', {id: row.id, busType: 'contract'}).then(res => {
                    if (res.success) {
                        window.open(res.message, '_blank')
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            } else {
                getAction(this.url.flowFileDownload, {id: row.id, busType: 'contract'}).then(res => {
                    if (res.success) {
                        console.log(res.result)
                        window.open(res.result[0].fileUrl)
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }
        },
        showSignCondition (row) {
            if (row.sign == '1') {
                if (row.contractStatus === '3' && row.startSign !== '1') {
                    return false
                }
                return true
            } else {
                return true
            }
        },
        showEditCondition (row) {
            if ((row.contractStatus == '1' || row.contractStatus == '5') && row.auditStatus == '0' || row.auditStatus == '3') {
                return false
            } else {
                return true
            }
        },
        showDelCondition (row) {
            if (row.contractStatus == '1' && row.auditStatus == '0' || row.auditStatus == '3') {
                return false
            } else {
                return true
            }
        },
        handleHis (row) {
            this.currentEditRow = row
            this.showHisPage = true
        },
        handleChidCallback (){
            this.hideFadadaPage ()
        },
        hideFadadaPage () {            
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
            if (query.source == 'demand-pool') {
                this.$router.replace({path: this.$route.path})
            }
            this.showEditPage = false
            this.showDetailPage = false
            this.showFadadaOnePage = false
            this.showFadadaPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        hideHisPage () {
            this.showHisPage = false
        },
        submitCallBack (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack () {
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        changeVersion (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_rvHV_35833220`, '版本更新'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterVersionUpdatedContractCanModifiedNeedsApprovedSureUpdate`, '版本更新后可以修改合同但需重新审批，确认是否更新?'),
                onOk: function () {
                    that.postData(row)
                }
            })
        },
        postData (row) {
            this.$refs.listPage.confirmLoading = true
            getAction(this.url.changeVersion, {id: row.id}).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    // 变更版本后进入 编辑页
                    this.handleEdit(row)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        archive (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LA_bf351`, '归档'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToArchive`, '确认是否归档?'),
                onOk: function () {
                    that.postUpdateData(that.url.archive, row)
                }
            })
        },
        cancel (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        postUpdateData (url, row) {
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        allowViewESign (row) {
            //返回 false是有权限true是没有权限
            if (row.sign == '1' && row.startSign == '1') {
                return false
            }
            return true
        },
        esignBtn (row) {
            let companySet = Vue.ls.get(USER_COMPANYSET) || {}
            let signType = companySet.signType
            const param = {
                relationId: row.id,
                toElsAccount: row.toElsAccount,
                purchaseName: row.purchaseName,
                supplierName: row.supplierName,
                busType: 'contract',
                documentName: row.contractName,
                busNumber: row.contractNumber
            }
            if (signType == 'contractLock') {
                param.onlineSealed = '1'
                postAction('/contractLock/elsClContract/add', param).then((res) => {
                    if (res.success) {
                        this.currentEditRow = res.result
                        this.showContractLockPage = true
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }
            else if(signType == 'esignV3'){
                postAction('/esignv3/elsEsignV3Flow/add', param).then((res) => {
                    if (res.success) {
                        this.currentEditRow = res.result
                        this.showEsignV3Page = true
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }
            else if(signType == 'fadada'){
                let params = {
                    busId: row.id,
                    toElsAccount: row.toElsAccount,
                    purchaseName: row.purchaseName,
                    saleName: row.supplierName,
                    busType: 'contract',
                    signTaskSubject: row.contractName,
                    busNumber: row.contractNumber
                }
                params.reportUrl = REPORT_ADDRESS
                params.token = this.$ls.get('Access-Token')
                postAction('/electronsign/fadada/fadadaSignTaskPurchase/add', params).then((res) => {
                    if (res.success) {
                        this.currentEditRow = res.result
                        if(res.result.onlineSealed==='1'){
                            this.showFadadaPage = true
                        }else{
                            this.showFadadaOnePage = true
                        }
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }else {
                postAction('/esign/elsEsign/add', param).then((res) => {
                    if (res.success) {
                        this.currentEditRow = res.result
                        this.showEsignPage = true
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }
        },
        hideEsignPage () {
            this.showEsignPage = false
        },
        handleViewESign (row) {
            let queryParams = {relationId: row.id}
            this.$refs.viewESignList.open(queryParams, 'esign/elsEsign/list')
        },
        handleDownloadESign (row) {
            let queryParams = {relationId: row.id}
            this.$refs.downloadESignList.open(queryParams, 'esign/elsEsign/list')
        }
    }
}
</script>