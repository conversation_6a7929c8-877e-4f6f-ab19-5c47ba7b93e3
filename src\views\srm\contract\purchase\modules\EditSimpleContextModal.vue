<template>
  <a-modal
    v-drag    
    forceRender
    :visible="madalVisible"
    :title="modalTitle"
    :width="1146"
    @cancel="cancelEvent"
    @ok="editOk">
    <a-spin :spinning="confirmLoading">
      <!--<a-form-model
        :model="form"
        layout="inline">
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_SaleMassProdHeadList_projectName`, '项目名称')>
          <a-input
            placeholder="请输入项目名称"
            v-model="form.itemName"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button
            type="primary"
            @click="showParam">查看参数</a-button>
        </a-form-model-item>
      </a-form-model>-->
      <a-form-model 
        :model="form">
        <a-form-model-item
          label="">
          <j-editor
            v-if="madalVisible"
            v-model="form.itemContent"></j-editor>    
        </a-form-model-item>
      </a-form-model>
      <Query-Param-Modal
        ref="queryParamModal"/>
    </a-spin>
  </a-modal>
</template>
<script>
import JEditor from '@comp/els/JEditor'
import QueryParamModal from './QueryPurchaseContractParamModal'
import { cloneDeep } from 'lodash'

export default {
    name: 'SetLadderModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        JEditor,
        QueryParamModal
    },
    data () {
        return {
            modalTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_newProject`, '新建项目'),
            confirmLoading: false,
            madalVisible: false,
            form: {
                itemContent: '', id: '', itemName: '', itemType: '3', itemType_dictText: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_neTV_277b4001`, '合同条款'), sourceType: '', itemVersion: ''
            }
        }
    },
    mounted () {
    },
    methods: {
        open (row) {
            this.madalVisible = true
            if(row){
                this.modalTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editProject`, '编辑项目')
                var cloneRow = cloneDeep(row)
                this.form = cloneRow
            }else{
                this.modalTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_newProject`, '新建项目')
                this.form = {itemContent: '', id: '', itemName: '', itemType: '3', itemType_dictText: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_neTV_277b4001`, '合同条款'), itemVersion: '1'}
            }
        },
        goBack () {
            this.$emit('hide')
        },
        showParam (){
            this.$refs.queryParamModal.open({paramType: '1'})
        },
        editOk (){
            if(this.$parent.fieldContentItemOk(this.form)){
                this.madalVisible = false
            }
        },
        cancelEvent () {
            this.madalVisible = false
        }
    }
}
</script>
