<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 收货界面 -->
    <Purcase-Delivery-Head-Modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
    <!-- 查看页面 -->
    <ViewPurchaseDeliveryModal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :allowEdit="allowViewToEdit"
      @toEdit="handleEditFromViewPage"
      @hide="hideEditPage"
    />
    <logistics-timeline
      :show="logisticsVisible"
      :logisticsData="logisticsMsg"
      @logisticsHandleOk="logisticsHandleOk"
      @logisticsHandleCancel="handleCancel"
    ></logistics-timeline>
    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="printVisible"
      @ok="selectedPrintTemplate">
      <template slot="footer">
        <a-button
          key="back"
          @click="handlePrintCancel">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedPrintTemplate">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="printNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"/>
    </a-modal>

    <a-modal v-model="refundDialogVisible" title="退回原因" @ok="refund(refundDialogRow)">
        <div style="display:flex;align-items:center">
            <span style="flex:none;padding-right:16px">退回原因</span>
            <a-input v-model="refundReason"></a-input>
        </div>
    </a-modal>
  </div>
</template>

<script>
import PurcaseDeliveryHeadModal from './modules/PurchaseDeliveryHeadModal'
import ViewPurchaseDeliveryModal from './modules/ViewPurchaseDeliveryModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {REPORT_ADDRESS} from '@/utils/const'
import LogisticsTimeline from '@comp/LogisticsTimeline/LogisticsTimeline'
import {getAction, httpAction, postAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        PurcaseDeliveryHeadModal,
        ViewPurchaseDeliveryModal,
        LogisticsTimeline
    },
    data () {
        return {
            refundReason: '',
            refundDialogVisible: false,
            refundDialogRow: null,
            logisticsVisible: false,
            currentEditRow: {},
            logisticsMsg: {},
            printVisible: false,
            submitLoading: false,
            printStyle: 'single',
            printRow: {},
            printNumber: undefined,
            templateOpts: [],
            pageData: {
                businessType: 'purchaseDelivery',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDescOrOrderCode`, '请输入单据描述或发货单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliveryWay`, '配送方式'),
                        fieldName: 'deliveryWay',
                        dictCode: 'srmDeliveryWay',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelect`, '请选择')
                    }
                ],
                form: {
                    keyWord: '',
                    deliveryWay: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SMWWWWF_b44eb4e2`, '获取ERP数据'),
                        icon: 'arrow-down',
                        clickFn: this.getDataByErp,
                        authorityCode: 'delivery#purchaseDeliveryHead:getDeliveryHeadDataByErp'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'),
                        icon: 'arrow-up',
                        clickFn: this.pushDataToERP,
                        authorityCode: 'delivery#purchaseDeliveryHead:pushPurchaseDeliveryHead'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_zRfW_2ef2f473`, '批量打印'),
                        icon: 'download',
                        folded: false,
                        authorityCode: 'order#purchaseDeliveryHead:print',
                        clickFn: this.printBatchs
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'order#purchaseDeliveryHead:queryById'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receive`, '收货'),
                        clickFn: this.handleEdit,
                        allow: this.showEditCondition,
                        authorityCode: 'delivery#purchaseDeliveryHead:receive'
                    },
                    {
                        type: '',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsInfo`, '物流信息'),
                        clickFn: this.logisticsMessage,
                        allow: this.showLogisticsCondition,
                        authorityCode: 'logistics#trace:express'
                    },
                    {
                        type: '',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印'),
                        clickFn: this.serachTemplate,
                        authorityCode: 'order#purchaseDeliveryHead:print'
                    },
                    {
                        type: '',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_drawBack`, '退回'),
                        clickFn: this.openRefundReason,
                        allow: this.showRefund,
                        authorityCode: 'delivery#purchaseDeliveryHead:refund'
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ],
                optColumnWidth: 180
            },
            tabsList: [],
            url: {
                list: '/delivery/purchaseDeliveryHead/list',
                express: '/api/trace/express',
                columns: 'purchaseDeliveryHeadList',
                printBatchs: '/report/print/elsUReportPrint/downLoadPdfprint',
                printBatchsJimu: '/report/elsJmReportPrint/batchPrint',
                getDataByErpUrl: '/delivery/purchaseDeliveryHead/getDeliveryHeadDataByErp',
                refund: '/delivery/purchaseDeliveryHead/refund',
                pushDataToERPUrl: '/delivery/purchaseDeliveryHead/pushPurchaseDeliveryHead'
            }
        }
    },
    computed: {
        allowViewToEdit() {
            if(!!this.viewRow) return !this.showEditCondition(this.viewRow);
            return false;
        }
    },

    mounted () {
        // this.serachTabs('srmDeliveryStatus', 'deliveryStatus')
        this.serachCountTabs('/delivery/purchaseDeliveryHead/counts')
    },
    methods: {
        showLogisticsCondition (row) {
            if (this.btnInvalidAuth('express:viewArrival')) {
                return false
            }
            if (row.trackingNumber) {
                return false
            }
            return true
        },
        showEditCondition (row) {
            if (this.btnInvalidAuth('delivery#purchaseDeliveryHead:receive')) {
                return true
            }
            if (row.deliveryStatus == '1' || row.deliveryStatus == '2' || row.deliveryStatus == '4') {
                return false
            } else {
                return true
            }
        },
        showCloseCondition (row) {
            if (row.deliveryStatus == '1' || row.deliveryStatus == '2' || row.deliveryStatus == '4') {
                return false
            } else {
                return true
            }
        },
        showRefund (row) {
            if (row.deliveryStatus == '1' || row.deliveryStatus == '4') {
                return false
            } else {
                return true
            }
        },
        printBatchs () {
            this.currentPrintRow = {}
            let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            if (selectedRows.length <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPfWjc_3a1d557d`, '请选择需要打印的行'))
                return
            } else {
                this.nextOpt = true
                selectedRows.forEach((item, i) => {
                    if (parseFloat(item.deliveryStatus) == '0' || parseFloat(item.deliveryStatus) == '-1') {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_czELuIRL_4a848d`, '行状态为非已确认'))
                        this.nextOpt = false
                        return
                    }
                })
                this.currentPrintRow = selectedRows
                if (this.nextOpt) {
                    this.openBatchModal(selectedRows[0].busAccount)
                }
            }
        },
        openBatchModal (busAccount) {
            this.queryBatchPrintTemList(busAccount).then(res => {
                if (res.success) {
                    if (res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.id,
                                printId: item.printId,
                                printName: item.printName,
                                title: item.templateName,
                                printType: item.printType,
                                param: item.param
                            }
                        })
                        this.printNumber = ''
                        this.templateOpts = options
                        this.printStyle = 'batch'
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length === 1) {
                            this.printNumber = this.templateOpts[0].value
                            this.selectedPrintTemplate()
                        } else {
                            // 有多个模板先选择在新建
                            this.printVisible = true
                        }
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        queryBatchPrintTemList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: this.pageData.businessType}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        handlePrintCancel () {
            this.printVisible = false
        },
        serachTemplate (row) {
            this.printRow = row
            this.printStyle = 'single'
            this.openModal(row)
        },
        openModal (row) {
            this.queryPrintTemList(row.busAccount).then(res => {
                if (res.success) {
                    if (res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.id,
                                printId: item.printId,
                                printName: item.printName,
                                title: item.templateName,
                                printType: item.printType,
                                param: item.param
                            }
                        })
                        this.printNumber = ''
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length === 1) {
                            this.printNumber = this.templateOpts[0].value
                            this.selectedPrintTemplate()
                        } else {
                            // 有多个模板先选择在新建
                            this.printVisible = true
                        }
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        queryPrintTemList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: this.pageData.businessType}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        selectedPrintTemplate () {
            if (this.printStyle == 'batch') {
                if (this.printNumber) {
                    let that = this
                    that.submitLoading = true
                    let template = this.templateOpts.filter(item => {
                        return item.value == that.printNumber
                    })
                    let param = {
                        objectList: that.$refs.listPage.$refs.listGrid.getCheckboxRecords(),
                        parameter: template[0].param,
                        ureportName: template[0].printName
                    }
                    let urlPrint = this.url.printBatchs
                    if (template[0].printType == 'jimu') {
                        param = {
                            objectList: that.$refs.listPage.$refs.listGrid.getCheckboxRecords(),
                            parameter: template[0].param,
                            excelConfigId: template[0].printId
                        }
                        urlPrint = this.url.printBatchsJimu
                    }
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_zRfW_2ef2f473`, '批量打印'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLPmistFuGWWWfW_a8c51d60`, '确认将此选中单据导出PDF打印'),
                        onOk: function () {
                            //that.currentRow.tacticsObject = 'person'
                            that.downLoadPdfprint(urlPrint, param)
                        }
                    })
                }
            } else {
                if (this.printNumber) {
                    const that = this
                    this.submitLoading = true
                    let template = this.templateOpts.filter(item => {
                        return item.value == that.printNumber
                    })
                    let params = {
                        templateNumber: this.printNumber,
                        printId: template[0].printId,
                        printName: template[0].printName,
                        printType: template[0].printType,
                        param: template[0].param
                    }
                    that.printVisible = false
                    that.submitLoading = false
                    let rowItem = this.printRow
                    this.printRow = {}
                    let urlParam = ''
                    if (params.param) {
                        let json = JSON.parse(params.param)
                        Object.keys(json).forEach((key, i) => {
                            urlParam += '&' + key + '=' + rowItem[json[key]]
                        })
                    }
                    console.log(urlParam)
                    if (params.printType == 'ureport') {
                        const token = this.$ls.get('Access-Token')
                        //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
                        const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:' + params.printName + '&token=' + token + urlParam
                        window.open(url, '_blank')
                    }
                    if (params.printType == 'jimu') {
                        const token = this.$ls.get('Access-Token')
                        const url = REPORT_ADDRESS + '/els/report/jmreport/view/' + params.printId + '?token=' + token + urlParam
                        //const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.title+'&token=' + token+'&id='+rowItem.id
                        window.open(url, '_blank')
                    }
                }
            }
        },
        downLoadPdfprint (url, row) {
            this.$refs.listPage.confirmLoading = true
            postAction(url, row, {
                responseType: 'arraybuffer'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('target', '_blank')
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
                // this.searchEvent()
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        openRefundReason (row) {
            this.refundReason = ''
            this.refundDialogRow = row
            this.refundDialogVisible = true
        },
        refund (row) {
            let that = this

            if (that.refundReason == '') {
               this.$message.warning('请填写退回原因')
               return
            }

            that.$confirm({
                title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_drawBack`, '退回'),
                content: that.$srmI18n(`${that.$getLangAccount()}#i18n_field_KQYMisWF_f47908b7`, '是否退回选中数据?'),
                onOk: function () {
                    that.refundDialogVisible = false
                    row.refundReason = that.refundReason
                    that.postUpdateData(that.url.refund, row)
                }
            })
        },
        logisticsMessage (row) {
            if (row.trackingNumber == null) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsDocNoIseEmpty`, '物流单号为空'))
                return
            } else {
                if (row.trackingNumber.startsWith('SF') && row.receivePhone == '') {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '顺丰单号查询需要输入收货人手机号'))
                    return
                }
                this.$refs.listPage.confirmLoading = true
                getAction(this.url.express, {
                    'expressNumber': row.trackingNumber,
                    'phone': row.receivePhone
                }, 'get').then((res) => {
                    if (res.success) {
                        this.logisticsMsg = res.result
                        this.logisticsVisible = true
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.$refs.listPage.confirmLoading = false
                })
            }
        },
        hideDetailPage () {
            this.showDetailPage = false
        },
        logisticsHandleOk () {
            this.logisticsVisible = false
        },
        postUpdateData (url, row) {
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        handleCancel () {
            this.logisticsVisible = false
        }
    }
}
</script>
