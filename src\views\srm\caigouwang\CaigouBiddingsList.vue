<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      :url="url" />
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            pageData: {
                superQueryShow: false,
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称'),
                        fieldName: 'purchaserCompany',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称')
                    },
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subject`, '标题'),
                        fieldName: 'title',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subject`, '标题')
                    },
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectNumber`, '项目编号'),
                        fieldName: 'businessNo',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectNumber`, '项目编号')
                    }
                ],
                form: {
                    purchaserCompany: '',
                    title: '',
                    businessNo: ''
                },
                button: [{ label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns }],
                showOptColumn: true,
                optColumnList: [{ type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView }],
                isOrder: {
                    column: 'status',
                    order: 'asc'
                }
            },
            tabsList: [],
            url: {
                list: '/caigouwang/caigouBiddings/list',
                detail: '/enterprise/elsEnterpriseInfo/getLoginB2BBusinessUrl',
                columns: 'CaigouBiddingsList'
            }
        }
    },
    methods: {
        goToCreadOrder ({ servicePackId = '', priceType = '' }) {
            this.$router.push({
                name: 'addedApplicationRecharge',
                query: {
                    servicePackId,
                    priceType
                }
            })
        },
        handleOpen ({ id = '' }) {
            this.goToCreadOrder({ servicePackId: id, priceType: 0 })
        },
        handleView (row) {
            getAction(this.url.detail, { detailUrl: row.detailUrl }).then((res) => {
                console.log(res)
                if (res.success && res.result.success) {
                    window.open(res.result.data.data, '_blank')
                } else {
                    getAction('/ipaas/srmToIPaas/servicePackList', {serviceType: 'b2b'}).then(res => {
                        if(res.success) {
                            this.handleOpen({ id: res.result.data.data.records[0].id })
                        }else {
                            this.$message.warning(res.message)
                        }
                    })
                }
            })
        }
    }
}
</script>
