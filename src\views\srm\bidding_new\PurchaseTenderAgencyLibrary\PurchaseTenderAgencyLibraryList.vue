<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showDetailPage && ! showEditPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
    />
    <!-- 详情界面 -->
    <SupplierMasterDataDetail
      immediate
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <field-select-modal
      ref="fieldSelectModal" />

  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import SupplierMasterDataDetail from '@views/srm/supplier/SupplierMasterDataDetail'
import {getAction, postAction} from '@/api/manage'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import fieldSelectModal from '@comp/template/fieldSelectModal'

export default {
    mixins: [ListMixin],
    components: {
        SupplierMasterDataDetail,
        fieldSelectModal
    },
    data () {
        return {
            showEditPage: false,
            templateOpts: [],
            submitLoading: false,
            pageData: {
                businessType: 'supplierMasterData',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ovtRRL_30d35c90`, '代理机构名称')
                    }
                ],
                form: {
                    keyWord: '',
                    name: ''
                },
                button: [
                    {
                        allow: ()=> {
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'tender#purchaseTenderAgencyLibraryHead:add',
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'tender#purchaseTenderAgencyLibraryHead:queryById'},
                    {type: 'enable', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enable`, '启用'), clickFn: this.handleEnable, authorityCode: 'tender#purchaseTenderAgencyLibraryHead:enable', allow: this.allowEnable},
                    {type: 'disable', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_frosted`, '冻结'), clickFn: this.handleDisable, authorityCode: 'tender#purchaseTenderAgencyLibraryHead:disable', allow: this.allowDisable},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'tender#purchaseTenderAgencyLibraryHead:delete', clickFn: this.handleDelete, allow: this.allowDelete},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord},
                    {type: 'situaion', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dIREVc_e9cefa2a`, '项目执行情况'), clickFn: this.handleSituaion}
                ],
                isOrder: {
                    column: 'create_time',
                    order: 'desc'
                }
            },
            tabsList: [],
            agencyName: '',
            url: {
                list: '/tender/library/purchaseTenderAgencyLibraryHead/list',
                add: '/tender/library/purchaseTenderAgencyLibraryHead/add',
                delete: '/tender/library/purchaseTenderAgencyLibraryHead/delete',
                columns: 'PurchaseTenderAgencyLibraryHeadList',
                enable: '/tender/library/purchaseTenderAgencyLibraryHead/enable',
                disable: '/tender/library/purchaseTenderAgencyLibraryHead/freeze'
            }
        }
    },
    mounted () {
        this.serachTabs('srmSupplierStatus', 'supplierStatus')
        this.serachCountTabs('/tender/library/purchaseTenderAgencyLibraryHead/counts')
    },
    methods: {
        // 跳转项目执行情况页面
        handleSituaion (row){
            console.log('row', row)
            let params = { linkFilter: true, agencyElsAccount: row.supplierAccount, selectResponse: 1, ifToList: false}
            this.$router.push({ path: '/ProjectSituation/ProjectSituationList', query: params })
        },
        allowDelete (row) {
            return !['0', '3'].includes(row.status)
        },
        allowEnable (row) {
            return !['0', '3'].includes(row.status)
        },
        allowDisable (row) {
            return row.status !== '2'
        },
        handleAdd () {
            let url = '/supplier/supplierMaster/contactList'
            let columns = [
                { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号') },
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称') },
                { field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码') }
            ]
            this.$refs.fieldSelectModal.open(url, {
                frozenFunctionValue: 'frozenFunctionValue'
            }, columns, 'single')
        },
        handleView (row) {
            //打开详情页
            this.showDetailPage = false
            this.$nextTick(() => {
                this.currentEditRow = {
                    ...row,
                    id: row.supplierId
                }
                this.showDetailPage = true
            })
        },
        fieldSelectOk (data) {
            let {templateName, templateNumber, templateVersion, templateAccount, id, supplierName, toElsAccount, supplierCode} = data[0]
            let params = {
                templateName,
                templateNumber,
                templateVersion,
                templateAccount,
                supplierId: id,
                supplierEnterpriseName: supplierName,
                supplierAccount: toElsAccount,
                supplierNumber: supplierCode
            }
            postAction(this.url.add, params).then(res => {
                let type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.modalFormOk()
                }
            })
        },
        handleEnable (row) {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmToEnable`, '确认启用'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enableSelecteData`, '是否启用选中数据?'),
                onOk: () => {
                    this.loading = true
                    postAction(this.url.enable, row).then(res => {
                        if(res.success) {
                            this.$message.success(res.message)
                            this.$refs.listPage.loadData() // 刷新页面
                        }else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.loading = false
                    })
                }
            })
        },
        handleDisable (row) {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmTodisable`, '确认禁用'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disableSelecteData`, '是否禁用选中数据?'),
                onOk: () => {
                    this.loading = true
                    postAction(this.url.disable, row).then(res => {
                        if(res.success) {
                            this.$message.success(res.message)
                            this.$refs.listPage.loadData() // 刷新页面
                        }else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.loading = false
                    })
                }
            })
        }
    }
}
</script>