import {textColor} from '@comp/chart/widget/utils/theme.js'
// 仪表盘
export default {
    backgroundColor: 'rgba(255, 255, 255, 0)',
    title: {
        show: true,
        text: '',
        top: 10,
        left: 'center',
        textStyle: {
            color: textColor,
            fontWeight: 'normal',
            fontSize: 18
        },
        subtext: '',
        subtextStyle: {
            color: textColor,
            fontWeight: 'normal',
            fontSize: 16
        }
    },
    tooltip: {
        show: true,
        textStyle: {
            color: '#000000',
            fontWeight: 'normal',
            fontSize: 14
        }
    },
    series: [
        {
            type: 'gauge',
            min: 0,
            max: 100,
            title: {
                show: false
            },
            progress: { // 进度
                width: 6,
                show: true
            },
            itemStyle: { // 指针跟进度颜色
                color: '#83bff6'
            },
            axisLine: { // 轴线
                show: true,
                lineStyle: {
                    width: 6,
                    color: [[1, '#E6EBF8']],
                    customColor: '#E6EBF8' // 自定义的属性，方便与v-model绑定
                }
            },
            splitLine: { // 分割线
                show: true,
                lineStyle: {
                    color: textColor
                }
            },
            axisTick: {  // 度线
                show: true,
                lineStyle: {
                    color: textColor
                }
            },
            axisLabel: { // 标签 - 刻度值
                show: true,
                fontWeight: 'normal',
                fontSize: 12,
                color: textColor
            },
            detail: { // 值的属性
                valueAnimation: true,
                formatter: '',
                color: textColor,
                fontSize: 30,
                fontWeight: 'normal',
                offsetCenter: ['0%', '65%'],
                customOffsetCenter: [0, 65] // 自定义
            },
            data: [
                {
                    name: '',
                    value: 0
                }
            ]
        }
    ]
}