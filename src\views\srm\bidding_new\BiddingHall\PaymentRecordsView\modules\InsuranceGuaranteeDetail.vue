<template>
  <div class="insurance-guarantee-edit">
    <a-spin :spinning="confirmLoading">
      <div class="btns">
        <a-button
          @click="returnPage">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </div>
      <business-layout
        v-if="layoutShow"
        :ref="businessRefName"
        :current-edit-row="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :fromSourceData="fromSourceData"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    inject: ['tenderCurrentRow', 'subpackageId'],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        } 
    },
    data () {
        return {
            layoutShow: false,
            pageStatus: 'detail',
            confirmLoading: false,
            businessRefName: 'businessRef',
            externalToolBar: {},
            // pageHeaderButtons: [
            //     {
            //         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
            //         key: 'goBack',
            //         click: this.returnPage
            //     }
            // ],
            remoteJsFilePath: '',
            fromSourceData: {},
            url: {
                queryById: '/tender/supplier/purchaseTenderProjectMarginHead/queryItemById',
                queryProjectInfo: '/tender/purchaseTenderProjectHead/queryProjectInfo',
                add: '/tender/supplier/purchaseTenderProjectMarginHead/addMargin',
                edit: '/tender/supplier/purchaseTenderProjectMarginHead/editMargin',
                submit: '/tender/supplier/purchaseTenderProjectMarginHead/submitMargin'
            }
        }
    },
    mounted () {
        this.getBusinessTemplate()
    },
    methods: {
        // 获取业务模板信息
        getBusinessTemplate () {
            let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'tenderProjectMargin'}
            this.confirmLoading = true
            getAction('/template/templateHead/getListByType', params).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                templateNumber: item.templateNumber,
                                templateName: item.templateName,
                                templateVersion: item.templateVersion,
                                templateAccount: item.elsAccount
                            }
                        })
                        this.currentEditRow = Object.assign({}, this.currentEditRow, options[0])
                        this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/sale_tenderProjectMargin_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
                        this.getData()
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
                this.confirmLoading = false
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentDTOList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentDTOList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentDTOList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    },
                    {
                        groupCode: 'attachmentDTOList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log(pageConfig, resultData)
            // 保函缴纳不用显示这些指定
            const noField = ['payAccount', 'payName', 'bankName', 'payType']
            let formFields = []
            pageConfig.groups[0].formFields.forEach(item => { // 因为保函保单和其他方式的缴纳原型不一样对此做数据筛查
                if (noField.indexOf(item.fieldName) == -1) {
                    if (item.fieldName == 'amount') {
                        item.fieldLabel = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sxHfWjW_ef47d502`, '保函金额（元）')
                    }
                    formFields.push(item)
                }
            })
            pageConfig.groups[0].formFields = formFields
            pageConfig.groups[1]['extend'] = {
                optColumnList: [
                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                    { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                ]
            }
            if(pageConfig.groups[1].loadData){
                pageConfig.groups[1].loadData.forEach(item=>{
                    let fileTypeList = this.formatType
                    fileTypeList = fileTypeList.filter(target=>item.fileType == target.value)
                    console.log(fileTypeList)
                    item.fileType_dictText = fileTypeList[0].text
                })
            }
            
        },
        async getFileTypeDict () {
            let postData1 = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'tenderMarginFileType'
            }
            await ajaxFindDictItems(postData1).then(res => {
                console.log('res', res)
                if (res.success) {
                // 获取文件类型的字典值
                    this.formatType = res.result || []
                }
            })
            console.log(this.formatType)
        },
        preViewEvent (Vue, row){
            row.subpackageId = this.subpackageId()
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            row.subpackageId = this.subpackageId()
            const fileName = row.fileName
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        checkItemSelectOk (data) {
            console.log(data)
        },
        returnPage () { // 返回
            this.$parent.currentEditRow = {}
            this.$parent.inShowDetailPage = false
        },
        getData () {
            let url = ''
            let params = {}
            let cb = null
            if (this.currentEditRow.id) {
                url = this.url.queryById
                params = {id: this.currentEditRow.id}
                cb = (data = {}) => {
                    this.fromSourceData = data
                }
            } else {
                url = this.url.queryProjectInfo
                params = {subpackageId: this.subpackageId()}
                cb = (data = {}) => {
                    this.fromSourceData = data
                    let {realname, elsAccount} = this.$ls.get(USER_INFO)
                    this.fromSourceData['supplierName'] = this.$ls.get(USER_COMPANYSET).companyName
                    this.fromSourceData['supplierAccount'] = elsAccount
                }
            }
            getAction(url, params).then(res => {
                if (res.success) {
                    if (res.result) {
                        cb(res.result)
                    }
                }
            }).finally(() => {
                this.layoutShow = true
                this.confirmLoading = false
            })
        }
    },
    created (){
        this.getFileTypeDict()
    }
}
</script>

<style lang="less" scoped>
.insurance-guarantee-edit {
  :deep(.edit-page){
    margin-top: 10px;
  }
  :deep(.page-header){
    .ant-col-6 {
        display: none;
    }
    .ant-col-18 {
        width: 100%;
    }
  }
}
:deep(.main){
    position:relative
  }
.btns {
    position: absolute;
    right: 15px;
    top: -35px;
}
</style>