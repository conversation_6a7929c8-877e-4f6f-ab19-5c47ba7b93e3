/*
 * @Author: your name
 * @Date: 2021-07-22 10:32:08
 * @LastEditTime: 2021-07-27 19:13:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\i18n\index.js
 */
import Vue from 'vue'
import VueI18n from 'vue-i18n'

import Storage from 'vue-ls'
import config from '@/basicSettings/defaultSettings'

import zh_CN from 'ant-design-vue/lib/locale-provider/zh_CN'
import en_US from 'ant-design-vue/lib/locale-provider/en_US'

import vxeTable_zhCN from 'vxe-table/lib/locale/lang/zh-CN'
import vxeTable_enUS from 'vxe-table/lib/locale/lang/en-US'

import { DEFAULT_LANG } from '@/store/mutation-types'


Vue.use(VueI18n)
Vue.use(Storage, config.storageOptions)

const i18n = new VueI18n({
    locale: Vue.ls.get(DEFAULT_LANG) || 'zh',
    messages: {
        'zh': {
            ...zh_CN,
            ...vxeTable_zhCN
        },
        'en': {
            ...en_US,
            ...vxeTable_enUS
        }
    }
})

export default i18n
