<template>
  <div class="els-page-comtainer tabs-collapase-template">
    <div
      class="table-page-search-wrapper"
      style="padding:8px">
      <a-spin :spinning="confirmLoading">
        <a-tabs
          :activeKey="activeIndex"
          @change="changeTabs"
          :tabBarStyle="isFixedTabBar ? tabBarStyle : null"
          type="card">
          <a-tab-pane
            forceRender
            v-for="(tab, index) in pageData.panels"
            :tab="tab.title"
            :key="'tab_panel_'+index">
            <a-form-model
              v-if="tab.content.type=='form'"
              :ref="tab.content.ref"
              :model="tab.content.form"
              :label-col="labelCol"
              :rules="tab.content.validRules"
              :wrapper-col="wrapperCol"
              layout="inline">
              <a-collapse
                v-if="tab.content.list[0].type == 'collapse'"
                v-model="activeCollapseKey"
                expandIconPosition="right">
                <a-collapse-panel
                  v-for="(collapse, col) in tab.content.list"
                  :key="'collapse_panel_'+col"
                  :header="collapse.title"
                >
                  <a-row :gutter="24">
                    <a-col
                      v-for="(item, i) in collapse.formList"
                      :key="'field_'+i"
                      :span="12">
                      <a-form-model-item
                        :prop="item.fieldName"
                        v-if="item.type==='input'"
                        :label="item.label">
                        <a-input
                          :disabled="item.disabled || false"
                          v-model="tab.content.form[item.fieldName]"
                          :placeholder="item.placeholder" />
                      </a-form-model-item>
                      <a-form-model-item
                        :prop="item.fieldName"
                        v-if="item.type==='number'"
                        :label="item.label">
                        <a-input-number
                          style="width:100%"
                          :disabled="item.disabled || false"
                          v-model="tab.content.form[item.fieldName]"
                          :placeholder="item.placeholder" />
                      </a-form-model-item>
                      <a-form-model-item
                        :prop="item.fieldName"
                        v-if="item.type==='selectModal'"
                        :label="item.label">
                        <a-input
                          readOnly
                          :disabled="item.disabled || false"
                          v-model="tab.content.form[item.fieldName]"
                          :placeholder="item.placeholder"
                          @click="e => openSelectModal(e, item)">
                          <a-icon
                            slot="suffix"
                            type="close-circle"
                            @click="(e) => clearInputValue(item)"></a-icon>
                        </a-input>
                      </a-form-model-item>
                      <a-form-model-item
                        v-if="item.type==='numberGroup'"
                        :label="item.label">
                        <a-input-number
                          v-for="(field,fieldIndex) in item.fieldName"
                          :style="{width: (1/item.fieldName.length*100) + '%'}"
                          :key="'number_group_'+fieldIndex"
                          :disabled="item.disabled || false"
                          v-model="tab.content.form[field]" />
                      </a-form-model-item>
                      <a-form-model-item
                        :prop="item.fieldName"
                        v-if="item.type==='select'"
                        :label="item.label">
                        <m-select
                          :mode="item.mode"
                          :disabled="item.disabled"
                          v-model="tab.content.form[item.fieldName]"
                          :placeholder="item.placeholder"
                          :show-opt-value="item.showOptValue"
                          :options="item.options"
                          :source-url="item.sourceUrl"
                          :source-map="item.sourceMap"
                          :dict-code="item.dictCode"
                          :noEmptyOpt="item.noEmptyOpt"
                          @change="item.changeEvent" />
                      </a-form-model-item>
                      <a-form-model-item
                        :prop="item.fieldName"
                        :label="item.label"
                        v-else-if="item.type==='dateRange'">
                        <a-range-picker
                          :show-time="item.showTime"
                          @change="(dates) => changeDateRangePicker(dates, index, item)"
                          :disabled="item.disabled || false"
                          :value="tab.content.form[item.fieldName]"
                          :valueFormat="item.valueFormat || 'YYYY-MM-DD'" />
                      </a-form-model-item>
                      <a-form-model-item
                        :prop="item.fieldName"
                        :label="item.label"
                        v-else-if="item.type==='date'">
                        <a-date-picker
                          :show-time="item.showTime"
                          :disabled="item.disabled || false"
                          :valueFormat="item.valueFormat || 'YYYY-MM-DD'"
                          v-model="tab.content.form[item.fieldName]" />
                      </a-form-model-item>
                      <a-form-model-item
                        :prop="item.fieldName"
                        :label="item.label"
                        v-else-if="item.type==='addressCascader'">
                        <a-cascader
                          :options="areas"
                          :placeholder="item.placeholder"
                          :value="tab.content.form[item.fieldName]"
                          @change="(val) => onChangeArea(val, index, item)"
                        />
                      </a-form-model-item>
                      <a-form-model-item
                        class="textarea-form-item"
                        :prop="item.fieldName"
                        :label="item.label"
                        v-else-if="item.type==='textarea'">
                        <a-textarea 
                          :disabled="item.disabled || false"
                          v-model="tab.content.form[item.fieldName]"
                          :placeholder="item.placeholder" />
                      </a-form-model-item>
                      <a-form-model-item
                        v-else-if="item.type==='switch'"
                        :label="item.label"
                        :prop="item.fieldName"
                      >
                        <m-switch
                          :disabled="item.disabled"
                          :close-value="item.closeValue"
                          :open-value="item.openValue"
                          :checked-children="item.checkedChildren"
                          :un-checked-children="item.unCheckedChildren"
                          v-model="tab.content.form[item.fieldName]"
                        />
                      </a-form-model-item>
                      <a-form-model-item
                        :prop="item.fieldName"
                        :label="item.label"
                        v-else-if="item.type==='timePicker'">
                        <a-time-picker
                          style="width:100%"
                          valueFormat="HH:mm:ss"
                          :disabled="item.disabled || false"
                          v-model="tab.content.form[item.fieldName]" />
                      </a-form-model-item>
                      <a-form-model-item
                        :prop="item.fieldName"
                        :label="item.label"
                        v-else-if="item.type==='treeSelect'">
                        <m-tree-select
                          allowClear
                          v-model="tab.content.form[item.fieldName]"
                          :sourceUrl="item.sourceUrl"
                          :sourceMap="item.sourceMap"
                          :titleMap="item.titleMap"
                          :valueMap="item.valueMap"
                          :showEmptyNode="item.showEmptyNode"
                          :placeholder="item.placeholder" />
                      </a-form-model-item>
                    </a-col>
                  </a-row>
                </a-collapse-panel>
              </a-collapse>
            </a-form-model>
            <vxe-grid
              v-else-if="tab.content.type=='table'"
              border
              resizable
              auto-resize
              show-overflow
              highlight-hover-row
              :columns="tab.content.columns"
              :edit-rules="tab.content.validRules"
              :toolbar="{slots: {buttons: 'toolbar_buttons'}}"
              :ref="tab.content.ref"
              :height="tableHeight"
              size="small"
              align="center"
              :edit-config="{trigger: 'click', mode: 'cell', activeMethod: tab.content.activeMethod}">
              <template slot="empty">
                <a-empty />
              </template>
              <template v-slot:cell_edit_modal="{ row, column }">
                <a
                  @click="openCellEditModal(row, column)"
                  v-html="row[column.property] || $srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择') "></a>
              </template>
              <template v-slot:toolbar_buttons>
                <div style="margin-top:-16px;text-align:right">
                  <a-button
                    v-for="(btn, key) in tab.button"
                    v-show="btn.showCondition ? btn.showCondition(btn.id) : true"
                    :key="'btn_'+key"
                    style="margin-left:8px"
                    @click="btn.clickFn"
                    :type="btn.type || ''">
                    {{ btn.title }}
                  </a-button>
                </div>
              </template>
            </vxe-grid>
            <upload-list
              :ref="tab.content.ref"
              v-else-if="tab.content.type=='upload'"></upload-list>
          </a-tab-pane>
          <a-button
            v-for="(item, key) in pageData.publicBtn"
            v-show="item.showCondition ? item.showCondition(item.id) : true"
            :type="item.type || 'default'"
            style="margin-left:8px"
            slot="tabBarExtraContent"
            @click="item.clickFn"
            :key="'public_btn_' + key">{{ item.title }}</a-button>
        </a-tabs>
      </a-spin>
    </div>
    <field-select-modal
      ref="fieldSelectModal">
    </field-select-modal>  
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import { ajaxFindDictItems } from '@/api/api'
import { Empty, TimePicker } from 'ant-design-vue'
import { areas } from '@/store/area'
import fieldSelectModal from '../fieldSelectModal'
import uploadList from '@comp/uploadList/uploadList'
import MTreeSelect from '@comp/treeSelect/mTreeSelect'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
export default {
    name: 'TabsCollapsePage',
    props: {
        pageData: { //页面数据
            type: Object,
            default: null
        },
        url: {  //后台接口
            type: Object,
            default: null
        }
    },
    components: {
        AEmpty: Empty,
        fieldSelectModal,
        uploadList,
        MTreeSelect,
        ATimePicker: TimePicker
    },
    data () {
        return {
            activeIndex: 'tab_panel_0',
            voucherId: '',
            voucherDetail: {},
            areas: areas,
            // dictionary: {},
            confirmLoading: false,
            isFixedTabBar: false,
            tabHeaderHeight: 56,
            activeCollapseKey: '',
            formList: [],
            labelCol: {
                xs: { span: 24 },
                sm: { span: 6 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 18 }
            },
            currentSelectModal: {},
            oldFormField: {}
        }
    },
    computed: {
        tabIndex () {
            return this.activeIndex.split('_')[2]
        },
        tabButton () {
            return this.pageData.panels[this.tabIndex].button
        },
        gridRef () {
            return this.pageData.panels[this.tabIndex].content.ref
        },
        tabBarWidth () {
            if(this.$store.state.app.sidebar.opened) {
                return '200px'
            }else {
                return '80px'
            }
        },
        tabBarStyle () {
            return {
                position: 'fixed',
                backgroundColor: '#fff',
                padding: '4px 16px 0 16px',
                top: 0,
                right: 0,
                width: 'calc(100% - ' + this.tabBarWidth + ')',
                borderBottom: '1px solid #ccc',
                zIndex: 99
            }
        },
        tableHeight () {
            return document.documentElement.clientHeight - 210
        }
    },
    mounted () {
        this.computedCollapseKey()
        // this.getMultipleDict()
        this.initDictData()
        window.addEventListener('scroll', this.handleScroll)
    },
    destroyed () {
        window.removeEventListener('scroll', this.handleScroll)
    },
    methods: {
        handleScroll () {
            let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 100 + this.tabHeaderHeight) {
                this.tabHeaderHeight = 0
                this.isFixedTabBar = true
            }else {
                this.tabHeaderHeight = 56
                this.isFixedTabBar = false
            }
        },
        computedCollapseKey () {
            let keyList = []
            for(let i = 0; i < this.pageData.panels.length; i++) {
                let panel = this.pageData.panels[i]
                if(panel.content.type == 'form') {
                    this.formList.push({...panel.content.form})
                    for(let j = 0; j < panel.content.list.length; j++) {
                        keyList.push('collapse_panel_'+j)
                    }
                }
            }
            this.activeCollapseKey = keyList
        },
        changeTabs (key) {
            let that = this
            let type = this.pageData.panels[this.tabIndex].content.type
            let validRules = this.pageData.panels[this.tabIndex].content.validRules
            let preIndex = key.split('_')[2]
            let preType = this.pageData.panels[preIndex].content.type
            if(preType == 'table') {
                this.$refs[this.pageData.panels[preIndex].content.ref][0].refreshScroll()
            }
            if(type == 'form' && validRules) {
                this.$refs[this.gridRef][0].validate(valid => {
                    if(valid) {
                        that.activeIndex = key
                    }else {
                        return false
                    }
                })
            }else {
                this.activeIndex = key
            }
        },
        initDictData () {
            let that = this
            //根据字典Code, 初始化字典数组
            this.pageData.panels.forEach( item => {
                if(item.content.type === 'table') {
                    item.content.columns.forEach(element => {
                        if(element.editRender && element.editRender.dictCode) {
                            let postData = {
                                busAccount: that.$ls.get(USER_ELS_ACCOUNT),
                                dictCode: element.editRender.dictCode
                            }
                            ajaxFindDictItems(postData).then((res) => {
                                let options = res.result
                                if (res.success) {
                                    element.editRender.options = options.map(option => {
                                        return {label: option.title, value: option.value}
                                    })
                                    that.$forceUpdate()
                                }
                            })
                        }
                    })
                }
            })
        },
        goBack () {
            this.$emit('goBack')
        },
        //新增行
        addRow (rows) {
            if(rows) {
                this.$refs[this.gridRef][0].insert(rows)
            }else {
                this.$refs[this.gridRef][0].insert([{}])
            }
        },
        // 删除选中行
        deleteRow () {
            let grid = this.$refs[this.gridRef][0]
            let selectedData = grid.getCheckboxRecords()
            if(!selectedData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDeleteRowTips`, '请选择删除行'))
            }
            grid.removeCheckboxRow()
        },
        //获取表格数据
        getCurrentTableData () {
            let { fullData } = this.$refs[this.gridRef][0].getTableData()
            return fullData
        },
        //提交数据
        postData () {
            let url = this.url.add
            let params = this.getParamsData()
            if (this.voucherId) {
                url = this.url.edit
            }
            this.confirmLoading = true
            postAction(url, params).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.refreshList()
                    if (!this.voucherId) {
                        this.setParamsData(res.result.id)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 保存操作
        saveEvent () {
            let that =this
            //表格检验
            let flag = true
            this.confirmLoading = true
            this.pageData.panels.forEach((panel, index) => {
                let type = panel.content.type
                if(type == 'form') {
                    that.$refs[panel.content.ref][0].validate(valid => {
                        if(!valid) {
                            flag = false
                            that.activeIndex = 'tab_panel_' + index
                            that.confirmLoading = false
                            return
                        }
                    })
                }else if(type == 'table') {
                    let rows = that.$refs[panel.content.ref][0].getTableData().fullData
                    that.$refs[panel.content.ref][0].validate(rows, valid => {
                        if(valid) {
                            flag = false
                            that.activeIndex = 'tab_panel_' + index
                            that.confirmLoading = false
                            return
                        }
                    })
                }
            })
            window.setTimeout(() => {
                if(flag) {
                    this.postData()
                }
            }, 10)
        },
        //保存select旧值
        setOldFormField (fieldName) {
            let tabIndex = this.tabIndex
            this.oldFormField[fieldName] = this.pageData.panels[tabIndex].content.form[fieldName]
        },
        //设置单个属性方法
        setFormFieldValue (key, value, index) {
            let addKey = {}
            if(key && value) {
                addKey[key] = value
            }
            let tabIndex = this.tabIndex
            if(index || index === 0) {
                tabIndex = index
            }
            Object.assign(this.pageData.panels[tabIndex].content.form, addKey)
        },
        //批量设置form属性方法
        setFormFieldsValue (obj, index) {
            let params = { ...obj }
            let tabIndex = this.tabIndex
            if(index || index === 0) {
                tabIndex = index
            }
            Object.assign(this.pageData.panels[tabIndex].content.form, params)
        },
        //设置表单禁用
        setFormFieldDisabled (fileds) {
            this.pageData.panels.forEach( item => {
                if(item.content.type === 'form') {
                    item.content.list.forEach(li => {
                        if(li.type == 'collapse') {
                            li.formList.forEach(form => {
                                if(fileds) {
                                    if(fileds.includes(form.fieldName)) {
                                        form.disabled = true
                                    }
                                }else {
                                    form.disabled = true
                                }
                            })
                        }else {
                            if(fileds) {
                                if(fileds.includes(li.fieldName)) {
                                    li.disabled = true
                                }
                            }else {
                                li.disabled = true
                            }
                        }
                    })
                }
            })
            this.$forceUpdate()
        },
        // 获取界面数据
        getParamsData () {
            let that = this
            let params = {}
            this.pageData.panels.forEach(panel => {
                if(panel.content.type == 'table') {
                    params[panel.content.ref] = that.$refs[panel.content.ref][0].getTableData().fullData
                }else if(panel.content.type == 'form') {
                    Object.assign(params, panel.content.form)
                }
            })
            return params
        },
        //获取
        requestTabsData () {
            let url = this.url.detail
            let that = this
            this.confirmLoading = true
            getAction(url, {id: this.voucherId}).then(res => {
                let data = res.result
                this.pageData.panels.forEach(panel => {
                    if(panel.content.type == 'form') {
                        panel.content.form = Object.assign({}, panel.content.form, data)
                    }else if(panel.content.type == 'table') {
                        that.$refs[panel.content.ref][0].loadData(data[panel.content.ref])
                    }else if(panel.content.type == 'upload') {
                        panel.content.relatedId = data[panel.content.relatedIdMap]
                    }
                })
                this.handleData()
                this.getUploadListData()
                this.confirmLoading = false
                this.requestAfter(data)
            })
        },
        requestAfter (data) {
            this.$parent.requestAfter(data)
        },
        // 设置绑定数据
        setParamsData (id) {
            this.activeIndex = 'tab_panel_0'
            this.voucherId = id
            this.requestTabsData()
        },
        // 日期区间事件
        changeDateRangePicker (dates, index, item) {
            this.pageData.panels[index].content.form[item.start] = dates[0]
            this.pageData.panels[index].content.form[item.end] = dates[1]
            this.pageData.panels[index].content.form[item.fieldName] = [dates[0], dates[1]]
            this.$forceUpdate()
        },
        // 设置数据
        handleData () {
            this.pageData.panels.forEach(panel => {
                if(panel.content.type == 'form') {
                    panel.content.list.forEach(li => {
                        li.formList.forEach(form => {
                            if(form.type == 'addressCascader') {
                                if(panel.content.form[form.state] && panel.content.form[form.city] && panel.content.form[form.area]) {
                                    panel.content.form[form.fieldName] = [panel.content.form[form.state], panel.content.form[form.city], panel.content.form[form.area]]
                                }else {
                                    panel.content.form[form.fieldName] = []
                                }
                            }else if(form.type == 'dateRange') {
                                if(panel.content.form[form.start] && panel.content.form[form.end]) {
                                    panel.content.form[form.fieldName] = [panel.content.form[form.start], panel.content.form[form.end]]
                                }else {
                                    panel.content.form[form.fieldName] = []
                                }
                            }
                        })
                    })
                }
            })
        },
        // 地址切换回调
        onChangeArea (val, index, item) {
            this.pageData.panels[index].content.form[item.state] = val[0]
            this.pageData.panels[index].content.form[item.city] = val[1]
            this.pageData.panels[index].content.form[item.area] = val[2]
            this.pageData.panels[index].content.form[item.fieldName] = [val[0], val[1], val[2]]
            this.$forceUpdate()
        },
        //打开行明细弹窗
        openCellEditModal (row, column) {
            this.$parent['edit_'+column.property](row)
        },
        //打开表单选择弹窗
        openSelectModal (e, item) {
            this.currentSelectModal = item
            this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        },
        //清空弹窗数据
        clearInputValue (item) {
            this.pageData.panels[this.tabIndex].content.form[item.fieldName] = ''
            this.$forceUpdate()
        },
        //修改弹窗传参数据
        setSelectModalParams (fieldName, params) {
            this.pageData.panels.forEach(panel => {
                panel.content.list.forEach(li => {
                    li.formList.forEach(form => {
                        if(form.type == 'selectModal' && fieldName == form.fieldName) {
                            form.params = params
                        }
                    })
                })
            })
            this.$forceUpdate()
        },
        //表单弹窗回调
        fieldSelectOk (data) {
            this.currentSelectModal.selectCallBack(data)
        },
        getUploadListData () {
            let that = this
            let uploadPanel = this.pageData.panels.filter(panel => {
                return panel.content.type == 'upload'
            })
            if(uploadPanel.length) {
                uploadPanel.forEach(item => {
                    that.$refs[item.content.ref][0].getFileList(item.content.relatedId, item.content.relatedType, item.content.roleName)
                })
            }
        },
        selectChangeEvent (val, opt, item) {
            this.setOldFormField(item.fieldName)
            if(item.changeEvent) {
                item.changeEvent(val, opt)
            }
        },
        // 重置界面
        resetPage () {
            let that = this
            this.activeIndex = 'tab_panel_0'
            this.voucherId = ''
            this.pageData.panels.forEach((panel, index) => {
                if(panel.content.type == 'table') {
                    that.$refs[panel.content.ref][0].loadData([])
                }else if(panel.content.type == 'form') {
                    panel.content.form = this.formList[index]
                    that.$refs[panel.content.ref][0].resetFields()
                }
            })
        }
    }
}
</script>
<style lang="less">
    .table-page-search-wrapper .ant-form-inline .textarea-form-item .ant-form-item-control {
        height: auto
    }
    .tabs-collapase-template .vxe-toolbar.size--small {
        height: 32px
    }
</style>