<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showHisPage && !showDetailPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url"/>
    <!-- 表单区域 -->
    <purchaseContractLibraryHis-modal
      ref="editContractLibraryItemModal"
    />
    <purchaseContractLibraryItemHis-modal
      ref="hisContractLibraryItemHisModal"
    />
    <hisPurchaseContractLibrary-modal
      ref="hisPurchaseContractLibraryModal"
      v-if="showHisPage"
      :current-edit-row="currentEditRow"
      @hide="hideHisPage"
    />
    <record-modal
      v-model="recordShowVisible"
      :currentEditRow="currentEditRow"
    />
  </div>
</template>

<script>
import EditPurchaseContractLibraryModal from './modules/EditPurchaseContractLibraryModal'
import PurchaseContractLibraryHisModal from './PurchaseContractLibraryHisModal'
import HisPurchaseContractLibraryModal from './modules/viewPurchaseContractLibraryHisModal'
import RecordModal from '@comp/recordModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {cloneDeep} from 'lodash'

export default {
    mixins: [ListMixin],
    components: {
        'purchaseContractLibraryHis-modal': EditPurchaseContractLibraryModal,
        'purchaseContractLibraryItemHis-modal': HisPurchaseContractLibraryModal,
        'hisPurchaseContractLibrary-modal': PurchaseContractLibraryHisModal,
        'record-modal': RecordModal
    },
    data () {
        return {
            showHisPage: false,
            recordShowVisible: false,
            currentEditRow: {},

            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterProjectName`, '请输入项目名称')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                        fieldName: 'itemType',
                        dictCode: 'srmItemType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectItemType`, '请选择项目类型')
                    }
                ],
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'contractLibrary#purchaseContractLibrary:add',
                        icon: 'plus',
                        clickFn: this.handleAddLibrary,
                        type: 'primary'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                form: {
                    keyWord: '',
                    itemType: ''
                },
                optColumnList: [
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        authorityCode: 'contractLibrary#purchaseContractLibrary:edit',
                        clickFn: this.handleEdit
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        authorityCode: 'contractLibrary#purchaseContractLibrary:view',
                        clickFn: this.handleShow
                    },
                    {
                        type: 'history',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_numberOfHistory`, '历史记录'),
                        clickFn: this.handleHis,
                        allow: this.allowEdit
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        authorityCode: 'contractLibrary#purchaseContractLibrary:delete',
                        clickFn: this.handleDelete
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ],
                optColumnWidth: 200
            },
            url: {
                list: '/contract/purchaseContractLibrary/list',
                delete: '/contract/purchaseContractLibrary/delete',
                deleteBatch: '/contract/purchaseContractLibrary/deleteBatch',
                exportXlsUrl: '/contract/purchaseContractLibrary/exportXls',
                importExcelUrl: '/contract/purchaseContractLibrary/importExcel',
                columns: 'purchaseContractLibraryList'
            }
        }
    },
    methods: {
        handleAddLibrary () {
            this.$refs.editContractLibraryItemModal.open()
        },
        handleEdit (row) {
            this.$refs.editContractLibraryItemModal.open(cloneDeep(row))
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls('合同条款库')
        },
        allowEdit (row) {
            if (row.itemVersion <= 1) {
                return true
            } else {
                return false
            }
        },
        handleHis (row) {
            this.currentEditRow = row
            this.showHisPage = true
        },
        hideHisPage () {
            this.showHisPage = false
        },
        handleShow (row) {
            const viewAuth = this.getViewAuth()
            if (!viewAuth) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BjbWWVKHRvjlb_beee0887`, '没有权限，请联系管理员授权'))
                return
            }
            this.$refs.hisContractLibraryItemHisModal.open(cloneDeep(row))
        }
    }
}
</script>
