<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction } from '@/api/manage'
export default {
    name: 'ReplaceEnterpriseCertificationAdd',
    mixins: [EditMixin],
    data () {
        return {
            selectType: 'esignEnterpriseCertification',
            pageData: {
                form: {
                    loadingCompany: '0',
                    companyCode: '',
                    companyName: '',
                    subAccount: '',
                    accountId: '',
                    idNumber: '',
                    idType: '',
                    orgLegalIdNumber: '',
                    orgLegalName: '',
                    authType: '',
                    elsAccount: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_64dEMQeAfsqvfsQ4`, '企业认证信息维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                                    fieldName: 'elsAccount',
                                    bindFunction: function (Vue, data){    
                                        Vue.form.elsAccount = data[0].toElsAccount,    
                                        Vue.form.companyName = data[0].supplierName,
                                        Vue.form.subAccount = null,    
                                        Vue.form.name = null,    
                                        Vue.form.mobile = null,    
                                        Vue.form.email = null,    
                                        Vue.form.subAccountId = null 
                                    }, extend: { 
                                        modalColumns: [
                                            {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_toElsAccount`, '账号'), fieldLabelI18nKey: 'i18n_alert_ey_116b91', with: 150},
                                            {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '名称'), fieldLabelI18nKey: 'i18n_alert_RL_aa783', with: 150}
                                        ], modalUrl: 'supplier/supplierMaster/list', modalParams: {}
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_loadingCompany`, '是否加载公司列表'),
                                    fieldName: 'loadingCompany',
                                    dictCode: 'yn',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'),
                                    fieldName: 'companyCode',
                                    dictCode: 'purchase_organization_info,org_name,org_code,org_category_code="companyCode"',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'),
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称'),
                                    fieldName: 'companyName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称')
                                },
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_userAccount`, '用户账号'),
                                    fieldName: 'subAccount',
                                    bindFunction: function (Vue, data){    
                                        Vue.form.subAccount = data[0].subAccount,    
                                        Vue.form.name = data[0].realname,    
                                        Vue.form.accountId = data[0].accountId
                                    }, extend: { 
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_subAccount`, '账号'), with: 150}, 
                                            {field: 'accountId', title: 'e签宝账户', with: 150}, 
                                            {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '名称'), with: 150}
                                        ], modalUrl: '/esign/elsSubaccountCertificationInfo/list', modalParams: function (Vue, form) {
                                            return {
                                                orgCreateFlag: '1', replaceAccount: form.elsAccount
                                            }
                                        },
                                        beforeCheckedCallBack: function (Vue, pageData, groupData, form) {
                                            return new Promise((resolve, reject) => {
                                                let elsAccount = form.elsAccount || ''
                                                return elsAccount !== '' ? resolve('success') : reject(Vue.$srmI18n(`${Vue.$getLangAccount()}#i18n_title_selectSupplierFirst`, '请先选择供应商！'))
                                            })
                                        }
                                    },
                                    required: '1'
                                },
                                {
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_fekrL2LC7bftWONd`, '用户e签宝id'),
                                    fieldType: 'input',
                                    fieldName: 'accountId',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_61w5j37BPo3g0ZZ5`, '机构证件类型'),
                                    fieldName: 'idType',
                                    dictCode: 'srmCompanyEsignIdType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_typeofInstitutionCertificate`, '机构证件类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    fieldName: 'idNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalIdNumber`, '法定代表人证件号'),
                                    fieldName: 'orgLegalIdNumber'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalName`, '法定代表人名称'),
                                    fieldName: 'orgLegalName'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型'),
                                    fieldName: 'authType',
                                    dictCode: 'srmCompanyCertificationType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_XQzTTQLipsGMe3Mt`, 'e签宝个人机构账户'),
                                    fieldName: 'orgId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cbN3tX16RHkSMJqd`, '是否认证完成'),
                                    fieldName: 'certificationStatus',
                                    disabled: true,
                                    dictCode: 'yn',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cbN3tX16RHkSMJqd`, '是否认证完成')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_shortLink`, '实名认证短链接'),
                                    fieldName: 'shortLink',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_longLink`, '实名认证长链接'),
                                    disabled: true,
                                    fieldName: 'longLink'
                                }
                            ],
                            validateRules: {
                                loadingCompany: [{required: true, message: '是否加载公司列表不能为空'}],
                                subAccount: [{required: true, message: '用户账号不能为空'}],
                                accountId: [{required: true, message: '用户账号对应的e签宝账号不能为空'}],
                                idType: [{required: true, message: '证件类型不能为空'}],
                                idNumber: [{required: true, message: '证件号不能为空'}]
                            }
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/esign/elsEnterpriseCertificationInfo/replaceAdd'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
        },
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    if(params.loadingCompany==='0' && !params.companyName){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCRLxOLV_3f6f291f`, '公司名称不能为空'))
                        return
                    }
                    if(params.loadingCompany==='1' && !params.companyCode){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCooxOLV_5449119a`, '公司代码不能为空'))
                        return
                    }
                    let url = this.url.add
                    const _this = this
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            _this.$parent.addCallBack(res.result)
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>