<template>
  <!-- 采购申请 -->
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 表单区域 -->
    <!--<purchaseRequestHead-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />-->

    <purcase-request-head-modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
    <Mapping-Price
      v-if="showCreateOrderPage"
      :current-edit-row="currentEditRow"
      @hide="hideCreateOrderPage"
    />
    <ViewPurchaseRequestModal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :return-load="returnLoad"
      :allowEdit="allowViewToEdit"
      @hide="hideEditPage"
      @toEdit="handleEditFromViewPage()"
    />
  </div>
</template>

<script>
import PurcaseRequestHeadModal from './modules/PurcaseRequestHeadModal'
import ViewPurchaseRequestModal from './modules/ViewPurchaseRequestModal'
import MappingPrice from './modules/MappingPrice'
import {getAction, httpAction} from '@/api/manage'
import {ListMixin} from '@comp/template/list/ListMixin'

export default {
    mixins: [ListMixin],
    components: {
        PurcaseRequestHeadModal,
        MappingPrice,
        ViewPurchaseRequestModal
    },
    data () {
        return {
            showEditPage: false,
            showCreateOrderPage: false,
            showEditEnquiryPage: false,
            currentEditRow: {},
            sourceType: '',
            pageData: {
                businessType: 'purchaseRequest',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTitleOriPurchaseRequisitionNo`, '请输入标题或采购申请单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseRequestType`, '采购申请类型'),
                        fieldName: 'requestType',
                        dictCode: 'srmRequestType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')
                    }
                ],
                form: {
                    keyWord: '',
                    requestType: '',
                    sourceType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), authorityCode: 'purchaseRequest#purchaseRequestHead:add', icon: 'plus', type: 'primary', clickFn: this.handleAdd, primary: true},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), authorityCode: 'purchaseRequest#purchaseRequestHead:export', icon: 'download', clickFn: this.handleExportXls},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt`, '导入'), authorityCode: 'purchaseRequest#purchaseRequestHead:import', icon: 'import', folded: false, type: 'upload', clickFn: this.importExcel},
                    // {label: '预算校验', icon: 'setting', clickFn: this.handleBudgetCheck},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SMWWWWF_b44eb4e2`, '获取ERP数据'), authorityCode: 'purchaseRequest#purchaseRequestHead:getPurchaseRequestByERP', icon: 'arrow-down', clickFn: this.getDataByErp},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'), authorityCode: 'purchaseRequest#purchaseRequestHead:pushPurchaseRequestData', icon: 'arrow-up', clickFn: this.pushDataToERP},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduXL_bf0b657f`, '推送到商城'), authorityCode: 'purchaseRequest#purchaseRequestHead:pushPurchaseRequestDataToMall',  icon: 'arrow-up', clickFn: this.pushDataToMall},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting',  clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'purchaseRequest#purchaseRequestHead:view', clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'purchaseRequest#purchaseRequestHead:edit', clickFn: this.handleEdit, allow: this.showEditCondition},
                    {type: 'close-circle', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_ku_9fac3`, '作废'), authorityCode: 'purchaseRequest#purchaseRequestHead:cancel', clickFn: this.cancel, allow: this.showCancelCondition},
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        authorityCode: 'purchaseRequest#purchaseRequestHead:delete',
                        clickFn: this.handleDelete,
                        allow: this.showDeleteCondition
                    },
                    {
                        type: 'copy',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'),
                        authorityCode: 'purchaseRequest#purchaseRequestHead:copy',
                        clickFn: this.handleCopy
                    }

                ],
                optColumnWidth: 150
            },
            tabsList: [],
            url: {
                list: '/demand/purchaseRequestHead/list',
                add: '/demand/purchaseRequestHead/add',
                delete: '/demand/purchaseRequestHead/delete',
                cancel: '/demand/purchaseRequestHead/cancel',
                copy: '/demand/purchaseRequestHead/copy',
                deleteBatch: '/demand/purchaseRequestHead/deleteBatch',
                importExcelUrl: 'demand/purchaseRequestHead/importExcel',
                excelCode: 'PurchaseRequestHeadExcel',
                columns: 'purchaseRequestHeadList',
                exportXlsUrl: '/demand/purchaseRequestHead/exportXls',
                getDataByErpUrl: '/demand/purchaseRequestHead/getPurchaseRequestByERP',
                pushDataToERPUrl: '/demand/purchaseRequestHead/pushPurchaseRequestData',
                pushDataToMallUrl: '/mall/pushPurchaseRequestDataToMall'
            }
        }
    },
    computed: {
        allowViewToEdit() {
            if(!!this.currentEditRow && !!this.showEditCondition(this.currentEditRow)) return false;
            return true;
        }
    },
    mounted () {
        // this.serachTabs('srmRequestStatus', 'requestStatus')
        this.sourceType = ''
        this.serachCountTabs('/demand/purchaseRequestHead/counts')
    },
    created () {
        this.pageData.form.sourceType = this.$route.query.sourceType
    },
    // 添加商城跳转查询来源条件
    beforeRouteEnter (to, from, next) {
        // 在渲染该组件的对应路由被 confirm 前调用
        // 不！能！获取组件实例 `this`
        // 因为当守卫执行前，组件实例还没被创建
        next(vm => {
            if (from.path.includes('mall')) {
                vm.pageData.form['sourceType'] = to.query.sourceType
            }
        })
    },
    methods: {
        // 重写路径变动监听事件
        handleRouterChange (query) {
            let row = {
                id: query.id,
                templateName: query.templateName,
                templateNumber: query.templateNumber,
                templateAccount: query.templateAccount,
                templateVersion: query.templateVersion
            }
            if (query.open && !query.pageShow) {
                if (row.id) {
                    this.showEditPage = false
                    this.handleView(row)
                }
            } else if (query.pageShow) {
                if (row.id) {
                    this.showEditPage = false
                    this.handlePageShow(row)
                }
            } else if (query.linkFilter || query.toList) {
                this.$nextTick(() => {
                    this.showEditPage = false
                    this.$refs.listPage.loadData('', query)
                })
            } else if(query&&query.editShow==='true'){
                this.$nextTick(() => {
                    this.currentEditRow = query.obj
                    this.showEditPage = true
                })
            }else {
                this.showDetailPage = false
                this.$nextTick(() => {
                    this.$refs.listPage && this.$refs.listPage.loadData()
                })
            }
        },
        // handleOtherPage (){
        //     this.showEditPage=false
        // },
        serachCountTabs (url) {
            let that = this
            let params = {
                sourceType: this.pageData.form.sourceType
            }
            getAction(url, params).then(res => {
                if (res.success) {
                    let options = []
                    let array = res.result || []
                    array.forEach(data => {
                        let tab = {}
                        tab.title = data.title
                        tab[data.fileName] = data.value
                        tab.total = data.total
                        tab.rejectReason = data.rejectReason
                        options.push(tab)
                    })
                    that.tabsList = options
                    this._countTabsUrl = url
                }
            })
        },
        returnLoad (){
            this.$refs.listPage.loadData()
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisi`, '采购申请'))
        },
        // 预算校验
        handleBudgetCheck (){
            let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            if (selectedRows.length == 0 || selectedRows.length > 1) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectPieceOfData`, '请选择一条数据!'))
            }else if(selectedRows[0].requestStatus != 0 && selectedRows[0].requestStatus != 9 && selectedRows[0].requestStatus != 7){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_budgetVerificationIsNotAllowedDataRequisitionstatus`, '此数据申请单状态不可进行预算校验!'))
            }else{
                let data = {
                    purchaseRequestHeadId: selectedRows[0].id
                }
                getAction('/budget/budgetManage/budgetCheck', data).then(res => {
                    if (res.success) {
                        this.$message.success(res.message)
                    }else{
                        this.$message.error(res.message)
                    }
                    this.modalFormOk()
                })
            }
        },
        showEditCondition (row) {
            if((row.requestStatus == '0'||row.requestStatus == '4'||row.requestStatus == '2'||row.requestStatus == null||row.requestStatus == '8'||row.requestStatus == '6'||row.requestStatus == '9'||row.requestStatus == '7')&&(!row.auditStatus || row.auditStatus == '0'||row.auditStatus == '4'||row.auditStatus == '3')) {
                return false
            }else {
                return true
            }
        },
        showCancelCondition (row) {
            if(row.requestStatus == '4'||((row.requestStatus == '0'&&(row.auditStatus == '2'||row.auditStatus == '3')))){
                return false
            }else {
                return true
            }
        },
        showDeleteCondition (row){
            if((row.requestStatus == '0'||row.requestStatus == null||row.requestStatus == '8'||row.requestStatus == '6'||row.requestStatus == '9') && (row.auditStatus == '0'||row.auditStatus == '4') ){
                return false
            }else{
                return true
            }
        },
        hideCreateOrderPage (){
            this.showCreateOrderPage = false
        },
        hideEditEnquiryPage (){
            this.showEditEnquiryPage = false
        },
        hideDetailPage (){
            this.showDetailPage = false
        },
        cancel (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        // 复制功能按钮
        handleCopy (row) {
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(row)
                }
            })
        },
        // 复制数据请求接口
        copyData (row) {
            this.confirmLoading = true
            let param  = { id: row.id }
            getAction(this.url.copy, param).then(res => {
                if (res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功！'))
                    this.searchEvent()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        createEnquiry (){
            let selectedRows = this.$refs.listPage.getSelectedRows()
            if(selectedRows.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelecDataTransferredrFQ`, '请选择需要转询价的数据'))
                return
            }
            let param = {id: null}
            param['enquiryBuyItemList'] = selectedRows
            this.currentEditRow = param
            this.showEditEnquiryPage = true
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (formData, result){ 
            if (result.auditStatus == '0') {
                this.showDetailPage = false
                this.showEditPage = true
                this.searchEvent()
            }
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        pushDataToMall () {
            let grid = this.$refs.listPage.$refs.listGrid
            let checkRow = grid.getCheckboxRecords() || []
            if (!checkRow.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRRiFIcWF_e9c4fe3f`, '请至少选择一行'))
                return
            }
            let ids = checkRow.map((row) => row.id).join(',')
            this.$refs.listPage.loading = true
            getAction(this.url.pushDataToMallUrl, { ids: ids })
                .then((res) => {
                    if (res.success) {
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功'))
                        this.searchEvent()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.$refs.listPage.loading = false
                })
        }
    }
}
</script>
