<template>
  <div>
    <!-- <a-modal
    v-drag    
      :visible="modalVisible"
      width="1000px"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_MY_b14e2`, '回退')"
      
      :confirmLoading="confirmLoading"
      :footer="null"> -->
    <titleCrtl>
      <!-- {{ $srmI18n(`${$getLangAccount()}#i18n_field_MY_b14e2`, '回退') }} -->

      <template slot="right">
        <a-button
          v-if="nodeList.length!=0"
          type="primary"
          @click="allBack">{{ $srmI18n(`${$getLangAccount()}#i18n_field_bxMY_272c6162`, '全部回退') }}</a-button>
        <!-- <a-button
            @click="backTo">{{ $srmI18n(`${$getLangAccount()}#i18n_field_MYuRIrv_a5fe6925`, '回退到指定版本') }}</a-button> -->
      </template>
    </titleCrtl>
    <list-table
      ref="listTable"
      :statictableColumns="statictableColumns"
      :pageData="modalPageData"
      setGridHeight="400"
      :fromSourceData="nodeList"
      :showTablePage="false"> </list-table>

    <!-- </a-modal> -->
  </div>
</template>
<script>
import listTable from '../../../bidding_new/BiddingHall/components/listTable'
import titleCrtl from '../../../bidding_new/BiddingHall/components/title-crtl'
import { postAction, getAction } from '@/api/manage'

export default {
    // mixins: [modalMixins],
    components: {
        listTable,
        titleCrtl
    },
    props: {
        taskInfo: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },  
    // watch: {
    //     nodeList: {
    //         handler (val){
    //             console.log(Boolean(val))
    //             this.flag = Boolean(val)
    //         }
    //     }
    // },
    data () {
        return {
            nodeList: [
            ],
            modalVisible: false,
            confirmLoading: false,
            modalPageData: {
                optColumnList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MYuAPrv_a42c8f4c`, '回退到当前版本'),
                        clickFn: this.backTo,
                        showCondition: (row, rowIndex) => {return rowIndex != 0 ? true : false}
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mAQL_3106e730`, '查看流程'),
                        clickFn: this.showFlowImage
                    }
                ]
            }
        }
    },
    computed: {
        statictableColumns () {
            let columns = [
                // { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                    groupCode: 'nodeList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nodeName`, '节点名称'),
                    fieldLabelI18nKey: '',
                    field: 'nodeName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    enabled: false
                },
                {
                    groupCode: 'nodeList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                    fieldLabelI18nKey: '',
                    field: 'createTime',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    enabled: false
                },
                {
                    groupCode: 'nodeList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_versionN`, '版本号'),
                    fieldLabelI18nKey: '',
                    // fieldType: 'input',
                    field: 'miComplete',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 80,
                    enabled: false
                },
                {
                    fixed: 'right',
                    groupCode: 'nodeList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    field: 'title',
                    width: 180,
                    slots: { default: 'grid_opration' }
                    // slots: {
                    //     default: ({row}) => {
                    //         return [
                    //             <div>
                    //                 <a-button type="link" onClick={() => this.backTo(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MYuAPrv_a42c8f4c`, '回退到当前版本')}</a-button>    
                    //                 <a-button type="link" onClick={() => this.showFlowImage(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mAQL_3106e730`, '查看流程')}</a-button>    
                    //             </div>
                    //         ]
                    //     }
                    // }
                }
            ]
            return columns
        }
    },
    methods: {
        getData (){
            getAction('/a1bpmn/api/cockpit/process-instance/dynamic/history/'+this.taskInfo.processInstanceId).then(res=>{
                console.log('res', res)
                if(res.success){
                    this.nodeList = res.data
                    // let param = res.data[0] ?? ''
                    // this.$emit('changeFlowImage', param)
                }
            })
        },
        // 全部退回
        allBack (){
            let params = {
                processInstanceId: this.taskInfo.processInstanceId,
                resetAll: true
            }
            postAction('/a1bpmn/audit/api/process-instance/withdraw/dynamic/version', params).then(res=>{
                if(res.success){
                    this.$message.success(res.message)
                    console.log('params.processInstanceId', params.processInstanceId)
                    this.$emit('changeFlowImage', 'undefined')
                    this.getData()
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        // 回退到指定版本
        backTo (row){
            let params = {
                processInstanceId: row.procId,
                processInstanceModelId: row.id,
                resetAll: false
            }
            postAction('/a1bpmn/audit/api/process-instance/withdraw/dynamic/version', params).then(res=>{
                if(res.success){
                    this.$message.success(res.message)
                    this.showFlowImage(row)
                    this.getData()
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        showFlowImage (row){
            this.$emit('changeFlowImage', row.id)
        }
    },
    mounted (){
        console.log(this.task)
        this.getData()
    }
}
</script>
