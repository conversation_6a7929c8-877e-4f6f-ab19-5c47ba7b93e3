<template>
  <a-modal
    v-drag    
    v-model="madalVisible"
    :title="modelTitle"
    @ok="setLadderOk">
    <a-spin :spinning="confirmLoading">
      <div class="compare-page-container">
        <div class="page-content-right">
          <div style="margin-bottom: 12px">
            <vxe-grid
              border
              ref="headerGrid"
              row-id="ladderQuantity"
              show-overflow
              size="small"
              height="350"
              :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderInfo`, '阶梯信息')"
              :columns="tableHeaderColumn"
              :data="tableHeaderData"
              :radio-config="{highlight: true, reserve: true, trigger: 'row'}"
              :toolbar="{ slots: { buttons: 'toolbar_buttons' }}"
            >
              <template #toolbar_buttons>
                <div
                  style="margin-top:-8px;"
                  v-if="isJtbjTableShow">
                  <label style="">{{ $srmI18n(`${$getLangAccount()}#i18n_field_currency`, '币别') }}：{{ currency }}</label>
                  <label style="margin-left:60px">{{ $srmI18n(`${$getLangAccount()}#i18n_title_enterTaxCode`, '税码') }}：{{ taxCode }}</label>
                  <label style="margin-left:60px">{{ $srmI18n(`${$getLangAccount()}#i18n_title_taxRate`, '税率') }}：{{ taxRate }}</label>
                </div>
              </template>
              
            </vxe-grid>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import {List} from 'ant-design-vue'
export default {
    name: 'ViewLadderPriceModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        isJtbjTableShow: {
            type: Boolean,
            default: true
                
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            modelTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderReportPrice`, '阶梯报价'),
            currency: '',
            taxCode: '',
            taxRate: '',
            headObj: {},
            ladderQuantity: '',
            confirmLoading: false,
            listData: [],
            madalVisible: false,
            tableHeaderData: [],
            tableHeaderColumn: [
                { field: 'ladder', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级'), width: 150},
                { field: 'ladderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderCount`, '阶梯数量'), width: 100},
                { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'), width: 100},
                { field: 'netPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrices`, '净价'), width: 100}
            ]
        }
    },
    mounted () {
    },
    methods: {
        open (row) {
            this.modelTitle =this.isJtbjTableShow ?this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vendorName`, '供应商')+'：' + row.supplierName + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '物料')+'：' + row.materialDesc :this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vendorName`, '供应商')+'：' + row.supplierName
            this.currency = row.currency
            this.taxCode = row.taxCode
            this.taxRate = row.taxRate
            let that = this
            this.madalVisible = true
            if(row.ladderPriceJson){
                let itemList = JSON.parse(row.ladderPriceJson)
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData(itemList)
                })
            }
        },
        goBack () {
            this.$emit('hide')
        },
        setLadderOk (){
            this.madalVisible = false
        }
    }
}
</script>
<style lang="less" scoped>
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 1000px;
            background-color: #fff
        }
    }
</style>