<template>
  <div>
    <a-modal
      centered
      v-drag
      :footer="null"
      :mask-closable="false"
      :title="title"
      :visible="visible"
      :width="1200"
      @cancel="visible = false">
      <div
        class="content-container"
        v-if="visible">
        <a-tabs
          :default-active-key="activeKey"
          @change="changeTab"
        >
          <a-tab-pane
            v-for="(sup) in suppliers"
            :key="sup.toElsAccount"
            :tab="sup.supplierName">
          </a-tab-pane>
        </a-tabs>
        <vxe-grid
          ref="selectedGrid"
          v-bind="gridConfig"
          :data="tableDatas"
          :columns="columns"
          :height="400"
          :loading="loading">
          <template #empty>
            <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
          </template>
        </vxe-grid>
      </div>
    </a-modal>
    <remote-js
      v-if="visible"
      :current-edit-row="currentEditRow"
      :src="src"
      @remote-js-load-success="loadRemoteJsSuccess"
      @remote-js-load-error="loadRemoteJsError"/>
  </div>
</template>
  
<script lang="jsx">
import RemoteJs from '@comp/template/business/remote-js'
import {add, div} from '@/utils/mathFloat.js'
export default {
    components: {RemoteJs},
    data (){
        return{
            columns: [],
            columnsOhters: [],
            currentEditRow: {},
            gridConfig: {
                autoResize: true,
                border: true,
                columnKey: true,
                highlightHoverRow: true,
                resizable: true,
                round: true,
                rowKey: true,
                showFooter: true,
                showHeaderOverflow: true,
                showOverflow: true,
                size: 'mini'
            },
            title: '',
            activeKey: '',
            row: {},
            suppliers: [],
            groupCode: '',
            loading: false,
            src: '',
            visible: false
        }
    },
    computed: {
        tableDatas () {
            let key = `supplier_${this.activeKey}_costFormJson`
            if (this.row[key] && this.row[key].data && this.row[key].data[this.groupCode]) {
                return this.row[key].data[this.groupCode]
            }
            return []
        }
    },
    methods: {
        loadRemoteJsError (err){
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMIrKmWWVImW_2e550783`, '获取模板失败, 请检查：') + err)
        },
        loadRemoteJsSuccess (){
            const configData = getPageConfig() // eslint-disable-line
            let d = configData.groups.filter(group => group.groupCode == this.row.costValue)[0]
            this.title = d.groupName
            this.groupCode = d.groupCode
            configData.itemColumns.map(item => {    
                if(item.sum == '0' && item.groupCode == this.groupCode) {
                    this.columnsOhters.push(
                        {fixed: 'left', field: item.field, title: item.title}
                    )
                }
            })
            // 未税价要前端手动计算
            if (this.row['supplier_priceType'] == 'netPrice') {
                this.suppliers.map(sup => {
                    let key = `supplier_${sup.toElsAccount}_costFormJson`
                    if (this.row[key].data && this.row[key].data[this.groupCode]) {
                        {
                            this.row[key].data[this.groupCode].map(item => {
                                this.columnsOhters.map(column => {
                                    item[column.field] = this.calculatePreTaxPrice(item[column.field], this.row.taxRate)
                                })
                            })
                        }
                    }
                })
            }
            this.columns.push(...this.columnsOhters)
        },
        changeTab (v) {
            this.activeKey = v
        },
        open ({currentEditRow, suppliers, row}){
            this.suppliers = suppliers
            this.activeKey = suppliers[0].toElsAccount
            this.row = JSON.parse(JSON.stringify(row))
            this.currentEditRow = currentEditRow
            let {
                busAccount,
                templateNumber,
                templateVersion } = currentEditRow
            const time = new Date().getTime()
            this.src = `${this.$variateConfig['configFiles']}/${busAccount}/purchase_costForm_${templateNumber}_${templateVersion}.js?t=${time}`
            this.columns = [
                {fixed: 'left', type: 'seq', width: 50}
            ]
            this.columnsOhters= []
            this.visible = true
        },
        calculatePreTaxPrice (priceWithTax, taxRate) {
            return div(priceWithTax, add(1, div(taxRate, 100))).toFixed(6)
        }
    },
    name: 'CostComparison'
}
</script>
  
  <style lang="less" scoped>
  :deep(.ant-modal-body) {
      padding: 0
  }
  .content-container {
      height: 600px;
      padding: 10px;
  }
  </style>