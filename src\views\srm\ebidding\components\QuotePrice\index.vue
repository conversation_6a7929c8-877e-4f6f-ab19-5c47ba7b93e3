<template>
  <div class="quotePrice">
    <a-modal
    v-drag    
      :visible.sync="visibleDialog"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_offer`, '报价')"
      :maskClosable="false"
      :footer="null"
      @cancel="handleCancel">
      <a-form-model
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        v-bind="layout">
        <a-form-model-item
          has-feedback
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_measurementUnit`, '计量单位')">
          <span>{{ subForm.priceUnit }}</span>
        </a-form-model-item>
        <a-form-model-item
          has-feedback
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_currency`, '币别')">
          <span>{{ subForm.currency }}</span>
        </a-form-model-item>
        <a-form-model-item
          has-feedback
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_price`, '价格')"
          prop="price">
          <a-input v-model.number="ruleForm.price" />
        </a-form-model-item>
        <a-form-model-item
          has-feedback
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_remark`, '备注')"
          prop="remark">
          <a-input
            v-model="ruleForm.remark"
            type="textarea" />
        </a-form-model-item>
        <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
          <a-button
            type="primary"
            @click="submitForm('ruleForm')">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确 定') }}
          </a-button>
          <a-button
            style="margin-left: 10px"
            @click="resetForm('ruleForm')">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重 置') }}

          </a-button>
        </a-form-model-item>

      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
const checkPrice = (rule, value, callback) => {
    if (!value) {
        return callback(new Error('请输入价格'))
    } else {
        callback()
    }
}

import { apiSaleQuotePrice } from '@/api/apiBidding.js'

export default {
    props: {
        // 对外暴露visible属性，用于显示隐藏弹框
        visible: {
            type: Boolean,
            default: false
        },
        info: {
            type: Object,
            default: () => {}
        },
        saleEbiddingItemList: {
            type: Array,
            default: () => []
        }
    },
    data () {
        return {
            ruleForm: {
                price: '',
                remark: ''
            },
            rules: {
                price: [{ validator: checkPrice, trigger: 'change' }]
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 14 }
            }
            
        }
    },
    computed: {
        // 通过计算属性，对.sync进行转换，外部也可以直接使用visible.sync
        visibleDialog: {
            get () {
                return this.visible
            },
            set (val) {
                this.$emit('update:visible', val)
            }
        },
        subForm () {
            let { priceUnit = '', currency = '' } = this.saleEbiddingItemList[0] || {}
            return { priceUnit, currency  }
        },
        // 报价传参计算 prop
        getProp () {
            /**
             * ebiddingWay, 0: 打包, 1: 逐条
             * quoteType, 0: 含税价, 1: 不含税价
             */
            const { ebiddingWay = '1', quoteType = '0' } = this.info
            const key = `${ebiddingWay + ''}${quoteType + ''}`
            const map = {
                '10': 'price',
                '11': 'netPrice',
                '00': 'totalAmount',
                '01': 'netTotalAmount'
            }
            return map[key] || 'price'
        }
    },
    methods: {
        handleCancel () {
            this.resetForm('ruleForm')
            this.$emit('update:visible', false)
        },
        submitForm (formName) {
            const callback = () => {
                
                const { price, remark } = this.ruleForm
                let saleQuoteItem = this.saleEbiddingItemList[0] || {}
                saleQuoteItem.supplierRemark = remark
                saleQuoteItem[this.getProp] = price
                const params = {
                    ...this.info,
                    saleQuoteItem
                }
                apiSaleQuotePrice(params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    this.$emit('success')
                    this.handleCancel()
                })
            }
            this.$refs[formName].validate(valid => {
                if (!valid) return false
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_RLsu_38d6fc68`, '确认报价'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherQuote`, '确认是否报价'),
                    onOk () {
                        callback && callback()
                    },
                    onCancel () {
                        console.log('Cancel')
                    }
                })
            })
        },
        resetForm (formName) {
            this.$refs[formName].resetFields()
        }
    }
}
</script>
