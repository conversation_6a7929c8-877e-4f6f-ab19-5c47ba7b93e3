<template>

  <a-form-model
    :model="form"
    :label-col="labelCol"
    :wrapper-col="wrapperCol">

    <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_titel`, '标题')">
      <a-input v-model="form.title" />
    </a-form-model-item>

    <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_noticeType`, '公告类型')">
      <a-select
        v-model="form.type"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectNoticeType`, '选择公告类型')">
        <a-select-option
          v-for="d in noticeType"
          :key="d.value"
          :value="d.value"> {{ d.title }} </a-select-option>
      </a-select>
    </a-form-model-item>

    <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_validTime`, '有效期')">
      <a-range-picker
        v-model="form.validTime"
        v-decorator="['range-picker', validTime]" />
    </a-form-item>

    <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_allReceived`, '是否发给所有人')">
      <a-radio-group v-model="form.allReceived">
        <a-radio :value="1">
          
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是') }}
        </a-radio>
        <a-radio :value="0">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_no`, '否') }}
        </a-radio>
      </a-radio-group>
    </a-form-model-item>

    <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_resource`, '是否对外')">
      <a-radio-group v-model="form.resource">
        <a-radio :value="1">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是') }}
        </a-radio>
        <a-radio :value="0">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_no`, '否') }}
        </a-radio>
      </a-radio-group>
    </a-form-model-item>

    <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_contents`, '内容')">
      <a-input
        v-model="form.contents"
        type="textarea" />
    </a-form-model-item>

    <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_attachment`, '附件')">
      <a-upload
        v-model="form.attachment"
        v-decorator="[
          'upload',
          {
            valuePropName: 'attachment',
            getValueFromEvent: attachment,
          },
        ]"
        list-type="picture"
        :customRequest="customRequest"
        :fileList="fileList"
        @download="headDownload"
        :showUploadList="{
          showRemoveIcon: true,
          showDownloadIcon: true
        }"
      >
        <a-button> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_clickUplod`, '点击上传') }} </a-button>
      </a-upload>
    </a-form-item>

    <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
      <a-button
        type="primary"
        @click="onSubmit">
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}
      </a-button>

      <a-button
        style="margin-left: 10px;"
        @click="handleClose">
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}
      </a-button>
    </a-form-model-item>

  </a-form-model>
</template>
<script>
import { ajaxFindDictItems } from '@/api/api'
import { httpAction, getAction } from '@/api/manage'

import { ACCESS_TOKEN, USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 14 },
            form: {},
            noticeType: [],
            validTime: [],
            attachment: [],
            fileList: [],
            noticeId: '',
            tokenHeader: {'X-Access-Token': this.$ls.get(ACCESS_TOKEN)}
        }
    },
    methods: {
        onSubmit () {
            console.log('submit!', this.form)
            let validTime = this.form.validTime
            // let param = {}
            // param['title'] = this.form.title
            // param['type'] = this.from.type
            // param['allReceived'] = this.form.allReceived
            // param['resource'] = this.form.resource
            // param['contents'] = this.form.contents
            // param['attachment'] = this.form.attachment
            this.form.validStartTime = new Date(validTime[0]).valueOf()
            this.form.validEndTime = new Date(validTime[1]).valueOf()
            this.form.attachment = JSON.stringify(this.attachment)
            let url = '/notice/notice/add'
            if(this.noticeId) {
                url = '/notice/notice/edit'
            }

            // this.$refs.detailPage.confirmLoading = true
            httpAction(url, this.form, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$emit('hide')
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                // this.$refs.detailPage.confirmLoading = false
            })


        },
        fileFormatter (data) {
            let file = {
                uid: data.id,    // 文件唯一标识，建议设置为负数，防止和内部产生的 id 冲突
                name: data.fileName,   // 文件名
                status: 'done', // 状态有：uploading done error removed
                response: '{"status": "success"}', // 服务端响应内容
                path: data.filePath
            }
            return file
        },
        initDictData () {
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'notice_type'
            }
            ajaxFindDictItems(postData).then((res) => {
                let options = res.result
                if (res.success) {
                    this.noticeType = options
                }
            })
        },
        handleClose (){
            this.$emit('hide')
        },
        customRequest (data){
            const param = new FormData()
            param.append('file', data.file)
            param.append('token', this.tokenHeader)
            param.append('businessType', 'notice_type')
            httpAction('/attachment/purchaseAttachment/upload', param, 'post').then((res) => {
                console.log(res)
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.attachment.push(res.result)
                    this.fileList.push(this.fileFormatter(res.result))
                } else {
                    this.$message.warning(res.message)
                }
            })

        },
        headDownload (file){
            console.log(file.path)

            // const param = new FormData()
            // param.append('token', this.tokenHeader)
            // getAction('/attachment/purchaseAttachment/queryById?id='+file.uid, param, 'get').then((res) => {
            //     console.log(res)
            // })
            window.open('/sys/common/download'+file.path)

            // console.log(file)
        }
    },
    created () {
        this.initDictData()
        let info = JSON.parse(sessionStorage.getItem('noticeInfo'))
        if (info && info.id){
            this.noticeId = info.id
            info.attachment = JSON.parse(info.attachment)
            this.form = info
            let validTime = []
            validTime.push(this.form.validStartTime)
            validTime.push(this.form.validEndTime)
            this.form.validTime = validTime
            this.fileList.push(this.fileFormatter(info.attachment))
            this.$forceUpdate()
            sessionStorage.removeItem('noticeInfo')
            console.log(this.form)
        }
    }
}
</script>
