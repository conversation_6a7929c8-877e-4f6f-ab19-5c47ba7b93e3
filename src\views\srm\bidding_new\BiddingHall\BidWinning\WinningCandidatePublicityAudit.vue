<template>
  <div class="page">
    <span
      v-if="subpackageTitle"
      style="position:fixed;margin:20px"><a-tag
        color="blue"
        style="font-size:16px">{{ subpackageTitle }}</a-tag></span>
    <a-page-header
    >
      <template
        slot="extra"
      >
        <taskBtn
          v-if="taskInfo.taskId"
          :currentEditRow="currentEditRow"
          :pageHeaderButtons="publicBtn"
          v-on="$listeners"/>
        <!-- <template v-else>
                  <a-button
                v-for="(btn, index) in publicBtn"
                :key="'pub_btn_' + index"
                :type="btn.type"
                v-show="btn.showCondition ? btn.showCondition() : true"
                @click="btn.click"
              >{{ btn.title }}</a-button>
              </template> -->
      </template>
    </a-page-header>

    <a-spin :spinning="confirmLoading">
      <div
        class="container"
        :style="style">
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBSiLRt_a1dce8d8`, '中标候选人名单') }}</span>
          </titleTrtl>
          <vxe-grid
            v-bind="gridConfig"
            :height="250"
            ref="table"
            :data="tableData"
            :columns="tableColumns"
            show-overflow="title" >
          </vxe-grid>
        </div>
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_basicInfo`, '基本信息') }}</span>
          </titleTrtl>
          <div>
            <Dataform
              v-if="dataReady"
              ref="dataform"
              :pageStatus="pageStatus"
              :formData="formData"
              :fields="fields" />
          </div>
          <div>
            <j-editor
              ref="ueditor"
              :disabled="pageStatus == 'detail'"
              v-model="formData.publicityContent" />
          </div>
        </div>
      </div>
    </a-spin>
    
    <a-modal
      v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '../components/content-header'
import Dataform from '../components/Dataform'
import titleTrtl from '../components/title-crtl'
import {getAction, postAction, httpAction} from '@/api/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import JEditor from '@/components/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import { mapGetters } from 'vuex'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
export default {
    name: 'WinningCandidatePublicity',
    components: {
        JEditor,
        Dataform,
        titleTrtl,
        ContentHeader,
        flowViewModal,
        taskBtn
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    mixins: [tableMixins],
    data () {
        return {
            subpackageTitle: '',
            dataReady: false,
            auditVisible: false,
            currentUrl: '',
            opinion: '',
            okText: '',
            confirmLoading: false,
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    'type': 'seq'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQSiL_c7729673`, '是否候选人'),
                    'field': 'candidateStatus',
                    slots: {
                        default: ({row, column}) => {
                            return [
                                <a-checkbox disabled={this.pageStatus == 'detail'}v-model={row[column.property]} ></a-checkbox>
                            ]
                        }
                    }
                }
            ],
            publicBtn: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    type: 'primary',
                    click: this.auditPass,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    type: '',
                    click: this.auditReject,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    type: '',
                    click: this.showFlow,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'rollBack',
                    click: this.goBackAudit,
                    showCondition: () => {
                        return true
                    }
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            fields: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRK_2fb96f65`, '是否公示'),
                    fieldLabelI18nKey: '',
                    field: 'publicity',
                    defaultValue: '0',
                    fieldType: 'select',
                    dictCode: 'yn'
                },  
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKvKKI_c9b04117`, '公示开始时间'),
                    fieldLabelI18nKey: '',
                    field: 'publicityBeginTime',
                    dataFormat: 'YYYY-MM-DD HH:mm:ss',
                    defaultValue: '',
                    fieldType: 'dateTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKyRKI_cb627d84`, '公示截止时间'),
                    fieldLabelI18nKey: '',
                    field: 'publicityEndTime',
                    defaultValue: '',
                    dataFormat: 'YYYY-MM-DD HH:mm:ss',
                    fieldType: 'dateTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKBD_26d76b3f`, '公示标题'),
                    fieldLabelI18nKey: '',
                    field: 'publicityTitle',
                    defaultValue: '',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFRKvL_6e31b8d6`, '请选择公示范围'),
                    fieldLabelI18nKey: '',
                    field: 'publicityScope',
                    defaultValue: '',
                    dictCode: 'srmNoticeScope',
                    fieldType: 'select'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQUz_2fba950f`, '是否审批'),
                    fieldLabelI18nKey: '',
                    field: 'audit',
                    fieldType: 'select',
                    dictCode: 'yn'
                }
            ],
            url: {
                queryById: '/tender/purchaseTenderProjectBidWinningCandidatePublicity/queryInfoById'
            }
        }
    },
    computed: {
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        ...mapGetters([
            'taskInfo'
        ]),
        pageStatus () {
            if (this.formData.status == '0' || !this.formData.status) {
                return 'edit'
            } else {
                return 'detail'
            }
            
        }
    },
    methods: {
        getData () {
            let params = {
                id: this.currentEditRow.id
            }
            this.confirmLoading = true
            getAction(this.url.queryById, params).then(res => {
                console.log('hello')
                if(res.success) {
                    let {bidWinningCandidateVOList = [], ...others} = res.result || {}
                    this.tableData = bidWinningCandidateVOList && bidWinningCandidateVOList.map(item => {
                        item.candidateStatus = item.candidateStatus != '0' ? true : false
                        return item
                    })
                    this.formData = others
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
            this.dataReady = true
        },
        /**
     * 审批方法
     */
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass () {
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject () {
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk () {
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = 'purchaseProduct'
            param['businessId'] = this.currentEditRow.businessId
            param['taskId'] = this.currentEditRow.taskId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            this.confirmLoading = true
            postAction(this.currentUrl, param)
                .then(res => {
                    if (res.success) {
                        this.auditVisible = false
                        this.$message.success(res.message)
                        this.$parent.reloadAuditList()
                        this.goBackAudit()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    mounted () {
        this.subpackageTitle = this.currentEditRow.subject || ''
        this.init()
        console.log('taskinfo', this.taskInfo)
    }
}
</script>
<style lang="less" scoped>
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
</style>




