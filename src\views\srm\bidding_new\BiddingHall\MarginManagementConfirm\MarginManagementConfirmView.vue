<template>
  <div class="payment-records-view">
    <ContentHeaderNew :btns="btns"></ContentHeaderNew>
    <div>
      <a-tabs
        default-active-key="1"
        @change="callback">
        <a-tab-pane
          key="1"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_field_sWsxJp_b516bfeb`, '保险保函缴纳')">
          <InsuranceGuaranteeConfirm />
        </a-tab-pane>
        <a-tab-pane
          key="2"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_field_AvCKJp_88896d15`, '其他方式缴纳')">
          <OtherMethodsConfirm />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import ContentHeaderNew from '../components/content-header-new'
import InsuranceGuaranteeConfirm from './modules/InsuranceGuaranteeConfirm'
import OtherMethodsConfirm from './modules/OtherMethodsConfirm'

export default {
    components: {
        ContentHeaderNew,
        InsuranceGuaranteeConfirm,
        OtherMethodsConfirm
    },
    data () {
        return {
            btns: [{title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: () => {this.$router.go(-1)}}]
        }
    },
    methods: {
        callback (key) {
            console.log(key)
        }
    }
}
</script>

<style lang="less" scoped>
.payment-records-view {
    background-color: #fff;
    height: 100%;
}
</style>
