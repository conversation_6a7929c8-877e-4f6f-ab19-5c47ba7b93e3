<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-04-27 15:34:49
 * @LastEditors: LokNum
 * @LastEditTime: 2022-05-16 16:39:43
 * @Description: 采购协同/寻源协同/询价报价历史
-->
<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage"
      ref="listPage"
      :pageData="pageData"
      :url="url" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
export default {
    mixins: [ListMixin],
    data () {
        return {
            pageData: {
                businessType: 'enquiry',
                form: {
                    enquiryNumber: '',
                    materialNumber: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', folded: true, clickFn: this.handleExportXls},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquirySheetNo`, '询价单号'),
                        fieldName: 'enquiryNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterinquirySheetNo`, '请输入询价单号') 
                    },
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'),
                        fieldName: 'materialNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterMaterialNumber`, '请输入物料编码')
                    }
                ],
                showOptColumn: false,
                optColumnWidth: 0,
                optColumnList: []
            },
            url: {
                list: '/enquiry/purchaseEnquiryItemHis/list',
                columns: 'purchaseEnquiryHisList',
                exportXlsUrl: '/enquiry/purchaseEnquiryItemHis/exportXls'
            }
        }
    },
    watch: {
        '$route': {
            handler ({ path }) {
                if (path === '/enquiry/purchaseEnquiryHisList') {
                    this.pageData.form.enquiryNumber = this.$route.query.enquiryNumbers || ''
                    if(this.$refs.listPage){
                        this.$refs.listPage.handleQuery()
                    }
                }
            },
            immediate: true
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls('询价报价历史')
        }
    }
}
</script>