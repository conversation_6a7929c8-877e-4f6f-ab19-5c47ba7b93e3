<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage&&!showDetailPage"
      :pageData="pageData"
      :url="url"/>
    <purchase-inquiry-quoted-config-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <purchase-inquiry-quoted-config-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetailPage"/>
  </div>
</template>
<script>

import {ListMixin} from '@comp/template/list/ListMixin'
import purchaseInquiryQuotedConfigEdit from './modules/PurchaseInquiryQuotedConfigEdit'
import purchaseInquiryQuotedConfigDetail from './modules/PurchaseInquiryQuotedConfigDetail'

export default {
    mixins: [ListMixin],
    components: {
        purchaseInquiryQuotedConfigEdit,
        purchaseInquiryQuotedConfigDetail
    },
    data () {
        return {
            showEditPage: false,
            form: {},
            labelCol: {
                span: 8
            },
            wrapperCol: {
                span: 14
            },
            pageData: {
                formField: [
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ERd_23178d8`, '配置项'),
                        dictCode: 'quotedConfigConfigItem',
                        fieldName: 'configItem',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请选择配置项')
                    }
                ],
                form: {
                    configItem: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnList: [
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEditSingle
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDeleteSingle
                    }
                ],
                optColumnWidth: 150
            },
            url: {
                list: '/enquiry/purchaseInquiryQuotedConfig/list',
                delete: '/enquiry/purchaseInquiryQuotedConfig/delete',
                add: '/enquiry/purchaseInquiryQuotedConfig/add',
                edit: '/enquiry/purchaseInquiryQuotedConfig/edit',
                columns: 'PurchaseInquiryQuotedConfigList'
            }
        }
    },
    methods: {
        handleDeleteSingle (row) {
            this.handleDelete(row)
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        hideDetailPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
        },
        handleAdd () {
            this.showEditPage = true
            this.showDetailPage = false
            this.currentEditRow = {}
        },
        handleEditSingle (row) {
            this.showEditPage = true
            this.showDetailPage = false
            this.currentEditRow = row
        }
    }
}
</script>