<template>
  <div>
    <div>
      <vxe-grid
        @radio-change="radioChange"
        :data="gridData"
        v-bind="gridOptions2">
        <template #default_formatType="{row, cloumn}">
          <span>{{ returnFormatText(row, cloumn) }}</span>
        </template>
      </vxe-grid>
    </div>
    <div v-show="titleName">
      <div class="registration-title"><span>{{ titleName }}</span></div>
      <!-- 投标函信息 -->
      <div class="bid-info">
        <vxe-grid 
          ref="gridBidInfo"
          :edit-rules="editRules"
          v-bind="gridOptions1"></vxe-grid>
      </div>
    </div>
  </div>
</template>

<script>
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
    mixins: [baseMixins],
    props: {
        gridData: {
            type: Array,
            default () {
                return []
            }
        },
        pageType: {
            type: String,
            default () {
                return 'detail'
            }
        },
        saleTenderPriceOpeningsObj: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            gridOptions1: {
                border: true,
                resizable: true,
                showOverflow: true,
                height: 200,
                editConfig: {
                    trigger: 'click',
                    mode: 'cell',
                    activeMethod: this.activeCellMethod
                },
                align: 'center',
                toolbarConfig: {
                    enabled: false
                },
                columns: [],
                data: []
            },
            gridOptions2: {
                border: true,
                resizable: true,
                showOverflow: true,
                height: 200,
                editConfig: {
                    trigger: 'click',
                    mode: 'cell'
                },
                align: 'center',
                toolbarConfig: {
                    enabled: false // 关闭工具栏高度
                },
                columns: [
                    {type: 'radio', title: '', width: 60},
                    {type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60},
                    {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxRL_9dbb44ee`, '投标函名称')},
                    {field: 'formatType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBxAc_9dc007db`, '投标函类型'), slots: {default: 'default_formatType'}}
                ]
            },
            oldData: {},
            editRules: {},
            titleName: '',
            formatType: []
        }
    },
    computed: {
        subPackage () {
            // 若在菜单栏想获取大厅里的分包信息，此处currentSubPackage为空，需要特殊处理
            if(this.currentSubPackage ?? '' == ''){
                return ''
            }
            return this.currentSubPackage()
        }
    },
    created () {
        console.log(this.gridData)
        let businessType = ''
        // 在菜单栏需要用分包信息时，currentSubPackage为空的特殊处理
        let subPackage = this.subPackage != '' ? this.subPackage : this.gridData
        if (this.checkType == undefined && this.processType == undefined) {
            this.checkType = subPackage.checkType
            this.processType = subPackage.processType
        }
        if (this.checkType == '0') {
            businessType = 'preTenderFormatType'
        } else {
            if (this.processType == '1') {
                businessType = 'resultTenderFormatType'
            } else {
                businessType = 'tenderFormatType'
            }
        }


        // 获取表格下拉字典
        let postData = {
            busAccount: this.$ls.get(USER_ELS_ACCOUNT),
            dictCode: businessType
        }
        ajaxFindDictItems(postData).then(res => {
            if (res.success) {
                this.formatType = res.result || []
            }
        })
    },
    methods: {
        returnFormatText (row) {
            let formatTypeText = ''
            this.formatType.forEach(forma => {
                if (forma.value == row.formatType) {
                    formatTypeText = forma.text
                }
            })
            return formatTypeText
        },
        getGridAllData () {
            let saleTenderPriceOpeningsList = []
            console.log(this.saleTenderPriceOpeningsObj)
            Object.keys(this.saleTenderPriceOpeningsObj).forEach(key => {
                let obj = this.saleTenderPriceOpeningsObj[key]
                if (this.oldData[key]) {
                    obj['customizeFieldData'] = JSON.stringify(this.oldData[key][1])
                    saleTenderPriceOpeningsList.push(obj)
                } else {
                    saleTenderPriceOpeningsList.push(obj)
                }
            })
            return saleTenderPriceOpeningsList
        },
        activeCellMethod () {
            return this.pageType == 'detail' ? false : true
        },
        radioChange ({newValue, oldValue, row}) { // 单选回调函数
            console.log(newValue, oldValue, row)
            const {id} = row
            this.titleName = row.name
            let priceOpeningsList = row.priceOpeningsList || []

            let customizeFieldData = priceOpeningsList[0] ? JSON.parse(priceOpeningsList[0]['customizeFieldData']) : []
            let customizeFieldModel = priceOpeningsList[0] ? JSON.parse(priceOpeningsList[0]['customizeFieldModel']) : []
            console.log(customizeFieldData, customizeFieldModel)
            customizeFieldModel.forEach(item => {
                if (item.inputOrg && item.inputOrg == '0') {
                    item.editRender && (item => {item.editRender.enabled = true})(item)
                } else {
                    item.editRender && (item => {item.editRender.enabled = false})(item)
                }
            })
            this.pageType == 'edit' && (()=>{this.oldData[id] = [customizeFieldModel, customizeFieldData]})()
            this.gridOptions1['columns'] = customizeFieldModel
            this.gridOptions1['data'] = customizeFieldData
            this.editRules = {}
            customizeFieldModel.forEach(item => {
                if(item.must && (item.must == '1'||item.must == 'true')) { // 有必填项
                    this.editRules[item.field] = [{required: true, message: `${item.title}必须填写`}]
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
.registration-title{
    background-color: #eee;
    padding: 5px 10px;
    margin-bottom: 10px;
    margin-top: 10px;
}
</style>