import {getLangAccount, srmI18n} from '@/utils/util'
import {message as Message} from 'ant-design-vue'
import router from '@/router'
import {postAction} from '@api/manage'

export class BatchDownloadBtn {
    constructor (option) {
        this.btnConfig = {
            title: '',
            label: '',
            msgType: '',
            key: '',
            type: '',
            beforeCheckedCallBack: () => {
            }
        }

        this.baseConfig = {
            params: {},
            url: ''
        }
        this.idKey = 'id'
        this.nameKey = 'fileName'
        this.pageCode = 'fileInfo'
        this.init(option)
    }

    init (options) {
        if (options && Object.keys(options).length > 0) {
            Object.keys(this).forEach(item => {
                if (options && Object.prototype.hasOwnProperty.call(options, item)) this[item] = options[item]
            })
        }
        this.btnConfig = {
            title: srmI18n(`${getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'),
            label: srmI18n(`${getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'),
            msgType: 'batchDownload',
            key: 'batchDownload',
            type: 'check',
            beforeCheckedCallBack: rowList => {
                this.batchDownloadZip(rowList)
            }
        }
        this.baseConfig = {
            url: '/attachment/purchaseAttachment/downloadZip'
        }
        if (router.currentRoute.fullPath.includes('/sale/') || router.currentRoute.fullPath.includes('Sale')) this.setUrl('sale')
    }

    // 在 group 内添加批量上传按钮
    handelCreateBatchDownload (group, businessType) {
    //modelFileInfo:条例附件
        const types = ['fileInfo', 'modelFileInfo', 'attachmentList', 'saleAttachmentList', 'purchaseAttachmentList', 'bankInfo']
        this.pageCode = group.groupCode
        if (businessType === 'externalToolBar') {
            if (group.groupType === 'item' && types.includes(group.groupCode)) {
                if (this.pageCode && this.pageCode.includes('sale')) this.setUrl('sale')
                if (!group.externalToolBar) {
                    group.externalToolBar = [this.btnConfig]
                } else {
                    if (group.externalToolBar.findIndex(itx => itx.key === this.btnConfig.key) === -1) group.externalToolBar.push(this.btnConfig)
                }
            }
        } else {
            if (group.type === 'grid' && types.includes(group.groupCode)) {
                if (group?.custom?.ref?.includes('sale')) this.setUrl('sale')
                if (!group.custom.buttons) {
                    group.custom.buttons = [this.btnConfig]
                } else {
                    if (group.custom.buttons.findIndex(itx => itx.msgType === this.btnConfig.msgType) === -1) group.custom.buttons.push(this.btnConfig)
                }
            }
        }
        return group
    }

    setUrl (type) {
        switch (type) {
        case ('sale' || 0):
            this.baseConfig.url = '/attachment/saleAttachment/downloadZip'
            break
        case ('purchase' || 1):
            this.baseConfig.url = '/attachment/purchaseAttachment/downloadZip'
            break
        default:
            this.baseConfig.url = ''
        }
        return this
    }

    //可以重写此方法，修改传入的name和id规则
    downloadBackCall (rowInfo) {
        let name = rowInfo[this.nameKey] || ''
        let id = rowInfo[this.idKey] || ''
        const fileNameList = ['bankInfo', 'supplierBankInfoList', 'suppliersupplierBankInfoListList']
        const fileAddressList = ['saleMentoringItemList', 'specialistProfessionalSuccessInfoList', 'specialistEducationExperienceInfoList', 'specialistProfessionalResumeInfoList', 'purchaseMentoringItemList']
        const fileIdList = ['massProdPpapItemList']
        const invoiceImgList = ['paymentApply']

        if (fileNameList.includes(this.pageCode)) {
            [id, name] = rowInfo.fileName ? rowInfo.fileName.split('-') : ['', '']
        } else if (invoiceImgList.includes(this.pageCode)) {
            [id, name] = rowInfo.invoiceImg ? rowInfo.invoiceImg.split('-') : ['', '']
        } else if (fileAddressList.includes(this.pageCode)) {
            [id, name] = rowInfo.fileAddress ? rowInfo.fileAddress.split('-') : ['', '']
        } else if (fileIdList.includes(this.pageCode)) {
            [id, name] = rowInfo.fileId ? rowInfo.fileId.split('-') : ['', '']
        }
        console.log(name, id, this.pageCode)
        return {id, name}
    }

    // 下载方法 废弃
    // downloadEvent(row) {
    //   let rowInfo = row && typeof row == 'string' ? JSON.parse(row) || {} : row || {}
    //   const {name = "", id = ""} = this.downloadBackCall(rowInfo)
    //   if (!name) return Message.warning(srmI18n(`${getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
    //   if (!id) return console.error('未绑定主键！')
    //   const params = {...this.baseConfig.params, id: id}
    //   const url = this.baseConfig.url || ''
    //   getAction(url, params, {
    //     responseType: 'blob'
    //   }).then(res => {
    //     let url = window.URL.createObjectURL(new Blob([res]))
    //     let link = document.createElement('a')
    //     link.style.display = 'none'
    //     link.href = url
    //     link.setAttribute('download', name)
    //     document.body.appendChild(link)
    //     link.click()
    //     document.body.removeChild(link) //下载完成移除元素
    //     window.URL.revokeObjectURL(url) //释放掉blob对象
    //   })
    // }

    // 批量下载方法 废弃
    // batchDownload(rowList) {
    //   if (!this.baseConfig.url) return console.log('请调用下载方法前设置downloadUrl,通过setUrl方法实现')
    //   if (rowList && rowList.length) {
    //     rowList.map(row => {
    //       this.downloadEvent(row)
    //     })
    //   }
    // }

    // 下载方法
    downloadZipParam (row) {
        let rowInfo = row && typeof row == 'string' ? JSON.parse(row) || {} : row || {}
        const {name = '', id = ''} = this.downloadBackCall(rowInfo)
        console.log('[name]', name, '[id]', id)
        if (!name || !id) {
            // Message.warning(srmI18n(`${getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
            return ''
        }
        return id
    }

    downloadZipEvent (ids) {
        const url = this.baseConfig.url
        postAction(url, ids, {
            responseType: 'blob'
        }).then(res => {
            if (res.code == 500) return Message.warning(res.message)
            let url = window.URL.createObjectURL(new Blob([res]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', 'download.zip')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(url) //释放掉blob对象
        })
    }


    // 批量下载方法
    batchDownloadZip (rowList) {
        if (!this.baseConfig.url) return console.log('请调用下载方法前设置downloadUrl,通过setUrl方法实现')
        // 附件id集合
        if (!rowList.length) return Message.warning(srmI18n(`${getLangAccount()}#i18n_title_pleaseSelectRowDataBeOperated`, '请选择需要操作的行'))
        let ids = []
        for (let row of rowList) {
            let id = this.downloadZipParam(row)
            // if (!id) {
            //     // ids = []
            //     break
            // }
            // ids.push(id)
            if (id) {
              ids.push(id)
            }
        }
        ids = ids.filter(item => item && item.trim())
        if (!ids.length) return Message.warning(srmI18n(`${getLangAccount()}#i18n_alert_BjqIKjBI_9a59ec9d`, '没有可下载的附件'))
        this.downloadZipEvent(ids)
    }
}
