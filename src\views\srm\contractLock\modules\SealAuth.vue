<template>
  <a-drawer
    :title="companyName+title"
    placement="right"
    :closable="false"
    :visible="visible"
    :width="860"
    @close="onClose"
  >
    <vxe-grid
      ref="employManagementGridRef"
      :edit-rules="validRules"
      :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
      v-bind="gridOptions">
      <template #toolbar_buttons>
        <a-button
          size="small"
          type="primary"
          @click="add">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_VajR_2f90b9fb`, '新增员工') }}</a-button>
      </template>
      <template v-slot:grid_opration="{ row }">
        <a-button
          style="margin-right: 10px;"
          v-if="!row.id"
          size="small"
          type="danger"
          @click="del(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
        <a-button
          v-if="!row.id"
          size="small"
          type="primary"
          @click="save(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <a-button
          v-if="row.id"
          size="small"
          type="danger"
          @click="del(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
      </template>
      <template #applyUserName_edit="{ row }">
        <a-auto-complete
          allowClear
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_btn_VWNjRcR_aeea9bd0`, '请输入员工姓名')"
          v-model="row.applyUserName"
          :data-source="applyUserNameDataSource"
          @select="applyUserNameOnSelect(row.applyUserName, row)"
          @search="applyUserNameOnSearch"
        />
      </template>
      <template #applyContactType_default="{ row }">
        <span>{{ formatApplyContactType(row.applyContactType) }}</span>
      </template>
      <template #applyContactType_edit="{ row }">
        <a-select
          allowClear
          v-model="row.applyContactType">
          <a-select-option
            v-for="item in applyContactTypeOpt"
            :key="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <template #pager>
        <vxe-pager
          :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
          :current-page.sync="tablePage.currentPage"
          :page-size.sync="tablePage.pageSize"
          :total="tablePage.total"
          @page-change="handlePageChange">
        </vxe-pager>
      </template>
    </vxe-grid>
  </a-drawer>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import { isEmail, isMobile} from '@/utils/validate.js'
export default {
    props: {
        companyName: {
            type: String,
            default: ''
        },
        row: {
            type: Object,
            default: ()=> {
                return { id: '' }
            }
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    data () {
        let phoneValidTip =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WNjltmKxiRWVVVWN_b59e3a24`, '输入的手机格式不正确，请重新输入')
        let emailValidTip =  this.$srmI18n(`${this.$getLangAccount()}#i18n_title_emailError`, '邮箱格式不正确')
        const applyContactValid = (data) => {
            let { cellValue, row } = data
            return new Promise((resolve, reject) => {
                if (row.applyContactType && row.applyContactType==='MOBILE') {
                    if (cellValue && !isMobile(cellValue)) {
                        reject(new Error(phoneValidTip))
                    }
                } else if (row.applyContactType && row.applyContactType==='EMAIL') {
                    if (cellValue && !isEmail(cellValue)) {
                        reject(new Error(emailValidTip))
                    }
                } else {
                    resolve()
                }
            })
        }
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jRRv_27c711d2`, '员工管理'),
            applyUserNameDataSource: [],
            gridOptions: {
                border: false,
                size: 'mini',
                resizable: true,
                showOverflow: true,
                align: 'center',
                toolbarConfig: {
                    slots: {
                        // 自定义工具栏模板
                        buttons: 'toolbar_buttons'
                    }
                },
                editConfig: {
                    trigger: 'click',
                    mode: 'cell',
                    showStatus: true,
                    activeMethod: this.activeRowMethod
                },
                columns: [
                    { type: 'seq', width: 50 },
                    { type: 'checkbox', width: 50 },
                    { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRWWWey_f8d6ad72`, '员工SRM账号'), showOverflow: true, editRender: { name: '$input' }},
                    { field: 'applyUserName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'), showOverflow: true, editRender: { }, slots: {  edit: 'applyUserName_edit' }  },
                    { field: 'applyContact', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'), showOverflow: true, editRender: { name: '$input' } },
                    { field: 'applyContactType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'), showOverflow: true, editRender: {}, slots: { default: 'applyContactType_default', edit: 'applyContactType_edit' }  },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), fixed: 'right', slots: { default: 'grid_opration' } }
                ],
                data: []
            },
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 20
            },
            validRules: {
                subAccount: [
                    { required: true,  message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRWWWeylTSM_57ceded6`, '员工SRM账号必须填写') }
                ],
                applyUserName: [
                    { required: true,  message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_jRcRlTSM_a335c8cb`, '员工姓名必须填写') }
                ],
                applyContact: [
                    { required: true,  message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KHCKlTSM_d458dc1`, '联系方式必须填写') }
                ],
                applyContactType: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KHCKAclTSM_41b2dcf1`, '联系方式类型必须填写') }
                ]
            },
            applyContactTypeOpt: [
                { value: 'MOBILE', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机') },
                { value: 'EMAIL', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱') }
            ]
        }
    },
    mounted () {
        this.getData()
    },
    methods: {
        onClose () {
            this.$emit('close', false)
        },
        activeRowMethod ({ row, rowIndex }) {
            if (row.id) {
                return false
            }
            return true
        },
        formatApplyContactType (val) {
            let label = ''
            this.applyContactTypeOpt.forEach((item)=> {
                if (item.value === val) {
                    label = item.label
                }
            })
            return label
        },
        // 员工搜索
        applyUserNameOnSearch (val) {
            let param= {name: val}
            this.applyUserNameDataSource= []
            getAction('/contractLock/purchaseClPersonalInfo/queryList', param).then((res)=> {
                if (res && res.success) {
                    res.result.forEach((item)=> {
                        if (item.applyUserName) {
                            this.applyUserNameDataSource.push(item.applyUserName)
                        }
                    })
                }
            })
        },
        // 员工选择
        applyUserNameOnSelect (val, row) {
            let param= {applyUserName: val}
            getAction('/contractLock/purchaseClPersonalInfo/list', param).then((res)=> {
                if (res && res.success) {
                    if (res.result.records && res.result.records.length) {
                        row.applyContact = res.result.records[0].applyContact
                        row.applyContactType = res.result.records[0].applyContactType
                    }
                }
            })
        },
        getData () {
            let param= {companyId: this.row.companyId}
            getAction('/contractLock/purchaseClPersonalInfo/list', param).then((res)=> {
                if (res && res.success) {
                    this.gridOptions.data = res.result.records
                    this.tablePage.total = res.result.total
                } else {
                    this.$message.error(res.message)
                }
            })
        },
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            this.getData()
        },
        add () {
            this.$refs.employManagementGridRef.insert({applyContactType: 'MOBILE'})
        },
        del (row) {
            if (row && row.id) {
                postAction('/contractLock/purchaseCLCompanyInfo/removeStaff', row).then((res)=> {
                    if (res && res.success) {
                        this.$message.success(res.message)
                        this.$refs.employManagementGridRef.remove(row)
                    } else {
                        this.$message.error(res.message)
                    }
                })
            } else {
                this.$refs.employManagementGridRef.remove(row)
            }
        },
        async save (row) {
            // 插入一条数据并触发校验
            const errMap = await this.$refs.employManagementGridRef.validate(row).catch(errMap => errMap)
            if (!errMap) {
                if(row.userId){
                    row.id = row.userId
                }
                row.companyId = this.row.companyId
                postAction('/contractLock/purchaseCLCompanyInfo/addStaff', row).then((res)=> {
                    if (res && res.success) {
                        this.$message.success(res.message)
                        this.getData()
                    } else {
                        if(res.message == '网络异常，请检查网络连接') {
                            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAQLAEsLHcAZdROlbWVWeRSMlbKyyVHcROlbKHcROQLtk_54c76816`, '发起流程企业还未进行契约锁功能授权，请先通过获取授权链接接口进行功能授权在进行功能流程操作'))
                        }else {
                            this.$message.error(res.message)
                        }
                    }
                })
            }
        }
    }
}
</script>