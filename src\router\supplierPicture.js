import { TabLayout } from '@/components/layouts'

const SupplierPictureRouter = {
    path: '/srm/supplier',
    name: 'srm/supplier',
    meta: {
        title: '供应商管理',
        titleI18nKey: 'i18n_menu_RdXEd_8e15d1c1',
        icon: 'contacts',
        keepAlive: false
    },
    component: TabLayout,
    children: [
        {
            path: '/srm/supplier/supplierPicture',
            name: 'SupplierPicture',
            meta: {
                title: '供应商风险',
                titleI18nKey: 'i18n_menu_RdXEd_8e15d1c1',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'SupplierPicture' */ '@/views/srm/supplier/SupplierPicture.vue')
        }
        // {
        //     path: '/srm/supplier/supplierValue',
        //     name: 'SupplierValue',
        //     meta: {
        //         title: '应用集市',
        //         titleI18nKey: 'i18n_menu_aRdj_298194f2',
        //         keepAlive: false
        //     },
        //     component: () => import(/* webpackChunkName: 'SupplierValue' */ '@/views/srm/supplier/SupplierValue.vue')
        // }
    ]
}

export default SupplierPictureRouter
