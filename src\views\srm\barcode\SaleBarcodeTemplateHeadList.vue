<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 详情界面 -->
    <sale-barcode-template-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>

import SaleBarcodeTemplateHeadDetail from './modules/SaleBarcodeTemplateHeadDetail'
import { ListMixin } from '@comp/template/list/ListMixin'
import { getAction, postAction } from '@/api/manage'
import { getLodop } from '@/utils/LodopFuncs'

var LODOP //声明为全局变量

export default {
    mixins: [ListMixin],
    components: {
        SaleBarcodeTemplateHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeTemplate',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fWIrAy_922ac69c`, '打印模板编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'barcode#saleTemplate:detail'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        clickFn: this.preview,
                        authorityCode: 'barcode#saleTemplate:preview'
                    }
                ]
            },
            url: {
                list: '/base/barcode/saleBarcodeTemplateHead/list',
                add: '/base/barcode/saleBarcodeTemplateHead/add',
                delete: '/base/barcode/saleBarcodeTemplateHead/delete',
                deleteBatch: '/base/barcode/saleBarcodeTemplateHead/deleteBatch',
                enabled: '/base/barcode/saleBarcodeTemplateHead/enabled',
                disabled: '/base/barcode/saleBarcodeTemplateHead/disabled',
                templatePreview: '/base/barcode/saleBarcodeTemplateHead/templatePreview',
                columns: 'saleBarcodeTemplateHeadList'
            }
        }
    },
    methods: {
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        preview (row) {
            let that = this
            getAction(this.url.templatePreview+'?id='+row.id, {}).then((res) => {
                if (res.success) {debugger
                    LODOP = getLodop()//调用getLodop获取LODOP对象
                    eval(res.message)
                    LODOP.SET_PREVIEW_WINDOW(0, 3, 0, 600, 400, '')
                    LODOP.PREVIEW()
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
            })
        }
    }
}
</script>