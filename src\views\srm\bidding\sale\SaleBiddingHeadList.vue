<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showDetailPage && !showUploadFilePage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url" 
      @afterChangeTab="handleAfterChangeTab" />
    <sale-bidding-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <upload-file-modal
      v-if="showUploadFilePage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideUploadFilePage" />

  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleBiddingDetail from './modules/SaleBiddingDetail'
import layIM from '@/utils/im/layIM.js'
import UploadFileModal from './modules/UploadFileModal'
import { getAction } from '@/api/manage'

import { SET_CACHE_VUEX_CURRENT_EDIT_ROW, SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapMutations } from 'vuex'

const URL = '/base/heartBeat/noToken/check'

export default {
    mixins: [ListMixin],
    components: {
        SaleBiddingDetail,
        UploadFileModal
    },
    data () {
        return {
            showUploadFilePage: false,
            pageData: {
                businessType: 'bidding',
                form: {
                    biddingNumber: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tenderNumber`, '招标单号'),
                        fieldName: 'biddingNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTenderNumberTips`, '请输入招标单号')
                    }
                ],
                optColumnWidth: 200,
                optColumnList: [
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat, authorityCode: 'bidding#saleBiddingHead:creatGruopChat'},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'bidding#saleBiddingHead:queryById'},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileUpload`, '文件上传'), clickFn: this.uploadFile, authorityCode: 'bidding#saleBiddingHead:upload'},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supBiddingHall`, '投标大厅'), clickFn: this.handleTender}
                ]
            },
            url: {
                add: '/bidding/saleBiddingHead/add',
                list: '/bidding/saleBiddingHead/list',
                columns: 'saleBiddingHead',
                excelCode: 'ebidding'
            }
        }
    },
    created () {
        this.getUrlParam()
    },
    mounted () {
        this.serachCountTabs('/bidding/saleBiddingHead/counts')
    },
    methods: {
        // 缓存当前行数据
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW,
            setCacheVuexCurrentEditRow: SET_CACHE_VUEX_CURRENT_EDIT_ROW
        }),
        handleChat (row) {
            let { id } = row
            let recordNumber = row.biddingNumber || id
            // 创建聊天
            layIM.creatGruopChat({id, type: 'SaleBidding', url: this.url || '', recordNumber})
        },
        allowChat (row){
            return false
        },
        handleView (row) {
            this.currentEditRow = row
            this.showDetailPage = true
        },
        getUrlParam (){
            let templateNumber = this.$route.query.templateNumber
            let templateVersion = this.$route.query.templateVersion
            let busAccount = this.$route.query.busAccount
            let id = this.$route.query.id
            if(templateNumber && templateVersion && id ){
                let row = {}
                row['templateNumber']=templateNumber
                row['templateVersion']=templateVersion
                row['id']=id
                row['busAccount']=busAccount
                this.currentEditRow = row
                this.showDetailPage = true
            }
        },
        handleTender (row) {
            let currentRow = Object.assign({}, row, { role: 'sale' })
            this.setVuexCurrentEditRow({})
            this.setCacheVuexCurrentEditRow({ row: currentRow })
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/applyHall',
                query: {
                    _t,
                    type: 'tender' // 投标
                }
            })
            window.open(routeUrl.href, '_blank')
        },
        uploadFile (row) {
            if (!row.bidEndTime_DateMaps) return
            this.now().then(res => {
                const serverTime = res?.timestamp || Date.now()
                // 判断是否已超过 投标开始时间
                if (serverTime >= row.bidEndTime_DateMaps) {
                    let tip = [
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APKI_2c96dd98`, '当前时间'),
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IBR_1725c54`, '已超过'),
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bidEndTime`, '投标截止时间')
                    ]
                    this.$message.error(tip.join(''))
                    return
                }
                this.currentEditRow = row
                this.showUploadFilePage = true
            })
        },
        now () {
            return getAction(URL)
        },
        hideUploadFilePage (){
            this.showUploadFilePage = false
        }
    }
}
</script>
<style lang="less" scoped>
.radio-wrap {
    display: block;
    height: 30px;
}
</style>