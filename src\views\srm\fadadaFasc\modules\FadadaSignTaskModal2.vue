<template>
  <div>
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      @goBack="goBack" />
  </div>
</template>

<script>
import { tileEditPageMixin } from '@comp/template/tileStyle/tileEditPageMixin'
export default {
    mixins: [tileEditPageMixin],
    name: 'FadadaSignTaskModal',
    data () {
        return {
            pageData: {
                title: '',
            form: {
            busAccount:'',
            busType:'',
            busNumber:'',
            toElsAccount:'',
            relationId:'',
            signTaskSubject:'',
            signDocType:'',
            expiresTime:'',
            catalogId:'',
            autoStart:'',
            autoStart:'',
            autoFinish:'',
            autoFinish:'',
            autoFillFinalize:'',
            controlTask:'',
            businessTypeId:'',
            signInOrder:'',
            certcaOrg:'',
            sendStatus:'',
            signerVindicateStatus:'',
            signTaskId:'',
            signTaskStatus:'',
            reason:'',
            purchaseEsignStatus:'',
            saleEsignStatus:'',
            sendBack:'',
            firstSeal:'',
            onlineSealed:'',
            signFileUploaded:'',
            fbk1:'',
            fbk2:'',
            fbk3:'',
            fbk4:'',
            fbk5:'',
            fbk6:'',
            fbk7:'',
            fbk8:'',
            fbk9:'',
            fbk10:'',
                            },
                            validRules: {
            busAccount:[{ required: true, message: '请输入业务账号!' },{max: 50, message: '内容长度不能超过50个字符'}],
            busType:[{ required: true, message: '请输入业务类型（合同等）!' },{max: 50, message: '内容长度不能超过50个字符'}],
            busNumber:[{max: 50, message: '内容长度不能超过50个字符'}],
            toElsAccount:[{ required: true, message: '请输入供方ELS账号!' },{max: 50, message: '内容长度不能超过50个字符'}],
            relationId:[{max: 50, message: '内容长度不能超过50个字符'}],
            signTaskSubject:[{max: 100, message: '内容长度不能超过100个字符'}],
            signDocType:[{max: 100, message: '内容长度不能超过100个字符'}],
            catalogId:[{max: 100, message: '内容长度不能超过100个字符'}],
            autoStart:[{max: 10, message: '内容长度不能超过10个字符'}],
            autoStart:[{max: 10, message: '内容长度不能超过10个字符'}],
            autoFinish:[{max: 10, message: '内容长度不能超过10个字符'}],
            autoFinish:[{max: 10, message: '内容长度不能超过10个字符'}],
            autoFillFinalize:[{max: 10, message: '内容长度不能超过10个字符'}],
            controlTask:[{max: 100, message: '内容长度不能超过100个字符'}],
            businessTypeId:[{max: 100, message: '内容长度不能超过100个字符'}],
            signInOrder:[{max: 100, message: '内容长度不能超过100个字符'}],
            certcaOrg:[{max: 100, message: '内容长度不能超过100个字符'}],
            sendStatus:[{max: 2, message: '内容长度不能超过2个字符'}],
            signerVindicateStatus:[{max: 2, message: '内容长度不能超过2个字符'}],
            signTaskId:[{max: 50, message: '内容长度不能超过50个字符'}],
            signTaskStatus:[{max: 2, message: '内容长度不能超过2个字符'}],
            reason:[{max: 65535, message: '内容长度不能超过65535个字符'}],
            purchaseEsignStatus:[{max: 2, message: '内容长度不能超过2个字符'}],
            saleEsignStatus:[{ required: true, message: '请输入供方签署状态!' },{max: 2, message: '内容长度不能超过2个字符'}],
            sendBack:[{max: 2, message: '内容长度不能超过2个字符'}],
            firstSeal:[{max: 20, message: '内容长度不能超过20个字符'}],
            onlineSealed:[{max: 20, message: '内容长度不能超过20个字符'}],
            signFileUploaded:[{max: 20, message: '内容长度不能超过20个字符'}],
            fbk1:[{max: 100, message: '内容长度不能超过100个字符'}],
            fbk2:[{max: 100, message: '内容长度不能超过100个字符'}],
            fbk3:[{max: 100, message: '内容长度不能超过100个字符'}],
            fbk4:[{max: 100, message: '内容长度不能超过100个字符'}],
            fbk5:[{max: 100, message: '内容长度不能超过100个字符'}],
            fbk6:[{max: 100, message: '内容长度不能超过100个字符'}],
            fbk7:[{max: 100, message: '内容长度不能超过100个字符'}],
            fbk8:[{max: 100, message: '内容长度不能超过100个字符'}],
            fbk9:[{max: 100, message: '内容长度不能超过100个字符'}],
            fbk10:[{max: 100, message: '内容长度不能超过100个字符'}],
                        },
                panels: [
                    {
                        title: '基本信息',
                        content: {
                            type: 'form',
                            ref: 'mainForm',
                            list: [
            {
                type: 'input', 
                label: '业务账号',
                fieldName: 'busAccount', 
                placeholder: '请输入业务账号'
            },
            {
                type: 'input', 
                label: '业务类型（合同等）',
                fieldName: 'busType', 
                placeholder: '请输入业务类型（合同等）'
            },
            {
                type: 'input', 
                label: '业务编号',
                fieldName: 'busNumber', 
                placeholder: '请输入业务编号'
            },
            {
                type: 'input', 
                label: '供方ELS账号',
                fieldName: 'toElsAccount', 
                placeholder: '请输入供方ELS账号'
            },
            {
                type: 'input', 
                label: '业务关联id',
                fieldName: 'relationId', 
                placeholder: '请输入业务关联id'
            },
            {
                type: 'input', 
                label: '签署主题',
                fieldName: 'signTaskSubject', 
                placeholder: '请输入签署主题'
            },
            {
                type: 'input', 
                label: '签署文档类型',
                fieldName: 'signDocType', 
                placeholder: '请输入签署文档类型'
            },
             {
                type: 'date', 
                label: '任务过期时间',
                fieldName: 'expiresTime', 
                placeholder: '请输入任务过期时间'
            },
            {
                type: 'input', 
                label: '签署任务归属的发起方文件夹',
                fieldName: 'catalogId', 
                placeholder: '请输入签署任务归属的发起方文件夹'
            },
            {
                type: 'input', 
                label: '是否自动提交签署任务',
                fieldName: 'autoStart', 
                placeholder: '请输入是否自动提交签署任务'
            },
            {
                type: 'input', 
                label: '是否已开启',
                fieldName: 'autoStart', 
                placeholder: '请输入是否已开启'
            },
            {
                type: 'input', 
                label: '是否自动结束',
                fieldName: 'autoFinish', 
                placeholder: '请输入是否自动结束'
            },
            {
                type: 'input', 
                label: '是否结束',
                fieldName: 'autoFinish', 
                placeholder: '请输入是否结束'
            },
            {
                type: 'input', 
                label: '是否自动定稿',
                fieldName: 'autoFillFinalize', 
                placeholder: '请输入是否自动定稿'
            },
            {
                type: 'input', 
                label: '签署任务控制',
                fieldName: 'controlTask', 
                placeholder: '请输入签署任务控制'
            },
            {
                type: 'input', 
                label: '签署业务类型id',
                fieldName: 'businessTypeId', 
                placeholder: '请输入签署业务类型id'
            },
            {
                type: 'input', 
                label: '签署流程是否有序',
                fieldName: 'signInOrder', 
                placeholder: '请输入签署流程是否有序'
            },
            {
                type: 'input', 
                label: '签署参与方使用的签章证书颁发机构',
                fieldName: 'certcaOrg', 
                placeholder: '请输入签署参与方使用的签章证书颁发机构'
            },
            {
                type: 'input', 
                label: '发送状态',
                fieldName: 'sendStatus', 
                placeholder: '请输入发送状态'
            },
            {
                type: 'input', 
                label: '签署人维护状态',
                fieldName: 'signerVindicateStatus', 
                placeholder: '请输入签署人维护状态'
            },
            {
                type: 'input', 
                label: '签署任务ID',
                fieldName: 'signTaskId', 
                placeholder: '请输入签署任务ID'
            },
            {
                type: 'input', 
                label: 'sign_progress：签署中,sign_completed：签署已完成 ,task_finished：任务已完成,task_terminated：任务已终止',
                fieldName: 'signTaskStatus', 
                placeholder: '请输入sign_progress：签署中,sign_completed：签署已完成 ,task_finished：任务已完成,task_terminated：任务已终止'
            },
            {
                type: 'input', 
                label: '签署失败原因',
                fieldName: 'reason', 
                placeholder: '请输入签署失败原因'
            },
            {
                type: 'input', 
                label: '采方签署状态',
                fieldName: 'purchaseEsignStatus', 
                placeholder: '请输入采方签署状态'
            },
            {
                type: 'input', 
                label: '供方签署状态',
                fieldName: 'saleEsignStatus', 
                placeholder: '请输入供方签署状态'
            },
            {
                type: 'input', 
                label: '是否退回（0-否，1-是）',
                fieldName: 'sendBack', 
                placeholder: '请输入是否退回（0-否，1-是）'
            },
            {
                type: 'input', 
                label: '哪一方先盖章(purchase-采方，sale-供方)',
                fieldName: 'firstSeal', 
                placeholder: '请输入哪一方先盖章(purchase-采方，sale-供方)'
            },
            {
                type: 'input', 
                label: '供应商是否线上盖章(默认是)',
                fieldName: 'onlineSealed', 
                placeholder: '请输入供应商是否线上盖章(默认是)'
            },
            {
                type: 'input', 
                label: '供应商签署文件是否上传',
                fieldName: 'signFileUploaded', 
                placeholder: '请输入供应商签署文件是否上传'
            },
            {
                type: 'input', 
                label: 'fbk1',
                fieldName: 'fbk1', 
                placeholder: '请输入fbk1'
            },
            {
                type: 'input', 
                label: 'fbk2',
                fieldName: 'fbk2', 
                placeholder: '请输入fbk2'
            },
            {
                type: 'input', 
                label: 'fbk3',
                fieldName: 'fbk3', 
                placeholder: '请输入fbk3'
            },
            {
                type: 'input', 
                label: 'fbk4',
                fieldName: 'fbk4', 
                placeholder: '请输入fbk4'
            },
            {
                type: 'input', 
                label: 'fbk5',
                fieldName: 'fbk5', 
                placeholder: '请输入fbk5'
            },
            {
                type: 'input', 
                label: 'fbk6',
                fieldName: 'fbk6', 
                placeholder: '请输入fbk6'
            },
            {
                type: 'input', 
                label: 'fbk7',
                fieldName: 'fbk7', 
                placeholder: '请输入fbk7'
            },
            {
                type: 'input', 
                label: 'fbk8',
                fieldName: 'fbk8', 
                placeholder: '请输入fbk8'
            },
            {
                type: 'input', 
                label: 'fbk9',
                fieldName: 'fbk9', 
                placeholder: '请输入fbk9'
            },
            {
                type: 'input', 
                label: 'fbk10',
                fieldName: 'fbk10', 
                placeholder: '请输入fbk10'
            },
                            ],
                            
                        button: []
                        }
                    },
                    {
                        title: '法大大签署人',
                        content: {
                            type: 'table',
                            ref: 'fadadaTaskActorList',
                            columns: [
                            { 
                                    type: 'checkbox', width: 40 
                                },
            {
              title: 'headId',
              field: 'headId',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'srm账号',
              field: 'subAccount',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '参与方标识(比如：甲方)',
              field: 'actorId',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '参与方主体类型',
              field: 'actorType',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '参与方具体名称',
              field: 'actorName',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '参与方权限',
              field: 'permissions',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '参与方法大大账号Id',
              field: 'actorOpenId',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '参与方企业成员列表',
              field: 'actorCorpMembers',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '参与方身份名称匹配信息',
              field: 'identNameForMatch',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '参与方证件号码匹配信息',
              field: 'certNoForMatch',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '是否需要法大大平台送达',
              field: 'sendNotification',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '送达方式',
              field: 'notifyWay',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '送达地址',
              field: 'notifyAddress',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '印章Id',
              field: 'sealId',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '签署文档Id(法大大)',
              field: 'docFileId',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '印章名称',
              field: 'sealName',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '签署顺序',
              field: 'orderNo',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '是否免验证签',
              field: 'requestVerifyFree',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '企业签署要求经办人签名',
              field: 'requestMemberSign',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '控件类型',
              field: 'fieldType',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '签署定位模式',
              field: 'positionMode',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '定位页码',
              field: 'positionPageNo',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '横坐标',
              field: 'positionX',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '纵坐标',
              field: 'positionY',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '关键字',
              field: 'positionKeyword',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '关键字中心点横向偏移量',
              field: 'keywordOffsetX',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: '关键字中心点纵向偏移量',
              field: 'keywordOffsetY',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk1',
              field: 'fbk1',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk2',
              field: 'fbk2',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk3',
              field: 'fbk3',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk4',
              field: 'fbk4',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk5',
              field: 'fbk5',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk6',
              field: 'fbk6',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk7',
              field: 'fbk7',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk8',
              field: 'fbk8',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk9',
              field: 'fbk9',
              width: 100,
              editRender: {name: 'AInput'}
            },
            {
              title: 'fbk10',
              field: 'fbk10',
              width: 100,
              editRender: {name: 'AInput'}
            },
                            ],
                            validRules: {
            headId: [{ required: true, message: '请输入headId!' },{max: 50, message: '内容长度不能超过50个字符'}],
            subAccount: [{max: 100, message: '内容长度不能超过100个字符'}],
            actorId: [{max: 100, message: '内容长度不能超过100个字符'}],
            actorType: [{max: 100, message: '内容长度不能超过100个字符'}],
            actorName: [{max: 50, message: '内容长度不能超过50个字符'}],
            permissions: [{max: 100, message: '内容长度不能超过100个字符'}],
            actorOpenId: [{max: 100, message: '内容长度不能超过100个字符'}],
            actorCorpMembers: [{max: 100, message: '内容长度不能超过100个字符'}],
            identNameForMatch: [{max: 100, message: '内容长度不能超过100个字符'}],
            certNoForMatch: [{max: 100, message: '内容长度不能超过100个字符'}],
            sendNotification: [{max: 100, message: '内容长度不能超过100个字符'}],
            notifyWay: [{max: 100, message: '内容长度不能超过100个字符'}],
            notifyAddress: [{max: 100, message: '内容长度不能超过100个字符'}],
            sealId: [{max: 100, message: '内容长度不能超过100个字符'}],
            docFileId: [{max: 100, message: '内容长度不能超过100个字符'}],
            sealName: [{max: 100, message: '内容长度不能超过100个字符'}],
            orderNo: [{max: 100, message: '内容长度不能超过100个字符'}],
            requestVerifyFree: [{max: 10, message: '内容长度不能超过10个字符'}],
            requestMemberSign: [{max: 100, message: '内容长度不能超过100个字符'}],
            fieldType: [{max: 100, message: '内容长度不能超过100个字符'}],
            positionMode: [{max: 100, message: '内容长度不能超过100个字符'}],
            positionPageNo: [{max: 100, message: '内容长度不能超过100个字符'}],
            positionX: [{max: 100, message: '内容长度不能超过100个字符'}],
            positionY: [{max: 100, message: '内容长度不能超过100个字符'}],
            positionKeyword: [{max: 100, message: '内容长度不能超过100个字符'}],
            keywordOffsetX: [{max: 100, message: '内容长度不能超过100个字符'}],
            keywordOffsetY: [{max: 100, message: '内容长度不能超过100个字符'}],
            fbk1: [{max: 50, message: '内容长度不能超过50个字符'}],
            fbk2: [{max: 50, message: '内容长度不能超过50个字符'}],
            fbk3: [{max: 50, message: '内容长度不能超过50个字符'}],
            fbk4: [{max: 50, message: '内容长度不能超过50个字符'}],
            fbk5: [{max: 50, message: '内容长度不能超过50个字符'}],
            fbk6: [{max: 50, message: '内容长度不能超过50个字符'}],
            fbk7: [{max: 50, message: '内容长度不能超过50个字符'}],
            fbk8: [{max: 50, message: '内容长度不能超过50个字符'}],
            fbk9: [{max: 50, message: '内容长度不能超过50个字符'}],
            fbk10: [{max: 50, message: '内容长度不能超过50个字符'}],
                            },
                            toolbarButton: [
                            {title: '新增', type: 'primary', clickFn: this.addRow},
                            {title: '删除', clickFn: this.deleteRow}
                        ]
                        },
                        
                    }
                ]
            },
            url: {
          add: "/electronsign.fadada/fadadaSignTask/add",
          edit: "/electronsign.fadada/fadadaSignTask/edit",
          detail: '/electronsign.fadada/fadadaSignTask/queryById'
          }
          
        }
    },
    methods: {
                deleteRow () {
                    this.$refs.editPage.deleteRow()
                },
                addRow () {
                    this.$refs.editPage.addRow()
                }
            }
}
</script>