<template>
  <multipane
    class="custom-resizer"
    layout="vertical">
    <div class="pane controls-list">
      <control-list />
    </div>
    <multipane-resizer></multipane-resizer>
    <div class="pane form-view">
      <control-view />
    </div>
    <multipane-resizer></multipane-resizer>
    <div
      class="pane"
      :style="{ flexGrow: 1 }">
      <control-setting
        :attachmentInfo="attachmentInfo"
      />
    </div>
  </multipane>
</template>

<script>
import { Multipane, MultipaneResizer } from 'vue-multipane'
import ControlList from './component/ControlList'
import ControlView from './component/ControlView'
import ControlSetting from './component/ControlSetting'
import { ACCEPT } from '@/utils/constant'
import { mapActions } from 'vuex'
export default {
    name: 'HFormContent',
    props: ['initData'],
    provide () {
        return {
            initData: this.initData
        }
    },
    data () {
        return {
            attachmentInfo: {
                accept: ACCEPT,
                action: '/els/attachment/purchaseAttachment/upload', 
                headers: {'X-Access-Token': this.$ls.get('Access-Token')}, 
                itemInfo: [],
                data: {
                    businessType: 'survey',
                    headId: this.initData.id
                },
                upload: [],
                fileName: []
            }
        }
    },
    components: {
        Multipane,
        MultipaneResizer,
        ControlList,
        ControlView,
        ControlSetting
    },
    methods: {
        ...mapActions([ 'questionDataInit' ])
    }
}
</script>

<style lang="less" scoped>
  .custom-resizer {
    width: 100%;
    // min-height: 500px;
    height: 100%;
    background: white;
  }
  .custom-resizer > .pane {
    text-align: left;
    padding: 15px;
    /*height: 100%;*/
    // min-height: 700px;
    border: 1px solid #ccc;
    overflow-y: scroll;
  }
  .custom-resizer > .multipane-resizer {
    margin: 0;
    left: 0;
    // margin-top: 20%;
    position: relative;
    &:before {
      display: block;
      content: "";
      width: 3px;
      height: 40px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -20px;
      margin-left: -1.5px;
      border-left: 1px solid #ccc;
      border-right: 1px solid #ccc;
    }
    &:hover {
      &:before {
        border-color: #999;
      }
    }
  }
  .controls-list {
   
    width: 135px;
  }
  .form-view {
    width: 50%;
  }
</style>
