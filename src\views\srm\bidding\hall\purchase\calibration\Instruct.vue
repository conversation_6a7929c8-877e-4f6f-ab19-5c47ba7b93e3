<template>
  <div class="Instruct">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      @content-header-record="handleRecord"
      @content-header-submit="beforeSubmit"
      @content-header-save="beforeSave"
      @content-header-showFlow="showFlow"
      @content-header-cancelAudit="cancelAudit"
      @content-header-exportRecord="exportRecord"
      @content-header-generateContract="handleGenerateContract"
    />

    <div
      class="container"
      :style="style">

      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="table">
            <vxe-grid
              ref="biddingSupplierList"
              v-bind="defaultGridOption"
              :columns="columns">
              <template slot="empty">
                <a-empty />
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>

    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="templateVisible"
      @ok="selectedTemplate">
      <template slot="footer">
        <a-button
          key="back"
          @click="handleTemplateCancel">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedTemplate">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"/>
    </a-modal>
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="vuex_currentEditRow" />

  </div>
</template>

<script lang="jsx">
import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import { getAction, postAction, httpAction, downFile } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
import { SEQ_COLUMN } from '@/utils/constant.js'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        flowViewModal,
        'content-header': ContentHeader
    },
    data () {
        return {
            businessType: '',
            submitLoading: false,
            templateVisible: false,
            nextOpt: true,
            currentEditRow: {},
            currentRow: {},
            templateNumber: '',
            templateOpts: [],
            flowView: false,
            flowId: '',
            confirmLoading: false,
            detailPromise: {},
            purchaseBiddingItemList: [],
            form: {},
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), type: 'primary', event: 'exportRecord', authorityCode: 'bidding#purchaseBiddingHead:export'},
                // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_bLne_36b82195`, '生成合同'), type: 'primary', event: 'generateContract', showCondition: this.generateContractlConditionBtn},
                // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatPriceRecord`, '生成价格记录'), type: 'primary', event: 'record', authorityCode: 'bidding#purchaseBiddingHead:manualCreatePrce'},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'), type: 'primary', event: 'submit', authorityCode: 'bidding#purchaseBiddingHead:submit'},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'), type: '', event: 'cancelAudit', showCondition: this.showRevokeApprovalBtn },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', event: 'showFlow', showCondition: this.showViewProcessBtn },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', event: 'save', showCondition: this.showSave, authorityCode: 'bidding#purchaseBiddingHead:save'}
            ],
            showHeader: true,
            height: 0,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                height: 400,
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'click', mode: 'cell' }
            },
            columns: [
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'),
                    fieldLabelI18nKey: 'i18n_title_supplierELSAccount',
                    field: 'toElsAccount',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'),
                    fieldLabelI18nKey: 'i18n_massProdHead88b_supplierErpCode',
                    field: 'supplierCode',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                    fieldLabelI18nKey: 'i18n_massProdHeade95_supplierName',
                    field: 'supplierName',
                    width: 150
                }
            ],
            options: [
                {
                    value: '2',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notWonTheBid`, '未中标')
                },
                {
                    value: '3',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_wonTheBid`, '已中标')
                }
            ]
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        // 判断是否为拆分方式
        isQuotaWay () {
            return this.vuex_currentEditRow.quotaWay === '1'
        }
    },

    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        getDetailPromise () {
            const { id = '' } = this.vuex_currentEditRow || {}
            const url = '/bidding/purchaseBiddingHead/queryById'
            return getAction(url, { id })
        },
        showFlow () {
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        showRevokeApprovalBtn () {
            const { resultAuditStatus, resultAudit } = this.detailPromise || {}
            if ((resultAuditStatus == '1' || resultAuditStatus == '3') && resultAudit === '1') {
                return true
            }
            return false
        },
        handleGenerateContract () {
            // 增加自定义校验, 根据某个字段判断是否已经发生成过合同
            // if (this.vuex_currentEditRow.xxx) {
            //     this.$message.error('已生成合同')
            //     return
            // }
            let { elsAccount = '' } = this.vuex_currentEditRow || {}
            this.openModal(elsAccount)
        },
        queryTemplateList (elsAccount) {
            const BUSINESS_TYPE = 'contract'
            let params = {
                pageSize: 100,
                elsAccount: elsAccount,
                templateStatus: '1',
                businessType: BUSINESS_TYPE,
                pageNo: 1
            }
            return getAction('/template/templateHead/getListByType', params)
        },
        openModal (elsAccount) {
            this.queryTemplateList(elsAccount).then(res => {
                if (!res.success) {
                    this.$message.warning(res.message)
                    return 
                }
                if (res.result.length > 0) {
                    let options = res.result.map(item => {
                        return {
                            value: item.templateNumber,
                            title: item.templateName,
                            version: item.templateVersion,
                            account: item.elsAccount
                        }
                    })
                    this.templateOpts = options
                    // 只有单个模板直接新建
                    if (this.templateOpts && this.templateOpts.length === 1) {
                        this.templateNumber = this.templateOpts[0].value
                        this.selectedTemplate()
                    } else {
                        // 有多个模板先选择在新建
                        this.templateVisible = true
                    }
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                }
            })
        },
        generateContractlConditionBtn () {
            const { biddingStatus, generateContract} = this.detailPromise || {}
            return biddingStatus === '5' && generateContract=='0'
        },
        handleTemplateCancel () {
            this.templateVisible = false
        },
        selectedTemplate () {
            if (this.templateNumber) {
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == this.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version,
                    templateAccount: template[0].account,
                    id: this.vuex_currentEditRow.id
                }
                this.templateVisible = false
                this.submitLoading = false
                this.postUpdateData('/bidding/purchaseBiddingHead/generateContract', params)
            }
        },
        postUpdateData (url, row){
            postAction(url, row).then(res => {
                if (!res.success) {
                    this.$message.error(res.message)
                    return 
                }
                this.$info({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                    content: res.message,
                    onOk: () =>{
                        // 更新vuex 当前行数据
                        this.updateVuexCurrentEditRow()
                        this.getHeadDataRow()
                    }
                })
            })
        },
        showViewProcessBtn () {
            const {resultAuditStatus, resultAudit } = this.detailPromise || {}
            if (resultAuditStatus == '1'  && resultAudit === '1') {
                return true
            }
            return false
        },
        showSave () {
            const {biddingStatus} = this.detailPromise || {}
            if (biddingStatus != '5') {
                return true
            }
            return false
        },
        closeFlowView () {
            this.flowView = false
        },
        cancelAudit () {
            let param = {}
            param['businessType'] = 'resultBidding'
            param['businessId'] = this.vuex_currentEditRow.id
            param['rootProcessInstanceId'] = this.flowId
            this.confirmLoading = true
            httpAction('/a1bpmn/audit/api/cancel', param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.init ()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        //导出
        exportRecord () {
            const exportUrl='/bidding/purchaseBiddingHead/exportConfirmBidXls'
            const params = { id: this.vuex_currentEditRow.id, quotaWay: this.vuex_currentEditRow.quotaWay }
            downFile(exportUrl, params).then((data) => {
                if (data.type=='application/json') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), '定标请示.xlsx')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '定标请示.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            }).finally(() => {
                this.gridLoading = false
            })
        },
        getData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingHead/queryConfirmBidById'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        this.btns = []
                        return
                    }
                    const { biddingSupplierList = [] } = res.result || {}
                    this.defaultGridOption.data = biddingSupplierList
                    if (!biddingSupplierList.length) {
                        return
                    }
                    const { purchaseBiddingItemList = [] } = biddingSupplierList[0] || {}
                    if (!purchaseBiddingItemList.length) {
                        return
                    }
                    this.purchaseBiddingItemList = purchaseBiddingItemList
                    this.setColumns()
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        setColumns () {
            console.log('purchaseBiddingItemList', this.purchaseBiddingItemList)
            const itemColumns = this.purchaseBiddingItemList.map((item, idx) => {
                console.log('idx :>> ', idx)
                return {
                    title: item.materialDesc,
                    children: [
                        {
                            title: '报价',
                            fieldLabelI18nKey: 'i18n_title_offer',
                            field: `price_${idx}`,
                            slots: {
                                default: ({ row }) => {
                                    return [
                                        (<span>{ row.purchaseBiddingItemList[idx].price || '' }</span>)
                                    ]
                                }
                            },
                            width: 150
                        },
                        {
                            title: '授标',
                            fieldLabelI18nKey: 'i18n_title_contractAward',
                            field: `itemStatus_${idx}`,
                            editRender: {
                                enabled: this.detailPromise.biddingStatus == '5' ? false : true
                            },
                            slots: {
                                default: ({ row }) => {
                                    let label = row.purchaseBiddingItemList[idx].itemStatus === '3' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_wonTheBid`, '已中标') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notWonTheBid`, '未中标')

                                    return [
                                        (<span>{ label }</span>)
                                    ]
                                },
                                edit: (({row}) => {
                                    const opts = this.options.map(n => {
                                        return (<vxe-option value={ n.value } label={n.label}></vxe-option>)
                                    })

                                    return [
                                        (<vxe-select vModel={ row.purchaseBiddingItemList[idx].itemStatus }>
                                            { opts }
                                        </vxe-select>)
                                    ]
                                })
                            },
                            width: 150
                        },
                        {
                            title: '拆分比例(%)',
                            fieldLabelI18nKey: 'i18n_title_splitRatio',
                            field: `quota_${idx}`,
                            visible: this.isQuotaWay,
                            editRender: {
                                enabled: this.detailPromise.biddingStatus == '5' ? false : true,
                                autofocus: '.custom-cell-number input'
                            },
                            slots: {
                                default: ({ row }) => {
                                    return [
                                        (<span>{ row.purchaseBiddingItemList[idx].quota || 0 }</span>)
                                    ]
                                },
                                edit: (({row}) => {
                                    const props = {
                                        type: 'number',
                                        min: '0'
                                    }

                                    const on = {
                                        change: ()=>{
                                            const quota = row.purchaseBiddingItemList[idx].quota
                                            console.log('quota', quota)
                                            if (quota > 0) { // 输入拆分比例，切换已中标
                                                row.purchaseBiddingItemList[idx].itemStatus = '3'
                                            } else {// 删除拆分比例，切换未中标
                                                row.purchaseBiddingItemList[idx].itemStatus = '2'
                                            }
                                        }
                                    }
                                    return [
                                        (<vxe-input class='custom-cell-number' {...{on}} vModel={row.purchaseBiddingItemList[idx].quota} {...{ props }} />)
                                    ]
                                })
                            },
                            width: 150
                        }
                    ]
                }
            })
            // 去除重复的物料的
            const COLUMNS = this.columns.filter((column) => {
                if (!column.hasOwnProperty('children')) {
                    return column
                }
            })
            this.columns = [...COLUMNS, ...itemColumns]
        },
        beforeSave () {
            let url = '/bidding/purchaseBiddingHead/checkMaterialNumber'
            let params = {headId: this.vuex_currentEditRow.id}
            this.confirmLoading = true
            getAction(url, params)
                .then(res => {
                    // 返回的数组不为空，则存在无物料号的行，需弹窗提示
                    if(res.result.length != 0){
                        this.$confirm({
                            content: this.$srmI18n(`${this.$getLangAccount()}#`, '定标结果中存在无物料号的数据（可补充物料号），请确定是否提交'),
                            onOk: () => {
                                this.handleSave()
                            }
                        })
                    }else{
                        // 返回数组为空，则所有行数据都有物料号，正常提交
                        this.handleSave()
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        beforeSubmit () {
            let url = '/bidding/purchaseBiddingHead/checkMaterialNumber'
            let params = {headId: this.vuex_currentEditRow.id}
            this.confirmLoading = true
            getAction(url, params)
                .then(res => {
                    // 返回的数组不为空，则存在无物料号的行，需弹窗提示
                    if(res.result.length != 0){
                        this.$confirm({
                            content: this.$srmI18n(`${this.$getLangAccount()}#`, '定标结果中存在无物料号的数据（可补充物料号），请确定是否提交'),
                            onOk: () => {
                                this.handleSubmit()
                            }
                        })
                    }else{
                        // 返回数组为空，则所有行数据都有物料号，正常提交
                        this.handleSubmit()
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleSave (handleSubmit) {
            // this.ifNoMaterialNumber()
            let biddingSupplierList = this.defaultGridOption.data || []
            const callback = () => {
                if (this.isQuotaWay) {
                    // 完成拆分百分比之和不能超过 100 的校验
                    for (let i = 0; i < this.purchaseBiddingItemList.length; i++) {
                        let materialDesc = this.purchaseBiddingItemList[i].materialDesc
                        // 中标数量
                        let count = 0
                        //已中标拆分比例
                        let quotaSum = 0
                        biddingSupplierList.forEach(n => {
                            let itemStatus = n.purchaseBiddingItemList[i].itemStatus
                            let quota = n.purchaseBiddingItemList[i].quota || 0
                            if(itemStatus == 3){
                                quotaSum += Number(quota)
                                count ++
                            }
                        })

                        //当物料存在中标单位时,校验其拆分比例
                        if (count > 0) {
                            if (quotaSum !== 100) {
                                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IsBtLSLvzlvntTEUWWWWW_a2ea8f1`, '已中标单位物料拆分比例合计须等于100%'))
                                return
                            }

                        }

                    }
                }


                const params = {
                    id: this.vuex_currentEditRow.id,
                    biddingSupplierList
                }
                const url = '/bidding/purchaseBiddingHead/saveConfirmBid'
                this.confirmLoading = true
                postAction(url, params)
                    .then(async res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.updateVuexCurrentEditRow()
                            if (handleSubmit) handleSubmit()
                            else {
                                await this.getHeadDataRow()
                                this.getData()
                            }
                        }
                    })
                    .finally(() => {
                        this.confirmLoading = false
                    })
            }

            // 校验授标 如果没有中标进行提示
            let contentTitle = ''
            for (let i = 0; i < this.purchaseBiddingItemList.length; i++) {
                //let materialDesc = this.purchaseBiddingItemList[i].materialDesc
                // 授标校验
                let count = 0
                biddingSupplierList.forEach(n => {
                    let itemStatus = n.purchaseBiddingItemList[i].itemStatus
                    if(itemStatus == 3){
                        count++
                    }
                })

                if (count == 0) {
                    contentTitle='当前存在物料未设置任何中标单位，请确定是否继续'
                    break
                }
            }

            if(contentTitle == ''){
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_calibration`, '定标'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmCalibration`, '是否确认当前定标结果?'),
                    onOk () {
                        callback && callback()
                    },
                    onCancel () {
                        console.log('Cancel')
                    }
                })
            }else {
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_calibration`, '定标'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APMKSLLGRLnsBtLWVRIKQtT_45d6c4c0`, contentTitle),
                    onOk () {
                        callback && callback(contentTitle)
                    },
                    onCancel () {
                        console.log('Cancel')
                    }
                })
            }



        },
        async getHeadDataRow () {
            await this.getDetailPromise()
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { timestamp = '', result = {} } = res || {}
                    this.timestamp = timestamp
                    this.detailPromise = result
                    console.log('this.detailPromise', this.detailPromise)
                    this.flowId = result.resultFlowId
                })
        },
        // 提交审批
        handleSubmit () {
            let that = this
            this.getDetailPromise()
                .then(async res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { timestamp = '', result = {} } = res || {}
                    this.timestamp = timestamp
                    const { id, biddingNumber, resultAuditStatus, resultAudit, biddingStatus } = result || {}
                    if (resultAudit === '0') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentNotNeedSubmit`, '该单据无需提交审批'))
                        return
                    }
                    if (resultAuditStatus === '1' || resultAuditStatus ==='2') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unapprovedSubmit`, '未审批的单据才可提交审批'))
                        return
                    }
                    if (biddingStatus !== '5') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingSubmit`, '已定标的单据才可提交审批'))
                        return
                    }

                    let auditSubject = await that.$FlowUtil.convertFlowTitle(result,
                        `招标结果审批, 单号: ${biddingNumber}`
                    )

                    const callback = () => {
                        const params = {
                            businessId: id,
                            businessType: 'resultBidding',
                            auditSubject: auditSubject,
                            params: JSON.stringify(this.vuex_currentEditRow)
                        }
                        const url = '/a1bpmn/audit/api/submit'

                        this.confirmLoading = true
                        postAction(url, params)
                            .then(res => {
                                const type = res.success ? 'success' : 'error'
                                this.$message[type](res.message)
                                if (res.success) {
                                    this.updateVuexCurrentEditRow()
                                }
                            })
                            .finally(() => {
                                this.confirmLoading = false
                            })
                    }
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmApproveTips`, '是否确认提交审批?'),
                        onOk () {
                            callback && callback()
                        },
                        onCancel () {
                            console.log('Cancel')
                        }
                    })
                })
        },
        // 生成价格记录
        handleRecord () {
            this.getDetailPromise()
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { timestamp = '', result = {} } = res || {}
                    this.timestamp = timestamp
                    const { id, biddingStatus, priceCreateWay } = result || {}
                    if (priceCreateWay == '1') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cIumCKLJOcIWxqlObL_4e50da54`, '创建价格方式为自动创建、不可手动生成'))
                        return
                    }
                    if (biddingStatus !== '5') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IIBntFnqbLumdWF_33b87883`, '已定标的单据才可生成价格主数据'))
                        return
                    }
                    const callback = () => {
                        const params = { id }
                        const url = '/bidding/purchaseBiddingHead/manualCreatePrce'

                        this.confirmLoading = true
                        getAction(url, params)
                            .then(res => {
                                const type = res.success ? 'success' : 'error'
                                this.$message[type](res.message)
                            })
                            .finally(() => {
                                this.confirmLoading = false
                            })
                    }
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatPriceInfoTips`, '是否确认生成价格信息记录?'),
                        onOk () {
                            callback && callback()
                        },
                        onCancel () {
                            console.log('Cancel')
                        }
                    })
                })
        },
        async init () {
            this.height = document.documentElement.clientHeight
            await this.getHeadDataRow()
            await this.getData()
        }
    },
    created () {
        this.init()
    }
}
</script>

<style lang="less" scoped>
.Instruct {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
}
</style>

