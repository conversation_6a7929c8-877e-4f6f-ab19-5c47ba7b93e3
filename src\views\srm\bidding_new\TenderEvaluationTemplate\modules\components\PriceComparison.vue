<template>
  <div>
    <a-form-model
      :label-col="labelCol"
      ref="form"
      :wrapper-col="wrapperCol"
      :rules="rules"
      :model="formData">
      <a-row :getterr="12">
        <a-col :span="colSapn">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_dict_ARLF_2e8fa0d0`, '排名规则')"
            prop="rankingRules"
            required>
            <m-select
              v-if="isEdit"
              v-model="formData.rankingRules"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
              dict-code="tenderRankingRules" />
            <span v-else>{{ formData.rankingRules_dictText }}</span>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col
          :span="colSapn"
          v-if="showTenderBidLette">
          <a-form-model-item prop="tenderBidLetterId">
            <span slot="label">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_eBx_17efc2b`, '投标函') }}
              <a-tooltip :title="$srmI18n(`${$getLangAccount()}#i18n_field_VWsMnOSMeBx_3486e1f0`, '请先保存才能获取投标函')">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <a-select
              v-model="formData.tenderBidLetterId"
              v-if="isEdit"
              allowClear
              @dropdownVisibleChange="getTenderBidTetterVoList"
              @change="changeTenderBidLetterId"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`">
              <a-select-option
                v-for="item in tenderBidTetterVoList"
                :value="item.value"
                :key="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
            <span
              v-else
              :title="formData.bidLetterName">{{ formData.bidLetterName }}</span>
          </a-form-model-item>
        </a-col>
        <a-col
          :span="colSapn"
          v-if="showTenderBidLette">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_suAR_2e0a1148`, '报价列名')"
            prop="quoteColumnName">
            <a-select
              v-model="formData.quoteColumnName"
              v-if="isEdit"
              allowClear
              :disabled="!formData.tenderBidLetterId"
              @dropdownVisibleChange="getTenderBidTetterVoListColumn"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`">
              <a-select-option
                v-for="item in quoteColumnNameOptions"
                :value="item.value"
                :key="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
            <span v-else>{{ formData.bidLetterFieldName }}</span>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
import { getAction } from '@views/srm/bidding_new/plugins/manage'
export default {
    props: {
        currentItem: {
            type: Object,
            default: () => {
                return {}
            }
        },
        tenderEvaluationTemplatePriceRegulationInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        pageStatus: {
            type: String,
            default: 'edit'
        },
        isBiddingFileModule: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        tenderEvaluationTemplatePriceRegulationInfo: {
            handler (data) {
                if (data) {
                    this.formData = Object.assign({}, data)
                }
            },
            immediate: true,
            deep: true
        }
    },
    inject: {
        subpackageId: {
            from: 'subpackageId',
            default: ''
        }
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        showTenderBidLette () {
            return this.isBiddingFileModule
        },
        subId () {
            return this.subpackageId() || ''
        }
    },
    data () {
        return {
            labelCol: {
                span: 12
            },
            wrapperCol: {
                span: 12
            },
            colSapn: 8,
            rules: {
                rankingRules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umlJd_89ca99e5`, '请选择计算方式') }]
            },
            url: {
                queryTenderBidLetter: '/tender/tenderProjectAttachmentInfo/queryTenderBidLetter',
                queryPriceColumn: '/tender/tenderProjectAttachmentInfo/queryPriceColumn'
            },
            tenderBidTetterVoList: [],
            quoteColumnNameOptions: [],
            getTenderBidTetterLoading: false,
            formData: {}
        }
    },
    methods: {
        getValidatePromise () {
            return this.$refs.form.validate()
        },
        getAllData () {
            return this.formData
        },
        getTenderBidTetterVoList (v) {
            if (v) {
                this.getTenderBidTetterLoading = true
                let params = {
                    subpackageId: this.subId
                }
                let url
                if(this.$ls.get('changeBidFile') && this.$ls.get('clarificationId')){
                    url = '/tender/purchaseTenderClarificationInfo/queryTenderBidLetter'
                    params.clarificationId = this.$ls.get('clarificationId')
                }else{
                    url = this.url.queryTenderBidLetter
                }
                // 二步法时候需要添加currentStep参数
                if (['0', '1'].includes(this.currentItem.currentStep)) params.currentStep = this.currentItem.currentStep
                getAction(url, params)
                    .then((res) => {
                        let data = res.result || []
                        this.tenderBidTetterVoList = data.map(({ name, id }) => {
                            return {
                                text: name,
                                value: id
                            }
                        })
                        this.$forceUpdate()
                    })
                    .finally(() => {
                        this.getTenderBidTetterLoading = false
                    })
            }
        },
        async getTenderBidTetterVoListColumn (v) {
            if (v) {
                this.getTenderBidTetterLoading = true
                let url
                if(this.$ls.get('changeBidFile')){
                    url = '/tender/purchaseTenderClarificationInfo/queryPriceColumn'
                }else{
                    url = this.url.queryPriceColumn
                }
                await getAction(url, {
                    subpackageId: this.subId,
                    bidLetterId: this.formData.tenderBidLetterId
                })
                    .then((res) => {
                        let data = res.result || []
                        this.quoteColumnNameOptions = data.map(({ title, field }) => {
                            return {
                                text: title,
                                value: field
                            }
                        })
                    })
                    .finally(() => {
                        this.getTenderBidTetterLoading = false
                    })
            }
        },
        changeTenderBidLetterId () {
            this.$set(this.formData, 'quoteColumnName', '')
        }
    },
    async mounted () {
        // 获取招标函信息
        if (this.formData.tenderBidLetterId) {
            await this.getTenderBidTetterVoList(1)
            await this.getTenderBidTetterVoListColumn(1)
        }
    }
}
</script>
<style lang="less" scoped>
    :deep(.ant-form-item-control){
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
