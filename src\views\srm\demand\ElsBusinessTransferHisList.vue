<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage"
      ref="listPage"
      :pageData="pageData"
      :url="url" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
export default {
    name: 'ElsTacticsTransferHisList',
    mixins: [ListMixin],
    data () {
        return {
            showEditPage: false,
            pageData: {
                form: {
                    businessTransferNumber: '',
                    businessTransferName: '',
                    businessType: undefined,
                    transferType: undefined
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attributeLineNum`, '属性行号'),
                        fieldName: 'businessTransferNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterAttributeLineNum`, '请输入属性行号')
                    },
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attributeLineName`, '属性行名称'),
                        fieldName: 'businessTransferName',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterAttributeLineName`, '请输入属性行名称')
                    }
                    ,
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                        fieldName: 'businessType',
                        dictCode: 'srmBusinessType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectAServiceType`, '请选择业务类型')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transferOperationType`, '转办操作类型'),
                        fieldName: 'transferType',
                        dictCode: 'srmOperateType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectTransferOperationType`, '请选择转办操作类型')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download',  clickFn: this.handleExportXls},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                showOptColumn: false,
                optColumnWidth: 0,
                optColumnList: []
            },
            url: {
                list: '/base/elsBusinessTransferHis/list',
                exportXlsUrl: '/base/elsBusinessTransferHis/exportXls',
                columns: 'elsBusinessTransferHisList'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessTransferHistory`, '业务转办历史记录'))
        }
    }
}
</script>