<template>
  <div class="hall-layout">
    <a-layout>
      <a-layout-header class="header">
        <div class="logo">{{ title }}</div>
        <div class="select">
          <a-select
            v-model="selectVal"
            style="width: 200px"
            :loading="loading"
            @change="handleSelectChange">
            <template v-for="el in options">
              <a-select-option
                :value="el.id"
                :key="el.id">
                {{ el.biddingDesc }}
              </a-select-option>
            </template>
          </a-select>
        </div>
        <div
          class="fl">
          <span class="subStatus">{{ $srmI18n(`${$getLangAccount()}#i18n_field_zszE_268f120a`, '分包状态') }}: {{ vuex_currentEditRow.biddingProjectStatus_dictText }}</span>
        </div>
        <div class="info">
          <div class="item">
            <span class="tit">{{ $srmI18n(`${$getLangAccount()}#i18n_SaleMassProdHeadList_projectName`, '项目名称') }}</span>
            <span
              class="titContent"
              :title="vuex_currentEditRow.projectName">{{ vuex_currentEditRow.projectName }}</span>
          </div>
          <div class="item">
            <span class="tit">{{ $srmI18n(`${$getLangAccount()}#i18n_field_zsty_268b3941`, '分包单号') }}</span>
            {{ vuex_currentEditRow.biddingNumber }}
          </div>
        </div>
      </a-layout-header>
      <a-layout
        class="siderLayout"
        style="min-height: calc(100vh - 50px)">
        <a-layout-sider
          width="200">
          <s-menu
            :collapsed="false"
            :menu="menus"
            :theme="navTheme"
            @select="onSelect"
            :style="style"
            :userIconFont="true"
          />
        </a-layout-sider>
        <a-layout style="padding: 8px;">
          <a-layout-content
            :style="{ position: 'relative' }">
            <!-- 待设置vuex行缓存后再渲染 -->
            <div
              class="routeView"
              v-if="routerAlive">
              <route-view v-if="vuex_currentEditRow.id" />
            </div>
          </a-layout-content>
        </a-layout>
      </a-layout>
    </a-layout>
  </div>
</template>

<script>
import RouteView from '@/components/layouts/RouteView'
import SMenu from '@/components/menu/index.jsx'
import { getAction } from '@/api/manage'
import { cloneDeep } from 'lodash'
import { handlePromise } from '@/utils/util.js'

import { SET_VUEX_CACHE_ROW, SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    name: 'HallLayout',
    components: {
        SMenu,
        RouteView
    },
    computed: {
        ...mapState({
            navTheme: state => state.app.theme,
            vuex_currentEditRow: state => state.app.vuex_currentEditRow,
            vuex_cacheRow: state => state.app.vuex_cacheRow
        }),
        style () {
            return {
                padding: 0,
                height: '100%',
                overflowX: 'hidden',
                overflowY: 'auto'
            }
        },
        // 导航菜单
        // 根据 评标方式 bidEvaluationWay 动态切换显示评标路由(0: 线下评标, 1: 线上评标)
        // 线上评标 project_bidEvaluation
        // 线下评标 project_offline
        menus () {
            if (!this.vuex_currentEditRow || !this.vuex_currentEditRow.id) {
                return []
            }

            let HALLROUTERNAME = this.$route.path.includes('projectHall') ? 'projectHall' : 'applyHall'
            let { children = [] } = this.$router.options.routes.find(n => n.name === HALLROUTERNAME) || {}
            const { bidEvaluationWay = '' } = this.vuex_currentEditRow || {}
            console.log('bidEvaluationWay :>> ', bidEvaluationWay)
            let unShowName = bidEvaluationWay === '0' ? 'project_bidEvaluation' : 'project_offline'
            return children.filter(n => n.name !== unShowName)
        }
    },
    provide () {
        return {
            routerRefresh: this.routerRefresh,
            getBiddingOptions: this.getBiddingOptions
        }
    },
    data () {
        return {
            routerAlive: true,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_JdYBfY_2d6065db`, '自主招标大厅'),
            collapsed: false,
            currentRow: {},
            options: [],
            selectVal: '',
            loading: false
        }
    },
    watch: {
        $route: {
            immediate: true,
            handler ({ path }) {
                this.title = path.indexOf('/applyHall') !== -1
                    ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supBiddingHall`, '投标大厅')
                    : this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_JdYBfY_2d6065db`, '自主招标大厅')
                this.getBiddingOptions(true)
            }
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW,
            setVuexCacheRow: SET_VUEX_CACHE_ROW
        }),
        /**
         * @description: 路由销毁重建（解决跳转同一路由不刷新问题）
         * @param {type}
         * @return:
         */
        routerRefresh () {
            this.routerAlive = false
            this.$nextTick(() => {
                this.routerAlive = true
            })
        },
        handleSelectChange (val) {
            let row = this.options.find(n => n.id === val)
            let copyRow = cloneDeep(row)
            this.setVuexCurrentEditRow({ row: copyRow })
            const { name = '' } = this.$route || {}
            let _t = +new Date()
            this.$nextTick(() => {
                this.$router.replace({
                    name,
                    query: {
                        _t
                    }
                })
            })
        },
        onSelect (obj) {
            console.log('obj', obj)
        },
        // 获取分包选项信息
        getBiddingOptions (noRouterRefresh = false) {
            let { id = '' } = this.vuex_cacheRow || {}
            if (!id) return
            let url = 'bidding/purchaseBiddingProjectHead/queryPurchaseBiddingHeadByMainId'
            this.loading = true
            getAction(url, { id }).then(res => {
                if (!res.success) {
                    return
                }
                let result = res.result || []
                this.asyncGetEvaluationData(result, noRouterRefresh)
            })
        },
        // 异步获取评标模板编号信息
        asyncGetEvaluationData (cacheItems, noRouterRefresh) {
            // 查询评标模板明细
            const URL = '/bidding/biddingEvaluationTemplateHead/list'
            const promises = cacheItems.map((n) => {
                let params = {
                    evaluationNumber: n.bidEvaluationNumber,
                    pageNo: 1,
                    pageSize: 20
                }
                return getAction(URL, params)
            })

            Promise.all(handlePromise(promises)).then((res) => {
                const result = res
                    // .filter((n) => n.status === "success")
                    .map((n) => n.res.result)

                this.asyncSetFieldOptions(cacheItems, result, noRouterRefresh)
            }).finally(() => {
                this.loading = false
            })
        },
        // 闭包处理接口异步
        asyncSetFieldOptions (cacheItems = [], result = [], noRouterRefresh) {
            cacheItems.forEach((v, idx) => {
                v.biddingEvaluationTemplateHeadVO = {}
                let records = result[idx].records || []
                if (records.length) {
                    v.biddingEvaluationTemplateHeadVO = records[0]
                }
            })

            this.options = cacheItems
            console.log('this.options :>> ', this.options)

            if (this.options && this.options.length) {
                if (!this.selectVal) {
                    if(!this.vuex_cacheRow.subpackageId){
                        let row = cloneDeep(this.options[0])
                        this.selectVal = row.id
                        this.setVuexCurrentEditRow({ row })
                    }else{
                        let arr = this.options.filter(item=>{
                            return item.id == this.vuex_cacheRow.subpackageId
                        })
                        let row = arr[0]
                        this.selectVal = row.id
                        this.setVuexCurrentEditRow({ row })
                    }

                } else {
                    // 用于项目公告提交审批或发布操作后更新 Vuex 行缓存
                    let row = this.options.find(n => n.id === this.selectVal) || {}
                    console.log('row :>> ', row)
                    this.setVuexCurrentEditRow({ row: cloneDeep(row) })
                    if (!noRouterRefresh) this.routerRefresh()
                }
            }
        }
    },
    created () {
        const { id = '' } = this.$route.query || {}
        this.selectVal = id
    },
    mounted () {
        // 获取自主招标缓存行数据
        if (!this.vuex_currentEditRow.id) {
            let row = this.$ls.get(SET_VUEX_CACHE_ROW) || null
            if (row) {
                console.log('row :>> ', row)
                this.setVuexCacheRow({ row })
                this.getBiddingOptions()
            }
        }
    }
}
</script>

<style lang="less" scoped>
.hall-layout {
	.header {
		display: flex;
		// justify-content: space-between;
		padding: 0 16px 0 0;
		height: 50px;
		background: #4e85ff;
		line-height: 50px;
    .select {
      flex: 1;
    }
		.logo {
			width: 200px;
			text-align: center;
			font-weight: 400;
			font-size: 25px;
			color: #fff;
		}
        .fl {
            flex: 2;
            text-align: center;
            font-weight: 400;
            font-size: 25px;
            color: #fff;
            padding: 0;
            height: 100%;
        }
        .subStatus {
            padding-left:30%;
        }
		.info {
            align-items: flex-end;
            width: 33%;
            display: flex;
            font-weight: 400;
			font-size: 14px;
			color: #fff;
            text-align: right;
            .item:nth-child(1) {
                max-width: 56%;
                display: flex;
            }
            .item:nth-child(2) {
                width: 44%;
            }
			.item {
				+ .item {
					position: relative;
					margin-left: 23px;
					&::before {
						position: absolute;
						left: -8%;
						top: 34%;
						width: 1px;
						height: 16px;
						background-color: #fff;
						content: "";
					}
				}
				.tit {
					&::after {
						margin-right: 8px;
						content: ":";
					}
				}
                .titContent {
                    display:block;
                    width: calc(100% - 80px);
                    white-space:nowrap;
                    overflow:hidden;
                    text-overflow:ellipsis;
                }
			}
		}
	}
  // 菜单高度修改(因为SMenu是一个js组件)
  :deep(.ant-menu-inline > .ant-menu-item){
      height: 28px;
      line-height: 28px;
  }
  :deep(.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title){
      height: 28px;
      line-height: 28px;
  }
  :deep(.ant-menu-sub.ant-menu-inline > .ant-menu-item, .ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title){
      height: 28px;
      line-height: 28px;
  }
}
</style>
