<!--
 * @Author: LokNum
 * @Date: 2022-04-07 00:52:35
 * @LastEditors: wangxin <EMAIL>
 * @LastEditTime: 2022-08-18 16:12:18
 * @Description: 首页
-->
<template>
  <div class="home-page-container">
    <div class="page-head">
      <a-card
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_orES_2caba140`, '待办业务')"
        class="to-do-lst"
        size="small">
        <div class="carousel">
          <div
            class="carousel-content"
            v-if="cardList.length > 0">
            <a-button
              class="custom-slick-arrow"
              style="left: 10px; zindex: 1"
              @click="carouselRightClick"
              v-show="carouselLArrowShow">
              <a-icon type="left" />
            </a-button>
            <a-button
              class="custom-slick-arrow"
              style="right: 10px"
              @click="carouselLeftClick"
              v-show="carouselRArrowShow">
              <a-icon type="right" />
            </a-button>
            <template>
              <div
                class="to-do-lst-itm"
                :style="{
                  transform: `translate3d(${carouselSlicelWidth}px, 0px, 0px)`,
                  transition: 'all 0.5s',
                }"
              >
                <div
                  class="lst-itm"
                  :class="'set-icon-col-' + (idx % 4)"
                  v-for="(itm, idx) in cardList"
                  :key="'card_item_' + idx"
                  @click="toListGo(itm)">
                  <div class="do-lst-itm-t">
                    <div class="itm-icon">
                      <icon-font
                        class="icon"
                        :style="{ fontSize: '16px', color: '#fff' }"
                        :type="iconfont[itm.pageRoute] || 'icon-shangchenglogo'"
                      />
                    </div>
                    <span>{{ itm.countNumber > 99 ? '99+' : itm.countNumber }}</span>
                  </div>
                  <div
                    class="do-lst-itm-b"
                    :title="itm.moduleName">
                    {{ itm.moduleName }}
                  </div>
                </div>
              </div>
            </template>
          </div>

          <div
            class="card-none to-do-lst-no"
            v-else-if="!flowLoading">
            {{ $srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据') }}
          </div>
          <div
            v-if="flowLoading"
            class="vxe-table--loading vxe-loading is--visible to-do-lst-load">
            <div class="vxe-loading--spinner do-size--mini"></div>
          </div>
        </div>
      </a-card>
    </div>
    <div class="page-content">
      <div class="page-left">
        <div class="a-card-todo ant-card">
          <div class="card-todo-tab card-todo-header">
            <div class="tab-lf">
              <template v-for="(item, index) in todoTabs">
                <a-button
                  @click="changeTodoTab(item.curkey, item.url)"
                  :key="'todo_btn' + index"
                  :type="item.curkey == curtodoTab ? 'primary' : ''">
                  {{ item.name }} <span :class="{ 'to-count': item.curkey != curtodoTab }">（{{ item.count }}）</span></a-button
                >
              </template>
            </div>
            <div class="tab-rg">
              <a-button
                slot="extra"
                size="small"
                class="more-btn"
                @click="moreTogo({ path: `/srm/bpm/${curtodoTab}`, query: {} })">{{ $srmI18n(`${$getLangAccount()}#i18n_title_more`, '更多') }} </a-button>
            </div>
          </div>

          <div
            class="card-todo-content"
            ref="todoGrid">
            <vxe-grid
              resizable
              autoResize
              showOverflow
              showHeaderOverflow
              row-id="id"
              height="auto"
              size="mini"
              :show-header="true"
              :loading="todoLoading"
              :columns="curToColumn"
              :data="curTodoData"
              @scroll="scrollEvent"
              :row-config="rowConfig">
              <template #custom_render="{ row, column }">
                <span
                  style="color: #1890ff; cursor: pointer;font-size:13px;"
                  @click="goToDoDetail(row)">{{ row[column.property] }}</span>
              </template>
              <template #custom_date="{ row, column }">
                <span style="color: #e96466;font-size:13px;">{{ row[column.field] }}</span>
              </template>
              <template #custom_point="{ row, column }">
                <span style="font-size:13px;">{{ row[column.field] }}</span>
              </template>
              <template #custom_type="{ row, column, $rowIndex }">
                <span
                  :class="'todo-table-type-' + ($rowIndex % 3)"
                  style="font-size:13px;">{{ row[column.field] }}</span>
              </template>
              <template #empty="{ row, column }">
                <a-empty :image="emptyImage" />
              </template>
            </vxe-grid>
          </div>
        </div>

      
      </div>
      <div class="page-right">
        <a-card
              :title="$srmI18n(`${$getLangAccount()}#i18n_title_announcement`, '平台公告')"
              size="small"
              ref="publicNotice"
              class="platform-adv">
              <a-button
                  slot="extra"
                  size="small"
                  class="set-btn"
                  @click="publicNoticeMoreHandle"
              >{{ $srmI18n(`${$getLangAccount()}#i18n_title_more`, '更多') }}</a-button>
              <div
                  class="publicNoticeWrap"
              >
                  <a-list
                      item-layout="horizontal"
                      :data-source="publicNotice">
                      <a-list-item
                          slot="renderItem"
                          slot-scope="item"
                          class="set-lst-itm">
                          <a-list-item-meta>
                              <div
                                  class="publicNotice-item"
                                  slot="title"
                              >
                                  <div
                                      class="publicNotice-left"
                                  >
                                      <b
                                          class="notice-type"
                                          :class="{'notice-type-hot': item['noticeType']!='2'}">{{ item['noticeType']=='2'?'常':"急" }}</b>
                                      <router-link
                                          class="route-link"
                                          target="_blank"
                                          :to="{
                          name: goTemplateName(item),
                          query: {
                            id: item.id,
                            businessId: item.businessId,
                            businessType: item.businessType,
                            noticeTitle: item.noticeTitle,
                            elsAccount: item.templateAccount || item.busAccount,
                            templateVersion: item.templateVersion,
                            templateNumber: item.templateNumber,
                            uploadElsAccount: item.busAccount,
                            roleType: item.busAccount === item.elsAccount ? 'purchase' : 'sale',
                          },
                        }"
                                      >
                        <span
                            class="title"
                            :title="item.noticeTitle">{{ item.noticeTitle || '' }}</span>
                                      </router-link>
                                  </div>
                                  <span class="time"> {{ item._time || '' }} </span>
                              </div>
                          </a-list-item-meta>
                      </a-list-item>
                  </a-list>
              </div>
              <div
                  v-if="noitceLoading"
                  class="vxe-table--loading vxe-loading is--visible notice-list-load">
                  <div class="vxe-loading--spinner do-size--mini"></div>
              </div>
          </a-card>
        <a-card
          class="page-quick"
          :title="$srmI18n(`${$getLangAccount()}#i18n_title_application`, '快捷应用')"
          size="small">
          <a-button
            slot="extra"
            size="small"
            class="set-btn"
            @click="setFastPermission"
            :loading="setFastLoding">{{ $srmI18n(`${$getLangAccount()}#i18n_title_setup`, '设置') }}</a-button>
          <a-list
            item-layout="horizontal"
            :data-source="appList">
            <a-list-item
              slot="renderItem"
              slot-scope="item, index"
              class="set-lst-itm">
              <a-list-item-meta>
                <div
                  class="quick-route"
                  slot="title"
                  @click="quickRoute(item)">
                  <div
                    class="title-icon"
                    :class="'set-icon-col-' + (index % 5)">
                    <a-icon
                      style="margin-right: 3px"
                      type="appstore" />
                  </div>
                  <span
                    class="set-fast-name"
                    :title="item.name"> {{ item.name }} </span>
                </div>
              </a-list-item-meta>
            </a-list-item>
          </a-list>
        </a-card>
      </div>
    </div>
    

    <fast-permission
      :visible="visible"
      :transferData="transferData"
      :targetKeys="targetKeys"
      @fast-permission-cancel="handleCancel"
      @fast-permission-change="handleChange"
      @fast-permission-refresh="getSelectedFastPermission" />
    <div class="emergency-announcement">
      <a-modal
        v-drag    
        :width="340"
        v-model="showEmergencyAnnouncement"
        :closable="false"
        :footer="null"
        @cancel="showEmergencyAnnouncement = false"
        @ok="showEmergencyAnnouncement = false">
        <div class="announcement-box">
          <div class="announcement-header"></div>
          <div class="announcement-title">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_HtRxeR_17de4c7`, '紧急公告通知') }}</div>
          <div class="announcement-sub-title">{{ $srmI18n(`${$getLangAccount()}#i18n_field_subject`, '标题') }}：{{ emergencyAnnouncement.noticeTitle }}</div>
          <div
            class="announcement-context"
            v-html="emergencyAnnouncement.noticeContent"></div>
          <a-button
            class="announcement-btn"
            shape="round"
            size="large"
            type="primary"
            @click="showEmergencyAnnouncement = false">
            {{ $srmI18n(`${$getLangAccount()}#i18n_btn_Rur_1d3db38`, '知道了') }}
          </a-button>
        </div>
      </a-modal>
    </div>
      <template>
          <a-modal title="平台公告" :visible="showNoticesTipMess" :footer="null" @cancel="handleCancelNoticeModal()">
              <div class="publicNoticeWrap notice_wrap_css">
                  <a-list
                      item-layout="horizontal"
                      :data-source="publicNotice">
                      <a-list-item
                          slot="renderItem"
                          slot-scope="item"
                          class="set-lst-itm">
                          <a-list-item-meta>
                              <div
                                  class="publicNotice-item list_item_css"
                                  slot="title"
                              >
                                  <div
                                      class="publicNotice-left"
                                  >
                                      <b
                                          class="notice-type"
                                          :class="{'notice-type-hot': item['noticeType']!='2'}">{{ item['noticeType']=='2'?'常':"急" }}</b>
                                      <router-link
                                          class="route-link"
                                          target="_blank"
                                          :to="{
                          name: goTemplateName(item),
                          query: {
                            id: item.id,
                            businessId: item.businessId,
                            businessType: item.businessType,
                            noticeTitle: item.noticeTitle,
                            elsAccount: item.templateAccount || item.busAccount,
                            templateVersion: item.templateVersion,
                            templateNumber: item.templateNumber,
                            uploadElsAccount: item.busAccount,
                            roleType: item.busAccount === item.elsAccount ? 'purchase' : 'sale',
                          },
                        }">
                        <span
                            class="title item_title_css"
                            :title="item.noticeTitle">{{ item.noticeTitle || '' }}</span>
                                      </router-link>
                                  </div>
                                  <span class="time"> {{ item._time || '' }} </span>
                              </div>
                          </a-list-item-meta>
                      </a-list-item>
                  </a-list>
              </div>
          </a-modal>
          <a-modal class="OneNoticeModal" :title="OneNoticeData.noticeTitle" :footer="null" :visible = "showOneNoticeTipMess" @cancel = "handleCancelOneNotice()">
            <div class="OneNotice">
                <div class="OneNoticeHead">
                    <div class="head-left">创建人：{{OneNoticeData.publishUser}}</div>
                    <div>|</div>
                    <div class="head-right">发布时间：{{OneNoticeData.publishTime}}</div>
                </div>
                <div class="OneNoticeContent" v-html="OneNoticeData.noticeContent"></div>
            </div>
          </a-modal>
      </template>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { ListConfig } from '@/plugins/table/gridConfig'
import { List } from 'ant-design-vue'
import FastPermission from './modules/FastPermission'
// import { ajaxFindDictItems } from '@/api/api'
// import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { USER_SUB_ACCOUNT} from '@/store/mutation-types'
import { srmI18n, getLangAccount } from '@/utils/util'
import { mixin } from '@/utils/mixin.js'
import { ajaxFindCount } from '@/api/api'
import { variateConfig } from '@/utils/variateConfig.js'

import { ICONFONT } from './mock.js'
import { Empty } from 'ant-design-vue'
export default {
    name: 'Workplace',
    mixins: [mixin],
    components: {
        AList: List,
        AListItem: List.Item,
        AListItemMeta: List.Item.Meta,
        FastPermission
    },
    data () {
        return {
            showOneNoticeTipMess:false,//单条公告
            OneNoticeData:{},
            showNoticesTipMess:false,//多条公告列表数据
            emptyImage: Empty.PRESENTED_IMAGE_SIMPLE,
            iconfont: ICONFONT,
            currentCardIndex: null,
            currentCardValue: null,
            height: 0,
            todoColumns: [
                { field: 'msgTitle', title: srmI18n(`${getLangAccount()}#i18n_field_msgTitle`, '消息标题') },
                { field: 'msgContent', title: srmI18n(`${getLangAccount()}#i18n_title_newsContent`, '消息内容'), slots: { default: 'todo_message_render' } },
                { field: 'updateTime', title: srmI18n(`${getLangAccount()}#i18n_field_jingpinDate`, '时间') },
                { field: 'sendElsAccount', title: srmI18n(`${getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号') },
                { field: 'sendElsAccount_dictText', title: srmI18n(`${getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称') },
                { fixed: 'right', title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'), slots: { default: 'grid_opration' } }
            ],
            gridConfig: ListConfig,
            tableData: [],
            loading: true,
            defaultCardList: [
                {
                    text: srmI18n(`${getLangAccount()}#i18n_title_itemizedAccessSheet`, '分项准入单'),
                    color: '#60646f',
                    colorTo: '#98a4ca',
                    icon: 'fund',
                    num: '0'
                },
                {
                    text: srmI18n(`${getLangAccount()}#i18n_title_accessManagement`, '准入管理'),
                    color: '#3d6de9',
                    colorTo: '#47c2fb',
                    icon: 'api',
                    num: '0'
                },
                {
                    text: srmI18n(`${getLangAccount()}#i18n_title_demandPool`, '需求池'),
                    color: '#d49e9e',
                    colorTo: '#fda984',
                    icon: 'radar-chart',
                    num: '0'
                }
            ],
            cardList: [],
            dictOptions: '',
            appList: [],
            publicNotice: [],
            publicNoticeOptList: [{ type: 'audio', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_UWrC_41e24e57`, '语音播放'), clickFn: this.handleAudio }],
            visible: false,
            transferData: [],
            targetKeys: [],
            setFastLoding: false,
            todoTabs: [
                {
                    name: srmI18n(`${getLangAccount()}#i18n_menu_orUz_2cad5471`, '待办审批'),
                    count: 0,
                    curkey: 'bpmnTodoList',
                    url: '/a1bpmn/audit/api/runtime/task/list'
                },
                {
                    name: srmI18n(`${getLangAccount()}#i18n_menu_oZUz_2daa5a18`, '待阅审批'),
                    count: 0,
                    curkey: 'commuReceiverList',
                    url: '/a1bpmn/audit/api/commu/task/list'
                }
                // {
                //   name:srmI18n(`${getLangAccount()}#i18n_field_srvM_42780a8a`, '转办给我'),
                //   count:0,
                //   curkey:"toMeList",
                //   url:'/a1bpmn/audit/api/toMe/task/list'
                // },
            ],
            curtodoTab: '',
            curtodoTabUrl: '',
            toDoParmas: {
                pageSize: 20,
                pageNo: 1,
                filter: {},
                priorityOrder: 'desc',
                createTimeOrder: 'desc'

            },
            publicNoticeParams: {
                pageNo: 1,
                pageSize: 10
            },
            isNoticeToatls: false,
            showNoticeMessage: false,
            noitceLoading: false,
            curTodoData: [],
            todoLoading: true,
            isToatls: false,
            showMessage: false,
            rowConfig: {
                isHover: true,
                height: 42
            },
            flowLoading: true,
            isJumpNow: false,
            showEmergencyAnnouncement: false,
            emergencyAnnouncement: {},
            carouselSpeed: 2, //调整轮播一次滑动的速度
            curCarouselItemWidth: 0, //轮播项的宽度
            carouselClientWidth: 0, //轮播的内容宽度
            carouselScorllWidth: 0, //轮播的滚动宽度
            carouselSlicelWidth: 0, //当前滚动滚动的宽度
            carouselLArrowShow: false, //当前滚动左箭头
            carouselRArrowShow: false //当前滚动右箭头
        }
    },
    computed: {
        halfHeight () {
            return (this.height - 94) / 2
        },
        langAccount () {
            return this.$getLangAccount()
        },
         
        noticeColumns () {
            let account = this.langAccount
            const { $srmI18n } = this
            return [
                { field: 'noticeType_dictText',  title: $srmI18n(`${account}#i18n_field_transferType`, '类型'), slots: { default: 'notice_type' }},
                {
                    field: 'noticeTitle',
                    align: 'center',
                    title: $srmI18n(`${account}#i18n_field_msgTitle`, '消息标题'),
                    slots: { default: 'custom_render' }
                },
                { field: 'noticeScope_dictText', align: 'center', title: $srmI18n(`${account}#i18n_field_noticeScope`, '通知范围') },
                { field: 'publishUser', align: 'center',  title: $srmI18n(`${account}#i18n_field_sender`, '发布人') },
                { field: 'businessType_dictText', align: 'center',  title: $srmI18n(`${account}#i18n_field_businessType`, '业务类型') },
                { field: 'updateTime', align: 'right', title: $srmI18n(`${account}#i18n_field_jingpinDate`, '时间'), slots: { default: 'custom_date' } }
            ]
        },
        curToColumn () {
            return this[this.curtodoTab + 'Column']
        },
        bpmnTodoListColumn () {
            let account = this.langAccount
            const { $srmI18n } = this
            return [
                { field: 'bizType_dictText', title: $srmI18n(`${account}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型'), slots: { default: 'custom_type' } },
                { field: 'subject', align: 'center', title: $srmI18n(`${account}#i18n_title_processTheTheme`, '流程主题'), slots: { default: 'custom_render' } },
                { field: 'name', align: 'center', title: $srmI18n(`${account}#i18n_field_nodeName`, '节点名称'), slots: { default: 'custom_point' } },
                { field: 'createTime', align: 'right', title: $srmI18n(`${account}#i18n_field_yCufjKI_eac431dd`, '节点到达的时间'), slots: { default: 'custom_date' } }
            ]
        },
        commuReceiverListColumn () {
            let account = this.langAccount
            const { $srmI18n } = this
            return [
                { field: 'bizType_dictText', title: $srmI18n(`${account}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型'), slots: { default: 'custom_type' } },
                { field: 'title', align: 'center', title: $srmI18n(`${account}#i18n_field_titel`, '标题'), slots: { default: 'custom_render' } },
                { field: 'receiver', align: 'center', title: $srmI18n(`${account}#i18n_title_receiver`, '接收人') },
                { field: 'receiveTime', align: 'right', title: $srmI18n(`${account}#i18n_field_inTime`, '接收时间'), slots: { default: 'custom_date' } }
            ]
        }
        // toMeListColumn(){
        //     let account = this.langAccount
        //     const { $srmI18n } = this
        //     return [
        //      { field: 'bizType_dictText', title: $srmI18n(`${account}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型'),slots: { default: 'custom_type' }},
        //      { field: 'title', title: $srmI18n(`${account}#i18n_field_taskName`, '任务标题'),   slots: { default: 'custom_render' }},
        //      { field: 'newAssigneeName', title: $srmI18n(`${account}#i18n_field_VorL_2faa5e71`, '新待办人')},
        //      { field: 'previousAssigneeName', title: $srmI18n(`${account}#i18n_field_jGvL_275f9779`, '原处理人') ,slots: { default: 'custom_date' } },
        //     ]
        // },
    },
    methods: {
        handleCancelNoticeModal(){
            this.showNoticesTipMess = false
        },
        handleCancelOneNotice(){
            this.showOneNoticeTipMess = false
        },
        // 公告更多跳转
        publicNoticeMoreHandle () {
            const routeUrl = this.$router.resolve({
                name: 'homePageNotice'
            })
            window.open(routeUrl.href, '_blank')
        },
        //快捷应用跳转
        quickRoute (item) {
            let component = item.component
            let url = item.url
            if (url.indexOf('domainURL ') > -1) {
                let URL = (url || '').replace(/{{([^}}]+)?}}/g, (s1, s2) => {
                    let val = s2.trim()
                    val = variateConfig[val]
                    return val
                })
                window.open(`${URL}?token=${this.$ls.get('Access-Token')}`, '_blank')
            } else {
                this.$router.push({ path: url })
            }
        },
        //滚动左边
        carouselRightClick () {
            let scarouselSlicelWidth = this.carouselSlicelWidth
            if (scarouselSlicelWidth == 0) {
                this.carouselSlicelWidth = 0
                this.carouselLArrowShow = false
            } else {
                this.carouselSlicelWidth = this.carouselSlicelWidth + this.carouselSpeed * this.curCarouselItemWidth

                this.carouselRArrowShow = true
            }
        },
        //滚动右边
        carouselLeftClick () {
            let scarouselSlicelWidth = -this.carouselSlicelWidth
            let carouselScorllWidth = this.carouselScorllWidth
            let carouselClientWidth = this.carouselClientWidth
            if (scarouselSlicelWidth + carouselClientWidth >= carouselScorllWidth) {
                this.carouselSlicelWidth = this.carouselSlicelWidth
                this.carouselRArrowShow = false
            } else {
                this.carouselSlicelWidth = this.carouselSlicelWidth - this.carouselSpeed * this.curCarouselItemWidth

                this.carouselLArrowShow = true
            }
        },
        //跳转路径
        toListGo (item) {
            if (this.isJumpNow) return
            this.isJumpNow = true
            // function onComplete(){
            // }
            let query = { toList: true }
            for (let i of item.statusList) {
                query[i.name] = i.value
            }
            query['myToDo'] = 1
            const hide = this.$message.loading(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ELTss_ab5c74c7`, '页面跳转中'), 0)
            // setTimeout(hide, 500
            this.$router
                .push({
                    path: item.pageRoute,
                    query
                })
                .then(() => {
                    setTimeout(hide, 500)
                    this.isJumpNow = false
                })
        },
        //获取待办事项
        getTodoListData () {
            postAction('/base/todo/getTodoCountList')
                .then((res) => {
                    if (res.success) {
                        this.cardList = res.result ? res.result : []
                    }
                })
                .finally(() => {
                    this.flowLoading = false
                    this.$nextTick(() => {
                        if (this.cardList.length == 0) return
                        if (!this.carouselClientWidth) {
                            this.carouselClientWidth = document.getElementsByClassName('to-do-lst-itm')[0].clientWidth
                        }
                        if (!this.carouselScorllWidth) {
                            this.carouselScorllWidth = document.getElementsByClassName('to-do-lst-itm')[0].scrollWidth
                        }
                        if (!this.curCarouselItemWidth&&document.getElementsByClassName('lst-itm')[1]) {
                            // console.log(document.getElementsByClassName('lst-itm')[1])
                            this.curCarouselItemWidth = document.getElementsByClassName('lst-itm')[1].offsetLeft
                        }

                        if (document.getElementsByClassName('to-do-lst-itm')[0].scrollWidth > document.getElementsByClassName('to-do-lst-itm')[0].clientWidth) {
                            // this.carouselLArrowShow=true
                            this.carouselRArrowShow = true
                        }
                    })
                })
        },
        //待办详情跳转
        goToDoDetail (row) {
            console.log('goToDoDetail', row, this.curtodoTab)
            let query = {}
            let ObjectQuery = {
                bpmnTodoList: {
                    taskId: row.id,
                    businessType: row.bizType,
                    businessId: row.bizKey,
                    processInstanceId: row.processInstanceId,
                    subject: row.subject
                },
                commuReceiverList: {
                    commuReceiverId: row.id,
                    businessType: row.bizType,
                    businessId: row.bizKey,
                    processInstanceId: row.procId,
                    subject: row.subject
                }
                // 'toMeList':{
                //    businessId:row.bizKey,
                //    businessType:row.bizType,
                // }
            }
            query = ObjectQuery[this.curtodoTab]
            this.$router.push({
                path: `/srm/bpm/${this.curtodoTab}`,
                query
            })
        },
        noticeScrollEvent (e) {
            if ((Math.ceil(e.srcElement.scrollTop) + e.srcElement.clientHeight == e.srcElement.scrollHeight) && !this.isNoticeToatls ) {
                this.publicNoticeParams.pageNo++
                this.getNoticeData()
            }else{
                if (this.isNoticeToatls && !this.showNoticeMessage) {
                    // this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IbxuK_5307f24f`, '已全部加载'))
                    this.showNoticeMessage = true
                }
            }
        },  
        //待办列表滚动监听
        scrollEvent (e) {
            if (e.scrollHeight - Math.floor(e.scrollTop) == e.bodyHeight && !this.isToatls) {
                this.toDoParmas.pageNo++
                this.getCurTodoList()
            } else {
                if (this.isToatls && !this.showMessage) {
                    // this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IbxuK_5307f24f`, '已全部加载'))
                    this.showMessage = true
                }
            }
        },
        //重置待办请求的参数
        resetTodoParms (pageSize) {
            this.toDoParmas = {
                pageSize: pageSize || 20,
                pageNo: 1,
                filter: {},
                priorityOrder: 'desc',
                createTimeOrder: 'desc'
            }
            this.showMessage = false
            this.isToatls = false
            this.curTodoData = []
        },
        //获取各待办列表的信息
        getCurTodoList () {
            // if (this.curTodoData.length >= 100) {
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CtHOWmAHOWF_aaa0c86e`, '点击更多，查看更多数据'))
            //     return
            // }
            if (!this.curtodoTabUrl) {
                this.todoLoading = false
                return
            }
            let _this = this
            this.todoLoading = true
            return new Promise((resolve, reject) => {
                getAction(this.curtodoTabUrl, this.toDoParmas)
                    .then((res) => {
                        if (res.success) {
                            _this.curTodoData = [..._this.curTodoData, ...res.result.records]

                            if (_this.curTodoData.length >= res.result.total) {
                                _this.isToatls = true
                            }
                            resolve('success')
                        } else {
                            reject('fail')
                        }
                    })
                    .finally(() => {
                        this.todoLoading = false
                    })
            })
        },
        // 设置待办的count
        setToDoCount () {
            return Promise.all([ajaxFindCount('/flow/tab/api/runtime/task/bpmn/tabs/count'), ajaxFindCount('flow/tab/api/commu/task/bpmn/tabs/count')])
        },
        //获取待办信息
        //待办导航切换
        changeTodoTab (tabKey, url) {
            this.curtodoTab = tabKey
            this.curtodoTabUrl = url
            this.resetTodoParms(this.toDoParmas.pageSize)
            this.getCurTodoList()
        },
        //更多
        moreTogo ({ path, name, query }) {
            if (path) this.$router.push({ path, query })
            else this.$router.push({ name, query })
        },
        // toolbar_buttons方法封装，暴露更多属性外部使用
        // toolbarButtonsCallBack (row, column, rowIndex, columnIndex, cb) {
        //     if (cb) {
        //         cb(row, column, rowIndex, columnIndex, this.tableData)
        //     }
        // },
        // handleAudio (row) {
        //     this.$voice.play({text: row.msgContent})
        // },
        // 调转到逻辑
        goTemplateName (data) {
            let name = 'noticeDetail'
            if (data && (data.businessType === 'ebidding' || data.businessType === 'bidding' || data.businessType === 'enquiry')) {
                name = 'noticeDetailTemplate'
            } else if (data.businessType === 'winBidNotification' || data.businessType === 'failBidNotification' || data.businessType === 'winBidNotice') {
                name = 'noticeDetail'
            } else if (data.businessType === 'survey') {
                name = 'observe'
            } else if (data.businessType === 'tender_bulletin') {
                name = 'BiddingInformationDetail'
            } else if (data.businessType === 'tender_candidate') {
                name = 'BiddingCandidateDetail'
            } else if (data.businessType === 'tender_winBulletin') {
                name = 'BiddingWinBulletinDetail'
            }
            return name
        },
        setFastPermission () {
            this.setFastLoding = true
            getAction('/account/fastPermission/queryAll').then((res) => {
                const { success, message, result = [] } = res || {}
                if (!success) {
                    this.$message.error(message)
                    return
                }
                this.transferData = result.map((n) => ({ ...n, title: n.name, key: n.id, description: `description_${n.name}` }))
                this.targetKeys = this.transferData.filter((n) => n.selected === '1').map((item) => item.key)
                this.visible = true
                this.setFastLoding = false
            })
        },
        handleCancel () {
            this.visible = false
        },
        handleChange (keys) {
            this.targetKeys = keys
        },

        getSelectedFastPermission () {
            getAction('/account/fastPermission/query').then((res) => {
                if (!res.success) {
                    return this.$message.error(res.message)
                }
                this.appList = res.result || []
                if(this.appList && this.appList.length>6){
                    this.appList.splice(6, 2)
                }
            })
        },
        getNoticeData () {
            this.noitceLoading = true
            const url = '/notice/purchaseNotice/getHomePageNotice'
            getAction(url, this.publicNoticeParams)
                .then((res) => {
                    this.noitceLoading = false
                    if (res.success) {
                        const records = res.result.records || []
                        this.getEmergencyAnnouncementData(records)
                        // this.publicNotice =  records.length > 6 ? records.slice(0, 6) : records
                        this.publicNotice = [...this.publicNotice, ...records] 
                        this.publicNotice.forEach(v=>{
                            const data =  v.updateTime.split(' ')
                            const date = data[0].split('-')
                            const m = date[1]
                            const d = date[2]
                            v._time = `${m}/${d}`
                        })
                        if (this.publicNotice.length >= res.result.total) {
                            this.isNoticeToatls = true
                        }
                        const subTip = this.$ls.get(USER_SUB_ACCOUNT)
                        const noticeTotal = res.result.total
                        if (subTip=='1001'&& noticeTotal >0){
                            if(noticeTotal==1){
                               this.OneNoticeData = res.result.records[0]
                                console.log(this.OneNoticeData,'公告内容')
                               this.showOneNoticeTipMess = true
                            }else{
                                this.showNoticesTipMess = true
                            }
                        }
                    }
                })
                .finally(() => {
                    this.noitceLoading = false
                })
        },
        // 获取紧急公告的数据-弹窗显示
        getEmergencyAnnouncementData (records) {
            if (records && records.length) {
                let items = records.filter((item) => {
                    return item.busAccount === '100000'
                })
                if (items && items.length) {
                    this.emergencyAnnouncement = items[0]
                    this.showEmergencyAnnouncement = true
                } else {
                    this.emergencyAnnouncement = {}
                    this.showEmergencyAnnouncement = false
                }
            }
        },
        async  createInit (){
            this.getTodoListData()
            this.getSelectedFastPermission()
            let coutArry = await this.setToDoCount().then((res) => {
                for (let [i, v] of res.entries()) {
                    if (v.success) {
                        if (v.result) {
                            for (let vt of v.result) {
                                if (vt && vt.value == null) {
                                    this.todoTabs[i].count = vt.total ? vt.total : 0
                                    break
                                }
                            }
                        } else {
                            this.todoTabs[i].count = 0
                        }
                    }
                }

                this.curtodoTab = this.todoTabs[0].curkey
                this.curtodoTabUrl = this.todoTabs[0].url
            })
            let toDoData = await this.getCurTodoList()
        }
    },
    
    async created () {
        this.height = document.documentElement.clientHeight

        await this.$nextTick()
        let pageLeftWeight = document.getElementsByClassName('page-left')[0].clientWidth
        this.carouselNum = Math.floor(pageLeftWeight / 210) >= 6 ? Math.floor(pageLeftWeight / 210) - 1 : 4
        // 动态计算待办审批高度及查询条数
        const todoGridHeight = this.$refs.todoGrid.clientHeight
        const pageSize = Math.ceil(todoGridHeight/42)
        this.toDoParmas.pageSize = pageSize
        // 动态计算平台公告高度及查询条数
        const publicNoticeHeight = document.getElementsByClassName('platform-adv')[0].clientHeight - 30
        const noticePageSize = Math.ceil(publicNoticeHeight/34)
        this.publicNoticeParams.pageSize = noticePageSize
        // 给平台公告增加滚动事件
        const dom = document.querySelectorAll('.platform-adv >.ant-card-body')[0]
        dom.onscroll = this.noticeScrollEvent
        this.createInit()
        this.getNoticeData()
        this.activatedHook = true
        // this.getTypesStatistical()
    },

    async activated () {
        if (!this.activatedHook) return
        this.flowLoading=true
        this.todoLoading = true
        this.cardList = []
        const todoGridHeight = this.$refs.todoGrid.clientHeight
        const pageSize = Math.ceil(todoGridHeight/42)
        this.resetTodoParms(pageSize)
        await this.$nextTick()
        this.createInit()
    }
}
</script>
<style lang="less" scoped>
@setItemBG: #fafbfc;
@hoverSetItemBG:rgba(59, 135, 247, 0.1);
@hoverAllAmin: all 0.5s ease-in-out;
@setIconCol0: #3b87f7;
@setIconCol1: #f9865b;
@setIconCol2: #3bdbcf;
@setIconCol3: #fccd65;
@setIconCol4: #4eb0ff;
@todoTypeCol0: #f0f5ff;
@todoTypeCol1: #f6ffed;
@todoTypeCol2: #fff9f0;

.todoTypeMixins {
    display: inline-block;
    min-width: 64px;
    height: 22px;
    border-radius: 2px;
    font-size: 12px;
    padding: 0px 8px;
}

@-webkit-keyframes pulse {
    0% {
        -webkit-transform: scaleX(1);
        transform: scaleX(1);
    }
    50% {
        -webkit-transform: scale3d(1.05, 1.05, 1.05);
        transform: scale3d(1.05, 1.05, 1.05);
    }
    to {
        -webkit-transform: scaleX(1);
        transform: scaleX(1);
    }
}
@keyframes pulse {
    0% {
        -webkit-transform: scaleX(1);
        transform: scaleX(1);
    }
    50% {
        -webkit-transform: scale3d(1.05, 1.05, 1.05);
        transform: scale3d(1.05, 1.05, 1.05);
    }
    to {
        -webkit-transform: scaleX(1);
        transform: scaleX(1);
    }
}
.home-page-container {
    overflow: auto;
    display: flex;
    flex-direction: column;
    height: 100%;
        :deep(.ant-card-head){
            border: none;
            padding-top: 4px;
            padding-bottom: 4px;
        }
    .page-head{
        .to-do-lst {
            border: none;
            border-radius: 8px;
            margin: 8px;
            .carousel-content {
                height: 120px;
                margin: 0 36px;
                overflow: hidden;
                white-space: nowrap;
            }
            :deep(.ant-card-body){
                padding: 6px;
            }

            :deep(.ant-card-head-title){
                font-size: 16px;
                font-weight: 500;
                padding-bottom: 0px;
            }
            .do-size--mini {
                width: 36px;
                height: 36px;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
            .to-do-lst-no {
                height: 120px;
                display: flex;
                justify-content: center;
                line-height: 120px;
            }
            .to-do-lst-itm {
                &.to-do-lst-two {
                    justify-content: flex-start;
                    .lst-itm {
                        margin-left: 10px;
                        &:first-child {
                            margin-left: 46px;
                        }
                    }
                }
                .lst-itm {
                    width: 205px;
                    height: 100px;
                    padding: 0 21px;
                    background-image: url('~@/assets/img/workplace/to-lst-bg.png');
                    background-repeat: no-repeat;
                    background-position: bottom right;
                    border-radius: 12px;
                    margin-top: 10px;
                    display: inline-flex;
                    margin-left: 10px;
                    cursor: pointer;
                    // &:hover{
                    //    transform: scale(1.1);
                    //    transition: all 0.6s ease-out;
                    // }
                    justify-content: center;
                    flex-direction: column;
                    .do-lst-itm-b {
                        text-align: left;
                        font-size: 14px;
                        margin-top: 10px;
                        color: #fff;
                        width: 100%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                    .do-lst-itm-t {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        span {
                            color: #fff;
                            font-weight: bold;
                            font-size: 22px;
                        }
                        .itm-icon {
                            width: 38px;
                            height: 38px;
                            background: rgba(255, 255, 255, 0.3);
                            border-radius: 12px;
                            text-align: center;
                            line-height: 38px;
                        }
                    }

                    &:first-child {
                        margin-left: 0px;
                    }
                    // &:last-child{
                    //   margin-right: 36px;
                    // }
                }
            }
        }
    }
    .page-content{
        flex: 1;
        min-height: 0;
        display: flex;
    }
    .page-left {
        flex: 1;
        min-width: 700px;
        display: flex;
        flex-direction: column;
        :deep(.ant-card-small > .ant-card-head){
            padding: 4px 16px;
        }
        .a-card-todo {
            flex: 1;
            min-height: 0;
            border-radius: 8px;
            margin: 0 8px 8px 8px;
            display: flex;
            flex-direction: column;
            .card-todo-header {
                padding: 12px 16px;
                min-height: 36px;
            }
            .card-todo-content {
                flex: 1;
                min-height: 0;
                padding: 12px 16px;
                padding-top: 0px;
                .todo-table-type-0 {
                    background: @todoTypeCol0;
                    border: 1px solid #adc6ff;
                    color: #2f54eb;
                    .todoTypeMixins();
                }
                .todo-table-type-1 {
                    background: @todoTypeCol1;
                    border: 1px solid #b7eb8f;
                    color: #52c41a;
                    .todoTypeMixins();
                }
                .todo-table-type-2 {
                    background: @todoTypeCol2;
                    border: 1px solid #ffd4ad;
                    color: #eb952f;
                    .todoTypeMixins();
                }
            }
            .card-todo-tab {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .ant-btn {
                    border-color: transparent;
                }
                .ant-btn-primary[disabled] {
                    background-color: transparent;
                }
                .ant-btn[disabled] {
                    background-color: transparent;
                }
                .to-count {
                    color: #ed8385;
                }
            }
        }
        
        
    }
    .set-btn,
    .more-btn {
        border: transparent;
        border-radius: 6px;
        background: #f0f2f5;
        color: #817f7f;
        font-size: 14px;
        font-weight: 500;
    }
    .page-right {
        width: 320px;
        // height: calc(100% - 16px);
        height: 100%;
        margin: 0px 8px 8px 0;
        display: flex;
        flex-direction: column;
        .set-fast-name {
            font-size: 13px;
        }
        & .page-quick {
            border-radius: 8px;
            border: none;
            height: 230px;
            margin-bottom: 8px;
                :deep(.ant-card-head-title){
                    font-size: 16px;
                    font-weight: 500;
                }
                :deep(.ant-card-head),
                .ant-list-item {
                    border: none;
                }
                :deep(.ant-card-body){
                    padding-top: 0px;
                    // overflow: auto;
                    // height: calc(100% - 50px);
                }
                :deep(.ant-list-items){
                    display: flex;
                    flex-wrap: wrap;
                    justify-content:space-between;
                }
            .set-lst-itm {
                width: 140px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                // margin-right: 10px;
                // padding: 8px 13px;
                // margin-top: 10px;
                // border-radius: 8px;
                // background-color: @setItemBG;
                // &:hover {
                //     background-color: @hoverSetItemBG;
                //     transition: @hoverAllAmin;
                //     span {
                //         color: #72a9f9;
                //         transition: @hoverAllAmin;
                //     }
                // }
                // &:active {
                //     background-color: @hoverSetItemBG;
                //     span {
                //         color: #72a9f9;
                //     }
                // }
                span {
                    color: #667187;
                    display: inline-block;
                    width: 102px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .ant-list-item-meta-title div.quick-route {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    .title-icon {
                        width: 32px;
                        height: 32px;
                        border-radius: 8px;
                        color: #fff;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-right: 11px;
                        & i {
                            margin-right: 0px !important;
                        }
                    }
                }
            }
            .set-lst-itm:nth-child(2n){
                margin-right: 0;
            }
        }
        .platform-adv {
            flex: 1;
            min-height: 0;
            margin-bottom: 8px;
            border: none;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            .notice-list-load{
                height: 100%;
                position: absolute; 
            }
            :deep(.vxe-loading .vxe-loading--spinner){
                top:50%;
                left: 50%;
                transform: translate(-50%);
            }
            :deep(.ant-card-head){
                border: none;
                min-height: unset;
            }
            :deep(.ant-card-head-title){
                    font-size: 16px;
                    font-weight: 500;
                    padding: 14px 0;
            }
            :deep(.ant-card-body){
                overflow: auto;
                padding: 0;
            }
            :deep(.ant-list-item){
                border: none;
                padding: 4px 0;
            }
            .publicNoticeWrap{
                height: 100%;
                // overflow: auto;
                padding: 12px;
            }
            .publicNotice-item{
                display: flex;
                justify-content: space-between;
                .publicNotice-left{
                    display: flex;
                    align-items: center;
                    .route-link{
                        display: flex;
                        align-items: center;
                    }
                    .title{
                        display: inline-block;
                        color:#1890ff;
                        cursor: pointer;
                        width: 184px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        margin-left: 10px;
                        font-size: 13px;
                    }   
                }
                .time{
                    color:#667187;
                    font-size:13px;
                }
            }
        }
    }
    .statistics-card {
        display: flex;
        margin-bottom: 6px;
        color: #fff;
        width: 100%;
        padding-bottom: 10px;
        overflow-x: auto;
        .active-card {
            background-image: linear-gradient(to right, #3d6de9, #47c2fb) !important;
        }

        .card-item {
            display: flex;
            flex: none;
            margin-left: 10px;
            padding: 6px;
            border-radius: 5px;
            height: 82px;
            width: 150px;
            cursor: pointer;
            font-size: 13px;
            .card-item-icon {
                flex: 1;
                line-height: 68px;
                text-align: center;
            }
        }
        .card-none {
            color: #606266;
            width: 98%;
            margin-left: 0px;
            justify-content: center;
            align-items: center;
            height: 82px;
            display: flex;
        }

        .card-item:hover {
            animation: pulse 0.5s ease-in-out;
            -webkit-animation: pulse 1s ease-in-out;
        }
    }
    .workbench-message-content-wrap {
        cursor: pointer;
        color: #2f54eb;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .set-icon-col-0 {
        background-color: @setIconCol0;
    }
    .set-icon-col-1 {
        background-color: @setIconCol1;
    }
    .set-icon-col-2 {
        background-color: @setIconCol2;
    }
    .set-icon-col-3 {
        background-color: @setIconCol3;
    }
    .set-icon-col-4 {
        background-color: @setIconCol4;
    }
}

.custom-slick-arrow {
    font-size: 25px;
    color: #c5c3c9;
    font-weight: 600;
    height: 120px;
    position: absolute;
    padding: 0px;
    border: transparent;
    box-shadow: none !important;
}
.custom-slick-arrow:before {
    display: none;
}
.custom-slick-arrow:hover {
    color: #4e85ff;
}

.to-do-lst-load {
    height: 120px;
    position: relative;
}

.emergency-announcement {
    :deep(.ant-modal-header){
        display: none;
    }
    :deep(.ant-modal-body){
        padding: 0;
    }
    :deep(.ant-modal-content){
        border-radius: 24px;
    }
}
.notice_wrap_css{
    max-height:460px;
    overflow: auto;
}
.list_item_css{
    display: flex;
    justify-content: space-between;
}
.item_title_css{
    margin-left: 10px;
}
.item_title_css:hover{
    color: #1890ff;
}
.OneNoticeModal{
    :deep(.ant-modal){
        width: 1000px !important;
    }
    :deep(.ant-modal-title){
        text-align: center;
        font-size: 26px;
        margin: 20px 0;
    }
    .OneNotice{
        .OneNoticeHead{
            display: flex;
            align-content: center;
            justify-content: center;
            color: #1890ff;
            margin-bottom: 20px;
            .head-left{
                margin-right: 20px;
            }
            .head-right{
                margin-left: 20px;
            }
        }
        .OneNoticeContent{
            max-height: 400px;
            overflow: auto;
        }
    }
}

.notice-type{
    display: inline-block;
    vertical-align: middle;
    width: 18px;
    margin-top: -2px;
    height: 18px;
    font-size: 12px;
    color: #fff;
    line-height: 18px;
    border-radius: 5px 0 5px 0;
    text-align: center;
    font-style: italic;
    text-indent: -2px;
    background: linear-gradient(180deg, #B0B0B0 14.41%, #949494 91.55%);
    &.notice-type-hot{
        background: linear-gradient(180deg, #FF8402 14.41%, #FF5C01 91.65%);
    }
}

.announcement-box {
    text-align: center;
    padding-bottom: 20px;
    .announcement-header {
        height: 208px;
        border-top-left-radius: 24px;
        border-top-right-radius: 24px;
        background: url('~@/assets/img/workplace/emergency-announcement.png') no-repeat;
        background-size: 100% 100%;
    }
    .announcement-title {
        color: #000;
        font-size: 20px;
        padding: 10px 12px 0px 12px;
    }
    .announcement-sub-title {
        color: #4e85ff;
        font-size: 14px;
        padding: 0px 12px 6px 12px;
    }
    .announcement-context {
        min-height: 100px;
        max-height: 240px;
        overflow: auto;
        padding: 0px 12px 12px 12px;
        color: #666;
    }
    .announcement-btn {
        width: 280px;
        margin-top: 20px;
        box-shadow: 0 0 4px 0px rgba(37, 44, 59, 0.39);
    }
}
</style>
