<template>
  <div class="custom-upload-form">
    <a-spin
      :spinning="spinning"
      :delay="delayTime">
      <a-modal
    v-drag    
        :visible="visible"
        title="发票文件"
        :okText="$srmI18n(`${$getLangAccount()}#i18n_menu_WWWKq_48a28a3`, 'OCR识别')"
        :keyboard="false"
        :maskClosable="false"
        @cancel="handleCancel"
        @ok="handleConfirm"
      >
        <a-form
          class="collectionForm"
          ref="collectionForm"
          :form="form"
          v-bind="formItemLayout"
          @submit="handleSubmit"
        >
          <a-form-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_title_attachment`, '附件')"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 14 }">
            <div class="dropbox">
              <a-upload-dragger
                v-decorator="[
                  'fileList',
                  {
                    valuePropName: 'fileList',
                    getValueFromEvent: normFile,
                    rules: [{ required: true, message: `${$srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '请上传附件')}` }]
                  },
                ]"
                v-bind="attrs.uploadAttrs"
                accept=".png, .jpg, .jpeg,.pdf"
                :before-upload="beforeUpload"
                :multiple="true"
                @change="handleUploadChange">
                <p class="ant-upload-drag-icon">
                  <a-icon type="inbox" />
                </p>
                <p class="ant-upload-text">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_clickOrDragToUploadAttachment`, '单击或拖动文件到此区域上传') }}
                </p>
                <p class="ant-upload-hint">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_oneOrMoreUpload`, '支持单次或批量上传') }}
                </p>
              </a-upload-dragger>
              <div class="theTooltip">{{ $srmI18n(`${$getLangAccount()}#i18n_field_nnOCRRujpgjpegpngbmptiftiffpdfofdQImKQeOCRRujpgpngpdfwebpofdQImK_92e4cb18`,'合合OCR支持jpg、jpeg、png、bmp、tif、tiff、pdf、ofd文件格式；文通OCR支持jpg,png,pdf,webp,ofd文件格式。') }}</div>
            </div>
          </a-form-item>
        </a-form>
      </a-modal>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import { USER_INFO } from '@/store/mutation-types'
import { getAction, postAction } from '@api/manage'
import REGEXP from '@/utils/regexp'
import { debounce } from 'lodash'

export default {
    inheritAttrs: false,
    props: {
        isList: {
            type: Boolean,
            default: true
        },
        visible: {
            type: Boolean,
            default: true
        },
        single: {
            type: Boolean,
            default: false
        },
        requiredFileType: {
            type: Boolean,
            default: false
        },
        disabledItemNumber: {
            type: Boolean,
            default: false
        },
        disabledHead: {
            type: Boolean,
            default: false
        },
        srmFileType: {
            type: Array,
            default: () => []
        },
        property: {
            type: String,
            default: 'materialName'
        },
        // 行项目 key
        itemNumberKey: {
            type: String,
            default: 'itemNumber'
        },
        itemNumbeValueProp: {
            type: String,
            default: ''
        },
        // 行项目 label
        itemNumberLabel: {
            type: String,
            default: '行项目'
        },
        attrCheck: {
            type: String,
            default: 'headId'
        }
    },
    data () {
        return {
            dataSource: [],
            spinning: false,
            delayTime: 300,
            userInfo: {},
            check: false,
            formItemLayout: {
                labelCol: { span: 6 },
                wrapperCol: { span: 14 }
            }
        }
    },
    computed: {
        // 父级传参
        attrs () {
            const { itemInfo = [], ...others } = this.$attrs || {}
            return {
                itemInfo,
                uploadAttrs: { ...others }
            }
        },
        // 获取物料行下拉选项
        itemInfo () {
            return this.attrs.itemInfo
        }
    },
    beforeCreate () {
        this.form = this.$form.createForm(this, { name: 'customUploadForm' })
    },
    created () {
        this.getUserInfo()
    },
    mounted () {
        this.$nextTick(() => {
            this.form.setFieldsValue({
                fileBelong: this.attrCheck
            })
            this.handleChange(this.attrCheck)
        })
    },
    methods: {
        onSelect (value) {
            const { materialName } = this.dataSource.find(n => n.materialNumber === value) || {}
            this.form.setFieldsValue({
                materialNumber: value,
                materialName: materialName
            })
        },

        handleSearch: debounce(function (value) {
            this.searchResult(value)
        }, 200),
        searchResult (query) {
            const url = '/material/purchaseMaterialHead/list'
            const params = {
                pageNo: 1,
                pageSize: 20
            }
            if (REGEXP.enOrNum.test(query)) {
                params.materialNumber = query
            } else {
                params.materialName = query
            }
            return getAction(url, params).then(res => {
                const records = res.result.records || []
                this.dataSource = records
            })
        },
        getUserInfo () {
            this.userInfo = this.$ls.get(USER_INFO)
        },
        beforeUpload (file) {
            let fileList = this.form.getFieldValue('fileList') || []
            fileList = [ ...fileList, file ]
            this.form.setFieldsValue({
                fileList
            })
            return window.Promise.reject(false)
        },
        handleRemove (file) {
            let fileList = this.form.getFieldValue('fileList')
            const index = fileList.indexOf(file)
            const newFileList = fileList.slice()
            newFileList.splice(index, 1)
            this.form.setFieldsValue({
                fileList: newFileList
            })
        },
        //附件上传
        handleUploadChange ({ file }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    if (file.response.code === 201) {
                        let { message, result: { msg, fileUrl, fileName } } = file.response
                        let href = this.$variateConfig['domainURL'] + fileUrl
                        this.$warning({
                            title: message,
                            content: (
                                <div>
                                    <span>{msg}</span><br/>
                                    <span>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_detailContent`, '具体详情请')} <a href={href} target="_blank" download={fileName}>点击下载</a> </span>
                                </div>
                            )
                        })
                    } else {
                        this.$message.success(file.response.message || `${file.name} 文件上传成功`)
                    }
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        },
        handleChange (e) {
            this.check = e.target ? e.target.value === 'itemNumber' : e === 'itemNumber'
            this.$nextTick(() => {
                this.form.validateFields(['itemNumber'], { force: true })
            })
        },
        resetData () {
            this.form.resetFields()
            Object.assign(this.$data, this.$options.data.call(this))
        },
        handleCancel () {
            this.$emit('hide')
        },
        handleConfirm () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))

            this.form.validateFields((err, values) => {
                console.log('values', values)
                if (err) {
                    return
                }
                // 获取上传方信息
                const { elsAccount, subAccount, realname } = this.userInfo
                const uploadElsAccount = [ elsAccount, subAccount, realname ].join('_')

                const {  materialNumber = null, materialName = null, fileBelong = null, itemNumber = null, fileType = '', fileList = [] } = values || {}

                const promises = fileList.map(file => {
                    const formData = new FormData()
                    let header = { ...this.$attrs.data, uploadElsAccount }
                    if (this.isList) {
                        header = Object.assign({}, header, {
                            materialNumber,
                            materialName
                        })
                    } else {
                        header = Object.assign({}, header, {
                            fileType
                        })
                        if (fileBelong === 'itemNumber') {
                            header[this.itemNumberKey] = itemNumber
                        }
                    }
                    // 获取 headId 与 businessType
                    Object.keys(header).forEach(key => {
                        formData.append(key, header[key])
                    })
                    formData.append('file', file)
                    return postAction(this.$attrs.action, formData)
                })
                console.log('promises', promises)
                this.spinning = true
                Promise.all(handlePromise(promises))
                    .then(res => {
                        const result = res
                            .filter(n => n.status === 'success')
                            .map(n => n.res.result)
                        this.$emit('custom_upload_create', result)
                        console.log('result', result)
                    })
                    .finally((err) => {
                        console.log('err', err)
                        this.spinning = false
                    })
            })
        },
        handleSubmit (e) {
            e.preventDefault()
            this.form.validateFields((err, values) => {
                if (!err) {
                    console.log('Received values of form: ', values)
                }
            })
        },
        normFile (e) {
            console.log('Upload event:', e)
            if (Array.isArray(e)) {
                return e
            }
            return e && e.fileList
        }
    }
}
</script>

<style lang="less" scoped>
.theTooltip{
    line-height: 20px;
}
.custom-upload-form {
  .collectionForm {
    .dropbox {
      height: 180px;
      line-height: 1.5;
    }
  }
}
.global-search {
  width: 100%;
}

.global-search.ant-select-auto-complete .ant-select-selection--single {
  margin-right: -46px;
}

.global-search.ant-select-auto-complete .ant-input-affix-wrapper .ant-input:not(:last-child) {
  padding-right: 62px;
}

.global-search.ant-select-auto-complete .ant-input-affix-wrapper .ant-input-suffix button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.global-search-item {
  display: flex;
}

.global-search-item-desc {
  flex: auto;
  text-overflow: ellipsis;
  overflow: hidden;
}

.global-search-item-count {
  flex: none;
}
</style>