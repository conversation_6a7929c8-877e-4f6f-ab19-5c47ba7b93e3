<template>
  <div>
    <a-modal
    v-drag    
      centered
      :title="modalTitle"
      :width="960"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="close"
    >
      <a-input-search
        :placeholder="placeholder"
        style="margin-bottom:8px"
        @search="onSearch"
        enterButton />
      <vxe-grid
        border
        resizable
        show-overflow
        highlight-hover-row
        max-height="350"
        row-id="id"
        size="small"
        :loading="loading"
        ref="selectGrid"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
        :data="tableData"
        :pager-config="isTree ? null : tablePage"
        :radio-config="selectModel === 'single' ? checkedConfig : undefined"
        :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
        :tree-config="isTree ? treeConfig : null"
        :columns="columns"
        @page-change="handlePageChange"
      >
      </vxe-grid>
    </a-modal>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'FieldSelectModal',
    props: {
        isEmit: {
            type: Boolean,
            default: false
        },
        isTree: {
            type: Boolean,
            default: false
        },
        treeConfig: {
            type: Object,
            default () {
                return {}
            }
        },
        pageConfigData: {
            type: Object,
            default () {
                return {}
            }
        },
        modalTitle: {
            type: String,
            default () {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SelectData`, '选择数据')
            }
        },
        placeholder: {
            type: String,
            default () {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterYourName`, '请输入姓名')
            }
        }
    },
    data () {
        return {
            visible: false,
            loading: false,
            confirmLoading: false,
            columns: [],
            url: '',
            selectModel: 'single',
            checkedConfig: { highlight: true, reserve: true, trigger: 'row' },
            queryParams: {},
            tableData: [],
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                align: 'left',
                pageSizes: [10, 20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            resInit: null,
            pageInit: null,
            keyWordCode: null,
            actionType: 'get'
        }
    },
    methods: {
        loadData (params, extend) {
            this.loading = true
            let pageInit = this.pageInit
            let resInit = this.resInit
            if (pageInit && typeof pageInit == 'function') {
                params = this.pageInit(params)
            }
            console.log(params)
            let fn = this.actionType == 'get' ? getAction : postAction
            fn(this.url, params).then(res => {
                let resData = res
                if (resInit && typeof resInit == 'function') {
                    resData = resInit(resData)
                }
                const flag = this.isTree
                    ? resData.success && resData.result && resData.result.length
                    : resData.success && resData.result && resData.result.records && resData.result.records.length
                if (flag) {
                    let result = this.isTree ? resData.result : resData.result.records
                    result = result || []
                    this.tableData = result
                    this.tablePage.total = resData.result.total
                    if (extend && extend.action === 'init') {
                        // 初始化清空选项 包括跨页勾选的
                        if (this.selectModel === 'single') {
                            this.$refs.selectGrid && this.$refs.selectGrid.clearRadioReserve()
                        } else {
                            this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxReserve()
                        }
                    }
                } else {
                    this.tableData = []
                    this.tablePage.total = 0
                    this.tablePage.currentPage = 1
                }
                this.loading = false
            })
        },
        open (url, params, columns, selectModel, checkedConfig, paramsReset) {
            if (paramsReset && typeof paramsReset == 'object') {
                this.resInit = paramsReset.resInit
                this.pageInit = paramsReset.pageInit
                this.keyWordCode = paramsReset.keyWordCode
                this.actionType = paramsReset.actionType
            }
            this.tablePage.currentPage = 1
            let tableColumns = columns ? [...columns] : []
            this.queryParams = { pageSize: this.tablePage.pageSize, pageNo: this.tablePage.currentPage }
            checkedConfig ? (this.checkedConfig = { ...this.checkedConfig, ...checkedConfig }) : ''
            this.url = url
            if (selectModel) {
                this.selectModel = selectModel
            }
            tableColumns.unshift({
                type: 'seq',
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),
                width: 60
            })
            if (this.selectModel === 'single') {
                tableColumns.unshift({ type: 'radio', width: 40 })
            } else if (this.selectModel === 'multiple') {
                tableColumns.unshift({ type: 'checkbox', width: 40 })
            }
            tableColumns.forEach(col => {
                // 国际化处理
                if (col.fieldLabelI18nKey) {
                    col.title = this.$srmI18n(`${this.$getLangAccount()}#${col.fieldLabelI18nKey}`, col.title)
                }
            })
            this.columns = tableColumns
            if (params) {
                this.queryParams = Object.assign({}, this.queryParams, params)
            }
            this.loadData(this.queryParams, { action: 'init' })
            this.visible = true
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            let selectedData = this.$refs.selectGrid.getCheckboxRecords() // 当前页已选择数据
            if (this.selectModel === 'single') {
                selectedData = this.$refs.selectGrid.getRadioRecord() ? [this.$refs.selectGrid.getRadioRecord()] : []
            } else {
                const otherPageCheckData = this.$refs.selectGrid.getCheckboxReserveRecords() || [] // 不包含当前页已选中数据
                selectedData = [...otherPageCheckData, ...selectedData]
            }
            if (selectedData.length) {
                this.visible = false
                if (this.pageConfigData.itemColumns) {
                    // 表行
                    selectedData.forEach(item => {
                        this.pageConfigData.itemColumns.forEach(el => {
                            if (el.defaultValue && (item[el.field] == '' || item[el.field] == null)) {
                                // 模板有默认值且当前表单返回没有值
                                item[el.field] = el.defaultValue
                            }
                        })
                    })
                }
                if (this.isEmit) {
                    this.$emit('ok', selectedData)
                } else {
                    this.$parent.fieldSelectOk(selectedData)
                }
            } else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
            }
        },
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            let params = Object.assign({}, this.queryParams, { pageSize: pageSize, pageNo: currentPage })
            this.loadData(params)
        },
        onSearch (keyWord) {
            let code = this.keyWordCode || 'keyWord'
            this.queryParams = Object.assign({}, this.queryParams, { [code]: keyWord })
            let params = Object.assign({}, this.queryParams, { pageNo: 1 })
            this.loadData(params)
        }
    }
}
</script>
