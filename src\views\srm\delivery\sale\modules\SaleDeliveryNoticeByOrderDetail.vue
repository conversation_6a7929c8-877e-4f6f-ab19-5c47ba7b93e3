<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url"
    >
    </detail-layout>
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />

  </div>
</template>
<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {getAction} from '@/api/manage'
import RelationGraphModal from '@comp/RelationGraphModal'
export default {
    mixins: [DetailMixin],
    components: {
        RelationGraphModal
    },
    data () {
        return {
            showRemote: false,
            pageData: {
                selectedType: '',
                form: {
                },
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eRcVH_2730e70f`, '通知行信息'), groupType: 'item', groupCode: 'purchaseDeliveryNoticeList', type: 'grid', custom: {
                        ref: 'saleDeliveryNotices',
                        columns: [],
                        buttons: [
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/delivery/saleDeliveryNoticeOrderHead/queryById',
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_purchaseDeliveryNoticeByOrder_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    mounted () {
        this.initInfo()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id, this.syncPlanList)
            }
        },
        initInfo () {
            if (this.currentEditRow && this.currentEditRow.id) {
                getAction(this.url.detail, {id: this.currentEditRow.id}).then((res) => {
                    if (res && res.success) {
                        this.currentEditRow = res.result
                        this.showRemote = true
                    }
                })
            } else {
                this.showRemote = true
            }
        },
        goBack () {
            this.$emit('hide')
        }
    }
}
</script>
<style lang="scss" scoped>
</style>