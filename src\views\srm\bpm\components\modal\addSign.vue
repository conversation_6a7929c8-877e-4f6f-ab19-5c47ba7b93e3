<template>
    <div style="margin: 0 10px 0 40px;">
        <a-spin :spinning="loading">
            <a-form-model :model="form" :rules="rules" :ref="formName" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_uPAc_276e27ce`, '加签类型')"
                    prop="parallel">
                    <a-radio-group name="radioGroup" @change="handleChangeParallel" :defaultValue="false"
                        v-model="form.parallel">
                        <a-radio v-for="(item, index) in [
                            { lable: $srmI18n(`${$getLangAccount()}#i18n_field_Vc_a005a`, '串行'), value: false },
                            { lable: $srmI18n(`${$getLangAccount()}#i18n_field_Gc_bf896`, '并行'), value: true }
                        ]" :key="index" :value="item.value">{{ item.lable }}</a-radio>
                    </a-radio-group>
                </a-form-model-item>

                <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_userDeal`, '处理人')" prop="usersInfo">
                    <a-tag v-for="(users, userIndex) in form.usersInfo" :key="users.id" size="large" color="blue" closable
                        @close="delCommonUsers(userIndex)">{{ users.fullName }}</a-tag>
                    <div>
                        <a-button type="primary" icon="user" @click="selectInformedUsers"></a-button>
                    </div>
                </a-form-model-item>
                <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_LSRL_2527e109`, '任务名称')"
                    prop="userTaskName">
                    <a-input v-model="form.userTaskName" clearable />
                </a-form-model-item>
                <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见')"
                    prop="opinion">
                    <a-textarea show-word-limit v-model="form.opinion" allowClear type="textarea" autoSize />
                </a-form-model-item>
            </a-form-model>
        </a-spin>
        <field-select-modal ref="fieldSelectModal" />
    </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { getOutgoingFlows, addSign } from '../../api/analy'
export default {
    mixins: [modalMixins],
    data () {
        return {
            rules: {
                userTaskName: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMLSRL_9060674e`, '请填写任务名称'), trigger: 'change' },
                    { max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符') }
                ],
                opinion: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMqdWII_13f544fb`, '请填写备注/意见'), trigger: 'change' },
                    { max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符') }
                ],
                usersInfo: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFGvL_f32585e1`, '请选择处理人') }]
            },
            singleUserData: {}
        }
    },
    methods: {
        handleChangeParallel (e) {
            if (e.target.value) {
                this.$set(this.form, 'unPass', 'next')
                this.$set(this.form, 'pass', 'next')
            } else {
                this.$set(this.form, 'unPass', '')
                this.$set(this.form, 'pass', '')
            }

        },
        selectInformedUsers () {
            this.showUserSelectModal({ selectModel: 'multiple' })
        },
        fieldSelectOk (data) {
            this.$set(this.form, 'usersInfo', data)
        },
        delCommonUsers (index) {
            this.form.usersInfo.splice(index, 1)
        },
        handleConfirm () {
            this.cheackValidate().then(() => {
                let { userTaskName, usersInfo, parallel, opinion, taskId } = this.form
                let userIdArr = usersInfo.map(item => {
                    return item.id
                })
                let userIds = userIdArr.join(',')
                let params = {
                    taskId,
                    userTaskName,
                    userIds,
                    parallel,
                    opinion
                }
                this.loading = true
                addSign(params).then(res => {
                    this.loading = false
                    if (res.code == 200) {
                        this.$emit('success')
                        this.$message.success(res.message)
                    } else {
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.loading = false
                    this.$emit('closeLoading')
                })
            })
        }
    },
    created () {
        this.loading = true
        getOutgoingFlows(this.taskId).then(res => {
            if (res.code == 0) {
                this.$set(this.form, 'nodeArray', res.data)
            }
            this.loading = false
        })
        this.$set(this.form, 'parallel', false)
    }
}
</script>
  