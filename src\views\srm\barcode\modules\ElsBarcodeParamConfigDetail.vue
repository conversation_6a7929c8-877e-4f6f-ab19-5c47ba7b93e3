<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url"
      @loadSuccess="handleLoadSuccess" />
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction } from '@/api/manage'
export default {
    name: 'ElsBarcodeParamConfigDetail',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            showRemote: false,
            selectType: 'esignEnterpriseCertification',
            pageData: {
                form: {
                    loadingCompany: '',
                    companyCode: '',
                    companyName: '',
                    subAccount: '',
                    accountId: '',
                    idNumber: '',
                    idType: '',
                    orgLegalIdNumber: '',
                    orgLegalName: '',
                    authType: ''
                },
                groups: [

                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/base/barcode/elsBarCodeParamConfig/queryById'

            }
        }
    },
    computed: {
        fileSrc () {
            console.log(this.currentEditRow)
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let busAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${busAccount}/purchase_barCodeParamConfig_${templateNumber}_${templateVersion}.js?t=` + time

        }
    },
    created () {
        if (this.$route.query && this.$route.query.open  && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        }else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    mounted () {
        this.init()
    },
    methods: {
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>