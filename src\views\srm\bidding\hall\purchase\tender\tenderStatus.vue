<template>
  <div class="tender-status">
    <content-header
      v-if="showHeader"
      class="posA"
      @content-header-sumbit="handleSubmit()"
    />
    <div
      class="container"
      :style="style">
      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_title_materialQuotation`, '物料报价情况') }}</div>
          <div class="table">
            <vxe-grid
              ref="purchaseBiddingItemList"
              v-bind="defaultGridOption"
              :columns="specialistColumns"
              :data="purchaseBiddingItemList">
              <template slot="empty">
                <a-empty />
              </template>
              <template #taxCode_default="{ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, _columnIndex }">
                <a-input
                  readOnly
                  v-model="row[column.property]"
                  @click="() => handleAddItem({row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, _columnIndex})">
                  <a-icon
                    slot="suffix"
                    type="close-circle"
                  ></a-icon>
                </a-input>
              </template>
              <!-- 含税单价 -->
              <template #price_default="{ row, rowIndex}">
                <a-input
                  v-model="row.price"
                  clearable
                  :disabled="isPriceEnable"
                  @blur="handlePriceBlur({row, rowIndex})">
                  <a-icon
                    slot="suffix"
                    type="close-circle"
                  ></a-icon>
                </a-input>
              </template>
              <!-- 不含税单价 -->
              <template #netPrice_default="{ row, rowIndex}">
                <a-input
                  v-model="row.netPrice"
                  clearable
                  :disabled="!isPriceEnable"
                  @blur="handleNetPriceBlur({row, rowIndex})">
                  <a-icon
                    slot="suffix"
                    type="close-circle"
                  ></a-icon>
                </a-input>
              </template>
            </vxe-grid>
          </div>
        </div>
        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单') }}
          </div>
          <div class="table">
            <vxe-grid
              ref="purchaseAttachmentDemandList"
              v-bind="defaultGridOption"
              :columns="checklistColumns"
              :data="purchaseAttachmentDemandList">
              <template slot="empty">
                <a-empty />
              </template>
            </vxe-grid>
          </div>
        </div>
        <div class="itemBox">
          <div class="title dark">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_attachmentList`, '附件列表') }}
            <!-- <div class="supplier_list_upload">
              <custom-upload
                title="上传附件"
                :visible.sync="modalVisible"
                :itemInfo="itemInfo"
                :action="uploadUrl"
                :accept="accept"
                :headers="tokenHeader"
                :data="uploadData"
                @change="(info) => handleUploadChange(info)">
              </custom-upload>
            </div> -->
          </div>
          <div class="table">
            <vxe-grid
              ref="purchaseAttachmentList"
              v-bind="defaultGridOption"
              :columns="accessoryListColumns"
              :data="purchaseAttachmentList">
              <template slot="empty">
                <a-empty />
              </template>
              <template #grid_opration="{ row, column }">
                <a
                  v-for="(item, i) in optColumnList"
                  :key="i"
                  :title="item.title"
                  v-show="item.title == '下载'"
                  style="margin:0 4px"
                  @click="item.clickFn(row, column, 'purchaseBiddingSpecialistList')" >{{ item.title }}</a>
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>
    <field-select-modal ref="fieldSelectModal" />
    
  </div>
</template>

<script>
import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import CustomUpload from '@comp/template/CustomUpload'
import { getAction, postAction } from '@/api/manage'
import { isDecimal } from '@/utils/validate.js'
import {  mul, div } from '@/utils/mathFloat.js'
import countdown from '@/components/countdown'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    name: 'TenderStatus',
    components: {
        ContentHeader,
        fieldSelectModal,
        CustomUpload,
        countdown
    },
    data () {
        return {
            modalVisible: false,
            serverTime: null,
            showHeader: true,
            confirmLoading: true,
            height: 0,
            purchaseBiddingItemList: [],
            purchaseBiddingItemListIndex: 0,
            purchaseAttachmentDemandList: [],
            purchaseAttachmentList: [],
            optColumnList: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteItemEvent },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadFile }
            ],
            uploadUrl: '/attachment/purchaseAttachment/upload',
            tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                columns: [],
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'dblclick', mode: 'cell' }
            },
            specialistColumns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), field: 'materialNumber', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), field: 'name', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'), field: 'materialSpec', width: 150},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialModel`, '物料型号'), field: 'materialModel', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量'), field: 'requireQuantity', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), field: 'purchaseUnit_dictText', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PVJSBA_bf78d67e`, '要求交货日期'), field: 'requireDate', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_firstCommittedDeliveryDate`, '承诺交货日期'), field: 'deliveryDate', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), field: 'currency_dictText', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'), field: 'taxCode', width: 150,  editDisabled: true },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), field: 'taxRate', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'), field: 'netPrice', width: 150 }, //
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'), field: 'price', type: 'number',  width: 150},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notTotalAmountIncludTaxi`, '未税总金额'), field: 'netAmount', width: 150 }, //
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_totalAmountIncludTaxi`, '含税总金额'), field: 'taxAmount', width: 150 }
            ],
            checklistColumns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), field: 'fileType_dictText', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), field: 'stageType_dictText', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), field: 'required_dictText', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), field: 'remark', width: 150 }
            ],
            accessoryListColumns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), field: 'fileType_dictText', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), field: 'fileName', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), field: 'uploadTime', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), field: 'uploadElsAccount_dictText', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_comfigSend`, '是否发送'), field: 'sendStatus_dictText', width: 150 },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
            ],
            url: {
                delete: '/attachment/purchaseAttachment/delete',
                publish: '/bidding/purchaseBiddingHead/quoteBidding'
            },
            form: {}
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        evaEndTime () {
            return this.vuex_currentEditRow.evaEndTime
        },
        // 不含税单价是否可输入
        isPriceEnable () {
            const { quoteType = '0' } = this.form || {}
            return !!(quoteType === '1')
        },
        uploadData () {
            return {businessType: 'bidding', headId: this.vuex_currentEditRow.id}
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        itemInfo () {
            let itemInfo = []
            // const groups = this.pageData.groups || []
            // const group = groups.find(n => n.groupCode === 'itemInfo')
            // if (group) {
            //     const refName = group.custom.ref
            //     itemInfo = this.$refs[refName][0].getTableData().fullData || []
            // }
            return itemInfo
        }
    },
    created () {
        this.init()
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        renderExtra (deadline, fn) {
            const scopedSlots = {
                default: (row) => {
                    return (<span>{ row.hours } : { row.minutes } : { row.seconds }</span>)
                }
            }
            return (
                <div class="countdown" style="display: flex; align-items: center;">
                    <a-icon type="info-circle" />
                    <span style="margin: 0 8px;">{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_evaEndTime`, '评标截止时间') }</span>
                    <countdown 
                        time={ deadline }
                        scopedSlots={scopedSlots}
                        style={ { fontSize: '12px', color: '#ee1d1d' } }
                        end={ fn }
                    >
                    </countdown> 
                </div>
            )
        },
        // 含税单价, 实时计算不含税价
        handlePriceBlur (data) {
            console.log(data)
            let { row, rowIndex } = data
            this.purchaseBiddingItemListIndex = rowIndex
            let value = row.price
            // if (!this.isPriceEnable) {
            //     return
            // }
            if (!value || !isDecimal(value)) return
            let { taxRate = 0 } =  row || {}
            if (!taxRate || !isDecimal(taxRate)) {
                taxRate = 0
            }
            let addTax = taxRate + 100
            let mulAddTax = div(addTax, 100)
            let netPrice = mul(value, mulAddTax)
            netPrice = netPrice.toFixed(2)
            this.$set(this.purchaseBiddingItemList[this.purchaseBiddingItemListIndex], 'netPrice', netPrice)
        },
        // 不含税单价, 实时计算含税价
        handleNetPriceBlur (data) {
            let { row, rowIndex } = data
            this.purchaseBiddingItemListIndex = rowIndex
            let value = row.netPrice
            // if (this.isPriceEnable) {
            //     return
            // }
            if (!value || !isDecimal(value)) return
            let { taxRate = 0 } = row || {}
            if (!taxRate || !isDecimal(taxRate)) {
                taxRate = 0
            }
            let addTax = taxRate + 100
            let mulAddTax = div(100, addTax)
            let price = mul(value, mulAddTax)
            price = price.toFixed(2)
            // this.quoteGridOptions.data[0].price = price
            this.$set(this.purchaseBiddingItemList[this.purchaseBiddingItemListIndex], 'price', price)
        },
        // 文件下载
        handleDownload ({ id, fileName }) {
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        downloadFile (row){
            let fromData = this.form
            let supplier = fromData.biddingSupplierList[0]
            let elsAccount = this.$ls.get('Login_elsAccount')
            if(row.fileType == '2' && row.uploadElsAccount != elsAccount){
                if(supplier.bidCheck != '1'){
                    this.$message.warning('暂无标书下载权限！')
                    return 
                }
            }
            this.handleDownload(row)
        },
        fieldSelectOk (selectData){
            console.log(selectData)
            let {taxCode, taxRate } = selectData[0]
            this.$set(this.purchaseBiddingItemList[this.purchaseBiddingItemListIndex], 'taxCode', taxCode)
            this.$set(this.purchaseBiddingItemList[this.purchaseBiddingItemListIndex], 'taxRate', taxRate)
        },
        //附件上传
        handleUploadChange (info) {
            this.purchaseAttachmentList = [...this.purchaseAttachmentList, ...info]
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
        },
        deleteItemEvent (row) {
            let params = {id: row.id}
            let that = this
            getAction(this.url.delete, params).then(res => {
                console.log(res)
                that.init()
            })
        },
        handleSubmit () {
            console.log(this.form)
            let params = {...this.form, purchaseBiddingItemList: this.purchaseBiddingItemList, purchaseAttachmentDemandList: this.purchaseAttachmentDemandList, purchaseAttachmentList: this.purchaseAttachmentList}
            postAction(this.url.publish, params).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    this.reload()
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        reload () {
            window.location.reload()
        },
        // 上传前检查
        checkedGridSelect () {

        },
        handleAddItem (data) {
            console.log(data)
            this.purchaseBiddingItemListIndex = data.rowIndex
            // this.selectType = type
            let url = '/base/tax/list'
            let columns = [
                {field: 'taxCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'), with: 150},
                {field: 'taxRate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), width: 150},
                {field: 'taxName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxName`, '税码名称'), width: 150},
                {field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'), with: 150}
            ]
            // 参数
            this.$refs.fieldSelectModal.open(url, {elsAccount: this.vuex_currentEditRow.busAccount}, columns)
        },
        getData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingHead/queryById'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { purchaseBiddingItemList = [], purchaseAttachmentDemandList = [], purchaseAttachmentList = [], ...others } = res.result || {}
                    this.form = { ...others }
                    this.purchaseBiddingItemList = purchaseBiddingItemList
                    this.purchaseAttachmentDemandList = purchaseAttachmentDemandList
                    this.purchaseAttachmentList = purchaseAttachmentList
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    }
}
</script>

<style lang="less" scoped>
.tender-status {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
    .supplier_list_upload{
        float: right;
    }
}
</style>