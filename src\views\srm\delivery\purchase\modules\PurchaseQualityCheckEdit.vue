<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      refresh
      :url="url"
    />
    <field-select-modal ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import moment from 'moment'

export default {
    name: 'PurchaseQualityCheckEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'material',
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineInformation`, '行信息'), groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                            ref: 'purchaseQualityCheckItemList',
                            columns: [],
                            buttons: [
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addBiddingItem, disabled: this.editBtn, authorityCode: 'qualityCheck#purchaseQualityCheckHead:add' },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteItemEvent, disabled: this.editBtn, authorityCode: 'qualityCheck#purchaseQualityCheckHead:delete'}
                            ]
                        }
                    }
                    ,
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                            ref: 'purchaseAttachmentList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                // { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                                { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                            ],
                            buttons: [
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                    type: 'upload', businessType: 'qualityCheck',
                                    attr: this.attrHandle,
                                    callBack: this.uploadCallBack },
                                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch}
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                            ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent, authorityCode: 'qualityCheck#purchaseQualityCheckHead:edit'},
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DJ_c64d4`, '提交'),
                        type: 'primary',
                        click: this.submitEvent,
                        showCondition: this.status0ConditionBtn,
                        authorityCode: 'qualityCheck#purchaseQualityCheckHead:submit'
                    },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publishEvent, showCondition: this.status1ConditionBtn, authorityCode: 'qualityCheck#purchaseQualityCheckHead:publish' },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'), type: 'primary', click: this.submit, showCondition: this.statusSubmitConditionBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/quality/purchaseQualityCheckHead/add',
                edit: '/quality/purchaseQualityCheckHead/edit',
                detail: '/quality/purchaseQualityCheckHead/queryById',
                public: '/quality/purchaseQualityCheckHead/publish',
                saveSubmit: '/quality/purchaseQualityCheckHead/submit',
                submit: '/elsUflo/audit/submit',
                upload: '/attachment/purchaseAttachment/upload'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_qualityCheck_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.checkNumber,
                actionRoutePath: '/srm/delivery/purchase/PurchaseQualityCheckList,/srm/delivery/sale/SaleQualityCheckList'
            }
        },
        init () {
            let self = this
            if (this.currentEditRow) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id, (data) => {
                    if (data.checkStatus == '1') {
                        for (let group of self.pageData.groups) {
                            if (group.type) continue
                            let formFields = group.custom.formFields
                            for (let sub of formFields) {
                                sub.disabled = true
                            }
                        }
                    }
                })
            }

        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        status0ConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let checkStatus = params.checkStatus
            if (checkStatus == '0' && params.id) {
                return true
            } else {
                return false
            }
        },
        editBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let checkStatus = params.checkStatus
            if (checkStatus == '0') {
                return false
            } else {
                return true
            }
        },
        statusSubmitConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let checkStatus = params.checkStatus
            let publishAudit = params.publishAudit
            if (checkStatus == '1' && publishAudit == 1) {
                return true
            } else {
                return false
            }
        },
        status1ConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let checkStatus = params.checkStatus
            let publishAudit = params.publishAudit
            if (checkStatus == '1' && publishAudit != 1) {
                return true
            } else {
                return false
            }
        },
        addBiddingItem () {
            this.selectType = 'material'
            const form = this.$refs.editPage.getPageData()
            console.log(form)
            const { mustMaterialNumber = '1' } = form
            if (mustMaterialNumber == '1') {
                let url = '/material/purchaseMaterialHead/list'
                let columns = [
                    { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150 },
                    { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                    { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200 },
                    { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200 },
                    { field: 'materialGroup', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroup`, '物料组'), width: 80 },
                    { field: 'materialGroupName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroupName`, '物料组名称'), width: 80 },
                    { field: 'cateCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SLzA_3589d785`, '物料分类'), width: 80 },
                    { field: 'cateName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'), width: 80 },
                    { field: 'checkType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_checkType`, '检验类型'), width: 200 }
                ]
                this.$refs.fieldSelectModal.open(url, undefined, columns, 'multiple')
            } else {
                let itemGrid = this.$refs.editPage.$refs.purchaseQualityCheckItemList[0]
                let itemData = {}
                this.pageConfig.itemColumns.forEach(item => {
                    if (item.defaultValue) {
                        itemData[item.field] = item.defaultValue
                    }
                })
                itemGrid.insertAt([itemData], -1)
            }
        },
        deleteItemEvent () {
            let itemGrid = this.$refs.editPage.$refs.purchaseQualityCheckItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        fieldSelectOk (data) {
            if (this.selectType == 'material') {
                let itemGrid = this.$refs.editPage.$refs.purchaseQualityCheckItemList[0]
                let { fullData } = itemGrid.getTableData()
                let materialList = fullData.map(item => {
                    return item.materialNumber
                })
                //过滤已有数据
                let insertData = data.filter(item => {
                    return !materialList.includes(item.materialNumber)
                })
                this.pageConfig.itemColumns.forEach(item => {
                    if (item.defaultValue) {
                        insertData.forEach(insert => {
                            if (item.field == 'sourceType') {
                                insert[item.field] = '1'
                            } else if (!insert[item.field]) {
                                insert[item.field] = item.defaultValue
                            }
                        })
                    }
                })
                itemGrid.insertAt(insertData, -1)
            }
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', { id: row.id }).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        saveEvent () {
            let itemGrid = this.$refs.editPage.$refs.purchaseQualityCheckItemList[0]
            let { fullData } = itemGrid.getTableData() || []
            console.log(fullData)
            if (fullData && fullData.length > 0) {
                var isStop
                fullData.forEach(item => {
                    let numberDefectiveProducts = parseFloat(item.numberDefectiveProducts)
                    let checkQuantity = parseFloat(item.checkQuantity)
                    if (numberDefectiveProducts < 0 || numberDefectiveProducts > checkQuantity) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WWxRNWRWWIOWR_4a3441be`, '0<不良品数量<=检验数量'))
                        isStop = true

                    }
                })
            }
            if (isStop) {
                return
            }
            this.$refs.editPage.postData()
        },
        checkValidate (form) {
            if (form.checkStartDate && form.checkEndDate && !moment(form.checkStartDate).isBefore(form.checkEndDate)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IOyWKITfUIOvKKIW_e0127adb`, '检验结束时间需大于检验开始时间！'))
                return false
            }
            return true
        },
        submitEvent () {
            let itemGrid = this.$refs.editPage.$refs.purchaseQualityCheckItemList[0]
            let { fullData } = itemGrid.getTableData() || []
            const form = this.$refs.editPage.getPageData()
            if (!this.checkValidate(form)) return
            if (fullData && fullData.length > 0) {
                var isStop
                fullData.forEach(item => {
                    let numberDefectiveProducts = parseFloat(item.numberDefectiveProducts)
                    let checkQuantity = parseFloat(item.checkQuantity)
                    if (numberDefectiveProducts < 0 || numberDefectiveProducts > checkQuantity) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WWxRNWRWWIOWR_4a3441be`, '0<不良品数量<=检验数量'))
                        isStop = true

                    }
                })
            } else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cVHxOLV_c6f5290a`, '行信息不能为空'))
                return
            }
            if (isStop) return
            const _this = this
            const fn = (url, param, vm) => {
                console.log('vm :>> ', vm) // 编辑模板组件实例
                _this.$refs.editPage.confirmLoading = true
                postAction(url, param).then(res => {
                    const type = res.success ? 'success' : 'error'
                    _this.$message[type](res.message)
                    if (res.success) {
                        _this.goBack()
                    }
                }).finally(() => {
                    _this.$refs.editPage.confirmLoading = false
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DJRI_2e98006c`, '提交质检'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQDJRIW_ae3626bc`, '是否提交质检？'),
                onOk () {
                    _this.$refs.editPage.handValidate(_this.url.saveSubmit, form, fn)
                }
            })
        },
        publishEvent () {
            const form = this.$refs.editPage.getPageData()
            if (!this.checkValidate(form)) return
            if (form.publishAudit == '1') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xqRyhxWVDJUzW_6b21a4c5`, '不可直接发布，请提交审批！'))
                return
            }
            this.$refs.editPage.handleSend()
        },
        submit () {
            const form = this.$refs.editPage.getPageData()
            if (!this.checkValidate(form)) return
            if (form.publishAudit != '1') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_STDJUzW_6e48a495`, '无需提交审批！'))
                return
            }
            const _this = this
            const fn = (url, param, vm) => {
                console.log('vm :>> ', vm) // 编辑模板组件实例
                _this.$refs.editPage.confirmLoading = true
                postAction(url, param).then(res => {
                    const type = res.success ? 'success' : 'error'
                    _this.$message[type](res.message)
                    if (res.success) {
                        //_this.goBack()
                        this.$parent.submitCallBack(form)
                    }
                }).finally(() => {
                    _this.$refs.editPage.confirmLoading = false
                })
            }
            const param = {
                businessId: form.id,
                businessType: 'publishQualityCheck',
                auditSubject: `来料检测单发布审批，单号：${form.checkNumber}`,
                params: JSON.stringify(form)
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLDJUz_8f71e759`, '是否确认提交审批'),
                onOk () {
                    _this.$refs.editPage.handValidate(_this.url.submit, param, fn)
                }
            })
        },
        // 批量删除
        deleteBatch (){
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        }
    }
}
</script>