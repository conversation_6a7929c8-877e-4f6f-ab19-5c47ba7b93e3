<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
export default {
    name: 'ElsSealsAdd',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'seals',
            confirmLoading: false,
            pageData: {
                form: {
                    relationId: '',
                    companyName: '',
                    alias: '',
                    height: '',
                    width: '',
                    transparentFlag: '',
                    filePath: '',
                    sealId: '',
                    subAccount: '',
                    orgId: '',
                    accountId: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WeLD_27cabba0`, '印章维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_transferType`, '类型'),
                                    fieldName: 'sealType',
                                    dictCode: 'srmEsignSealsType',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationOrg`, '选择机构'),
                                    fieldName: 'orgName',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'),
                                    fieldName: 'subAccount'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_sMURgjgo`, '印章别名'),
                                    fieldName: 'alias',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_OQj0KZ8V`, '关联机构id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_yVy933rH`, '机构id'),
                                    fieldName: 'orgId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_HS1PSf1z`, '子账号id'),
                                    fieldName: 'accountId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'number',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeVzpx_371db172`, '印章宽度(px)'),
                                    fieldName: 'width',
                                    placeholder: '个人默认95px, 机构默认159px'
                                },
                                {
                                    fieldType: 'number',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Wexzpx_e67fab37`, '印章高度(px)'),
                                    fieldName: 'height',
                                    placeholder: '个人默认95px, 机构默认159px'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_transparentFlag`, '是否对图片进行透明化处理'),
                                    fieldName: 'transparentFlag',
                                    defaultValue: '1',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'image',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_qBGuFJsS`, '图片路径'),
                                    fieldName: 'filePath',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sealId`, '印章id'),
                                    fieldName: 'sealId',
                                    disabled: true
                                }
                            ]
                        }

                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/esign/purchaseEsignSeals/queryById'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>