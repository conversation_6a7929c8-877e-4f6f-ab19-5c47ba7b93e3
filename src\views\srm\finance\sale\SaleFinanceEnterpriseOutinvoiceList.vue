<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />

    <!-- 编辑页面 -->
    <SaleFinanceEnterpriseOutinvoiceEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hidePage"/>

    <!-- 详情界面 -->
    <SaleFinanceEnterpriseOutinvoiceDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import SaleFinanceEnterpriseOutinvoiceEdit from './modules/SaleFinanceEnterpriseOutinvoiceEdit'
import SaleFinanceEnterpriseOutinvoiceDetail from './modules/SaleFinanceEnterpriseOutinvoiceDetail'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    mixins: [ListMixin],
    components: {
        SaleFinanceEnterpriseOutinvoiceEdit,
        SaleFinanceEnterpriseOutinvoiceDetail
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pfLKqy_11eaf4b3`, '纳税人识别号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary',  authorityCode: 'finance#saleFinanceEnterpriseOutinvoice:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView,  authorityCode: 'finance#saleFinanceEnterpriseOutinvoice:view' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit,  authorityCode: 'finance#saleFinanceEnterpriseOutinvoice:edit' },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete,  authorityCode: 'finance#saleFinanceEnterpriseOutinvoice:delete' },
                    { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord }
                ],
                optColumnWidth: 150
            },
            url: {
                list: '/finance/financeEnterpriseOutinvoice/saleToSaleByList',
                delete: '/finance/financeEnterpriseOutinvoice/saleToDelete',
                deleteBatch: '/finance/financeEnterpriseOutinvoice/deleteBatch',
                exportXlsUrl: 'finance/financeEnterpriseOutinvoice/exportXls',
                importExcelUrl: 'finance/financeEnterpriseOutinvoice/importExcel',
                columns: 'saleFinanceEnterpriseOutinvoiceList'
            }
        }
    },
    methods: {

        // 新增、编辑、详情页面隐藏
        hidePage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },

        handleAdd () {
            let newVar = this.$ls.get('Login_Userinfo')
            this.currentEditRow = {elsAccount: this.$ls.get('Login_elsAccount'), enterpriseName: this.$ls.get('Login_Userinfo')['enterpriseName'], busType: 'sale'}
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        }
    }
}
</script>