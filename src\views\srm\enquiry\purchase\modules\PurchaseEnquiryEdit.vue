<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-05-10 10:43:33
 * @LastEditors: LOK
 * @LastEditTime: 2022-08-22 15:11:17
 * @Description: 采购协同/寻源协同/询价管理-编辑
-->
<template>
  <div class="page-container">
    <edit-layout
      v-if="showEditLayout"
      refresh
      ref="editPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @cell-click="handleCellClick"
      @selectChange="formSelectChange"
    />
    <field-select-modal
      :pageConfigData="pageConfigData"
      ref="fieldSelectModal"
    />
    <!-- 阶梯报价编辑页组件 -->
    <set-ladder-modal
      :current-edit-row="currentEditRow"
      ref="ladderPage"
    />
    <!-- 成本报价编辑页组件 -->
    <purchase-edit-cost
      ref="costform"
      :current-edit-row="costEditRow"
    />
    <!-- 选择成本模板 -->
    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '选择模板')"
      :width="360"
      v-model="visible"
      @ok="selectedTemplate"
    >
      <template slot="footer">
        <a-button
          key="back"
          @click="handleCancel"
          >{{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, ' 取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          @click="selectedTemplate"
          >{{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, ' 确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, ' 请选择模板')"
      />
    </a-modal>
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @success="getConfigData"
      @error="loadError"
    />

    <ItemImportExcel
      ref="itemImportExcel"
      @importCallBack="importCallBack"
    />
  </div>
</template>
<script lang="jsx">
import { EditMixin } from '@comp/template/edit/EditMixin'
import moment from 'moment'
import { downFile, getAction, postAction } from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import SetLadderModal from '../../modules/SetLadderModal'
import PurchaseEditCost from './PurchaseEditCost'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { ajaxFindDictItems } from '@/api/api'
import { handlePromise } from '@/utils/util.js'
import { cloneDeep } from 'lodash'
import dateTrans from '@/utils/dateTransform'
export default {
  name: 'PurchaseEnquiryEdit',
  mixins: [EditMixin],
  components: {
    fieldSelectModal,
    SetLadderModal,
    PurchaseEditCost,
    ItemImportExcel
  },
  data() {
    return {
      selectType: 'material',
      selectRowType: '',
      templateNumber: undefined,
      pageConfigData: {},
      templateOpts: [],
      visible: false,
      costEditRow: {},
      pageData: {
        form: {},
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseEnquiryItemList',
              columns: [],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                  type: 'primary',
                  click: this.addItemEvent
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  click: this.deleteItemEvent
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#`, '匹配价格模板'),
                  click: this.autoLoadQuotePriceWay
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
                  params: this.importParams,
                  click: this.importExcel
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_templeteDownload`, '模板下载'),
                  click: this.downloadTemplate
                },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'),
            groupCode: 'supplierInfo',
            type: 'grid',
            custom: {
              ref: 'enquirySupplierListList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                  field: 'toElsAccount',
                  width: 150
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
                  field: 'supplierCode',
                  width: 150
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'),
                  field: 'supplierName',
                  width: 200
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'),
                  field: 'needCoordination',
                  width: 120,
                  dictCode: 'srmSupplierCoordinationWay',
                  editRender: { name: 'select' },
                  slots: { default: 'renderDictLabel' }
                }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                  type: 'primary',
                  click: this.addSupplierEvent
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuNARdX_c783af0e`, '添加品类供应商'),
                  click: this.addCodeSupplierEvent
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  click: this.deleteSupplierEvent
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'),
            groupCode: 'fileDemandInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseAttachmentDemandList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                  field: 'fileType',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                  width: 120,
                  dictCode: 'srmFileType',
                  editRender: { name: '$select', options: [] }
                },
                // {
                //     field: 'stageType',
                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'),
                //     width: 120,
                //     dictCode: 'srmEnquiryStageType',
                //     editRender: {name: '$select', options: []}
                // },
                {
                  field: 'stageType',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'),
                  width: 120,
                  dictCode: 'srmEnquiryStageType',
                  editRender: {
                    // enabled: true
                    // name: '$select', options: []
                  },
                  slots: {
                    default: ({ row }) => {
                      let dictcode = 'srmEnquiryStageType'
                      return [row['stageType'] ? this.getDictLabel(row['stageType'], dictcode) : '']
                    },
                    edit: ({ row }) => {
                      const form = this.$refs.editPage.getPageData()
                      // 0 邀请竞价
                      let dictcode = form.enquiryScope === '0' ? 'srmEnquiry3StageType' : 'srmEnquiryStageType'
                      return [
                        <m-select
                          configData={row}
                          getPopupContainer={(triggerNode) => {
                            return triggerNode.parentNode || document.body
                          }}
                          v-model={row.stageType}
                          placeholder={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')}
                          dict-code={dictcode}
                        />
                      ]
                    }
                  }
                },
                {
                  field: 'required',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'),
                  width: 120,
                  cellRender: { name: '$switch', props: { openValue: '1', closeValue: '0' } }
                },
                {
                  field: 'remark',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                  width: 220,
                  editRender: { name: '$input' }
                }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                  type: 'primary',
                  click: this.addFileDemand
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  click: this.deleteFileDemand
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupCode: 'fileInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseAttachmentList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                  field: 'fileType_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                  width: 120
                  // dictCode: 'srmFileType',
                  // editRender: { name: '$select', props: { clearable: true }, options: [] }
                },
                {
                  field: 'fileName',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                  width: 120
                },
                {
                  field: 'uploadTime',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                  width: 180
                },
                {
                  field: 'uploadElsAccount_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                  width: 120
                },
                {
                  field: 'uploadSubAccount_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                  width: 120
                },
                {
                  field: 'sourceNumber',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号'),
                  width: 130
                },
                { field: 'businessType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#`, '业务类型'), width: 130 },
                {
                  field: 'itemNumber',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                  width: 120
                },
                {
                  field: 'materialName',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'),
                  width: 120
                },
                {
                  field: 'grid_opration',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                  width: 120,
                  align: 'center',
                  slots: { default: 'grid_opration' }
                }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                  type: 'upload',
                  dictCode: 'srmFileType',
                  multiple: true,
                  businessType: 'enquiry',
                  attr: this.attrHandle,
                  callBack: this.uploadCallBack
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                  click: this.deleteBatch,
                  showCondition: this.showDeleteBatchBtn,
                  authorityCode: 'enquiry#purchaseEnquiryHead:deleteBatch'
                }
              ],
              showOptColumn: true,
              optColumnList: [
                {
                  type: 'download',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                  clickFn: this.downloadEvent
                },
                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                {
                  type: 'delete',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  clickFn: this.deleteFilesEvent
                }
              ]
            }
          }
        ],
        formFields: [],
        publicBtn: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
            type: 'primary',
            click: this.saveEvent
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
            type: 'primary',
            click: this.publishEvent,
            showCondition: this.showPublishConditionBtn,
            authorityCode: 'enquiry#purchaseEnquiryHead:publish'
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
            type: 'primary',
            click: this.submit,
            showCondition: this.showAuditConditionBtn
          },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
        ]
      },
      url: {
        add: '/enquiry/purchaseEnquiryHead/add',
        edit: '/enquiry/purchaseEnquiryHead/edit',
        detail: '/enquiry/purchaseEnquiryHead/queryById',
        public: '/enquiry/purchaseEnquiryHead/publish',
        isExistFrozenSource: 'supplier/supplierMaster/isExistFrozenSource',
        submit: '/a1bpmn/audit/api/submit', // '/elsUflo/audit/submit',
        autoLoadQuotePriceWay: '/enquiry/purchaseInquiryQuotedConfig/autoLoadQuotePriceWay',
        upload: '/attachment/purchaseAttachment/upload',
        import: '/els/base/excelByConfig/importExcel',
        // downloadTemplate: '/base/excelByConfig/downloadTemplate',
        downloadTemplate: '/base/excelHeader/downloadTemplate',
        materialEnquiry: '/inquiry/searchSource/materialEnquiry',
        listEffectiveMaterialFile: '/attachment/purchaseAttachment/listEffectiveMaterialFile',
        checkEnquirySameMaterial: '/enquiry/purchaseEnquiryHead/checkEnquirySameMaterial'
      },
      ladderSlots: {
        default: ({ row, column }) => {
          const tpl = (
            <div style='position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;'>
              <a-tooltip
                placement='topLeft'
                overlayClassName='tip-overlay-class'
              >
                <template slot='title'>
                  <div>
                    <vxe-table
                      auto-resize
                      border
                      row-id='id'
                      size='mini'
                      data={this.initRowLadderJson(row[column.property])}
                    >
                      <vxe-table-column
                        type='seq'
                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号')}
                        width='80'
                      ></vxe-table-column>
                      <vxe-table-column
                        field='ladder'
                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')}
                        width='140'
                      ></vxe-table-column>
                      <vxe-table-column
                        field='price'
                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价')}
                        width='140'
                      ></vxe-table-column>
                      <vxe-table-column
                        field='netPrice'
                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价')}
                        width='140'
                      ></vxe-table-column>
                    </vxe-table>
                  </div>
                </template>
                {this.defaultRowLadderJson(row[column.property])}
              </a-tooltip>
              <a style='position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 10;'>
                <a-icon
                  type='stock'
                  onClick={() => {
                    this.setLadder && this.setLadder(row)
                  }}
                />
              </a>
            </div>
          )

          if (row && row.quotePriceWay == 1) {
            //     let label =this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notSet`, '未设置')
            //     if(row.ladderPriceJson){
            //         label =this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isSet`, '已设置')
            //     }
            //     return [
            //         <a onClick={() => this.setLadder(row)}>{label}</a>
            //     ]
            return tpl
          } else {
            return ''
          }
        }
      },
      costSlots: {
        default: ({ row }) => {
          if (row && row.quotePriceWay == 2) {
            let costClick = []
            let costJson = row.costFormJson ? JSON.parse(row.costFormJson) : {}
            let label = costJson['templateName'] ? costJson['templateName'] : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelect`, '请选择')
            costClick.push(<a onClick={() => this.chooseCost(row)}>{label}</a>)
            if (costJson['templateName']) {
              costClick.push(<a onClick={() => this.openCost(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')} </a>)
            }
            return costClick
          } else {
            return ''
          }
        }
      },
      currentRow: {},
      optionsMap: [],

      isAddSingle: false, // 是否为加单模板
      showEditLayout: false, // 是否显示内容

      contractRowId: null // 點擊合同的row id
    }
  },
  computed: {
    fileSrc() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_enquiry_${templateNumber}_${templateVersion}.js?t=` + time
    }
  },
  created() {
    if (this.currentEditRow?.enquiryType == '1') {
      this.isAddSingle = true
    }
    this.setSinglePageConfig()
    this.getDictData()
  },
  methods: {
    formSelectChange(configData) {
      if (configData.fieldLabel === '报价项') {
        this.setColumnData()
      }
    },

    // 請求接口後格式化列表數據
    async formatTableData(data,ref) {
      this.setColumnData()
      console.log('請求接口後格式化列表數據', data)
      let foldList = []
      if(ref=="purchaseAttachmentList"){
          data.map(item=>{
              if(item.businessType != "purchaseRequest"){
                  foldList.push(item)
              }
          })
          data = foldList
      }
      return new Promise((resolve, reject) => {
        data = data.map((item) => {
          if (item.price === 0 || Number(item.price) > 0) {
            item.price = Number(item.price).toFixed(6)
          }
          if (item.netPrice === 0 || Number(item.netPrice) > 0) {
            item.netPrice = Number(item.netPrice).toFixed(6)
          }
          if (item.quotaTaxAmount === 0 || Number(item.quotaTaxAmount) > 0) {
            item.quotaTaxAmount = Number(item.quotaTaxAmount).toFixed(2)
          }
          if (item.quotaNetAmount === 0 || Number(item.quotaNetAmount) > 0) {
            item.quotaNetAmount = Number(item.quotaNetAmount).toFixed(2)
          }
          return item
        })
        resolve(data)
      })
    },

    // 设置列显示
    setColumnData() {
      let st = setTimeout(() => {
        const form = this.$refs.editPage.getPageData()
        // 0 含税价   1 不含税价
        let itemGrid = this.$refs.editPage.$refs.purchaseEnquiryItemList[0]
        itemGrid.editConfig = {
          trigger:'click',
          mode:'cell',
          beforeEditMethod({row,column}) {
              if(column.field == 'materialSpec' && row.materialSpec && row.fbk17!=='Y'){
                  return false
              }
              return true
          }
        }
        itemGrid.resetColumn(true)
        let columnsList = itemGrid.getColumns()
        columnsList = columnsList.map((column) => {
          if (column.field == 'taxCode' || column.field == 'taxRate') {
            column.visible = form.quoteType == 1 ? false : true
          }
          return column
        })
        itemGrid.loadColumn(columnsList)
      }, 100)
    },

    afterLoadConfig() {
      console.log(!this.currentEditRow.id, !this.isAddSingle, this.pageConfig?.formFields.findIndex((i) => i.fieldName == 'enquiryType' && i.defaultValue == '1') > -1)
      if (!this.currentEditRow.id && !this.isAddSingle && this.pageConfig?.formFields.findIndex((i) => i.fieldName == 'enquiryType' && i.defaultValue == '1') > -1) {
        this.isAddSingle = true
        this.setSinglePageConfig()
      }
    },
    setSinglePageConfig() {
      this.showEditLayout = false
      if (this.isAddSingle) {
        this.$set(this.pageData, 'groups', [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'),
            groupCode: 'supplierInfo',
            type: 'grid',
            custom: {
              ref: 'enquirySupplierListList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                  field: 'toElsAccount',
                  width: 150
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
                  field: 'supplierCode',
                  width: 150
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'),
                  field: 'supplierName',
                  width: 200
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'),
                  field: 'needCoordination',
                  width: 120,
                  dictCode: 'srmSupplierCoordinationWay',
                  slots: { default: 'renderDictLabel' }
                }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                  type: 'primary',
                  click: this.addSupplierEvent
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuNARdX_c783af0e`, '添加品类供应商'),
                  click: this.addCodeSupplierEvent
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  click: this.deleteSupplierEvent
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseEnquiryItemList',
              columns: [],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                  type: 'primary',
                  click: this.addItemEvent
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  click: this.deleteItemEvent
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#`, '匹配价格模板'),
                  click: this.autoLoadQuotePriceWay
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
                  params: this.importParams,
                  click: this.importExcel
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_templeteDownload`, '模板下载'),
                  click: this.downloadTemplate
                },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_matchingContracts`, '匹配合同'),
                  click: this.matchingContract
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'),
            groupCode: 'fileDemandInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseAttachmentDemandList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                  field: 'fileType',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                  width: 120,
                  dictCode: 'srmFileType',
                  editRender: { name: '$select', options: [] }
                },
                // {
                //     field: 'stageType',
                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'),
                //     width: 120,
                //     dictCode: 'srmEnquiryStageType',
                //     editRender: {name: '$select', options: []}
                // },
                {
                  field: 'stageType',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'),
                  width: 120,
                  dictCode: 'srmEnquiryStageType',
                  editRender: {
                    // enabled: true
                    // name: '$select', options: []
                  },
                  slots: {
                    default: ({ row }) => {
                      let dictcode = 'srmEnquiryStageType'
                      return [row['stageType'] ? this.getDictLabel(row['stageType'], dictcode) : '']
                    },
                    edit: ({ row }) => {
                      const form = this.$refs.editPage.getPageData()
                      // 0 邀请竞价
                      let dictcode = form.enquiryScope === '0' ? 'srmEnquiry3StageType' : 'srmEnquiryStageType'
                      return [
                        <m-select
                          configData={row}
                          getPopupContainer={(triggerNode) => {
                            return triggerNode.parentNode || document.body
                          }}
                          v-model={row.stageType}
                          placeholder={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')}
                          dict-code={dictcode}
                        />
                      ]
                    }
                  }
                },
                {
                  field: 'required',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'),
                  width: 120,
                  cellRender: { name: '$switch', props: { openValue: '1', closeValue: '0' } }
                },
                {
                  field: 'remark',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                  width: 220,
                  editRender: { name: '$input' }
                }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                  type: 'primary',
                  click: this.addFileDemand
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  click: this.deleteFileDemand
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupCode: 'fileInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseAttachmentList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                  field: 'fileType_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                  width: 120
                  // dictCode: 'srmFileType',
                  // editRender: { name: '$select', props: { clearable: true }, options: [] }
                },
                {
                  field: 'fileName',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                  width: 120
                },
                {
                  field: 'uploadTime',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                  width: 180
                },
                {
                  field: 'uploadElsAccount_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                  width: 120
                },
                {
                  field: 'uploadSubAccount_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                  width: 120
                },
                {
                  field: 'sourceNumber',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号'),
                  width: 130
                },
                { field: 'businessType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#`, '业务类型'), width: 130 },
                {
                  field: 'itemNumber',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                  width: 120
                },
                {
                  field: 'materialName',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'),
                  width: 120
                },
                {
                  field: 'grid_opration',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                  width: 120,
                  align: 'center',
                  slots: { default: 'grid_opration' }
                }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                  type: 'upload',
                  dictCode: 'srmFileType',
                  multiple: true,
                  businessType: 'enquiry',
                  attr: this.attrHandle,
                  callBack: this.uploadCallBack
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                  click: this.deleteBatch,
                  showCondition: this.showDeleteBatchBtn,
                  authorityCode: 'enquiry#purchaseEnquiryHead:deleteBatch'
                }
              ],
              showOptColumn: true,
              optColumnList: [
                {
                  type: 'download',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                  clickFn: this.downloadEvent
                },
                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                {
                  type: 'delete',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  clickFn: this.deleteFilesEvent
                }
              ]
            }
          }
        ])
      }
      this.showEditLayout = true
    },
    handleCellClick({ row, rowIndex, column, columnIndex }) {
      console.log(row, rowIndex, column, columnIndex)
      if (column.field === 'contractItemNumber') {
        // 获取供应商信息
        let supplierGrid = this.$refs.editPage.$refs.enquirySupplierListList[0]
        let { fullData } = supplierGrid.getTableData()

        let params = {
          toElsAccount: fullData[0].toElsAccount,
          materialNumber: row.materialNumber,
          pageSize: 20,
          pageNo: 1
        }
        let columns = [
          {
            field: 'contractNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '合同编号'),
            width: 150
          },
          {
            field: 'itemNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '合同行号'),
            width: 150
          },
          // 物料名称，物料编码，价格，数量
          {
            field: 'materialName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
            width: 150
          },
          {
            field: 'materialNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'),
            width: 150
          },
          {
            field: 'price',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '价格'),
            width: 100
          },
          {
            field: 'quantity',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantity`, '数量'),
            width: 100
          },
          // {
          //   field: 'id',
          //   title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '合同行id'),
          //   width: 150
          // },
          {
            field: 'effectiveDate',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '生效日期'),
            width: 150
          },
          {
            field: 'expiryDate',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '失效日期'),
            width: 150
          }
        ]
        this.selectType = 'contract'
        this.contractRowId = row.id
        this.selectRowType = row._X_ROW_KEY
        this.$refs.fieldSelectModal.open('/contract/purchaseContractHead/getPageBySupplierAccountAndMaterialNumber', params, columns, 'single')
      }
    },
    // 阶梯报价json数据组装
    initRowLadderJson(jsonData) {
      let arr = []
      if (jsonData) {
        arr = JSON.parse(jsonData)
      }
      return arr
    },
    // 阶梯报价默认显示
    defaultRowLadderJson(jsonData) {
      console.log(jsonData)
      let arrString = ''
      if (jsonData) {
        let arr = JSON.parse(jsonData)
        if (Array.isArray(arr)) {
          arr.forEach((item, index) => {
            let ladder = item.ladder
            let price = item.price || ''
            let str = `${ladder},${price}`
            let separator = index === arr.length - 1 ? '' : ';'
            arrString += str + separator
          })
        }
      }
      return arrString
    },
    autoLoadQuotePriceWay() {
      let itemGrid = this.$refs.editPage.$refs.purchaseEnquiryItemList[0]
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }

      let lastList = checkboxRecords
        .filter((item) => item.materialNumber !== '' && item.materialNumber !== undefined && item.materialNumber !== null && item.quotePriceWay !== '' && item.quotePriceWay !== undefined && item.quotePriceWay !== null && item.quotePriceWay !== '0')
        .filter((item) => (item.quotePriceWay === '1' && !item.ladderPriceJson) || (item.quotePriceWay === '2' && !item.costFormJson))

      let params = this.$refs.editPage ? this.$refs.editPage.form : {}

      if (lastList.length === 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '请选择报价方式为成本报价或者阶梯报价、并且报价字段为空的行进行匹配！'))
        return
      }

      let dataArray = lastList.map((item) => {
        let dto = {
          company: params.company || '',
          purchaseOrg: params.purchaseOrg || '',
          factory: item.factory || '',
          materialNumber: item.materialNumber || '',
          materialGroup: item.materialGroup || '',
          cateCode: item.cateCode || '',
          quotePriceWay: item.quotePriceWay || ''
        }
        return dto
      })
      const that = this
      postAction(that.url.autoLoadQuotePriceWay, dataArray)
        .then((res) => {
          if (res.success) {
            // materialNumber jsonValue
            let resultData = res.result
            let fileGrid = this.$refs.editPage.$refs.purchaseEnquiryItemList[0]
            let fullData = fileGrid.getTableData().fullData
            let flag = false
            fullData.forEach((f) => {
              let value = resultData[f.materialNumber]
              if (!value) {
                return
              }
              flag = true
              let quotePriceWay = f.quotePriceWay
              if (quotePriceWay === '1') {
                f['ladderPriceJson'] = value
              }
              if (quotePriceWay === '2') {
                that.autoSelectedTemplate(f, value)
                // f['costFormJson'] = JSON.stringify(value)
              }
            })
            fileGrid.loadData(fullData)
            if (flag) {
              that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#`, '匹配成功'))
            } else {
              that.$message.error(this.$srmI18n(`${this.$getLangAccount()}#`, '匹配失败'))
            }
          }
        })
        .finally(() => {})
    },
    attrHandle() {
      let cn = ''
      if (this.currentEditRow.enquiryNumber) {
        cn = this.currentEditRow.enquiryNumber
      } else {
        const params = (this.$refs.editPage && this.$refs.editPage.getPageData()) || {}
        cn = params.enquiryNumber || ''
      }
      return {
        sourceNumber: cn,
        actionRoutePath: '/srm/enquiry/purchase/PurchaseEnquiryList,/srm/enquiry/sale/SaleEnquiryList'
      }
    },
    getDictData() {
      let dictCodeArr = [{ code: 'srmEnquiryStageType', dict: 'srmEnquiryStageType' }]
      dictCodeArr.map((item) => {
        let postData = {
          busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
          dictCode: item.dict
        }
        ajaxFindDictItems(postData).then((res) => {
          if (res.success) {
            let options = res.result.map((item2) => {
              return {
                value: item2.value,
                label: item2.text,
                title: item2.title
              }
            })
            this.optionsMap[item.code] = options
          }
        })
      })
    },
    // 通过value显示label
    getDictLabel(value, dict) {
      let currentValueArr = value.split(',') || []
      if (dict) {
        let dictItem = this.optionsMap[dict]
          .filter((opt) => {
            return currentValueArr.includes(opt.value)
          })
          .map((item) => item.label)
        return dictItem.length ? dictItem.join('；') : currentValueArr[0]
      } else {
        return value
      }
    },
    preViewEvent(row) {
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    getConfigData() {
      this.pageConfigData = getPageConfig() // eslint-disable-line
    },
    beforeHandleData(data) {
      data.itemColumns.forEach((item) => {
        if (item.field == 'ladderPriceJson') {
          item.slots = this.ladderSlots
        }
        if (item.field == 'costFormJson') {
          item.slots = this.costSlots
        }
        //if(item.field == 'materialNumber') {
        //  item.sortable = true
        //}
      })
    },
    afterHandleData(data) {
      console.log('beforeHandleData', data)
      for (let i of data.groups) {
        if (i.groupCode == 'baseForm') {
          for (let formField of i.custom.formFields) {
            if (formField.fieldName == 'quoteEndTime') {
              formField.fieldType = 'currentDate'
              break
            }
          }
          break
        }
      }
    },
    showDeleteBatchBtn() {
      return this.$hasOptAuth('enquiry#purchaseEnquiryHead:deleteBatch')
    },
    showPublishConditionBtn() {
      let params = this.$refs.editPage ? this.$refs.editPage.form : {}
      if (params.id && this.$hasOptAuth('enquiry#purchaseEnquiryHead:publish')) {
        return true
      } else {
        return false
      }
    },
    addItemEvent() {
      if (this.isAddSingle) {
        let supplierGrid = this.$refs.editPage.$refs.enquirySupplierListList[0]
        let { tableData } = supplierGrid.getTableData()
        if (tableData.length === 0) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IdhuTWiFnRVR_2ffe38dd`, '先填写供应商信息'))
          return
        }
      }

      this.selectType = 'material'
      const form = this.$refs.editPage.getPageData()
      const { mustMaterialNumber = '1' } = form
      // 若询价范围是定向询价必须先选择采购组织
      if (form.enquiryScope == '2' && !form.purchaseOrg) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IdhuTWiFnRVR_2ffe38dd`, '定向询价需先选择采购组织'))
        return
      }
      if (mustMaterialNumber == '1') {
        let url = '/material/purchaseMaterialHead/list'
        let columns = [
          {
            field: 'cateCode',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
            width: 150
          },
          {
            field: 'cateName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
            width: 150
          },
          {
            field: 'materialNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'),
            width: 150
          },
          {
            field: 'materialDesc',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
            width: 150
          },
          {
            field: 'materialName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
            width: 150
          },
          {
            field: 'materialSpec',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),
            width: 150
          }
        ]
        this.$refs.fieldSelectModal.open(url, { blocDel: '0', freeze: '0' }, columns, 'multiple')
      } else {
        let itemGrid = this.$refs.editPage.$refs.purchaseEnquiryItemList[0]
        let itemData = {}
        this.pageConfig.itemColumns.forEach((item) => {
          if (item.defaultValue) {
            itemData[item.field] = item.defaultValue
          }
        })
        if (form.taxCode) {
          itemData['taxCode'] = form.taxCode
          itemData['taxRate'] = form.taxRate
        }
        itemGrid.insertAt([itemData], -1)
      }
    },
    deleteItemEvent() {
      let itemGrid = this.$refs.editPage.$refs.purchaseEnquiryItemList[0]
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }

      let materialList = checkboxRecords.filter((item) => undefined !== item.materialNumber && '' !== item.materialNumber).map((item) => item.materialNumber)
      itemGrid.removeCheckboxRow()
      // 询价范围是按准入品类的话，更新供应商行表格，并自动带出数据
      this.loadEnquirySupplier()
      if (materialList.length > 0) {
        // 移除存在物料编码匹配的物料文档
        let fileGird = this.$refs.editPage.$refs.purchaseAttachmentList[0]
        let tableData = fileGird.getTableData().fullData
        if (tableData.length === 0) {
          return
        }
        let newData = tableData.filter((item) => !materialList.includes(item.materialNumber))
        fileGird.loadData(newData)
      }
    },
    // 弹窗选择的数据返回
    fieldSelectOk(data) {
      if (this.selectType == 'material') {
        const form = this.$refs.editPage.getPageData()

        let itemGrid = this.$refs.editPage.$refs.purchaseEnquiryItemList[0]
        let { fullData } = itemGrid.getTableData()
        let materialList = fullData.map((item) => {
          return item.materialId
        })
        //过滤已有数据
        let insertData = data //data.filter(item => {return !materialList.includes(item.id)})
        console.log(insertData,'新增数据')
        //清除不带出的值
        let fieldList = ['auditStatus', 'flowId', 'createTime', 'createBy', 'updateTime', 'updateBy', 'sourceType', 'id']
        insertData.forEach((item) => {
          item['materialId'] = item['id']
          fieldList.forEach((field) => {
            item[field] = null
          })
        })
        //设置默认值
        this.pageConfig.itemColumns.forEach((item) => {
          if (item.defaultValue) {
            insertData.forEach((insert) => {
                //增加公用码的赋值
                insert['fbk17'] = insert['fbk30']
              if (!insert[item.field]) {
                insert[item.field] = item.defaultValue
              }
            })
          }
        })
        insertData.forEach((insert) => {
          insert['quantityUnit'] = insert['baseUnit']

          if (form.currency) {
            insert['currency'] = form.currency
          }
          if (form.taxCode) {
            insert['taxCode'] = form.taxCode
            insert['taxRate'] = form.taxRate
          }
          if (form.effectiveDate) {
            insert['effectiveDate'] = form.effectiveDate
          }
          if (form.expiryDate) {
            insert['expiryDate'] = form.expiryDate
          }
        })
        let param = {
          purOrgCode: form.purchaseOrg,
          materialDataVos: data
        }
        postAction(this.url.materialEnquiry, param).then((res) => {
          if (res.success) {
            itemGrid.insertAt(insertData, -1)
          } else {
            this.$confirm({
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLSu_38d85f7b`, '确认添加'),
              content: res.message,
              onOk: function () {
                itemGrid.insertAt(insertData, -1)
              }
            })
          }
        })
        let materialNumberList = insertData.filter((item) => undefined !== item.materialNumber && '' !== item.materialNumber).map((item) => item.materialNumber)
        if (materialNumberList.length > 0 && form.associatedMaterialDocument === '1') {
          const that = this
          postAction(that.url.listEffectiveMaterialFile, materialNumberList)
            .then((res) => {
              if (res.success) {
                let attachmentList = res.result
                // sourceNumber actionRoutePath
                let fileSource = that.attrHandle()
                let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
                attachmentList.forEach((f) => {
                  f['sourceNumber'] = fileSource.sourceNumber
                  f['actionRoutePath'] = fileSource.actionRoutePath
                  f['businessType'] = 'enquiry'
                })
                fileGrid.insertAt(attachmentList, -1)
              }
            })
            .finally(() => {})
        }
        // 询价范围是按准入品类的话，更新供应商行表格，并自动带出数据
        this.loadEnquirySupplier()
        //itemGrid.insertAt(insertData, -1)
      } else if (this.selectType == 'contract') {
        if (data?.length !== 0) {
          let enquiryGrid = this.$refs.editPage.$refs.purchaseEnquiryItemList[0]
          let { fullData } = enquiryGrid.getTableData()
          //let dataIndex = fullData.findIndex((item) => item.id === this.contractRowId)
          let dataIndex = fullData.findIndex((item) => item._X_ROW_KEY === this.selectRowType)
          if (dataIndex !== -1) {
            // 合同编号（contractNumber），合同行号（itemNumber），合同行id（id），生效日期（effectiveDate），失效日期（expiryDate）
            !!data[0].contractNumber && (fullData[dataIndex].contractNumber = data[0].contractNumber)
            !!data[0].itemNumber && (fullData[dataIndex].contractItemNumber = data[0].itemNumber)
            !!data[0].id && (fullData[dataIndex].contractItemId = data[0].id)
            !!data[0].effectiveDate && (fullData[dataIndex].effectiveDate = data[0].effectiveDate)
            !!data[0].expiryDate && (fullData[dataIndex].expiryDate = data[0].expiryDate)
            enquiryGrid.loadData(fullData)
          }
        }
      } else {
        let supplierGrid = this.$refs.editPage.$refs.enquirySupplierListList[0]
        let { fullData } = supplierGrid.getTableData()
        let supplierList = fullData.map((item) => {
          return item.toElsAccount
        })
        // // 过滤已有数据
        // let insertData = data.filter((item) => {
        //   return !supplierList.includes(item.toElsAccount)
        // })
        let insertData = data
        insertData = insertData.map((item) => {
          return {
            toElsAccount: item.toElsAccount,
            supplierCode: item.supplierCode,
            supplierName: item.supplierName,
            needCoordination: item.needCoordination
          }
        })
        if (this.isAddSingle && data.length > 0) {
          supplierGrid.remove()
          this.$refs.editPage.form = {
            ...this.$refs.editPage.form,
            supplierScope: data[0].supplierStatus_dictText,
            supplierScope_back: data[0].supplierStatus
          }
        }
        supplierGrid.insertAt(insertData, -1)
        console.log(this.$refs.editPage.form, data)
      }
    },
    downloadTemplate() {
      const form = this.$refs.editPage.getPageData()
      let params = { id: form.id, templateAccount: form.templateAccount, templateNumber: form.templateNumber, templateVersion: form.templateVersion, handlerName: 'purchaseEnquiryItemExtendExcelRpcServiceImpl', roleCode: 'purchase', excelCode: 'purchaseEnquiryItemExtendExcelRpcServiceImpl', excelTemplateType: 'excel' }
      if (!params.id) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsMWFWKIKIr_de818fdb`, '请先保存数据，再下载模板！'))
        return
      } else {
        downFile(this.url.downloadTemplate, params)
          .then((data) => {
            if (!data) {
              this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
              return
            }
            if (typeof window.navigator.msSaveBlob !== 'undefined') {
              window.navigator.msSaveBlob(new Blob([data]), 'template.xlsx')
            } else {
              let url = window.URL.createObjectURL(new Blob([data]))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_hucuNIr_20a4b03e`, '询价行导入模板') + '.xlsx')
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) //下载完成移除元素
              window.URL.revokeObjectURL(url) //释放掉blob对象
            }
          })
          .finally(() => {
            this.gridLoading = false
          })
      }
    },
    // 匹配合同
    matchingContract() {
      // 获取选择的【询价行信息】
      let enquiryGrid = this.$refs.editPage.$refs.purchaseEnquiryItemList[0]
      console.log(this.$refs.editPage)
      let checkData = enquiryGrid.getCheckboxRecords()
      console.log(checkData)
      if (checkData.length === 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
        return
      }

      // 获取供应商信息
      let supplierGrid = this.$refs.editPage.$refs.enquirySupplierListList[0]
      let { fullData } = supplierGrid.getTableData()

      let params = {
        toElsAccount: fullData[0].toElsAccount,
        materialNumberList: checkData.map((i) => i.materialNumber).join(',')
      }
      getAction('/contract/purchaseContractHead/getBySupplierAccountAndMaterialNumbers', params).then((res) => {
        if (!!res?.result) {
          let keys = Object.keys(res.result)
          // 更新数据
          checkData.forEach((item, index) => {
              if(keys.includes(item.materialNumber)){
                  let resItem = res.result[item.materialNumber]
                  // 合同编号（contractNumber），合同行号（itemNumber），合同行id（id），生效日期（effectiveDate），失效日期（expiryDate）
                  !!resItem.contractNumber && (item.contractNumber = resItem.contractNumber)
                  !!resItem.itemNumber && (item.contractItemNumber = resItem.itemNumber)
                  !!resItem.id && (item.contractItemId = resItem.id)
                  !!resItem.effectiveDate && (item.effectiveDate = resItem.effectiveDate)
                  !!resItem.expiryDate && (item.expiryDate = resItem.expiryDate)
              }
          })

          let { fullData } = enquiryGrid.getTableData()
          fullData.forEach((item) => {
            let checkItem = checkData.find((i) => i.id === item.id)
            if (!!checkItem) item = checkItem
          })
          enquiryGrid.loadData(fullData)
        }
      })
    },
    importExcel() {
      const form = this.$refs.editPage.getPageData()
      let params = { handlerName: 'purchaseEnquiryItemExtendExcelRpcServiceImpl', roleCode: 'purchase', templateAccount: form.templateAccount, templateNumber: form.templateNumber, templateVersion: form.templateVersion, excelCode: 'purchaseEnquiryItemExtendExcelRpcServiceImpl', excelTemplateType: 'excel' }
      if (form.id) {
        params.id = form.id
      }
      this.$refs.itemImportExcel.open(params, '询价行导入模板', 'purchaseBiddingItemList', this.url.downloadTemplate, params)
    },
    importParams() {
      const form = this.$refs.editPage.getPageData()
      let params = { templateAccount: form.templateAccount, templateNumber: form.templateNumber, templateVersion: form.templateVersion, handlerName: 'purchaseEnquiryItemExtendExcelRpcServiceImpl', roleCode: 'purchase', excelCode: 'purchaseEnquiryItemExtendExcelRpcServiceImpl', excelTemplateType: 'excel' }
      if (form.id) {
        params.id = form.id
      }
      return params
    },
    importCallBack(result) {
      if (result.file.status === 'done') {
        let response = result.file.response
        if (response.success) {
          let itemGrid = this.$refs.editPage.$refs.purchaseEnquiryItemList[0]
          let insertData = response.result.dataList
          this.pageConfig.itemColumns.forEach((item) => {
            if (item.defaultValue) {
              insertData.forEach((insert) => {
                  insert['fbk17'] = insert['fbk30']
                if (!insert[item.field]) {
                  insert[item.field] = item.defaultValue
                }
              })
            }
          })
          itemGrid.insertAt(insertData, -1)
          this.setColumnData()
        } else {
          this.$message.warning(response.message)
        }
      }
    },
    addSupplierEvent() {
      this.selectType = 'supplier'
      let url = '/supplier/supplierMaster/list'
      let columns = [
        {
          field: 'toElsAccount',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
          width: 150
        },
        {
          field: 'supplierCode',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
          width: 150
        },
        {
          field: 'supplierName',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '供应商名称'),
          width: 200
        },
        {
          field: 'supplierStatus_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'),
          width: 200
        },
        {
          field: 'supplierClassify',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'),
          width: 200
        },
        {
          field: 'needCoordination_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'),
          width: 200
        }
      ]
      // 获取供应商范围参数
      const form = this.$refs.editPage.getPageData()
      if (form.mustMaterialNumber == '0') form.accessCategoryFilter = '0'
      if (form.mustMaterialNumber == '1' && form.mustMaterialNumber == '2' && (!form.purchaseOrg || form.purchaseOrg == '' || form.purchaseOrg == null)) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '请先选择采购组织'))
        return
      }
      // 供应商冻结功能检查
      if (form.purchaseEnquiryItemList && form.purchaseEnquiryItemList.length > 0) {
        // 设置供应商信息和询价行信息
        const array = new Array()
        const cateCodeArray = new Array()
        form.purchaseEnquiryItemList.forEach((a) => {
          array.push(a.factory + ':' + a.cateCode)
          const cateCode = a.cateCode == null || a.cateCode == '' ? 'all' : a.cateCode
          if (cateCodeArray.indexOf(cateCode) == -1) cateCodeArray.push(cateCode)
        })
        form.purchaseOrgItemList = array.join(',')
        form.accessCategoryList = cateCodeArray.join(',')
      } else {
        form.purchaseOrgItemList = ''
        form.accessCategoryList = ''
      }
      const { supplierScope = '', purchaseOrgItemList = '', accessCategoryFilter = '0', accessCategoryList = '', supplierScope_back } = form
      let queryDeviceSupplierSpecialTypeS = this.handleDeviceApiParam(form)
      this.$refs.fieldSelectModal.open(
        url,
        {
          supplierStatus: this.isAddSingle ? (supplierScope_back !== undefined ? supplierScope_back : supplierScope) : supplierScope,
          frozenFunctionValue: '2',
          purchaseOrgItemList: purchaseOrgItemList,
          accessCategoryFilter: form.enquiryScope == '2' ? '1' : accessCategoryFilter,
          purchaseOrganization: form.purchaseOrg,
          accessCategoryList: accessCategoryList,
          pageFlag: 1,
          queryDeviceSupplierSpecialTypeS: queryDeviceSupplierSpecialTypeS
        },
        columns,
        this.isAddSingle ? 'single' : 'multiple',
        null,
        !this.isAddSingle
      )
    },
    addCodeSupplierEvent() {
      const form = this.$refs.editPage.getPageData()
      if (form.enquiryScope == '2') {
        // 询价范围是按准入品类
        this.loadEnquirySupplier()
      } else {
        if (form.purchaseEnquiryItemList && form.purchaseEnquiryItemList.length > 0) {
          const cateCodeArray = new Array()
          form.purchaseEnquiryItemList.forEach((a) => {
            const cateCode = a.cateCode == null ? '' : a.cateCode
            if (cateCodeArray.indexOf(cateCode) == -1) cateCodeArray.push(cateCode)
          })
          const param = cateCodeArray.join(',')
          let supplierGrid = this.$refs.editPage.$refs.enquirySupplierListList[0]
          getAction('/supplier/supplierMaster/inquiryQueryList', { cateCode: param }).then((res) => {
            const type = res.success ? 'success' : 'error'
            this.$message[type](res.message)
            if (res.success && res.result.length > 0) {
              let { fullData } = supplierGrid.getTableData()
              let supplierList = fullData.map((item) => {
                return item.toElsAccount
              })
              // 过滤已有数据
              let insertData = res.result.filter((item) => {
                return !supplierList.includes(item.toElsAccount)
              })
              insertData = insertData.map((item) => {
                return {
                  toElsAccount: item.toElsAccount,
                  supplierCode: item.supplierCode,
                  supplierName: item.supplierName,
                  needCoordination: item.needCoordination
                }
              })
              supplierGrid.insertAt(insertData, -1)
            }
          })
        }
      }
    },
    deleteSupplierEvent() {
      let supplierGrid = this.$refs.editPage.$refs.enquirySupplierListList[0]
      let checkboxRecords = supplierGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      supplierGrid.removeCheckboxRow()
      if (this.isAddSingle) {
        this.$refs.editPage.form = {
          ...this.$refs.editPage.form,
          supplierScope: '',
          supplierScope_back: ''
        }
      }
    },
    addFileDemand() {
      let demandGrid = this.$refs.editPage.$refs.purchaseAttachmentDemandList[0]
      demandGrid.insertAt({ required: '0' }, -1)
    },
    deleteFileDemand() {
      let demandGrid = this.$refs.editPage.$refs.purchaseAttachmentDemandList[0]
      let checkboxRecords = demandGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      demandGrid.removeCheckboxRow()
    },

    init() {
      // queryDetail方法已经处理了id,可以直接调用
      if (this.currentEditRow) {
        this.$refs.editPage.queryDetail(this.currentEditRow.id, (data) => {
          this.editFormData = data
          let attachmentList = this.editFormData.purchaseAttachmentList || []
          let purchaseRequestItemList = this.editFormData.purchaseEnquiryItemList || []
          let materialMap = purchaseRequestItemList.reduce((acc, obj) => {
            acc[obj.itemNumber] = obj.materialNumber + '_' + obj.materialName
            return acc
          }, {})
          attachmentList.forEach((item) => {
            let number = item.itemNumber
            if (number && materialMap[number] && !item.materialName) {
              item.materialNumber = materialMap[number].split('_')[0]
              item.materialName = materialMap[number].split('_')[1]
            }
          })
        })
      }

      let params = this.$refs.editPage.form
      if (params.enquiryDate == undefined || params.enquiryDate == null || params.enquiryDate == '') {
        params.enquiryDate = dateTrans.dateTrans(new Date(), 'yyyy-mm-dd')
      }
    },
    uploadCallBack(result) {
      let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
      let pageData = this.$refs.editPage.getPageData()
      let purchaseRequestItemList = pageData.purchaseEnquiryItemList || []
      let materialMap = purchaseRequestItemList.reduce((acc, obj) => {
        acc[obj.itemNumber] = obj.materialNumber + '_' + obj.materialName
        return acc
      }, {})
      result.forEach((item) => {
        let number = item.itemNumber
        if (number && materialMap[number] && !item.materialName) {
          item.materialNumber = materialMap[number].split('_')[0]
          item.materialName = materialMap[number].split('_')[1]
        }
      })
      fileGrid.insertAt(result, -1)
    },
    deleteFilesEvent(row) {
      let user = this.$ls.get(USER_ELS_ACCOUNT)
      let subAccount = this.$ls.get('Login_subAccount')
      let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
      //如果删除的数据有和登录人账号不一致的
      if ('purchaseRequest' !== row.businessType && (user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount)) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
        return
      }
      // 关联过来未保存的，肯定为select
      if (row.sourceOptType === 'select') {
        fileGrid.remove(row)
        return
      }
      getAction('/attachment/purchaseAttachment/delete', { id: row.id }).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.remove(row)
      })
    },
    // 批量删除
    deleteBatch() {
      let arr = []
      let delArr = []
      //大B
      let user = this.$ls.get(USER_ELS_ACCOUNT)
      let subAccount = this.$ls.get('Login_subAccount')
      const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
      const checkboxRecords = fileGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      if (checkboxRecords && checkboxRecords.length > 0) {
        checkboxRecords.forEach((row) => {
          //当前登录人等账号于查询的账号 大B
          if (user === row.uploadElsAccount) {
            if (subAccount === row.uploadSubAccount || 'purchaseRequest' === row.businessType) {
              delArr.push(row)
            } else {
              arr.push(row.fileName)
            }
          } else {
            arr.push(row.fileName)
          }
        })
        //如果删除的数据有和登录人账号不一致的
        if (arr && arr.length > 0) {
          let str = arr.join(',')
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') + str + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
          return
        }
      }
      let idList = checkboxRecords
        .filter((n) => undefined === n.sourceOptType || 'select' !== n.sourceOptType)
        .map((item) => {
          return item.id
        })
      if (idList.length === 0) {
        fileGrid.removeCheckboxRow()
        return
      }
      const ids = idList.join(',')
      const params = {
        ids
      }
      getAction('/attachment/purchaseAttachment/deleteBatch', params).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.removeCheckboxRow()
      })
    },
    loadEnquirySupplier() {
      const form = this.$refs.editPage.getPageData()
      if (form.enquiryScope == '2') {
        // 供应商冻结功能检查
        if (form.purchaseEnquiryItemList && form.purchaseEnquiryItemList.length > 0) {
          // 设置供应商信息和询价行信息
          const array = new Array()
          const cateCodeArray = new Array()
          form.purchaseEnquiryItemList.forEach((a) => {
            array.push(a.factory + ':' + a.cateCode)
            const cateCode = a.cateCode == null || a.cateCode == '' ? 'all' : a.cateCode
            if (cateCodeArray.indexOf(cateCode) == -1) cateCodeArray.push(cateCode)
          })
          form.purchaseOrgItemList = array.join(',')
          form.accessCategoryList = cateCodeArray.join(',')
        } else {
          form.purchaseOrgItemList = ''
          form.accessCategoryList = ''
        }
        const { supplierScope = '', purchaseOrgItemList = '', accessCategoryFilter = '1', accessCategoryList = '' } = form
        let queryDeviceSupplierSpecialTypeS = this.handleDeviceApiParam(form)
        let paramData = {
          pageSize: 10,
          pageNo: 1,
          supplierStatus: supplierScope,
          frozenFunctionValue: '2',
          purchaseOrgItemList: purchaseOrgItemList,
          accessCategoryFilter: '1',
          purchaseOrganization: form.purchaseOrg,
          accessCategoryList: accessCategoryList,
          queryDeviceSupplierSpecialTypeS: queryDeviceSupplierSpecialTypeS
        }
        getAction('/supplier/supplierMaster/list', paramData).then((res) => {
          if (res.success) {
            this.$refs.editPage.$refs.enquirySupplierListList[0].loadData(res.result.records)
          }
        })
      }
    },
    saveEvent() {
      this.$refs.editPage.postData()
    },
    publishEvent() {
      const form = cloneDeep(this.$refs.editPage.getPageData())
      if (!!form.supplierScope_back && this.isAddSingle) form.supplierScope = form.supplierScope_back
      if (form.publishAudit == '1') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSubmitApprovalFirst`, '请先提交审批！'))
        return
      }

      if (!this.checkValidate(form)) {
        return
      }

      if(!this.checkSupplierNeedCoordinationNotEmpty(form.enquirySupplierListList)) {
        return 
      }

      // 供应商冻结功能检查
      if (form.purchaseEnquiryItemList && form.purchaseEnquiryItemList.length > 0 && form.enquirySupplierListList && form.enquirySupplierListList.length > 0) {
        // 设置供应商信息和询价行信息
        form['purchaseOrgItemList'] = form.purchaseEnquiryItemList
        form['supplierItemList'] = form.enquirySupplierListList
        postAction(this.url.isExistFrozenSource, form).then((rest) => {
          if (rest.success) {
            // 发布
            this.$refs.editPage.handleSend()
          } else {
            this.$message.warning(rest.message)
          }
        })
      } else {
        // 发布
        this.$refs.editPage.handleSend()
      }
    },
    showAuditConditionBtn() {
      let params = this.$refs.editPage ? this.$refs.editPage.form : {}
      let auditStatus = params.auditStatus
      let audit = params.publishAudit
      if (audit == '1' && (!auditStatus || auditStatus == '0' || auditStatus == '3')) {
        return true
      } else {
        return false
      }
    },
    checkValidate(form) {
      let that = this
      const currentDate = moment().format('YYYY-MM-DD')
      if (form.effectiveDate && !(moment(form.effectiveDate).isSame(currentDate) || moment(form.effectiveDate).isAfter(currentDate))) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umbXBATfUEUAPBAW_dd1edba9`, '价格生效日期需大于等于当前日期！'))
        return
      }
      if (form.effectiveDate && form.expiryDate && !moment(form.effectiveDate).isBefore(form.expiryDate)) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_umKXBATfUumbXBA_4246ea19`, '价格失效日期需大于价格生效日期！'))
        return
      }

      const params = this.$refs.editPage.getPageData()

      if (params.enquiryDate == undefined || params.enquiryDate == null || params.enquiryDate == '') {
        params.enquiryDate = moment().format('YYYY-MM-DD')
      }

      const purchaseEnquiryItemList = params.purchaseEnquiryItemList
      let indexDates = []
      let indexDateOthers = []
      let indexDateCurs = []
      let itemNum = []
      let indexDateTwo = []  //价格生效日期大于等于报价截至日期
      let quoteEndTime = form.quoteEndTime.split(' ')[0]
      purchaseEnquiryItemList.forEach((i, index) => {
        if (!i.effectiveDate || !i.expiryDate) indexDates.push(index + 1)
        if (!(moment(i.effectiveDate).isSame(currentDate) || moment(i.effectiveDate).isAfter(currentDate))) indexDateCurs.push(index + 1)
        if (i.effectiveDate < quoteEndTime) indexDateTwo.push(index+1)
        if (new Date(i.effectiveDate).getTime() >= new Date(i.expiryDate).getTime()) indexDateOthers.push(index + 1)
        if (!i.materialNumber) {
          if (!i.materialName && !i.materialDesc) itemNum.push(index + 1)
        }
      })
      if(that.$TemplateUtil.isDeviceTemplate_enquiry(form) == false) {
        if (indexDateCurs.length > 0) {
          if (indexDateCurs.length > 10) this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')} ${indexDateCurs.slice(0, 10).toString()} ... : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umbXBATfUEUAPBAW_dd1edba9`, '价格需大于等于当前日期！')}`)
          else this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')} ${indexDateCurs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umbXBATfUEUAPBAW_dd1edba9`, '价格生效日期需大于等于当前日期！')}`)
          return
        }
        if(indexDateTwo.length>0){
            if (indexDateTwo.length > 10) this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')} ${indexDateTwo.slice(0, 10).toString()} ... : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umbXBATfUEUAPBAW_dd1edba9`, '价格生效日期应大于等于报价截止日期')}`)
            else this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')} ${indexDateTwo.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umbXBATfUEUAPBAW_dd1edba9`, '价格生效日期应大于等于报价截止日期')}`)
            return
        }
        if (indexDateOthers.length > 0) {
          if (indexDateOthers.length > 10) this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')} ${indexDateOthers.slice(0, 10).toString()} ... : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KXBAdfUbXBA_4902668d`, '失效日期应大于生效日期')}`)
          else this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')} ${indexDateOthers.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KXBAdfUbXBA_4902668d`, '失效日期应大于生效日期')}`)
          return
        }
      }
      if (itemNum.length > 0) {
        let numText = itemNum.toString()
        if (itemNum.length > 10) numText = itemNum.slice(0, 10).toString() + ' ... '
        let itemText = `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')}: ${numText}`
        this.$message.warning(itemText + this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SLAoLVVSMSLRLSNSLMW_7b49c587`, '物料编码为空，请填写物料名称或者物料描述'))
        return
      }

      return true
    },
    checkSupplierNeedCoordinationNotEmpty(enquirySupplierListList) {
      let that = this
      if (enquirySupplierListList && enquirySupplierListList != null) {
        let checkRest = true
        enquirySupplierListList.forEach(item => {
          if (!item.needCoordination || item.needCoordination == null || item.needCoordination == '') {
            checkRest = false
            return
          }
        })
        if (!checkRest) {
          that.$message.error('请选择供应商是否协同')
          return false
        }
      }

      return true
    },
    setPromise() {
      let that = this
      let promise = this.pageData.groups.map((group) => {
        if (group.type === 'grid') {
          return that.$refs.editPage.$refs[group.custom.ref][0].validate(true)
        } else {
          return that.$refs.editPage.$refs[group.groupCode][0].validate()
        }
      })
      return promise
    },
    submitPromise() {
      let that = this 

      const form = this.$refs.editPage.getPageData()
      if (!form.id) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
        return
      }
      if (form.publishAudit == '0') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noNeedSubmitApproval`, '无需提交审批！'))
        return
      }
      if (!this.checkValidate(form)) return

      let url = this.url.edit
      this.$refs.editPage.confirmLoading = true
      postAction(url, form)
        .then(async (res) => {
          this.$refs.editPage.confirmLoading = false
          if (res.success) {
            const _this = this
            const fn = (url, param, vm) => {
              console.log('vm :>> ', vm) // 编辑模板组件实例
              _this.$refs.editPage.confirmLoading = true
              postAction(url, param)
                .then((res) => {
                  const type = res.success ? 'success' : 'error'
                  _this.$message[type](res.message)
                  if (res.success) {
                    _this.$parent.submitCallBack(form)
                  }
                })
                .finally(() => {
                  _this.$refs.editPage.confirmLoading = false
                })
            }

            let defaultAduitSubject = `询价发布审批，单号：${form.enquiryNumber}`
            let auditSubject = await that.$FlowUtil.convertFlowTitle(form, defaultAduitSubject)

            const param = {
              businessId: form.id,
              businessType: 'publishEnquiry',
              auditSubject: auditSubject,
              params: JSON.stringify(form)
            }
            this.$confirm({
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitForApproval`, '提交审批'),
              content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApprovalTips`, '是否确认提交审批'),
              onOk() {
                _this.$refs.editPage.handValidate(_this.url.submit, param, fn)
              },
              onCancel() {
                _this.init()
              }
            })
          } else {
            const msgType = res.success ? 'success' : 'error'
            this.$message[msgType](res.message)
          }
        })
        .finally(() => {
          this.$refs.editPage.confirmLoading = false
        })
    },
    submit() {
      const formData = cloneDeep(this.$refs.editPage.getPageData())
      if(!this.checkSupplierNeedCoordinationNotEmpty(formData.enquirySupplierListList)) {
        return 
      }

      let promise = this.setPromise()
      Promise.all(handlePromise(promise))
        .then((result) => {
          let flag = false
          for (let i = 0; i < result.length; i++) {
            if (result[i].status === 'success') {
              flag = true
            } else {
              this.currentStep = i
              return
            }
          }
          if (flag) {
            // 供应商冻结功能检查
            const form = cloneDeep(this.$refs.editPage.getPageData())
            if (!!form.supplierScope_back && this.isAddSingle) form.supplierScope = form.supplierScope_back
            if (form.purchaseEnquiryItemList && form.purchaseEnquiryItemList.length > 0 && form.enquirySupplierListList && form.enquirySupplierListList.length > 0) {
              // 设置供应商信息和询价行信息
              form['purchaseOrgItemList'] = form.purchaseEnquiryItemList
              form['supplierItemList'] = form.enquirySupplierListList
              postAction(this.url.isExistFrozenSource, form).then((rest) => {
                if (rest.success) {
                  postAction(this.url.checkEnquirySameMaterial, form.purchaseEnquiryItemList).then((rest) => {
                    if (rest.success) {
                      // 提交
                      this.submitPromise()
                    } else {
                      this.$message.warning(rest.message)
                    }
                  })
                } else {
                  this.$message.warning(rest.message)
                }
              })
            } else {
              postAction(this.url.checkEnquirySameMaterial, form.purchaseEnquiryItemList).then((rest) => {
                if (rest.success) {
                  // 提交
                  this.submitPromise()
                } else {
                  this.$message.warning(rest.message)
                }
              })
            }
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    setLadder(row) {
      this.currentRow = row
      this.$refs.ladderPage.open(row)
    },
    setLadderCallBack(itemList) {
      this.currentRow['ladderPriceJson'] = JSON.stringify(itemList)
    },
    chooseCost(row) {
      this.currentRow = row
      let params = { elsAccount: row.elsAccount, businessType: 'costForm' }
      getAction('/template/templateHead/getListByType', params).then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            let options = res.result.map((item) => {
              return {
                value: item.templateNumber,
                title: item.templateName,
                version: item.templateVersion,
                account: item.elsAccount
              }
            })
            this.templateOpts = options
            this.visible = true
          } else {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleCancel() {
      this.visible = false
    },
    selectedTemplate() {
      if (this.templateNumber) {
        const that = this
        let template = this.templateOpts.filter((item) => {
          return item.value == that.templateNumber
        })
        let costJson = {
          templateNumber: this.templateNumber,
          templateName: template[0].title,
          templateVersion: template[0].version,
          busAccount: this.$ls.get('Login_elsAccount'),
          templateAccount: template[0].account
        }
        this.currentRow['costFormJson'] = JSON.stringify(costJson)
      }
      this.visible = false
    },
    autoSelectedTemplate(row, json) {
      this.currentRow = row
      let jObj = JSON.parse(json)
      let costJson = jObj[0]
      costJson.busAccount = this.$ls.get('Login_elsAccount')
      this.currentRow['costFormJson'] = JSON.stringify(costJson)
      this.visible = false
    },
    openCost(row) {
      this.currentRow = row
      let costJson = row.costFormJson ? JSON.parse(row.costFormJson) : {}
      this.costEditRow = costJson
      let data = costJson['data'] || {}
      this.$refs.costform.open(data)
    },
    costCallBack(extendAllData) {
      let costJson = JSON.parse(this.currentRow['costFormJson'])
      let data = costJson['data'] || {}
      const allData = extendAllData.allData || []
      for (let k in allData) {
        data[k] = allData[k]
      }
      //分组信息
      let costGruops = []
      extendAllData.pageConfig.groups.forEach((gruop) => {
        let costGruop = {}
        costGruop['groupCode'] = gruop['groupCode']
        costGruop['groupName'] = gruop['groupName']
        costGruop['groupType'] = gruop['groupType']
        costGruops.push(costGruop)
      })
      costJson['groups'] = costGruops
      //数据
      costJson['data'] = data
      this.currentRow['costFormJson'] = JSON.stringify(costJson)
    },
    // 设备部需求 fbk20: 是否专业分类过滤; fbk2: 供应商专业分类
    handleDeviceApiParam(form) {
      if(!form) {
        return ''
      }
      // 是否设备部询价模板
      if(this.$TemplateUtil.isDeviceTemplate_enquiry(form) == false) {
        return ''
      }
      if(form.fbk2 && form.fbk2 != '' && form.fbk20 && form.fbk20 == '1') {
        return form.fbk2
      }
      return ''
    }
  }
}
</script>
