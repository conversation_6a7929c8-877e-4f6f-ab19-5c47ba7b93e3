<template>
  <div class="els-page-comtainer"> 
    <tabs-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url" 
      @goBack="goBack"/>  
    <select-modal
      ref="smsTemplateList"
      :url="url.selectSmsTemplateList"
      :columns="selectSmsTemplateColumns"
      :title="selectSmsTemplateTitle"
      @ok="selectSmsTemplateOk"/>
  </div>
</template>

<script>
import { editPageMixin } from '@comp/template/tabsCollapse/tabsCollapseMixin'
import { httpAction } from '@/api/manage'
import selectModal from '@comp/selectModal/selectModal'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    name: 'BussSmsConfigHeadModal',    
    mixins: [editPageMixin],
    components: {
        selectModal
    },
    data () {
        return {
            title: srmI18n(`${getLangAccount()}#i18n_field_100100`, '基本信息'),
            confirmLoading: false,
            pageData: {
                dictList: [],              
                panels: [
                    {
                        title: srmI18n(`${getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            form: {
                                bussName: '',
                                bussCode: '',
                                bussOperateCode: '', 
                                bussOperateName: ''
                            },
                            list: [
                                {
                                    type: 'collapse',
                                    title: srmI18n(`${getLangAccount()}#i18n_field_100100`, '基本信息'),
                                    formList: [
                                        {
                                            fieldName: 'bussName',
                                            type: 'input',
                                            label: srmI18n(`${getLangAccount()}#i18n_title_businessModuleName`, '业务模块名称'),
                                            placeholder: srmI18n(`${getLangAccount()}#i18n_title_businessModuleNameMsg`, '请输入业务模块名称')
                                        },
                                        {
                                            fieldName: 'bussCode',
                                            type: 'input',
                                            label: srmI18n(`${getLangAccount()}#i18n_title_businessModuleCode`, '业务模块编码'),
                                            placeholder: srmI18n(`${getLangAccount()}#i18n_title_businessModuleCodeMsg`, '请输入业务模块编码')
                                        },
                                        {
                                            fieldName: 'bussOperateName',
                                            type: 'input',
                                            label: srmI18n(`${getLangAccount()}#i18n_title_bussOperateName`, '业务操作名称'),
                                            placeholder: srmI18n(`${getLangAccount()}#i18n_title_bussOperateNameMsg`, '请输入业务操作名称')
                                        },
                                        {
                                            fieldName: 'bussOperateCode',
                                            type: 'input',
                                            label: srmI18n(`${getLangAccount()}#i18n_title_bussOperateCode`, '业务操作编码'),
                                            placeholder: srmI18n(`${getLangAccount()}#i18n_title_bussOperateCodeMsg`, '请输入业务操作编码')
                                        },
                                        {
                                            fieldName: 'bussLinkUrl',
                                            type: 'input',
                                            label: srmI18n(`${getLangAccount()}#i18n_title_bussLinkUrl`, '业务链接地址'),
                                            placeholder: srmI18n(`${getLangAccount()}#i18n_title_bussLinkUrlMsg`, '请输入业务链接地址')
                                        },
                                        {
                                            type: 'textarea',
                                            label: srmI18n(`${getLangAccount()}#i18n_title_remarkDesc`, '配置说明'),
                                            fieldName: 'remark',
                                            placeholder: srmI18n(`${getLangAccount()}#i18n_title_remarkDescMsg`, '请输入配置说明')
                                        }    
                                    ]
                                }
                            ],
                            validRules: {
                                bussName: [
                                    { required: true, message: srmI18n(`${getLangAccount()}#i18n_title_businessModuleNameMsg`, '请输入业务模块名称!')},
                                    {max: 100, message: srmI18n(`${getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')}
                                ],
                                bussCode: [
                                    { required: true, message: srmI18n(`${getLangAccount()}#i18n_title_businessModuleCodeMsg`, '请输入业务模块编码!')},
                                    {max: 50, message: srmI18n(`${getLangAccount()}#i18n_title_overflow`, '内容长度不能超过50个字符')}
                                ],
                                bussOperateCode: [
                                    { required: true, message: srmI18n(`${getLangAccount()}#i18n_title_bussOperateCodeMsg`, '请输入业务操作编码!') },
                                    {max: 50, message: srmI18n(`${getLangAccount()}#i18n_title_overflow`, '内容长度不能超过50个字符')}
                                ],
                                bussOperateName: [
                                    { required: true, message: srmI18n(`${getLangAccount()}#i18n_title_bussOperateNameMsg`, '请输入业务操作名称!') },
                                    {max: 100, message: srmI18n(`${getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')}
                                ]
                            }
                        }
                    },
                    {
                        title: srmI18n(`${getLangAccount()}#i18n_title_msgTemplateConfig`, '消息模板配置'),
                        content: {
                            type: 'table',
                            ref: 'bussSmsConfigDetailList',
                            columns: [{ 
                                type: 'checkbox', width: 40 
                            },                      
                            {
                                title: srmI18n(`${getLangAccount()}#i18n_field_templateCode`, '模板编码'),
                                field: 'templateCode',
                                width: 240
                            },
                            {
                                title: srmI18n(`${getLangAccount()}#i18n_title_templateTitle`, '模板标题'),
                                field: 'templateTitle',
                                width: 150
                            },     
                            {
                                title: srmI18n(`${getLangAccount()}#i18n_title_templateType`, '模板类型'),
                                field: 'templateType',
                                width: 120,
                                editRender: {name: 'ASelect', dictCode: 'msgType', props: {disabled: true}}

                            },                     
                            {
                                title: srmI18n(`${getLangAccount()}#i18n_title_aInputNumber`, '失败重试次数'),
                                field: 'count',
                                width: 180,
                                editRender: {name: 'AInputNumber'}
                            }
                            ],
                            validRules: {      
                                count: [
                                    { required: true, message: srmI18n(`${getLangAccount()}#i18n_title_aInputNumberMsg`, '请输入失败重试次数!') }
                                ]                          
                            }
                        },
                        button: [
                            {title: srmI18n(`${getLangAccount()}#i18n_title_add`, '新增'), type: 'primary', clickFn: this.addPageRow },
                            {title: srmI18n(`${getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deletePageRow }
                        ]
                    }         
                ]
            },
            url: {
                add: '/message/bussSmsConfigHead/add',
                edit: '/message/bussSmsConfigHead/edit',
                detail: '/message/bussSmsConfigHead/queryDetailById',
                selectSmsTemplateList: '/message/sysMessageTemplate/list'
            },
            selectSmsTemplateColumns: [
                { type: 'checkbox', width: 40, align: 'center'},
                { field: 'templateCode', title: srmI18n(`${getLangAccount()}#i18n_field_templateCode`, '模板编码'), width: 240, align: 'center'},
                { field: 'templateName', title: srmI18n(`${getLangAccount()}#i18n_title_templateName`, '模板名称'), width: 150, align: 'center'},
                { field: 'templateType_dictText', title: srmI18n(`${getLangAccount()}#i18n_title_templateType`, '模板类型'), width: 120, align: 'center'},
                { field: 'templateContent', title: srmI18n(`${getLangAccount()}#i18n_title_templateContent`, '模板内容'), width: 400, align: 'center'}
            ],
            selectSmsTemplateTitle: srmI18n(`${getLangAccount()}#i18n_title_templateSelect`, '模板选择'),
            selectRow: {}
        }
    },
    mounted () {
       
    },
    created (){
      
    },
    methods: {   
        addPageRow (){
            this.$refs.smsTemplateList.open()        
        },
        selectSmsTemplateOk (data){
            let addTableData = []
            let bussSmsConfigDetailListGrid = this.$refs.editPage.$refs.bussSmsConfigDetailList[0]
            let tableData =  bussSmsConfigDetailListGrid.getTableData().fullData
            let idList = tableData.map(item => {
                return item.templateType
            })
            let filterList = data.filter(item => {
                return !idList.includes(item.templateType)
            })
            filterList.forEach((item, i) => {
                addTableData.push({ 
                    templateId: item.id,               
                    templateCode: item.templateCode,
                    templateTitle: item.templateName,
                    templateType: item.templateType,
                    count: 3
                })
            })
            bussSmsConfigDetailListGrid.insertAt(addTableData, -1)
        },

        deletePageRow () {
            this.$refs.editPage.deleteRow()
        },   
        postData (){
            let params = this.$refs.editPage.getParamsData()
            this.confirmLoading = true
            httpAction(this.url.generatorConfig, params, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$emit('ok')
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
       
    }
}
</script>
