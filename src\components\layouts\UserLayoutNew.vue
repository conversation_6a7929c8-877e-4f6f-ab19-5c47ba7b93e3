<template>
  <div
    id="userLayout"
    class="userLayout"
    :class="['user-layout-wrapper', device]"
  >
    <!-- <div class="header">
      <div class="wrapper">
        <slot></slot>
        <router-link
          to="/"
          class="box">
          <img
            class="logo"
            src="~@/assets/img/login/logo.png"
            alt="logo"
          />
          <span class="blue">QQT SRM</span>
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_easyToLearnEasyToUse`, '易学易用') }}</span>
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_interconnection`, '互联互通') }}</span>
        </router-link> 
      </div>
    </div> -->
    <route-view />
  </div>
</template>

<script>
import RouteView from '@/components/layouts/RouteView'
import { mixinDevice } from '@/utils/mixin.js'

export default {
    name: 'UserLayout',
    components: { RouteView },
    mixins: [mixinDevice],
    data () {
        return {}
    },
    mounted () {
        document.body.classList.add('userLayout')
    },
    beforeDestroy () {
        document.body.classList.remove('userLayout')
    }
}
</script>

<style lang="less" scoped>
@blue: #178aff;
@gray: #8c8c8c;

.userLayout {
  .blue {
    color: @blue;
  }
  .wrapper {
    margin: 0 auto;
    width: 1280px;
  }
  .header {
    padding: 8px 0;
    font-size: 16px;
    color: @gray;
    .box {
      display: flex;
      align-items: center;
      color: #c1c1c1;
      font-weight: 300;
      .logo {
        flex: 0 0 40px;
        width: 40px;
        height: 40px;
      }
      span + span {
        margin-left: 16px;
      }
      .blue {
        position: relative;
        margin-left: 8px;
        &::after {
          position: absolute;
          right: -8px;
          top: 4px;
          width: 1px;
          height: 16px;
          background-color: @gray;
          content: "";
        }
      }
    }
  }
}
@media only screen and (max-width: 1280px) {
  .userLayout {
    .wrapper {
      width: 1000px;
    }
  }
}

</style>
