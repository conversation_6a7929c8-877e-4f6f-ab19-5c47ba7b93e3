<template>
  <div>
    <div class="tenderBidTetterVoBox">
      <div
        class="priceOpeningsList" 
      >
        <titleTrtl class="margin-b-10">
          <span>{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_eBxVH_9dbaaa19`, '投标函信息')) }}</span>
          <template
            slot="right"
            v-if="isEdit">
            <a-button
              type="primary"
              size="small"
              v-if="formData.quoteType"
              @click="handleAddTetterVoItem">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VaeBx_c2bd847d`, '新增投标函') }}</a-button>
          </template>
        </titleTrtl>
        <div
          class="tenderBidTetterBox"
          :style="{height: `${tenderBidTetterVoListHeight}px`}"
        >
          <div
            class="tetterVoItem"
            :class="tetterVoModalIndex == i ? 'activeBox': 'normalBox'"
            v-for="(item, i) in tetterVoData"
            :key="i"
            @click="radioChange(item, i)">
            <div class="tetterVoItemLine margin-b-10">
              <span class="flex3 colorName">{{ item.name }}</span>
            </div>
            <div class="tetterVoItemLine">
              <span
                class="flex3"
                :class="item.formatType == '9' ? 'colorFormatTypeNormal': 'colorFormatType'">{{ item.formatType ? formatTypeOptions[item.formatType] : "" }}</span>
            </div>
          </div>
        </div>

      </div>
      <div class="infoRightBox">
        <listInformation
          v-if="isTetterListTable"
          @maintenanceMaterial="maintenanceMaterial"
          ref="listInformation"
          :formData="formData"
          :pageStatus="pageStatus"
          :currentRow="currentRow"
          :materialData="materialData"
        />
        <materialList 
          v-else
          ref="materialList"
          @back="showTetterListTable"
          :formData="formData"
          :pageStatus="pageStatus"
          :currentRow="materialData"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import listInformation from './listInformation'
import materialList from './materialList'
import titleTrtl from '@/views/srm/bidding_new/BiddingHall/components/title-crtl'
import { cloneDeep } from 'lodash'
export default {
    name: 'TenderBidLetterFormatGroupVo',
    props: {
        pageStatus: {
            default: 'detail',
            type: String
        },
        clarifyRow: {
            default () {
                return {}
            },
            type: Object 
        }
    },
    components: {
        listInformation,
        titleTrtl,
        materialList
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        tenderQuotationsType () {
            if (this.clarifyRow.checkType == '0') {
                return 'preTenderFormatType'
            } else {
                if (this.clarifyRow.processType == '1') {
                    return 'resultTenderFormatType '
                }
                return 'tenderFormatType'
            }
        }
    },
    data () {
        return {
            materialData: {},
            currentRow: null,
            formData: {},
            formatTypeOptions: {},
            tetterVoData: [],
            tetterVoModalType: 'add',
            tenderBidTetterVoListHeight: '200',
            tetterVoModalIndex: null,
            isTetterListTable: true,
            quoteColumnList: []
        }
    },
    methods: {
        dealLabel (str) {
            let prefix = ''
            switch (this.clarifyRow.checkType) {
            case '0':
                prefix = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UU_12d39d`, '预审')
                break
            case '1':
                switch (this.clarifyRow.processType) {
                case '0':
                    prefix = ''
                    break
                case '1':
                    switch (this.clarifyRow.currentStep) {
                    case '0':
                        prefix = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nIx_1d83d91`, '第一步')
                        break
                    case '1':
                        prefix = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nxx_1d84e85`, '第二步')
                        break
                    default:
                        prefix = ''
                    }
                    break
                default:
                    prefix = ''
                }
                break
            default:
                prefix = ''
            }
            return `${prefix}${str}`
        },
        // 获取表格下拉字典
        queryDictData (dictCode) {
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: dictCode
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    res.result.map(({value, text, title}) => {
                        this.formatTypeOptions[value] = title
                        return {
                            value,
                            title,
                            label: text
                        }
                    })
                    this.$forceUpdate()
                }
            })
        },
        // 投标函列表选择切换
        radioChange (row, i) {
            this.tetterVoModalIndex = i
            this.materialData = this.tetterVoData[i]
            this.currentRow = row.priceOpeningsList[0]
            this.currentRow['formatType'] = row['formatType']
            this.$refs.listInformation.initColumns(this.currentRow)
            this.isTetterListTable = true
        },
        init ({ tenderBidLetterFormatGroupVo }) {
            let data = tenderBidLetterFormatGroupVo
            this.formData = tenderBidLetterFormatGroupVo[0] || {}
            this.tetterVoData = []
            if (data[0].priceOpeningsList) {
                data.map(item1=>{
                    item1.priceOpeningsList.map((item2) => {
                        item2.customizeFieldData = JSON.parse(item2.customizeFieldData)
                        item2.customizeFieldModel = JSON.parse(item2.customizeFieldModel)
                        item2.name = data.name
                        item2.formatType = data.formatType
                        return item2
                    })
                    this.tetterVoData.push(item1)
                    console.log('this.tetterVoData', this.tetterVoData)
                })
                this.radioChange(this.tetterVoData[0], 0)
            }
        },
        maintenanceMaterial () {
            let quoteColumnList = cloneDeep(this.currentRow.customizeFieldModel).filter((item) => {
                // 从自定义列过滤报价列
                return item.fieldCategory == '1'
            })
            quoteColumnList.map(quoteItem => {
                if (!quoteItem.materialDataList) {
                    quoteItem.materialDataList = []
                    // 如果存在物料行
                    if (this.currentRow.quoteColumnList && this.currentRow.quoteColumnList.length > 0) {
                        // 每个物料列映射对应物料行
                        this.currentRow.quoteColumnList.map(materitItem => {
                            if (materitItem.field == quoteItem.field && materitItem?.materialDataList) quoteItem.materialDataList.push(...materitItem.materialDataList)
                        })
                    }
                }
            })
            this.materialData.quoteColumnList = quoteColumnList
            this.currentRow.quoteColumnList = quoteColumnList
            this.isTetterListTable = false
        },
        showTetterListTable () {
            this.isTetterListTable = true
        }
    },
    created () {
        this.queryDictData(this.tenderQuotationsType)
        this.tenderBidTetterVoListHeight = document.documentElement.clientHeight - 400
        this.currentRow = this.clarifyRow || {}
    }
}
</script>
<style lang="less" scoped>
.fl{
    float: left;
}
.fr{
    float: right;
}
.clearFix{
    clear: both;
}
.margin-b-10{
    margin-bottom: 10px;
}
.tenderBidTetterVoBox{
    display: flex;
}
.priceOpeningsList{
    .tenderBidTetterBox{
        overflow: auto;
    }
    min-width: 200px;
    padding: 5px;
    border: 1px solid #e8e8e8;
    flex: 17%;
    margin-right: 10px;
}
.infoRightBox{
    flex: 78%;
    padding: 5px;
    border: 1px solid #e8e8e8;
    min-width: 400px;
}
.tetterVoItem{
    padding: 5px 10px;
    cursor: pointer;
    margin-bottom: 10px;
    .tetterVoItemLine{
        display: flex;
    }
    .flex1{
        flex: 1;
        height: 21px;
        :deep(svg){
            height: 21px;
        }
    }
    .flex3{
        flex: 5;
        overflow: hidden;
    }
}
.normalBox{
    border: 1px solid #ccc;
}
.activeBox {
    border: 1px solid #1890ff;
}
.colorName{
    color: #1890ff;
}
.colorFormatType{
    color: #e68824;
}
.colorFormatTypeNormal{
    color: #ccc;
}
</style>
