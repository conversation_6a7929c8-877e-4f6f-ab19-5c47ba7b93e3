<template>
  <div class="iconModel">
    <span>
      <a-input
        v-model="selectValue"
        allowClear
        :disabled="disabled"
        @click="selectIcons"
      >
        <a-icon
          v-if="selectValue"
          slot="addonAfter"
          :type="selectValue"
        />
      </a-input>
    </span>
    <icons  
      @choose="handleIconChoose"
      @close="handleIconCancel"
      :icon-choose-visible="iconChooseVisible"
    />
  </div>
</template>

<script>
import Icons from '@/views/sys/menu/modules/icon/Icons'

export default {
    name: 'IconModel',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        placeholder: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        },
        value: {
            type: [Number, String],
            default: null
        }
    },
    components: {
        Icons
    },
    watch: {
        value: {
            immediate: true,
            handler (val) {
                this.selectValue = val || ''
            }
        }
    },
    data () {
        return {
            iconChooseVisible: false,
            selectValue: ''
        }
    },
    methods: {
        selectIcons (){
            this.iconChooseVisible = true
        },
        handleIconCancel () {
            this.iconChooseVisible = false
        },
        handleIconChoose (value) {
            this.$emit('change', value)
            this.iconChooseVisible = false
        }
    }
}
</script>
