<template>
  <div class="page-container">
    <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :remoteJsFilePath="remoteJsFilePath"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    <!-- 加载配置文件 -->
    <RelationGraphModal
      v-if="modalVisibleDocket && currentEditRow.documentId"
      :modalVisibleDocket="modalVisibleDocket"
      :id="currentEditRow.documentId"
      :rootId="currentEditRow.id"
      @closeModalDocket="closeModalDocket"
      @onNodeClick="clickNode"
    >
    </RelationGraphModal>
      <!-- 驳回弹出框 -->
    <a-modal
          v-model="rejectVisible"
          :okText="okText"
          @ok="handleOk">
          <a-form-model
              ref="rejectForm"
              :rules="rejectRules"
              :model="rejectForm">
              <a-form-model-item
                  :label="this.$srmI18n(`${$getLangAccount()}#i18n_field_rejectReason`, '拒绝原因')"
                  rop="rejectReason" >
                  <a-textarea
                      v-model="rejectForm.rejectReason"
                      :placeholder="this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWNFKjW_b5fa4895`, '请输入拒绝原因')"
                      :auto-size="{ minRows: 3, maxRows: 5 }"
                  />
              </a-form-model-item>
          </a-form-model>
      </a-modal>
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction, getAction } from '@/api/manage'
import RelationGraphModal from '@comp/RelationGraphModal'

export default {
    name: 'PurchaseAddCostDetail',
    mixins: [businessUtilMixin],
    components: {
        RelationGraphModal,
        BusinessLayout
    },
    data () {
        return {
            modalVisibleDocket: false,
            rejectVisible: false,
            rejectForm: {rejectReason: ''},
            rejectRules: {
                rejectReason: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWNFKjW_b5fa4895`, '请输入拒绝原因'), trigger: 'blur' } ]
            },
            showRemote: false,
            businessRefName: 'businessRef',
                requestData: {
                    detail: {
                        url: '/finance/purchaseAddCost/queryById',
                        args: (that) => {
                            return { id: that.currentEditRow.id }
                        }
                    }
                },
            pageHeaderButtons:[
                { 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tFKm_2766a28a`, '单据联查'),
                    show: this.showDocket,
                    click: this.viewDocket 
                },
                { 
                   title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                   attrs: {
                        type: 'primary'
                        },
                   click: this.confirmEvent,
                   show: this.showcConfirmConditionBtn,
                   authorityCode: 'finance#purchaseAddCost:confirmById'
                },
                { 
                   title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'),
                   attrs: {
                        type: 'danger'
                        },
                   click: this.rejectEvent,
                   show: this.showcRejectConditionBtn,
                   authorityCode: 'finance#purchaseAddCost:rejectById'
                },
                {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                        key: 'goBack'
                }
            ],
            url: {
                detail: '/finance/purchaseAddCost/queryById',
                confirm: '/finance/purchaseAddCost/confirmById',
                reject: '/finance/purchaseAddCost/rejectById'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${elsAccount}/purchase_addCost_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        // 如果是外部的参数，先请求获取模板js必须的参数
        if (this.$route.query && this.$route.query.open  && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.currentEditRow.sourceType = res.result.sourceType
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
        handleBeforeRemoteConfigData(){
            return {
                    groups: [
                        {
                            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                            groupNameI18nKey: '',
                            groupCode: 'purchaseAttachmentList',
                            groupType: 'item',
                            sortOrder: '6',
                            extend: {
                                optColumnList: [
                                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent  },
                                    { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                ]
                            }
                        }
                    ],
                    formFields: [],
                    itemColumns: [
                        { 
                            groupCode: 'purchaseAttachmentList',
                            field: 'fileType_dictText',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                            fieldLabelI18nKey: '',
                            width: 200
                        },
                        { 
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                            fieldLabelI18nKey: '',
                            field: 'fileName',
                            width: 180
                        },
                        { 
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                            fieldLabelI18nKey: '',
                            field: 'uploadTime',
                            width: 120
                        },
                        { 
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                            fieldLabelI18nKey: '',
                            field: 'uploadElsAccount_dictText',
                            width: 120
                        },
                        {
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                            fieldLabelI18nKey: '',
                            field: 'uploadSubAccount_dictText',
                            width: 120
                        },
                        {
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                            fieldLabelI18nKey: '',
                            field: 'grid_opration',
                            width: '100',
                            slots: { default: 'grid_opration' }
                        }
                    ]
                }
        },
        // 单据联查
        clickNode (){
            this.$store.dispatch('SetTabConfirm', false)
            this.modalVisibleDocket = false
        },
        closeModalDocket (){
            this.modalVisibleDocket = false
        },
        // 是否显示单据联查
        showDocket () {
            return this.currentEditRow.documentId
        },
        // 单据联查
        viewDocket () {
            this.modalVisibleDocket = true
        },

        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        preViewEvent (Vue,row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        goBack () {
            this.$emit('hide')
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            this.attachmentDownloadEvent(row)
        },
        confirmEvent () {
            let params = this.getAllData() || {}
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmAddCharges`, '确认附加费用'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmAddCharges`, '是否确认该附加费用?'),
                onOk: function () {
                    postAction(that.url.confirm, {id: params.id, relationId: params.relationId, addCostItemList: params.addCostItemList}).then(res => {
                        if(res.success) {
                            that.form = res.result
                            that.$message.success(res.message)
                            that.goBack()
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },

        handleOk (){
            let data = this.getAllData() || {}
            let that = this
            if(this.opreationType == 'rejectOption'){
                let rejectReason = this.rejectForm.rejectReason
                if(!rejectReason || rejectReason == ''){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWNFKjW_b5fa4895`, '请输入拒绝原因'))
                    return
                }
                const params = {id: data.id, relationId: data.relationId, rejectReason: rejectReason}
                postAction(this.url.reject, params).then((res) => {
                    that.confirmLoading = true
                    if (res && res.success) {
                        this.goBack()
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n__FKLRW_abb50027`, '拒绝成功！'))
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                this.rejectVisible = false
            }
        },


        rejectEvent () {
            this.rejectForm.rejectReason = ''
            this.rejectVisible = true
            this.opreationType = 'rejectOption'
        },
        showcConfirmConditionBtn ({pageData}) {
            let params = pageData||{}
            let confirmStatus = params.confirmStatus
            if ('1' == confirmStatus) {
                return true
            } else {
                // 不可操作
                return false
            }
        },
        showcRejectConditionBtn ({pageData}) {
            let params = pageData||{}
            let confirmStatus = params.confirmStatus
            if ('1' == confirmStatus) {
                return true
            } else {
                // 不可操作
                return false
            }
        }
    }
}
</script>
