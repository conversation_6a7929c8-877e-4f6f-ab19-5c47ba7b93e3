<template>
  <div class="page-container">
    <div :class="isEdit ? 'edit-page' : 'detail-page'">
      <a-spin :spinning="confirmLoading">
        <div
          class="page-header"
          v-if="showHeader"
        >
          <a-row>
            <a-col
              v-if="hasTitle"
              class="desc-col"
              :style="getDefaultColor"
              :span="6"
            >
              <slot name="title-page">
                <span v-if="isEdit">{{ headerTitle || `${$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')}${currentPageName}` }}</span>
                <span v-else>{{ currentPageName }}</span>
              </slot>
            </a-col>
            <a-col
              v-if="pageHeaderButtons && pageHeaderButtons.length"
              class="btn-col"
              :span="18"
            >
              <template v-if="isEdit && displayModel === 'tab'">
                <a-button
                  style="margin-right: 10px"
                  :type="customPageFooterPreBtn.type"
                  v-if="currentStep && displayModel === 'tab'"
                  @click="customPageFooterPreBtn.click"
                  >{{ customPageFooterPreBtn.title }}</a-button
                >
                <a-button
                  :type="customPageFooterNextBtn.type"
                  v-if="currentStep < pageConfig.groups.filter((item) => item.show).length - 1 && displayModel === 'tab'"
                  @click="customPageFooterNextBtn.click"
                  >{{ customPageFooterNextBtn.title }}</a-button
                >
              </template>
              <taskBtn
                v-if="taskInfo.taskId && APPROVAL_PATH_LIST.includes($route.path)"
                :currentEditRow="currentEditRow"
                :resultData="resultData"
                :pageHeaderButtons="pageHeaderButtons"
                v-on="$listeners"
              />
              <business-button
                v-else
                :buttons="pageHeaderButtons"
                :pageConfig="pageConfig"
                :resultData="resultData"
                v-on="$listeners"
              >
              </business-button>
            </a-col>
          </a-row>
        </div>
        <div
          class="page-header"
          v-if="isEdit && displayModel === 'tab'"
        >
          <a-steps
            v-model="currentStep"
            @change="handleStepChange"
            size="small"
          >
            <template v-for="group in pageConfig.groups">
              <a-step
                v-if="group.show"
                :key="group.groupCode"
              >
                <template #title>
                  <a-tooltip :title="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)">
                    {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}
                  </a-tooltip>
                </template>
              </a-step>
            </template>
          </a-steps>
        </div>
        <div
          id="page-content"
          class="page-content"
        >
          <template v-if="displayModel === 'tab'">
            <template v-if="isEdit">
              <div
                v-for="(group, index) in groupFilter"
                :key="group.groupCode"
                v-show="currentStep === index"
              >
                <div v-if="group.show">
                  <slot
                    :name="`${group.groupCode}Tab`"
                    :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                  >
                    <edit-form-layout
                      v-if="group.groupType === 'head' && group.show"
                      :ref="group.groupCode + 'form'"
                      :busAccount="busAccount"
                      :currentStep="currentStep"
                      :currentEditRow="currentEditRow"
                      :group="group"
                      :displayModel="displayModel"
                      :gridConfig="getGridConfig(group)"
                      :pageConfig="pageConfig"
                      v-on="$listeners"
                    />
                    <edit-grid-layout
                      v-if="group.groupType === 'item' && group.show"
                      :ref="group.groupCode + 'grid'"
                      :busAccount="busAccount"
                      :resultData="resultData"
                      :currentStep="currentStep"
                      :currentEditRow="currentEditRow"
                      :gridLoading="gridLoading"
                      :group="group"
                      :displayModel="displayModel"
                      :gridConfig="getGridConfig(group)"
                      :loadData="group.loadData"
                      :pageConfig="pageConfig"
                      :showGridFooter="showGridFooter"
                      :gridFooterMethod="gridFooterMethod"
                      v-on="$listeners"
                    />
                  </slot>
                  <slot
                    :name="group.groupCode"
                    :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                  ></slot>
                </div>
              </div>
            </template>
            <template v-else>
              <a-tabs
                @change="
                  (tabCode) => {
                    this.$emit('handleTabChange', tabCode)
                  }
                "
              >
                <template v-for="group in pageConfig.groups">
                  <a-tab-pane
                    v-if="group.show"
                    :key="group.groupCode"
                    forceRender
                  >
                    <div
                      slot="tab"
                      class="tabStyle"
                    >
                      <span>{{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}</span>
                      <span
                        v-if="group.hasStar"
                        class="star"
                        >*</span
                      >
                    </div>
                    <slot
                      :name="`${group.groupCode}Tab`"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                    >
                      <detail-form-layout
                        v-if="group.groupType === 'head' && group.show"
                        :ref="group.groupCode + 'form'"
                        :currentEditRow="currentEditRow"
                        :busAccount="busAccount"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :pageConfig="pageConfig"
                      >
                        <template #customRender="slotProps">
                          <slot
                            name="customRender"
                            :slotProps="slotProps"
                          />
                        </template>
                      </detail-form-layout>
                      <detail-grid-layout
                        v-if="group.groupType === 'item' && group.show"
                        :ref="group.groupCode + 'grid'"
                        :currentEditRow="currentEditRow"
                        :resultData="resultData"
                        :pageConfig="pageConfig"
                        :busAccount="busAccount"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :loadData="group.loadData"
                        :showGridFooter="showGridFooter"
                        :gridFooterMethod="gridFooterMethod"
                        :rowConfig="rowConfig"
                      />
                    </slot>
                    <slot
                      :name="group.groupCode"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                    ></slot>
                  </a-tab-pane>
                </template>
              </a-tabs>
            </template>
          </template>
          <template v-if="displayModel === 'collapse'">
            <template v-if="isEdit">
              <a-collapse :default-active-key="pageConfig.groups[0].groupCode">
                <template v-for="group in pageConfig.groups">
                  <a-collapse-panel
                    v-if="group.show"
                    forceRender
                    :key="group.groupCode"
                    :header="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)"
                  >
                    <slot
                      :name="`${group.groupCode}Tab`"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                    >
                      <edit-form-layout
                        v-if="group.groupType === 'head'"
                        :ref="group.groupCode + 'form'"
                        :busAccount="busAccount"
                        :currentStep="currentStep"
                        :currentEditRow="currentEditRow"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :pageConfig="pageConfig"
                        v-on="$listeners"
                      />
                      <edit-grid-layout
                        v-if="group.groupType === 'item'"
                        :ref="group.groupCode + 'grid'"
                        :busAccount="busAccount"
                        :currentStep="currentStep"
                        :currentEditRow="currentEditRow"
                        :resultData="resultData"
                        :gridLoading="gridLoading"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :loadData="group.loadData"
                        :pageConfig="pageConfig"
                        :showGridFooter="showGridFooter"
                        :gridFooterMethod="gridFooterMethod"
                        v-on="$listeners"
                      />
                    </slot>
                    <slot
                      :name="group.groupCode"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, pageConfig, resultData, loadData: group.loadData }"
                    />
                  </a-collapse-panel>
                </template>
              </a-collapse>
            </template>
            <template v-else>
              <a-collapse :default-active-key="pageTitle ? pageConfig.groups.map((i) => i.groupCode) : pageConfig.groups[0].groupCode">
                <template v-for="group in pageConfig.groups">
                  <a-collapse-panel
                    forceRender
                    v-if="group.show"
                    :key="group.groupCode"
                  >
                    <div
                      slot="header"
                      class="tabStyle"
                    >
                      <span>{{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}</span>
                      <span
                        v-if="group.hasStar"
                        class="star"
                        >*</span
                      >
                    </div>
                    <slot
                      :name="`${group.groupCode}Tab`"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                    >
                      <detail-form-layout
                        v-if="group.groupType === 'head'"
                        :ref="group.groupCode + 'form'"
                        :currentEditRow="currentEditRow"
                        :busAccount="busAccount"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :pageConfig="pageConfig"
                      >
                        <template #customRender="slotProps">
                          <slot
                            name="customRender"
                            :slotProps="slotProps"
                          />
                        </template>
                      </detail-form-layout>
                      <detail-grid-layout
                        v-if="group.groupType === 'item'"
                        :ref="group.groupCode + 'grid'"
                        :currentEditRow="currentEditRow"
                        :resultData="resultData"
                        :pageConfig="pageConfig"
                        :busAccount="busAccount"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :loadData="group.loadData"
                        :showGridFooter="showGridFooter"
                        :gridFooterMethod="gridFooterMethod"
                        :rowConfig="rowConfig"
                        @cell-click="cellClickEvent"
                      >
                        <tempalte v-if="!!group.buttons && group.buttons.length > 0">
                          <a-button
                            v-for="(item, index) in group.buttons"
                            :key="index"
                            style="margin-bottom: 10px; margin-right: 10px"
                            :type="item.type || ''"
                            @click="item.click()"
                            >{{ item.title }}</a-button
                          >
                        </tempalte>
                      </detail-grid-layout>
                    </slot>
                    <slot
                      :name="group.groupCode"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, pageConfig, resultData, loadData: group.loadData }"
                    />
                  </a-collapse-panel>
                </template>
              </a-collapse>
            </template>
          </template>
          <template v-if="displayModel === 'unCollapse'">
            <template v-if="isEdit">
              <div
                v-for="group in pageConfig.groups"
                :key="group.groupCode"
              >
                <div
                  class="item-box-title dark"
                  v-if="group.show"
                >
                  {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}
                </div>
                <slot
                  :name="`${group.groupCode}Tab`"
                  :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                >
                  <edit-form-layout
                    v-if="group.groupType === 'head' && group.show"
                    :ref="group.groupCode + 'form'"
                    :busAccount="busAccount"
                    :currentStep="currentStep"
                    :currentEditRow="currentEditRow"
                    :group="group"
                    :displayModel="displayModel"
                    :gridConfig="getGridConfig(group)"
                    :pageConfig="pageConfig"
                    v-on="$listeners"
                  />
                  <edit-grid-layout
                    v-if="group.groupType === 'item' && group.show"
                    :ref="group.groupCode + 'grid'"
                    :busAccount="busAccount"
                    :currentStep="currentStep"
                    :currentEditRow="currentEditRow"
                    :gridLoading="gridLoading"
                    :resultData="resultData"
                    :group="group"
                    :displayModel="displayModel"
                    :gridConfig="getGridConfig(group)"
                    :loadData="group.loadData"
                    :pageConfig="pageConfig"
                    :showGridFooter="showGridFooter"
                    :gridFooterMethod="gridFooterMethod"
                    v-on="$listeners"
                  />
                </slot>
                <slot
                  :name="group.groupCode"
                  :slotProps="{ groups: pageConfig.groups, group, busAccount, pageConfig, resultData, loadData: group.loadData }"
                />
              </div>
            </template>
            <template v-else>
              <div>
                <div
                  v-for="group in pageConfig.groups"
                  :key="group.groupCode"
                  forceRender
                  :tab="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)"
                >
                  <div
                    class="item-box-title dark tabStyle"
                    v-if="group.show"
                  >
                    {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}
                    <span
                      v-if="group.hasStar"
                      class="star"
                      >*</span
                    >
                  </div>
                  <slot
                    :name="`${group.groupCode}Tab`"
                    :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                  >
                    <detail-form-layout
                      v-if="group.groupType === 'head' && group.show"
                      :ref="group.groupCode + 'form'"
                      :currentEditRow="currentEditRow"
                      :busAccount="busAccount"
                      :group="group"
                      :displayModel="displayModel"
                      :gridConfig="getGridConfig(group)"
                      :pageConfig="pageConfig"
                    >
                      <template #customRender="slotProps">
                        <slot
                          name="customRender"
                          :slotProps="slotProps"
                        />
                      </template>
                    </detail-form-layout>
                    <detail-grid-layout
                      v-if="group.groupType === 'item' && group.show"
                      :ref="group.groupCode + 'grid'"
                      :currentEditRow="currentEditRow"
                      :busAccount="busAccount"
                      :resultData="resultData"
                      :pageConfig="pageConfig"
                      :group="group"
                      :displayModel="displayModel"
                      :gridConfig="getGridConfig(group)"
                      :loadData="group.loadData"
                      :rowConfig="rowConfig"
                      :showGridFooter="showGridFooter"
                      :gridFooterMethod="gridFooterMethod"
                    />
                  </slot>
                  <slot
                    :name="group.groupCode"
                    :slotProps="{ groups: pageConfig.groups, group, busAccount, pageConfig, resultData, loadData: group.loadData }"
                  />
                </div>
              </div>
            </template>
          </template>
          <template v-if="displayModel === 'masterSlave'">
            <template v-if="isEdit">
              <a-collapse
                forceRender
                v-if="collapseHeadCode.length"
                :activeKey="localCollapseKeys"
              >
                <template v-for="group in pageConfig.groups">
                  <a-collapse-panel
                    v-if="localCollapseHeadCode.includes(group.groupCode) && group.show"
                    :key="group.groupCode"
                    :header="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)"
                  >
                    <slot
                      :name="`${group.groupCode}Tab`"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                    >
                      <edit-form-layout
                        v-if="group.groupType === 'head' && group.show"
                        :ref="group.groupCode + 'form'"
                        :busAccount="busAccount"
                        :currentStep="currentStep"
                        :currentEditRow="currentEditRow"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :pageConfig="pageConfig"
                        v-on="$listeners"
                      ></edit-form-layout>
                      <edit-grid-layout
                        v-if="group.groupType === 'item' && group.show"
                        :ref="group.groupCode + 'grid'"
                        :busAccount="busAccount"
                        :resultData="resultData"
                        :currentStep="currentStep"
                        :currentEditRow="currentEditRow"
                        :gridLoading="gridLoading"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :loadData="group.loadData"
                        :pageConfig="pageConfig"
                        :showGridFooter="showGridFooter"
                        :gridFooterMethod="gridFooterMethod"
                        v-on="$listeners"
                      >
                      </edit-grid-layout>
                    </slot>
                    <slot
                      :name="group.groupCode"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, pageConfig, resultData, loadData: group.loadData }"
                    />
                  </a-collapse-panel>
                </template>
              </a-collapse>
              <div
                v-else
                v-for="(group, index) in pageConfig.groups"
                :key="group.groupCode"
              >
                <div v-if="index === 0 && group.show">
                  <div class="item-box-title dark">
                    {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}
                  </div>
                  <slot
                    :name="`${group.groupCode}Tab`"
                    :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                  >
                    <edit-form-layout
                      v-if="group.groupType === 'head' && group.show"
                      :ref="group.groupCode + 'form'"
                      :busAccount="busAccount"
                      :currentStep="currentStep"
                      :currentEditRow="currentEditRow"
                      :group="group"
                      :displayModel="displayModel"
                      :gridConfig="getGridConfig(group)"
                      :pageConfig="pageConfig"
                      v-on="$listeners"
                    />
                  </slot>
                  <slot
                    :name="group.groupCode"
                    :slotProps="{ groups: pageConfig.groups, group, busAccount, pageConfig, resultData, loadData: group.loadData }"
                  />
                </div>
              </div>
              <a-tabs>
                <template v-for="(group, index) in pageConfig.groups">
                  <template v-if="group.show">
                    <a-tab-pane
                      v-if="collapseHeadCode.length ? !localCollapseHeadCode.includes(group.groupCode) : index > 0"
                      :key="group.groupCode"
                      :class="group.groupCode"
                      forceRender
                      :tab="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)"
                    >
                      <template>
                        <slot
                          :name="`${group.groupCode}Tab`"
                          :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                        >
                          <edit-form-layout
                            v-if="group.groupType === 'head'"
                            :ref="group.groupCode + 'form'"
                            :busAccount="busAccount"
                            :currentStep="currentStep"
                            :currentEditRow="currentEditRow"
                            :group="group"
                            :displayModel="displayModel"
                            :gridConfig="getGridConfig(group)"
                            :pageConfig="pageConfig"
                            v-on="$listeners"
                          ></edit-form-layout>
                          <edit-grid-layout
                            v-if="group.groupType === 'item'"
                            :ref="group.groupCode + 'grid'"
                            :busAccount="busAccount"
                            :currentStep="currentStep"
                            :resultData="resultData"
                            :currentEditRow="currentEditRow"
                            :gridLoading="gridLoading"
                            :group="group"
                            :displayModel="displayModel"
                            :gridConfig="getGridConfig(group)"
                            :loadData="group.loadData"
                            :pageConfig="pageConfig"
                            :showGridFooter="showGridFooter"
                            :gridFooterMethod="gridFooterMethod"
                            v-on="$listeners"
                          >
                          </edit-grid-layout>
                        </slot>
                        <slot
                          :name="group.groupCode"
                          :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                        />
                      </template>
                    </a-tab-pane>
                  </template>
                </template>
              </a-tabs>
            </template>
            <template v-else>
              <a-collapse
                forceRender
                v-if="collapseHeadCode.length"
                :activeKey="localCollapseKeys"
              >
                <template v-for="group in pageConfig.groups">
                  <a-collapse-panel
                    v-if="localCollapseHeadCode.includes(group.groupCode) && group.show"
                    :key="group.groupCode"
                  >
                    <div
                      slot="header"
                      class="tabStyle"
                    >
                      <span>{{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}</span>
                      <span
                        v-if="group.hasStar"
                        class="star"
                        >*</span
                      >
                    </div>
                    <slot
                      :name="`${group.groupCode}Tab`"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                    >
                      <detail-form-layout
                        v-if="group.groupType === 'head' && group.show"
                        :ref="group.groupCode + 'form'"
                        :currentEditRow="currentEditRow"
                        :busAccount="busAccount"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :pageConfig="pageConfig"
                      >
                        <template #customRender="slotProps">
                          <slot
                            name="customRender"
                            :slotProps="slotProps"
                          />
                        </template>
                      </detail-form-layout>
                      <detail-grid-layout
                        v-if="group.groupType === 'item' && group.show"
                        :ref="group.groupCode + 'grid'"
                        :currentEditRow="currentEditRow"
                        :resultData="resultData"
                        :pageConfig="pageConfig"
                        :busAccount="busAccount"
                        :group="group"
                        :displayModel="displayModel"
                        :gridConfig="getGridConfig(group)"
                        :loadData="group.loadData"
                        :showGridFooter="showGridFooter"
                        :gridFooterMethod="gridFooterMethod"
                        :rowConfig="rowConfig"
                      />
                    </slot>
                    <slot
                      :name="group.groupCode"
                      :slotProps="{ groups: pageConfig.groups, group, busAccount, pageConfig, resultData, loadData: group.loadData }"
                    />
                  </a-collapse-panel>
                </template>
              </a-collapse>
              <div
                v-else
                v-for="(group, index) in pageConfig.groups"
                :key="group.groupCode"
              >
                <div v-if="index === 0 && group.show">
                  <div class="item-box-title dark tabStyle">
                    {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}
                    <span
                      v-if="group.hasStar"
                      class="star"
                      >*</span
                    >
                  </div>
                  <slot
                    :name="`${group.groupCode}Tab`"
                    :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                  >
                    <detail-form-layout
                      v-if="group.groupType === 'head' && group.show"
                      :ref="group.groupCode + 'form'"
                      :currentEditRow="currentEditRow"
                      :busAccount="busAccount"
                      :resultData="resultData"
                      :group="group"
                      :displayModel="displayModel"
                      :gridConfig="getGridConfig(group)"
                      :pageConfig="pageConfig"
                    >
                      <template #customRender="slotProps">
                        <slot
                          name="customRender"
                          :slotProps="slotProps"
                        />
                      </template>
                    </detail-form-layout>
                  </slot>
                  <slot
                    :name="group.groupCode"
                    :slotProps="{ groups: pageConfig.groups, group, busAccount, pageConfig, resultData, loadData: group.loadData }"
                  />
                </div>
              </div>
              <a-tabs
                @change="
                  (tabCode) => {
                    this.$emit('handleTabChange', tabCode)
                  }
                "
              >
                <template v-for="(group, index) in pageConfig.groups">
                  <template v-if="group.show">
                    <a-tab-pane
                      v-if="collapseHeadCode.length ? !localCollapseHeadCode.includes(group.groupCode) : index > 0"
                      :key="group.groupCode"
                      :class="group.groupCode"
                      forceRender
                    >
                      <div
                        slot="tab"
                        class="tabStyle"
                      >
                        <span>{{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}</span>
                        <span
                          v-if="group.hasStar"
                          class="star"
                          >*</span
                        >
                      </div>
                      <slot
                        :name="`${group.groupCode}Tab`"
                        :slotProps="{ groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData }"
                      >
                        <detail-form-layout
                          v-if="group.groupType === 'head'"
                          :ref="group.groupCode + 'form'"
                          :currentEditRow="currentEditRow"
                          :busAccount="busAccount"
                          :group="group"
                          :displayModel="displayModel"
                          :gridConfig="getGridConfig(group)"
                          :pageConfig="pageConfig"
                        >
                          <template #customRender="slotProps">
                            <slot
                              name="customRender"
                              :slotProps="slotProps"
                            />
                          </template>
                        </detail-form-layout>
                        <detail-grid-layout
                          v-if="group.groupType === 'item'"
                          :ref="group.groupCode + 'grid'"
                          :currentEditRow="currentEditRow"
                          :busAccount="busAccount"
                          :resultData="resultData"
                          :pageConfig="pageConfig"
                          :group="group"
                          :displayModel="displayModel"
                          :gridConfig="getGridConfig(group)"
                          :loadData="group.loadData"
                          :showGridFooter="showGridFooter"
                          :gridFooterMethod="gridFooterMethod"
                          :rowConfig="rowConfig"
                        />
                      </slot>
                      <slot
                        :name="group.groupCode"
                        :slotProps="{ groups: pageConfig.groups, group, busAccount, pageConfig, resultData, loadData: group.loadData }"
                      />
                    </a-tab-pane>
                  </template>
                </template>
              </a-tabs>
            </template>
          </template>
        </div>
        <div
          class="page-footer"
          v-if="isEdit && pageConfig.pageFooterButtons && pageConfig.pageFooterButtons.length"
        >
          <a-button
            :type="customPageFooterPreBtn.type"
            v-if="currentStep && displayModel === 'tab'"
            @click="customPageFooterPreBtn.click"
            >{{ customPageFooterPreBtn.title }}</a-button
          >
          <a-button
            :type="customPageFooterNextBtn.type"
            v-if="currentStep < pageConfig.groups.filter((item) => item.show).length - 1 && displayModel === 'tab'"
            @click="customPageFooterNextBtn.click"
            >{{ customPageFooterNextBtn.title }}</a-button
          >

          <business-button
            :buttons="pageFooterButtons"
            :pageConfig="pageConfig"
            :resultData="resultData"
            v-on="$listeners"
          />
        </div>
      </a-spin>
      <!-- 加载配置文件 -->

      <remote-js
        v-if="remoteJsFilePath || moduleName"
        :isLocal="isLocal"
        :role="role"
        :moduleName="moduleName"
        :currentEditRow="currentEditRow"
        :src="remoteJsFileSrc"
        @remote-js-load-success="loadRemoteJsSuccess"
        @remote-js-load-error="loadRemoteJsError"
      >
      </remote-js>
      <GridFoldDrawer
        ref="GridFoldDrawer"
        templateDesc="business"
        :itemColumns="itemColumns"
        :pageStatus="pageStatus"
        :busAccount="busAccount"
      />
    </div>
  </div>
</template>
<script lang="jsx">
import { GOING_LIST_PATH, DONE_LIST_PATH, APPROVAL_PATH_LIST } from '@/utils/const'
import { ajaxFindDictItems } from '@/api/api'
import { getAction, postAction } from '@/api/manage'
import { DEFAULT_COLOR, USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { EditConfig } from '@/plugins/table/gridConfig'
import RemoteJS from '@/components/template/business/remote-js.vue'
import MUpload from '@comp/mUpload'
import LadderPrice from '@comp/LadderPrice'
import rebateLadderPrice from '@comp/rebateLadderPrice'
import MTreeSelect from '@comp/treeSelect/mTreeSelect'
import DetailFormLayout from '@comp/template/business/DetailFormLayout.vue'
import DetailGridLayout from '@comp/template/business/DetailGridLayout.vue'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import EditFormLayout from '@comp/template/business/EditFormLayout.vue'
import CustomUpload from '@comp/template/CustomUpload'
import RichEditorModel from '@comp/richEditorModel/RichEditorModel'
import { cloneDeep, isArray, mergeWith, sortBy } from 'lodash'
import BusinessButton from './components/BusinessButton'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
import { formatFloat, currency, formatCascader } from '@/filters'
import { bindDefaultValue, handlePromise, inputNumberBlurMethod } from '@/utils/util.js'
import { mapGetters } from 'vuex'
import { bindfunctionMiddleware, getObjType } from '@/utils/util'
import { AFTER_CLEAR_CALLBACK, PRIVATE_BIND_FUNCTION, PRIVATE_AFTER_CLEAR_CALLBACK } from '@/utils/constant.js'
import { BatchDownloadBtn } from '@comp/template/business/class/batchDownloadBtn'
import { GRID_OPTION_ROW } from '@/utils/constant.js'
import GridFoldDrawer from '@comp/template/gridFoldDrawer'
import { USER_INFO } from '@/store/mutation-types'
import IconModel from '@comp/IconModel'

export default {
  name: 'BusinessLayout',
  inject: ['tplRootRef'],
  props: {
    // 当前编辑行的数据
    currentEditRow: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    },
    subpackageTitle: {
      type: String,
      default() {
        return ''
      }
    },
    /** 是否拥有title */
    hasTitle: {
      type: Boolean,
      default: true
    },
    // 获取自定义的模板路径
    remoteJsFilePath: {
      type: String,
      default: null
    },
    // 是否使用传入布局模式
    useLocalModelLayout: {
      type: Boolean,
      default: false
    },
    // 配置数据组装前，返回所有的的组装数据{}
    handleBeforeRemoteConfigData: {
      type: Function
    },
    // 配置数据组装后，返回所有的的组装数据{}
    handleAfterRemoteConfigData: {
      type: Function
    },
    // 处理完所有的数据，如有需要，暴露给外部处理最后的数据
    handleAfterDealSource: {
      type: Function
    },
    // 接口请求数据
    requestData: {
      type: Object,
      default() {
        return {
          // url:string,method:get/post,args:function
          detail: {}
          // 模板下载:/base/excelByConfig/downloadTemplate
          // export: {}
          // Excel导入:/base/excelByConfig/importExcel
          // import: {}
        }
      }
    },
    // 不用接口请求外部传入的数据
    fromSourceData: {
      type: Object,
      default() {
        return {}
      }
    },
    // 页面的状态
    pageStatus: {
      type: String,
      default: 'edit'
    },
    // 头部的工具栏 {groupCode: [] }
    externalToolBar: {
      type: Object,
      default() {
        return {}
      }
    },
    // 底部按钮
    pageFooterButtons: {
      type: Array,
      default() {
        return []
      }
    },
    // 详情头部按钮
    pageHeaderButtons: {
      type: Array,
      default() {
        return []
      }
    },
    // 模式布局方式： tab格式：tab，折叠格式：collapse，非折叠格式：unCollapse，单一主从格式：masterSlave
    modelLayout: {
      type: String,
      default: 'tab'
    },
    // modelLayout 是主从masterSlave可折叠的数组 [{code:'baseForm', expand: true}, {code:'eightDisciplinesTeamList', expand: true}]
    // 如果是一维数组折默认打开所有的折叠
    collapseHeadCode: {
      type: Array,
      default() {
        return []
      }
    },
    // 用于控制 是否需要判断新增或编辑接口 开关
    isNeedJudge: {
      type: Boolean,
      default: true
    },
    // 路径传参数
    queryData: {
      type: Object,
      default() {
        return {}
      }
    },
    // 详情页面控制 字段编辑 开关
    needEdit: {
      type: Boolean,
      default: false
    },
    // 用于修改下载地址
    url: {
      type: Object,
      default() {}
    },
    // 本地调试使有
    // 开启后从本地拉取代码
    isLocal: {
      type: Boolean,
      default: false
    },
    role: {
      type: String,
      default: 'purchase'
    },
    // 远程加载模块用名
    moduleName: {
      type: [String, Function]
    },
    // 行配置 { 参数： useKey, keyField, isCurrent, isHover,height .具体看文档}
    rowConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    // 编辑时，头部的title 自定义
    headerTitle: {
      type: String,
      default: ''
    },
    // 表格自定义配置项
    gridCustomConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    // 单个group 配置, 覆盖原配置，如高度等
    singleGroupCoverConfig: {
      type: Function,
      default: null
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    //查看详情页面-表格是否需要编辑
    detailGridIsEdit: {
      type: Boolean,
      default: true
    },
    gridFooterMethod: {
      type: Function,
      default: null
    },
    showGridFooter: {
      type: Boolean,
      default: false
    },
    pageTitle: {
      type: String,
      default: ''
    }
  },
  inheritAttrs: false,
  components: {
    taskBtn,
    BusinessButton,
    MUpload,
    MTreeSelect,
    CustomUpload,
    DetailFormLayout,
    DetailGridLayout,
    EditFormLayout,
    EditGridLayout,
    RichEditorModel,
    rebateLadderPrice,
    'remote-js': RemoteJS,
    LadderPrice,
    IconModel,
    GridFoldDrawer
  },
  data() {
    return {
      GOING_LIST_PATH,
      DONE_LIST_PATH,
      APPROVAL_PATH_LIST,
      resultData: {}, // 明细接口请求数据
      gridLoading: false,
      confirmLoading: false,
      currentStep: 0,
      currentPageName: null,
      // form表单默认配置
      formModelConfig: {
        labelCol: { span: 9 },
        wrapperCol: { span: 15 }
      },
      //默认表格配置
      pageConfig: {
        editLayout: '',
        examineLayout: '',
        groups: [
          {
            groupCode: null, // 分组编码,可自定义,要求对应后端的字段ref
            groupName: null, // 分组显示的名称
            groupType: null, // head头分组-表单, item行分组-表格
            // 表单专有,groupType为head时
            // form表单的model对象
            formModel: {
              id: null // form表单id
            },
            validateRules: null, // form表单的验证规则对象
            // form表单字段数组
            formFields: [
              {
                fieldType: null, // 字段类型
                fieldName: null, // 字段名
                fieldLabel: null, // 字段显示名称
                helpText: null, // 字段显示名称超出提示内容
                disabled: null, // 字段是否可编辑
                placeholder: null, // 字段类型的placeholder
                dictCode: null, // 字段数据字典code，用于请求数据字典数据
                extend: null, // 表单字段扩展，m-tree-select使用，暂时不清楚具体的配置
                sourceMap: null, // 表单字段扩展，m-tree-select使用，暂时不清楚具体的配置
                showEmptyNode: null // 表单字段扩展，m-tree-select使用，暂时不清楚具体的配置
              }
            ],
            // 表格专有的字段
            editConfig: {}, // 表格编辑配置
            editRules: {}, // 表格编辑验证规则
            columns: [], // 表格列
            toolbarButtons: [], // 表格工具栏
            gridOpration: [], // 表格操作栏
            total: { culumnName: null, totalValue: null } // 单个表格汇总
          }
        ],
        pageFooterButtons: [] // 底部按钮
      },
      editFormData: {},
      // 上一步配置
      customPageFooterPreBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_prevStep`, '上一步'), type: 'primary', belong: 'preStep', click: this.prevStep },
      // 下一步配置
      customPageFooterNextBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nextStep`, '下一步'), type: 'primary', belong: 'nextStep', click: this.nextStep },
      localCollapseHeadCode: [], // modelLayout 是主从masterSlave可折叠的数组 ['baseForm', 'eightDisciplinesTeamList']
      localCollapseKeys: [], // 折叠打开数组
      itemColumns: [],
      cascaderDictData: []
    }
  },
  computed: {
    ...mapGetters(['taskInfo']),
    displayModel() {
      let model = ''
      model = this.isEdit ? this.editLayoutModel : this.detailLayoutModel
      if (this.useLocalModelLayout) {
        model = this.modelLayout
      }
      return model
    },
    // 远程模板地址路径
    remoteJsFileSrc() {
      let remoteJsPath = this.remoteJsFilePath
      let time = new Date().getTime()
      // ${this.$variateConfig['configFiles']}/${elsAccount}/purchase_delivery_${templateNumber}_${templateVersion}.js?t=`+time
      return `${this.$variateConfig['configFiles']}/${remoteJsPath}.js?t=` + time
    },
    // 判断页面是编辑态还是详情态
    isEdit() {
      return this.pageStatus === 'edit'
    },
    // busAccount
    busAccount() {
      let account = this.$ls.get(USER_ELS_ACCOUNT)
      if (this.currentEditRow) {
        if (this.currentEditRow.busAccount || this.currentEditRow.elsAccount) {
          account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount
        }
      }
      return account
    },
    // 获取默认主题色
    getDefaultColor() {
      return {
        color: this.$ls.get(DEFAULT_COLOR)
      }
    },
    editLayoutModel() {
      let model = ''
      if (this.pageConfig && this.pageConfig.editLayout) {
        model = this.pageConfig.editLayout
      }
      return model
    },
    detailLayoutModel() {
      let model = ''
      if (this.pageConfig && this.pageConfig.examineLayout) {
        model = this.pageConfig.examineLayout
      }
      return model
    },
    // tab布局数据过滤
    groupFilter() {
      return this.pageConfig.groups.filter((n) => n.show)
    }
  },
  mounted() {
    // 自定义组装数据时通过调用传入的方法组装
    if (!this.remoteJsFilePath && !this.moduleName) {
      this.initRemoteConfigData({})
    }

    // 当前页面的名称
    const approvalTitle = this.currentEditRow && this.currentEditRow.bizType_dictText // 审批详情标题
    // 通用单据单号
    const commonOddNumber = this.currentEditRow && this.currentEditRow.commonOddNumber ? '_' + this.currentEditRow.commonOddNumber : ''
    // 通用标题
    const commonTitle = this.$route.meta ? this.$route.meta.title : ''
    const detailText = this.isEdit ? '' : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_details`, '详情')
    this.currentPageName = this.pageTitle ? this.pageTitle : approvalTitle ? approvalTitle : commonTitle + detailText + commonOddNumber
    this.$store.dispatch('modifyBusAccountLangData', { busAccount: this.busAccount })
  },
  methods: {
    currency,
    formatFloat,
    formatCascader,
    cellClickEvent(info) {
      const { row, rowIndex, column, columnIndex } = info
      console.log(info)
      this.$emit('cell-click', { row, rowIndex, column, columnIndex })
    },
    getGridConfig(group) {
      let config = Object.assign({}, EditConfig, this.gridCustomConfig)
      if (group.groupType === 'item') {
        // 当前表行是否已配置按钮组
        if (!group.externalToolBar || group.externalToolBar.length === 0) {
          delete config.toolbarConfig
        }
      }

      // 计算当前页面最大高度
      const clientHeight = document.documentElement.clientHeight
      let contextHeight = clientHeight - 246
      let oneThird = contextHeight / 3
      let MIN = 334
      if (!!group.groupName && group.groupName.includes('行信息')) oneThird = contextHeight
      if (contextHeight < MIN) contextHeight = MIN
      if (oneThird < MIN) oneThird = MIN

      let height
      if (this.displayModel === 'tab') {
        height = contextHeight
      } else if (this.displayModel === 'collapse') {
        height = oneThird
      } else if (this.displayModel === 'unCollapse') {
        height = oneThird
      } else if (this.displayModel === 'masterSlave') {
        if (oneThird < 442) oneThird = 442
        height = oneThird
      }

      config.height = height

      // 可根据分组返回自定义配置
      if (this.singleGroupCoverConfig && getObjType(this.singleGroupCoverConfig) === 'function') {
        return this.singleGroupCoverConfig(config, group, this.displayModel)
      } else {
        return config
      }
    },
    prevStep() {
      this.currentStep > 0 && this.currentStep--
      this.handleStepChange()
    },
    nextStep() {
      if (this.currentStep < this.pageConfig.groups.filter((item) => item.show).length - 1) {
        this.currentStep++
      }
      this.handleStepChange()
    },
    // 步骤条事件
    handleStepChange() {
      this.$emit('stepChange', this.currentStep)
    },
    // 表行编辑change事件
    changeGridItem(currentRow, currentValue, cb) {
      if (cb && typeof cb === 'function') {
        if (cb.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
          let params = {
            _pageData: this.pageConfig, // 页面所有数据
            _cacheAllData: this.tplRootRef.getAllData(),
            _row: currentRow.row,
            _value: currentValue.value // 当前值
          }
          bindfunctionMiddleware(this.tplRootRef, cb, params)
        } else {
          cb(currentRow.row, currentRow.column, currentValue.value, this.$parent, this.tplRootRef)
        }
      }
    },
    // 表行编辑 select
    changeGridSelectItem(currentRow, currentValue, cb) {
      if (cb && typeof cb === 'function') {
        if (cb.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
          let params = {
            _pageData: this.pageConfig, // 页面所有数据
            _cacheAllData: this.tplRootRef.getAllData(),
            _row: currentRow,
            _value: currentValue // 当前值
          }
          bindfunctionMiddleware(this.tplRootRef, cb, params)
        } else {
          cb(currentRow, {}, currentValue, this.tplRootRef)
        }
      }
    },
    // 表行编辑  Cascader
    changeCascaderValue(currentRow, currentValue, selectedOptions, cb) {
      if (cb && typeof cb === 'function') {
        if (cb.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
          let params = {
            _pageData: this.pageConfig, // 页面所有数据
            _cacheAllData: this.tplRootRef.getAllData(),
            _row: currentRow,
            _value: currentValue, // 当前值
            _selectedOptions: selectedOptions
          }
          bindfunctionMiddleware(this.tplRootRef, cb, params)
        } else {
          cb(currentRow, selectedOptions, currentValue, this.tplRootRef)
        }
      }
    },
    // 表行编辑float小数change事件
    changeGridFloatItem(currentRow, currentColumn, currentValue, cb) {
      if (cb && typeof cb === 'function') {
        if (cb.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
          let params = {
            _pageData: this.pageConfig, // 页面所有数据
            _cacheAllData: this.tplRootRef.getAllData(),
            _row: currentRow,
            _value: currentValue // 当前值
          }
          bindfunctionMiddleware(this.tplRootRef, cb, params)
        } else {
          cb(currentRow, currentColumn, currentValue, this.tplRootRef, this.pageConfig)
        }
      }
    },
    //远程配置加载成功执行
    loadRemoteJsSuccess() {
      let tempConfigData = getPageConfig() // eslint-disable-line
      this.initRemoteConfigData(tempConfigData)
    },
    //远程配置加载异常执行
    loadRemoteJsError(err) {
      this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMIrKmWWVImW_2e550783`, '获取模板失败, 请检查：') + err)
    },
    // 组装远程配置数据前,返回组装前的所有数据
    beforeRemoteConfigData(remoteData) {
      // 混合本地与接口返回配置数据
      let localData = {}
      let configData = remoteData
      if (this.handleBeforeRemoteConfigData && typeof this.handleBeforeRemoteConfigData === 'function') {
        localData = this.handleBeforeRemoteConfigData(remoteData) || {}
        configData = mergeWith(remoteData, cloneDeep(localData), function (objValue, srcValue) {
          if (isArray(objValue)) return objValue.concat(srcValue)
        })
      }
      if (this.handleAfterRemoteConfigData && typeof this.handleAfterRemoteConfigData === 'function') {
        configData = this.handleAfterRemoteConfigData(configData) || configData
      }
      return configData
    },
    // 组装远程的配置数据
    initRemoteConfigData(remoteData) {
      let userInfo = this.$ls.get(USER_INFO)
      let that = this
      let initConfigData = this.beforeRemoteConfigData(remoteData)
      // 组装所有分组
      if (initConfigData && initConfigData.groups && initConfigData.groups.length) {
        initConfigData.groups = sortBy(initConfigData.groups, function (item) {
          return parseInt(item.sortOrder)
        })
        // 兼容旧模板(editlayout，saleEdit等) 过滤表行规则
        initConfigData.groups = initConfigData.groups.filter((groupItem) => {
          if (groupItem.groupType && groupItem.groupType !== 'gridEditConfig') {
            return true
          }
        })
        let sourceGroups = []
        initConfigData.groups.forEach((item) => {
          let group = {}
          group.buttons = item.buttons || null
          group.groupCode = item.groupCode
          group.groupName = item.groupName
          group.groupNameI18nKey = item.groupNameI18nKey
          group.groupType = item.groupType
          group.show = item.show !== false
          group.extend = item.extend || {}
          group.total = { culumnName: null, totalValue: null }
          // 表格工具栏, 如果是页面初始化传过来，全部以页面为主，如果从配置传过来，读取配置项工具栏数据
          if (item.groupType === 'item') {
            let initExternalToolBar = (toolbars) => {
              if (toolbars) {
                // 正常情况key等于各个分组groupCode,当不知道分组时，找不到groupCode,所以统一用all代替，所有的表格顶部按钮一样比如成本报价
                for (let key of Object.keys(toolbars)) {
                  if (key === 'all') {
                    group.externalToolBar = toolbars[key]
                    return false
                  } else {
                    if (key === group.groupCode) {
                      group.externalToolBar = toolbars[key]
                    }
                  }
                }
              }
            }
            if (that.externalToolBar) {
              initExternalToolBar(that.externalToolBar)
            } else {
              if (item.extend && item.extend.externalToolBar) {
                initExternalToolBar(item.extend.externalToolBar)
              }
            }
            // 注入批量下载
            this.initBatchDownloadBtn(group)
          }
          sourceGroups.push(group)
          // console.log('[initRemoteConfigData-sourceGroups]', sourceGroups)
        })
        // 表单配置项组装
        if (initConfigData && initConfigData.formFields && initConfigData.formFields.length) {
          sourceGroups.forEach((groupData) => {
            let formFields = initConfigData.formFields.filter((filterData) => {
              return filterData.groupCode === groupData.groupCode
            })
            let formModel = {}
            let validateRules = {}
            if (groupData.groupType === 'head') {
              // 过滤配置为图片类型的字段，并塞在数组最后面
              let fieldTypeArr = ['hiddenField', 'image', 'textArea']
              let sortArr = formFields.filter((n) => fieldTypeArr.includes(n.fieldType))
              let otherFields = formFields.filter((n) => !fieldTypeArr.includes(n.fieldType))
              let lastFields = []
              let textAreaFields = []
              sortArr.forEach((rs) => {
                if (rs.fieldType == 'textArea') {
                  //隐藏域字段放最后
                  lastFields.push(rs)
                } else if (rs.fieldType == 'image') {
                  lastFields.unshift(rs)
                } else if (rs.fieldType == 'hiddenField') {
                  textAreaFields.push(rs)
                }
              })
              lastFields = lastFields.concat(textAreaFields)
              formFields = otherFields.concat(lastFields)
            }
            formFields.forEach((field) => {
              if (groupData.groupType === 'head' && groupData.groupCode === field.groupCode) {
                // 添加一个hide属性，用于是否隐藏该字段
                field.hide = false
                if (field.fieldType === 'treeSelect') {
                  field.multiple = groupData.multiple || false
                }
                // 默认值
                const defaultValue = bindDefaultValue(field.defaultValue)
                if (!defaultValue && field?.extend?.userInfoConfig?.open && field?.extend?.userInfoConfig?.useFieldName) {
                  // 设置默认值优先
                  const useFieldName = field.extend.userInfoConfig.useFieldName
                  if (typeof useFieldName === 'function' && userInfo) {
                    formModel[field.fieldName] = useFieldName(userInfo)
                  } else {
                    formModel[field.fieldName] = userInfo[useFieldName] ? userInfo[useFieldName] : ''
                  }
                } else {
                  formModel[field.fieldName] = defaultValue
                }
                if (field.required === '1') {
                  let msg = that.$srmI18n(`${that.busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)
                  let ruleRequired = that.$srmI18n(`${that.$getLangAccount()}#i18n_title_required`, '必填')
                  validateRules[field.fieldName] = [{ required: true, message: `${msg}${ruleRequired}!` }]
                }
                if (field.regex) {
                  const prop = field.fieldName
                  validateRules[prop] = validateRules[prop] || []
                  validateRules[prop].push({
                    pattern: field.regex,
                    message: field.alertMsg
                  })
                }
                if (field.fieldValidator) {
                  const prop = field.fieldName
                  if (Array.isArray(validateRules[prop])) {
                    validateRules[prop].push({
                      validator: function (rule, value, callback) {
                        return that.initFieldValidateRule(field, rule, value, callback, field.fieldValidator.validateMethod)
                      },
                      trigger: field.fieldValidator.trigger || 'change'
                    })
                  } else {
                    validateRules[prop] = [
                      {
                        validator: (rule, value, callback) => {
                          return that.initFieldValidateRule(field, rule, value, callback, field.fieldValidator.validateMethod)
                        },
                        trigger: field.fieldValidator.trigger || 'change',
                        required: field.fieldValidator.required || 'false'
                      }
                    ]
                  }
                }
                // 单个表单只有一个总汇总
                if (field.sum === '1') {
                  groupData.total.culumnName = field.fieldName
                }
                if (!field.disabled) {
                  field.disabled = false
                }
              }
            })
            groupData.formModel = Object.assign({}, groupData.formModel, formModel)
            groupData.validateRules = validateRules
            groupData.formFields = formFields
          })
        }
        // 表格列组装
        if (initConfigData && initConfigData.itemColumns && initConfigData.itemColumns.length) {
          sourceGroups.forEach((groupData) => {
            // 拖动列
            let dropColumn = {
              width: 60,
              visible: !!(groupData.extend && groupData.extend.dragAndDrop),

              slots: {
                default: () => {
                  return [
                    <span class='drag-btn'>
                      <i class='vxe-icon--menu'></i>
                    </span>
                  ]
                },
                header: () => {
                  return [
                    <vxe-tooltip
                      content={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pressHoldDragUpDown`, '按住后可以上下拖动排序！')}
                      enterable
                    >
                      <i class='vxe-icon--question'></i>
                    </vxe-tooltip>
                  ]
                }
              }
            }
            let defaultColumns = [dropColumn, { type: 'checkbox', width: this.showGridFooter ? 50 : 40, fixed: 'left' }, { type: 'seq', width: 60, fixed: 'left', title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_seq`, '序号') }]
            groupData.columns = defaultColumns.concat(
              initConfigData.itemColumns.filter((filterData) => {
                return filterData.groupCode == groupData.groupCode
              })
            )
            let rules = {}
            let gridEditConfig = {}
            groupData.columns.forEach((col, index) => {
              // 默认设置slot为空
              // col.slots = Object.assign({}, col.slots)
              if (col.fieldLabelI18nKey) {
                col.title = that.$srmI18n(`${that.busAccount}#${col.fieldLabelI18nKey}`, col.title)
              }
              // if( col.fixType) {//add 新增编辑和详情冻结功能
              //     col.fixed=col.fixType

              //   }
              if (col.fixType) {
                //add 新增编辑和详情冻结功能
                col.fixed = col.fixType == 'unfrozen' ? '' : col.fixType
              } else {
                if (col.fixed == 'unfrozen') delete col.fixed
              }
              if (groupData.groupType === 'item' && groupData.groupCode === col.groupCode) {
                // 单个表格只有一个总汇总
                if (col.sum === '1') {
                  groupData.total.culumnName = col.field
                }
                // 隐藏域处理
                if (col.fieldType == 'hiddenField' || col.fold === '1') {
                  col.visible = false
                }
                // 是否必填
                if (col.required === '1') {
                  // 添加必填列附加样式
                  col.className = 'required-col'
                  col.headerClassName = 'required-col'
                  let msg = that.$srmI18n(`${that.busAccount}#${col.fieldLabelI18nKey}`, col.title)
                  let ruleRequired = that.$srmI18n(`${that.$getLangAccount()}#i18n_title_required`, '必填')
                  rules[col.field] = [{ required: true, message: `${msg}${ruleRequired}!` }]
                }
                // 是否正则表达式验证
                if (col.regex) {
                  const prop = col.field
                  rules[prop] = rules[prop] || []
                  rules[prop].push({
                    pattern: col.regex,
                    message: col.alertMsg
                  })
                }
                // 表格自定义校验
                if (col.fieldValidator) {
                  const prop = col.field
                  rules[prop] = rules[prop] || []
                  rules[prop].push({
                    validator: function ({ rule, cellValue, row, column }) {
                      return that.initFieldValiRowRule(col, rule, cellValue, col.fieldValidator.validateMethod, row, column)
                    },
                    trigger: col.fieldValidator.trigger || 'change'
                  })
                }
                const { showTime = false } = col.extend || {}
                // 编辑态的显示-隐藏有col.slots，配置项与vxe-table的slots的配置参数一样
                switch (col.fieldType) {
                  case 'input':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.editRender = {
                        name: '$input',
                        events: {
                          change: (currentRow, currentValue) => {
                            that.changeGridItem(currentRow, currentValue, col.bindFunction)
                          }
                        }
                      }
                    }
                    break
                  case 'textArea':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.editRender = {
                        name: 'srmTextArea',
                        events: {
                          change: (currentRow, currentValue) => {
                            that.changeGridItem(currentRow, currentValue, col.bindFunction)
                          }
                        }
                      }
                    }
                    break
                  case 'select':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.editRender = {
                        name: 'srmSelect',
                        props: { showSearch: true, disabled: col.disabled },
                        options: that.queryDictData(col),
                        events: {
                          change: (currentRow, currentValue) => {
                            that.changeGridSelectItem(currentRow, currentValue, col.bindFunction)
                          }
                        }
                      }
                    }
                    break
                  case 'multiple':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.editRender = {
                        name: '$select',
                        props: { multiple: true, clearable: true },
                        options: that.queryDictData(col),
                        events: {
                          change: (currentRow, currentValue) => {
                            that.changeGridSelectItem(currentRow, currentValue, col.bindFunction)
                          }
                        }
                      }
                    }
                    break
                  case 'switch':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.cellRender = {
                        name: '$switch',
                        props: { openValue: '1', closeValue: '0', disabled: that.pageStatus !== 'edit' },
                        events: {
                          change: (currentRow, currentValue) => {
                            that.changeGridItem(currentRow, currentValue, col.bindFunction)
                          }
                        }
                      }
                    }
                    break
                  case 'date':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.editRender = {
                        name: 'mDatePicker',
                        props: { valueFormat: col.dataFormat, showTime: showTime },
                        events: {
                          change: (currentRow, currentValue) => {
                            that.changeGridItemOther(currentRow, currentValue, col.bindFunction)
                          }
                        }
                      }
                    }
                    break
                  case 'number':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      // col.editRender = { name: '$input', props: {type: 'number'}, events: {change: (currentRow, currentValue)=> {that.changeGridItem(currentRow, currentValue, col.bindFunction)} } }
                      col.editRender = { enabled: true, autofocus: '.custom-cell-number input' }
                      col.slots = Object.assign({}, col.slots, {
                        default: ({ row, rowIndex, column, columnIndex }, h) => {
                          return [<span>{row[column.property]}</span>]
                        },
                        edit: ({ row, rowIndex, column, columnIndex }, h) => {
                          const props = {
                            type: 'number'
                          }
                          if (col?.extend?.hasOwnProperty('min')) props.min = Number(col.extend.min) == NaN ? -Infinity : Number(col.extend.min)
                          const on = {
                            change: (currentValue) => {
                              that.changeGridFloatItem(row, column, currentValue, col.bindFunction)
                            },
                            blur: () => inputNumberBlurMethod({ value: row[column.property], fieldLabel: column.title, type: 'column', fieldType: col.fieldType, row, column })
                          }
                          return [
                            <a-input-number
                              class='custom-cell-number'
                              vModel={row[column.property]}
                              {...{ props, on }}
                            />
                          ]
                        }
                      })
                    }
                    break
                  case 'float':
                    if (!that.isEdit) {
                      col.slots = Object.assign({}, col.slots, {
                        default: ({ row, column }, h) => {
                          return [<span>{formatFloat(row[column.property], col.dataFormat)}</span>]
                        }
                      })
                    } else {
                      col.editRender = { enabled: true, autofocus: '.custom-cell-float input' }
                      col.slots = Object.assign({}, col.slots, {
                        default: ({ row, column }, h) => {
                          return [<span>{formatFloat(row[column.property], col.dataFormat)}</span>]
                        },
                        edit: ({ row, column }, h) => {
                          const on = {
                            change: (currentValue) => {
                              that.changeGridFloatItem(row, column, currentValue, col.bindFunction)
                            }
                          }
                          return [
                            <a-input-number
                              class='custom-cell-float'
                              vModel={row[column.property]}
                              {...{ on }}
                            />
                          ]
                        }
                      })
                    }
                    break
                  // 货币千分位
                  case 'currency':
                    if (!that.isEdit) {
                      col.slots = Object.assign({}, col.slots, {
                        default: ({ row, column }, h) => {
                          let extend = col.extend || {}
                          let symbol = (extend && extend.symol) || ''
                          let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
                          return [<span>{currency(row[column.property], symbol, decimals)}</span>]
                        }
                      })
                    } else {
                      col.editRender = { enabled: true, autofocus: '.custom-cell-currency input' }
                      col.slots = Object.assign({}, col.slots, {
                        default: ({ row, column }, h) => {
                          let extend = col.extend || {}
                          let symbol = (extend && extend.symol) || ''
                          let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
                          return [<span>{currency(row[column.property], symbol, decimals)}</span>]
                        },
                        edit: ({ row, column }, h) => {
                          const on = {
                            change: (currentValue) => {
                              that.changeGridFloatItem(row, column, currentValue, col.bindFunction)
                            }
                          }
                          return [
                            <a-input-number
                              class='custom-cell-currency'
                              vModel={row[column.property]}
                              {...{ on }}
                            />
                          ]
                        }
                      })
                    }
                    break
                  case 'computed':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.editRender = {
                        name: '$input',
                        props: { type: 'number' },
                        events: {
                          blur: (currentRow) => {
                            setTimeout(() => {
                              that.blurGridItem(currentRow, col, groupData)
                            }, 0)
                          }
                        }
                      }
                    }
                    break
                  case 'remoteSelect':
                    col.slots = this.remoteSelectSlots(col)
                    break
                  case 'selectModal':
                    col.slots = this.selectModalSlots(col)
                    break
                  case 'richEditorModel':
                    col.slots = Object.assign({}, col.slots, { default: 'rich_editor_col_render' })
                    break
                  case 'customSwitch': // 条码单管理模块需求，需要根据业务单据类型有无，进行切换
                    col.slots = this.selectModalSlots(col, 'switch')
                    break
                  case 'fileUpload':
                    col.slots = this.fileUploadModalSlots(col)
                    break
                  case 'ladderPrice':
                    col.slots = this.ladderPriceModalSlots(col)
                    break
                  case 'cascader':
                    that.queryCascaderDictData(col)
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.editRender = { enabled: true }
                      col.slots = Object.assign({}, col.slots, {
                        default: ({ row, rowIndex, column, columnIndex }, h) => {
                          const currentVal = row[column.property]
                          return formatCascader(currentVal, that.cascaderDictData, 'value', 'title')
                        },
                        edit: ({ row, column }, h) => {
                          const props = {
                            // mode: col.dictCode,
                            fieldNames: { label: 'title', value: 'value', children: 'children' },
                            options: this.cascaderDictData
                          }
                          const on = {
                            change: (currentValue, selectedOptions) => {
                              that.changeCascaderValue(row, currentValue, selectedOptions, col.bindFunction)
                            }
                          }
                          return [
                            <m-cascader
                              change-on-select
                              vModel={row[column.property]}
                              {...{ props, on }}
                            />
                          ]
                        }
                      })
                    }
                    break
                  case 'color':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.editRender = { enabled: true }
                      col.slots = Object.assign({}, col.slots, {
                        default: ({ row, column }, h) => {
                          return [<span style={{ color: row[column.property] }}>{row[column.property]}</span>]
                        },
                        edit: ({ row, column }, h) => {
                          const on = {
                            change: (currentRow, currentValue) => {
                              that.changeGridItem(currentRow, currentValue, col.bindFunction)
                            }
                          }
                          return [
                            <div class='field-color-box'>
                              <a-input
                                class='color-input'
                                style={{ color: row[column.property] }}
                                allowClear
                                vModel={row[column.property]}
                                {...{ on }}
                              />
                              <a-input
                                class='color-modal'
                                vModel={row[column.property]}
                                type='color'
                                {...{ on }}
                              />
                            </div>
                          ]
                        }
                      })
                    }
                    break
                  case 'icon':
                    if (!that.isEdit) {
                      col.slots = { default: 'renderDictLabel' }
                    } else {
                      col.slots = Object.assign({}, col.slots, {
                        default: ({ row, column }, h) => {
                          const on = {
                            change: (currentValue) => {
                              that.changeGridItem(row, currentValue, col.bindFunction)
                            }
                          }
                          return [
                            <IconModel
                              vModel={row[column.property]}
                              {...{ on }}
                            />
                          ]
                        }
                      })
                    }
                    break
                }
                if (col.extend && col.extend.linkConfig && !this.isEdit) {
                  col.slots = this.linkModalSlots(col)
                }
                // 自定义渲染
                if (col.slots && !col.slots.header) {
                  // 头部slots
                  col.slots.header = ({ column }) => {
                    const flag = !!col.helpText
                    const dom = flag ? (
                      <vxe-tooltip content={col.helpText}>
                        <span style='display: flex; alignItems: center;'>
                          <span style='marginLeft: 6px;'>{col.title}</span>
                          <i class='vxe-icon--question'></i>
                        </span>
                      </vxe-tooltip>
                    ) : (
                      <span>{col.title}</span>
                    )
                    return [dom]
                  }
                }
              }
            })
            // 增加表行默认操作列
            this._addFoldOptColumn(groupData)
            groupData.editRules = rules
            // 编辑规则
            if (groupData.extend && groupData.extend.editConfig) {
              gridEditConfig.trigger = groupData.extend.editConfig.trigger || 'click'
              gridEditConfig.mode = groupData.extend.editConfig.mode || 'cell'
              gridEditConfig.showStatus = groupData.extend.editConfig.showStatus || true
              if (groupData.extend.editConfig.activeMethod) {
                gridEditConfig.activeMethod = (gridData) => {
                  return that.gridActiveMethod(gridData, groupData.extend.editConfig.activeMethod, groupData)
                }
              }
              groupData.editConfig = gridEditConfig
            } else {
              groupData.editConfig = {
                trigger: 'click',
                mode: 'cell',
                showStatus: true
              }
            }
          })
        }
        if (initConfigData && initConfigData.sortConfig) {
          sourceGroups = sourceGroups.map((i) => {
            i.sortConfig = initConfigData.sortConfig
            return i
          })
        }
        // 分组模板
        that.$set(that.pageConfig, 'groups', sourceGroups)
        // 底部按钮组
        if (that.pageFooterButtons && that.pageFooterButtons.length) {
          that.$set(that.pageConfig, 'pageFooterButtons', that.pageFooterButtons)
        }
        // 头部按钮组
        if (that.pageHeaderButtons && that.pageHeaderButtons.length) {
          that.$set(that.pageConfig, 'pageHeaderButtons', that.pageHeaderButtons)
        }
        // 组装布局
        that.$set(that.pageConfig, 'editLayout', this.useLocalModelLayout ? this.modelLayout : remoteData.editLayout || this.modelLayout)
        that.$set(that.pageConfig, 'examineLayout', this.useLocalModelLayout ? this.modelLayout : remoteData.examineLayout || this.modelLayout)
        // 业务模板角色
        that.$set(that.pageConfig, 'tempRole', initConfigData.tempRole ? initConfigData.tempRole : '')

        this.setGroupFieldNameDictionaryData()

        // 赋值-组装data
        if (that.requestData && that.requestData.detail && that.requestData.detail.url) {
          that.queryDetail()
        } else if (that.fromSourceData && Object.keys(that.fromSourceData).length) {
          that.initMockData(that.fromSourceData)
        }
      }
    },
    /**
         *form表单字段填充自定义验证方法
         * @param {*} _this
         * @param {*} form
         * @param {*} item3
         * @param {*} rule
         * @param {*} value
         * @param {*} callback
         * @param {*} validMethod
         * {
                validateMethod: function (taht, form, currentField, rule, value, callback) {
                    let startTime = new Date(form.checkStartDate).getTime()
                    let endTime = new Date(form.checkEndDate).getTime()
                    if (startTime> endTime) {
                        callback(new Error('开始时间不能大于结束时间'))
                    } else {
                        callback()
                    }
                },
                trigger: 'change'
            } 
         */
    initFieldValidateRule(item, rule, value, callback, validMethod) {
      if (!validMethod) {
        return callback()
      } else {
        return validMethod(this.tplRootRef, this.editFormData, item, rule, value, callback)
      }
    },
    // 表行字段自定义验证方法
    /**
         *
         *
         * @param {*} _this
         * @param {*} item
         * @param {*} rule
         * @param {*} value
         * @param {*} row
         * @param {*} column
         * @param {*} validMethod
         *  {
                validateMethod: function (that, item, rule, value, row, column) {
                    // 编辑态下，that可以获取当前页面所有值
                    if (row.effectiveDate && row.expiryDate) {
                        let effectiveDate = new Date(row.effectiveDate).getTime()
                        let expiryDate = new Date(row.expiryDate).getTime()
                        if (effectiveDate > expiryDate) {
                            return  new Error('生效日期不能大于失效日期')
                        }
                    }
                },
                trigger: 'change' // blur,change,manual
            }
         */
    initFieldValiRowRule(item, rule, value, validMethod, row, column) {
      if (validMethod) {
        return validMethod(this, item, rule, value, row, column)
      }
    },
    _addFoldOptColumn(groupData) {
      let columns = groupData.columns
      // 增加表行默认操作列
      if (groupData.groupType === 'item') {
        if (columns.find((rs) => rs.fold && rs.fold === '1')) {
          groupData.extend.optColumnList = groupData.extend.optColumnList || []
          groupData.extend.optColumnList.push({ key: 'fold', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_HOJO_3026ba64`, '更多字段'), type: 'fold', click: this.openFoldDrawer })
          if (!columns.find((rs) => rs.field === 'grid_opration')) {
            let customOption = {
              fixed: 'right',
              groupCode: groupData.groupCode
            }
            customOption = { ...GRID_OPTION_ROW, ...customOption }
            groupData.columns = groupData.columns.concat(customOption)
          }
        }
      }
    },
    // 表行编辑change事件 其他类型 date
    changeGridItemOther(currentRow, currentValue, cb) {
      cb && cb(currentRow, null, currentValue, this)
    },
    openFoldDrawer(that, row, column, tplRootRef, { columns }) {
      this.itemColumns = columns
      this.$refs?.GridFoldDrawer.openFoldDrawer(row, column)
    },
    // 设置 group 数据字典值
    setGroupFieldNameDictionaryData() {
      let dictCodeItems = []
      const groups = this.pageConfig.groups || []
      groups.forEach((group) => {
        let flag = group.groupType === 'item'
        const columns = flag ? group.columns : group.formFields

        return (
          columns
            .filter((sub) => !!sub.dictCode)
            // 过滤 select类型，组件内会自动获取下拉字典选项值
            .filter((sub) => sub.fieldType !== 'select') // 过滤 select
            .filter((sub) => sub.fieldType !== 'switch') // 过滤 switch
            .filter((sub) => sub.fieldType !== 'cascader') // 过滤 cascader
            .forEach((sub) => {
              dictCodeItems.push({
                item: sub,
                dictCode: sub.dictCode
              })
            })
        )
      })

      const promises = dictCodeItems.map((n) => {
        let params = {
          busAccount: this.busAccount,
          dictCode: n.dictCode
        }
        return ajaxFindDictItems(params)
      })

      Promise.all(handlePromise(promises)).then((res) => {
        const result = res
          // .filter((n) => n.status === "success")
          .map((n) => n.res.result)

        this.asyncSetFieldOptions(dictCodeItems, result)
      })
    },
    // 闭包处理接口异步返回字典数据
    asyncSetFieldOptions(dictCodeItems = [], result = []) {
      dictCodeItems.forEach((v, idx) => {
        if (!v.item.options) v.item.options = []
        let options = []
        if (result && result[idx]) {
          options = result[idx].map((n) => ({
            ...n,
            label: n.title,
            disabled: false
          }))
        }
        v.item.options = options
        if (v.item.editRender) {
          v.item.editRender.options = options
        }
      })
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    remoteSelectSlots(col) {
      const that = this
      col.editRender = {}
      const commonSlot = ({ row, column, rowIndex, columnIndex }) => {
        const props = {
          config: col,
          row: row,
          form: that.editFormData,
          isRow: true
        }
        const on = {
          afterClearCallBack: () => {
            const cb = col.extend && col.extend.afterRowClearCallBack
            if (cb && typeof cb === 'function') {
              if (cb.name.indexOf(PRIVATE_AFTER_CLEAR_CALLBACK) !== -1) {
                let params = {
                  _pageData: this.pageConfig, // 页面所有数据
                  _cacheAllData: this.tplRootRef.getAllData(),
                  _row: row
                }
                bindfunctionMiddleware(this.tplRootRef, cb, params)
              } else {
                cb(that, row, col, rowIndex, columnIndex)
              }
            }
          },
          ok: (data) => {
            if (col.bindFunction && typeof col.bindFunction === 'function') {
              if (col.bindFunction.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
                let params = {
                  _pageData: that.pageConfig, // 页面所有数据
                  _cacheAllData: that.tplRootRef.getAllData(),
                  _row: row,
                  _data: data // 当前值
                }
                bindfunctionMiddleware(that.tplRootRef, col.bindFunction, params)
              } else {
                col.bindFunction(row, data, that, that.tplRootRef)
              }
            }
          }
        }

        return [
          <m-remote-select
            {...{ props, on }}
            vModel={row[column.property]}
          />
        ]
      }

      return Object.assign({}, col.slots, {
        default: ({ row, column }) => [<span>{row[column.property]}</span>],
        edit: (info) => {
          return commonSlot(info)
        }
      })
    },
    // type=selectModal 弹窗的slots 逻辑抽取
    selectModalSlots(col, type) {
      const that = this
      col.editRender = {}
      const afterRowClearCallBack = col.extend && col.extend.afterRowClearCallBack
      const commonSlot = ({ row, rowIndex, column, columnIndex }, status) => {
        const scopedSlots = {
          default: ({ openFunc }) => {
            const text =
              status === 'edit'
                ? [
                    <a-input
                      title={row[column.property]}
                      disabled
                      style='text-align: center;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;'
                      vModel={row[column.property]}
                      bordered={false}
                    />
                  ]
                : [<span>{row[column.property]}</span>]
            const closeIcon =
              row[column.property] && status === 'edit'
                ? [
                    <a-icon
                      type='close-circle'
                      style='position: absolute;display: inline-block;font-size: 14px;right: 27px;top: 50%;transform:translateY(-50%);z-index: 2; cursor:pointer'
                      onClick={(event) => {
                        event.stopPropagation()
                        getObjType(afterRowClearCallBack) === 'function' && afterRowClearCallBack(that, row, column, rowIndex, columnIndex)
                      }}
                    />
                  ]
                : []
            const tpl = (
              <div
                title={row[column.property]}
                style='position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;'
              >
                {text}
                {closeIcon}
                <a style='position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 2;'>
                  <a-icon
                    type='file-search'
                    onClick={() => {
                      openFunc && openFunc()
                    }}
                  />
                </a>
              </div>
            )
            const detailTpl = <div style='position: relative;min-height: 30px;padding-right: 20px;'>{row[column.property]}</div>
            return that.pageStatus == 'edit' ? tpl : detailTpl
          }
        }
        const props = {
          config: col,
          row: row,
          form: that.editFormData,
          isRow: true
        }
        const on = {
          afterClearCallBack: (cb) => {
            if (cb && typeof cb === 'function') {
              if (cb.name.indexOf(PRIVATE_AFTER_CLEAR_CALLBACK) !== -1) {
                let params = {
                  _pageData: this.pageConfig, // 页面所有数据
                  _cacheAllData: this.tplRootRef.getAllData(),
                  _row: row
                }
                bindfunctionMiddleware(this.tplRootRef, cb, params)
              } else {
                cb(that, row, col, rowIndex, columnIndex)
              }
            }
          },
          ok: (data) => {
            if (col.bindFunction && typeof col.bindFunction === 'function') {
              if (col.bindFunction.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
                let params = {
                  _pageData: that.pageConfig, // 页面所有数据
                  _cacheAllData: that.tplRootRef.getAllData(),
                  _row: row,
                  _data: data // 当前值
                }
                bindfunctionMiddleware(that.tplRootRef, col.bindFunction, params)
              } else {
                col.bindFunction(row, data, that, that.tplRootRef)
              }
            }
          }
        }
        const $selectModalBox = (
          <m-select-modal
            scopedSlots={scopedSlots}
            {...{ props, on }}
          />
        )
        const $editInputBox = (
          <vxe-input
            v-model={row[column.property]}
            placeholder=''
            clearable
          />
        )
        const $detailInput = <span>{row[column.property]}</span>
        if (type && type === 'switch') {
          if (row.businessType) {
            return [$selectModalBox]
          } else {
            return that.pageStatus == 'edit' ? [$editInputBox] : [$detailInput]
          }
        }

        return [$selectModalBox]
      }
      return Object.assign({}, col.slots, {
        default: (info) => {
          return commonSlot(info, 'default')
        },
        edit: (info) => {
          return commonSlot(info, 'edit')
        }
      })
    },
    // 选择弹窗的 slots (fieldType:selectModal)
    ladderPriceModalSlots(col) {
      const that = this
      // 阶梯报价json数据组装
      const initRowLadderJson = function (jsonData) {
        let arr = []
        if (jsonData) {
          arr = JSON.parse(jsonData)
        }
        return arr
      }
      // 阶梯报价默认显示
      const defaultRowLadderJson = function (jsonData) {
        console.log(jsonData)
        let arrString = ''
        if (jsonData) {
          let arr = JSON.parse(jsonData)
          if (Array.isArray(arr)) {
            arr.forEach((item, index) => {
              let ladder = item.ladder
              let price = item.price || ''
              let fix = item.fix || ''
              let rate = item.rate || ''
              let union = item.union || ''
              let str = `${ladder},${price || fix || rate || union || ''}`
              let separator = index === arr.length - 1 ? '' : ';'
              arrString += str + separator
            })
          }
        }
        return arrString
      }
      return Object.assign({}, col.slots, {
        default: ({ row, rowIndex, column, columnIndex }) => {
          const scopedSlots = {
            default: ({ openFunc }) => {
              if (
                (props?.config?.groupCode == 'rebateRuleItems' && props?.config?.field == 'rebateLadder') ||
                (props?.config?.groupCode == 'rebateRuleSupplements' && props?.config?.field == 'rebateLadder') ||
                (props?.config?.groupCode == 'rebateCalculationSheetRuleSupplements' && props?.config?.field == 'rebateLadder') ||
                (props?.config?.groupCode == 'rebateCalculationSheetRuleDetails' && props?.config?.field == 'rebateLadder')
              ) {
                const tpl = (
                  <div style='position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;'>
                    <a-tooltip
                      placement='topLeft'
                      overlayClassName='tip-overlay-class'
                    >
                      <template slot='title'>
                        <div>
                          <vxe-table
                            auto-resize
                            border
                            row-id='id'
                            size='mini'
                            data={initRowLadderJson(row[column.property])}
                          >
                            <vxe-table-column
                              type='seq'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_seq`, '序号')}
                              width='80'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='ladder'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='fix'
                              title={that.$srmI18n(`${that.$getLangAccount()}#`, '固定值')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='rate'
                              title={that.$srmI18n(`${that.$getLangAccount()}#`, '比例值%')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='union'
                              title={that.$srmI18n(`${that.$getLangAccount()}#`, '每单位返')}
                              width='140'
                            ></vxe-table-column>
                          </vxe-table>
                        </div>
                      </template>
                      {defaultRowLadderJson(row[column.property])}
                    </a-tooltip>
                    <a style='position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 10;'>
                      <a-icon
                        type='stock'
                        onClick={() => {
                          openFunc && openFunc(row[column.property])
                        }}
                      />
                    </a>
                  </div>
                )
                const detailTpl = (
                  <div style='position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;'>
                    <a-tooltip
                      placement='topLeft'
                      overlayClassName='tip-overlay-class'
                    >
                      <template slot='title'>
                        <div>
                          <vxe-table
                            auto-resize
                            border
                            row-id='id'
                            size='mini'
                            data={initRowLadderJson(row[column.property])}
                          >
                            <vxe-table-column
                              type='seq'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_seq`, '序号')}
                              width='80'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='ladder'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='fix'
                              title={that.$srmI18n(`${that.$getLangAccount()}#`, '固定值')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='rate'
                              title={that.$srmI18n(`${that.$getLangAccount()}#`, '比例值%')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='union'
                              title={that.$srmI18n(`${that.$getLangAccount()}#`, '每单位返')}
                              width='140'
                            ></vxe-table-column>
                          </vxe-table>
                        </div>
                      </template>
                      {defaultRowLadderJson(row[column.property])}
                    </a-tooltip>
                  </div>
                )
                return that.pageStatus == 'edit' ? tpl : detailTpl
              } else {
                const tpl = (
                  <div style='position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;'>
                    <a-tooltip
                      placement='topLeft'
                      overlayClassName='tip-overlay-class'
                    >
                      <template slot='title'>
                        <div>
                          <vxe-table
                            auto-resize
                            border
                            row-id='id'
                            size='mini'
                            data={initRowLadderJson(row[column.property])}
                          >
                            <vxe-table-column
                              type='seq'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_seq`, '序号')}
                              width='80'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='ladder'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='price'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_price`, '含税价')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='netPrice'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_netPrice`, '不含税价')}
                              width='140'
                            ></vxe-table-column>
                          </vxe-table>
                        </div>
                      </template>
                      {defaultRowLadderJson(row[column.property])}
                    </a-tooltip>
                    <a style='position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 10;'>
                      <a-icon
                        type='stock'
                        onClick={() => {
                          openFunc && openFunc(row[column.property])
                        }}
                      />
                    </a>
                  </div>
                )
                const detailTpl = (
                  <div style='position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;'>
                    <a-tooltip
                      placement='topLeft'
                      overlayClassName='tip-overlay-class'
                    >
                      <template slot='title'>
                        <div>
                          <vxe-table
                            auto-resize
                            border
                            row-id='id'
                            size='mini'
                            data={initRowLadderJson(row[column.property])}
                          >
                            <vxe-table-column
                              type='seq'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_seq`, '序号')}
                              width='80'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='ladder'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='price'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_price`, '含税价')}
                              width='140'
                            ></vxe-table-column>
                            <vxe-table-column
                              field='netPrice'
                              title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_netPrice`, '不含税价')}
                              width='140'
                            ></vxe-table-column>
                          </vxe-table>
                        </div>
                      </template>
                      {defaultRowLadderJson(row[column.property])}
                    </a-tooltip>
                  </div>
                )
                return that.pageStatus == 'edit' ? tpl : detailTpl
              }
            }
          }
          const props = {
            config: col,
            row: row,
            pageData: that.pageConfig,
            form: that.editFormData,
            isRow: true
          }
          const on = {
            afterClearCallBack: (cb) => {
              if (cb && typeof cb === 'function') {
                if (cb.name.indexOf(PRIVATE_AFTER_CLEAR_CALLBACK) !== -1) {
                  let params = {
                    _pageData: this.pageConfig, // 页面所有数据
                    _cacheAllData: this.tplRootRef.getAllData(),
                    _row: row
                  }
                  bindfunctionMiddleware(this.tplRootRef, cb, params)
                } else {
                  cb(that.tplRootRef, row, col, rowIndex, columnIndex)
                }
              }
            },
            ok: (data) => {
              if (col.bindFunction && typeof col.bindFunction === 'function') {
                if (col.bindFunction.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
                  let params = {
                    _pageData: that.pageConfig, // 页面所有数据
                    _cacheAllData: that.tplRootRef.getAllData(),
                    _row: row,
                    _value: data // 当前值
                  }
                  bindfunctionMiddleware(that.tplRootRef, col.bindFunction, params)
                } else {
                  col.bindFunction && col.bindFunction(row, data, that.tplRootRef)
                }
              }
            }
          }
          if (
            (props?.config?.groupCode == 'rebateRuleItems' && props?.config?.field == 'rebateLadder') ||
            (props?.config?.groupCode == 'rebateRuleSupplements' && props?.config?.field == 'rebateLadder') ||
            (props?.config?.groupCode == 'rebateCalculationSheetRuleSupplements' && props?.config?.field == 'rebateLadder') ||
            (props?.config?.groupCode == 'rebateCalculationSheetRuleDetails' && props?.config?.field == 'rebateLadder')
          ) {
            props['pageStatus'] = that.pageStatus
            const $modalBoxRebate = (
              <rebateLadderPrice
                scopedSlots={scopedSlots}
                {...{ props, on }}
              />
            )
            return [$modalBoxRebate]
          } else {
            const $modalBox = (
              <LadderPrice
                scopedSlots={scopedSlots}
                {...{ props, on }}
              />
            )
            return [$modalBox]
          }
        }
      })
    },
    // 附件预览
    preViewEvent(row) {
      let preViewFile = {}
      if (row && typeof row == 'string') {
        preViewFile = JSON.parse(row) || ''
      }
      this.$previewFile.open({ params: preViewFile })
    },
    //批量下载 注入
    initBatchDownloadBtn(group) {
      new BatchDownloadBtn().handelCreateBatchDownload(group, 'externalToolBar')
      // if (this.isEdit) { // 编辑页表格特有
      //     new ButtonComponent().createButton(group, 'externalToolBar') // 默认向下填充按钮
      // }
    },
    // 附件下载
    downloadEvent(row, config) {
      let rowInfo = {}
      if (row && typeof row == 'string') {
        rowInfo = JSON.parse(row) || ''
      }
      if (!rowInfo.fileName) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
        return
      }
      const id = rowInfo.id
      const fileName = rowInfo.fileName
      let params = config.params || { id }
      const url = config.url || this.url.download || '/attachment/saleAttachment/download'
      getAction(url, params, {
        responseType: 'blob'
      }).then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    },
    // 文件上传 slots
    fileUploadModalSlots(col, type) {
      const that = this
      let fileUpInfo = {}
      return Object.assign({}, col.slots, {
        default: ({ row, rowIndex, column, columnIndex }) => {
          let fileUploadConfig = {
            property: 'label', // 可省略
            itemInfo: [], // 必传
            action: '/attachment/purchaseAttachment/upload', // 必传
            businessType: 'eightDisciplines', // 必传,
            itemNumberKey: 'materialNumber',
            itemNumbeValueProp: 'value',
            itemNumberLabel: '关联tab',
            fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
            modalVisible: false // 必传
          }
          let fileDownConfig = {}
          if (col.extend && col.extend.fileUploadConfig) fileUploadConfig = col.extend.fileUploadConfig
          if (col.extend && col.extend.fileDownConfig) fileDownConfig = col.extend.fileDownConfig
          const tokenHeader = { 'X-Access-Token': this.$ls.get('Access-Token') }
          const ACTION = '/attachment/purchaseAttachment/upload'
          const accept = '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf'
          let props = {
            property: fileUploadConfig.property || 'materialName',
            visible: fileUploadConfig.modalVisible,
            isGridUpload: true,
            title: fileUploadConfig.title || '附件上传'
          }
          let attrs = {
            tokenHeader,
            action: fileUploadConfig.action || ACTION,
            itemInfo: fileUploadConfig.itemInfo || [],
            accept,
            data: {
              businessType: fileUploadConfig.businessType,
              headId: that.resultData.id || ''
            }
          }
          const scopedSlots = {
            default: ({ openFunc }) => {
              const editTpl = (
                <div>
                  <a-button
                    size='small'
                    onClick={() => {
                      openFunc && openFunc()
                    }}
                  >
                    上传
                  </a-button>
                  <a-button
                    style='margin-left:2px'
                    type='link'
                    size='small'
                    onClick={() => {
                      that.preViewEvent(row[column.property])
                    }}
                  >
                    预览
                  </a-button>
                  <a-button
                    style='margin-left:2px'
                    type='link'
                    size='small'
                    onClick={() => {
                      that.downloadEvent(row[column.property], fileDownConfig)
                    }}
                  >
                    下载
                  </a-button>
                </div>
              )
              const noEditTpl = (
                <a-button
                  size='small'
                  onClick={() => {
                    openFunc && openFunc()
                  }}
                >
                  上传
                </a-button>
              )
              const detailTpl = (
                <div>
                  <a-button
                    type='link'
                    size='small'
                    onClick={() => {
                      that.preViewEvent(row[column.property])
                    }}
                  >
                    预览
                  </a-button>
                  <a-button
                    style='margin-left:2px'
                    type='link'
                    size='small'
                    onClick={() => {
                      that.downloadEvent(row[column.property], fileDownConfig)
                    }}
                  >
                    下载
                  </a-button>
                </div>
              )
              const noDetailTpl = <div style='position: relative;min-height: 30px;padding-right: 20px;'>{row[column.property]}</div>
              let hasRoute = row[column.property] && JSON.parse(row[column.property]).id && JSON.parse(row[column.property]).fileName
              return that.pageStatus == 'edit' ? (hasRoute ? editTpl : noEditTpl) : hasRoute ? detailTpl : noDetailTpl
            }
          }
          const on = {
            change: (data) => {
              fileUpInfo = data[0] || ''
              if (col.bindFunction && typeof col.bindFunction === 'function') {
                if (col.bindFunction.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
                  let params = {
                    _pageData: that.pageConfig, // 页面所有数据
                    _cacheAllData: that.tplRootRef.getAllData(),
                    _row: row,
                    _data: data // 当前值
                  }
                  bindfunctionMiddleware(that.tplRootRef, col.bindFunction, params)
                } else {
                  col.bindFunction && col.bindFunction(that, { row, info: data, rootRef: that.tplRootRef })
                }
              }
            }
          }
          const $customUploadBox = (
            <custom-upload
              scopedSlots={scopedSlots}
              {...{ props, attrs, on }}
            />
          )

          return [$customUploadBox]
        }
      })
    },
    // 超链接跳转
    getNewRouter(col, row, column, linkConfig) {
      let params = {
        _pageData: this.pageConfig, // 页面所有数据
        _cacheAllData: this.tplRootRef.getAllData(),
        _row: row,
        _value: row[column.property], // 当前值
        _itemColumns: col
      }
      if (col?.bindFunction && typeof col.bindFunction === 'function') {
        bindfunctionMiddleware(this.tplRootRef, col.bindFunction, params)
      }
      // 注入钩子
      if (col?.extend?.handleBefore && typeof col.extend.handleBefore === 'function') {
        let callbackObj = col.extend.handleBefore(this.tplRootRef, params, linkConfig) || {}
        linkConfig = { ...linkConfig, ...callbackObj }
      }
      // 页内跳转逻辑
      if (row[column.property] && linkConfig.actionPath && linkConfig.bindKey) {
        let query = {
          [linkConfig.primaryKey]: row[linkConfig.bindKey],
          ...linkConfig.otherQuery,
          t: new Date().getTime()
        }
        this.$router.push({ path: linkConfig.actionPath.trim(), query: { ...query } })
      }
    },
    // 超链接 slots
    linkModalSlots(col) {
      console.log('[slots执行]')
      const that = this
      let linkConfig = {
        title: '认证链接',
        titleI18nKey: 'i18n_title_authenticationLink',
        primaryKey: 'id', //查询用的主键名
        bindKey: 'fbk1', //绑定的主键key
        actionPath: '', //目标路由
        otherQuery: { open: true } //其余参数
      }
      let exLink = false
      if (col?.extend?.linkConfig) linkConfig = { ...linkConfig, ...col.extend.linkConfig }
      if (col?.extend?.exLink) exLink = true //是否外链
      return Object.assign({}, col.slots, {
        default: ({ row, rowIndex, column, columnIndex }) => {
          console.log('[default执行]')
          if (exLink) {
            return [
              <a
                href={row[column.property]}
                target='_blank'
              >
                <span>{that.$srmI18n(`${that.$getLangAccount()}#${linkConfig.titleI18nKey}`, linkConfig.title)}</span>
              </a>
            ]
          } else {
            return [
              <a
                style='cursor:pointer'
                onClick={() => {
                  that.getNewRouter(col, row, column, linkConfig)
                }}
              >
                {row[column.property]}
              </a>
            ]
          }
        }
      })
    },
    // 获取表格下拉字典
    queryDictData(column) {
      const that = this
      if (column && column.dictCode) {
        let postData = {
          busAccount: that.busAccount || that.$ls.get(USER_ELS_ACCOUNT),
          dictCode: column.dictCode
        }
        ajaxFindDictItems(postData).then((res) => {
          if (res.success) {
            let options = res.result.map((dictItem) => {
              return {
                value: dictItem.value,
                label: dictItem.text,
                title: dictItem.title
              }
            })
            if (column.editRender) {
              column.editRender.options = options
            }
            // dictOptions初始化数据字典的字段会用到
            column.dictOptions = options
            that.$forceUpdate()
          } else {
            if (column.editRender) {
              column.editRender.options = []
            }
            // dictOptions初始化数据字典的字段会用到
            column.dictOptions = []
            that.$forceUpdate()
          }
        })
      } else {
        if (column.editRender) {
          column.editRender.options = []
        }
        // dictOptions初始化数据字典的字段会用到
        column.dictOptions = []
        that.$forceUpdate()
      }
    },
    // 获取级联下拉数据字典
    queryCascaderDictData(column) {
      const that = this
      if (column && column.dictCode) {
        let postData = {
          busAccount: that.busAccount || that.$ls.get(USER_ELS_ACCOUNT),
          dictCode: column.dictCode
        }
        ajaxFindDictItems(postData).then((res) => {
          if (res.success) {
            that.cascaderDictData = res.result
          }
        })
      } else {
        this.cascaderDictData = []
      }
    },
    // 处理获取数据，将接口或者自定义的数据所有key赋值
    dealSource(resultData) {
      console.log(cloneDeep(resultData))
      // 处理辅数量（需求转多标、立项）初始物料数据中的辅数量
      let specialUrl = ['/bidding/purchaseBiddingProjectHead/queryById', '/tender/tenderProjectApprovalHead/queryById', '/tender/purchaseTenderProjectHead/queryById', '/tender/purchaseTenderProjectHead/queryById']
      let listData = resultData || []
      if (!!this.requestData?.detail?.url && this.requestData.detail.url == '/bidding/purchaseBiddingProjectHead/queryById' && !listData.length) {
        listData = resultData.purchaseBiddingItemList || []
      }
      if (!!this.requestData?.detail?.url && specialUrl.includes(this.requestData.detail.url) && !!listData?.length) {
        listData = (listData || []).map((i) => {
          i.quantityUnit = !!i.baseUnit ? i.baseUnit : i.quantityUnit
          if (!!i.requireQuantity) {
            i.secondaryQuantity = i.requireQuantity / (i.conversionRate || 1)
          } else {
            i.secondaryQuantity = 0
          }
          i.secondaryQuantity = i.secondaryQuantity.toFixed(6)
          return i
        })
      }

      try {
        if (!!this.$parent && !!this.$parent.formatPageData) {
          resultData = this.$parent.formatPageData(resultData)
        } else if (!!this.$parent && !!this.$parent.$parent && !!this.$parent.$parent.formatPageData) {
          resultData = this.$parent.$parent.formatPageData(resultData)
        }
      } catch (error) {}

      try {
        if (!!this.$parent && !!this.$parent.formatTableData) {
          resultData = this.$parent.formatTableData(resultData)
        } else if (!!this.$parent && !!this.$parent.$parent && !!this.$parent.$parent.formatTableData) {
          resultData = this.$parent.$parent.formatTableData(resultData)
        }
      } catch (error) {}

      this.resultData = resultData
      // this.showBusinessButton = true
      const that = this
      that.pageConfig.groups.forEach((group) => {
        const _show = group.show == false ? false : true
        if (group.groupType === 'head' && _show) {
          // 循环匹配
          let currentHeadTag = null
          for (let resultDataKey in resultData) {
            if (group.groupCode === resultDataKey) {
              currentHeadTag = resultDataKey
            }
          }
          if (group.formModel) {
            if (currentHeadTag) {
              // currentHeadTag内容可能为空
              if (resultData[currentHeadTag]) {
                group.formModel = Object.assign(group.formModel, resultData[currentHeadTag])
              }
            } else {
              group.formModel = Object.assign(group.formModel, resultData)
              // // 修复多个表单时，只赋值当前formFields 已配置字段值
              // if (group.groupCode === 'baseForm') {
              //     group.formModel = Object.assign(group.formModel, resultData)
              // } else {
              //     let formFields = group.formFields || []
              //     let formModel = formFields.reduce((acc, obj) => {
              //         acc[obj.fieldName] = resultData[obj.fieldName] || ''
              //         return acc
              //     }, {})
              //     group.formModel = formModel
              // }
              // 删除不属于当前组里面formModel的值
              for (let key in group.formModel) {
                that.pageConfig.groups.forEach((groupItem) => {
                  if (groupItem.groupType === 'item' && key === groupItem.groupCode) {
                    delete group.formModel[key]
                  }
                })
              }
            }
          }
          group.formModel = Object.assign({}, group.formModel)
          that.$nextTick(() => {
            const formFields = group.formFields || []
            formFields.forEach((field) => {
              const { bindFunction, fieldName, fieldType, groupCode, defaultValue } = field
              const Fdom = that.$refs[`${groupCode}form`]
              if (bindFunction && typeof bindFunction === 'function' && Fdom) {
                const formRef = Fdom[0]
                const groupData = group
                const value = group.formModel[fieldName] || bindDefaultValue(defaultValue)
                let params = {
                  _pageData: that.pageConfig, // 页面所有数据
                  _form: group.formModel,
                  _cacheAllData: that.tplRootRef.getAllData(),
                  _value: value // 当前值
                }
                let disabledFieldTypes = ['selectModal', 'customSelectModal', 'remoteSelect']
                if (!disabledFieldTypes.includes(fieldType)) {
                  if (bindFunction.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
                    bindfunctionMiddleware(that.tplRootRef, bindFunction, params)
                    return
                  }
                  if (fieldType === 'input') {
                    bindFunction.call(null, that.$parent, formRef.$refs[groupCode], that.pageConfig, groupData, value, field, group.formModel, '', that.tplRootRef)
                  } else {
                    bindFunction.call(null, that.$parent, formRef.$refs[groupCode], that.pageConfig, groupData, value, [], '', group.formModel, that.tplRootRef)
                  }
                }
              }
            })
          })
        }
        if (group.groupType === 'item' && _show) {
          group.loadData = resultData[group.groupCode]
          if (resultData[group.groupCode] && resultData[group.groupCode].length) {
            that.countTotalValue(resultData, group)
          }
        }
      })
      // console.log('[数据处理-key赋值]', that.pageConfig, resultData)
      // 数据返回后，如有需要，暴露给外部使用
      if (this.handleAfterDealSource && typeof this.handleAfterDealSource === 'function') {
        return this.handleAfterDealSource(that.pageConfig, resultData)
      }
    },
    // 获取静态数据
    initMockData(data) {
      this.dealSource(data)
    },
    // 通过接口获取数据
    async queryDetail() {
      const that = this
      this.confirmLoading = true
      if (this.requestData.detail) {
        let url = this.requestData.detail.url
        if (url) {
          let method = 'get'
          let args = this.requestData.detail.args(that)
          // 有id 才能请求,新建时是没有id的，不用请求查询接口

          if (args && args.id) {
            if (this.requestData.detail.method) {
              method = this.requestData.detail.method.toLowerCase()
            }
            // 招投标平台新增预审、后审、一步、二步法，需要在查详情时添加请求头
            let config = {}
            this.requestData.detail.config &&
              (() => {
                config = this.requestData.detail.config(that)
              })()

            let query = method === 'get' ? await getAction(url, args, config) : await postAction(url, args, config)
            this.confirmLoading = false
            if (query && query.success) {
              that.editFormData = query.result
              that.dealSource(query.result)
            } else {
              that.$message.error(query.message)
            }
          } else {
            this.confirmLoading = false
            this.$nextTick(() => {
              that.dealSource(this.currentEditRow)
            })
          }
        }
      }
    },
    // 表格编辑控制
    gridActiveMethod(gridData, cb, groupData) {
      let { row, _rowIndex, column, _columnIndex } = gridData
      return cb(this, row, _rowIndex, column, _columnIndex, groupData)
    },
    /**
     * 特殊功能存放处
     */
    // 表格计算属性失去焦点时-成本报价
    blurGridItem(data, column, group) {
      const that = this
      let url = '/formula/compute'
      let postData = {
        templateNumber: this.currentEditRow.templateNumber,
        templateVersion: this.currentEditRow.templateVersion,
        busAccount: this.busAccount || this.$ls.get('Login_elsAccount'),
        field: column.field,
        row: data.row,
        ref: group.groupCode
      }
      postAction(url, postData).then((res) => {
        if (res.success) {
          if (res.result) {
            for (let key in res.result.row) {
              data.row[key] = res.result.row[key]
            }
            if (that.$refs[group.groupCode + 'grid']) {
              let grid = that.$refs[group.groupCode + 'grid'][0].$children[0]
              let countData = {}
              let tableData = grid.getTableData().tableData
              countData[group.groupCode] = tableData
              that.countTotalValue(countData, group)
            }
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    // 单表格汇总-成本报价
    countTotalValue(data, group) {
      let total = null
      data[group.groupCode].forEach((data) => {
        if (data[group.total.culumnName]) {
          if (data[group.total.culumnName]) {
            total += Number(data[group.total.culumnName])
          }
        }
      })
      group.total.totalValue = total
    },
    /**
     * 引用组件对象，对外暴露功能存放处
     */
    // 暴露所有数据的方法
    extendAllData() {
      const that = this
      let allData = {}
      this.pageConfig.groups.forEach((group) => {
        let _show = group.show !== false
        let data = {}
        if (group.groupType === 'item' && _show) {
          let parentRef = that.$refs[group.groupCode + 'grid'] ? that.$refs[group.groupCode + 'grid'][0] : null
          if (parentRef) {
            let grid = parentRef.$refs[group.groupCode]
            if (grid && grid.getTableData()) {
              data[group.groupCode] = grid.getTableData().fullData
              allData = Object.assign({}, allData, data)
            }
          }
        } else if (group.groupType === 'head' && _show) {
          if (group.groupCode === 'baseForm') {
            data = group.formModel
          } else {
            data[group.groupCode] = group.formModel
          }
          allData = Object.assign({}, allData, data)
        }
        if (!group.show && group.verify) {
          if (group.groupType === 'item') {
            let parentRef = that.tplRootRef.$refs[group.groupCode + 'grid']
            let grid = parentRef.$refs[group.groupCode]
            if (grid && grid.getTableData()) {
              data[group.groupCode] = grid.getTableData().fullData
              allData = Object.assign({}, allData, data)
            }
          } else if (group.groupType === 'head') {
            if (group.groupCode === 'baseForm') {
              data = group.formModel
            } else {
              data[group.groupCode] = group.formModel
            }
            allData = Object.assign({}, allData, data)
          }
        }
      })
      return {
        pageConfig: this.pageConfig,
        allData: allData
      }
    },
    goBackAudit() {
      this.$parent.$parent.$parent.hideController()
    }
  },
  watch: {
    collapseHeadCode: {
      immediate: true,
      handler: function (val) {
        if (!val.length) {
          return
        }
        this.localCollapseHeadCode = val.map((rs) => (rs.code ? rs.code : rs))
        this.localCollapseKeys = val.map((rs) => (rs.code ? (rs.expand == false ? '' : rs.code) : rs)) // 兼容一维数组，一维数组默认都展开，expand为false或者不填默认都不展开
      }
    },
    fromSourceData: {
      immediate: true,
      handler: function (val) {
        this.initMockData(this.fromSourceData)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.summary-message {
  height: 14px;
  display: flex;
  align-items: center;
  margin: 10px 0;
}
.summary-message-content {
  flex-grow: 1;
  font-weight: bolder;
  .total-num {
    font-size: 16px;
    color: red;
  }
}
.update-z-index {
  z-index: 100000;
}
.item-box-title {
  padding: 0 7px;
  border: 1px solid #ededed;
  height: 34px;
  line-height: 34px;
  margin: 6px 0;
  &.dark {
    background: #f2f2f2;
  }
}
.tabStyle {
  display: flex;
  align-items: center;
  .star {
    color: red;
    font-size: 20px;
    padding-top: 5px;
    margin-left: 3px;
  }
}
</style>
