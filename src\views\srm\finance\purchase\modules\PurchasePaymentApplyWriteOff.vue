<template>
  <div class="page-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
    <!-- 加载配置文件 -->
    <RelationGraphModal
      v-if="modalVisibleDocket && currentEditRow.documentId"
      :modalVisibleDocket="modalVisibleDocket"
      :id="currentEditRow.documentId"
      :rootId="currentEditRow.id"
      @closeModalDocket="closeModalDocket"
      @onNodeClick="clickNode"
    />
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
import RelationGraphModal from '@comp/RelationGraphModal'
import {BUTTON_SAVE} from '@/utils/constant.js'
export default {
    name: 'PurchasePaymentApplyDetail',
    mixins: [businessUtilMixin],
    components: {
        flowViewModal,
        RelationGraphModal,
        BusinessLayout
    },
    data () {
        return {
            modalVisibleDocket: false,
            confirmLoading: false,
            showRemote: false,
            businessRefName: 'businessRef',
            requestData: {
                detail: {
                    url: '/finance/purchasePaymentApplyHead/queryById',
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.confirmEvent
                },
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/finance/purchasePaymentApplyHead/updateWriteOff'
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                detail: '/finance/purchasePaymentApplyHead/queryById',
                confirmWriteOff: '/finance/purchasePaymentApplyHead/confirmWriteOff'
            },
            externalToolBar: {
                paymentApplyWriteOffList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DMUBVt_36a5a8b1`, '提取预付款单'),
                        attrs: {
                            type: 'primary'
                        },
                        click: this.extractItemEvent,
                        authorityCode: 'paymentApply#purchasePaymentApplyHead:extractAdvanceCharge'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        click: this.deleteGridItem
                    }
                ]
            },
            flowView: false,
            flowId: ''
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${elsAccount}/purchase_paymentApply_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        // 如果是外部的参数，先请求获取模板js必须的参数
        if (this.$route.query && this.$route.query.open  && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'attachmentList',
                        groupType: 'item',
                        sortOrder: '6',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent  },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    }
                ],
                formFields: [],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        field: 'fileType_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                        fieldLabelI18nKey: '',
                        width: 200
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        width: 180

                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        width: 120
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount',
                        width: 120
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount',
                        width: 120
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        width: '100',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            if(!resultData.paymentApplyOtherList.length) {
                this.hideSingleGroup(this.businessRefName, 'paymentApplyOtherList', true)
            }
        },
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        goBack () {
            this.$emit('hide')
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            this.attachmentDownloadEvent(row)
        },
        //提取对账单
        extractItemEvent (){
            const params = this.getAllData() || {}
            let _this = this
            let url = '/finance/purchasePaymentApplyHead/extractAdvanceCharge'
            postAction(url, params).then(res => {
                if (res.success) {
                    let writeOffReconciliationGrid = _this.getItemGridRef('paymentApplyWriteOffList')
                    if(res.result.paymentApplyWriteOffList && res.result.paymentApplyWriteOffList.length > 0 ){
                        let oldData = _this.getItemGridRef('paymentApplyWriteOffList').getTableData().fullData
                        //过滤出可以编辑的数据
                        let tempData = []
                        oldData.forEach(item => {
                            let flag =  item.writeOffType == 2 &&
                                    (item.writeOffStatus == null || item.writeOffStatus == 1 || (item.writeOffStatus == 2 && paymentApplyNumber == item.writeOffPaymentNumber))
                            if(flag) {
                                tempData.push(item)
                            }
                        })
                        let oldBusiness = tempData.map(item => item.businessId)
                        //存储界面不存在的数据
                        let newData = []
                        res.result.paymentApplyWriteOffList.map(row => {
                            if (!oldBusiness.includes(row.businessId)) {
                                newData.push(row)
                            }
                        })
                        writeOffReconciliationGrid.remove()
                        writeOffReconciliationGrid.insert(newData)
                        writeOffReconciliationGrid.insert(oldData)
                        _this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataExtractionSucceeded`, '数据提取成功'))
                    } else {
                        _this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WFDMLVqHSDMTI_90a75550`, '数据提取为空，可更换提取条件'))
                    }
                } else {
                    _this.$message.warning(res.message)
                }
            }).finally(() => {
                this.getAllData().confirmLoading = false
            })
        },
        //删除复选框选定行
        deleteGridItem () {
            let itemGrid = this.getItemGridRef('paymentApplyWriteOffList')
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            let flag = true
            let paymentApplyNumber = this.getAllData().paymentApplyNumber
            try{
                checkboxRecords.forEach(item => {
                    flag =  item.writeOffType == 2 && (item.writeOffStatus == null || item.writeOffStatus == 1 || (item.writeOffStatus == 2 && paymentApplyNumber == item.writeOffPaymentNumber))
                    if(!flag) {
                        throw new Error('false')
                    }
                })
            }
            catch(e){
                if(e.message == 'false'){
                    this.$message.error('不能删除核销记录')
                    return
                }
            }
            itemGrid.removeCheckboxRow()
        },
        confirmEvent () {
            let url = this.url.confirmWriteOff
            const params = this.getAllData() || {}
            postAction(url, params).then(res => {
                const type = res.success ? 'success' : 'error'
                if (!this.currentEditRow.id) {
                    this.currentEditRow.id = res.result.id
                }
                this.$message[type](res.message)
                this.goBack()
            }).finally(() => {
                this.confirmLoading = false
            })
        }

    }
}
</script>
