<template>
  <!-- 保险保函缴纳 -->
  <div class="insurance-guarantee-view">
    <div v-if="!showEditPage">
      <a-row style="padding: 10px 15px;">
        <a-col :span="12">
          <label>{{ $srmI18n(`${$getLangAccount()}#i18n_field_RIJW_274265fe`, '关键字：') }}</label>
          <a-input
            style="width: 220px;"
            v-model="tenderName"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNeBLRL_fb7f81a2`, '请输入投标人名称')"></a-input>
        </a-col>
        <a-col
          :span="12"
          style="text-align: right;">
          <a-button
            type="primary"
            icon="search"
            @click="searchFun"
            style="margin-right: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_search`, '搜索') }}</a-button>
          <a-button
            icon="reload"
            @click="searchFun('0')">{{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}</a-button>
        </a-col>
      </a-row>
      <div class="add">
        <a-button
          v-if="tenderCurrentRow.applyRole == '1'"
          type="primary"
          @click="batchConfirm">{{ $srmI18n(`${$getLangAccount()}#i18n_field_zRRL_2ef5d9ec`, '批量确认') }}</a-button>
      </div>
      <div class="grid">
        <ListTable
          ref="listTable"
          :enclosure="true"
          :url="url"
          :pageData="pageData"
          :defaultParams="{subpackageId:this.subId, status: '1', marginCollectionType: '2', supplierName: tenderName}"
          :statictableColumns="tableColumns"
          :showTablePage="false">
        </ListTable>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
import ListTable from '../../components/listTable'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    components: {
        ListTable
    },
    inject: ['tenderCurrentRow', 'subpackageId'],
    data () {
        return {
            iconLoading: false,
            showEditPage: false,
            tenderName: '',
            tableColumns: [
                {
                    'type': 'checkbox',
                    'width': 50
                },
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBLRL_9daf066b`, '投标人名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tsL_17cc002`, '担保人'),
                    'field': 'guarantor'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                    'field': 'updateTime'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'),
                    'field': 'updateBy'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sxHfWjW_ef47d502`, '保函金额（元）'),
                    'field': 'amount'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sxBI_25940ef2`, '保函附件'),
                    'field': 'refundAmount',
                    'width': 250,
                    slots: {
                        // default: 'default_enclosure'
                        default: ({row, column}) => {
                            const {attachmentDTOList} = row
                            const span = attachmentDTOList.map(item => {
                                // return <div><span style='color: blue;cursor: pointer;'>{item.fileName}</span></div>
                                return <div><a-popover>
                                    <template slot="content">
                                        <a-button type="link" onClick={() => this.preViewEvent(item)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览')}</a-button>
                                        <a-button type="link" onClick={() => this.downloadEvent(item)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载')}</a-button>
                                    </template>
                                    <a-button type="link">{item.fileName}</a-button>
                                </a-popover></div>
                            })
                            return span
                        }
                    }
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    slots: { default: 'grid_opration' },
                    'width': 120
                }
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'), clickFn: this.handleConfirm, allow: this.showBtn},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_drawBack`, '退回'), clickFn: this.handleReturn, allow: this.showBtn}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            url: {
                list: '/tender/supplier/purchaseTenderProjectMarginHead/queryItem',
                confirm: '/tender/supplier/purchaseTenderProjectMarginHead/confirmMargin',
                goBack: '/tender/supplier/purchaseTenderProjectMarginHead/reject'
            }
        }
    },
    computed: {
        subId () {
            return this.subpackageId()
            // return '1532190061912612866'
        }
    },
    methods: {
        preViewEvent (row){ // 预览文件
            row.subpackageId = this.subId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (row) { // 下载文件
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row)
            this.$refs.listTable.loading= true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.$refs.listTable.loading= false
            })
        },
        batchConfirm () { // 批量确认
            const tableArray = this.$refs.listTable.$refs.table.getCheckboxRecords(true)
            var that =this
            if (tableArray.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFTPzRRLjWF_123188a3`, '请选择需要批量确认的数据！'))
                return false
            }
            const ids = tableArray.map(item => {
                return item.id
            })
            that.$refs.listTable.loading = true
            postAction(this.url.confirm, ids).then(res => {
                const type = res.success ? 'success' : 'error'
                that.$message[type](res.message)
                that.$refs.listTable.loadData()
                that.$refs.listTable.loading = false
            })
        },
        searchFun (type) {
            if (type === '0') {
                this.tenderName = ''
            }
            this.$refs.listTable.loadData && this.$refs.listTable.loadData()
        },
        showBtn (row) {
            const {status} = row
            return status == '1' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1' ? false : true
        },
        handleConfirm (row) {
            const that = this
            const {id} = row
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_warmPrompt`, '温馨提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLisWF_a38b96cf`, '是否确认选中数据'),
                onOk: function () {
                    that.$refs.listTable.loading = true

                    postAction(that.url.confirm, [id]).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        that.$refs.listTable.loadData()
                        that.$refs.listTable.loading = false
                    })
                }
            })
        },
        handleReturn (row) {
            const {id} = row
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_warmPrompt`, '温馨提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQYMisWF_f47908b7`, '是否退回选中数据'),
                onOk: function () {
                    that.$refs.listTable.loading = true
                    getAction(that.url.goBack, {id: id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        that.$refs.listTable.loading = false
                        that.$refs.listTable.loadData()
                    })
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
.add {
    padding: 10px 15px;
}
</style>


