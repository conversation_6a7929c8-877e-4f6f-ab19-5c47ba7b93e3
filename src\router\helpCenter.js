import { TabLayout } from '@/components/layouts'

const helpCenterRouter = {
    path: '/sys/menu',
    name: 'sys/menu',
    meta: {
        title: '平台管理',
        titleI18nKey: 'i18n_menu_UERv_2c39ad22',
        icon: 'contacts',
        keepAlive: false
    },
    component: TabLayout,
    children: [
        {
            path: '/sys/menu/helpCenter',
            name: 'HelpCenter',
            meta: {
                title: '帮助中心',
                titleI18nKey: 'i18n_menu_ydsV_2c0feeb1',
                keepAlive: true
            },
            component: () => import(/* webpackChunkName: 'HelpCenter' */ '@/views/sys/menu/HelpCenter.vue')
        }
    ]
}

export default helpCenterRouter
