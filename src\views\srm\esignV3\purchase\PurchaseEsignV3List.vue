<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <PurchaseEsignV3FlowEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <PurchaseEsignV3FlowDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <PurchaseEsignV3FlowAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <a-modal
      v-drag
      v-model="visible"
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reasonsRepealing`, '撤销原因')"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :layout="layout">
        <a-form-model-item :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reasonsRepealing`, '撤销原因')">
          <a-input
            v-model="form.reason" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import PurchaseEsignV3FlowAdd from './modules/PurchaseEsignV3FlowAdd'
import PurchaseEsignV3FlowEdit from './modules/PurchaseEsignV3FlowEdit'
import PurchaseEsignV3FlowDetail from './modules/PurchaseEsignV3FlowDetail'
import { getAction, postAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        PurchaseEsignV3FlowAdd,
        PurchaseEsignV3FlowEdit,
        PurchaseEsignV3FlowDetail
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            visible: false,
            rowIndex: -1,
            form: {
                reason: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXELSyRdXRLQIdDESAy_709a00f5`, '供应商ELS号/供应商名称/文件主题/业务编号')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esignv3#elsEsignV3Flow:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esignv3#elsEsignV3Flow:add'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signingInitiation`, '一步签署发起'), clickFn: this.createFlowOneStep, allow: this.allowCreateFlowOneStep, authorityCode: 'esignv3#elsEsignV3Flow:createFlowOneStep'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processtoOpen`, '流程开启'), clickFn: this.startFlow, allow: this.allowStartFlow, authorityCode: 'esign#elsEsignV3Flow:startFlow'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_flowArchiving`, '流程归档'), clickFn: this.archiveFlow, allow: this.allowArchiveFlow, authorityCode: 'esign#elsEsignV3Flow:archiveFlow'},
                    {type: 'query', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QLzEmh_5c8fbdd2`, '流程状态查询'), clickFn: this.queryFlow, allow: this.allowQueryFlow, authorityCode: 'esign#elsEsignV3Flow:flowQuery'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_drawBack`, '退回'), clickFn: this.sendBack, allow: this.sendBackFlow, authorityCode: 'esignv3#elsEsignV3Flow:sendBack'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销'), clickFn: this.backout, allow: this.backoutFlow, authorityCode: 'esign#elsEsignV3Flow:revoke'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentDownload`, '流程文档下载'), clickFn: this.flowFileDownload, allow: this.showFlowFileDownload, authorityCode: 'esign#elsEsignV3Flow:signFileDownload'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/esignv3/elsEsignV3Flow/add',
                list: '/esignv3/elsEsignV3Flow/list',
                delete: '/esignv3/elsEsignV3Flow/delete',
                createFlowOneStep: '/esignv3/elsEsignV3Flow/createFlowOneStep',
                startFlow: '/esignv3/elsEsignV3Flow/startFlow',
                archiveFlow: '/esignv3/elsEsignV3Flow/archiveFlow',
                sendBack: '/esignv3/elsEsignV3Flow/sendBack',
                backout: '/esignv3/elsEsignV3Flow/backout',
                flowFileDownload: '/esignv3/elsEsignV3Flow/signFileDownload',
                queryFlow: '/esignv3/elsEsignV3Flow/flowQuery',
                columns: 'ElsEsignV3Flow'
            }
        }
    },
    methods: {
        //已认证不能被编辑
        handleAdd () {
            this.showAddPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        sendBackFlow (row){
            if(row.sendBack==='1'){
                return true
            }
            //未发起一步签署
            if(row.launch!=='1'){
                return false
            }
            //发起了一步签署，但已撤销
            if(row.launch==='1' && row.esignStatus==='3'){
                return false
            }
            return true
        },
        backoutFlow (row){
            if(row.purchaseEsignStatus==='1' && row.saleEsignStatus==='1'){
                return true
            }
            //已发起，已开启，并且处于签署中（未完成）
            if(row.launch==='1' && row.initiate==='1'&& row.esignStatus==='0'){
                return false
            }
            return true
        },
        allowDelete (row){
            if(row.uploaded==='1'){
                return true
            }
            return false
        },
        showFlowFileDownload (row){
            //流程已经发起
            if(row.launch==='1' && row.archiving==='1' && row.esignStatus === '2'){
                return false
            }
            return true
        },
        allowCreateFlowOneStep (row){
            //已退回的单不可编辑
            if(row.sendBack==='1'){
                return true
            }
            //维护完成，没有发起
            if(row.signerVindicateStatus==='3' && row.launch!=='1'){
                return false
            }
            //撤销未退回
            if(row.esignStatus==='3' && row.sendBack!=='1'){
                return false
            }
            return true
        },
        allowStartFlow (row){
            //手动开启流程并且还未开启并且已经发起一步签署
            if(row.autoInitiate!=='1' && row.initiate!=='1' && row.launch==='1' && row.esignStatus==='0'){
                return false
            }
            return true
        },
        allowArchiveFlow (row){
            //流程完成，未归档，非自动归档
            if(row.purchaseEsignStatus==='1' && row.saleEsignStatus==='1' && row.archiving !=='1' && row.autoArchiving !==1){
                return false
            }
            return true
        },
        allowEdit (row){
            //已退回的单不可编辑
            if(row.sendBack==='1'){
                return true
            }
            //未发起，可编辑
            if(row.launch!=='1'){
                return false
            }
            //发起后撤销，可编辑
            if(row.launch ==='1' && row.esignStatus==='3'){
                return false
            }
            return true
        },
        createFlowOneStep (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmationStepSignInitiate`, '确认一步签署发起'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherToInitiateAStepSignature`, '是否发起一步签署?'),
                onOk: function () {
                    postAction(that.url.createFlowOneStep, row).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.launch_dictText = '是'
                            row.launch = '1'
                            row.esignStatus = '0'
                            row.esignStatus_dictText = '未完成'
                            row.flowId = res.result.flowId
                        }
                    })
                }
            })
        },
        startFlow (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmTheStartOfTheProcess`, '确认流程开启'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherToEnableTheProcess`, '是否确认开启流程?'),
                onOk: function () {
                    getAction(that.url.startFlow, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.initiate_dictText = '是'
                            row.initiate = '1'
                        }
                    })
                }
            })
        },
        flowFileDownload (row){
            getAction(this.url.flowFileDownload, {id: row.id}).then(res => {
                if(res.success){
                    console.log(res.result)
                    const result = res.result
                    const files = result.files[0]
                    getAction(files.downloadUrl, {}, {
                        responseType: 'blob'
                    }).then(res => {
                        let url = window.URL.createObjectURL(new Blob([res]))
                        let link = document.createElement('a')
                        link.style.display = 'none'
                        link.href = url
                        link.setAttribute('download', row.businessScene+'.pdf')
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link) //下载完成移除元素
                        window.URL.revokeObjectURL(url) //释放掉blob对象
                    })
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        archiveFlow (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmProcessArchiving`, '确认流程归档'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheArchivingProcessIsConfirmed`, '是否确认归档流程?'),
                onOk: function () {
                    getAction(that.url.archiveFlow, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.archiving_dictText = '是'
                            row.archiving= '1'
                        }
                    })
                }
            })
        },
        sendBack (row){
            this.$refs.listPage.confirmLoading = true
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmBack`, '确认退回'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmReturn`, '是否确认退回'),
                onOk: function () {
                    that.$refs.listPage.confirmLoading = false
                    getAction(that.url.sendBack, {id: row.id}).then(res => {
                        if(res.success){
                            that.$message.success('退回成功')
                            that.$refs.listPage.loadData()
                        }
                    })
                },
                onCancel: function (){
                    that.$refs.listPage.confirmLoading = false
                }
            })
        },
        backout (row, column, $rowIndex){
            this.rowIndex = $rowIndex
            this.visible = true
        },
        handleOk () {
            let param = this.$refs.listPage.tableData[this.rowIndex]
            if(!this.form.reason){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_qXjWxOLV_2175b28d`, '撤销原因不能为空'))
                return
            }
            getAction('/esignv3/elsEsignV3Flow/revoke', {id: param.id, reason: this.form.reason}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    param.esignStatus_dictText = '已撤销'
                    param.esignStatus= '3'
                }
            })
            this.visible = false
        },
        allowQueryFlow (row){
            if(row.launch==='1'){
                return false
            }
            return true
        },
        queryFlow (row){
            getAction(this.url.queryFlow, {id: row.id}).then(res => {
                if(res.success){
                    this.$message.info('流程'+ res.message)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>