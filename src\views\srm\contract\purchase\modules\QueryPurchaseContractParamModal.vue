<template>
  <div>
    <a-modal
      v-drag    
      centered
      :title="title"
      :width="720"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="close"
    >
      <a-form-model layout="inline">
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_paramType`, '参数类型')">
          <m-select
            style="width:200px"
            v-model="paramType"
            :disabled="statusSelect"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectParamsTypeTips`, '请选择参数类型')"
            dict-code="srmParamType" />
        </a-form-model-item>
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_keyword`, '关键字')">
          <a-input
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')"
            v-model="keyWord"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button
            type="primary"
            @click="onSearch">{{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}</a-button>
        </a-form-model-item>
      </a-form-model>
      <vxe-grid
        border
        resizable
        max-height="350"
        row-id="id"
        size="mini"
        :loading="loading"
        ref="selectGrid"
        :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
        :data="tableData"
        :pager-config="tablePage"
        :radio-config="selectModel === 'single' ? checkedConfig : undefined"
        :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
        :columns="currentColumns"
        @page-change="handlePageChange"
      >
      </vxe-grid>
    </a-modal>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import {srmI18n, getLangAccount} from '@/utils/util' 
export default {
    props: {
        title: {
            type: String,
            default: srmI18n(`${getLangAccount()}#i18n_title_viewParams`, '查看参数')
        },
        columns: {
            type: Array,
            default: () => [
                { type: 'checkbox', width: 40 },
                { type: 'seq', title: srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号'), width: 60 }
            ]
        },
        selectModel: {
            type: String,
            default: 'single'
        }
    },
    data () {
        return {
            paramType: '',
            keyWord: '',
            visible: false,
            loading: false,
            statusSelect: false,
            confirmLoading: false,
            currentUrl: '/contract/purchaseContractParam/queryPageListSys',
            currentColumns: [
                { type: 'radio', width: 40 },
                { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                { field: 'paramType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paramType`, '参数类型'), width: 120 },
                { field: 'paramName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paramName`, '参数名称'), width: 150 },
                { field: 'paramContent', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paramContent`, '参数内容'), width: 250 }
            ],
            labelCol: {
                md: { span: 8 },
                lg: { span: 8 },
                xxl: { span: 6 }
            },
            wrapperCol: {
                md: { span: 16 },
                lg: { span: 16 },
                xxl: { span: 18 }
            },
            checkedConfig: {highlight: true, reserve: true, trigger: 'row'},
            tableData: [],
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                align: 'left',
                pageSizes: [10, 20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            propParams: {}
        }
    },
    methods: {
        loadData (params) {
            this.loading = true
            getAction(this.currentUrl, params).then(res => {
                if (res.success) {
                    let list = res.result.records || []
                    this.tableData = list
                    this.tablePage.total = res.result.total
                }
                this.loading = false
            })
        },
        open (paramDetail) {
            this.keyWord = ''
            if (paramDetail) {
                this.paramType = paramDetail.paramType
                this.statusSelect = true
            } else {
                this.paramType = ''
            }
            this.tablePage.currentPage = 1
            let queryParams = { pageSize: this.tablePage.pageSize, pageNo: this.tablePage.currentPage }
            this.propParams = {...queryParams}

            this.loadData(queryParams)
            this.visible = true
            this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxRow()
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            const row = this.$refs.selectGrid.getRadioRecord()
            this.$emit('ok', row)
            this.visible = false
        },
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            this.loadData({ pageSize: pageSize, pageNo: currentPage, keyWord: this.keyWord, paramType: this.paramType})
        },
        onSearch () {
            this.loadData(Object.assign({}, this.propParams, {keyWord: this.keyWord, paramType: this.paramType}))
        }
    }
}
</script>