<template>
  <a-drawer
    :title="$srmI18n(`${this.$getLangAccount()}#i18n_dict_HOJO_3026ba64`, '更多字段')"
    placement="right"
    :destroyOnClose="true"
    width="500"
    class="grid-fold-drawer"
    :visible="drawerVisible"
    @close="drawerVisible=false"
  >
    <a-form-model
      :model="currentGridRow"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }">
      <a-row :getterr="12">
        <a-col
          v-for="(item, idx) in flodColumns"
          :key="idx"
          :span="24"
        >
          <a-form-model-item
            :prop="item.field"
            v-if="pageStatus == 'detail'">
            <template #label>
              <span>
                <a-tooltip :title="`${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`">{{ $srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title) }}</a-tooltip>
                <a-tooltip
                  v-if="item.helpText"
                  :title="item.helpText">
                  <a-icon type="question-circle-o" />
                </a-tooltip>
              </span>
            </template>
            <a-input 
              type="input"
              :disabled="true"
              auto-size 
              v-model="currentGridRow[item.field]"
            />
          </a-form-model-item>
          <a-form-model-item
            v-else
            :prop="item.field">
            <template #label>
              <span>
                <a-tooltip :title="`${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`">{{ $srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title) }}</a-tooltip>
                <a-tooltip
                  v-if="item.helpText"
                  :title="item.helpText">
                  <a-icon type="question-circle-o" />
                </a-tooltip>
              </span>
            </template>
            <template v-if="['input', 'password', 'textArea'].includes(item.fieldType)">
              <a-input 
                @change="flodChange(item, $event)"
                :type="item.fieldType === 'input' ? 'text' : item.fieldType.toLowerCase()"
                :disabled="item.disabled"
                auto-size 
                v-model="currentGridRow[item.field]"
                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
              />
            </template>
            <template v-else-if="item.fieldType === 'select'">
              <m-select
                v-model="currentGridRow[item.field]"
                returnTitle
                :configData="item"
                :options="item.options"
                :disabled="item.disabled"
                :dictCode="item.dictCode"
                :currentEditRow="currentGridRow"
                :getPopupContainer="node => node.parentNode || document.body"
                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
                @change="(...param) => { flodChange(item, param) }"
              />
            </template>
            <template v-else-if="item.fieldType === 'multiple'">
              <a-tooltip
                v-if="item.disabled"
                :title="currentGridRow[item.field + '_dictText'] || currentGridRow[item.field]">
                <span class="ant-form-item-children">
                  <span
                    class="ant-input ant-input-disabled"
                    style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                    {{ currentGridRow[item.field + '_dictText'] || currentGridRow[item.field] }}
                  </span>
                </span>
              </a-tooltip>
              <m-select
                v-else
                v-model="currentGridRow[item.field]"
                mode="multiple"
                :configData="item"
                :dictCode="item.dictCode"
                :currentEditRow="currentGridRow"
                :maxTagCount="item.extend && item.extend.maxTagCount || 1"
                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
                @change="(...param) => { flodChange(item, param) }"
              />
            </template>
            <template v-else-if="item.fieldType === 'cascader'"> 
              <m-cascader
                v-model="currentGridRow[item.field]"
                changeOnSelect
                :mode="item.dictCode"
                :disabled="item.disabled"
                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
                @change="() => changeInputValue(currentGridRow[item.field], item)"
              />
            </template>
            <template v-else-if="item.fieldType === 'number'">
              <a-input-number
                v-model="currentGridRow[item.field]"
                :disabled="item.disabled"
                v-bind="item.extend || {}"
                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
                @change="() => changeInputValue(currentGridRow[item.field], item)"
              />
            </template>

            <template v-else-if="item.fieldType === 'currency'">
              <a-input-number
                v-model="currentGridRow[item.field]"
                :disabled="item.disabled"
                v-bind="item.extend || {}"
                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
                @change="(...param) => { flodChange(item, param) }"
              />
            </template>

            <template v-else-if="['date', 'currentDate'].includes(item.fieldType)">
              <a-date-picker
                v-model="currentGridRow[item.field]"
                :disabled="item.disabled"
                :disabledDate="item.fieldType === 'currentDate' ? disabledDate : null"
                :show-time="item.dataFormat && item.dataFormat.length > 10 ? true : false"
                :valueFormat="(item.dataFormat || 'YYYY-MM-DD')"
                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
                @change="(...param) => { flodChange(item, param) }"
              />
            </template>

            <template v-else-if="item.fieldType === 'switch'">
              <m-switch
                v-model="currentGridRow[item.field]"
                :disabled="item.disabled"
                :configData="item"
                @change="(...param) => { flodChange(item, param) }"
              />
            </template>

            <template v-else-if="item.fieldType === 'treeSelect'">
              <m-tree-select
                v-model="currentGridRow[item.field]"
                allowClear
                :configData="item"
                :disabled="item.disabled"
                :multiple="item.extend && item.extend.multiple || false"
                :maxTagCount="item.extend && item.extend.maxTagCount || 1"
                :sourceUrl="item.dictCode"
                :sourceMap="item.sourceMap"
                :showEmptyNode="item.showEmptyNode"
                :parentNodeSelectable="item.extend && (item.extend.parentNodeSelectable || true)"
                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
                @change="(...param) => { flodChange(item, param) }"
              />
            </template>

            <template v-else-if="item.fieldType === 'customSelectModal'">
              <div @click="customSelectModel(item)">
                <a-input
                  v-model="group.formModel[item.field]"
                  readOnly
                  allowClear
                />
              </div>
            </template>

            <template v-else-if="item.fieldType === 'richEditorModel'">
              <rich-editor-model
                :value="currentGridRow[item.field]"
                :disabled="item.disabled"
                :showTooltip="(item.extend && item.extend.showTooltip) || false"
                @handleSureClick="content => currentGridRow[item.field] = content"
              />

            </template>

            <template v-else-if="item.fieldType === 'image'">
              <m-upload
                :value.sync="currentGridRow[item.field]"
                :accept="accept2"
                :disabled="item.disabled"
                :headers="tokenHeader"
                :data="{ businessType: (item.extend && item.extend.businessType) || item.dictCode, headId: form.id}"
              />
            </template>

            <template v-else-if="item.fieldType === 'hiddenField'">
              <a-input
                v-model="currentGridRow[item.field]"
                :disabled="item.disabled"
                type="hidden"
                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
                @change="() => changeInputValue(currentGridRow[item.field], item)"
              />
            </template>

            <template v-else-if="item.fieldType === 'float'">
              <m-float
                v-model="currentGridRow[item.field]"
                :configData="item"
                :disabled="item.disabled"
                :currentEditRow="currentGridRow"
                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
                @change="() => changeInputValue(currentGridRow[item.field], item)"
              />
            </template>
            <template v-else>
              <a-form-model-item :prop="item.field" >
                <a-input 
                  type="input"
                  :disabled="true"
                  auto-size 
                  v-model="currentGridRow[item.field]"
                />
              </a-form-model-item>
            </template>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </a-drawer>
</template>

<script>
export default {
    inject: ['tplRootRef'],
    props: {
        itemColumns: {
            type: Array,
            default () {
                return []
            }
        },
        busAccount: {
            type: String,
            default: ''
        },
        templateDesc: {
            type: String,
            default: 'editlayout'
        },
        pageStatus: {
            type: String,
            default: 'edit'
        }
    },
    data () {
        return {
            drawerVisible: false,
            currentGridRow: {},
            currentGridCol: {},
            inputI18nTxt: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入'),
            selectI18nTxt: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')
        }
    },
    computed: {
        flodColumns () {
            // 折叠字段集合、排除表行无用字段以及隐藏字段
            console.log(this.itemColumns)
            return this.itemColumns.filter(rs =>  rs.fold && rs.fold == 1)
        }
    },
    methods: {
        handleSelectModalAfterSelect (item, rows) {
            this.flodChange(item)
        },
        changeInputValue (value, item) {
            this.flodChange(item)
        },
        flodChange (item, otherParams) {
            const commonTypes = ['input', 'password', 'textArea', 'select', 'multiple', 'switch']
            const rootRef = this.templateDesc == 'editlayout' ? this.tplRootRef : this.tplRootRef.$refs.businessRef
            const value = this.currentGridRow[item.field]
            // debugger
            const fieldType = item.fieldType
            debugger
            if (commonTypes.includes(fieldType)) {
                let currentRow = {
                    row: this.currentGridRow,
                    column: this.currentGridCol
                }
                let currentValue = {
                    value
                }
                rootRef.changeGridItem(currentRow, currentValue, item.bindFunction)
            } else if (['number', 'float', 'currency'].includes(fieldType)) {
                rootRef.changeGridFloatItem(this.currentGridRow, this.currentGridCol, value, item.bindFunction)
            } else if (fieldType === 'date') {
                rootRef.changeGridItemOther(this.currentGridRow, value, item.bindFunction )
            }
        },
        openFoldDrawer (row, column) {
            // this.currentGridRow = param
            this.currentGridRow = row // 当前所有行
            this.currentGridCol = column // 当前所有列
            this.drawerVisible = true
        }
    }
}
</script>

<style lang='less' scoped>
    .grid-fold-drawer .ant-form-item{
        margin-bottom: 8px;
        :deep(.ant-calendar-picker){
            width: 100%;
        }
         :deep(.ant-input-number){
            width: 100%;
        }
    }
</style>