/**
 * vxe-table pro v1.0.24
 */
import XEUtils from 'xe-utils/ctor'
/* eslint-disable */
(function(e){var be=Math.abs,Re=Math.max,ye=Math.min,Ae=Math.floor;function t(e){return e&&!1!==e.enabled}function l(e,t){return e.type===t}function o(e){return e==="copy"?1:e==="extend"?2:e==="multi"?3:e==="active"?4:0}function r(e,t,l){return e.$vxe.t(t,l)}function n(e){clearTimeout(e._msTout),e._msTout=null}function s(e){e.stopPropagation(),a(e)}function a(e){e.preventDefault()}function i(e,t){return e.querySelector(t)}function d(e){return e.getBoundingClientRect()}function c(e){e&&(e.style.display="block")}function f(e){e&&(e.style.display="")}function u(e,t){e&&e.className&&Xe(e.className.split(" "),e=>e!==t).concat([t]).join(" ")}function m(e,t){e&&e.className&&Xe(e.className.split(" "),e=>e!==t).join(" ")}function p(e,t){const{$refs:l}=e,{tableBody:o,leftBody:r,rightBody:n}=l;let s=o?o.$el:null;return"left"===t&&r?s=r.$el:"right"==t&&n&&(s=n.$el),s}function h(e){const t=document.createElement("span");return t.className="vxe-table--cell-main-area",e.appendChild(t),t}function g(e,t){t?e.setAttribute("half",1):e.removeAttribute("half")}function w(e,t){c(e),e.style.height=`${t.height}px`,e.style.width=`${t.width}px`,e.style.left=`${t.left}px`,e.style.top=`${t.top}px`}function v(e,t){Me(e.$el.querySelectorAll(".vxe-table--cell-area"),t)}function C(e,t){v(e,e=>{const l=e.children[o(t)];f(l)})}function x(e,t){const{editStore:l}=t,{actived:o}=l,{column:r,row:n}=o;if(r&&n){const{offsetRow:l,offsetColumn:r}=I(t,o.row,o.column);t.clearActived(e),t.$nextTick(()=>t.setCellAreas([{type:Ge,startRow:l,endRow:l,startColumn:r,endColumn:r}]))}}function b(e,t,l){const{mergeList:o}=e;return Ne(o,({row:e,col:o,rowspan:r,colspan:n})=>t>=e&&t<e+r&&l>=o&&l<o+n)}function R(e,t){const l=d(t);return{offsetY:e.clientY-l.top,offsetX:e.clientX-l.left}}function y(e){const{mergeList:t,afterFullData:l,visibleColumn:o}=e,r=e.getCellAreas();return Xe(t,({row:e,col:t,rowspan:n,colspan:s})=>Be(r,r=>{const{rows:a,cols:i}=r,d=De(l,je(a)),c=De(l,Pe(a)),f=De(o,je(i)),u=De(o,Pe(i));return e>=d&&e+n-1<=c&&t>=f&&t+s-1<=u}))}function A(e,t){const{$vxe:l,afterFullData:o,visibleColumn:n}=e,{modal:s}=l,a=e.getCellAreas(),i=a.length;let d=!1;const c={};for(let l=0;l<i;l++){const t=a[l],{rows:i,cols:f}=t;for(let t=0,l=i.length;t<l;t++){const l=i[t],a=De(o,l);for(let o=0,i=f.length;o<i;o++){const i=f[o],u=De(n,i),m=a+":"+u;if(c[m])return void(s&&s.message({message:r(e,"vxe.pro.area.multiErr"),status:lt,id:tt}));!d&&(0<t||0<o)&&Ye(Ke(l,i.property))&&(d=!0),c[m]=!0}}}const f=y(e);let u=!1;if(f.length)e.removeMergeCells(f);else{if(Be(a,({rows:e,cols:t})=>e.length===o.length||t.length===n.length))return void(s&&s.message({message:r(e,"vxe.pro.area.mergeErr"),status:lt,id:tt}));u=!0,e.setMergeCells(a.map(({rows:e,cols:t})=>(Me(e,(e,l)=>{Me(t,(t,o)=>{(0<l||0<o)&&qe(e,t.property,null)})}),{row:je(e),col:je(t),rowspan:e.length,colspan:t.length})))}const m=a.map(({rows:e,cols:t})=>({rows:e,cols:t}));e.emitEvent("cell-area-merge",{status:u,targetAreas:m},t)}function I(e,t,l){const{afterFullData:o,visibleColumn:r}=e,n=De(o,t),s=De(r,l),a=b(e,n,s);if(a){const{row:e,col:n}=a;t=o[e],l=r[n]}return{offsetRow:t,offsetColumn:l}}function E(e,t,l){const{mergeList:o,afterFullData:r,visibleColumn:n}=e;if(o.length){const o=je(t),s=je(l),a=De(r,o),i=De(n,s),d=a+t.length-1,c=i+l.length-1;let f=a,u=i,m=d,p=c;for(let t=f;t<=m;t++)for(let l=u;l<=p;l++){const o=b(e,t,l);if(o){const{row:e,col:r,rowspan:n,colspan:s}=o,a=e+n-1,i=r+s-1;let d=!1;e<f&&(d=!0,f=e),r<u&&(d=!0,u=r),a>m&&(d=!0,m=a),i>p&&(p=i,d=!0),d&&(t=f,l=u)}}return{rows:$e(r,f,m+1),cols:$e(n,u,p+1)}}return{rows:t,cols:l}}function T(e,t,l,o,r,n){const{afterFullData:s,visibleColumn:a,scrollYLoad:i,scrollYStore:d}=e,{rowHeight:c}=d;if(i)return(Ae(n/c)+1)*c;let f=0;const u=(r?"next":"previous")+"ElementSibling";if(r&&o){const{row:e,col:r}=o;t=s[e],l=a[r]}const m=e.getCell(t,l);for(let s=m.parentNode;s&&n>=f;)f+=s.offsetHeight,s=s[u];return f}function F(e,t,l,o,r,n){const{visibleColumn:s}=e;let a=0;if(r)for(let e=De(s,l)+1,t=s.length;e<t;e++){const t=s[e];if(a+=t.renderWidth,a>=n)return a}else for(let e=De(s,l)-1;0<=e;e--){const t=s[e];if(a+=t.renderWidth,a>=n)return a}return a}function $(e,t,l,o,r,n){const{afterFullData:s,scrollYLoad:a,scrollYStore:i}=e;let d=0,c=0,f=[];const u=De(s,t);if(a){const{rowHeight:e}=i;c=Ae(n/e)+1,d=c*e}else{const e=(r?"next":"previous")+"ElementSibling";for(r&&(c++,d+=o.offsetHeight);o&&n>=d;)d+=o.offsetHeight,o=o[e],c++}return f=r?$e(s,u,ye(s.length+1,u+c)):$e(s,Re(0,u-c),u+1),{moveHeight:d,moveSize:c,rows:f}}function S(e,t,l,o,r,n){const{visibleColumn:s}=e;let a=0;const i=[l];let d=De(s,l);if(r){a+=l.renderWidth,d++;for(let e=s.length;d<e&&!(a>=n);d++){const e=s[d];i.push(e),a+=e.renderWidth}}else for(d--;0<=d&&!(a>=n);d--){const e=s[d];i.unshift(e),a+=e.renderWidth}return{moveWidth:a,moveSize:i.length,cols:i}}function k(e,t,l){const{visibleColumn:o}=e,{left:r,width:n}=l;let s=0;const a=[];for(let i=0,d=o.length;i<d;i++){const e=o[i];if(s>=r&&a.push(e),s+=e.renderWidth,s>=r+n)return a}return a}function D(e,t,l){const{afterFullData:o,scrollYLoad:r,scrollYStore:n}=e,{top:s,height:a}=l;if(r){const{rowHeight:e}=n,t=Ae(s/e);return $e(o,t,t+Ae(a/e))}let i=0;const d=[];for(;t&&i+2<s+a;)i+2>=s&&d.push(e.getRowNode(t).item),i=t.offsetTop+t.offsetHeight,t=t.nextElementSibling;return d}function V(e,t){const{visibleColumn:l,afterFullData:o,scrollYLoad:r,scrollYStore:n}=e,{type:s,area:a,column:d,row:c}=t;let f=d,u=c;const m=p(e,f.fixed),h=i(m,".vxe-table--cell-area"),g=h.children,v=g[4];let C=De(l,f),x=De(o,u);if(a&&-1<C&&-1<x){let i=0,m=0,p=0,h=0;const g=b(e,x,C);if(g){const{row:r,col:n,rowspan:i,colspan:m}=g;return x=r,u=o[x],C=n,f=l[C],t.rows=$e(o,x,x+i),t.cols=$e(l,C,C+m),Fe(M(e,t),{type:s,area:a,column:d,row:c})}if(i+=d.renderWidth,Me($e(l,0,C),e=>{h+=e.renderWidth}),r){const{rowHeight:e}=n;if(p=x*e,g){const{rowspan:t}=g;m=e*t}else m=e}else{const t=e.getCell(u,f),l=t.parentNode;p=l.offsetTop,m=t.offsetHeight}const R={type:s,area:a,column:d,row:c,top:p,left:h,width:i,height:m};return w(v,R),R}return null}function W(e,t,l,o,r,n){const{showOverflow:s,spanMethod:a,scrollXLoad:d,columnStore:u,keyboardConfig:m,keyboardOpts:v,mergeList:C,visibleColumn:x,afterFullData:R,scrollYLoad:y,scrollYStore:A}=e,{type:I,cols:E}=t,T=p(e,o),F=i(T,".vxe-table--cell-area"),$=F.children,S=$[0],V=$[1],W=$[2],M=$[3],L=$[4],N=i(T,".vxe-body--row"),O=N.firstChild;let H=je(n),_=je(r),Y=Pe(n),X=Pe(r),B=De(x,_),K=De(R,H),q=De(x,X),j=De(R,Y),P=0,z=0;const U=[];if(C.length&&Me(n,(t,l)=>{const o=De(R,t);Me(r,(t,s)=>{const a=De(x,t),i=b(e,o,a);if(i){const{row:e,col:t,rowspan:o,colspan:a}=i;0===l&&0===s&&(K=e,H=R[K]),l===n.length-1&&s===r.length-1&&(j=e,P=o-1,Y=R[j]),0===l&&0===s&&(B=t,_=x[B]),l===n.length-1&&s===r.length-1&&(q=t,z=a-1,X=x[q]),i&&-1===De(U,i)&&U.push(i)}})}),-1<B&&-1<q&&-1<K&&-1<j){let n=0,i=0,p=0,b=0,R=$e(x,0,B);if("right"===o){const e=u[`${o}List`];let t=[];C.length||a||m&&v.isMerge||(o&&s?t=e:d&&o&&(t=e)),t.length&&(R=$e(t,0,De(t,x[B])))}if(Me(R,e=>{b+=e.renderWidth}),Me($e(x,B,q+z+1),e=>{n+=e.renderWidth}),y){const{rowHeight:e}=A;p=K*e,i=(j+P+1-K)*e}else{const t=e.getCell(H,_),l=e.getCell(Y,X),o=t.parentNode,r=l.parentNode;p=o.offsetTop,i=r.offsetTop+l.offsetHeight-p}const T={el:t.el,leftEl:t.leftEl,rightEl:t.rightEl,type:I,cols:[],rows:[],top:p,left:b,width:n,height:i},$=o&&r.length!==E.length;if(T.cols=k(e,O,T),T.rows=D(e,N,T),c(F),I===Ge)l?c(S.firstChild):f(S.firstChild),g(S,$),w(S,T);else if(I===Ze)g(V,$),w(V,T);else if(I===Je)g(W,$),w(W,T);else if(I===Qe){const e=o?`${o}El`:"el",t=T[e]||h(M);T[e]=t,g(t,$),w(t,T)}else I===et&&(g(L,$),w(L,T));return T}return null}function M(e,t){const{type:l,cols:o,rows:r}=t,n=Xe(o,e=>"left"===e.fixed),s=Xe(o,e=>"right"===e.fixed);return l!==Qe&&C(e,l),n.length&&W(e,t,o.length===n.length,"left",n,r),s.length&&W(e,t,!0,"right",s,r),W(e,t,!s.length,null,o,r)}function L(e,t){const{visibleColumn:l,afterFullData:o}=e,{startColumn:r,endColumn:n,startRow:s,endRow:a}=t,i=De(o,s),d=De(o,a),c=De(l,r),f=De(l,n),u=$e(o,i,d+1),m=$e(l,c,f+1),{rows:p,cols:h}=E(e,u,m);return t.rows=p,t.cols=h,M(e,t)}function N(e){return /\n/.test(e)?`"${e.replace(/"/g,"\"\"")}"`:e}function O(e){return e.replace(/"/g,"&quot;")}function H(e){return ke(e)?e?"TRUE":"FALSE":e}function _(e,t,o){const r=[],n=[];if(t){const{afterFullData:s,visibleColumn:a,clipOpts:i,seqOpts:d}=e,{cols:c,rows:f}=t,u=d.seqMethod,m=i.copyMethod||i.getMethod;let p=De(s,je(f));Me(f,t=>{const i=[],d=[],f=De(s,t);p++,Me(c,r=>{const n=De(a,r),s=b(e,f,n);let c;if(c=l(r,rt)?u?u({row:t,rowIndex:e.getRowIndex(t),column:r,columnIndex:e.getColumnIndex(r)}):p:l(r,nt)?e.isCheckedByCheckboxRow(t):l(r,st)?e.isCheckedByRadioRow(t):Ke(t,r.property),m&&(c=m({isCut:o,row:t,column:r,cellValue:c,$table:this})),c=Ye(H(c)),s){const{row:e,col:t,rowspan:l,colspan:o}=s;f===e&&n===t&&i.push(`<td rowspan="${l}" colspan="${o}">${O(c)}</td>`)}else i.push(`<td>${O(c)}</td>`);d.push(N(c))}),r.push(d),n.push(`<tr>\n${i.join("")}\n</tr>`)})}const s=n.length?["<html>","<head>","<meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,minimal-ui\">","</head>","<body>","<table border=0 cellpadding=0 cellspacing=0>",n.join("\n"),"</table>","</body>","</html>"]:[];return{area:t,cellValues:r,text:r.map(e=>e.join("\t")).join(Ue),html:s.join(Ue)}}function Y(e){const t=document.activeElement,l=e.getCellAreas(),o=t?t.tagName:"";return l.length&&(!o||!Ve(["input","textarea"],o.toLowerCase()))}function X(e,t,l){const{$vxe:o,clipOpts:n,filterStore:s}=e,{modal:i}=o,d=n.beforeCopyMethod||n.beforeGetMethod,c=e.getCellAreas(),f={text:"",html:""};let u,m=!1,p=[];if(!s.visible){if(t&&a(t),1===c.length){const t=je(c);if(t){const{rows:o,cols:r}=t,n=$e(o,0),s=$e(r,0);if(p=[{rows:n,cols:s}],!d||!1!==d({isCut:l,targetAreas:p,$table:this})){const o=Fe({},t,{cut:l,type:Ze,rows:n,cols:s});m=!0,u=_(e,t,l),f.text=u.text,f.html=u.html,e.copyAreaMpas=Fe(o,M(e,o))}}}else 1<c.length&&i&&i.message({message:r(e,"vxe.pro.area.multiErr"),status:lt,id:tt});t&&e.emitEvent(`cell-area-${l?"cut":"copy"}`,{status:m,targetAreas:m?p:[],cellValues:u?u.cellValues:[]},t),o.clipboard=f}return f}function B(e,t){return e.replace(/#\d+@\d+/g,e=>Le(t,e)?t[e]:e)}function K(e){return`#${e}@${Ie()}`}function q(e,t){const l=B(e,t);return l.replace(/^"+$/g,e=>"\"".repeat(Math.ceil(e.length/2)))}function j(e){return"TRUE"===e||"true"===e||!0===e}function P(e,t,o){const{$vxe:n,clipOpts:s,copyAreaMpas:i,filterStore:d}=e,{cutMethod:c,beforeCutMethod:f}=s,u=s.pasteMethod||s.setMethod,m=s.beforePasteMethod||s.beforeSetMethod,{modal:p}=n,h=e.getCellAreas();let g=!1,w=[];if(!d.visible){if(t&&a(t),1===h.length){const t=je(h),{rows:n,cols:s}=t,a=je(s),d=je(n),v=[],C=h.map(({rows:e,cols:t})=>({rows:e,cols:t})),x=[],R=i&&i.cut;if(R){const{cols:t,rows:o}=i;if(x.push({rows:t,cols:o}),!f||!1!==f({cutAreas:x,currentAreas:C,$table:this})){let r;const n=null;Me(o,o=>{Me(t,t=>{if(c)c({row:o,column:t,cellValue:n,$table:this});else{const{property:s}=t;l(t,nt)?e.setCheckboxRow(o,!1):l(t,st)?e.isCheckedByRadioRow(o)&&(r=o):s&&qe(o,s,n)}})}),r&&e.clearRadioRow(),e.clearCopyCellArea()}}const{text:y}=o,A={},I=Date.now();let E,T;if(Me(y.replace(/(^\r\n)|(\r\n$)/,"").split(Ue),e=>{const t=[];Me(e.split(it),e=>{let l=e.trim();/\n/.test(l)&&(l=B(l.replace(/("")|(\n)/g,(e,t)=>{const l=K(I);return A[l]=t?"\"":"\n",l}).replace(/"(.*?)"/g,(e,t)=>q(t,A)),A)),t.push(l)}),v.push(t)}),1===v.length&&1===v[0].length){if(w=C,!m||!1!==m({isCut:R,cutAreas:x,currentAreas:C,targetAreas:w,cellValues:v,$table:this})){const t=v[0][0];g=!0,Me(h,o=>{const{rows:r,cols:n}=o;Me(r,o=>{Me(n,r=>{u?u({isCut:R,row:o,column:r,cellValue:t,$table:this}):l(r,nt)?e.setCheckboxRow(o,j(t)):l(r,st)?(!E&&(E=r),j(t)&&(T=o)):qe(o,r.property,t)})})})}}else if(1===h.length){const{visibleColumn:t,afterFullData:o}=e;let n=d,s=a;const i=De(o,d),c=De(t,a);if(-1<c&&-1<i){const f=[],h=[],y=[],A=v.length,I=v[0].length;for(let l=0;l<A;l++){const u=v[l],m=i+l,g=o[m];if(g){const w=[];for(let f=0;f<I;f++){const g=u[f],v=c+f,C=t[v];if(C){const f=b(e,m,v);if(f){const{row:l,col:u,rowspan:m,colspan:h}=f;if(i+A<l+m||c+I<u+h)return n=o[l+m-1],s=t[u+h-1],e.setCellAreas([{type:Ge,startColumn:a,endColumn:s,startRow:d,endRow:n}],{column:a,row:d}),void(p&&p.message({message:r(e,"vxe.pro.area.mergeErr"),status:lt,id:tt}))}l||h.push(C),w.push(g)}}f.push(g),y.push(w)}}w=[{rows:f,cols:h}],m&&!1===m({isCut:R,cutAreas:x,currentAreas:C,targetAreas:w,cellValues:y,$table:this})||(g=!0,Me(y,(r,a)=>{const d=o[i+a];d&&(n=d,Me(r,(o,r)=>{const n=t[c+r];n&&(s=n,u?u({isCut:R,row:d,column:n,cellValue:o,$table:this}):l(n,nt)?e.setCheckboxRow(d,j(o)):l(n,st)?(!E&&(E=n),j(o)&&(T=d)):qe(d,n.property,o))}))}),e.setCellAreas([{type:Ge,startColumn:a,endColumn:s,startRow:d,endRow:n}],{column:a,row:d}))}}else p&&p.message({message:r(e,"vxe.pro.area.multiErr"),status:lt,id:tt});E&&(T?e.setRadioRow(T):e.clearRadioRow())}else 1<h.length&&p&&p.message({message:r(e,"vxe.pro.area.multiErr"),status:lt,id:tt});t&&e.emitEvent("cell-area-paste",{status:g,targetAreas:g?w:[]},t)}}function z(e,t,l){const{afterFullData:o,visibleColumn:r}=e,n=De(o,je(t)),s=De(r,je(l));for(let o=0,r=t.length;o<r;o++){const t=n+o;for(let o=0,a=l.length;o<a;o++){const l=s+o,i=b(e,t,l);if(i){const{row:e,col:t,rowspan:l,colspan:o}=i;if(n>e||n+r<e+l||s>t||s+a<t+o)return!1}}}return!0}function U(e,t,l,o){const{afterFullData:r,visibleColumn:n}=e,s=De(r,l),a=De(n,o);if(t.length){if(1===t.length){const{cols:l,rows:o}=je(t);if(1===l.length&&1===o.length)return!0;else{const t=De(r,je(o)),i=De(n,je(l)),d=b(e,s,a);if(d){const{row:e,col:r,rowspan:n,colspan:s}=d;if(t===e&&o.length===n&&i===r&&l.length===s)return!0}}}return!1}return!0}function G(e,t,l,o){const{afterFullData:r,visibleColumn:n}=e;let s=r[t];const a=n[l];if(s){const t=De(r,s),l=De(n,a),o=b(e,t,l);if(o){const{row:e}=o;t!==e&&(s=r[e])}}else if(o)return l--,0>l&&(l=n.length-1),t=r.length-1,G(e,t,l,o);return{offsetRow:s,offsetColumn:a}}function Z(e,t,l,o){const{afterFullData:r,visibleColumn:n}=e;let s=r[t];const a=n[l];if(s){const t=De(r,s),l=De(n,a),o=b(e,t,l);if(o){const{row:e,rowspan:l}=o;t!==e&&(s=r[e+l-1+1])}}else if(o)return l++,l>n.length-1&&(l=0),t=0,Z(e,t,l,o);return{offsetRow:s,offsetColumn:a}}function J(e,t,l,o){const{afterFullData:r,visibleColumn:n}=e,s=r[t];let a=n[l];if(a){const t=De(r,s),l=De(n,a),o=b(e,t,l);if(o){const{col:e}=o;l!==e&&(a=n[e])}}else if(o)return t--,0>t&&(t=r.length-1),l=n.length-1,J(e,t,l,o);return{offsetRow:s,offsetColumn:a}}function Q(e,t,l,o){const{afterFullData:r,visibleColumn:n}=e,s=r[t];let a=n[l];if(a){const t=De(r,s),l=De(n,a),o=b(e,t,l);if(o){const{col:e,colspan:t}=o;l!==e&&(a=n[l+t-1+1])}}else if(o)return t++,t>r.length-1&&(t=0),l=0,Q(e,t,l,o);return{offsetRow:s,offsetColumn:a}}function ee(e,t,l,o,r){const{afterFullData:n,visibleColumn:s}=e,a=t[l],{cols:i,rows:d}=a;let c=d[o];const f=i[r];if(c){const u=De(n,c),m=De(s,f),p=b(e,u,m);if(p){const{row:s,col:h}=p,g=De(d,n[s]);return u===s&&m===h?(o=g,c=d[r],{offsetArea:a,offsetRow:c,offsetColumn:f}):(m===h?(o=g,c=d[o]):(o=g-1,0>o&&(r--,0>r&&(r=i.length-1),o=d.length-1)),ee(e,t,l,o,r))}return{offsetArea:a,offsetRow:c,offsetColumn:f}}return r--,0>r?(l--,0>l&&(l=t.length-1),o=t[l].rows.length-1,r=t[l].cols.length-1):o=d.length-1,ee(e,t,l,o,r)}function te(e,t,l,o,r){const{afterFullData:n,visibleColumn:s}=e,a=t[l],{cols:i,rows:d}=a;let c=d[o];const f=i[r];if(c){const u=De(n,c),m=De(s,f),p=b(e,u,m);if(p){const{row:s,col:h,rowspan:g}=p,w=De(d,n[s]);return u===s&&m===h?(o=w,c=d[o],{offsetArea:a,offsetRow:c,offsetColumn:f}):(m===h?(o=w+g-1+1,c=d[o]):(o=w+g-1+1,o>d.length+1&&(r++,o>i.length-1&&(r=0),o=0)),te(e,t,l,o,r))}return{offsetArea:a,offsetRow:c,offsetColumn:f}}return o++,r++,o>d.length-1&&(o=0),r>i.length-1&&(l++,l>t.length-1&&(l=0),r=0),te(e,t,l,o,r)}function le(e,t,l,o,r,n,s){a(e);const i=13===e.keyCode,{afterFullData:d,visibleColumn:c}=t,{row:f,column:u}=l,m=t.getCellAreas(),p=!i||U(t,m,f,u);if(p){let e=De(d,f),l=De(c,u),a=f,m=u;const p=b(t,e,l);if(r){if(p){const{row:t}=p;e=t}const{offsetRow:o,offsetColumn:r}=G(t,e-1,l,i);a=o,m=r}else if(s){if(p){const{row:t,rowspan:l}=p;e=t+l-1}const{offsetRow:o,offsetColumn:r}=Z(t,e+1,l,i);a=o,m=r}else if(o){if(p){const{col:e}=p;l=e}const{offsetRow:o,offsetColumn:r}=J(t,e,l-1);a=o,m=r}else if(n){if(p){const{col:e,colspan:t}=p;l=e+t-1}const{offsetRow:o,offsetColumn:r}=Q(t,e,l+1);a=o,m=r}a&&m&&(t.scrollToRow(a,m),t.setCellAreas([{type:Ge,startColumn:m,endColumn:m,startRow:a,endRow:a}],{column:m,row:a}))}else{const e=t.getActiveCellArea(),{area:l,row:o,column:n}=e,s=_e(m,e=>e===l);let a=m[s],i=o,d=n;if(a){const{cols:e,rows:l}=a,c=De(l,o),f=De(e,n),{offsetArea:u,offsetRow:p,offsetColumn:h}=r?ee(t,m,s,c-1,f):te(t,m,s,c+1,f);a=u,i=p,d=h}i&&d&&t.scrollToRow(i,d).then(()=>{t.setActiveCellArea({area:a,column:d,row:i})})}}function oe(e,t,l,o,n){const{$vxe:s,afterFullData:i,visibleColumn:d}=t,{modal:c}=s;if(a(e),1>=o.length){const{row:e,column:o}=l,{row:r,column:s}=n,a=De(i,e),c=De(d,o),f=De(i,r),u=De(d,s),m=a>f?r:e,p=a>f?e:r,h=c>u?s:o,g=c>u?o:s;t.setCellAreas([{type:Ge,startRow:m,endRow:p,startColumn:h,endColumn:g}],{column:o,row:e})}else c&&c.message({message:r(t,"vxe.pro.area.multiErr"),status:lt,id:tt})}function re(e,t,l,o,r,n,s){a(e);const{afterFullData:i,visibleColumn:d}=t,{area:c,row:f,column:u}=l,{rows:m,cols:p}=c;let h=je(m),g=Pe(m),w=je(p),v=Pe(p);const C=De(i,f),x=De(d,u);let R=De(i,h),y=De(d,w),A=De(i,g),I=De(d,v),T=m,F=p;const $=b(t,C,x);if(r||s){if(r){if(Me(p,e=>{const l=De(d,e),o=b(t,A,l);if(o){const{row:e}=o;A=ye(A,e)}}),A>($?C+$.rowspan-1:C))A-=1,g=i[A];else{if(0>=R)return;R-=1,h=i[R]}}else if(Me(p,e=>{const l=De(d,e),o=b(t,R,l);if(o){const{row:e,rowspan:t}=o;R=Re(R,e+t-1)}}),R<($?C+$.rowspan-1:C))R+=1,h=i[R];else{if(A>=i.length-1)return;A+=1,g=i[A]}const{rows:e,cols:l}=E(t,$e(i,R,A+1),F);T=e,F=l}else{if(o){if(Me(m,e=>{const l=De(i,e),o=b(t,l,I);if(o){const{col:e}=o;I=ye(I,e)}}),I>($?x+$.colspan-1:x))I-=1,v=d[I];else{if(0>=y)return;y-=1,w=d[y]}}else if(Me(m,e=>{const l=De(i,e),o=b(t,l,y);if(o){const{col:e,colspan:t}=o;y=Re(y,e+t-1)}}),y<($?x+$.colspan-1:x))y+=1,w=d[y];else{if(I>=d.length-1)return;I+=1,v=d[I]}const{rows:e,cols:l}=E(t,T,$e(d,y,I+1));T=e,F=l}h=je(T),g=Pe(T),w=je(F),v=Pe(F),r||s?t.scrollToRow(je(m)===h?g:h):t.scrollToColumn(je(p)===w?v:w),t.setCellAreas([{type:Ge,startRow:h,endRow:g,startColumn:w,endColumn:v}],{column:u,row:f})}function ne(e,t,l,o,r){const{afterFullData:n,visibleColumn:s}=e,a=t[l],{cols:i,rows:d}=a,c=d[o];let f=i[r];if(f){const u=De(n,c),m=De(s,f),p=b(e,u,m);if(p){const{row:n,col:h}=p,g=De(i,s[h]);return u===n&&m===h?(r=g,f=i[r],{offsetArea:a,offsetRow:c,offsetColumn:f}):(u===n?(r=g,f=i[r]):(r=g-1,0>r&&(o--,0>o&&(o=d.length-1),r=i.length-1)),ne(e,t,l,o,r))}return{offsetArea:a,offsetRow:c,offsetColumn:f}}return o--,0>o?(l--,0>l&&(l=t.length-1),o=t[l].rows.length-1,r=t[l].cols.length-1):r=i.length-1,ne(e,t,l,o,r)}function se(e,t,l,o,r){const{afterFullData:n,visibleColumn:s}=e,a=t[l],{cols:i,rows:d}=a,c=d[o];let f=i[r];if(f){const u=De(n,c),m=De(s,f),p=b(e,u,m);if(p){const{row:n,col:h,colspan:g}=p,w=De(i,s[h]);return u===n&&m===h?(r=w,f=i[r],{offsetArea:a,offsetRow:c,offsetColumn:f}):(u===n?(r=w+g-1+1,f=i[r]):(r=w+g-1+1,r>i.length-1&&(o++,o>d.length-1&&(l++,l>t.length-1&&(l=0),o=0),r=0)),se(e,t,l,o,r))}return{offsetArea:a,offsetRow:c,offsetColumn:f}}return o++,o>d.length-1?(l++,l>t.length-1&&(l=0),o=0,r=0):r=0,se(e,t,l,o,r)}function ae(e,t,l,o){const{afterFullData:r,visibleColumn:n}=t,{column:s,row:a}=l,i=t.getCellAreas(),d=U(t,i,a,s);if(d){const e=De(r,a);let l=De(n,s),i=a,d=s;const c=b(t,e,l);if(c){const{col:e,colspan:t}=c;l=o?e:l+t-1}const{offsetRow:f,offsetColumn:u}=o?J(t,e,l-1,!0):Q(t,e,l+1,!0);i=f,d=u,i&&d&&(t.scrollToRow(i,d),t.setCellAreas([{type:Ge,startColumn:d,endColumn:d,startRow:i,endRow:i}],{column:d,row:i}))}else{const e=t.getActiveCellArea(),{area:l,row:r,column:n}=e,s=_e(i,e=>e===l);let a=i[s],d=r,c=n;if(a){const{cols:e,rows:l}=a,f=De(l,r),u=De(e,n),{offsetArea:m,offsetRow:p,offsetColumn:h}=o?ne(t,i,s,f,u-1):se(t,i,s,f,u+1);a=m,d=p,c=h}d&&c&&t.scrollToRow(d,c).then(()=>{t.setActiveCellArea({area:a,column:c,row:d})})}}function ie(e,t,l,o,r,n,s,a,i){const{clientX:c,clientY:f}=e,{$refs:u,scrollbarWidth:m}=t,p=u.leftContainer,h=u.rightContainer,g=c-o,w=f-r,v=a.scrollLeft,C=i.scrollTop;let x=g+(v-n);let b,R,y=0;p&&(y=p.offsetWidth,b=d(p)),h&&(R=d(h));const A=a.scrollWidth-a.offsetWidth-v;return b&&R?"left"===l?c>R.left?x=g+v+m+A:c<b.left+y&&(x=g):"right"===l?c<b.left+y?x=g-m-A-v:c<R.left&&(x=g-m-A):c<b.left+y?x=g-m-A-v:c>R.left&&(x=g+v+m+A):b?"left"===l?c<b.left+y&&(x=g):c<b.left+y&&(x=g-m-A-v):R&&("right"===l?c<R.left&&(x=g-m-A):c>R.left&&(x=g+v+m+A)),{moveX:g,moveY:w,offsetLeft:x,offsetTop:w+(C-s)}}function de(t,l){const{$vxe:o,fnrStore:n}=t,{modal:s}=o;if(n.showREErr=!1,n.findCellRE=null,n.isRE)try{n.findCellRE=new RegExp(l,n.isSensitive?"":"i")}catch(l){return n.showREErr=!0,s&&s.message({message:r(t,"vxe.pro.fnr.reError"),status:lt,id:tt}),!1}return!0}function ce(e,t,l,o){if(l.property){const{fnrStore:r,fnrOpts:n}=e,{isWhole:s,isRE:a,isSensitive:i,findCellRE:d}=r,{findMethod:c}=n;let f=Ye(Ke(t,l.property));return c?c({cellValue:f,isWhole:s,isRE:a,isSensitive:i,findValue:o,findRE:d}):o?a?d&&d.test(f):(i||(f=f.toLowerCase(),o=o.toLowerCase()),s?f===o:-1<De(f,o)):!f}return!1}function fe(e,t,l,o,r,n,s,a,i,d){const{afterFullData:c,visibleColumn:f}=e,u=[];for(let m=l,p=t.length;m<p;m++){const p=t[m],{rows:h,cols:g}=p,w=De(c,je(h)),v=De(f,je(g));for(let t=m===l?r:0,C=h.length;t<C;t++){const h=w+t;for(let w=m===l&&t===r?s:0,C=g.length;w<C;w++){const l=v+w,r=b(e,h,l);if(r){const{row:e,col:t}=r;if(h!==e||l!==t)continue}const s=c[h],g=f[l];if(ce(e,s,g,i)&&(u.push({_rowIndex:h,_columnIndex:l,offsetArea:p,offsetRow:s,offsetColumn:g}),!d))return u;if(m>=o&&t>=n&&w>=a)return u}}}return u}function ue(e,t,l,o,r,n,s){const{afterFullData:a,visibleColumn:i}=e,d=De(a,t),c=De(i,l)+1,f=De(a,o),u=De(i,r)+1,m=[];for(let p=0,h=a.length;p<h;p++){const t=d+p;for(let l=0==p?c:0,o=i.length;l<o;l++){const o=b(e,t,l);if(o){const{row:e,col:r}=o;if(t!==e||l!==r)continue}const r=a[t],d=i[l];if(ce(e,r,d,n)&&(m.push({_rowIndex:t,_columnIndex:l,offsetRow:r,offsetColumn:d}),!s))return m;if(p>=f&&l>=u)return m}}return m}function me(e){const{$vxe:t}=e,{modal:l}=t;e.$nextTick(()=>{const t=l.get("VXE_FNR");if(t){const l=i(t.$el,".vxe-table--fnr-form-input .vxe-input--inner");l&&(e.blur(),l.focus())}})}function pe(e,t){const{afterFullData:l,visibleColumn:o,fnrStore:r}=e,n=e.getActiveCellArea(),s=e.getCellAreas();let a=je(l),i=je(o),d=Pe(l),c=Pe(o);const f=Ye(r.findValue);let u=[],m=!0;if(r.showREErr=!1,!de(e,f))return u;if(n){const{row:l,column:o}=n;m=U(e,s,l,o),!t&&m&&(a=l,i=o)}if(!m){const{area:l,row:o,column:r}=n;let a=t?0:_e(s,e=>e===l);const i=s[a];if(i){const{cols:l,rows:n}=i;let d=t?0:De(n,o),c=t?0:De(l,r)+1,m=s.length-1;const p=s[m];let h=p.rows.length-1,g=p.cols.length-1;if(u=fe(e,s,a,m,d,h,c,g,f,t),!t&&(u.length||(m=a,h=d,g=c,a=0,d=0,c=0,u=fe(e,s,a,m,d,h,c,g,f,t)),u.length)){const{offsetArea:t,offsetRow:l,offsetColumn:o}=u[0];e.scrollToRow(l,o).then(()=>{e.setActiveCellArea({area:t,column:o,row:l})})}}}else if(u=ue(e,a,i,d,c,f,t),!t&&(u.length||(d=a,c=i,a=je(l),i=je(o),u=ue(e,a,i,d,c,f,t)),u.length)){const{offsetRow:t,offsetColumn:l}=u[0];e.scrollToRow(t,l),e.setCellAreas([{type:Ge,startRow:t,endRow:t,startColumn:l,endColumn:l}])}return u}function he(e){Me(e.fnrSearchList,e=>{e.isActived=!1})}function ge(e,t){const{$vxe:l,fnrOpts:o,fnrStore:n,fnrTabs:s}=e,{modal:a}=l,i=e.getActiveCellArea(),d=s.filter(e=>"find"===e.value&&o.isFind||"replace"===e.value&&o.isReplace),c=-1<d.indexOf(t)?t:d[0];c&&(n.visible=!0,n.activeTab=c.value,n.findValue=i?Ke(i.row,i.column.property):"",a.open({id:"VXE_FNR",title:r(e,"vxe.pro.fnr.title"),className:"vxe-table--ignore-areas-clear vxe-table--fnr",size:e.vSize,width:540,minWidth:540,height:370,minHeight:370,resize:!0,showZoom:!0,lockView:!1,mask:!1,escClosable:!0,events:{hide(){const{fnrStore:t}=e;t.visible=!1,t.showCount=!1,e.fnrSearchList=[],e.focus()},show(){me(e)}},slots:{default(t,l){const{fnrStore:o,fnrSearchList:n}=e,{$modal:s}=t,{activeTab:a,isRE:i,showREErr:c}=o,f=[],u="vxe-table--fnr-";return"replace"===a&&f.push(l("vxe-button",{on:{click:e.triggerFNRReplaceAllEvent}},r(e,"vxe.pro.fnr.btns.replaceAll")),l("vxe-button",{on:{click:e.triggerFNRReplaceEvent}},r(e,"vxe.pro.fnr.btns.replace"))),f.push(l("vxe-button",{on:{click:e.triggerFNRFindAllEvent}},r(e,"vxe.pro.fnr.btns.findAll")),l("vxe-button",{key:"findNext",props:{status:"primary"},on:{click:e.triggerFNRFindEvent}},r(e,"vxe.pro.fnr.btns.findNext")),l("vxe-button",{on:{click(){s.close()}}},r(e,"vxe.pro.fnr.btns.cancel"))),[l("div",{staticClass:u+"tabs"},d.map((t,o)=>l("span",{key:o,class:{"is--active":a===t.value},on:{click(l){e.triggerFNRTabChangeEvent(l,t)}}},r(e,t.label)))),l("div",{staticClass:u+"body"},[l("table",{staticClass:u+"form",attrs:{cellspacing:0,cellpadding:0,border:0}},[l("tbody",[l("tr",{staticClass:"is--visible"},[l("td",{staticClass:u+"form-title"},r(e,"vxe.pro.fnr.findTitle")),l("td",{staticClass:u+"form-content"},[l("vxe-input",{staticClass:u+"form-input",props:{value:o.findValue,clearable:!0},on:{input(e){o.findValue=Se(e)?e.value:e},keydown:e.triggerFNRFindKeydownEvent}})]),l("td",{staticClass:u+"form-filter",attrs:{rowspan:2}},[l("vxe-checkbox",{props:{value:i,content:r(e,"vxe.pro.fnr.filter.re")},on:{input(e){o.isRE=e}}}),l("vxe-checkbox",{props:{value:!i&&o.isWhole,disabled:i,content:r(e,"vxe.pro.fnr.filter.whole")},on:{input(e){o.isRE||(o.isWhole=e)}}}),l("vxe-checkbox",{props:{value:o.isSensitive,content:r(e,"vxe.pro.fnr.filter.sensitive")},on:{input(e){o.isSensitive=e}}})])]),l("tr",{class:{"is--visible":"replace"===o.activeTab}},[l("td",{staticClass:u+"form-title"},r(e,"vxe.pro.fnr.replaceTitle")),l("td",{staticClass:u+"form-content"},[l("vxe-input",{staticClass:u+"form-input",props:{value:o.replaceValue,clearable:!0},on:{input(e){o.replaceValue=Se(e)?e.value:e},keydown:e.triggerFNRReplaceKeydownEvent}})])])])])]),l("div",{staticClass:u+"footer"},f),l("div",{staticClass:u+"search"},[l("div",{staticClass:u+"search-header"},[l("div",r(e,"vxe.pro.fnr.header.seq")),l("div",r(e,"vxe.pro.fnr.header.cell")),l("div",r(e,"vxe.pro.fnr.header.value"))]),l("div",{staticClass:u+"search-body"},[l("vxe-list",{staticClass:u+"search-list",props:{data:n,autoResize:!0,scrollY:{gt:10,sItem:u+"find-item"}},scopedSlots:{default(t,l){const{items:o}=t;return o.map(t=>l("div",{key:t.seq,class:[u+"find-item",{"is--active":t.isActived}],on:{click(l){e.triggerFNRItemEvent(l,t)}}},[l("div",t.seq),l("div","Row:"+(t.row+1)+", Col:"+(t.col+1)),l("div",t.value)]))}}})]),l("div",{class:[u+"search-footer",{"is--error":c,"is--visible":c||o.showCount}]},c?r(e,"vxe.pro.fnr.reError"):r(e,"vxe.pro.fnr.recordCount",[o.findCount]))])]}}}))}function we(e,t){const{mouseConfig:l,mouseOpts:o,fnrStore:r}=e;return l&&o.area&&(e.fnrStore.visible?t.value!==r.activeTab&&(r.activeTab=t.value,me(e)):ge(e,t)),e.$nextTick()}function ve(e,o){const{$vxe:n,editStore:s,keyboardConfig:i,keyboardOpts:d,editConfig:c,editOpts:f,highlightCurrentRow:u,bodyCtxMenu:m,clipOpts:p,fnrOpts:h,filterStore:g}=o,{modal:w}=n,{actived:v}=s,{activeMethod:C}=f,{keyCode:b}=e,R=32===b,y=27===b,E=37===b,T=38===b,F=39===b,$=40===b,S=8===b,k=46===b,D=88===b,V=67===b,W=86===b,M=70===b,L=e.metaKey,N=e.ctrlKey,O=e.shiftKey,H=o.getActiveCellArea(),_=c&&v.column&&v.row;let B;if(g.visible)return void(y&&o.closeFilter());if(R&&H&&i&&d.isChecked&&(l(H.column,nt)?(a(e),o.handleToggleCheckRowEvent(e,{row:H.row,column:H.column})):l(H.column,st)&&(a(e),o.triggerRadioRowEvent(e,{row:H.row,column:H.column}))),y&&c)o.closeMenu(),o.closeFilter(),x(e,o);else if(113===b&&c&&H){if(!_){const{offsetRow:l,offsetColumn:r}=I(o,H.row,H.column);t(r.editRender)&&(B={row:l,column:r,cell:o.getCell(l,r)},a(e),o.handleActived(B,e))}}else if(93===b)o._keyCtx=H&&H.row&&H.column&&m.length,clearTimeout(o.keyCtxTimeout),o.keyCtxTimeout=setTimeout(()=>{o._keyCtx=!1},1e3);else if(N&&77===b&&i&&d.isMerge&&H)_||(a(e),A(o,e),o.clearCopyCellArea());else if(N&&i&&d.isFNR&&(M&&h.isFind||72===b&&h.isReplace)){a(e);const t=o.fnrTabs[M?0:1];o.fnrStore.visible?o.triggerFNRTabChangeEvent(e,t):o.triggerFNROpenEvent(e,t.value),x(e,o)}else if(13===b&&i&&d.isEnter){const{column:t,row:l}=v;if(t&&l){const{offsetRow:r,offsetColumn:n}=I(o,l,t);a(e),o.clearActived(),d.enterToTab?ae(e,o,{row:r,column:n},O):le(e,o,{row:r,column:n},!1,O,!1,!O)}else H&&(a(e),d.enterToTab?ae(e,o,H,O):le(e,o,H,!1,O,!1,!O))}else if((E||T||F||$)&&i&&d.isArrow){if(!_)if(!H)(T||$)&&u&&o.moveCurrentRow(T,$,e);else if(O){const t=o.getCellAreas();1>=t.length?re(e,o,H,E,T,F,$):w&&w.message({message:r(o,"vxe.pro.area.multiErr"),status:lt,id:tt})}else le(e,o,H,E,T,F,$);}else if(9===b&&i&&d.isTab){const{column:t,row:l}=v;if(l&&t){const{offsetRow:r,offsetColumn:n}=I(o,l,t);a(e),o.clearActived(),ae(e,o,{row:r,column:n},O)}else H&&(a(e),ae(e,o,H,O))}else if((k||S)&&i&&d.isDel){const{delMethod:l,backMethod:r}=d;if(!_)if(S&&H){const{column:l,row:n}=H;if(n&&l){const{offsetRow:s,offsetColumn:a}=I(o,n,l);t(a.editRender)&&(B={row:s,rowIndex:o.getRowIndex(s),column:a,columnIndex:o.getColumnIndex(a),cell:o.getCell(s,a),$table:o},(!C||C(B))&&(r?r(B):(B.cell=o.getCell(s,a),qe(s,a.property,null),o.handleActived(B,e))))}}else if(k){const e=o.getCellAreas();Me(e,e=>{const{rows:r,cols:n}=e;Me(r,e=>{Me(n,r=>{t(r.editRender)&&(B={row:e,rowIndex:o.getRowIndex(e),column:r,columnIndex:o.getColumnIndex(r),cell:o.getCell(e,r),$table:o},(!C||C({row:e,column:r}))&&(l?l(B):qe(e,r.property,null)))})})})}}else if(i&&N&&65===b)_||o.triggerAllCellAreaEvent(e);else if(i&&d.isClip&&N&&ze.msie&&(D||V||W)){if(!_){const t=window.clipboardData;if(t&&Y(o,e))if(D&&!1!==p.isCut||V&&!1!==p.isCopy){const{text:l}=X(o,e,D);t.setData("text",l)}else if(W&&!1!==p.isPaste){const l=t.getData("text");P(o,e,{text:l})}}}else if(i&&c&&d.isEdit&&!N&&!L&&(R||48<=b&&57>=b||65<=b&&90>=b||96<=b&&111>=b||186<=b&&192>=b||219<=b&&222>=b)&&!_&&H){const{column:l,row:r}=H;if(r&&l){const{offsetRow:n,offsetColumn:s}=I(o,r,l),{editMethod:a}=d;t(s.editRender)&&(B={row:n,rowIndex:o.getRowIndex(n),column:s,columnIndex:o.getColumnIndex(s),cell:o.getCell(n,s),$table:o},(!C||C(B))&&(a?a(B):(qe(n,s.property,null),o.handleActived(B,e))))}}}function Ce(e,t,l){const{fixed:o}=l,a=t.getCellAreas(),c=Ne(a,e=>e.type===Ge);if(n(t),c){const a=document.onmousemove,h=document.onmouseup,{$el:g,$refs:w,$vxe:v,afterFullData:C,visibleColumn:x}=t,{modal:R}=v,y=e.clientX,A=e.clientY,{tableBody:I,rightBody:E,tableFooter:$}=w,S=I.$el,V=E?E.$el:null,W=$?$.$el:null,L=V||S,N=W||S,{cols:O,rows:H}=c,_=je(O),Y=je(H),X=Pe(O),B=Pe(H),K=c.top,q=c.left,j=c.width,P=c.height,U=p(t,_.fixed),G=i(U,".vxe-table--cell-area"),Z=G.children[2],J=L.scrollTop,Q="left"===o?0:N.scrollLeft,ee=De(C,B),te=De(x,X),le=b(t,ee,te);let oe,re=1,ne=1,se=0,ae=0,de=null,ce=null;const fe=i(U,".vxe-body--row"),ue=fe.firstChild,me={el:null,type:Je,top:0,left:0,width:0,height:0},pe=e=>{const{offsetTop:l,offsetLeft:r}=ie(e,t,o,y,A,Q,J,N,L),n=be(l),s=be(r),a=N.scrollWidth,i=L.scrollHeight;let d=P,c=j,f=K,u=q;switch(oe){case"top":if(l<-P){const e=ye(K,T(t,Y,_,le,!1,n-P));f-=e,d+=e}break;case"bottom":0<l&&(d+=ye(i-K-P,T(t,B,X,le,!0,n)));break;case"left":if(r<-j){const e=ye(q,F(t,Y,_,le,!1,s-j));u-=e,c+=e}break;case"right":0<r&&(c+=ye(a-q-j,F(t,B,X,le,!0,s)));}me.height=d,me.width=c,me.left=u,me.top=f,me.cols=k(t,ue,me),me.rows=D(t,fe,me),Fe(me,M(t,me))},he=e=>{n(t),t._msTout=setTimeout(()=>{if(t._msTout){const{clientHeight:l,clientWidth:o}=S,{scrollLeft:r,scrollWidth:s}=N,{scrollTop:a,scrollHeight:i}=L;let d,c;null!==de&&(de?a+l<i&&(d=a+re*ot):a&&(d=a-re*ot)),null!==ce&&(ce?r+o<s&&(c=r+ne*ot):r&&(c=r-ne*ot)),Ee(c)||Ee(d)?(t.scrollTo(c,d),he(e),pe(e)):n(t)}},50)};document.onmousemove=e=>{s(e);const{clientY:l,clientX:r}=e,{clientHeight:a,clientWidth:i}=S,{offsetTop:c,offsetLeft:f}=ie(e,t,o,y,A,Q,J,N,L),u=d(S),m=u.top,p=u.left,h=q+j+f,g=K+P+c;let w;de=null,ce=null,se=l,ae=r,se<m?(w=!0,de=!1,re=m-se):se>m+a&&(w=!0,de=!0,re=se-m-a),ae<p?(w=!0,ce=!1,ne=p-ae):ae>p+i&&(w=!0,ce=!0,ne=ae-p-i),h<q?g>K&&g<K+P&&(oe="left"):h>q+j?g>K&&g<K+P&&(oe="right"):g<K?oe="top":g>K+P&&(oe="bottom"),w?!t._msTout&&he(e):n(t),pe(e)},document.onmouseup=()=>{document.onmousemove=a,document.onmouseup=h,n(t),m(g,"drag--extend-range");const l=D(t,fe,me),o=k(t,ue,me);z(t,l,o)?(c.rows=l,c.cols=o):R&&R.message({message:r(t,"vxe.pro.area.extendErr"),status:lt,id:tt}),Fe(c,M(t,c)),f(Z),t._isCAEd=!1,t.emitEvent("cell-area-extension-end",{rows:c.rows,cols:c.cols},e)},u(g,"drag--extend-range"),t._isCAEd=!0,t.emitEvent("cell-area-extension-start",l,e)}}function xe(e,t,l){const{$el:o,$refs:r,editStore:g,editOpts:v,_isCAEd:C}=t,{actived:x}=g,{fixed:b,row:y,column:A,cell:I}=l;if(C)return void a(e);if(x.row===y&&("cell"===v.mode?x.column===A:"row"===v.mode))return;const T=2===e.button,F=e.ctrlKey,V=e.shiftKey,W=I.offsetTop,L=I.offsetLeft,N=I.offsetWidth,O=I.offsetHeight;let H=t.getCellAreas();const _=t.getActiveCellArea();if(n(t),!T&&V&&_)return void oe(e,t,_,H,l);if(t.clearActived(e),t.clearSelected(e),!T||!Be(H,e=>e.top<=W&&W<e.top+e.height&&e.left<=L&&L<e.left+e.width)){F||(H=[],t.clearCellAreas());const a=document.onmousemove,g=document.onmouseup,v=e.clientX,C=e.clientY,{tableBody:x,rightBody:V,tableFooter:_}=r,Y=R(e,I),X=x.$el,B=V?V.$el:null,K=_?_.$el:null,q=B||X,j=K||X,P=p(t,A.fixed),z=i(P,".vxe-table--cell-area"),U=z.children,G=U[0],Z=U[2],J=U[3],Q=I.parentNode,ee=i(P,".vxe-body--row"),te=ee.firstChild,le=q.scrollTop,oe="left"===b?0:j.scrollLeft;let re=null,ne=null,se=1,ae=1,de=0,ce=0;const fe={el:null,type:F?Qe:Ge,cols:[],rows:[],top:0,left:0,width:0,height:0},ue={el:null,type:et,area:fe,column:A,row:y,top:I.offsetTop,left:I.offsetLeft,width:I.offsetWidth,height:I.offsetHeight};if(F){const e=b?`${b}El`:"el",t=Ne(H,e=>e.type===Ge);t&&(t.type=Qe,t[e]=h(J),w(t[e],t)),fe[e]=h(J)}t.setActiveCellArea(ue),H.push(fe),t.activeCellArea=ue;const me=e=>{const{offsetTop:l,offsetLeft:o}=ie(e,t,b,v,C,oe,le,j,q),r=j.scrollWidth,n=q.scrollHeight,s=0<=l,a=0<=o;let i,d,u=be(l),m=be(o);s?(i=$(t,y,A,Q,s,u+Y.offsetY),u=ye(n-W,i.moveHeight)):(i=$(t,y,A,Q,s,u-Y.offsetY),u=ye(W+O,i.moveHeight)),a?(d=S(t,y,A,I,a,m+Y.offsetX),m=ye(r-L,d.moveWidth)):(d=S(t,y,A,I,a,m-Y.offsetX),m=ye(L+N,d.moveWidth));const{rows:p,cols:h}=E(t,i.rows,d.cols);fe.rows=p,fe.cols=h,f(Z),F?(f(G),c(J)):(f(J),c(G)),Fe(fe,M(t,fe))},pe=e=>{n(t),t._msTout=setTimeout(()=>{if(t._msTout){const{clientHeight:l,clientWidth:o}=X,{scrollLeft:r,scrollWidth:s}=j,{scrollTop:a,scrollHeight:i}=q;let d,c;null!==re&&(re?a+l<i&&(d=a+se*ot):a&&(d=a-se*ot)),null!==ne&&(ne?r+o<s&&(c=r+ae*ot):r&&(c=r-ae*ot)),Ee(c)||Ee(d)?(t.scrollTo(c,d),pe(e),me(e)):n(t)}},50)};T||(document.onmousemove=e=>{s(e);const{clientY:l,clientX:o}=e,{clientHeight:r,clientWidth:a}=X,i=d(X),c=i.top,f=i.left;let u;re=null,ne=null,de=l,ce=o,de<c?(u=!0,re=!1,se=c-de):de>c+r&&(u=!0,re=!0,se=de-c-r),ce<f?(u=!0,ne=!1,ae=f-ce):ce>f+a&&(u=!0,ne=!0,ae=ce-f-a),u?!t._msTout&&pe(e):n(t),me(e)}),document.onmouseup=()=>{document.onmousemove=a,document.onmouseup=g,n(t),fe.cols=k(t,te,fe),fe.rows=D(t,ee,fe),m(o,"drag--area"),t.emitEvent("cell-area-selection-end",{rows:fe.rows,cols:fe.cols},e)},u(o,"drag--area"),me(e),t.cellAreas=H,t.emitEvent("cell-area-selection-start",l,e)}}const{uniqueId:Ie,isNumber:Ee,browse:Te,assign:Fe,slice:$e,isObject:Se,isBoolean:ke,indexOf:De,includes:Ve,eachTree:We,arrayEach:Me,hasOwnProp:Le,find:Ne,findLast:Oe,findLastIndexOf:He,findIndexOf:_e,toString:Ye,filter:Xe,some:Be,get:Ke,set:qe,first:je,last:Pe}=e,ze=Te(),Ue="\r\n",Ge="main",Ze="copy",Je="extend",Qe="multi",et="active",tt="operErr",lt="error",ot=4,rt="seq",nt="checkbox",st="radio",at={children:"children"},it="\t";"undefined"!=typeof window&&(window.VXETableMixin={data(){return{fnrStore:{visible:!1,activeTab:null,findValue:null,replaceValue:null,isRE:!1,isWhole:!1,isSensitive:!1,showREErr:!1,showCount:!1,findCount:0},fnrSearchList:[],fnrTabs:[{value:"find",label:"vxe.pro.fnr.tabs.find"},{value:"replace",label:"vxe.pro.fnr.tabs.replace"}]}},beforeDestroy(){const{$vxe:e}=this;e.modal.close("VXE_FNR")},methods:{handleHeaderCellAreaEvent(e,t){const{mouseConfig:l,mouseOpts:o,afterFullData:r}=this,{triggerFilter:n,column:s}=t;l&&o.area&&(n?this.clearCopyCellArea():r.length?(this.clearActived(),setTimeout(()=>{const e=je(r),t=[];We([s],e=>{e.children&&e.children.length||t.push(e)},at);const l=je(t),o=Pe(t);this.$nextTick(()=>{this.clearCopyCellArea(),this.setCellAreas([{type:Ge,startColumn:l,endColumn:o,startRow:e,endRow:Pe(r)}],{column:l,row:e})}),this.scrollToRow(e,l)},10)):(this.clearCellAreas(),this.clearCopyCellArea()))},handleUpdateCellAreas(){const e=this.getActiveCellArea(),t=this.getCellAreas();t.length&&this.$nextTick(()=>{const{copyAreaMpas:l,afterFullData:o,visibleColumn:r}=this,n=[];if(Me(t,e=>{const{type:t,cols:l,rows:s}=e,a=Ne(l,e=>Ve(r,e)),i=Ne(s,e=>Ve(o,e));if(i&&a){const e=Oe(l,e=>Ve(r,e)),d=Oe(s,e=>Ve(o,e));n.push({type:t,startColumn:a,endColumn:e,startRow:i,endRow:d})}}),n.length?(e&&(e.area=n[De(t,e.area)]),this.setCellAreas(n,e)):this.clearCellAreas(),l){const{cols:e,rows:t}=l,n=_e(e,e=>Ve(r,e)),s=_e(t,e=>Ve(o,e));if(-1<n&&-1<s){const a=He(e,e=>Ve(r,e)),i=He(t,e=>Ve(o,e));l.cols=$e(e,n,a+1),l.rows=$e(t,s,i+1),this.copyAreaMpas=M(this,l)}}})},_getCellAreas(){return this.cellAreas||[]},_clearCellAreas(){return v(this,e=>{const t=e.children,l=t[0],o=t[2],r=t[3],n=t[4];if(f(l),f(l.firstChild),f(o),f(n),r){const e=r.children;for(let t=e.length-1;0<=t;t--)r.removeChild(e[t])}}),this.activeCellArea=null,this.cellAreas=[],this.$nextTick()},_getCopyCellArea(){return this.copyAreaMpas||null},_clearCopyCellArea(){return C(this,Ze),this.copyAreaMpas=null,this.$nextTick()},_setCellAreas(e,t){if(e&&e.length){this.clearCellAreas();let l=Ne(e,e=>e.type===Ge),o=[];if(l||(l=Ne(e,e=>!e.type),l&&(l.type=Ge)),l){const e=L(this,l);o=e?[e]:[]}else{const t=[];Me(e,e=>{const l=L(this,e);l&&t.push(l)}),o=t}if(o.length){const l=o[0],{cols:r,rows:n}=l;t&&t.area&&(t.area=o[De(e,t.area)]||l),this.setActiveCellArea(Fe({type:et,area:l,column:Pe(r),row:Pe(n)},t))}this.cellAreas=o}return this.$nextTick()},_setActiveCellArea(e){return C(this,et),e.type=et,this.activeCellArea=V(this,e),this.$nextTick()},_getActiveCellArea(){return this.activeCellArea||null},handleKeyboardEvent(e){ve(e,this)},_openFind(){const{fnrTabs:e,fnrOpts:t}=this;return t.isFind?we(this,e[0]):this.$nextTick()},_openReplace(){const{fnrTabs:e,fnrOpts:t={}}=this;return t.isReplace?we(this,e[1]):this.$nextTick()},triggerFNROpenEvent(e,t){const{fnrStore:l,fnrTabs:o}=this;we(this,Ne(o,e=>e.value===t)||o[0]),this.emitEvent("open-fnr",{tab:l.activeTab},e)},triggerFNRTabChangeEvent(e,t){const{fnrStore:l}=this;l.activeTab!==t.value&&(l.activeTab=t.value,me(this),this.emitEvent("change-fnr",{tab:l.activeTab},e))},triggerFNRFindKeydownEvent(e){const{$event:t}=e;13===t.keyCode&&("replace"===this.fnrStore.activeTab?this.triggerFNRReplaceEvent(e):this.triggerFNRFindEvent(e))},triggerFNRFindEvent(){const{$vxe:e,fnrStore:t,fnrOpts:l}=this,{modal:o}=e,{beforeFindMethod:n}=l,s=Ye(t.findValue);if(!t.showREErr&&!(n&&!1===n({findValue:s,$table:this}))){const e=pe(this);he(this),!e.length&&o&&o.message({message:r(this,"vxe.pro.fnr.notCell"),status:"warning",id:"operWarn"})}},triggerFNRFindAllEvent(){const{$vxe:e,fnrStore:t,fnrOpts:l}=this,{modal:o}=e,{beforeFindMethod:n}=l,s=Ye(t.findValue);if(this.fnrSearchList=[],t.showREErr)return;if(n&&!1===n({findValue:s,$table:this}))return;const a=pe(this,!0);t.findCount=a.length,t.showCount=!0,a.length?setTimeout(()=>{this.fnrSearchList=a.map((e,t)=>{const l=Ye(Ke(e.offsetRow,e.offsetColumn.property));return{seq:t+1,row:e._rowIndex,col:e._columnIndex,isActived:!1,value:l||r(this,"vxe.pro.fnr.empty")}})},10):o&&o.message({message:r(this,"vxe.pro.fnr.notCell"),status:"warning",id:"operWarn"})},triggerFNRItemEvent(e,t){const{afterFullData:l,visibleColumn:o}=this,r=l[t.row],n=o[t.col];r&&n&&(he(this),t.isActived=!0,this.scrollToRow(r,n),this.setCellAreas([{type:Ge,startRow:r,endRow:r,startColumn:n,endColumn:n}]))},triggerFNRReplaceKeydownEvent(e){const{$event:t}=e;13===t.keyCode&&this.triggerFNRReplaceEvent(e)},triggerFNRReplaceEvent(){const{$vxe:e,fnrStore:t,fnrOpts:l}=this,{modal:o}=e,n=this.getActiveCellArea(),s=Ye(t.findValue),a=Ye(t.replaceValue);if(de(this,s)&&(s||a))if(n){const{beforeReplaceMethod:e,replaceMethod:t}=l,{row:i,column:d}=n;if(e&&!1===e({findValue:s,replaceValue:a,$table:this}))return;ce(this,i,d,s)?(t?t({row:i,column:d,cellValue:a}):qe(i,d.property,a),pe(this),he(this)):o&&o.message({message:r(this,"vxe.pro.fnr.notCell"),status:"warning",id:"operWarn"})}else o&&o.message({message:r(this,"vxe.pro.fnr.notCell"),status:"warning",id:"operWarn"})},triggerFNRReplaceAllEvent(){const{$vxe:e,fnrStore:t,fnrOpts:l}=this,{modal:o}=e,n=Ye(t.findValue),s=Ye(t.replaceValue);if(n||s){const{beforeReplaceMethod:e,replaceMethod:a}=l;if(t.showREErr)return;if(e&&!1===e({findValue:n,replaceValue:s,$table:this}))return;const i=pe(this,!0);he(this),i.length?(Me(i,e=>{const{offsetRow:t,offsetColumn:l}=e;a?a({row:t,column:l,cellValue:s}):qe(t,l.property,s)}),o&&o.message({message:r(this,"vxe.pro.fnr.replaceSuccess",[i.length]),status:"success",id:"operEnd"})):o&&o.message({message:r(this,"vxe.pro.fnr.notCell"),status:"warning",id:"operWarn"})}},triggerAllCellAreaEvent(e){const{afterFullData:t,visibleColumn:l}=this;t.length&&l.length&&(a(e),this.setCellAreas([{type:Ge,startRow:je(t),endRow:Pe(t),startColumn:je(l),endColumn:Pe(l)}]))},handleCopyCellAreaEvent(e){const{clipOpts:t}=this;if(!1!==t.isCopy&&Y(this,e)){const{text:t,html:l}=X(this,e,!1),o=e.clipboardData;o.setData("text/plain",t),o.setData("text/html",l)}},handleCutCellAreaEvent(e){const{clipOpts:t}=this;if(!1!==t.isCut&&Y(this,e)){const{text:t,html:l}=X(this,e,!0),o=e.clipboardData;o.setData("text/plain",t),o.setData("text/html",l)}},handlePasteCellAreaEvent(e){const{clipOpts:t}=this;if(!1!==t.isPaste&&Y(this,e)){const t=e.clipboardData,l=t.getData("text/plain");P(this,e,{text:l})}},_copyCellArea(){return X(this,null,!1)},_cutCellArea(){return X(this,null,!0)},_pasteCellArea(){const{$vxe:e}=this,{clipboard:t}=e;return t&&(t.html||t.text)&&P(this,null,t),this.$nextTick()},triggerCellExtendMousedownEvent(e,t){Ce(e,this,t)},handleCellAreaEvent(e,t){xe(e,this,t)}}})})(XEUtils);