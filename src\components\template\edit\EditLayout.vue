<template>
  <div class="edit-page">
    <slot
      name="remoteJs"
      :pageData="pageData"
      :resultData="resultData"
    >
      <a-spin :spinning="confirmLoading">
        <div class="page-header">
          <a-row>
            <a-col
              class="desc-col"
              :style="getDefaultColor"
              :span="6"
            >
              <slot name="title-page">
                <span>{{ headerTitle || `${$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')}${currentPageName}` }}</span>
              </slot>
            </a-col>
            <a-col
              class="btn-col"
              :span="18"
            >
              <template v-if="displayModel !== 'tab'">
                <a-button
                  v-for="(btn, idx) in getAuthCodeBtns(pageData.publicBtn || pageData.footerButtons)"
                  :key="`pub_btn_${idx}`"
                  :type="btn.type"
                  v-show="btn.showCondition ? btn.showCondition() : true"
                  v-debounce="{ method: 'customBtnClick', event: 'click', args: btn }"
                >
                  {{ btn.title }}
                </a-button>
              </template>
              <template v-els>
                <a-button
                  v-for="(btn, idx) in getAuthCodeBtns(pageData.extraBtn)"
                  :key="`pub_btn_${idx}`"
                  :type="btn.type"
                  v-show="btn.showCondition ? btn.showCondition() : true"
                  v-debounce="{ method: 'customBtnClick', event: 'click', args: btn }"
                >
                  {{ btn.title }}
                </a-button>
              </template>
            </a-col>
          </a-row>
        </div>
        <div
          class="page-header"
          v-if="displayModel === 'tab'"
        >
          <a-steps
            v-model="currentStep"
            @change="stepChange"
            size="small"
          >
            <a-step
              v-for="group in pageData.groups"
              :key="group.groupCode"
              :title="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)"
            />
          </a-steps>
        </div>
        <div
          class="page-content"
          :style="{ paddingBottom: displayModel !== 'tab' ? '12px' : '0' }"
        >
          <template v-if="displayModel === 'tab'">
            <div
              v-for="(group, idx) in pageData.groups"
              :key="group.groupCode"
              v-show="currentStep === idx"
              :class="[group.type === 'grid' ? 'edit-grid-box' : 'edit-form-box', group.split && group.type === 'grid' ? 'pageSplit' : '', `${group.groupCode}Tab`]"
            >
              <template v-if="!group.type">
                <slot
                  :name="`${group.groupCode}Tab`"
                  :pageData="pageData"
                  :resultData="resultData"
                >
                  <a-form-model
                    :ref="group.groupCode"
                    :model="form"
                    :rules="group.custom.validateRules"
                    class="ant-advanced-rule-form"
                    layout="inline"
                    v-bind="layout"
                  >
                    <a-row :getterr="12">
                      <a-col
                        v-for="(item, sIdx) in group.custom.formFields"
                        :key="`col_${group.groupCode}_${item.fieldName}_${sIdx}`"
                        :class="fieldClassByCol(item)"
                        :span="setColSpan(item)"
                      >
                        <template v-if="operateLabelsByType.includes(item.fieldType)">
                          <a-form-model-item :prop="item.fieldName">
                            <a-input
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              type="hidden"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </a-form-model-item>
                        </template>
                        <a-form-model-item
                          v-else
                          :prop="item.fieldName"
                        >
                          <template #label>
                            <span>
                              <a-tooltip :title="$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)">{{ $srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel) }} </a-tooltip>
                              <a-tooltip
                                v-if="item.helpText"
                                :title="item.helpText"
                              >
                                <a-icon type="question-circle-o" />
                              </a-tooltip>
                            </span>
                          </template>

                          <template v-if="['input', 'password', 'textArea'].includes(item.fieldType)">
                            <a-input
                              :disabled="item.disabled"
                              autocomplete="new-password"
                              :type="item.fieldType === 'input' ? 'text' : item.fieldType.toLowerCase()"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                              :title="item.disabled && item.fieldType == 'input' ? form[item.fieldName] : ''"
                              v-model="form[item.fieldName]"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'select'">
                            <m-select
                              v-model="form[item.fieldName]"
                              returnTitle
                              :configData="item"
                              :options="item.options"
                              :disabled="item.disabled"
                              :dictCode="item.dictCode"
                              :currentEditRow="currentEditRow"
                              :getPopupContainer="(node) => node.parentNode || document.body"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @afterClearCallBack="handleSelectModalAfterClear"
                              @afterSelectCallBack="handleSelectAfterSelect"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'multiple'">
                            <a-tooltip
                              v-if="item.disabled"
                              :title="form[item.fieldName + '_dictText'] || form[item.fieldName]"
                            >
                              <span class="ant-form-item-children">
                                <span
                                  class="ant-input ant-input-disabled"
                                  style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
                                >
                                  {{ form[item.fieldName + '_dictText'] || form[item.fieldName] }}
                                </span>
                              </span>
                            </a-tooltip>
                            <m-select
                              v-else
                              v-model="form[item.fieldName]"
                              mode="multiple"
                              :configData="item"
                              :dictCode="item.dictCode"
                              :currentEditRow="currentEditRow"
                              :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'cascader'">
                            <m-cascader
                              v-model="form[item.fieldName]"
                              changeOnSelect
                              :mode="item.dictCode"
                              :disabled="item.disabled"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'number'">
                            <a-input-number
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              v-bind="item.extend || {}"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                              @blur="() => inputNumberBlurMethod({ value: form[item.fieldName], fieldLabel: item.fieldLabel, type: 'form', fieldType: item.fieldType, form, item })"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'currency'">
                            <m-currency
                              v-model="form[item.fieldName]"
                              :configData="item"
                              :disabled="item.disabled"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="(val) => changeInputValue(val, item)"
                            />
                          </template>

                          <template v-else-if="['date', 'currentDate'].includes(item.fieldType)">
                            <a-date-picker
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              :disabledDate="item.fieldType === 'currentDate' ? disabledDate : null"
                              :show-time="item.dataFormat && item.dataFormat.length > 10 ? { format: 'HH:mm:ss', defaultValue: defalutValueTime} : false"
                              :valueFormat="item.dataFormat || 'YYYY-MM-DD'"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'switch'">
                            <m-switch
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              :configData="item"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'treeSelect'">
                            <m-tree-select
                              v-model="form[item.fieldName]"
                              allowClear
                              :configData="item"
                              :disabled="item.disabled"
                              :multiple="(item.extend && item.extend.multiple) || false"
                              :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                              :sourceUrl="item.dictCode"
                              :sourceMap="item.sourceMap"
                              :sourceData="item.extend && item.extend.sourceData"
                              :showEmptyNode="item.showEmptyNode"
                              :treeNodeFilterProp="item.extend && item.extend.treeNodeFilterProp"
                              :treeDefaultExpandAll="item.extend && item.extend.treeDefaultExpandAll"
                              :parentNodeSelectable="item.extend && (item.extend.parentNodeSelectable || true)"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              :getPopupContainer="(node) => node.parentNode || document.body"
                              @afterClearCallBack="handleSelectModalAfterClear"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'remoteSelect'">
                            <m-remote-select
                              v-model="form[item.fieldName]"
                              :config="item"
                              :pageData="pageData"
                              :form="form"
                              :currentStep="currentStep"
                              :disabled="item.disabled"
                              :getPopupContainer="(node) => node.parentNode || document.body"
                              @afterClearCallBack="() => handleSelectModalAfterClear(item?.extend?.afterClearCallBack)"
                              @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'selectModal'">
                            <m-select-modal
                              v-model="form[item.fieldName]"
                              :config="item"
                              :pageData="pageData"
                              :form="form"
                              :currentStep="currentStep"
                              @afterClearCallBack="handleSelectModalAfterClear"
                              @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                            />
                          </template>

                          <!-- 自定义弹窗选择，抛出事件在自己的业务下编写自己业务需要的弹窗 -->
                          <template v-else-if="item.fieldType === 'customSelectModal'">
                            <div @click="customSelectModel(item)">
                              <a-input
                                v-model="group.formModel[item.fieldName]"
                                readOnly
                                allowClear
                              />
                            </div>
                          </template>

                          <template v-else-if="item.fieldType === 'ladderPrice'">
                            <ladder-price
                              v-model="form[item.fieldName]"
                              :config="item"
                              :pageData="pageData"
                              :form="form"
                              @afterClearCallBack="handleLadderPriceAfterClear"
                              @ok="(rows) => handleLadderPriceAfterSelect(item, rows)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'richEditorModel'">
                            <rich-editor-model
                              :value="form[item.fieldName]"
                              :disabled="item.disabled"
                              :showTooltip="(item.extend && item.extend.showTooltip) || false"
                              @handleSureClick="(content) => (form[item.fieldName] = content)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'image'">
                            <m-upload
                              :value.sync="form[item.fieldName]"
                              :accept="accept2"
                              :headers="tokenHeader"
                              :multiple="item.extend && item.extend.multiple"
                              :limit="item.extend && item.extend.limit"
                              :disabled="item.disabled"
                              :data="{ businessType: (item.extend && item.extend.businessType) || item.dictCode, headId: form.id, sourceNumber: item?.extend?.sourceNumber || form.id || '', actionRoutePath: item?.extend?.actionRoutePath || '' }"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'hiddenField'">
                            <a-input
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              type="hidden"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'float'">
                            <m-float
                              v-model="form[item.fieldName]"
                              :configData="item"
                              :disabled="item.disabled"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>
                        </a-form-model-item>
                      </a-col>
                    </a-row>
                  </a-form-model>
                  <slot
                    :name="group.groupCode"
                    :pageData="pageData"
                    :resultData="resultData"
                  />
                </slot>
              </template>
              <template v-else-if="group.type === 'grid'">
                <slot
                  :name="`${group.groupCode}Tab`"
                  :pageData="pageData"
                  :resultData="resultData"
                >
                  <div :class="['tableWrap', group.split ? 'customTable' : '', isDynamics(group) ? 'lineWrap' : '']">
                    <vxe-grid
                      :key="group.custom.ref === 'saleDeliverySubList' || group.custom.ref === 'purchaseDeliverySubList' ? `pageContentKey_${pageContentKey}` : group.custom.ref"
                      :ref="group.custom.ref"
                      v-bind="getGridConfig(group)"
                      :editConfig="group.custom.editConfig || gridCustomEditConfig"
                      :editRules="group.custom.rules"
                      :columns="resetCustomCols(group.custom.columns, group)"
                      :menuConfig="menuConfig"
                      :show-footer="showGridFooter"
                      :footer-method="
                        ({ columns, data }) => {
                          return footerMethod({ group, columns, data })
                        }
                      "
                      @menu-click="menuClickEvent"
                      @cell-menu="cellMenuEvent"
                      @cell-click="cellClickEvent"
                      @cell-dblclick="cellDblclickEvent"
                      @edit-actived="editActivedEvent"
                      @checkbox-all="checkboxAll"
                      @checkbox-change="checkboxChange"
                    >
                      <!-- 表格代码编辑器 -->
                      <template #code_editor_col_render="{ row, column }">
                        <code-editor-model
                          :value="row[column.property]"
                          @handleSureClick="
                            (content) => {
                              row[column.property] = content
                            }
                          "
                        />
                      </template>
                      <!-- 表格富文本编辑器 -->
                      <template #rich_editor_col_render="{ row, column }">
                        <rich-editor-model
                          :value="row[column.property]"
                          @handleSureClick="
                            (content) => {
                              row[column.property] = content
                            }
                          "
                        />
                      </template>
                      <template #renderDictLabel="{ row, column }">
                        <span>{{ getDictLabel(row[column.property], column, group.custom.columns) }}</span>
                      </template>
                      <!-- 货币千分位 -->
                      <template #renderCurrency="{ row, column }">
                        <span>{{ currencyFormat(row[column.property], column, group.custom.columns) }}</span>
                      </template>
                      <template #toolbar_buttons>
                        <span
                          v-for="(btn, sIdx) in getAuthCodeBtns(group.custom.buttons)"
                          class="tools-btn"
                          :key="`btn_${sIdx}`"
                        >
                          <a-button
                            v-if="btn.type === 'check'"
                            :type="btn.type"
                            :disabled="(btn.disabled && btn.disabled(btn)) || false"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                          >
                            {{ btn.title }}
                          </a-button>
                          <!-- 向下填充 -->
                          <a-button
                            v-else-if="btn.type === 'tool-fill'"
                            :type="btn.type"
                            :disabled="(btn.disabled && btn.disabled(btn)) || false"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            @click="toolButtonHandle(btn, group, btn.beforeCheckedCallBack)"
                          >
                            {{ btn.title }}
                          </a-button>

                          <a-upload
                            v-else-if="btn.type === 'import'"
                            :multiple="false"
                            :show-upload-list="false"
                            :data="btn.params"
                            :action="url.import"
                            :headers="tokenHeader"
                            @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                          >
                            <a-button
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              type="primary"
                              >{{ btn.title }}</a-button
                            >
                          </a-upload>
                          <a-select
                            v-else-if="btn.type === 'select'"
                            :defaultValue="btn.defaultValue || ''"
                            :style="btn.style ? btn.style : 'width: 120px'"
                            @change="btn.change"
                          >
                            <a-select-option
                              v-for="sub in btn.options"
                              :value="sub.value"
                              :key="sub.value"
                            >
                              {{ sub.name }}
                            </a-select-option>
                          </a-select>

                          <custom-upload
                            v-else-if="btn.type === 'upload'"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            :single="btn.single"
                            :disabledItemNumber="btn.disabledItemNumber"
                            :disabledHead="btn.disabledHead"
                            :disabled="btn.disabled || false"
                            :attrCheck="btn.attrCheck"
                            :requiredFileType="btn.requiredFileType"
                            :property="btn.property"
                            :visible.sync="btn.modalVisible"
                            :title="btn.title"
                            :dictCode="btn.dictCode || ''"
                            :itemInfo="itemInfo"
                            :isGridUpload="btn.isGridUpload || false"
                            :action="url.upload"
                            :multiple="btn.multiple || true"
                            :accept="accept"
                            :acceptDictCode="attachmentExtensionDictCode"
                            :itemNumberDefaultValue="btn.itemNumberDefaultValue || ''"
                            :headers="tokenHeader"
                            :data="getRequiredData(btn)"
                            :currentEditRow="currentEditRow"
                            @changeStatus="updateItemInfo"
                            @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                          >
                            <a-button
                              v-if="btn.beforeChecked"
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              type="primary"
                              icon="cloud-upload"
                              @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                            >
                              {{ btn.title }}
                            </a-button>
                          </custom-upload>
                          <a-button
                            v-else
                            :type="btn.type"
                            :disabled="(btn.disabled && btn.disabled(btn)) || false"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            v-debounce="{ method: btn.click, event: 'click' }"
                          >
                            {{ btn.title }}
                          </a-button>
                        </span>
                      </template>

                      <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
                        <a
                          v-for="(item, sIdx) in group.custom.optColumnList"
                          :key="`opt_${sIdx}`"
                          :title="item.title"
                          style="margin: 0 4px"
                          :disabled="item.allow ? item.allow(row) : false"
                          v-show="item.showCondition ? item.showCondition(row) : true"
                          @click="item.clickFn(row, column, $rowIndex, $columnIndex)"
                        >
                          {{ item.title }}
                        </a>
                      </template>

                      <template #empty>
                        <m-empty
                          :displayModel="displayModel"
                          :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
                        />
                      </template>
                    </vxe-grid>
                    <slot
                      :name="group.groupCode"
                      :pageData="pageData"
                      :resultData="resultData"
                    />
                  </div>
                </slot>
              </template>
            </div>
          </template>
          <template v-if="displayModel === 'collapse'">
            <a-collapse
              :default-active-key="pageData.groups[0].groupCode"
              @change="collapseChange"
            >
              <template v-for="group in pageData.groups">
                <a-collapse-panel
                  forceRender
                  :key="group.groupCode"
                  :header="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)"
                  :class="[`${group.groupCode}Tab`]"
                >
                  <template v-if="!group.type">
                    <slot
                      :name="`${group.groupCode}Tab`"
                      :pageData="pageData"
                      :resultData="resultData"
                    >
                      <a-form-model
                        :ref="group.groupCode"
                        :model="form"
                        :rules="group.custom.validateRules"
                        class="ant-advanced-rule-form"
                        layout="inline"
                        v-bind="layout"
                      >
                        <a-row :getterr="12">
                          <a-col
                            v-for="(item, idx) in group.custom.formFields"
                            :key="`col_${group.groupCode}_${item.fieldName}_${idx}`"
                            :class="fieldClassByCol(item)"
                            :span="setColSpan(item)"
                          >
                            <template v-if="operateLabelsByType.includes(item.fieldType)">
                              <a-form-model-item :prop="item.fieldName">
                                <a-input
                                  v-model="form[item.fieldName]"
                                  :disabled="item.disabled"
                                  type="hidden"
                                  :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  @change="() => changeInputValue(form[item.fieldName], item)"
                                />
                              </a-form-model-item>
                            </template>
                            <a-form-model-item
                              v-else
                              :prop="item.fieldName"
                            >
                              <template #label>
                                <span>
                                  <a-tooltip :title="$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)">{{ $srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel) }} </a-tooltip>
                                  <a-tooltip
                                    v-if="item.helpText"
                                    :title="item.helpText"
                                  >
                                    <a-icon type="question-circle-o" />
                                  </a-tooltip>
                                </span>
                              </template>

                              <template v-if="['input', 'password', 'textArea'].includes(item.fieldType)">
                                <a-input
                                  :disabled="item.disabled"
                                  :type="item.fieldType === 'input' ? 'text' : item.fieldType.toLowerCase()"
                                  @change="() => changeInputValue(form[item.fieldName], item)"
                                  v-model="form[item.fieldName]"
                                  :title="item.disabled && item.fieldType == 'input' ? form[item.fieldName] : ''"
                                  :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'select'">
                                <m-select
                                  v-model="form[item.fieldName]"
                                  returnTitle
                                  :configData="item"
                                  :options="item.options"
                                  :disabled="item.disabled"
                                  :dictCode="item.dictCode"
                                  :currentEditRow="currentEditRow"
                                  :getPopupContainer="(node) => node.parentNode || document.body"
                                  :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  @afterClearCallBack="handleSelectModalAfterClear"
                                  @afterSelectCallBack="handleSelectAfterSelect"
                                  @change="changeSelectValue"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'multiple'">
                                <a-tooltip
                                  v-if="item.disabled"
                                  :title="form[item.fieldName + '_dictText'] || form[item.fieldName]"
                                >
                                  <span class="ant-form-item-children">
                                    <span
                                      class="ant-input ant-input-disabled"
                                      style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
                                    >
                                      {{ form[item.fieldName + '_dictText'] || form[item.fieldName] }}
                                    </span>
                                  </span>
                                </a-tooltip>
                                <m-select
                                  v-else
                                  v-model="form[item.fieldName]"
                                  mode="multiple"
                                  :configData="item"
                                  :dictCode="item.dictCode"
                                  :currentEditRow="currentEditRow"
                                  :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                                  :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  @change="changeSelectValue"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'cascader'">
                                <m-cascader
                                  v-model="form[item.fieldName]"
                                  changeOnSelect
                                  :mode="item.dictCode"
                                  :disabled="item.disabled"
                                  :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  @change="() => changeInputValue(form[item.fieldName], item)"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'number'">
                                <a-input-number
                                  v-model="form[item.fieldName]"
                                  :disabled="item.disabled"
                                  v-bind="item.extend || {}"
                                  :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  @change="() => changeInputValue(form[item.fieldName], item)"
                                  @blur="() => inputNumberBlurMethod({ value: form[item.fieldName], fieldLabel: item.fieldLabel, type: 'form', fieldType: item.fieldType, form, item })"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'currency'">
                                <m-currency
                                  v-model="form[item.fieldName]"
                                  :configData="item"
                                  :disabled="item.disabled"
                                  :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  @change="() => changeInputValue(form[item.fieldName], item)"
                                />
                              </template>

                              <template v-else-if="['date', 'currentDate'].includes(item.fieldType)">
                                <a-date-picker
                                  v-model="form[item.fieldName]"
                                  :disabled="item.disabled"
                                  :disabledDate="item.fieldType === 'currentDate' ? disabledDate : null"
                                  :show-time="item.dataFormat && item.dataFormat.length > 10 ? true : false"
                                  :valueFormat="item.dataFormat || 'YYYY-MM-DD'"
                                  :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  @change="() => changeInputValue(form[item.fieldName], item)"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'switch'">
                                <m-switch
                                  v-model="form[item.fieldName]"
                                  :disabled="item.disabled"
                                  :configData="item"
                                  @change="changeSelectValue"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'treeSelect'">
                                <m-tree-select
                                  v-model="form[item.fieldName]"
                                  allowClear
                                  :configData="item"
                                  :disabled="item.disabled"
                                  :multiple="(item.extend && item.extend.multiple) || false"
                                  :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                                  :sourceUrl="item.dictCode"
                                  :sourceMap="item.sourceMap"
                                  :sourceData="item.extend && item.extend.sourceData"
                                  :showEmptyNode="item.showEmptyNode"
                                  :treeNodeFilterProp="item.extend && item.extend.treeNodeFilterProp"
                                  :treeDefaultExpandAll="item.extend && item.extend.treeDefaultExpandAll"
                                  :parentNodeSelectable="item.extend && (item.extend.parentNodeSelectable || true)"
                                  :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  :getPopupContainer="(node) => node.parentNode || document.body"
                                  @afterClearCallBack="handleSelectModalAfterClear"
                                  @change="changeSelectValue"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'remoteSelect'">
                                <m-remote-select
                                  v-model="form[item.fieldName]"
                                  :config="item"
                                  :pageData="pageData"
                                  :form="form"
                                  :currentStep="currentStep"
                                  :disabled="item.disabled"
                                  :getPopupContainer="(node) => node.parentNode || document.body"
                                  @afterClearCallBack="() => handleSelectModalAfterClear(item?.extend?.afterClearCallBack)"
                                  @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'selectModal'">
                                <m-select-modal
                                  v-model="form[item.fieldName]"
                                  :config="item"
                                  :pageData="pageData"
                                  :form="form"
                                  :currentStep="currentStep"
                                  @afterClearCallBack="handleSelectModalAfterClear"
                                  @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                                />
                              </template>

                              <!-- 自定义弹窗选择，抛出事件在自己的业务下编写自己业务需要的弹窗 -->
                              <template v-else-if="item.fieldType === 'customSelectModal'">
                                <div @click="customSelectModel(item)">
                                  <a-input
                                    v-model="group.formModel[item.fieldName]"
                                    readOnly
                                    allowClear
                                  />
                                </div>
                              </template>

                              <template v-else-if="item.fieldType === 'ladderPrice'">
                                <ladder-price
                                  v-model="form[item.fieldName]"
                                  :config="item"
                                  :pageData="pageData"
                                  :form="form"
                                  @afterClearCallBack="handleLadderPriceAfterClear"
                                  @ok="(rows) => handleLadderPriceAfterSelect(item, rows)"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'richEditorModel'">
                                <rich-editor-model
                                  :value="form[item.fieldName]"
                                  :showTooltip="(item.extend && item.extend.showTooltip) || false"
                                  :disabled="item.disabled"
                                  @handleSureClick="(content) => (form[item.fieldName] = content)"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'image'">
                                <m-upload
                                  :value.sync="form[item.fieldName]"
                                  :accept="accept2"
                                  :disabled="item.disabled"
                                  :multiple="item.extend && item.extend.multiple"
                                  :limit="item.extend && item.extend.limit"
                                  :headers="tokenHeader"
                                  :data="{ businessType: (item.extend && item.extend.businessType) || item.dictCode, headId: form.id }"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'hiddenField'">
                                <a-input
                                  v-model="form[item.fieldName]"
                                  :disabled="item.disabled"
                                  type="hidden"
                                  :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  @change="() => changeInputValue(form[item.fieldName], item)"
                                />
                              </template>

                              <template v-else-if="item.fieldType === 'float'">
                                <m-float
                                  v-model="form[item.fieldName]"
                                  :configData="item"
                                  :disabled="item.disabled"
                                  :currentEditRow="currentEditRow"
                                  :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                  @change="() => changeInputValue(form[item.fieldName], item)"
                                />
                              </template>
                            </a-form-model-item>
                          </a-col>
                        </a-row>
                      </a-form-model>
                      <slot
                        :name="group.groupCode"
                        :pageData="pageData"
                        :resultData="resultData"
                      />
                    </slot>
                  </template>
                  <template v-else-if="group.type === 'grid'">
                    <slot
                      :name="`${group.groupCode}Tab`"
                      :pageData="pageData"
                      :resultData="resultData"
                    >
                      <div :class="['tableWrap', group.split ? 'customTable' : '', isDynamics(group) ? 'lineWrap' : '']">
                        <vxe-grid
                          :key="group.custom.ref === 'saleDeliverySubList' || group.custom.ref === 'purchaseDeliverySubList' ? `pageContentKey_${pageContentKey}` : group.custom.ref"
                          :ref="group.custom.ref"
                          v-bind="getGridConfig(group)"
                          :editConfig="group.custom.editConfig || gridCustomEditConfig"
                          :editRules="group.custom.rules"
                          :columns="resetCustomCols(group.custom.columns, group)"
                          :menuConfig="menuConfig"
                          @menu-click="menuClickEvent"
                          :show-footer="showGridFooter"
                          :footer-method="
                            ({ columns, data }) => {
                              return footerMethod({ group, columns, data })
                            }
                          "
                          @cell-menu="cellMenuEvent"
                          @cell-click="cellClickEvent"
                          @cell-dblclick="cellDblclickEvent"
                          @edit-actived="editActivedEvent"
                          @checkbox-all="checkboxAll"
                          @checkbox-change="checkboxChange"
                        >
                          <!-- 表格代码编辑器 -->
                          <template #code_editor_col_render="{ row, column }">
                            <code-editor-model
                              :value="row[column.property]"
                              @handleSureClick="
                                (content) => {
                                  row[column.property] = content
                                }
                              "
                            />
                          </template>
                          <!-- 表格富文本编辑器 -->
                          <template #rich_editor_col_render="{ row, column }">
                            <rich-editor-model
                              :value="row[column.property]"
                              @handleSureClick="
                                (content) => {
                                  row[column.property] = content
                                }
                              "
                            />
                          </template>
                          <template #renderDictLabel="{ row, column }">
                            <span>{{ getDictLabel(row[column.property], column, group.custom.columns) }}</span>
                          </template>
                          <!-- 货币千分位 -->
                          <template #renderCurrency="{ row, column }">
                            <span>{{ currencyFormat(row[column.property], column, group.custom.columns) }}</span>
                          </template>
                          <template #toolbar_buttons>
                            <span
                              v-for="(btn, idx) in getAuthCodeBtns(group.custom.buttons)"
                              class="tools-btn"
                              :key="`btn_${idx}`"
                            >
                              <a-button
                                v-if="btn.type === 'check'"
                                :type="btn.type"
                                :disabled="(btn.disabled && btn.disabled(btn)) || false"
                                v-show="btn.showCondition ? btn.showCondition() : true"
                                @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                              >
                                {{ btn.title }}
                              </a-button>
                              <!-- 向下填充 -->
                              <a-button
                                v-else-if="btn.type === 'tool-fill'"
                                :type="btn.type"
                                :disabled="(btn.disabled && btn.disabled(btn)) || false"
                                v-show="btn.showCondition ? btn.showCondition() : true"
                                @click="toolButtonHandle(btn, group, btn.beforeCheckedCallBack)"
                              >
                                {{ btn.title }}
                              </a-button>
                              <a-upload
                                v-else-if="btn.type === 'import'"
                                :multiple="false"
                                :show-upload-list="false"
                                :data="btn.params"
                                :action="url.import"
                                :headers="tokenHeader"
                                @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                              >
                                <a-button
                                  :disabled="(btn.disabled && btn.disabled(btn)) || false"
                                  type="primary"
                                  >{{ btn.title }}</a-button
                                >
                              </a-upload>
                              <a-select
                                v-else-if="btn.type === 'select'"
                                :defaultValue="btn.defaultValue || ''"
                                :style="btn.style ? btn.style : 'width: 120px'"
                                @change="btn.change"
                              >
                                <a-select-option
                                  v-for="sub in btn.options"
                                  :value="sub.value"
                                  :key="sub.value"
                                >
                                  {{ sub.name }}
                                </a-select-option>
                              </a-select>

                              <custom-upload
                                v-else-if="btn.type === 'upload'"
                                v-show="btn.showCondition ? btn.showCondition() : true"
                                :single="btn.single"
                                :disabledItemNumber="btn.disabledItemNumber"
                                :disabledHead="btn.disabledHead"
                                :disabled="btn.disabled || false"
                                :attrCheck="btn.attrCheck"
                                :requiredFileType="btn.requiredFileType"
                                :property="btn.property"
                                :visible.sync="btn.modalVisible"
                                :title="btn.title"
                                :dictCode="btn.dictCode || ''"
                                :itemInfo="itemInfo"
                                :isGridUpload="btn.isGridUpload || false"
                                :action="url.upload"
                                :multiple="btn.multiple || true"
                                :accept="accept"
                                :itemNumberDefaultValue="btn.itemNumberDefaultValue || ''"
                                :headers="tokenHeader"
                                :acceptDictCode="attachmentExtensionDictCode"
                                :data="getRequiredData(btn)"
                                :currentEditRow="currentEditRow"
                                @changeStatus="updateItemInfo"
                                @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                              >
                                <a-button
                                  v-if="btn.beforeChecked"
                                  :disabled="(btn.disabled && btn.disabled(btn)) || false"
                                  type="primary"
                                  icon="cloud-upload"
                                  @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                                >
                                  {{ btn.title }}
                                </a-button>
                              </custom-upload>
                              <a-button
                                v-else
                                :type="btn.type"
                                :disabled="(btn.disabled && btn.disabled(btn)) || false"
                                v-show="btn.showCondition ? btn.showCondition() : true"
                                v-debounce="{ method: btn.click, event: 'click' }"
                              >
                                {{ btn.title }}
                              </a-button>
                            </span>
                          </template>

                          <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
                            <a
                              v-for="(item, idx) in group.custom.optColumnList"
                              :key="`opt_${idx}`"
                              :title="item.title"
                              style="margin: 0 4px"
                              :disabled="item.allow ? item.allow(row) : false"
                              v-show="item.showCondition ? item.showCondition(row) : true"
                              @click="item.clickFn(row, column, $rowIndex, $columnIndex)"
                            >
                              {{ item.title }}
                            </a>
                          </template>

                          <template #empty>
                            <m-empty
                              :displayModel="displayModel"
                              :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
                            />
                          </template>
                        </vxe-grid>
                      </div>
                      <slot
                        :name="group.groupCode"
                        :pageData="pageData"
                        :resultData="resultData"
                      />
                    </slot>
                  </template>
                </a-collapse-panel>
              </template>
            </a-collapse>
          </template>
          <template v-if="displayModel === 'unCollapse'">
            <div
              v-for="group in pageData.groups"
              :key="group.groupCode"
              :class="[`${group.groupCode}Tab`]"
            >
              <div class="item-box-title dark">
                {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}
              </div>
              <template v-if="!group.type">
                <slot
                  :name="`${group.groupCode}Tab`"
                  :pageData="pageData"
                  :resultData="resultData"
                >
                  <a-form-model
                    :ref="group.groupCode"
                    :model="form"
                    :rules="group.custom.validateRules"
                    class="ant-advanced-rule-form"
                    layout="inline"
                    v-bind="layout"
                  >
                    <a-row :getterr="12">
                      <a-col
                        v-for="(item, idx) in group.custom.formFields"
                        :key="`col_${group.groupCode}_${item.fieldName}_${idx}`"
                        :class="fieldClassByCol(item)"
                        :span="setColSpan(item)"
                      >
                        <template v-if="operateLabelsByType.includes(item.fieldType)">
                          <a-form-model-item :prop="item.fieldName">
                            <a-input
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              type="hidden"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </a-form-model-item>
                        </template>
                        <a-form-model-item
                          v-else
                          :prop="item.fieldName"
                        >
                          <template #label>
                            <span>
                              <a-tooltip :title="$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)">{{ $srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel) }} </a-tooltip>
                              <a-tooltip
                                v-if="item.helpText"
                                :title="item.helpText"
                              >
                                <a-icon type="question-circle-o" />
                              </a-tooltip>
                            </span>
                          </template>

                          <template v-if="['input', 'password', 'textArea'].includes(item.fieldType)">
                            <a-input
                              :disabled="item.disabled"
                              :type="item.fieldType === 'input' ? 'text' : item.fieldType.toLowerCase()"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                              v-model="form[item.fieldName]"
                              :title="item.disabled && item.fieldType == 'input' ? form[item.fieldName] : ''"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'select'">
                            <m-select
                              v-model="form[item.fieldName]"
                              returnTitle
                              :configData="item"
                              :options="item.options"
                              :disabled="item.disabled"
                              :dictCode="item.dictCode"
                              :currentEditRow="currentEditRow"
                              :getPopupContainer="(node) => node.parentNode || document.body"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @afterClearCallBack="handleSelectModalAfterClear"
                              @afterSelectCallBack="handleSelectAfterSelect"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'multiple'">
                            <a-tooltip
                              v-if="item.disabled"
                              :title="form[item.fieldName + '_dictText'] || form[item.fieldName]"
                            >
                              <span class="ant-form-item-children">
                                <span
                                  class="ant-input ant-input-disabled"
                                  style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
                                >
                                  {{ form[item.fieldName + '_dictText'] || form[item.fieldName] }}
                                </span>
                              </span>
                            </a-tooltip>
                            <m-select
                              v-else
                              v-model="form[item.fieldName]"
                              mode="multiple"
                              :configData="item"
                              :dictCode="item.dictCode"
                              :currentEditRow="currentEditRow"
                              :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'cascader'">
                            <m-cascader
                              v-model="form[item.fieldName]"
                              changeOnSelect
                              :mode="item.dictCode"
                              :disabled="item.disabled"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'number'">
                            <a-input-number
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              v-bind="item.extend || {}"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                              @blur="() => inputNumberBlurMethod({ value: form[item.fieldName], fieldLabel: item.fieldLabel, type: 'form', fieldType: item.fieldType, form, item })"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'currency'">
                            <m-currency
                              v-model="form[item.fieldName]"
                              :configData="item"
                              :disabled="item.disabled"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="['date', 'currentDate'].includes(item.fieldType)">
                            <a-date-picker
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              :disabledDate="item.fieldType === 'currentDate' ? disabledDate : null"
                              :show-time="item.dataFormat && item.dataFormat.length > 10 ? true : false"
                              :valueFormat="item.dataFormat || 'YYYY-MM-DD'"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'switch'">
                            <m-switch
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              :configData="item"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'treeSelect'">
                            <m-tree-select
                              v-model="form[item.fieldName]"
                              allowClear
                              :configData="item"
                              :disabled="item.disabled"
                              :multiple="(item.extend && item.extend.multiple) || false"
                              :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                              :sourceUrl="item.dictCode"
                              :sourceMap="item.sourceMap"
                              :sourceData="item.extend && item.extend.sourceData"
                              :showEmptyNode="item.showEmptyNode"
                              :treeNodeFilterProp="item.extend && item.extend.treeNodeFilterProp"
                              :treeDefaultExpandAll="item.extend && item.extend.treeDefaultExpandAll"
                              :parentNodeSelectable="item.extend && (item.extend.parentNodeSelectable || true)"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              :getPopupContainer="(node) => node.parentNode || document.body"
                              @afterClearCallBack="handleSelectModalAfterClear"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'remoteSelect'">
                            <m-remote-select
                              v-model="form[item.fieldName]"
                              :config="item"
                              :pageData="pageData"
                              :form="form"
                              :currentStep="currentStep"
                              :disabled="item.disabled"
                              :getPopupContainer="(node) => node.parentNode || document.body"
                              @afterClearCallBack="() => handleSelectModalAfterClear(item?.extend?.afterClearCallBack)"
                              @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'selectModal'">
                            <m-select-modal
                              v-model="form[item.fieldName]"
                              :config="item"
                              :pageData="pageData"
                              :form="form"
                              :currentStep="currentStep"
                              @afterClearCallBack="handleSelectModalAfterClear"
                              @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                            />
                          </template>

                          <!-- 自定义弹窗选择，抛出事件在自己的业务下编写自己业务需要的弹窗 -->
                          <template v-else-if="item.fieldType === 'customSelectModal'">
                            <div @click="customSelectModel(item)">
                              <a-input
                                v-model="group.formModel[item.fieldName]"
                                readOnly
                                allowClear
                              />
                            </div>
                          </template>

                          <template v-else-if="item.fieldType === 'ladderPrice'">
                            <ladder-price
                              v-model="form[item.fieldName]"
                              :config="item"
                              :pageData="pageData"
                              :form="form"
                              @afterClearCallBack="handleLadderPriceAfterClear"
                              @ok="(rows) => handleLadderPriceAfterSelect(item, rows)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'richEditorModel'">
                            <rich-editor-model
                              :value="form[item.fieldName]"
                              :disabled="item.disabled"
                              :showTooltip="(item.extend && item.extend.showTooltip) || false"
                              @handleSureClick="(content) => (form[item.fieldName] = content)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'image'">
                            <m-upload
                              :value.sync="form[item.fieldName]"
                              :accept="accept2"
                              :disabled="item.disabled"
                              :multiple="item.extend && item.extend.multiple"
                              :limit="item.extend && item.extend.limit"
                              :headers="tokenHeader"
                              :data="{ businessType: (item.extend && item.extend.businessType) || item.dictCode, headId: form.id }"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'hiddenField'">
                            <a-input
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              type="hidden"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'float'">
                            <m-float
                              v-model="form[item.fieldName]"
                              :configData="item"
                              :disabled="item.disabled"
                              :currentEditRow="currentEditRow"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>
                        </a-form-model-item>
                      </a-col>
                    </a-row>
                  </a-form-model>
                  <slot
                    :name="group.groupCode"
                    :pageData="pageData"
                    :resultData="resultData"
                  />
                </slot>
              </template>
              <template v-else-if="group.type === 'grid'">
                <slot
                  :name="`${group.groupCode}Tab`"
                  :pageData="pageData"
                  :resultData="resultData"
                >
                  <div :class="['tableWrap', group.split ? 'customTable' : '', isDynamics(group) ? 'lineWrap' : '']">
                    <vxe-grid
                      :key="group.custom.ref === 'saleDeliverySubList' || group.custom.ref === 'purchaseDeliverySubList' ? `pageContentKey_${pageContentKey}` : group.custom.ref"
                      :ref="group.custom.ref"
                      v-bind="getGridConfig(group)"
                      :editConfig="group.custom.editConfig || gridCustomEditConfig"
                      :editRules="group.custom.rules"
                      :columns="resetCustomCols(group.custom.columns, group)"
                      :menuConfig="menuConfig"
                      @menu-click="menuClickEvent"
                      @cell-menu="cellMenuEvent"
                      @cell-click="cellClickEvent"
                      @cell-dblclick="cellDblclickEvent"
                      @edit-actived="editActivedEvent"
                      @checkbox-all="checkboxAll"
                      :show-footer="showGridFooter"
                      :footer-method="
                        ({ columns, data }) => {
                          return footerMethod({ group, columns, data })
                        }
                      "
                      @checkbox-change="checkboxChange"
                    >
                      <!-- 表格代码编辑器 -->
                      <template #code_editor_col_render="{ row, column }">
                        <code-editor-model
                          :value="row[column.property]"
                          @handleSureClick="
                            (content) => {
                              row[column.property] = content
                            }
                          "
                        />
                      </template>
                      <!-- 表格富文本编辑器 -->
                      <template #rich_editor_col_render="{ row, column }">
                        <rich-editor-model
                          :value="row[column.property]"
                          @handleSureClick="
                            (content) => {
                              row[column.property] = content
                            }
                          "
                        />
                      </template>
                      <template #renderDictLabel="{ row, column }">
                        <span>{{ getDictLabel(row[column.property], column, group.custom.columns) }}</span>
                      </template>
                      <!-- 货币千分位 -->
                      <template #renderCurrency="{ row, column }">
                        <span>{{ currencyFormat(row[column.property], column, group.custom.columns) }}</span>
                      </template>
                      <template #toolbar_buttons>
                        <span
                          v-for="(btn, idx) in getAuthCodeBtns(group.custom.buttons)"
                          class="tools-btn"
                          :key="`btn_${idx}`"
                        >
                          <a-button
                            v-if="btn.type === 'check'"
                            :type="btn.type"
                            :disabled="(btn.disabled && btn.disabled(btn)) || false"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                          >
                            {{ btn.title }}
                          </a-button>
                          <!-- 向下填充 -->
                          <a-button
                            v-else-if="btn.type === 'tool-fill'"
                            :type="btn.type"
                            :disabled="(btn.disabled && btn.disabled(btn)) || false"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            @click="toolButtonHandle(btn, group, btn.beforeCheckedCallBack)"
                          >
                            {{ btn.title }}
                          </a-button>
                          <a-upload
                            v-else-if="btn.type === 'import'"
                            :multiple="false"
                            :show-upload-list="false"
                            :data="btn.params"
                            :action="url.import"
                            :headers="tokenHeader"
                            @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                          >
                            <a-button
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              type="primary"
                              >{{ btn.title }}</a-button
                            >
                          </a-upload>
                          <a-select
                            v-else-if="btn.type === 'select'"
                            :defaultValue="btn.defaultValue || ''"
                            :style="btn.style ? btn.style : 'width: 120px'"
                            @change="btn.change"
                          >
                            <a-select-option
                              v-for="sub in btn.options"
                              :value="sub.value"
                              :key="sub.value"
                            >
                              {{ sub.name }}
                            </a-select-option>
                          </a-select>

                          <custom-upload
                            v-else-if="btn.type === 'upload'"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            :single="btn.single"
                            :disabledItemNumber="btn.disabledItemNumber"
                            :disabledHead="btn.disabledHead"
                            :disabled="btn.disabled || false"
                            :attrCheck="btn.attrCheck"
                            :requiredFileType="btn.requiredFileType"
                            :property="btn.property"
                            :visible.sync="btn.modalVisible"
                            :title="btn.title"
                            :dictCode="btn.dictCode || ''"
                            :itemInfo="itemInfo"
                            :isGridUpload="btn.isGridUpload || false"
                            :action="url.upload"
                            :multiple="btn.multiple || true"
                            :accept="accept"
                            :itemNumberDefaultValue="btn.itemNumberDefaultValue || ''"
                            :headers="tokenHeader"
                            :acceptDictCode="attachmentExtensionDictCode"
                            :data="getRequiredData(btn)"
                            :currentEditRow="currentEditRow"
                            @changeStatus="updateItemInfo"
                            @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                          >
                            <a-button
                              v-if="btn.beforeChecked"
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              type="primary"
                              icon="cloud-upload"
                              @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                            >
                              {{ btn.title }}
                            </a-button>
                          </custom-upload>
                          <a-button
                            v-else
                            :type="btn.type"
                            :disabled="(btn.disabled && btn.disabled(btn)) || false"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            v-debounce="{ method: btn.click, event: 'click' }"
                          >
                            {{ btn.title }}
                          </a-button>
                        </span>
                      </template>

                      <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
                        <a
                          v-for="(item, idx) in group.custom.optColumnList"
                          :key="`opt_${idx}`"
                          :title="item.title"
                          style="margin: 0 4px"
                          :disabled="item.allow ? item.allow(row) : false"
                          v-show="item.showCondition ? item.showCondition(row) : true"
                          @click="item.clickFn(row, column, $rowIndex, $columnIndex)"
                        >
                          {{ item.title }}
                        </a>
                      </template>

                      <template #empty>
                        <m-empty
                          :displayModel="displayModel"
                          :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
                        />
                      </template>
                    </vxe-grid>
                  </div>
                  <slot
                    :name="group.groupCode"
                    :pageData="pageData"
                    :resultData="resultData"
                  />
                </slot>
              </template>
            </div>
          </template>
          <template v-if="displayModel === 'masterSlave'">
            <a-collapse
              forceRender
              v-if="collapseHeadCode.length"
              :activeKey="localCollapseKeys"
              @change="collapseChange"
            >
              <template v-for="group in pageData.groups">
                <a-collapse-panel
                  v-if="localCollapseHeadCode.includes(group.groupCode)"
                  :key="group.groupCode"
                  :header="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)"
                  :class="[`${group.groupCode}Tab`]"
                >
                  <slot
                    :name="`${group.groupCode}Tab`"
                    :pageData="pageData"
                    :resultData="resultData"
                  >
                    <a-form-model
                      v-if="!group.type"
                      :ref="group.groupCode"
                      :model="form"
                      :rules="group.custom.validateRules"
                      class="ant-advanced-rule-form"
                      layout="inline"
                      v-bind="layout"
                    >
                      <a-row :getterr="12">
                        <a-col
                          v-for="(item, idx) in group.custom.formFields"
                          :key="`col_${group.groupCode}_${item.fieldName}_${idx}`"
                          :class="fieldClassByCol(item)"
                          :span="setColSpan(item)"
                        >
                          <template v-if="operateLabelsByType.includes(item.fieldType)">
                            <a-form-model-item :prop="item.fieldName">
                              <a-input
                                v-model="form[item.fieldName]"
                                :disabled="item.disabled"
                                type="hidden"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </a-form-model-item>
                          </template>
                          <a-form-model-item
                            v-else
                            :prop="item.fieldName"
                          >
                            <template #label>
                              <span>
                                <a-tooltip :title="$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)">{{ $srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel) }} </a-tooltip>
                                <a-tooltip
                                  v-if="item.helpText"
                                  :title="item.helpText"
                                >
                                  <a-icon type="question-circle-o" />
                                </a-tooltip>
                              </span>
                            </template>

                            <template v-if="['input', 'password', 'textArea'].includes(item.fieldType)">
                              <a-input
                                :disabled="item.disabled"
                                :type="item.fieldType === 'input' ? 'text' : item.fieldType.toLowerCase()"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                                v-model="form[item.fieldName]"
                                :title="item.disabled && item.fieldType == 'input' ? form[item.fieldName] : ''"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'select'">
                              <m-select
                                v-model="form[item.fieldName]"
                                returnTitle
                                :configData="item"
                                :options="item.options"
                                :disabled="item.disabled"
                                :dictCode="item.dictCode"
                                :currentEditRow="currentEditRow"
                                :getPopupContainer="(node) => node.parentNode || document.body"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @afterClearCallBack="handleSelectModalAfterClear"
                                @afterSelectCallBack="handleSelectAfterSelect"
                                @change="changeSelectValue"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'multiple'">
                              <a-tooltip
                                v-if="item.disabled"
                                :title="form[item.fieldName + '_dictText'] || form[item.fieldName]"
                              >
                                <span class="ant-form-item-children">
                                  <span
                                    class="ant-input ant-input-disabled"
                                    style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
                                  >
                                    {{ form[item.fieldName + '_dictText'] || form[item.fieldName] }}
                                  </span>
                                </span>
                              </a-tooltip>
                              <m-select
                                v-else
                                v-model="form[item.fieldName]"
                                mode="multiple"
                                :configData="item"
                                :dictCode="item.dictCode"
                                :currentEditRow="currentEditRow"
                                :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="changeSelectValue"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'cascader'">
                              <m-cascader
                                v-model="form[item.fieldName]"
                                changeOnSelect
                                :mode="item.dictCode"
                                :disabled="item.disabled"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'number'">
                              <a-input-number
                                v-model="form[item.fieldName]"
                                :disabled="item.disabled"
                                v-bind="item.extend || {}"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                                @blur="() => inputNumberBlurMethod({ value: form[item.fieldName], fieldLabel: item.fieldLabel, type: 'form', fieldType: item.fieldType, form, item })"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'currency'">
                              <m-currency
                                v-model="form[item.fieldName]"
                                :configData="item"
                                :disabled="item.disabled"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </template>

                            <template v-else-if="['date', 'currentDate'].includes(item.fieldType)">
                              <a-date-picker
                                v-model="form[item.fieldName]"
                                :disabled="item.disabled"
                                :disabledDate="item.fieldType === 'currentDate' ? disabledDate : null"
                                :show-time="item.dataFormat && item.dataFormat.length > 10 ? true : false"
                                :valueFormat="item.dataFormat || 'YYYY-MM-DD'"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'switch'">
                              <m-switch
                                v-model="form[item.fieldName]"
                                :disabled="item.disabled"
                                :configData="item"
                                @change="changeSelectValue"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'treeSelect'">
                              <m-tree-select
                                v-model="form[item.fieldName]"
                                allowClear
                                :configData="item"
                                :disabled="item.disabled"
                                :multiple="(item.extend && item.extend.multiple) || false"
                                :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                                :sourceUrl="item.dictCode"
                                :sourceMap="item.sourceMap"
                                :sourceData="item.extend && item.extend.sourceData"
                                :showEmptyNode="item.showEmptyNode"
                                :treeNodeFilterProp="item.extend && item.extend.treeNodeFilterProp"
                                :treeDefaultExpandAll="item.extend && item.extend.treeDefaultExpandAll"
                                :parentNodeSelectable="item.extend && (item.extend.parentNodeSelectable || true)"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                :getPopupContainer="(node) => node.parentNode || document.body"
                                @afterClearCallBack="handleSelectModalAfterClear"
                                @change="changeSelectValue"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'remoteSelect'">
                              <m-remote-select
                                v-model="form[item.fieldName]"
                                :config="item"
                                :pageData="pageData"
                                :form="form"
                                :currentStep="currentStep"
                                :disabled="item.disabled"
                                :getPopupContainer="(node) => node.parentNode || document.body"
                                @afterClearCallBack="() => handleSelectModalAfterClear(item?.extend?.afterClearCallBack)"
                                @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'selectModal'">
                              <m-select-modal
                                v-model="form[item.fieldName]"
                                :config="item"
                                :pageData="pageData"
                                :form="form"
                                :currentStep="currentStep"
                                @afterClearCallBack="handleSelectModalAfterClear"
                                @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                              />
                            </template>

                            <!-- 自定义弹窗选择，抛出事件在自己的业务下编写自己业务需要的弹窗 -->
                            <template v-else-if="item.fieldType === 'customSelectModal'">
                              <div @click="customSelectModel(item)">
                                <a-input
                                  v-model="group.formModel[item.fieldName]"
                                  readOnly
                                  allowClear
                                />
                              </div>
                            </template>

                            <template v-else-if="item.fieldType === 'ladderPrice'">
                              <ladder-price
                                v-model="form[item.fieldName]"
                                :config="item"
                                :pageData="pageData"
                                :form="form"
                                @afterClearCallBack="handleLadderPriceAfterClear"
                                @ok="(rows) => handleLadderPriceAfterSelect(item, rows)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'richEditorModel'">
                              <rich-editor-model
                                :value="form[item.fieldName]"
                                :disabled="item.disabled"
                                :showTooltip="(item.extend && item.extend.showTooltip) || false"
                                @handleSureClick="(content) => (form[item.fieldName] = content)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'image'">
                              <m-upload
                                :value.sync="form[item.fieldName]"
                                :accept="accept2"
                                :disabled="item.disabled"
                                :multiple="item.extend && item.extend.multiple"
                                :limit="item.extend && item.extend.limit"
                                :headers="tokenHeader"
                                :data="{ businessType: (item.extend && item.extend.businessType) || item.dictCode, headId: form.id }"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'float'">
                              <m-float
                                v-model="form[item.fieldName]"
                                :configData="item"
                                :disabled="item.disabled"
                                :currentEditRow="currentEditRow"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </template>
                          </a-form-model-item>
                        </a-col>
                      </a-row>
                    </a-form-model>
                    <div
                      :class="['tableWrap', group.split ? 'customTable' : '', isDynamics(group) ? 'lineWrap' : '']"
                      v-else-if="group.type === 'grid'"
                    >
                      <vxe-grid
                        :key="group.custom.ref === 'saleDeliverySubList' || group.custom.ref === 'purchaseDeliverySubList' ? `pageContentKey_${pageContentKey}` : group.custom.ref"
                        :ref="group.custom.ref"
                        v-bind="getGridConfig(group)"
                        :editConfig="group.custom.editConfig || gridCustomEditConfig"
                        :editRules="group.custom.rules"
                        :columns="resetCustomCols(group.custom.columns, group)"
                        :menuConfig="menuConfig"
                        @menu-click="menuClickEvent"
                        @cell-menu="cellMenuEvent"
                        @cell-click="cellClickEvent"
                        @cell-dblclick="cellDblclickEvent"
                        @edit-actived="editActivedEvent"
                        @checkbox-all="checkboxAll"
                        :show-footer="showGridFooter"
                        :footer-method="
                          ({ columns, data }) => {
                            return footerMethod({ group, columns, data })
                          }
                        "
                        @checkbox-change="checkboxChange"
                      >
                        <!-- 表格代码编辑器 -->
                        <template #code_editor_col_render="{ row, column }">
                          <code-editor-model
                            :value="row[column.property]"
                            @handleSureClick="
                              (content) => {
                                row[column.property] = content
                              }
                            "
                          />
                        </template>
                        <!-- 表格富文本编辑器 -->
                        <template #rich_editor_col_render="{ row, column }">
                          <rich-editor-model
                            :value="row[column.property]"
                            @handleSureClick="
                              (content) => {
                                row[column.property] = content
                              }
                            "
                          />
                        </template>
                        <template #renderDictLabel="{ row, column }">
                          <span>{{ getDictLabel(row[column.property], column, group.custom.columns) }}</span>
                        </template>
                        <!-- 货币千分位 -->
                        <template #renderCurrency="{ row, column }">
                          <span>{{ currencyFormat(row[column.property], column, group.custom.columns) }}</span>
                        </template>
                        <template #toolbar_buttons>
                          <span
                            v-for="(btn, idx) in getAuthCodeBtns(group.custom.buttons)"
                            class="tools-btn"
                            :key="`btn_${idx}`"
                          >
                            <a-button
                              v-if="btn.type === 'check'"
                              :type="btn.type"
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              v-show="btn.showCondition ? btn.showCondition() : true"
                              @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                            >
                              {{ btn.title }}
                            </a-button>
                            <!-- 向下填充 -->
                            <a-button
                              v-else-if="btn.type === 'tool-fill'"
                              :type="btn.type"
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              v-show="btn.showCondition ? btn.showCondition() : true"
                              @click="toolButtonHandle(btn, group, btn.beforeCheckedCallBack)"
                            >
                              {{ btn.title }}
                            </a-button>
                            <a-upload
                              v-else-if="btn.type === 'import'"
                              :multiple="false"
                              :show-upload-list="false"
                              :data="btn.params"
                              :action="url.import"
                              :headers="tokenHeader"
                              @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                            >
                              <a-button
                                :disabled="(btn.disabled && btn.disabled(btn)) || false"
                                type="primary"
                                >{{ btn.title }}</a-button
                              >
                            </a-upload>
                            <a-select
                              v-else-if="btn.type === 'select'"
                              :defaultValue="btn.defaultValue || ''"
                              :style="btn.style ? btn.style : 'width: 120px'"
                              @change="btn.change"
                            >
                              <a-select-option
                                v-for="sub in btn.options"
                                :value="sub.value"
                                :key="sub.value"
                              >
                                {{ sub.name }}
                              </a-select-option>
                            </a-select>

                            <custom-upload
                              v-else-if="btn.type === 'upload'"
                              v-show="btn.showCondition ? btn.showCondition() : true"
                              :single="btn.single"
                              :disabledItemNumber="btn.disabledItemNumber"
                              :disabledHead="btn.disabledHead"
                              :disabled="btn.disabled || false"
                              :attrCheck="btn.attrCheck"
                              :requiredFileType="btn.requiredFileType"
                              :property="btn.property"
                              :visible.sync="btn.modalVisible"
                              :title="btn.title"
                              :dictCode="btn.dictCode || ''"
                              :itemInfo="itemInfo"
                              :isGridUpload="btn.isGridUpload || false"
                              :action="url.upload"
                              :multiple="btn.multiple || true"
                              :accept="accept"
                              :itemNumberDefaultValue="btn.itemNumberDefaultValue || ''"
                              :headers="tokenHeader"
                              :acceptDictCode="attachmentExtensionDictCode"
                              :data="getRequiredData(btn)"
                              :currentEditRow="currentEditRow"
                              @changeStatus="updateItemInfo"
                              @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                            >
                              <a-button
                                v-if="btn.beforeChecked"
                                :disabled="(btn.disabled && btn.disabled(btn)) || false"
                                type="primary"
                                icon="cloud-upload"
                                @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                              >
                                {{ btn.title }}
                              </a-button>
                            </custom-upload>
                            <a-button
                              v-else
                              :type="btn.type"
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              v-show="btn.showCondition ? btn.showCondition() : true"
                              v-debounce="{ method: btn.click, event: 'click' }"
                            >
                              {{ btn.title }}
                            </a-button>
                          </span>
                        </template>

                        <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
                          <a
                            v-for="(item, idx) in group.custom.optColumnList"
                            :key="`opt_${idx}`"
                            :title="item.title"
                            style="margin: 0 4px"
                            :disabled="item.allow ? item.allow(row) : false"
                            v-show="item.showCondition ? item.showCondition(row) : true"
                            @click="item.clickFn(row, column, $rowIndex, $columnIndex)"
                          >
                            {{ item.title }}
                          </a>
                        </template>

                        <template #empty>
                          <m-empty
                            :displayModel="displayModel"
                            :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
                          />
                        </template>
                      </vxe-grid>
                    </div>
                  </slot>
                  <slot
                    :name="group.groupCode"
                    :pageData="pageData"
                    :resultData="resultData"
                  />
                </a-collapse-panel>
              </template>
            </a-collapse>
            <div
              v-else
              v-for="(group, index) in pageData.groups"
              :key="group.groupCode"
              :class="[`${group.groupCode}Tab`]"
            >
              <div v-if="index === 0">
                <div class="item-box-title dark">
                  {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}
                </div>
                <slot
                  :name="`${group.groupCode}Tab`"
                  :pageData="pageData"
                  :resultData="resultData"
                >
                  <a-form-model
                    :ref="group.groupCode"
                    :model="form"
                    :rules="group.custom.validateRules"
                    class="ant-advanced-rule-form"
                    layout="inline"
                    v-bind="layout"
                  >
                    <a-row :getterr="12">
                      <a-col
                        v-for="(item, idx) in group.custom.formFields"
                        :key="`col_${group.groupCode}_${item.fieldName}_${idx}`"
                        :class="{ textAreaClass: onlyRowFieldTypes.includes(item.fieldType), 'required-field': item.required === '1' }"
                        :span="setColSpan(item)"
                      >
                        <template v-if="operateLabelsByType.includes(item.fieldType)">
                          <a-form-model-item :prop="item.fieldName">
                            <a-input
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              type="hidden"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </a-form-model-item>
                        </template>
                        <a-form-model-item
                          v-else
                          :prop="item.fieldName"
                        >
                          <template #label>
                            <span>
                              <a-tooltip :title="$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)">{{ $srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel) }} </a-tooltip>
                              <a-tooltip
                                v-if="item.helpText"
                                :title="item.helpText"
                              >
                                <a-icon type="question-circle-o" />
                              </a-tooltip>
                            </span>
                          </template>

                          <template v-if="['input', 'password', 'textArea'].includes(item.fieldType)">
                            <a-input
                              :disabled="item.disabled"
                              :title="item.disabled && item.fieldType == 'input' ? form[item.fieldName] : ''"
                              :type="item.fieldType === 'input' ? 'text' : item.fieldType.toLowerCase()"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                              v-model="form[item.fieldName]"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'select'">
                            <m-select
                              v-model="form[item.fieldName]"
                              returnTitle
                              :configData="item"
                              :options="item.options"
                              :disabled="item.disabled"
                              :dictCode="item.dictCode"
                              :currentEditRow="currentEditRow"
                              :getPopupContainer="(node) => node.parentNode || document.body"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @afterClearCallBack="handleSelectModalAfterClear"
                              @afterSelectCallBack="handleSelectAfterSelect"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'multiple'">
                            <a-tooltip
                              v-if="item.disabled"
                              :title="form[item.fieldName + '_dictText'] || form[item.fieldName]"
                            >
                              <span class="ant-form-item-children">
                                <span
                                  class="ant-input ant-input-disabled"
                                  style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
                                >
                                  {{ form[item.fieldName + '_dictText'] || form[item.fieldName] }}
                                </span>
                              </span>
                            </a-tooltip>
                            <m-select
                              v-else
                              v-model="form[item.fieldName]"
                              mode="multiple"
                              :configData="item"
                              :dictCode="item.dictCode"
                              :currentEditRow="currentEditRow"
                              :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'cascader'">
                            <m-cascader
                              v-model="form[item.fieldName]"
                              changeOnSelect
                              :mode="item.dictCode"
                              :disabled="item.disabled"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'number'">
                            <a-input-number
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              v-bind="item.extend || {}"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                              @blur="() => inputNumberBlurMethod({ value: form[item.fieldName], fieldLabel: item.fieldLabel, type: 'form', fieldType: item.fieldType, form, item })"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'currency'">
                            <m-currency
                              v-model="form[item.fieldName]"
                              :configData="item"
                              :disabled="item.disabled"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="['date', 'currentDate'].includes(item.fieldType)">
                            <a-date-picker
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              :disabledDate="item.fieldType === 'currentDate' ? disabledDate : null"
                              :show-time="item.dataFormat && item.dataFormat.length > 10 ? true : false"
                              :valueFormat="item.dataFormat || 'YYYY-MM-DD'"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'switch'">
                            <m-switch
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              :configData="item"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'treeSelect'">
                            <m-tree-select
                              v-model="form[item.fieldName]"
                              allowClear
                              :configData="item"
                              :disabled="item.disabled"
                              :multiple="(item.extend && item.extend.multiple) || false"
                              :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                              :sourceUrl="item.dictCode"
                              :sourceMap="item.sourceMap"
                              :sourceData="item.extend && item.extend.sourceData"
                              :showEmptyNode="item.showEmptyNode"
                              :treeNodeFilterProp="item.extend && item.extend.treeNodeFilterProp"
                              :treeDefaultExpandAll="item.extend && item.extend.treeDefaultExpandAll"
                              :parentNodeSelectable="item.extend && (item.extend.parentNodeSelectable || true)"
                              :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              :getPopupContainer="(node) => node.parentNode || document.body"
                              @afterClearCallBack="handleSelectModalAfterClear"
                              @change="changeSelectValue"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'remoteSelect'">
                            <m-remote-select
                              v-model="form[item.fieldName]"
                              :config="item"
                              :pageData="pageData"
                              :form="form"
                              :currentStep="currentStep"
                              :disabled="item.disabled"
                              :getPopupContainer="(node) => node.parentNode || document.body"
                              @afterClearCallBack="() => handleSelectModalAfterClear(item?.extend?.afterClearCallBack)"
                              @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'selectModal'">
                            <m-select-modal
                              v-model="form[item.fieldName]"
                              :config="item"
                              :pageData="pageData"
                              :form="form"
                              :currentStep="currentStep"
                              @afterClearCallBack="handleSelectModalAfterClear"
                              @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                            />
                          </template>

                          <!-- 自定义弹窗选择，抛出事件在自己的业务下编写自己业务需要的弹窗 -->
                          <template v-else-if="item.fieldType === 'customSelectModal'">
                            <div @click="customSelectModel(item)">
                              <a-input
                                v-model="group.formModel[item.fieldName]"
                                readOnly
                                allowClear
                              />
                            </div>
                          </template>

                          <template v-else-if="item.fieldType === 'ladderPrice'">
                            <ladder-price
                              v-model="form[item.fieldName]"
                              :config="item"
                              :pageData="pageData"
                              :form="form"
                              @afterClearCallBack="handleLadderPriceAfterClear"
                              @ok="(rows) => handleLadderPriceAfterSelect(item, rows)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'richEditorModel'">
                            <rich-editor-model
                              :value="form[item.fieldName]"
                              :disabled="item.disabled"
                              :showTooltip="(item.extend && item.extend.showTooltip) || false"
                              @handleSureClick="(content) => (form[item.fieldName] = content)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'image'">
                            <m-upload
                              :value.sync="form[item.fieldName]"
                              :accept="accept2"
                              :disabled="item.disabled"
                              :multiple="item.extend && item.extend.multiple"
                              :limit="item.extend && item.extend.limit"
                              :headers="tokenHeader"
                              :data="{ businessType: (item.extend && item.extend.businessType) || item.dictCode, headId: form.id }"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'hiddenField'">
                            <a-input
                              v-model="form[item.fieldName]"
                              :disabled="item.disabled"
                              type="hidden"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>

                          <template v-else-if="item.fieldType === 'float'">
                            <m-float
                              v-model="form[item.fieldName]"
                              :configData="item"
                              :disabled="item.disabled"
                              :currentEditRow="currentEditRow"
                              :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              @change="() => changeInputValue(form[item.fieldName], item)"
                            />
                          </template>
                        </a-form-model-item>
                      </a-col>
                    </a-row>
                  </a-form-model>
                  <slot
                    :name="group.groupCode"
                    :pageData="pageData"
                    :resultData="resultData"
                  />
                </slot>
              </div>
            </div>
            <a-tabs>
              <template v-for="(group, index) in pageData.groups">
                <a-tab-pane
                  v-if="collapseHeadCode.length ? !localCollapseHeadCode.includes(group.groupCode) : index > 0"
                  :key="group.groupCode"
                  forceRender
                  :tab="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)"
                  :class="[`${group.groupCode}Tab`]"
                >
                  <slot
                    :name="`${group.groupCode}Tab`"
                    :pageData="pageData"
                    :resultData="resultData"
                  >
                    <a-form-model
                      v-if="!group.type"
                      :ref="group.groupCode"
                      :model="form"
                      :rules="group.custom.validateRules"
                      class="ant-advanced-rule-form"
                      layout="inline"
                      v-bind="layout"
                    >
                      <a-row :getterr="12">
                        <a-col
                          v-for="(item, idx) in group.custom.formFields"
                          :key="`col_${group.groupCode}_${item.fieldName}_${idx}`"
                          :class="fieldClassByCol(item)"
                          :span="setColSpan(item)"
                        >
                          <a-form-model-item :prop="item.fieldName">
                            <template #label>
                              <span>
                                <a-tooltip :title="$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)">{{ $srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel) }} </a-tooltip>
                                <a-tooltip
                                  v-if="item.helpText"
                                  :title="item.helpText"
                                >
                                  <a-icon type="question-circle-o" />
                                </a-tooltip>
                              </span>
                            </template>

                            <template v-if="['input', 'password', 'textArea'].includes(item.fieldType)">
                              <a-input
                                :disabled="item.disabled"
                                :title="item.disabled && item.fieldType == 'input' ? form[item.fieldName] : ''"
                                :type="item.fieldType === 'input' ? 'text' : item.fieldType.toLowerCase()"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                                v-model="form[item.fieldName]"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'select'">
                              <m-select
                                v-model="form[item.fieldName]"
                                returnTitle
                                :configData="item"
                                :options="item.options"
                                :disabled="item.disabled"
                                :dictCode="item.dictCode"
                                :currentEditRow="currentEditRow"
                                :getPopupContainer="(node) => node.parentNode || document.body"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @afterClearCallBack="handleSelectModalAfterClear"
                                @afterSelectCallBack="handleSelectAfterSelect"
                                @change="changeSelectValue"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'multiple'">
                              <a-tooltip
                                v-if="item.disabled"
                                :title="form[item.fieldName + '_dictText'] || form[item.fieldName]"
                              >
                                <span class="ant-form-item-children">
                                  <span
                                    class="ant-input ant-input-disabled"
                                    style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
                                  >
                                    {{ form[item.fieldName + '_dictText'] || form[item.fieldName] }}
                                  </span>
                                </span>
                              </a-tooltip>
                              <m-select
                                v-else
                                v-model="form[item.fieldName]"
                                mode="multiple"
                                :configData="item"
                                :dictCode="item.dictCode"
                                :currentEditRow="currentEditRow"
                                :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="changeSelectValue"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'cascader'">
                              <m-cascader
                                v-model="form[item.fieldName]"
                                changeOnSelect
                                :mode="item.dictCode"
                                :disabled="item.disabled"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'number'">
                              <a-input-number
                                v-model="form[item.fieldName]"
                                :disabled="item.disabled"
                                v-bind="item.extend || {}"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                                @blur="() => inputNumberBlurMethod({ value: form[item.fieldName], fieldLabel: item.fieldLabel, type: 'form', fieldType: item.fieldType, form, item })"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'currency'">
                              <m-currency
                                v-model="form[item.fieldName]"
                                :configData="item"
                                :disabled="item.disabled"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </template>

                            <template v-else-if="['date', 'currentDate'].includes(item.fieldType)">
                              <a-date-picker
                                v-model="form[item.fieldName]"
                                :disabled="item.disabled"
                                :disabledDate="item.fieldType === 'currentDate' ? disabledDate : null"
                                :show-time="item.dataFormat && item.dataFormat.length > 10 ? true : false"
                                :valueFormat="item.dataFormat || 'YYYY-MM-DD'"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'switch'">
                              <m-switch
                                v-model="form[item.fieldName]"
                                :disabled="item.disabled"
                                :configData="item"
                                @change="changeSelectValue"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'treeSelect'">
                              <m-tree-select
                                v-model="form[item.fieldName]"
                                allowClear
                                :configData="item"
                                :disabled="item.disabled"
                                :multiple="(item.extend && item.extend.multiple) || false"
                                :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                                :sourceUrl="item.dictCode"
                                :sourceMap="item.sourceMap"
                                :sourceData="item.extend && item.extend.sourceData"
                                :showEmptyNode="item.showEmptyNode"
                                :treeNodeFilterProp="item.extend && item.extend.treeNodeFilterProp"
                                :treeDefaultExpandAll="item.extend && item.extend.treeDefaultExpandAll"
                                :parentNodeSelectable="item.extend && (item.extend.parentNodeSelectable || true)"
                                :placeholder="`${selectI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                :getPopupContainer="(node) => node.parentNode || document.body"
                                @afterClearCallBack="handleSelectModalAfterClear"
                                @change="changeSelectValue"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'remoteSelect'">
                              <m-remote-select
                                v-model="form[item.fieldName]"
                                :config="item"
                                :pageData="pageData"
                                :form="form"
                                :currentStep="currentStep"
                                :disabled="item.disabled"
                                :getPopupContainer="(node) => node.parentNode || document.body"
                                @afterClearCallBack="() => handleSelectModalAfterClear(item?.extend?.afterClearCallBack)"
                                @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'selectModal'">
                              <m-select-modal
                                v-model="form[item.fieldName]"
                                :config="item"
                                :pageData="pageData"
                                :form="form"
                                :currentStep="currentStep"
                                @afterClearCallBack="handleSelectModalAfterClear"
                                @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                              />
                            </template>

                            <!-- 自定义弹窗选择，抛出事件在自己的业务下编写自己业务需要的弹窗 -->
                            <template v-else-if="item.fieldType === 'customSelectModal'">
                              <div @click="customSelectModel(item)">
                                <a-input
                                  v-model="group.formModel[item.fieldName]"
                                  readOnly
                                  allowClear
                                />
                              </div>
                            </template>

                            <template v-else-if="item.fieldType === 'ladderPrice'">
                              <ladder-price
                                v-model="form[item.fieldName]"
                                :config="item"
                                :pageData="pageData"
                                :form="form"
                                @afterClearCallBack="handleLadderPriceAfterClear"
                                @ok="(rows) => handleLadderPriceAfterSelect(item, rows)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'richEditorModel'">
                              <rich-editor-model
                                :value="form[item.fieldName]"
                                :disabled="item.disabled"
                                :showTooltip="(item.extend && item.extend.showTooltip) || false"
                                @handleSureClick="(content) => (form[item.fieldName] = content)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'image'">
                              <m-upload
                                :value.sync="form[item.fieldName]"
                                :accept="accept2"
                                :disabled="item.disabled"
                                :multiple="item.extend && item.extend.multiple"
                                :limit="item.extend && item.extend.limit"
                                :headers="tokenHeader"
                                :data="{ businessType: (item.extend && item.extend.businessType) || item.dictCode, headId: form.id }"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'hiddenField'">
                              <a-input
                                v-model="form[item.fieldName]"
                                :disabled="item.disabled"
                                type="hidden"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </template>

                            <template v-else-if="item.fieldType === 'float'">
                              <m-float
                                v-model="form[item.fieldName]"
                                :configData="item"
                                :disabled="item.disabled"
                                :currentEditRow="currentEditRow"
                                :placeholder="`${inputI18nTxt}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.fieldLabel)}`"
                                @change="() => changeInputValue(form[item.fieldName], item)"
                              />
                            </template>
                          </a-form-model-item>
                        </a-col>
                      </a-row>
                    </a-form-model>
                    <div
                      v-else-if="group.type === 'grid'"
                      :class="['tableWrap', group.split ? 'customTable' : '', isDynamics(group) ? 'lineWrap' : '']"
                    >
                      <vxe-grid
                        :key="group.custom.ref === 'saleDeliverySubList' || group.custom.ref === 'purchaseDeliverySubList' ? `pageContentKey_${pageContentKey}` : group.custom.ref"
                        :ref="group.custom.ref"
                        v-bind="getGridConfig(group)"
                        :editConfig="group.custom.editConfig || gridCustomEditConfig"
                        :editRules="group.custom.rules"
                        :columns="resetCustomCols(group.custom.columns, group)"
                        :menuConfig="menuConfig"
                        @menu-click="menuClickEvent"
                        @cell-click="cellClickEvent"
                        @cell-dblclick="cellDblclickEvent"
                        @edit-actived="editActivedEvent"
                        @cell-menu="cellMenuEvent"
                        @checkbox-all="checkboxAll"
                        :show-footer="showGridFooter"
                        :footer-method="
                          ({ columns, data }) => {
                            return footerMethod({ group, columns, data })
                          }
                        "
                        @checkbox-change="checkboxChange"
                      >
                        <!-- 表格代码编辑器 -->
                        <template #code_editor_col_render="{ row, column }">
                          <code-editor-model
                            :value="row[column.property]"
                            @handleSureClick="
                              (content) => {
                                row[column.property] = content
                              }
                            "
                          />
                        </template>
                        <!-- 表格富文本编辑器 -->
                        <template #rich_editor_col_render="{ row, column }">
                          <rich-editor-model
                            :value="row[column.property]"
                            @handleSureClick="
                              (content) => {
                                row[column.property] = content
                              }
                            "
                          />
                        </template>
                        <template #renderDictLabel="{ row, column }">
                          <span>{{ getDictLabel(row[column.property], column, group.custom.columns) }}</span>
                        </template>
                        <!-- 货币千分位 -->
                        <template #renderCurrency="{ row, column }">
                          <span>{{ currencyFormat(row[column.property], column, group.custom.columns) }}</span>
                        </template>
                        <template #toolbar_buttons>
                          <span
                            v-for="(btn, idx) in getAuthCodeBtns(group.custom.buttons)"
                            class="tools-btn"
                            :key="`btn_${idx}`"
                          >
                            <a-button
                              v-if="btn.type === 'check'"
                              :type="btn.type"
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              v-show="btn.showCondition ? btn.showCondition() : true"
                              @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                            >
                              {{ btn.title }}
                            </a-button>
                            <!-- 向下填充 -->
                            <a-button
                              v-else-if="btn.type === 'tool-fill'"
                              :type="btn.type"
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              v-show="btn.showCondition ? btn.showCondition() : true"
                              @click="toolButtonHandle(btn, group, btn.beforeCheckedCallBack)"
                            >
                              {{ btn.title }}
                            </a-button>
                            <a-upload
                              v-else-if="btn.type === 'import'"
                              :multiple="false"
                              :show-upload-list="false"
                              :data="btn.params"
                              :action="url.import"
                              :headers="tokenHeader"
                              @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                            >
                              <a-button
                                :disabled="(btn.disabled && btn.disabled(btn)) || false"
                                type="primary"
                                >{{ btn.title }}</a-button
                              >
                            </a-upload>
                            <a-select
                              v-else-if="btn.type === 'select'"
                              :defaultValue="btn.defaultValue || ''"
                              :style="btn.style ? btn.style : 'width: 120px'"
                              @change="btn.change"
                            >
                              <a-select-option
                                v-for="sub in btn.options"
                                :value="sub.value"
                                :key="sub.value"
                              >
                                {{ sub.name }}
                              </a-select-option>
                            </a-select>

                            <custom-upload
                              v-else-if="btn.type === 'upload'"
                              v-show="btn.showCondition ? btn.showCondition() : true"
                              :single="btn.single"
                              :disabledItemNumber="btn.disabledItemNumber"
                              :disabledHead="btn.disabledHead"
                              :disabled="btn.disabled || false"
                              :attrCheck="btn.attrCheck"
                              :requiredFileType="btn.requiredFileType"
                              :property="btn.property"
                              :visible.sync="btn.modalVisible"
                              :title="btn.title"
                              :dictCode="btn.dictCode || ''"
                              :itemInfo="itemInfo"
                              :isGridUpload="btn.isGridUpload || false"
                              :action="url.upload"
                              :multiple="btn.multiple || true"
                              :accept="accept"
                              :itemNumberDefaultValue="btn.itemNumberDefaultValue || ''"
                              :headers="tokenHeader"
                              :acceptDictCode="attachmentExtensionDictCode"
                              :data="getRequiredData(btn)"
                              :currentEditRow="currentEditRow"
                              @changeStatus="updateItemInfo"
                              @change="(info) => handleUploadChange(info, btn, group.custom.ref)"
                            >
                              <a-button
                                v-if="btn.beforeChecked"
                                :disabled="(btn.disabled && btn.disabled(btn)) || false"
                                type="primary"
                                icon="cloud-upload"
                                @click="checkedGridSelect(btn, group.custom.ref, btn.beforeCheckedCallBack)"
                              >
                                {{ btn.title }}
                              </a-button>
                            </custom-upload>
                            <a-button
                              v-else
                              :type="btn.type"
                              :disabled="(btn.disabled && btn.disabled(btn)) || false"
                              v-show="btn.showCondition ? btn.showCondition() : true"
                              v-debounce="{ method: btn.click, event: 'click' }"
                            >
                              {{ btn.title }}
                            </a-button>
                          </span>
                        </template>
                        <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
                          <a
                            v-for="(item, idx) in group.custom.optColumnList"
                            :key="`opt_${idx}`"
                            :title="item.title"
                            style="margin: 0 4px"
                            :disabled="item.allow ? item.allow(row) : false"
                            v-show="item.showCondition ? item.showCondition(row) : true"
                            @click="item.clickFn(row, column, $rowIndex, $columnIndex)"
                          >
                            {{ item.title }}
                          </a>
                        </template>

                        <template #empty>
                          <m-empty
                            :displayModel="displayModel"
                            :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
                          />
                        </template>
                      </vxe-grid>
                    </div>
                  </slot>
                  <slot
                    :name="group.groupCode"
                    :pageData="pageData"
                    :resultData="resultData"
                  />
                </a-tab-pane>
              </template>
            </a-tabs>
          </template>
        </div>
        <div
          class="page-footer"
          v-if="displayModel === 'tab'"
        >
          <a-button
            v-if="currentStep"
            :type="customPageFooterPreBtn.type"
            @click="customPageFooterPreBtn.click"
            >{{ customPageFooterPreBtn.title }}</a-button
          >
          <a-button
            v-if="currentStep < pageData.groups.length - 1"
            :type="customPageFooterNextBtn.type"
            @click="customPageFooterNextBtn.click"
            >{{ customPageFooterNextBtn.title }}</a-button
          >
          <a-button
            v-for="(btn, idx) in getAuthCodeBtns(pageData.publicBtn || pageData.footerButtons)"
            :key="`pub_btn_${idx}`"
            :type="btn.type"
            v-show="btn.showCondition ? btn.showCondition() : true"
            v-debounce="{ method: 'customBtnClick', event: 'click', args: btn }"
          >
            {{ btn.title }}
          </a-button>
        </div>
      </a-spin>
    </slot>
    <GridFoldDrawer
      ref="GridFoldDrawer"
      :itemColumns="itemColumns"
      :busAccount="busAccount"
    />
  </div>
</template>

<script>
import moment from 'moment'

import { getAction, postAction } from '@/api/manage'
import { bindDefaultValue, filterObjUnEmpty, handlePromise, getObjType, inputNumberBlurMethod } from '@/utils/util.js'
import { EditConfig } from '@/plugins/table/gridConfig'
import { DEFAULT_COLOR, USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { PURCHASEATTACHMENTDOWNLOADAPI, ATTACHMENT_EXTENSION_DICT_CODE } from '@/utils/const'
import { ACCEPT } from '@/utils/constant'
import { cloneDeep, debounce } from 'lodash'
import MUpload from '@comp/mUpload'
import { mapState, mapActions } from 'vuex'
import GridFoldDrawer from '@comp/template/gridFoldDrawer'
import { USER_INFO } from '@/store/mutation-types'
import LadderPrice from '@comp/LadderPrice'
import MTreeSelect from '@comp/treeSelect/mTreeSelect'
import CustomUpload from '@comp/template/CustomUpload'
import RichEditorModel from '@comp/richEditorModel/RichEditorModel'
import { currency } from '@/filters'

export default {
  name: 'EditLayout',
  inject: ['tplRootRef'],
  props: {
    pageData: {
      type: Object,
      default() {
        return {
          groups: [
            {
              custom: {
                form: {},
                formFields: [],
                validateRules: {}
              },
              extend: null,
              groupCode: null,
              groupName: null,
              sortOrder: null
            }
          ]
        }
      }
    },
    url: {
      type: Object,
      default() {
        return {}
      }
    },
    extraEditConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    refresh: {
      type: Boolean,
      default: false
    },
    currentEditRow: {
      type: Object,
      default() {
        return {}
      }
    },
    // 是否使用传入布局模式
    useLocalModelLayout: {
      type: Boolean,
      default: false
    },
    // 模式布局方式： tab格式：tab，折叠格式：collapse，非折叠格式：unCollapse，单一主从格式：masterSlave
    modelLayout: {
      type: String,
      default: 'tab'
    },
    // 单个group 配置, 覆盖原配置，如高度等
    singleGroupCoverConfig: {
      type: Function,
      default: null
    },
    // modelLayout 是主从remoteJs可折叠的数组 [{code:'baseForm', expand: true}, {code:'eightDisciplinesTeamList', expand: true}]
    // 如果是一维数组折默认打开所有的折叠
    collapseHeadCode: {
      type: Array,
      default() {
        return []
      }
    },
    // 编辑时，头部的title 自定义
    headerTitle: {
      type: String,
      default: ''
    },
    // 旧模板业务模板配置数据
    // 原模板默认不传
    pageConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    gridFooterMethod: {
      type: Function,
      default: null
    },
    showGridFooter: {
      type: Boolean,
      default: false
    }
  },
  components: {
    MTreeSelect,
    CustomUpload,
    MUpload,
    RichEditorModel,
    LadderPrice,
    GridFoldDrawer
  },
  data() {
    return {
      resultData: {},
      refreshTimeStamp: '',
      currentPageName: null,
      needEcho: null,
      mustMaterialNumber: null,
      showFloat: false,
      setStartPrice: null,
      ebiddingWay: null,
      delayRule: null,
      currentStep: 0,
      confirmLoading: false,
      onlyRowFieldTypes: ['textArea', 'image'], // 表头独占一行
      layout: {
        labelCol: { span: 9 },
        wrapperCol: { span: 15 }
      },
      voucherId: '',
      form: this.pageData.form || {},
      //附件上传配置
      tokenHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
      attachmentExtensionDictCode: ATTACHMENT_EXTENSION_DICT_CODE,
      accept: ACCEPT,
      accept2: '.png, .jpg, .jpeg, .gif',
      currentSelectModal: null,
      currentObject: null,
      gridCustomEditConfig: {
        trigger: 'click',
        mode: 'cell',
        showStatus: true
      },
      customPageFooterPreBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_prevStep`, '上一步'), type: 'primary', belong: 'preStep', click: this.prevStep },
      customPageFooterNextBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nextStep`, '下一步'), type: 'primary', belong: 'nextStep', click: this.nextStep },
      inputI18nTxt: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入'),
      selectI18nTxt: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择'),
      operateLabelsByType: ['hiddenField'],
      // 抽屉显示
      drawerVisible: false,
      currentDrawer: {},
      currentItemGrid: {},
      menuConfig: EditConfig.menuConfig,

      pageContentKey: 1
    }
  },
  computed: {
    ...mapState({
      cache_vuex_editActivedInfo: (state) => state.app.cache_vuex_editActivedInfo
    }),
    busAccount() {
      let account = this.$ls.get(USER_ELS_ACCOUNT)
      if (this.currentEditRow && this.currentEditRow.busAccount) {
        account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount || this.$ls.get(USER_ELS_ACCOUNT)
      }
      return account
    },
    itemColumns() {
      const groups = this.pageData.groups || []
      let itemInfo = groups.filter((item) => item.groupCode === 'itemInfo')
      if (itemInfo.length) {
        return itemInfo[0].custom.columns
      }
      return []
    },
    itemInfo() {
      let itemInfo = []
      const groups = this.pageData.groups || []
      const group = groups.find((n) => n.groupCode === 'itemInfo')
      if (group) {
        const refName = group.custom.ref
        itemInfo = (this.$refs[refName] && this.$refs[refName][0].getTableData().fullData) || []
      }
      // !important 依赖收集 刷新数据用
      if (!this.refreshTimeStamp) return itemInfo
      return itemInfo
    },
    getLangAccount() {
      return this.$getLangAccount(this.busAccount)
    },
    displayModel() {
      let model = this.editLayoutModel || 'tab'
      if (this.useLocalModelLayout) {
        model = this.modelLayout
      }
      console.log('model :>> ', model)
      return model
    },
    // 获取默认主题色
    getDefaultColor() {
      return {
        color: this.$ls.get(DEFAULT_COLOR)
      }
    },
    // 旧模板默认不传业务模板配置，所以 this.$parent中获取
    editLayoutModel() {
      let model = ''
      if (this.pageConfig && this.pageConfig.editLayout) {
        model = this.pageConfig.editLayout
      } else if (this.$parent.pageConfig && this.$parent.pageConfig.editLayout) {
        model = this.$parent.pageConfig.editLayout
      }
      return model
    }
  },
  watch: {
    collapseHeadCode: {
      immediate: true,
      handler: function (val) {
        if (!val.length) return
        this.localCollapseHeadCode = val.map((rs) => (rs.code ? rs.code : rs))
        this.localCollapseKeys = val.map((rs) => (rs.code ? (rs.expand == false ? '' : rs.code) : rs)) // 兼容一维数组，一维数组默认都展开，expand为false或者不填默认都不展开
      }
    }
  },
  mounted() {
    // form表单默认字段
    if (this.currentEditRow && this.currentEditRow.templateNumber) {
      this.form['templateNumber'] = this.currentEditRow.templateNumber
      this.form['templateName'] = this.currentEditRow.templateName
      this.form['templateVersion'] = this.currentEditRow.templateVersion
    }
    this.$store.dispatch('modifyBusAccountLangData', { busAccount: this.busAccount })

    // 当前页面的名称
    this.currentPageName = this.$route.meta ? this.$route.meta.title : ''
  },
  methods: {
    ...mapActions(['setEditActivedInfo']),
    inputNumberBlurMethod,
    fieldClassByCol(item) {
      let className = { textAreaClass: this.onlyRowFieldTypes.includes(item.fieldType), 'required-field': item.required === '1' }
      if (item.fieldType === 'image') {
        className['image-class'] = true
      }
      return className
    },
    setColSpan(item) {
      return this.onlyRowFieldTypes.includes(item.fieldType) ? 24 : 8
    },
    // 添加必填列附加样式
    resetCustomCols(cols, tab) {
      let needSortKeyList = []
      if (tab.groupName == '采购申请行信息' && tab.custom.ref == 'purchaseRequestItemList') {
        needSortKeyList = ['物料编码', '物料名称', '物料规格', '需求数量', '主单位', '辅数量', '辅单位']
      }
      cols.forEach((col) => {
        if (col.required && col.required === '1') {
          col.className = 'required-col'
          col.headerClassName = 'required-col'
        }
        if (needSortKeyList.includes(col.title)) {
          col.sortable = true
        }
      })
      return cols
    },
    footerMethod({ group, columns, data }) {
      let footerData = []
      if (columns) {
        footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '合计'
            }
            return null
          })
        ]
      }
      if (this.gridFooterMethod && getObjType(this.gridFooterMethod) === 'function') {
        return this.gridFooterMethod({ group, columns, data })
      } else {
        return footerData
      }
    },
    getRequiredData(btn) {
      let data = { businessType: btn.businessType, headId: this.form.id }
      if (btn.attr && typeof btn.attr === 'function') {
        data = Object.assign(data, btn.attr())
      }
      return data
    },
    cellMenuEvent({ column }) {
      const setVisibleByCode = (code, flag) => {
        if (this.menuConfig?.body?.options.length) {
          this.menuConfig.body.options[0].forEach((rs) => {
            if (rs.code === code) {
              rs.visible = flag
            }
          })
        }
      }
      if (column.editRender) {
        // 关闭右键菜单 向下填充
        setVisibleByCode('FILLDOWN_CELL', false)
      } else {
        setVisibleByCode('FILLDOWN_CELL', false)
      }
    },
    menuClickEvent(info) {
      const { $grid, column, row, menu } = info
      const { fullData } = $grid.getTableData()
      if (menu.code === 'FILLDOWN_CELL') {
        // 向下填充
        // 虚拟行index
        const rowIndex = $grid.getVMRowIndex(row)
        fullData.forEach((item, index) => {
          if (index > rowIndex) {
            // 当前选中的值
            const currentSelectVal = fullData[rowIndex][column.property]
            item[column.property] = currentSelectVal
          }
        })
      }
    },
    cellClickEvent(info) {
      const { row, rowIndex, column, columnIndex } = info
      // 非同一个表格置空
      if (this.cache_vuex_editActivedInfo?.column?.property !== column.property) {
        this.setEditActivedInfo({})
      }
      this.$emit('cell-click', { row, rowIndex, column, columnIndex })
    },
    cellDblclickEvent(info) {
      const { row, rowIndex, column, columnIndex } = info
      this.$emit('cell-dblclick', { row, rowIndex, column, columnIndex })
    },
    editActivedEvent(info) {
      const { row, rowIndex, column, columnIndex } = info
      this.setEditActivedInfo(info)
      this.$emit('edit-actived', { row, rowIndex, column, columnIndex })
    },
    isDynamics(group) {
      let { groupName, groupCode, needDynamicsHeight } = group
      if (needDynamicsHeight) return true;
      if ((!!groupName && (groupName.includes('行信息') || groupName.includes('品牌信息') || groupName.includes('计量单位信息'))) || groupCode === 'saleDeliverySubList' || groupCode === 'fileInfo') return true
      return false
    },
    getGridConfig(group) {
      let assignConfig = Object.assign({}, EditConfig, this.extraEditConfig)
      let { toolbarConfig, ...config } = assignConfig || {}

      // 计算当前页面最大高度
      const clientHeight = document.documentElement.clientHeight
      let contextHeight = clientHeight - 246
      let oneThird = contextHeight / 3
      let MIN = 334

      // 🚀 针对联系人信息和地址信息页签的极致空间优化 - 编辑页面
      const isContactInfo = group.groupCode === 'contactsInfo' ||
                           group.groupCode === 'supplierContactsInfoList' ||
                           (group.groupName && group.groupName.includes('联系人信息')) ||
                           (group.custom && group.custom.ref &&
                            (group.custom.ref.includes('contactsInfo') || group.custom.ref.includes('supplierContactsInfoList')))

      const isAddressInfo = group.groupCode === 'addressInfo' ||
                           group.groupCode === 'supplierAddressInfoList' ||
                           (group.groupName && group.groupName.includes('地址信息')) ||
                           (group.custom && group.custom.ref &&
                            (group.custom.ref.includes('addressInfo') || group.custom.ref.includes('supplierAddressInfoList')))

      if (isContactInfo || isAddressInfo) {
        const infoType = isContactInfo ? '联系人信息' : '地址信息'
        console.log(`🎯 编辑页面 - ${infoType}表格极致空间优化:`, {
          groupCode: group.groupCode,
          groupName: group.groupName,
          customRef: group.custom?.ref,
          originalHeight: contextHeight,
          clientHeight,
          displayModel: this.displayModel
        })

        // 在tab模式下，极致利用空间
        if (this.displayModel === 'tab') {
          contextHeight = clientHeight - 120  // 极致减少预留空间，充分利用红框区域
          MIN = 200 // 降低最小高度限制
        } else {
          contextHeight = clientHeight - 150  // 其他模式也适当优化
          MIN = 250
        }
      }

      if (this.isDynamics(group)) oneThird = contextHeight
      if (contextHeight < MIN) contextHeight = MIN
      if (oneThird < MIN) oneThird = MIN

      let height
      if (this.displayModel === 'tab') {
        // 🚀 tab模式下，联系人信息和地址信息表格使用极致高度
        if (isContactInfo || isAddressInfo) {
          height = contextHeight  // 使用优化后的contextHeight
          const infoType = isContactInfo ? '联系人信息' : '地址信息'
          console.log(`🎯 编辑页面 - ${infoType}表格在tab模式下的最终高度:`, height)
        } else {
          height = contextHeight
        }
      } else if (this.displayModel === 'collapse') {
        height = oneThird
      } else if (this.displayModel === 'unCollapse') {
        height = oneThird
      } else if (this.displayModel === 'masterSlave') {
        if (oneThird < 442) oneThird = 442
        height = oneThird
      }
      config.height = height
      if (this.isDynamics(group)) {
        config.minHeight = MIN
        config.maxHeight = config.height
        config.height = '100%'
      }

      // 当前表行是否已配置按钮组
      if (group.custom.buttons && group.custom.buttons.length) {
        config.toolbarConfig = toolbarConfig
      }

      // 可根据分组返回自定义配置
      if (this.singleGroupCoverConfig && getObjType(this.singleGroupCoverConfig) === 'function') {
        return this.singleGroupCoverConfig(config, group, this.displayModel)
      } else {
        return config
      }
    },
    openFoldDrawer(param) {
      this.currentDrawer = param
      this.currentItemGrid = param // 当前所有列
      this.drawerVisible = true
    },
    // 获取非权限码按钮组
    getAuthCodeBtns(btns) {
      let authBtns = []
      if (btns && btns.length) {
        btns.forEach((item) => {
          // 配置authorityCode做权限控制
          if (item && item.authorityCode) {
            // 有权限
            if (this.$hasOptAuth(item.authorityCode)) {
              authBtns.push(item)
            }
          } else {
            // 不配置authorityCode就不做权限控制
            authBtns.push(item)
          }
        })
      }
      // console.log(btns, authBtns)
      return authBtns
    },
    updateItemInfo() {
      this.refreshTimeStamp = new Date()
    },
    // 步骤条点击变更
    stepChange(step) {
      let formCompnent = null
      let gridCompnent = null
      let groupData = null
      if (this.pageData.groups[this.currentStep]) {
        let formRefName = this.pageData.groups[this.currentStep].groupCode
        let gridRefName = this.pageData.groups[this.currentStep].custom ? this.pageData.groups[this.currentStep].custom.ref : ''
        formCompnent = formRefName ? this.$refs[formRefName] : ''
        gridCompnent = gridRefName ? (this.$refs[gridRefName] && this.$refs[gridRefName][0] ? this.$refs[gridRefName][0] : '') : ''
        groupData = this.pageData.groups[this.currentStep]
      }
      let emitData = { formCompnent: formCompnent, gridCompnent: gridCompnent, pageData: this.pageData, groupData: groupData, form: this.form, currentStep: this.currentStep, step: step }
      this.$emit('stepChange', emitData)
    },
    // 標籤展開
    collapseChange() {
      this.$emit('collapseChange', { groupData: { groupCode: 'saleDeliverySubList' } })
    },
    //自定义seleclModel
    customSelectModel(field) {
      if (field.extend && field.extend.modalColumns) {
        field.extend.modalColumns.forEach((col) => {
          if (col.fieldLabelI18nKey) {
            col.title = this.$srmI18n(`${this.$getLangAccount()}#${col.fieldLabelI18nKey}`, col.title)
          }
        })
      }
      this.$emit('customSelect', field)
    },
    // 禁用小于当前日期的
    disabledDate(current) {
      return current && current < moment().startOf('day')
    },
    checkboxChange({ row }) {
      this.$emit('checkboxChange', row)
    },
    checkboxAll() {
      this.$emit('checkboxAll')
    },
    handleSelectModalAfterClear(cb) {
      cb && cb(this.form, this.pageData, this)
    },
    handleSelectAfterSelect(cb) {
      cb && cb(this.form, this.pageData)
    },
    handleLadderPriceAfterClear(cb) {
      cb && cb(this.form, this.pageData)
    },
    customRequestBybindFuncion(fn, args) {
      this.$parent[fn] && this.$parent[fn](...(args || ''))
    },
    handleSelectModalAfterSelect(item, rows) {
      item.bindFunction && item.bindFunction.call(null, this, rows)
    },
    handleLadderPriceAfterSelect(item, rows) {
      item.bindFunction && item.bindFunction.call(null, this, rows)
    },
    handleDownload({ id, fileName }, url = '') {
      const params = {
        id
      }
      let downloadUrl = url || PURCHASEATTACHMENTDOWNLOADAPI
      if (this.url.download) {
        downloadUrl = this.url.download
      }
      getAction(downloadUrl, params, {
        responseType: 'blob'
      }).then((res) => {
        console.log(res)
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    },
    prevStep() {
      this.currentStep > 0 && this.currentStep--
      this.preOrNextStepHandle('pre')
    },
    nextStep() {
      if (this.currentStep < this.pageData.groups.length - 1) {
        this.currentStep++
      }
      this.preOrNextStepHandle('next')
    },
    // 上一步和下一步的处理逻辑外部使用
    preOrNextStepHandle(type) {
      let formCompnent = null
      let gridCompnent = null
      let groupData = null
      if (this.pageData.groups[this.currentStep]) {
        let formRefName = this.pageData.groups[this.currentStep].groupCode
        let gridRefName = this.pageData.groups[this.currentStep].custom ? this.pageData.groups[this.currentStep].custom.ref : ''
        formCompnent = formRefName ? this.$refs[formRefName] : ''
        gridCompnent = gridRefName ? (this.$refs[gridRefName] && this.$refs[gridRefName][0] ? this.$refs[gridRefName][0] : '') : ''
        groupData = this.pageData.groups[this.currentStep]
      }
      let emitData = { formCompnent: formCompnent, gridCompnent: gridCompnent, pageData: this.pageData, groupData: groupData, form: this.form, currentStep: this.currentStep }
      if (type === 'next') {
        this.$emit('nextStepHandle', emitData)
      } else {
        this.$emit('preStepHandle', emitData)
      }
    },
    goBack() {
      this.$parent.goBack()
    },
    setFormPropDefaultData() {
      const { formFields = [] } = this.pageData || {}
      let userInfo = this.$ls.get(USER_INFO)
      formFields.forEach(({ fieldName, defaultValue, extend = {} }) => {
        const val = bindDefaultValue(defaultValue) || ''
        if (!val && extend?.userInfoConfig?.open && extend?.userInfoConfig?.useFieldName) {
          // 设置默认值优先
          const useFieldName = extend.userInfoConfig.useFieldName
          if (typeof useFieldName === 'function' && userInfo) {
            this.form[fieldName] = useFieldName(userInfo)
          } else {
            this.form[fieldName] = userInfo[useFieldName] ? userInfo[useFieldName] : ''
          }
        } else {
          this.form[fieldName] = val
        }
      })
    },
    setDetailData(detailData, cb) {
      this.setFormPropDefaultData()
      this.form = Object.assign({}, this.form, filterObjUnEmpty(detailData))
      this.pageData.groups.forEach(async (group) => {
        if (group.type && group.type === 'grid') {
          const ref = group.custom.ref
          if (detailData[ref]) {
            if (this.$refs[ref] && this.$refs[ref][0]) {
              let listData = detailData[ref]

              try {
                if (!!this.$parent && !!this.$parent.formatTableData) {
                  listData = await this.$parent.formatTableData(listData, ref)
                  console.log('listData', listData)
                }
              } catch (error) {}
              this.$refs[ref][0].loadData(listData)
            }
          }
        }
      })
      this.pageData.groups.forEach((group) => {
        if (!group.type || !group.type === 'grid') {
          this.$nextTick(() => {
            const formFields = group.custom.formFields || []
            formFields.forEach((item) => {
              const { bindFunction, fieldName, fieldType, groupCode, defaultValue } = item

              if (bindFunction && typeof bindFunction === 'function') {
                const parentRef = this.$refs[groupCode]
                const groupData = this.pageData.groups[this.currentStep]
                const value = this.form[fieldName] || bindDefaultValue(defaultValue)
                let disabledFieldTypes = ['selectModal', 'customSelectModal', 'remoteSelect']
                if (!disabledFieldTypes.includes(fieldType)) {
                  if (fieldType === 'input' || fieldType === 'date') {
                    bindFunction.call(null, parentRef, this.pageData, groupData, value, item, this.form)
                  } else {
                    bindFunction.call(null, parentRef, this.pageData, groupData, value, [], '', this.form, '', this)
                  }
                }
              }
            })
          })
        }
      })
      this.$emit('afterLoadData', detailData)
      cb && cb(detailData)
    },
    async queryDetail(id, cb) {
      let detailData = {}
      if (!this.voucherId) {
        this.voucherId = id
      }
      // 保证有id才去请求
      if (id || this.voucherId) {
        this.confirmLoading = true
        return getAction(this.url.detail, { id: id || this.voucherId })
          .then((res) => {
            if (res && res.success) {
              detailData = res.result || {}
              try {
                if (!!this.$parent && !!this.$parent.formatPageData) {
                  detailData = this.$parent.formatPageData(detailData)
                }
              } catch (error) {}
              if (!this.currentEditRow.id) {
                // 新增 保存后更新currentEditRow
                this.tplRootRef.currentEditRow = detailData
              }
              this.resultData = cloneDeep(res.result) || {}
              this.setDetailData(detailData, cb)
            } else {
              this.$message.error(res.message)
            }
          })
          .finally(() => {
            this.confirmLoading = false
          })
      } else {
        this.confirmLoading = false
        detailData = Object.assign({}, this.currentEditRow)
        this.setDetailData(detailData, cb)
      }
    },
    getPageData() {
      let params = { ...this.form }
      // 没有模板时，赋值默认参数
      if (!this.voucherId) {
        params = Object.assign(params, this.currentEditRow)
      }
      this.pageData.groups.forEach((group) => {
        if (group.type === 'grid') {
          let ref = group.custom.ref
          if (this.$refs[ref] && this.$refs[ref][0]) {
            params[ref] = this.$refs[ref][0].getTableData().fullData
          }
        }
      })
      return params
    },
    // 兼容旧模板
    // 公共-基本信息模块使用
    queryDetailNotid(id, cb, currentGroupCode) {
      this.confirmLoading = true
      getAction(this.url.detail)
        .then((res) => {
          this.currentStep = currentGroupCode || 0
          if (!res.success) {
            this.$message.error(res.message)
            return
          }
          let detailData = res.result || {}
          if (!this.voucherId) {
            this.voucherId = detailData.id || ''
          }
          this.setDetailData(detailData, cb)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    setPromise() {
      let that = this
      let promise = this.pageData.groups.map((group) => {
        if (group.type === 'grid') {
          return that.$refs[group.custom.ref][0].validate(true)
        } else {
          return that.$refs[group.groupCode][0].validate()
        }
      })
      return promise
    },
    handValidate(url, params, callback) {
      let promise = this.setPromise()
      Promise.all(handlePromise(promise))
        .then((result) => {
          let flag = false
          for (let i = 0; i < result.length; i++) {
            if (result[i].status === 'success') {
              flag = true
            } else {
              this.currentStep = i
              return
            }
          }
          if (flag) return callback && callback(url, params, this)
        })
        .catch((err) => {
          console.log(err)
        })
    },
    handleSend(type = 'public', callback) {
      let promise = this.setPromise()
      Promise.all(handlePromise(promise))
        .then((result) => {
          let flag = false
          for (let i = 0; i < result.length; i++) {
            if (result[i].status === 'success') {
              flag = true
            } else {
              this.currentStep = i
              return
            }
          }
          if (flag) this.postData(type, callback)
        })
        .catch((err) => {
          console.error(err)
        })
    },
    postData(type, callback) {
      let params = this.getPageData()
      let url = type === 'public' ? this.url.public : this.voucherId ? this.url.edit : this.url.add
      this.confirmLoading = true
      postAction(url, params)
        .then(async (res) => {
          if (res.success) {
            const msgType = res.success ? 'success' : 'error'
            this.$message[msgType](res.message)
            if (res.result && res.result.id) {
              this.voucherId = res.result.id
            }
            if (type === 'public') {
              this.$parent.goBack()
            } else {
              this.refresh && (await this.queryDetail())
              // 自定义回调
              return callback && callback(params, this)
            }
          } else {
            if (!!res.message && res.message.indexOf('\n') >= 0) {
              const h = this.$createElement
              let strList = res.message.split('\n')
              strList = strList.map((str, strIndex) => {
                return h(strIndex === 0 ? 'span' : 'div', strIndex === 0 ? null : { style: 'margin-left: 24px' }, str)
              })
              this.$message.error(h('span', { style: 'text-align: left' }, strList))
              return
            }

            const msgType = res.success ? 'success' : 'error'
            this.$message[msgType](res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    //附件上传
    handleUploadChange(info, btn, refName) {
      btn.callBack && btn.callBack(info, refName)
    },
    // input 改变事件,value-当前值
    changeInputValue: debounce(function changeInputValue(value, item) {
      let parentRef = null
      let groupData = null
      if (this.pageData.groups[this.currentStep]) {
        let parentRefName = this.pageData.groups[this.currentStep].groupCode
        parentRef = this.$refs[parentRefName]
        groupData = this.pageData.groups[this.currentStep]
      }
      if (item && item.bindFunction && typeof item.bindFunction === 'function') {
        item.bindFunction(parentRef, this.pageData, groupData, value, item, this.form)
      }
    }, 200),
    // select 改变事件
    changeSelectValue: debounce(function changeSelectValue(realValue, opt, oldVal, configData, rTitle) {
      this.$emit('selectChange', configData)
      let parentRef = null
      let groupData = null
      if (this.pageData.groups[this.currentStep]) {
        let parentRefName = this.pageData.groups[this.currentStep].groupCode
        parentRef = this.$refs[parentRefName]
        groupData = this.pageData.groups[this.currentStep]
      }
      if (configData && configData.bindFunction && typeof configData.bindFunction === 'function') {
        configData.bindFunction(parentRef, this.pageData, groupData, realValue, opt, oldVal, this.form, rTitle, this)
      }
    }, 200),
    // 获取列数据字典对应label
    getDictLabel(value, column, columns = []) {
      let txt = ''
      for (const { field, options = [] } of columns) {
        if (field === column.property) {
          let { label = '' } = options.find((sub) => sub.value == value) || {}
          txt = label
          break
        }
      }
      return txt || value
    },
    // 货币千分位
    currencyFormat(value, info, columns = []) {
      if (!value) {
        return ''
      }
      let extend = {}
      for (let item of columns) {
        if (info.property === item.field) {
          extend = item.extend || {}
          break
        }
      }

      console.log('extend :>> ', extend)

      let symbol = (extend && extend.symbol) || ''
      let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
      return currency(value, symbol, decimals)
    },
    toolButtonHandle(btn, group, cb) {
      // .custom.ref
      const otherParams = {
        buttonInfo: btn,
        flag: 'editlayout',
        self: this,
        tplRootRef: this.tplRootRef,
        group
      }
      cb(this.cache_vuex_editActivedInfo, otherParams)
    },
    checkedGridSelect(btn, refName, cb) {
      if (!this.voucherId) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
        return
      }
      let selectData = null
      let that = this
      if (this.$refs[refName]) {
        if (this.$refs[refName][0]) {
          selectData = this.$refs[refName][0].getCheckboxRecords() || this.$refs[refName][0].getRadioRecord()
        }
      }
      if (selectData && selectData.length) {
        if (cb && typeof cb === 'function') {
          if (btn.key !== 'batchDownload') {
            cb(selectData, that).then((res) => {
              if (res) {
                btn.modalVisible = true
              }
            })
          } else {
            cb(selectData, that)
          }
        } else {
          this.modalVisible = true
        }
      } else {
        console.log(btn)
        if (btn.msgType === 'batchDownload') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRiTIKBIcW_10289077`, '请勾选需下载附件行！'))
        } else {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterChoose`, '请先选择'))
        }
      }
    },
    // 底部通用按钮点击
    customBtnClick(btn) {
      if (btn && btn.key && btn.key === 'submit') {
        if (this.currentEditRow && (this.currentEditRow.id || this.voucherId)) {
          btn.click()
        } else {
          if (this.url && this.url.add && btn.click) {
            let params = this.getPageData()
            postAction(this.url.add, params).then((res) => {
              if (res && res.success) {
                this.currentEditRow.id = res.result.id
                this.queryDetail(res.result.id, btn.click)
              }
            })
          }
        }
      } else {
        btn.click()
      }
    },
    // 超链接 跳转
    getNewRouter(value, item) {
      let that = this
      let linkConfig = item?.extend?.linkConfig || {}
      if (item?.extend?.handleBefore && typeof item.extend.handleBefore === 'function') {
        let callbackObj = item.extend.handleBefore(this.form, linkConfig, that, { pageConfig: this.pageConfig || this.$parent.pageConfig }) || {}
        linkConfig = { ...linkConfig, ...callbackObj }
      }
      if (value && linkConfig.actionPath && linkConfig.bindKey) {
        let query = {
          [linkConfig.primaryKey]: this.form[linkConfig.bindKey],
          ...linkConfig.otherQuery,
          t: new Date().getTime()
        }
        console.warn({ path: linkConfig.actionPath.trim(), query: { ...query } })
        this.$router.push({ path: linkConfig.actionPath.trim(), query: { ...query } })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-page {
  :deep(.lineWrap) {
    .vxe-table--fixed-left-wrapper,
    .vxe-table--fixed-right-wrapper {
      height: calc(100% - 16px) !important;
      .vxe-table--body{
        width: 100% !important;
      }
    }
  }
  .edit-grid-box {
    .tableWrap {
      height: 100%;
    }
  }
  .pageSplit {
    display: flex;
    flex-direction: column;
    .tableWrap {
      height: 100%;
    }
    .customTable {
      flex: 1;
      min-height: 0;
    }
  }
  .ant-input-number,
  .ant-calendar-picker {
    width: 100%;
    font-size: 13px;
  }
  .item-box-title {
    padding: 0 7px;
    border: 1px solid #ededed;
    height: 34px;
    line-height: 34px;
    margin: 6px 0;
    &.dark {
      background: #f2f2f2;
    }
  }
  .btn-col {
    .ant-btn {
      margin-right: 6px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  :deep(.textAreaClass) {
    .ant-form-item-label {
      width: 12%;
    }
    .ant-form-item-control-wrapper {
      width: 88%;
    }
    &.image-class {
      height: 112px;
      line-height: 112px;
    }
  }
  .required-field {
    :deep(.ant-form-item-control) {
      > textarea,
      > input,
      .ant-select-selection {
        background-color: #fff9f7;
        border: 1px solid #fdaf96;
        border-radius: 4px;
      }
    }
  }
  .ant-input {
    font-size: 13px;
  }
  :deep(.ant-input) {
    font-size: 13px;
  }
  :deep(.ant-select) {
    font-size: 13px;
  }
  :deep(.ant-calendar-picker-input.ant-input) {
    font-size: 13px;
  }
  :deep(.vxe-table--render-default.size--mini) {
    font-size: 13px;
  }
  :deep(.vxe-body--column.required-col) {
    background-color: #f6ebe8;
    background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
  }
  :deep(.vxe-header--column.required-col) {
    background-color: #f6ebe8;
    background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
  }

  // 🚀 编辑页面 - 联系人信息表格极致空间优化样式
  :deep(.ant-tabs-content-holder) {
    // 确保整个tab内容容器充分利用空间
    height: calc(100vh - 120px) !important;
    min-height: calc(100vh - 120px) !important;

    .ant-tabs-tabpane {
      // 确保tab内容区域充分利用空间
      height: 100% !important;
      min-height: calc(100vh - 120px) !important;

      // 针对联系人信息和地址信息表格的特殊处理
      [data-group-code="supplierContactsInfoList"],
      [data-group-code="contactsInfo"],
      [data-group-code="supplierAddressInfoList"],
      [data-group-code="addressInfo"],
      .lineWrap[ref*="supplierContactsInfoList"],
      .lineWrap[ref*="contactsInfo"],
      .lineWrap[ref*="supplierAddressInfoList"],
      .lineWrap[ref*="addressInfo"] {
        height: 100% !important;
        min-height: calc(100vh - 150px) !important;

        .vxe-table {
          height: 100% !important;
          min-height: calc(100vh - 150px) !important;
        }

        .vxe-table--body-wrapper {
          max-height: calc(100% - 50px) !important;
          min-height: calc(100vh - 200px) !important;
          overflow-y: auto;
        }
      }
    }
  }

  // 编辑页面表格数据区域强制填充 - 联系人信息和地址信息
  :deep([data-group-code="supplierContactsInfoList"]),
  :deep([data-group-code="supplierAddressInfoList"]) {
    height: calc(100vh - 180px) !important;
    min-height: 700px !important;

    .vxe-table {
      height: 100% !important;
    }
  }

  // 编辑页面联系人信息和地址信息表格优化 - 使用ref选择器
  :deep([ref*="supplierContactsInfoList"]),
  :deep([ref*="supplierAddressInfoList"]) {
    .vxe-table--body-wrapper {
      max-height: calc(100vh - 120px) !important;
      min-height: 600px !important;
      overflow-y: auto;
    }

    .vxe-table--header-wrapper {
      position: sticky;
      top: 0;
      z-index: 10;
      background: #fff;
    }

    .vxe-table {
      height: calc(100vh - 120px) !important;
      min-height: 650px !important;
    }
  }
}
</style>
