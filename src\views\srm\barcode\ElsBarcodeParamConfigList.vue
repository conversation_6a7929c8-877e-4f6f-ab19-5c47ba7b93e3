<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <ElsBarcodeParamConfigEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <ElsBarcodeParamConfigDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import ElsBarcodeParamConfigEdit from './modules/ElsBarcodeParamConfigEdit.vue'
import ElsBarcodeParamConfigDetail from './modules/ElsBarcodeParamConfigDetail.vue'
import { postAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        ElsBarcodeParamConfigEdit,
        ElsBarcodeParamConfigDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barCodeParamConfig',
                form: {
                    mouldGroupDesc: ''
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'paramSerialNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNsWAy_aec73ff8`, '请输入参数编号')
                    }
                ],
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'barcode#paramConfig:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'barcode#paramConfig:detail' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showOperaEdit, authorityCode: 'barcode#paramConfig:edit' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enable`, '启用'), clickFn: this.handleEnabled, allow: this.showOperaOn, authorityCode: 'barcode#paramConfig:enable' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disable`, '禁用'), clickFn: this.handleDisabled, allow: this.showOperaOff, authorityCode: 'barcode#paramConfig:disabled' },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.showOperaEdit, authorityCode: 'barcode#paramConfig:delete' },
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/base/barcode/elsBarCodeParamConfig/add',
                list: '/base/barcode/elsBarCodeParamConfig/list',
                delete: '/base/barcode/elsBarCodeParamConfig/delete',
                operaOn: '/base/barcode/elsBarCodeParamConfig/operaOn',
                operaOff: '/base/barcode/elsBarCodeParamConfig/operaOff',
                columns: 'ElsBarCodeParamConfigList'
            }
        }
    },
    methods: {
        handleResultRecord (row) {
            this.currentEditRow = row
            this.showResultPage = true
            this.showEditPage = false
            this.showDetailPage = false
        },
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        showOperaEdit (row) {
            return row.status != '1' ? false : true
        },
        showOperaOn (row) {
            return row.status == '2' ? false : true
        },
        showOperaOff (row) {
            return row.status == '1' ? false : true
        },
        handleEnabled (row) {
            let that = this
            const url = this.url.operaOn + '/' + row.id
            postAction(url, null).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_AjLR_28088728`, '启用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        handleDisabled (row) {
            let that = this
            const url = this.url.operaOff + '/' + row.id
            postAction(url, null).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_HjLR_38ff8896`, '禁用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        }
    }
}
</script>