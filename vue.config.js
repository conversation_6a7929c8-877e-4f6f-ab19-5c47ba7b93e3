const { defineConfig } = require('@vue/cli-service')
const path = require('path')
const webpack = require('webpack');
const CompressionPlugin = require('compression-webpack-plugin')
const ProgressBarPlugin = require('progress-bar-webpack-plugin');
const chalk = require('chalk');
const { DefaultDeserializer } = require('v8');
const { ColumnConfig } = require('vxe-table');
function resolve (dir) {
    return path.join(__dirname, dir)
}

const env = process.env.NODE_ENV
module.exports = defineConfig({
    pages:{
        index:{
            entry: 'src/main.js',
        }
    },
    // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建.
    productionSourceMap: false,
    // publicPath:'./',
    transpileDependencies: [
        'vue-echarts',
        'resize-detector'
    ],
    //打包app时放开该配置
    // publicPath:'./',
    configureWebpack: config => {
    //生产环境取消 console.log
        if (env === 'production') {
            // 5.x版本默认没有drop_console，需手动加上
            // config.optimization.minimizer[0].options.minimizer.options.compress = Object.assign(
            //     config.optimization.minimizer[0].options.minimizer.options.compress,
            //     {drop_console: true}
            // )
            // config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
            config.plugins = [
                ...config.plugins,
                new ProgressBarPlugin({
                    format: '  build [:bar] ' + chalk.green.bold(':percent') + ' (:elapsed seconds)',
                    clear: false
                }),
            ]
        }
        config.plugins = [
            ...config.plugins
        ]
    },
    chainWebpack: (config) => {
        console.log(config.entry,'======')
        config.resolve.alias.set('@$', resolve('src'))
        config.resolve.alias.set('@api', resolve('src/api'))
        config.resolve.alias.set('@assets', resolve('src/assets'))
        config.resolve.alias.set('@comp', resolve('src/components'))
        config.resolve.alias.set('@views', resolve('src/views'))
        config.resolve.alias.set('@layout', resolve('src/layout'))
        config.resolve.alias.set('@static', resolve('src/static'))
        config.resolve.alias.set('@mobile', resolve('src/module/mobile'))
        // 生产环境配置
        if (process.env.NODE_ENV === 'production') {
            // 删除预加载
            config.plugins.delete('preload');
            config.plugins.delete('prefetch');
            // 压缩代码
            config.optimization.minimize(true);
            // 分割代码
            config.optimization.splitChunks({
                chunks: 'all'
            })
        }
    },
    css: {
        loaderOptions: {
            less: {
                javascriptEnabled: true
            }
        }
    },

    devServer: {
        compress:true,
        hot: true,
        open: true,
        https: false,      
        host: 'localhost', 
        port: 3000,
        proxy: {
            '/els': {
                target: 'https://test-srm.gzr.com.cn:8789',
                ws: false,
                changeOrigin: true
            },
            '/els/websocket': {
                target: 'ws://localhost:8080', //请求本地 需要els后台项目
                // target: 'ws://v5sit.51qqt.com', //请求本地 需要els后台项目
                ws: true,
                changeOrigin: true
            }
        },
        client: {
            overlay: {
                warnings: false,
                errors: false
            }
        }
    },
    lintOnSave: false
})