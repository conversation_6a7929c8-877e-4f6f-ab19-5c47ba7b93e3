<template>
  <div style="margin: 0 10px 0 40px;">
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n__IByC_37a6bb10`, '目标节点')"
          prop="nextId">
          <a-select
            style="width: 120px"
            v-model="form.nextId">
            <a-select-option
              :key="item.id + index"
              :value="item.id"
              v-for="(item, index) in form.nodeArray"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_userDeal`, '处理人')"
          prop="usersInfo">
          <a-tag
            v-if="singleUserData != null && singleUserData.userNo != null"
            size="large"
            color="blue"
            closable
            @close="delSingleUsers"
          >{{ singleUserData.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectSingleShowUsers"></a-button>
          </div>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_LSRL_2527e109`, '任务名称')"
          prop="userTaskName">
          <a-input
            v-model="form.userTaskName"
            clearable />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { getOutgoingFlows, addNodeOperate } from '../../api/analy'
export default {
    mixins: [modalMixins],
    data () {
        return {
            rules: {
                nextId: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFIByC_7efe05c7`, '请选择目标节点') } ],
                userTaskName: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMLSRL_9060674e`, '请填写任务名称'), trigger: 'change' },
                    {max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}
                ],
                usersInfo: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFGvL_f32585e1`, '请选择处理人') } ]
            },
            singleUserData: {}
        }
    },
    methods: {
        selectSingleShowUsers () {
            this.showUserSelectModal({ selectModel: 'single' })
        },
        fieldSelectOk (data) {
            this.$set(this.form, 'usersInfo', data[0])
            this.singleUserData = data[0]
        },
        delSingleUsers () {
            this.singleUserData = {}
        },
        handleConfirm () {
            this.cheackValidate().then(() => {
                let {nextId, userTaskName, taskId, usersInfo} = this.form
                let params = {
                    nextId, userTaskName, taskId,
                    userIds: usersInfo.id
                }
                this.loading = true
                addNodeOperate(params).then(res => {
                    this.loading = false
                    if (res.code == 0) {
                        this.$emit('success', 'back')
                        this.$message.success(res.message)
                    } else {
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.loading = false
                    this.$emit('closeLoading')
                })
            })
        }
    },
    created () {
        this.loading = true
        getOutgoingFlows(this.taskId).then(res => {
            if (res.code == 0) {
                this.$set(this.form, 'nodeArray', res.data)
            }
            this.loading = false
        })
    }
}
</script>
