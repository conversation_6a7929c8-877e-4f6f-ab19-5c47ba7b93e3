<template>
  <!-- 2022-03-08 新版本采购竞价大厅（逐条、打包、批量）英式《QQT SRM V5-PRD-竞价管理优化方案-v2.1-********》 -->
  <div class="saleBuyBid">
    <a-spin :spinning="spinning">
      <div class="container">
        <div class="top">
          <div class="breadcrumb">
          </div>
          <div class="menu">
            <a-button
              type="primary"
              @click="submit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_offer`, '报价') }}</a-button>
            <a-button
              type="primary"
              @click="openRange">{{ $srmI18n(`${$getLangAccount()}#i18n_title_biddingRangeUpper`, '竞价幅度上限') }}</a-button>
            <a-button
              type="primary"
              @click="refreshBtn">{{ $srmI18n(`${$getLangAccount()}#i18n_title_refresh`, '刷新') }}</a-button>
            <!-- <a-button
              type="default"
              @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_menu_Rl_a72da`, '关闭') }}</a-button> -->
            <a-button
              type="primary"
              @click="windowOpenClose">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
          </div>
        </div>

        <div class="content">
          <div class="gutter">
            <div class="item price">
              <div class="info">
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_field_OuyO_391539d8`, '竞价阶段') }}</b>
                  <b class="value">{{ getCurrentTitle }}</b>
                </span>
                <span 
                 
                  class="inline">
                  <b class="value">{{ getCurrentMaterialStatus }}</b>
                </span>
                <span
                  v-if="form.ebiddingStatus === '3' || form.ebiddingStatus === '4'"
                  class="inline"
                  style="float: right;">
                  <b class="label">{{ countdownText }}</b>
                  <b class="value red">
                    <span
                      class="icon"
                      v-if="deadline">
                      <a-icon
                        type="clock-circle"
                        theme="outlined" 
                        :style="{ fontSize: '22px', color: '#f41616', marginRight: '8px' }"
                      />
                    </span>
                    <countdown
                      :time="deadline"
                      v-if="deadline"
                      :style="valueStyle"
                      @end="handleFinish"
                    >
                      <template slot-scope="props">{{ props.days ? `${props.days} ${$srmI18n(`${$getLangAccount()}#i18n_dict_S_5929`, '天')} ` : '' }}{{ props.hours }} : {{ props.minutes }} : {{ props.seconds }}</template>
                    </countdown>
                  </b>
                </span>
              </div>
            </div>
          </div>
          <div class="gutter">
            <div class="item price">
              <div class="info">
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号') }}</b>
                  <b class="value m-l-24">{{ form.ebiddingNumber }} </b>
                  <b
                    class="value inline-flot"
                    @click="changeToggle">{{ getToggleStatu }} <a-icon :type="resizeHeight==0?'down':'up'" /></b>
                </span>
                <a-descriptions
                  v-if="resizeHeight==0"
                  class="m-t-12"
                  id="description"
                  bordered
                  size="small"
                  :column="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }">
                  <a-descriptions-item
                    v-for="item in baseInfo"
                    :key="item.key"
                    :label="item.label"
                    :span="item.span || 1">
                    {{ form[item.key] }}
                  </a-descriptions-item>
                </a-descriptions>
              </div>
            </div>
          </div>
          <!-- 打包报价 左物料列表，右报价 -->
          <div
            v-if="form.ebiddingWay === '0'"
            class="gutter"
            style="margin-right: 5px">
            <div class="item material">
              <div class="info">
                <a-card
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息')"
                  :bordered="false"
                  :headStyle="headStyle"
                  :bodyStyle="bodyStyle_2"
                >
                  <vxe-grid
                    ref="quoteGrid"
                    v-bind="materialPackGridOptions"
                  >
                  </vxe-grid>
                </a-card>
              </div>
            </div>
            <div class="item pack-quota">
              <div class="info">
                <a-card
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_offer`, '报价')"
                  :bordered="false"
                  :headStyle="headStyle"
                  :bodyStyle="bodyStyle_2"
                >
                  <div class="cardContent">
                    <div class="table">
                      <!-- 打包报价 -->
                      <vxe-grid
                        v-if="form.ebiddingWay === '0'"
                        ref="rankGrid"
                        :height="350+resizeHeight"
                        v-bind="rankGridOptions"
                      >
                        <template slot="empty">
                          <a-empty />
                        </template>
                        <template #price_header="{ column }">
                          <i class="vxe-icon--edit-outline"></i>
                          <span>{{ column.title }}</span>
                          <span class="red">*</span>
                        </template>

                        <template #totalAmount_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInputPack(row)"
                              v-model="row.totalAmount"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额')"
                              type="float"
                              :step="priceStep(row.totalAmount)"
                              @blur="priceStep2(row.totalAmount)"
                              min="0"
                              clearable
                              :disabled="isPriceDisabled"
                              @change="handlePackChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.totalAmount }}</span>
                          </div>
                        </template>

                        <template #netTotalAmount_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInputPack(row)"
                              v-model="row.netTotalAmount"
                              placeholder="打包未税金额"
                              type="float"
                              min="0"
                              clearable
                              :disabled="!isPriceDisabled"
                              :step="priceStep(row.netTotalAmount)"
                              @change="handleNetPackChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.netTotalAmount }}</span>
                          </div>
                        </template>

                        <template #confirmQuantity_default="{ row }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.confirmQuantity"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_qty`, '数量')"
                              type="float"
                              min="0"
                              clearable
                            >
                            </vxe-input>
                            <span v-else>{{ row.confirmQuantity }}</span>
                          </div>
                        </template>

                        <template #supplierRemark_default="{ row }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.supplierRemark"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_remark`, '备注')"
                              clearable
                            >
                            </vxe-input>
                            <span v-else>{{ row.supplierRemark }}</span>
                          </div>
                        </template>
                      </vxe-grid>
                    </div>
                  </div>
                </a-card>
              </div>
            </div>
          </div>
          <!-- 逐条、批量报价 物料列表报价 -->
          <div
            v-else
            class="gutter">
            <div class="item price">
              <div class="info">
                <a-card
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息')"
                  :bordered="false"
                  :headStyle="headStyle"
                  :bodyStyle="bodyStyle_2"
                >
                  <div class="cardContent">
                    <a-button
                      v-if="(form.ebiddingWay === '2' || form.ebiddingWay === '3') && form.ebiddingStatus === '4'"
                      type="default"
                      style=" position:absolute; top: 2px; left:100px;"
                      @click="fillDown">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充') }}</a-button>
                    <div class="table">
                      <vxe-grid
                        v-if="form.ebiddingWay === '3'"
                        ref="quoteGrid"
                        :height="350+resizeHeight"
                        v-bind="materialOnceGridOptions"
                        :row-class-name="handleRowClass"
                        @cell-click="cellClickEvent"
                      >
                        <template slot="empty">
                          <a-empty />
                        </template>
                        <template #price_header="{ column }">
                          <i class="vxe-icon--edit-outline"></i>
                          <span>{{ column.title }}</span>
                          <span class="red">*</span>
                        </template>
                        <template #notQuote_header="{ column }">
                          <vxe-checkbox
                            :disabled="form.ebiddingStatus !== '4'"
                            @change="v => handleNotQuoteAllChange(v)"
                          >
                          </vxe-checkbox>
                          <span>{{ column.title }}</span>
                          <span class="red">*</span>
                        </template>
                        <template #notQuote_default="{ row, rowIndex }">
                          <div style="text-align: center;">
                            <!-- v-if="setQuoteInput(row)" -->
                            <vxe-checkbox
                              v-model="row.notQuote"
                              checked-value="1"
                              unchecked-value="0"
                              :disabled="form.ebiddingStatus !== '4'"
                              @change="v => handleNotQuoteChange(v, { row, rowIndex })"
                            >
                            </vxe-checkbox>
                            <!-- <span v-else>{{ row.notQuote === '1' ? '不报价' : '报价' }}</span> -->
                          </div>
                        </template>

                        <template #price_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.price"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价')"
                              type="float"
                              :step="priceStep(row.price)"
                              min="0"
                              clearable
                              :disabled="isPriceDisabled"
                              @change="handlePriceChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.price }}</span>
                          </div>
                        </template>

                        <template #netPrice_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.netPrice"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价')"
                              type="float"
                              :step="priceStep(row.netPrice)"
                              min="0"
                              clearable
                              :disabled="!isPriceDisabled"
                              @change="handleNetPriceChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.netPrice }}</span>
                          </div>
                        </template>

                        <template #confirmQuantity_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.confirmQuantity"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_qty`, '数量')"
                              type="float"
                              min="0"
                              clearable
                              @change="handleQtyChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.confirmQuantity }}</span>
                          </div>
                        </template>

                        <template #supplierRemark_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.supplierRemark"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_remark`, '备注')"
                              clearable
                              @change="handleRemarkChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.supplierRemark }}</span>
                          </div>
                        </template>
                      </vxe-grid>
                      <!-- 逐条、批量报价 -->
                      <vxe-grid
                        v-else
                        ref="quoteGrid"
                        :height="350+resizeHeight"
                        v-bind="materialGridOptions"
                        :row-class-name="handleRowClass"
                        @cell-click="cellClickEvent"
                      >
                        <template slot="empty">
                          <a-empty />
                        </template>
                        <template #price_header="{ column }">
                          <i class="vxe-icon--edit-outline"></i>
                          <span>{{ column.title }}</span>
                          <span class="red">*</span>
                        </template>

                        <template #price_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.price"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价')"
                              type="float"
                              :step="priceStep(row.price)"
                              min="0"
                              clearable
                              :disabled="isPriceDisabled"
                              @change="handlePriceChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.price }}</span>
                          </div>
                        </template>

                        <template #netPrice_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.netPrice"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价')"
                              type="float"
                              :step="priceStep(row.netPrice)"
                              min="0"
                              clearable
                              :disabled="!isPriceDisabled"
                              @change="handleNetPriceChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.netPrice }}</span>
                          </div>
                        </template>

                        <template #confirmQuantity_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.confirmQuantity"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_qty`, '数量')"
                              type="float"
                              min="0"
                              clearable
                              @change="handleQtyChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.confirmQuantity }}</span>
                          </div>
                        </template>

                        <template #supplierRemark_default="{ row, rowIndex }">
                          <div>
                            <vxe-input
                              v-if="setQuoteInput(row)"
                              v-model="row.supplierRemark"
                              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_remark`, '备注')"
                              clearable
                              @change="handleRemarkChange({ row, rowIndex })"
                            >
                            </vxe-input>
                            <span v-else>{{ row.supplierRemark }}</span>
                          </div>
                        </template>
                      </vxe-grid>
                    </div>
                  </div>
                </a-card>
              </div>
            </div>
          </div>
          <div
            class="gutter"
            v-if="form.supplierShow">
            <div class="item price">
              <div class="info">
                <vxe-grid
                  ref="rankGrid"
                  v-bind="supplierGridOptions">
                </vxe-grid>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>

    <a-modal
      v-drag
      v-model="openUpper"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_setAmplitudeUpper`, '设置竞价幅度上限') "
      @ok="setUpper">
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_amplitudeUpper`, '幅度上限') }}：
      <a-input-number
        v-model="changeRangeUpper"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterAmplitudeUpper`, '请输竞价幅度上限')" /><br>
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_rangeUnit`, '幅度单位') }}：<span>{{ form.rangeUnit_dictText }}</span><br>
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_amplitudeLower`, '幅度下限') }}：<span>{{ form.changeRange }}</span>
    </a-modal>
  </div>
</template>

<script lang='jsx'>
const resizeChartMethod = '$__resizeMethod'

import { getAction } from '@/api/manage'
import { debounce } from 'lodash'
import { apiSaleQueryBidLobbyDetail, apiSaleQueryBidLobbyQuote, apiSaleHisQueryBidLobbyDetail, apiSaleQuotePrice, apiSaleSetUpper, apiSetOnlineAccount } from '@/api/apiBidding.js'
import { currency } from '@/filters'
import { materialGridOptions, materialOnceGridOptions, quoteGridOptions, rankGridOptions, chartGridOptions, materialPackGridOptions } from '../../gridConfig/sale'
import { isDecimal } from '@/utils/validate.js'
import {USER_INFO} from '@/store/mutation-types'
import { add, mul, div } from '@/utils/mathFloat.js' // 加 减 乘 除
import LineChart from '../../components/LineChart'
import { mapActions, mapGetters } from 'vuex'
import countdown from '@/components/countdown/index.js'
import {nominalEdgePullWhiteBlack } from '@/utils/util.js'
import { closeWS, createSocket } from '../../websocket.js'

const PACK = '0' // 打包
const ONEBYONE = '1' // 逐条
const BATCH = '2' // 批量
const ONCE = '3' // 批量

export default {
    components: {
        LineChart,
        countdown
        // QuotePrice
        // Breadcrumb
    },
    filters: {
        currency
    },
    data () {
        return {
            materialGridOptions,
            materialOnceGridOptions, // 一次性
            quoteGridOptions,
            rankGridOptions, // 打包报价
            materialPackGridOptions, // 打包 物料列表
            supplierGridOptions: chartGridOptions, // 供应商列表 排名、报价信息
            current: -1,
            currentItemNumber: -1,
            deadline: 0,
            countdownText: '',
            gt1900: false,
            spinning: false,
            delayTime: 300,
            headStyle: {
                borderBottom: 'none',
                padding: '0 6px',
                minHeight: '34px',
                lineHeight: '34px',
                height: '40px'
            },
            valueStyle: {
                fontSize: '24px',
                color: '#f41616'
            },
            bodyStyle: {
                padding: '0 24px 24px'
            },
            bodyStyle_2: {
                padding: '0 6px 24px 6px'
            },
            progress: {
                percent: 78,
                status: 'active',
                strokeColor: {
                    from: '#4892ff',
                    to: '#596fe1'
                }
            },
            endTime: 0,
            minPrice: 1200,
            form: {
                ebiddingNumber: '', // 竞价单号
                ebiddingType: '', // 竞价方式
                changeRange: '', // 价格调整幅度
                rangeUnit: '', // 幅度单位
                beginTime: '', // 竞价开始时间  
                endTime: '', // 竞价结束时间
                keepMinute: '', // 每轮持续时间
                intervalMinute: '', // 每轮间隔时间
                currentRound: '',
                ebiddingWay: '',
                id: '',
                itemNumber: ''
            },
            chartData: {},
            // websocket: null,
            socket: null,
            lockReconnect: false, //是否真正建立连接
            timeout: 30*1000, //30秒一次心跳
            timeoutObj: null, //心跳心跳倒计时
            serverTimeoutObj: null, //心跳倒计时
            timeoutnum: null, //断开 重连倒计时
            visible: false,
            openUpper: false,
            changeRangeUpper: undefined,
            onceQuoteData: null,
            url: {
                detail: '/ebidding/saleEbiddingHead/queryById'
            },
            wsOnlineUrl: '',
            serivceUrl: '',
            elsAccount: '',
            currentMaterialNumName: null,
            currentMaterialStatus: null,
            refreshBtnHandle: true, // 手动刷新，重置数据，默认手动刷新
            cellClickObj: {},
            resizeHeight: 0,
            currentEndMaterialItemNumber: 1
        }
    },
    computed: {
        ...mapGetters(['getOnlineSuppliers', 'getOnlineID', 'getPageRefreshTime']),
        getCurrentTitle () {
            if (this.form.ebiddingWay === '1' && ['3', '4'].includes(this.form.ebiddingStatus)) { // 逐条报价、竞价中
                return this.currentMaterialNumName ? `【 ${ this.currentMaterialNumName } 】` : ''
            } else {
                return ''
            }
        },
        getToggleStatu (){
            let lable=this.resizeHeight==0?this.$srmI18n(`${this.$getLangAccount()}#i18n_title_putAway`, '收起'):this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expand`, '展开')
            return lable
        },
        getCurrentMaterialStatus () {
            if (this.form.ebiddingWay === '1' && ['3', '4'].includes(this.form.ebiddingStatus)) { // 逐条报价、竞价中
                return this.currentMaterialStatus ? `${ this.currentMaterialStatus }` : ''
            } else {
                return this.form.ebiddingStatus_dictText
            }
        },
        baseInfo () {
            const { ebiddingWay = '', rangeUnit_dictText} = this.form || {}
            if (ebiddingWay === PACK) {
                return [
                    // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingNumber`, '竞价单号'), key: 'ebiddingNumber', show: true, span: '4' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'), key: 'ebiddingStatus_dictText', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingType`, '竞价类型'), key: 'ebiddingType_dictText', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quotePriceWay`, '报价方式'), key: 'ebiddingWay_dictText', show: true },
                    { label: (this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingRange`, '竞价幅度'))+`(${rangeUnit_dictText})`, key: 'changeRange', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AOKI_2787c0d7`, '启动时间'), key: 'startTime', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_endTime`, '结束时间'), key: 'endTime', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIzs_63db992`, '持续时间(分钟)'), key: 'keepMinute' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ImKIzs_cd425da6`, '间隔时间(分钟)'), key: 'intervalMinute' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yWKIPzs_1ed46e2b`, '结束时间前(分钟)'), key: 'beforeEndMinute', show: true }, // 分钟内，有报价
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OAKIzs_ca46caaf`, '延期时间(分钟)'), key: 'delayMinute', show: true }, // 分钟
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packageStartingUnitPrice`, '打包起拍价'), key: 'startTotalAmount' } // 打包起拍价 - 打包竞价显示
                ]
            }
            return [
                // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingNumber`, '竞价单号'), key: 'ebiddingNumber', show: true, span: '4' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'), key: 'ebiddingStatus_dictText', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingType`, '竞价类型'), key: 'ebiddingType_dictText', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quotePriceWay`, '报价方式'), key: 'ebiddingWay_dictText', show: true },
                { label: (this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingRange`, '竞价幅度'))+`(${rangeUnit_dictText})`, key: 'changeRange', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AOKI_2787c0d7`, '启动时间'), key: 'startTime', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_endTime`, '结束时间'), key: 'endTime', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIzs_63db992`, '持续时间(分钟)'), key: 'keepMinute' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ImKIzs_cd425da6`, '间隔时间(分钟)'), key: 'intervalMinute' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yWKIPzs_1ed46e2b`, '结束时间前(分钟)'), key: 'beforeEndMinute', show: true }, // 分钟内，有报价
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OAKIzs_ca46caaf`, '延期时间(分钟)'), key: 'delayMinute', show: true } // 分钟
            ]
        },
        // 反否反向竞价
        isReverse () {
            const { ebiddingType = '' } = this.form || {}
            return ebiddingType === '1'
        },
        // 是否打包状态
        isPack () {
            const { ebiddingWay = '' } = this.form || {}
            return ebiddingWay === PACK
        },
        // 含税单价是否可输入
        isPriceDisabled () {
            return this.form.quoteType === '1'
        },
        getProp () {
            /**
             * ebiddingWay, 0: 打包, 1: 逐条
             * quoteType, 0: 含税价, 1: 不含税价
             */
            const { ebiddingWay = '1', quoteType = '0' } = this.form || {}
            const key = `${ebiddingWay + ''}${quoteType + ''}`
            const map = {
                '10': 'price',
                '11': 'netPrice',
                '00': 'totalAmount',
                '01': 'netTotalAmount'
            }
            return map[key] || 'price'
        }
    },

    methods: {
        ...mapActions(['SetOnlineWS', 'CloseOnlineWS']),
        //关闭页面
        windowOpenClose (){
            window.close()
        },
        //收取和展开逻辑
        changeToggle (){
            if(document.getElementById('description')){
                let domHeight=document.getElementById('description').clientHeight
                this.resizeHeight=domHeight
            }else{
                this.resizeHeight=0
            }

        },
        fillDown () {
            const { field, rowIndex } = this.cellClickObj
            if (!['netPrice', 'price', 'confirmQuantity', 'supplierRemark'].includes(field)) return

            if (this.form.ebiddingWay === ONCE) this.materialOnceGridOptions.data = this.materialOnceGridOptions.data.map((i, index) => {
                if (index > rowIndex) {
                    i[field] = this.materialOnceGridOptions.data[rowIndex][field]
                    if (field === 'price') i.netPrice = this.materialOnceGridOptions.data[rowIndex].netPrice
                    if (field === 'netPrice') i.price = this.materialOnceGridOptions.data[rowIndex].price
                }
                return i
            })
            else this.materialGridOptions.data = this.materialGridOptions.data.map((i, index) => {
                if (index > rowIndex) {
                    i[field] = this.materialGridOptions.data[rowIndex][field]
                    if (field === 'price') i.netPrice = this.materialGridOptions.data[rowIndex].netPrice
                    if (field === 'netPrice') i.price = this.materialGridOptions.data[rowIndex].price
                }
                return i
            })
        },
        handleQtyChange ({ row, rowIndex }) {
            // const { confirmQuantity } = row
            // this.cellClickObj = { field: 'confirmQuantity', rowIndex, data: confirmQuantity }
        },
        handleRemarkChange ({ row, rowIndex }) {
            // const { supplierRemark } = row
            // this.cellClickObj = { field: 'supplierRemark', rowIndex, data: supplierRemark }
        },
        priceStep (price) {
            const rangeUnit = this.form.rangeUnit // 幅度单位 0 金额，1 百分比
            const changeRange = this.form.changeRange // 幅度
            if (rangeUnit === '0') return changeRange
         
            return Math.ceil(changeRange / 100 * price*100)/100
        },
        priceStep2 (price) {
            const rangeUnit = this.form.rangeUnit // 幅度单位 0 金额，1 百分比
            const changeRange = this.form.changeRange // 幅度
            if (rangeUnit === '0') return changeRange
            console.log('Number((changeRange / 100 * price).toFixed(2))', changeRange, price, Number((changeRange / 100 * price)), Math.ceil(changeRange / 100 * price*100)/100)
      
        },
        getOnlineWebsocketUrl () {
            let url = this.serivceUrl.replace('https://', 'wss://').replace('http://', 'ws://')
            this.wsOnlineUrl = `${url}/els/websocket/online/${this.$route.query.relationId}/${this.elsAccount}`
            createSocket(this.wsOnlineUrl)
        },
        async getSocketData (newVal) {
            console.log('getSocketData newVal.detail', newVal.detail.data)
            const message = newVal.detail.data

            if (message === this.$route.query.relationId) {
                this.refreshBtnHandle = false
                if (this.form && (this.form.ebiddingStatus === '3'|| this.form && this.form.ebiddingStatus === '4')) this.refresh(false)
                else {
                    await this.refreshMaterialList() // 更新物料列表
                    await this.refreshSupplierList() // 更新供应商、排名列表
                }
                this.refreshBtnHandle = true
                return
            }
        },
        sendRefresh (time = 3000) {
            this.setIntervalWesocketPush && clearInterval(this.setIntervalWesocketPush)
            this.setIntervalWesocketPush = setInterval(() => {
                this.sendRefresh()
                apiSetOnlineAccount({ headRelationId: this.$route.query.relationId }).then(res => {
                    console.log('res', res)
                })
            }, time)
        },
        setQuoteInput (row) {
            return this.form.ebiddingStatus === '4'
            && ((this.currentItemNumber === row.itemNumber && this.form.ebiddingWay === ONEBYONE) 
              || this.form.ebiddingWay !== ONEBYONE)
        },
        setQuoteInputPack (row) {
            return this.form.ebiddingStatus === '4' && row.elsAccount === this.elsAccount
        },
        getMessage (msg) {
            console.log(msg.data)
            this.refresh()
            this.reset()
        },
        goBack () {
            closeWS()
            // this.CloseOnlineWS()
            this.closeCurrent()
        },
        handleSuccess () {
            this.refresh()
        },
        refreshBtn () {
            this.refreshBtnHandle = true
            this.getData()
        },
        refresh: debounce(function (spinning = true) {
            this.getData(spinning)
        }, 1000 ),
        handleFinish () {
            console.log('handleFinish ===============================')
            this.refreshBtn()
        },
        getData (spinning = true) {
            this.getQueryData().then(res=>{
                if (res.result && res.result.ebiddingStatus==='5') {
                    this.$notification.open({
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                        description: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingOver`, '竞价结束')}`,
                        icon: <a-icon type="sound" style="color: #108ee9" />,
                        duration: 10
                    })
                } else {
                    if(this.currentEndMaterialItemNumber!=this.currentItemNumber){
                        if(this.currentMaterialNumName&&this.currentItemNumber!='1'){
                            let materNumber=this.materialGridOptions.data.find(val=> this.currentEndMaterialItemNumber==val.itemNumber  )
                            this.$notification.open({
                                message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                                description: `${materNumber.materialNumber}-${materNumber.materialDesc}${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingOver`, '竞价结束')},切换到下一个物料`,
                                icon: <a-icon type="sound" style="color: #108ee9" />,
                                duration: 10
                            })
                        }
                        this.currentEndMaterialItemNumber=this.currentItemNumber
                    }
                }
                this.sendRefresh()
                if (spinning) this.spinning = true
            
                const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))
                const { id, busAccount } = this.$route.query
                const params = {
                    id,
                    busAccount,
                    itemNumber: this.form.ebiddingWay === PACK ? '' : this.currentItemNumber
                }
                const promiseList = [
                    apiSaleQueryBidLobbyDetail(params)
                ]
                Promise.all(handlePromise(promiseList)).then(res => {
                    const [infoRes] = res || []
                    if (infoRes && infoRes.status === 'success') {
                        this.fixInfoData(infoRes.res)
                    }
                    this.getItemData()
                    // 报价
                    // this.getQuoteTableData()
                })

            })
     

        },
        getItemData (num = '') {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))
            const { id, busAccount } = this.$route.query
            const params = {
                id,
                busAccount,
                itemNumber: num || this.currentItemNumber
            }
            const promiseList = [
                apiSaleQueryBidLobbyQuote(params),
                apiSaleHisQueryBidLobbyDetail(params)
            ]
            Promise.all(handlePromise(promiseList)).then((res) => {
                this.spinning = false
                const [itemRes] = res || []
                if (itemRes && itemRes.status === 'success') {
                    this.fixItemData(itemRes.res)
                }
                // 报价
                this.getQuoteTableData()
            })

        },
        // 倒计时计算
        checkTime () {
            let { beginTime, beginTime_DateMaps,  endTime, endTime_DateMaps, ebiddingStatus } = this.form
            const { serviceTime } = this.form
            const now = serviceTime || Date.now()
            beginTime = beginTime ? beginTime_DateMaps : Date.now()
            endTime = endTime ? endTime_DateMaps : Date.now()
            if(ebiddingStatus === '3'){ // 待竞价状态
                if (now < beginTime) {
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OuvKutK_bc29df23`, '竞价开始倒计时')
                    this.deadline = beginTime - now
                }else{
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n__EonRvAOu_6f8beeea`, '等待采购开启竞价')
                    this.deadline = 0
                }
            }else if(ebiddingStatus === '4'){ // 竞价中状态
                if(now < endTime) {
                    this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIutK_b713bdfd`, '持续时间倒计时')
                    this.deadline = endTime - now
                }else{
                    this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n__EonRyWOu_7e4b6167`, '等待采购结束竞价')
                    this.deadline = 0
                }
            }else{
                this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SutK_2f8d3027`, '无倒计时')
                this.deadline = 0
            }
        },
        fixInfoData (res) {
            let { saleEbiddingItemList = [], ebiddingWay, currentItemNumber, ...others } = res.result || {}
            console.log('this.refreshBtnHandle', this.refreshBtnHandle)
            if (this.refreshBtnHandle) {
                if (ebiddingWay === PACK) this.materialPackGridOptions.data = saleEbiddingItemList
                else{
                    if (this.form.ebiddingStatus!='5') {
                        if (ebiddingWay == BATCH) {
                            saleEbiddingItemList = this.materialGridOptions.data.length > 0 ? this.materialGridOptions.data : saleEbiddingItemList
                        } else if (ebiddingWay == ONCE) {
                            saleEbiddingItemList = this.materialOnceGridOptions.data.length > 0 ? this.materialOnceGridOptions.data : saleEbiddingItemList
                            // let curMaterialIndex = this.materialOnceGridOptions.data.findIndex(val=>val.itemNumber==this.currentItemNumber)
                            // if (curMaterialIndex > -1
                            //     && (this.materialOnceGridOptions.data[curMaterialIndex].netPrice || this.materialOnceGridOptions.data[curMaterialIndex].price || this.materialOnceGridOptions.data[curMaterialIndex].confirmQuantity || this.materialOnceGridOptions.data[curMaterialIndex].supplierRemark)
                            // ) {
                            //     this.materialOnceGridOptions.data[curMaterialIndex].quoteCount = saleEbiddingItemList[curMaterialIndex].quoteCount
                            //     saleEbiddingItemList[curMaterialIndex] = this.materialOnceGridOptions.data[curMaterialIndex]
                            // }
                        } else {
                            let curMaterialIndex = this.materialGridOptions.data.findIndex(val=>val.itemNumber==this.currentItemNumber)
                            if (curMaterialIndex > -1
                                && (this.materialGridOptions.data[curMaterialIndex].netPrice || this.materialGridOptions.data[curMaterialIndex].price || this.materialGridOptions.data[curMaterialIndex].confirmQuantity || this.materialGridOptions.data[curMaterialIndex].supplierRemark)
                            ) {
                                this.materialGridOptions.data[curMaterialIndex].quoteCount = saleEbiddingItemList[curMaterialIndex].quoteCount
                                saleEbiddingItemList[curMaterialIndex] = this.materialGridOptions.data[curMaterialIndex]
                            }
                        }
                    }
                    if (ebiddingWay === ONCE) this.materialOnceGridOptions.data = saleEbiddingItemList
                    else this.materialGridOptions.data = saleEbiddingItemList
                } 
            } else {
                if (ebiddingWay === PACK) this.materialPackGridOptions.data = this.materialPackGridOptions.data || saleEbiddingItemList
                else {
                    if (ebiddingWay === ONCE) this.materialOnceGridOptions.data = this.materialOnceGridOptions.data.map(i => {
                        const newItem = saleEbiddingItemList.find(item => item.itemNumber === i.itemNumber)
                        i.itemStatus = newItem.itemStatus
                        i.itemStatus_dictText = newItem.itemStatus_dictText
                        i.quoteCount = newItem.quoteCount
                        console.log('i', i, i.price)
                        return i
                    })
                    else this.materialGridOptions.data = this.materialGridOptions.data.map(i => {
                        const newItem = saleEbiddingItemList.find(item => item.itemNumber === i.itemNumber)
                        i.itemStatus = newItem.itemStatus
                        i.itemStatus_dictText = newItem.itemStatus_dictText
                        i.quoteCount = newItem.quoteCount
                        console.log('i', i, i.price)
                        return i
                    })
                }
            }
            this.currentItemNumber = currentItemNumber || '1'
            if (this.current === -1 || !this.current) this.current = this.currentItemNumber
            const itemNumber = this.current === -1 ? this.currentItemNumber : this.current
            const currentMaterial = saleEbiddingItemList.find(i => i.itemNumber === itemNumber)
            if (currentMaterial) {
                const { materialNumber, materialName, itemStatus_dictText } = currentMaterial
                this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
            }
            this.form = {
                ebiddingWay,
                currentItemNumber: this.currentItemNumber,
                ...others,
                serviceTime: res.timestamp
            }
            // 逐条，获取起拍价格
            if (ebiddingWay === ONEBYONE) {
                let i = this.currentItemNumber - 1
                this.form.startPrice = saleEbiddingItemList[i].startPrice
            }
            this.checkTime()
        },
        fixItemData ({ result }) {
            const { saleEbiddingItemList = [], ebiddingWay } = result || {}
            this.supplierGridOptions.data = JSON.parse(JSON.stringify(saleEbiddingItemList))
            // this.rankGridOptions.data = saleEbiddingItemList.filter(i => i.elsAccount === this.elsAccount)
            if (this.refreshBtnHandle) {
                if (ebiddingWay === PACK){
                    let tmSaleEbiddingItemList=saleEbiddingItemList.filter(i => i.elsAccount === this.elsAccount)
                    if(this.form.ebiddingStatus!='5'){
                        if(this.rankGridOptions.data.length>0){
                            this.rankGridOptions.data.forEach((val, idx)=>{
                                if(tmSaleEbiddingItemList.length>0){
                                    tmSaleEbiddingItemList[idx].netTotalAmount=val.netTotalAmount
                                    tmSaleEbiddingItemList[idx].totalAmount=val.totalAmount
                                    tmSaleEbiddingItemList[idx].confirmQuantity=val.confirmQuantity
                                }
                            })

                        } 
                    }
                    this.rankGridOptions.data = tmSaleEbiddingItemList
                }
            } else {
                if (ebiddingWay === PACK){
                    this.rankGridOptions.data =
                        this.rankGridOptions.data.length > 0&& this.form.ebiddingStatus!='5'? this.rankGridOptions.data : saleEbiddingItemList.filter(i => i.elsAccount === this.elsAccount)
                }
                   
            }
        },
        cellClickEvent ({ row, column, rowIndex }) {
            const field = column.field
            this.cellClickObj = { field, rowIndex }

            const { itemNumber= '', materialNumber, materialName, itemStatus_dictText } = row || {}
            this.current = itemNumber
            this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
            this.getItemData(this.current)
        },
        setCurrentMaterialInfo (materialNumber = null, materialName = null, itemStatus_dictText = null) {
            this.currentMaterialNumName = `${materialNumber} - ${materialName}`
            this.currentMaterialStatus = itemStatus_dictText
        },
        setRankTableColumn () {
            this.rankGridOptions.columns.forEach(item => {
                if (item.field === 'price') {
                    item.visible = !this.isPack
                }
            })
        },
        getScreenWidth () {
            const clientWidth = document.documentElement.clientWidth
            this.gt1900 = (clientWidth >= 1900)
        },
        async getQueryData () {
            const res = await getAction(this.url.detail, { id: this.$route.query.id })
            if (res && res.success) {
                this.currentItemNumber = res.result.currentItemNumber
                const { ebiddingNumber, id } = this.$route.query || {}
                this.current = this.currentItemNumber
                this.changeRangeUpper=res.result.changeRangeUpper
                this.form = Object.assign({}, this.form, {
                    relationId: res.result.relationId,
                    ebiddingNumber,
                    currentItemNumber: this.currentItemNumber,
                    id
                })
            }
            return res
        },
        // 通过lodash的防抖函数来控制resize的频率
        [resizeChartMethod]: debounce(function () {
            this.getScreenWidth()
        }, 200),
        getQuoteTableData () {
            this.quoteGridOptions.data = []
            let index = this.currentItemNumber
            let tableData = this.materialGridOptions.data
            let { taxRate = 0, priceUnit = '', currency = '', effectiveDate='', expiryDate='' } = tableData.find(n => n.itemNumber === index) || {}
            taxRate = isDecimal(taxRate) ? taxRate : 0
            const row = {
                taxRate,
                priceUnit,
                currency,
                effectiveDate,
                expiryDate,
                price: '',
                netPrice: ''
            }
            this.quoteGridOptions.data.push(row)
        },
        // 打包：
        // 打包未税金额 netTotalAmount, 实时计算: 打包含税金额 totalAmount
        // 不含税价 = 含税价 / (1 + 税率 / 100)
        handleNetPackChange (data) {
            const { netTotalAmount } = data.row

            const rowIndex = data.rowIndex
            if (!netTotalAmount || !isDecimal(netTotalAmount)) return
            
            let { taxRate = 0 } = this.rankGridOptions.data[rowIndex] || {}
            if (!taxRate || !isDecimal(taxRate)) {
                taxRate = 0
            }
            let tax = add(1, div(taxRate, 100))
            let totalAmount = mul(netTotalAmount, tax)
            totalAmount = totalAmount.toFixed(2)
            this.rankGridOptions.data[rowIndex].totalAmount = totalAmount
        },
        // 打包：
        // 打包含税金额 totalAmount, 实时计算: 打包未税金额 netTotalAmount
        // 不含税价 = 含税价 / (1 + 税率 / 100)
        handlePackChange (data) {
            const { totalAmount } = data.row
            const rowIndex = data.rowIndex
            if (!totalAmount || !isDecimal(totalAmount)) return
            let { taxRate = 0 } = this.rankGridOptions.data[rowIndex] || {}
            if (!taxRate || !isDecimal(taxRate)) {
                taxRate = 0
            }
            let tax = add(1, div(taxRate, 100))
            let netTotalAmount = div(totalAmount, tax)
            netTotalAmount = netTotalAmount.toFixed(2)
            this.rankGridOptions.data[rowIndex].netTotalAmount = netTotalAmount
        },
        handleNotQuoteAllChange (v) {
            const { checked } = v
            this.materialOnceGridOptions.data.forEach((i, index) => {
                this.$set(this.materialOnceGridOptions.data[index], 'notQuote', checked ? '1' : '0')
            })
        },
        handleNotQuoteChange (v, data) {
            this.$set(this.materialOnceGridOptions.data[data.rowIndex], 'notQuote', v.checked ? '1' : '0')
        },
        handlePriceChange (data) {
            const { price } = data.row

            // this.cellClickObj = { field: 'price', rowIndex: data.rowIndex, data: price }

            const rowIndex = data.rowIndex
            if (!price || !isDecimal(price)) return
            let { taxRate = 0 } = this.form.ebiddingWay !== '3' ? (this.materialGridOptions.data[rowIndex] || {}) : (this.materialOnceGridOptions.data[rowIndex] || {})
            if (!taxRate || !isDecimal(taxRate)) {
                taxRate = 0
            }
            let tax = add(1, div(taxRate, 100))
            let netPrice = div(price, tax)
            netPrice = netPrice.toFixed(2)
            // this.materialGridOptions.data[rowIndex].netPrice = netPrice
            this.$set(this.form.ebiddingWay !== '3' ? this.materialGridOptions.data[rowIndex] : this.materialOnceGridOptions.data[rowIndex], 'netPrice', netPrice)
        },
        // 未税单价 netPrice, 实时计算: 含税价 price
        // 含税价 = 不含税价 * (1 + 税率 / 100)
        handleNetPriceChange (data) {
            const { netPrice } = data.row

            // this.cellClickObj = { field: 'netPrice', rowIndex: data.rowIndex, data: netPrice }

            const rowIndex = data.rowIndex
            if (!netPrice || !isDecimal(netPrice)) return
            
            let { taxRate = 0 } = this.form.ebiddingWay !== '3' ? (this.materialGridOptions.data[rowIndex] || {}) : (this.materialOnceGridOptions.data[rowIndex] || {})
            if (!taxRate || !isDecimal(taxRate)) {
                taxRate = 0
            }
            let tax = add(1, div(taxRate, 100))
            let price = mul(netPrice, tax)
            price = price.toFixed(2)
            // this.materialGridOptions.data[rowIndex].price = price
            this.$set(this.form.ebiddingWay !== '3' ? this.materialGridOptions.data[rowIndex] : this.materialOnceGridOptions.data[rowIndex], 'price', price)
        },
        submit () {
            if (this.form.ebiddingWay === ONEBYONE) {
                const currentQuoteItem = this.materialGridOptions.data.find(i => i.itemNumber === this.currentItemNumber) || {}
                const { price, netPrice } = currentQuoteItem // , effectiveDate, expiryDate
                if (this.isPriceDisabled) {
                    if (!netPrice) {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LftulS_e9e2152c`, '未税单价必填'))
                        return
                    }
                } else {
                    if (!price) {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_xftulS_7f8320cb`, '含税单价必填'))
                        return
                    }
                }
            } else if (this.form.ebiddingWay === BATCH) {
                const netPriceIndexs = []
                const priceIndexs = []
                const effectiveDateIndexs = []
                const expiryDateIndexs = []
                this.materialGridOptions.data.forEach((item, index) => {
                    const { price, netPrice, effectiveDate, expiryDate } = item
                    if (this.isPriceDisabled) {
                        if (!netPrice) {
                            netPriceIndexs.push(index + 1)
                        }
                    } else {
                        if (!price) {
                            priceIndexs.push(index + 1)
                        }
                    }
                    if (!effectiveDate) {
                        effectiveDateIndexs.push(index + 1)
                    }
                    if (!expiryDate) {
                        expiryDateIndexs.push(index + 1)
                    }
                })
                if (netPriceIndexs.length > 0) {
                    this.$message.error(`${netPriceIndexs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LftulS_e9e2152c`, '未税单价必填')}`)
                    return
                }
                if (priceIndexs.length > 0) {
                    this.$message.error(`${priceIndexs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_xftulS_7f8320cb`, '含税单价必填')}`)
                    return
                }
            } else if (this.form.ebiddingWay === ONCE) {
                const netPriceIndexs = []
                const priceIndexs = []
                const effectiveDateIndexs = []
                const expiryDateIndexs = []
                this.materialOnceGridOptions.data.forEach((item, index) => {
                    const { price, netPrice, effectiveDate, expiryDate, notQuote } = item
                    if (this.isPriceDisabled) {
                        if (!netPrice) {
                            netPriceIndexs.push(index + 1)
                        }
                    } else {
                        if (!price && notQuote != '1') {
                            priceIndexs.push(index + 1)
                        }
                    }
                    if (!effectiveDate) {
                        effectiveDateIndexs.push(index + 1)
                    }
                    if (!expiryDate) {
                        expiryDateIndexs.push(index + 1)
                    }
                })
                if (netPriceIndexs.length > 0) {
                    this.$message.error(`${netPriceIndexs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LftulS_e9e2152c`, '未税单价必填')}`)
                    return
                }
                if (priceIndexs.length > 0) {
                    this.$message.error(`${priceIndexs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_xftulS_7f8320cb`, '含税单价必填')}`)
                    return
                }
            } else if (this.form.ebiddingWay === PACK) {
                const currentQuoteItem = this.rankGridOptions.data.filter(i => i.elsAccount === this.elsAccount)[0]
                const { totalAmount, netTotalAmount } = currentQuoteItem
                if (this.isPriceDisabled) {
                    if (!netTotalAmount) {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n__fsLfHflS_964212e8`, '打包未税金额必填'))
                        return
                    }
                } else {
                    if (!totalAmount) {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n__fsxfHflS_2be31e87`, '打包含税金额必填'))
                        return
                    }
                }
            }
            const callback = () => {
                let saleEbiddingItemList = []
                if (this.form.ebiddingWay === ONEBYONE) {
                    const currentQuoteItem = this.materialGridOptions.data.find(i => i.itemNumber === this.currentItemNumber) || {}
                    const { price, netPrice, supplierRemark, effectiveDate, expiryDate } = currentQuoteItem
                    let saleQuoteItem = currentQuoteItem || {}
                    saleQuoteItem.supplierRemark = supplierRemark
                    saleQuoteItem.effectiveDate = effectiveDate
                    saleQuoteItem.expiryDate = expiryDate
                    // 确定取值为 含税价 与 不含税价
                    const val = ['price', 'totalAmount'].includes(this.getProp)
                        ? price
                        : netPrice
                    saleQuoteItem[this.getProp] = val
                    saleEbiddingItemList.push(saleQuoteItem)
                } else if (this.form.ebiddingWay === BATCH) {
                    saleEbiddingItemList = this.materialGridOptions.data
                } else if (this.form.ebiddingWay === ONCE) {
                    saleEbiddingItemList = this.materialOnceGridOptions.data
                } else if (this.form.ebiddingWay === PACK) {
                    saleEbiddingItemList = this.rankGridOptions.data.filter(i => i.elsAccount === this.elsAccount)
                }
                const params = {
                    ...this.form,
                    saleEbiddingItemList: saleEbiddingItemList
                }
                this.spinning = true
                apiSaleQuotePrice(params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.spinning = false
                    this.$message[type](res.message)
                    if (!res.success) return
                    this.$emit('success')
                    this.getQuoteTableData()
                    this.refreshBtn()
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_RLsu_38d6fc68`, '确认报价'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherQuote`, '确认是否报价'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        openRange (){
            this.openUpper = true
            // this.changeRangeUpper = ''
        },
        setUpper (){
            if(this.changeRangeUpper == ''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BzXWxOLV_1bde1ed7`, '幅度上限不能为空'))
                return
            }
            if(String(this.changeRangeUpper).length>12){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BzXWHzxOBRWW_28796c21`, '幅度上限长度不能超过12'))
                return
            }
            if(this.changeRangeUpper <= this.form.changeRange){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BzXWlTfUOuBzIW_f0bd5fa3`, '幅度上限必须大于竞价幅度下限'))
                return
            }
            let params = {}
            const that = this
            params['id'] = this.form.id
            params['changeRangeUpper'] = this.changeRangeUpper
            apiSaleSetUpper(params).then(res => {
                const type = res.success ? 'success' : 'error'
                that.openUpper = false
                this.spinning = false
                this.$message[type](res.message)
                this.$emit('success')
            }).finally(() => {
                that.refreshBtn()
            })
        },
        init () {
            this.refreshBtnHandle = true
            this.spinning = true
            //  this.getQueryData()
            this.getData()
        },
        handleRowClass ({ row }) {
            const { itemNumber = '' } = row || {}
            if (itemNumber === this.current) {
                return 'row--current'
            }
        },
        async refreshSupplierList () {
            const { id, busAccount } = this.$route.query
            const params = {
                id,
                busAccount,
                itemNumber: this.currentItemNumber
            }
            const res = await apiSaleQueryBidLobbyQuote(params)
            this.fixItemData(res)
        },
        async refreshMaterialList () {
            const { id, busAccount } = this.$route.query
            const params = {
                id,
                busAccount,
                itemNumber: this.current === -1 ? this.currentItemNumber : this.current
            }
            const res = await apiSaleQueryBidLobbyDetail(params)
            this.form = res.result
            if (this.form.ebiddingWay === ONCE) this.materialOnceGridOptions.data = this.materialOnceGridOptions.data || res.result.saleEbiddingItemList
            else this.materialGridOptions.data = this.materialGridOptions.data || res.result.saleEbiddingItemList
            const currentMaterial = this.materialGridOptions.data.find(i => i.itemNumber === this.current)
            if (currentMaterial) {
                const { materialNumber, materialName, itemStatus_dictText } = currentMaterial
                this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
            }
        },
        setOnline () {
            apiSetOnlineAccount({ headRelationId: this.$route.query.relationId }).then(res => {
                console.log('res', res)
            })
        }
    },
    watch: {
        '$route': {
            handler ({ path }) {
                console.log('$route path', path)
                if (path !== '/ebidding/saleLobbyNew') {
                    return
                }
                this.init()
            },
            immediate: true
        },
        getOnlineID: {
            handler (newVal, oldVal) {
                console.log('getOnlineID newVal refresh', newVal)
                if (newVal && oldVal && newVal.id === this.$route.query.relationId && oldVal.time !== newVal.time) {
                    if (this.form.ebiddingStatus === '3') this.refresh()
                    else {
                        this.refreshMaterialList() // 更新物料列表
                        this.refreshSupplierList() // 更新供应商、排名列表
                    }
                }
            },
            immediate: true,
            deep: true
        },
        getPageRefreshTime: {
            handler (newVal) {
                console.log('getPageRefreshTime newVal', newVal)
                if (newVal && newVal.id === this.$route.query.relationId) {
                    this.refresh()
                }
                this.setOnline()
            },
            immediate: true,
            deep: true
        }
    },
    created () {
        this.onceQuoteData = this.getQuoteTableData
        this.getScreenWidth()
        let { serivceUrl = '', elsAccount = '' } = this.$ls.get(USER_INFO) || {}
        this.serivceUrl = serivceUrl
        this.elsAccount = elsAccount
        this.getOnlineWebsocketUrl()
        this.refreshBtnHandle = true
        // this.getData()
        nominalEdgePullWhiteBlack()
        this.$nextTick(()=>{
            this.changeToggle()
        })
    },
    activated () {
        console.log('activated ------------------------- activated')
        this.getOnlineWebsocketUrl()
        this.sendRefresh()
        window.addEventListener('resize', this[resizeChartMethod])
    },
    mounted () {
        console.log('mounted ------------------------- mounted')
        window.addEventListener('onmessageWS', this.getSocketData)
    },
    beforeDestroy () {
        console.log('beforeDestroy ------------------------- beforeDestroy')
        window.removeEventListener('onmessageWS', this.getSocketData)
        window.removeEventListener('reisze', this[resizeChartMethod])
    }
}
</script>

<style lang="less" scoped>
@red: #f41616;
@blue: #1690ff;
.m-l-24{
  margin-left:36px!important;
}
.m-t-12{
  margin-top:12px
}
.inline-flot{
  display: inline-block;
  float: right;
  font-size: 14px;
}
.saleBuyBid {
    background-color: #eaeaea;
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    .redTxt {
      color: @red;
    }
    .blue {
      color: #1890ff;
    }
    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px;
        background: #fff;
        position: fixed;
        // width: calc(100% - 200px);
        width: 100%;
        position: absolute;
        height: 44px;
        z-index: 99;
        .menu {
            text-align: right;
            .ant-btn {
                & +.ant-btn {
                    margin-left: 8px;
                }
            }

        }
    }
	.content {
		padding: 8px;
    padding-top: 52px;
		.gutter {
			display: flex;
			& + .gutter {
				margin-top: 8px;
			}
			.price {
				max-width: 100%;
				flex: 1;
			}
			.material {
				flex: 1;
				max-width: 65%;
			}
			.pack-quota {
				flex: 1;
				max-width: 35%;
			}
			.history,
			.compare {
				overflow-y: auto;
				flex: 1;
				max-width: 100%;
                .ant-btn {
                    & + .ant-btn {
                        margin-left: 8px;
                    }
                }
			}
		}
		.item {
			padding: 12px;
			background: #fff;
      border-radius: 4px;
			& + .item {
				margin-left: 8px;
			}
		}
		.info {
			font-size: 20px;
			color: #000;
			.inline {
        .tit {
          margin-right: 8px;
          &::after {
            content: ':'
          }
        }
				&:first-of-type {
					border-left: 4px solid @blue;
				}
				& + .inline {
					margin-left: 24px;
				}
				.label {
					margin-left: 12px;
					&::after {
						content: ":";
					}
				}
				.value {
					margin-left: 12px;
					color: @blue;
				}
				.red {
					color: @red;
				}
			}
		}
		// .table {
			// height: 420px;
		// }
	}
}
.ant-modal-root
.ant-modal-wrap
.ant-modal
.ant-modal-content
.ant-modal-body
.chartInfo {
  display: flex;
  margin-top: 30px;
  .echart {
    flex: 1;
    min-height: 320px;
  }
  .echart.unShow {
      // background-size: 100px 100px;
      position: relative;
      background-color: #fff;
      background-image: url(~@/assets/img/ebidding/x1.png);
      background-repeat: no-repeat;
      background-position: center;
  }
  .chartTable {
    flex: 0 0 260px;
    margin-left: 20px;
  }
  .red {
    color: @red;
    position: absolute;
    left: 50%;
    bottom: 20px;
    transform: translateX(-50%);
  }
}
:deep(.ant-descriptions-item-content) {
  width: 20%;
}
:deep(.ant-card-head-title) {
  padding: 0;
}
</style>
