<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />

    <!-- 详情界面 -->
    <SaleFinanceEnterpriseOutinvoiceDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    />
  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import SaleFinanceEnterpriseOutinvoiceDetail from './modules/SaleFinanceEnterpriseOutinvoiceDetail'

export default {
    mixins: [ListMixin],
    components: {
        SaleFinanceEnterpriseOutinvoiceDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pfLKqy_11eaf4b3`, '纳税人识别号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'finance#saleFinanceEnterpriseOutinvoice:view'}
                ]
            },
            url: {
                list: '/finance/financeEnterpriseOutinvoice/purchaseToSaleByList',
                delete: '/finance/financeEnterpriseOutinvoice/delete',
                deleteBatch: '/finance/financeEnterpriseOutinvoice/deleteBatch',
                exportXlsUrl: 'finance/financeEnterpriseOutinvoice/exportXls',
                importExcelUrl: 'finance/financeEnterpriseOutinvoice/importExcel',
                columns: 'saleFinanceEnterpriseOutinvoiceList'
            }
        }
    },
    methods: {
    }
}
</script>