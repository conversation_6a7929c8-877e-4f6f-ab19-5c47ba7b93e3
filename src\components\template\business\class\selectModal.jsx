import {getLangAccount, srmI18n, getObjType} from '@/utils/util'
// export const slots = function (that) {
//   return new SelectModal(that).slots
// }
export class SelectModal {
    constructor (option) {
        this.groupCode = ''
        this.title = ''
        this.field = ''
        this.fieldType = 'selectModal'
        this.fieldLabel = ''
        this.fieldName = ''
        this.dictCode = ''
        this.defaultValue = ''
        this.dataFormat = ''
        this.helpText = ''
        this.alertMsg = ''
        this.required = '0'
        this.sortOrder = '20'
        this.width = 150
        this.bindFunction = () => {}
        this.extend = {}
        this.placeholder = ''
        this.slots = {
            default: ({row, rowIndex, column, columnIndex}, h) => {
                const afterRowClearCallBack = this.extend && this.extend.afterRowClearCallBack
                const closeIcon = row[column.property] ?
                    [<a-icon
                        type="close-circle" 
                        style="position: absolute;display: inline-block;font-size: 14px;right: 23px; top: 50%;transform:translateY(-50%);z-index: 2; cursor:pointer"
                        onClick={(event) => {
                            event.stopPropagation()
                            getObjType(afterRowClearCallBack) === 'function' && afterRowClearCallBack(row, column, rowIndex, columnIndex, this)
                        }}
                    />]
                    : []
                const scopedSlots = {
                    default: ({openFunc}) => {
                        return (
                            <div title={row[column.property]} style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                                <span style="width:100px;display: inline-block;height:100%;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">{row[column.property]}</span>
                                {closeIcon}
                                <a style="position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 2;">
                                    <a-icon type="file-search" onClick={() => {
                                        openFunc && openFunc()
                                    }}/>
                                </a>
                            </div>
                        )
                    }
                }
                const props = {
                    config: this || {},
                    row: row,
                    isRow: true
                }
                const on = {
                    afterClearCallBack: (cb) => cb && cb(row, column, rowIndex, columnIndex),
                    ok: (data) => {
                        this.bindFunction && this.bindFunction(row, data)
                    }
                }
                return [
                    (<m-select-modal scopedSlots={scopedSlots} {...{props, on}} />)
                ]
            }
        }
        this.init(option)
    }

    init (options) {
        if (options && Object.keys(options).length > 0) {
            Object.keys(this).forEach(item => {
                if (options && Object.prototype.hasOwnProperty.call(options, item)) this[item] = options[item]
            })
        }
    }

    initTaxModal () {
        this.groupCode = 'baseForm'
        this.fieldLabel = '税码'
        this.fieldName = 'taxCode'
        this.extend = {
            modalColumns: [
                { field: 'taxCode', title: srmI18n(`${getLangAccount()}#i18n_title_enterTaxCode`, '税码'), with: 150 },
                { field: 'taxRate', title: srmI18n(`${getLangAccount()}#i18n_title_taxRate`, '税率'), width: 150 },
                { field: 'taxName', title: srmI18n(`${getLangAccount()}#i18n_field_taxName`, '税码名称'), width: 150 },
                { field: 'remark', title: srmI18n(`${getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'), with: 150 }
            ],
            modalUrl: '/base/tax/list',
            modalParams: {}
        }
    }

    initSubAccountModal () {
        this.bindFunction = function (row, data) {
            row.principal = data[0].subAccount + '_' + data[0].realname
        }
        this.extend = {
            modalColumns: [
                {field: 'subAccount', title: srmI18n(`${getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 150},
                {field: 'realname', title: srmI18n(`${getLangAccount()}#i18n_field_realname`, '姓名'), width: 150}
            ],
            modalUrl: '/account/elsSubAccount/list',
            modalParams: {}
        }
    }
}
