<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <sale-barcode-level-head-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <sale-barcode-level-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />

    <!-- 新增弹窗 -->
    <BarcodeLevelTempSelectModal
      ref="temSelectModal"
      :pageData="pageData"
      @success="handleEdit"></BarcodeLevelTempSelectModal>
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleBarcodeLevelHeadEdit from './modules/SaleBarcodeLevelHeadEdit'
import SaleBarcodeLevelHeadDetail from './modules/SaleBarcodeLevelHeadDetail'
import BarcodeLevelTempSelectModal from './modules/BarcodeLevelTempSelectModal'

export default {
    mixins: [ListMixin],
    components: {
        SaleBarcodeLevelHeadEdit,
        SaleBarcodeLevelHeadDetail,
        BarcodeLevelTempSelectModal
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeLevel',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNtFAy_ae8e2a03`, '请输入单据编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'barcode#saleLevel:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'barcode#saleLevel:detail'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.showEdit,
                        authorityCode: 'barcode#saleLevel:edit'
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.showEdit,
                        authorityCode: 'barcode#saleLevel:delete'
                    },
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/base/barcode/saleBarcodeLevelHead/list',
                add: '/base/barcode/saleBarcodeLevelHead/add',
                delete: '/base/barcode/saleBarcodeLevelHead/delete',
                changeStatus: '/base/barcode/saleBarcodeLevelHead/changeStatus',
                invalid: '/base/barcode/saleBarcodeLevelHead/invalid',
                deleteBatch: '/base/barcode/saleBarcodeLevelHead/deleteBatch',
                columns: 'saleBarcodeLevelHeadList'
            }
        }
    },
    methods: {
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        showEdit (row) {
            return row.levelStatus == 'final' ? true : false
        },
        handleAdd () {
            let data = {
                url: '/enterprise/elsEnterpriseInfo/getPurchaseAccount',
                params: {
                    toElsAccount: this.$ls.get('Login_elsAccount'),
                    // frozenFunctionValue: '1'
                }
            }
            this.$refs.temSelectModal.open(data)
        }
    }
}
</script>