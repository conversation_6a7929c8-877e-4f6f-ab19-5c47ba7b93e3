<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <PurchaseClarificationInfoEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <PurchaseClarificationInfoDetail 
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import PurchaseClarificationInfoAdd from './modules/PurchaseClarificationInfoAdd'
import PurchaseClarificationInfoEdit from './modules/PurchaseClarificationInfoEdit'
import PurchaseClarificationInfoDetail from './modules/PurchaseClarificationInfoDetail'
import layIM from '@/utils/im/layIM.js'
export default {
    mixins: [ListMixin],
    components: {
        PurchaseClarificationInfoEdit,
        PurchaseClarificationInfoDetail,
        PurchaseClarificationInfoAdd
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                businessType: 'purchase_clarification',
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'bidding#purchaseClarificationInfo:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_clarificationNumber`, '澄清单号'),
                        fieldName: 'clarificationNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_clarificationNumber`, '澄清单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                        fieldName: 'businessType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                        dictCode: 'srmClarificationBusinessType'
                    }
                ],
                optColumnWidth: 170,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'bidding#purchaseClarificationInfo:queryById'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'bidding#purchaseClarificationInfo:edit'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'bidding#purchaseClarificationInfo:delete'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat, authorityCode: 'bidding#purchaseClarificationInfo:creatGruopChat'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/bidding/purchaseClarificationInfo/add',
                list: '/bidding/purchaseClarificationInfo/list',
                delete: '/bidding/purchaseClarificationInfo/delete',
                columns: 'PurchaseClarificationInfo' 
            }
        }
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.clarificationNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'PurchaseClarificationInfo', url: this.url || '', recordNumber})
        },
        allowChat (row){
            if (row.sendStatus == '1') {
                return false
            }else {
                return true
            }
        },
        handleAdd () {
            this.currentEditRow = {}
            this.showEditPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        allowEdit (row){
            const status = row.sendStatus
            if(status!=='0'){
                return true
            }
            return false
        },
        allowDelete (row){
            const status = row.sendStatus
            if(status!=='0'){
                return true
            }
            return false
        }
    }
}
</script>