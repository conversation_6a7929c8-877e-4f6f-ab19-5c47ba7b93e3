<template>
  <a-modal
    title="视图预览"
    :closable="false"
    :visible="visibleViews"
    :width="800"
    :zIndex="99"
  >
    <template slot="footer">
      <a-button 
        type="primary" 
        @click="visibleViews=false">确定</a-button>
    </template>
    <!-- :style="{height: modalData.chartType == 'echart' ? '500px': 'auto'}" -->
    <div
      class="preview"
      :style="{height: modalData.chartType == 'echart' || modalData.chartType == 'list' ? '400px': 'auto'}"
    >
      <viewPreviewCard
        :modalData="modalData"
        :widget="widget"
        :options="options"
        :extraGridConfig="{height:'336px'}"
        :resultData="resultData"
      />
    </div>
  </a-modal>
</template>

<script>
import ViewPreviewCard from '@views/dashboard/modules/ViewPreviewCard'

export default {
    components: {
        ViewPreviewCard
    },
    props: {
        modalData: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        widget: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        options: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        resultData: {
            type: Array,
            default: ()=> {
                return null
            }
        }
    },
    data () {
        return {
            visibleViews: false,
            gridConfig: {
                border: true,
                stripe: true,
                resizable: true,
                autoResize: true,
                keepSource: true,
                height: 'auto',
                showOverflow: true,
                showHeaderOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center'
            }
        }
    },
    methods: {
        open () {
            this.visibleViews = true
        }
    }
}
</script>

<style lang="less" scoped>
.preview{
  :deep(.ant-card-body){
    height: 100%;
  }
}
.num-panel-item-new {
  display: flex;
  flex-wrap: wrap;
      height: 500px;
    overflow: auto;
  .box{
      width: 218px;
    padding: 10px;
    height: 100px;
    margin-right: 20px;
    box-shadow: 0px 0px 6px #949494;
    margin-bottom: 20px;
    }
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      border-radius: 30px;
      text-align: center;
      padding: 9px 16px;
      color: white;
      background: #ed2f2f;
      line-height: 100%;
      height: 15px;
      font-weight: 600;
      -webkit-box-sizing: content-box;
      box-sizing: content-box;
    }
  }
  .count {
    padding: 10px 0;
    font-size: 18px;
    color: #494949;
    font-weight: 500;
  }
}
</style>