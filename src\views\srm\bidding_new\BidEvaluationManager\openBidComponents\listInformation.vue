<template>
  <div>
    <titleTrtl class="margin-b-10">
      <span>{{ rowTitel }}{{ currentRow ? '' : $srmI18n(`${$getLangAccount()}#i18n_field_WumvBIBBVHWVWRiYBxW_e5696734`, '：价格开标一览表信息(请先勾选招标函)') }}</span>
      <template
        slot="right"
      >
        <a-button
          v-if="ifShowButton"
          type="primary"
          size="small"
          @click="maintenanceMaterial">{{ $srmI18n(`${$getLangAccount()}#i18n_field_LDSLc_2b9121cc`, '维护物料行') }}</a-button>
      </template>
    </titleTrtl>
    <div v-if="currentRow">
      <vxe-grid
        :height="height"
        v-bind="gridConfig"
        ref="customizeFieldDataTable"
        key="customizeFieldDataTable"
        :data="currentRow.customizeFieldData"
        :edit-rules="editRules"
        :merge-cells="mergeCells"
        :columns="currentRow.customizeFieldModel"
        show-overflow="title">
        <template #grid_opration="{ row, column, rowIndex }">
          <div v-if="optColumnList && isEdit">
            <span
              v-for="(opt, optIndex) in optColumnList"
              :key="'opt_' + row.id + '_' + optIndex">
              <a
                :title="opt.title"
                style="margin: 0 4px"
                :disabled="typeof opt.disabled === 'function' ? opt.disabled(row) : opt.disabled"
                v-show="!opt.hide"
                @click="
                  () => {
                    optColumnFuntion(opt, row, column, rowIndex)
                  }
                "
              >{{ opt.title }}</a
              >
            </span>
          </div>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>
<script lang="jsx">
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import titleTrtl from '@/views/srm/bidding_new/BiddingHall/components/title-crtl'
import { valitNumberLength } from '@views/srm/bidding_new/utils/index'
import { cloneDeep } from 'lodash'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'

let checkNumber = (rule, value, callback) => {
    if (value) {
        if (!valitNumberLength(value, 8)){
            callback(new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '8'))
        }
    }
    callback()
}
export default {
    components: {
        titleTrtl
    },
    mixins: [tableMixins],
    props: {
        currentRow: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: '',
            type: String
        },
        formData: {
            default: () => {
                return {}
            },
            type: Object
        },
        materialData: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        rowTitel () {
            if (this.materialData) return this.materialData.name
            return '某投标函'
        },
        ifShowButton () {
            return (this.materialData.formatType != '9' && this.materialData.checkType != '0')
        },
        mergeCells () {
            if (!['0', '1', '2'].includes(this.currentRow.formatType)) return[]
            let rowspan = this.currentRow.customizeFieldData.length
            return [
                // row: 行，col: 列，
                { row: 0, col: 1, rowspan, colspan: 1 }
            ]
        }
    },
    data () {
        return {
            optColumnList: [{ type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.handleTableDel }],
            height: 300,
            businessType: 'column',
            editRules: {}
        }
    },
    methods: {
        selectedOk (data) {
            let businessTypeMap = {
                column: this.handleAddTableColumn
            }
            businessTypeMap[this.businessType](data)
        },
        // 获取表格下拉字典
        queryDictData (column) {
            const that = this
            if (column && column.dictCode) {
                let postData = {
                    busAccount: that.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: column.dictCode
                }
                ajaxFindDictItems(postData).then((res) => {
                    if (res.success) {
                        let options = res.result.map((dictItem) => {
                            return {
                                value: dictItem.value,
                                label: dictItem.text,
                                title: dictItem.title
                            }
                        })
                        if (column.editRender) {
                            column.editRender.options = options
                        }
                        // dictOptions初始化数据字典的字段会用到
                        column.dictOptions = options
                        that.$forceUpdate()
                    } else {
                        if (column.editRender) {
                            column.editRender.options = []
                        }
                        // dictOptions初始化数据字典的字段会用到
                        column.dictOptions = []
                        that.$forceUpdate()
                    }
                })
            } else {
                if (column.editRender) {
                    column.editRender.options = []
                }
                // dictOptions初始化数据字典的字段会用到
                column.dictOptions = []
                that.$forceUpdate()
            }
        },
        
        // 列处理
        columnSerialization (item) {
            // 1招标单位, 0投标单位
            let canEdit = item.inputOrg == '1'
            let field = item.columnFieldName || item.field
            this.editRules[field] = []
            // 默认能删除列
            switch (item.fieldType) {
            case 'string':
                item.editRender = { enabled: canEdit, name: '$input' }
                this.editRules[field].push({ max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')})
                break
            case 'dict':
                item.editRender = { enabled: canEdit, name: 'srmSelect', options: this.queryDictData(item) }
                break
            case 'date':
                item.editRender = { enabled: canEdit, name: 'mDatePicker' }
                break
            case 'number':
                item.editRender = { enabled: canEdit, name: '$input', props: { type: 'number' } }
                this.editRules[field].push({ validator: checkNumber, trigger: 'change' })
                break
            }
            // 默认操作列头部
            let slots = {
                header: ({ columnIndex, column }) => {
                    return this.isEdit && !item.canDeleteColumn
                        ? [
                            <span>
                                {item.name || item.title}
                                <a-icon type="delete" onClick={this.handleDeleteColumn.bind(this, columnIndex, column)} />
                            </span>
                        ]
                        : [<span>{item.name || item.title}</span>]
                }
            }
            let itemColumn = {
                title: item.name || item.title,
                field: field,
                fieldType: item.fieldType,
                fieldCategory: item.fieldCategory,
                required: item.must == '1' || item.must == true ? true : false,
                must: item.must == '1' || item.must == true ? true : false,
                inputOrg: item.inputOrg,
                width: 160,
                slots: item.slots || slots,
                editRender: item.editRender
            }
            if (item.must == '1' || item.must == true) {
                this.editRules[field].push({ required: true, message: `请输入${itemColumn.title}` })
            }
            if (item.dictCode) {
                itemColumn.dictCode = item.dictCode
            }
            return itemColumn
        },

        // 操作列方法
        optColumnFuntion (opt, row, col, index) {
            opt.click && opt.click(this, row, col, index)
        },
        // 格式化列
        initColumns (data) {
            if (!data) return
            // 初始化为空的时候默认插入操作列
            if (!data.customizeFieldModel) {
                let custom = [
                    {
                        type: 'seq',
                        width: 50,
                        key: 'seq',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                    }
                ]
                // 报价类型: 1：分项报价
                if (this.formData.quoteType == '1' && data.formatType !== '9') {
                    custom.push(...[
                        {
                            editRender: { name: '$input', enabled: false },
                            width: 120,
                            field: 'supplierName',
                            inputOrg: '0',
                            canDeleteColumn: true,
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称')
                        },
                        {
                            width: 120,
                            field: 'materialName',
                            canDeleteColumn: true,
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称')
                        },
                        {
                            width: 100,
                            field: 'quotedPrice',
                            inputOrg: '0',
                            fieldCategory: '1',
                            canDeleteColumn: true,
                            must: true,
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价')
                        }
                    ])
                } else {
                    // 其他操作类型
                    if (data.formatType == '9') {
                        custom.push({
                            slots: { default: 'grid_opration' },
                            width: 50,
                            key: 'grid_opration',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作')
                        })
                    } else {
                        custom.push(
                            {
                                editRender: { name: '$input', enabled: false },
                                width: 120,
                                field: 'supplierName',
                                inputOrg: '0',
                                canDeleteColumn: true,
                                title: '投标单位名称'
                            }
                        )
                    }
                }
                this.$set(data, 'customizeFieldModel', custom)
            } else {
                // 存在列时候需要拼接列自定义
                let flag = false
                data.customizeFieldModel.map((item) => {
                    // 存在操作列
                    if (item.key == 'seq') {
                        flag = true
                    }
                })
                let columnlsit = []
                // 不存在操作列的情况下
                if (!flag) {
                    columnlsit = data.customizeFieldModel.map((item) => {
                        return this.columnSerialization(item)
                    })
                    columnlsit.unshift({
                        type: 'seq',
                        width: 50,
                        key: 'seq',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                    })
                    // 其他操作类型
                    if (data.formatType == '9') {
                        columnlsit.push({
                            slots: { default: 'grid_opration' },
                            width: 50,
                            key: 'grid_opration',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作')
                        })
                    }
                } else {
                    columnlsit = data.customizeFieldModel.map(item => {
                        if (!['seq', 'grid_opration'].includes(item.key)) return this.columnSerialization(item)
                        return item
                    })
                }
                this.$set(data, 'customizeFieldModel', columnlsit)

            }
        },
        maintenanceMaterial () {
            this.$emit('maintenanceMaterial')
        },
        reloadData () {
            this.$refs.customizeFieldDataTable.reloadData(this.currentRow.customizeFieldData)
        }
    },
    created () {
        this.height = document.documentElement.clientHeight - 290
    }
}
</script>
<style lang="less" scoped>
    .margin-l-10{
        margin-left: 10px;
    }
</style>
