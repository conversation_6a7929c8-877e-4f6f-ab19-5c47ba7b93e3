<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />

    <edit-delivery-order-address-modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />

    <view-delivery-order-address-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />

  </div>
</template>
<script>
import EditDeliveryOrderAddressModal from './modules/EditDeliveryOrderAddressModal'
import ViewDeliveryOrderAddressModal from './modules/ViewDeliveryOrderAddressModal'
import { ListMixin } from '@comp/template/list/ListMixin'

export default {
    mixins: [ListMixin],
    components: {
        ViewDeliveryOrderAddressModal,
        EditDeliveryOrderAddressModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_organizationType`, '组织类型'),
                        dictCode: 'orgCategoryCode',
                        fieldName: 'organizationType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectOrganizationType`, '请选择组织类型')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.add, type: 'primary', authorityCode: 'deliveryOrderAddress#deliveryOrderAddress:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'deliveryOrderAddress#deliveryOrderAddress:view' },
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, authorityCode: 'deliveryOrderAddress#deliveryOrderAddress:edit' },
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, authorityCode: 'deliveryOrderAddress#deliveryOrderAddress:delete' },
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleView }
                ],
                form: {
                    businessType: '',
                    operateType: ''
                }
            },
            url: {
                list: '/delivery/deliveryOrderAddress/list',
                delete: '/delivery/deliveryOrderAddress/delete',
                deleteBatch: '/delivery/deliveryOrderAddress/deleteBatch',
                columns: 'deliveryOrderAddressList'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_msgConfigurationHeader`, '消息配置头'))
        },
        add (){
            this.currentEditRow = null
            this.showEditPage = true
        }
    }
}
</script>