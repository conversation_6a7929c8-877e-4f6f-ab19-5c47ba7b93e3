// 浮点数计算模块
export { add, sub, mul, div } // 加 减 乘 除
function add (a, b) {
    if(a==undefined){
        a = 0
    }
    if(b==undefined){
        b = 0
    }
    var c, d, e
    try {
        c = a.toString().split('.')[1].length
    } catch (f) {
        c = 0
    }
    try {
        d = b.toString().split('.')[1].length
    } catch (f) {
        d = 0
    }
    e = Math.pow(10, Math.max(c, d))
    return (mul(a, e) + mul(b, e)) / e
}

function sub (a, b) {
    var c, d, e
    try {
        c = a.toString().split('.')[1].length
    } catch (f) {
        c = 0
    }
    try {
        d = b.toString().split('.')[1].length
    } catch (f) {
        d = 0
    }
    e = Math.pow(10, Math.max(c, d))
    return (mul(a, e) - mul(b, e)) / e
}

function mul (a, b) {
    var c = 0
    var d = a.toString()
    var e = b.toString()
    try {
        c += d.split('.')[1].length
    } catch (f) {}
    try {
        c += e.split('.')[1].length
    } catch (f) {}
    return Number(d.replace('.', '')) * Number(e.replace('.', '')) / Math.pow(10, c)
}

function div (a, b) {
    var c = 0
    var d = 0
    var e = 0
    var f = 0
    try {
        e = a.toString().split('.')[1].length
    } catch (g) {}
    try {
        f = b.toString().split('.')[1].length
    } catch (g) {}
    c = Number(a.toString().replace('.', ''))
    d = Number(b.toString().replace('.', ''))
    return mul(c / d, Math.pow(10, f - e))
}
