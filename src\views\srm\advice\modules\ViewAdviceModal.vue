<template>
  <div>
    
    <a-page-header
    >
      <template slot="title">
        <p>{{ pageData.title }}<span class="ant-breadcrumb-separator">/</span><span>{{ pageData.title }}详情</span></p>
      </template>
      <template
        slot="extra">
        <a-button
          v-for="(item, key) in pageData.publicBtn"
          v-show="item.showCondition ? item.showCondition(item.id) : true"
          :type="item.type || 'default'"
          style="margin-left:8px"
          slot="tabBarExtraContent"
          @click="item.clickFn"
          :key="'public_btn_' + key">{{ item.title }}</a-button>
      </template>
    </a-page-header>
    <div class="page-content" >
      <a-spin :spinning="confirmLoading">
        <a-tabs >
          <a-tab-pane
            v-for="(group, index) in pageData.panels"
            :key="index"
            forceRender
            :tab="group.title"
            :class="{ 'chat_tabpanel': group.content.type == 'chat' }">
            <div
              class="base_info"
              v-if="group.content.type == 'form'">
              <div
                v-for="(panel, index) in group.content.baseInfoList"
                :key="'panel_1' + index">
                <div class="item-box-title">{{ panel.title }}</div>                
                <div style="padding-bottom: 10px;">
                  <a-descriptions
                    bordered
                    size="small"
                    :colon="false"
                    v-if="panel.content.type == 'form'">
                    <a-descriptions-item
                      :span="1"
                      :label="item.fieldLabel"
                      v-for="(item, i) in panel.content.list"
                      :key="i">
                      <span :class="item.styleClass">{{ item.isBoolean ? (formData[item.fieldName] == 1 ? '是' : '否') : formData[item.fieldName] }}</span>
                    </a-descriptions-item>
                  </a-descriptions>
                  <div
                    class="orderContent"
                    v-if="panel.content.type == 'orderConten'">
                    <div v-html="formData[panel.content.fieldName]"></div>
                  </div>
                </div>
              </div>
            </div>
            <AdviceChat
              ref="chat"
              v-if="group.content.type == 'chat'"
              :messageRecordList="complaintAdviceRecordList"
              :disabled="orderChatDisabled"
              :chatHeight="getChatHeight()"
              @sendNews="sendNews"
              @reSendNews="reSendNews"></AdviceChat>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>

    <a-modal
      v-drag    
      :width="660"
      v-model="scoreVisible"
      title="评分"
      :okText="$srmI18n(`${$getLangAccount()}#i18n_field_RL_f20f6`, '确认')"
      @ok="scoreHandleOk">
      <a-form-model
        ref="scoreFormRef"
        :model="scoreForm"
        :rules="scoreformRules"
        :layout="formLayout"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          label="处理速度"
          prop="processSpeed">
          <a-rate
            v-model="scoreForm.processSpeed"
            :tooltips="scoredesc"
            :count="10"
            :allow-clear="true" />
        </a-form-model-item>
        <a-form-model-item
          label="服务质量"
          prop="serviceQuality">
          <a-rate
            v-model="scoreForm.serviceQuality"
            :tooltips="scoredesc"
            :count="10"
            :allow-clear="true" />
        </a-form-model-item>
        <a-form-model-item
          label="服务态度"
          prop="serviceAttitude">
          <a-rate
            v-model="scoreForm.serviceAttitude"
            :tooltips="scoredesc"
            :count="10"
            :allow-clear="true" />
        </a-form-model-item>
        <a-form-model-item
          label="备注"
          prop="content">
          <a-textarea
            :auto-size="{ minRows: 6, maxRows: 10 }"
            v-model="scoreForm.content" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <a-modal
      v-drag    
      :width="660"
      v-model="deliverVisible"
      :title="$srmI18n(`${$getLangAccount()}#`, '工单转办')"
      :okText="$srmI18n(`${$getLangAccount()}#`, '确认')"
      @ok="deliverHandleOk">
      <a-form-model
        ref="deliverFormRef"
        :model="deliverForm"
        :rules="formRules"
        :layout="formLayout"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_receiver`, '接收人')"
          prop="recipient">
          <a-select
            placeholder="请选择接收人"
            v-model="deliverForm.recipient"
            option-filter-prop="children"
            :filter-option="filterOption"
            show-search
            allow-clear>
            <a-select-option
              v-for="(f,fIndex) in subAccountList"
              :key=" 'field'+fIndex"
              :value="f.value"
              :data-idx="fIndex"
            >
              {{ f.title }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(``, '备注')"
          prop="content">
          <a-textarea
            :auto-size="{ minRows: 6, maxRows: 10 }"
            v-model="deliverForm.content" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <a-modal
      v-drag    
      :width="660"
      v-model="confirmVisible"
      :title="confirmTitle"
      :okText="$srmI18n(`${$getLangAccount()}#`, '确认')"
      @ok="confirmHandleOk">
      <a-form-model
        :model="confirmForm"
        :layout="formLayout"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(``, '备注')"
          prop="content">
          <a-textarea
            :auto-size="{ minRows: 6, maxRows: 10 }"
            v-model="confirmForm.content" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import AdviceChat from '@comp/Chat'
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'ViewAdviceModal',
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    components: {
        AdviceChat
    },
    data () {
        return {
            websocket: null,
            editContent: '',
            confirmLoading: false,
            formData: {},
            orderChatDisabled: false,
            complaintAdviceRecordList: [
            ],
            deliverVisible: false,
            confirmVisible: false,
            confirmTitle: '',
            optType: '',
            subAccountList: [],
            deliverForm: { content: '', recipient: '' },
            confirmForm: { content: ''},
            formLayout: 'vertical',
            formRules: {
                recipient: [ { required: true, message: '请选择接收人', trigger: 'blur' }]
            },
            labelCol: {},
            wrapperCol: {},
            scoreVisible: false,
            scoreForm: {
                content: '',
                processSpeed: null,
                serviceQuality: null,
                serviceAttitude: null
            },
            scoreformRules: {
                processSpeed: [ { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_Vfz_219a44a`, '请打分'), trigger: 'blur' }],
                serviceQuality: [ { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_Vfz_219a44a`, '请打分'), trigger: 'blur' }],
                serviceAttitude: [ { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_Vfz_219a44a`, '请打分'), trigger: 'blur' }]
            },
            scoredesc: ['非常差', '非常差', '差', '差', '一般', '一般', '中', '良好', '良好', '优秀'],
            pageData: {
                title: '投诉建议',
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            baseInfoList: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                                    content: {
                                        type: 'form',
                                        ref: 'baseForm',
                                        form: {
                                            projectName: '',
                                            projectType: ''
                                        },
                                        list: [
                                            {
                                                fieldType: 'input',
                                                fieldLabel: '单据单号',
                                                fieldLabelI18nKey: '',
                                                fieldName: 'adviceNumber',
                                                disabled: true
                                            },
                                            {
                                                fieldType: 'input',
                                                fieldLabel: '单据标题',
                                                fieldLabelI18nKey: '',
                                                fieldName: 'title'
                                            },
                                            {
                                                fieldType: 'input',
                                                fieldLabel: '单据类型',
                                                fieldLabelI18nKey: '',
                                                fieldName: 'adviceType_dictText'
                                            },
                                            {
                                                fieldType: 'input',
                                                fieldLabel: '单据状态',
                                                fieldLabelI18nKey: '',
                                                fieldName: 'adviceStatus_dictText'
                                            },
                                            {
                                                fieldType: 'input',
                                                fieldLabel: '单据处理人',
                                                fieldLabelI18nKey: '',
                                                fieldName: 'adviceHandler'
                                            }
                                        ]
                                    }
                                },
                                {
                                    title: '单据内容',
                                    content: {
                                        type: 'orderConten',
                                        ref: 'workerOrder',
                                        fieldName: 'content'
                                    }
                                }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RetH_338e9640`, '沟通记录'),
                        content: {
                            type: 'chat',
                            ref: 'chat',
                            list: []
                        }
                    }
                ],
                publicBtn: [
                    //{type: 'primary', title: '确认', clickFn: this.confirm, showCondition: this.allowConfirmOpt},
                    //{type: 'primary', title: '指派', clickFn: this.deliverTo, showCondition: this.allowOpt},
                    //{type: 'primary', title: '提交验证', clickFn: this.submitCheck, showCondition: this.allowSubmitCheckOpt},
                    {type: 'primary', title: '关闭', clickFn: this.close, showCondition: this.allowCloseOpt},
                    {type: 'primary', title: '评分', clickFn: this.score, showCondition: this.allowScoreOpt},
                    {type: 'rollBack', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack}
                ],
                url: {
                    detail: '/other/complaintAdviceHead/queryById'
                }
            }
        }
    },
    mounted () {
        this.initWebSocket()
        this.getPageDetail()
    },
    methods: {
        allowOpt (){
            if(this.currentEditRow.adviceStatus ==='1'){
                return true
            }
            return false
        },
        allowCloseOpt (){
            if(this.currentEditRow.adviceStatus ==='1'){
                return true
            }
            return false
        },
        // 是否显示评分
        allowScoreOpt () {
            if(this.currentEditRow.adviceStatus ==='2' && this.currentEditRow.score ==='0'){
                return true
            }
            return false
        },     
        close (){
            this.confirmTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RtRl_2bf1e0ea`, '工单关闭')
            this.confirmForm = { content: '' }
            this.confirmVisible = true
            this.optType = 'close'
        },
        confirmHandleOk (){
            const that = this
            let param = Object.assign({}, this.currentEditRow)
            param['content']=this.confirmForm.content    
            let url = ''
            if(this.optType == 'confirm'){
                url = '/other/complaintAdviceHead/confirm'
            }else if(this.optType == 'submitCheck'){
                url = '/other/complaintAdviceHead/submitCheck'
            }else if(this.optType == 'close'){
                url = '/other/complaintAdviceHead/close'
            }
            postAction(url, param).then((res)=>{
                if(res && res.success){
                    that.$message.success(res.message)
                    that.confirmVisible = false
                    that.getPageDetail()
                } else{
                    that.$message.warning(res.message)
                }
            })                
        },
        // 显示评分弹窗
        score () {
            this.scoreForm = { 
                processSpeed: null,
                serviceQuality: null,
                serviceAttitude: null,
                content: '' 
            }
            this.scoreVisible = true
        },
        // 评分-确认
        scoreHandleOk () {
            this.$refs.scoreFormRef.validate(valid=> {
                if (valid) {
                    const that = this
                    let param = {}
                    let row = Object.assign({}, this.currentEditRow)
                    param['id']=row.id
                    param['content']=this.scoreForm.content
                    param['processSpeed']=this.scoreForm.processSpeed   
                    param['serviceQuality']=this.scoreForm.serviceQuality
                    param['serviceAttitude']=this.scoreForm.serviceAttitude 
                    postAction('/other/complaintAdviceHead/score', param).then((res)=>{
                        if(res && res.success){
                            that.$message.success(res.message)
                            that.scoreVisible = false
                            that.getPageDetail()
                        } else{
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        deliverTo (){
            this.deliverForm = { content: '', recipient: '' }
            this.receiver()
            this.deliverVisible = true
        },
        deliverHandleOk (){
            this.$refs.deliverFormRef.validate(valid => {
                if (valid) {
                    const that = this
                    let param = Object.assign({}, this.currentEditRow)
                    param['content']=this.deliverForm.content
                    param['recipient']=this.deliverForm.recipient            
                    //const t = this.$srmI18n(`${this.$getLangAccount()}#`, '工单转办确认')
                    //const c = this.$srmI18n(`${this.$getLangAccount()}#`, '确认是否进行工单转交?')
                    // this.$confirm({
                    //     title: t,
                    //     content: c,
                    //     onOk: function (){
                    that.loading = true
                    postAction('/other/complaintAdviceHead/transfer', param).then((res)=>{
                        if(res && res.success){
                            that.$message.success(res.message)
                            that.deliverVisible = false
                            that.getPageDetail()
                        } else{
                            that.$message.warning(res.message)
                        }
                    })
                    // }
                    // })
                }
            })
        },
        // 接收人过滤
        filterOption (input, option) {
            return (
                option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
            )
        },
        // 接收人
        receiver () {
            getAction('/account/elsSubAccount/list', {pageSize: '100000'}).then(res => {
                if(res && res.success) {
                    let records = res.result.records
                    const t = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')
                    this.subAccountList = [{value: '', title: t}]
                    for(let i in records){
                        let record = records[i]
                        let item = {value: record.subAccount, title: record.realname}
                        this.subAccountList.push(item)
                    }
                }
            })
        },
        // 初始化websocket
        initWebSocket () {
        // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https
            let id = this.currentEditRow.id
            let url = this.$variateConfig['domainURL'].replace('https://', 'wss://').replace('http://', 'ws://')+'/websocket/advice/'+id
            this.websocket = new WebSocket(url)
            this.websocket.onopen = this.websocketonopen
            this.websocket.onerror = this.websocketonerror
            this.websocket.onmessage = this.websocketonmessage
            this.websocket.onclose = this.websocketonclose
        },
        // 打开websocket
        websocketonopen () {
            console.log('websocket 连接成功')
        
        },
        // 连接错误
        websocketonerror () {
            console.log('websocket 连接失败')
        },
        // 接收到数据
        websocketonmessage (msg) {
            let { data } = msg
        
            let curdata=JSON.parse(data)
            let customerData = Object.assign({}, {type: 'customer'}, curdata)
            this.complaintAdviceRecordList.push(customerData)
        },
        // 发送消息
        sendMessage (msg) {
            if (this.websocket) {
                // this.websocket.send(JSON.stringify({type: 'message', 'content': JSON.stringify(msg) }))
            }
        },
        // 关闭websocket
        websocketonclose () {
            if (this.websocket) {
                this.websocket.close()
                this.websocket = null
            }
            console.log('websocket 已关闭')
        },
        goBack () {
            this.$emit('hide')
        },
        getPageDetail (){
            this.confirmLoading=true
            let params={
                id: this.currentEditRow.id
            }
            getAction(this.pageData.url.detail, params).then(res => {
                if(res.success) {
                    let result=res.result
                    this.formData=result
                    this.editContent=result.content
                    this.complaintAdviceRecordList= result.complaintAdviceRecordList
                    this.orderChatDisabled=result.adviceStatus==4||result.adviceStatus==0?true:false
                    this.$emit('handleUpdate', result)
                    this.$refs.chat[0].scrollToMessageEnd()
                }
            }).finally(()=>{
                this.confirmLoading=false
            })
        },
        sendNews (content){
            if (content && content.length) {
                let params = {
                    id: this.currentEditRow.id,
                    content: content
                }
                postAction('/other/complaintAdviceHead/sendMsg', params).then((res)=> {
                    if (res && res.success) {
                        this.complaintAdviceRecordList.push(res.result)
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }
        },
        reSendNews (params){
            postAction('/other/complaintAdviceHead/retryRecord', params).then((res)=> {
                if (res && res.success) {
                    this.complaintAdviceRecordList.map(item=>{
                        if(item.content == params.content){
                            item.sendStatus ='1'
                        }
                    })
                } else {
                    this.$message.warning(res.message)
                }
            })
      
        },
        getChatHeight (){
            return (window.innerHeight - 48 - 45-70 -51-32) + 'px' 
        }
    },
    destroyed () { 
        // 离开页面生命周期函数
        this.websocketonclose()
    }
}
</script>

<style lang="less" scoped>
// :deep() .ant-tabs-tabpane {
//   padding-bottom: 10px;
// }
.ant-page-header {
  padding: 16px 16px 11px;
  background: #fff;
  margin-bottom: 11px;
}
.ant-page-header-heading-title {
  p {
    margin: 0;
    font-size: 13px;
    color: #b0adb7;
    font-weight: 100;
  }
  span {
    color: #4290f7;
    font-weight: 100;
  }
  .ant-breadcrumb-separator {
    color: #b0adb7;
  }
}
.ant-descriptions-bordered .ant-descriptions-item-label{
  background:red!important;
}
.chat_tabpanel {
  padding: 0 10px;
  padding-bottom: 10px;
  box-sizing: border-box;
}
.ant-page-header-heading-title {
  color: rgb(24, 144, 255);
  font-size: 14px;
}
.item-box-title {
  background: #f2f2f2;
  padding: 0 7px;
  border: 1px solid #ededed;
  height: 34px;
  line-height: 34px;
  margin-bottom: 6px;
}
.page-content {
  flex: 1;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: auto;
  padding-left: 6px;
  padding-top: 6px;
  padding-right: 6px;
  margin-left: 6px;
  margin-right: 6px;
  background-color: #fff;
  .ant-row {
    width: 100%;
  }
}
.orderContent{
  min-height: 100px;
  padding: 5px 24px;
}
.color-f93C00 {
  color: #f93C00;
}
.color-389810 {
  color: #389810;
}
.color-f9c600 {
  color: #f9c600;
}
.color-0048f9 {
  color: #0048f9;
}
:deep(.ant-descriptions-item-label ){
  min-width: 120px;
  color: #626885;
  text-align: left;
}
:deep(.ant-descriptions-item-content){
  color: #798087;
}
</style>
