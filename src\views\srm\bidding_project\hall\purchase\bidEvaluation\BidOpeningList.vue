<template>
  <div class="BidOpeningList">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      :deadline="evaEndTime"
      :renderExtra="renderExtra"
      @content-header-submit="handleSubmit"
      @content-header-export="handleExport"
    />

    <div
      class="container"
      :style="style">

      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_menu_UByRIBB_31e232f4`, '评标结果一览表') }}</div>
          <div class="table">
            <vxe-grid
              ref="grid"
              v-bind="defaultGridOption"
              :columns="columns"
              :data="biddingSupplierVOList">
              <template slot="empty">
                <a-empty />
              </template>
            </vxe-grid>
          </div>
        </div>

        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_UBnLBI_6a89b67e`, '评标材料附件') }}</div>
          <div class="table">
            <vxe-grid
              ref="grid"
              v-bind="defaultGridOption"
              :toolbarConfig="toolbarConfig"
              :columns="attachmentColumns"
              :data="purchaseAttachmentBidEvaList">
              <template #empty>
                <a-empty />
              </template>
              <template #toolbar_buttons>
                <custom-upload
                  :disabledItemNumber="true"
                  :visible.sync="upload.modalVisible"
                  :title="upload.title"
                  :action="upload.action"
                  :accept="upload.accept"
                  :headers="upload.tokenHeader"
                  :dictCode="upload.dictCode"
                  :data="extraData"
                  @change="getBiddingHeadData"
                />
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>

  </div>
</template>

<script lang="jsx">
import ContentHeader from '@/views/srm/bidding_project/hall/components/content-header'
import {getAction, postAction, downFile} from '@/api/manage'
import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'
import countdown from '@/components/countdown'
import CustomUpload from '@comp/template/CustomUpload'
import {
    GRID_OPTION_ROW,
    ATTACHMENT_COLUMNS_WITHOUT_OPRATION
} from '@/utils/constant.js'
import { PURCHASEATTACHMENTDOWNLOADAPI } from '@/utils/const'
import { add, sub, mul, div } from '@/utils/mathFloat.js' // 加、减、乘、除

let ATTACHMENT_COLUMNS = ATTACHMENT_COLUMNS_WITHOUT_OPRATION({ groupCode: 'purchaseAttachmentBidEvaList' })

export default {
    components: {
        'content-header': ContentHeader,
        countdown,
        CustomUpload
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh'
    ],
    data () {
        return {
            evaEndTime: null,
            serverTime: null,
            confirmLoading: false,
            purchaseAttachmentBidEvaList: [],
            attachmentColumns: [
                ...ATTACHMENT_COLUMNS,
                {
                    ...GRID_OPTION_ROW,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    groupCode: 'purchaseAttachmentBidEvaList',
                    width: 140,
                    slots: {
                        default: ({ row }) => {
                            return [
                                (<span>
                                    <a-button type="link" style="padding: 0 4px;" onClick={ () => this.attachmentDownloadEvent(row) }>
                                        { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载') }
                                    </a-button>
                                    <a-button type="link" style="padding: 0 4px;" onClick={ () => this.attachmentPreview(row) }>
                                        { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览') }
                                    </a-button>
                                    <a-button type="link" style="padding: 0 4px;" onClick={ () => this.attachmentDelete(row, { groupCode: 'purchaseAttachmentBidEvaList' }) }>
                                        { this.$srmI18n(`${this.$getLangAccount(row)}#i18n_title_delete`, '删除') }
                                    </a-button>
                                </span>)
                            ]
                        }
                    }
                }
            ],
            supplierEvaScoreList: [],
            form: {},
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'), type: 'primary', event: 'submit' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), type: 'primary', event: 'export' }
            ],
            showHeader: true,
            height: 0,
            //默认表格配置
            biddingSupplierVOList: [],
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                // data: [],
                radioConfig: {highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'click', mode: 'cell' }
            },
            toolbarConfig: {
                slots: {
                    buttons: 'toolbar_buttons'
                }
            },
            columns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'), field: 'supplierName', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QKeB_277f7b1f`, '是否决投标'),  width: 150,
                    editRender: {
                        enabled: true
                    },
                    slots: {
                        default: ({ row }) => {
                            let txt = row.bidEva === '1' ? `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是')}` : `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否')}`
                            return [
                                (<span>{ txt }</span>)
                            ]
                        },
                        edit: (({row}) => {
                            const props = {
                                dictCode: 'yn'
                            }
                            return [
                                (<m-select vModel={row.bidEva} {...{ props }} />)
                            ]
                        })
                    }},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQLULYIL_88b87b1f`, '是否评委推荐人'), fieldType: 'select',
                    editRender: {
                        enabled: true,
                        autoselect: true
                    },
                    slots: {
                        default: ({ row }) => {
                            let txt = row.recommend === '1' ? `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是')}` : `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否')}`
                            return [
                                (<span>{ txt }</span>)
                            ]
                        },
                        edit: (({row}) => {
                            const props = {
                                dictCode: 'yn'
                            }

                            return [
                                (<m-select vModel={row.recommend} {...{ props }} />)
                            ]
                        })
                    }, width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_busScore`, '商务分'), field: 'busScore', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tecScore`, '技术分'), field: 'tecScore', width: 150 },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_umz_1355e81`, '价格分'),
                    // field: 'priceScore',
                    width: 150,
                    editRender: {
                        enabled: true,
                        autofocus: '.vxe-input--inner'
                    },
                    slots: {
                        default: ({ row }) => {
                            return [
                                (<span>{ row.priceScore }</span>)
                            ]
                        },
                        edit: (({row}) => {
                            const props = {
                                type: 'number',
                                min: '0'
                            }
                            const on = {
                                change: () => this.handlePriceScoreChange(row)
                            }
                            return [
                                (<vxe-input vModel={row.priceScore} {...{ props, on }} />)
                            ]
                        })
                    }
                },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_totalScore`, '总分'), field: 'totalScoreNew', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_synthesisRank`, '综合排名'), field: 'synthesisRank', width: 150 }
                // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 150,
                //     editRender: {
                //         enabled: true,
                //         autofocus: '.vxe-input--inner'
                //     },
                //     slots: {
                //         default: ({ row }) => {
                //             return [
                //                 (<span>{ row.remark }</span>)
                //             ]
                //         },
                //         edit: (({row}) => {
                //             return [
                //                 (<vxe-input vModel={row.remark} />)
                //             ]
                //         })
                //     }
                // }
            ],
            upload: {
                modalVisible: false,
                action: '/attachment/purchaseAttachment/upload',
                tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
                accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
                dictCode: 'srmFileTypeBidEva'
                // extraData: {
                //     businessType: 'winBidNotice',
                //     headId: this.vuex_currentEditRow.id
                // }
            }
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        extraData () {
            return {
                businessType: 'bidding',
                headId: this.vuex_currentEditRow.id || '', // 之前传的是包id，现在需要项目列的id
                sourceNumber: this.vuex_currentEditRow.biddingNumber,
                actionRoutePath: '/srm/bidding/purchase/PurchaseBiddingProjectHeadList,/srm/bidding/sale/SaleBiddingHeadList'
            }
        }
    },

    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        // 实时计算总分 totalScoreNew
        handlePriceScoreChange (row) {
            console.log('row :>> ', row)
            // busScore: 商务分, tecScore: 技术分, priceScore: 价格分
            const { busScore = 0, tecScore = 0, priceScore = 0 } = row || {}
            let addTecScore = add(busScore, tecScore)
            let totalScoreNew = add(addTecScore, priceScore)
            totalScoreNew = totalScoreNew.toFixed(4)
            row.totalScoreNew = totalScoreNew
        },
        renderExtra (deadline, fn) {
            const scopedSlots = {
                default: (row) => {
                    // return (<span>{ row.hours } : { row.minutes } : { row.seconds }</span>)
                    return (<span>{ row.totalHours } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_time`, '时') } { row.minutes } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_branch`, '分') } { row.seconds } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_second`, '秒') }</span>)
                }
            }
            return (
                <div class="countdown" style="display: flex; align-items: center;">
                    <a-icon type="info-circle" />
                    <span style="margin: 0 8px;">{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRutK_9fc18dac`, '评标截止倒计时') }：</span>
                    <countdown
                        time={ deadline }

                        scopedSlots={scopedSlots}
                        style={ { fontSize: '12px', color: '#ee1d1d' } }
                        end={ fn }
                    >
                    </countdown>
                </div>
            )
        },
        handleSubmit () {
            const callback = () => {
                const params = {
                    id: this.vuex_currentEditRow.id,
                    biddingSupplierVOList: this.biddingSupplierVOList
                }
                const url = '/bidding/purchaseBiddingEvaResult/submitEvaResult'
                this.confirmLoading = true
                postAction(url, params)
                    .then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                    })
                    .finally(() => {
                        this.confirmLoading = false
                        this.init()
                    })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_UBHN_411b0cb3`, '负责人决标'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLAPHNyRW_d8025ebf`, '是否确认当前录入结果?'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        handleExport () {
            let params = {
                id: this.vuex_currentEditRow.id,
                type: 'project'
            }
            let fileName = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_exportFile`, '导出文件')
            downFile('/bidding/purchaseBiddingEvaResult/exportProjectEvaResult', params).then(data => {
                if (!data) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), fileName+'.xls')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', fileName+'.xls')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            })
        },
        getData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingEvaResult/queryEvaResultByBiddingId'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    this.biddingSupplierVOList = res.result || []
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        getBiddingHeadData () {
            const { id = '' } = this.vuex_currentEditRow || {}
            const url = '/bidding/purchaseBiddingHead/queryById'

            getAction(url, { id }).then(res => {
                if (!res.success) {
                    return
                }
                const { timestamp = '', result = {} } = res || {}
                let evaEndTime = result.evaEndTime_DateMaps || ''
                this.serverTime = timestamp
                if (evaEndTime) {
                    if (timestamp < evaEndTime) {
                        this.evaEndTime = evaEndTime - timestamp
                    }
                }
                // 获取评标材料附件
                this.purchaseAttachmentBidEvaList = result.purchaseAttachmentBidEvaList || []
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            let half = this.height / 2
            this.defaultGridOption.height = Math.max(half, 400)
            this.getBiddingHeadData()
            this.getData()
        },
        attachmentDownloadEvent (row) {
            const { id, fileName } = row
            let downloadUrl = this.url?.download || PURCHASEATTACHMENTDOWNLOADAPI
            getAction(downloadUrl, { id }, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        // 附件预览
        attachmentPreview (preViewFile) {
            this.$previewFile.open({ params: preViewFile, path: preViewFile?.path || '' })
        },
        attachmentDelete (row = {}, config = {}) {
            const url = config.url || (this.url && this.url.attachmentDelete) || '/attachment/purchaseAttachment/delete'
            getAction(url, { id: row.id || '' }).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.getBiddingHeadData()
                }
            })
        }
    },
    // 使用 beforeRouteUpdate 导航守卫监听路由变化
    // 对路由销毁重建
    beforeRouteUpdate (to, from, next) {
        this.routerRefresh() //路由销毁重建方法
        next()
    },
    created () {
        this.init()
    }
}
</script>

<style lang="less" scoped>
.BidOpeningList {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
            margin-top: 12px;
        }
    }
    .title {
        padding: 0 7px;
        border: 1px solid #ededed;
        height: 34px;
        line-height: 34px;
        &.dark {
            background: #f2f2f2;
        }
    }
    .table,
    .description {
        margin-top: 10px;
    }
}
</style>

