<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showDetailPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      refresh
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- <SaleInvitationReceiptEdit
      v-if="showEditPage"
      ref="supplierCapacity"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    /> -->
    <SaleInvitationReceiptDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :ifDetail="ifDetail"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
// import SaleInvitationReceiptEdit from './modules/SaleInvitationReceiptEdit'
import SaleInvitationReceiptDetail from './modules/SaleInvitationReceiptDetail'
import { postAction, getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        // SaleInvitationReceiptEdit,
        SaleInvitationReceiptDetail
    },
    data () {
        return {
            ready: false,
            showDetailPage: false,
            showEditPage: false,
            pageData: {
                businessType: 'tenderInvitationSupplierReceipt',
                form: {
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNdIRLSdIAy_cecd25eb`, '请输入项目名称或项目编号')
                    }
                ],
                
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLsu_38d53294`, '确认参加'), clickFn: this.handleParticipant, allow: this.allowParticipantOrReject},
                    {type: 'release', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xsu_12f75eb`, '不参加'), clickFn: this.handleReject, allow: this.allowParticipantOrReject}
                ],
                optColumnWidth: 270,
                tabsList: []
            }, 
            url: {
                // 列表数据展示
                list: '/tender/sale/tenderInvitationSupplierReceipt/list',
                columns: 'tenderInvitationSupplierReceiptList',
                reject: '/tender/sale/tenderInvitationSupplierReceipt/reject'
            },
            receiptStatus: ''
        }
    },
    watch: {
        '$route.query': {
            immediate: true,
            handler (value) {
                let {receiptStatus} = value
                if (receiptStatus && !this.receiptStatus) {
                    this.receiptStatus = receiptStatus
                    const tenderCurrentRow =this.$ls.get('SET_TENDERCURRENTROW')
                    console.log('tenderCurrentRow', tenderCurrentRow)
                    this.$nextTick(() => {
                        this.handleParticipant(tenderCurrentRow)
                    })
                }
            }
        }
    },
    methods: {
        hideEditPage (){
            // 防止从公告页面进入后点击页面刷新按钮后又进入编辑页面
            const data = this.$route.query
            if (data.receiptStatus) {
                this.$router.push({query: {}})
            }
            this.showDetailPage = false
            this.$refs.listPage.loadData()

        },
        handleView (row){
            this.currentEditRow = row
            // 待确认状态的查看，则不展示第一部分的投标确认函
            if(row.receiptStatus === '1'){
                this.$set(this.currentEditRow, 'ifShow', false)
            }else{
                this.$set(this.currentEditRow, 'ifShow', true)
            }
            this.ifDetail = true
            this.showDetailPage =true
            console.log(this.showDetailPage)
        },
        handleParticipant (row){
            this.currentEditRow = row
            this.$set(this.currentEditRow, 'ifShow', true)
            this.ifDetail = false
            this.showDetailPage = true
        },
        handleReject (row) {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xsu_12f75eb`, '不参加'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLxsu_eb8943be`, '是否确认不参加?'),
                onOk: () =>  {
                    this.loading = true
                    getAction(this.url.reject, {id: row.id}).then(res => {
                        if(res.success) {
                            this.$message.success(res.message)
                            this.$refs.listPage.loadData()
                        }else {
                            this.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        allowParticipantOrReject (row){
            // 只有待确认状态1才能进行 参加 与 不参加 操作
            return (row.receiptStatus !== '1' && row.subpackageStatus < 4010)
            // return false
        }
        
    },
    async mounted () {
        await this.serachCountTabs('/tender/purchase/tenderInvitationSupplierReceipt/counts')
    },
    created () {
        console.log(this.showDetailPage)
    }
    
}
</script>