<template>
  <div class="PurchaseEightDisciplinesHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :collapseHeadCode="['baseForm', 'busRule', 'personFrom']"
        modelLayout="masterSlave"
        pageStatus="edit"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
      <a-modal
        v-drag
        :width="1000"
        :height="500"
        v-model="fileCompareVisible"
        title="对比结果"
        @ok="fileCompareVisible = false"
      >
        <vxe-grid
          :height="300"
          v-bind="recordGridOptions"
        >
        </vxe-grid>
      </a-modal>
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"
      />
      <a-modal
        v-drag
        forceRender
        :visible="editRowModal"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')"
        :width="800"
        @ok="confirmEdit"
        @cancel="closeEditModal"
      >
        <j-editor
          v-if="editRowModal"
          v-model="currentItemContent"
        ></j-editor>
      </a-modal>
      <a-modal
        v-drag
        v-model="previewModal"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
        :footer="null"
        :width="1000"
      >
        <div
          style="width: 210mm; margin: 0 auto; padding: 2.54mm 3.18mm; border: 1px solid #ccc; overflow: auto"
          v-html="previewContent"
        ></div>
      </a-modal>
      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk"
      />
      <view-item-diff-modal ref="viewDiffModal" />
      <His-Contract-Item-Modal ref="hisContractItemModal" />
      <select-Data-Modal
        ref="selectDataModal"
        :contractType="currentEditRow.contractType"
        @ok="selectDataOk"
      />
    </a-spin>
      <ItemImportExcel
          ref="itemImportExcel2"
          @importCallBack="importCallBack"
      />
  </div>
</template>

<script lang="jsx">
import { composePromise, getLangAccount, srmI18n } from '@/utils/util.js'
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import selectDataModal from './selectDataModal'
import ViewItemDiffModal from './ViewItemDiffModal'
import HisContractItemModal from './HisContractItemModal'
import { getAction, postAction } from '@/api/manage'
import JEditor from '@comp/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import { axios } from '@/utils/request'
import { BUTTON_BACK, BUTTON_SAVE, BUTTON_SUBMIT, BUTTON_PUBLISH } from '@/utils/constant.js'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
import dayjs from 'dayjs'

export default {
  name: 'PurchaseContractHeadModal',
  mixins: [businessUtilMixin],
  components: {
    flowViewModal,
    BusinessLayout,
    fieldSelectModal,
    selectDataModal,
    ViewItemDiffModal,
    HisContractItemModal,
    JEditor,
    ItemImportExcel
  },
  props: {
    currentEditRow: {
      required: true,
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      recordGridOptions: {
        columns: [],
        data: []
      },
      fileCompareVisible: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 15 },
      refresh: true,
      showHelpTip: false,
      editRowModal: false,
      previewModal: false,
      notShowTableSeq: true,
      fileRow: {},
      selectType: '',
      editItemRow: {},
      currentItemContent: '',
      previewContent: '',
      confirmLoading: false,
      currentBasePath: this.$variateConfig['domainURL'],
      flowView: false,
      flowId: '0',
      requestData: {
        detail: {
          url: '/contract/purchaseContractHead/queryById',
          args: (that) => {
            return { id: that.currentEditRow.id }
          }
        }
      },
      externalToolBar: {
        purchaseContractItemList: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_iFnewj_628317cf`, '选择合同来源'),
            key: 'gridAdd',
            click: this.insertGridItem,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            click: this.insertGridNewItem,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            click: this.deleteGridItem
          },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
            key: 'fillDown',
            type: 'tool-fill',
            beforeCheckedCallBack: this.fillDownGridItem
          },
          // {
          //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Improt`, '导入'),
          //     type: 'upload',
          //     click: this.importItemInfo
          // }
        ],
        purchaseContractContentItemList: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            click: this.addContentItemRow,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            click: this.deleteContentGridItem
          }
        ],
        purchaseContractPromiseList: [],
        contractItemCustom1List: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            click: this.businessGridAdd,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            click: this.businessGridDelete
          }
        ],
        contractItemCustom2List: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            click: this.businessGridAdd,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            click: this.businessGridDelete
          }
        ],
        contractItemCustom3List: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            click: this.businessGridAdd,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            click: this.businessGridDelete
          }
        ],
        contractItemCustom4List: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            click: this.businessGridAdd,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            click: this.businessGridDelete
          }
        ],
        contractItemCustom5List: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            click: this.businessGridAdd,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            click: this.businessGridDelete
          }
        ],
        purchaseAttachmentList: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
            key: 'upload',
            args: {
              property: 'label', // 可省略
              itemInfo: [], // 必传
              action: '/attachment/purchaseAttachment/upload', // 必传
              businessType: 'contract', // 必传,
              itemNumberKey: 'materialNumber',
              itemNumbeValueProp: 'value',
              // itemNumberLabel: '关联tab',
              itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
              fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
              headId: '', // 必传
              modalVisible: false // 必传
            },
            attr: this.attrHandle,
            callBack: this.uploadCallBack
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
            key: 'gridDelete',
            click: this.deleteFilesEvent
          }
        ]
      },
      pageHeaderButtons: [
        {
          ...BUTTON_SAVE,
          args: {
            url: '/contract/purchaseContractHead/edit'
          },
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
          authorityCode: 'contract#purchaseContractHead:edit',
          isFlattenHeadTypeForm: true // 是否展开 groupType 为 head 的分组合并至提交接口中
        },
        // {
        //   ...BUTTON_SUBMIT,
        //   args: {
        //     url: '/contract/purchaseContractHead/submitOA'
        //   },
        //   title: '提交OA审批',
        //   click: this.handleCustomSubmit,
        //   show: this.handleShowFn,
        //   handleBefore: this.handleSubmitBefore //this.handleOaSubmitBefore
        // },
        {
          ...BUTTON_SUBMIT,
          args: {
            url: '/a1bpmn/audit/api/submit'
          },
          title: '提交审批',
          click: this.handleCustomSubmit,
          show: this.handleShowCustomSubmit,
          handleBefore: this.handleSubmitBefore //this.handleOaSubmitBefore
        },
        {
          ...BUTTON_PUBLISH,
          args: {
            url: '/contract/purchaseContractHead/publish'
          },
          method: 'get',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '发布'),
          show: this.handleShowCustomSubmit2,
          click: this.handleCustomSubmit,
          handleBefore: this.handleSubmitBefore //this.handleOaSubmitBefore
        },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
          attrs: {
            type: 'primary'
          },
          authorityCode: 'contract#purchaseContractHead:getPreviewData',
          click: this.previewPdf
        },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
          attrs: {
            type: 'primary'
          },
          authorityCode: 'contract#purchaseContractHead:download',
          click: this.downloadFile
        },
        {
          ...BUTTON_BACK,
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回')
        }
      ],
      url: {
        save: '/contract/purchaseContractHead/edit',
        audit: '/a1bpmn/audit/api/submit',
        isExistFrozen: 'supplier/supplierMaster/isExistFrozenStateData',
        download: '/attachment/purchaseAttachment/download',
        detail: '/contract/purchaseContractHead/queryById'
      }
    }
  },
  computed: {
    remoteJsFilePath() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
      return `${account}/purchase_contract_${templateNumber}_${templateVersion}`
    }
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy()
    }
  },
  mounted() {},
  methods: {
    // importItemInfo(){
    //     const form = this.getAllData()
    //     let params = {
    //         id: form.id,
    //         templateAccount: form.templateAccount,
    //         templateNumber: form.templateNumber,
    //         templateVersion: form.templateVersion,
    //         handlerName: 'purchaseContractItemImportExcel',
    //         roleCode: 'purchase',
    //         excelCode: 'purchaseContractItemImportExcel',
    //         excelTemplateType: 'excel'
    //     }
    //
    //     if (!params.id) {
    //         return this.$message.warning('请先保存再进行该操作')
    //     }
    //     this.$refs.itemImportExcel2.open(params, '合同行导入', 'purchaseContractItemList', '/base/excelHeader/downloadTemplate')
    //
    // },
    //   importCallBack(result) {
    //       try {
    //           let response = result.file.response
    //           let itemGrid = this.$refs.editPage.$refs.purchaseContractItemList[0]
    //           let insertData = response.result.dataList
    //           this.pageConfig.itemColumns.forEach((item) => {
    //               if (item.defaultValue) {
    //                   insertData.forEach((insert) => {
    //                       if (!insert[item.field]) {
    //                           insert[item.field] = item.defaultValue
    //                       }
    //                   })
    //               }
    //           })
    //           itemGrid.insertAt(insertData, -1)
    //       } catch (error) {
    //           console.log(error)
    //       } finally {
    //           this.$refs.itemImportExcel2.visible = false
    //       }
    //   },
    formatPageData(item) {
      console.log('請求接口後格式化页面數據', item)
      if (item.totalTaxAmount === 0 || Math.abs(item.totalTaxAmount) > 0) {
        item.totalTaxAmount = Number(item.totalTaxAmount).toFixed(2)
      }
      if (item.totalNetAmount === 0 || Math.abs(item.totalNetAmount) > 0) {
        item.totalNetAmount = Number(item.totalNetAmount).toFixed(2)
      }

      return item
    },
    // 請求接口後格式化列表數據
    formatTableData(data) {
      this.setColumnData()
      let purchaseContractItemList = data.purchaseContractItemList || []
      console.log('請求接口後格式化列表數據', purchaseContractItemList)
      purchaseContractItemList = purchaseContractItemList.map((item) => {
        if (item.price === 0 || Number(item.price) > 0) {
          item.price = Number(item.price).toFixed(6)
        }
        if (item.taxAmount === 0 || Number(item.taxAmount) > 0) {
          item.taxAmount = Number(item.taxAmount).toFixed(2)
        }
        if (item.netAmount === 0 || Number(item.netAmount) > 0) {
          item.netAmount = Number(item.netAmount).toFixed(2)
        }
        if (item.totalTaxAmount === 0 || Number(item.totalTaxAmount) > 0) {
          item.totalTaxAmount = Number(item.totalTaxAmount).toFixed(2)
        }
        if (item.totalNetAmount === 0 || Number(item.totalNetAmount) > 0) {
          item.totalNetAmount = Number(item.totalNetAmount).toFixed(2)
        }
        return item
      })
      data.purchaseContractItemList = purchaseContractItemList
      return data
    },
      // 设置列显示
      setColumnData() {
          let st = setTimeout(() => {
              let itemGrid = this.getItemGridRef('purchaseContractItemList')
              itemGrid.editConfig = {
                  trigger:'click',
                  mode:'cell',
                  beforeEditMethod({row,column}) {
                      if(column.field == 'price'&&(row.price!==undefined || row.price!==null || row.price!=='') && !['material','addedManually'].includes(row.sourceType)){
                          return false
                      }
                      if(column.field == 'quantity'&&(row.quantity !==undefined || row.quantity !==null || row.quantity !=='')&& !['material','addedManually'].includes(row.sourceType)){
                          return false
                      }
                      return true
                  }
              }
              clearTimeout(st)
              st = null
          }, 100)
      },
    // OA提交审批
    oaSubmit(args) {
      const pageData = this.getAllData()
      const steps = [
        this.stepBusinessOaSubmit,
        this.confirmSubmit,
        this.setShowMessageTrue, // 恢复按钮配置的 showMessage
        this.stepBusinessSave,
        this.changeRequestUrl,
        this.setShowMessageFalse, // 隐藏按钮配置的 showMessage
        this.stepValidate
      ]
      const handleCompose = composePromise(...steps)
      this.confirmLoading = true
      try {
        handleCompose({ ...args, pageData })
          .then(
            (res) => {
              console.log('all submit success', res)
              this.businessHide()
            },
            (err) => {
              console.log('all submit error', err)
            }
          )
          .finally(() => {
            this.confirmLoading = false
          })
      } finally {
        this.confirmLoading = false
      }
    },
    stepBusinessOaSubmit(args) {
      const steps = this.normalizeStepPromiseMethod('oaSubmit') || []
      const handleCompose = composePromise(...steps)
      return handleCompose(args)
    },
    handleOaSubmitBefore(args) {
      let thisData = this.getItemGridRef('purchaseContractContentItemList').getTableData().fullData
      let thisData2 = this.getItemGridRef('purchaseContractItemList').getTableData().fullData
      let param = this.getAllData()
      if (param.contractLevels == 'subsidiary' && param.masterContractNumber == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_neDtLNneKWdneylSW_94ea76ac`, '合同层级为从合同时,主合同号必填!'))
        return
      }
      if (!(param && param.id != '')) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WsMWKDJUz_81a98950`, '先保存,再提交审批'))
        return
      }
      if (!thisData || thisData.length === 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTermLibraryInformationNeedsConfigured`, '需配置合同条款库信息!'))
        return
      }
      if (!thisData2 || thisData2.length === 0) {
        this.$message.warning('需填写合同标的信息!')
        return
      }
      return new Promise((resolve) => {
        let { pageData = {} } = args || {}
        let { busRule = {}, personFrom = {}, oaFrom = {} } = pageData || {}

        // pageData = { ...pageData, ...busRule, ...personFrom}
        delete pageData.busRule
        delete pageData.personFrom
        delete pageData.oaFrom

        pageData = Object.assign({}, pageData, busRule, personFrom, oaFrom)

        args = Object.assign({}, args, {
          pageData
        })
        resolve(args)
      })
    },
    // [FILLDOWN_CALLBACK] ({row, $grid, column}) {
    //     // 当前表格回调重写 注意原bindfunction会有
    //     // 虚拟行index
    //     const rowIndex = $grid.getVMRowIndex(row)
    //     const { fullData } = $grid.getTableData()
    //     fullData.forEach((item, index) => {
    //         if (index > rowIndex) {
    //             // 关联影响的值
    //             item['materialDesc'] = fullData[rowIndex][column.property]
    //         }
    //     })
    // },
    handleAccount({ _pageData, _form, _row, _value, _cacheAllData, _data }) {
      // 自定义方法
    },
    preViewEvent(Vue, row) {
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    // 显示OA审批
    handleShowCustomSubmit() {
      let rs = true
      if (this.currentEditRow && this.currentEditRow.id) {
        let formData = this.getAllData();
        if (formData.systemAudit == 1) rs = true
        else rs = false
      } else {
        rs = false
      }
      return rs
    },
    // 显示发布
    handleShowCustomSubmit2() {
      let rs = true
      if (this.currentEditRow && this.currentEditRow.id) {
        let formData = this.getAllData();
        if (formData.systemAudit == 1) rs = false
        else rs = true
      } else {
        rs = false
      }
      console.log(123, rs)
      return rs
    },
    attrHandle() {
      return {
        sourceNumber: this.currentEditRow.contractNumber,
        actionRoutePath: '/srm/contract/purchase/PurchaseContractHeadList,/srm/contract/sale/SaleContractHeadList'
      }
    },
    handleBeforeRemoteConfigData(data) {
      const formFields = data.formFields
      formFields.forEach((v) => {
        if (v.fieldName == 'contractType') {
          if (!this.currentEditRow.id) {
            this.$emit('update:currentEditRow', { ...this.currentEditRow, contractType: v.defaultValue })
          }
        }
      })
      return {
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupNameI18nKey: '',
            groupCode: 'purchaseAttachmentList',
            groupType: 'item',
            sortOrder: '10',
            width: 300,
            extend: {
              optColumnList: [
                {
                  key: 'download',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                  click: this.downloadEvent
                },
                {
                  key: 'preView',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                  click: this.preViewEvent
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  key: 'gridDelete',
                  click: this.deleteFiles
                },
                {
                  key: 'preView',
                  title: this.$srmI18n(`${this.$getLangAccount()}#`, '比对'),
                  click: this.fileCompare,
                  authorityCode: 'compare#elsFileCompareHead:edit'
                },
                {
                  key: 'preView',
                  title: this.$srmI18n(`${this.$getLangAccount()}#`, '比对结果'),
                  click: this.fileCompareResult,
                  authorityCode: 'compare#elsFileCompareHead:list'
                }
              ]
            }
          },
          {
            groupName: '合同条款库',
            groupNameI18nKey: 'i18n_title_purchaseContractLibrary',
            groupCode: 'purchaseContractContentItemList',
            groupType: 'item',
            sortOrder: '4',
            show: true,
            extend: {
              optColumnList: []
            }
          },
          {
            groupName: '关联订单',
            groupNameI18nKey: 'i18n_field_RKIt_26f94cf4',
            groupCode: 'orderItemList',
            groupType: 'item',
            sortOrder: '5',
            show: false, // 业务要求 关联订单隐藏
            extend: {
              optColumnList: []
            }
          }
        ],
        itemColumns: [
          {
            title: '订单号',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_orderNumber',
            field: 'orderNumber',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '订单行号',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_orderItemNumber',
            field: 'itemNumber',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '行类型',
            field: 'itemType_dictText',
            fieldLabelI18nKey: 'i18n_field_cAc_20f0fbc',
            groupCode: 'orderItemList',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '订单行状态',
            field: 'itemStatus_dictText',
            fieldLabelI18nKey: 'i18n_field_ItczE_d79d4484',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '工厂',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_factory',
            field: 'factoryCode_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '库存地点代码',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_GMnCoo_92e8fb6c',
            field: 'storageLocationCode_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '物料编码',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_materialCode',
            field: 'materialNumber',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '物料名称',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_title_materialName',
            field: 'materialName',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '物料描述',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_itemDescription',
            field: 'materialDesc',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '物料规格',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_title_materialSpec',
            field: 'materialSpec',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '物料组',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_title_materialSpec',
            field: 'materialGroupCode',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '物料组名称',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_materialGroupName',
            field: 'materialGroupName',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '采购周期',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_purchaseCycle',
            field: 'purchaseCycle',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '采购类型',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_purchaseType',
            field: 'purchaseType_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '要求交期',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_title_requiredDeliveryDate',
            field: 'requireDate',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '订单数量',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_title_orderQuantity',
            field: 'quantity',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '采购单位',
            groupCode: 'orderItemList',
            fieldLabelI18nKey: 'i18n_field_purchaseUnit',
            field: 'purchaseUnit',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: '来源合同行号',
            fieldLabelI18nKey: 'i18n_field_wjneEy_ad073b7a',
            groupCode: 'orderItemList',
            field: 'sourceItemNumber',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: '项目编号',
            fieldLabelI18nKey: 'i18n_field_projectNumber',
            field: 'itemId',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            dictCode: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: '项目名称',
            fieldLabelI18nKey: 'i18n_field_projectName',
            field: 'itemName',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: '项目类型',
            fieldLabelI18nKey: 'i18n_field_itemType',
            field: 'itemType_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: '项目版本',
            fieldLabelI18nKey: 'i18n_btn_dIrv_471c1a39',
            field: 'itemVersion',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: '变更标识',
            fieldLabelI18nKey: 'i18n_title_changeIdentification',
            field: 'changeFlag',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            cellRender: {
              name: '$switch',
              type: 'visible',
              props: { closeValue: '0', openValue: '1', disabled: true }
            },
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: '来源类型',
            fieldLabelI18nKey: 'i18n_title_sourceType',
            field: 'sourceType_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: '操作',
            fieldLabelI18nKey: 'i18n_title_operation',
            field: 'sourceType_dictText',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            align: 'left',
            slots: {
              default: ({ row }) => {
                let resultArray = []
                resultArray.push(
                  <a
                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                    onClick={() => this.viewDetail(row)}
                  >
                    {' '}
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                  </a>
                )
                resultArray.push(
                  <a
                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑')}
                    style='margin-left:8px'
                    onClick={() => this.editRow(row)}
                  >
                    {' '}
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑')}
                  </a>
                )
                if (row.changeFlag == '1') {
                  resultArray.push(
                    <a
                      title={this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Il_b8efb`, '比对')}
                      style='margin-left:8px'
                      onClick={() => this.viewDiff(row)}
                    >
                      {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Il_b8efb`, '比对')}{' '}
                    </a>
                  )
                }
                return resultArray
              }
            }
          },
          {
            groupCode: 'purchaseAttachmentList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
            fieldLabelI18nKey: '',
            field: 'fileName',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150'
          },
          {
            groupCode: 'purchaseAttachmentList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
            fieldLabelI18nKey: '',
            field: 'uploadTime',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150'
          },
          {
            groupCode: 'purchaseAttachmentList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
            fieldLabelI18nKey: '',
            field: 'uploadElsAccount_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150'
          },
          {
            groupCode: 'purchaseAttachmentList',
            fieldLabelI18nKey: '',
            field: 'uploadSubAccount_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: 120
          },
          {
            groupCode: 'purchaseAttachmentList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
            fieldLabelI18nKey: '',
            field: 'grid_opration',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            slots: { default: 'grid_opration' }
          }
        ]
      }
    },
    handleAfterDealSource(pageConfig, resultData) {
      let formModel = pageConfig.groups[0].formModel
      if (resultData.id) {
        for (let key in resultData) {
          formModel[key] = resultData[key]
        }
        this.externalToolBar['purchaseAttachmentList'][0].args.headId = resultData.id || ''
      }
      let itemInfo = pageConfig.groups.map((n) => ({ label: n.groupName, value: n.groupCode }))
      this.externalToolBar['purchaseAttachmentList'][0].args.itemInfo = itemInfo
      if (formModel.showCustom1 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', false)
      }
      if (formModel.showCustom2 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', false)
      }
      if (formModel.showCustom3 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', false)
      }
      if (formModel.showCustom4 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', false)
      }
      if (formModel.showCustom5 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', false)
      }
      if (formModel.showItem == '1') {
        this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', false)
      } else {
        this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', true)
      }
      if ((formModel.contractStatus == '3' || formModel.contractStatus == '8' || formModel.contractStatus == '6') && (formModel.promiseType == 'promiseSale' || formModel.promiseType == 'promisePurchase')) {
        this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', false)
      } else {
        this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', true)
      }

      // 业务要求 关联订单隐藏
      // if (formModel.promisePurchase == 'order' && (formModel.contractStatus == '3' || formModel.contractStatus == '8' || formModel.contractStatus == '6')) {
      //     this.hideSingleGroup(this.businessRefName, 'orderItemList', false)
      // } else {
      //     this.hideSingleGroup(this.businessRefName, 'orderItemList', true)
      // }
    },
    viewDiff(row) {
      this.$refs.viewDiffModal.open(row)
    },
    viewDetail(row) {
      this.$refs.hisContractItemModal.open(row)
    },
    handleSaveAfter(obj) {
      return new Promise((resolve) => {
        return resolve(obj)
      })
    },
    filterObj(code, tar) {
      const localPageConfig = getPageConfig() // eslint-disable-line
      const arr = localPageConfig.formFields.filter((rs) => rs.groupCode == code)
      console.log(arr)
      let result = {}
      arr.forEach((rs) => {
        const name = tar[rs.fieldName]
        if (name != 'undefined') {
          result[rs.fieldName] = name
        }
      })
      return result
    },
    handleSaveBefore(args) {
      let { Vue, pageConfig, btn, allData } = args
      let obj = allData && JSON.parse(JSON.stringify(allData))
      const localBusRule = this.filterObj('busRule', obj.busRule)
      const localPersonFrom = this.filterObj('personFrom', obj.personFrom)
      obj = Object.assign({}, obj, localBusRule, localPersonFrom)
      let params = {
        Vue,
        pageConfig,
        btn,
        allData: obj
      }
      delete obj.busRule
      delete obj.personFrom
      return new Promise((resolve) => {
        return resolve(params)
      })
    },
    paramIntegrate() {
      let pageData = this.getAllData() || {}
      let { busRule = {}, personFrom = {} } = pageData || {}
      // pageData = { ...pageData, ...busRule, ...personFrom}
      delete pageData.busRule
      delete pageData.personFrom
      pageData = Object.assign({}, pageData, busRule, personFrom)
      return pageData
    },
    preview() {
      let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
      if (!contentGrid.getTableData().tableData.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
        return
      }
      this.$refs.editPage.confirmLoading = true
      getAction('/contract/purchaseContractHead/getPreviewData', { id: this.currentEditRow.id })
        .then((res) => {
          if (res.success) {
            this.previewModal = true
            this.previewContent = res.result
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.editPage.confirmLoading = false
        })
    },
    //新增行
    insertGridItem() {
      let pageData = this.getAllData()
      if (!pageData.personFrom.toElsAccount) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierFirst`, '请先选择供应商！'))
        return
      }
      let params = { toElsAccount: pageData.personFrom.toElsAccount, company: pageData.company || null }
      this.$refs.selectDataModal.open(params)
    },
    insertGridNewItem() {
      let detailGrid = this.getItemGridRef('purchaseContractItemList')
      let tableData = detailGrid.getTableData().fullData
      let addTableData = []
      let lineNum = tableData.length + 1
      addTableData.push({
        itemNumber: lineNum,
        sourceType: 'addedManually',
        sourceType_dictText: '手工新增',
        oaAuditStatus: this.currentEditRow.auditStatus
      })
      detailGrid.insertAt(addTableData, -1)
    },
    selectDataOk(data) {
      console.log('data type ==== ', data, this.currentEditRow.contractType)
      let detailGrid = this.getItemGridRef('purchaseContractItemList')
      let tableData = detailGrid.getTableData().fullData
      let addTableData = []
      let sourceType = this.currentEditRow.contractType
      data.forEach((item, i) => {
        let lineNum = tableData.length + (i + 1)
        if (!!item.quantity) {
          item.secondaryQuantity = item.quantity / (item.conversionRate || 1)
        } else {
          item.secondaryQuantity = 0
        }
        item.secondaryQuantity = item.secondaryQuantity.toFixed(6)
        console.log(item.secondaryQuantity)
        let newTableRowData = {
          itemNumber: lineNum,
          quantityUnit: item.baseUnit || item.quantityUnit, // 计量单位
          conversionRate: item.conversionRate, // 换算率
          secondaryQuantity: item.secondaryQuantity, // 辅数量
          sourceType: item.sourceType,
          sourceType_dictText: item.sourceType_dictText,
          sourceNumber: item.sourceNumber,
          sourceItemNumber: item.sourceItemNumber,
          sourceId: item.sourceId,
          sourceItemId: item.sourceItemId,
          taxAmount: item.taxAmount,
          netAmount: item.netAmount || 0,
          currency: item.currency,
          price: item.price,
          purchaseUnit: item.purchaseUnit,
          quantity: item.quantity,
          materialSpec: item.materialSpec,
          materialDesc: item.materialDesc,
          materialName: item.materialName,
          materialGroup: item.materialGroup,
          materialGroup_dictText: item.materialGroup_dictText,
          taxCode: item.taxCode,
          taxRate: item.taxRate,
          materialNumber: item.materialNumber,
          materialId: item.materialId,
          oaAuditStatus: this.currentEditRow.auditStatus,
          purchaseRemark:item.purchaseRemark,
          supplierRemark:item.supplierRemark
      }

        if (sourceType == 'bidding') {
          // 招投标
          newTableRowData.sourceBizOwnType = item.sourceType
        } else if (sourceType == 'enquiry') {
          // 询报价
          newTableRowData.sourceBizOwnType = item.sourceType
        }

        if (Object.keys(item).includes('controlNumber')) {
          newTableRowData.controlNumber = item.controlNumber // 是否控制數量
          newTableRowData.balanceLackTolerance = item.balanceLackTolerance // 结算不足容差率
          newTableRowData.balanceOverTolerance = item.balanceOverTolerance // 结算超量容差率
          newTableRowData.lackTolerance = item.lackTolerance // 不足容差率
          newTableRowData.overTolerance = item.overTolerance // 超量容差率
        }
        addTableData.push(newTableRowData)
      })
      this.getRecordExpiryDate(data)
      detailGrid.insertAt(addTableData, -1)
      this.setColumnData()
      this.calculateAmount()
    },
    getRecordExpiryDate(data) {
      // 时间数据数组, 去掉空值， null 或者是undefined
      const timeData = data.map(item => item.recordExpiryDate)?.filter(Boolean)
      // 将时间字符串转换为Date对象进行比较
      const earliestDate = timeData?.reduce((earliest, current) => {
        const currentDate = dayjs(current);
        return currentDate.isBefore(earliest) ? currentDate : earliest;
      }, dayjs(timeData[0]));
      this.setValueByField('busRule','expiryDate', earliestDate ? earliestDate.format('YYYY-MM-DD'): '');
    },
    calculateAmount() {
      let detailGrid = this.getItemGridRef('purchaseContractItemList').getTableData().tableData
      let totalTaxAmount = 0
      let totalNetAmount = 0
      detailGrid.forEach((item) => {
        if (item.taxAmount) {
          totalTaxAmount += Number(item.taxAmount)
        }
        if (item.netAmount) {
          totalNetAmount += Number(item.netAmount)
        }
      })
      let allData = this.$refs[`${this.businessRefName}`].extendAllData()
      allData.pageConfig.groups[1].formModel.totalTaxAmount = totalTaxAmount
      allData.pageConfig.groups[1].formModel.totalNetAmount = totalNetAmount
    },
    //删除复选框选定行
    deleteGridItem() {
      let itemGrid = this.getItemGridRef('purchaseContractItemList')
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      itemGrid.removeCheckboxRow()
      this.calculateAmount()
    },
    addContentItemRow() {
      this.selectType = 'content'
      let item = {
        selectModel: 'multiple',
        sourceUrl: '/contract/purchaseContractLibrary/list',
        params: {
          order: 'desc',
          column: 'id'
        },
        columns: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
            field: 'itemType_dictText',
            width: 100
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
            field: 'itemName',
            width: 250
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_dIrv_471c1a39`, '项目版本'),
            field: 'itemVersion',
            width: 100
          }
        ]
      }
      this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
    },
    addSelectModel(id) {
      getAction('/contract/purchaseContractTemplateHead/queryById', { id: id }).then((res) => {
        if (res.success) {
          let detailGrid = this.getItemGridRef('purchaseContractContentItemList')
          detailGrid.remove()
          let tableData = detailGrid.getTableData().fullData

          let addTableData = []
          res.result.purchaseContractTemplateItemList.forEach((item, i) => {
            let lineNum = tableData.length + (i + 1)
            addTableData.push({
              itemNumber: lineNum,
              itemName: item.itemName,
              itemVersion: item.itemVersion,
              itemType: item.itemType,
              itemType_dictText: item.itemType_dictText,
              itemContent: item.itemContent,
              originalContent: item.itemContent,
              itemId: item.id,
              changeFlag: '0',
              sourceType: '1',
              sourceType_dictText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTemplate`, '合同模板')
            })
          })
          let conent = ''
          conent = res.result.purchaseContractTemplateItemList.map((item) => {
            return item.itemContent
          })
          this.businessTemplate = conent.join('')
          detailGrid.insertAt(addTableData, -1)
        }
      })
    },
    //删除复选框选定行
    deleteContentGridItem() {
      let itemGrid = this.getItemGridRef('purchaseContractContentItemList')
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      itemGrid.removeCheckboxRow()
    },
    // 修改
    editRow(row) {
      this.editItemRow = row
      this.currentItemContent = row.itemContent
      this.editRowModal = true
    },
    closeEditModal() {
      this.editRowModal = false
    },
    confirmEdit() {
      if (this.currentItemContent == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CcxOLVW_60c5e69d`, '内容不能为空'))
        return false
      }
      this.editItemRow.itemContent = this.currentItemContent
      let changeFlag = '0'
      if (this.editItemRow.itemContent != this.editItemRow.originalContent) {
        changeFlag = '1'
      }
      this.editItemRow.changeFlag = changeFlag
      this.editRowModal = false
    },
    /*rowDrop () {
            this.$nextTick(() => {
                let contractBuyContentItemList = this.getItemGridRef("purchaseContractContentItemList")
                this.sortable = Sortable.create(contractBuyContentItemList.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
                    handle: '.drag-btn',
                    onEnd: ({ newIndex, oldIndex }) => {
                        let { fullData } = contractBuyContentItemList.getTableData()
                        let tableData = [...fullData]
                        let currRow = tableData.splice(oldIndex, 1)[0]
                        tableData.splice(newIndex, 0, currRow)
                        tableData = tableData.map((item, index) => {
                            item.itemNumber = index + 1
                            return item
                        })
                        contractBuyContentItemList.loadData(tableData)
                        contractBuyContentItemList.syncData()
                    }
                })
            })
        },*/
    uploadCallBack(result) {
      let fileGrid = this.getItemGridRef('purchaseAttachmentList')
      fileGrid.insertAt(result, -1)
    },
    deleteFilesEvent() {
      const fileGrid = this.getItemGridRef('purchaseAttachmentList')
      const checkboxRecords = fileGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      const ids = checkboxRecords.map((n) => n.id).join(',')
      const params = {
        ids
      }
      getAction('/attachment/purchaseAttachment/deleteBatch', params).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.removeCheckboxRow()
      })
    },
    // 批量删除
    deleteFiles(Vue, row) {
      const fileGrid = Vue.$refs.purchaseAttachmentList
      const id = row.id
      const params = {
        id
      }
      getAction('/attachment/purchaseAttachment/delete', params).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.remove(row)
      })
    },
    goBack() {
      this.$emit('hide')
    },
    saveEvent() {
      this.$refs.editPage.postData()
    },
    async handleCustomSubmit(args) {
      try {
        let res = await getAction('/contract/purchaseContractHead/queryContractSrmFlowStatus')
        if (res.result == 1) {
          args.btn.args.url = '/a1bpmn/audit/api/submit'
          args.btn.key = 'submit'
        } else {
          args.btn.args.url = '/contract/purchaseContractHead/publish'
          args.btn.key = 'publish'
        }
      } catch (error) {
        this.$message.warning(res.message)
      }

      const { showItem, contractLevels, masterContractNumber } = this.getAllData()
      if (contractLevels == 'subsidiary' && masterContractNumber == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_neDtLNneKWdneylSW_94ea76ac`, '合同层级为从合同时,主合同号必填!'))
        return new Promise((resolve, reject) => {
          reject(false)
        })
      }
      if (showItem == '1') {
        let itemGrid = this.getItemGridRef('purchaseContractItemList')
        let { fullData } = itemGrid.getTableData()
        if (!fullData.length) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRRSuIccVH_cca12f4`, '请至少添加一行行信息'))
          return false
        }
      }
      // 获取页面所有数据
      // const allData = this.getAllData() || {}
      // 这里可以添加自定义校验逻辑
      function add(a, b) {
        let c, d, e
        try {
          c = a.toString().split('.')[1].length
        } catch (f) {
          c = 0
        }
        try {
          d = b.toString().split('.')[1].length
        } catch (f) {
          d = 0
        }
        e = Math.pow(10, Math.max(c, d))
        return (mul(a, e) + mul(b, e)) / e
      }

      function mul(a, b) {
        let c = 0
        let d = a.toString()
        let e = b.toString()
        try {
          c += d.split('.')[1].length
        } catch (f) {}
        try {
          c += e.split('.')[1].length
        } catch (f) {}
        return (Number(d.replace('.', '')) * Number(e.replace('.', ''))) / Math.pow(10, c)
      }

      if (this.getAllData().showCustom1 === '1') {
        let contractItemCustom1List = this.getItemGridRef('contractItemCustom1List').getTableData().fullData
        let totalAmount = 0.0
        let totalPerformanceRatio = 0.0
        for (let i = 0; i < contractItemCustom1List.length; i++) {
          if (contractItemCustom1List[i].performanceRatio === '0.000%') {
            let num = i + 1
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '第' + num + '行履行比例需大于0%'))
            return
          }
          totalAmount = add(totalAmount, contractItemCustom1List[i].amount)
          totalPerformanceRatio = add(totalPerformanceRatio, (contractItemCustom1List[i].performanceRatio + '').replace('%', ''))
        }

        if (totalPerformanceRatio < 100 || totalPerformanceRatio > 100) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IclknxEU100xqDJ_6cd18c87`, '履行比总和不等于100%，不可提交！'))
          return
        }
        if (this.getAllData().targetQuantity && (totalAmount < this.getAllData().targetQuantity || totalAmount < this.getAllData().targetQuantity)) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '履行总金额不等于合同含税总金额 ' + this.getAllData().targetQuantity + '，不可提交！'))
          return
        }
      }
      this.composeBusinessSubmit(args)
    },
    // 编辑模板组合式步骤: 提交审批
    stepBusinessSubmit(args) {
      let key = 'submit'
      try {
        if (args.btn.key == 'publish') key = 'publish'
      } catch (error) {}
      const steps = this.normalizeStepPromiseMethod(key) || []
      const handleCompose = composePromise(...steps)
      return handleCompose(args)
    },
    handleSubmitBefore(args) {
      let thisData = this.getItemGridRef('purchaseContractContentItemList').getTableData().fullData
      let param = this.paramIntegrate()
      if (param.contractLevels == 'subsidiary' && param.masterContractNumber == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_neDtLNneKWdneylSW_94ea76ac`, '合同层级为从合同时,主合同号必填!'))
        return new Promise((resolve, reject) => {
          reject(false)
        })
      }
      if (!(param && param.id != '')) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WsMWKDJUz_81a98950`, '先保存,再提交审批'))
        return new Promise((resolve, reject) => {
          reject(false)
        })
      }

      if (!thisData || thisData.length === 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTermLibraryInformationNeedsConfigured`, '需配置合同条款库信息!'))
        return new Promise((resolve, reject) => {
          reject(false)
        })
      }

      return new Promise((resolve) => {
        const frozen = {
          toElsAccount: args.allData.toElsAccount,
          frozenFunction: '3',
          orgType: '0',
          orgCode: args.allData.purchaseOrg
        }
        // 检测供应商对否被冻结
        if (frozen.toElsAccount && frozen.toElsAccount.length > 0 && frozen.orgCode && frozen.orgCode.length > 0) {
          postAction(this.url.isExistFrozen, frozen).then((rest) => {
            if (rest.success) {
              let { allData = {} } = args || {}
              let { busRule = {}, personFrom = {} } = allData || {}

              // pageData = { ...pageData, ...busRule, ...personFrom}
              delete allData.busRule
              delete allData.personFrom

              allData = Object.assign({}, allData, busRule, personFrom)
              let params = {
                businessId: allData.id,
                businessType: 'contract',
                auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractNoCode`, '合同编号')}：${allData.contractNumber} ${allData.contractName || ''}`,
                params: JSON.stringify(allData)
              }
              args = Object.assign({}, args, {
                allData: params
              })
              resolve(args)
            } else {
              this.$message.warning(rest.message)
              this.confirmLoading = false
              return
            }
          })
        } else {
          let { allData = {} } = args || {}
          let { busRule = {}, personFrom = {} } = allData || {}

          // pageData = { ...pageData, ...busRule, ...personFrom}
          delete allData.busRule
          delete allData.personFrom

          allData = Object.assign({}, allData, busRule, personFrom)
          let params = {
            businessId: allData.id,
            businessType: 'contract',
            auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractNoCode`, '合同编号')}：${allData.contractNumber} ${allData.contractName || ''}`,
            params: JSON.stringify(allData)
          }
          args = Object.assign({}, args, {
            allData: params
          })
          resolve(args)
        }
      })
    },

    showcAuditConditionBtn() {
      let params = this.getAllData()
      let auditStatus = params.auditStatus
      if (auditStatus != '0') {
        return false
      } else {
        return true
      }
    },
    auditPostData(invokeUrl) {
      this.$refs.editPage.confirmLoading = true
      let formData = this.getAllData()
      let param = {}
      param['businessId'] = formData.id
      param['rootProcessInstanceId'] = formData.flowId
      param['businessType'] = 'contract'
      param['auditSubject'] = '合同编号：' + formData.contractNumber + ' ' + formData.contractName || ''
      param['params'] = JSON.stringify(formData)
      postAction(invokeUrl, param, 'post')
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.$parent.submitCallBack(formData)
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.editPage.confirmLoading = false
        })
    },

    showFlow() {
      let params = this.getAllData()
      this.flowId = params.flowId
      this.flowView = true
    },
    closeFlowView() {
      this.flowView = false
    },
    handleCancel() {
      this.visible = false
    },
    fileCompareResult(Vue, row) {
      this.confirmLoading = true
      getAction('/compare/elsFileCompareHead/getResultById', { id: row.id })
        .then((res) => {
          if (res.success) {
            if (res.result.elsFileCompareResultList && res.result.elsFileCompareResultList.length == 1) {
              let filePath = res.result.elsFileCompareResultList[0].filePath
              this.$previewFile.open({ params: {}, path: filePath })
            } else {
              this.recordGridOptions.columns = [
                {
                  title: '比对时间',
                  fieldLabelI18nKey: 'i18n_field_lIKI_326a4423',
                  field: 'createTime',
                  helpText: ''
                },
                {
                  title: '文件类型',
                  fieldLabelI18nKey: 'i18n_field_fileType',
                  field: 'fbk2_dictText',
                  helpText: ''
                },
                {
                  title: '文件名称',
                  fieldLabelI18nKey: 'i18n_title_fileName',
                  field: 'fileName',
                  helpText: ''
                },
                {
                  title: '文件路径',
                  fieldLabelI18nKey: 'i18n_field_filePath',
                  field: 'filePath',
                  helpText: ''
                },
                {
                  title: '操作',
                  fieldLabelI18nKey: 'i18n_title_operation',
                  field: 'sourceType_dictText',
                  headerAlign: 'center',
                  slots: {
                    default: ({ row }) => {
                      let resultArray = []
                      resultArray.push(
                        <a
                          title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '预览')}
                          onClick={() => this.resultView(row)}
                        >
                          {' '}
                          {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '预览')}
                        </a>
                      )
                      resultArray.push(
                        <a
                          title={this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}
                          style='margin-left:8px'
                          onClick={() => this.resultDownload(row)}
                        >
                          {' '}
                          {this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}
                        </a>
                      )
                      return resultArray
                    }
                  }
                }
              ]
              this.recordGridOptions.data = res.result.elsFileCompareResultList
              this.fileCompareVisible = true
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
      /*this.$router.push({
                path: '/srm/compare/ElsFileCompareHeadList',
                query: {sourceId: row.id}
            })*/
    },
    resultView(row) {
      this.$previewFile.open({ params: {}, path: row.filePath })
    },
    resultDownload(row) {
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = row.filePath
      link.setAttribute('download', row.fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) //下载完成移除元素
      window.URL.revokeObjectURL(row.filePath) //释放掉blob对象
    },
    fileCompare(Vue, row) {
      this.fileRow = row
      this.selectType = 'file'
      let item = {
        selectModel: 'single',
        sourceUrl: '/attachment/purchaseAttachment/listFileCompare',
        params: {
          businessType: 'contract'
        },
        columns: [
          {
            field: 'businessType_dictText',
            fieldLabelI18nKey: 'i18n_field_businessType',
            title: '业务类型',
            with: 150
          },
          {
            field: 'fileType_dictText',
            fieldLabelI18nKey: 'i18n_field_fileType',
            title: '文件类型',
            with: 150
          },
          {
            field: 'uploadElsAccount',
            fieldLabelI18nKey: 'i18n_field_uploadElsAccount',
            title: '文件上传方账号',
            with: 150
          },
          {
            field: 'uploadSubAccount',
            fieldLabelI18nKey: 'i18n_title_fileUploadPartySubAccount',
            title: '文件上传方子账号',
            with: 150
          },
          {
            field: 'uploadTime',
            fieldLabelI18nKey: 'i18n_title_uploadTime',
            title: '上传时间',
            with: 150
          },
          {
            field: 'fileName',
            fieldLabelI18nKey: 'i18n_title_fileName',
            title: '文件名称',
            with: 150
          },
          {
            field: 'filePath',
            fieldLabelI18nKey: 'i18n_field_filePath',
            title: '文件路径',
            with: 150
          },
          {
            field: 'fileSize',
            fieldLabelI18nKey: 'i18n_field_fileSize',
            title: '文件大小',
            with: 150
          }
        ]
      }
      this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
    },
    compareFileTypes(file1, file2) {
      const getFileType = (filename) => {
        const fileExtension = filename.split('.').pop()
        return fileExtension
      }
      const type1 = getFileType(file1)
      const type2 = getFileType(file2)
      if ((type1 == 'doc' && type2 == 'docx') || (type1 == 'docx' && type2 == 'doc')) return true
      if ((type1 == 'xlsx' && type2 == 'xls') || (type1 == 'xls' && type2 == 'xlsx')) return true
      return type1 === type2
    },
    fieldSelectOk(data) {
      if (this.selectType == 'content') {
        let detailGrid = this.getItemGridRef('purchaseContractContentItemList')
        let tableData = detailGrid.getTableData().fullData
        let addTableData = []
        data.forEach((item, i) => {
          let lineNum = tableData.length + (i + 1)
          addTableData.push({
            itemNumber: lineNum,
            itemName: item.itemName,
            itemVersion: item.itemVersion,
            itemType: item.itemType,
            itemType_dictText: item.itemType_dictText,
            itemContent: item.itemContent,
            originalContent: item.itemContent,
            itemId: item.id,
            changeFlag: '0',
            sourceType: '2',
            sourceType_dictText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractLibrary`, '合同库')
          })
        })
        let conent = ''
        conent = data.map((item) => {
          return item.itemContent
        })
        this.businessTemplate = conent.join('')
        detailGrid.insertAt(addTableData, -1)
      } else if (this.selectType == 'file') {
        let flag = this.compareFileTypes(this.fileRow.fileName, data[0].fileName)
        if (!flag) return this.$message.warning('不同类型文件不做对比')
        this.confirmLoading = true
        let fileCompareRow = {
          fileASourceId: this.fileRow.id,
          fileABusinessType: this.fileRow.businessType,
          fileAType: this.fileRow.fileType,
          fileAUploadElsAccount: this.fileRow.uploadElsAccount,
          fileAUploadSubAccount: this.fileRow.uploadSubAccount,
          fileAUploadTime: this.fileRow.uploadTime,
          fileAName: this.fileRow.fileName,
          fileAPath: this.fileRow.filePath,
          fileASize: this.fileRow.fileSize,
          fileBSourceId: data[0].id,
          fileBBusinessType: data[0].businessType,
          fileBType: data[0].fileType,
          fileBUploadElsAccount: data[0].uploadElsAccount,
          fileBUploadSubAccount: data[0].uploadSubAccount,
          fileBUploadTime: data[0].uploadTime,
          fileBName: data[0].fileName,
          fileBPath: data[0].filePath,
          fileBSize: data[0].fileSize
        }
        postAction('/compare/elsFileCompareHead/fileCompare', fileCompareRow)
          .then((res) => {
            if (res.success) {
              this.$message.success(res.message)
            } else {
              this.$message.warning(res.message)
            }
          })
          .finally(() => {
            this.confirmLoading = false
          })
      }
    },
    downloadEvent(Vue, row) {
      if (!row.fileName) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownloadi18n_title_lineNotDownload`, '该行没有可下载的附件'))
        return
      }
      const id = row.id
      const fileName = row.fileName
      const params = {
        id
      }
      getAction(this.url.download, params, {
        responseType: 'blob'
      }).then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    },
    previewPdf() {
      if (!this.currentEditRow.id) return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
      let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
      if (!contentGrid.getTableData().tableData.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
        return
      }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_NRcrRWFVWsMSUBRsUBWFIR_b041fe37`, '如果修改过数据，请先保存后预览，确保预览数据一致'),
        onOk: () => {
          this.confirmLoading = true
          axios({
            url: '/contract/purchaseContractHead/download',
            responseType: 'blob',
            params: { id: this.currentEditRow.id }
          })
            .then((res) => {
              if (res) {
                let url = window.URL.createObjectURL(new Blob([res], { type: 'application/pdf' }))
                window.open(url)
              } else {
                this.$message.warning(res.message)
              }
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      })
    },
    downloadFile() {
      let { id, purchaseContractContentItemList } = this.paramIntegrate()
      if (!id) return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
      if (!purchaseContractContentItemList.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
        return
      }
      let params = { id }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_NRcrRWFVWsMSIKRsIKWFIR_96f416f7`, '如果修改过数据，请先保存后下载，确保下载数据一致'),
        onOk: () => {
          this.confirmLoading = true
          axios({
            url: '/contract/purchaseContractHead/download',
            responseType: 'blob',
            params: params
          }).then((res) => {
            this.confirmLoading = false
            let fieldName = this.currentEditRow.contractNumber + '_' + this.currentEditRow.contractVersion + '.pdf'
            //console.log(res
            const blob = new Blob([res])
            const blobUrl = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.style.display = 'none'
            a.href = blobUrl
            a.download = fieldName
            a.click()
          })
        }
      })
    }
  }
}
</script>
