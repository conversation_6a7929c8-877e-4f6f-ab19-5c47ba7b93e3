<template>
  <div class="PageNoticeList">
    <div class="content">
      <div class="info">
        <h1 class="title">{{ info.noticeTitle }}</h1>
        <div class="user">
          <span class="item">
            <span class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_title_creator`, '创建者') }}</span>
            <span class="val">{{ info.publishUser }}</span>
          </span>
          <span class="item">
            <span class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_field_publishTime`, '发布时间') }}</span>
            <span class="val">{{ info.publishTime }}</span>
          </span>
        </div>
        <div
          class="richContent"
          v-html="info.noticeContent"
        >
        </div>
        <div class="download">
          <div class="line"></div>
          <a
            class="attachment"
            href="javascript: void(0)"
          >{{ $srmI18n(`${$getLangAccount()}#i18n_title_downloadAttachment`, '附件下载') }}</a>
          <div class="file">
            <ul>
              <template v-for="el in purchaseAttachmentList">
                <li :key="el.id">
                  <a
                    href="javascript:void(0)"
                    class="link"
                    @click="downloadFile(el)"
                  >
                    {{ el.fileName }}
                  </a>
                </li>
              </template>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import { axios } from '@/utils/request'
// import { ACCESS_TOKEN } from '@/store/mutation-types'

export default {
    data () {
        return {
            id: '',
            info: {},
            purchaseAttachmentList: [],
            url: {
                download: '/attachment/purchaseAttachment/download'
            }
        }
    },
    methods: {
        getDetailData () {
            const url = '/notice/purchaseNotice/noToken/queryById'
            const params = {
                id: this.id
            }
            getAction(url, params).then(res => {
                const { purchaseAttachmentList = [], ...others } = res.result || {}
                this.purchaseAttachmentList = purchaseAttachmentList
                this.info = { ...others }
            })
        },
        downloadFile (row) {
            let params = {
                id: row.id
                // 'X-Access-Token': this.$ls.get(ACCESS_TOKEN || '')
            }
            axios({
                url: this.url.download,
                responseType: 'blob',
                params: params
            }).then(res => {
                console.log(res)
                const blob = new Blob([res])
                const blobUrl = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.style.display = 'none'
                a.href = blobUrl
                a.download = row.fileName
                a.click()
            })
        }
    },
    watch: {
        $route: {
            immediate: true,
            handler ({ path, query }) {
                if (path !== '/pageNotice/detail') return
                const { id = '' } = query
                this.id = id
                this.getDetailData()
            }
        }
    }
}
</script>

<style lang="less" scoped>
@blue: #178aff;
@darkBlue: #3287ff;
.PageNoticeList {
	display: flex;
	flex-direction: column;
	margin: 20px 20px 0 20px;
	min-height: calc(100vh - 56px);
	background: #fff;
	.content {
		flex: 1;
	}
	.info {
		margin-top: 16px;
		padding: 26px 68px 120px;
	}
	.title {
		text-align: center;
		font-size: 22px;
		color: #333;
	}
	.user {
		margin-top: 22px;
		text-align: center;
		color: @blue;
		.item {
			position: relative;
			padding: 0 28px;
			+ .item {
				border-left: 1px solid @blue;
			}
		}
		.label {
			&::after {
				content: ":";
			}
		}
		.val {
			padding-left: 10px;
		}
	}
	.richContent {
		overflow: hidden;
		margin-top: 36px;
		max-width: 100%;
	}
	.download {
		.line {
			margin: 22px 0;
			border-top: 1px dotted #e6e6e6;
		}
		.attachment {
			font-weight: 700;
			font-size: 16px;
			color: @darkBlue;
		}
	}
	.file {
		.link {
			color: @blue;
		}
	}
}
</style>
