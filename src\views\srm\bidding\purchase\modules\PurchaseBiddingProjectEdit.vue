<template>
  <div class="PurchaseBiddingProjectHead business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        pageStatus="edit"
        modelLayout="masterSlave"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        v-on="businessHandler"
      />
    </a-spin>

    <field-select-modal
      isEmit
      ref="fieldSelectModal"
      @ok="fieldSelectOk"
    />

    <ItemImportExcel
      ref="itemImportExcel"
      @importCallBack="importCallBack"
    />

    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"
    />
  </div>
</template>

<script lang="jsx">
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
import flowViewModal from '@comp/flowView/flowView'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { downFile, getAction, postAction } from '@/api/manage'
import { TOOLBAR_BUTTON_ADD, TOOLBAR_BUTTON_DELETE, TOOLBAR_BUTTON_UPLOAD, BUTTON_SAVE, BUTTON_PUBLISH, BUTTON_SUBMIT, BUTTON_BACK, GRID_OPTION_ROW, ATTACHED_DEMAND_LIST_GROUP, ATTACHMENT_DEMAND_COLUMNS, ATTACHMENT_GROUP, ATTACHMENT_COLUMNS_WITHOUT_OPRATION } from '@/utils/constant.js'
import { getGroupDefaultData, createPromise } from '@/utils/util'

export default {
  name: 'PurchaseBiddingProjectEdit',
  mixins: [businessUtilMixin],
  components: {
    flowViewModal,
    BusinessLayout,
    fieldSelectModal,
    ItemImportExcel
  },
  props: {
    currentEditRow: {
      required: true,
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      selectType: 'material',
      flowView: false,
      flowId: null,
      requestData: {
        detail: {
          url: '/bidding/purchaseBiddingProjectHead/queryById',
          args: () => ({ id: this.currentEditRow.id })
        }
      },
      externalToolBar: {
        purchaseBiddingHeadList: [
          TOOLBAR_BUTTON_ADD,
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
            key: 'gridDelete',
            authorityCode: 'bidding#purchaseBiddingProjectHead:attachmentsDelete'
          }
        ],
        purchaseBiddingItemList: [
          {
            ...TOOLBAR_BUTTON_ADD,
            click: this.addBiddingItem,
            authorityCode: 'bidding#purchaseBiddingProjectHead:add'
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
            key: 'gridDelete',
            authorityCode: 'bidding#purchaseBiddingProjectHead:attachmentsDelete'
          },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
            params: this.getImportParams,
            click: this.importExcel,
            authorityCode: 'bidding#purchaseProjectBiddingHead:importExcel'
          }
        ],
        purchaseBiddingSpecialistList: [
          {
            ...TOOLBAR_BUTTON_ADD,
            click: this.addSpecialistEvent,
            authorityCode: 'bidding#purchaseBiddingProjectHead:add'
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
            key: 'gridDelete',
            authorityCode: 'bidding#purchaseBiddingProjectHead:attachmentsDelete'
          }
        ],
        biddingSupplierList: [
          {
            ...TOOLBAR_BUTTON_ADD,
            click: this.addSupplierEvent,
            authorityCode: 'bidding#purchaseBiddingProjectHead:add'
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
            key: 'gridDelete',
            authorityCode: 'bidding#purchaseBiddingProjectHead:attachmentsDelete'
          }
        ],
        purchaseAttachmentDemandList: [
          {
            ...TOOLBAR_BUTTON_UPLOAD,
            args: {
              modalVisible: false, // 必传
              businessType: 'bidding', // 必传
              property: 'materialNumber', // 可省略
              itemNumberKey: 'itemNumber',
              refName: 'purchaseAttachmentDemandList' // 指定 itemInfo 的 ref 名称
            },
            attr: this.attrHandle
          }
          // TOOLBAR_BUTTON_DELETE,
        ],
        purchaseAttachmentList: [
          {
            ...TOOLBAR_BUTTON_UPLOAD,
            args: {
              modalVisible: false, // 必传
              businessType: 'contract', // 必传
              itemNumberKey: 'materialNumber'
            }
          }
          // TOOLBAR_BUTTON_DELETE,
        ]
      },
      pageHeaderButtons: [
        {
          ...BUTTON_SAVE,
          args: {
            url: '/bidding/purchaseBiddingProjectHead/edit'
          },
          authorityCode: 'bidding#purchaseBiddingProjectHead:save',
          handleBefore: this.handleSaveBefore
        },
        {
          ...BUTTON_PUBLISH,
          args: {
            url: '/bidding/purchaseBiddingProjectHead/publish'
          },
          click: this.handleCustomPublish,
          authorityCode: 'bidding#purchaseBiddingProjectHead:publish'
        },
        {
          ...BUTTON_SUBMIT,
          args: {
            url: '/a1bpmn/audit/api/submit'
          },
          handleBefore: this.handleBeforeSubmit,
          click: this.handleCustomSubmit,
          authorityCode: 'bidding#purchaseBiddingProjectHead:submit'
        },
        BUTTON_BACK
      ],
      url: {
        add: '/bidding/purchaseBiddingProjectHead/add',
        edit: '/bidding/purchaseBiddingProjectHead/edit',
        detail: '/bidding/purchaseBiddingProjectHead/queryById',
        public: '/bidding/purchaseBiddingProjectHead/publish',
        isExistFrozenSource: 'supplier/supplierMaster/isExistFrozenSource',
        materialBidding: '/inquiry/searchSource/materialBidding',
        submit: '/a1bpmn/audit/api/submit',
        upload: '/attachment/purchaseAttachment/upload',
        import: '/els/base/excelByConfig/importExcel',
        downloadTemplate: '/base/excelByConfig/downloadTemplate',
        checkEnquirySameMaterial: '/bidding/purchaseBiddingProjectHead/checkEnquirySameMaterial'
      }
    }
  },
  computed: {
    remoteJsFilePath() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
      return `${account}/purchase_biddingProject_${templateNumber}_${templateVersion}`
    },
    // 实时获取分包明细表行数据
    purchaseBiddingHeadList() {
      let rows = []
      let grid = this.getItemGridRef('purchaseBiddingHeadList')
      if (grid) {
        rows = grid.getTableData().tableData || []
      }
      return rows
    }
  },
  methods: {
    formatPageData(item) {
        console.log('請求接口後格式化页面數據', item)
        if (item.budgetAmount === 0 || Math.abs(item.budgetAmount) > 0) {
            item.budgetAmount = Number(item.budgetAmount).toFixed(2)
        }
        if (item.economyAmount === 0 || Math.abs(item.economyAmount) > 0) {
            item.economyAmount = Number(item.economyAmount).toFixed(2)
        }
        return item;
    },
    formatTableData(data) {
        console.log('請求接口後格式化列表數據', data)
        let purchaseBiddingItemList = data.purchaseBiddingItemList || [];
        purchaseBiddingItemList = purchaseBiddingItemList.map((item) => {
            if (item.price === 0 || Math.abs(item.price) > 0) {
                item.price = Number(item.price).toFixed(6)
            }
            if (item.netPrice === 0 || Math.abs(item.netPrice) > 0) {
                item.netPrice = Number(item.netPrice).toFixed(6)
            }
            if (item.targetPrice === 0 || Math.abs(item.targetPrice) > 0) {
                item.targetPrice = Number(item.targetPrice).toFixed(6)
            }
            return item
        })
        data.purchaseBiddingItemList = purchaseBiddingItemList;

        let purchaseBiddingHeadList = data.purchaseBiddingHeadList || [];;
        purchaseBiddingHeadList = purchaseBiddingHeadList.map((item) => {
            if (item.budgetAmount === 0 || Math.abs(item.budgetAmount) > 0) {
                item.budgetAmount = Number(item.budgetAmount).toFixed(2)
            }
            
            return item
        })
        data.purchaseBiddingHeadList = purchaseBiddingHeadList;
        return data
    },
    handleSaveBefore(args) {
      let { allData } = args
      return new Promise((resolve, reject) => {
        // 逻辑判断
        if (allData.purchaseBiddingHeadList.length == 0) {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsVHRRPjIT_76572b74`, '分包信息至少要有一条'))
          reject(args)
        } else {
          resolve(args)
        }
      })
    },
    attrHandle() {
      return {
        sourceNumber: this.currentEditRow.projectNumber,
        actionRoutePath: '/srm/bidding/purchase/PurchaseBiddingProjectHeadList,/srm/bidding/sale/SaleBiddingProjectHeadList'
      }
    },
    preViewEvent(row) {
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    downloadTemplate() {
      const form = this.getAllData() || {}
      let params = { id: form.id, templateAccount: form.templateAccount, templateNumber: form.templateNumber, templateVersion: form.templateVersion, handlerName: 'purchaseBiddingItemExcelRpcServiceImpl', roleCode: 'purchase', groupCode: 'purchaseBiddingItemList', excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息') }
      if (!params.id) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsMWFWKIKIr_de818fdb`, '请先保存数据，再下载模板！'))
        return
      } else {
        downFile(this.url.downloadTemplate, params)
          .then((data) => {
            if (!data) {
              this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
              return
            }
            if (typeof window.navigator.msSaveBlob !== 'undefined') {
              window.navigator.msSaveBlob(new Blob([data]), 'template.xlsx')
            } else {
              let url = window.URL.createObjectURL(new Blob([data]))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_hucuNIr_20a4b03e`, '询价行导入模板') + '.xlsx')
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) //下载完成移除元素
              window.URL.revokeObjectURL(url) //释放掉blob对象
            }
          })
          .finally(() => {
            this.gridLoading = false
          })
      }
    },
    getImportParams() {
      let { id = '', templateAccount = '', templateNumber = '', templateVersion = '' } = this.getAllData() || {}
      return {
        id,
        templateAccount,
        templateNumber,
        templateVersion,
        handlerName: 'purchaseBiddingItemExcelRpcServiceImpl',
        roleCode: 'purchase',
        groupCode: 'purchaseBiddingItemList',
        excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息')
      }
    },
    importExcel() {
      let params = this.getImportParams()
      this.$refs.itemImportExcel.open(params, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'), 'purchaseBiddingItemList')
    },
    importCallBack(result) {
      if (result.file.status === 'done') {
        let response = result.file.response
        debugger
        if (response.success) {
          let pageConfig = this.$refs[this.businessRefName].pageConfig || {}
          // 获取页面所有配置默认值
          let result = getGroupDefaultData(pageConfig, true)
          let defaultObj = result['purchaseBiddingItemList'] || {}
          let itemGrid = this.getItemGridRef('purchaseBiddingItemList')
          let insertData = response.result.dataList
          let inserDataNew = insertData.map((row) => ({ ...defaultObj, ...row }))
          itemGrid.insertAt(inserDataNew, -1)
        } else {
          this.$message.warning(response.message)
        }
      }
    },
    handleBeforeRemoteConfigData(pageConfig) {
      // 设置自定义插槽
      this.setFieldTypeSelect(pageConfig)

      return {
        groups: [
          // 附件需求清单
          ATTACHED_DEMAND_LIST_GROUP,
          // 附件
          ATTACHMENT_GROUP
        ],
        itemColumns: [
          ...ATTACHMENT_DEMAND_COLUMNS({ groupCode: 'purchaseAttachmentDemandList' }),
          {
            ...GRID_OPTION_ROW,
            groupCode: 'purchaseAttachmentDemandList',
            width: 140,
            slots: {
              default: ({ row, rowIndex, column, columnIndex }) => {
                return [
                  <span>
                    <a-button
                      type='link'
                      style='padding: 0 4px;'
                      onClick={() => this.attachmentDownload(row)}
                    >
                      {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载')}
                    </a-button>
                    <a-button
                      type='link'
                      style='padding: 0 4px;'
                      onClick={() => this.attachmentPreview(row)}
                    >
                      {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览')}
                    </a-button>
                    <a-button
                      type='link'
                      style='padding: 0 4px;'
                      onClick={() => this.attachmentDelete(row, { groupCode: 'purchaseAttachmentDemandList' })}
                    >
                      {this.$srmI18n(`${this.$getLangAccount(row)}#i18n_title_delete`, '删除')}
                    </a-button>
                  </span>
                ]
              }
            }
          },
          ...ATTACHMENT_COLUMNS_WITHOUT_OPRATION({ groupCode: 'purchaseAttachmentList' }),
          {
            ...GRID_OPTION_ROW,
            groupCode: 'purchaseAttachmentList',
            width: 140,
            slots: {
              default: ({ row, rowIndex, column, columnIndex }) => {
                return [
                  <span>
                    <a-button
                      type='link'
                      style='padding: 0 4px;'
                      onClick={() => this.attachmentDownload(row)}
                    >
                      {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载')}
                    </a-button>
                    <a-button
                      type='link'
                      style='padding: 0 4px;'
                      onClick={() => this.attachmentPreview(row)}
                    >
                      {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览')}
                    </a-button>
                    <a-button
                      type='link'
                      style='padding: 0 4px;'
                      onClick={() => this.attachmentDelete(row, { groupCode: 'purchaseAttachmentList' })}
                    >
                      {this.$srmI18n(`${this.$getLangAccount(row)}#i18n_title_delete`, '删除')}
                    </a-button>
                  </span>
                ]
              }
            }
          }
        ]
      }
    },
    handleAfterDealSource(pageConfig, resultData) {
      this.hideSingleGroup(this.businessRefName, 'biddingSupplierList', true)
      this.hideSingleGroup(this.businessRefName, 'purchaseAttachmentDemandList', true)
      this.hideSingleGroup(this.businessRefName, 'purchaseAttachmentList', true)
    },
    // 设置分包明细 + 参与人员 表行数据的 分包行号列自定义插槽
    setFieldTypeSelect(pageConfig) {
      let itemColumns = pageConfig.itemColumns || []
      let groupCodes = [
        'purchaseBiddingItemList' // 分包明细
      ]

      itemColumns.forEach((sub) => {
        if (groupCodes.includes(sub.groupCode) && sub.field === 'headItem') {
          sub.slots = Object.assign({}, sub.slots, {
            default: ({ row, column }, h) => {
              return [<span>{row[column.property]}</span>]
            },
            edit: ({ row, column }, h) => {
              const opts = this.purchaseBiddingHeadList.map((n, idx) => (
                <vxe-option
                  value={idx + 1}
                  label={n.biddingDesc}
                ></vxe-option>
              ))

              const props = {
                value: row[column.property]
              }

              const on = {
                change: ({ value }) => {
                  row.headItem = value
                  let { biddingDesc = '' } = this.purchaseBiddingHeadList[value - 1] || {}
                  row.headName = biddingDesc
                }
              }

              return [<vxe-select {...{ props, on }}>{opts}</vxe-select>]
            }
          })
        }
      })
    },
    addBiddingItem({ pageConfig, groupCode }) {
      this.selectType = 'material'
      const form = this.getAllData()
      const { mustMaterialNumber = '1' } = form
      if (mustMaterialNumber == '1') {
        let url = '/material/purchaseMaterialHead/list'
        let columns = [
          {
            field: 'cateCode',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
            width: 150
          },
          {
            field: 'cateName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
            width: 150
          },
          { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150 },
          { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
          { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200 },
          { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200 }
        ]
        this.$refs.fieldSelectModal.open(url, { blocDel: '0', freeze: '0' }, columns, 'multiple')
      } else {
        console.log('pageConfig :>> ', pageConfig)
        this.businessGridAdd({ pageConfig, groupCode })
      }
    },
    fieldSelectOk(data) {
      console.log(data)
      let pageConfig = this.$refs[this.businessRefName].pageConfig || {}
      // 获取页面所有配置默认值
      let result = getGroupDefaultData(pageConfig, true)
      if (this.selectType == 'material') {
        let defaultObj = result['purchaseBiddingItemList'] || {}
        let itemGrid = this.getItemGridRef('purchaseBiddingItemList')
        let { fullData } = itemGrid.getTableData()
        let materialList = fullData.map((item) => {
          return item.materialNumber
        })
        //过滤已有数据
        /*let insertData = data.filter(item => {
                    return !materialList.includes(item.materialNumber)
                })*/
        let inserDataNew = data //data.map((row) => ({ ...defaultObj, ...row }))
        inserDataNew.forEach((insert) => {
          insert['materialId'] = insert['id']
          insert.quantityUnit = insert.baseUnit
          if (!!insert.requireQuantity) {
            insert.secondaryQuantity = insert.requireQuantity / (insert.conversionRate || 1)
          } else {
            insert.secondaryQuantity = 0
          }
          insert.secondaryQuantity = insert.secondaryQuantity.toFixed(6)
        })
        let param = {
          purOrgCode: this.getAllData().purchaseOrg,
          materialDataVos: inserDataNew
        }
        postAction(this.url.materialBidding, param).then((res) => {
          if (res.success) {
            itemGrid.insertAt(inserDataNew, -1)
          } else {
            this.$confirm({
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLSu_38d85f7b`, '确认添加'),
              content: res.message,
              onOk: function () {
                itemGrid.insertAt(inserDataNew, -1)
              }
            })
          }
        })
        //itemGrid.insertAt(insertData, -1)
      } else if (this.selectType == 'supplier') {
        let defaultObj = result['biddingSupplierList'] || {}
        let supplierGrid = this.getItemGridRef('biddingSupplierList')
        let { fullData } = supplierGrid.getTableData()
        let supplierList = fullData.map((item) => {
          return item.toElsAccount
        })
        // 过滤已有数据
        let insertData = data.filter((item) => {
          return !supplierList.includes(item.toElsAccount)
        })
        insertData = insertData.map((item) => {
          return {
            toElsAccount: item.toElsAccount,
            supplierCode: item.supplierCode,
            supplierName: item.supplierName,
            supplierStatus_dictText: item.supplierStatus_dictText
          }
        })
        supplierGrid.insertAt(insertData, -1)
      } else if (this.selectType == 'specialist') {
        let defaultObj = result['purchaseBiddingSpecialistList'] || {}
        let specialistGrid = this.getItemGridRef('purchaseBiddingSpecialistList')
        let { fullData } = specialistGrid.getTableData()
        let specialistList = fullData.map((item) => {
          return item.subAccount
        })
        // 过滤已有数据
        let insertData = defaultObj.filter((item) => {
          return !specialistList.includes(item.subAccount)
        })
        insertData = insertData.map((item) => {
          return {
            subAccount: item.subAccount,
            name: item.name,
            mobileTelephone: item.mobileTelephone,
            specialistClasses_dictText: item.specialistClasses_dictText,
            specialistClasses: item.specialistClasses
          }
        })
        specialistGrid.insertAt(insertData, -1)
      } else if (this.selectType == 'member') {
        let defaultObj = result['purchaseBiddingSpecialistList'] || {}
        let gridRef = this.getItemGridRef('purchaseBiddingSpecialistList')
        const { fullData = [] } = gridRef.getTableData() || {}
        let list = fullData.map((item) => {
          return item.subAccount
        })
        // 过滤已有数据
        let insertData = data
          .filter((item) => {
            return !list.includes(item.subAccount)
          })
          .map(({ subAccount, realname, phone, email }) => ({
            subAccount,
            name: realname,
            mobileTelephone: phone,
            email,
            memberType: '2'
          }))
        gridRef.insertAt(insertData, -1)
      }
    },
    addSupplierEvent() {
      this.selectType = 'supplier'
      let url = '/supplier/supplierMaster/list'
      let columns = [
        { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150 },
        { field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150 },
        { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200 },
        { field: 'supplierStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'), width: 200 },
        { field: 'cateName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'), width: 200 }
      ]
      // 获取供应商范围参数
      const form = this.getAllData()
      // 供应商冻结功能检查
      if (form.purchaseBiddingItemList && form.purchaseBiddingItemList.length > 0) {
        // 设置供应商信息和询价行信息
        const array = new Array()
        form.purchaseBiddingItemList.forEach((a) => {
          array.push(a.factory + ':' + a.cateCode)
        })
        form.purchaseOrgItemList = array.join(',')
      } else {
        form.purchaseOrgItemList = ''
      }
      const { supplierScope = '', purchaseOrgItemList = '' } = form
      this.$refs.fieldSelectModal.open(url, { supplierStatus: supplierScope, frozenFunctionValue: '2', purchaseOrgItemList: purchaseOrgItemList }, columns, 'multiple')
    },
    addSpecialistEvent() {
      this.selectType = 'member'
      let url = '/specialist/specialistInfo/list'
      let columns = [
        { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistName`, '专家姓名'), width: 150 },
        { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistAccount`, '专家账号'), width: 150 },
        { field: 'specialistClasses_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistType`, '专家类型'), width: 200 },
        { field: 'mobileTelephone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expertPhone`, '专家电话'), width: 200 }
      ]
      if ((this.selectType = 'member')) {
        url = '/account/elsSubAccount/list'
        columns = [
          { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), with: 150 },
          { field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), with: 150 }
        ]
      }
      // 获取供应商范围参数
      //const form = this.$refs.editPage.getPageData()
      //const { supplierScope = '' } = form
      this.$refs.fieldSelectModal.open(url, {}, columns, 'multiple')
    },
    addFileDemand() {
      let demandGrid = this.getItemGridRef('purchaseAttachmentDemandList')
      demandGrid.insertAt({}, -1)
    },
    deleteFilesEvent() {
      const fileGrid = this.getItemGridRef('purchaseAttachmentList')
      const checkboxRecords = fileGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      const ids = checkboxRecords.map((n) => n.id).join(',')
      const params = {
        ids
      }
      getAction('/attachment/purchaseAttachment/deleteBatch', params).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.removeCheckboxRow()
      })
    },
    handleBeforeSubmit(args) {
      const { allData = {} } = args || {}
      // 格式化提交审批数据逻辑
      // 注: 需要返回一个 promise 对象
      let fn = (resolve, reject) => {
        let formatData = {
          businessId: allData.id || '',
          businessType: 'biddingProject',
          auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YBthxUzAy_eee84974`, '招标单发布审批编号')}：${allData.projectNumber || ''} ${allData.projectName || ''}`,
          params: JSON.stringify(allData)
        }

        resolve({
          ...args,
          allData: formatData
        })
      }

      return createPromise(fn)
    },
    // 校验分包名称、分包行号是否正确
    // 存在情况：分包明细表行上已选第三个分包行号，但手动去删除分包信息表行数据行为
    // 需要在发布或提交审批时手动校验，以确保正确性
    checkHeadItem() {
      const allData = this.getAllData() || {}
      let {
        purchaseBiddingHeadList = [], // 分包信息
        purchaseBiddingItemList = [], // 分包明细
        purchaseBiddingSpecialistList = [], // 参与人员
        budgetAmount = 0 //预算金额
      } = allData

      // if(Number(budgetAmount) <=0 ){
      //     this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_UdHfTPfUQ_1f573b0f`, '预算金额需要大于零'))
      //     return false
      // }

      if (!purchaseBiddingHeadList.length) {
        let tip = [this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zsVHBc_af4ff051`, '分包信息表行'), this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_empty_addLine`, '请添加行项目')]
        this.$message.error(tip.join(''))
        return false
      }

      if (!purchaseBiddingItemList.length) {
        let tip = [this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zsRHBc_b9d6739b`, '分包明细表行'), this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_empty_addLine`, '请添加行项目')]
        this.$message.error(tip.join(''))
        return false
      }

      let biddingDescs = this.purchaseBiddingHeadList.map((n) => n.biddingDesc)
      console.log('biddingDescs :>> ', biddingDescs)

      for (let [idx, item] of purchaseBiddingItemList.entries()) {
        debugger
        if (!biddingDescs.includes(item.headName)) {
          let tip = [this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zsRHBc_b9d6739b`, '分包明细表行'), ` ${idx + 1} `, this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zsRLxiR_8031fb76`, '分包名称不正确'), this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VVViF_f832453a`, '请重新选择')]
          this.$message.error(tip.join(''))
          return false
        }
      }

      // for (let [idx, item] of purchaseBiddingSpecialistList.entries()) {
      //     if (!biddingDescs.includes(item.headName)) {
      //         let tip = [
      //             this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_sULjBc_5e4d76ce`, '参与人员表行'),
      //             ` ${idx + 1} `,
      //             this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zsRLxiR_8031fb76`, '分包名称不正确'),
      //             this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VVViF_f832453a`, '请重新选择'),
      //         ]
      //         this.$message.error(tip.join(''))
      //         return false
      //     }
      // }

      return true
    },
    // 自定义发布
    handleCustomPublish(args) {
      // 分包信息自定义校验
      let flag = this.checkHeadItem()
      if (!flag) {
        return
      }
      // 此处手动触发校验方法
      this.composeBusinessPublish(args)
    },
    // 自定义提交审批
    handleCustomSubmit(args) {
      // 分包信息自定义校验
      let flag = this.checkHeadItem()
      if (!flag) {
        return
      }

      // 获取页面所有数据
      const allData = this.getAllData() || {}
      let { supplierTaxRate = '', biddingType = '', participateQuantity = '', purchaseBiddingItemList = [], biddingSupplierList = [] } = allData

      // 校验供应商税率
      if (supplierTaxRate === '1') {
        let flag = purchaseBiddingItemList.some((n) => !n.taxCode)
        if (flag) {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierTaxRateTips`, '供应商税率不能为空'))
          return
        }
      }

      if (biddingType === '0' && participateQuantity) {
        if (participateQuantity > biddingSupplierList.length) {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cantBeLessOfParticipants`, '邀请供应商数量不能小于参与数量！'))
          return
        }
      }
      // 默认保存方法不会校验当前业务模板必填字段
      // 此处手动触发校验方法

      postAction(this.url.checkEnquirySameMaterial, allData.purchaseBiddingItemList).then((rest) => {
        if (rest.success) {
          // 提交
          this.composeBusinessSubmit(args)
        } else {
          this.$message.warning(rest.message)
        }
      })
    }
  }
}
</script>
