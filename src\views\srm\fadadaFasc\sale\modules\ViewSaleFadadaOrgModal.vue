<template>
  <div class="SaleFadadaOrg business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="detail"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />
  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { BUTTON_BACK } from '@/utils/constant.js'

export default {
    name: 'DetailSaleFadadaOrgModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/saleFadadaOrg/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            url: {
                detail: '/contract/purchaseContractHead/queryById'
            }
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEsNo`, '采购ELS号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'busAccount'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRRL_4460e249`, '采购名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'companyName'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQuKVRtRAB_8bd82932`, '是否加载组织机构列表'),
                        fieldLabelI18nKey: '',
                        fieldName: 'loadingOrg',
                        dictCode: 'yn'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpName',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRoo_307b3288`, '机构代码'),
                        fieldLabelI18nKey: '',
                        fieldName: 'orgCode'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQlbUzVHvAPdj_f982edd5`, '是否授权身份信息给当前应用'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authorizeUserInfo_dictText'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIAc_e6376152`, '组织机构证件类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'orgCardType',
                        dictCode: 'orgIdCardType',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIy_76cf8d5`, '组织机构证件号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpIdentNo',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEVRAc_58b0c8ec`, '企业组织类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpIdentType',
                        required: '1',
                        dictCode: 'fadadaCorpIdentType'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hLcR_32aba3df`, '法人姓名'),
                        fieldLabelI18nKey: '',
                        fieldName: 'legalRepName',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '设置页面中默认选择的实名认证方式'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpIdentMethod',
                        dictCode: 'fadadaCorpIdentMethod'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'multiple',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xqcrjAEVH_fe52d43e`, '不可修改的企业信息'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpNonEditableInfo',
                        dictCode: 'fadadaCorpNonEditableInfo'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'multiple',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESVVjAElbvLAB_dbd4e688`, '业务请求的企业授权范围列表'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authScopes',
                        dictCode: 'fadadaCropAuthScopes',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLey_21bdb3bc`, '经办人账号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'accountName',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLSRMID_945a535e`, '经办人ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'clientUserId',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLcR_21b77cc5`, '经办人姓名'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userName',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIAc_a87a0df0`, '经办人证件类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userIdentType',
                        dictCode: 'fadadaUserIdentType',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIy_15f34077`, '经办人证件号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userIdentNo',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLlty_155a8cbd`, '经办人手机号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'mobile',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLWEmy_ad7bbab7`, '经办人银行卡号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'bankAccountNo',
                        disabled: true
                    },
                    // {
                    //     groupCode: 'baseForm',
                    //     fieldType: 'multiple',
                    //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ELsqiFjmLLiCK_15d6ca58`, '页面中可选择的个人认证方式'),
                    //     fieldLabelI18nKey: '',
                    //     fieldName: 'oprIdentMethod',
                    //     dictCode: 'fadadaUserIdentMethod',
                    //     disabled: true
                    // },
                    // {
                    //     groupCode: 'baseForm',
                    //     fieldType: 'multiple',
                    //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xqcrjOrLVH_6fb6d522`, '不可修改的经办人信息'),
                    //     fieldLabelI18nKey: '',
                    //     fieldName: 'oprNonEditableInfo',
                    //     dictCode: 'fadadaUserNonEditableInfo',
                    //     disabled: true
                    // },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRKRLizE_43405a21`, '机构实名认证状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpIdentProcessStatus',
                        dictCode: 'fadadaIdentStatus',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbyR_2ed1f524`, '授权结果'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authResult',
                        dictCode: 'fadadaAuthStatus',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tReyID_1a3ee6f6`, 'SRM机构账号ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'clientCorpId',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hfftReyID_8b97abab`, '法大大机构账号ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'openCorpId',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbLiKy_c896ceff`, '授权认证链接'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authUrl',
                        extend: {
                            linkConfig: {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                titleI18nKey: 'i18n_title_authenticationLink'
                            },
                            exLink: true
                        }
                    }
                ]
            }
        }
    }
}
</script>
