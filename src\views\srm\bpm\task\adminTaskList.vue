<template>
  <div
    :style="pageStyle"
    ref="page">
    <!-- 列表界面 -->
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showAuditDetail"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url"
      :tabsList="tabsList"
      :tabCurrent="tabCurrent"
      @afterChangeTab="handleAfterChangeTab"
    >

      <!-- 覆盖模板tab内容 -->
      <template
        #tabs
        v-if="toggleCardMode">
        <div></div>
      </template>

      <template
        #query_extra>
        <a-tooltip
          :title="toggleCardMode ? $srmI18n(`${$getLangAccount()}#i18n_alert_ABCK_275e29ff`, '列表模式') : $srmI18n(`${$getLangAccount()}#i18n_alert_mOCK_27a11a94`, '卡片模式')">
          <a-button
            :style="{ 'marginLeft': toggleCardMode ? '0' : '10px'}"
            @click="handleToggleCardMode"><icon-font :type="toggleCardMode ? 'icon-list' : 'icon-card'" /></a-button>
        </a-tooltip>
      </template>

      <template
        #grid="{ activeKey, tabsList, columns, tableData, loadDataFunc, pagerConfig, getNewRouterFunc, loading }"
        v-if="toggleCardMode">
        <a-spin
          :spinning="loading"
          :delayTime="300">
          <card-style-list-layout
            :activeKey="activeKey"
            :tabsList="tabsList"
            :columns="columns"
            :tableData="tableData"
            :pagerConfig="pagerConfig"
            :buttons="pageData.optColumnList"
            @sidebar-click="(payload) => handleSidebarClick(payload, pagerConfig, loadDataFunc)"
            @card-approve="({ row }) => showDetail(row)"
            @card-chat="({ row }) => handleChat(row)"
            @card-link="({ col, column }) => handleExtendLink(col, column, getNewRouterFunc)"
            @page-change="(payload) => handlePageChange(payload, pagerConfig, loadDataFunc)"
          />
        </a-spin>
      </template>

    </list-layout>

    <audit-detail-controller
      :current-edit-row="currentEditRow"
      v-if="showAuditDetail"></audit-detail-controller>
    <!-- 批量转办 -->
    <a-modal
      v-drag
      v-if="modalForm.visible"
      class="actionModal"
      v-bind="{ ...modalForm }"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div class="modalCtn">
        <transfer-task-modal
          ref="transferModal"
          :taskIds="taskIds"
          @success="modalFormSuccess"
        ></transfer-task-modal>
        
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import TransferTaskModal from './modal/transferTaskModal'
import AuditDetailController from '@/views/srm/workFlow/AuditDetailController'

import cardStyleListLayout from '@/views/srm/bpm/components/cardStyleListLayout/index.vue'
import { todoTempMixin } from '@/views/srm/bpm/mixin/todoTempMixin.js'

export default {
    name: 'AuditTodoList',
    mixins: [ListMixin, todoTempMixin],
    components: {
        AuditDetailController,
        TransferTaskModal,
        'card-style-list-layout': cardStyleListLayout
    },
    data () {
        return {
            task: {
                action: ''
            },
            modalForm: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                confirmLoading: false,
                visible: false
            },
            taskIds: [],
            showModal: false,
            showEditPage: false,
            showAuditDetail: false,
            currentRow: {},
            subAccountList: [],
            superQueryShow: false,
            currentUrl: '',
            auditHisData: [],
            taskId: '',
            pageData: {
                superQueryShow: false,
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        folded: false,
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zRsr_2ef869a8`, '批量转办'),
                        //icon: 'setting',
                        folded: false,
                        type: 'primary',
                        clickFn: this.batchTransfer,
                        authorityCode: 'bpmn#auditAPI:batchTransferTask'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                    
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processTheTheme`, '流程主题'),
                        fieldName: 'subject',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processTheTheme`, '流程主题')
                    }
                    // {
                    //     type: 'select',
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型'),
                    //     fieldName: 'businessType',
                    //     dictCode: 'srmAuditBussinessType',
                    //     placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型')
                    // }
                ],
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_details`, '详情'),
                        clickFn: this.showDetail,
                        authorityCode: 'bpmn#adminTask:Detail',
                        attrs: { type: 'primary' },
                        event: 'approve'
                    }
                ],
                optColumnWidth: 290,
                form: {
                    keyWord: ''
                },
                isOrder: {
                    column: 'createDate',
                    order: 'desc'
                }
            },
            url: {
                list: '/a1bpmn/audit/api/runtime/admin/task/list',
                columns: 'bpmnTodoList'
            }
        }
    },
    activated () {
        // 重置任务按钮
        let {taskId} = this.$route.query
        if(!taskId){
            this.$store.commit('SET_TASKBTN', [])
            this.$store.commit('SET_TASK', {})
        }
    },
    mounted () {
        this.serachCountTabs('/flow/tab/api/runtime/admin/task/bpmn/tabs/count')
    },
    methods: {
        showDetail (row) {
            this.currentEditRow = Object.assign({}, row)
            this.currentEditRow['taskId'] = this.currentEditRow.id
            this.currentEditRow['id'] = row.bizKey
            this.currentEditRow['businessType'] = row.bizType
            this.currentEditRow['businessId'] = row.bizKey
            // this.$store.commit('SET_TASKID', this.currentEditRow.taskId)
            this.$store.commit('SET_TASK', {
                taskId: this.currentEditRow.taskId,
                processInstanceId: this.currentEditRow.processInstanceId
            })
            this.$store.commit('SET_TASKBTN', [
                { alias: 'approvalHistory', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvalComments`, '审批意见') },
                { alias: 'flowImage', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLP_1a93f54`, '流程图') },
                {
                    alias: 'transferTask',
                    btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办')
                }
            ])
            this.showAuditDetail = true
        },
        hideDetail () {
            this.showAuditDetail = false
            this.reloadAuditList()
            this.serachCountTabs('/flow/tab/api/runtime/admin/task/bpmn/tabs/count')
        },
        reloadAuditList () {
            this.$refs.listPage.handleQuery()
        },
        modalFormSuccess () {
            this.modalForm.visible = false
            this.$refs.listPage.handleQuery()
        },
        getCheckboxRecords () {
            let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords() || []
            let arr = []
            console.log('selectedRows', selectedRows)
            if(selectedRows.length>0){
                selectedRows.forEach((v)=>{
                    arr.push(v.id)
                })
            }
            return  arr
        },
        // 批量转办
        batchTransfer (){
            this.taskIds = this.getCheckboxRecords()
            if (this.taskIds.length<=0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            console.log('this.taskIds', this.taskIds)
            this.showModal = true
            this.modalForm.visible = true
        },
        handleOk () {
            console.log('this.taskIds', this.taskIds)
            this.$refs['transferModal'].handleConfirm(this.taskIds)
            this.$refs.listPage.loadData()
        },
        handleCancel () {
            this.modalForm.visible = false
            this.task.action = ''
        }
    }
}
</script>
