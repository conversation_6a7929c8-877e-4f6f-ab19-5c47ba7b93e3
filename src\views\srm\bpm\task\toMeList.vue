<template>
  <div
    :style="pageStyle"
    ref="page">
    <!-- 列表界面 -->
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showAuditDetail"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url"
      :tabsList="tabsList"
      :tabCurrent="tabCurrent"
      @afterChangeTab="handleAfterChangeTab">

      <!-- 覆盖模板tab内容 -->
      <template
        #tabs
        v-if="toggleCardMode">
        <div></div>
      </template>

      <template
        #query_extra>
        <a-tooltip
          :title="toggleCardMode ? $srmI18n(`${$getLangAccount()}#i18n_alert_ABCK_275e29ff`, '列表模式') : $srmI18n(`${$getLangAccount()}#i18n_alert_mOCK_27a11a94`, '卡片模式')">
          <a-button
            :style="{ 'marginLeft': toggleCardMode ? '0' : '10px'}"
            @click="handleToggleCardMode"><icon-font :type="toggleCardMode ? 'icon-list' : 'icon-card'" /></a-button>
        </a-tooltip>
      </template>

      <template
        #grid="{ activeKey, tabsList, columns, tableData, loadDataFunc, pagerConfig, getNewRouterFunc, loading }"
        v-if="toggleCardMode">
        <a-spin
          :spinning="loading"
          :delayTime="300">
          <card-style-list-layout
            :activeKey="activeKey"
            :tabsList="tabsList"
            :columns="columns"
            :tableData="tableData"
            :pagerConfig="pagerConfig"
            :buttons="pageData.optColumnList"
            @sidebar-click="(payload) => handleSidebarClick(payload, pagerConfig, loadDataFunc)"
            @card-approve="({ row }) => showDetail(row)"
            @card-chat="({ row }) => handleChat(row)"
            @card-link="({ col, column }) => handleExtendLink(col, column, getNewRouterFunc)"
            @page-change="(payload) => handlePageChange(payload, pagerConfig, loadDataFunc)"
          />
        </a-spin>
      </template>

    </list-layout>

    <!-- 表单区域 -->
    <audit-detail-controller
      :current-edit-row="currentEditRow"
      v-if="showAuditDetail"></audit-detail-controller>

  </div>
</template>

<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import AuditDetailController from '@/views/srm/workFlow/AuditDetailController'

import cardStyleListLayout from '@/views/srm/bpm/components/cardStyleListLayout/index.vue'
import { todoTempMixin } from '@/views/srm/bpm/mixin/todoTempMixin.js'

export default {
    name: 'AuditTodoList',
    mixins: [ListMixin, todoTempMixin],
    inject: ['routeReload'],
    components: {
        AuditDetailController,
        'card-style-list-layout': cardStyleListLayout
    },
    data () {
        return {
            showEditPage: false,
            showAuditDetail: false,
            currentRow: {},
            superQueryShow: false,
            subAccountList: [],
            currentUrl: '',
            auditHisData: [],
            pageData: {
                superQueryShow: false,
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: false, clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processTheTheme`, '流程主题'),
                        fieldName: 'subject',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processTheTheme`, '流程主题')
                    }
                    // {
                    //     type: 'select',
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型'),
                    //     fieldName: 'businessType',
                    //     dictCode: 'srmAuditBussinessType',
                    //     placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型')
                    // }
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_details`, '详情'), clickFn: this.showDetail, authorityCode: 'bpmn#auditAPI:meDetail', attrs: { type: 'primary' }, event: 'approve'}
                ],
                optColumnWidth: 290,
                form: {
                    keyWord: ''
                },
                isOrder: {
                    column: 'createDate',
                    order: 'desc'
                }
            },
            url: {
                list: '/a1bpmn/audit/api/toMe/task/list',
                columns: 'toMeList'          
            }
        }
    },
    mounted () {
        this.serachCountTabs('/flow/tab/api/tome/task/bpmn/tabs/count')
    },
    activated () {
        // 重置任务按钮
        let {taskId} = this.$route.query
        if(!taskId){
            this.$store.commit('SET_TASKBTN', [])
            this.$store.commit('SET_TASK', {})
        }
    },
    watch: {
        '$route.query': {
            immediate: true,
            handler () {
                this.getUrlParam()
            }
        }
    },
    methods: {
        getUrlParam () {
            let businessId = this.$route.query.businessId
            let businessType = this.$route.query.businessType
            if(businessId && businessType){
                let row = {}
                row['id'] = businessId
                row['businessType'] = businessType
                row['businessId'] = businessId
                this.showAuditDetail = false
                this.showEditPage = false
                this.$nextTick(()=> {
                    this.currentEditRow = row
                    this.showAuditDetail = true
                    this.showEditPage = false
                    this.$store.dispatch('SetTabConfirm', true)
                })
            }
        },
        showDetail (row) {
            this.currentEditRow = Object.assign({}, row)
            this.currentEditRow['taskId'] = this.currentEditRow.id
            this.currentEditRow['id'] = row.bizKey
            this.currentEditRow['businessType'] = row.bizType
            this.currentEditRow['businessId'] = row.bizKey
            // this.$store.commit('SET_TASKID', this.currentEditRow.taskId)
            this.$store.commit('SET_TASK', {
                taskId: this.currentEditRow.taskId,
                processInstanceId: this.currentEditRow.procId,
                businessType: this.currentEditRow.businessType,
                id: this.currentEditRow.businessId
            })
            if(row.bizType =='resultEnquiry'){
                this.$store.commit('SET_TASKBTN', [
                    { alias: 'print', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印') },
                    { alias: 'approvalHistory', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvalComments`, '审批意见') },
                    { alias: 'flowImage', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLP_1a93f54`, '流程图') }
                ])
            }else{
                this.$store.commit('SET_TASKBTN', [
                    { alias: 'approvalHistory', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvalComments`, '审批意见') },
                    { alias: 'flowImage', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLP_1a93f54`, '流程图') }
                ])
            }
            this.showAuditDetail = true
        },
        hideDetail () {
            this.showAuditDetail = false
            this.reloadAuditList()
        },
        reloadAuditList () {
            this.$refs.listPage.handleQuery()
        }
    }
}
</script>
