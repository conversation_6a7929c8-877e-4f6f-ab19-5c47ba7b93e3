<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction } from '@/api/manage'
export default {
    name: 'ElsSealsAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'seals',
            pageData: {
                form: {
                    sealType: '',
                    companyName: '',
                    subAccount: '',
                    companyId: '',
                    accountId: '',
                    userIds: '',
                    userNames: '',
                    sealName: '',
                    filePath: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WeLD_27cabba0`, '印章维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_transferType`, '类型'),
                                    fieldName: 'sealType',
                                    dictCode: 'contractLockSealsType',
                                    required: '1'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dWRC_2e0e62aa`, '所属公司'),
                                    fieldName: 'companyName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dWRC_2e0e62aa`, '所属公司'),
                                    bindFunction: function (Vue, data){
                                        Vue.form.companyName = data[0].companyName,
                                        Vue.form.subAccount = data[0].subAccount,
                                        Vue.form.companyId = data[0].companyId,
                                        Vue.form.accountId = data[0].accountId
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称'), with: 150},
                                            {field: 'companyId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCAZdWW_99b0ddbb`, '公司id'), with: 150},
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '绑定子账号'), with: 150},
                                            {field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), with: 150}
                                        ],
                                        modalUrl: '/contractLock/purchaseCLCompanyInfo/list',
                                        modalParams: {certificationStatus: 2}
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WedWL_d12084ec`, '印章所属人'),
                                    fieldName: 'userNames',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WedWL_d12084ec`, '印章所属人'),
                                    bindFunction: function (Vue, data) {
                                        let userNames = []
                                        let userIds = []
                                        if (data && data.length) {
                                            data.forEach((item)=> {
                                                if (item.id) {
                                                    userIds.push(item.id)
                                                    userNames.push(item.applyUserName)
                                                }
                                            })
                                        }
                                        Vue.form.userIds = userIds.join(',')
                                        Vue.form.userNames = userNames.join(',')
                                    }, extend: {
                                        selectModel: 'multiple',
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), with: 150},
                                            {field: 'applyUserName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150},
                                            {field: 'accountId', title: '契约锁账号', with: 150}
                                        ],
                                        modalUrl: '/contractLock/purchaseClPersonalInfo/list',
                                        modalParams: function (that, form, row){
                                            return {companyId: form.companyId,realName:'1'}
                                        },
                                        beforeCheckedCallBack: function (Vue, pageData, groupData, form) {
                                            return new Promise((resolve, reject) => {
                                                return  form.companyId !== '' ? resolve('success') : reject('先选择公司')
                                            })
                                        }
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_sMURgjgo`, '印章别名'),
                                    fieldName: 'sealName',
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeLm_27cc1068`, '印章规格'),
                                    fieldName: 'sealSpec',
                                    dictCode: 'contractLockSealsSpec'
                                },
                                {
                                    fieldType: 'image',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_qBGuFJsS`, '图片路径'),
                                    fieldName: 'filePath',
                                    required: '1',
                                    extend: {multiple: false, limit: 1, businessType: 'contractLock', actionRoutePath: '/srm/contractLock/PurchaseCLSealsList'}
                                },
                                {
                                    fieldType: 'image',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hLlbPO_3b0edf09`, '法人授权图片'),
                                    fieldName: 'lpLetterPath',
                                    extend: {multiple: false, limit: 1, businessType: 'contractLock', actionRoutePath: '/srm/contractLock/PurchaseCLSealsList'}
                                }
                            ],
                            validateRules: {
                                companyName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dWRCxOLV_ad201ba`, '所属公司不能为空')}],
                                sealName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeqRxOLV_4fade7f4`, '签章别名不能为空')}],
                                filePath: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WexOLV_4a4b6180`, '印章不能为空')}],
                                sealType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WWWWWWWWWWWWWWWWWW_38a90bb`, '类型不能为空')}]
                            }
                        }

                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/contractLock/purchaseClSeals/add'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {},
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const form = this.$refs.addPage.getPageData()
            if(!form.filePath){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WexOLV_4a4b6180`, '印章不能为空'))
                return
            }
            this.$refs.addPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        this.$refs.addPage.confirmLoading = false
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    if(params.sealType == 'LP' && !params.lpLetterPath){
                        this.$refs.addPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AWeAcLhLKWlTXVhLlbW_b4518378`, '当印章类型为法人时，必须上传法人授权书'))
                        return
                    }
                    let url = this.url.add
                    const _this = this
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            _this.$parent.addCallBack(res.result)
                        }
                    }).finally(() => {
                    })
                }
            })
        }
    }
}
</script>