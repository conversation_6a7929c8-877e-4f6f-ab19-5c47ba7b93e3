<template>
  <div>
    <template>
      <!-- 文本域 -->
      <div
        class="code-editor-textarea"
        v-if="showLabel === 'textarea'">
        <a-textarea
          :disabled="true"
          :rows="6"
          v-model="code">
        </a-textarea>
        <div 
          class="textarea-icon-wrap"
          v-if="!disabled"
          @click="editorModalFlag= true"
        >
          <a-icon
            class="textarea-icon"
            type="edit"
            :style="{cursor: 'pointer', color: defaultColor }"
          />
        </div>
      </div>
      <!-- 输入框 -->
      <a-input
        v-else
        :disabled="true"
        v-model="code">
        <a-icon
          v-if="!disabled"
          slot="suffix"
          type="edit"
          :style="{cursor: 'pointer', color: defaultColor }"
          @click="editorModalFlag= true" />
      </a-input>
    </template>
    <a-modal
    v-drag    
      forceRender
      :visible="editorModalFlag"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_srmCodeEditor`, 'SRM代码编辑器')"
      :width="800"
      @ok="handleSureClick"
      @cancel="editorModalFlag=false">
      <codemirror
        v-if="editorModalFlag"
        v-model="code"
        :options="editorOption"
        @change="changeCodeMirror">
      </codemirror>
    </a-modal>
  </div>
</template>

<script>
import { codemirror }  from 'vue-codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/javascript/javascript'
// 光标行背景高亮，配置也需要配置styleA
import 'codemirror/addon/selection/active-line'
// sublime编辑器效果
import 'codemirror/keymap/sublime'
import 'codemirror/mode/css/css'
// 编辑器主题样式，配置里面theme需要设置monokai
import 'codemirror/theme/monokai.css'
import 'codemirror/mode/markdown/markdown'
import 'codemirror/mode/vue/vue'
//下面这几个引入的主要是验证提示类的,配置里的lint需要设置true,gutters: ['CodeMirror-lint-markers']
import 'codemirror/addon/hint/show-hint.js'
import 'codemirror/addon/hint/show-hint.css'
import 'codemirror/addon/hint/javascript-hint.js'
//及时自动更新，配置里面也需要设置autoRefresh为true
import 'codemirror/addon/display/autorefresh'

import 'codemirror/addon/scroll/annotatescrollbar.js'
import 'codemirror/addon/search/matchesonscrollbar.js'
import 'codemirror/addon/search/match-highlighter.js'
import 'codemirror/addon/search/jump-to-line.js'

import 'codemirror/addon/dialog/dialog.js'
import 'codemirror/addon/dialog/dialog.css'
import 'codemirror/addon/search/searchcursor.js'
import 'codemirror/addon/search/search.js'

// 代码段折叠功能
import 'codemirror/addon/fold/foldcode'
import 'codemirror/addon/fold/foldgutter'
import 'codemirror/addon/fold/foldgutter.css'

import 'codemirror/addon/fold/brace-fold'
import 'codemirror/addon/fold/comment-fold'
import 'codemirror/addon/fold/xml-fold.js'
import 'codemirror/addon/fold/indent-fold.js'
import 'codemirror/addon/fold/markdown-fold.js'
import 'codemirror/addon/fold/comment-fold.js'
import { DEFAULT_COLOR } from '@/store/mutation-types'
export default {
    name: 'CodeEditorModel',
    components: {
        codemirror
    },
    props: {
        // value 必传，并且是字符串
        value: {
            type: String,
            default: ''
        },
        // codemirro的配置参数
        options: {
            type: Object,
            default: null
        },
        // 
        disabled: {
            type: Boolean,
            default: null
        },
        showLabel: {
            type: String,
            default: ''
        }
    },
    data () {
        return {
            code: '',
            editorModalFlag: false
        }
    },
    computed: {
        editorOption () {
            let opt =  {
                mode: 'text/javascript',
                // mode: 'application/json',
                theme: 'monokai',
                indentUnit: 4,
                lineNumbers: true,
                styleActiveLine: true,
                autofocus: true,
                foldGutter: true,
                lineWrapping: true,
                gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
                // 代码提示功能
                hintOptions: {
                    // 避免由于提示列表只有一个提示信息时，自动填充
                    completeSingle: false
                },
                keyMap: 'sublime'
            }
            let editorOpt = Object.assign({}, opt, this.options)
            return editorOpt
        },
        // 获取项目的主题色
        defaultColor () {
            return this.$ls.get(DEFAULT_COLOR)
        }
    },
    watch: {
        value: {
            immediate: true,
            handler (val) {
                this.code = val? val: ''
            }
        }
    },
    methods: {
        changeCodeMirror (val) {
            this.code = val
        },
        handleSureClick () {
            this.editorModalFlag = false
            // 有传入的回写值
            this.$emit('handleSureClick', this.code)
        }
    }
}
</script>
<style lang="less" scoped>
.code-editor-textarea{
    position: relative;
}
.textarea-icon-wrap{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer
}
.textarea-icon{
    font-size: 22px;
}
</style>