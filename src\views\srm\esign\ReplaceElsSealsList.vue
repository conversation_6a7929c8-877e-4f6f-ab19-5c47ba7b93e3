<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      modelLayout="seal"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <ReplaceElsSealsEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <ReplaceElsSealsDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <ReplaceElsSealsAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>

</template>
<script>
// 新列表模板
import { ListMixin } from './list/ListMixin'
import ReplaceElsSealsAdd from './modules/ReplaceElsSealsAdd'
import ReplaceElsSealsEdit from './modules/ReplaceElsSealsEdit'
import ReplaceElsSealsDetail from './modules/ReplaceElsSealsDetail'
export default {
    mixins: [ListMixin],
    components: {
        ReplaceElsSealsEdit,
        ReplaceElsSealsDetail,
        ReplaceElsSealsAdd
    },
    data () {
        return {
            showAddPage: false,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'esign#saleEsignSeals:add'}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeqR_27c540b2`, '(公司名称)')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esign#saleEsignSeals:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esign#saleEsignSeals:edit'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'esign#saleEsignSeals:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/esign/elsSeals/replaceList',
                delete: '/esign/elsSeals/delete',
                columns: 'ElsSealsList'
            }
        }
    },
    methods: {
        //已认证不能被编辑
        handleAdd () {
            this.showAddPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        allowEdit (row){
            if(row.sealId){
                return true
            }
            return false
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        allowDelete (row){
            if(row.sealId){
                return true
            }
            return false
        }
    }
}
</script>