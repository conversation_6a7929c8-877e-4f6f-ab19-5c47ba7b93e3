<template>
  <div
    class="page-container"
    :style="{height: pageContentHeight}">
    <div class="edit-page">
      <div
        class="page-content list" 
        v-if="!showNoticeEditPage && !showNoticeDetailPage"
      >
        <content-header />
        <ChangeTenderNotice
          @handNoticeEditPage="handNoticeEditPage"
          @handleNoticeViewPage="handleNoticeViewPage"/>
      </div>
      <ChangeTenderNoticeEdit
        v-if="showNoticeEditPage"
        :current-edit-row="currentEditRow"
        @hide="hideEditPage"
      ></ChangeTenderNoticeEdit>
      <ChangeTenderNoticeDetail
        v-if="showNoticeDetailPage"
        :current-edit-row="currentEditRow"
        @hide="hideDetailPage"
      ></ChangeTenderNoticeDetail>
    </div>
  </div>
</template>
<script>
import ChangeTenderNotice from './modules/ChangeTenderNotice'
import ChangeTenderNoticeEdit from './modules/ChangeTenderNoticeEdit'
import ChangeTenderNoticeDetail from './modules/ChangeTenderNoticeDetail'
import contentHeader from '../components/content-header'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'

export default {
    name: 'ChangeTenderNoticeList',
    inject: ['tenderCurrentRow', 'subpackageId'],
    components: {
        ChangeTenderNotice,
        ChangeTenderNoticeEdit,
        ChangeTenderNoticeDetail,
        contentHeader
    },
    computed: {
        pageContentHeight () {
            let height = document.documentElement.clientHeight - 60
            return height + 'px'
        },
        subId () {
            return this.subpackageId()
        }
    },
    data () {
        return {
            showNoticeEditPage: false,
            showNoticeDetailPage: false,
            currentEditRow: {}
        }
    },
    methods: {
        hideEditPage () {
            this.showNoticeEditPage = false
        },
        hideDetailPage (){
            this.showNoticeDetailPage = false
        },
        handNoticeEditPage (row) {
            this.currentEditRow = row
            this.showNoticeEditPage = true
        },
        handleNoticeViewPage (row){
            this.currentEditRow = row
            this.showNoticeDetailPage = true
        }
    },
    created () {
        let url = '/tender/purchaseTenderProjectHead/querySubpackageInfoBySubpackageId'
        getAction(url, {subpackageId: this.subId}).then(res => {
            console.log('resresres', res)
            if (res.success) {
                console.log('res.result', res.result.status)
                this.status=res.result.status

            }
        })
    }
}
</script>
<style lang="less" scoped>
.ClarifyAndQuestions{
  height: 100%;
}
.page-content{
    flex: 1;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    overflow: auto;
    padding: 6px;
    background-color: #fff;
}
</style>


