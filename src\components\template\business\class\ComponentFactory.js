import {getLangAccount, srmI18n} from '@/utils/util'
import store from '@/store'
import {message as Message} from 'ant-design-vue'
import {FILLDOWN_CALLBACK} from '@/utils/const.js'

// 抽象工厂
class Component {
    package () {
        throw new Error('不允许直接调用！')
    }
}

// 具体产品类
class ButtonComponent extends Component  {
    constructor (option) {
        super()
        this.btnConfig = {}
        this.pageCode = 'fileInfo'
        this.group = []
        this.currentColumn = ''
        this.limitField = ['selectModal', 'remoteSelect']
        this.init(option)
    }
    init (options) {
        if (options && Object.keys(options).length > 0) {
            Object.keys(this).forEach(item => {
                if (options && Object.prototype.hasOwnProperty.call(options, item)) this[item] = options[item]
            })
        }
        if (!Object.keys(this.btnConfig).length) {
            // 默认值
            this.btnConfig = {
                title: srmI18n(`${getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                label: srmI18n(`${getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                msgType: 'fillDown',
                key: 'fillDown',
                type: 'tool-fill',
                beforeCheckedCallBack: (info, otherParams) => {
                    this.gridFillDown(info, otherParams)
                }
            }
        }
    }
    createButton (group, businessType) {
        this.pageCode = group.groupCode
        this.group = group
        if (businessType === 'externalToolBar') { // 所有表行都插入
            if (group.groupType === 'item') {
                if (!group.externalToolBar) {
                    group.externalToolBar = [this.btnConfig]
                } else {
                    if (group.externalToolBar.findIndex(itx => itx.key === this.btnConfig.key) === -1) group.externalToolBar.push(this.btnConfig)
                }
            }
        } else {
            if (group.type === 'grid') {
                if (!group.custom.buttons) {
                    group.custom.buttons = [this.btnConfig]
                } else {
                    if (group.custom.buttons.findIndex(itx => itx.msgType === this.btnConfig.msgType) === -1) group.custom.buttons.push(this.btnConfig)
                }
            }
        }
        return group
    }
    // 表格向下填充
    gridFillDown (info, otherParams) {
        this.info = info
        this.otherParams = otherParams
        if (!this.info || Object.keys(this.info).length === 0) {
            Message.warning(srmI18n(`${getLangAccount()}#i18n_field_VisImqAtjBmW_97caa78e`, '请选中一个可编辑的表格！'))
            return
        }
        const { tplRootRef, group, flag } = this.otherParams
        const {$grid, row, column} = this.info

        if (typeof group === 'object') { // 处理模板传参差异
            this.currentColumn = group?.custom?.columns.find(rs => rs.field === column.field)
        } else {
            let currentGroup = tplRootRef.$refs.businessRef.pageConfig?.groups.find(rs => rs.groupCode === group)
            this.currentColumn = currentGroup.columns.find(rs => rs.field === column.field)
        }
        if (!this.currentColumn?.editRender) {
            Message.warning(srmI18n(`${getLangAccount()}#i18n_field_xqAtJOWHRSVW_62b544d5`, '不可编辑字段，禁止填充！'))
            return
        }
        if (this.limitField.includes(this.currentColumn.fieldType)) {
            Message.warning(srmI18n(`${getLangAccount()}#i18n_alert_APJOAcHRSVW_9ff9f8de`, '当前字段类型禁止填充！'))
            return
        }
        // 虚拟行index
        const rowIndex = $grid.getVMRowIndex(row)
        const { fullData } = $grid.getTableData()
        // filldown回调 先执行，防止bindfunction阻塞
        if (tplRootRef[FILLDOWN_CALLBACK] && typeof tplRootRef[FILLDOWN_CALLBACK] === 'function') {
            const params = {
                currentSelectVal: this.currentSelectVal,
                currentColumn: this.currentColumn,
                row,
                column,
                fullData,
                $grid,
                ...this.otherParams
            }
            tplRootRef[FILLDOWN_CALLBACK](params)
        }
        fullData.forEach((item, index) => {
            // 处理行编辑规则
            let isAllow = this._cellIsAllowClick(item)
            if (index > rowIndex && isAllow) {
                // 当前选中的值
                this.currentSelectVal = fullData[rowIndex][column.property]
                item[column.property] = this.currentSelectVal
                // 税码向下填充时,也填充税率
                if(column.property == 'taxCode') {
                   item.taxRate = fullData[rowIndex]['taxRate']
                }
                // 主供应商编码向下填充时，也填充名称
                if(column.property == 'mainSupplierCode') {
                    item.mainSupplierName = fullData[rowIndex]['mainSupplierName'];
                }
                // bindfunction处理 
                if (this.currentColumn.bindFunction && typeof this.currentColumn.bindFunction === 'function') {
                    this.fieldChange(item)
                } 
            }
        })
        // 填充后清空表格数据
        store.dispatch('setEditActivedInfo', {})
    }
    _cellIsAllowClick (_row) {
        const { tplRootRef, group, flag, self} = this.otherParams
        const {$grid, column } = this.info
        const _columnIndex = $grid.getVMColumnIndex(column)
        const _rowIndex = $grid.getVMRowIndex(_row)
        let isAllow = true
        let currentGroup = ''
        let that = tplRootRef
        if (flag == 'editlayout'||flag =='tileEditPage') {
            // 获取行编辑规则
            currentGroup = tplRootRef?.pageConfig?.groups.find(rs => rs.groupCode === 'gridEditConfig')
        } else {
            that = tplRootRef.$refs.businessRef
            currentGroup = tplRootRef.$refs.businessRef.pageConfig?.groups.find(rs => rs.groupCode === group)
        } 
        if (currentGroup?.extend?.editConfig?.activeMethod) {
            // 执行行编辑规则
            isAllow = currentGroup.extend.editConfig.activeMethod(that, _row, _rowIndex, column, _columnIndex)
        }
        return isAllow
    }
    fieldChange (row) {
        const { tplRootRef, self, flag } = this.otherParams
        const { column} = this.info
        const commonTypes = ['input', 'password', 'textArea', 'select', 'multiple', 'switch']
        const rootRef = flag == 'editlayout' ? tplRootRef : tplRootRef.$refs.businessRef
        const fieldType = this.currentColumn.fieldType
        if (commonTypes.includes(fieldType)) {
            let currentRow = {
                row,
                column
            }
            let currentValue = {
                value: this.currentSelectVal
            }
            rootRef.changeGridItem(currentRow, currentValue, this.currentColumn.bindFunction)
        } else if (['number', 'float', 'currency'].includes(fieldType)) {
            rootRef.changeGridFloatItem(row, column, this.currentSelectVal, this.currentColumn.bindFunction)
        } else if (fieldType === 'date') {
            rootRef.changeGridItemOther(row, this.currentSelectVal, this.currentColumn.bindFunction )
        } else if (fieldType === 'selectModal') {
            // const data = [this.currentSelectVal]
            // rootRef.changeSelectModal(row, data, this.currentColumn.bindFunction)
        }

    }
    
}
export {  ButtonComponent }