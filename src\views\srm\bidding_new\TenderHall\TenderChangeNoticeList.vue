<template>
  <div
    class="page-container"
    :style="{height: pageContentHeight}">
    <div class="edit-page">
      <div
        class="page-content list" 
        v-if="!showNoticeDetailPage"
      >
        <content-header />
        <ChangeTenderNotice
          :typePage="pageType"
          @handleNoticeViewPage="handleNoticeViewPage"/>
      </div>
      <ChangeTenderNoticeDetail
        v-if="showNoticeDetailPage"
        :current-edit-row="currentEditRow"
        @hide="hideDetailPage"
      ></ChangeTenderNoticeDetail>
    </div>
  </div>
</template>
<script>
import ChangeTenderNotice from '../BiddingHall/ChangeTenderNotice/modules/ChangeTenderNotice'
import ChangeTenderNoticeDetail from '../BiddingHall/ChangeTenderNotice/modules/ChangeTenderNoticeDetail'
import contentHeader from '../BiddingHall/components/content-header'
export default {
    name: 'ChangeTenderNoticeList',
    components: {
        ChangeTenderNotice,
        ChangeTenderNoticeDetail,
        contentHeader
    },
    computed: {
        pageContentHeight () {
            let height = document.body.clientHeight -70
            return height + 'px'
        }
    },
    data () {
        return {
            showNoticeDetailPage: false,
            pageType: 'detail',
            currentEditRow: {}
        }
    },
    methods: {
        hideDetailPage (){
            this.showNoticeDetailPage = false
        },
        handleNoticeViewPage (row){
            this.currentEditRow = row
            this.showNoticeDetailPage = true
        }
    },
    created (){
    }
}
</script>
<style lang="less" scoped>
.ClarifyAndQuestions{
  height: 100%;
}
.page-content{
    flex: 1;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    overflow: auto;
    padding: 6px;
    background-color: #fff;
}
</style>


