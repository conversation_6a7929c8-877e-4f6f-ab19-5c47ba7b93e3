<template>
  <!-- 评分项 -->
  <div class="ReviewItems">
    <a-spin :spinning="confirmLoading">
      <div class="review-items-top">
        <a-row>
          <a-col :span="12">
            <span>{{ currentRow.title }}</span>
            <span class="desc">{{ $srmI18n(`${$getLangAccount()}#i18n_field_yCkz_3d0675e2`, '节点总分') }}：{{ evaluationGroup.score }}</span>
            <span class="desc">{{ $srmI18n(`${$getLangAccount()}#i18n_field_bs_d12ea`, '权重') }}：{{ evaluationGroup.weights ? (evaluationGroup.weights + '%') : '' }}</span>
          </a-col>
          <a-col 
            :span="12" 
            style="text-align: right;">
            <a-button
              type="primary"
              @click="preView">{{ $srmI18n(`${$getLangAccount()}#i18n_field_QIUB_2f624453`, '文件预览') }}</a-button>
            <a-button 
              v-if="currentRow['editStatus']"
              type="primary"
              @click="saveData('')"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
            <a-button 
              v-if="currentRow['editStatus']"
              type="primary"
              style="margin-left: 10px;"
              @click="publishData">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
            <a-button 
              @click="goBack"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </a-col>
        </a-row>
      </div>
      <div class="review-items-grid">
        <div
          v-if="!currentRow.summary"
          class="grid-ul">
          <ul>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_title_seq`, '序号') }}</li>
            <li
              :key="item.regulationId"
              v-for="(item, index) in reviewList">{{ index + 1 }}</li>
            <li></li>
            <li v-if="currentRow.summary"></li>
          </ul>
          <ul class="ul">
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_UUTv_40ed7967`, '评审条例') }}</li>
            <li
              :key="review.regulationId"
              v-for="review in reviewList">
              <overflowspan>
                {{ review.regulationName }}
              </overflowspan>
            </li>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_nt_ab899`, '合计') }}</li>
            <li v-if="currentRow.summary">{{ $srmI18n(`${$getLangAccount()}#i18n_field_yV_fe747`, '结论') }}</li>
          </ul>
          <ul class="ul">
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_TvMW_30358fcb`, '条例描述') }}</li>
            <li
              :key="review.regulationId + review.regulationName"
              v-for="review in reviewList">
              <overflowspan>
                {{ regulationNameMap[review.regulationId] }}
              </overflowspan>
            </li>
            <li v-if="currentRow.summary"></li>
            <li v-else></li>
          </ul>
          <ul 
            class="ul" 
            v-for="supplier in resultData.supplierList" 
            :key="supplier.supplierAccount">
            <li v-if="supplier.invalid == '1'">
              <overflowspan>
                {{ supplier.supplierName }}({{ $srmI18n(`${$getLangAccount()}#i18n_alert_IuB_16c86ba`, '已否决') }})
              </overflowspan>
            </li>
            <li v-else>
              <overflowspan>
                {{ supplier.supplierName }}
              </overflowspan>
            </li>
            <template v-if="resultData.evaResultListMap">
              <template v-if="resultData.evaResultListMap[supplier.supplierAccount]">
                <li
                  :key="review.regulationId"
                  v-for="(review, index) in resultData.evaResultListMap[supplier.supplierAccount]">
                  <vxe-input 
                    ref="input"
                    size="small"
                    :class="{'warning-tip': tipStatusFun(review.score)}"
                    v-model="review.score"
                    :disabled="!currentRow['editStatus']"
                    @blur="changeScore(supplier.supplierAccount, review, scoreRangeList[index]['scoreRange'], $event)" 
                    @prev-number="changeScore(supplier.supplierAccount, review, scoreRangeList[index]['scoreRange'], $event)"
                    @next-number="changeScore(supplier.supplierAccount, review, scoreRangeList[index]['scoreRange'], $event)"
                    :placeholder="scoreRangeList.length ? scoreRangeList[index]['scoreRange'] : '0~0'" 
                    type="number"
                    clearable></vxe-input>
                  <a-icon
                    type="edit"
                    theme="twoTone"
                    @click="showLeaderOpinion(review)"/>
                </li>
              </template>
              <template v-else>
                <template v-for="review in reviewList">
                  <li :key="review.regulationId"><span>/</span></li>
                </template>
              </template>
            </template>
            <li v-if="supplier.invalid == 1"><span>/</span></li>
            <li v-else>{{ supplier.totalScore }}</li>
          </ul>
        </div>
        <!-- 汇总进入显示该模块 -->
        <div
          class="grid-ul"
          v-else>
          <ul>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_title_seq`, '序号') }}</li>
            <li
              v-for="(item, index) in resultListSummary"
              :key="item.id">{{ index + 1 }}</li>
            <li></li>
            <li key="weights"></li>
            <li key="weightScore"></li>
          </ul>
          <ul class="ul">
            <li>{{ evaluationPrinciples.summaryCalType=='0' ? $srmI18n(`${$getLangAccount()}#i18n_btn_suRL_24e082c6`, '专家名称'): $srmI18n(`${$getLangAccount()}#i18n_title_regulationName`, '条例名称') }}</li>
            <li
              v-for="resultList in resultListSummary"
              :key="resultList.id">
              <overflowspan>
                {{ resultList.name }}
              </overflowspan>
            </li>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_yV_fe747`, '结论') }}</li>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_bs_d12ea`, '权重') }}</li>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_Ktjz_2be595f6`, '实际得分') }}</li>
          </ul>
          <!-- <ul class="ul">
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_TvMW_30358fcb`, '条例描述') }}</li>
            <template v-if="resultListSummary.length > 0">
              <li
                :key="resultList.regulationId + resultList.regulationName"
                v-for="resultList in resultListSummary">{{ regulationNameMap[resultList.id] }}</li>
              <li></li>
              <li></li>
              <li></li>
            </template>
            <li v-else></li>
          </ul> -->
          <ul 
            class="ul" 
            v-for="supplier in supplierListSummary" 
            :key="supplier.supplierAccount">
            <li v-if="supplier.invalid == '1'">{{ supplier.supplierName }}({{ $srmI18n(`${$getLangAccount()}#i18n_alert_IuB_16c86ba`, '已否决') }})</li>
            <li v-else>{{ supplier.supplierName }}</li>
            <template>
              <li
                v-for="resultList in resultListSummary"
                :key="resultList.id">{{ resultList[supplier.supplierAccount] || '/' }}</li>
            </template>
            <li v-if="supplier.invalid=='1' && currentRow.summary"><span>/</span></li>
            <li v-else-if="currentRow.summary">
              <span
                style="cursor: pointer; color: blue;" 
                @click="summaryFn(supplier.supplierAccount, supplier.supplierName)">{{ supplier.totalScore || '/' }}</span>             
            </li>
            <li>{{ supplier.weights || '/' }}</li>
            <li >{{ supplier.weightScore || '/' }}</li>
          </ul>
        </div>
      </div>

      <!-- 汇总详情弹窗 -->
      <a-modal
        v-drag     
        v-model="visible" 
        :width="800"
        :title="supplierName" 
        :cancel-text="$srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭')">
        <template slot="footer">
          <a-button @click="() => {this.visible=false}">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
        </template>
        <vxe-grid
          v-bind="gridOptions">
        </vxe-grid>
      </a-modal>
    </a-spin>

    <!-- 文件预览 -->
    <PreView 
      ref="pv" 
      :currentRow="currentRow" 
      :supplierList="supplierList"></PreView>
    <LeaderOpinionModal
      ref="LeaderOpinionModal"
      :pageStatus="!currentRow['editStatus'] ? 'detail' : 'edit'"
      @success="LeaderOpinionSuccess"
      typeNum="noLeader"/>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { gridOptionsMixin } from '../public/gridOptionsMixin.js'
import { add } from '@/utils/mathFloat.js'
import PreView from '../public/preView'
import LeaderOpinionModal from './LeaderOpinionModal'
import overflowspan from './overflowspan'

export default {
    mixins: [gridOptionsMixin],
    components: {
        PreView,
        LeaderOpinionModal,
        overflowspan
    },
    props: {
        currentRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flag: true,
            supplierList: [],
            supplierListSummary: [],
            resultListSummary: [],
            confirmLoading: false,
            tipStatus: false,
            resultData: {},
            reviewList: [],
            scoreRangeList: [],
            visible: false,
            gridOptions: {
                border: true,
                resizable: true,
                showOverflow: true,
                height: 300,
                editConfig: {
                    trigger: 'click',
                    mode: 'cell'
                },
                align: 'center',
                toolbarConfig: {
                    enabled: false // 禁用自定义工具
                },
                columns: [],
                data: []
            },
            supplierName: '',
            evaluationPrinciples: {},
            regulationNameMap: {},
            evaluationGroup: {}
        }
    },
    created () {
        this.getData()
    },
    computed: {
        headParams () {
            let {checkType, currentStep, processType } = this.currentRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return {xNodeId}
        }
    },
    watch: {
        resultData (val) {
            this.supplierList = val.supplierList
            console.log('watch', val)
        }
    },
    methods: {
        preView () { // 文件预览
            const pv = this.$refs.pv
            pv.queryFiel()
        },
        screenColumnsData (evaList) {
            let old = [], 
                columns = [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), type: 'seq', width: 50},
                    {field: 'regulationName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUTv_40ed7967`, '评审条例')}
                ]
            evaList.forEach((item, index) => {
                if (index == 0) {
                    const obj = {field: item.judgesTaskHeadId, title: item.judgesName}
                    columns.push(obj)
                    old.push(item.judgesTaskHeadId) // 数据只保存一次
                } else {
                    if (!old.includes(item.judgesTaskHeadId)) {
                        const obj = {field: item.judgesTaskHeadId, title: item.judgesName}
                        columns.push(obj)
                        old.push(item.judgesTaskHeadId) // 数据只保存一次
                    }
                }
            })
            this.gridOptions.columns = columns
            this.screenData(evaList, old)
        },
        screenData (evaList, old) {
            let headIds = [], 
                regulationId = [], // 记录去重的数据
                fields = []
            evaList.forEach((item, index) => {
                if (regulationId.includes(item.regulationId)) { // 评审条例 ID 存在时的操作
                    fields.forEach(field => {
                        if (field.regulationId == item.regulationId) { // 判断条例id是否一致
                            field[item.judgesTaskHeadId] = item.score // 在原有的数据新增其他专家的评审结果
                        }
                    })
                } else { // 评审条例 ID 不存在时的操作
                    for (let i=0; i<old.length; i++) {
                        if (old[i] == item.judgesTaskHeadId) {
                            const obj = {
                                regulationName: item.regulationName, 
                                regulationId: item.regulationId
                            }
                            obj[old[i]] = item.score
                            fields.push(obj)
                        }
                    }
                    regulationId.push(item.regulationId)
                }
            })
            this.gridOptions.data = fields
            console.log('fields', fields)
        },
        // 汇总详情方法 
        summaryFn (supplierAccount, supplierName) {
            this.supplierName = supplierName
            const {evaGroupId, evaInfoId} = this.currentRow
            const params = {
                evaGroupId: evaGroupId,
                evaInfoId: evaInfoId,
                supplierAccount: supplierAccount.split('_')[0] || ''
            }
            this.visible = true
            let columns = [], data = []
            getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/querySupplierByEvaGroupId', params).then(res => {
                if (res.code == '200' && res.result) {
                    let {evaRegulationResultList} = res.result
                    console.log(evaRegulationResultList || [])
                    this.screenColumnsData(evaRegulationResultList || [])
                    // this.screenData(evaRegulationResultList)
                }
            })
        },
        tipStatusFun (score) { // 必填项样式显隐判断
            if (this.tipStatus && (score == null || score == '')) {
                return true
            }
            return false
        },
        // 获取评审项的数据 ?evaGroupId=1518091956243259394
        getData () {
            const {evaGroupId, id, summary, evaInfoId} = this.currentRow
            let params = {
                evaGroupId: evaGroupId
            }
            !summary && (() => {params['judgesTaskItemId'] = id})()
            summary && (() => {params['evaInfoId'] = evaInfoId})()
            this.confirmLoading = true
            if (!summary) { // 不是从汇总按钮进入的
                this.regulationNameMap = {}
                getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/querySupplierEvaGroupResultByGroupId', params, {headers: this.headParams}).then(res => {
                    if (res.code == 200) {
                        const {result = {} } = res
                        const {evaResultListMap = {}, evaluationGroupVO={}, evaluationPrinciples = {}} = result
                        const {tenderEvaluationTemplateRegulationInfoList=[]} = evaluationGroupVO
                        this.evaluationGroup = evaluationGroupVO
                        this.scoreRangeList = tenderEvaluationTemplateRegulationInfoList
                        this.resultData = result
                        this.evaluationPrinciples = evaluationPrinciples
                        if (evaResultListMap) {
                            this.reviewList = Object.keys(evaResultListMap).length > 0 ? evaResultListMap[Object.keys(evaResultListMap)[0]] : []
                        }
                        if (tenderEvaluationTemplateRegulationInfoList?.length > 0) {
                            for (let k of tenderEvaluationTemplateRegulationInfoList) {
                                this.regulationNameMap[k.id] = k.regulationDesc
                            }
                        }
                    }else{
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            } else { // 查询汇总记录数据
                getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/querySummaryEvaGroupResultByGroupId', params, {headers: this.headParams}).then(res => {
                    if (res.code == 200) {
                        const {result = {} } = res
                        const {resultList = {}, supplierList=[], evaluationPrinciples={}, evaluationGroup: {tenderEvaluationTemplateRegulationInfoList = []}} = result
                        this.evaluationGroup = result.evaluationGroup
                        this.supplierList = [...supplierList]
                        supplierList.forEach(supplier => {
                            supplier.supplierAccount = `${supplier.supplierAccount}_totalScope`
                        })
                        this.evaluationPrinciples = evaluationPrinciples
                        this.resultListSummary = resultList
                        this.supplierListSummary = supplierList
                        if (tenderEvaluationTemplateRegulationInfoList?.length > 0) {
                            for (let k of tenderEvaluationTemplateRegulationInfoList) {
                                this.regulationNameMap[k.id] = k.regulationDesc
                            }
                            console.log('this.regulationNameMap', this.regulationNameMap)
                        }
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            }
        },
        // 动态计算合计分数
        changeScore (supplierAccount, review, scoreRange, event) {
            let {$event, value} = event
            // 判断参数值的范围是否合规
            let bool = false
            scoreRange && (scoreRange => {
                let scoreList = scoreRange.split(',')
                for (let i = 0; i < scoreList.length; i++) {
                    if (scoreList[i].indexOf('~') != -1) {
                        let scores = scoreList[i].split('~')
                        const MIN = scores[0], MAX = scores[1]
                        if (parseFloat(MIN) <= parseFloat(value) && parseFloat(value) <= parseFloat(MAX)) {
                            bool = true
                            break
                        }
                    } else if(value == scoreList[i]) {
                        bool = true
                        break
                    }
                }
            })(scoreRange)

            if (value && bool) {
                this.flag = true
                let {evaResultListMap = {}, supplierList=[]} = this.resultData
                let totalScore = 0
                evaResultListMap[supplierAccount] && evaResultListMap[supplierAccount].forEach(eva => {
                    const score = eva.score ? parseFloat(eva.score) : 0
                    totalScore = add(totalScore, score)
                })
                supplierList.forEach(supplier => {
                    if (supplier.supplierAccount == supplierAccount) {
                        supplier.totalScore = totalScore
                    }
                })
            } else {
                this.$message.warning(`分值输入有误，区间为${scoreRange}`)
                review.score = ''
                this.flag = false
                let {evaResultListMap = {}, supplierList=[]} = this.resultData
                let totalScore = 0
                evaResultListMap[supplierAccount] && evaResultListMap[supplierAccount].forEach(eva => {
                    // totalScore += eva.score ? parseFloat(eva.score) : 0
                    const score = eva.score ? parseFloat(eva.score) : 0
                    totalScore = add(totalScore, score)
                })
                supplierList.forEach(supplier => {
                    if (supplier.supplierAccount == supplierAccount) {
                        supplier.totalScore = totalScore == 0 ? '' : totalScore
                    }
                })
                // $event.focus()
            }
            
        },
        // 保存数据
        saveData (path=null) {
            let valBoolean = false
            this.$refs.input && (() => {
                const inputObjs = this.$refs.input
                inputObjs.forEach(item => {
                    if (!item.inputValue) {
                        valBoolean = true
                    }
                })
                
            })()
            if (valBoolean) {
                // this.$message.warning('存在未完成评审的条例，不允许提交！')
                this.tipStatus = true
                return false
            }
            if(!this.flag){
                this.$message.warning('分值输入有误,请修改')
                return false
            }
            this.resultData.evaResultListMap && (() => {
                const evaObj = this.resultData.evaResultListMap
                Object.keys(evaObj).forEach(key => {
                    evaObj[key].forEach(item => {
                        item.judgesTaskItemId = this.currentRow.id
                        item.judgesTaskHeadId = this.currentRow.judgesTaskHeadId
                    })
                })
            })()
            this.resultData.supplierList && this.resultData.supplierList.forEach(data => {
                data['judgesTaskItemId'] = this.currentRow.id || ''
            })
            this.confirmLoading = true
            let url = path ? path : '/tender/evaluation/purchaseTenderProjectBidEvaHead/saveSupplierEvaGroupResult'
            postAction(url, this.resultData, {headers: this.headParams}).then(res => {
                this.confirmLoading = false
                if (res.code == 200) {
                    this.$message.success(res.message)
                    if (path) {
                        this.goBack()
                    }
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        publishData () {
            let path = '/tender/evaluation/purchaseTenderProjectBidEvaHead/publishSupplierEvaGroupResult'
            this.saveData(path)
        },
        // 返回
        goBack () {
            this.$parent.scoringStatus = false
            
        },
        // 评审意见
        showLeaderOpinion (item) {
            this.currentItem = item
            this.$refs.LeaderOpinionModal.open({data: item, title: item.regulationName, opinionCode: 'remark'})
        },
        LeaderOpinionSuccess (data) {
            this.$set(this.currentItem, 'remark', data.remark)
        }
    }
}
</script>

<style lang="less" scoped>
  .ReviewItems {
    height: 100%;
    background-color: #fff;
    padding: 15px 10px;
  }
  .review-items-top {
    line-height: 30px;

    .ant-col-12:nth-child(1) {
      font-size: 16px;
      font-weight: 600;
    }
    .desc {
        font-size: 13px;
        margin-left: 15px;
        color: #3a6bff;
      }
  }
  .review-items-grid {
    margin-top: 15px;

    .grid-ul {
      display: flex;

      ul:nth-child(1) {
        width: 80px;
      }
      ul {
        text-align: center;

        li:nth-child(1) {
          background-color: #e4e4e4;
        }
        li {
          border: 1px solid #f0eeee;
          height: 40px;
          line-height: 40px;

          :deep(.vxe-input--extra-suffix){
            top: 0.4em;
          }
          :deep(.vxe-input--suffix){
            top: 0.4em;
          }
          :deep(.warning-tip){
            input {
              border: 1px solid red;
            }
          }
        }
      }
      .ul {
        flex-grow: 1;
      }
      
    }
  }
 
</style>
