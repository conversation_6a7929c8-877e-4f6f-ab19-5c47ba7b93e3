<template>
  <div class="cardStyleListLayout">
    <div class="container">
      <div
        class="sidebar"
        v-if="showSidebar">
        <sidebar
          :tabsList="tabsList"
          :activeKey="activeKey"
          v-on="$listeners"
        />
      </div>
      <div :class="['main', showSidebar ? 'marginLeft10' : '']">
        <better-scroll-layout
          v-if="tableData.length"
          :titleProp="titleProp"
          :columns="columns"
          :tableData="tableData"
          :buttons="buttons"
          :pagerConfig="pagerConfig"
          v-on="$listeners"
        />
        <div
          class="emptyWrapper"
          v-else>
          <m-empty
            :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
        </div>
        <div class="pager">
          <vxe-pager
            v-show="tableData.length"
            :current-page.sync="tablePage.currentPage"
            :page-size.sync="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="tablePage.layouts"
            v-on="$listeners"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const TITLEPROP = 'taskTitle'

import sidebar from './sidebar.vue'
import betterScrollLayourt from './better-scroll-layout.vue'

export default {
    name: 'CardStyleListLayout',
    components: {
        'sidebar': sidebar,
        'better-scroll-layout': betterScrollLayourt
    },
    props: {
        titleProp: {
            type: String,
            default: TITLEPROP
        },
        tabsList: {
            type: Array,
            default () {
                return []
            }
        },
        activeKey: {
            type: [Number, String],
            default: 0
        },
        columns: {
            type: Array,
            default () {
                return []
            }
        },
        tableData: {
            type: Array,
            default () {
                return []
            }
        },
        buttons: {
            type: Array,
            default () {
                return []
            }
        },
        pagerConfig: {
            type: Object,
            default () {
                return {}
            }
        },
        showSidebar: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 500,
                pageSizes: [20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total']
            }
        }
    },
    watch: {
        pagerConfig: {
            deep: true,
            immediate: true,
            handler (obj) {
                this.tablePage.total = obj.total || 0
                this.tablePage.currentPage = obj.currentPage || 1
                this.tablePage.pageSize = obj.pageSize || 20
            }
        }
    }
}
</script>

<style lang="less" scoped>
:deep(.vxe-pager) {
  background: transparent;
}
.cardStyleListLayout {
	padding: 0 8px;
	.container {
		display: flex;
	}
	.sidebar {
		overflow-x: hidden;
		overflow-y: auto;
		flex: 0 0 200px;
		border-radius: 8px;
		min-height: calc(100vh - 174px);
		max-height: calc(100vh - 174px);
		background: rgb(255 255 255);
	}
	.main {
		flex: 1;
	}
	.marginLeft10 {
		margin-left: 10px;
	}
	.emptyWrapper {
		position: relative;
		border-radius: 8px;
		min-height: calc(100vh - 174px);
		background: #fff;
	}
}
</style>
