<template>
  <div class="els-page-comtainer"> 
    <tabs-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url" 
      @goBack="goBack"/>
  </div>
</template>

<script>
import { httpAction } from '@/api/manage'
import { editPageMixin } from '@comp/template/tabsCollapse/tabsCollapseMixin'

export default {
    name: 'DeliveryNoticeSaleHeadModal',
    mixins: [editPageMixin],
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryNotice`, '送货通知单'),
            confirmLoading: false,
            disabledFlag: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowId: 0,
            flowView: false,
            pageData: {
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            form: {
                                noticeNumber: '',
                                supplierId: '',
                                supplierCode: '',
                                supplierName: '',
                                companyCode: '',
                                companyName: '',
                                purchaseGroupCode: '',
                                purchaseGroupName: '',
                                purchaseOrgCode: '',
                                purchaseOrgName: '',
                                noticeType: '0',
                                noticeStatus: '0',
                                auditStatus: '0',
                                noticeDesc: '',
                                receiveAddress: '',
                                receiveContact: '',
                                receivePhone: '',
                                purchasePrincipal: '',
                                supplierPrincipal: '',
                                purchaseRemark: '',
                                supplierRemark: ''
                            },
                            list: [
                                {
                                    type: 'collapse',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                                    formList: [
                                        {
                                            type: 'input', 
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notificationNumber`, '通知单号'),
                                            fieldName: 'noticeNumber', 
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_systemGeneration`, '系统生成'),
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                                            fieldName: 'noticeStatus_dictText',
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm65d_auditStatus`, '审批状态'),
                                            fieldName: 'auditStatus_dictText',
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                                            fieldName: 'supplierName',
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaDesc`, '单据描述'),
                                            fieldName: 'noticeDesc',
                                            placeholder: '',
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司'),
                                            fieldName: 'companyCode',
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectCompanyTips`, '请选择公司'),
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeadc98_purchaseOrgCode`, '采购组织'),
                                            fieldName: 'purchaseOrgCode',
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectProcurementOrg`, '请选择采购组织'),
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseGroupCode`, '采购组'),
                                            fieldName: 'purchaseGroupCode',
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectProcurementSection`, '请选择采购组'),
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_recommdRemarks`, '需方备注'),
                                            fieldName: 'purchaseRemark',
                                            placeholder: '',
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRemarks`, '供方备注'),
                                            fieldName: 'supplierRemark'
                                        }
                                    ]
                                }
                            ],
                            validRules: {
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                        content: {
                            type: 'table',
                            ref: 'deliveryNoticeSaleItemList',
                            columns: [
                                { 
                                    type: 'checkbox', width: 40 
                                },
                                { 
                                    type: 'seq', width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                                    field: 'orderNumber',
                                    width: 130
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                    field: 'orderItemNumber',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialCode',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                    field: 'materialDesc',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
                                    field: 'materialSpec',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                                    field: 'materialGroupCode',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderDemandDate`, '订单需求日期'),
                                    field: 'orderRequireDate',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'),
                                    field: 'quantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityUnit`, '数量单位'),
                                    field: 'quantityUnit',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
                                    field: 'taxCode',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'),
                                    field: 'taxRate',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '净价'),
                                    field: 'netPrice',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'),
                                    field: 'price',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_demandDate`, '需求日期'),
                                    field: 'requireDate',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量'),
                                    field: 'requireQuantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_recommdRemarks`, '需方备注'),
                                    field: 'purchaseRemark',
                                    width: 220
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_replyDate`, '回复交期'),
                                    field: 'replyDate',
                                    width: 120,
                                    editRender: {name: 'mDatePicker'}
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_replyQuantity`, '回复数量'),
                                    field: 'replyQuantity',
                                    width: 120,
                                    editRender: {name: 'AInputNumber'}
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRemarks`, '供方备注'),
                                    field: 'supplierRemark',
                                    width: 220,
                                    editRender: {name: 'AInput'}
                                }
                            ],
                            validRules: {
                                replyDate: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_replyDeliveryCannotbBeBlank`, '回复交期不能为空') }
                                ],
                                replyQuantity: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_replyNumCannotbBeBlank`, '回复数量不能为空') }
                                ]
                            }
                        }
                    }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', clickFn: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', clickFn: this.publish },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack }
                ]
            },
            url: {
                publish: '/delivery/deliveryNoticeSaleHead/reply',
                detail: '/delivery/deliveryNoticeSaleHead/queryDetailById'
            }
        }
    },
    mounted () {
       
    },
    created (){
      
    },
    methods: {
        goBack () {
            this.$emit('hide')
        },
        publish (){
            let itemList = this.$refs.editPage.getParamsData().deliveryNoticeSaleItemList
            for(let i in itemList){
                let item = itemList[i]
                if(item.replyQuantity > item.requireQuantity){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_replyQuantityCannotGreaterThanDemandQuantity`, '回复数量不能大于需求数量！请重新输入回复数量。'))
                    item.replyQuantity = ''
                    return
                }
            }
            this.$refs.editPage.$refs.baseForm[0].validate(valid => {
                this.$refs.editPage.$refs.deliveryNoticeSaleItemList[0].validate(err => {
                    if(valid && !err) {
                        let that = this
                        this.$confirm({
                            title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_confirmRelease`, '确认发布'),
                            content: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_replyDeliveryDateReplyQuantityDemand`, '默认回复交期及回复数量默认等于需求日期、需求数量，是否确认发布?'),
                            onOk: function () {
                                that.postData()
                            }
                        })
                    }
                })
            })
        },
        postData (){
            let params = this.$refs.editPage.getParamsData()
            this.$refs.editPage.confirmLoading = true
            httpAction(this.url.publish, params, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$emit('ok')
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        }
    }
}
</script>
