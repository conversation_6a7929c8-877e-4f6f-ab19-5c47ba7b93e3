<template>
  <div>
    <titleTrtl>
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_baseInfo`, '基本信息') }}</span>
    </titleTrtl>
    <Dataform
      ref="dataform"
      :formData="fromSourceData"
      :validateRules="validateRules"
      :pageStatus="pageStatus"
      :fields="fields" />
  </div>
</template>
<script>
import Dataform from '@views/srm/bidding_new/BiddingHall/components/Dataform.vue'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import { USER_INFO } from '@/store/mutation-types'
import REGEXP from '@/utils/regexp'

export default {
    mixins: [baseMixins],
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        fromSourceData: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    computed: {
        subpackage (){
            return this.currentSubPackage()
        }
    },
    data () {
        let checkContactsPhone = (rule, cellValue, callback) => {
            if (cellValue === '') {
                callback(new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNyo_f6dbcb13`, '请输入号码')))
            } else {
                let reg = REGEXP.mobile
                if (!reg.test(cellValue)) {
                    callback(new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNiRyo_b9c72f7e`, '请输入正确号码')))
                }
                callback()
            }
        }
        return {
            validateRules: {
                contactsPhone: [
                    { validator: checkContactsPhone, trigger: 'change' }
                ]
            },
            fields: [
                {
                    fieldType: 'input',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ULMAo_db9b3bb5`, '评委会编码'),
                    field: 'juryNumber',
                    disabled: true,
                    required: '0'
                },
                {
                    fieldType: 'input',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ULMRL_db96078d`, '评委会名称'),
                    field: 'juryName',
                    required: '1',
                    placeholder: ''
                },
                {
                    fieldType: 'select',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_ULMAc_db9aca7a`, '评委会类型'),
                    field: 'juryType',
                    dictCode: 'tenderJuryType',
                    defaultValue: '',
                    disabled: true,
                    required: '1',
                    placeholder: ''
                },
                {
                    fieldType: 'select',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VMCh_2e29bcb5`, '抽取方法'),
                    field: 'samplingWay',
                    dictCode: 'tenderSamplingWay',
                    defaultValue: '0',
                    required: '1',
                    placeholder: '',
                    bindFunction: function (value, item, fields, that){
                        //线下，则禁用确认方式并且将确认方式值清零
                        if(value == '0'){
                            // fields.forEach((items, index)=>{
                            //     console.log(items)
                            //     if(items.field == 'confirmWay'){
                            //         // item.required = '0'
                            //         // item.disabled = true
                            //         // console.log(that.formData.confirmWay = '')
                            //         that.formData.confirmWay = ''
                            //         delete fields[index]
                            //     }
                            // })
                            that.formData.confirmWay = ''
                            if(fields.length == 11 && fields[10].field == 'confirmWay'){
                                fields.pop()
                            }
                            
                        }else{
                            // fields.forEach(item=>{
                            //     if(item.field == 'confirmWay'){
                            //         item.required = '1'
                            //         item.disabled = false
                            //         // console.log(that.formData.confirmWay = '0')
                            //         that.formData.confirmWay = '0'
                            //     }
                            // })
                            fields.push(
                                {
                                    fieldType: 'select',
                                    title: that.$srmI18n(`${that.$getLangAccount()}#i18n_field_RLCK_38d76bec`, '确认方式'),
                                    field: 'confirmWay',
                                    dictCode: 'tenderConfirmWay',
                                    defaultValue: '0',
                                    required: '1',
                                    placeholder: '',
                                    disabled: false
                                }
                            )
                            that.formData.confirmWay = '0'
                        }
                        that.initValidateRules()
                    }
                },
                {
                    sortOrder: '8',
                    fieldType: 'input',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                    field: 'contacts',
                    required: '1',
                    defaultValue: '',
                    placeholder: ''
                },
                {
                    fieldType: 'input',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系人电话'),
                    field: 'contactsPhone',
                    defaultValue: '',
                    required: '1',
                    placeholder: ''
                },
                {
                    fieldType: 'input',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBnR_411a1613`, '评标地址'),
                    field: 'bidEvaAddress',
                    required: '1',
                    placeholder: ''
                },
                {
                    fieldType: 'number',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YBLoBLW_62e29749`, '招标人代表人数'),
                    field: 'bidRepresentTotal',
                    required: '1',
                    placeholder: ''
                },
                {
                    fieldType: 'number',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBsuLW_5ee3047c`, '评标专家人数'),
                    field: 'evaExpertTotal',
                    required: '1',
                    placeholder: ''
                },
                {
                    fieldType: 'number',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBkLW_e249fbce`, '评标总人数'),
                    field: 'countTotal',
                    disabled: true,
                    required: '0'
                }
                // {
                //     fieldType: 'select',
                //     title: '确认方式',
                //     field: 'confirmWay',
                //     dictCode: 'tenderConfirmWay',
                //     // defaultValue: '0',
                //     required: '0',
                //     placeholder: '',
                //     disabled: true
                // }
            ]
        }
    },
    watch: {
    },
    components: {
        titleTrtl,
        Dataform
    },
    methods: {
        externalAllData () {
            return this.$refs.dataform.externalAllData()
        },
        getValidatePromise () {
            return this.$refs.dataform.getValidatePromise()
        }
    },
    created () {
        let samplingWay = this.fromSourceData.samplingWay ? this.fromSourceData.samplingWay : '0'
        if(samplingWay == '1'){
            this.fields.push(
                {
                    fieldType: 'select',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLCK_38d76bec`, '确认方式'),
                    field: 'confirmWay',
                    dictCode: 'tenderConfirmWay',
                    defaultValue: '0',
                    required: '1',
                    placeholder: '',
                    disabled: false
                }
            )
        }
        this.fields.forEach(item=>{
            // if(item.field == 'confirmWay'){
            //     // 判断抽取方法是否线下
            //     item.disabled = (samplingWay == '0')
            //     item.required = samplingWay == '0' ? '0' : '1'
            //     item.defaultValue = samplingWay == '0' ? '' : '0'
            //     console.log(item)
                
            // }
            if(item.field == 'juryType'){
                console.log(this.currentNode())
                item.defaultValue = this.currentNode().extend.checkType
            }
            if(item.field == 'contacts'){
                item.defaultValue = this.$ls.get(USER_INFO).realname
            }
            if(item.field == 'contactsPhone'){
                item.defaultValue = this.$ls.get(USER_INFO).phone
            }
        })
        // let {elsAccount, subAccount} = this.$ls.get(USER_INFO)
        console.log(this.$ls.get(USER_INFO))

    }
}
</script>
