import { deepClone } from './index'
import store from '@/store/index.js'
import { srmI18n, getLangAccount } from '@/utils/util'

// console.log(this.$store)
const questionType = store.state.formDesigner.questionType // 0普通问卷 1试卷问卷
export const controlType = [
    {
        name: 'form',
        title: srmI18n(`${getLangAccount()}#i18n_title_type`, '类型')
    }
]
export const controls = [
    {
        name: srmI18n(`${getLangAccount()}#i18n_title_input`, '输入框'),
        type: 'input',
        category: controlType[0],
        icon: 'plus',
        attr: {
            id: '',
            label: srmI18n(`${getLangAccount()}#i18n_field_titel`, '标题'),
            placeholder: '',
            initialValue: '',
            rules: [],
            layout: 24
        }
    },
    {
        name: srmI18n(`${getLangAccount()}#i18n_title_select`, '选择器'),
        type: 'select',
        category: controlType[0],
        icon: 'plus',
        attr: {
            id: ' ',
            label: srmI18n(`${getLangAccount()}#i18n_field_titel`, '标题'),
            placeholder: '',
            initialValue: '',
            rules: [],
            layout: 24,
            options: [
                { label: srmI18n(`${getLangAccount()}#i18n_title_option1`, '选项1'), value: '0' },
                { label: srmI18n(`${getLangAccount()}#i18n_title_option2`, '选项2'), value: '1' }
            ]
        }
    },
    {
        name: srmI18n(`${getLangAccount()}#i18n_title_score`, '评分'),
        type: 'score',
        category: controlType[0],
        icon: 'plus',
        attr: {
            id: ' ',
            label: srmI18n(`${getLangAccount()}#i18n_field_titel`, '标题'),
            placeholder: '',
            initialValue: 1,
            rules: [],
            layout: 24
            // options: [
            //     { label: '选项1', value: '0' },
            //     { label: '选项2', value: '1' }
            // ]
        }
    },
    // {
    //     name: '数字输入框',
    //     type: 'inputNumber',
    //     category: controlType[0],
    //     icon: 'plus',
    //     attr: {
    //         id: '',
    //         label: '数字输入框',
    //         placeholder: '请输入数字',
    //         initialValue: '',
    //         rules: [],
    //         layout: 4
    //     }
    // },
    {
        name: srmI18n(`${getLangAccount()}#i18n_title_radio`, '单选框'),
        type: 'radio',
        category: controlType[0],
        icon: 'plus',
        attr: {
            id: '',
            label: srmI18n(`${getLangAccount()}#i18n_field_titel`, '标题'),
            initialValue: '',
            rules: [],
            layout: 24,
            options: [
                { label: srmI18n(`${getLangAccount()}#i18n_title_option1`, '选项1'), value: '0' },
                { label: srmI18n(`${getLangAccount()}#i18n_title_option2`, '选项2'), value: '1' }
            ]
        }
    },
    {
        name: srmI18n(`${getLangAccount()}#i18n_title_checkbox`, '多选框'),
        type: 'checkbox',
        category: controlType[0],
        icon: 'plus',
        attr: {
            id: '',
            label: srmI18n(`${getLangAccount()}#i18n_field_titel`, '标题'),
            initialValue: [],
            rules: [],
            layout: 24,
            options: [
                { label: srmI18n(`${getLangAccount()}#i18n_title_option1`, '选项1'), value: '0' },
                { label: srmI18n(`${getLangAccount()}#i18n_title_option2`, '选项2'), value: '1' }
            ]
        }
    },
    {
        name: srmI18n(`${getLangAccount()}#i18n_field_Attachment`, '附件'),
        type: 'file',
        category: controlType[0],
        icon: 'plus',
        attr: {
            id: '',
            label: srmI18n(`${getLangAccount()}#i18n_field_titel`, '标题'),
            placeholder: '',
            initialValue: '',
            rules: [],
            layout: 24,
            name: 'file',
            action: '/els/attachment/purchaseAttachment/upload',
            tokenHeader: {},
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            data: {
                businessType: 'RFI',
                headId: ''
            },
            itemInfo: []
        }
    }
    // {
    //     name: '栅格',
    //     type: 'grid',
    //     category: controlType[1],
    //     icon: 'plus',
    //     attr: {
    //         id: '',
    //         count: 2,
    //         options: [
    //             { label: '2', value: 2 },
    //             { label: '3', value: 3 },
    //             { label: '4', value: 4 },
    //             { label: '6', value: 6 }
    //         ]
    //     },
    //     columns: [
    //         {
    //             span: 12,
    //             children: []
    //         },
    //         {
    //             span: 12,
    //             children: []
    //         }
    //     ]
    // }
]
// 新类型必须在这里添加
export const renderRowType = {
    formItem: ['input', 'inputNumber', 'select', 'radio', 'checkbox', 'grid', 'score', 'file']
}

export function getControlList () {
    const controlData = []
    for (let i = 0; i < controlType.length; i++) {
        const topLevel = deepClone(controlType[i])
        const list = []
        for (let n = 0; n < controls.length; n++) {
            if (controlType[i].name === controls[n].category.name) {
                console.log(controls[n])
                if (questionType == 1) { // 试卷类型
                    controls[n].attr.options && controls[n].attr.options.forEach((rs, idx) => {
                        rs.rightAnswer = '0'
                        if (idx == 0) {
                            rs.rightAnswer = '1'
                        }
                    })
                }
                list.push(controls[n])
            }
        }
        topLevel.list = list
        controlData.push(topLevel)
    }
    return controlData
}
