<template>
  <a-popover :placement="placement">
    <template slot="content">
      <div
        class="popover-box"
        v-html="content"></div>
    </template>
    <template
      v-if="title"
      slot="title">
      <span>{{ title }}</span>
    </template>
    <div
      v-html="content"
      class="content"></div>
  </a-popover>
</template>

<script>
export default {
    props: {
        title: {
            type: String,
            default: ''
        },
        placement: {
            type: String,
            default: 'top'
        },
        content: {
            type: String,
            default: ''
        }
    }, filters: {
      
        stripHTML (val){
          
            let reTag = /<(?:.|\s)*?>/g    
            return val.replace(reTag, '')  
        }

    }
}
</script>
<style lang="less" scoped>
.popover-box {
  max-height: 300px;
  overflow-y: scroll;
}
</style>
