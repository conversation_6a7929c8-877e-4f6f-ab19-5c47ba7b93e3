import { RouteView } from '@/components/layouts'

const calibrationRouter = {
    path: '/hall/calibration',
    name: 'calibration',
    meta: {
        title: '定标',
        titleI18nKey: 'i18n_title_calibration',
        icon: 'icon-111-01',
        keepAlive: false
    },
    component: RouteView,
    children: [
        {
            path: '/hall/calibration/instruct',
            name: 'instruct',
            meta: {
                title: '定标请示',
                titleI18nKey: 'i18n_menu_IBVK_2b3bb490',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'instruct' */ '@/views/srm/bidding/hall/purchase/calibration/Instruct.vue')
        },
        {
            path: '/hall/calibration/resultNotice',
            name: 'resultNotice',
            meta: {
                title: '中标通知书',
                titleI18nKey: 'i18n_title_bidWinNotice',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'resultNotice' */ '@/views/srm/bidding/hall/purchase/calibration/ResultNotice.vue')
        }
    ]
}

export default calibrationRouter