<template functional>
  <div
    :class="['empty', data.attrs && data.attrs.displayModel !== 'tab' ? 'horizontal' : '']">
    <span class="noMsg">{{ data.attrs.noMsg }}</span>
  </div>
</template>

<style lang="less" scoped>
.empty {
    // position: relative;
    &::before {
        content: '';
        width: 220px;
        height: 130px;
        background: url(~@/assets/img/template/empty.png) no-repeat center top/220px auto;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    span.noMsg {
        color: rgba(69, 79, 89, 0.8);
        font-size: 16px;
        letter-spacing: 1px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translateX(-50%);
        margin-top: 100px;
    }
    &.horizontal {
        span.noMsg {
            transform: translate(-50%, -50%);
            margin-top: 0;
            margin-left: 90px;
            letter-spacing: unset;
        }
    }
}
</style>