import VXETable from 'vxe-table'
import i18n from '../../i18n'

/**
 * 全局默认配置
 */
VXETable.setup({
    // 对组件内置的提示语进行国际化翻译
    i18n: (key, args) => i18n.t(key, args),
    // 可选，对参数中的列头、校验提示..等进行自动翻译（只对支持国际化的有效）
    translate (key, args) {
    // 例如，只翻译 "app." 开头的键值
        if (key && key.indexOf('i18n_') > -1) {
            return i18n.t(key, args)
        }
        return key
    },
    version: 0,
    zIndex: 100,
    authId: 'vcvukwp2vofogoog',
    validToReject: 'obsolete'
})
