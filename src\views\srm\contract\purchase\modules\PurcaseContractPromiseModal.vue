<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :current-edit-row="currentEditRow"
      refresh
      :page-data="pageData"
      :url="url" />
    <!-- 行明细弹出选择框 -->
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
      v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_IZYM_2bed417f`, '履约退回')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_alert_VWNIZYMqd_41ecfe29`, '请输入履约退回备注') "
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {downFile, getAction, httpAction, postAction} from '@/api/manage'
import {EditMixin} from '@comp/template/edit/EditMixin'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'PurcaseContractPromiseModal',
    components: {
        flowViewModal,
        fieldSelectModal
    },
    mixins: [EditMixin],
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            confirmLoading: false,
            okText: '',
            opinion: '',
            auditVisible: false,
            flowId: 0,
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZcVH_519d8b79`, '履约行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchasePromiseItemList',
                        columns: [],
                        buttons: [

                        ]
                    } }
                    /*{ groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCbL_713646eb`, '上传方全称'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'purchaseRequest', callBack: this.uploadCallBack}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }*/

                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), authorityCode: 'contract#purchaseContractHead:edit', type: 'primary', click: this.saveEvent, showCondition: this.showEditConditionBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'), type: 'primary', click: this.submitAudit, showCondition: this.showAuditConditionBtn },

                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZYM_2bed417f`, '履约退回'),
                        type: 'primary',
                        click: this.refund,
                        authorityCode: 'contractPromise#purchaseContractPromise:refund',
                        showCondition: this.showConfirmConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZRL_2bea9b97`, '履约确认'),
                        type: 'primary',
                        click: this.confirm,
                        authorityCode: 'contractPromise#purchaseContractPromise:confirmedSupplier',
                        showCondition: this.showConfirmConditionBtn
                    },

                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', authorityCode: 'contractPromise#purchaseContractPromise:pulish', click: this.publishEvent, showCondition: this.showPublishConditionBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/contract/purchaseContractPromise/add',
                edit: '/contract/purchaseContractPromise/edit',
                detail: '/contract/purchaseContractPromise/queryById',
                public: '/contract/purchaseContractPromise/publishEvent',
                upload: '/attachment/purchaseAttachment/upload',
                import: '/els/base/excelByConfig/importExcel',
                confirm: '/contract/purchaseContractPromise/confirmedSupplier',
                refund: '/contract/purchaseContractPromise/refund',
                submitAudit: '/a1bpmn/audit/api/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_contractPromise_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        goBack () {
            this.$emit('hide')
        },
        loadSuccess () {
            this.pageConfig = getPageConfig() // eslint-disable-line
            this.handlePageData(this.pageConfig)
        },
        afterHandleData (data) {
            if (this.currentEditRow?.sourceType!='item') {
                data.groups.splice(1, 1)
            }
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        showAuditConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if (params.busAccount == params.createAccount) {
                let auditStatus = params.auditStatus
                let audit = params.audit
                if(((audit=='1')&&(auditStatus=='1'||auditStatus=='2'))||audit=='0'){
                    return false
                }else{
                    return true
                }
            }
        },
        showPublishConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if (params.busAccount == params.createAccount) {
                return true
            }else {
                return false
            }
        },
        showEditConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if (params.busAccount == params.createAccount) {
                return true
            }else {
                return false
            }
        },
        showConfirmConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if ((params.auditStatus==0||params.auditStatus==4)&&params.promiseStatus=='1'&&params.busAccount != params.createAccount) {
                return true
            } else {
                return false
            }
        },
        saveEvent (){
            this.$refs.editPage.postData()
        },
        showFlow (){
            this.flowId = this.$refs.editPage.form.flowId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        getDataInfo (id){
            return new Promise((resolve, reject) => {
                let data = {
                    id: id
                }
                getAction('/contract/purchaseContractPromise/queryById', data).then(res => {
                    if (res.success) {
                        resolve(res.result)
                    }else{
                        this.$message.error(res.message)
                        this.$refs.editPage.confirmLoading = false
                    }
                })
            })
        },
        refund (){
            debugger
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZYM_2bed417f`, '履约退回')
        },
        handleOk (){
            this.getData(this.url.refund, {refundRemark: this.opinion})
        },
        confirm (){
            let that = this
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IZRL_2bea9b97`, '履约确认')
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mtkPRLtFWKQRL_647bb8d7`, '此操作将确认单据，是否确认?'),
                onOk: function () {
                    postAction(that.url.confirm, params).then(res => {
                        if(res.success) {
                            that.form = res.result
                            that.auditVisible = false
                            that.opinion = ''
                            that.$message.success(res.message)
                            // that.goBack()
                            that.$parent.submitCallBack()
                            that.init()
                        }else {
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.auditVisible = false
                        that.init()
                        that.$parent.modalFormOk()
                    })
                }
            })
        },
        getData (url, param){
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if(!param){
                param = {}
            }
            param['id'] = params.id
            getAction(url, param).then(res => {
                if(res.success) {
                    this.form = res.result
                    this.auditVisible = false
                    this.opinion = ''
                    this.$message.success(res.message)
                    this.goBack
                }else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.auditVisible = false
                this.init()
                this.$parent.modalFormOk()
            })
        },
        handleApproval (formData){
            let that = this
            let auditSubject = '履约单号'+'：'+formData.promiseNumber
            that.$refs.editPage.confirmLoading = true
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterSubmittingApprovalItcannotIsurSubmitApproval?`, '提交审批后将不能修改，是否确认提交审批?'),
                onCancel: ()=> {
                    that.$refs.editPage.confirmLoading = false
                },
                onOk: function () {
                    let param = {}
                    param['businessId'] = formData.id
                    param['rootProcessInstanceId'] = formData.flowId
                    param['businessType'] = 'contractPromise'
                    param['auditSubject'] =  auditSubject
                    param['params'] = JSON.stringify(formData)
                    httpAction(that.url.submitAudit, param, 'post').then((res) => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.$parent.submitCallBack(formData)
                            that.init()
                        } else {
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.$refs.editPage.confirmLoading = false
                    })
                }
            })
        },
        submitAudit () {
            const that = this
            const params = that.$refs.editPage.getPageData()
            const fn = (url, params, vm) => {
                postAction(that.url.edit, params ).then(res =>  {
                    if (res.success) {
                        that.getDataInfo(params.id).then((res)=>{
                            that.handleApproval(res)
                        })
                    }
                })
            }
            this.$refs.editPage.handValidate(that.url.edit, params, fn)
        },
        publishEvent () {
            let params = this.$refs.editPage.getPageData()
            if (params.audit == '1') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_APtFTPUzeRShxW_f2af42da`, '当前单据需要审批通过后发布！'))
                return
            } else {
                this.$refs.editPage.handleSend()
            }
        }
    }
}
</script>
