<template>
  <a-skeleton
    active
    :loading="loading"
    :paragraph="{rows: 17}"
  >
    <a-card :bordered="false">
      <a-alert
        type="info"
        :show-icon="true"
      >
        <div slot="message">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_lastUpdated`, '上次更新时间') }}：{{ this.time }}
          <a-divider type="vertical" />
          <a @click="handleClickUpdate">{{ $srmI18n(`${$getLangAccount()}#i18n_title_nowUpdated`, '立即更新') }}</a>
        </div>
      </a-alert>

      <a-table
        row-key="id"
        size="middle"
        :columns="columns"
        :data-source="dataSource"
        :pagination="false"
        :loading="tableLoading"
        style="margin-top: 20px;"
      >
        <template
          slot="param"
          slot-scope="text, record"
        >
          <a-tag :color="textInfo[record.param].color">
            {{ text }}
          </a-tag>
        </template>

        <template
          slot="text"
          slot-scope="text, record"
        >
          {{ textInfo[record.param].text }}
        </template>

        <template
          slot="value"
          slot-scope="text, record"
        >
          {{ text }} {{ textInfo[record.param].unit }}
        </template>
      </a-table>
    </a-card>
  </a-skeleton>
</template>
<script>
import moment from 'moment'
import { getAction } from '@/api/manage'
import { Tag, Alert, Skeleton } from 'ant-design-vue'
import { srmI18n, getLangAccount } from '@/utils/util'

moment.locale('zh-cn')

export default {
    components: {
        ATag: Tag,
        AAlert: Alert,
        ASkeleton: Skeleton
    },
    data () {
        return {
            time: '',
            loading: true,
            tableLoading: true,
            columns: [{
                title: srmI18n(`${getLangAccount()}#i18n_title_params`, '参数'),
                width: '30%',
                dataIndex: 'param',
                scopedSlots: { customRender: 'param' }
            }, {
                title: srmI18n(`${getLangAccount()}#i18n_title_describe`, '描述'),
                width: '40%',
                dataIndex: 'text',
                scopedSlots: { customRender: 'text' }
            }, {
                title: srmI18n(`${getLangAccount()}#i18n_title_currentValue`, '当前值'),
                width: '30%',
                dataIndex: 'value',
                scopedSlots: { customRender: 'value' }
            }],
            dataSource: [],
            // 列表通过 textInfo 渲染出颜色、描述和单位
            textInfo: {
                'system.cpu.count': { color: 'green', text: srmI18n(`${getLangAccount()}#i18n_title_cpuCount`, 'CPU 数量'), unit: srmI18n(`${getLangAccount()}#i18n_title_nucleus`, '核') },
                'system.cpu.usage': { color: 'green', text: srmI18n(`${getLangAccount()}#i18n_title_cpuUsage`, '系统 CPU 使用率'), unit: '%' },
                'process.start.time': { color: 'purple', text: srmI18n(`${getLangAccount()}#i18n_title_cpuStartTime`, '应用启动时间点'), unit: '' },
                'process.uptime': { color: 'purple', text: srmI18n(`${getLangAccount()}#i18n_title_cpuUpTime`, '应用已运行时间'), unit: srmI18n(`${getLangAccount()}#i18n_title_second`, '秒') },
                'process.cpu.usage': { color: 'purple', text: srmI18n(`${getLangAccount()}#i18n_title_applicationCpuUsage`, '当前应用 CPU 使用率'), unit: '%' },
                'git.branch': { color: 'green', text: srmI18n(`${getLangAccount()}#i18n_title_gitBranch`, 'git 分支'), unit: '' },
                'git.abbrev': { color: 'green', text: srmI18n(`${getLangAccount()}#i18n_title_gitVersion`, 'git 版本号'), unit: '' },
                'git.message': { color: 'green', text: srmI18n(`${getLangAccount()}#i18n_title_gitRecentlySubmitInfo`, 'git 最近提交信息'), unit: '' },
                'git.commitTime': { color: 'green', text: srmI18n(`${getLangAccount()}#i18n_title_gitRecentlySubmitTime`, 'git 最近提交时间'), unit: '' },
                'flowable.version': { color: 'blue', text: srmI18n(`${getLangAccount()}#i18n_title_flowableVersion`, 'flowable 版本号'), unit: '' }
            },
            // 当一条记录中需要取出多条数据的时候需要配置该字段
            moreInfo: {}
        }
    },
    mounted () {
        this.loadTomcatInfo()
    },
    methods: {

        handleClickUpdate () {
            this.loadTomcatInfo()
        },

        loadTomcatInfo () {
            this.tableLoading = true
            this.time = moment().format('YYYY年MM月DD日 HH时mm分ss秒')
            Promise.all([
                getAction('actuator/metrics/system.cpu.count'),
                getAction('actuator/metrics/system.cpu.usage'),
                getAction('actuator/metrics/process.start.time'),
                getAction('actuator/metrics/process.uptime'),
                getAction('actuator/info'),
                getAction('actuator/metrics/process.cpu.usage')
            ]).then((res) => {
                let info = []
                res.forEach((value, id) => {
                    if (value.flowable) {
                        info.push({ param: 'flowable.version', text: 'false value', value: value.flowable.version })
                    }
                    if (value.git) {
                        info.push({ param: 'git.branch', text: 'false value', value: value.git.branch })
                        info.push({ param: 'git.abbrev', text: 'false value', value: value.git.commit.id.abbrev })
                        info.push({ param: 'git.message', text: 'false value', value: value.git.commit.message.full })
                        info.push({ param: 'git.commitTime', text: 'false value', value: value.git.commit.time, Date })
                        
                    } else {
                        let more = this.moreInfo[value.name]
                        if (!(more instanceof Array)) {
                            more = ['']
                        }
                        more.forEach((item, idx) => {
                            let param = value.name + item
                            let val = value.measurements[idx].value
                            if (param === 'system.cpu.usage' || param === 'process.cpu.usage') {
                                val = this.convert(val, Number)
                            }
                            if (param === 'process.start.time') {
                                val = this.convert(val, Date)
                            }
                            info.push({ id: param + id, param, text: 'false value', value: val })
                        })
                    }
                    
                })
                this.dataSource = info
            }).catch((e) => {
                console.error(e)
                this.$message.error(srmI18n(`${getLangAccount()}#i18n_title_feactServerError`, '获取服务器信息失败'))
            }).finally(() => {
                this.loading = false
                this.tableLoading = false
            })
        },

        convert (value, type) {
            if (type === Number) {
                return Number(value * 100).toFixed(2)
            } else if (type === Date) {
                return moment(value * 1000).format('YYYY-MM-DD HH:mm:ss')
            }
            return value
        }
    }
}
</script>
<style></style>
