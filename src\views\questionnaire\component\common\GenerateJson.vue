<template>
  <span>
    <a
      href="#"
      style="margin-right: 20px;"
      @click="visible = true">{{ $srmI18n(`${$getLangAccount()}#i18n_title_generateJsonData`, '生成JSON数据') }}</a>
    <a-modal
    v-drag    
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_jsonData`, 'JSON数据')"
      :visible="visible"
      @ok="visible = false"
      @cancel="visible = false"
    >
      <codemirror
        v-model="code"
        :options="cmOptions" />
    </a-modal>
  </span>
</template>

<script>
import { mapState } from 'vuex'
import { codemirror } from 'vue-codemirror'
import 'codemirror/lib/codemirror.css'

import { deepClone } from '../../utils'
import { formRenderData } from '../../utils/core'

export default {
    name: '<PERSON>rate<PERSON><PERSON>',
    components: {
        codemirror
    },
    data () {
        return {
            visible: false,
            cmOptions: {
                tabSize: 4,
                mode: 'text/javascript',
                theme: 'base16-dark',
                lineNumbers: true,
                line: true
            },
            code: ''
        }
    },
    computed: mapState({
        formData: state => state.formDesigner.formData
    }),
    watch: {
        visible (val) {
            if (val) {
                const formData = deepClone(this.formData)
                const result = formRenderData(formData)
                this.code = 'var JSON = ' + JSON.stringify(result, null, 2)
            }
        }
    },
    methods: {}
}
</script>

<style lang="less" scoped>
</style>
