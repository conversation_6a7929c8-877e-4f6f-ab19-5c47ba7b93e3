<script lang="jsx">
import Breadcrumb from '@/components/tools/Breadcrumb.vue'
export default {
    name: 'ContentHeader',
    props: {
        btns: {
            type: Array,
            default () {
                return []
            }
        }
    },
    components: {
        Breadcrumb
    },
    watch: {
        deadline () {
            console.log('test')
        }
    },
    methods: {
        handleClick (e, btn) {
            // console.log('e', e)
            // this.$emit(`content-header-${event}`)
            btn.click && btn.click()
        }
    },
    render () {
        let { btns } = this
        const btnsEl = btns.map(n => (
            <a-button
                class="ant-btn"
                type={ n.type || '' }
                vShow={ n.showCondition ? n.showCondition() : true }
                onClick={ (event) => this.handleClick(event, n) }>
                { n.title }
            </a-button>
        ))
        return (
            <div class="content-Header">
                <div class="topWrap">
                    <div class="breadcrumb">
                        <breadcrumb></breadcrumb>
                    </div>
                    <div class="box">
                        <div class="menu">
                            { btnsEl }
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
</script>

<style lang="less" scoped>
.content-Header {
    width: 100%;
    padding: 0 6px;
    &.posA {
        position: absolute;
        left: 0;
        top: 0;
    }
	.topWrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px;
		background: #fff;
        // margin: -8px;
        min-height: 44px;
        .box {
            display: flex;
            align-items: center;
        }
        .extra {
            & +.menu {
                margin-left: 24px;
            }
        }
		.menu {
			text-align: right;
			.ant-btn {
				& + .ant-btn {
					margin-left: 10px;
				}
			}
		}
	}
}
</style>