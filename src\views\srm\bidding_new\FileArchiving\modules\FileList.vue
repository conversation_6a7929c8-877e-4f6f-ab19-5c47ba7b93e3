<template>
  <div v-if="ifShow">
    <content-header :btns="btns"/>

    <!-- 普通列表 -->
    <div
      v-for="result in resultData.result"
      :key="result.title">
      <titleTrtl class="margin-b-10">
        <span >{{ result.title }}</span>
      </titletrtl>
      <div>
        <listTable 
          :pageData="pageData1"
          pageStatus="detail"
          ref="tables"
          :defaultParams="defaultParams"
          :fromSourceData="result.infoList"
          :statictableColumns="tableColumns"
          :showTablePage="false"
        />
        
      </div>
      
    </div>

    <!-- 补充材料列表 -->
    <div>
      <titleTrtl class="margin-b-10">
        <span > {{ $srmI18n(`${$getLangAccount()}#i18n_field_xVnL_3f3e5dc9`, '补充材料') }} </span>
        <a-upload
          name="file"
          :multiple="true"
          :showUploadList="false"
          :action="uploadUrl"
          :headers="uploadHeader"
          :accept="accept"
          :data="{headId: currentEditRow.id, businessType: 'biddingPlatform', 'sourceNumber': currentEditRow.tenderProjectNumber || currentEditRow.id || '', actionRoutePath: '/srm/ebidding/FileArchivingList,采购商与供应商的路径逗号隔开'}"
          @change="handleUploadChange"
        >
          <a-button
            type="primary"
            size="small">{{ $srmI18n(`${$getLangAccount()}#i18n_field_XVxVnL_c553051f`, '上传补充材料') }}</a-button>
        </a-upload>
      </titletrtl>
      <div>
        <listTable 
          :pageData="pageData2"
          ref="tablePage"
          :defaultParams="defaultParams"
          :fromSourceData="archiveData"
          :statictableColumns="getColumn(1)"
          :showTablePage="false"
        />
      </div>
    </div> 

  </div>
</template>
<script lang="jsx">
import listTable from '../../BiddingHall/components/listTable'
import titleTrtl from '../../BiddingHall/components/title-crtl'
// import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
import { postAction, getAction } from '@/api/manage'
import ContentHeader from '../../BiddingHall/components/content-header'
import {USER_INFO, USER_COMPANYSET} from '@/store/mutation-types'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    // mixins: [baseMixins],
    components: {
        titleTrtl,
        listTable,
        ContentHeader
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    computed: {
        // tenderQuotationsType (row) {
        //     if (this.row. == '0') {
        //         return 'preSaleDocumentSubmitFileType'
        //     } else {
        //         if (this.processType == '1') {
        //             return 'resultSaleDocumentSubmitFileType '
        //         }
        //         return 'saleDocumentSubmitFileType'
        //     }
        //     return 1
        // }
    },
    data () {
        return {
            formatType: [],
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            ifShow: false,
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                    'field': 'fileType',
                    // dictCode: 'fileType_dictText',
                    slots: {
                        default: ({row, column})=>{
                            let text = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_testother_01`, '其他')
                            this.formatType.forEach((item)=>{
                                if(row.fileType == item.value){
                                    text = item.text
                                }
                            })
                            return [
                                <span>{text}</span>
                            ]
                        }}
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称'),
                    'field': 'fileName'
                    // 'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLtLRL_b8fc6d3f`, '上传人单位名称'),
                    'field': 'uploadEnterpriseName'
                // 'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLRL_70e045a7`, '上传人名称'),
                    'field': 'uploadSubAccount'
                    // 'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                    'field': 'createTime'
                    // 'width': 180
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    // width: 80,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            pageData1: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent},
                    {type: 'reply', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '下载'), clickFn: this.downloadEvent}
                ],
                // optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            pageData2: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent},
                    {type: 'reply', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '下载'), clickFn: this.downloadEvent},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete}
                ],
                // optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            archiveData: [],
            defaultParams: {},
            // url: {
            //     list: '/tender/archive/tenderProjectArchiveAttachmentHead/queryByProjectId?id='+ this.currentEditRow.tenderProjectId
            // },
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.handleSave },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: this.handleBack }
            ]
        }
    },
    methods: {
        getColumn () {
            return [{
                'type': 'seq',
                'width': 50,
                'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
            },
            {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                'field': 'fileType',
                dictCode: 'tenderArchiveSupplement',
                fieldType: 'select'
            },
            {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称'),
                'field': 'fileName'
                // 'width': 160
            },
            {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLtLRL_b8fc6d3f`, '上传人单位名称'),
                'field': 'uploadEnterpriseName'
                // 'width': 160
            },
            {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLRL_70e045a7`, '上传人名称'),
                'field': 'uploadSubAccount'
                // 'width': 160
            },
            {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                'field': 'createTime'
                // 'width': 180
            },
            {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                // width: 80,
                fixed: 'right',
                slots: { default: 'grid_opration' }
            } ]
        },
        handleDownload (row) {
            this.$emit('handleDownload', row)
        },
        handleView (row) {
            this.$emit('handleView', row)
        },
        handleBack (){
            this.$emit('hide')
        },
        handleDelete (row) {
            let url = '/tender/archive/tenderProjectArchiveAttachmentHead/deleteInfoById'
            getAction(url, {id: row.id}).then((res) => {
                if(res.success){
                    this.$message.success(res.message)
                    this.getData()
                }else{
                    this.$message.warning(res.message)
                }
            }).finally(()=>{
            })
        },
        async downloadEvent (row) {
            const fileName = row.fileName
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            if(this.$refs.tables){
                this.$refs.tables.loading = true
            }
            this.$refs.tablePage.loading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                if(this.$refs.tables){
                    this.$refs.tables.loading = false
                }
                this.$refs.tablePage.loading = false
            })
        },
        // 预览文件
        preViewEvent (row) {
            console.log('row:', row)
            let preViewFile = {
                id: row.id,
                sourceType: row.sourceType,
                filePath: row.filePath,
                subpackageId: this.currentEditRow.subpackageId
            }
            this.$previewFile.open({params: preViewFile })
        },
        handleSave (){
            console.log(this.$refs.tablePage.getTableData())
            let url = '/tender/archive/tenderProjectArchiveAttachmentHead/edit'
            postAction(url, {...this.currentEditRow, archiveAttachmentInfoList: this.archiveData}).then((res) => {
                if(res.success){
                    this.$message.success(res.message)
                }else{
                    this.$message.warning(res.message)
                }
            }).finally(()=>{
            })
        },
        //附件上传
        handleUploadChange ({ file }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    let archiveAttachmentInfoList = file.response.result
                    archiveAttachmentInfoList.uploadElsAccount = this.$ls.get(USER_INFO).elsAccount
                    archiveAttachmentInfoList.uploadSubAccount = this.$ls.get(USER_INFO).subAccount
                    archiveAttachmentInfoList.uploadEnterpriseName = this.$ls.get(USER_COMPANYSET).companyName || ''
                    archiveAttachmentInfoList.uid = file.response.result.id
                    archiveAttachmentInfoList.name = file.response.result.fileName
                    archiveAttachmentInfoList.url = file.response.result.filePath
                    // delete archiveAttachmentInfoList.id

                    // 上传成功后更新listtable数据
                    this.archiveData.push(archiveAttachmentInfoList)

                    // //将上传的文件通过接口传到后端文件归档处
                    // postAction('/tender/archive/tenderProjectArchiveAttachmentHead/add', {...this.currentEditRow, archiveAttachmentInfoList:[archiveAttachmentInfoList]}).then((res) => {
                    //     if(res.success){
                    
                    //         this.$message.success(res.message)
                    //     }else{
                    //         this.$message.warning(res.message)
                    //     }
                    // }).finally(()=>{
                    // })
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        },

        async getData (){
            let url = '/tender/archive/tenderProjectArchiveAttachmentHead/queryByProjectId'
            this.resultData = await getAction(url, {projectId: this.currentEditRow.tenderProjectId})
            console.log('this.resultData', this.resultData)
            let archiveUrl = '/tender/archive/tenderProjectArchiveAttachmentHead/queryByProjectIdSupplement'
            getAction(archiveUrl, {projectId: this.currentEditRow.tenderProjectId}).then((res) => {
                if(res.success){
                    this.archiveData = res.result
                    this.$set(this, 'archiveData', res.result)
                    this.ifShow=true
                }else{
                    this.$message.warning(res.message)
                }
            }).finally(()=>{
            })

        }
    },
    async created () {
        // 获取表格下拉字典
        let postData1 = {
            busAccount: this.$ls.get(USER_ELS_ACCOUNT),
            dictCode: 'tenderFileTypeArchive'
        }
        let postData2 = {
            busAccount: this.$ls.get(USER_ELS_ACCOUNT),
            dictCode: 'tenderFileTypeArchive2'
        }
        await ajaxFindDictItems(postData1).then(res => {
            console.log('res', res)
            if (res.success) {
            // 获取文件类型的字典值
                this.formatType = res.result || []
            }
        })
        await ajaxFindDictItems(postData2).then(res => {
            console.log('res', res)
            if (res.success && res.result.length != 0) {
            // 获取文件类型的字典值
                this.formatType.push(...res.result)
            }
        })
        console.log('this.formatType', this.formatType)
    },
    mounted (){
        this.getData()
    }
}
</script>
<style lang="less" scoped>
.margin-b-10{
  margin-bottom: 5px;
}
</style>


