<template>
  <div class="els-page-comtainer">
    <div
      class="table-page-search-wrapper"
      style="padding:8px">
      <a-spin :spinning="confirmLoading">
        <!-- 主表单区域 -->
        <a-tabs
          type="card"
          @change="changeTabs">
          <a-tab-pane
            :tab="$srmI18n(`${$getLangAccount()}#i18n_title_basicInfo`, '基本信息')"
            key="tab_1">
            <a-form :form="form">
              <a-row>
                <a-col
                  :span="12"
                  :gutter="8">
                  <a-form-item
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_kanbangCode`, '看板编码')"
                  >
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_kanbangCodeMsg`, '请输入看板编码')"
                      v-decorator="['dashboardCode', validatorRules.dashboardCode]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :span="12"
                  :gutter="8">
                  <a-form-item
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_kanbangName`, '看板名称')"
                  >
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_kanbangNameMsg`, '请输入看板名称')"
                      v-decorator="['dashboardName', validatorRules.dashboardName]"
                    />
                  </a-form-item>
                </a-col>
                <a-col
                  :span="12"
                  :gutter="8">
                  <a-form-item
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_kanbangDes`, '看板描述')"
                  >
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_kanbangDesMsg`, '请输入看板描述')"
                      type="textarea"
                      rows="5"
                      v-decorator="['dashboardDesc', validatorRules.dashboardDesc]"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-tab-pane>
          <a-tab-pane
            forceRender
            :tab="$srmI18n(`${$getLangAccount()}#i18n_title_design`, '设计')"
            key="tab_2">
            <div
              class="panel-container-design"
              :style="minHeight">
              <grid-layout
                :layout.sync="panelData"
                :col-num="12"
                :row-height="30"
                :is-draggable="true"
                :is-resizable="true"
                :is-mirrored="false"
                :vertical-compact="true"
                :margin="[10, 10]"
                :use-css-transforms="true"
                @layout-updated="layoutUpdatedEvent"
              >
                <grid-item
                  v-for="(item,index) in panelData"
                  :x="item.x"
                  :y="item.y"
                  :w="item.w"
                  :h="item.h"
                  :i="item.i"
                  style="background:#fff"
                  dragIgnoreFrom=".panel-delete-btn"
                  :class="[item.chartType == 'num' ? 'data-statis-item' : '']"
                  :dragAllowFrom="item.chartType == 'num' ? '.data-statis-item' : '.ant-card-head'"
                  :key="item.i"
  
                  @resized="movedEvents('cardView'+index)"
                >
                  <viewPreviewCard
                    :modalData="item.modalData"
                    :widget="item.widget"
                    :options="item.options || {}"
                    :resultData="item.resultData"
                    :ref="'cardView'+index"
                    :chartStyle="chartStyle"
                  />
                </grid-item>
              </grid-layout>
            </div>
          </a-tab-pane>
          <a-button
            @click="handleOk"
            type="primary"
            slot="tabBarExtraContent"
            key="1">{{
              $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存')
            }}</a-button>
          <a-button
            v-show="showNewBtn"
            @click="insertDashboardChart"
            type="primary"
            style="margin-left:8px"
            slot="tabBarExtraContent"
            key="2"
          >{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}</a-button
          >
          <a-button
            @click="goBack"
            style="margin-left:8px"
            slot="tabBarExtraContent"
            key="3"
          >{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button
          >
        </a-tabs>
      </a-spin>
    </div>
    <select-chart-modal
      ref="pickList"
      @ok="selectOk"></select-chart-modal>
  </div>
</template>

<script>
import moment from 'moment'
import pick from 'lodash.pick'
import { duplicateCheck } from '@/api/api'
import { postAction, getAction } from '@/api/manage'
import selectChartModal from './selectChartModal'
import VueGridLayout from 'vue-grid-layout'
import EchartMap from '@/components/EchartMap/index'
import { initWidgetInfo } from '@/components/chart/widget/utils/widgetUtils'
import ViewPreviewCard from '@views/dashboard/modules/ViewPreviewCard'

export default {
    name: 'DashboardModal',
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    provide (){
        return {
            getMap: this.gridItemRefName
        }
    },
    components: {
        selectChartModal,
        GridLayout: VueGridLayout.GridLayout,
        GridItem: VueGridLayout.GridItem,
        EchartMap,
        ViewPreviewCard
    },
    data () {
        return {
            chartStyle: {'width': '100%', 'height': '100%'},
            title: '',
            minHeight: 'min-height:' + (document.documentElement.clientHeight - 200) + 'px',
            fixPageHeader: false,
            form: this.$form.createForm(this),
            warehouseFlag: false,
            confirmLoading: false,
            panelData: [],
            model: {},
            showNewBtn: false,
            gridItemRefName: '',
            labelCol: {
                xs: { span: 24 },
                sm: { span: 6 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 18 }
            },
            gridConfig: {
                border: true,
                stripe: true,
                resizable: true,
                autoResize: true,
                keepSource: true,
                height: 'auto',
                showOverflow: true,
                showHeaderOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center'
            },
            // 新增时子表默认添加几行空数据
            addDefaultRowNum: 1,
            url: {
                add: '/report/dashboard/dashboard/add',
                edit: '/report/dashboard/dashboard/edit',
                data: '/report/dataSource/elsReportChartDataSet/getDataById',
                dashboardChart: {
                    list: '/report/dashboard/dashboard/queryDashboardChartByMainId',
                    mapList: '/report/dataSource/elsReportChartDataSet/getDataById'
                    
                }
            }
        }
    },
    computed: {
        langAccount () {
            return this.$getLangAccount()
        },

        validatorRules () {
            let account = this.langAccount
            const { $srmI18n } = this
            return {
                dashboardCode: {
                    rules: [
                        { required: true, message: $srmI18n(`${account}#i18n_title_kanbangCodeMsg`, '请输入看板编码!') },
                        { max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符!') }
                    ]
                },
                dashboardName: {
                    rules: [
                        { required: true, message: $srmI18n(`${account}#i18n_title_kanbangNameMsg`, '请输入看板名称!') },
                        { max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符!') }
                    ]
                },
                dashboardDesc: {
                    rules: [{ max: 100, message: $srmI18n(`${account}#i18n_title_overflow100`, '内容长度不能超过100个字符!') }]
                }
            }
        }

    },

    activated () {
        let event = document.createEvent('HTMLEvents')
        event.initEvent('resize', true, true)
        event.eventType = 'message'
        window.dispatchEvent(event)
    },
    mounted () {
        this.init()
    },
    methods: {
        moment,
        movedEvents (refName){
            // console.log('refName', this.$refs[refName][0])
            this.$refs[refName][0].$refs.EchartMap.setChartDivReize() 
        },
        init () {
            if (this.currentEditRow) {
                this.edit(this.currentEditRow)
            } else {
                this.add()
            }
        },
        add () {
            this.edit({})
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addKanbang`, '新增看板')
            this.panelData = []
        },
        edit (record) {
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editKanbang`, '编辑看板')
            if (typeof this.editBefore === 'function') this.editBefore(record)
            this.visible = true
            this.form.resetFields()
            this.model = Object.assign({}, record)
            if (typeof this.editAfter === 'function') this.editAfter(this.model)
        },
        /** 调用完edit()方法之后会自动调用此方法 */
        editAfter () {
            this.$nextTick(() => {
                this.form.setFieldsValue(pick(this.model, 'dashboardCode', 'dashboardName', 'dashboardDesc'))
                // 时间格式化
            })
            // 加载子表数据
            if (this.model.id) {
                let params = { id: this.model.id }
                this.requestSubTableData(this.url.dashboardChart.list, params)
            }
        },
        requestSubTableData (url, params) {
            let that = this
            getAction(url, params).then(res => {
                let list = res.result.records
                debugger
                //字段转换
                list.forEach(item => {
                    //数据必须包含x,y,w,h,i字段
                    item.x = item.xCoord
                    item.y = item.yCoord
                    item.w = item.chartWidth
                    item.h = item.chartHeight
                    item.i = item.chartId
                    item.modalData = item
                    item.resultData = []
                    item.loading = true
                    if (item.chartType == 'list') {
                        // item.columns = JSON.parse(item.chartExt)
                    } else if (item.chartType == 'echart') {
                        // 具体图表类型 配置
                        item.widget = initWidgetInfo(item.chartSubType)
                        // panel.resultData = this.handlePercenByData(res.result.data)
                        
                        // item.widget.data = panel.resultData // 过滤后的数据
                    }
                    
                }) 
                that.panelData = list
                that.getResultData()
            })
        },
        validateCode (rule, value, callback) {
            // 重复校验
            var params = {
                tableName: 'els_dashboard',
                fieldName: 'dashboard_code',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then(res => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        //新增行
        insertDashboardChart () {
            let panelData = this.panelData
            this.$refs.pickList.open(panelData)
        },
        //删除报表
        deletePanel (chartId) {
            let index = 0
            this.panelData.forEach((item, i) => {
                if (item.i == chartId) {
                    index = i
                }
            })
            this.panelData.splice(index, 1)
        },
        goBack () {
            this.$emit('hide')
        },
        handleScroll () {
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 50) {
                this.fixPageHeader = true
            } else {
                this.fixPageHeader = false
            }
        },
        handleOk () {
            let that = this
            // 表单检验
            that.form.validateFields(err => {
                if (!err) {
                    that.postData()
                }
            })
        },
        //提交数据
        postData () {
            let url = this.url.add
            if (this.model.id) {
                url = this.url.edit
            }
            this.confirmLoading = true
            let panelData = []
            this.panelData.forEach(item => {
                let panel = {
                    xCoord: item.x,
                    yCoord: item.y,
                    chartWidth: item.w,
                    chartHeight: item.h,
                    chartId: item.chartId,
                    id: item.id || null
                }
                panelData.push(panel)
            })
            let formData = Object.assign(this.model, this.form.getFieldsValue(), { dashboardChartList: panelData })
            postAction(url, formData)
                .then(res => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.$emit('ok')
                        this.goBack()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handlePercenByData (data) {
            data.forEach(rs => {
                if (rs.value && typeof(rs.value) == 'string') {
                    rs.value = parseInt(rs.value)
                }
            })
            return data
        },
        selectOk (data) {
            let that = this
            
            console.log('selectOk', data)
            data.forEach(item => {
                let getUrl=item.chartType=='map'?this.url.dashboardChart.mapList+'?id='+item.dataId:this.url.data + '?id=' + item.dataId
                getAction(getUrl, {}).then(res => {
                    if (!res.success) {
                        this.$message.warning(res.message)
                        return
                    }
                    let panel = {
                        x: 0,
                        y: 0,
                        w: item.chartWidth,
                        h: item.chartHeight,
                        i: item.id,
                        chartId: item.id,
                        id: null,
                        chartName: item.chartName,
                        chartType: item.chartType,
                        modalData: item
                    }
                    if (item.chartType == 'num') {
                        panel.resultData = res.result.data
                        
                    } else if (item.chartType == 'list') {
                        panel.resultData = res.result.data?.length ? res.result.data.slice(0, 1) : []
                        // panel.columns = JSON.parse(item.chartExt)
                    }else if(item.chartType=='map'){
                        panel.resultData = res.result.data[0].result
                        panel.options = JSON.parse(item.chartExt)
                    } else { // 图表
                    // 具体图表类型 配置
                        panel.widget = initWidgetInfo(item.chartSubType)
                        panel.resultData = this.handlePercenByData(res.result.data)
                        panel.widget.data = panel.resultData // 过滤后的数据
                    }
                    
                    that.panelData.push(panel)
                })
            })
        },
        layoutUpdatedEvent (newLayout) {
            this.panelData = newLayout
        },
        getResultData () {
            let that = this
            this.panelData.forEach(panel => {
                let getUrl=panel.chartType=='map'?this.url.dashboardChart.mapList+'?id='+panel.dataId:this.url.data + '?id='+panel.dataId
                getAction(getUrl, {}).then(res => {
                    if (panel?.chartType == 'list') {
                        panel.resultData = res.result.data
                    } else if(panel?.chartType=='num'){
                        panel.resultData = res.result.data?.length ? res.result.data.slice(0, 1) : []
                    } else if(panel?.chartType=='map'){
                        panel.resultData = res.result.data[0].result
                        panel.options = JSON.parse(panel.chartExt)
                    } else if (panel?.chartType == 'echart') {
                        panel.resultData = this.handlePercenByData(res.result.data)
                        panel.widget.data = panel.resultData // 过滤后的数据
                    }
                    that.$forceUpdate()
                })
            })
        },
        changeTabs (key) {
            if (key == 'tab_2') {
                this.showNewBtn = true
            } else {
                this.showNewBtn = false
            }
        },
        clearPanel () {
            this.panelData = []
        }
    }
}
</script>
<style lang="less">
.panel-container-design {
  .workplace_card {
    &:hover {
      box-shadow: 0px 0px 9px rgba(0, 0, 0, 0.5);
    }
  }
  .echarts {
    width: 100%;
    height: 100%;
  }
  .ant-card-body {
    width: 100%;
    height: calc(100% - 37px);
  }
  .num-panel-item {
    display: flex;
    height: 100%;
    align-items: center;
    .num-panel-icon {
      text-align: center;
      font-size: 16px;
      width: 30%;
      border-right: 1px solid #e5e5e5;
    }
    .num-panel-content {
      flex: 1;
      text-align: center;
      font-size: 32px;
      font-weight: bold;
    }
  }
  .num-panel-item-new {
    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        border-radius: 30px;
        text-align: center;
        padding: 9px 16px;
        color: white;
        background: #ed2f2f;
        line-height: 100%;
        height: 15px;
        font-weight: 600;
        -webkit-box-sizing: content-box;
        box-sizing: content-box;
      }
    }
    .count {
      padding: 10px 0;
      font-size: 18px;
      color: #494949;
      font-weight: 500;
    }
  }
}
</style>
