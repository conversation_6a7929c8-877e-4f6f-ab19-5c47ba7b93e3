<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <PurchaseEcnEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <PurchaseEcnDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import PurchaseEcnEdit from './modules/PurchaseEcnEdit'
import PurchaseEcnDetail from './modules/PurchaseEcnDetail'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, postAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseEcnEdit,
        PurchaseEcnDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'ecn',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeOrderNo`, '(变更单号)')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', type: 'primary',  clickFn: this.handleAdd, authorityCode: 'ecn#purchaseEcnHead:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SMWWWWF_b44eb4e2`, '获取ERP数据'), icon: 'arrow-down', clickFn: this.getDataByErp, authorityCode: 'ecn#purchaseEcnHead:getDataByErp'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'), icon: 'arrow-up', clickFn: this.pushDataToERP, authorityCode: 'ecn#purchaseEcnHead:pushDataToErp'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'ecn#purchaseEcnHead:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.isEdit, authorityCode: 'ecn#purchaseEcnHead:edit'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopy, authorityCode: 'ecn#purchaseEcnHead:copy'},
                    {type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_hx_a8452`, '发布'), clickFn: this.handlePublish, allow: this.isPublish, authorityCode: 'ecn#purchaseEcnHead:publish'},
                    {type: 'backout', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销'), clickFn: this.handleBackout, allow: this.allowBackout, authorityCode: 'ecn#purchaseEcnHead:backout'},
                    {type: 'cancellation', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_ku_9fac3`, '作废'), clickFn: this.handleCancellation, allow: this.allowCancellation, authorityCode: 'ecn#purchaseEcnHead:cancellation'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'ecn#purchaseEcnHead:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord, authorityCode: 'ecn#purchaseEcnHead:record'}
                ]
            },
            url: {
                list: '/ecn/purchaseEcn/list',
                add: '/ecn/purchaseEcn/add',
                copyData: '/ecn/purchaseEcn/copyById',
                delete: '/ecn/purchaseEcn/delete',
                deleteBatch: '/ecn/purchaseEcn/deleteBatch',
                exportXlsUrl: 'ecn/purchaseEcn/exportXls',
                importExcelUrl: 'ecn/purchaseEcn/importExcel',
                informBuyer: '/ecn/purchaseEcn/publishBuyer',
                sendToSupplier: '/ecn/purchaseEcn/publishSuppliers',
                publish: '/ecn/purchaseEcn/publish',
                backout: '/ecn/purchaseEcn/backout',
                cancellation: '/ecn/purchaseEcn/cancellation',
                columns: 'PurchaseECNList',
                getDataByErpUrl: '/ecn/purchaseEcn/getDataByErp',
                pushDataToERPUrl: '/ecn/purchaseEcn/pushDataToErp'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_engineeringChangeNotice `, '工程变更通知书'))
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelCallBack (){
            this.showEditPage = true
            this.showDetailPage = false
            this.searchEvent()
        },
        allowEdit (row){
            if((row.ecnStatus==='0' && (row.auditStatus==='0' || row.auditStatus==='3'))||row.auditStatus==='4'){
                return false
            }
            return true
        },
        allowDelete (row){
            //新建  审批通过+审批中的单据不能删除
            if(row.ecnStatus==='0') {
                if (row.auditStatus === '0' || row.auditStatus === '3' || row.auditStatus === '4') {
                    return false
                }
            }else {
                return true
            }
            //正常-未发布-无需审批的单据，删除按钮应置灰
            if(row.ecnStatus==='1' ){
                if(row.auditStatus==='4'&&row.sendStatus=='0'){
                    return true
                }
            }else {
                return true
            }
        },
        // allowInform (row){
        //     if(row.ecnStatus==='1' && row.buyerExist==='1' && row.informBuyer!=='1'){
        //         return false
        //     }
        //     return true
        // },
        // allowSend (row){
        //     if(row.ecnStatus==='1' && row.supplierExist==='1' && row.sendStatus!=='1'){
        //         return false
        //     }
        //     return true
        // },
        allowPublish (row){
            if(((row.ecnStatus==='1'||row.ecnStatus==='0'))&& (row.supplierExist==='1' || row.buyerExist==='1') && row.sendStatus!=='1'&&((row.auditStatus=='2'||row.auditStatus==='4'))){
                return false
            }
            return true
        },
        allowBackout (row){
            //正常，已发送状态下
            if(row.ecnStatus==='1' && row.sendStatus==='1'){
                return false
            }
            return true
        },

        isEdit (row){
            if(row.auditStatus=='1'||row.auditStatus=='2'){
                return true
            }else {
                if( (row.sendStatus=='0'||row.sendStatus=='2' ) && (row.ecnStatus=='0')){
                    return false
                }else {
                    return true
                }
            }
        },
        isPublish (row){
            if (row.ecnStatus==='1' &&row.sendStatus==='0'){
                if((row.auditStatus==='2'||row.auditStatus==='4')){
                    return false
                }else{
                    return true
                }
            }else{
                return true
            }
        },

        allowCancellation (row){
            //正常
            if(row.ecnStatus==='1'){
                return false
            }
            return true
        },
        handlePublish (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areYouSureWantPublish`, '是否确定发布?'),
                onOk: function () {
                    postAction(that.url.publish, row).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.sendStatus = '1'
                            row.sendStatus_dictText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_published `, '已发布')
                        }
                    })
                }
            })
        },
        handleBackout (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLqXhxW_bf63cc9b`, '确认撤销发布?'),
                onOk: function () {
                    getAction(that.url.backout, row).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.sendStatus = '0'
                            row.sendStatus_dictText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Unpublished`, '未发布')
                        }
                    })
                }
            })
        },
        handleCancellation (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areYouSurWantVoid`, '是否确定作废?'),
                onOk: function () {
                    getAction(that.url.cancellation, row).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.ecnStatus = '2'
                            row.ecnStatus_dictText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废')
                        }
                    })
                }
            })
        },
        // 复制功能按钮
        handleCopy (row){
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(row)
                }
            })
        },
        // 复制数据请求接口
        copyData (row){
            this.confirmLoading = true
            let param  = {id: row.id}
            getAction(this.url.copyData, param).then(res => {
                if(res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功！'))
                    this.searchEvent()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    }
}
</script>