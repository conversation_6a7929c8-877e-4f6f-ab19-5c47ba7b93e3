import Vue from 'vue'
import store from '@/store/'
import i18n from '@/i18n'
import * as api from '@/api/api'
import { isURL } from '@/utils/validate'
import { downFile } from '@/api/manage'
import { LOGIN_RESULT_DATA, ACCESS_TOKEN, USER_ELS_ACCOUNT, DEFAULT_LANG, USER_COMPANYSET, SET_BUSINESS_RULE_LIST } from '@/store/mutation-types'
import { variateConfig } from '@/utils/variateConfig.js'
import { NUMBER_MAX_LENGTH } from '@/utils/const.js'
import { message } from 'ant-design-vue'
let isOnloadSign = false // onload触发标识
let onloadCallbackArr = [] // onload时间触发后调用，记录回调函数

export function timeFix () {
    const time = new Date()
    const hour = time.getHours()
    let lang = Vue.ls.get(DEFAULT_LANG) === 'en' ? true : false
    // return hour < 9 ? srmI18n(`${getLangAccount()}#i18n_title_goodMorning`, '早上好') : (hour <= 11 ? srmI18n(`${getLangAccount()}#i18n_title_goodMorning`, '上午好') : (hour <= 13 ? srmI18n(`${getLangAccount()}#i18n_title_goodNoon`, '中午好') : (hour < 20 ? srmI18n(`${getLangAccount()}#i18n_title_goodAfternoon`, '下午好') :srmI18n(`${getLangAccount()}#i18n_title_goodAfternoon`, '晚上好') )))
    return hour < 9 ? (lang ? 'good morning' : '早上好') : hour <= 11 ? (lang ? 'good morning' : '上午好') : hour <= 13 ? (lang ? 'Good noon' : '中午好') : hour < 20 ? (lang ? 'Good afternoon' : '下午好') : lang ? 'good evening' : '晚上好'
}

export function welcome () {
    let tips1 = srmI18n(`${getLangAccount()}#i18n_title_takeABreak`, '休息一会儿吧')
    let tips2 = srmI18n(`${getLangAccount()}#i18n_title_whatAreYouGoingToEat`, '准备吃什么呢?')
    let tips3 = srmI18n(`${getLangAccount()}#i18n_title_doYouWantToPlayDota`, '要不要打一把 DOTA')
    let tips4 = srmI18n(`${getLangAccount()}#i18n_title_guessYouMayBeTired`, '我猜你可能累了')

    const arr = [tips1, tips2, tips3, tips4]
    let index = Math.floor(Math.random() * arr.length)
    return arr[index]
}

/**
 * 触发 window.resize
 */
export function triggerWindowResizeEvent () {
    let event = document.createEvent('HTMLEvents')
    event.initEvent('resize', true, true)
    event.eventType = 'message'
    window.dispatchEvent(event)
}

/**
 * 过滤对象中为空的属性
 * @param obj
 * @returns {*}
 */
export function filterObj (obj) {
    if (!(typeof obj == 'object')) {
        return
    }

    for (var key in obj) {
        if (obj.hasOwnProperty(key) && (obj[key] == null || obj[key] == undefined || obj[key] === '')) {
            delete obj[key]
        }
    }
    return obj
}
/**
 * 过滤对象中为空的属性(除空字符串)
 * @param obj
 * @returns {*}
 */
export function filterObjUnEmpty (obj) {
    if (!(typeof obj == 'object')) {
        return
    }

    for (var key in obj) {
        if (obj.hasOwnProperty(key) && (obj[key] == null || obj[key] == undefined)) {
            delete obj[key]
        }
    }
    return obj
}

/**
 * 时间格式化
 * @param value
 * @param fmt
 * @returns {*}
 */
export function formatDate (value, fmt) {
    var regPos = /^\d+(\.\d+)?$/
    if (regPos.test(value)) {
        //如果是数字
        let getDate = new Date(value)
        let o = {
            'M+': getDate.getMonth() + 1,
            'd+': getDate.getDate(),
            'h+': getDate.getHours(),
            'm+': getDate.getMinutes(),
            's+': getDate.getSeconds(),
            'q+': Math.floor((getDate.getMonth() + 3) / 3),
            S: getDate.getMilliseconds()
        }
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (getDate.getFullYear() + '').substr(4 - RegExp.$1.length))
        }
        for (let k in o) {
            if (new RegExp('(' + k + ')').test(fmt)) {
                fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
            }
        }
        return fmt
    } else {
        //TODO
        value = value.trim()
        return value.substr(0, fmt.length)
    }
}

// 生成首页路由
export function generateIndexRouter (data) {
    let homePath = data[0].path
    let indexRouter = [
        {
            path: '/',
            name: 'dashboard',
            //component: () => import('@/components/layouts/BasicLayout'),
            component: (resolve) => import('@/components/layouts/TabLayout').then((res) => resolve(res.default)),
            // component: resolve => require(['@/components/layouts/TabLayout'], resolve),
            meta: { title: '首页', titleI18nKey: 'i18n_menu_lE_13319f' },
            redirect: homePath,
            children: [...generateChildRouters(data)]
        },
        {
            path: '*',
            redirect: '/404',
            hidden: true
        }
    ]
    return indexRouter
}
export function getUrlParams (url) {
    // \w+ 表示匹配至少一个(数字、字母及下划线), [\u4e00-\u9fa5]+ 表示匹配至少一个中文字符
    let pattern = /(\w+|[\u4e00-\u9fa5]+)=(\w+|[\u4e00-\u9fa5]+)/gi
    let result = {}
    url.replace(pattern, ($, $1, $2) => {
        result[$1] = $2
    })
    return result
}

function generateChildRouters (data) {
    const routers = []

    for (var item of data) {
        let component = ''
        if (item.component.indexOf('layouts') >= 0) {
            component = 'components/' + item.component
        } else {
            component = 'views/' + item.component
        }
        let URL = (item.meta.url || '').replace(/{{([^}}]+)?}}/g, (s1, s2) => {
            let val = s2.trim()
            val = variateConfig[val]
            return val
        }) // URL支持{{ window.xxx }}占位符变量
        if (isURL(URL)) {
            item.meta.url = URL
        }
        let menu = {
            path: item.path,
            name: item.name,
            redirect: item.redirect,
            hidden: item.hidden,
            component: (resolve) => import(`@/${component}.vue`).then((res) => resolve(res.default)),
            // component: resolve => require([`@/${component}.vue`], res => resolve(res.default)),
            // props: { keys: item.name },
            meta: {
                title: item.meta.title,
                icon: item.meta.icon,
                url: item.meta.url,
                permissionList: item.meta.permissionList,
                keepAlive: item.meta.keepAlive,
                /*update_begin author:wuxianquan date:20190908 for:赋值 */
                internalOrExternal: item.meta.internalOrExternal
                /*update_end author:wuxianquan date:20190908 for:赋值 */
            }
        }
        // if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        //     menu.component=IEshimRoutre(component)
        // } else {
        //     menu.component=resolve => require(['@/' + component+'.vue'], resolve)
        // }
        if (item.alwaysShow) {
            menu.alwaysShow = true
            menu.redirect = menu.path
        }
        if (item.children && item.children.length > 0) {
            menu.children = [...generateChildRouters(item.children)]
        }
        //判断是否生成路由
        if (item.route && item.route === '0') {
            //console.log(' 不生成路由 item.route：  '+item.route);
            //console.log(' 不生成路由 item.path：  '+item.path);
        } else {
            routers.push(menu)
        }
    }
    return routers
}

/**
 * 深度克隆对象、数组
 * @param obj 被克隆的对象
 * @return 克隆后的对象
 */
export function cloneObject (obj) {
    return JSON.parse(JSON.stringify(obj))
}

/**
 * 随机生成数字
 *
 * 示例：生成长度为 12 的随机数：randomNumber(12)
 * 示例：生成 3~23 之间的随机数：randomNumber(3, 23)
 *
 * @param1 最小值 | 长度
 * @param2 最大值
 * @return int 生成后的数字
 */
export function randomNumber () {
    // 生成 最小值 到 最大值 区间的随机数
    const random = (min, max) => {
        return Math.floor(Math.random() * (max - min + 1) + min)
    }
    if (arguments.length === 1) {
        let [length] = arguments
        // 生成指定长度的随机数字，首位一定不是 0
        let nums = [...Array(length).keys()].map((i) => (i > 0 ? random(0, 9) : random(1, 9)))
        return parseInt(nums.join(''))
    } else if (arguments.length >= 2) {
        let [min, max] = arguments
        return random(min, max)
    } else {
        return Number.NaN
    }
}

/**
 * 随机生成字符串
 * @param length 字符串的长度
 * @param chats 可选字符串区间（只会生成传入的字符串中的字符）
 * @return string 生成的字符串
 */
export function randomString (length, chats) {
    if (!length) length = 1
    if (!chats) chats = '0123456789qwertyuioplkjhgfdsazxcvbnm'
    let str = ''
    for (let i = 0; i < length; i++) {
        let num = randomNumber(0, chats.length - 1)
        str += chats[num]
    }
    return str
}

/**
 * 随机生成uuid
 * @return string 生成的uuid
 */
export function randomUUID () {
    let chats = '0123456789abcdef'
    return randomString(32, chats)
}

/**
 * 下划线转驼峰
 * @param string
 * @returns {*}
 */
export function underLine2CamelCase (string) {
    return string.replace(/_([a-z])/g, function (all, letter) {
        return letter.toUpperCase()
    })
}

/**
 * 判断是否显示办理按钮
 * @param bpmStatus
 * @returns {*}
 */
export function showDealBtn (bpmStatus) {
    if (bpmStatus != '1' && bpmStatus != '3' && bpmStatus != '4') {
        return true
    }
    return false
}

/**
 * 增强CSS，可以在页面上输出全局css
 * @param css 要增强的css
 * @param id style标签的id，可以用来清除旧样式
 */
export function cssExpand (css, id) {
    let style = document.createElement('style')
    style.type = 'text/css'
    style.innerHTML = `@charset "UTF-8"; ${css}`
    // 清除旧样式
    if (id) {
        let $style = document.getElementById(id)
        if ($style != null) $style.outerHTML = ''
        style.id = id
    }
    // 应用新样式
    document.head.appendChild(style)
}

/**
 * 重复值验证工具方法
 *
 * 使用示例：
 * { validator: (rule, value, callback) => validateDuplicateValue('sys_fill_rule', 'rule_code', value, this.model.id, callback) }
 *
 * @param tableName 被验证的表名
 * @param fieldName 被验证的字段名
 * @param fieldVal 被验证的值
 * @param dataId 数据ID，可空
 * @param callback
 */
export function validateDuplicateValue (tableName, fieldName, fieldVal, dataId, callback) {
    let params = { tableName, fieldName, fieldVal, dataId }
    api.duplicateCheck(params)
        .then((res) => {
            res['success'] ? callback() : callback(res['message'])
        })
        .catch((err) => {
            callback(err.message || err)
        })
}

// onload完成后触发
export function onloadAfterEmit () {
    isOnloadSign = true // 标识
    onloadCallbackArr.forEach((fn) => {
        if (fn && typeof fn === 'function') fn()
    })
    onloadCallbackArr = [] // 清空数组
    // 检测全局调用
    if (window._onloadCallback_ && Array.isArray(window._onloadCallback_)) {
        window._onloadCallback_.forEach((fn) => {
            if (fn && typeof fn === 'function') fn()
        })
        window._onloadCallback_ = null
    }
}
// onload全局调用
export function onloadCallback (fn) {
    if (typeof fn !== 'function') return false
    if (isOnloadSign === true) fn()
    else onloadCallbackArr.push(fn)
}

/**
 *多组模板验证方法
 * @export
 * @param {*} validTaget, 当前组件的ref
 * @param {*} pageConfig, 当前的配置
 */
export function handValidate (validTaget, pageConfig, tplRootRef) {
    const handlePromise = (list = []) =>
        list.map((promise) =>
            promise.then(
                (res) => ({
                    status: 'success',
                    res
                }),
                (err) => ({
                    status: 'error',
                    err
                })
            )
        )
    let setPromise = (groups) => {
        // 只校验打开的表单和表行 或者
        return groups
            .filter((rs) => rs.show !== false || (rs.show === false && rs.verify === true))
            .map((group) => {
                if (group.groupType === 'item' && group.show !== false) {
                    const Gdom = validTaget.$refs[group.groupCode + 'grid']
                    let gridParentRef = Gdom && Gdom[0]
                    return gridParentRef && gridParentRef.$refs[group.groupCode].validate(true)
                } else if (group.groupType === 'head' && group.show !== false) {
                    const Fdom = validTaget.$refs[group.groupCode + 'form']
                    let formParentRef = Fdom && Fdom[0]
                    return formParentRef && formParentRef.$refs[group.groupCode].validate()
                } else if (group.show === false && group.verify === true) {
                    //处理模板slot校验
                    if (group.groupType === 'item') {
                        const Gdom = tplRootRef && tplRootRef.$refs[group.groupCode + 'grid']
                        const gridParentRef = Gdom && Gdom.$refs[group.groupCode]
                        return gridParentRef && gridParentRef.validate(true)
                    } else {
                        const Fdom = tplRootRef && tplRootRef.$refs[group.groupCode + 'form']
                        const formParentRef = Fdom && Fdom.$refs[group.groupCode]
                        return formParentRef && formParentRef.validate()
                    }
                }
            })
            .filter((promise) => promise)
    }
    let promise = setPromise(pageConfig.groups)
    return new Promise((resolve, reject) => {
        Promise.all(handlePromise(promise))
            .then((result) => {
                let currentStep = null
                let flag = true
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'error') {
                        currentStep = i
                        flag = false
                    }
                }
                if (flag) {
                    let resolveData = { validStatus: true, currentStep: currentStep, message: srmI18n(`${getLangAccount()}#i18n_alert_OiLR_48599c04`, '验证成功') }
                    resolve(resolveData)
                } else {
                    let resolveData = { validStatus: false, currentStep: currentStep, message: srmI18n(`${getLangAccount()}#i18n_alert_OiKmWVGv_95d29236`, '验证失败，请处理') }
                    resolve(resolveData)
                }
            })
            .catch((err) => {
                reject(err)
            })
    })
}

//根据字典Code, 初始化字典数组
function initDictData (dictCode, busAccount) {
    let postData = {
        busAccount,
        dictCode
    }
    return api.ajaxFindDictItems(postData)
}
/**
 * 根据选中值获取对应obj同级的字段
 * 暂支持select 单选和多选，其他需要扩展
 * codeObj object 填入已选择的code 比如 {factory: '1004'}；
 * formFields 当前pageData.formFields 模板返回或者前端页面写死 获取比如：this.$refs.editPage.pageData.formFields
 * busAccount 获取初始化字典数组 必要字段 如：this.busAccount || this.$ls.get(USER_ELS_ACCOUNT)
 */
export function getValueByCode (codeObj, formFields, busAccount) {
    return new Promise((resolve, reject) => {
        let obj = {}
        let nameArr = Object.keys(codeObj)
        let request = formFields.filter((el) => nameArr.includes(el.fieldName) && el.dictCode).map((rs) => initDictData(rs.dictCode, busAccount))
        Promise.all(request)
            .then(function (posts) {
                posts = posts.map((ts) => ts.result)
                posts.forEach((p, idx) => {
                    let name = `${nameArr[idx]}_text`
                    // 兼容单选和多选
                    obj[name] = p
                        .filter((f) => codeObj[nameArr[idx]].includes(f.value))
                        .map((tx) => tx.text)
                        .join(',')
                })
                resolve(obj)
            })
            .catch(function (reason) {
                reject(reason)
            })
    })
}
/**
 * @method
 * @param option 传入参数
 * @param url 下载url
 * @param id 可以在this.$refs.editPage.getPageData()里面取
 * @param handlerName {String} eg：'purchaseEnquiryItemExcelHandler'
 * @param roelCode {String} eg：'purchase'
 * @param callback {Function} 完成回调，处理loadingor其他
 * @desc 下载模板
 */
export function downloadTemplate (option) {
    let params = {
        id: option.id || '',
        handlerName: option.handlerName || '',
        roelCode: option.roelCode || '',
        data: option.data || ''
    }
    downloadFile(option, params)
}
/**
 * @method
 * @param option 传入参数
 * @param url 下载url
 * @param id 可以在this.$refs.editPage.getPageData()里面取
 * @param handlerName {String} eg：'purchaseEnquiryItemExcelHandler'
 * @param roelCode {String} eg：'purchase'
 * @param callback {Function} 完成回调，处理loadingor其他
 * @desc 下载模板（询价 成本模板专用）
 */
export function downloadCostTemplate (option) {
    let params = {
        busAccount: option.busAccount,
        account: option.account,
        id: option.itemId,
        templateNumber: option.templateNumber,
        templateVersion: option.templateVersion
    }
    downloadFile(option, params)
}
function downloadFile (option, params) {
    downFile(option.url, params)
        .then((data) => {
            if (!data) {
                this.$message.error('文件下载失败')
                return
            }
            if (typeof window.navigator.msSaveBlob !== 'undefined') {
                window.navigator.msSaveBlob(new Blob([data]), '模板.xlsx')
            } else {
                let url = window.URL.createObjectURL(new Blob([data]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', '模板.xlsx')
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }
        })
        .finally(() => {
            option.cb && option.cb()
        })
}
// 获取当前的所属的国际化账号
export function getLangAccount (account) {
    // 如果传入account,表示返回的所属的归属方
    if (Vue.ls.get(ACCESS_TOKEN)) {
        if (account) {
            return account
        } else {
            return Vue.ls.get(USER_ELS_ACCOUNT)
        }
    } else {
        return '100000'
    }
}
// 检查是否显示国际化默认值
export function srmI18n (i18nKey, defaultValue, isShowKey) {
    try {
        if (store.state.switchLanguage.currentLangData[`${i18nKey}`]) {
            return i18n.t(i18nKey)
        } else if (isShowKey) {
            return i18nKey
        } else {
            return defaultValue
        }
    } catch (error) {
        console.error(error)
    }
}
// 全局使用如：im获取其它静态模块
window.globalGetLangAccount = getLangAccount
window.globalSrmI18n = srmI18n

// 检查操作权限,返回true有权限，返回false无权限
export function hasOptAuth (authCode) {
    let companySet = this.$ls.get(USER_COMPANYSET) || {}
    if (companySet && companySet.permissionOpt === '0') {
        return true
    } else {
        try {
            if (store.state.user.sysAuth && store.state.user.sysAuth.length) {
                let flag = false
                store.state.user.sysAuth.forEach((auth) => {
                    if (auth.action === authCode) {
                        flag = true
                    }
                })
                return flag
            } else {
                return false
            }
        } catch (error) {
            console.error(error)
        }
    }
}
// 通过业务规则配置，是否隐藏按钮，true隐藏，false显示
export function hideBtnByBusinessRule (ruleCode) {
    let businessRuleList = this.$ls.get(SET_BUSINESS_RULE_LIST) || []
    if (businessRuleList && businessRuleList.length) {
        try {
            if (businessRuleList && businessRuleList.length && ruleCode) {
                let flag = false
                businessRuleList.forEach((rule) => {
                    if (rule.action === ruleCode) {
                        flag = true
                    }
                })
                return flag
            } else {
                return false
            }
        } catch (error) {
            console.error(error)
        }
    } else {
        return false
    }
}
export function injectScript (src, onload, async, id, onerror) {
    document.getElementById(id) && document.getElementById(id).remove()
    let head = document.getElementsByTagName('head')[0],
        script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = src
    script.async = async || false
    script.onload = onload || function () {}
    id ? (script.id = id) : ''
    script.onerror = onerror || function () {}
    head.appendChild(script)
}

/**
 * 递归扁平化数组
 *
 * @export
 * @param {*} [arr=[]]
 * @returns
 */
export function deepFlatten (arr = []) {
    let result = []
    arr.map((v) => {
        if (Array.isArray(v)) {
            result = result.concat(deepFlatten(v))
        } else {
            result.push(v)
        }
    })
    return result
}

/**
 * Scroll to top.
 *
 * @export
 * @param {*} [arr=[]]
 * @returns
 */
export const scrollsTop = () => {
    if (window) {
        try {
            // The New API.
            window.scroll({
                top: 0,
                left: 0,
                behavior: 'smooth'
            })
        } catch (error) {
            // For older browsers.
            window.scrollTo(0, 0)
        }
    }
}

/**
 * Compose 函数组合
 *
 * @export
 * @param {*} fns
 * @returns
 */
export function compose (...fns) {
    return function composed (result) {
        // 拷贝一份保存函数的数组
        let list = fns.slice()
        while (list.length > 0) {
            // 取出队列尾部最后一个函数
            // 并执行
            result = list.pop()(result)
        }
        return result
    }
}

/**
 * Compose 异步函数组合
 * 优化: 因为之前是倒序调用
 * 现改成顺序调用，更符合代码书写、阅读顺序(20220314)
 * @export
 * @param {*} fns
 * @returns
 */
export function composePromise (...fns) {
    const init = fns.shift()
    return function (...args) {
        return fns.reduce(function (sequence, func) {
            return sequence.then(function (result) {
                return func.call(null, result)
            })
        }, Promise.resolve(init.apply(null, args)))
    }
}

// 判断是否为对象（排除null）
export function isObject (obj) {
    return obj !== null && typeof obj === 'object'
}

// 判断是否为Promise对象
export function isPromise (val) {
    return val && typeof val.then === 'function'
}

/**
 * @description: 生成 promise
 * @param {*} fn
 * @return {*}
 */
export function createPromise (fn) {
    return new Promise((resolve, reject) => {
        fn && fn(resolve, reject)
    })
}

export function isFunction (method) {
    return Object.prototype.toString.call(method) === '[object Function]'
}

export function isObjectPlus (method) {
    return Object.prototype.toString.call(method) === '[object Object]'
}

export function makeMap (str) {
    let arr = str.split(',')
    let obj = {}
    arr.forEach((v) => {
        obj[v] = true
    })
    return function () {
        let args = arguments
        return obj[args[0]]
    }
}

export function debounce (fn, delay = 50) {
    let timer = null
    return function () {
        let args = arguments
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
            fn.apply(this, args)
        }, delay)
    }
}
// 数组根据某属性去重
export function removeRepet (list, key) {
    let map = new Map()
    list.forEach((item) => {
        if (!map.has(item[key])) map.set(item[key], item)
    })
    return [...map.values()]
}

export const handlePromise = (list = []) =>
    list.map((promise) =>
        promise.then(
            (res) => ({
                status: 'success',
                res
            }),
            (err) => ({
                status: 'error',
                err
            })
        )
    )

export const getObjType = (obj) => {
    let toString = Object.prototype.toString
    let map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object'
    }

    return map[toString.call(obj)]
}

/**
 * @description: 判断不等于 undefind 和 null
 * @param {*} v
 * @return {*}
 */
export const isDef = (v) => v !== undefined && v !== null

/**
 * @description: 精确显示小数后2位
 * @param {*} num
 * @return {*}
 */
export const keepTwoDecimalWithReg = (num) => {
    return Number(num.toString().match(/^\d+(?:\.\d{0,2})?/))
}

export function once (fn, context) {
    var result
    return function () {
        if (fn) {
            result = fn.apply(context || this, arguments)
            fn = null
        }
        return result
    }
}
export function bindfunctionMiddleware (tplRootRef, bindFunction, params) {
    function customFuncion (...args) {
        return (fn, ..._args) => {
            return tplRootRef[fn] && tplRootRef[fn](...args, ..._args)
        }
    }
    return bindFunction(tplRootRef, params, customFuncion(params))
}
// 业务模板绑定默认值-自定义变量值
export function bindDefaultValue (defaultValue) {
    let value = defaultValue
    if (defaultValue && /{{([^}}]+)?}}/g.test(defaultValue)) {
        let loginData = Vue.ls.get(LOGIN_RESULT_DATA)
        value = defaultValue.replace(/{{([^}}]+)?}}/g, (s1, s2) => {
            let fieldName = s2.trim()
            if (loginData && typeof loginData === 'object') {
                let levelKey = fieldName.split('.')
                if (levelKey && levelKey.length) {
                    let val = levelKey.reduce((data, currentValue) => {
                        return data[currentValue]
                    }, loginData)
                    return val
                } else {
                    return loginData[`${fieldName}`]
                }
            } else {
                return fieldName
            }
        })
    }
    return value
}

// 获取页面所有默认配置数据
export function getGroupDefaultData (config = {}, isBusiness = false) {
    console.log('isBusiness :>> ', isBusiness)
    let { groups = [] } = config
    let result = {}
    groups.forEach((item) => {
        if (!result[item.groupCode]) {
            result[item.groupCode] = {}
        }
        // 表格配置 与 表单配置字段不相同
        // 兼容 business-edit-layout 模板配置
        // let condition = !isBusiness
        //   ? item.type && item.type === "grid"
        //   : item.groupType === "item";
        if (!isBusiness) {
            if (item.type && item.type === 'grid') {
                let columns = (item.custom && item.custom.columns) || []
                result[item.groupCode] = columns.reduce((acc, obj) => {
                    if (obj.field) {
                        acc[obj.field] = obj.defaultValue || ''
                    }
                    return acc
                }, {})
            } else {
                let formFields = (item.custom && item.custom.formFields) || []
                result[item.groupCode] = formFields.reduce((acc, obj) => {
                    acc[obj.fieldName] = obj.defaultValue || ''
                    return acc
                }, {})
            }
        } else {
            if (item.groupType === 'item') {
                let columns = item.columns || []
                result[item.groupCode] = columns.reduce((acc, obj) => {
                    if (obj.field) {
                        acc[obj.field] = obj.defaultValue || ''
                    }
                    return acc
                }, {})
            } else {
                let formFields = item.formFields || []
                result[item.groupCode] = formFields.reduce((acc, obj) => {
                    acc[obj.fieldName] = obj.defaultValue || ''
                    return acc
                }, {})
            }
        }
    })

    return result
}
export function inputNumberBlurMethod (info) {
    let { value, fieldLabel, type, form, item, row, column, fieldType } = info
    if (String(value).length > NUMBER_MAX_LENGTH && fieldType === 'number') {
        const tips1 = srmI18n(`${getLangAccount()}#i18n_alert_JOKmWW_5ce500ee`, '校验失败，[')
        const tips2 = srmI18n(`${getLangAccount()}#i18n_alert_WWNRHzBRWL_4280512a`, ']输入值长度超过9位')
        message.error(`${tips1}${fieldLabel}${tips2}`)
        if (type === 'form') {
            // 表头
            form[item.fieldName] = ''
        } else {
            // 表行
            row[column.property] = ''
        }
    }
}
// 检查合同是否可编辑-增加可编辑区域$[edit]
export function checkEditContractTemplate (content) {
    content = content.replace(/(\$\[edit=?.*?\])|(\$\[edit\])/g, (s1, s2) => {
        let str = ''
        let part = s1
        if (/\$\[edit(=)?(.*)?\]/g.test(part)) {
            str = part.replace(/\$\[edit(=)?(.*)?\]/g, (s1, s2, s3) => {
                let value = s3
                return value ? `<i style="padding: 4px;font-style: normal;" contenteditable="true"> ${value} </i>` : ''
            })
        }
        return str
    })
    return content
}

//加载字体
export function loadFont (documentEln, row, name, url) {
    const fonts = documentEln.fonts
    let isAdd = 1
    let idx = 0
    fonts.forEach((v) => {
        idx++
        if (v.family == name) {
            isAdd = 0
        }
        if (idx == fonts.size && isAdd == 1 && row.status == '1') {
            const font = new FontFace(name, `url(${window.origin}/opt/upFiles${url})`)
            font.family = 'custom'
            font.load().then(() => {
                fonts.add(font)
            })
        }
    })
}

//移除某个字体
export function deleteCustomFont (documentEln, font) {
    const fonts = documentEln.fonts
    fonts.forEach((v) => {
        if (v.family == font.fontsName) {
            fonts.delete(v)
        }
    })
}

//清空字体
export function clearCustomFont (documentEln, name, url) {
    const fonts = documentEln.fonts
    fonts.clear()
}
//vxe-table select 下拉过滤方法
export function vxeFilterOption (optionText, searchValue, group) {
    return optionText.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0
}
//兼容edge下拉空白方法
export function nominalEdgePullWhiteBlack () {
    setTimeout(() => {
        const userAgent = navigator
        // console.log(userAgent?.userAgentData?.brands&&JSON.stringify (userAgent.userAgentData.brands).indexOf('Edge')>-1)
        if (userAgent?.userAgentData?.brands && JSON.stringify(userAgent.userAgentData.brands).indexOf('Edge') > -1) {
            // document.getElementById('Layout').addEventListener('touchstart', function (e){
            //     e.preventDefault()
            // })
            document.getElementsByTagName('html')[0].classList.add('htmlHidden')
            // })
        }
    }, 2000)
}

//需求池跳转公共方法提前
export function handleDemandPool (context) {
    context.showEditPage = false
    const query = context.$route.query
    if (query.source == 'demand-pool') {
        // 需求调整过来
        if (query?.result?.length == 1) {
            // 只有一条
            context.currentEditRow = query.result[0]
            context.showEditPage = true
            context.$store.dispatch('SetTabConfirm', true)
        }
    }
}
