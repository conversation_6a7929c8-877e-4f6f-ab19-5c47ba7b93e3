<template>
  <div class="detail-page">
    <div class="container">
      <a-spin :spinning="loading">
        <a-page-header
          :title="title"
          :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
        >
          <template slot="extra">
            <template v-if="not100000">
              <a-button
                @click="save"
                type="primary"
                v-if="!currentEditRow.newAdd && showSaveConfigBtn"
              >{{ $srmI18n(`${$getLangAccount()}#i18n__sMER_25b6c41c`, '保存配置') }}</a-button>
              <a-button
                :type="customPageFooterPreBtn.type"
                v-if="currentStep == 1"
                @click="customPageFooterPreBtn.click"
              >{{ customPageFooterPreBtn.title }}</a-button>
              <a-button
                v-else
                :type="customPageFooterNextBtn.type"
                @click="customPageFooterNextBtn.click"
              >{{ customPageFooterNextBtn.title }}</a-button>
            </template>
            <a-button
              @click="cancel"
            >{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </template>
        </a-page-header>
        <div class="content">
          <div
            class="baseInfo"
            v-if="currentStep == 0 && not100000">
            <a-form-model
              class="ant-advanced-search-form"
              ref="formModel"
              :rules="rules"
              :model="form"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
            >
              <div
                :key="'field_'+i"
                v-for="(item, i) in formFields">
                <a-form-model-item
                  v-if="item.fieldType==='input'"
                  :required="item.required"
                  :prop="item.fieldName"
                >
                  <span
                    slot="label">
                    {{ item.label }}
                    <a-tooltip
                      :title="tips"
                      v-if="item.slots">
                      <a-icon type="question-circle-o" />
                    </a-tooltip>
                  </span>
                  <a-input
                    v-model="form[item.fieldName]"
                    :disabled="item.disabled"
                    :placeholder="item.placeholder"
                  />
                </a-form-model-item>
                <a-form-model-item
                  v-if="item.fieldType==='select'"
                  :prop="item.fieldName"
                  :required="item.required"
                  :label="item.label"
                >
                  <m-select
                    v-model="form[item.fieldName]"
                    :dict-code="item.dictCode"
                    :disabled="item.disabled"
                    :placeholder="item.placeholder"
                  />
                </a-form-model-item>
              </div>
            </a-form-model>
          </div>
          <div
            class="modal"
            v-else>
            <iframe
              width="100%"
              :height="iframeHeight"
              style="min-height: 750px"
              frameborder="0"
              align="center"
              allowfullscreen="true"
              allow="autoplay"
              id="iframe"
              :src="modelerSrc"
            ></iframe>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
import { auditConfigAdd, auditConfigEdit, queryByByModelKey } from '../../api/analy.js'
import { ACCESS_TOKEN, DEFAULT_LANG } from '@/store/mutation-types'
export default {
    name: 'BpmModelerModal',
    props: {
        pageData: {
            type: Object,
            default: () => {}
        },
        currentEditRow: {
            type: Object,
            default: () => {}
        },
        not100000: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            tips: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KvWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWW_35f9739`, '示例："${templateNumber}"=="TC2022091604"'),
            fixPageHeader: '',
            iframeHeight: window.innerHeight - 100 + 'px',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormade`, '基础信息'),
            currentStep: 0,
            form: {},
            formDemo: {},
            modelerSrc: '',
            labelCol: { span: 4 },
            wrapperCol: { span: 14 },
            tokenHeader: this.$ls.get(ACCESS_TOKEN),
            loading: false,
            showSaveConfigBtn: true,
            customPageFooterPreBtn: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_prevStep`, '上一步'),
                type: 'primary',
                belong: 'preStep',
                click: this.prevStep
            },
            customPageFooterNextBtn: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nextStep`, '下一步'),
                type: 'primary',
                belong: 'nextStep',
                click: this.nextStep
            },
            rules: {
                businessType: [
                    {
                        required: true,
                        message: this.$srmI18n(
                            `${this.$getLangAccount()}#i18n_field_VSMESAc_8ffee2bc`,
                            '请填写业务类型'
                        ),
                        trigger: 'change'
                    }
                ],
                processName: [
                    {
                        required: true,
                        message: this.$srmI18n(
                            `${this.$getLangAccount()}#i18n_field_VSMQLRL_9eb75032`,
                            '请填写流程名称'
                        ),
                        trigger: 'blur'
                    }
                ],
                beanName: [
                    {
                        required: true,
                        message: this.$srmI18n(
                            `${this.$getLangAccount()}#i18n_field_VSMKEAWWWWRL_b2df3cab`,
                            '请填写适配器Bean名称'
                        ),
                        trigger: 'change'
                    }
                ]
            },
            formFields: [
                {
                    fieldType: 'select',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型'),
                    fieldName: 'businessType',
                    dictCode: 'srmAuditBussinessType',
                    defaultValue: '',
                    // required: true,
                    placeholder: '',
                    disabled: false
                },
                {
                    fieldType: 'input',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_processName`, '流程名称'),
                    fieldName: 'processName',
                    dictCode: '',
                    defaultValue: '',
                    placeholder: '',
                    // required: true,
                    disabled: false
                },
                {
                    fieldType: 'select',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KEAWWWWRL_bbf59fd0`, '适配器Bean名称'),
                    // fieldLabelI18nKey: 'i18n_field_blocDel',
                    fieldName: 'beanName',
                    dictCode: 'auditOptImpl',
                    // required: true,
                    defaultValue: '',
                    disabled: false
                },
                {
                    fieldType: 'input',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QLBfK_3d22004f`, '流程表达式'),
                    fieldName: 'processExpression',
                    dictCode: '',
                    defaultValue: '',
                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KvWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWW_35f9739`, '示例："${templateNumber}"=="TC2022091604"'),
                    disabled: false,
                    slots: true
                }
            ]
        }
    },
    methods: {
        save (){
            this.loading = true
            let fn = this.form.id ? auditConfigEdit : auditConfigAdd
            let params = Object.assign({}, this.form)
            fn(params)
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.loading = false
                })
        },
        cancel () {
            this.$emit('cancel')
        },
        prevStep () {
            this.currentStep = 0
            this.showSaveConfigBtn = true
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormade`, '基础信息')
        },
        nextStep () {
            this.$refs['formModel'].validate((valid) => {
                if (valid) {
                    // 存在第一次进入this.$ls.get(DEFAULT_LANG)为空就为中文
                    let lang = this.$ls.get(DEFAULT_LANG) ? (this.$ls.get(DEFAULT_LANG) == 'zh' ? 'zh_CN' : 'en-US') : 'zh_CN'
                    this.showSaveConfigBtn = false
                    if (
                        this.form.businessType === this.formDemo.businessType &&
                        this.form.processName === this.formDemo.processName &&
                        this.form.beanName === this.formDemo.beanName &&
                        this.form.processExpression === this.formDemo.processExpression
                    ) {
                        this.currentStep = 1
                        this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_QLGt_33859a0d`, '流程设计')
                        this.modelerSrc = `/els/bpmns/diagram/index.html?id=${this.form.modelId}&messageId=${this.tokenHeader}&edit=false&languageType=${lang}`
                    } else {
                        this.loading = true
                        let fn = this.form.id ? auditConfigEdit : auditConfigAdd
                        let params = Object.assign({}, this.form)
                        fn(params)
                            .then((res) => {
                                if (res.success) {
                                    this.form = JSON.parse(JSON.stringify(res.result))
                                    this.formDemo = JSON.parse(JSON.stringify(res.result))
                                    this.currentStep = 1
                                    this.title = this.$srmI18n(
                                        `${this.$getLangAccount()}#i18n_menu_QLGt_33859a0d`,
                                        '流程设计'
                                    )
                                    this.modelerSrc = `/els/bpmns/diagram/index.html?id=${this.form.modelId}&messageId=${this.tokenHeader}&edit=true&languageType=${lang}`
                                }
                            })
                            .finally(() => {
                                this.loading = false
                            })
                    }
                }
            })
        }
    },
    created () {
        let lang = this.$ls.get(DEFAULT_LANG) == 'zh' ? 'zh_CN' : 'en-US'
        if (this.currentEditRow.modelKey) {
            if ([1, 3].includes(this.currentEditRow.status)) {
                this.formFields[0].disabled = true
                this.formFields[1].disabled = true
                this.formFields[3].disabled = true
            }
            if (this.not100000) {
                this.loading = true
                queryByByModelKey(this.currentEditRow.modelKey)
                    .then((res) => {
                        if (res.success) {
                            this.formDemo = JSON.parse(JSON.stringify(res.result))
                            this.form = JSON.parse(JSON.stringify(res.result))
                            this.modelerSrc = `/els/bpmns/diagram/index.html?id=${this.form.modelId}&messageId=${this.tokenHeader}&edit=false&languageType=${lang}`
                        } else {
                            this.$message.error(res.message)
                            this.cancel()
                        }
                    })
                    .finally(() => {
                        this.loading = false
                    })
            } else {
                this.modelerSrc = `/els/bpmns/diagram/index.html?id=${this.currentEditRow.id}&messageId=${this.tokenHeader}&edit=false&languageType=${lang}`
            }
        }
    }
}
</script>