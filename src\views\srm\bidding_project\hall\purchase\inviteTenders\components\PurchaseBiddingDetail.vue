<template>
  <div class="PurchaseBiddingDetailView">
    <div class="page-container">
      <detail-layout
        ref="detailPage"
        useLocalModelLayout
        modelLayout="unCollapse"
        :page-data="pageData"
        :current-edit-row="vuex_currentEditRow"
        :url="url"
      />
    </div>

    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />

    <field-select-modal ref="fieldSelectModal" />

    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="vuex_currentEditRow"/>

  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {postAction, getAction} from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import flowViewModal from '@comp/flowView/flowView'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    mixins: [DetailMixin],
    components: {
        flowViewModal,
        fieldSelectModal
    },
    data () {
        return {
            flowView: false,
            flowId: '',
            currentBasePath: this.$variateConfig['domainURL'],
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseBiddingItemList',
                        columns: [],
                        buttons: [{title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplementaryMaterial`, '补充物料'), click: this.replenishMaterialNumber}]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                        ref: 'biddingSupplierList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'), field: 'supplierCode', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 300 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'), field: 'replyStatus_dictText', width: 100},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'), field: 'replyTime', width: 140},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'), field: 'contacts', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'), field: 'phone', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mail`, '邮件'), field: 'email', width: 150},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewBidsPermission`, '查看标书权限'), field: 'bidCheck_dictText', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidAuthority`, '投标权限'), field: 'bidQuote_dictText', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_wjAc_30ae977b`, '来源类型'), field: 'sourceType_dictText', width: 120}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_participants`, '参与人员'), groupCode: 'specialistInfo', type: 'grid', custom: {
                        ref: 'purchaseBiddingSpecialistList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), width: 120},
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), width: 120 },
                            { field: 'memberType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberType`, '成员类型'), width: 220 },
                            { field: 'memberRole_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberRole`, '成员角色'), width: 220 },
                            { field: 'specialistClasses_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistType`, '专家类型'), width: 220 },
                            { field: 'mobileTelephone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话'), width: 220 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'), width: 220 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 220 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 140 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            // { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocationApproval`, '审批撤销'), type: 'danger', click: this.auditCancel, showCondition: this.auditCancelConditionBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow, showCondition: this.showFlowConditionBtn}
                    // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/bidding/purchaseBiddingHead/queryById',
                cancel: '/a1bpmn/audit/api/cancel',
                replenish: '/bidding/purchaseBiddingHead/replenishMaterialNumber'
            }
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        fileSrc () {
            const {
                templateNumber = '',
                templateVersion = '',
                templateAccount = '',
                busAccount = ''
            } = this.vuex_currentEditRow || {}

            const configFiles = this.$variateConfig['configFiles']
            const time = +new Date()
            const url = `${configFiles}/${templateAccount || busAccount}/purchase_bidding_${templateNumber}_${templateVersion}.js?t=`+time
            return url
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        afterHandleData (config) {
            const groups = config.groups || []

            // 编辑页面时，项目信息/分包信息/招标行信息分组隐藏，评标专家分组去掉；
            // 查看页面时，项目信息/分包信息/招标行信息分组显示，评标专家分组去掉；

            let codes = [
                // 'projectForm', // 项目信息
                // 'subcontractForm', // 分包信息
                // 'itemInfo', // 招标行信息
                'specialistInfo' // 评标专家
            ]
            config.groups = groups.filter(n => !codes.includes(n.groupCode))
        },
        preViewEvent (row){
            let fromData = this.vuex_currentEditRow
            if(row.fileType == '2' && (fromData.biddingStatus === '1' || fromData.biddingStatus === '0')){
                this.$message.warning('开标前不允许查看投标文件！')
                return
            }
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        auditCancelConditionBtn (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let auditStatus = params.auditStatus
            if(auditStatus == '1'){
                return true
            }else{
                return false
            }
        },
        showFlowConditionBtn (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let auditStatus = params.auditStatus
            if(auditStatus =='1' || auditStatus =='2' || auditStatus =='3'){
                return true
            }else{
                return false
            }
        },
        showFlow (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            this.flowId = params.flowId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        downloadEvent (row) {
            this.downloadFile(row)
        },
        handleDownload ({ id, fileName }) {
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        downloadFile (row){
            let fromData = this.vuex_currentEditRow
            if(row.fileType == '2' && (fromData.biddingStatus === '1' || fromData.biddingStatus === '0')){
                this.$message.warning('开标前不允许下载投标文件！')
                return
            }
            this.handleDownload(row)
        },
        auditCancel () {
            const callback = () => {
                let param = {
                    businessType: 'publishBidding',
                    businessId: this.vuex_currentEditRow.id,
                    rootProcessInstanceId: this.vuex_currentEditRow.flowId
                }
                this.confirmLoading = true
                postAction(this.url.cancel, param).then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                    this.$emit('routerRefreshEvent')
                })
            }

            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLtT_392b88b3`, '是否确认继续'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        replenishMaterialNumber (){
            let records = this.$refs.detailPage.$refs.purchaseBiddingItemList[0].getCheckboxRecords() || []
            if(records.length !== 1){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectARowDataOperate`, '请选择一条行数据进行操作'))
                return
            }
            let row = JSON.parse(JSON.stringify(records[0]))
            if(row.materialNumber){
                this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_material`, '物料')}：` + row.materialDesc + `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_codeAlreadyExistsNoNeedSupplement`, '已存在编码，无需补充！')}`)
                return
            }
            let columns = [
                {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150},
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150},
                {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200},
                {field: 'brand', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialBrand`, '物料品牌'), width: 200},
                {field: 'purchaseCycle', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), width: 200},
                {field: 'purchaseType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseType`, '采购类型'), width: 200},
                {field: 'checkWay_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkWay`, '检验方式'), width: 200},
                {field: 'purchaseUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingUnit`, '采购单位'), width: 200},
                {field: 'materialModel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialModel`, '物料型号'), width: 200},
                {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'), width: 200}
            ]
            this.$refs.fieldSelectModal.open('/material/purchaseMaterialHead/list', {}, columns, 'single')
        },
        fieldSelectOk (data) {
            let records = this.$refs.detailPage.$refs.purchaseBiddingItemList[0].getCheckboxRecords()
            var row = JSON.parse(JSON.stringify(records[0]))
            row.materialNumber = data[0].materialNumber
            const _this = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, '是否确认将物料')}：` + row.materialDesc + `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_encodingUpdatedTo`, '的编码更新为')}：` + data[0].materialNumber,
                onOk () {
                    _this.$refs.detailPage.showLoading()
                    row.materialDesc = data[0].materialDesc
                    row.materialGroup = data[0].materialGroup
                    row.materialGroupName = data[0].materialGroupName
                    row.materialModel = data[0].materialModel
                    row.materialSpec = data[0].materialSpec
                    row.materialName = data[0].materialName
                    row.materialId = data[0].id
                    row.materialModel = data[0].materialModel
                    postAction(_this.url.replenish, row).then((res) => {
                        if (res.success) {
                            _this.$message.success(res.message)
                            _this.init()
                        } else {
                            _this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        _this.$refs.detailPage.hideLoading()
                    })
                }
            })
        }
    }
}
</script>
