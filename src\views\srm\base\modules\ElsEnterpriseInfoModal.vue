<template>
  <div class="els-page-comtainer">
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      @goBack="goBack"
    />
  </div>
</template>

<script>
import { duplicateCheck } from '@/api/api'
import { tileEditPageMixin } from '@comp/template/tileStyle/tileEditPageMixin'

export default {
    name: 'ElsEnterpriseInfoModal',
    mixins: [tileEditPageMixin],
    data () {
        return {
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterpriseInfo`, '企业基本信息'),
                form: {
                    percentileScore: '',
                    staffNumRange: '',
                    fromTime: '',
                    type: '',
                    bondBame: '',
                    microEnt: '',
                    usedBondName: '',
                    regNumber: '',
                    regCapital: '',
                    name: '',
                    regInstitute: '',
                    regLocation: '',
                    industry: '',
                    approvedTime: '',
                    updateTimes: '',
                    socialStaffNum: '',
                    tags: '',
                    taxNumber: '',
                    businessScope: '',
                    property3: '',
                    alias: '',
                    orgNumber: '',
                    regStatus: '',
                    establishTime: '',
                    bondType: '',
                    legalPersonName: '',
                    toTime: '',
                    actualCapital: '',
                    companyOrgType: '',
                    base: '',
                    creditCode: '',
                    historyNames: '',
                    historyNameList: '',
                    bondNum: '',
                    regCapitalCurrency: '',
                    actualCapitalCurrency: '',
                    email: '',
                    websiteList: '',
                    phoneNumber: '',
                    revokeDate: '',
                    revokeReason: '',
                    cancelDate: '',
                    cancelReason: '',
                    city: '',
                    district: '',
                    category: '',
                    categoryBig: '',
                    categoryMiddle: '',
                    categorySmall: ''
                },
                panels: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            ref: 'baseForm',
                            colSpan: '24',
                            type: 'form',
                            form: {},
                            list: [
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_percentileScore`, '企业评分'),
                                    fieldName: 'percentileScore', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterPercentileScoreTips`, '请输入企业评分')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_staffNumRange`, '人员规模'),
                                    fieldName: 'staffNumRange', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterStaffNumRangetips`, '请输入人员规模')
                                },
                                {
                                    type: 'date', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_manageFromTime`, '经营开始时间'),
                                    fieldName: 'fromTime', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterManageFromTimeTips`, '请输入经营开始时间')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_legalPersonType`, '法人类型，1 人 2 公司'),
                                    fieldName: 'type', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterLegalPersonType`, '请输入法人类型，1 人 2 公司')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bondBame`, '股票名'),
                                    fieldName: 'bondBame', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterBondBameTips`, '请输入股票名')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_smallEnterprise`, '是否是小微企业 0不是 1是'),
                                    fieldName: 'microEnt', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterSmallEnterpriseTips`, '请输入是否是小微企业 0不是 1是')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_usedBondName`, '股票曾用名'),
                                    fieldName: 'usedBondName', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterUsedBondNameTips`, '请输入股票曾用名')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regNumber`, '注册号'),
                                    fieldName: 'regNumber', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterRegNumberTips`, '请输入注册号')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regCapital`, '注册资本'),
                                    fieldName: 'regCapital', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterRegCapitalTips`, '请输入注册资本')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterpriseName`, '企业名称'),
                                    fieldName: 'name', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseWriteEnterpriseName`, '请输入企业名')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regInstitute`, '登记机关'),
                                    fieldName: 'regInstitute', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterRegInstituteTips`, '请输入登记机关')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regLocation`, '注册地址'),
                                    fieldName: 'regLocation', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterRegLocationTips`, '请输入注册地址')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_industry`, '行业'),
                                    fieldName: 'industry', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterIndustryTips`, '请输入行业')
                                },
                                {
                                    type: 'date', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedTime`, '核准时间'),
                                    fieldName: 'approvedTime', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterApprovedTimeTips`, '请输入核准时间')
                                },
                                {
                                    type: 'date', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_updateTimes`, '修改时间'),
                                    fieldName: 'updateTimes', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterUpdateTimesTips`, '请输入修改时间')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_socialStaffNum`, '参保人数'),
                                    fieldName: 'socialStaffNum', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterSocialStaffNumTips`, '请输入参保人数')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_percentileTags`, '企业标签'),
                                    fieldName: 'tags', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterPercentileTagsTips`, '请输入企业标签')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxpayerNum`, '纳税人识别号'),
                                    fieldName: 'taxNumber', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxpayerNum`, '请输入纳税人识别号')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessScope`, '经营范围'),
                                    fieldName: 'businessScope', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterBusinessScope`, '请输入经营范围')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_englishName`, '英文名'),
                                    fieldName: 'property3', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterEnglishName`, '请输入英文名')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_alias`, '简称'),
                                    fieldName: 'alias', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterAlias`, '请输入简称')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationCode`, '组织机构代码'),
                                    fieldName: 'orgNumber', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterOrganizationCode`, '请输入组织机构代码')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterpriseStatus`, '企业状态'),
                                    fieldName: 'regStatus', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterEnterpriseStatus`, '请输入企业状态')
                                },
                                {
                                    type: 'date', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_estiblishTime`, '成立日期'),
                                    fieldName: 'establishTime', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterEstablishTime`, '请输入成立日期')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stockType`, '股票类型'),
                                    fieldName: 'bondType', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterStockType`, '请输入股票类型')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_legalPersonName`, '法人'),
                                    fieldName: 'legalPersonName', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterLegalPersonName`, '请输入法人')
                                },
                                {
                                    type: 'date', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationEndTime`, '经营结束时间'),
                                    fieldName: 'toTime', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterOperationEndTime`, '请输入经营结束时间')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_actualCapital`, '实收注册资金'),
                                    fieldName: 'actualCapital', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterActualCapital`, '请输入实收注册资金')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enterpriseType`, '企业类型'),
                                    fieldName: 'companyOrgType', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterCompanyOrgType`, '请输入企业类型')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_provinceAbbreviation`, '省份简称'),
                                    fieldName: 'base', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterProvinceAbbreviation`, '请输入省份简称')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creditCode`, '统一社会信用代码'),
                                    fieldName: 'creditCode', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterCreditCode`, '请输入统一社会信用代码')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_historyNames`, '曾用名'),
                                    fieldName: 'historyNames', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterHistoryNames`, '请输入曾用名')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_historyNames`, '曾用名'),
                                    fieldName: 'historyNameList', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterHistoryNames`, '请输入曾用名')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stockCode`, '股票号'),
                                    fieldName: 'bondNum', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterStockCode`, '请输入股票号')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regCapitalCurrency`, '注册资本币种 人民币 美元 欧元 等'),
                                    fieldName: 'regCapitalCurrency', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterRegCapitalCurrency`, '请输入注册资本币种 人民币 美元 欧元 等')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_actualCapitalCurrency`, '实收注册资本币种 人民币 美元 欧元 等'),
                                    fieldName: 'actualCapitalCurrency', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterActualCapitalCurrency`, '请输入实收注册资本币种 人民币 美元 欧元 等')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'),
                                    fieldName: 'email', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseWriteEmail`, '请输入邮箱')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_website`, '网址'),
                                    fieldName: 'websiteList', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterWebsite`, '请输入网址')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterpriseContactInfo`, '企业联系方式'),
                                    fieldName: 'phoneNumber', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterEnterpriseContactInfo`, '请输入企业联系方式')
                                },
                                {
                                    type: 'date', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeDate`, '吊销日期'),
                                    fieldName: 'revokeDate', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterRevokeDate`, '请输入吊销日期')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeReason`, '吊销原因'),
                                    fieldName: 'revokeReason', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterRevokeReason`, '请输入吊销原因')
                                },
                                {
                                    type: 'date', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cancelDate`, '注销日期'),
                                    fieldName: 'cancelDate', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterCancelDate`, '请输入注销日期')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cancelReason`, '注销原因'),
                                    fieldName: 'cancelReason', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterCancelReason`, '请输入注销原因')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_city`, '市'),
                                    fieldName: 'city', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterCity`, '请输入市')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_district`, '区'),
                                    fieldName: 'district', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDistrict`, '请输入区')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nationalCategory`, '国民经济行业分类门类'),
                                    fieldName: 'category', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterNationalCategory`, '请输入国民经济行业分类门类')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nationalBigCategory`, '国民经济行业分类大类'),
                                    fieldName: 'categoryBig', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterNationalBigCategory`, '请输入国民经济行业分类大类')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nationalMiddleCategory`, '国民经济行业分类中类'),
                                    fieldName: 'categoryMiddle', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterNationalMiddleCategory`, '请输入国民经济行业分类中类')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nationalSmallCategory`, '国民经济行业分类小类'),
                                    fieldName: 'categorySmall', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterNationalSmallCategory`, '请输入国民经济行业分类小类')
                                }
                            ]}}
                ],
                validRules: {
                    staffNumRange: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符')}],
                    type: [{max: 1, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow1`, '内容长度不能超过1个字符')}],
                    bondBame: [{max: 20, message: '内容长度不能超过20个字符'}],
                    microEnt: [{max: 1, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow1`, '内容长度不能超过1个字符')}],
                    usedBondName: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')}],
                    regNumber: [{max: 31, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow31`, '内容长度不能超过31个字符')}],
                    regCapital: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    name: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    regInstitute: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    regLocation: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    industry: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    tags: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    taxNumber: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    businessScope: [{max: 4091, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow4091`, '内容长度不能超过4091个字符')}],
                    property3: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    alias: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    orgNumber: [{max: 31, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow31`, '内容长度不能超过31个字符')}],
                    regStatus: [{max: 31, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow31`, '内容长度不能超过31个字符')}],
                    bondType: [{max: 31, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow31`, '内容长度不能超过31个字符')}],
                    legalPersonName: [{max: 120, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow120`, '内容长度不能超过120个字符')}],
                    actualCapital: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    companyOrgType: [{max: 127, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow127`, '内容长度不能超过127个字符')}],
                    base: [{max: 31, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow31`, '内容长度不能超过31个字符')}],
                    creditCode: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    historyNames: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    historyNameList: [{max: 65535, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow65535`, '内容长度不能超过65535个字符')}],
                    bondNum: [{max: 20, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow20`, '内容长度不能超过20个字符')}],
                    regCapitalCurrency: [{max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符')}],
                    actualCapitalCurrency: [{max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符')}],
                    email: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    websiteList: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    phoneNumber: [{max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}],
                    revokeReason: [{max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}],
                    cancelReason: [{max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}],
                    city: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符')}],
                    district: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符')}],
                    category: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符')}],
                    categoryBig: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符')}],
                    categoryMiddle: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符')}]
                }
            },
            url: {
                add: '/system/elsEnterpriseInfo/add',
                edit: '/system/elsEnterpriseInfo/edit',
                detail: '/system/elsEnterpriseInfo/queryById'
            }
        }
    },
    methods: {
        validateCode (rule, value, callback) {
            // 重复校验
            var params = {
                tableName: '',
                fieldName: '',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then(res => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        selectCallBack (item) {
            this.pageData.form.dictCode = item[0].dictCode
            this.$refs.editPage.$forceUpdate()
        }
    }
}
</script>

<style lang="less" scoped>
</style>