<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterRemoteConfigData="handleAfterRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>

  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'

export default {
    name: 'SaleContractAcceptanceDetail',
    components: {
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
            }
        }
    },
    data () {
        return {
            requestData: {
                detail: {
                    url: '/contract/saleContractAcceptance/queryById', args: (that) => {
                        return {id: that.currentEditRow.id}
                    }
                }
            },
            externalToolBar: {
                saleAttachmentList: [
                    {...new BatchDownloadBtn({pageCode: 'saleAttachmentList'}).btnConfig}
                ]
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ]
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_contractAcceptance_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        // 文件下载
        downloadEvent (vue, row) {
            console.log(row)
            if (!row.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseUploadFile`, '请上传文件'))
                return
            }
            const params = {id: row.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        // 文件预览
        preViewEvent (vue, row) {
            if (!row.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseUploadFile`, '请上传文件'))
                return
            }
            let preViewFile = {id: row.id, fileName: row.fileName}
            this.$previewFile.open({params: preViewFile})
        },
        // 配置数据组装前，传入需要组装数据
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {

        },
        handleAfterRemoteConfigData (data) {
            return data
        }
    }
}
</script>
