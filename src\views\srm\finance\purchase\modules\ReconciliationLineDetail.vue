<!--
 * @Author: your name
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2021-07-26 17:34:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\purchase\modules\PurchaseEditCost.vue
-->
<template>
  <a-modal
    v-drag    
    v-if="showVisible"
    :visible="showVisible"
    :width="800"
    :height="160"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_statementDetails`, '对账单详情')"
    @cancel="showVisible=false"
    @ok="showVisible=false">
    <div
      class="page-container"
      style="height: 500px;overflow:scroll">
      <business-layout
        ref="containerRef"
        :currentEditRow="currentEditRow"
        :pageStatus="containerStatus"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigDataFunc"
        :requestData="requestData"
      >
      </business-layout>
    </div>
  </a-modal>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
export default {
    name: 'ReconciliationLineDetail',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            containerStatus: 'detail',
            showVisible: false,
            sourceNumber: []
        }
    },
    computed: {
        requestData () {
            let id =this.sourceNumber
            return {
                detail: {
                    url: '/reconciliation/purchaseReconciliation/queryReconciliationItemList',
                    method: 'get',
                    args: ()=> {
                        return {
                            id: id,
                            sourceType: this.sourceType

                        }
                    }
                }
            }
        }
    },
    methods: {
        open (data){
            this.showVisible = true
            this.sourceNumber = data
        },
        handleBeforeRemoteConfigDataFunc () {
     
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receiptVoucher`, '收货凭证'),
                        groupCode: 'purchaseRecAcceptReturnList',
                        groupType: 'item',
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_additionalCost`, '附加费用'),
                        groupCode: 'purchaseRecAdditionalChargesList',
                        groupType: 'item',
                        sortOrder: '2'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deductionDetails`, '扣款明细'),
                        groupCode: 'purchasePaymentChargeList',
                        groupType: 'item',
                        sortOrder: '3'
                    }
                ],
                itemColumns: [
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reconciliationNo`, '对账单号'), field: 'reconciliationNumber', width: 150, visible: false},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItemType`, '行项目类型'), field: 'itemType_dictText', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voucherNo`, '凭证号'), field: 'voucherNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voucherWalkNo`, '凭证行号'), field: 'voucherItemNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_movementType`, '移动类型'), field: 'mobileType', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lendingDirection`, '借贷方向'), field: 'directionBorrowing', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voucherQuantity`, '凭证数量'), field: 'voucherQuantity', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), field: 'orderNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), field: 'orderItemNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseMaterialCode`, '采购物料编码'), field: 'materialNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseMaterialDescription`, '采购物料描述'), field: 'materialDesc', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseMaterialSpecification`, '采购物料规格'), field: 'materialSpec', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_salesItemCode`, '销售物料编码'), field: 'saleMaterialNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_salesItemDescription`, '销售物料描述'), field: 'saleMaterialDesc', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SalesmaterialSpecification`, '销售物料规格'), field: 'saleMaterialSpec', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), field: 'quantity', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityUnit`, '数量单位'), field: 'unitQuantity', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_priceUnit`, '价格单位'), field: 'priceUnit', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'), field: 'price', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceExcludingTax`, '不含税单价'), field: 'noTaxUnitPrice', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), field: 'totalAmount', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountExcludingTax`, '不含税金额'), field: 'totalNonTaxAmount', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_referenceDocumentYear`, '参考凭证年份'), field: 'refVoucherYear', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_referenceDocumentNo`, '参考凭证号'), field: 'refVoucherNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_referenceWalkDocumentNo`, '参考凭证行号'), field: 'refVoucherItemNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shipmentReturnDocNo`, '发/退货单号'), field: 'deliveryNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shipmentReturnWalkDocNo`, '发/退货行号'), field: 'deliveryItemNumber', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fiscalYear`, '会计年度'), field: 'accountingYear', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_postingDate `, '过账日期'), field: 'postingTime', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paymentWay`, '付款方式'), field: 'payWay_dictText', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_voucherDate `, '凭证日期'), field: 'voucherTime', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocationCode`, '库存地点编码'), field: 'inventoryAddress', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocationDes`, '库存地点描述'), field: 'inventoryAddressDesc', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'), field: 'taxCode', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), field: 'taxRate', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasersFactory`, '采购方工厂'), field: 'purchaseFactory_dictText', width: 150},
                    { groupCode: 'purchaseRecAcceptReturnList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currency`, '采购方工厂'), field: 'currency_dictText', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '采购方工厂'), field: 'toElsAccount', width: 150, visible: false},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reconciliationNo`, '对账单号'), field: 'reconciliationNumber', width: 150, visible: false},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItemType`, '行项目类型'), field: 'itemType_dictText', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_costNumber`, '费用单号'), field: 'voucherNumber', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'), field: 'source', width: 150, visible: false},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'), field: 'source_dictText', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_relationType`, '关联单据类型'), field: 'businessType_dictText', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), field: 'totalAmount', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountExcludingTax`, '不含税金额'), field: 'totalNonTaxAmount', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_coastStyle`, '费用类型'), field: 'costType_dictText', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_coastDate`, '费用日期'), field: 'costTime', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'), field: 'taxCode', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), field: 'taxRate', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currency`, '采购方工厂'), field: 'currency_dictText', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'), field: 'company_dictText', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingOrganization`, '采购组织'), field: 'purchaseOrg_dictText', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_costReason`, '费用原因'), field: 'costReason_dictText', width: 150},
                    { groupCode: 'purchaseRecAdditionalChargesList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_costDescription`, '费用说明'), field: 'costInstruction', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItemType`, '行项目类型'), field: 'itemType_dictText', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deductionNumber`, '扣款单号'), field: 'voucherNumber', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qty`, '数量'), field: 'voucherQuantity', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_source`, '来源'), field: 'source_dictText', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_relationType`, '关联单据类型'), field: 'businessType_dictText', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), field: 'totalAmount', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deductionType`, '扣款类型'), field: 'deductionType_dictText', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reasonDeduction`, '扣款原因'), field: 'deductionsReason_dictText', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deductionDescription`, '扣款说明'), field: 'deductionsInstructions', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'), field: 'company_dictText', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingOrganization`, '采购组织'), field: 'purchaseOrg_dictText', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deductionDate`, '扣款日期'), field: 'voucherTime', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'), field: 'taxCode', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), field: 'taxRate', width: 150},
                    { groupCode: 'purchasePaymentChargeList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currency`, '采购方工厂'), field: 'currency_dictText', width: 150}
                
                ]
            }
        }
    }
}
</script>