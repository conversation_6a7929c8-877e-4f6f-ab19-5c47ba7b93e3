<template>
  <a-modal
    v-drag
    centered
    :title="$srmI18n(`${$getLangAccount()}#i18n_field_uNWF_2b0e8aa7`, '导入数据')"
    :width="720"
    :visible="visible"
    :maskClosable="false"
    @ok="ok"
    :footer="null"
    @cancel="close">
    <a-steps
      v-if="!isLoading"
      v-model="current"
      @change="onChange">
      <a-step
        disabled
        key="step-1"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_improt`, '导入')">
        <a-icon
          slot="icon"
          type="import" />
      </a-step>
      <a-step
        v-if="isPreview === 1"
        disabled
        key="step-preview"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_WFUB_2fa4a642`, '数据预览')">
        <a-icon
          slot="icon"
          type="container" />
      </a-step>
      <a-step
        disabled
        key="step-2"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_ML_b7804`, '完成')">
        <a-icon
          slot="icon"
          type="smile-o" />
      </a-step>
    </a-steps>
    <a-spin :spinning="confirmLoading">
      <div
        style="margin-top:12px"
        class="steps-content">
        <div
          v-if="current === 0"
          v-show="!isLoading"
          class="step-2">
          <div class="import-desc">
            <h3>{{ $srmI18n(`${$getLangAccount()}#i18n_field_uNlR_2b133743`, '导入说明') }}：</h3>
            <ul>
              <li style="color: red;font-weight: bold;">{{ $srmI18n(`${$getLangAccount()}#`, '单次导入数量建议控制在5000以内') }}；</li>
              <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_QIfXxOBR_f2502a49`, '文件大小不能超过') }} 10M；</li>
              <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_HRuWWWWWWWWWWWWWWmKQI_121e8f6`, '仅支持 *.xls、*.xlsx 格式文件') }}；</li>
              <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_qICtNv_c65982cb`, '可以点击这里') }}，<a
                href="#"
                @click="downloadFile">{{ $srmI18n(`${$getLangAccount()}#i18n_title_downloadTemplate`, '下载模板') }}!</a>
              </li></ul>
          </div>
          <a-upload-dragger
            name="file"
            :headers="uploadHeader"
            :accept="accept"
            :action="importUrl"
            @change="imortChangeEvent"
            :beforeUpload="beforeUpload"
          >
            <p class="ant-upload-drag-icon">
              <a-icon type="inbox" />
            </p>
            <p class="ant-upload-text">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_CtSdQIenuNmMUXV_d2c66d10`, '点击或把文件拖拽到这个区域上传') }}
            </p>
          </a-upload-dragger>
        </div>
        <div v-if="isPreview === 1 && current === 1 && !isLoading">
          <vxe-grid
            border
            auto-resize 
            resizable
            column-key
            row-id="id"
            highlight-hover-row
            show-overflow
            ref="previewGrid"
            height="220"
            size="mini"
            :columns="tableColumns"
            :data="tableData"
            header-align="center">
          </vxe-grid>
          <div style="margin-top:6px;display: flex;align-items:flex-end">
            <div
              style="flex:1;margin-right: 15px;display: flex;"
              v-if="importPreviewFilesData.errorId">
              <div style="margin-right: 15px;" >
                <p style="margin-bottom:5px"> {{ $srmI18n(`${$getLangAccount()}#i18n_alert_uNHz_2b13a8d4`, '导入进度') }}</p>
                <p>
                  <a-icon
                    type="close-circle"
                    style="margin-right: 5px;color: #F5222D;" />
                  <span class="error">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_uNKm_2b0d38bd`, '导入失败') }}!</span>
                </p>
              </div>
              <div>
                <p style="margin-bottom:5px" >
                  <a-progress
                    :percent="100"
                    status="active"/></p>
                <div class="import-error" >
                  <p> {{ $srmI18n(`${$getLangAccount()}#i18n_alert_kt_c32c6`, '总计') }}<span>{{ importPreviewFilesData.successCount + importPreviewFilesData.failCount }}</span>{{ $srmI18n(`${$getLangAccount()}#i18n_alert_T_6761`, '条') }}，{{ $srmI18n(`${$getLangAccount()}#i18n_alert_uNLR_2b0e1138`, '导入成功') }}<span class="success">{{ importPreviewFilesData.successCount }}</span>{{ $srmI18n(`${$getLangAccount()}#i18n_alert_T_6761`, '条') }}，{{ $srmI18n(`${$getLangAccount()}#i18n_alert_uNKm_2b0d38bd`, '导入失败') }}<span class="error">{{ importPreviewFilesData.failCount }}</span>{{ $srmI18n(`${$getLangAccount()}#i18n_alert_T_6761`, '条') }}</p>
                  <p
                    class="error download"
                    @click="downloadTaskErrorFiles(errorId)">{{ fileName }}{{ $srmI18n(`${$getLangAccount()}#i18n_btn_NSBR_45e0ab08`, '错误日志') }}.xlsx</p>
                </div>
              </div>
            </div>
            <div
              v-else
              style="flex:1;">

            </div>
            <a-button
              type="primary"
              :disabled="!tableData.length"
              @click="importPreviewData">{{ $srmI18n(`${$getLangAccount()}#i18n_title_improt`, '导入') }}</a-button>
          </div>
        </div>
        <div
          v-if="isLoading"
          class="progress">
          <a-progress
            type="circle"
            :stroke-color="{
              '0%': '#108ee9',
              '100%': '#87d068',
            }"
            :percent="percent"
          />
        </div>
        <div
          v-if="current === lastStepIndex && !isLoading"
          class="step-3">
          <a-result
            status="success"
            :title="$srmI18n(`${$getLangAccount()}#i18n_field_uNLR_2b0e1138`, '导入成功')"></a-result>
          <div class="import-error" >
            <p> {{ $srmI18n(`${$getLangAccount()}#i18n_alert_kt_c32c6`, '总计') }}<span>{{ importFilesResult.successCount + importFilesResult.failCount }}</span>{{ $srmI18n(`${$getLangAccount()}#i18n_alert_T_6761`, '条') }}，{{ $srmI18n(`${$getLangAccount()}#i18n_alert_uNLR_2b0e1138`, '导入成功') }}<span class="success">{{ importFilesResult.successCount }}</span>{{ $srmI18n(`${$getLangAccount()}#i18n_alert_T_6761`, '条') }}，{{ $srmI18n(`${$getLangAccount()}#i18n_alert_uNKm_2b0d38bd`, '导入失败') }}<span class="error">{{ importFilesResult.failCount }}</span>{{ $srmI18n(`${$getLangAccount()}#i18n_alert_T_6761`, '条') }}</p>
            <div
              class="error download"
              v-if="importPreviewErrorId"
              @click="downloadTaskErrorFiles(importPreviewErrorId)">
              {{ fileName }}-{{ $srmI18n(`${$getLangAccount()}#i18n_alert_uNIrWFxzIHWqCtIKmA_4f928296`, '导入模板数据部分异常，可点击下载查看') }}            
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import { Steps, Result } from 'ant-design-vue'
import { downFile, postAction } from '@api/manage'
import { isArray } from 'xe-utils'
export default {
    name: 'ImportExcel',
    props: {
        excelCode: String
    },
    components: {
        ASteps: Steps,
        AStep: Steps.Step,
        AResult: Result
    },
    data () {
        return {
            visible: false,
            current: 0,
            confirmLoading: false,
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            accept: '.xls, .xlsx',
            isPreview: 0,
            tableColumns: [],
            tableData: [],
            errorId: '',
            importPreviewErrorId: '',
            dataKey: '',
            fileName: '模板文件',
            isLoading: false,
            percent: 0,
            importPreviewFilesData: {},
            handleBefore: null,
            importFilesResult: {}
        }
    },
    computed: {
        importUrl () {
            let url = this.isPreview === 1 ?  '/els/base/excelHeader/previewExcelData/' : '/els/base/excelHeader/importExcelData/'
            url += this.excelCode
            return  url 
        },
        lastStepIndex () {
            let current = this.isPreview === 1 ? 2 : 1
            return current
        }
    },
    methods: {
        open (preview, columns, fileName, excelCode, handleBefore = null) {
            this.current = 0
            this.tableData = []
            this.errorId = ''
            this.importPreviewErrorId = ''
            this.isPreview = 0
            this.isPreview = preview
            this.tableColumns = columns.map(item => {
                if (item.dataType == 'dict') item.field = item.field + '_dictText'
                return item
            })
            this.fileName = fileName
            this.visible = true
            if (excelCode) {
                this.excelCode = excelCode
            }
            this.handleBefore = handleBefore || window.Promise.resolve({
                flag: true,
                params: {}
            })
        },
        ok () {
            this.visible = false
            this.handleBefore = null
        },
        close () {
            this.visible = false
            this.handleBefore = null
        },
        onChange (current) {
            this.current = current
        },
        //下载模板
        downloadFile (){
            downFile('/base/excelHeader/downloadTemplate', {excelCode: this.excelCode}).then((data)=>{
                if (!data) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LYuIdIr_12f75695`, '未找到对应模板'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), this.fileName + '.xlsx')
                }else{
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', this.fileName+'.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            })
        },
        //导入
        imortChangeEvent (info) {
            // 导入时loading
            if (info.event && info.file.status === 'uploading') {
                this.isLoading = true
                try {
                    this.percent = Math.floor(info.event.loaded/info.event.total*100)
                } catch (error) {
                    throw new Error(error)
                }
            }
            if (info.file.status === 'done') {
                let res = info.file.response
                if(res.success){
                    if(this.isPreview === 1) {
                        let result=res.result
                        this.dataKey = res.result.dataKey
                        this.tableData = res.result.dataList
                        this.errorId = res.result.errorId
                        this.importPreviewFilesData=result
                    }else {
                        let result=res.result
                        this.dataKey = res.result.dataKey
                        this.tableData = res.result.dataList
                        this.errorId = res.result.errorId
                        this.importPreviewFilesData=result
                        if(isArray( res.result )){
                            this.importFilesResult = res.result[0]
                        }else{
                            this.importFilesResult = res.result || {}
                        }
                        this.importPreviewErrorId = res.result.errorId
                        this.$parent.loadData()
                    }
                    this.current += 1
                    this.percent = 0
                }else{
                    this.$message.warning(res.message)
                }
                this.isLoading = false
            }
            if (info.file.status === 'error') {
                this.isLoading = false
            }
        },
        beforeUpload (file) {
            if(file.size > 10 * 1024 * 1024) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BRefQIWR_929fb07e`, '超过最大文件限制!'))
                return false
            }
        },
        importPreviewData () {
            const callback = (params = {}) => {
                this.confirmLoading = true
                postAction(`/base/excelHeader/importPreviewExcelData/${this.excelCode}/${this.dataKey}`, params).then(res => {
                    if(res.success) {
                        debugger
                        this.$parent && this.$parent.loadData()
                        this.current += 1
                        this.importFilesResult = res.result || {}
                        if (res.result && res.result.errorId) {
                            this.importPreviewErrorId = res.result.errorId
                        }                    
                        setTimeout(() => {
                            if (!this.importPreviewErrorId) {
                                this.visible = false
                            }
                        }, 3000)
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            }


            let beforeUrl = `/base/excelHeader/importExcelPreviewDataInteractive/${this.excelCode}/${this.dataKey}`
            postAction(beforeUrl, {}).then(res => {
                if (!res.success) {
                    return
                }
                let result = res.result || {}
                this.handleBefore(result).then(({ flag = false, params }) => {
                    if (!flag) {
                        return
                    }
                    callback && callback(params)
                })
            })
        },
        // 下载部分异常文件
        downloadTaskErrorFiles (errorId) {
            let id = errorId
            downFile(`/base/fileTask/downTaskFile/${id}`).then((data)=>{
                if (!data) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LYuIdQI_12f6af66`, '未找到对应文件'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), this.fileName + '_error.xlsx')
                }else{
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', this.fileName+'_error.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            })
        }
    }
}
</script>
<style lang="less" scoped>
    .progress{
      text-align: center;
    }
    .success{
        color:#52C41A;
      }
    .error{
      color:#F5222D;
    }
    .download{
      cursor: pointer;
    }
    .import-error {
      p{
        margin:0;
      }
      p:nth-of-type(1){
        margin-bottom: 5px;
      }
      // color: #f5222d;
      // padding-bottom: 6px;
      span{
        font-weight: bolder;
        padding: 0 2px;
      }
      
    }
</style>