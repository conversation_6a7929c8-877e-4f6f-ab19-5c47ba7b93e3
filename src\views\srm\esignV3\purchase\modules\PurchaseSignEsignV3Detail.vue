<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
export default {
    name: 'EsignFlowAdd',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            visible: false,
            form: {
                reason: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAy_24c6a9a8`, '业务编号'),
                                    fieldName: 'busNumber'
                                },

                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationDateSigning`, '签署截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_p9Gt6s92wep4uCmw`, '主体文件是否已上传e签宝'),
                                    fieldName: 'uploaded',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType_dictText'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')
                                }
                            ]
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCPWL_36613b74`, '采方签署人'), groupCode: 'purchaseSignersList', type: 'grid', custom: {
                        ref: 'purchaseSignersList',
                        columns: [
                            { field: 'signerType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型'), width: 120},
                            {
                                field: 'signFieldStyle_dictText',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeMVK_55b1010`, '签章区样式'),
                                width: 120
                            },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 180 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDEPsey_25baae92`, '签署用户E签宝账号'), width: 120 },
                            { field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDcR_f5c3f89d`, '签署用户姓名'), width: 120 }

                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RCPWL_91e5ef48`, '供方签署人'), groupCode: 'saleSignersList', type: 'grid', custom: {
                        ref: 'saleSignersList',
                        columns: [
                            { field: 'signerType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型'), width: 120},
                            {
                                field: 'signFieldStyle_dictText',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeMVK_55b1010`, '签章区样式'),
                                width: 120
                            },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 180 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDEPsey_25baae92`, '签署用户E签宝账号'), width: 120 },
                            { field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDcR_f5c3f89d`, '签署用户姓名'), width: 120 }

                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'signType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), width: 120},
                            { field: 'uploaded', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LaUawliDMFVcRPYm`, '是否上传e签宝'), width: 180, visible: false },
                            { field: 'uploaded_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LaUawliDMFVcRPYm`, '是否上传e签宝'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 200, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载'), clickFn: this.downloadEvent },
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_by6FtppjfaHaxK4d`, '签署文件下载'), clickFn: this.flowFileDownload, allow: this.allowDownload },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/esignv3/elsEsignV3Flow/queryById',
                getSignature: '/attachment/purchaseAttachment/getSignature',
                flowFileDownload: '/esignv3/elsEsignV3Flow/signFileDownload'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        },
        showBtn (){
            let data = this.currentEditRow
            if(data.returnSignedFile==='1' && data.reject!=='1' && data.returnFileConfirm!=='1'){
                return true
            }
            return false
        },
        allowDownload (row){
            if(this.currentEditRow.esignStatus !== '2' ){
                return true
            }
            if(row.signType==='1'){
                return false
            }
            return true
        },
        flowFileDownload (row){
            getAction(this.url.flowFileDownload, {id: this.currentEditRow.id}).then(res => {
                if(res.success){
                    console.log(res.result)
                    const result = res.result
                    let files = null
                    for(let file of result.files){
                        if(row.fileId == file.fileId){
                            files = file
                            break
                        }
                    }
                    if(files){
                        getAction(files.downloadUrl, {}, {
                            responseType: 'blob'
                        }).then(res => {
                            let url = window.URL.createObjectURL(new Blob([res]))
                            let link = document.createElement('a')
                            link.style.display = 'none'
                            link.href = url
                            link.setAttribute('download', files.fileName)
                            document.body.appendChild(link)
                            link.click()
                            document.body.removeChild(link) //下载完成移除元素
                            window.URL.revokeObjectURL(url) //释放掉blob对象
                        })
                    }
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        preViewEvent (row){
            this.$previewFile.open({path: row.absoluteFilePath})
        },
        downloadReturnFileEvent (row){
            this.downloadFile(row, this.url.returnFileDownload, row.fileName)
        },
        downloadFile (data, url, fileName){
            let id = data.id
            const params = {
                id
            }
            getAction(url, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        rejectEvent (){
            this.visible = true
        },
        handleOk (){
            const params = this.currentEditRow
            params.rejectReason = this.form.reason
            postAction(this.url.reject, params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.goBack()
                }
            })
        },
        confirmEvent (){
            const params = this.currentEditRow
            postAction(this.url.confirm, params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.goBack()
                }
            })
        }
    }
}
</script>