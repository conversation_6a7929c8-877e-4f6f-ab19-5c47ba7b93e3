<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="masterSlave"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        @ok="fieldSelectOk"
        isEmit
      />
      <ItemImportExcel
        ref="itemImportExcel"
        @importCallBack="importCallBack"
      />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction, postAction } from '@api/manage'
import { USER_COMPANYSET } from '@/store/mutation-types'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'

import { BUTTON_PUBLISH, BUTTON_SUBMIT } from '@/utils/constant.js'
export default {
  name: 'PurchaseSupplierCapacityHead',
  components: {
    BusinessLayout,
    fieldSelectModal,
    ItemImportExcel
  },
  mixins: [businessUtilMixin],
  props: {
    currentEditRow: {
      required: true,
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      businessRefName: 'businessRefName',
      labelCol: { span: 4 },
      wrapperCol: { span: 15 },
      rejectVisible: false,
      refresh: true,
      rejectForm: {
        node: '',
        reject: ''
      },
      requestData: {
        detail: {
          url: '/tender/tenderProjectApprovalHead/queryById',
          args: (that) => {
            return { id: that.currentEditRow.id }
          }
        }
      },
      externalToolBar: {
        approvalItemList: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
            attrs: {
              type: 'primary'
            },
            click: this.addBiddingItem,
            authorityCode: 'tender#tenderProjectApprovalHead:replenish'
          },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteItemEvent, authorityCode: 'bidding#purchaseBiddingHead:delete' },
          // {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
          //     key: 'fillDown',
          //     type: 'tool-fill',
          //     beforeCheckedCallBack: this.fillDownGridItem},
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
            authorityCode: 'bidding#purchaseBiddingHead:importExcel',
            params: this.importParams,
            click: this.importExcel
          }
        ]
      },
      pageFooterButtons: [
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
          args: {
            url: '/tender/tenderProjectApprovalHead/edit'
          },
          attrs: {
            type: 'primary'
          },
          key: 'save',
          showMessage: true,
          handleBefore: this.handleSaveBefore,
          handleAfter: this.handleSaveAfter
        },
        {
          ...BUTTON_PUBLISH,
          args: {
            url: '/tender/tenderProjectApprovalHead/publish'
          },
          show: false
        },
        {
          ...BUTTON_SUBMIT,
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
          args: {
            url: '/elsUflo/audit/submit'
          },
          handleBefore: this.handleBeforeFooterSubmit,
          // key: 'submit'
          click: this.handleSubmit
        },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
          key: 'goBack'
        }
      ],
      url: {
        save: '/tender/tenderProjectApprovalHead/edit',
        publish: '/tender/tenderProjectApprovalHead/publish'
      }
    }
  },
  computed: {
    remoteJsFilePath() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
      return `${account}/purchase_projectApproval_${templateNumber}_${templateVersion}`
    }
  },
  methods: {
    importCallBack(result) {
      if (result.file.status === 'done') {
        let response = result.file.response
        if (response.success) {
          let itemGrid = this.$refs.businessRefName.$refs.approvalItemListgrid[0]
          let insertData = response.result.dataList
          let pageConfig = itemGrid.pageConfig.groups[1]
          pageConfig.columns.forEach((item) => {
            if (item.defaultValue) {
              insertData.forEach((insert) => {
                if (!insert[item.field]) {
                  insert[item.field] = item.defaultValue
                }
              })
            }
          })
          itemGrid.$refs.approvalItemList.insertAt(insertData, -1)
        } else {
          this.$message.warning(response.message)
        }
      }
    },
    addBiddingItem() {
      this.selectType = 'material'
      console.log(this.$refs.businessRefName.$refs.approvalItemListgrid[0])
      const form = this.$refs.businessRefName.$refs.approvalItemListgrid[0].gridData
      const { mustMaterialNumber = '1' } = form
      if (mustMaterialNumber == '1') {
        let url = '/material/purchaseMaterialHead/list'
        let columns = [
          {
            field: 'cateCode',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
            width: 150
          },
          {
            field: 'cateName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
            width: 150
          },
          { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150 },
          { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
          { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200 },
          { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200 }
        ]
        this.$refs.fieldSelectModal.open(url, { blocDel: '0', freeze: '0' }, columns, 'multiple')
      } else {
        console.log('进入了else')
        let itemGrid = this.$refs.businessRefName.$refs.approvalItemListgrid[0]
        let itemData = {}
        this.pageConfig.itemColumns.forEach((item) => {
          if (item.defaultValue) {
            itemData[item.field] = item.defaultValue
          }
        })
        itemGrid.insertAt([itemData], -1)
      }
    },
    fieldSelectOk(data) {
      let itemGrid = this.$refs.businessRefName.$refs.approvalItemListgrid[0]
      // let gridData = itemGrid.gridData
      // let materialList = gridData.map(item => {
      //     return item.materialNumber
      // })
      let { tableData } = itemGrid.$refs.approvalItemList.getTableData()
      let materialList = tableData.map((item) => {
        return item.materialId
      })
      //过滤已有数据
      let insertData = data.filter((item) => {
        if (materialList.includes(item.materialId)) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSVBxVSL_321b5369`, '请勿重复补充物料！'))
          return false
        }
        return true
        // return !materialList.includes(item.materialNumber)
      })
      itemGrid.pageConfig.groups[1].columns.forEach((item) => {
        if (item.defaultValue) {
          insertData.forEach((insert) => {
            if (!insert[item.field]) {
              insert[item.field] = item.defaultValue
            }
          })
        }
      })
      insertData.forEach((insert) => {
        insert['materialId'] = insert['id']
        delete insert.id

        insert.quantityUnit = insert.baseUnit
        if (!!insert.requireQuantity) {
          insert.secondaryQuantity = insert.requireQuantity / (insert.conversionRate || 1)
        } else {
          insert.secondaryQuantity = 0
        }
        insert.secondaryQuantity = insert.secondaryQuantity.toFixed(6)
      })
      // let param = {
      //     'purOrgCode': this.$refs.businessRefName.$refs.approvalItemListgrid[0].gridData.purchaseOrg,
      //     'materialDataVos': data
      // }
      // postAction(this.url.materialBidding, param).then(res => {
      //     if(res.success){
      //         itemGrid.insertAt(insertData, -1)
      //     } else {
      //         this.$confirm({
      //             title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLSu_38d85f7b`, '确认添加'),
      //             content: res.message,
      //             onOk: function () {
      //                 itemGrid.insertAt(insertData, -1)
      //             }
      //         })
      //     }
      // })
      itemGrid.$refs.approvalItemList.insertAt(insertData, -1)
    },
    deleteItemEvent() {
      let itemGrid = this.$refs.businessRefName.$refs.approvalItemListgrid[0]
      let checkboxRecords = itemGrid.$refs.approvalItemList.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      checkboxRecords.forEach((item) => {
        console.log(item)

        if (item.sourceType == 'request') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xiTQG_e9fdf359`, '不允许删除！'))
        } else {
          itemGrid.$refs.approvalItemList.remove(item)
        }
      })
    },
    importParams() {
      const form = this.$refs.businessRefName.$refs.approvalItemListgrid[0]
      return { id: this.currentEditRow.id, templateAccount: form.currentEditRow.templateAccount, templateNumber: form.currentEditRow.templateNumber, templateVersion: form.currentEditRow.templateVersion, handlerName: 'purchaseApprovalItemExcelRpcDubboServiceImpl', roleCode: 'purchase', excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息') }
    },
    importExcel() {
      if (!this.currentEditRow.id) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
        return false
      }
      const form = this.$refs.businessRefName.$refs.approvalItemListgrid[0]
      let params = { id: this.currentEditRow.id, templateAccount: form.currentEditRow.templateAccount, templateNumber: form.currentEditRow.templateNumber, templateVersion: form.currentEditRow.templateVersion, handlerName: 'purchaseApprovalItemExcelRpcDubboServiceImpl', roleCode: 'purchase', excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息') }
      this.$refs.itemImportExcel.open(params, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'), 'approvalItemListgrid')
    },
    handleBeforeFooterSubmit(args) {
      const { allData = {} } = args || {}
      return new Promise((resolve) => {
        let params = {
          businessId: allData.id,
          businessType: 'projectApproval',
          auditSubject: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dIvdty_7ce2fb6f`, '项目立项，单号：') + `${allData.projectNumber}` + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RL_753b5d6b`, '，名称：') + `${allData.projectName}`,
          params: JSON.stringify(allData)
        }
        args = Object.assign({}, args, params)
        args.allData = params
        resolve(args)
      })
    },
    async handleAfterDealSource(pageConfig, resultData) {
      pageConfig.groups[0].formModel.purchaseEnterpriseName = this.$ls.get(USER_COMPANYSET).companyName
    },
    // 提交审批
    handleSubmit(params) {
      const allData = this.getAllData()
      // if( !this.currentEditRow.id ){
      //     let url = '/tender/tenderProjectApprovalHead/edit'
      //     this.confirmLoading = true
      //     postAction(url, params).then(res => {
      //         if(res.success) {
      //             this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_savaSuccess`, '保存成功'))
      //             this.confirmLoading = false
      //         }else{
      //             this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VsK_21f9ddf`, '请重试'))
      //             this.confirmLoading = false
      //             return false
      //         }
      //     })
      // }
      console.log(allData, params)
      // 如果物料行信息不为空，则删除每一条的id
      if (allData.approvalItemList.length > 0) {
        allData.approvalItemList.forEach((item) => {
          item.sourceType = this.currentEditRow.sourceType || 'new'
          delete item.id
        })
      }
      if (allData.audit === '1') {
        console.log(params)
        // 审批状态
        this.composeBusinessSubmit(params)
      } else {
        params.pageConfig.groups[0].formModel.audit = '0'
        this.composeBusinessPublish(params)
        console.log(params)
      }
    }
  },
  mounted() {
    // 有id，则二次编辑，不是新增页面
    if (this.currentEditRow.id) {
      this.ifNew = false
    } else {
      // 无id，则是新增页面
      this.ifNew = true
    }
    console.log(this.currentEditRow)
  }
}
</script>
