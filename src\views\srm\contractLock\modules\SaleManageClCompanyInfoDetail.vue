<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
export default {
    name: 'ClCompanyInfoDetail',
    mixins: [DetailMixin],
    data () {
        return {
            selectType: 'esignEnterpriseCertification',
            pageData: {
                form: {
                    elsAccount: '',
                    loadingCompany: '0',
                    companyCode: '',
                    companyName: '',
                    subAccount: '',
                    accountId: '',
                    legalPerson: '',
                    applyUserName: '',
                    applyContact: '',
                    applyContactType: '',
                    registerNo: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_64dEMQeAfsqvfsQ4`, '企业认证信息维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterpriseName`, '企业名称'),
                                    fieldName: 'companyName'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_role`, '角色'),
                                    fieldName: 'roleStr'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    fieldName: 'subAccount',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCK_2babc4f3`, '申请人联系方式'),
                                    fieldName: 'applyContact',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCK_2babc4f3`, '申请人联系方式'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    fieldName: 'applyContactType',
                                    dictCode: 'contractLockContactType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVNcR_b51847bb`, '申请者姓名'),
                                    fieldName: 'applyUserName',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCRXdiy_c6d93ea6`, '公司工商注册号'),
                                    fieldName: 'registerNo'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RChLcR_c2c6edeb`, '公司法人姓名'),
                                    fieldName: 'legalPerson'
                                },
                                {
                                    
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_longLink`, '实名认证长链接'),
                                    extend: {
                                        linkConfig: {
                                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                            titleI18nKey: 'i18n_title_authenticationLink'
                                        },
                                        exLink: true
                                    },
                                    disabled: true,
                                    fieldName: 'certificationPageUrl'
                                }
                            ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/contractLock/saleCLCompanyInfo/queryById'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>
