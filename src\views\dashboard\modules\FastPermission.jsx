import { getAction } from '@api/manage'
import { message as Message } from 'ant-design-vue'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    name: 'FastPermission',
    functional: true,
    render (h, context) {
        let spinning = false
       
        let { visible, transferData = [], targetKeys} = context.props
  
        const { listeners } = context
        const emitEventCancel = listeners['fast-permission-cancel']
        const emitEventChange = listeners['fast-permission-change']
        const emitEventRefresh = listeners['fast-permission-refresh']
        
        const handleConfirm = () => {
            // if (!targetKeys.length) {
            //     Message.warn(srmI18n(`${getLangAccount()}#i18n_title_selectShortcutMenuMsg`, '请选择快捷应用菜单'))
            // }
            let tarkeyLength=targetKeys.length
  
            if(tarkeyLength>6){//只能插入15条
                Message['warn'](srmI18n(`${getLangAccount()}#i18n_alert_QydjABeOROuN6T_2a4d1a0e`, '快捷应用列表最多只能加入6条'))
                return
            }
            spinning = true
            const ids = targetKeys.join(',')
            getAction(`/account/fastPermission/save?ids=${ids}`)
                .then(res => {
                    const type = res.success ? 'success' : 'error'
                    if(targetKeys.length) Message[type](res.message)
                    emitEventRefresh && emitEventRefresh()
                    emitEventCancel && emitEventCancel('cancel')
                })
                .finally(() => {
                    spinning = false
                })
        }

        const modalProps = {
            visible,
            width: 900,
            title: srmI18n(`${getLangAccount()}#i18n_title_quickApplicationSettings`, '快捷应用设置'),
            keyboard: false,
            maskClosable: false,
            destroyOnClose: true
        }

        const modalOn = {
            cancel: () => emitEventCancel && emitEventCancel('cancel'),
            ok: () => handleConfirm && handleConfirm()
        }

        const spinProps = {
            spinning,
            delayTime: 300
        }
        const transferProps = {
            dataSource: transferData,
            targetKeys: targetKeys,
            listStyle: {
                minWidth: '400px',
                height: '360px'
            },
            showSearch: true,
            filterOption: (inputValue, { title }) => title.toLocaleLowerCase().indexOf(inputValue) > -1,
            titles: [srmI18n(`${getLangAccount()}#i18n_title_toBeSelected`, '待选'), srmI18n(`${getLangAccount()}#i18n_title_isSelected`, '已选')],
            render: item => item.name
        }
        const transferOn = {
            change: (keys, direction, moveKeys) => emitEventChange && emitEventChange(keys)
        }

        const scopedSlots = {
            notFoundContent: () => <span class="notFoundContent">{srmI18n(`${getLangAccount()}#i18n_title_noData`, '没数据')}</span>
        }

        return (
            !transferData || !transferData.length
                ? null
                : (
                    <div class="fastPermission">
                        <a-modal { ...{ props: modalProps, on: modalOn } } >
                            <a-spin { ...{ props: spinProps } } >
                                <a-transfer { ...{ props: transferProps, on: transferOn } } scopeSlots={ scopedSlots }>
                                </a-transfer>
                            </a-spin>
                        </a-modal>
                    </div>
                )
        )
    }
}
