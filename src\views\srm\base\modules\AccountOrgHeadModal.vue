<template>
  <div class="els-page-comtainer">
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      @goBack="goBack"
    />
  </div>
</template>

<script>
import { tileEditPageMixin } from '@comp/template/tileStyle/tileEditPageMixin'

export default {
    name: 'AccountOrganizationInfoModal',
    mixins: [tileEditPageMixin],
    data () {
        return {
            pageData: {
                dictList: [],
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addAccountManagement`, '新增账号组织管理'),
                form: { userAccount: '' },
                validRules: {
                    userAccount: [
                        { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectUserAccountTips`, '请选择用户账号') },
                        { max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符') }
                    ],
                    userRealName: [{ max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符') }]
                },
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            list: [
                                {
                                    type: 'selectModal',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_userAccount`, '用户账号'),
                                    fieldName: 'userAccount', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_clickSelectUserName`, '请点击选择用户名称'),
                                    selectCallBack: this.selectUserName,
                                    sourceUrl: '/account/elsSubAccount/list?supplierUser=0',
                                    selectModel: 'single',                                      
                                    columns: [
                                        { field: 'username', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_userAccount`, '用户账号'), align: 'center'},
                                        { field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_userName`, '用户名称'),  align: 'center'}
                                    ]
                                },
                                {
                                    type: 'input',
                                    fieldName: 'userRealName',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_userName`, '用户名称'),
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectAccountFill`, '选择用户账号自动填充'),
                                    disabled: true

                                },
                                {
                                    type: 'input',
                                    fieldName: 'remark',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterRemark`, '请输入备注')
                                }
                            ], 
                            colSpan: 12
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationInfo`, '组织信息'),
                        content: {
                            type: 'table',
                            ref: 'accountOrgDetailList',
                            columns: [
                                {
                                    type: 'checkbox',
                                    width: 40
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationCode`, '组织类别编码'),
                                    field: 'orgTypeCode',
                                    width: 200
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationDesc`, '组织类别描述'),
                                    field: 'orgTypeName',
                                    width: 200
                                },
                               
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orgCode`, '组织编码'),
                                    field: 'orgCode',
                                    width: 200
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orgDesc`, '组织描述'),
                                    field: 'orgName',
                                    width: 200
                                }
                            ],
                            toolbarButton: [
                                { type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), clickFn: this.addRow },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteRow }
                            ]
                        }
                    }
                ]
            },
            url: {
                add: '/base/accountOrgHead/add',
                edit: '/base/accountOrgHead/edit',
                detail: '/base/accountOrgHead/queryDetailById'
            }
        }
    },
    methods: {
        deleteRow () {
            this.$refs.editPage.deleteRow()
        },
        selectUserName (data){
            let ky={'userId': data[0].id, 'userAccount': data[0].username, 'userRealName': data[0].realname}
            this.$refs.editPage.setFormFieldsValue(ky)   
        },
        addRow () {
            let item = {
                selectModel: 'multiple',
                sourceUrl: '/base/orgInfo/list',
                params: {},
                columns: [
                    {
                        field: 'orgTypeCode',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationCode`, '组织类别编码'),
                        width: 150
                    },
                    {
                        field: 'orgTypeName',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationDesc`, '组织类别描述'),
                        width: 150
                    },
                    {
                        field: 'orgCode',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orgCode`, '组织编码'),
                        width: 150
                    },
                    {
                        field: 'orgName',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orgDesc`, '组织描述'),
                        width: 150
                    }
                ]
            }
            this.$refs.editPage.currentSelectModal.selectCallBack = this.selectCallBack
            this.$refs.editPage.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        },
        selectCallBack (data) {
            let accountOrgDetailListGrid = this.$refs.editPage.$refs.accountOrgDetailList[0]
            let tableData =  accountOrgDetailListGrid.getTableData().fullData
            let idList = tableData.map(item => {
                return item.orgTypeCode+'_'+item.orgCode
            })
            let filterList = data.filter(item => {
                return !idList.includes(item.orgTypeCode+'_'+item.orgCode)
            })

            var orgList = filterList.map(item => {
                return {
                    orgTypeCode: item.orgTypeCode,
                    orgTypeName: item.orgTypeName,
                    orgCode: item.orgCode,
                    orgName: item.orgName
                }
            })
            this.$refs.editPage.addRow(orgList)
        },
        goBack () {
            this.$emit('hide')
        }
    }
}
</script>
