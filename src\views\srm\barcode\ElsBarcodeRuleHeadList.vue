<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <els-barcode-rule-head-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <els-barcode-rule-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import ElsBarcodeRuleHeadEdit from './modules/ElsBarcodeRuleHeadEdit.vue'
import ElsBarcodeRuleHeadDetail from './modules/ElsBarcodeRuleHeadDetail.vue'
import { postAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        ElsBarcodeRuleHeadEdit,
        ElsBarcodeRuleHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeRule',
                form: {
                    mouldGroupDesc: ''
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm21b_ruleName`, '单据编码/规则名称')
                    }
                ],
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'barcode#rule:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'barcode#rule:detail'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.showEdit,
                        authorityCode: 'barcode#rule:edit'
                    },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enable`, '启用'), clickFn: this.handleEnabled, allow: this.showEnabled, authorityCode: 'barcode#rule:enable'},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disable`, '禁用'), clickFn: this.handleDisabled, allow: this.showDisabled, authorityCode: 'barcode#rule:disabled' },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.showEdit,
                        authorityCode: 'barcode#rule:delete'
                    },
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/base/barcode/elsBarcodeRuleHead/add',
                list: '/base/barcode/elsBarcodeRuleHead/list',
                delete: '/base/barcode/elsBarcodeRuleHead/delete',
                changeStatus: '/base/barcode/elsBarcodeRuleHead/changeStatus',
                columns: 'elsBarcodeRuleHeadList'
            }
        }
    },
    methods: {
        handleResultRecord (row) {
            this.currentEditRow = row
            this.showResultPage = true
            this.showEditPage = false
            this.showDetailPage = false
        },
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        showEdit (row) {
            return (row.ruleStatus == 'new' || row.ruleStatus == 'disabled') ? false : true
        },
        showEnabled (row) {
            return row.ruleStatus == 'disabled' ? false : true
        },
        showDisabled (row) {
            return row.ruleStatus == 'enabled' ? false : true
        },
        handleEnabled (row) {
            let that = this
            const params = { id: row.id, statusType: '1' }
            postAction(this.url.changeStatus, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_AjLR_28088728`, '启用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        handleDisabled (row) {
            let that = this
            const params = { id: row.id, statusType: '2' }
            postAction(this.url.changeStatus, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_HjLR_38ff8896`, '禁用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        }
    }
}
</script>