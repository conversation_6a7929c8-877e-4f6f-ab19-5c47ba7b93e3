<template>
  <div>
    <titleTrtl class="margin-b-10">
      <span >{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_eBQD_2e695bfc`, '投标问题')) }}</span>
    </titletrtl>
    <div>
      <listTable
        :pageData="pageData"
        :url="url"
        :defaultParams="defaultParams"
        :statictableColumns="tableColumns"
        :showTablePage="true"
      />
    </div>
  </div>
</template>
<script>
import listTable from '../../components/listTable'
import titleTrtl from '../../components/title-crtl'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
export default {
    mixins: [baseMixins],
    components: {
        titleTrtl,
        listTable
    },
    inject: ['subpackageId', 'tenderCurrentRow'],
    computed: {
        subId (){
            return this.subpackageId()
        },
        applyRoleCanEdit () {
            return this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'
        }
    },
    data () {
        return {
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QfBD_4603a317`, '问题标题'),
                    'field': 'title'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DGL_180ebd0`, '提出人'),
                    'field': 'questionerName',
                    'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DGKI_2e9ff468`, '提出时间'),
                    'field': 'createTime',
                    'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText',
                    'width': 80
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    {type: 'reply', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reply`, '回复'), clickFn: this.handleReply, allow: this.allowReply}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            defaultParams: {},
            url: {
                list: '/tender/purchaseTenderMentoringHead/list'
            }
        }
    },
    methods: {
        handleReply (row) {
            this.$emit('handleReply', row)
        },
        handleView (row) {
            this.$emit('handleView', row)
        },
        allowReply (row){
            if (this.applyRoleCanEdit) {
                return row.status != '2' ? false : true
            } else {
                return true
            }
        }
    },
    created () {
        this.defaultParams= { subpackageId: this.subId }
        console.log('defaultParams', this.defaultParams)
    }
}
</script>
<style lang="less" scoped>
.margin-b-10{
  margin-bottom: 5px;
}
</style>


