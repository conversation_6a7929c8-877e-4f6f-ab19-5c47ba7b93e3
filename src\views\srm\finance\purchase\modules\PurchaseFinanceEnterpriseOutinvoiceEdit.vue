<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :pageFooterButtons="pageFooterButtons"
        :externalToolBar="externalToolBar"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { composePromise } from '@/utils/util.js'
import { getAction, postAction } from '@/api/manage'
import {
    BUTTON_SAVE,
    BUTTON_BACK,
    BUTTON_PUBLISH
} from '@/utils/constant.js'
export default {
    name: 'PurchaseFinanceEnterpriseOutinvoiceEdit',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            externalToolBar: {
            },
            pageFooterButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/finance/financeEnterpriseOutinvoice/edit'
                    },
                    click: this.saveEvent
                },
                BUTTON_BACK
            ],
            requestData: {
                detail: {
                    url: '/finance/financeEnterpriseOutinvoice/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            url: {
                edit: '/finance/financeEnterpriseOutinvoice/add',
                edit: '/finance/financeEnterpriseOutinvoice/edit',
                detail: '/finance/financeEnterpriseOutinvoice/queryById'
            }
        }
    },
    methods: {
        saveEvent (args) {
            console.log(args, 'args====')
            const allData = this.getAllData()
            this.stepValidate(args).then(()=>{
                let pushMode = allData.pushMode
                let phone = allData.phone
                let email = allData.email
                if (('1'===pushMode || '2'===pushMode) && !phone) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_YdltxOLV_85520d78`, '推送手机不能为空'))
                    return
                }
                if (('0'===pushMode || '2'===pushMode) && !email) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_YdjdxOLV_cd9165ec`, '推送邮箱不能为空'))
                    return
                }
                this.composeBusinessSave(args, allData)
            })
        },
        // 重写保存方法
        composeBusinessSave (args, allData) {
            let hasId = !!allData.id
            let flag = !hasId && this.$refs[this.businessRefName].isNeedJudge
            let steps = flag ? [this.stepBusinessAdd] : [this.stepBusinessSave]
            const handleCompose = composePromise(...steps)

            this.confirmLoading = true
            handleCompose({ ...args, allData })
                .then(res => {
                    console.log('all save success', res)
                }, err => {
                    console.log('all save error', err)
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.$refs[this.businessRefName] && this.$refs[this.businessRefName].queryDetail()
                })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_basicData`, '基础信息'),
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {   groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserElsAccountNumber`, '采购商ELS账号'),
                        fieldName: 'elsAccount',
                        disabled: true,
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserElsAccountNumber`, '采购商ELS账号')
                    },
                    {   groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseName`, '采购商名称'),
                        fieldName: 'enterpriseName',
                        disabled: true,
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseName`, '采购商名称')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码'),
                        fieldName: 'companyCode',
                        dictCode: 'purchase_organization_info,org_name,org_code,org_category_code="companyCode" && status="1"',
                        disabled: false,
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingOrganization`, '采购组织'),
                        fieldName: 'purchaseOrg',
                        disabled: false,
                        dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="purchaseOrganization" && status="1"',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingOrganization`, '采购组织')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseGroup`, '采购组'),
                        fieldName: 'purchaseGroup',
                        dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="purchaseGroup" && status="1"',
                        disabled: false,
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseGroup`, '采购组')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxNumber`, '纳税人识别号'),
                        fieldName: 'taxpayerRegNumber',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxNumber`, '纳税人识别号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regLocation`, '注册地址'),
                        fieldName: 'registerAddress',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regLocation`, '注册地址')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_diCE_32c265ec`, '注册电话'),
                        fieldName: 'registerTelephone',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_diCE_32c265ec`, '注册电话'),
                        disabled: false
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vDWE_2cb2838d`, '开户银行'),
                        fieldName: 'depositBank',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vDWE_2cb2838d`, '开户银行')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankAccount`, '银行账号'),
                        fieldName: 'bankAccount',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankAccount`, '银行账号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRCBFL_b8135c43`, '采购方负责人'),
                        fieldName: 'purchasePrincipal',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRCBFL_b8135c43`, '采购方负责人')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YdCK_2f765f8f`, '推送方式'),
                        fieldName: 'pushMode',
                        dictCode: 'outinvoicePushMode',
                        defaultValue: '1',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YdCK_2f765f8f`, '推送方式')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ydlt_2f75fd68`, '推送手机'),
                        fieldName: 'phone',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ydlt_2f75fd68`, '推送手机')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ydjd_2f7bafdc`, '推送邮箱'),
                        fieldName: 'email',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ydjd_2f7bafdc`, '推送邮箱')
                    }
                ],
                itemColumns: [
                ]
            }
        },

        // 返回操作
        goBack () {
            this.$emit('hide')
        },

        // 发布操作
        publishEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    let url = this.url.public
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (type == 'success'){
                            this.goBack()
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>