<template>
  <div class="els-page-comtainer">
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      @goBack="goBack"
    />
  </div>
</template>

<script>
import { tileEditPageMixin } from '@comp/template/tileStyle/tileEditPageMixin'
import {postAction, getAction} from '@/api/manage'
export default {
    name: 'DeliveryNoticeToDeliveryModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    mixins: [tileEditPageMixin],
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            propertyCodeOptions: [],
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryNoticeForwarding`, '送货通知单转发货单'),
                form: {
                    factory_dictText: '',
                    storageLocation_dictText: '',
                    templateNumber: '',
                    templateName: '',
                    templateVersion: '',
                    templateAccount: '',
                    busAccount: '',
                    toElsAccount: '',
                    purchaseName: '',
                    supplierCode: '',
                    supplierName: '',
                    deliveryNoticeId: ''
                },
                panels: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            ref: 'baseForm',
                            colSpan: '12',
                            type: 'form',
                            list: [
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factoryCode`, '工厂代码'),
                                    fieldName: 'factory_dictText',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocation`, '库存地点'),
                                    fieldName: 'storageLocation_dictText',
                                    disabled: true
                                }
                            ]}},
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                        content: {
                            type: 'table',
                            ref: 'purchaseDeliveryNoticeToDeliveryItemVOList',
                            columns: [
                                {
                                    type: 'checkbox',
                                    width: 40
                                },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryNoticeNo`, '送货通知单号'),
                                    field: 'noticeNumber',
                                    width: 180,
                                    defaultValue: ''
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialNumber',
                                    width: 180,
                                    defaultValue: ''
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                    field: 'materialDesc',
                                    width: 200,
                                    defaultValue: ''
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unRelationQuantity`, '未关联发货数量'),
                                    field: 'unRelationQuantity',
                                    width: 160,
                                    defaultValue: ''
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_replyQuantity`, '回复数量'),
                                    field: 'replyQuantity',
                                    width: 160,
                                    defaultValue: ''
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryQuantity`, '发货数量'),
                                    field: 'deliveryQuantity',
                                    width: 160,
                                    defaultValue: '',
                                    editRender: {name: 'AInput'}
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                                    field: 'factory_dictText',
                                    width: 180,
                                    defaultValue: ''
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocation`, '库存地点'),
                                    field: 'storageLocation_dictText',
                                    width: 180,
                                    defaultValue: ''
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_news`, '消息'),
                                    field: 'message',
                                    width: 200,
                                    defaultValue: ''
                                }
                            ],
                            toolbarButton: [
                            ]
                        }
                    }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatShipmentDoc`, '生成发货单'), type: 'primary', clickFn: this.generateDelivery},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack }
                ]

            },
            url: {
                noticeToDelivery: '/delivery/saleDeliveryHead/noticeToDelivery'
            }
        }
    },
    created () {
    },
    mounted () {
        this.init()
    },
    beforeDestroy () {

    },
    methods: {
        generateDelivery (){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatShipmentDoc`, '生成发货单'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmToGenerateShipmentDoc`, '是否确认生成发货单'),
                onOk () {
                    let params = that.$refs.editPage.getParamsData()
                    postAction(that.url.noticeToDelivery, params).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$emit('hide')
                        }else {
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                    })
                },
                onCancel () {
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        init () {
        // elsTacticsItemList
            let that = this
            let data = this.currentEditRow
            Object.assign(this.pageData.form, data)
            this.pageData.panels.forEach(panel => {
                if(panel.content.type == 'table') {
                    that.$refs.editPage.$refs.purchaseDeliveryNoticeToDeliveryItemVOList[0].loadData(data[panel.content.ref])
                }
            })
        },
        changeTacticsObject () {

        }

    }
}
</script>
