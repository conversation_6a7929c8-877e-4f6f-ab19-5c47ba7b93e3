<template>
  <div class="FadadaSignTaskSale business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="detail"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />
  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {  getAction, httpAction } from '@/api/manage'
import { BUTTON_BACK } from '@/utils/constant.js'

export default {
    name: 'DetailFadadaSignTaskSaleModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/fadadaSignTaskSale/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            url: {               
                download: '/attachment/purchaseAttachment/download',
                signFileDownload: '/electronsign/fadada/fadadaSignTaskSale/signFileDownload',
                detail: '/electronsign/fadada/fadadaSignTaskSale/queryById'
            }
        }
    },
    methods: {
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dDVH_24c39726`, '主体信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PWQIBI_dc74fdbe`, '签署文件/附件'),
                        groupNameI18nKey: '',
                        groupCode: 'fadadaSignAttachmenSaleList',
                        groupType: 'item',
                        sortOrder: '3'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_MVPWQIBI_e616037c`, '回传签署文件/附件'),
                        groupNameI18nKey: '',
                        groupCode: 'fadadaSignAttachmenSaleBackList',
                        groupType: 'item',
                        sortOrder: '2'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEsNo`, '采购ELS号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'toElsAccount',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRRL_4460e249`, '采购名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'purchaseName',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWdD_3a0b6871`, '签署主题'),
                        fieldLabelI18nKey: '',
                        fieldName: 'signTaskSubject',
                        required: '1',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQAAc_eec498e0`, '签署文档类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'signDocType',
                        dictCode: 'fadadaSignDocType',
                        defaultValue: 'contract',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LSRAKI_95a080fc`, '任务过期时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'expiresTime',
                        required: '1',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pICWre_61090d0f`, '哪一方先盖章'),
                        fieldLabelI18nKey: '',
                        fieldName: 'firstSeal',
                        dictCode: 'srmSignatoryType',
                        defaultValue: 'sale',
                        required: '1',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherToStampOnline`, '是否线上盖章'),
                        fieldLabelI18nKey: '',
                        fieldName: 'onlineSealed',
                        dictCode: 'yn',
                        defaultValue: '0',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWLSzE_e431f745`, '签署任务状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'signTaskStatus',
                        dictCode: 'fadadaSignTaskStatus',
                        defaultValue: '',
                        disabled: true
                    }
                ],
                itemColumns: [
                    // {
                    //     title: '文件类型',
                    //     // fieldType: 'input',
                    //     groupCode: 'fadadaSignAttachmenSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'fileType',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '0',
                    //     disabled: true
                    // },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称'),
                        // fieldType: 'input',
                        groupCode: 'fadadaSignAttachmenSaleList',
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'),
                        // fieldType: 'select',
                        groupCode: 'fadadaSignAttachmenSaleList',
                        fieldLabelI18nKey: '',
                        field: 'signType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'doc',
                        width: '120',
                        dictCode: 'fadadaFileType',
                        required: '1'
                    },
                    {
                        title: '文件表文件ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaSignAttachmenSaleList',
                        fieldLabelI18nKey: '',
                        field: 'relationId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQMKPWQI_153fa66a`, '是否存在签署文件'),
                        fieldType: 'input',
                        groupCode: 'fadadaSignAttachmenSaleList',
                        fieldLabelI18nKey: '',
                        field: 'existSignFile_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        groupCode: 'fadadaSignAttachmenSaleList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: 'sourceType_dictText',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载')}
                                    onClick={() => this.downloadEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载')}</a>)
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jQIUB_c9c1b072`, '原文件预览')}
                                    style="margin-left:8px"
                                    onClick={() => this.preViewEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jQIUB_c9c1b072`, '原文件预览')}</a>)  
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQI_1183a9c9`, '查看签署文件')}
                                    style="margin-left:8px"
                                    onClick={() => this.downloadSignEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQI_1183a9c9`, '查看签署文件')}</a>)                              
                                return resultArray
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称'),
                        // fieldType: 'input',
                        groupCode: 'fadadaSignAttachmenSaleBackList',
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'),
                        fieldType: 'select',
                        groupCode: 'fadadaSignAttachmenSaleBackList',
                        fieldLabelI18nKey: '',
                        field: 'signType',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'doc',
                        width: '120',
                        dictCode: 'fadadaFileType',
                        required: '1'
                    },
                    {
                        title: '文件表文件ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaSignAttachmenSaleBackList',
                        fieldLabelI18nKey: '',
                        field: 'relationId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQMKPWQI_153fa66a`, '是否存在签署文件'),
                        fieldType: 'input',
                        groupCode: 'fadadaSignAttachmenSaleBackList',
                        fieldLabelI18nKey: '',
                        field: 'existSignFile_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        groupCode: 'fadadaSignAttachmenSaleBackList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: 'sourceType_dictText',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载')}
                                    onClick={() => this.downloadEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载')}</a>)
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jQIUB_c9c1b072`, '原文件预览')}
                                    style="margin-left:8px"
                                    onClick={() => this.preViewEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jQIUB_c9c1b072`, '原文件预览')}</a>)  
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQI_1183a9c9`, '查看签署文件')}
                                    style="margin-left:8px"
                                    onClick={() => this.downloadSignEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQI_1183a9c9`, '查看签署文件')}</a>)                              
                                return resultArray
                            }
                        }
                    }
                ]
            }
        },
        downloadEvent (row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownloadi18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction(this.url.download, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        downloadSignEvent (row){
            const allData = this.getAllData()     
            if(row.existSignFile!='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_xMKPWQI_bb284020`, '不存在签署文件'))
                return
            }     
            httpAction(this.url.signFileDownload, {id: allData.relationId, fileId: row.id}, 'get').then((res) => {
                if (res.success) {
                    window.open(res.message, '_blank')
                } else {
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>
