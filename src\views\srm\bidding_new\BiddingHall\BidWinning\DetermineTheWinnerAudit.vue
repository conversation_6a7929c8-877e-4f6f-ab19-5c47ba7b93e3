<template>
  <div class="page-container">
    <a-spin :spinning="confirmLoading">
      <div
        v-if="show"
        :resultData="resultData"
        @reset="getData"
        @goBackAudit="goBackAudit"
        :is="componentsName"
        :currentEditRow="currentEditRow"
      />
    </a-spin>
  </div>
</template>
<script lang="jsx">
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import TotalDetermineTheWinnerAudit from './components/TotalDetermineTheWinnerAudit'
import SubDetermineTheWinnerAudit from './components/SubDetermineTheWinnerAudit'
export default {
    name: 'DetermineTheWinner',
    components: {
        TotalDetermineTheWinnerAudit,
        SubDetermineTheWinnerAudit
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            componentsName: '',
            show: false,
            confirmLoading: false,
            resultData: {},
            url: {
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryById: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryById'
            }
        }
    },
    computed: {
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        pageStatus () {
            if (this.resultData.status == '0' || !this.resultData.status) {
                return 'edit'
            } else {
                return 'detail'
            }
            
        },
        currentPageName () {
            if(this.currentEditRow && this.currentEditRow.bizType_dictText){
                return  this.currentEditRow.bizType_dictText
            }
            return this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBLVH_7e1ecb2e`, '中标人信息')
        }
    },
    methods: {
        async getData () {
            let params = {
                id: this.currentEditRow.id,
                affirmType: '0'
            }
            this.confirmLoading = true
            this.show = false
            getAction(this.url.queryById, params).then(res => {
                if (res.code == 200 && res.result) {
                    this.resultData = res.result || {}
                    this.componentsName = this.resultData.quoteType == '1' ? 'SubDetermineTheWinnerAudit' : 'TotalDetermineTheWinnerAudit'
                }
            }, (err) => {
                this.show = true   
                this.confirmLoading = false
            }).finally(() => {
                this.show = true   
                this.confirmLoading = false
            })
        },
        /**
     * 审批方法
     */
        goBackAudit () {
            this.$parent.hideController()
        }
    },
    created () {
        this.getData()
    }
}
</script>
<style lang="less" scoped>
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
</style>




