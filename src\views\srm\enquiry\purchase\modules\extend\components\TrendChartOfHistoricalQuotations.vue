<template>
  <a-modal
    centered
    :footer="null"
    :mask-closable="false"
    :title="title"
    :visible="visible"
    :width="1120"
    @cancel="visible = false">
    <div
      v-if="visible"
      id="his-chart"
      style="height: 330px; width: 1000px"/>
  </a-modal>
</template>

<script lang="jsx">
import {postAction} from '@api/manage'
import * as echarts from 'echarts'

export default {
    data (){
        return{
            form: {},
            options: {
                grid: {bottom: '5%', containLabel: true, left: '5%', top: '90'},
                legend: {data: [], top: '30', type: 'scroll'},
                series: [],
                toolbox: {
                    feature: {
                        magicType: {
                            title: {
                                bar: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_FSLdzP_96f476fa`, '切换为柱状图'),
                                line: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_FSLNWP_96e1d8f8`, '切换为折线图')
                            },
                            type: ['line', 'bar']
                        },
                        restore: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_Sj_11bec7`, '还原') },
                        saveAsImage: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_sMLPO_902181c8`, '保存为图片') }
                    }
                },
                tooltip: {trigger: 'item'},
                xAxis: {
                    boundaryGap: true,
                    data: [],
                    name: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_date`, '日期'),
                    type: 'category'
                },
                yAxis: {
                    name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_um_9f825`, '价格'),
                    type: 'value'
                }
            },
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_historicalPriceTrendChart`, '历史价格趋势图'),
            visible: false
        }
    },
    methods: {
        getData (){
            postAction('/price/purchaseInformationRecords/queryPriceByMaterial', this.form).then(res => {
                const { legendData = [], series = [], xAxisData = [], xaxisData = [] } = res.result || {}
                if (!series) return this.$messages.error('暂无数据')
                this.$set(this.options.legend, 'data', legendData)
                this.$set(this.options, 'series', series)
                let x = xAxisData.length > 0 ? xAxisData : xaxisData
                this.$set(this.options.xAxis, 'data', x)
                this.visible = true
                this.$nextTick(() => {
                    this.initChart()
                })
            })
        },
        initChart (){
            const chart = echarts.init(document.getElementById('his-chart'))
            chart.setOption(this.options)
        },
        open ({materialNumber, rows}){
            const toElsAccountList = rows.map(row => row.toElsAccount)
            this.$set(this.form, 'materialNumber', materialNumber)
            this.$set(this.form, 'toElsAccountList', toElsAccountList)
            this.getData()
        }
    },
    name: 'TrendChartOfHistoricalQuotations'
}
</script>
