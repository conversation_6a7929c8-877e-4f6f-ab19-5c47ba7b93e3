 
 
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<title>消息盒子</title>

<link rel="stylesheet" href="../../../layui.css?v=1">
<style>
.layim-msgbox{margin: 15px;}
.layim-msgbox li{position: relative; margin-bottom: 10px; padding: 0 130px 10px 60px; padding-bottom: 10px; line-height: 22px; border-bottom: 1px dotted #e2e2e2;}
.layim-msgbox .layim-msgbox-tips{margin: 0; padding: 10px 0; border: none; text-align: center; color: #999;}
.layim-msgbox .layim-msgbox-system{padding: 0 10px 10px 10px;}
.layim-msgbox li p span{padding-left: 5px; color: #999;}
.layim-msgbox li p em{font-style: normal; color: #FF5722;}

.layim-msgbox-avatar{position: absolute; left: 0; top: 0; width: 50px; height: 50px;}
.layim-msgbox-user{padding-top: 5px;}
.layim-msgbox-content{ min-height: 22px;}
.layim-msgbox .layui-btn-small{padding: 0 15px; margin-left: 5px;}
.layim-msgbox-btn{position: absolute; right: 0; top: 12px; color: #999;}
</style>
</head>
<body>

<ul class="layim-msgbox" id="LAY_view"></ul>


<textarea title="消息模版" id="LAY_tpl" style="display:none;">
{{# layui.each(d.data, function(index, item){ }}
    <li data-avatar="{{ item.avatar }}" data-username="{{ item.username }}" data-uid="{{ item.id }}" data-fromGroup="{{ item.from_group }}">
      <a href="javacript:void(0);" target="_blank">
        <img src="{{ item.avatar }}" class="layui-circle layim-msgbox-avatar">
      </a>
      <p class="layim-msgbox-user">
        <a href="javacript:void(0);" target="_blank">{{ item.elsAccount ||'' }}_{{ item.username ||'' }}请求加你为好友</a>
        <span>{{ item.createTime }}</span>
      </p>
      <p class="layim-msgbox-content">
      </p>
      <p class="layim-msgbox-btn">
        <button class="layui-btn layui-btn-small" data-type="agree">同意</button>
        <button class="layui-btn layui-btn-small layui-btn-primary" data-type="refuse">拒绝</button>
      </p>
    </li>
{{# }); }}
<!-- <li class="layim-msgbox-system">
  <p><em>系统：</em>同意加你为好友<span></span></p>
</li> -->
</textarea>

<!-- 
上述模版采用了 laytpl 语法，不了解的同学可以去看下文档：http://www.layui.com/doc/modules/laytpl.html 
-->


<script src="../../../../layui.js?v=1"></script>
<script>
  const mglobalSrmI18n = window.parent.globalSrmI18n ? window.parent.globalSrmI18n : function(){}
  const mglobalGetLangAccount = window.parent.globalGetLangAccount ? window.parent.globalGetLangAccount : function(){}

  var token = localStorage.getItem('t_token');
  var BASE_URL = localStorage.getItem('IM_BASE_URL') || 'https://v5sit.51qqt.com/els/im'
  if (location.hostname.indexOf('localhost') !== -1) {
    BASE_URL = 'https://v5sit-micro.51qqt.com/els/im'
  }
layui.use(['layim', 'flow'], function(){
  var layim = layui.layim
  ,layer = layui.layer
  ,laytpl = layui.laytpl
  ,$ = layui.jquery
  ,flow = layui.flow;

  var cache = {}; //用于临时记录请求到的数据
  var localCache = parent.layui.layim.cache()
  //请求消息
  var renderMsg = function(page, callback){
    
    
    // $.get('getmsg.json', {
    //   page: page || 1
    // }, function(res){
    //   if(res.code != 0){
    //     return layer.msg(res.msg);
    //   }

    //   //记录来源用户信息
    //   layui.each(res.data, function(index, item){
    //     cache[item.from] = item.user;
    //   });

    // });
    console.log(localCache)
    console.log('localCache.srmParams.frendsRequest :>> ', localCache.srmParams.frendsRequest);
    callback && callback(localCache.srmParams.frendsRequest, 1);
  };

  //消息信息流
  flow.load({
    elem: '#LAY_view' //流加载容器
    ,isAuto: false
    ,end: '<li class="layim-msgbox-tips">' + mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_PSHOVXH_45124653", "暂无更多新消息") + '</li>'
    ,done: function(page, next){ //加载下一页
      // 从缓存里面获取消息数组
      renderMsg(page, function(data, pages){
        var html = laytpl(LAY_tpl.value).render({
          data: data
          ,page: page
        });
        next(html, page < pages);
      });
    }
  });

  //打开页面即把消息标记为已读
  /*
  $.post('/message/read', {
    type: 1
  });
  */

  //操作
  var active = {
    //同意
    agree: function(othis){
      var li = othis.parents('li')
      ,uid = li.data('uid')
      ,from_group = li.data('fromGroup')
      ,user = {
        username: li.data('username'),
        avatar: li.data('avatar')
        // sign: li.data('avatar')
      }
        
      //选择分组
      parent.layui.layim.setFriendGroup({
        type: 'friend'
        ,username: user.username
        ,avatar: user.avatar
        ,group: parent.layui.layim.cache().friend //获取好友分组数据
        ,submit: function(group, index){
          // 弹窗后执行上面操作
          // 添加好友请求
          $.ajax({
            headers: {
              'X-Access-Token': token
            },
            url: BASE_URL + '/user/agreeFriendRequest?friendId=' + uid + '&groupId=' + group,
            type: 'get',
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            success: function (res) {
              //将好友追加到主面板
              parent.layui.layim.addList({
                type: 'friend'
                ,avatar: user.avatar //好友头像
                ,username: user.username //好友昵称
                ,groupid: group //所在的分组id
                ,id: uid //好友ID
                ,sign: '' //好友签名
              });
              parent.layer.close(index);
              othis.parent().html(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_dict_IeI_16b37f5", "已同意"));

              var localCache = parent.layui.layim.cache()
              var frendsRequest = localCache.srmParams.frendsRequest
              var idx = localCache.srmParams.frendsRequest.findIndex(n => n.id === uid)
              if (idx !== -1) {
                frendsRequest.splice(idx, 1)
              }
            },
            error: function () {
              console.log('ajax error');
            }
            });
          }
      });
    }

    //拒绝
    ,refuse: function(othis){
      var li = othis.parents('li')
      ,uid = li.data('uid');

      layer.confirm(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_RIFKoW_bae7d85f", "确定拒绝吗？"), function(index){
        $.ajax({
          headers: {
            'X-Access-Token': token
          },
          url: BASE_URL + '/user/refuseFriendRequest?requestId=' + uid,
          type: 'get',
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          success: function (res) {
            layer.close(index);
            othis.parent().html('<em>'+mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_IFK_16d1fbd", "已拒绝")+'</em>');

            var localCache = parent.layui.layim.cache()
            var frendsRequest = localCache.srmParams.frendsRequest
            var idx = localCache.srmParams.frendsRequest.findIndex(n => n.id === uid)
            if (idx !== -1) {
              frendsRequest.splice(idx, 1)
            }
          },
          error: function () {
            console.log('ajax error');
          }
        });
      });
    }
  };

  $('body').on('click', '.layui-btn', function(){
    var othis = $(this), type = othis.data('type');
    active[type] ? active[type].call(this, othis) : '';
  });
});
</script>
</body>
</html>
