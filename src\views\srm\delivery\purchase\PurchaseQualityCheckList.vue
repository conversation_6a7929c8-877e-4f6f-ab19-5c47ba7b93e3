<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"
    />
    <!-- 编辑界面 -->
    <purchase-quality-check-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <!-- 详情界面 -->
    <purchase-quality-check-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import PurchaseQualityCheckEdit from './modules/PurchaseQualityCheckEdit'
import PurchaseQualityCheckDetail from './modules/PurchaseQualityCheckDetail'
import layIM from '@/utils/im/layIM.js'
import { postAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseQualityCheckEdit,
        PurchaseQualityCheckDetail
    },
    data () {
        return {
            currentRow: {},
            pageData: {
                businessType: 'qualityCheck',
                form: {
                    checkNumber: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'qualityCheck#purchaseQualityCheckHead:add'
                    },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SMWWWWF_b44eb4e2`, '获取ERP数据'), icon: 'arrow-down', clickFn: this.getDataByErp, allow: () => { return this.btnInvalidAuth('qualityCheck#purchaseQualityCheckHead:getDataByErp') } },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'), icon: 'arrow-up', clickFn: this.pushDataToERP, allow: () => { return this.btnInvalidAuth('qualityCheck#purchaseQualityCheckHead:pushDataToErp') } },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF }
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkNumber`, '检测单号'),
                        fieldName: 'checkNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterCheckNumber`, '请输入检测单号')
                    }
                    // {
                    //     type: 'select',
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                    //     fieldName: 'ebiddingStatus',
                    //     dictCode: 'srmEbiddingStatus',
                    //     placeholder: '请选择单据状态'
                    // }
                ],
                optColumnWidth: 210,
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'qualityCheck#purchaseQualityCheckHead:query' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'qualityCheck#purchaseQualityCheckHead:edit'},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_resultEnty`, '结果录入'), clickFn: this.handleEdit, allow: this.resultEdit, authorityCode: 'qualityCheck#purchaseQualityCheckHead:result'},
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'qualityCheck#purchaseQualityCheckHead:delete'},
                    { type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopy, authorityCode: 'qualityCheck#purchaseQualityCheckHead:copy'},
                    // { type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopy},
                    { type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat, authorityCode: 'qualityCheck#purchaseQualityCheckHead:chat' },
                    { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord, authorityCode: 'qualityCheck#purchaseQualityCheckHead:record'}
                ]
            },
            tabsList: [],
            url: {
                add: '/quality/purchaseQualityCheckHead/add',
                list: '/quality/purchaseQualityCheckHead/list',
                delete: '/quality/purchaseQualityCheckHead/delete',
                regret: '/quality/purchaseQualityCheckHead/regret',
                columns: 'PurchaseQualityCheckHead',
                getDataByErpUrl: '/quality/purchaseQualityCheckHead/getDataByErp',
                pushDataToERPUrl: '/quality/purchaseQualityCheckHead/pushDataToErp',
                copy: '/quality/purchaseQualityCheckHead/copy'
            }
        }
    },
    mounted () {
        // this.serachTabs('srmCheckStatus', 'requestStatus')
        this.serachCountTabs('/quality/purchaseQualityCheckHead/counts')
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.checkNumber || id
            // 创建
            layIM.creatGruopChat({ id, type: 'PurchaseQualityCheck', url: this.url || '', recordNumber })
        },
        allowChat (row) {
            if (row.checkStatus != '0') {
                return false
            } else {
                return true
            }
        },
        allowEdit (row) {

            if (row.auditStatus == '1') {
                return true
            }
            return row.checkStatus != '0'
        },
        allowDelete (row) {

            if (row.auditStatus == '1') {
                return true
            }
            return row.checkStatus != '0'
        },
        submitCallBack (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (row) {
            this.currentEditRow = row
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        resultEdit (row) {

            if (row.auditStatus == '1') {
                return true
            }
            return row.checkStatus != '1'
        },
        handleCopy (row) {
            let that = this
            const url = this.url.copy + '/' + row.id
            postAction(url, null).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BRBmBrWFLRW_e4c909f0`, '复制采购来料数据成功！'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        }
    }
}
</script>
<style lang="less" scoped>
.radio-wrap {
    display: block;
    height: 30px;
}
</style>