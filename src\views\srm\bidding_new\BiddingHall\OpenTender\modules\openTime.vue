<template>
  <div class="openTime">
    <a-spin :spinning="confirmLoading">
      <div class="boxWrap">
        <h3 class="h3">{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_title_bidCountdown`, '开标倒计时')) }}</h3>
        <div class="count">
          <div class="countdown">
            <!-- <a-statistic-countdown
              :value="deadline"
              format="DD:HH:mm:ss"
              :value-style="valueStyle"
              @finish="handleFinish"
            /> -->
            <countdown
              v-if="deadline"
              :time="deadline"
              :style="valueStyle"
              @end="handleFinish"
            >
              <template slot-scope="props"> {{ props.days }} : {{ props.hours }} : {{ props.minutes }} : {{ props.seconds }}</template>
            </countdown>
          </div>
            
        </div>
        <div class="time">
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_day`, '天') }}</span>
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_time`, '时') }}</span>
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_branch`, '分') }}</span>
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_second`, '秒') }}</span>
        </div>
        <div class="btns">
          <a-button
            type="primary"
            :disabled="canOpen"
            @click="handleOpen">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_bidOpenEnter`, '进入开标') }}
          </a-button>
        </div>
      </div>
    </a-spin>
  </div>
</template>
<script>
import moment from 'moment'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
import countdown from '@/components/countdown/index.js'
export default {
    mixins: [baseMixins],
    props: {
        role: {
            default: 'purchase',
            type: String
        }
    },
    components: {
        countdown
    },
    data () {
        return {
            valueStyle: {
                width: '100%',
                height: '78px',
                fontSize: '42px',
                fontWeight: 700,
                color: '#727272',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                letterSpacing: '8px'
            },
            confirmLoading: false,
            height: 0,
            names: '',
            openBiddingTime: '',
            deadline: null,
            checkType: '',
            autoOpenBid: false,
            canOpen: true,
            openBidSettingStatus: null,
            url: {
                getSettingUrl: '/tender/purchaseTenderProjectOpenSettingHead/queryBySubpackageId',
                purDetail: '/tender/purchaseTenderProjectHead/querySubpackageInfoBySubpackageId',
                supDetail: '/tender/sale/supplierTenderProjectMasterInfo/openBid/permission'
            }
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        openTimeCode () {
            let code = ''
            switch (this.checkType) {
            case '0':
                code = 'preOpenBiddingTime_DateMaps'
                break
            case '1': 
                switch (this.processType) { 
                case '0':
                    code = 'resultOpenBiddingTime_DateMaps'
                    break
                case '1': 
                    switch (this.currentStep) { 
                    case '0':
                        code = 'openBiddingTime_DateMaps'
                        break
                    case '1': 
                        code = 'resultOpenBiddingTime_DateMaps'
                        break
                    default :
                        code = 'openBiddingTime_DateMaps'
                    }
                    break
                default :
                    code = 'openBiddingTime_DateMaps'
                }
                break
            default :
                code = 'openBiddingTime_DateMaps'
            }
            return code
        }
    },
    methods: {
        handleFinish () {
            console.log(222)
            if (this.autoOpenBid) {
                this.$emit('changeSetp', 2)
            } else {
                this.canOpen = false
            }
        },
        handleOpen ()  {
            this.confirmLoading = true
            // 开始请求时间
            let StartTime = moment().valueOf()
            this.getDetailPromise()
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    let {result} = res
                    this.openBiddingTime = result[this.openTimeCode]
                    let deadline = this.openBiddingTime
                    let timestamp = res.timestamp
                    if (timestamp <= deadline) {
                        this.$message.error(`未到达开标时间 ${this.openBiddingTime}`)
                        // 响应时间
                        let ResponseTime = moment().valueOf() - StartTime
                        this.deadline = deadline - timestamp - ResponseTime
                        return
                    }
                    this.$emit('changeSetp', 2)
                }).finally(() => {
                    this.confirmLoading = false
                })
        },
        getDetailPromise () {
            let params = {
                subpackageId: this.subId
            }
            let url = this.role == 'purchase' ? this.url.purDetail : this.url.supDetail
            return getAction(url, params)
        },
        getSetting () {
            return getAction(this.url.getSettingUrl, {subpackageId: this.subId, checkType: this.checkType}).then(res => {
                if(res.success) {
                    this.autoOpenBid = res.result.autoOpenBid
                    this.openBidSettingStatus = res.result.status
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vBGRWFSMKm_39a163c8`, '开标设置数据获取失败'))
                }
            })
        },
        getOpenSettingPath () {
            let pathName = 'BinddingOpenSetting' 
            switch (this.checkType) {
            case '0':
                pathName = 'PreBinddingOpenSetting'
                break
            case '1': 
                switch (this.processType) { 
                case '0':
                    pathName = 'BinddingOpenSetting'
                    break
                case '1': 
                    switch (this.currentStep) { 
                    case '0':
                        pathName = 'FirstBinddingOpenSetting'
                        break
                    case '1': 
                        pathName = 'SecondBinddingOpenSetting'
                        break
                    default :
                        pathName = 'BinddingOpenSetting'
                    }
                    break
                default :
                    pathName = 'BinddingOpenSetting'
                }
                break
            default :
                pathName = 'BinddingOpenSetting'
            }
            return pathName
        },
        async init () {
            this.confirmLoading = true
            await this.getSetting()
            // 未发布开标设置，跳转到开标设置
            if (this.openBidSettingStatus != '1' && this.role == 'purchase') {
                this.$info({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vBGRLML_e0a3fe97`, '开标设置未完成'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vBGRLMLWVWMLvBVHGR_350e676f`, '开标设置未完成，请先完成开标信息设置'),
                    okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vtGR_391c3b78`, '立即设置'),
                    onOk: () => {
                        let pathName = this.getOpenSettingPath()
                        this.$router.push({name: pathName})
                    }
                })
                return false
            }
            this.getDetailPromise()
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    let {result} = res
                    let timestamp = res.timestamp
                    this.openBiddingTime = result[this.openTimeCode]
                    let deadline = this.openBiddingTime
                    if (timestamp >= deadline) {
                        this.$emit('changeSetp', 2)
                        // this.canOpen = false
                    } else {
                        this.deadline = deadline - timestamp
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    created () {
        this.init()
    }
}
</script>
<style lang="less" scoped>
.openTime{
  .boxWrap {
			margin-top: 80px;
		}
  .h3 {
		font-size: 24px;
		color: #3d3d3d;
	}
	.count,
	.time {
		width: 454px;
	}
	.count {
		margin: 0 auto;
		border: 1px solid #000;
		border-radius: 8px;
		height: 78px;
	}
	.time {
		display: flex;
		justify-content: space-between;
		margin: 14px auto;
		padding: 0 36px;
	}
	.info {
		line-height: 2.4;
		.tit {
			&::after {
				margin-right: 8px;
				content: ":";
			}
		}
	}
	.btns {
		margin-top: 14px;
	}
}
</style>


