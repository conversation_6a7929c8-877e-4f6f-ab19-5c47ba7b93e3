<template>
  <div class="els-page-comtainer">
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      @goBack="goBack"
    />
  </div>
</template>

<script>
import { duplicateCheck } from '@/api/api'
import { tileEditPageMixin } from '@comp/template/tileStyle/tileEditPageMixin'

export default {
    name: 'PurchaseContractParamModal',
    mixins: [tileEditPageMixin],
    data () {
        return {
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractParams`, '合同参数'),
                form: {
                    paramName: '',
                    paramType: '',
                    paramContent: '',
                    fbk1: '',
                    fbk2: '',
                    fbk3: '',
                    fbk4: '',
                    fbk5: '',
                    fbk6: '',
                    fbk7: '',
                    fbk8: '',
                    fbk9: '',
                    fbk10: '',
                    extendField: ''
                },
                panels: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            ref: 'baseForm',
                            colSpan: '24',
                            type: 'form',
                            form: {},
                            list: [
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_paramsName`, '参数名称'),
                                    fieldName: 'paramName', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterParamsName`, '请输入参数名称')
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_paramsType`, '参数类型'),
                                    fieldName: 'paramType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterParamsTypeList`, '请输入参数类型：1、合同体；2、合同清单'),
                                    dictCode: 'srmParamType'
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_paramContent`, '参数内容'),
                                    fieldName: 'paramContent', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterParamContent`, '请输入参数内容')
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk1',
                                    fieldName: 'fbk1', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk1'
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk2',
                                    fieldName: 'fbk2', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk2'
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk3',
                                    fieldName: 'fbk3', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk3'
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk4',
                                    fieldName: 'fbk4', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk4'
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk5',
                                    fieldName: 'fbk5', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk5'
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk6',
                                    fieldName: 'fbk6', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk6'
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk7',
                                    fieldName: 'fbk7', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk7'
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk8',
                                    fieldName: 'fbk8', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk8'
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk9',
                                    fieldName: 'fbk9', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk9'
                                },
                                {
                                    type: 'input', 
                                    label: 'fbk10',
                                    fieldName: 'fbk10', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')+ 'fbk10'
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extendedField`, '扩展字段'),
                                    fieldName: 'extendField', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterExtendedField`, '请输入扩展字段')
                                }
                            ]}}
                ],
                validRules: {
                    paramName: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    paramType: [{max: 20, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow20`, '内容长度不能超过20个字符')}],
                    paramContent: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')}],
                    fbk1: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    fbk2: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    fbk3: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    fbk4: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    fbk5: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    fbk6: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    fbk7: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    fbk8: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    fbk9: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    fbk10: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    extendField: [{max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}]
                }
            },
            url: {
                add: '/contract/purchaseContractParam/add',
                edit: '/contract/purchaseContractParam/edit',
                detail: '/contract/purchaseContractParam/queryById'
            }
        }
    },
    methods: {
        validateCode (rule, value, callback) {
            // 重复校验
            var params = {
                tableName: '',
                fieldName: '',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then(res => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        selectCallBack (item) {
            this.pageData.form.dictCode = item[0].dictCode
            this.$refs.editPage.$forceUpdate()
        }
    }
}
</script>

<style lang="less" scoped>
</style>