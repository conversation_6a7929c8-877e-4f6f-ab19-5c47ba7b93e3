<template>
  <div style="height: 100%">
    <a-spin :spinning="confirmLoading">
      <setp-lay-out
        v-if="show"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :pageFooterButtons="pageFooterButtons"
        :pageHeaderButtons="pageHeaderButtons"
        :sourceGroups="sourceGroups"
        :pageStatus="pageStatus"
        :canchangeStep="canchangeStep"
        @nextStepHandle="nextStepHandle"
        @preStepHandle="preStepHandle"
        :fromSourceData="fromSourceData">
        <template #tenderBidLetterFormatGroupVo="{ slotProps }">
          <tenderBidLetterFormatGroupVo
            ref="tenderBidLetterFormatGroupVo"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :tenderBidLetterFormatGroupVo="fromSourceData.tenderBidLetterFormatGroupVo"></tenderBidLetterFormatGroupVo>
        </template>
        <template #purchaseTenderEvaluationPrinciples="{ slotProps }">
          <purchaseTenderEvaluationPrinciples
            ref="purchaseTenderEvaluationPrinciples"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :purchaseTenderEvaluationPrinciples="fromSourceData.purchaseTenderEvaluationPrinciples"> </purchaseTenderEvaluationPrinciples>
        </template>
        <template #tenderEvaluationInfoVoList="{ slotProps }">
          <tenderEvaluationInfoVoList
            ref="tenderEvaluationInfoVoList"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :fromSourceData="fromSourceData"></tenderEvaluationInfoVoList>
        </template>
      </setp-lay-out>
    </a-spin>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import purchaseTenderEvaluationPrinciples from '@views/srm/bidding_new/BiddingHall/BiddingFile/modules/purchaseTenderEvaluationPrinciples'
import tenderBidLetterFormatGroupVo from '@views/srm/bidding_new/BiddingHall/BiddingFile/modules/tenderBidLetterFormatGroupVo'
import tenderEvaluationInfoVoList from '@views/srm/bidding_new/BiddingHall/BiddingFile/modules/tenderEvaluationInfoVoList'
import attachmentInfoList from '@views/srm/bidding_new/BiddingHall/BiddingFile/modules/attachmentInfoList'
import setpLayOut from '../../components/setpLayOut'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import { cloneDeep } from 'lodash'

export default {
    name: 'BiddingFile',
    props: {
        row: {
            type: Object,
            default (){
                return {}
            }
        },
        check: {
            type: Boolean,
            default (){
                return false
            }
        }
    },
    components: {
        fieldSelectModal,
        purchaseTenderEvaluationPrinciples,
        tenderEvaluationInfoVoList,
        attachmentInfoList,
        setpLayOut,
        tenderBidLetterFormatGroupVo
    },
    mixins: [baseMixins],
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        },
        canchangeStep () {
            return this.pageStatus == 'detail'
        },
        sourceGroups () {
            let g = this.handleInitSourceGroups()
            return g
        }
    },
    data () {
        return {
            biddingData: {},
            flowView: false,
            pageStatus: 'edit',
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            show: false,
            rejectForm: {
                node: '',
                reject: ''
            },
            sourceGroupsDemo: [
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxmK_9dbd9bbe`, '投标函格式'),
                    groupNameI18nKey: '',
                    groupCode: 'tenderBidLetterFormatGroupVo',
                    groupType: 'head',
                    show: true,
                    sortOrder: '1'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBjF_4119a25d`, '评标原则'),
                    groupNameI18nKey: '',
                    groupCode: 'purchaseTenderEvaluationPrinciples',
                    groupType: 'head',
                    show: true,
                    sortOrder: '2'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBCh_411bee3f`, '评标方法'),
                    groupNameI18nKey: '',
                    groupCode: 'tenderEvaluationInfoVoList',
                    groupType: 'head',
                    show: true,
                    sortOrder: '3'
                }
            ],
            businessRefName: 'businessRef',
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    type: 'primary',
                    click: this.saveEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    click: this.handleBack
                }
            ],
            pageHeaderButtons: [],
            currentGroupCode: {
                groupCode: 'tenderBidLetterFormatGroupVo'
            },
            currentEditRow: {},
            status: '',
            confirmLoading: false,
            url: {
                detail: '/tender/tenderProjectAttachmentInfo/query',
                add: '/tender/tenderProjectAttachmentInfo/add',
                edit: '/tender/tenderProjectAttachmentInfo/edit',
                publish: '/tender/tenderProjectAttachmentInfo/publish'
            }
        }
    },
    methods: {
        // 角色判断 投标单位不能查看评标方法和评标规则
        checkApplyRole () {
            let sourceGroups = cloneDeep(this.sourceGroupsDemo)
            let applyRole = this.$route.path.indexOf('/tenderHall') !== -1 ? '2' : '1'
            if (applyRole == '2') {
                sourceGroups = cloneDeep(this.sourceGroupsDemo).filter(item => {
                    return ['tenderBidLetterFormatGroupVo'].includes(item.groupCode)
                })
            }
            return sourceGroups
        },
        handleInitSourceGroups () {
            let sourceGroups = []
            // 开标方式  0-全部、1-线上、2-线下*/   评标方式 0-全部、1-线上、2-线下*/
            let {bidOpenType, evaluationType} = this.subPackageRow
            let methodsMap = {
                '1_2': () => {
                    sourceGroups = cloneDeep(this.sourceGroupsDemo).filter(item => {
                        return ['tenderBidLetterFormatGroupVo'].includes(item.groupCode)
                    })
                },
                '2_2': () => {
                    sourceGroups = cloneDeep(this.sourceGroupsDemo).filter(item => {
                        return [].includes(item.groupCode)
                    })
                },
                '1_1': () => {
                    sourceGroups = this.checkApplyRole()
                },
                '2_1': () => {
                    sourceGroups = this.checkApplyRole()
                }
            }
            methodsMap[`${bidOpenType}_${evaluationType}`]()
            return sourceGroups
        },
        setPageStatus () {
            // let status = ['1', '2'].includes(this.fromSourceData.status) ? 'detail' : 'edit'
            let status = 'edit'
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0' || this.check) {
                status = 'detail'
                this.pageHeaderButtons = [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                        click: this.handleBack
                    }
                ]
            }
            this.pageStatus = status
        },
        // 数据更新
        processingResData (res, refreshChildren) {
            if (res.success) {
                this.fromSourceData = Object.assign({}, res.result)
                console.log('this.fromSourceData', this.fromSourceData)
                this.show = true
                this.setPageStatus()
                // 更新子组件数据
                if (refreshChildren) {
                    this.$nextTick(() => {
                        this.childrenInitData(this.fromSourceData)
                    })
                }
            } else {
                this.$message.error(res.message)
            }
        },
        queryDetail (refreshChildren = false) {
            this.confirmLoading = true
            let res = {
                success: true,
                result: {
                    ...this.row
                }
            }
            console.log(res)
            this.processingResData(res, refreshChildren)
            this.confirmLoading = false
        },
        setCurrentStep (i) {
            this.$refs[this.businessRefName].currentStep = i
        },
        async saveEvent (arg) {
            // 保存不用校验附件
            let ValidateArr = ['tenderBidLetterFormatGroupVo', 'purchaseTenderEvaluationPrinciples', 'tenderEvaluationInfoVoList']
            this.getAllValidate(ValidateArr).then(async (res) => {
                if (res.validStatus) {
                    let params = await this.getParamsName()
                    let url = '/tender/purchaseTenderClarificationInfo/change/attachment'
                    this.confirmLoading = true
                    postAction(url, params)
                        .then((res) => {
                            let type = res.success ? 'success' : 'error'
                            if (res.success) {
                                let result = JSON.parse(JSON.stringify(res.result))
                                this.biddingData = result || {}
                                this.$message[type](res.message)
                                // this.queryDetail(true)
                                this.processingResData(res, true)
                                // this.$emit('resetCurrentSubPackage')
                                this.resetCurrentSubPackage()
                            } else {
                                this.$message[type](res.message)
                            }
                        })
                        .finally(() => {
                            this.confirmLoading = false
                        })
                } else {
                    this.setCurrentStep(res.currentStep)
                }
            })
        },
        async getParamsName () {
            let params = {
                ...this.fromSourceData,
                subpackageId: this.subId,
                tenderProjectId: this.tenderCurrentRow.id
            }
            // 开标方式  0-全部、1-线上、2-线下*/   评标方式 0-全部、1-线上、2-线下*/
            let {bidOpenType, evaluationType} = this.subPackageRow
            if (bidOpenType == '1') {
                params['tenderBidLetterFormatGroupVo'] = this.$refs.tenderBidLetterFormatGroupVo.externalAllData()
            }
            if (evaluationType == '1') {
                params['purchaseTenderEvaluationPrinciples'] = await this.$refs.purchaseTenderEvaluationPrinciples.externalAllData()
                params['tenderEvaluationInfoVoList'] = await this.$refs.tenderEvaluationInfoVoList.externalAllData()
            }
            return params
        },
        handleBack () {
            this.$emit('hide', {biddingData: this.biddingData})
        },  
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef(this.currentGroupCode)
            itemGrid.insertAt([...data], -1)
        },
        getAllValidate (refNames) {
            // 定义Promise回调处理
            const handlePromise = (list = []) =>
                list.map((promise) =>
                    promise.then(
                        (res) => ({
                            status: 'success',
                            res
                        }),
                        (err) => ({
                            status: 'error',
                            err
                        })
                    )
                )
            // 获取子组件的抛出的校验promise
            let promiseValidateArr = refNames.map((ref) => {
                if (this.$refs[ref]) return this.$refs[ref].getValidatePromise()
            }).filter((promise) => promise)
            return new Promise((resolve, reject) => {
                Promise.all(handlePromise(promiseValidateArr))
                    .then((result) => {
                        let currentStep = null
                        let flag = true
                        for (let i = 0; i < result.length; i++) {
                            if (result[i].status === 'error') {
                                currentStep = i
                                flag = false
                            }
                        }
                        if (flag) {
                            let resolveData = { validStatus: true, currentStep: currentStep, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiLR_48599c04`, '验证成功') }
                            resolve(resolveData)
                        } else {
                            let resolveData = { validStatus: false, currentStep: currentStep, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiKmWVGv_95d29236`, '验证失败，请处理') }
                            resolve(resolveData)
                        }
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        },
        nextStepHandle (data) {
            if (this.currentGroupCode.groupCode == 'tenderBidLetterFormatGroupVo') {
                if (this.pageStatus == 'edit') {
                    // 只需要校验当前投标函组件数据
                    this.getAllValidate(['tenderBidLetterFormatGroupVo']).then(async (res) => {
                        if (res.validStatus) {
                            this.confirmLoading = true
                            let params = await this.getParamsName()
                            let url = '/tender/purchaseTenderClarificationInfo/change/attachment'
                            let p = new Promise((resolve, reject) => {
                                postAction(url, params)
                                    .then((res) => {
                                        if (res.success) {
                                            this.processingResData(res, true)
                                            resolve(res)
                                        } else {
                                            this.setCurrentStep(0)
                                            reject(res)
                                        }
                                    })
                                    .finally(() => {
                                        if (data) {
                                            this.currentGroupCode = data.groupData
                                        }
                                        this.confirmLoading = false
                                    })
                            })
                            return p
                        } else {
                            this.setCurrentStep(0)
                        }
                    })
                }
            } else {
                if (data) {
                    this.currentGroupCode = data.groupData
                }
            }
        },
        preStepHandle (data) {
            this.currentGroupCode = data.groupData
        },
        // 子组件数据更新调用
        childrenInitData (data) {
            this.sourceGroups.map((ref) => {
                this.$refs[ref.groupCode].init(data)
            })
        }
    },
    created () {
        this.queryDetail()
    },
    beforeDestroy () {
        this.$ls.remove('changeBidFile')
    }
}
</script>
