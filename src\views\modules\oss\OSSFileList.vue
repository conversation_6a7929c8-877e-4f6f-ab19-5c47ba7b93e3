<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form
        layout="inline"
        @keyup.enter.native="searchQuery"
      >
        <a-row :gutter="24">
          <a-col
            :md="6"
            :sm="8"
          >
            <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_fileName`, '文件名称')">
              <a-input
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_inputFileName`, '请输入文件名称')"
                v-model="queryParam.fileName"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="6"
            :sm="8"
          >
            <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_fileAdderss`, '文件地址')">
              <a-input
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_inputFileAdderss`, '请输入文件地址')"
                v-model="queryParam.url"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!--      <a-button type="primary" icon="download" @click="handleExportXls('文件列表')">导出</a-button>-->
      <a-upload
        name="file"
        :multiple="false"
        :action="uploadAction"
        :headers="tokenHeader"
        :show-upload-list="false"
        :before-upload="beforeUpload"
        @change="handleChange"
      >
        <a-button>
          <a-icon type="upload" />
          
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_fileUpload`, '文件上传') }}
        </a-button>
      </a-upload>
    </div>

    <!-- table区域-begin -->
    <div>
      <div
        class="ant-alert ant-alert-info"
        style="margin-bottom: 16px;"
      >
        <em class="anticon anticon-info-circle ant-alert-icon" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_selected`, '已选择') }} <a
          style="font-weight: 600"
        >{{
          selectedRowKeys.length }}</a>{{ $srmI18n(`${$getLangAccount()}#i18n_title_term`, '项') }}
        <a
          style="margin-left: 24px"
          @click="onClearSelected"
        >{{ $srmI18n(`${$getLangAccount()}#i18n_title_clearALL`, '清空') }}</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        row-key="id"
        :columns="columns"
        :data-source="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :row-selection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange"
      >
        <span
          slot="action"
          slot-scope="text, record"
        >
          <a @click="ossDelete(record.id)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->
  </a-card>
</template>

<script>
import {elsListMixin} from '@/mixins/elsListMixin'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    name: 'OSSFileList',
    mixins: [elsListMixin],
    data () {
        return {
            description: srmI18n(`${getLangAccount()}#i18n_title_fileList`, '文件列表'),
            // 表头
            columns: [
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号'),
                    dataIndex: '',
                    key: 'rowIndex',
                    width: 60,
                    align: 'center',
                    customRender: function (t, r, index) {
                        return parseInt(index) + 1
                    }
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_fileName`, '文件名称'),
                    align: 'center',
                    dataIndex: 'fileName'
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_fileAdderss`, '文件地址'),
                    align: 'center',
                    dataIndex: 'url'
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
                    dataIndex: 'action',
                    align: 'center',
                    scopedSlots: {customRender: 'action'}
                }
            ],
            url: {
                upload: '/oss/file/upload',
                list: '/oss/file/list',
                delete: '/oss/file/delete'
            }
        }
    },
    computed: {
        uploadAction () {
            return this.$variateConfig['domainURL'] + this.url.upload
        }
    },
    methods: {
        beforeUpload (file) {
            var fileType = file.type
            if (fileType === 'image') {
                if (fileType.indexOf('image') < 0) {
                    this.$message.warning(srmI18n(`${getLangAccount()}#i18n_title_pleaseUploadImg`, '请上传图片'))
                    return false
                }
            } else if (fileType === 'file') {
                if (fileType.indexOf('image') >= 0) {
                    this.$message.warning(srmI18n(`${getLangAccount()}#i18n_title_pleaseUploadFile`, '请上传文件'))
                    return false
                }
            }
            return true
        },
        handleChange (info) {
            if (info.file.status === 'done') {
                if (info.file.response.success) {
                    this.loadData()
                    this.$message.success(`${info.file.name} ` + srmI18n(`${getLangAccount()}#i18n_title_uploadSuccess`, '上传成功!'))
                } else {
                    this.$message.error(`${info.file.name} `+ srmI18n(`${getLangAccount()}#i18n_title_uploadError`, '上传失败!'))
                }
            } else if (info.file.status === 'error') {
                this.$message.error(`${info.file.name}`+ srmI18n(`${getLangAccount()}#i18n_title_uploadError`, '上传失败!'))
            }
        },
        ossDelete (id) {
            var that = this
            that.$confirm({
                title: srmI18n(`${getLangAccount()}#i18n_title_confirmDelete`, '确认删除'),
                content: srmI18n(`${getLangAccount()}#i18n_title_confirmDeleteFile`, '是否删除选中文件?'),
                onOk: function () {
                    that.handleDelete(id)
                }
            })
        }
    }
}
</script>

<style scoped>
  @import '~@assets/less/common.less'
</style>
