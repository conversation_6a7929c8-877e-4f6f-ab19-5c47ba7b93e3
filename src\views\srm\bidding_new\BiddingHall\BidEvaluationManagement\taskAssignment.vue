<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <content-header :btns="btns" />
      <titleTrtl>
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_OVsutvVH_2489926`, '多轮报价基本信息') }}</span>
      </titleTrtl>
      <div>
        <Dataform
          ref="dataform"
          :formData="formData"
          :pageStatus="pageStatus"
          :validateRules="validateRules"
          :fields="fields"> </Dataform>
      </div>
      <titleTrtl>
        <span>{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_LSzE_2527b98d`, '任务分配')) }}</span>
      </titleTrtl>
      <div>
        <vxe-grid
          v-bind="gridConfig"
          :height="250"
          ref="table"
          :data="tableData"
          :columns="tableColumns"
          @radio-change="radioChange"
          show-overflow="title">
          <template #singe="{ row, column }">
            <a-radio :value="row[column.property]"></a-radio>
          </template>
        </vxe-grid>
      </div>
    </a-spin>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '../components/content-header'
import titleTrtl from '../components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import Dataform from '../components/Dataform'
export default {
    mixins: [tableMixins, baseMixins],
    components: {
        ContentHeader,
        titleTrtl,
        Dataform
    },
    data () {
        return {
            confirmLoading: false,
            tableColumns: [],
            tableColumnsDemo: [
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ULRL_40e48a13`, '评委名称'),
                    field: 'judgesName',
                    width: 100
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ULWWWey_d157ccad`, '评委els账号'),
                    field: 'judgesElsAccount',
                    width: 120
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBVe_411f1f3e`, '评标组长'),
                    field: 'judgesGroupLeader',
                    width: 100,
                    disabled: true,
                    type: 'radio',
                    slots: {
                        radio: ({ row }) => {
                            return [
                                <a-radio disabled={this.pageStatus == 'detail'} value={row.judgesGroupLeader} checked={row.judgesGroupLeader == '1'} onChange={() => this.radioChange({ row })}>
                                    {''}
                                </a-radio>
                            ]
                        }
                    }
                }
            ],
            originColumn: [],
            tableData: [],
            specialFields: [],
            headStatus: '0',
            btns: [{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'), type: 'primary', click: this.save, showCondition: this.showSaveBtn }],
            fields: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQiTOVsu_b2d6fb34`, '是否允许多轮报价'),
                    fieldLabelI18nKey: '',
                    field: 'stageQuote',
                    defaultValue: '0',
                    required: '1',
                    fieldType: 'select',
                    dictCode: 'yn',
                    bindFunction: (value, item) => {
                        this.fields[1].disabled = value == '0'
                        this.$set(this.formData, 'stageQuoteOperator', '')
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OVsuhAL_bdf7586e`, '多轮报价发起人'),
                    fieldLabelI18nKey: '',
                    field: 'stageQuoteOperator',
                    defaultValue: '0',
                    fieldType: 'select',
                    dictCode: 'tenderStageQuoteOperator',
                    disabled: false
                }
            ],
            validateRules: {
                stageQuote: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMKQiTOVsu_2628d6f9`, '请填写是否允许多轮报价'), trigger: 'blur' }]
            },
            formData: {},
            url: {
                queryBySubpackageId: '/tender/evaluation/purchaseTenderProjectEvaSettingHead/queryBySubpackageId',
                columns: '/tender/evaluation/purchaseTenderProjectEvaSettingHead/queryEvaGroupBySubpackageId',
                save: '/tender/evaluation/purchaseTenderProjectEvaSettingHead/apply'
            }
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'resetCurrentSubPackage'],
    computed: {
        subId () {
            return this.subpackageId()
        },
        pageStatus () {
            return this.headStatus == '0' ? 'edit' : 'detail'
        }
    },
    methods: {
        showSaveBtn () {
            return this.headStatus == '0'
        },
        save () {
            
            let headVOList = JSON.parse(JSON.stringify(this.tableData))
            let judgesGroupLeaderFlag = true,
                expertTaskSettingListFlag = true
            // 拼接数据
            headVOList.map((row) => {
                row['expertTaskSettingList'] = this.handleInitEvaGroupIdMap(row)
                if (row.judgesGroupLeader == '1') judgesGroupLeaderFlag = false
                if (row['expertTaskSettingList'].length > 0) expertTaskSettingListFlag = false
                delete row.evaGroupIdMap
            })
            let params = Object.assign({}, this.formData)
            params['headVOList'] = headVOList
            let cb = () => {
                this.confirmLoading = true
                postAction(this.url.save, params)
                    .then((res) => {
                        if (res.success) {
                            this.$message.success(res.message)
                            // this.$emit('resetCurrentSubPackage')
                            this.resetCurrentSubPackage()
                            this.init()
                        }
                    })
                    .finally(() => {
                        this.confirmLoading = false
                    })
            }
            if (judgesGroupLeaderFlag) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiFUBVe_cbc3d2e8`, '未选择评标组长'))
            this.$refs.dataform.getValidatePromise().then(res => {
                if (res) {
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sMUBGR_904b9d0e`, '保存评标设置'),
                        content: expertTaskSettingListFlag ? this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jUULSLzEULWVRIKQDJ_97252d7`, '有评审任务未分配评委，请确定是否提交') : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQsMUBGR_c021d3a5`, '是否保存评标设置'),
                        onOk: () => {
                            cb()
                        }
                    })
                }
            })
            
        },
        handleInitEvaGroupIdMap (row) {
            let { evaGroupIdMap = {}, tenderProjectId, subpackageId } = row
            let expertTaskSettingList = []
            for (let key in evaGroupIdMap) {
                if (evaGroupIdMap[key]) {
                    expertTaskSettingList.push({
                        subpackageId,
                        tenderProjectId,
                        evaGroupId: key
                    })
                }
            }
            return expertTaskSettingList
        },
        // 单选
        radioChange ({ row }) {
            this.tableData.map((item) => {
                this.$set(item, 'judgesGroupLeader', '0')
                // 特殊处理3的选项
                this.specialFields.map((key) => {
                    this.$set(item['evaGroupIdMap'], key, false)
                    this.$set(row['evaGroupIdMap'], key, true)
                })
            })
            this.$set(row, 'judgesGroupLeader', '1')
        },
        getData () {
            let params = {
                subpackageId: this.subId
            }
            return getAction(this.url.queryBySubpackageId, params).then((res) => {
                if (res.success) {
                    this.formData = res.result || {}
                    if (this.checkType == '0') {
                        this.formData.stageQuote = '0'
                        this.formData.stageQuoteOperator = ''
                        this.fields[1].disabled = true
                        this.fields[0].disabled = true
                    }
                    this.tableData = this.formData.headVOList || []
                    this.headStatus = this.tableData[0].headStatus || '0'
                    this.tableData.map((row) => {
                        this.$set(row, 'evaGroupIdMap', {})
                        row.expertTaskSettingList &&
                            row.expertTaskSettingList.map((item) => {
                                let id = item.evaGroupId
                                // 设置iD的MAP,是否勾选
                                row['evaGroupIdMap'][id] = true
                            })
                        if (row.judgesGroupLeader == '1') {
                            this.$nextTick(() => {
                                this.$refs.table.setRadioRow(row)
                            })
                        }
                    })
                }
            })
        },
        getColumns () {
            let params = {
                subpackageId: this.subId
            }
            return getAction(this.url.columns, params).then((res) => {
                if (res.success) {
                    this.originColumn = res.result
                    this.initColumn(res.result)
                }
            })
        },
        // 处理动态列
        initColumn (data = []) {
            this.tableColumns = [...this.tableColumnsDemo]
            data.map((item) => {
                // 特殊类型3
                if (item.type == 3 || item.type == 4) this.specialFields.push(item.id)
                let column = {
                    title: item.name,
                    field: item.id,
                    type: item.type,
                    width: 160,
                    slots: {
                        default: ({ row, column }) => {
                            return [<a-checkbox v-model={row.evaGroupIdMap[column.property]} disabled={this.pageStatus == 'detail' || item.type == 3 || item.type == 4}></a-checkbox>]
                        }
                    }
                }
                this.tableColumns.push(column)
            })
        },
        initSpecialistClasses () {
            if (this.headStatus == '0') {
                // 远端列处理
                this.originColumn.map((column) => {
                    this.tableData.map((item) => {
                        // 专家类处理
                        if (item.specialistClasses && item.specialistClasses.includes(column.specialistClasses)) {
                            // item.evaGroupIdMap[column.id] = true
                            // 价格分特殊处理
                            if (column.type == '3' || column.type == '4') return
                            this.$set(item.evaGroupIdMap, column.id, true)
                        }
                    })
                })
            }
            console.log(this.tableData)
        },
        init () {
            this.confirmLoading = true
            this.tableColumns = []
            Promise.all([this.getData(), this.getColumns()])
                .then(() => {
                    console.log(this.headStatus)
                    this.initSpecialistClasses()
                    this.confirmLoading = false
                })
                .catch((error) => {
                    this.confirmLoading = false
                    console.log(error)
                })
        }
    },
    mounted () {
        this.init()
    }
}
</script>
