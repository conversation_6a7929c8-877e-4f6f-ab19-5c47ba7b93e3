<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      refresh
      :currentEditRow="currentEditRow"
      :url="url" />
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script>
import {EditMixin} from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'
import {SelectModal} from '@comp/template/business/class/selectModal'
import {getLangAccount, srmI18n} from '@/utils/util.js'
import flowViewModal from '@comp/flowView/flowView'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'

export default {
    name: 'EnterpriseInfoChangeEdit',
    mixins: [EditMixin],
    components: {
        flowViewModal,
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        let batchDownloadBtn = new BatchDownloadBtn()
        batchDownloadBtn.pageCode = 'bankInfo'
        return {
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            pageData: {
                form: {},
                groups: [
                    { groupName: '表格行控制', groupCode: 'gridEditConfig', groupType: 'gridEditConfig',  extend: {
                        editConfig: {
                            showStatus: true,
                            mode: 'row',
                            activeMethod: function (gridData, row) {
                                if (!row.toElsAccount&&row.elsAccount) {
                                    return false
                                }else{
                                    if(row.accessed&&row.accessed==='1'){
                                        return false
                                    }
                                }
                                return true
                            }
                        }
                    }, custom: {}},
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contactInfo`, '联系人信息'), groupCode: 'contactsInfo', type: 'grid', custom: {
                        ref: 'supplierContactsInfoList',
                        columns: [
                            {
                                type: 'checkbox', width: 40
                            },
                            {
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseElsAccount`, '采购ELS账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                ...new SelectModal({
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                                    field: 'subAccount',
                                    required: '1',
                                    width: 150,
                                    bindFunction: function (row, data) {
                                        row.subAccount = data[0].subAccount,
                                        row.name = data[0].realname,
                                        row.telphone = data[0].phone,
                                        row.email = data[0].email
                                    }, extend: { modalColumns: [
                                        {field: 'subAccount', title: srmI18n(`${getLangAccount()}#i18n_field_subAccount`, '子账号'), with: 150},
                                        {field: 'realname', title: srmI18n(`${getLangAccount()}#i18n_title_name`, '姓名'), with: 150},
                                        {field: 'phone', title: srmI18n(`${getLangAccount()}#i18n_title_telphone`, '电话号码'), with: 150},
                                        {field: 'email', title: srmI18n(`${getLangAccount()}#i18n_field_email`, '邮箱'), with: 150}],
                                    modalUrl: '/account/elsSubAccount/getByElsAccount',
                                    modalParams: function (Vue, form, row) { return {participants: 'sale', toElsAccount: row.elsAccount} },
                                    beforeCheckedCallBack: function (Vue, row) {
                                        return new Promise((resolve, rejec) => {
                                            let toElsAccount = row.toElsAccount || ''
                                            return toElsAccount === '' ? rejec(srmI18n(`${getLangAccount()}#i18n_title_publicInformationOnlyMaintainedSupplierBasicInformationEnterprise`, '公共信息只能供方在企业基本信息中维护')) :resolve('success')
                                        })
                                    }}
                                })
                                
                            },
                            {
                                required: '0',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_function`, '职能'),
                                field: 'functionName',
                                width: 150,
                                dictCode: 'JobFunction',
                                editRender: {name: '$select'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_position`, '职位'),
                                required: '1',
                                field: 'position',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                                field: 'name',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话号码'),
                                field: 'telphone',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'),
                                required: '1',
                                field: 'email',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: 'QQ',
                                field: 'qqNo',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_weChat`, '微信'),
                                field: 'wxNo',
                                width: 150,
                                editRender: {name: '$input'}
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addContactsItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteContactsEvent}
                        ],
                        rules: {
                            subAccount: [{required: true, message: '子账号不能为空'}],
                            functionName: [{required: true, message: '职能不能为空'}],
                            position: [{required: true, message: '职位不能为空'}],
                            name: [{required: true, message: '姓名不能为空'}],
                            telphone: [{
                                required: true,
                                pattern: /^((13|14|15|16|17|18|19)[0-9]{1}\d{8})$/,
                                message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phoneError`, '手机号不正确')
                            }],
                            email: [{
                                required: true,
                                pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
                                message: '邮箱格式不正确'
                            }]
                        }
                    }},
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addressInfo`, '地址信息'), groupCode: 'addressInfo', type: 'grid', custom: {
                        ref: 'supplierAddressInfoList',
                        columns: [
                            {
                                type: 'checkbox', width: 40
                            },
                            {
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseElsAccount`, '采购ELS账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_country`, '国家'),
                                field: 'country',
                                width: 150,
                                required: '1',
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_province`, '省份'),
                                field: 'province',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_city`, '城市'),
                                field: 'city',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_address`, '详细地址'),
                                field: 'address',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话'),
                                field: 'telphone',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zipCode`, '邮编'),
                                field: 'fax',
                                width: 150,
                                editRender: {name: '$input', props: {type: 'number'}}
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addAddressItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteAddressEvent}
                        ],
                        rules: {
                            country: [{required: true, message: '国家不能为空'}],
                            province: [{required: true, message: '省份不能为空'}],
                            city: [{required: true, message: '城市不能为空'}],
                            address: [{required: true, message: '地址不能为空'}],
                            telphone: [{required: true, message: '电话不能为空'}],
                            fax: [{required: true, message: '邮编不能为空'}]
                        }
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankInfo`, '银行信息'), groupCode: 'bankInfo', type: 'grid', custom: {
                        ref: 'supplierBankInfoList',
                        columns: [
                            {
                                type: 'checkbox', width: 40
                            },
                            {
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseElsAccount`, '采购ELS账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCountry`, '银行国家'),
                                field: 'bankCountry',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankProvince`, '银行省份'),
                                field: 'bankProvince',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCity`, '银行城市'),
                                field: 'bankCity',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankBranchName`, '开户行全称'),
                                field: 'bankBranchName',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankAccount`, '银行账号'),
                                field: 'bankAccount',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankAccountName`, '银行账号名称'),
                                field: 'bankAccountName',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cooperationBankType`, '合作银行类型'),
                                field: 'cooperationBankType',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCode`, '银行代码'),
                                field: 'bankCode',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCodeIBAN`, 'IBAN（国际银行帐户号码）'),
                                field: 'iban',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_swiftCode`, 'SWIFT CODE（银行国际代码）'),
                                field: 'swiftCode',
                                width: 150,
                                editRender: {name: '$input'}
                            }
                            ,
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedName`, '开户资料扫描件名称'),
                                field: 'fileName',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedPath`, '开户资料扫描件地址'),
                                field: 'filePath',
                                width: 150
                            },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addBankItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteBankEvent},
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                type: 'upload',
                                businessType: 'supplierMasterData',
                                beforeChecked: true,
                                beforeCheckedCallBack: this.bankFileUploadCallBack,
                                single: true,
                                modalVisible: false,
                                callBack: this.uploadCallBack
                            },
                            { ...batchDownloadBtn.btnConfig }
                        ],
                        rules: {
                            bankCountry: [{required: true, message: '银行国家不能为空'}],
                            bankProvince: [{required: true, message: '银行省份不能为空'}],
                            bankCity: [{required: true, message: '银行城市不能为空'}],
                            bankBranchName: [{required: true, message: '开户行全称不能为空'}],
                            bankAccount: [{required: true, message: '银行账号不能为空'}],
                            bankAccountName: [{required: true, message: '银行名称不能为空'}],
                            bankCode: [{required: true, message: '银行代码不能为空'}]
                        },
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.bankFileDownloadEvent }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'supplierInfoChangeAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'supplierMasterData', callBack: this.uploadInfoChangCallBack }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }
                ],
                formFields: [],
                footerButtons: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.releaseEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/supplier/supplierInfoChangeHead/add',
                edit: '/supplier/supplierInfoChangeHead/edit',
                detail: '/supplier/supplierMaster/queryById',
                release: '/supplier/supplierInfoChangeHead/releaseByIds',
                submit: '/supplier/supplierInfoChangeHead/submit',
                submitAudit: '/a1bpmn/audit/api/submit',
                upload: '/attachment/purchaseAttachment/upload',
                download: '/attachment/purchaseAttachment/download'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount
            if(!elsAccount || elsAccount==''){
                elsAccount = this.$ls.get('Login_elsAccount')
            }
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_supplierMasterData_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        if (this.currentEditRow.changeNumber && this.currentEditRow.initiatorElsAccount) {
            this.url.detail = '/supplier/supplierInfoChangeHead/queryAfterInfoById'
        }
    },
    methods: {
        allowSubmitAudit (row) {
            const params = this.$refs.editPage.getPageData()
            if(params.needAudit==='1'){
                if(row.auditStatus==='0'){
                    return false
                }
            }
            return true
        },
        showcCncelConditionBtn (row){
            if(row.auditStatus==='1'){
                return false
            }
            return true
        },
        showFlowConditionBtn (row){
            if(row.auditStatus==='0'){
                return false
            }
            return true
        },
        closeFlowView (){
            this.flowView = false
        },
        bankFileDownloadEvent (row) {
            this.downloadFile (row)
        },
        certificateFileDownloadEvent (row) {
            this.downloadFile (row)
        },
        downloadFile (row){
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const [id, fileName] = row.fileName.split('-')
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        bankFileUploadCallBack (data){
            return new Promise((resolve)=> {
                // 验证的规则
                if (data && data.length) {
                    if (data.length===1) {
                        if(data[0].elsAccount && !data[0].toElsAccount){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_rWFLRdXLDjRRVHWxOHccr_cae59b`, '改数据为供应商维护的公共信息，不能进行修改'))
                            resolve(false)
                        }
                        resolve(true)
                    } else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_onlySelectOne`, '只能选择一条'))
                        resolve(false)
                    }
                }else{
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDataoPerated`, '请选择需要操作的数据'))
                    resolve(false)
                }

            })
        },
        certificateFileUploadCallBack (data){
            return new Promise((resolve)=> {
                // 验证的规则
                if (data && data.length) {
                    if (data.length===1) {
                        if(data[0].elsAccount && !data[0].toElsAccount){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_rWFLRdXLDjRRVHWxOHccr_cae59b`, '改数据为供应商维护的公共信息，不能进行修改'))
                            resolve(false)
                        }
                        resolve(true)
                    } else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_onlySelectOne`, '只能选择一条'))
                        resolve(false)
                    }
                }else{
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDataoPerated`, '请选择需要操作的数据'))
                    resolve(false)
                }

            })
        },
        uploadCallBack (result, refName) {
            console.log(result)
            const { id = '', fileName = '', filePath = '' } = result[0] || {}
            const fileGrid = this.$refs.editPage.$refs[refName][0].getCheckboxRecords()
            fileGrid[0].filePath = `${filePath}`
            fileGrid[0].fileName = `${id}-${fileName}`
        },

        // 上传企业信息变更文件回调事件
        uploadInfoChangCallBack (result, refName) {
            console.log(result)
            let fileGrid = this.$refs.editPage.$refs.supplierInfoChangeAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        // 下载企业信息变更文件
        downloadEvent (row) {
            const params = {id: row.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        // 预览企业信息变更文件
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        // 删除企业信息变更文件
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.supplierInfoChangeAttachmentList[0]
            fileGrid.remove(row)
        },

        addContactsItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierContactsInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach(item => {
                if(item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            const form =  this.$refs.editPage.getPageData()
            itemData['elsAccount'] = form.toElsAccount
            itemData['toElsAccount'] = form.elsAccount
            itemGrid.insert([itemData])
        },
        deleteContactsEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierContactsInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            for(let item of checkboxRecords){
                if(!item.toElsAccount){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qiFjWFsMKRdXLDjRRWFWxOqQG_a891e523`, '被选择的数据中存在供应商维护的公共数据，不能被删除'))
                    return
                }
            }
            itemGrid.removeCheckboxRow()
        },
        addAddressItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierAddressInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach(item => {
                if(item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            const form =  this.$refs.editPage.getPageData()
            itemData['elsAccount'] = form.toElsAccount
            itemData['toElsAccount'] = form.elsAccount
            itemGrid.insert([itemData])
        },
        deleteAddressEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierAddressInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            for(let item of checkboxRecords){
                if(!item.toElsAccount){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '被选择的数据中存在供应商维护的公共数据，不能被删除'))
                    return
                }
            }
            itemGrid.removeCheckboxRow()
        },
        addBankItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierBankInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach(item => {
                if(item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            const form =  this.$refs.editPage.getPageData()
            itemData['elsAccount'] = form.toElsAccount
            itemData['toElsAccount'] = form.elsAccount
            itemGrid.insert([itemData])
        },
        deleteBankEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierBankInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            for(let item of checkboxRecords){
                if(!item.toElsAccount){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qiFjWFsMKRdXLDjRRWFWxOqQG_a891e523`, '被选择的数据中存在供应商维护的公共数据，不能被删除'))
                    return
                }
            }
            itemGrid.removeCheckboxRow()
        },
        fieldSelectOk (data) {
            let supplierGrid = this.$refs.editPage.$refs.supplierOrgInfoList[0]
            let { fullData } = supplierGrid.getTableData()
            let supplierList = fullData.map(item => {
                return item.elsAccount
            })
            // 过滤已有数据
            let insertData = data.filter(item => {
                return !supplierList.includes(item.elsAccount)
            })
            insertData = insertData.map(item => {
                return {
                    toElsAccount: item.elsAccount
                }
            })
            supplierGrid.insertAt(insertData, -1)
        },
        addOrgItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierOrgInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach(item => {
                if(item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            const form =  this.$refs.editPage.getPageData()
            itemData['elsAccount'] = form.toElsAccount
            itemData['toElsAccount'] = form.elsAccount
            itemData['orgCategoryId'] = 'purchaseOrganization'
            itemData['orgCategoryDesc'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeadc98_purchaseOrgCode`, '采购组织')
            itemData['regulationType'] = '0'
            itemData['frozenFlag'] = '0'
            itemData['frozenFlag_dictText'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_normal`, '正常')
            itemData['regulationType_dictText'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lRrN_2e1926d9`, '手工准入')
            itemGrid.insert([itemData])
        },
        deleteOrgEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierOrgInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }else{
                let i = 1
                for(let item of checkboxRecords){
                    if(item.accessed && item.accessed=='1'){
                        this.$message.warning('第'+i+'行是通过准入流程添加的组织信息,不能删除')
                        return
                    }
                    i++
                }
            }
            itemGrid.removeCheckboxRow()
        },
        submitEvent () {
            const params = this.$refs.editPage.getPageData()
            if(!params.supplierInfoChangeId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }

            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApproval`, '确认提交审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApprovalTips`, '是否确认提交审批?'),
                onOk: function () {
                    // 需要审批
                    if (params.needAudit && params.needAudit==='1') {
                        let param = {}
                        param['businessId'] = params.supplierInfoChangeId
                        param['rootProcessInstanceId'] = params.flowId
                        param['businessType'] = 'supplierInfoChangAudit'
                        param['auditSubject'] = params.toElsAccount + ' -- 供应商信息变更审批'
                        param['params'] = JSON.stringify(params)
                        postAction(that.url.submitAudit, param).then(res => {
                            if (res.success) {
                                that.$message.success(res.message)
                                that.init()
                                that.$parent.showEditPage = false
                                // 刷新列表
                                that.$parent.$refs.listPage.loadData()
                            } else {
                                that.$message.warning(res.message)
                            }
                        })
                    } else {
                        // 无需审批
                        postAction(that.url.submit, params).then(res => {
                            if(res.success) {
                                if (!this.currentEditRow.id) {
                                    this.currentEditRow.id = res.result.id
                                }
                                this.form.supplierInfoChangeId = res.result.supplierInfoChangeId
                                this.$message.success(res.message)
                                this.init()
                                this.$parent.showEditPage = false
                                // 刷新列表
                                this.$parent.$refs.listPage.loadData()
                            }else {
                                this.$message.warning(res.message)
                            }
                            this.confirmLoading = false
                        })
                    }
                }
            })
        },
        // 保存操作
        saveEvent () {
            const params = this.$refs.editPage.getPageData()

            // 判断组织中是否存在相同值（判断条件：组织编码+准入类别）
            let orgList = params.supplierOrgInfoList
            if(orgList && orgList.length>0){
                let map = new Map()
                for(let sub of orgList){
                    if(map.get(sub.orgCode) && map.get(sub.orgCode)===sub.accessCategory){
                        this.$message.warning('组织信息中存在同一组织的同一准入品类：'+sub.orgCode+'---'+sub.accessCategory+' ,不能进行保存')
                        return
                    }
                    map.set(sub.orgCode, sub.accessCategory)
                }

            }
            let url = this.pageData.form.supplierInfoChangeId ? this.url.edit : this.url.add
            this.$refs.editPage.confirmLoading = true
            postAction(url, params).then(res => {
                if(res.success) {
                    if (!this.currentEditRow.id) {
                        this.currentEditRow.id = res.result.id
                    }
                    this.pageData.form = res.result
                    this.$refs.editPage.form = res.result
                    this.$message.success(res.message)
                }else {
                    this.$message.warning(res.message)
                }
                this.$refs.editPage.confirmLoading = false
            })
            // const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
            //     status: 'success',
            //     res
            // }), err => ({
            //     status: 'error',
            //     err
            // })))
            // let promise = this.$refs.editPage.setPromise()
            // Promise.all(handlePromise(promise)).then(result => {
            //     // 校验规则
            //     const errorIndex = result.findIndex(n => n.status === 'error')
            //     if (errorIndex !== -1) {
            //         this.$refs.editPage.currentStep = errorIndex
            //         return
            //     }
            // }).catch(err => {
            //     console.log(err)
            // })
        },
        // 发布操作
        releaseEvent (){
            const params = this.$refs.editPage.getPageData()

            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                // 校验规则
                const errorIndex = result.findIndex(n => n.status === 'error')
                if (errorIndex !== -1) {
                    this.$refs.editPage.currentStep = errorIndex
                    return
                }
                //判断组织中是否存在相同值（判断条件：组织编码+准入类别）
                let orgList = params.supplierOrgInfoList
                if(orgList && orgList.length>0){
                    let map = new Map()
                    for(let sub of orgList){
                        if(map.get(sub.orgCode) && map.get(sub.orgCode)===sub.accessCategory){
                            this.$message.warning('组织信息中存在同一组织的同一准入品类：'+sub.orgCode+'---'+sub.accessCategory+' ,不能进行保存')
                            return
                        }
                        map.set(sub.orgCode, sub.accessCategory)
                    }

                }
                let url = this.pageData.form.supplierInfoChangeId ? this.url.edit : this.url.add
                const that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                    onOk: function () {
                        // 开启笼罩
                        that.$refs.editPage.confirmLoading = true

                        // 先保存
                        postAction(url, params).then(res => {
                            if(res.success) {
                                that.pageData.form = res.result
                                that.$refs.editPage.form = res.result
                                const supplierInfoChangeId = res.result.supplierInfoChangeId
                                // 后发布操作
                                getAction(that.url.release, {id: supplierInfoChangeId}).then(res => {
                                    if(res.success){
                                        that.$message.success(res.message)
                                        that.init()
                                        that.$refs.editPage.confirmLoading = false
                                        that.$parent.showEditPage = false
                                        // 刷新列表
                                        that.$parent.$refs.listPage.loadData()
                                    } else {
                                        that.$message.warning(res.message)
                                        that.$refs.editPage.confirmLoading = false
                                    }
                                })
                            }else {
                                that.$message.warning(res.message)
                                that.$refs.editPage.confirmLoading = false
                            }
                        })
                    }
                })
            }).catch(err => {
                console.log(err)
            })
        },
        publishEvent () {
            this.$refs.editPage.handleSend()
        }
    }
}
</script>