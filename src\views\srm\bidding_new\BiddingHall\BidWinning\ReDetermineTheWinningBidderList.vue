<template>
  <div class="ChangeTenderNotice">
    <div
      v-if="!bidderStatus && !showSub"
      class="page-content list" >
      <content-header />
      <titleTrtl class="margin-b-10">
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_VVRLsBtL_4388a4cb`, '重新确认中标单位') }}</span>
        <template slot="right">
          <a-button
            v-if="this.$ls.get('SET_TENDERCURRENTROW').applyRole == 1"
            type="primary"
            @click="handleAddNotice">{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}</a-button>
        </template>
      </titleTrtl>
      <div>
        <listTable
          ref="listTable"
          :setGridHeight="pageContentHeight"
          :pageData="pageData"
          :url="url"
          :defaultParams="defaultParams"
          :statictableColumns="tableColumns"
          :showTablePage="false" />
      </div>
    </div>
    <ReDetermineTheWinningBidder
      :currentEditRow="currentEditRow"
      @back="back"
      :pageType="pageStatus"
      v-if="bidderStatus"></ReDetermineTheWinningBidder>
    <ReDetermineTheWinningSub 
      v-if="showSub" 
      :currentEditRow="currentEditRow"
      @back="back"
      :canEdit="canEdit"></ReDetermineTheWinningSub>
  </div>
</template>
<script>
import contentHeader from '../components/content-header'
import titleTrtl from '../components/title-crtl'
import listTable from '../components/listTable'
import ReDetermineTheWinningBidder from './components/ReDetermineTheWinningBidder'
import ReDetermineTheWinningSub from './components/ReDetermineTheWinningSub'
import { getAction } from '@/api/manage'

export default {
    components: {
        contentHeader,
        titleTrtl,
        listTable,
        ReDetermineTheWinningBidder,
        ReDetermineTheWinningSub
    },
    data () {
        return {
            bidderStatus: false,
            showSub: false,
            currentEditRow: {},
            pageStatus: false,
            tableColumns: [
                {
                    type: 'seq',
                    width: 60,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBLAy_7e247621`, '中标人编号'),
                    field: 'affirmNumber'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeReason`, '变更原因'),
                    field: 'reason'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_status`, '状态'),
                    field: 'status_dictText'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_publishTime`, '发布时间'),
                    field: 'publishTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 200,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit }
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            quoteType: '',
            canEdit: true,
            url: {
                queryBidWinning: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningConfirmInitInfoBySubpackage',
                list: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningListBySubpackageAndAffirmType'
            }
        }
    },
    props: {
        typePage: {
            type: String,
            default () {
                return ''
            }
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentNode'],
    computed: {
        pageContentHeight () {
            let height = document.documentElement.clientHeight - 170
            return height + 'px'
        },
        defaultParams () {
            return {
                subpackageId: this.subpackageId(),
                affirmType: '1'
            }
        },
        sub () {
            return this.subpackageId()
        }
    },
    methods: {
        handleAddNotice () { // 新增函数
            if (this.quoteType == '1') {
                this.showSub = true
            } else {
                this.bidderStatus = true
            }
            this.currentEditRow = {}
            this.canEdit = true
            this.pageStatus = true
        },
        handleView (row) {
            if (this.quoteType == '1') {
                this.showSub = true
            } else {
                this.bidderStatus = true
            }
            this.currentEditRow = row
            this.canEdit = false
            this.pageStatus = false
        },
        handleEdit (row) {
            if (this.quoteType == '1') {
                this.showSub = true
            } else {
                this.bidderStatus = true
            }
            this.currentEditRow = row
            this.canEdit = true
            this.pageStatus = true
        },
        back () {
            this.bidderStatus = false
            this.showSub = false
        },
        allowEdit ({ status }) {
            // 招标公告状态:0-新建,1-已发布,3-已变更
            if (status === '0') {
                return false
            } else {
                return true
            }
        },
        getQuoteType () {
            getAction(this.url.queryBidWinning, {subpackageId: this.sub}).then((res) => {
                this.quoteType = res.result.quoteType || '0'
            })
        }
    },
    created () {
        this.getQuoteType()
    }
}
</script>
<style lang="less" scoped>
.margin-b-10 {
    margin-bottom: 5px;
}
.page-content{
    flex: 1;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    overflow: auto;
    padding: 6px;
    background-color: #fff;
}
</style>
