<template>
  <div>
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :url="url"
      isAudit
      modelLayout="collapse"
      :pageData="pageData"
      @loadSuccess="handleLoadSuccess"
    />
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
      v-drag
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, ' 审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, ' 请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {httpAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'PurchaseBiddingAudit',
    mixins: [DetailMixin],
    components: {
        flowViewModal
    },
    data () {
        return {
            flowId: '',
            auditVisible: false,
            opinion: '',
            showRemote: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            currentRow: {},
            currentUrl: '',
            flowView: false,
            cancelAuditShow: false,
            currentBasePath: this.$variateConfig['domainURL'],
            previewContent: '',
            confirmLoading: false,
            previewModal: false,
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseBiddingItemList',
                        columns: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                        ref: 'biddingSupplierList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'), field: 'supplierCode', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 300 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'), field: 'replyStatus_dictText', width: 100},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'), field: 'replyTime', width: 140},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'), field: 'contacts', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'), field: 'phone', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_mail`, '邮件'), field: 'email', width: 150},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewBidsPermission`, '查看标书权限'), field: 'bidCheck_dictText', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidAuthority`, '投标权限'), field: 'bidQuote_dictText', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'), field: 'sourceType_dictText', width: 120}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_participants`, '参与人员'), groupCode: 'specialistInfo', type: 'grid', custom: {
                        ref: 'purchaseBiddingSpecialistList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), width: 120},
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), width: 120 },
                            { field: 'memberType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberType`, '成员类型'), width: 220 },
                            { field: 'memberRole_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberRole`, '成员角色'), width: 220 },
                            { field: 'specialistClasses_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expertType`, '专家类型'), width: 220 },
                            { field: 'mobileTelephone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话'), width: 220 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120 },
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 320 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 140 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                form: {},
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'), type: 'primary', click: this.auditPass, showCondition: this.showAuditBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'), type: '', click: this.auditReject, showCondition: this.showAuditBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'rollBack',  click: this.goBackAudit}
                ]
            },
            url: {
                detail: '/bidding/purchaseBiddingHead/queryById'
            }
        }
    },
    computed: {
        fileSrc () {
            const {
                templateNumber = '',
                templateVersion = '',
                templateAccount = '',
                busAccount = ''
            } = this.currentRow || {}
            let account = templateAccount || busAccount
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${account}/purchase_bidding_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleLoadSuccess (res) {
            this.currentRow = res.res.result
            this.showRemote = true
            this.flowId = this.currentRow.flowId
            this.currentEditRow.rootProcessInstanceId = this.currentRow.flowId
        },
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBackAudit()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = true
            })
        }
    }
}
</script>