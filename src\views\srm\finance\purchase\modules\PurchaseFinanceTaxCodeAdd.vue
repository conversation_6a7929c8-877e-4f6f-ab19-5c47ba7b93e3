<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :pageHeaderButtons="pageHeaderButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {
    BUTTON_SAVE,
    BUTTON_BACK
} from '@/utils/constant.js'
export default {
    mixins: [businessUtilMixin],
    name: 'PurchaseFinanceTaxCodeAdd',
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            requestData: {
                detail: { url: '/reconciliation/purchaseFinanceTaxCode/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/reconciliation/purchaseFinanceTaxCode/add'
                    },
                    click: this.save
                },
                BUTTON_BACK
            ]
        }
    },
    methods: {
        // handleAfterDealSource (pageConfig, resultData) {
        //     debugger
        //     console.log(pageConfig, resultData)
        //     let formModel = pageConfig.groups[0].formModel
        //     if(formModel.category == '1' && formModel.category ){
        //         // pageConfig.formFields[2].fieldType = 'select'
        //         formModel.goodsAndServicesNames = '10'
        //     }else{
        //         formModel.goodsAndServicesNames = '100'
        //     }
        // },
        save (args) {
            this.stepValidate(args).then(()=>{
                this.composeBusinessSave(args)
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '税收分类编码',
                        placeholder: '税收分类编码',
                        fieldName: 'taxCategoryCode',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: '类别',
                        placeholder: '类别',
                        dictCode: 'taxCodeType',
                        fieldName: 'category',
                        required: '1',
                        defaultValue: '0',
                        bindFunction: function bindFunction (_Vue, pageConfig, groupData, group, realValue){
                            group.formFields.forEach(item=>{
                                if(item.fieldName === 'goodsAndServicesNames') {
                                    if(realValue === '0' || realValue == '' || realValue == null) {
                                        item.fieldType = 'selectModal'
                                        group.formModel.goodsAndServicesNames = ''
                                    } else if (realValue === '1') {
                                        item.fieldType = 'select'
                                        item.dictCode = 'srmAcceptanceProject'
                                    } else  {
                                        item.fieldType = 'select'
                                        item.dictCode = 'srmAddCostType'
                                    }
                                }
                            })
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'selectModal',
                        fieldLabel: '商品和服务名称',
                        placeholder: '商品和服务名称',
                        fieldName: 'goodsAndServicesNames',
                        dictCode: 'srmAcceptanceProject',
                        required: '1',
                        function (Vue, {_data, _form}){
                            _form.cateName = _data[0].cateName
                        },
           	            bindFunction: function (Vue, data){
                            let form = Vue.group.formModel
                            Vue.$set(form, 'goodsAndServicesNames', data[0].cateName)
                            Vue.$set(form, 'cateCode', data[0].cateCode)
                        },
                        extend: {
                            modalColumns: [
                                {field: 'cateCode', title: '物料分类编码', with: 150},
                                {field: 'cateName', title: '物料分类名称', with: 150}
                            ],
                            modalUrl: '/material/purchaseMaterialCode/list', 
                            modalParams: {}
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '商品和服务分类简称',
                        placeholder: '商品和服务分类简称',
                        fieldName: 'abbreviation',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'remoteSelect',
                        fieldLabel: '税码',
                        placeholder: '税码',
                        fieldName: 'taxCode',

                        function (Vue, {_data, _form}){
                            _form.taxCode = _data[0].taxCode
                            _form.taxRate = _data[0].taxRate
                        },
           	            bindFunction: function (Vue, data){
                            let form = Vue.group.formModel
                            Vue.$set(form, 'taxCode', data[0].taxCode)
                            Vue.$set(form, 'taxRate', data[0].taxRate)
                        },
                        extend: {
                            modalColumns: [
                                {field: 'taxCode', title: '税码', with: 150},
                                {field: 'taxRate', title: '税率', with: 150}
                            ],
                            modalUrl: '/base/tax/queryTaxForTemplate', modalParams: {} }
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '税率',
                        fieldName: 'taxRate',
                        placeholder: '税率',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '征收率',
                        fieldName: 'collectionRate',
                        placeholder: '征收率'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        sortOrder: '5',
                        fieldLabel: '物料分类编码',
                        fieldName: 'cateCode',
                        placeholder: '物料分类编码'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        sortOrder: '5',
                        fieldLabel: '备注',
                        fieldName: 'remark',
                        placeholder: '备注'
                    }
                ]
            }
        }
    }
}
</script>