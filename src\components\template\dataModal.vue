<template>
  <a-spin :spinning="spinning">
    <a-modal 
      centered
        :title="modalTitle"
        :visible="showModal"
        :width="640"
        :maskClosable="false"
        @ok="confirm"
        @cancel="close">
        <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_JLBD_41c1d106`, '资料标题')}`"
            prop="knowledgeTitle">
            <a-input
            type="text"
            :disabled="editDisable"
            v-model="form.knowledgeTitle"
            :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_field_VWNJLBD_c8e992ef`, '请输入资料标题')}`"
            />
        </a-form-model-item>
        <!-- <a-form-model-item
            label="资料上传">
            <custom-upload></custom-upload>
        </a-form-model-item> -->
        <a-form-model-item
            v-if="form.id"
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_JLXV_41be61eb`, '资料上传')}`"
            prop="attachmentList"
        >
            <div class="dropbox">
            <a-upload-dragger
                name="file"
                :disabled="editDisable"
                :headers="tokenHeader"
                :fileList="form.attachmentList"
                :accept="fileAccept"
                :multiple="false"
                :data="{
                businessType: businessType,
                headId: form.id,
                sourceNumber:form.id,
                businessSourceType:'standard',
                actionRoutePath: '/srm/knowledge/PurchaseKnowledgeBaseList,/srm/knowledge/SaleKnowledgeBaseList'
                }"
                :action="url.fileUpload"
                @change="handleUploadChange"
                :beforeUpload="beforeUpload"
                :remove="handleRemove">
                <p class="ant-upload-drag-icon">
                <a-icon type="inbox"/>
                </p>
                <p class="ant-upload-text">
                {{ $srmI18n(`${$getLangAccount()}#i18n_title_clickOrDragToUploadAttachment`, '单击或拖动文件到此区域上传') }}
                </p>
                <p class="ant-upload-hint">
                {{ $srmI18n(`${$getLangAccount()}#i18n_title_oneOrMoreUpload`, '支持单次或批量上传') }}
                </p>
            </a-upload-dragger>
            <div class="custom-upload-max-limit">
                {{ $srmI18n(`${$getLangAccount()}#i18n_dict_dWiTXVjBIfXefL_58ea22b4`, '注:允许上传的附件大小最大为') }}{{ limitSize }}M
            </div>
            </div>
        </a-form-model-item>
        <a-form-model-item
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_JLAc_41c40a05`, '资料类型')}`"
            prop="knowledgeType">
            <a-select
            v-model="form.knowledgeType"
            allowClear
            :disabled="editDisable">
            <a-select-option
                v-for="item in knowledgeTypeList"
                :key="item.value"
                :value="item.value">
                {{ item.title }}
            </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_alert_hBu_14b7a59`, '发表到')}`"
            prop="knowledgeCatalogue">
            <m-tree-select
            allowClear
            :disabled="editDisable"
            v-model="form.knowledgeCatalogue"
            :sourceUrl="url.sourceUrl"
            :sourceMap="tree.sourceMap"
            :titleMap="tree.titleMap"
            :valueMap="tree.valueMap"
            :showEmptyNode="tree.showEmptyNode"
            @change="treeSelectChange"
            :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_alert_ViFIH_f757529e`, '请选择目录')}`"></m-tree-select>
        </a-form-model-item>
        <!--  数据字典  -->
        <a-form-model-item
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_JLwj_41c19360`, '资料来源')}`"
            prop="knowledgeSourceType">
            <a-select
            v-model="form.knowledgeSourceType"
            allowClear
            :disabled="editDisable">
            <a-select-option
                v-for="item in knowledgeSourceTypeList"
                :key="item.value"
                :value="item.value">
                {{ item.title }}
            </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item
            v-if="form.id"
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_alert_EP_11ef51`, '配图')}`"
            :disabled="editDisable"
            prop="knowledgeThumbnail">
            <m-upload
            :value.sync="form.knowledgeThumbnail"
            :disabled="editDisable"
            :accept="picAccept"
            :headers="tokenHeader"
            :multiple="false"
            :limit="1"
            :data="{ businessType: 'attachmentList', headId: form.id}"
            :beforeUpload="beforeImgUpload"
            >
            </m-upload>
            <div class="custom-upload-max-limit limit-line-h">
            {{ $srmI18n(`${$getLangAccount()}#i18n_alert_dWjUIOOJLABXKWYIuMWWWWWWWWWWW_99e67f2c`, '注:用于移动端资料列表展示,推荐尺寸300px*198px') }}
            </div>
        </a-form-model-item>
        <a-form-model-item
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_alert_JLWR_41c46270`, '资料署名')}`"
            prop="knowledgeDetail">
            <a-input
            :disabled="editDisable"
            type="text"
            v-model="form.knowledgeDetail"
            :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_alert_VWNJLWR_c8ec2459`, '请输入资料署名')}`"
            />
        </a-form-model-item>
        <a-form-model-item
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_alert_bWGR_31365b9d`, '权限设置')}`"
            prop="permissionSetting">
            <a-checkbox-group
            v-model="form.permissionSetting"
            @change="handlePermissionSetting"
            :disabled="editDisable">
            <a-checkbox
                v-for="(item,index) in permissionSettingList"
                :key="index"
                :value="item.value"
                :name="item.value">
                {{ item.text }}
            </a-checkbox>
            </a-checkbox-group>
        </a-form-model-item>
        <a-form-model-item
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_alert_CQbW_41c651bc`, '访问权限')}`"
            prop="visitorPerm">
            <a-select
            v-model="form.visitorPerm"
            allowClear
            @change="visitorPermReset"
            :disabled="editDisable">
            <a-select-option
                v-for="item in visitLimitList"
                :key="item.value"
                :value="item.value">
                {{ item.title }}
            </a-select-option>
            </a-select>
        </a-form-model-item>
        <a-form-model-item
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_alert_RICQbW_4852b9af`, '指定访问权限')}`"
            prop="visitorPermElsAccountString"
            v-show="selectAccount">
            <m-select-modal
            v-model="form.visitorPermElsAccountString"
            :config="modalConfig"
            :form="form"
            :disabled="editDisable"
            @afterClearCallBack="(cb)=> { handleSelectModalAfterClear(form, cb)}"
            @ok="(rows) => handleSelectModalAfterSelect(modalConfig, rows)"
            />
        </a-form-model-item>
        <!-- 是否审批 -->
        <a-form-model-item
            :label="`${$srmI18n(`${$getLangAccount()}#i18n_PurchaseMassProdHeadList_pubishAudit_dictText`, '是否审批')}`"
            prop="needAudit">
            <div style="display:flex;">
            <m-switch
                :configData="auditList"
                :disabled="editDisable"
                @change="auditChange"
                v-model="form.needAudit"/>
            <template v-if="form.id && form.auditStatus_dictText">
                <span style="padding-left:25%;">{{ $srmI18n(`${$getLangAccount()}#i18n_baseForm925b_auditStatus`, '审批状态') }}：{{ form.auditStatus_dictText }} </span>
            </template>
            </div>
        </a-form-model-item>
        </a-form-model>
        <!-- 按钮slot -->
        <template #footer>
        <a-button
            key="back"
            @click="handleCancel">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
            key="save"
            :loading="loading"
            @click="handleSave"
            v-if="!(form.auditStatus=='1'||form.auditStatus=='2'||form.status=='2')">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}
        </a-button>
        <a-button
            key="submit"
            type="primary"
            :loading="loading"
            @click="handlePublish"
            v-if="form.id && ((form.auditStatus=='2'&&form.needAudit==='1')
            ||(form.auditStatus=='4'&&form.needAudit!='1')) && form.status=='1'">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_publish`, '发布') }}
        </a-button>
        <a-button
            key="publish"
            type="primary"
            :loading="loading"
            @click="submitAudit"
            v-if="form.needAudit==='1'&&form.id && (form.auditStatus!='2'&&form.auditStatus!='1')">
            {{ $srmI18n(`${$getLangAccount()}#i18n_field_DJUz_2e91facc`, '提交审批') }}
        </a-button>
        </template>
        <field-select-modal></field-select-modal>
    </a-modal>
  </a-spin>
</template>
<script lang="jsx">
import CustomUpload from '@comp/template/CustomUpload'
import mTreeSelect from '@comp/treeSelect/mTreeSelect'
import mUpload from '@comp/mUpload'
import mSwitch from '@comp/mSwitch'
import mSelectModal from '@comp/mSelectModal'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {ajaxFindDictItems} from '@api/api'
import {getAction, postAction} from '@api/manage'
import {getLangAccount, srmI18n} from '../../utils/util'

export default {
    name: 'DataModal',
    model: {
        prop: 'visible',
        event: 'change'
    },
    props: {
        visible: {
            type: Boolean,
            default: true
        },
        modalTitle: {
            type: String,
            default: srmI18n(`${getLangAccount()}#i18n_alert_VaJL_2f97a643`, '新增资料')
        },
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        limitSize: {
            type: String,
            default: '200'
        },
        selectedKey: {
            type: String,
            default: ''
        }
    },
    components: {
        CustomUpload,
        mTreeSelect,
        mUpload,
        mSwitch,
        mSelectModal,
        fieldSelectModal
    },
    data () {
        return {
            spinning: false,
            showModal: this.visible,
            businessType: 'knowledgeBase',
            loading: false,
            editDisable: false,
            labelCol: {span: 6},
            wrapperCol: {span: 15},
            form: {
                attachmentList: [], //文件上传回显
                fileList: [], //文件上传
                knowledgeTitle: '', //资料标题
                knowledgeCatalogue: this.selectedKey, //发表到
                knowledgeLabel: '',
                knowledgeType: 'file', //资料类型
                knowledgeSourceType: '1', //资料来源
                knowledgeDetail: '', //资料署名
                knowledgeThumbnail: '', //配图
                permissionSetting: [], //权限设置
                visitorPerm: 'in_all', //访问权限
                visitorPerm_dictText: '',
                visitorPermElsAccountString: '', //指定访问权限
                visitorPermElsAccount: [], //指定访问权限Arr
                needAudit: '0', //是否审批
                allowComment: '0',
                allowDown: '0',
                auditStatus: '', //审批状态,
                auditStatus_dictText: ''
            },
            rules: {
                knowledgeTitle: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_JLBDlS_d89d98cc`, '资料标题必填!')
                }],
                knowledgeType: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_JLAclS_e0f58e0b`, '资料类型必填!')
                }],
                knowledgeCatalogue: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_hBulS_dc62395f`, '发表到必填!')
                }],
                knowledgeDetail: [{
                    required: false,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_JLWRulS_65bbbf06`, '资料署名到必填!')
                }],
                knowledgeSourceType: [{
                    required: false,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_JLwjlS_d7b62ca6`, '资料来源必填!')
                }],
                visitorPerm: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CQbWlS_e984c402`, '访问权限必填!')
                }],
                visitorPermElsAccount: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RICQbWlS_7e96fb35`, '指定访问权限必填!')
                }]
            },
            //附件上传配置
            tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            fileAccept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf, .rar, .zip, .avi, .mp4',
            picAccept: '.png, .jpg, .jpeg, .gif',
            selectAccount: false,
            permissionSettingList: [
                {value: 'allowComment', text: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_UV_117876`, '评论')},
                {value: 'allowDown', text: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载')}
            ],
            visitLimitList: [],
            knowledgeTypeList: [],
            knowledgeSourceTypeList: [],
            auditList: [],
            tree: {
                sourceMap: {},
                titleMap: 'title',
                valueMap: 'key',
                showEmptyNode: false
            },
            modalConfig: {},
            accountModalConfig: {
                extend: {
                    selectModel: 'multiple',
                    'modalColumns': [
                        {
                            'field': 'elsAccount',
                            'title': this.$srmI18n(`${this.$getLangAccount()}#i18n__WWWey_401851d`, 'ELS账号'),
                            'with': 150
                        },
                        {
                            'field': 'subAccount',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                            'with': 150
                        },
                        {
                            'field': 'realname',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                            'with': 150
                        }
                    ],
                    'modalUrl': '/account/elsSubAccount/list',
                    'modalParams': {status: '1'},
                    'beforeCheckedCallBack': function (Vue, row, column) {
                        return new Promise((resolve, reject) => {
                            return resolve('success')
                        })
                    }
                }
            },
            supplierModalConfig: {
                extend: {
                    selectModel: 'multiple',
                    modalColumns: [{
                        field: 'toElsAccount',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                        with: 150
                    }, {
                        field: 'supplierCode',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
                        with: 150
                    }, {
                        field: 'supplierName',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                        with: 150
                    }],
                    modalUrl: '/supplier/supplierMaster/list',
                    modalParams: {}
                }
            },
            url: {
                detail: '/knowledge/purchaseKnowledgeBase/queryById',
                add: '/knowledge/purchaseKnowledgeBase/add',
                edit: '/knowledge/purchaseKnowledgeBase/edit',
                submitAudit: '/elsUflo/audit/submit',
                publish: '/knowledge/purchaseKnowledgeBase/purchase-publish',
                fileUpload: '/els/attachment/purchaseAttachment/upload',
                sourceUrl: '/knowledge/purchaseKnowledgeCatalogue/loadKnowledgeCatalogueTree'
            }
        }
    },
    created () {
        this.initDictData()
    },
    computed: {
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow && this.currentEditRow.busAccount) {
                account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount
            }
            return account
        }
    },
    watch: {
        visible (bool) {
            this.showModal = bool
            if (bool) {
                this.resetData()
                this.initDictData()
            } else {
                this.$refs.form.resetFields()
            }
        },
        selectedKey (val = '') {
            this.form.knowledgeCatalogue = val
        }
    },
    methods: {
        resetData () {
            Object.assign(this.$data, this.$options.data.call(this))
        },
        // 数据初始化
        initDictData () {
            this.loadDictData()
            this.queryDetail()
        },
        //初始化-编辑详情
        queryDetail () {
            const  that = this
            if (this.currentEditRow?.id) {
                this.loading = true
                this.spinning = true
                getAction(this.url.detail, {id: this.currentEditRow.id}).then((res) => {
                    if (res && res.success) {
                        that.form = {...that.form, ...res.result}
                        if(that.form.auditStatus=='1' ||that.form.auditStatus=='2'||that.form.status=='2'){
                            that.editDisable = true
                        }
                        if(res.result.attachmentList && res.result.attachmentList.length){
                            res.result.attachmentList.map(item=>{
                                item.uid = item.id
                                item.name = item.fileName
                            })
                        }
                        that.handlePermissionSettingSet()
                        that.visitorPermChange()
                        console.log(this.form)
                    } else {
                        that.$message.error(res.message)
                    }
                }).finally(()=>{
                    that.loading = false
                    this.spinning = false
                })
            }
        },
        //初始化-字典
        loadDictData () {
            //根据字典Code, 初始化字典数组
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'knowledgeVisitorPerm'
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    this.visitLimitList = res.result
                }
            })
            postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'yn'
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    this.auditList = res.result
                }
            })
            postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'knowledgeSourceType'
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    this.knowledgeSourceTypeList = res.result
                }
            })
            postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'knowledgeType'
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    this.knowledgeTypeList = res.result
                }
            })
        },
        // 树切换
        treeSelectChange ( realValue, extra, oldVal, configData, realLabel){
            console.log( realValue, extra, oldVal, configData, realLabel)
            this.form.knowledgeLabel = realLabel && realLabel.length ? realLabel.toString() : ''
        },
        visitorPermReset () {
            this.form.visitorPermElsAccount = []
            this.form.visitorPermElsAccountString = ''
            this.visitorPermChange()
        },
        // 访问权限改动
        visitorPermChange () {
            // 以下两个值的时候
            if (this.form.visitorPerm === 'in_custom') {
                this.modalConfig = this.accountModalConfig
                this.selectAccount = true
            } else if (this.form.visitorPerm === 'out_custom') {
                this.modalConfig = this.supplierModalConfig
                this.selectAccount = true
            } else {
                this.modalConfig = {}
                this.selectAccount = false
            }
        },
        handleCancel () {
            this.close()
        },
        //提交
        handlePublish () {
            if (!this.form.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }
            if (this.form.needAudit=='1' && this.form.auditStatus!='2') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWeRUzSKhxW_c8bf94ba`, '请先通过审批后再发布！'))
                return
            }
            if (this.form.status=='2') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWeRUzSKhxW_c8bf94ba`, '资料已经发布，请不要重复操作！'))
                return
            }

            if (this.loading) return
            if (this.form.visitorPerm == 'out_custom') {
                if (!this.form.visitorPermElsAccountString) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPRIjRdXCQbW_582b4db0`, '请选择需要指定的供应商访问权限'))
                    return
                }
                this.form.visitorPermElsAccount = this.form.visitorPermElsAccountString.split(',')
            } else if (this.form.visitorPerm == 'in_custom') {
                if (!this.form.visitorPermElsAccountString) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPRIjjDCQbW_c5fe6f04`, '请选择需要指定的用户访问权限'))
                    return
                }
                this.form.visitorPermElsAccount = this.form.visitorPermElsAccountString.split(',')
            } else {
                this.form.visitorPermElsAccount = []
                this.form.visitorPermElsAccountString = ''
            }
            this.$refs.form.validate(valid => {
                if (valid) {
                    this.loading = true
                    let params = {...this.form}
                    postAction(this.url.publish, params)
                        .then(res => {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            this.$emit('success')
                        })
                        .finally(() => {
                            this.loading = false
                            this.handleCancel()
                        })
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },
        //数据处理
        handlePermissionSetting () {
            console.log(this.form)
            this.permissionSettingList.map(item => {
                if (this.form.permissionSetting.includes(item.value)) {
                    this.form[item.value] = '1'
                } else {
                    this.form[item.value] = '0'
                }
            })
        },
        //反向数据处理
        handlePermissionSettingSet () {
            this.form.permissionSetting = []
            this.permissionSettingList.map(item => {
                if (this.form[item.value] === '1') {
                    this.form.permissionSetting.push(item.value)
                }
            })
        },
        handleSave () {
            const that = this
            if (this.loading) return
            if (this.form.visitorPerm == 'out_custom') {
                if (!this.form.visitorPermElsAccountString) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPRIjRdXCQbW_582b4db0`, '请选择需要指定的供应商访问权限'))
                    return
                }
                this.form.visitorPermElsAccount = this.form.visitorPermElsAccountString.split(',')
            } else if (this.form.visitorPerm == 'in_custom') {
                if (!this.form.visitorPermElsAccountString) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPRIjjDCQbW_c5fe6f04`, '请选择需要指定的用户访问权限'))
                    return
                }
                this.form.visitorPermElsAccount = this.form.visitorPermElsAccountString.split(',')
            } else {
                this.form.visitorPermElsAccount = []
                this.form.visitorPermElsAccountString = ''
            }
            this.$refs.form.validate(valid => {
                if (valid) {
                    that.loading = true
                    //校验成功后提交数据在这里
                    console.log(that.form)
                    let url = that.form.id ? that.url.edit : that.url.add
                    let params = {...that.form}
                    postAction(url, params)
                        .then(res => {
                            const type = res.success ? 'success' : 'error'
                            if(res.success && res.result){
                                if(!this.currentEditRow.id){
                                    this.currentEditRow.id = res.result.id
                                }
                                that.queryDetail()
                            }
                            that.$message[type](res.message)
                        })
                        .finally(() => {
                            that.loading = false
                            this.$emit('success')
                            if(url==that.url.edit){
                                that.handleCancel()
                            }
                        })
                    console.log(that.tree)
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },
        // 提交审批
        submitAudit () {
            const data = this.form
            if (!data.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }
            if (this.loading) return
            if (this.form.visitorPerm == 'out_custom') {
                if (!this.form.visitorPermElsAccountString) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPRIjRdXCQbW_582b4db0`, '请选择需要指定的供应商访问权限'))
                    return
                }
                this.form.visitorPermElsAccount = this.form.visitorPermElsAccountString.split(',')
            } else if (this.form.visitorPerm == 'in_custom') {
                if (!this.form.visitorPermElsAccountString) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPRIjjDCQbW_c5fe6f04`, '请选择需要指定的用户访问权限'))
                    return
                }
                this.form.visitorPermElsAccount = this.form.visitorPermElsAccountString.split(',')
            } else {
                this.form.visitorPermElsAccount = []
                this.form.visitorPermElsAccountString = ''
            }
            this.$refs.form.validate(valid => {
                if (valid) {
                    this.loading = true
                    //校验成功后提交数据在这里
                    let params = {...this.form}
                    const param = {
                        businessId: data.id,
                        businessType: 'knowledgeBase',
                        auditSubject: `资料标题：${params.knowledgeTitle}`,
                        params: JSON.stringify(params)
                    }
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApprovalTips`, '是否确认提交审批'),
                        onOk: () => {
                            postAction(this.url.submitAudit, param).then(res => {
                                const type = res.success ? 'success' : 'error'
                                this.$message[type](res.message)
                                this.loading = false
                                if (res.success) {
                                    this.$emit('success')
                                    this.handleCancel()
                                }
                            }).finally(() => {
                                this.loading = false
                            })
                        }
                    })
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },
        auditChange (res) {
            if(res=='0'){
                this.form.auditStatus = '4'
            }
        },
        confirm () {
            this.$emit('confirm', this.form)
        },
        close () {
            this.$emit('change', false)
        },
        // selectModal 清除
        handleSelectModalAfterClear (formModel, cb) {
            this.form.visitorPermElsAccount = []
        },
        // selectModal 确认时
        handleSelectModalAfterSelect (item, data) {
            // 要区分当前选的是out_custom 还是in_custom
            if (this.form.visitorPerm === 'out_custom') {
                this.form.visitorPermElsAccountString = data.map((item) => {
                    return item.toElsAccount
                }).toString()
            } else if (this.form.visitorPerm === 'in_custom') {
                this.form.visitorPermElsAccountString = data.map((item) => {
                    return item.elsAccount + '_' + item.subAccount
                }).toString()
            } else {
                this.form.visitorPermElsAccountString = ''
            }
        },
        //上传前检测
        beforeUpload (file) {
            const limitSize = file.size / 1024 / 1024 > this.limitSize
            if (limitSize) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BRefQIWR_929fb07e`, '超过最大文件限制!'))
                return Promise.reject(false)
            }
            // if (this.form.attachmentList.length > 0) {
            //   this.$message.warning('仅支持上传单个文件!')
            //   return Promise.reject(false)
            // }
            if (!this.form.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return Promise.reject(false)
            }
            return Promise.resolve(true)
        },
        beforeImgUpload () {
            if (!this.form.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return Promise.reject(false)
            }
            return Promise.resolve(true)
        },
        //文件删除
        handleRemove (file) {
            let {attachmentList} = this.form
            const index = attachmentList.indexOf(file)
            const newFileList = attachmentList.slice()
            newFileList.splice(index, 1)
            this.form.attachmentList = newFileList
        },
        //附件上传
        handleUploadChange ({file, fileList, event}) {
            let attachmentList = [...fileList]
            attachmentList = attachmentList.map(file => {
                if (file.response && file.response.result) {
                    file = file.response.result
                    file.uid = file.id
                    file.name = file.fileName
                }
                return file
            })
            this.form.attachmentList = attachmentList
            if (file.status === 'done') {
                if (file.response.success) {
                    if (file.response.code === 201) {
                        let {message, result: {msg, fileUrl, fileName}} = file.response
                        // let href = this.$variateConfig['domainURL'] + fileUrl
                        let href = fileUrl
                        this.$warning({
                            title: message,
                            content: (
                                <div>
                                    <span>{msg}</span><br/>
                                    <span>
                                        {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_detailContent`, '具体详情请')}
                                        <a href={href} target="_blank" download={fileName}>
                                            {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '点击下载')}
                                        </a>
                                    </span>
                                </div>
                            )
                        })
                    } else {
                        let message = file.response.message || this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadSuccess`, '文件上传成功')
                        this.$message.success(message)
                        const filePath = file.response.result.absoluteFilePath || ''

                        console.log('filePath', filePath)
                        // this.$emit('update:value', filePath)
                    }
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(
          `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadError`, '文件上传失败')} : ${file.msg}`
                )
            }
        }
    }
}
</script>

<style lang="less" scoped>
.custom-upload-max-limit {
  color: rgba(0, 0, 0, 0.45);
  font-size: 13px;
}

.limit-line-h {
  line-height: 1.5;
}

.custom-upload-form {
  .collectionForm {
    .dropbox {
      height: 180px;
      line-height: 1.5;
    }
  }
}

.global-search {
  width: 100%;
}

.global-search.ant-select-auto-complete .ant-select-selection--single {
  margin-right: -46px;
}

.global-search.ant-select-auto-complete .ant-input-affix-wrapper .ant-input:not(:last-child) {
  padding-right: 62px;
}

.global-search.ant-select-auto-complete .ant-input-affix-wrapper .ant-input-suffix button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.global-search-item {
  display: flex;
}

.global-search-item-desc {
  flex: auto;
  text-overflow: ellipsis;
  overflow: hidden;
}

.global-search-item-count {
  flex: none;
}
</style>