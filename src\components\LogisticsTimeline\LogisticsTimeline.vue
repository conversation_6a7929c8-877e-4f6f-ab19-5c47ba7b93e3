<template>
  <a-modal
    v-drag    
    v-model="visible"
    title="物流信息"
    @cancel="handleCancel"
    @ok="handleOk">
    <a-timeline v-if="logisticsData && logisticsData.logisticsCompanyItemList && logisticsData.logisticsCompanyItemList.length">
      <a-timeline-item
        v-for="(trace, index) in logisticsData.logisticsCompanyItemList"
        :key="trace + index">
        <p>{{ trace.acceptStation }}</p>
        <p>{{ trace.acceptTime }}</p>
      </a-timeline-item>
    </a-timeline>
    <div v-else>{{ noneDataMsg }}</div>
  </a-modal>
</template>
<script>
export default {
    name: 'LogisticsTimeline',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        logisticsData: {
            type: Object,
            default: () => {}
        },
        noneDataMsg: {
            type: String,
            default: () => {
                return '暂无物流信息'
            }
        }
    },

    data () {
        return {
            timelineData: {
                LogisticCode: '210001633605',
                ShipperCode: 'ANE',
                Traces: [
                    {
                        acceptStation: '暂无信息',
                        acceptTime: new Date()
                    }
                ],
                State: '3',
                EBusinessID: '1708842',
                Success: true
            }
        }
    },
    computed: {
        visible: {
            get () {
                return this.show
            },
            set () {}
        }
    },
    methods: {
        handleOk (e) {
            this.$emit('logisticsHandleOk', e)
        },
        handleCancel (e) {
            this.$emit('logisticsHandleCancel', e)
        }
    }
}
</script>
