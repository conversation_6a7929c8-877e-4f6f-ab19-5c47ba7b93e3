<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      refresh
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"/>
    <!-- 行明细弹出选择框 -->
    <field-select-modal
      ref="fieldSelectModal"/>
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'
import {EditMixin} from '@comp/template/edit/EditMixin'

export default {
    name: 'SaleRefundsDeliveryHeadModal',
    components: {
        fieldSelectModal
    },
    mixins: [EditMixin],
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            selectType: 'material',
            flowId: 0,
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnLineInfo`, '退货行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleRefundsDeliveryItemList',
                            columns: [],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                                    type: 'primary',
                                    click: this.insertGridItem
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteGridItem
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                                    key: 'fillDown',
                                    type: 'tool-fill',
                                    beforeCheckedCallBack: this.fillDownGridItem
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleAttachmentList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 180
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'uploadSubAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                                    width: 120
                                }

                            ],
                            buttons: []
                        }
                    }

                ],
                formFields: [],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        type: 'primary',
                        click: this.saveEvent,
                        authorityCode: 'order#saleRefundsDeliveryHead:edit'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                        type: 'primary',
                        click: this.saleRefundDeliveryPublish,
                        authorityCode: 'order#saleRefundsDeliveryHead:publishs'
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                edit: '/delivery/saleRefundsDeliveryHead/edit',
                confrimRefunds: '/delivery/saleRefundsDeliveryHead/confrimRefunds',
                returnRefunds: '/delivery/saleRefundsDeliveryHead/returnRefunds',
                detail: '/delivery/saleRefundsDeliveryHead/queryById',
                upload: '/attachment/saleAttachment/upload'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let elsAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let templateType = this.currentEditRow.templateType
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_${templateType}_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    methods: {
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent () {
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        //新增行
        insertGridItem () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (params.toElsAccount == '') {
                this.$message.warning('请选择供应商！')
                return
            }
            if (params.purchaseOrg == '') {
                this.$message.warning('请选择采购组织！')
                return
            }
            if (params.elsAccount == params.toElsAccount) {
                if (params.toCompany == '') {
                    this.$message.warning('公司内部单据，请选择供应商公司代码')
                    return
                }
            }
            let parm = {
                toElsAccount: this.$ls.get('Login_elsAccount'),
                elsAccount: params.toElsAccount,
                reconciliation: '0',
                voucherStatus: '0',
                loanDirection: '+',
                purchaseOrg: params.purchaseOrg
            }
            let url = '/reconciliation/saleDeliveryWater/list'
            let columns = [
                {
                    field: 'relationId',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_relationId`, '关联id'),
                    width: 150
                },
                {
                    field: 'toElsAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEsNo`, '采购els号'),
                    width: 150
                },
                {
                    field: 'company_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_SaleMassProdHeadList_companyCode`, '公司代码'),
                    width: 150
                },
                {
                    field: 'purchaseOrg_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '采购组织'),
                    width: 80
                }, {
                    field: 'documentCategory_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                    width: 80
                },
                {
                    field: 'documentCategory_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '交付单据类别'),
                    width: 80
                },
                {
                    field: 'materialDesc',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                    width: 80
                },
                {
                    field: 'materialName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
                    width: 80
                },
                {
                    field: 'materialSpec',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),
                    width: 80
                },
                {
                    field: 'materialGroup',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                    width: 80
                },
                {
                    field: 'materialGroupName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroupName`, '物料组名称'),
                    width: 80
                },
                {
                    field: 'cateCode',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cateCode`, '物料分类'),
                    width: 80
                },
                {
                    field: 'cateName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cateName`, '物料分类名称'),
                    width: 80
                },
                {
                    field: 'requireDate',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_requiredDeliveryDate`, '要求交期'),
                    width: 100
                },
                {
                    field: 'quantity',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'),
                    width: 100
                },
                {
                    field: 'factory_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_factory`, '工厂'),
                    width: 100
                },
                {
                    field: 'storageLocation_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocation`, '库存地点'),
                    width: 100
                },
                {
                    field: 'voucherQuantity',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_factory`, '凭证数量'),
                    width: 100
                },
                {
                    field: 'refund',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qYSWR_e25aee35`, '可退货数量'),
                    width: 100
                }
            ]
            this.$refs.fieldSelectModal.open(url, parm, columns, 'multiple')
        },
        fieldSelectOk (data) {
            let itemGrid = this.$refs.editPage.$refs.saleRefundsDeliveryItemList[0]
            let {fullData} = itemGrid.getTableData()
            let materialList = fullData.map(item => {
                return item.sourceItemId
            })
            //过滤已有数据
            let insertData = data.filter(item => {
                return !materialList.includes(item.id)
            })
            let that = this
            postAction(that.url.voucherToRefundsItem, insertData).then(res => {
                if (res.success) {
                    itemGrid.insertAt(res.result, -1)
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        //删除复选框选定行
        deleteGridItem () {
            let itemGrid = this.$refs.editPage.$refs.saleRefundsDeliveryItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning('请选择数据！')
                return
            }
            itemGrid.removeCheckboxRow()
        },
        confrimRefunds () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (params.saleRefundsDeliveryItemList.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
                return false
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areYouSureHaveReceivedReturnOrder`, '确认退货单'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notifyPurchaserConfirmationTheReturnOrderWaitReturnSure`, '将退货单确认通知采购方等待退货, 是否确认?'),
                onOk: function () {
                    postAction(that.url.confrimRefunds, params).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.$refs.editPage.goBack()
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        returnRefunds () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (params.saleRefundsDeliveryItemList.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
                return false
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnOrder`, '退回退货单'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notifyPurchaserConfirmationTheReturnOrderWaitReturnBack`, '将采购方发送的退货单进行退回, 是否退回?'),
                onOk: function () {
                    postAction(that.url.returnRefunds, params).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.$refs.editPage.goBack()
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })

        },
        saveEvent () {
            this.$refs.editPage.postData()
        }
    }
}
</script>
