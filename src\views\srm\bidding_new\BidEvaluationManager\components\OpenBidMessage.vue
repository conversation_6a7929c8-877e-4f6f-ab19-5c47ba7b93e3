<template>
  <div style="height: 100%">
    <a-spin :spinning="confirmLoading">
      <setp-lay-out
        v-if="show"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :sourceGroups="sourceGroups"
        :pageStatus="pageStatus"
        :canchangeStep="canchangeStep"
        :pageHeaderButtons="pageHeaderButtons"
        @nextStepHandle="nextStepHandle"
        @preStepHandle="preStepHandle">
        <template #purchaseTenderProjectAttachmentInfoList="{ slotProps }">
          <!-- 下拉框：切换到不同供应商展示不同供应商的投标文件 -->
          <div>
            <a-select
              :default-value="firstItemName"
              style="width: 120px"
              @change="handleChange"
            >
              <a-select-option 
                v-for="item in supplierList"
                :key="item.supplierAccount"
                :value="item.supplierAccount">{{ item.supplierName }}</a-select-option>
            </a-select>
          </div>
          <!-- 根据下拉框选择的不同供应商展示不同供应商的投标文件 -->
          <attachmentInfoList
            ref="purchaseTenderProjectAttachmentInfoList"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :clarifyRow="currentEditRow"></attachmentInfoList>
        </template>
        <!-- 根据下拉框选择的不同供应商展示不同供应商的投标函 -->
        <template #tenderBidLetterFormatGroupVo="{ slotProps }">
          <tenderBidLetterFormatGroupVo
            ref="tenderBidLetterFormatGroupVo"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :clarifyRow="currentEditRow"></tenderBidLetterFormatGroupVo>
        </template>
      </setp-lay-out>
    </a-spin>
  </div>
</template>
  
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import tenderBidLetterFormatGroupVo from '../openBidComponents/tenderBidLetterFormatGroupVo'
import attachmentInfoList from '../openBidComponents/attachmentInfoList'
import setpLayOut from '@views/srm/bidding_new/BiddingHall/components/setpLayOut'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
  
export default {
    name: 'BiddingFile',
    components: {
        attachmentInfoList,
        setpLayOut,
        tenderBidLetterFormatGroupVo,
        titleTrtl
    },
    computed: {
        canchangeStep () {
            return this.pageStatus == 'detail'
        }
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            tenderBidLetterVoList: {},
            supplierList: [],
            supplierMsg: {},
            flowView: false,
            flowId: 0,
            pageStatus: 'detail',
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            show: false,
            rejectForm: {
                node: '',
                reject: ''
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.goBack
                }
            ],
            sourceGroups: [
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBQI_2e6335e1`, '投标文件'),
                    groupNameI18nKey: '',
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    groupType: 'item',
                    show: true,
                    sortOrder: '1'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxmK_9dbd9bbe`, '投标函格式'),
                    groupNameI18nKey: '',
                    groupCode: 'tenderBidLetterFormatGroupVo',
                    groupType: 'head',
                    show: true,
                    sortOrder: '2'
                }
            ],
            businessRefName: 'businessRef',
            currentGroupCode: {
                groupCode: 'tenderBidLetterFormatGroupVo'
            },
            status: '',
            confirmLoading: false,
            url: {
                detail: '/tender/purchase/supplierTenderProjectMasterInfo/querySupplierBySubpackage',
                fileDetail: '/tender/sale/supplierTenderDocumentSubmitInfo/querySaleSunmitInfoBySubpackageId'
            }
        }
    },
    methods: {
        goBack () {
            this.$parent.showOpenBid = false
        },
        // 下拉框切换不同供应商时触发事件
        async handleChange (supplierAccount) {
            let target = this.supplierList.filter(item=>{
                return item.supplierAccount == supplierAccount
            })
            let params = {
                subpackageId: target[0].subpackageId,
                supplierId: target[0].id
            }
            this.confirmLoading = false
            await getAction(this.url.fileDetail, params, {headers: {xNodeId: `${this.currentEditRow.checkType}_${this.currentEditRow.processType}_${this.currentEditRow.currentStep}`}}).then(res=>{
                if(res.success){
                    let supplierMsg = res.result ? res.result : {}
                    this.$refs.purchaseTenderProjectAttachmentInfoList.saleAttachmentDTOList=supplierMsg.saleAttachmentDTOList
                    this.$refs.tenderBidLetterFormatGroupVo.init({tenderBidLetterFormatGroupVo: supplierMsg.tenderBidLetterVoList})
                }else{
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
            
        },
        async getSupplier () {
            let params = {
                subpackageId: this.currentEditRow.subpackageId
            }
            this.confirmLoading = true
            await getAction(this.url.detail, params, {headers: {xNodeId: `${this.currentEditRow.checkType}_${this.currentEditRow.processType}_${this.currentEditRow.currentStep}`}}).then(res=>{
                if(res.success){
                    let supplierList = res.result ? res.result : []
                    this.$set(this, 'supplierList', supplierList)
                    this.firstItemName = this.supplierList ? this.supplierList[0].supplierName : ''
                    if(this.firstItemName) this.handleChange(supplierList[0].supplierAccount)
                    this.show = true
                }else{
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
            
        },
        nextStepHandle (data) {
            this.currentGroupCode = data.groupData
        },
        preStepHandle (data) {
            this.currentGroupCode = data.groupData
        }
    },
    created () {
        this.getSupplier()
    }
}
</script>
  