<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"/>
    <field-select-modal
      ref="fieldSelectModal"/>
    <!-- <a-modal
                v-drag
                  centered
                  :width="960"
                  :maskClosable="false"
                  :visible="flowView"
                  @ok="closeFlowView"
                  @cancel="closeFlowView">
                  <iframe
                    style="width:100%;height:560px"
                    title=""
                    :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
                    frameborder="0"></iframe>
                </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
      v-drag
      forceRender
      :visible="editRowModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')"
      :width="800"
      @ok="confirmEdit"
      @cancel="closeEditModal">
      <j-editor
        v-if="editRowModal"
        v-model="currentItemContent"></j-editor>
    </a-modal>
    <a-modal
      v-drag
      v-model="previewModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
    <view-item-diff-modal ref="viewDiffModal"/>
    <His-Contract-Item-Modal ref="hisContractItemModal"/>
    <select-Data-Modal
      ref="selectDataModal"
      @ok="selectDataOk"/>
  </div>
</template>

<script>

import {EditMixin} from '@comp/template/edit/EditMixin'
import selectDataModal from './selectDataModal'
import Sortable from 'sortablejs'
import ViewItemDiffModal from './ViewItemDiffModal'
import HisContractItemModal from './HisContractItemModal'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'
import JEditor from '@comp/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import {axios} from '@/utils/request'

export default {
    name: 'PurchaseContractHeadModal',
    mixins: [EditMixin],
    components: {
        flowViewModal,
        fieldSelectModal,
        selectDataModal,
        ViewItemDiffModal,
        HisContractItemModal,
        JEditor
    },
    data () {
        return {
            showHelpTip: false,
            editRowModal: false,
            previewModal: false,
            notShowTableSeq: true,
            editItemRow: {},
            currentItemContent: '',
            previewContent: '',
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            pageData: {
                form: {
                    contractNumber: '',
                    contractName: '',
                    contractDesc: '',
                    contractSignAddress: '',
                    contractSignDate: '',
                    currency: '',
                    contractTemplateNumber: '',
                    contractTemplateName: '',
                    contractTemplateVersion: '',
                    auditStatus: '0',
                    contractStatus: '1',
                    contractVersion: '1',
                    contractType: '1',
                    companyCode: '',
                    companyName: '',
                    purchaseOrgCode: '',
                    purchaseGroupCode: '',
                    supplierName: '',
                    toElsAccount: '',
                    supplierCode: '',
                    purchasePrincipal: '',
                    templateName: '',
                    templateId: '',
                    templateNumber: '',
                    templateVersion: '',
                    totalTaxAmount: ''
                },
                groups: [

                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLineInfo`, '采购合同行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseContractItemList',
                            columns: [],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                                    type: 'primary',
                                    click: this.insertGridItem
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteGridItem
                                }
                            ],
                            notShowTableSeq: true
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLibrary`, '采购合同条款库'),
                        groupCode: 'itemContentInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseContractContentItemList',
                            columns: [
                                {
                                    width: 60,
                                    slots: {
                                        default: () => {
                                            return [
                                                <span class="drag-btn" style="cursor:move">
                                                    <i class="vxe-icon--menu"></i>
                                                </span>
                                            ]
                                        },
                                        header: () => {
                                            return [
                                                <vxe-tooltip v-model={this.showHelpTip}
                                                    content="按住后可以上下拖动排序！" enterable>
                                                    <i class="vxe-icon--question" onClick={() => {
                                                        this.showHelpTip = !this.showHelpTip
                                                    }}></i>
                                                </vxe-tooltip>
                                            ]
                                        }
                                    }
                                },
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'itemId',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectNumber`, '项目编号'),
                                    width: 120
                                },
                                {
                                    field: 'itemName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                                    width: 120
                                },
                                {
                                    field: 'itemType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                                    width: 120
                                },
                                {
                                    field: 'itemVersion',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_dIrv_471c1a39`, '项目版本'),
                                    width: 120
                                },
                                {
                                    field: 'changeFlag',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeIdentification`, '变更标识'),
                                    width: 120,
                                    cellRender: {
                                        name: '$switch',
                                        type: 'visible',
                                        props: {closeValue: '0', openValue: '1', disabled: true}
                                    }
                                },
                                {
                                    field: 'sourceType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'),
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 150,
                                    align: 'left',
                                    slots: {
                                        default: ({row}) => {
                                            let resultArray = []
                                            resultArray.push(<a
                                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                                onClick={() => this.viewDetail(row)}>查看</a>)
                                            resultArray.push(<a style="margin-left:8px" title="编辑"
                                                onClick={() => this.editRow(row)}>修改</a>)
                                            if (row.changeFlag == '1') {
                                                resultArray.push(<a title="比对" style="margin-left:8px"
                                                    onClick={() => this.viewDiff(row)}>比对</a>)
                                            }
                                            return resultArray
                                        }
                                    }
                                }

                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                                    type: 'primary',
                                    click: this.addContentItemRow
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteContentGridItem
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachmentList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 180
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                    type: 'upload',
                                    businessType: 'contract',
                                    callBack: this.uploadCallBack
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    click: this.previewEvent
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteFilesEvent
                                }
                            ]
                        }
                    }

                ],
                formFields: [],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        type: 'primary',
                        click: this.saveEvent,
                        showCondition: this.showcEditConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_examineApprove`, '审批'),
                        type: 'primary',
                        click: this.submitAudit,
                        showCondition: this.showcEditConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_previewContractText`, '预览合同文本'),
                        type: 'primary',
                        click: this.previewPdf
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_versionChange`, '版本变更'),
                        type: 'primary',
                        click: this.saveEvent,
                        showCondition: this.showUpgradeConditionBtn
                    },
                    //{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_examineApprove`, '审批'), type: 'primary', click: this.submitAudit, showCondition: this.showcAuditConditionBtn},
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                        type: 'primary',
                        click: this.cancelAudit,
                        id: 'cancelAudit',
                        showCondition: this.showcCncelConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                        type: '',
                        click: this.showFlow,
                        id: 'showFlow',
                        showCondition: this.showFlowConditionBtn
                    },
                    //{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publishEvent },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                add: '/contract/purchaseContractHead/add',
                edit: '/contract/purchaseContractHead/edit',
                audit: '/a1bpmn/audit/api/submit',
                detail: '/contract/purchaseContractHead/queryById',
                //public: '/contract/purchaseContractHead/publish',
                upload: '/attachment/purchaseAttachment/upload',
                submitAudit: '/a1bpmn/audit/api/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_contract_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    created () {
        this.rowDrop()
    },
    beforeDestroy () {
        if (this.sortable) {
            this.sortable.destroy()
        }
    },
    methods: {
        previewEvent () {
            const fileGrid = this.$refs.editPage.$refs.attachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if (checkboxRecords.length > 1) {
                this.$message.warning('只能选择一条数据！')
                return
            }
            let preViewFile = checkboxRecords[0]
            this.$previewFile.open({params: preViewFile})
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        },
        preview () {
            let contentGrid = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.$refs.editPage.confirmLoading = true
            getAction('/contract/purchaseContractHead/getPreviewData', {id: this.currentEditRow.id}).then((res) => {
                if (res.success) {
                    this.previewModal = true
                    this.previewContent = res.result
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        previewPdf () {
            let contentGrid = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.confirmLoading = true
            axios({
                url: '/contract/purchaseContractHead/download',
                responseType: 'blob',
                params: {id: this.currentEditRow.id}
            }).then((res) => {
                if (res) {
                    debugger
                    let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                    window.open(url)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        //新增行
        insertGridItem () {
            let pageData = this.$refs.editPage ? this.$refs.editPage.form : {}
            if (!pageData.toElsAccount) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierFirst`, '请先选择供应商！'))
                return
            }
            let params = {toElsAccount: pageData.toElsAccount}
            this.$refs.selectDataModal.open(params)
        },
        selectDataOk (data) {
            let detailGrid = this.$refs.editPage.$refs.purchaseContractItemList[0]
            let tableData = detailGrid.getTableData().fullData
            let addTableData = []
            data.forEach((item, i) => {
                let lineNum = tableData.length + (i + 1)
                addTableData.push({
                    itemNumber: lineNum,
                    sourceType: item.sourceType,
                    sourceType_dictText: item.sourceType_dictText,
                    sourceNumber: item.sourceNumber,
                    sourceItemNumber: item.sourceItemNumber,
                    taxAmount: item.taxAmount,
                    netAmount: item.netAmount || 0,
                    currency: item.currency,
                    price: item.price,
                    purchaseUnit: item.purchaseUnit,
                    quantity: item.quantity,
                    materialSpec: item.materialSpec,
                    materialDesc: item.materialDesc,
                    taxCode: item.taxCode,
                    taxRate: item.taxRate,
                    materialNumber: item.materialNumber
                })
            })
            detailGrid.insertAt(addTableData, -1)
            this.calculateAmount()
        },
        calculateAmount () {
            let detailGrid = this.$refs.editPage.$refs.purchaseContractItemList[0].getTableData().tableData
            let totalTaxAmount = 0
            let totalNetAmount = 0
            detailGrid.forEach(item => {
                if (item.taxAmount) {
                    totalTaxAmount += Number(item.taxAmount)
                }
                if (item.netAmount) {
                    totalNetAmount += Number(item.netAmount)
                }
            })
            this.$refs.editPage.form.totalTaxAmount = totalTaxAmount
            this.$refs.editPage.form.totalNetAmount = totalNetAmount
        },
        //删除复选框选定行
        deleteGridItem () {
            let itemGrid = this.$refs.editPage.$refs.purchaseContractItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
            this.calculateAmount()
        },
        addContentItemRow () {
            let item = {
                selectModel: 'multiple',
                sourceUrl: '/contract/purchaseContractLibrary/list',
                params: {
                    order: 'desc',
                    column: 'id'
                },
                columns: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                        field: 'itemType_dictText',
                        width: 100
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                        field: 'itemName',
                        width: 250
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_dIrv_471c1a39`, '项目版本'),
                        field: 'itemVersion',
                        width: 100
                    }
                ]
            }
            this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        },
        fieldSelectOk (data) {
            let detailGrid = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
            let tableData = detailGrid.getTableData().fullData
            let addTableData = []
            data.forEach((item, i) => {
                let lineNum = tableData.length + (i + 1)
                addTableData.push({
                    itemNumber: lineNum,
                    itemName: item.itemName,
                    itemVersion: item.itemVersion,
                    itemType: item.itemType,
                    itemType_dictText: item.itemType_dictText,
                    itemContent: item.itemContent,
                    originalContent: item.itemContent,
                    itemId: item.id,
                    changeFlag: '0',
                    sourceType: '2',
                    sourceType_dictText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractLibrary`, '合同库')
                })
            })
            let conent = ''
            conent = data.map(item => {
                return item.itemContent
            })
            this.businessTemplate = conent.join('')
            detailGrid.insertAt(addTableData, -1)
        },
        addSelectModel (id) {
            getAction('/contract/purchaseContractTemplateHead/queryById', {id: id}).then(res => {
                if (res.success) {
                    let detailGrid = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
                    detailGrid.remove()
                    let tableData = detailGrid.getTableData().fullData

                    let addTableData = []
                    res.result.purchaseContractTemplateItemList.forEach((item, i) => {
                        let lineNum = tableData.length + (i + 1)
                        addTableData.push({
                            itemNumber: lineNum,
                            itemName: item.itemName,
                            itemVersion: item.itemVersion,
                            itemType: item.itemType,
                            itemType_dictText: item.itemType_dictText,
                            itemContent: item.itemContent,
                            originalContent: item.itemContent,
                            itemId: item.id,
                            changeFlag: '0',
                            sourceType: '1',
                            sourceType_dictText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTemplate`, '合同模板')
                        })
                    })
                    let conent = ''
                    conent = res.result.purchaseContractTemplateItemList.map(item => {
                        return item.itemContent
                    })
                    this.businessTemplate = conent.join('')
                    detailGrid.insertAt(addTableData, -1)
                }
            })
        },
        //删除复选框选定行
        deleteContentGridItem () {
            let itemGrid = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        editRow (row) {
            this.editItemRow = row
            this.currentItemContent = row.itemContent
            this.editRowModal = true
        },
        closeEditModal () {
            this.editRowModal = false
        },
        confirmEdit () {
            this.editItemRow.itemContent = this.currentItemContent
            let changeFlag = '0'
            if (this.editItemRow.itemContent != this.editItemRow.originalContent) {
                changeFlag = '1'
            }
            this.editItemRow.changeFlag = changeFlag
            this.editRowModal = false
        },
        rowDrop () {
            this.$nextTick(() => {
                let contractBuyContentItemList = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
                this.sortable = Sortable.create(contractBuyContentItemList.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
                    handle: '.drag-btn',
                    onEnd: ({newIndex, oldIndex}) => {
                        let {fullData} = contractBuyContentItemList.getTableData()
                        let tableData = [...fullData]
                        let currRow = tableData.splice(oldIndex, 1)[0]
                        tableData.splice(newIndex, 0, currRow)
                        tableData = tableData.map((item, index) => {
                            item.itemNumber = index + 1
                            return item
                        })
                        contractBuyContentItemList.loadData(tableData)
                        contractBuyContentItemList.syncData()
                    }
                })
            })
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent () {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        goBack () {
            this.$emit('hide')
        },
        saveEvent () {
            this.$refs.editPage.postData()
        },
        showcEditConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let contractStatus = params.contractStatus
            if (contractStatus == '1' || contractStatus == '5') {
                return true
            } else {
                return false
            }
        },
        showUpgradeConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let contractStatus = params.contractStatus
            if (contractStatus != '4') {
                return false
            } else {
                return true
            }
        },
        /*submitAudit () {
            const _this = this
            const fn = (data, vm) => {
                console.log('data :>> ', data)
                console.log('vm :>> ', vm) // 编辑模板组件实例
                const cb = () => {
                    const param = {
                        businessId: data.id,
                        rootProcessInstanceId: data.flowId || '',
                        businessType: 'contract',
                        auditSubject: `合同编号：${data.contractNumber}`,
                        ...data
                    }
                    postAction('/a1bpmn/audit/api/submit', param ).then(res =>  {
                        const type = res.success ? 'success' : 'error'
                        _this.$message[type](res.message)
                        _this.goBack()
                        //this.$parent.submitCallBack(data)
                    })
                }
                console.log('_this :>> ', _this)
                _this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                    content: '提交审批后将不能修改，是否确认提交审批?',
                    onOk () {
                        cb && cb()
                    },
                    onCancel () {
                        console.log('onCancel')
                    }
                })
            }
            this.$refs.editPage.handleSend('', fn)
        },*/
        submitAudit () {
            const _this = this
            let thisData = this.$refs.editPage.$refs.purchaseContractContentItemList[0].getTableData().fullData
            if (!thisData || thisData.length === 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTermLibraryInformationNeedsConfigured`, '需配置合同条款库信息!'))
                return
            }
            const fn = (data, vm) => {
                console.log('data :>> ', data)
                console.log('vm :>> ', vm) // 编辑模板组件实例
                const param = {
                    businessId: data.id,
                    rootProcessInstanceId: data.flowId || '',
                    businessType: 'contract',
                    auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractNoCode`, '合同编号')}：${data.contractNumber} ${data.contractName || ''}`,
                    params: JSON.stringify(data)
                }

                postAction('/a1bpmn/audit/api/submit', param).then(res => {
                    const type = res.success ? 'success' : 'error'
                    _this.$message[type](res.message)
                    //_this.goBack()
                    _this.$parent.submitCallBack(data)
                })

            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saveAndApprove`, '保存并审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterSavingCannotModifiedSureSaveSubmitForApproval`, '保存提交审批后将不能修改，是否确认保存并提交审批?'),
                onOk () {
                    _this.$refs.editPage.handleSend('', fn)
                },
                onCancel () {
                    console.log('onCancel')
                }
            })

        },
        showcAuditConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus != '0') {
                return false
            } else {
                return true
            }
        },
        auditPostData (invokeUrl) {
            this.$refs.editPage.confirmLoading = true
            let formData = this.$refs.editPage.form
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'contract'
            param['auditSubject'] = '合同编号：' + formData.contractNumber + ' ' + formData.contractName || ''
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.submitCallBack(formData)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        cancelAudit () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.init()
                    that.auditPostData(that.url.cancelAudit)
                }
            })
        },
        showcCncelConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus != '1') {
                return false
            } else {
                return true
            }
        },
        showFlowConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus == '0' || auditStatus == '3') {
                return false
            } else {
                return true
            }
        },
        showFlow () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            this.flowId = params.flowId
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        handleCancel () {
            this.visible = false
        }
    }
}
</script>