import { RouteView } from '@/components/layouts'

const offlineRouter = {
    path: '/projectHall/offline',
    name: 'project_offline',
    meta: {
        title: '评标',
        titleI18nKey: 'i18n_menu_WIUB_3ad3e0ef',
        icon: 'icon-111-01',
        keepAlive: false
    },
    component: RouteView,
    children: [
        {
            path: '/projectHall/offline/offlineEvaluation',
            name: 'project_offlineEvaluation',
            meta: {
                title: '线下评标',
                titleI18nKey: 'i18n_alert_WIUB_3ad3e0ef',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'offlineEvaluation' */ '@/views/srm/bidding_project/hall/purchase/offline/offlineEvaluation.vue')
        }
    ]
}

export default offlineRouter