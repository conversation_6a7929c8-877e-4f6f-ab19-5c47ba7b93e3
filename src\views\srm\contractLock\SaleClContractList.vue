<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showSingerEditPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleClContractEdit
      v-if="showSingerEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <SaleClFileEdit
      v-if="showEditPage"
      ref="fileEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleClContractDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleClContractEdit from './modules/SaleClContractEdit'
import SaleClContractDetail from './modules/SaleClContractDetail'
import SaleClFileEdit from './modules/SaleClFileEdit'
import { getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        SaleClContractEdit,
        SaleClContractDetail,
        SaleClFileEdit
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            showSingerEditPage: false,
            showEditPage: false,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'), icon: 'download', clickFn: this.batchDownload, authorityCode: 'contractLock#saleSignFlow:down'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: '(采方ELS号/名称/合同主题)'
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractLock#saleSignFlow:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LDPeL_2bb593a8`, '维护签章人'), clickFn: this.handleSingerEdit, allow: this.allowEdit, authorityCode: 'contractLock#saleSignFlow:signer'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQILD_ee657e73`, '签署文件维护'), clickFn: this.handleEdit, allow: this.allowSignUpload},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentDownload`, '流程文档下载'), clickFn: this.flowFileDownload, allow: this.showFlowFileDownload, authorityCode: 'contractLock#saleSignFlow:down'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/contractLock/elsClContract/saleList',
                flowFileDownload: '/contractLock/elsClContract/downloadArchive',
                columns: 'ElsClSignContract'
            }
        }
    },
    methods: {
        batchDownload (){
            let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords() || []
            if(selectedRows.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFTPIKjWF_7090178`, '请选择需要下载的数据'))
                return
            }
            selectedRows.forEach(row=>{
                const tips = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_warmPrompt`, '温馨提示')
                if(row.archiving !== '1'){
                    this.$notification.warn({
                        message: tips,
                        description: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_warmPrompt`, '业务编号:') + row.busNumber + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jtFQLLLAIKKm_9093393e`, '的单据流程未归档，下载失败')
                    })
                }else {
                    this.flowFileDownload(row)
                    this.$notification.success({
                        message: tips,
                        description: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_warmPrompt`, '业务编号:')  + row.busNumber + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IKLAQILR_18887541`, '下载归档文件成功')
                    })
                }

            })
        },
        allowEdit (row){
            //线上，签章人未维护或者供方签章人未维护
            if(row.sendBack == '1'){
                return true
            }
            if(row.onlineSealed == '1' &&row.saleSignStatus == '-1'){
                return false
            }
            return true
        },
        handleEdit (row) {
            this.showEditPage = true
            this.currentEditRow = row
        },
        allowSignUpload (row){
            //线下，文件未上传
            if(row.onlineSealed!=='1'  && row.sendBack!=='1' && row.saleSignStatus !=='1'){
                return false
            }
            return true
        },
        showFlowFileDownload (row){
            //流程已经发起
            if(row.launch==='1' && row.archiving==='1'){
                return false
            }
            return true
        },
        flowFileDownload (row){
            getAction(this.url.flowFileDownload, {id: row.id}).then(res => {
                if(res.success){
                    getAction(res.result.remoteFilePath, {}, {
                        responseType: 'blob'
                    }).then(ref => {
                        let url = window.URL.createObjectURL(new Blob([ref]))
                        let link = document.createElement('a')
                        link.style.display = 'none'
                        link.href = url
                        link.setAttribute('download', res.result.documentName)
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link) //下载完成移除元素
                        window.URL.revokeObjectURL(url) //释放掉blob对象
                    })
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showSingerEditPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleSingerEdit (row){
            this.showSingerEditPage = true
            this.currentEditRow = row
        }
    }
}
</script>
