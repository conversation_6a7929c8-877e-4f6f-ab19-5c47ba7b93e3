<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_taskName`, '任务标题')"
          required
          prop="taskTitle">
          <a-input
            v-model="form.taskTitle"
            clearable />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见')"
          prop="opinion">
          <a-textarea
            show-word-limit
            v-model="form.opinion"
            allowClear
            type="textarea"
            autoSize />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_VZL_13b8075`, '传阅人')"
          required
          prop="usersInfo">
          <a-tag
            v-for="(users, userIndex) in form.usersInfo"
            :key="users.id + userIndex"
            size="large"
            color="blue"
            closable
            @close="delCommonUsers(userIndex)"
          >{{ users.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectInformedUsers"></a-button>
          </div>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { informedTask } from '../../api/analy.js'

export default {
    mixins: [modalMixins],
    data () {
        return {
            rules: {
                taskTitle: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMLSBD_9062f13c`, '请填写任务标题'), trigger: 'change' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                opinion: [
                    // { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMqdWII_13f544fb`, '请填写备注/意见'), trigger: 'change' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                usersInfo: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFVZL_f30482de`, '请选择传阅人') } ]
            }
            // existData: [],
            // addSign: {}
        }
    },
    methods: {
        selectInformedUsers () {
            try {
                this.existData = this.form.usersInfo
            } catch (e) {
                this.existData = []
            }
            this.showUserSelectModal({ selectModel: 'multiple' })
        },
        fieldSelectOk (data) {
            this.$set(this.form, 'usersInfo', data)
            this.existData = data
        },
        delCommonUsers (index) {
            this.form.usersInfo.splice(index, 1)
            // this.addSign.usersInfo.splice(index, 1)
            // this.existData.splice(index, 1)
        },
        handleConfirm () {
            this.cheackValidate().then(() => {
                this.loading = true
                informedTask(this.form).then(res => {
                    this.loading = false
                    if (res.code == 0) {
                        this.$message.success(res.message)
                        this.$emit('success')
                    } else {
                        this.$message.error(res.msg)
                    }
                }).finally(() => {
                    this.loading = false
                    this.$emit('closeLoading')
                })
            })
        }
    },
    created () {}
}
</script>
