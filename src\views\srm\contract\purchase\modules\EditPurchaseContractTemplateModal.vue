<template>
  <div class="els-page-comtainer">
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      @goBack="goBack"
    />
    <a-modal
      v-drag
      forceRender
      :visible="editRowModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')"
      :width="1000"
      @ok="confirmEdit"
      @cancel="closeEditModal">
      <j-editor
        style="width:246mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc"
        v-if="editRowModal"
        v-model="currentItemContent"></j-editor>
    </a-modal>
    <a-modal
      v-drag
      v-model="previewModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <field-select-modal ref="fieldSelectModal"/>
    <view-item-diff-modal ref="viewDiffModal"/>
    <His-Contract-Item-Modal ref="hisContractItemModal"/>
  </div>
</template>

<script lang="jsx">
import {tileEditPageMixin} from '@comp/template/tileStyle/tileEditPageMixin'
import Sortable from 'sortablejs'
import JEditor from '@comp/els/JEditor'
import {httpAction} from '@/api/manage'
import ViewItemDiffModal from './ViewTemplateItemDiffModal'
import HisContractItemModal from './HisPurchaseContractTemplateItemModal'
import fieldSelectModal from '@comp/template/fieldSelectModal'

export default {
    name: 'EditContractTemplateModal',
    mixins: [tileEditPageMixin],
    components: {
        JEditor,
        ViewItemDiffModal,
        HisContractItemModal,
        fieldSelectModal
    },
    data () {
        return {
            showHelpTip: false,
            editRowModal: false,
            previewModal: false,
            sortableLine: null,
            editItemRow: {},
            currentItemContent: '',
            previewContent: '',
            saveType: null,
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editContractTemplate`, '编辑合同模板'),
                form: {
                    templateStatus: '1',
                    templateVersion: '1',
                    auditStatus: '0',
                    templateNumber: '',
                    contractTemplateNumber: '',
                    contractTemplateVersion: '',
                    contractTemplateName: '',
                    templateType: '',
                    company: '',
                    purchaseOrg: '',
                    purchaseGroup: '',
                    templateDesc: ''
                },
                validRules: {
                    contractTemplateName: [{
                        required: true,
                        message: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_required`, '必填')}!`
                    }],
                    templateType: [{
                        required: true,
                        message: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_required`, '必填')}!`
                    }]
                },
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            ref: 'baseForm',
                            colSpan: '12',
                            type: 'form',
                            list: [
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTemplateNo`, '合同模板编号'),
                                    fieldName: 'contractTemplateNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_systemAutoCreat`, '系统自动生成'),
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractTemplateVersion`, '合同模板版本'),
                                    fieldName: 'contractTemplateVersion',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_systemAutoCreat`, '系统自动生成'),
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractTemplateName`, '合同模板名称'),
                                    fieldName: 'contractTemplateName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterATemplateName`, '请输入模板名称'),
                                    required: '1'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTemplateType`, '合同模板类型'),
                                    fieldName: 'templateType',
                                    dictCode: 'srmContractTemplateType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectContractTemplateType`, '请选择模板类型'),
                                    required: '1'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTemplateDesc`, '合同模板描述'),
                                    fieldName: 'templateDesc',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterContractTemplateDesc`, '请输入模板描述')
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractApprovalStatus`, '合同审批状态'),
                                    fieldName: 'auditStatus',
                                    dictCode: 'srmAuditStatus',
                                    disabled: true
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司'),
                                    fieldName: 'company',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code=\'companyCode\' && status=\'1\''
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeadc98_purchaseOrgCode`, '采购组织'),
                                    fieldName: 'purchaseOrg',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="purchaseOrganization" && status="1"'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseGroupCode`, '采购组'),
                                    fieldName: 'purchaseGroup',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="purchaseGroup" && status="1"'
                                }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                        content: {
                            type: 'table',
                            ref: 'purchaseContractTemplateItemList',
                            columns: [
                                {
                                    width: 60,
                                    slots: {
                                        default: () => {
                                            return [
                                                <span class="drag-btn" style="cursor:move">
                                                    <i class="vxe-icon--menu"></i>
                                                </span>
                                            ]
                                        },
                                        header: () => {
                                            return [
                                                <vxe-tooltip v-model={this.showHelpTip}
                                                    content="按住后可以上下拖动排序！" enterable>
                                                    <i class="vxe-icon--question" onClick={() => {
                                                        this.showHelpTip = !this.showHelpTip
                                                    }}></i>
                                                </vxe-tooltip>
                                            ]
                                        }
                                    }
                                },
                                {
                                    type: 'checkbox',
                                    width: 40
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                                    field: 'itemNumber',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                                    field: 'itemType_dictText',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                                    field: 'itemName',
                                    align: 'left',
                                    width: 350
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_itemVersion`, '项目版本'),
                                    field: 'itemVersion',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isChangeFlag`, '是否更改'),
                                    field: 'changeFlag',
                                    width: 120,
                                    editRender: {
                                        name: 'mSwitch',
                                        type: 'visible',
                                        props: {closeValue: '0', openValue: '1', disabled: true}
                                    }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 100,
                                    align: 'left',
                                    slots: {
                                        default: ({row}) => {
                                            let resultArray = []
                                            resultArray.push(<a
                                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                                onClick={() => this.viewDetail(row)}>
                                                <a-icon type="profile"></a-icon>
                                            </a>)
                                            resultArray.push(<a
                                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_At_ff33b`, '编辑')}
                                                style="margin-left:8px" onClick={() => this.editRow(row)}>
                                                <a-icon type="edit"></a-icon>
                                            </a>)
                                            if (row.changeFlag == '1') {
                                                resultArray.push(<a
                                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_comparisons`, '比对')}
                                                    style="margin-left:8px" onClick={() => this.viewDiff(row)}>
                                                    <a-icon type="diff"></a-icon>
                                                </a>)
                                            }
                                            return resultArray
                                        }
                                    }
                                }
                            ],
                            toolbarButton: [
                                {
                                    type: 'primary',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                                    clickFn: this.addRow
                                },
                                {
                                    type: 'primary',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preview
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    clickFn: this.deleteRow
                                }
                            ]
                        }
                    }
                ],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        authorityCode: 'contractTemplate#purchaseContractTemplateHead:edit',
                        type: 'primary',
                        clickFn: this.saveEvent
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                        type: 'primary',
                        clickFn: this.submitAudit
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack}
                ]

            },
            url: {
                add: '/contract/purchaseContractTemplateHead/add',
                edit: '/contract/purchaseContractTemplateHead/edit',
                detail: '/contract/purchaseContractTemplateHead/queryById',
                submitAudit: '/a1bpmn/audit/api/submit'
            }
        }
    },
    created () {
        this.rowDrop()
    },
    beforeDestroy () {
        if (this.sortableLine) {
            this.sortableLine.destroy()
        }
    },
    methods: {
        addRow () {
            let item = {
                selectModel: 'multiple',
                sourceUrl: '/contract/purchaseContractLibrary/list',
                params: {
                    order: 'desc',
                    column: 'id'
                },
                columns: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                        field: 'itemType_dictText',
                        width: 100
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                        field: 'itemName',
                        width: 250
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_itemVersion`, '项目版本'),
                        field: 'itemVersion',
                        width: 100
                    }
                ]
            }
            this.$refs.editPage.currentSelectModal.selectCallBack = this.fieldSelectOk
            this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        },
        fieldSelectOk (data) {
            let detailGrid = this.$refs.editPage.$refs.purchaseContractTemplateItemList[0]
            let tableData = detailGrid.getTableData().fullData
            let addTableData = []
            data.forEach((item, i) => {
                let lineNum = tableData.length + (i + 1)
                addTableData.push({
                    itemNumber: lineNum,
                    itemName: item.itemName,
                    itemVersion: item.itemVersion,
                    itemType: item.itemType,
                    itemType_dictText: item.itemType_dictText,
                    itemContent: item.itemContent,
                    originalContent: item.itemContent,
                    itemId: item.id,
                    changeFlag: '0'
                })
            })
            detailGrid.insertAt(addTableData, -1)
        },
        deleteRow () {
            this.$refs.editPage.deleteRow()
        },
        preview () {
            this.previewModal = true
            let {fullData} = this.$refs.editPage.$refs.purchaseContractTemplateItemList[0].getTableData()
            let previewContent = fullData.map(item => {
                return item.itemContent
            })
            this.previewContent = previewContent.join('')
        },
        editRow (row) {
            this.editItemRow = row
            this.currentItemContent = row.itemContent
            this.editRowModal = true
        },
        closeEditModal () {
            this.editRowModal = false
        },
        confirmEdit () {
            this.editItemRow.itemContent = this.currentItemContent
            let changeFlag = '0'
            if (this.editItemRow.itemContent != this.editItemRow.originalContent) {
                changeFlag = '1'
            }
            this.editItemRow.changeFlag = changeFlag
            this.editRowModal = false
        },
        rowDrop () {
            this.$nextTick(() => {
                let contractTemplateItemList = this.$refs.editPage.$refs.purchaseContractTemplateItemList[0]
                this.sortableLine = Sortable.create(contractTemplateItemList.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
                    handle: '.drag-btn',
                    onEnd: ({newIndex, oldIndex}) => {
                        let {fullData} = contractTemplateItemList.getTableData()
                        let tableData = [...fullData]
                        let currRow = tableData.splice(oldIndex, 1)[0]
                        tableData.splice(newIndex, 0, currRow)
                        tableData = tableData.map((item, index) => {
                            item.itemNumber = index + 1
                            return item
                        })
                        contractTemplateItemList.loadData(tableData)
                        contractTemplateItemList.syncData()
                    }
                })
            })
        },
        submitAudit () {
            let formData = this.$refs.editPage.getParamsData()
            if (formData.contractTemplateNumber == '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }
            if (formData.contractTemplateName == '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseFillTemplateName`, '请填写模板名称！'))
                return
            }
            if (formData.templateType == '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectContractTemplateType`, '请选择模板类型！'))
                return
            }
            if (formData.purchaseContractTemplateItemList.length <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSucdIW_8572587c`, '请添加行项目！'))
                return
            }
            this.saveType = 'saveAndApprove' // 保存并审批
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saveAndApprove`, '保存并审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterSubmittingApprovalItcannotIsurSubmitApproval?`, '提交审批后将不能修改，是否确认提交审批?'),
                onOk: () => {
                    // 保存
                    this.$refs.editPage.saveEvent()
                }
            })
        },
        afterPostData () {
            if (this.saveType) {
                // 提交
                this.postData(this.url.submitAudit)
            }
        },

        postData (invokeUrl) {
            // this.$refs.editPage.confirmLoading = true
            let formData = this.$refs.editPage.getParamsData()
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'contractTemplate'
            param['auditSubject'] = '合同模板编号：' + formData.contractTemplateNumber + ' ' + formData.contractTemplateName || ''
            param['params'] = JSON.stringify(formData)
            httpAction(invokeUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.submitCallBack(formData)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                // this.$refs.editPage.confirmLoading = false
            })
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        }
    }
}
</script>
