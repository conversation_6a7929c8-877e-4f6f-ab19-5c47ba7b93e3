<template>
  <div class="MSelectModal">
    <div class="content">
      <slot :openFunc="openSelectModal">
        <a-input
          readOnly
          :disabled="config.disabled"
          :value="value"
          :placeholder="config.placeholder"
          @click="openSelectModal">
          <a-icon
            v-if="!config.disabled"
            slot="suffix"
            type="close-circle"
            @click="clearInputValue"></a-icon> 
        </a-input>
      </slot>
    </div>
    
    <field-select-modal
      isEmit
      :isTree="isTree"
      :requestMethod="requestMethod"
      :customType="config?.extend?.customType || {}"
      :treeConfig="treeConfig"
      :handleListData="handleListData"
      ref="fieldSelectModal"
      v-on="$listeners" />
    <grid-width-modal
      isEmit
      :isTree="isTree"
      :requestMethod="requestMethod"
      :treeConfig="treeConfig"
      :handleListData="handleListData"
      :row="row"
      ref="gridWidthModal"
      v-on="$listeners" />
  </div>
</template>

<script>
/**
 * MSelectModal
 * @description 弹窗选择组件
 * @property {Object} config 当前字段配置
 * @property {Object} pageData 父级所有数据
 * @property {Object} form 父级表头数据
 * @property {Boolean} isRow 是否为表行弹窗类型
 * @property {Object} row 表行配置，当前行数据
 * @property {Object} column 表行配置，当前列数据
 * @property {Boolean} config.extend.isTree 是否配置为表格树
 * @property {Object} config.extend.treeConfig 树节点配置项
 * @property {Object} config.extend.checkedConfig 弹出勾选置项
 * @property {Array} config.extend.modalConfigs 多套弹窗参数配置
 * @property {Boolean} isClearCallBindFunction 是否在点击取消按钮时调用bindFunction
 */

import fieldSelectModal from '@comp/template/fieldSelectModal'
import gridWidthModal from '@comp/template/gridWidthModal'

import {
    EXTEND,
    BEFORE_CHECK_CALLBACK,
    AFTER_CLEAR_CALLBACK,
    MODAL_PARAMS,
    SELECT_MODEL,
    MODAL_URL,
    MODAL_COLUMNS,
    PRIVATE_MODAL_PARAMS_FUNC,
    PRIVATE_BEFORE_CHECK_CALLBACK
} from '@/utils/constant.js'

import { getObjType, bindfunctionMiddleware } from '@/utils/util.js'

export default {
    name: 'MSelectModal',
    inject: {
        tplRootRef: {
            default: () => {}
        }
    },
    components: {
        fieldSelectModal,
        gridWidthModal
    },
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: String,
            default: ''
        },
        config: {
            type: Object,
            default () {
                return {}
            }
        },
        pageData: {
            type: Object,
            default () {
                return {}
            }
        },
        form: {
            type: Object,
            default () {
                return {}
            }
        },
        currentStep: {
            type: Number,
            default: 0
        },
        isRow: {
            type: Boolean,
            default: false
        },
        row: {
            type: Object,
            default () {
                return {}
            }
        },
        column: {
            type: Object,
            default () {
                return {}
            }
        },
        visible: {
            type: Boolean,
            default: false
        },
        isFromTileEditPage: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {}
    },
    computed: {
        // 配置下标，当弹窗配置为数组时
        current () {
            return this.config && this.config[EXTEND] && this.config[EXTEND].current || 0
        },
        // 处理list数据
        handleListData () {
            let rs = null
            if (this.config?.extend?.handleListData && typeof this.config.extend.handleListData === 'function') {
                rs = this.config.extend.handleListData
            }
            return rs
        },
        requestMethod () {
            return this.config?.extend?.requestMethod || 'get'
        },
        // 当前配置项
        curConfig () {
            let extend = (this.config && this.config[EXTEND]) || {}
            if (extend.modalConfigs && Array.isArray(extend.modalConfigs)) {
                return extend.modalConfigs[this.current]
            }
            return extend
        },
        isTree () {
            return this.curConfig && this.curConfig.isTree || false
        },
        treeConfig () {
            return this.curConfig && this.curConfig.treeConfig || {}
        }
    },
    watch: {
        visible (flag) {
            if (flag) {
                this.openSelectModal()
            }
        }
    },
    methods: {
        //打开选择弹窗
        openSelectModal () {
            if (!this.curConfig || !Object.keys(this.curConfig).length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_configurationParameter`, '请配置参数'))
                return
            }
            // 适配key值 modalParams
            let modalParams = this.curConfig[MODAL_PARAMS] || {}
            let beforeCheckedCallBack = this.curConfig[BEFORE_CHECK_CALLBACK] || (() => Promise.resolve())
            // 配置为一个方法时
            // 返回方法执行后的结果
            if (getObjType(modalParams) === 'function') {
                if (modalParams.name === PRIVATE_MODAL_PARAMS_FUNC) {
                    let params = {
                        _pageData: this.pageData,
                        _cacheAllData: this.tplRootRef.getAllData(),
                        _row: this.row,
                        _value: this.value,
                        _form: this.form
                    }
                    modalParams = bindfunctionMiddleware(this.tplRootRef, modalParams, params)
                } else {
                    modalParams = modalParams(this, this.form, this.row)
                }
            }

            // 兼容旧代码业务模板配置
            let selectModel = this.curConfig[SELECT_MODEL]
            if (getObjType(selectModel) === 'function') { // 动态配置selectModel参数
                selectModel = selectModel(this, this.form, this.row)
            }
            let modalUrl = this.curConfig[MODAL_URL]
            if (getObjType(modalUrl) === 'function') { // 动态配置modalUrl参数
                modalUrl = modalUrl(this, this.form, this.row)
            }
            let modalColumns = this.curConfig[MODAL_COLUMNS]
            if (getObjType(modalColumns) === 'function') { // 动态配置modalColumns参数
                modalColumns = modalColumns(this, this.form, this.row)
            }
            let checkedConfig = this.curConfig['checkedConfig'] || {}
            this.changeSelectModalValue(beforeCheckedCallBack).then(() => {
                if(this.curConfig.modalTypeGrid){
                    this.$refs.gridWidthModal.open(modalUrl, modalParams, modalColumns, selectModel, checkedConfig)
                }else{
                    this.$refs.fieldSelectModal.open(modalUrl, modalParams, modalColumns, selectModel, checkedConfig)
                } 
            }).catch((errTxt) => {
                const text = this.handleMultilingual(errTxt)
                this.$message.error(text)
                if (this.isRow) {
                    this.$emit('error', text)
                }
            })
        },
        // 处理国际化
        handleMultilingual (errorInfo) {
            if (errorInfo instanceof Object) {
                errorInfo = this.$srmI18n(`${this.$getLangAccount()}#${errorInfo.textI18nKey}`, errorInfo.text)
            }
            return errorInfo
        },
        clearInputValue () {
            let beforeCheckedCallBack = this.curConfig[BEFORE_CHECK_CALLBACK] || (() => Promise.resolve())
            let afterClearCallBack = this.curConfig[AFTER_CLEAR_CALLBACK] || ((f) => f)
            
            this.changeSelectModalValue(beforeCheckedCallBack).then(() => {
                this.$emit('change', '')
                this.$emit('afterClearCallBack', afterClearCallBack)
            }).catch((errTxt) => {
                this.$message.error(errTxt)
                if (this.isRow) {
                    this.$emit('error', errTxt)
                }
            })
        },
        // 统一表头、表行回调参数
        changeSelectModalValue (cb) {
            if (cb.name === PRIVATE_BEFORE_CHECK_CALLBACK) {
                let params = {
                    _pageData: this.pageData,
                    _cacheAllData: this.tplRootRef.getAllData(),
                    _value: this.value,
                    _row: {},
                    _form: {}
                }
                params[this.isRow ? '_row' : '_form'] = this.row
                return bindfunctionMiddleware(this.tplRootRef, cb, params)
            } else {
                if (this.isRow) {
                    return cb && cb(this, this.row, this.column, this.form)
                } else {
                    return cb && cb(this, this.pageData, this.groupData, this.form)
                }
            }
        }
    }
}
</script>
