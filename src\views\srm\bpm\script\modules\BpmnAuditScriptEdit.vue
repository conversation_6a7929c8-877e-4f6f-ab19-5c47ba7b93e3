<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :pageFooterButtons="pageFooterButtons"
        modelLayout="masterSlave"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>

      <a-modal
    v-drag    
        :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_JvRcyR_2a31a900`, '脚本执行结果')"
        v-model="showResult"
        @ok="scriptExcuteCofirm"
      >
        <p>{{ this.scriptResult }}</p>
      </a-modal>
      <field-select-modal
        ref="fieldSelectModal"
        isEmit
      />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@api/manage'

export default {
    name: 'BpmnAuditScriptEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            businessRefName: 'businessRef',
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            confirmLoading: false,
            showResult: false,
            scriptResult: '',
            refresh: true,
            requestData: {
                detail: { url: '/a1bpmn/audit/script/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    authorityCode: 'bpmn#auditScript:edit',
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save',
                    click: this.save
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_JviK_3c40c4bc`, '脚本测试'),
                    authorityCode: 'bpmn#auditScript:test',
                    attrs: {
                        type: 'primary'
                    },
                    key: 'scriptTest',
                    click: this.scriptTest
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                add: '/a1bpmn/audit/script/add',
                edit: '/a1bpmn/audit/script/edit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = 'TC2022073101'
            let templateVersion = '1'
            let account = '100000'
            return `${account}/purchase_bpmnScript_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        save () {
            const pageData = this.getAllData()
            let url = pageData.id ? this.url.edit : this.url.add
            if (pageData.id && pageData.enable == '1') {
                let that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLDK_38d74ae0`, '确认提示'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_JvIAjAPtkMddUzsntFWKQtTW_9827a2b2`, '脚本已启用当前操作会影响审批中的单据，是否继续？'),
                    onOk: () => {
                        that.confirmLoading = true
                        postAction(url, pageData).then(res => {
                            if(res.success) {
                                that.$message.success(res.message)
                                this.$refs.businessRef.queryDetail()
                            } else {
                                that.$message.error(res.message)
                            }
                        }).finally(() => {
                            that.confirmLoading = false
                        })
                    }
                })
            } else {
                this.confirmLoading = true
                postAction(url, pageData).then(res => {
                    if(res.success) {
                        this.$message.success(res.message)
                        this.currentEditRow.id = res.result ?  res.result.id : this.currentEditRow.id
                        this.$refs.businessRef.queryDetail()
                        // this.dealSource(this.currentEditRow)
                    } else {
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            }

        },
        scriptTest () {
            const pageData = this.getAllData()
            console.log(pageData)
            let requestParams = {
                script: pageData['scriptContext']
            }
            if (pageData.scriptParams) {
                let paramsJSON = JSON.parse(pageData.scriptParams)
                requestParams.params = paramsJSON
            }

            console.log(requestParams)

            this.confirmLoading = true
            postAction('/a1bpmn/audit/script/executeScript', requestParams).then(res => {
                if(res.success) {
                    this.showResult = true
                    this.scriptResult = res.result
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        scriptExcuteCofirm (){
            this.showResult = false
        }
    }
}
</script>