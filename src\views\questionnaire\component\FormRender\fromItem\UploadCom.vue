<template>
  <a-form-item
    :has-feedback="field.validateOption.icon"
    :validate-status="field.validateOption.status"
    :help="field.validateOption.message"
    :required="!!field.rules && field.rules.length > 0"
  >
    <span slot="label" >
      <!-- {{ formData }} -->
      {{ index +'.' + field.label }} 
      <a-button
        v-if="showDownloadBtn(field)"
        @click="downloadEventByView(field, 'title')">
        {{ $srmI18n(`${$getLangAccount()}#i18n_field_IKBI_25a71784`, '下载附件') }}
      </a-button>
    </span>
    <a-upload-dragger
      v-if="isPre != 2"
      :disabled="field.disabled"
      :placeholder="field.placeholder"
      v-decorator="[
        field.id,
        {
          initialValue: field.initialValue,
          getValueFromEvent: normFile,
          rules: [{ required: true, message: `${$srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '请上传附件')}` }]
        },
      ]"
      :before-upload="beforeUpload"
      @change="handleUploadChange" 
      v-bind="obj"
    >
      <p class="ant-upload-drag-icon">
        <a-icon type="inbox" />
      </p>
      <div >
        <p class="ant-upload-text">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_clickOrDragToUploadAttachment`, '单击或拖动文件到此区域上传') }}
        </p>
        <p class="ant-upload-hint">
          {{ $srmI18n(`${$getLangAccount()}#i18n_field_Rutm_2f7d019e`, '支持单次') }}
        </p>
      </div>
    </a-upload-dragger>
    <div v-if="isPre == 2">
      <div v-if="showAnswerDownloadBtn(field)">
        <a-button
          type="primary"
          @click="downloadEventByView(field, 'answer')">{{ $srmI18n(`${$getLangAccount()}#i18n_field_IKBI_25a71784`, '下载附件') }}</a-button>
      </div>
    </div>
    <div class="custom-upload-max-limit">{{ $srmI18n(`${$getLangAccount()}#i18n_dict_dWiTXVjBIfXefL_58ea22b4`, '注:允许上传的附件大小最大为') }}100M</div>
  </a-form-item>
</template>

<script lang='jsx'>
import {getAction} from '@/api/manage'
import { ACCEPT } from '@/utils/constant'
export default {
    name: 'UploadCom',
    props: {
        field: {
            type: Object,
            required: true
        },
        answerId: {
            type: String,
            default: ''
        },
        answerAllData: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        },
        isPre: {
            type: Number,
            default: 0
        },
        index: {
            type: Number,
            required: true
        },
        isDownload: {
            type: Number,
            default: 0
        }
    },
    inject: ['index', 'initData'],
    data () {
        return {
            obj: {
                action: '/els/attachment/purchaseAttachment/upload',
                accept: ACCEPT,
                headers: {'X-Access-Token': this.$ls.get('Access-Token')}, 
                itemInfo: [],
                data: {
                    businessType: 'survey',
                    headId: this.answerId || this.initData?.id
                },
                upload: [],
                fileName: []
            }
        }
    },
    created (){
        if(this.answerAllData.purchaseRfiAnswerLibraryList){
            let  optionlist =JSON.parse(JSON.stringify(this.answerAllData.purchaseRfiAnswerLibraryList))
            this.fileName=[]
            let arr=null
            for (let i = 0; i < optionlist.length; i++) {
                if(optionlist[i].itemType=='file'&&this.index==i+1){
                    arr=optionlist[i].purchaseRfiAnswerOptionList[0]
                    this.fileName.push({fileName: arr.fileName, id: arr.fbk2})
                }
            }
        }
    },
    methods: {
        showDownloadBtn (attr) {
            let rs = false
            if (attr?.sampleAttach?.length) {
                if (attr.sampleAttach[0]?.response?.success){
                    rs = true
                }
            }
            return rs
        },
        showAnswerDownloadBtn (attr) {
            let rs = false
            if (attr?.initialValue?.length) {
                if (attr.initialValue[0]?.response?.success){
                    rs = true
                }
            }
            return rs
        },
        downloadEventByView (attr, type) {
            const label = type == 'title' ? 'sampleAttach' : 'initialValue'
            const params = {id: attr[label][0]?.response?.result.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', attr[label][0]?.response?.result.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        downloadAnswerByView (attr) {
            const params = {id: attr.sampleAttach[0]?.response?.result.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', attr.sampleAttach[0]?.response?.result.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        normFile (e) {
            console.log('Upload event:', e)
            this.upload= e.fileList
            if (Array.isArray(e)) {
                return e
            }
            return e && e.fileList
        },
        beforeUpload (file) {
            let Size=100
            if (this.upload&&this.upload.length >= 1) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RiTXVImBI_8522573`, '只允许上传一个附件'))
                return window.Promise.reject(false)
            }
            //查看时，不予许上传附件
            if(this.isPre == 1 || this.isPre == 2){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xiTXVBI_52cd156c`, '不允许上传附件'))
                return window.Promise.reject(false)
            }
            if (Size) {
                const limitSize = file.size / 1024 / 1024 > Size
                if (limitSize) {
                    const tips = `${this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_iTXVjBIfXefL_d2cdd62`, '允许上传的附件大小最大为')}${Size}M`
                    this.$message.error(tips)
                    return window.Promise.reject(false)
                }
            }
            //  fileList = [ ...fileList, file ]
            //  this.form.setFieldsValue({
            //      fileList
            //  })
            // return window.Promise.reject(false)
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    this.$message.success(file.response.message || `${file.name} 文件上传成功`)
                } else {
                    if (file.response.code == '520') {
                        this.$message.error('未登录系统不允许上传附件，请先登录系统')
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        },
        downloadFile (item){
            if(!item.id){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            let id=item.id
            const params = {
                id
            }
            getAction('/attachment/saleAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', item.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }
    }
}
</script>

<style scoped>

</style>
