<template>
  <div style="background: rgb(255 255 255) ">
    <a-page-header
      v-if="false"
      style="border: 1px solid rgb(235, 237, 240); background:rgb(255 255 255) "
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_questionnaireDesign`, '问卷设计')"
      sub-title=""
      :backIcon="false"
      @back="() => null"
    />
  </div>
</template>
<script>
export default {
    name: 'HFormHeader'
}
</script>

<style
  lang="less"
  scoped>

</style>