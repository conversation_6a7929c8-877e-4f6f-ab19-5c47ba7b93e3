<template>
  <div>
    <div class="NavigationBar">
      <ul>
        <a-tooltip
          placement="left"
          v-for="(el, i) in menuList"
          :key="i">
          <template slot="title">
            <span>{{ el.title }}</span>
          </template>
          <li
            class="navigation-item"
            @click="el.btn && el.fn()"
          >
            <router-link
              v-if="el.path"
              :to="{path: el.path}"><a-icon
                :type="el.icon"
                :style="{ fontSize: '25px', color: '#fff'}" /></router-link>
            <a-icon
              v-else
              :type="el.icon"
              :style="{ fontSize: '25px', color: '#fff'}" />
          </li>
        </a-tooltip>
        
      </ul>
    </div>
    <router-view />
  </div>
</template>

<script>

export default {
    name: 'MallLayout',
    data () {
        return {
            menuList: [
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_backToTop`, '回到顶部'), path: null, btn: true, icon: 'up', fn: this.scrollsTop},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_backToHome`, '首页'), path: '/mall/productList', icon: 'shop'},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_myFav`, '我的收藏'), path: '/mall/myFav', query: {dataSource: 'SRM'}, icon: 'heart'},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_myPurchaseOrder`, '我的申购单'), path: '/srm/demand/PurchaseRequestHeadList?sourceType=mall', icon: 'file-done'},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shoppingCart`, '购物车'), path: '/mall/cart', icon: 'shopping-cart'},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparison`, '比价'), path: '/mall/comparisonDetail', icon: 'transaction'}
            ] 
        }
    },
    methods: {
        scrollsTop ( behavior = 'smooth' ) {
            if (window) {
                try {
                    // The New API.
                    window.scroll({
                        behavior, // 'smooth' Or 'auto'
                        top: 0,
                        left: 0
                    })
                } catch (e) {
                    // For older brolser
                    window.scrollTo(0, 0)
                }
            }
        }
    }
}
</script>

<style scoped lang="less">
.NavigationBar{
    position: fixed;
    width: 47px;
    top: 50%;
    right: 10px;
    z-index: 30;
    transform: translateY(-60%);
    .navigation-item{
        text-align: center;
        background: #595959;
        width: 47px;
        height: 47px;
        margin-top: 2px;
        padding-top: 10px;
        cursor: pointer;
    }
}
</style>