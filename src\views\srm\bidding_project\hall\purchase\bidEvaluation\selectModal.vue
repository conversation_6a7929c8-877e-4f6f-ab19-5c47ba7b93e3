<template>
  <div>
    <a-modal
    v-drag    
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectScore`, '选择分值')"
      :width="600"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="close">
      <vxe-grid
        border
        resizable
        show-overflow
        highlight-hover-row
        max-height="350"
        row-id="id"
        size="small"
        :loading="loading"
        ref="selectGrid"
        :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
        :data="tableData"
        :radio-config="selectModel === 'single' ? checkedConfig : undefined"
        :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
        :columns="columns"
      >
      </vxe-grid>
    </a-modal>
  </div>
</template>

<script>
export default {
    name: 'FieldSelectModal',
    props: {
        pageConfigData: {
            type: Object,
            default () {
                return {}
            }
        },
        tableData: {
            type: Array,
            default: ()=>[]
        }
    },
    data () {
        return {
            visible: false,
            loading: false,
            confirmLoading: false,
            columns: [],
            url: '',
            selectModel: 'single',
            checkedConfig: {highlight: true, reserve: true, trigger: 'row'},
            queryParams: {},
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                align: 'left',
                pageSizes: [10, 20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            }
        }
    },
    methods: {
        open (columns, selectModel, checkedConfig) {
            let tableColumns = columns ? [...columns] : []
            checkedConfig ? this.checkedConfig = {...this.checkedConfig, ...checkedConfig} : ''
            if (selectModel) {
                this.selectModel = selectModel
            }
            tableColumns.unshift({ type: 'seq',  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 })
            if (this.selectModel === 'single') {
                tableColumns.unshift({ type: 'radio', width: 40 })
            }else if (this.selectModel === 'multiple') {
                tableColumns.unshift({ type: 'checkbox', width: 40 })
            }
            this.columns = tableColumns
            this.visible = true
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            let selectedData = this.$refs.selectGrid.getCheckboxRecords() // 当前页已选择数据
            if (this.selectModel === 'single') {
                selectedData = this.$refs.selectGrid.getRadioRecord() ? [this.$refs.selectGrid.getRadioRecord()] : []
            }
            if (selectedData.length) {
                this.visible = false
                if (this.pageConfigData.itemColumns) { // 表行
                    selectedData.forEach(item => {
                        this.pageConfigData.itemColumns.forEach(el => {
                            if (el.defaultValue && (item[el.field] == '' || item[el.field] == null)) { // 模板有默认值且当前表单返回没有值
                                item[el.field] = el.defaultValue
                            }   
                        })
                    })
                }
                this.$emit('ok', selectedData)
            }else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectScore`, '请选择分值'))
            }
        }
    }
}
</script>