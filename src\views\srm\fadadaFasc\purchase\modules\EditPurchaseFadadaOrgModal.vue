<template>
  <div class="PurchaseFadadaOrg business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="edit"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">


import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {  BUTTON_SAVE, BUTTON_BACK } from '@/utils/constant.js'
// import REGEXP from '@/utils/regexp'
import { postAction } from '@/api/manage'

export default {
    name: 'EditPurchaseFadadaOrgModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            businessRefName: 'businessRef',
            requestData: {
                detail: { url: '/electronsign/fadada/purchaseFadadaOrg/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/electronsign/fadada/purchaseFadadaOrg/edit'
                    },
                    authorityCode: 'fadada#purchaseFadadaOrg:edit'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, '认证授权提交'),
                    key: 'save',
                    attrs: {
                        type: 'primary'
                    },
                    args: {
                        url: '/electronsign/fadada/purchaseFadadaOrg/auth'
                    },
                    click: this.handleCustomPublish,
                    showCondition: this.showButton,
                    authorityCode: 'fadada#purchaseFadadaOrg:auth'
                },
                BUTTON_BACK
            ],
            url: {
                save: '/electronsign/fadada/purchaseFadadaOrg/edit',
                detail: '/electronsign/fadada/purchaseFadadaOrg/queryById',
                auth: '/electronsign/fadada/purchaseFadadaOrg/auth'
            }
        }
    },
    methods: {
        showButton () {
            const params = this.getAllData()
            //未保存单据，按钮隐藏
            if(!params.id || params.id ==''){
                return false
            }
            return true
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQuKVRtRAB_8bd82932`, '是否加载组织机构列表'),
                        fieldLabelI18nKey: '',
                        fieldName: 'loadingOrg',
                        dictCode: 'yn',
                        defaultValue: '1',
                        required: '1',
                        bindFunction: function (parentRef, pageData, groupData, obj, value) {
                            let index = -1
                            let title
                            let rule = {}
                            let setDisabledByProp = (prop, flag) => {
                                for (let group of groupData.groups) {
                                    if (group.type) continue
                                    let formFields = group.formFields
                                    for (let sub of formFields) {
                                        if (sub.fieldName === prop) {
                                            sub.disabled = flag
                                            break
                                        }
                                    }
                                }
                            }
                            let setValidateRuleByProp = (prop, flag) => {
                                for (let group of groupData.groups) {
                                    if (group.type) continue
                                    let formFields = group.formFields
                                    for (let sub of formFields) {
                                        if (sub.fieldName === prop) {
                                            sub.required = flag ? '1' : '0'
                                            title = sub.fieldLabel
                                            index = groupData.groups.indexOf(group);
                                            break
                                        }
                                    }
                                }
                                rule[prop] = [{
                                    required: flag,
                                    message: `${title}必填`
                                }]
                                if (index != -1) {
                                    groupData.groups[index].validateRules = Object.assign({}, groupData.groups[index].validateRules, rule)
                                }
                            }
                            setValidateRuleByProp('corpName', value !== '1')
                            setValidateRuleByProp('orgCode', value === '1')
                            console.log(value)
                            if(value){
                                let flag = (value === '0')
                                setDisabledByProp('orgCode', flag)
                                setDisabledByProp('corpName', !flag)
                            }else{
                                setDisabledByProp('orgCode', true)
                                setDisabledByProp('corpName', true)
                            }
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpName',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRoo_307b3288`, '机构代码'),
                        fieldLabelI18nKey: '',
                        fieldName: 'orgCode',
                        dictCode: 'purchase_organization_info,org_name,org_code,org_category_code="companyCode"',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQlbUzVHvAPdj_f982edd5`, '是否授权身份信息给当前应用'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authorizeUserInfo',
                        dictCode: 'yn'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIAc_e6376152`, '组织机构证件类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'orgCardType',
                        dictCode: 'orgIdCardType',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIy_76cf8d5`, '组织机构证件号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpIdentNo',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEVRAc_58b0c8ec`, '企业组织类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpIdentType',
                        required: '1',
                        dictCode: 'fadadaCorpIdentType'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hLcR_32aba3df`, '法人姓名'),
                        fieldLabelI18nKey: '',
                        fieldName: 'legalRepName',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '设置页面中默认选择的实名认证方式'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpIdentMethod',
                        dictCode: 'fadadaCorpIdentMethod'
                    },
                    // {
                    //     groupCode: 'baseForm',
                    //     fieldType: 'multiple',
                    //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, '不可修改的企业信息',
                    //     fieldLabelI18nKey: '',
                    //     fieldName: 'corpNonEditableInfo',
                    //     dictCode: 'fadadaCorpNonEditableInfo',
                    //     required: '1'     
                    // },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'multiple',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESVVjAElbvLAB_dbd4e688`, '业务请求的企业授权范围列表'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authScopes',
                        dictCode: 'fadadaCropAuthScopes',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLey_21bdb3bc`, '经办人账号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'accountName',
                        required: '1',
                        initFunction: 
                        function initFunction (Vue, { _pageData, _form, _row, _value, _cacheAllData, _data }) {
                            let { openUserId='', clientUserId = '', accountName = '', userName = '', userIdentType = '', userIdentNo = '', mobile = '', bankAccountNo = '', oprIdentMethod = '', oprNonEditableInfo = '' } = _data[0] || {}
                            _form.clientUserId = clientUserId
                            _form.openUserId = openUserId
                            _form.accountName = accountName
                            _form.mobile = mobile
                            _form.userName = userName
                            _form.clientUserId = clientUserId
                            _form.userIdentType = userIdentType
                            _form.userIdentNo = userIdentNo
                            _form.bankAccountNo = bankAccountNo
                            _form.oprIdentMethod = oprIdentMethod
                            _form.oprNonEditableInfo = oprNonEditableInfo
                        },
                        bindFunction: function bindFunction (Vue, data) {
                            if (Vue.pageConfig) {
                                let formModel = Vue.pageConfig.groups[0].formModel
                                formModel.clientUserId = data[0].clientUserId
                                formModel.accountName = data[0].accountName
                                formModel.mobile = data[0].mobile
                                formModel.userIdentNo = data[0].userIdentNo
                                formModel.userName = data[0].userName
                                formModel.userIdentType = data[0].userIdentType
                                formModel.bankAccountNo = data[0].bankAccountNo
                                formModel.oprIdentMethod = data[0].identMethod
                                formModel.oprNonEditableInfo = data[0].nonEditableInfo
                                formModel.openUserId = data[0].openUserId
                            }
                        },
                        extend: {
                            modalColumns: [
                                {field: 'accountName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLey_21bdb3bc`, '经办人账号'), with: 150},
                                {field: 'userName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLcR_21b77cc5`, '经办人姓名'), with: 150}
                            ], modalUrl: '/electronsign/fadada/purchaseFadadaPersonal/listMember', modalParams: {}
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLSRMID_945a535e`, '经办人SRMID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'clientUserId',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLhffID_b71bb445`, '经办人法大大ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'openUserId',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLcR_21b77cc5`, '经办人姓名'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userName',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIAc_a87a0df0`, '经办人证件类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userIdentType',
                        dictCode: 'fadadaUserIdentType',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIy_15f34077`, '经办人证件号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userIdentNo',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLlty_155a8cbd`, '经办人手机号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'mobile',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLWEmy_ad7bbab7`, '经办人银行卡号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'bankAccountNo',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'multiple',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ELsqiFjmLLiCK_15d6ca58`, '页面中可选择的个人认证方式'),
                        fieldLabelI18nKey: '',
                        fieldName: 'oprIdentMethod',
                        dictCode: 'fadadaUserIdentMethod',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'multiple',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xqcrjOrLVH_6fb6d522`, '不可修改的经办人信息'),
                        fieldLabelI18nKey: '',
                        fieldName: 'oprNonEditableInfo',
                        dictCode: 'fadadaUserNonEditableInfo',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRKRLizE_43405a21`, '机构实名认证状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpIdentProcessStatus',
                        dictCode: 'fadadaIdentStatus',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbyR_2ed1f524`, '授权结果'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authResult',
                        dictCode: 'fadadaAuthStatus',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KRLizE_38a0c217`, '实名认证状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'realnameStatus',
                        dictCode: 'fadadaIdentStatus',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tReyID_1a3ee6f6`, '机构账号ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'clientCorpId',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hfftReyID_8b97abab`, '法大大机构账号ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'openCorpId',
                        disabled: true
                    }
                ]
            }
        },
        handleCustomPublish (args){
            // 获取页面所有数据
            const allData = this.getAllData() || {}
            // 这里可以添加自定义校验逻辑
            if (!allData.id || allData.id=='') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WsM_13b2663`, '先保存'))
                return
            }

            // 默认保存方法不会校验当前业务模板必填字段
            // 此处手动触发校验方法
            this.stepValidate(args).then(() => {
                this.$refs[this.businessRefName].confirmLoading = true
                postAction(this.url.auth, allData).then(res => {
                    this.$refs[this.businessRefName].confirmLoading = false
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.$emit('handleChidCallback', allData)
                    }
                })

            })
        }
    }
}
</script>
