<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :pageHeaderButtons="pageHeaderButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="detail"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {
    BUTTON_BACK
} from '@/utils/constant.js'
export default {
    mixins: [businessUtilMixin],
    name: 'PurchaseFinancetaxCodeDetail',
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            requestData: {
                detail: { url: '/reconciliation/purchaseFinanceTaxCode/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                BUTTON_BACK
            ]
        }
    },
    methods: {
        handleAfterDealSource (pageConfig) {
            pageConfig.groups[0].formModel.exchangeUse = '0'
            pageConfig.groups[0].formModel.exchangeSource = '0'
        },
        save (args) {
            this.stepValidate(args).then(()=>{
                this.composeBusinessSave(args)
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '税收分类编码',
                        placeholder: '税收分类编码',
                        fieldName: 'taxCategoryCode'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: '类别',
                        placeholder: '类别',
                        fieldName: 'category',
                        dictCode: 'taxCodeType'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '商品和服务名称',
                        placeholder: '商品和服务名称',
                        fieldName: 'goodsAndServicesNames'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '商品和服务分类简称',
                        placeholder: '商品和服务分类简称',
                        fieldName: 'abbreviation'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '税码',
                        placeholder: '税码',
                        fieldName: 'taxCode'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '税率',
                        fieldName: 'taxRate',
                        placeholder: '税率'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '征收率',
                        fieldName: 'collectionRate',
                        placeholder: '征收率'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        sortOrder: '5',
                        fieldLabel: '物料分类编码',
                        fieldName: 'cateCode',
                        placeholder: '物料分类编码'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        sortOrder: '5',
                        fieldLabel: '备注',
                        fieldName: 'remark',
                        placeholder: '备注'
                    }
                ]
            }
        }
    }
}
</script>