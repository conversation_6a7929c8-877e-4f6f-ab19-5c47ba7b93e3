<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <content-header
          v-if="showHeader"
          :btns="btns" />
        <div class="page-content">
          <div class="title">
            <span
              class="margin-r-3"
              style="font-size: 16px">{{ $srmI18n(`${$getLangAccount()}#i18n_field_QDW_2464bf0`, '问题：') }}</span>
            <span
              class="margin-r-8"
              style="font-size: 16px">{{ allData.clarificationTitle }}</span>
            <span
              class="margin-r-3"
              style="font-size: 13px; color: grey">{{ $srmI18n(`${$getLangAccount()}#i18n_field_LVyRKI_87cc05b7`, '澄清截止时间') }}</span>
            <span style="font-size: 13px; color: grey">{{ allData.fileClarificationEndTime }}</span>
          </div>
          <div
            class="record"
            v-if="show">
            <div style="padding: 6px 6px 6px 6px; font-size: 16px; font-weight: 700">{{ $srmI18n(`${$getLangAccount()}#i18n_field_LVCc_34586d95`, '澄清内容') }}:</div>
            <div
              class="record-replyContent"
              :style="{ height: pageContentHeight }">
              <a-steps
                progress-dot
                :current="formData"
                direction="vertical">
                <!-- 采购方初始提出的问题内容渲染 -->
                <a-step>
                  <template #title="{ slotProps }">
                    <p style="font-size: 14px; font-weight: bold">{{ getSupplierTitle(allData) }}</p>
                  </template>
                  <!-- 回复框 -->
                  <template #description="{ slotProps }">
                    <div class="replyContent-item">
                      <!-- 回复内容 -->
                      <p style="padding: 6px 15px; margin-bottom: 0px; overflow-wrap: break-word">{{ allData.content }}</p>
                      <!-- 文件列表 -->
                      <div class="fileList">
                        <div
                          class="fileItem"
                          v-for="(file, index) in allData.attachmentList"
                          :key="index">
                          <a-button
                            type="link"
                            @click="downloadEvent(file)">{{ file.fileName }}</a-button>
                          <a
                            style="margin-right: 8px"
                            @click="preViewEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                          <a @click="downloadEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-step>

                <a-step v-if="showFormData.id">
                  <template #title="{ slotProps }">
                    <p style="font-size: 14px; font-weight: bold">{{ getTitle(showFormData) }}</p>
                  </template>
                  <!-- 回复框 -->
                  <template #description="{ slotProps }">
                    <div class="replyContent-item">
                      <!-- 回复内容 -->
                      <p style="padding: 6px 15px; margin-bottom: 0px; overflow-wrap: break-word">{{ showFormData.replyContent }}</p>
                      <!-- 文件列表 -->
                      <div class="fileList">
                        <div
                          class="fileItem"
                          v-for="(file, index) in showFormData.attachmentList"
                          :key="index">
                          <a-button
                            type="link"
                            @click="downloadEvent(file)">{{ file.fileName }}</a-button>
                          <a
                            style="margin-right: 8px"
                            @click="preViewEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                          <a @click="downloadEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-step>
              </a-steps>
            </div>
          </div>

          <div
            class="reply"
            v-if="check != true">
            <div style="padding: 6px 6px 6px 6px; font-size: 16px; font-weight: 700">{{ $srmI18n(`${$getLangAccount()}#i18n_field_LVMB_345910b0`, '澄清回复') }}:</div>
            <hr />
            <div class="reply-Content">
              <a-form-model
                ref="form"
                :label-col="labelCol"
                :wrapper-col="wrapperCol"
                :model="formData">
                <a-row>
                  <a-col :span="24">
                    <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_MBCc_28d55903`, '回复内容')">
                      <a-textarea v-model="formData.replyContent"></a-textarea>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="24">
                    <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_attachment`, '附件')">
                      <a-upload
                        v-if="formData.id"
                        name="file"
                        :multiple="true"
                        :showUploadList="false"
                        :action="uploadUrl"
                        :headers="uploadHeader"
                        :accept="accept"
                        :data="{ headId: formData.id, businessType: 'biddingPlatform' }"
                        :beforeUpload="beforeUpload"
                        @change="handleUploadChange">
                        <a-button type="primary"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                      </a-upload>
                      <a-button
                        v-else
                        @click="beforeUpload"
                        type="primary"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>

                      <div
                        v-for="(fileItem, index) in formData.attachmentList"
                        :key="fileItem.id">
                        <span style="color: blue; cursor: pointer; margin-right: 8px">{{ fileItem.fileName }}</span>
                        <a-icon
                          type="delete"
                          @click="handleDeleteFile(index)" />
                        <a
                          style="margin-right: 8px"
                          @click="preViewEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                        <a @click="downloadEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </a-form-model>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
    <a-modal
      v-drag    
      :visible="previewVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_changeRecord`, '变更记录')"
      :footer="null"
      @cancel="handleCancel"
      :width="800">
      <list-table
        ref="purchaseTenderProjectAttachmentInfoList"
        :statictableColumns="statictableColumns"
        setGridHeight="250"
        :fromSourceData="purchaseTenderProjectAttachmentInfoList"
        :showTablePage="false"> </list-table>
    </a-modal>
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import listTable from '../../BiddingHall/components/listTable'
import ContentHeader from '../../BiddingHall/components/content-header'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        check: {
            type: Boolean
        }
    },
    components: {
        titleCrtl,
        listTable,
        ContentHeader
    },
    data () {
        return {
            showHeader: true,
            previewVisible: false,
            show: false,
            confirmLoading: false,
            labelCol: { span: 2 },
            wrapperCol: { span: 15 },
            allData: {},
            formData: {
                replyContent: '',
                attachmentList: []
            },
            showFormData: {},
            rules: {},
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/saleAttachment/upload`,
            requestData: {
                detail: {
                    url: '/tender/clarification/saleTenderEvaClarificationHead/queryById',
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            url: {
                changeRecord: '/tender/clarification/saleTenderEvaClarificationHead/queryRecordByEvaClarificationId',
                add: '/tender/clarification/saleTenderEvaClarificationHead/replyAdd',
                edit: '/tender/clarification/saleTenderEvaClarificationHead/replyEdit',
                submit: '/tender/clarification/saleTenderEvaClarificationHead/replySubmit'
            }
        }
    },
    computed: {
        pageContentHeight () {
            let height = this.check ? document.body.clientHeight - 200 : document.body.clientHeight - 395
            return height + 'px'
        },
        statictableColumns () {
            let columns = [
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHL_147841e`, '变更人'),
                    fieldLabelI18nKey: '',
                    field: 'createBy',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHLVyRKI_56628ed3`, '变更澄清截止时间'),
                    fieldLabelI18nKey: '',
                    field: 'fileClarificationEndTime',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeReason`, '变更原因'),
                    fieldLabelI18nKey: '',
                    field: 'remark',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                }
            ]
            return columns
        },
        btns () {
            let btn = []
            if (this.check) {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHtHmA_fe90a047`, '变更记录查看'), click: this.handleChangeRecord },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.handleBack }
                ]
            } else {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHtHmA_fe90a047`, '变更记录查看'), click: this.handleChangeRecord },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.handleSave },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'), type: 'primary', click: this.handleSubmit },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.handleBack }
                ]
            }
            return btn
        }
    },
    methods: {
        handleDeleteFile (index) {
            this.formData.attachmentList.splice(index, 1)
        },
        handleCancel () {
            this.previewVisible = false
        },
        getSupplierTitle (item) {
            return `${item.createTime} ${item.realName ?? ''}`
        },
        getTitle (item) {
            return `${item.createTime} ${item.supplierName ?? ''} ${item.realName ?? ''}`
        },
        handleChangeRecord () {
            this.confirmLoading = true
            getAction(this.url.changeRecord, { evaClarificationId: this.currentEditRow.evaClarificationId })
                .then((res) => {
                    if (res.success) {
                        console.log('res.result', res.result)
                        this.purchaseTenderProjectAttachmentInfoList = res.result
                        this.previewVisible = true
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        async queryDetail () {
            let url = this.requestData.detail.url
            let args = this.requestData.detail.args(this)
            console.log(args)
            // this.confirmLoading = true
            let query = await getAction(url, args)
            // this.confirmLoading = false
            console.log(query)

            if (query && query.success) {
                // querybyid接口返回的值
                this.allData = Object.assign({}, query.result)
                // 回复内容数组
                let formData = this.allData.saleTenderEvaClarificationItemList[0] || {
                    replyContent: '',
                    attachmentList: []
                }
                if (formData.status == 1) {
                    this.showFormData = formData
                } else if (formData.status == 0) {
                    this.formData = formData
                }
                console.log(formData)
                console.log(Boolean(this.showFormData))
                console.log(this.showFormData)
                console.log(this.formData)
            } else {
                this.$message.error(query.message)
            }
            this.show = true
        },
        beforeUpload () {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
                return false
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if (file.status === 'done') {
                if (file.response.success) {
                    console.log(file.response.result)
                    let { fileName, filePath, fileSize, id, uploadSubAccount, uploadElsAccount, uploadTime } = file.response.result
                    let fileListData = {
                        uploadSubAccount,
                        uploadElsAccount,
                        uploadTime,
                        fileName,
                        filePath,
                        fileSize,
                        id
                    }
                    if (!this.formData.attachmentList) this.$set(this.formData, 'attachmentList', [])
                    this.formData.attachmentList.push(fileListData)
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        },
        async downloadEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            this.$previewFile.open({ params: row })
        },
        handleSave () {
            this.confirmLoading = true
            this.formData.headId = this.allData.id
            // 判断走的哪个接口（根据saleTenderEvaClarificationItemList是否有值：回复过（保存过））
            let url = this.allData.saleTenderEvaClarificationItemList.length != 0 ? this.url.edit : this.url.add
            // 有值，则回复过，将（保存过）返回的id赋值到formData，再二次保存上去
            if (this.allData.saleTenderEvaClarificationItemList.length != 0) {
                this.formData.id = this.allData.saleTenderEvaClarificationItemList[0].id
            }
            valiStringLength(this.formData, [
                {field: 'remark', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjW_27a9ee3d`, '变更原因'), maxLength: 100},
                {field: 'content', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MBCc_28d55903`, '回复内容'), maxLength: 100}
            ])
            postAction(url, this.formData)
                .then((res) => {
                    let type = res.success ? 'success' : 'error'
                    if (res.success) {
                        if (!this.formData.id) {
                            this.formData.id = res.result.id
                        }
                        this.$message[type](res.message)
                        console.log(this.$message[type](res.message))
                    } else {
                        this.$message[type](res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.queryDetail()
                })
        },
        handleSubmit () {
            this.confirmLoading = true
            this.formData.headId = this.allData.id
            valiStringLength(this.formData, [
                {field: 'remark', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjW_27a9ee3d`, '变更原因'), maxLength: 100},
                {field: 'content', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MBCc_28d55903`, '回复内容'), maxLength: 100}
            ])
            postAction(this.url.submit, this.formData)
                .then((res) => {
                    let type = res.success ? 'success' : 'error'
                    if (res.success) {
                        this.$message[type](res.message)
                        this.$emit('hide')
                        console.log(this.$message[type](res.message))
                    } else {
                        this.$message[type](res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                    // this.queryDetail()
                })
        },
        handleBack () {
            this.$emit('hide')
        }
    },
    mounted () {
        this.queryDetail()
    }
}
</script>
<style lang="less" scoped>
.title {
    padding: 8px;
    background: #f2f3f5;
}
.margin-r-3 {
    margin-right: 3px;
    font-weight: 700;
}

.margin-r-8 {
    margin-right: 8px;
    font-weight: 700;
}

.margin-r-10 {
    margin-right: 10px;
}
.record-replyContent,
.reply-Content {
    padding: 0 6px;
    overflow: auto;
    border: 1px solid #f2f3f5;
}
.replyContent-item {
    border: 1px solid #f2f3f5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
:deep(.ant-steps-dot .ant-steps-item-content){
    width: 90%;
}
:deep(.ant-form-item ){
    margin-bottom: 0px;
}
.fileList {
    display: flex;
    flex-wrap: wrap;
}
.fileItem {
    flex-basis: 33%;
}
</style>
