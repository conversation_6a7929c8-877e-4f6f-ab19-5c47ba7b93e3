<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage "
      :pageData="pageData"
      :url="url" />
    <!-- 编辑界面 -->
    <ElsEnterpriseInfoEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/> 
    <!-- 详情界面 -->
    <els-enterprise-info-detail 
      v-if="showDetailPage"
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>
</template>
<script>
import ElsEnterpriseInfoModal from './modules/ElsEnterpriseInfoModal'
import ElsEnterpriseInfoDetail from './ElsEnterpriseInfoDetail'
import ElsEnterpriseInfoEdit from './ElsEnterpriseInfoEdit'
import {ListMixin} from '@comp/template/list/ListMixin'

export default {
    mixins: [ListMixin],
    components: {
        ElsEnterpriseInfoModal,
        ElsEnterpriseInfoDetail,
        ElsEnterpriseInfoEdit
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeywordEnterprise`, '请输入关键字(企业全称/简称/法人)')
                    }
                ],
                form: {
                    keyWord: '',
                    name: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: true, clickFn: this.settingColumns}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit}
                ]
            },
            url: {
                list: '/enterprise/elsEnterpriseInfo/list',
                delete: '/enterprise/elsEnterpriseInfo/delete',
                deleteBatch: '/enterprise/elsEnterpriseInfo/deleteBatch',
                exportXlsUrl: 'enterprise/elsEnterpriseInfo/exportXls',
                importExcelUrl: 'enterprise/elsEnterpriseInfo/importExcel',
                columns: 'elsEnterpriseInfoList'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterpriseInfo`, '企业基本信息' ))
        }
    }
}
</script>