<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction } from '@/api/manage'
import REGEXP from '@/utils/regexp'
console.log('REGEXP', REGEXP)
export default {
    name: 'SubaccountCertificationEdit',
    mixins: [EditMixin],
    data () {
        return {
            selectType: 'esignPersonCertification',
            pageData: {
                form: {
                    elsAccount: '',
                    subAccount: '',
                    applyUserName: '',
                    applyContact: '',
                    applyContactType: '',
                    mode: '',
                    modifyFields: '',
                    subAccountId: '',
                    paperType: '',
                    otherModes: '',
                    idCardNo: '',
                    bankMobile: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_UzSQNJOHrA0MPEQA`, '个人认证信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                                    fieldName: 'elsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    fieldName: 'subAccount',
                                    disabled: true,
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDLiRL_29ec92a2`, '指定用户认证名称'),
                                    fieldName: 'applyUserName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDLiRL_29ec92a2`, '指定用户认证名称'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'),
                                    fieldName: 'applyContact',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    fieldName: 'applyContactType',
                                    dictCode: 'contractLockContactType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KRLiCK_389fb71a`, '实名认证模式'),
                                    fieldName: 'mode',
                                    dictCode: 'contractLockMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KRLiCK_389fb71a`, '实名认证模式')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiqcrdW_254a9ced`, '认证可修改项'),
                                    fieldName: 'modifyFields',
                                    dictCode: 'contractLockModifyFields',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiqcrdW_254a9ced`, '认证可修改项')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                                    fieldName: 'paperType',
                                    dictCode: 'contractLockPaperType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PtLiCKqid_7031f7d2`, '降级认证方式可选项'),
                                    fieldName: 'otherModes',
                                    dictCode: 'contractLockOtherModes',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PtLiCKqid_7031f7d2`, '降级认证方式可选项')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDUziy_2a6f5b4a`, '指定用户身份证号'),
                                    fieldName: 'idCardNo',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDUziy_2a6f5b4a`, '指定用户身份证号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmy_2e1bfb0e`, '指定用户银行卡号'),
                                    fieldName: 'bankNo',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmy_2e1bfb0e`, '指定用户银行卡号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmUQlty_7742a9aa`, '指定用户银行卡预留手机号'),
                                    fieldName: 'bankMobile',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmUQlty_7742a9aa`, '指定用户银行卡预留手机号')
                                }
                            ],
                            validateRules: {
                                applyUserName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDLiRLxOLV_8ba775b2`, '指定用户认证名称不能为空')}],
                                applyContactType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAcxOLV_394ba69d`, '联系方式类型不能为空')}],
                                applyContact: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKxOLV_4de576d`, '联系方式不能为空')}]
                            }
                        }

                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), type: 'primary', click: this.submitCertificationEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                edit: '/contractLock/saleClPersonalInfo/edit',
                detail: '/contractLock/saleClPersonalInfo/queryById',
                auth: '/contractLock/saleClPersonalInfo/submitCertification',
                modifyAuthInfo: '/contractLock/saleClPersonalInfo/modifyAuthInfo'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        modifyInfoBtn (){
            if(this.currentEditRow.accountId){
                return true
            }
            return false
        },
        saveEvent () {
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    let url = this.url.edit
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        this.goBack()
                    }).finally(() => {
                        this.$refs.editPage.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        submitCertificationEvent () {
            this.$refs.editPage.confirmLoading = true
            const _this = this
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    let url = this.url.auth
                    postAction(url, params).then(res => {
                        if(!res.success){
                            this.$message.error(res.message)
                        }else {
                            this.goBack()
                            window.open(res.result.certificationPageUrl)
                        }
                    }).finally(() => {
                        this.$refs.editPage.confirmLoading = false
                    })
                }
            }).catch(err => {
                _this.$refs.editPage.confirmLoading = false
                console.log(err)
            })
        }
    }
}
</script>