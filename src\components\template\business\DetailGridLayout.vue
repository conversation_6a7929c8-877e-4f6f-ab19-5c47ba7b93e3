<template>
  <div>
    <slot></slot>
    <vxe-grid
      :height="getGridHeight()"
      :ref="group.groupCode"
      v-bind="currentGridConfig"
      :edit-config="group.editConfig"
      :edit-rules="group.editRules"
      :columns="group.columns"
      :data="gridData"
      :show-footer="showGridFooter"
      :footer-method="
        ({ columns, data }) => {
          return footerMethod({ group, columns, data })
        }
      "
      @checkbox-change="checkboxChangeDetail"
      @cell-click="cellClickEvent"
    >
      <!-- 表格代码编辑器 -->
      <template #code_editor_col_render="{ row, column }">
        <code-editor-model
          :value="row[column.property]"
          @handleSureClick="
            (content) => {
              row[column.property] = content
            }
          "
        ></code-editor-model>
      </template>
      <!-- 表格富文本编辑器 -->
      <template #rich_editor_col_render="{ row, column }">
        <renderHtmlModal :content="row[column.property]" />
      </template>
      <template #renderDictLabel="{ row, column }">
        <span>
          {{ getDictLabel(row, column) }}
        </span>
      </template>
      <!-- 货币千分位 -->
      <template #renderCurrency="{ row, column }">
        <span>{{ currencyFormat(row[column.property], column, group.columns) }}</span>
      </template>
      <template #toolbar_buttons>
        <business-button
          :buttons="group.externalToolBar"
          :groupCode="group.groupCode"
          :pageConfig="pageConfig"
          :currentEditRow="currentEditRow"
          isToolbarButtons
          v-bind="$attrs"
          v-on="$listeners"
        />
      </template>
      <template #grid_opration="{ row, column }">
        <div v-if="group.extend && group.extend.optColumnList">
          <span
            v-for="(opt, optIndex) in group.extend.optColumnList"
            :key="'opt_' + row.id + '_' + optIndex"
          >
            <a
              :title="opt.title"
              style="margin: 0 4px"
              :disabled="opt.disabled"
              v-show="optionHandle({ opt, row, column })"
              @click="
                () => {
                  optColumnFuntion(opt, row, column, group.columns)
                }
              "
              >{{ opt.title }}</a
            >
          </span>
        </div>
      </template>
      <!--使用 bottom 插槽-->
      <template #bottom>
        <div
          class="summary-message"
          v-if="group.total.totalValue"
        >
          <span class="summary-message-content">
            {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }} {{ $srmI18n(`${$getLangAccount()}#${group.i18n_title_generalSummary}`, '总汇总') }}：<span class="total-num">{{ group.total.totalValue }}</span></span
          >
        </div>
      </template>

      <template #empty>
        <m-empty
          :displayModel="displayModel"
          :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
        />
      </template>
    </vxe-grid>
  </div>
</template>

<script>
import BusinessButton from './components/BusinessButton'
import RichEditorModel from '@comp/richEditorModel/RichEditorModel'
import { getObjType, isPromise } from '@/utils/util'
import { currency } from '@/filters'

export default {
  name: 'DetailGridLayout',
  inject: ['tplRootRef'],
  components: {
    RichEditorModel,
    BusinessButton
  },
  props: {
    // 传入归属方busAccount
    busAccount: {
      required: true,
      type: String,
      default: null
    },
    group: {
      type: Object,
      required: true,
      default() {
        return {}
      }
    },
    loadData: {
      type: Array,
      default() {
        return []
      }
    },
    pageConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    currentEditRow: {
      type: Object,
      default() {
        return {}
      }
    },
    gridCustomConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    gridConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    displayModel: {
      type: String,
      default: 'tab'
    },
    gridFooterMethod: {
      type: Function,
      default: null
    },
    showGridFooter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      //默认表格配置
      gridData: this.loadData || []
    }
  },
  computed: {
    currentGridConfig() {
      return this.gridConfig
    },
    columnsCompute() {
      return (columns) => columns.filter((rs) => rs.fold != 1)
    }
  },
  watch: {
    loadData: {
      immediate: true,
      handler: function (val) {
        this.gridData = val
        console.log(this.gridData)
        this.loadGridData()
      }
    }
  },
  methods: {
    // 获取表格高度
    getGridHeight() {
      // 检查是否启用根据数据条数设置高度
      if (this.group.extend && this.group.extend.autoHeightByData) {
        const dataLength = this.gridData.length
        if (dataLength === 0) {
          return '100px' // 无数据时的最小高度
        }

        // 根据数据条数计算高度
        const rowHeight = this.group.extend.rowHeight || 36 // 每行高度，默认36px
        const headerHeight = this.group.extend.headerHeight || 50 // 表头高度，默认50px
        const maxHeight = this.group.extend.maxHeight || 600 // 最大高度限制
        const minHeight = this.group.extend.minHeight || 100 // 最小高度限制

        let calculatedHeight = dataLength * rowHeight + headerHeight

        // 应用最大最小高度限制
        if (calculatedHeight > maxHeight) {
          calculatedHeight = maxHeight
        }
        if (calculatedHeight < minHeight) {
          calculatedHeight = minHeight
        }

        return calculatedHeight + 'px'
      }

      // 原有的动态高度逻辑
      if (this.isDynamics()) {
        return '100%'
      }

      return '334px'
    },

    isDynamics() {
      if(
        this.group.groupCode === 'purchaseBiddingHeadList' ||
        this.group.groupCode === 'purchaseBiddingItemList' ||
        this.group.groupCode === 'recAcceptReturnList' ||
        this.group.groupCode === 'recAdditionalChargesList' ||
        this.group.groupCode === 'recChargeList' ||
        this.group.groupCode === 'prePaymentWriteOffList' ||
        this.group.groupCode === 'invoiceList' ||
        this.group.groupCode === 'supplierContactsInfoList' ||
        this.group.groupCode === 'supplierAddressInfoList'
      ) {
        return true;
      }
      return false;
    },
    cellClickEvent(info) {
      const { row, rowIndex, column, columnIndex } = info
      console.log(info)
      this.$emit('cell-click', { row, rowIndex, column, columnIndex })
    },
    footerMethod({ group, columns, data }) {
      let footerData = []
      if (columns) {
        footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '合计'
            }
            return null
          })
        ]
      }
      if (this.gridFooterMethod && getObjType(this.gridFooterMethod) === 'function') {
        return this.gridFooterMethod({ group, columns, data })
      } else {
        return footerData
      }
    },
    optionHandle(data) {
      let { opt, row } = data
      let hide = opt.hide
      let response = false
      if (hide && typeof hide === 'function') {
        response = hide(row)
        if (isPromise(response)) {
          response.then(
            () => {
              response = true
            },
            () => {
              response = false
            }
          )
        }
      } else if (typeof hide !== 'undefined') {
        response = !!hide
      } else {
        response = false
      }
      return !response
    },
    // 操作列方法
    optColumnFuntion(opt, row, col, columns) {
      opt.click && opt.click(this, row, col, this.tplRootRef, { columns })
    },
    loadGridData() {
      let that = this
      this.$nextTick(() => {
        if (that.$refs[that.group.groupCode]) {
          that.$refs[that.group.groupCode].loadData(that.gridData)
        }
      })
    },
    // 通过value显示label
    getDictLabel(row, column) {
      // 如果没有配置数据字典则走返回的field key值
      return row[column.property + '_dictText'] || row[column.property]
    },
    // 货币千分位
    currencyFormat(value, info, columns = []) {
      if (!value) {
        return ''
      }
      let extend = {}
      for (let item of columns) {
        if (info.property === item.field) {
          extend = item.extend || {}
          break
        }
      }

      console.log('extend :>> ', extend)

      let symbol = (extend && extend.symbol) || ''
      let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
      return currency(value, symbol, decimals)
    },
    // 多选点击事件
    checkboxChangeDetail({ checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
      this.tplRootRef.checkboxChange && this.tplRootRef.checkboxChange({ checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event })
    }
  }
}
</script>
<style lang="less" scoped>
.summary-message {
  height: 14px;
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.summary-message-content {
  flex-grow: 1;
  font-weight: bolder;

  .total-num {
    font-size: 16px;
    color: red;
  }
}
:deep(.vxe-body--column.required-col) {
  background-color: #f6ebe8;
  background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
}
:deep(.vxe-header--column.required-col) {
  background-color: #f6ebe8;
  background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
}
</style>
