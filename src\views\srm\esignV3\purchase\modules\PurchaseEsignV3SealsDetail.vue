<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
export default {
    name: 'PurchaseEsignV3SealsDetail',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'seals',
            confirmLoading: false,
            pageData: {
                form: {
                    relationId: '',
                    companyName: '',
                    alias: '',
                    height: '',
                    width: '',
                    transparentFlag: '',
                    filePath: '',
                    sealId: '',
                    subAccount: '',
                    orgId: '',
                    accountId: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WedV_27cc4b0f`, '印章详情'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_transferType`, '类型'),
                                    fieldName: 'sealType_dictText',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dWtR_2e111a28`, '所属机构'),
                                    fieldName: 'orgName',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WedWL_d12084ec`, '印章所属人'),
                                    fieldName: 'psnName'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeRL_27c5a0f3`, '印章名称'),
                                    fieldName: 'sealName',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WezE_27c93d7b`, '印章状态'),
                                    fieldName: 'sealStatus_dictText',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WezE_27c93d7b`, '印章状态')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIHAElb_744f8381`, '是否已跨企业授权'),
                                    fieldName: 'orgAuth_dictText',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIHAElb_744f8381`, '是否已跨企业授权')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HAElbbXKI_37163a23`, '跨企业授权生效时间'),
                                    fieldName: 'effectiveTime',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HAElbbXKI_37163a23`, '跨企业授权生效时间')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HAElbKXKI_2a6404d1`, '跨企业授权失效时间'),
                                    fieldName: 'expireTime',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HAElbKXKI_2a6404d1`, '跨企业授权失效时间')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_rMjW_4785fb2c`, '驳回原因'),
                                    fieldName: 'rejectReason',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_rMjW_4785fb2c`, '驳回原因'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_width`, '印章宽度（单位：mm，上限为100mm）'),
                                    fieldName: 'sealWidth',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_width`, '印章宽度（单位：mm，上限为100mm）'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_height`, '印章高度（单位：mm，上限为100mm）'),
                                    fieldName: 'sealHeight',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_height`, '印章高度（单位：mm，上限为100mm）'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeOy_27cdf6c6`, '印章颜色'),
                                    fieldName: 'sealColor_dictText',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeOy_27cdf6c6`, '印章颜色')

                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeESAc_49a8b9e7`, '印章业务类型'),
                                    fieldName: 'sealBizType_dictText',
                                    required: '1',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeESAc_49a8b9e7`, '印章业务类型')
                                },
                                {
                                    fieldType: 'image',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_qBGuFJsS`, '图片路径'),
                                    fieldName: 'filePath',
                                    required: '1',
                                    extend: {multiple: false, limit: 1}
                                }
                            ]
                        }

                    }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/esignv3/purchaseEsignV3Seals/queryById'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>