<template>
  <div style="height:100%">
    <list-layout
      refresh
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <EditSaleFadadaOrgPsn-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideEditPage" />
    <ViewSaleFadadaOrgPsn-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import EditSaleFadadaOrgPsnModal from './modules/EditSaleFadadaOrgPsnModal'
import ViewSaleFadadaOrgPsnModal from './modules/ViewSaleFadadaOrgPsnModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {httpAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EditSaleFadadaOrgPsnModal,
        ViewSaleFadadaOrgPsnModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'fadada',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRRLjRcR_c2bd2629`, '机构名称/员工姓名'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRRLjRcR_c2bd2629`, '机构名称/员工姓名')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'fadada#saleFadadaOrgPsn:add', icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'fadada#saleFadadaOrgPsn:view', clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disable`, '禁用'), authorityCode: 'fadada#saleFadadaOrgPsn:disable', clickFn: this.handleDisable, allow: this.showDisableCondition},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enable`, '启用'), authorityCode: 'fadada#saleFadadaOrgPsn:enable', clickFn: this.handleEnable, allow: this.showEnableCondition},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'fadada#saleFadadaOrgPsn:delete', clickFn: this.handleDelete, allow: this.showDelCondition},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 300
            },
            url: {
                list: '/electronsign/fadada/saleFadadaOrgPsn/list',
                add: '/electronsign/fadada/saleFadadaOrgPsn/add',
                delete: '/electronsign/fadada/saleFadadaOrgPsn/delete',
                disabled: '/electronsign/fadada/purchaseFadadaOrgPsn/disabled',
                enable: '/electronsign/fadada/purchaseFadadaOrgPsn/enable',
                columns: 'saleFadadaOrgPsnList'
            }
        }
    },
    methods: {
        handleChidCallback (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleAdd () {          
            this.currentEditRow = {}
            this.showEditPage = true
        },
        showDisableCondition (row){
            if(row.roleType && row.roleType.search('super_admin')!=-1){
                return true
            }
            if(row.active==='1' && (row.memberStatus==='enable' || row.memberStatus==='activated')){
                return false
            }
            return true
        },
        showEnableCondition (row){
            if(row.roleType &&  row.roleType.search('super_admin')!=-1){
                return true
            }
            if(row.active==='1' && row.memberStatus==='disable'){
                return false
            }
            return true
        },
        handleView (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },
        // showEditCondition (row) {
        //     return false
        // },
        showDelCondition (row) {
            if(row.active==='1'){
                return true
            }
            return false
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (){
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        cancel (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row, 'post')
                }
            })
        },
        postUpdateData (url, row, type){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, type).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        handleDisable (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disable`, '禁用'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRIHj_d65abe55`, '是否确定禁用？'),
                onOk: function () {
                    let param = {
                        id: row.id
                    }
                    that.postUpdateData(that.url.disabled, param, 'get')
                }
            })
        },
        handleEnable (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enable`, '启用'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRIAj_d5cea583`, '是否确定启用？'),
                onOk: function () {
                    let param = {
                        id: row.id
                    }
                    that.postUpdateData(that.url.enable, param, 'get')
                }
            })
        }
    }
}
</script>