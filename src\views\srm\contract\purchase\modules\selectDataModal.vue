<template>
  <div>
    <a-modal
      v-drag
      centered
      :title="title"
      :width="960"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="close"
    >
      <a-form-model layout="inline">
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_sourceType`, '来源类型')">
          <m-select
            style="width: 200px"
            v-model="suorceType"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectSourceType`, '请选择来源类型')"
            @change="suorceTypeChange"
            :dict-code="contractType === '1' ? 'srmContractItemSourceTypeStandard' : 'srmContractItemSourceType'"
          />
        </a-form-model-item>
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_keyword`, '关键字')">
          <a-input
            @keyup.enter.native="onSearch"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')"
            v-model="keyWord"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button
            type="primary"
            @click="onSearch"
            >{{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}
          </a-button>
        </a-form-model-item>
      </a-form-model>
      <vxe-grid
        border
        resizable
        max-height="350"
        row-id="id"
        size="mini"
        :loading="loading"
        ref="selectGrid"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
        :data="tableData"
        :pager-config="tablePage"
        :radio-config="selectModel === 'single' ? checkedConfig : undefined"
        :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
        :columns="currentColumns"
        @page-change="handlePageChange"
      >
      </vxe-grid>
    </a-modal>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import { getLangAccount, srmI18n } from '@/utils/util'

export default {
  props: {
    title: {
      type: String,
      default: srmI18n(`${getLangAccount()}#i18n_title_selectDataMsg`, '选择数据')
    },
    columns: {
      type: Array,
      default: () => [
        { type: 'checkbox', width: 40 },
        { type: 'seq', title: srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号'), width: 60 }
      ]
    },
    selectModel: {
      type: String,
      default: 'multiple'
    },
    contractType: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      suorceType: 'material',
      suorceTypeName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisi`, '采购申请'),
      keyWord: '',
      visible: false,
      loading: false,
      confirmLoading: false,
      currentUrl: '/demand/purchaseRequestHead/contractQueryRequest',
      urlList: {
        material: '/material/purchaseMaterialHead/list',
        purchaseRequest: '/demand/purchaseRequestHead/contractQueryRequest',
        order: '/order/purchaseOrderItem/contractQueryOrderItem',
        bidding: '/bidding/purchaseBiddingHead/contractQueryBidding',
        ebidding: '/ebidding/purchaseEbiddingHead/contractQueryEbidding',
        enquiry: '/enquiry/purchaseEnquiryHead/contractQueryEnquiry'
      },
      currentColumns: [
        { type: 'checkbox', width: 40 },
        { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
        {
          field: 'requestNumber',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requestNumber`, '采购申请号'),
          width: 120
        },
        {
          field: 'itemNumber',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdPpapItemListf87c_rowNumber`, '行号'),
          width: 100
        },
        {
          field: 'materialNumber',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead9ab6_materialCode`, '物料编码'),
          width: 120
        },
        {
          field: 'materialName',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
          width: 120
        },
        {
          field: 'materialDesc',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeada4a_materialDesc`, '物料描述'),
          width: 150
        },
        {
          field: 'materialSpec',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
          width: 150
        },
        {
          field: 'quantity',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TVWR_46474721`, '需求数量'),
          width: 100
        },
        {
          field: 'purchaseUnit_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'),
          width: 100
        }
      ],
      columnList: {
        material: [
          { type: 'checkbox', width: 40 },
          {
            type: 'seq',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),
            width: 60
          },
          {
            field: 'materialNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
            width: 120
          },
          {
            field: 'materialName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
            width: 120
          },
          {
            field: 'materialDesc',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
            width: 150
          },
          {
            field: 'materialSpec',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
            width: 150
          },
          {
            field: 'purchaseUnit_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'),
            width: 100
          }
        ],
        purchaseRequest: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
          {
            field: 'requestNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请号'),
            width: 120
          },
          {
            field: 'itemNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
            width: 100
          },
          {
            field: 'materialNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
            width: 120
          },
          {
            field: 'materialName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
            width: 120
          },
          {
            field: 'materialDesc',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
            width: 150
          },
          {
            field: 'materialSpec',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
            width: 150
          },
          {
            field: 'quantity',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量'),
            width: 100
          },
          {
            field: 'purchaseUnit_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'),
            width: 100
          }
        ],
        order: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
          {
            field: 'orderNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseOrderNo`, '采购订单号'),
            width: 120
          },
          {
            field: 'itemNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
            width: 100
          },
          {
            field: 'materialNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
            width: 120
          },
          {
            field: 'materialName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
            width: 120
          },
          {
            field: 'materialDesc',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
            width: 150
          },
          {
            field: 'materialSpec',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
            width: 150
          },
          {
            field: 'quantity',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qty`, '数量'),
            width: 100
          },
          {
            field: 'purchaseUnit_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'),
            width: 100
          },
          {
            field: 'taxCode',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxCode`, '税码'),
            width: 80
          },
          {
            field: 'taxRate',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxRate`, '税率'),
            width: 80
          },
          {
            field: 'price',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            width: 100
          },
          {
            field: 'netAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netAmount`, '未税金额'),
            width: 100
          },
          {
            field: 'taxAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'),
            width: 100
          },
          {
            field: 'currency_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'),
            width: 100
          }
        ],
        bidding: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
          {
            field: 'biddingNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tenderNumber`, '招标单号'),
            width: 120
          },
          {
            field: 'itemNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
            width: 100
          },
          {
            field: 'materialNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
            width: 120
          },
          {
            field: 'materialName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
            width: 120
          },
          {
            field: 'materialDesc',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
            width: 150
          },
          {
            field: 'materialSpec',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
            width: 150
          },
          {
            field: 'quotaQuantity',
            title: this.$srmI18n(`${this.$getLangAccount()}#quotaQuantity`, '拆分数量'),
            width: 100
          },
          {
            field: 'purchaseUnit_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'),
            width: 100
          },
          {
            field: 'taxCode',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxCode`, '税码'),
            width: 80
          },
          {
            field: 'taxRate',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxRate`, '税率'),
            width: 80
          },
          {
            field: 'price',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            width: 100
          },
          {
            field: 'quotaNetAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netAmount`, '未税金额'),
            width: 100
          },
          {
            field: 'quotaTaxAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'),
            width: 100
          },
          {
            field: 'currency_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'),
            width: 100
          }
        ],
        ebidding: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
          {
            field: 'ebiddingNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号'),
            width: 120
          },
          {
            field: 'itemNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
            width: 100
          },
          {
            field: 'materialNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
            width: 120
          },
          {
            field: 'materialName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
            width: 120
          },
          {
            field: 'materialDesc',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
            width: 150
          },
          {
            field: 'materialSpec',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
            width: 150
          },
          {
            field: 'quotaQuantity',
            title: this.$srmI18n(`${this.$getLangAccount()}#quotaQuantity`, '拆分数量'),
            width: 100
          },
          {
            field: 'purchaseUnit_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'),
            width: 100
          },
          {
            field: 'taxCode',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxCode`, '税码'),
            width: 80
          },
          {
            field: 'taxRate',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxRate`, '税率'),
            width: 80
          },
          {
            field: 'price',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            width: 100
          },
          {
            field: 'quotaNetAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netAmount`, '未税金额'),
            width: 100
          },
          {
            field: 'quotaTaxAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'),
            width: 100
          },
          {
            field: 'currency_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'),
            width: 100
          }
        ],
        enquiry: [
          { type: 'checkbox', width: 40 },
          { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
          {
            field: 'enquiryType_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enquiryType`, '询价类型'),
            width: 120
          },
          {
            field: 'enquiryNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquirySheetNo`, '询价单号'),
            width: 120
          },
          {
            field: 'itemNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
            width: 100
          },
          {
            field: 'materialNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
            width: 120
          },
          {
            field: 'materialDesc',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
            width: 150
          },
          {
            field: 'materialSpec',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
            width: 150
          },
          {
            field: 'quotaQuantity',
            title: this.$srmI18n(`${this.$getLangAccount()}#quotaQuantity`, '拆分数量'),
            width: 100
          },
          {
            field: 'purchaseUnit_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'),
            width: 100
          },
          {
            field: 'taxCode',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxCode`, '税码'),
            width: 80
          },
          {
            field: 'taxRate',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxRate`, '税率'),
            width: 80
          },
          {
            field: 'price',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            width: 100
          },
          {
            field: 'quotaNetAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netAmount`, '未税金额'),
            width: 100
          },
          {
            field: 'quotaTaxAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'),
            width: 100
          },
          {
            field: 'currency_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'),
            width: 100
          }
        ]
      },
      labelCol: {
        md: { span: 8 },
        lg: { span: 8 },
        xxl: { span: 6 }
      },
      wrapperCol: {
        md: { span: 16 },
        lg: { span: 16 },
        xxl: { span: 18 }
      },
      checkedConfig: { highlight: true, reserve: true, trigger: 'row' },
      tableData: [],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        pageNo: 1,
        align: 'left',
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
        perfect: true
      },
      propParams: {}
    }
  },
  methods: {
    suorceTypeChange(newValue, opt) {
      console.log(newValue)
      console.log(opt.componentOptions.propsData.title)
      if (newValue == 'addedManually') {
        this.$message.warning('手动新增合同行信息,请点击新增按钮')
        this.visible = false
        return
      }
      if (newValue != '') {
        this.suorceType = newValue
        this.suorceTypeName = opt.componentOptions.propsData.title
        this.currentUrl = this.urlList[newValue]
        this.currentColumns = this.columnList[newValue]
        this.$refs.selectGrid.reloadColumn(this.currentColumns)

        /*页面查询限制
                  采购申请：转需求池即可
                  采购订单：已确认订单
                  招标：已定标
                  竞价：已定标
                  询价：已定标
                  物料：非集团级删除
                 */
        var p = Object.assign({}, this.propParams, { keyWord: this.keyWord })

        if (newValue == 'order') {
          p = Object.assign(p, { itemStatus: '1' })
        }

        if (newValue == 'material') {
          p = Object.assign(p, { blocDel: '0' })
        }
        if (newValue == 'bidding') {
          p = Object.assign(p, {
            isOrder: {
              column: 'biddingNumber',
              order: 'desc'
            }
          })
        }
        this.loadData(p)
        this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxRow()
      }
    },
    loadData(params1) {
      let params = Object.assign({}, params1)
      this.loading = true
      // 非询报价接口不需要company参数
      if (this.currentUrl != this.urlList.enquiry) {
        delete params.company
      }
      getAction(this.currentUrl, params).then((res) => {
        if (res.success) {
          let list = res.result.records || []
          this.tableData = list
          this.tablePage.total = res.result.total
        }
        this.loading = false
      })
    },
    open(params) {
      // 重置第一个
      this.suorceType = 'material'
      let queryParams = { pageSize: this.tablePage.pageSize, pageNo: '1' }
      if (params) {
        queryParams = Object.assign({}, queryParams, params)
      }
      this.propParams = { ...queryParams }
      this.currentUrl = this.urlList[this.suorceType]
      this.currentColumns = this.columnList[this.suorceType]
      this.$refs.selectGrid && this.$refs.selectGrid.reloadColumn(this.currentColumns)
      this.loadData(queryParams)
      this.visible = true
      this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxRow()
    },
    close() {
      this.visible = false
    },
    selectedOk() {
      let selectedData = this.$refs.selectGrid.getCheckboxRecords()
      if (selectedData.length) {
        let resultList = []
        for (let i in selectedData) {
          let data = selectedData[i]
          let item = {}
          item['materialNumber'] = data['materialNumber']
          item['materialId'] = data['materialId']
          item['materialDesc'] = data['materialDesc']
          item['materialSpec'] = data['materialSpec']
          item['materialName'] = data['materialName']
          item['quantityUnit'] = data['quantityUnit']
          item['sourceType'] = this.suorceType
          item['sourceType_dictText'] = this.suorceTypeName
          item['sourceItemNumber'] = data['itemNumber']
          item['sourceId'] = data['headId']
          item['documentParentId'] = data['id']
          item['documentId'] = data['documentId']
          item['documentItemId'] = data['documentItemId']
          item['sourceItemId'] = data['id']
          item['currency'] = data['currency']
          item['materialGroup'] = data['materialGroup']
          item['materialGroup_dictText'] = data['materialGroup_dictText']
          item['price'] = data['price']
          item['purchaseUnit'] = data['purchaseUnit']
          item['taxCode'] = data['taxCode']
          item['taxRate'] = data['taxRate']
          item['conversionRate'] = data['conversionRate']
          if (this.suorceType == 'purchaseRequest') {
            item['quantity'] = data['quantity']
            item['sourceNumber'] = data['requestNumber']
            let requireQuantity = data['quantity']
            let price = data['price']
            if (requireQuantity && price) {
              item['taxAmount'] = price * requireQuantity
            }
            if (data['taxRate'] && price && requireQuantity) {
              item['netAmount'] = (item['taxAmount'] / (1 + parseFloat(data['taxRate']) / 100)).toFixed(4)
            }
          } else if (this.suorceType == 'order') {
            item['quantity'] = data['quantity']
            item['sourceNumber'] = data['orderNumber']
            item['taxAmount'] = data['taxAmount']
            item['netAmount'] = data['netAmount']
          } else if (this.suorceType == 'bidding' || this.suorceType == 'ebidding') {
            /*let requireQuantity = data['requireQuantity']
                        let quota = data['quota']
                        if (requireQuantity && quota) {
                            let quantity = parseFloat(requireQuantity) * parseFloat(quota) / 100.0
                            item['quantity'] = quantity
                            let price = data['price']
                            if (price) {
                                item['taxAmount'] = price * quantity
                            }
                            if (data['taxRate'] && price && requireQuantity) {
                                item['netAmount'] = (item['taxAmount'] / (1 + parseFloat(data['taxRate']) / 100)).toFixed(4)
                            }
                        }*/
            item['quantity'] = data['quotaQuantity']
            item['taxAmount'] = data['quotaTaxAmount']
            item['netAmount'] = data['quotaNetAmount']
            if (this.suorceType == 'bidding') {
              item['sourceNumber'] = data['biddingNumber']
            } else {
              item['sourceNumber'] = data['ebiddingNumber']
            }
          } else if (this.suorceType == 'enquiry') {
            /*let requireQuantity = data['requireQuantity']
                        let price = data['price']
                        item['quantity'] = requireQuantity
                        if (requireQuantity && price) {
                            item['taxAmount'] = price * requireQuantity
                        }
                        if (data['taxRate'] && price && requireQuantity) {
                            item['netAmount'] = (item['taxAmount'] / (1 + parseFloat(data['taxRate']) / 100)).toFixed(4)
                        }*/
            item['quantity'] = data['quotaQuantity']
            item['taxAmount'] = data['quotaTaxAmount']
            item['netAmount'] = data['quotaNetAmount']
            item['sourceNumber'] = data['enquiryNumber']
          }
          resultList.push({
            ...data,
            ...item
          })
        }
        this.visible = false
        this.$emit('ok', resultList)
      } else {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
      }
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      let pageNo = currentPage
      let queryParams = Object.assign({}, this.propParams, { pageNo, pageSize, keyWord: this.keyWord })

      /*
        根据不同的url增加入参
        this.suorceType
         */
      let p = queryParams

      if (this.suorceType == 'order') {
        p = Object.assign(p, { itemStatus: '1' })
      }

      if (this.suorceType == 'material') {
        p = Object.assign(p, { blocDel: '0' })
      }
      this.loadData(p)
    },
    onSearch() {
      console.log(this.suorceType)
      // 提交重置
      this.tablePage.currentPage = 1
      this.tablePage.pageSize = this.propParams.pageSize
      if (this.suorceType == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectSourceType`, '请选择来源类型'))
      } else {
        let p = Object.assign({}, this.propParams, { keyWord: this.keyWord })
        /*
        根据不同的url增加入参  采购类型的入参有多个，直接在后端写死了
         */
        if (this.suorceType == 'order') {
          p = Object.assign(p, { itemStatus: '1' })
        }

        if (this.suorceType == 'material') {
          p = Object.assign(p, { blocDel: '0' })
        }
        this.loadData(p)
      }
    }
  }
}
</script>
