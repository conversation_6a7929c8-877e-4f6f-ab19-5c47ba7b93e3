<template>
  <div class="NoticeList">
    <div class="header">
      <div class="wrapper">
        <router-link
          to="/"
          class="box"
        >
          <img
            class="logo"
            :src="userInfo.companyLogo || qqtLogoSrc"
            alt="logo"
          />
          <span class="blue">SRM</span>
<!--          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_easyToLearnEasyToUse`, '易学易用') }}</span>-->
<!--          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_interconnection`, '互联互通') }}</span>-->
        </router-link>
      </div>
    </div>
    <div
      class="banner"
      :class="'banner1'"
    ></div>
    <div class="wrapper">
      <div
        class="content"
      >
        <h1
          class="title msg"
        >
          <span>{{ title }}</span>
          <span>{{ ENTitle }}</span>
        </h1>
        <div
          class="list"
          v-if="publicNotice.length>0">
          <ul>
            <template v-for="el in publicNotice">
              <li :key="el.id">
                <router-link
                  class="row space"
                  :to="{ name: goTemplateName(el), query: { 
                    id: el.id, 
                    businessId: el.businessId, 
                    businessType: el.businessType, 
                    noticeTitle: el.noticeTitle, 
                    elsAccount:el.templateAccount || el.busAccount,
                    templateVersion:el.templateVersion, 
                    templateNumber: el.templateNumber,
                    uploadElsAccount: el.busAccount,
                    roleType: el.busAccount === el.elsAccount ? 'purchase' : 'sale',
                  } 
                  }"
                >
                  <span class="tit"> <b
                    class="notice-type m-r-10"
                    :class="{'notice-type-hot': el['noticeType']!='2'}">{{ el['noticeType']=='2'?'常':"急" }}</b> {{ el.noticeTitle }}</span>
                  <span class="date">{{ el.publishTime | formatPublishTime }}</span>
                </router-link>
  
              </li>
            </template>
          </ul>
        </div>
        <div
          class="enpty"
          v-else>
          <img
            src="@/assets/img/login/enpty.png"
            alt="">
        </div>
        <div class="pagination">
          <a-pagination
            size="small"
            :total="total"
            :current="pageNo"
            :show-total="showTotal(total)"
            show-size-changer
            show-quick-jumper
            @showSizeChange="onShowSizeChange"
            @change="onChange"
          />
        </div>
      </div>
  
    </div>
  </div>
</template>
  
<script>
import { getAction } from '@/api/manage'
export default {
    filters: {
        formatPublishTime (val) {
            return val && (typeof val === 'string') ? val.slice(0, 10) : val
        }
    },
    data () {
        return {
            publicNotice: [],
            pageNo: 1,
            pageSize: 10,
            total: 0,
            userInfo: '',
            qqtLogoSrc: require('@/assets/img/login/logo.png')
        }
    },
    methods: {
        //分页总数
        showTotal (total) {
            let labels = `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_commonTotal`, '共')} ${total} ${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_records`, '条')}`
            return () => labels
        },
        // 跳转到逻辑
        goTemplateName (data) {
            let name = 'noticeDetail'
            if (data && (data.businessType === 'ebidding' || data.businessType === 'bidding' || data.businessType === 'enquiry')) {
                name = 'noticeDetailTemplate'
            } else if (data.businessType === 'winBidNotification' || data.businessType === 'failBidNotification' || data.businessType === 'winBidNotice') {
                name = 'noticeDetail'
            } else if (data.businessType === 'survey') {
                name = 'observe'
            } else if (data.businessType === 'tender_bulletin') {
                name = 'BiddingInformationDetail'
            } else if (data.businessType === 'tender_candidate') {
                name = 'BiddingCandidateDetail'
            } else if (data.businessType === 'tender_winBulletin') {
                name = 'BiddingWinBulletinDetail'
            }
            return name
        },
        getNoticeData () {
            const url = '/notice/purchaseNotice/getHomePageNotice'
            const params = {
                pageNo: this.pageNo,
                pageSize: this.pageSize
            }
            getAction(url, params).then(res => {
                this.publicNotice = res.result.records || []
                this.total = res.result.total
            })
        },
        onShowSizeChange (page, pageSize) {
            this.pageNo = page
            this.pageSize = pageSize
            this.getNoticeData()
        },
        onChange (page, pageSize) {
            this.pageNo = page
            this.getNoticeData()
        }
    },
    computed: {
     
        title () {
            return this.$srmI18n(`${this.$getLangAccount()}#i18n_title_plateformAnnouncement`, '平台公告')
        },
        ENTitle (){
            return 'Platform announcement'
        }
    },
    created () {
        this.getNoticeData()
      
    }
}
</script>
  
  <style lang="less" scoped>
  @blue: #178aff;
  @banner1: '~@/assets/img/login/notice_1.jpg';
  @banner2: '~@/assets/img/login/notice_2.jpg';
  @banner3: '~@/assets/img/login/notice_3.jpg';
  @icon: '~@/assets/img/login/icon_1.png';
  @icon2: '~@/assets/img/login/icon_2.png';
  @icon3: '~@/assets/img/login/新闻与活动.png';
  @gray: #8c8c8c;
  .header {
    padding: 8px 0;
    font-size: 16px;
    color: @gray;
    background-color: #fff;
    .wrapper {
      margin: 0 auto;
      width: 83%;
    }
    .box {
      display: flex;
      align-items: center;
      color: #c1c1c1;
      font-weight: 300;
      .logo {
        flex: 0 0 40px;
        width: 40px;
        height: 40px;
      }
      span + span {
        margin-left: 16px;
      }
      .blue {
        position: relative;
        margin-left: 8px;
        color: @blue;
        font-weight: 600;
        &::after {
          position: absolute;
          right: -8px;
          top: 4px;
          width: 1px;
          height: 16px;
          background-color: @gray;
          content: '';
        }
      }
    }
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .NoticeList {
    font-size: 14px;
    padding-bottom: 16px;
    background-color: #f6f6f6;
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    .banner {
      height: 280px;
    }
    .banner1 {
      background: #fff url(@banner1) no-repeat top/auto 280px;
    }
    .banner2 {
      background: #fff url(@banner2) no-repeat top/auto 280px;
    }
    .banner3 {
      background: #fff url(@banner3) no-repeat top/auto 280px;
    }
    .wrapper {
      margin: 0 auto;
      width: 83%;
    }
   
  
     .content {
      min-height: 635px;
      width: 100%;
      background-color: #fff;
      padding: 30px;
      margin-top: 16px;
      .title {
      padding-left: 32px;
      font-weight: 700;
      font-size: 16px;
      color: @blue;
      display: flex;
      justify-content: space-between;
      padding-right: 36px;
      &.msg {
        background: #fff url(@icon) no-repeat center left/20px 20px;
      }
      &.bid {
        background: #fff url(@icon2) no-repeat center left/20px 20px;
      }
      &.news {
        background: #fff url(@icon3) no-repeat center left/20px 20px;
      }
      span {
        &:last-child {
          position: relative;
          &::after {
            position: absolute;
            right: -31px;
            top: 12px;
            width: 16px;
            height: 4px;
            content: '';
            background-color: #3f8bf7;
          }
        }
      }
    }
      .list {
      margin-top: 10px;
      min-height: 470px;
      font-weight: 300;
      ul {
        > li:nth-of-type(even) {
          background: #f8f8f8;
        }
      }
      
      .row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 18px;
        height: 48px;
        color: #606060;
        .tit {
          flex: 1;
          max-width: 700px;
  
          .ellipsis;
        }
        .date {
          padding-right: 2px;
        }
      }
    }
    .enpty{
        text-align: center;
        line-height: 470px;
      }
    .pagination {
      text-align: right;
      margin-top: 50px;
    }
    }
    .news{
          display: flex;
          margin-top: 16px;
      .menu{
        position: relative;
        width: 250px;
        background-color: #fff;
        padding: 30px;
        ul{
          width: 190px;
          height: 168px;
          li{
            display: flex;
            justify-content: space-between;
            padding: 0 20px;
            width: 100%;
            height: 42px;
            line-height: 42px;
            border-bottom: 1px solid rgba(222, 225, 230, 0.8);
            font-weight: 400;
            font-size: 14px;
            cursor:pointer;
            i{
              line-height: 42px;
            }
            // &:hover{
            //   .selected
            // }
          }
          .selected{
            background: #3F8BF7;
            color:#fff;
          }
        }
        &::after {
            position: absolute;
            right: 0px;
            top: 30px;
            bottom: 92px;
            width: 1px;
            background-color: rgba(222, 225, 230, 0.8);
            content: "";
          }
      }
      .newsList{
      min-height: 635px;
      width: calc(100% - 250px);
      background-color: #fff;
      padding: 30px;
  
        .title {
      margin: 0;
      padding-left: 32px;
      font-weight: 700;
      font-size: 14px;
      color: @blue;
      display: flex;
      justify-content: space-between;
      padding-right: 36px;
      &.msg {
        background: #fff url(@icon) no-repeat center left/20px 20px;
      }
      &.bid {
        background: #fff url(@icon2) no-repeat center left/20px 20px;
      }
      &.news {
        background: #fff url(@icon3) no-repeat center left/20px 20px;
      }
      span {
        &:last-child {
          position: relative;
          &::after {
            position: absolute;
            right: -31px;
            top: 12px;
            width: 16px;
            height: 4px;
            content: '';
            background-color: #3f8bf7;
          }
        }
      }
    }
      .list {
      margin-top: 10px;
      min-height: 470px;
      font-weight: 300;
      width: 100%;
      .item{
        display: flex;
        width: 100%;
        height: 122px;
        align-items: center;
        cursor: pointer;
        .newsImg{
          width: 180px;
          height: 90px;
          img{
            width: 100%;
            height: 100%;
          }
        }
  
          .newsContent {
              flex: 1;
              overflow: hidden;
              height: 90px;
              margin-left: 16px;
              .newsTitle {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color:#000;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                margin-top: 4px;
              }
              .newsDetail {
                margin-top: 5px;
                font-weight: 500;
                font-size: 12px;
                line-height: 24px;
                color: rgba(69, 79, 89, 0.6);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                margin-top: 4px;
              }
              .newsDate{
                color: rgba(69, 79, 89, 0.6);
                font-weight: 400;
                font-size: 12px;
                margin-top: 4px;
                line-height: 24px;
                img{
                  vertical-align: text-bottom;
                }
              }
            }
        
      }
      
    }
    .enpty{
        text-align: center;
        line-height: 470px;
      }
    .pagination {
      text-align: right;
      margin-top: 50px;
    }
    }
    }
    
    
  }
  .notice-type{
      display: inline-block;
      vertical-align: middle;
      width: 18px;
      height: 18px;
      font-size: 12px;
      color: #fff;
      line-height: 18px;
      border-radius: 5px 0 5px 0;
      text-align: center;
      font-style: italic;
      text-indent: -2px;
      background: linear-gradient(180deg, #B0B0B0 14.41%, #949494 91.55%);
      &.notice-type-hot{
          background: linear-gradient(180deg, #FF8402 14.41%, #FF5C01 91.65%);
      }
  }
  .m-r-10{
    margin-right: 5px;
  }
  </style>
  