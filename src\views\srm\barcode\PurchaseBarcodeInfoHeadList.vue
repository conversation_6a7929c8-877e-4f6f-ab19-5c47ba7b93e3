<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <purchase-barcode-info-head-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <purchase-barcode-info-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>
import PurchaseBarcodeInfoHeadEdit from './modules/PurchaseBarcodeInfoHeadEdit'
import PurchaseBarcodeInfoHeadDetail from './modules/PurchaseBarcodeInfoHeadDetail'
import { ListMixin } from '@comp/template/list/ListMixin'
import { getAction } from '@/api/manage'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseBarcodeInfoHeadEdit,
        PurchaseBarcodeInfoHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeInfo',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNtFAyLFAy_2d54c7c2`, '请输入单据编号/规则编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    { 
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus', 
                        clickFn: this.handleAdd, 
                        type: 'primary',
                        authorityCode: 'barcode#info:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnWidth: 250,
                isOrder: {
                    column: 'create_time',
                    order: 'desc'
                },
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'barcode#info:detail'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.showEdit,
                        authorityCode: 'barcode#info:edit'
                    },
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制')
                        , clickFn: this.handleCopy, authorityCode: 'barcode#info:copy'},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.handleInvalid, allow: this.showInvalid, authorityCode: 'barcode#info:invalid' },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.showDelete,
                        authorityCode: 'barcode#info:delete'
                    },
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/base/barcode/purchaseBarcodeInfoHead/list',
                add: '/base/barcode/purchaseBarcodeInfoHead/add',
                delete: '/base/barcode/purchaseBarcodeInfoHead/delete',
                invalid: '/base/barcode/purchaseBarcodeInfoHead/invalid',
                deleteBatch: '/base/barcode/purchaseBarcodeInfoHead/deleteBatch',
                exportXlsUrl: '/base/barcode/purchaseBarcodeInfoHead/exportXls',
                importExcelUrl: '/base/barcode/purchaseBarcodeInfoHead/importExcel',
                columns: 'purchaseBarcodeInfoHeadList',
                copyData: '/base/barcode/purchaseBarcodeInfoHead/copyData'
            }
        }
    },
    methods: {
        // 复制功能按钮
        handleCopy (row){
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(row)
                }
            })
        },
        // 复制数据请求接口
        copyData (row){
            this.confirmLoading = true
            let param  = {id: row.id}
            getAction(this.url.copyData, param).then(res => {
                if(res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功！'))
                    this.searchEvent()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.infoNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'PurchaseBarcodeInfo', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if(row.infoStatus == 'publish') {
                return false
            }else {
                return true
            }
        },
        handleResultRecord (row) {
            this.currentEditRow = row
            this.showResultPage = true
            this.showEditPage = false
            this.showDetailPage = false
        },
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }else{
                this.showDetailPage = false
            }
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        showEdit (row) {
            return row.infoStatus == 'new' && row.auditStatus == '0' ? false : true
        },
        showInvalid (row) {
            return row.infoStatus == 'publish' ? false : true
        },
        showDelete (row) {
            return row.infoStatus == 'new' && row.auditStatus == '0' ? false : true
        },
        handleInvalid (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoid`, '确认作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQkumTotW_6af3dff4`, '是否作废此条码单？'),
                onOk: function () {
                    that.loading = true
                    getAction(that.url.invalid, {id: row.id}).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData() // 刷新页面
                        }else {
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.loading = false
                    })
                }
            })
        }
    }
}
</script>