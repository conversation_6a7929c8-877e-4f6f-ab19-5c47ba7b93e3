import { RouteView } from '@/components/layouts'

const bidEvaluationRouter = {
    path: '/projectHall/bidEvaluation',
    name: 'project_bidEvaluation',
    meta: {
        type: 'purchase',
        title: '评标',
        titleI18nKey: 'i18n_menu_UB_1154c3',
        icon: 'icon-111-02',
        keepAlive: false
    },
    component: RouteView,
    children: [
        {
            path: '/projectHall/bidEvaluation/bidResultList',
            name: 'project_bidResultList',
            meta: {
                title: '评标录入',
                titleI18nKey: 'i18n_menu_UBHN_411b0cb3',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'bidResultList' */ '@/views/srm/bidding_project/hall/purchase/bidEvaluation/BidResultList.vue')
        },
        {
            path: '/projectHall/bidEvaluation/bidOpeningList',
            name: 'project_bidOpeningList',
            meta: {
                title: '评标结果一览表',
                titleI18nKey: 'i18n_menu_UByRIBB_31e232f4',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'bidOpeningList' */ '@/views/srm/bidding_project/hall/purchase/bidEvaluation/BidOpeningList.vue')
        }
    ]
}

export default bidEvaluationRouter