<template>
  <div
    style="height:100%"
    class="PurchaseCLSealsList">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      modelLayout="seal"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <PurchaseEsignV3SealsEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <PurchaseEsignV3SealsDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <PurchaseEsignV3SealsAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <SealAuth
      v-if="showAuthPage"
      :title="sealName"
      :row="sealRow"
      :visible="showAuthPage"
      @close="showAuthPage=false">
    </SealAuth>
  </div>

</template>
<script>
// 新列表模板
import { ListMixin } from '@comp/template/list/ListMixin'
import {getAction, postAction} from '@/api/manage'
import PurchaseEsignV3SealsAdd from './modules/PurchaseEsignV3SealsAdd'
import PurchaseEsignV3SealsEdit from './modules/PurchaseEsignV3SealsEdit'
import PurchaseEsignV3SealsDetail from './modules/PurchaseEsignV3SealsDetail'
import SealAuth from '../modules/SealAuth'
export default {
    mixins: [ListMixin],
    components: {
        PurchaseEsignV3SealsEdit,
        PurchaseEsignV3SealsDetail,
        PurchaseEsignV3SealsAdd,
        SealAuth
    },
    data () {
        return {
            sealRow: null,
            showAuthPage: false,
            showAddPage: false,
            showNewRoundPage: false,
            sealName: null,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'esignV3#purchaseEsignV3Seals:add'}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCRLWeqR_511a0e32`, '公司名称/印章别名')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esignV3#purchaseEsignV3Seals:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esignV3#purchaseEsignV3Seals:add'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_lb_c74bb`, '授权'), clickFn: this.auth, allow: this.allowAuth,  authorityCode: 'esignV3#purchaseEsignV3Seals:auth'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteItem, allow: this.allowDelete, authorityCode: 'esignV3#purchaseEsignV3Seals:delete' }
                ],
                showGridLayoutBtn: false
            },
            url: {
                add: '/esignv3/purchaseEsignV3Seals/add',
                edit: '/esignv3/purchaseEsignV3Seals/edit',
                list: '/esignv3/purchaseEsignV3Seals/list',
                delete: '/esignv3/purchaseEsignV3Seals/delete'
            }
        }
    },
    methods: {
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        // 新增
        handleAdd () {
            this.showAddPage = false
            this.$nextTick(() => {
                this.showDetailPage = false
                this.showEditPage = false
                this.showAddPage = true
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        // 查看
        handleView (row) {
            this.$nextTick(() => {
                this.currentEditRow = row
                this.showDetailPage = true
                this.showEditPage = false
                this.showAddPage = false
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        // 编辑
        handleEdit (row){
            this.showEditPage = false
            this.$nextTick(() => {
                this.currentEditRow = row
                this.showDetailPage = false
                this.showEditPage = true
                this.showAddPage = false
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        // 启用
        auth (row) {
            this.sealName = row.sealName
            this.sealRow = row
            this.showAuthPage = true
        },
        // 删除
        deleteItem (row) {
            getAction(this.url.delete, {id: row.id}).then((res)=>{
                // 接口调用完查询
                if(res && res.success){
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_deleteSuccess`, '删除成功'))
                    this.searchEvent()
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        allowEdit (row){
            return row.sealStatus == '1' || row.sealStatus == '2'
        },
        allowAuth (row){
            return row.sealStatus !== '1'
        },
        enableEdit (row){
            if(row.sealId && row.operateStatus == 'DISABLE'){
                return false
            }
            return true
        },
        disabledEdit (row){
            if(row.sealId && row.operateStatus == 'ENABLE'){
                return false
            }
            return true
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        allowDelete (row){
            if(row.sealId || row.uploaded == '1'){
                return true
            }
            return false
        }
    }
}
</script>

<style lang="less" scoped>
.PurchaseCLSealsList {
    .page-container {
        :deep(.grid-box){
            overflow: auto;
            overflow-x: hidden;
            overflow-y: auto;
        }
    }
}
</style>
