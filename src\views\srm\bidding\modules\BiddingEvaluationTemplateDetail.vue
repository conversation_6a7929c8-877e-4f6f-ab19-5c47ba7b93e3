<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @loadSuccess="handleLoadSuccess" />
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal>  -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {postAction, getAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
export default {
    name: 'BiddingEvaluationTemplateDetail',
    mixins: [DetailMixin],
    components: {
        flowViewModal
    },
    data () {
        return {
            showRemote: false,
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_UBTv_411c044d`, ' 评标条例'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'biddingEvaluationTemplateItemList',
                        columns: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regulationsAnnex`, '条例附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'attachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'fbk1', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_correspondingRegulations`, '对应条例'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'), type: 'primary', click: this.cancelAudit, id: 'cancelAudit', showCondition: this.showcCncelConditionBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow, id: 'showFlow', showCondition: this.showFlowConditionBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/bidding/biddingEvaluationTemplateHead/queryById',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount
            if(!elsAccount || elsAccount==''){
                elsAccount = this.$ls.get('Login_elsAccount')
            }
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_biddingEvaluationTemplate_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        } else {
            this.showRemote = true
        }
    },
    methods: {
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        showcCncelConditionBtn (){
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            let needAudit = params.needAudit
            if(auditStatus=='1' && needAudit==='1'){
                return true
            }else{
                return false
            }
        },
        showFlowConditionBtn (){
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            let needAudit = params.needAudit
            if(!auditStatus || auditStatus==='0' || auditStatus==='' || needAudit === '0'){
                return false
            }else{
                return true
            }
        },
        cancelAudit (){
            let that = this
            this.$confirm({
                title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.auditPostData(that.url.cancelAudit)
                }
            })
        },
        auditPostData (invokeUrl){
            const _this = this
            this.$refs.detailPage.confirmLoading = true
            let formData = this.$refs.detailPage.form
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'biddingEvaluationTemplate'
            param['auditSubject'] = '评标模板审批：'+formData.evaluationNumber+' '+formData.evaluationName||''
            // param['auditSubject'] = '评标模板审批：'+formData.evaluationNumber
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    _this.$parent.cancelCallBack(formData)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.init()
                this.$refs.detailPage.confirmLoading = false
            })
        },
        showFlow (){
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            this.flowId = params.flowId
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        }
    }
}
</script>