<template>
  <div style="height:100%">
      <list-layout
      v-if="!showEditPage && !showDetailPage"
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url" />
    <!-- 表单区域 -->
    <purchase-tender-variable-library-edit
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="modalForm"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
    <purchase-tender-variable-library-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import PurchaseTenderVariableLibraryEdit from './modules/PurchaseTenderVariableLibraryEdit'
import PurchaseTenderVariableLibraryDetail from './modules/PurchaseTenderVariableLibraryDetail'
import {ListMixin} from '@comp/template/list/ListMixin'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseTenderVariableLibraryEdit,
        PurchaseTenderVariableLibraryDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'tenderVariableLibrary',
                formField: [
                    {
                        type: 'input',
                        label: srmI18n(`${getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseEnterCodeName`, '请输入编码或名称')
                    }
                ],
                form: {
                }
            },
            optColumnList: [
                { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: true, key: 'edit'},
                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: true, key: 'delete'}
            ],
            tabsList: [],
            url: {
              list: "/tender/template/purchaseTenderVariableLibrary/list",
              delete: "/tender/template/purchaseTenderVariableLibrary/delete",
              deleteBatch: "/tender/emplate/purchaseTenderVariableLibrary/deleteBatch",
              exportXlsUrl: "tender/template/purchaseTenderVariableLibrary/exportXls",
              importExcelUrl: "tender/template/purchaseTenderVariableLibrary/importExcel",
              columns: 'PurchaseTenderVariableLibraryList'
           },
        }
    },
    methods: {
    }
}
</script>