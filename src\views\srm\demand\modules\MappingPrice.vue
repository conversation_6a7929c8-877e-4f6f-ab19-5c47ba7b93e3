<template>
  <a-spin :spinning="confirmLoading">
    <div class="compare-page-container">
      <div class="page-content-right">
        <div style="text-align:right;margin-right:12px;margin-bottom:12px;margin-top: 10px;">
          <a-button
            @click="createOrder"
            type="primary"
            style="margin-left:8px"
            slot="tabBarExtraContent">{{ $srmI18n(`${$getLangAccount()}#i18n_title_orderCreate`, '创建订单') }}
          </a-button>
          <a-button
            @click="goBack"
            type="primary"
            style="margin-left:8px"
            slot="tabBarExtraContent">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}
          </a-button>
        </div>
        <div style="margin-bottom: 12px">
          <vxe-grid
            border
            ref="headerGrid"
            show-overflow
            size="small"
            height="200"
            :title="$srmI18n(`${$getLangAccount()}#i18n_title_requestItem`, '采购申请行')"
            @radio-change="radioChange"
            :radio-config="{highlight: true, reserve: true, trigger: 'row'}"
            :columns="tableHeaderColumn"
            :data="tableHeaderData"></vxe-grid>
        </div>
        <div>
          <div>{{ $srmI18n(`${$getLangAccount()}#i18n_title_priceRecord`, '价格记录') }}</div>
          <vxe-grid
            border
            ref="priceGrid"
            show-overflow
            size="small"
            height="350"
            :columns="hisTableColumn"
            :edit-config="{trigger: 'click', mode: 'cell'}"
            :data="hisTableData"></vxe-grid>
        </div>
      </div>
    </div>
  </a-spin>
</template>
<script>
import {List} from 'ant-design-vue'
import {  httpAction } from '@/api/manage'
export default {
    name: 'MappingPrice',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            headObj: {},
            priceMap: {},
            confirmLoading: false,
            listData: [],
            tableHeaderData: [],
            tableHeaderColumn: [
                { type: 'radio', width: 40, align: 'center'},
                { field: 'requestNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请号'), width: 130},
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNueber`, '申请行号'), width: 80},
                { field: 'factoryCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'), width: 80},
                { field: 'materialCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150},
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200},
                { field: 'materialGroupCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'), width: 80},
                { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_demandDate`, '需求日期'), width: 100},
                { field: 'requireQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量'), width: 100},
                { field: 'createdOrderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_createdOrderQuantity`, '已转单数量'), width: 100},
                { field: 'quantityUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityUnit`, '数量单位'), width: 100},
                { field: 'estimatePrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListd03_verifyFinishTime`, '预估单价'), width: 100},
                { field: 'currency', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), width: 80},
                { field: 'orderQuantity', fixed: 'right', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transferqQuantity`, '转单数量'), width: 100}
            ],
            hisTableColumn: [
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 120},
                { field: 'factoryCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'), width: 80},
                { field: 'materialCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150},
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200},
                { field: 'materialGroupCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'), width: 80},
                { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'), width: 100},
                //{ field: 'priceUnit', title: '价格单位', width: 100},
                { field: 'taxRate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), width: 100},
                { field: 'currency', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), width: 80},
                { field: 'effectiveDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_effectiveDate`, '生效日期'), width: 100},
                { field: 'expiryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_unableDate`, '失效日期'), width: 100},
                { field: 'quota', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quota`, '配额'), width: 100},
                { field: 'startQuotaQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_startingPoint`, '起配点'), width: 100},
                { field: 'minOrderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_minOrderQuantity`, '最小订单量'), width: 100},
                { field: 'quantity', fixed: 'right', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transferqQuantity`, '转单数量'), width: 100, editRender: {name: 'AInputNumber', events: {blur: this.computeOrderQuantity}}}
            ],
            hisTableData: []
        }
    },
    mounted () {
        this.refreshData ()
    },
    methods: {
        goBack () {
            this.$emit('hide')
        },
        refreshData (){
            this.getBaseInfo()
            this.getPriceData()
        },
        getBaseInfo () {
            this.$refs.headerGrid.loadData(this.currentEditRow)
        },
        getPriceData () {
            this.confirmLoading = true
            let param = {}
            param['purcaseRequestItemList'] = this.currentEditRow
            httpAction('/demand/purcaseRequestHead/mappingPirceData', param, 'post').then((res) => {
                if (res.success) {
                    this.listData = res.result
                    this.$refs.headerGrid.setRadioRow(this.currentEditRow[0])
                    this.radioChange({row: this.currentEditRow[0]})
                    //this.$refs.priceGrid.loadData(this.listData)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        radioChange ({row}){
            let id = row.id
            let itemList = this.priceMap[id]
            if(!itemList){
                itemList = []
                let key = row.factoryCode+row.materialCode
                for(let i in this.listData){
                    let item = Object.assign({}, this.listData[i])
                    let key2 = item.factoryCode+item.materialCode
                    if(key == key2){
                        itemList.push(item)
                    }
                }
            }
            this.$refs.priceGrid.loadData(itemList)
        },
        computeOrderQuantity ({row}){
            let head = this.$refs.headerGrid.getRadioRecord()
            let orderQuantity = 0
            let priceList = this.$refs.priceGrid.getTableData().fullData
            for(let i in priceList){
                let item = priceList[i]
                orderQuantity += item.quantity?item.quantity:0
                item['requestNumber']=head.requestNumber
                item['itemNumber']=head.itemNumber
                item['itemId']=head.id
                item['deliveryAddress']=head.deliveryAddress
                item['receiveContact']=head.receiveContact
                item['receivePhone']=head.receivePhone
                item['requireDate']=head.requireDate
                item['quantityUnit']=head.quantityUnit
            }
            //需求数量
            let requireQuantity = head.requireQuantity?head.requireQuantity:0
            //已转订单数量
            let createdOrderQuantity = head.createdOrderQuantity?head.createdOrderQuantity:0
            if(orderQuantity > (requireQuantity - createdOrderQuantity)){
                row.quantity = ''
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_totalDocTransferquantityCannotBe`, '转单数量合计不能大于需求数量与已转单数量之差！'))
                return
            }
            this.priceMap[head.id] = priceList
            let itemList = this.$refs.headerGrid.getTableData().fullData
            for(let i in itemList){
                let item = itemList[i]
                if(item.id == head.id){
                    item['orderQuantity']=orderQuantity
                }
            }
            this.$refs.headerGrid.loadData(itemList)
        },
        createOrder (){
            let param = {}
            let resultList = []
            for(let i in this.priceMap){
                let itemList = this.priceMap[i]
                for(let j in itemList){
                    let item = itemList[j]
                    if(item.quantity && item.quantity > 0){
                        resultList.push(item)
                    }
                }
            }
            if(resultList.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseFillCrrespondingTransferQuantityOrderNeedscreated`, '请在需要创建订单的行项目上填写对应的转单数量！'))
                return
            }
            param['priceMasterList']=resultList
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreate`, '创建订单'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isSureorderCreate`, '确认是否创建订单?'),
                onOk: function () {
                    that.postData(param)
                }
            })
        },
        postData (param){
            this.confirmLoading = true
            httpAction('/order/orderBuyHead/createOrderByPurchaseRequest', param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.goBack ()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 1000px;
            background-color: #fff
        }
    }
</style>