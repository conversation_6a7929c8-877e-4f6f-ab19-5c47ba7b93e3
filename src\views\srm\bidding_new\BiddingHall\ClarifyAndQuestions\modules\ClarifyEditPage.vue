<template>
  <div class="page-container">
    <a-spin :spinning="confirmLoading">
      <div
        class="edit-page"
        v-show="!toBiddingFile" >
        <div class="page-content">
          <a-form-model
            ref="baseForm"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            :rules="rules"
            :model="formData">
            <div class="deliveryTime">
              <div class="deliveryTime-title">
                {{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_LVtnJKI_b7736b79`, '澄清及递交时间')) }}
              </div>
              <div class="deliveryTime-content">
                <a-row >
                  <a-col :span="12">
                    <a-form-model-item
                      :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_QILVyRKI_218968c6`, '文件澄清截止时间'))"
                      required
                      prop="fileClarificationEndTime">
                      <a-date-picker
                        v-if="!check"
                        style="width:90%"
                        show-time
                        valueFormat="YYYY-MM-DD HH:mm"
                        v-model="formData.fileClarificationEndTime"/>
                      <span v-else>{{ formData.fileClarificationEndTime }}</span>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-model-item
                      :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_QInJyRKI_f4bdea17`, '文件递交截止时间'))"
                      required
                      prop="fileSubmitEndTime">
                      <a-date-picker
                        v-if="!check"
                        style="width:90%"
                        show-time
                        valueFormat="YYYY-MM-DD HH:mm"
                        v-model="formData.fileSubmitEndTime" />
                      <span v-else>{{ formData.fileSubmitEndTime }}</span>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-model-item
                      :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_title_bidOpenTime`, '开标时间'))"
                      prop="openBiddingTime"
                      required>
                      <a-date-picker
                        v-if="!check"
                        style="width:90%"
                        show-time
                        valueFormat="YYYY-MM-DD HH:mm"
                        v-model="formData.openBiddingTime"/>
                      <span v-else>{{ formData.openBiddingTime }}</span>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </div>
            </div>
            <div class="clarification">
              <div class="clarification-title">
                {{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_QILV_2f5d3190`, '文件澄清')) }}
              </div>
              <div class="clarification-content">
                <a-row >
                  <a-col :span="12">
                    <a-form-model-item
                      :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题'))"
                      required
                      prop="title">
                      <a-input
                        v-model="formData.title"
                        style="width:90%"
                        v-if="!check"></a-input>
                      <span v-else>{{ formData.title }}</span>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row >
                  <a-col :span="12">
                    <a-form-model-item
                      :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_LVCc_34586d95`, '澄清内容'))"
                      prop="content"
                      required>
                      <a-textarea
                        v-if="!check"
                        style="width:90%"
                        v-model="formData.content"
                        :auto-size="{ minRows: 2, maxRows: 6 }"></a-textarea>
                      <span v-else>{{ formData.content }}</span>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <!-- v-if="formData." -->
                <a-row v-if="this.currentSubPackage().evalutionType == '1' || this.currentSubPackage().bidOpenType == '1'">
                  <a-col :span="12">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_YBQIAH_99eb0b37`, '招标文件变更')">
                      <a-button
                        type="link"
                        @click="toBidFile">{{ $srmI18n(`${$getLangAccount()}#i18n_field_AtYBQI_f2a0e596`, '编辑招标文件') }}</a-button>
                      <span
                        v-if="ifAlter"
                        style="color: orange">
                        ({{ $srmI18n(`${$getLangAccount()}#i18n_field_Icr_16abc7d`, '已修改') }})
                        <a
                          style="margin-left: 20px"
                          @click="clearChange">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VGAHCc_2c8d036f`, '清除变更内容') }}</a>
                      </span>
                      <span
                        v-else
                        style="color: orange">({{ $srmI18n(`${$getLangAccount()}#i18n_field_Lcr_18d57b5`, '未修改') }})</span>
                    </a-form-model-item>
                  </a-col>
                </a-row>

                <a-row v-if="ifAlter">
                  <a-col :span="12">
                    <a-form-model-item
                      :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_InJRdXGvCp_2582d7a`, '已递交供应商处理方案'))">
                      <a-radio-group
                        :disabled="check"
                        v-model="formData.supplierHadResponseHandlerType">
                        <a-radio
                          :style="radioStyle"
                          value="0">
                          {{ $srmI18n(`${$getLangAccount()}#i18n_field_hGJOYMeBQI_43719486`, '发出自动退回投标文件') }}
                        </a-radio>
                        <a-radio
                          :style="radioStyle"
                          value="1">
                          {{ $srmI18n(`${$getLangAccount()}#i18n_field_HDKRdXJcqM_a945dd9a`, '仅提示供应商自行撤回') }}
                        </a-radio>
                      </a-radio-group>
                    </a-form-model-item>
                  </a-col>
                </a-row>

                <a-row >
                  <a-col :span="12">
                    <a-form-model-item
                      :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_title_accessory`, '附件'))"
                    >
                      <div
                        class="dropbox">
                        <a-upload
                          name="file"
                          v-if="formData.id && !check"
                          :multiple="true"
                          :showUploadList="false"
                          :action="uploadUrl"
                          :headers="uploadHeader"
                          :accept="accept"
                          :data="{headId: formData.id, businessType: 'biddingPlatform', 'sourceNumber': tenderCurrentRow.tenderProjectNumber || tenderCurrentRow.id || '', actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'}"
                          :beforeUpload="beforeUpload"
                          @change="handleUploadChange"
                        >
                          <a-button
                            v-if="(formData.status != '1' && formData.status != '2')"
                            type="primary"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                        </a-upload>
                        <a-button
                          v-else
                          type="primary"
                          @click="beforeUpload"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                        <div
                          v-for="(fileItem, index) in formData.purchaseAttachmentList"
                          :key="fileItem.id">
                          <span style="color: blue; cursor:pointer; margin-right:8px" >{{ fileItem.fileName }}</span>
                          <a-icon
                            type="delete"
                            v-if="!check"
                            style="margin-right:8px"
                            @click="handleDeleteFile(index)"/>
                          <a
                            style="margin-right:8px"
                            @click="preViewEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                          <a
                            @click="downloadEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>    
                        </div>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-form-model>
        </div>
        <div class="page-footer" >
          <a-button
            @click="handleSave"
            type="primary"
            v-if="!check">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
          <a-button
            @click="submit"
            type="primary"
            v-if="!check">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
          <a-button @click="() => {this.$emit('hide')}">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
        </div>
        <a-modal
          v-model="visible"
          :title="$srmI18n(`${$getLangAccount()}#i18n_field_RLc_1d56610`, '确认框')"
          @ok="handleOk">
          {{ $srmI18n(`${$getLangAccount()}#i18n_field_RLSVGvmLVIYBQIjcrCcVRIKQtT_39fe0d30`, '确认后清除本次澄清对招标文件的修改内容，请确定是否继续') }}
        </a-modal>
      </div>
      <BiddingFile 
        :check="check"
        v-if="toBiddingFile"
        :row="formData"
        @hide="handleHide"></BiddingFile>
    </a-spin>
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { USER_INFO } from '@/store/mutation-types'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
import BiddingFile from '../components/BiddingFile.vue'
export default {
    mixins: [baseMixins],
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        check: {
            type: Boolean,
            default: ()=> {
                return false
            }
        }
    },
    components: {
        BiddingFile
    },
    data () {
        return {
            radioStyle: {
                display: 'inline-block'
                // height: '30px',
                // lineHeight: '30px'
            },
            visible: false,
            biddingData: {},
            toBiddingFile: false,
            labelCol: { span: 8 },
            wrapperCol: { span: 16 },
            confirmLoading: false,
            formData: {},
            rules: {
                fileClarificationEndTime: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_QILVyRKIxOLVW_bdaf9feb`, '文件澄清截止时间不能为空!')
                }],
                fileSubmitEndTime: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_QInJyRKIxOLVW_765c493a`, '文件递交截止时间不能为空!')
                }],
                openBiddingTime: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_vBKIxOLVW_cb5d460c`, '开标时间不能为空!')
                }],
                title: [
                    {required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LVBDxOLVW_ce2de7ff`, '澄清标题不能为空！')},
                    { max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')}
                ],
                content: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LVCcxOLVW_48de447c`, '澄清内容不能为空!')
                }]
            },
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            requestData: {
                detail: { url: '/tender/purchaseTenderClarificationInfo/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            url: {
                add: '/tender/purchaseTenderClarificationInfo/add',
                edit: '/tender/purchaseTenderClarificationInfo/edit',
                submit: '/tender/purchaseTenderClarificationInfo/submit'
            }
        }
    },
    inject: [
        'subpackageId',
        'tenderCurrentRow', 
        'resetCurrentSubPackage',
        'currentSubPackage'
    ],
    computed: {
        subId () {
            return this.subpackageId
        },
        ifAlter () {
            return this.formData.biddingDocuments == '1'
        }
    },
    methods: {
        handleHide (params) {
            let {biddingData} = params
            this.toBiddingFile = false
            this.biddingData = biddingData
            this.queryDetail()
        },
        clearChange () {
            if(this.check){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_APzExqtk_128cab96`, '当前状态不可操作'))
            }else{
                this.visible = true
            }
        },  
        handleOk () {
            this.confirmLoading = true
            let url = '/tender/purchaseTenderClarificationInfo/change/attachment/clear'
            let {id, subpackageId} = this.formData
            getAction(url, {id, subpackageId})
                .then(res => {
                    if (res.success) {
                        this.visible = false
                        this.$message.success(res.message)
                        this.queryDetail()
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        async toBidFile () {
            if(!this.formData.status || this.formData.status == '0'){
                await this.handleSave()
            }
            this.$ls.set('changeBidFile', true)
            this.$ls.set('clarificationId', this.currentEditRow.id)
            this.toBiddingFile = true
        },  
        async handleSave (callBack = null) {
            let url = this.formData.id ? this.url.edit : this.url.add
            let params
            // 拿到项目id和分包id
            this.formData.tenderProjectId=this.tenderCurrentRow.id
            this.formData.subpackageId=this.subId()
            params = Object.assign({}, this.formData)
            valiStringLength(params, [
                {field: 'content', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVCc_34586d95`, '澄清内容'), maxLength: 100},
                {field: 'title', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题'), maxLength: 100}
            ])
            this.confirmLoading = true
            let requestStatus = 0
            await postAction(url, params).then(res => {
                if (res.success) {
                    requestStatus = 1
                    if (!this.formData.id) {
                        this.$set(this.formData, 'id', res.result.id)
                        // inCurrentEditRow表示：当前行的id值
                        this.$set(this.currentEditRow, 'id', res.result.id)
                    }
                    this.$message.success(res.message)
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
                if (typeof callBack == 'function' && requestStatus == 1) {
                    callBack()
                }
            })
        },
        submit (){
            let that = this
            if(this.ifAlter){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YBQIIAHVVVGRVRu_a20bf717`, '招标文件已变更，请重新设置控制价'))
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布?'),
                // cancelButtonProps: { style: { display: 'none' }}, // 隐藏取消按钮
                onOk: function () {
                    that.$refs.baseForm.validate(valid => {
                        if (valid) {
                            that.handleSubmit()
                        }
                    })
                }
            })
        },
        handleSubmit () {
            this.confirmLoading = true
            this.formData.subpackageId=this.subId()
            this.formData.tenderProjectId=this.tenderCurrentRow.id
            let params = Object.assign({}, this.formData)
            // let {purchaseAttachmentList, title, content, fileClarificationEndTime, fileSubmitEndTime, openBiddingTime} = this.formData
            // if((JSON.stringify(this.biddingData) != '{}')){
            //     params = Object.assign({}, this.biddingData, {purchaseAttachmentList, title, content, fileClarificationEndTime, fileSubmitEndTime, openBiddingTime})
            // }else{
            //     params = Object.assign({}, this.formData)
            // }
            console.log('submit params:', params)
            valiStringLength(params, [
                {field: 'content', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVCc_34586d95`, '澄清内容'), maxLength: 1000},
                {field: 'title', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题')}
            ])
            postAction(this.url.submit, params).then(res => {
                if (res.success) {
                    // this.$emit('resetCurrentSubPackage') || ''
                    this.resetCurrentSubPackage()
                    this.$message.success(res.message)
                    this.$emit('hide')
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        async queryDetail () {
            if (this.requestData.detail) {
                let url = this.requestData.detail.url
                let args = this.requestData.detail.args(this)
                args.subpackageId = this.subId()
                // 有id 才能请求,新建时是没有id的，不用请求查询接口
                if (args && args.id) {
                    this.confirmLoading = true
                    let query = await getAction(url, args)
                    this.confirmLoading = false
                    if (query && query.success) {
                        console.log(query.result)
                        this.formData = Object.assign({}, query.result)
                        this.nodeListData = this.formData.tenderEvaluationTemplateItemVoList
                    } else {
                        this.$message.error(query.message)
                    }
                }else{
                    this.formData = this.currentEditRow
                    console.log('this.formData', this.formData)
                }
            }
        },
        beforeUpload () {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
                return false
            }
            if(this.check){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_APzExqtk_128cab96`, '当前状态不可操作'))
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if(this.formData.id){
                if (file.status === 'done') {
                    if (file.response.success) {
                        let {fileName, filePath, fileSize, id, headId} = file.response.result
                        let fileListData = {
                            uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                            uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName, filePath, fileSize, id, headId
                        }
                        if (!this.formData.purchaseAttachmentList) this.$set(this.formData, 'purchaseAttachmentList', [])
                        this.formData.purchaseAttachmentList.push(fileListData)
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                }
            }
        },
        handleDeleteFile (index) {
            this.formData.purchaseAttachmentList.splice(index, 1)
        },
        async downloadEvent (row) {
            console.log('row', row)
            row.subpackageId = this.subId()
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.subId()
            this.$previewFile.open({params: row })
        }
    },
    mounted () {
        this.queryDetail()
    }
}
</script>
<style lang="less" scoped>
.deliveryTime-title, .clarification-title{
  padding: 8px;
  background: #F2F3F5;
}
.edit-page{
    height: 95%;
}
</style>


