<template>
  <div
    style="height: 650px"
    v-if="this.subId">
    <titleTrtl v-if="!showDetailList">
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBLV_411d1f04`, '评标澄清') }}</span>
    </titleTrtl>
    <listTable 
      ref="listTable"
      v-show="!showDetailList"
      :pageData="pageData"
      :url="url"
      :statictableColumns="tableColumns"
      :showTablePage="true"
    />
    <BidEvaluationClarifyDetail
      v-if="showDetailList"
      :check="check"
      ref="supplierCapacity"
      :current-edit-row="currentEditRow"
      @hide="hidePage"
    />
  </div>
</template>
<script>
// import { ListMixin } from '@comp/template/list/ListMixin'
import listTable from '../BiddingHall/components/listTable'
import BidEvaluationClarifyDetail from './modules/SaleBidEvaluationClarifyDetail'
import { postAction, getAction } from '@/api/manage'
import titleTrtl from '../BiddingHall/components/title-crtl'

export default {
    // mixins: [ListMixin],
    components: {
        BidEvaluationClarifyDetail,
        listTable,
        titleTrtl
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    data () {
        return {
            showDetailList: false,
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    { type: 'reply', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reply`, '回复'), clickFn: this.handleReply, allow: this.allowReply},
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_view`, '查看'), clickFn: this.handleView}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            tableColumns: [
                {
                    'type': 'seq',
                    // 'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题'),
                    'field': 'clarificationTitle'
                    // 'width': 250
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVyRKI_87cc05b7`, '澄清截止时间'),
                    'field': 'fileClarificationEndTime'
                    // 'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText'
                    // 'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    // width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            url: {
                // 列表数据展示
                list: '/tender/clarification/saleTenderEvaClarificationHead/list?subpackageId='+this.subpackageId()+'&checkType='+this.currentSubPackage().checkType,
                columns: 'tenderEvaClarificationList'
            }
        }
    },
    methods: {
        // 返回按钮，隐藏页面，返回list列表
        hidePage (){
            this.showDetailList = false
            this.$refs.listTable.loadData()
        },
        // 回复
        handleReply (row){
            this.currentEditRow = row
            this.showDetailList = true
            this.check = false
        },
        // 详情
        handleView (row){
            this.currentEditRow = row
            this.check = true
            this.showDetailList = true
        },
        //允许回复
        allowReply (row){
            let time = new Date(row.fileClarificationEndTime).getTime()
            // 待回复同时当前时间小于截止时间才可回复
            return !(row.status == '0' && Date.now() < time)
        }
    }
    
}
</script>
<style lang="less" scoped>
    :deep(.vxe-table--render-default .vxe-body--column.col--ellipsis>.vxe-cell){
        max-height: none;
    }
     :deep(.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis>.vxe-cell){
        max-height: none;
     }
</style>