<template>
  <div
    :style="pageStyle"
    ref="page">
    <!-- 列表界面 -->
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showAuditDetail"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url"
      :tabsList="tabsList"
      :tabCurrent="tabCurrent"
      @afterChangeTab="handleAfterChangeTab">
      
      <!-- 覆盖模板tab内容 -->
      <template
        #tabs
        v-if="toggleCardMode">
        <div></div>
      </template>

      <template
        #query_extra>
        <a-tooltip
          :title="toggleCardMode ? $srmI18n(`${$getLangAccount()}#i18n_alert_ABCK_275e29ff`, '列表模式') : $srmI18n(`${$getLangAccount()}#i18n_alert_mOCK_27a11a94`, '卡片模式')">
          <a-button
            :style="{ 'marginLeft': toggleCardMode ? '0' : '10px'}"
            @click="handleToggleCardMode"><icon-font :type="toggleCardMode ? 'icon-list' : 'icon-card'" /></a-button>
        </a-tooltip>
      </template>

      <template
        #grid="{ activeKey, tabsList, columns, tableData, loadDataFunc, pagerConfig, getNewRouterFunc, loading }"
        v-if="toggleCardMode">
        <a-spin
          :spinning="loading"
          :delayTime="300">
          <card-style-list-layout
            :activeKey="activeKey"
            :tabsList="tabsList"
            :columns="columns"
            :tableData="tableData"
            :pagerConfig="pagerConfig"
            :buttons="pageData.optColumnList"
            @sidebar-click="(payload) => handleSidebarClick(payload, pagerConfig, loadDataFunc)"
            @card-approve="({ row }) => showDetail(row)"
            @card-chat="({ row }) => handleChat(row)"
            @card-link="({ col, column }) => handleExtendLink(col, column, getNewRouterFunc)"
            @page-change="(payload) => handlePageChange(payload, pagerConfig, loadDataFunc)"
          />
        </a-spin>
      </template>

    </list-layout>

    <!-- 表单区域 -->
    <audit-detail-controller
      :current-edit-row="currentEditRow"
      v-if="showAuditDetail"></audit-detail-controller>
    <a-modal
      v-drag
      v-if="showModal"
      class="actionModal"
      v-bind="{ ...modalForm }"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div class="modalCtn">
        <agreeModal
          ref="agreeModal"
          :taskInfoList="taskInfoList"
          @success="modalFormSuccess"
          v-if="alias == 'agree'"
        />
        <backToNodeModal
          ref="backToNodeModal"
          :taskInfoList="taskInfoList"
          @success="modalFormSuccess"
          v-if="alias == 'backToNode'"
        />
        <transfer-task-modal
          ref="transferModal"
          :taskIds="taskIds"
          @success="modalFormSuccess"
          v-if="alias == 'transfer'"
        ></transfer-task-modal>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import layIM from '@/utils/im/layIM.js'
import AuditDetailController from '@/views/srm/workFlow/AuditDetailController'
import { REPORT_ADDRESS } from '@/utils/const.js'
import agreeModal from './modal/bacthAgreeModal'
import backToNodeModal from './modal/bacthBackToNodeModal'

import cardStyleListLayout from '@/views/srm/bpm/components/cardStyleListLayout/index.vue'
import { todoTempMixin } from '@/views/srm/bpm/mixin/todoTempMixin.js'
import TransferTaskModal from './modal/transferTaskModal'

export default {
    name: 'AuditTodoList',
    mixins: [ListMixin, todoTempMixin],
    inject: ['routeReload'],
    components: {
        AuditDetailController,
        agreeModal,
        backToNodeModal,
        TransferTaskModal,
        'card-style-list-layout': cardStyleListLayout
    },
    data () {
        return {
            showEditPage: false,
            showAuditDetail: false,
            superQueryShow: false,
            currentRow: {},
            subAccountList: [],
            currentUrl: '',
            auditHisData: [],
            pageData: {
                superQueryShow: false,
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: false, clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRUz_2ef233ee`, '批量审批'), type: 'primary', icon: 'file', folded: false, clickFn: this.batchComplete, authorityCode: 'bpmn#auditAPI:batchComplete'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRYM_2ef87fd4`, '批量退回'), type: 'primary', icon: 'file', folded: false, clickFn: this.batchReject, authorityCode: 'bpmn#auditAPI:batchReject'},
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zRsr_2ef869a8`, '批量转办'),
                        folded: false,
                        type: 'primary',
                        icon: 'file',
                        clickFn: this.batchTransfer,
                        authorityCode: 'bpmn#auditAPI:batchTransferTask'
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processTheTheme`, '流程主题'),
                        fieldName: 'subject',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processTheTheme`, '流程主题')
                    }
                    // {
                    //     type: 'select',
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型'),
                    //     fieldName: 'businessType',
                    //     dictCode: 'srmAuditBussinessType',
                    //     placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型')
                    // }
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_examineApprove`, '审批'), clickFn: this.showDetail, authorityCode: 'bpmn#auditAPI:complete', attrs: { type: 'primary' }, event: 'approve' },
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, authorityCode: 'bpmn#auditAPI:chat', event: 'chat'}
                    
                    // {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印'),  clickFn: this.print, allow: this.showPrint}
                ],
                optColumnWidth: 400,
                form: {
                    keyWord: ''
                },
                isOrder: {
                    column: 'createDate',
                    order: 'desc'
                }
            },
            url: {
                list: '/a1bpmn/audit/api/runtime/task/list',
                columns: 'bpmnTodoList'          
            },
            alias: '',
            moadlName: '',
            modalForm: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                confirmLoading: false,
                visible: false
            },
            task: {
                action: ''
            },
            showModal: false,
            taskInfo: {},
            taskInfoList: []
        }
    },
    mounted () {
        this.serachCountTabs('/flow/tab/api/runtime/task/bpmn/tabs/count')
    },
    beforeDestroy () {
        console.log('beforeDestroy')
    },
    destroyed () {
        console.log('beforeDestroy')
    },
    activated () {
        console.log('activated')
        // 重置任务按钮
        let {taskId} = this.$route.query
        if (!taskId) {
            this.$store.commit('SET_TASKBTN', [])
            this.$store.commit('SET_TASK', {})
        }
    },
    watch: {
        '$route.query': {
            immediate: true,
            handler () {
                this.getUrlParam()
            }
        }
    },
    methods: {
        modalFormSuccess () {
            this.modalForm.visible = false
            this.task.action = ''
            this.alias = ''
            this.showModal = false
            this.reloadAuditList()
        },
        showPrint (row) {
            if (row.bizType == 'order') {
                return false
            }
            return true
        },
        getUrlParam () {
            let {taskId, businessType, businessId, processInstanceId, subject} = this.$route.query
            if(businessId && businessType && taskId){
                let row = {
                    taskId,
                    businessType,
                    id: businessId,
                    businessId,
                    processInstanceId,
                    subject
                }
                this.showAuditDetail = false
                this.showEditPage = false
                this.$store.commit('SET_TASK', {
                    taskId: row.taskId,
                    businessType: row.businessType,
                    id: row.businessId,
                    processInstanceId: row.processInstanceId || ''
                })
                this.$store.commit('SET_TASKBTN', [])
                this.$nextTick(()=> {
                    this.currentEditRow = row
                    console.log(this.currentEditRow, 'this.currentEditRow====')
                    this.showAuditDetail = true
                    this.showEditPage = false
                    this.$store.dispatch('SetTabConfirm', true)
                })
            }
        },
        showDetail (row) {
            this.currentEditRow = Object.assign({}, row)
            this.currentEditRow['taskId'] = this.currentEditRow.id
            this.currentEditRow['id'] = row.bizKey
            this.currentEditRow['businessId'] = row.bizKey
            this.currentEditRow['businessType'] = row.bizType
            // this.$store.commit('SET_TASKID', this.currentEditRow.taskId)
            this.$store.commit('SET_TASK', {
                taskId: this.currentEditRow.taskId,
                processInstanceId: this.currentEditRow.processInstanceId,
                businessType: row.bizType,
                id: row.bizKey
            })
            const query = {
                taskId: this.currentEditRow.taskId,
                businessType: row.bizType,
                businessId: row.bizKey,
                processInstanceId: this.currentEditRow.processInstanceId,
                subject: this.currentEditRow.subject
            }
            this.$router.push({ query })
            this.showAuditDetail = true
        },
        hideDetail () {
            this.showAuditDetail = false
            this.$store.dispatch('SetTabConfirm', false)
            this.reloadAuditList()
            // this.serachCountTabs('/flow/tab/api/runtime/task/bpmn/tabs/count')
            this.$router.replace({path: this.$route.path})
        },
        reloadAuditList () {
            this.$refs.listPage.handleQuery()
        },
        handleChat (row) {
            let { id } = row
            layIM.creatGruopChat({id, type: 'auditProcessAllAuditUserApply', url: this.url || '', recordNumber: id })
        },
        print (row) {
            const token = this.$ls.get('Access-Token')
            //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
            const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:order_println.ureport.xml&id='+row.bizKey + '&token=' + token
            window.open(url, '_blank')
        },
        getCheckboxRecords () {
            let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords() || []
            return  selectedRows
        },
        batchComplete () {
            this.taskInfoList = this.getCheckboxRecords()
            if (this.taskInfoList.length<=0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            this.showModal = true
            this.alias = 'agree'
            this.moadlName = 'agreeModal'
            this.modalForm.visible = true
        },
        batchReject () {
            this.taskInfoList = this.getCheckboxRecords()
            if (this.taskInfoList.length<=0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            this.showModal = true
            this.alias = 'backToNode'
            this.moadlName = 'backToNodeModal'
            this.modalForm.visible = true
        },
        handleOk () {
            this.$refs[this.moadlName].handleConfirm(this.taskIds)
        },
        handleCancel () {
            this.modalForm.visible = false
            this.task.action = ''
            this.alias = ''
        },
        // 批量转办
        batchTransfer (){
            this.taskIds = this.getCheckboxRecords().map(item => item.id) || []
            if (this.taskIds.length<=0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            this.alias = 'transfer'
            this.moadlName = 'transferModal'
            this.showModal = true
            this.modalForm.visible = true
        }
    }
}
</script>

<style lang="less" scoped>
.actionModal {
    .modalCtn{
        overflow: auto;
    }
}
</style>

