<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <span
        v-if="subpackageTitle"
        style="position:fixed;margin:20px"><a-tag
          color="blue"
          style="font-size:16px">{{ subpackageTitle }}</a-tag></span>
      <a-page-header
      >
        <template slot="extra">
          <taskBtn
            v-if="taskInfo.taskId"
            :currentEditRow="currentEditRow"
            :pageHeaderButtons="publicBtn"
            v-on="$listeners"/>
        </template>
      </a-page-header>
      <div
        class="container"
        :style="style">
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBLRt_7e1f3f68`, '中标人名单') }}</span>
          </titleTrtl>
          <listTable
            ref="listTable"
            :setGridHeight="250"
            pageStatus="detail"
            :fromSourceData="tableData"
            :showTablePage="false"
            :statictableColumns="tableColumns"
          ></listTable>
        </div>
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_basicInfo`, '基本信息') }}</span>
          </titleTrtl>
          <div>
            <Dataform
              v-if="dataReady"
              ref="dataform"
              :formData="formData"
              pageStatus="detail"
              :fields="fields" />
          </div>
          <div>
            <j-editor
              ref="ueditor"
              disabled
              v-model="formData.noticeContent" />
          </div>
        </div>
      </div>
    </a-spin>
    <a-modal
      v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '../components/content-header'
import Dataform from '../components/Dataform'
import titleTrtl from '../components/title-crtl'
import listTable from '../components/listTable'
import {getAction, postAction, httpAction} from '@/api/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import JEditor from '@/components/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import { mapGetters } from 'vuex'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
export default {
    name: 'BidWinningAnnouncement',
    components: {
        JEditor,
        Dataform,
        titleTrtl,
        ContentHeader,
        listTable,
        flowViewModal,
        taskBtn
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    mixins: [tableMixins],
    data () {
        return {
            subpackageTitle: '',
            dataReady: false,
            auditVisible: false,
            currentUrl: '',
            opinion: '',
            okText: '',
            flowView: false,
            confirmLoading: false,
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHf_252229e6`, '中标金额'),
                    field: 'winnerAmount',
                    width: '100'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    'field': 'scopeSort',
                    width: '80'
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            fields: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRx_2fb94a75`, '是否公告'),
                    fieldLabelI18nKey: '',
                    field: 'notice_dictText',
                    defaultValue: '0',
                    fieldType: 'input',
                    dictCode: 'yn'
                },  
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxvKKI_c12c1427`, '公告开始时间'),
                    fieldLabelI18nKey: '',
                    field: 'noticeStartTime',
                    dataFormat: 'YYYY-MM-DD HH:mm:ss',
                    defaultValue: '',
                    fieldType: 'dateTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxyRKI_c2de5094`, '公告截止时间'),
                    fieldLabelI18nKey: '',
                    field: 'noticeEndTime',
                    defaultValue: '',
                    dataFormat: 'YYYY-MM-DD HH:mm:ss',
                    fieldType: 'dateTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxBD_264cc24f`, '公告标题'),
                    fieldLabelI18nKey: '',
                    field: 'noticeTitle',
                    defaultValue: '',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFRxvL_6da70fe6`, '请选择公告范围'),
                    fieldLabelI18nKey: '',
                    field: 'noticeRange_dictText',
                    defaultValue: '',
                    dictCode: 'srmNoticeScope',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQUz_2fba950f`, '是否审批'),
                    fieldLabelI18nKey: '',
                    field: 'audit',
                    fieldType: 'select',
                    dictCode: 'yn'
                }
                // {
                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFRxIr_6da41d13`, '请选择公告模板'),
                //     fieldLabelI18nKey: '',
                //     field: 'noticeTemplate',
                //     defaultValue: '',
                //     fieldType: 'selectModal',
                //     curConfig: {
                //         modalColumns: [
                //             {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150}, 
                //             {field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_xat0lPoJ`, 'e签宝账户'), with: 150}, 
                //             {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150}
                //         ],
                //         selectModel: 'single',
                //         modalUrl: '/esign/elsSubaccountCertificationInfo/list', modalParams: {orgCreateFlag: '1'},
                //         bindFunction: (vue, data) =>{
                //             console.log(vue, data)
                //         }
                //     }
                // }
            ],
            publicBtn: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    type: 'primary',
                    click: this.auditPass,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    type: '',
                    click: this.auditReject,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    type: '',
                    click: this.showFlow,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'rollBack',
                    click: this.goBackAudit,
                    showCondition: () => {
                        return true
                    }
                }
            ],
            url: {
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryById: '/tender/purchaseTenderProjectBidWinningAffirmNotice/queryInfoById'
            }
        }
    },
    computed: {
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        ...mapGetters([
            'taskInfo'
        ])
    },
    methods: {
        async getData () {
            let params = {
                id: this.currentEditRow.businessId
            }
            try {
                this.confirmLoading = true

                let res2 = await getAction(this.url.queryById, params)
                if (res2.code == 200 && res2.result) {
                    let {bidWinningAffirmList = [], ...others} = res2.result || {}
                    this.formData = others
                    bidWinningAffirmList && bidWinningAffirmList.forEach(item => {
                        item.saleQuoteColumnVOS.forEach(vos => {
                            item[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                            item.affirm = item.affirm == '1' ? true : false
                        })
                    })
                    this.tableData = bidWinningAffirmList
                    console.log(this.tableData)
                }


                const res = await getAction(this.url.queryPrice, {subpackageId: this.formData.subpackageId})
                if (res.code == 200 && res.result) {
                    // 分项报价且线上评标的时候，或者线下的时候去掉排名
                    if ((res2.result.quoteType == '1' && res2.result.evaluationType =='1') || (res2.result.quoteType == null)) {
                        this.tableColumns = this.tableColumns.filter(item=>{
                            return item['field'] != 'scopeSort'
                        })
                    } else {
                        // this.tableColumns.splice(1, 0, {
                        //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                        //     field: 'evaPrice'
                        // })
                        // 否则，其他情况就附带上排名以及各个投标函的投标报价列信息
                        const resultData = res.result
                        let columns = []
                        resultData.forEach(data => {
                            let obj = {
                                title: data.title,
                                children: []
                            }
                            let columnChildren = []
                            data.quoteColumnList.forEach(column => {
                                column['field'] = `${column['field']}_${data['id']}`
                                columnChildren.push(column)
                            })
                            obj.children = columnChildren
                            columns.push(obj)
                        })
                        console.log('columns', columns)
                        // 如果是线下的情况，则替换投标报价字段field
                        if(res2.result.evaluationType == '2'){
                            console.log('jinlaile')
                            columns.forEach(item=>{
                                item.children.forEach(item2=>{
                                    console.log(item2)
                                    item2.field = 'evaPrice'
                                })
                            })
                        }
                        console.log('columns', columns)
                        this.tableColumns = this.tableColumns.filter(column => {
                            if (!column.hasOwnProperty('children')) {
                                return column
                            }
                        })
                        this.tableColumns.splice(3, 0, ...columns)
                    }
                }

            } catch (error) {
                console.log(error)
            }
            this.confirmLoading = false
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
            this.dataReady = true
        },
        /**
     * 审批方法
     */
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass () {
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject () {
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk () {
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = 'purchaseProduct'
            param['businessId'] = this.currentEditRow.businessId
            param['taskId'] = this.currentEditRow.taskId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            this.confirmLoading = true
            postAction(this.currentUrl, param)
                .then(res => {
                    if (res.success) {
                        this.auditVisible = false
                        this.$message.success(res.message)
                        this.$parent.reloadAuditList()
                        this.goBackAudit()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    mounted () {
        this.subpackageTitle = this.currentEditRow.subject || ''
        this.init()
    }
}
</script>
<style lang="less" scoped>
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
</style>




