<template>
  <!-- 此页面为【询价大厅】，未共用 -->
  <div class="purchase-enquiry-hall">
    <a-spin :spinning="spinning">
      <div class="container">
        <a-row
          class="page-header-button"
          justify="end"
          type="flex"
        >
          <enquiry-hall-button :buttons="pageHeaderButton" />
        </a-row>
        <div class="content">
          <div class="gutter">
            <a-row class="price page-header-title">
              <a-col
                class="title__col"
                v-for="title in pageHeaderTitle"
                :key="title.value"
                :span="8"
              >
                <span class="title__col-label">{{ $srmI18n(`${$getLangAccount()}#${title.i18n}`, title.label) }}:</span>
                <span
                  class="title__col-value"
                  :class="title.value == 'quoteEndTime' ? 'red' : ''"
                  >{{ form[title.value] }}</span
                >
              </a-col>
            </a-row>
          </div>
          <div class="gutter">
            <multipane
              class="custom-resizer"
              layout="vertical"
            >
              <div class="item material">
                <div style="min-width: 358px; margin-bottom: 10px">
                  <a-button
                    v-if="showReplenishMaterial"
                    @click="handleSupplement"
                    >{{ $srmI18n(`${$getLangAccount()}#i18n_title_supplementaryMaterialCode`, '补充物料编码') }}</a-button
                  >
                  <!-- 新增状态筛选 -->
                  <a-select
                    v-model="materialStatus"
                    :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectStatus`, '请选择状态')"
                    style="width: 90px; margin-left: 10px; vertical-align: top"
                    @change="handleFilter"
                    allowClear
                  >
                    <a-select-option value="99">{{ $srmI18n(`${$getLangAccount()}#i18n_title_pending`, '待处理') }}</a-select-option>
                    <a-select-option value="4">{{ $srmI18n(`${$getLangAccount()}#i18n_title_accept`, '接受') }}</a-select-option>
                    <a-select-option value="5">{{ $srmI18n(`${$getLangAccount()}#i18n_title_refuse`, '拒绝') }}</a-select-option>
                  </a-select>
                  <!-- 关键字搜索 -->
                  <a-input
                    @input="
                      (e) => {
                        this.keywords = e.target.value
                      }
                    "
                    allowClear
                    :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNSLAoSSLRL_1a16ff85`, '请输入物料编码或物料名称')"
                    style="width: 238px; margin-left: 10px"
                    @pressEnter="handleFilter"
                  >
                    <a-icon
                      slot="addonAfter"
                      @click="handleFilter"
                      type="search"
                    />
                  </a-input>
                </div>
                <div class="material-table">
                  <vxe-grid
                    v-bind="gridConfig"
                    :checkbox-config="{ trigger: 'cell' }"
                    :columns="materialColumns"
                    :data="materialsFilterList"
                    :edit-config="editConfig"
                    highlight-hover-row
                    :height="vxTableHeight"
                    :row-config="{ isCurrent: true }"
                    @current-change="cellClickEvent"
                    ref="Material"
                    :show-overflow="true"
                  >
                    <template #empty>
                      <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
                    </template>
                  </vxe-grid>
                </div>
              </div>
              <multipane-resizer></multipane-resizer>
              <div class="compare">
                <a-row class="content-header-button">
                  <enquiry-hall-button
                    :buttons="contentHeaderButton"
                    class="flex1"
                  />
                  <a-input
                    @input="
                      (e) => {
                        this.supKeywords = e.target.value
                      }
                    "
                    :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNELSeySRdXRL_8666e34e`, '请输入ELS账号或供应商名称')"
                    allowClear
                    style="width: 238px; margin-left: 10px"
                    @pressEnter="handleSupFilter"
                  >
                    <a-icon
                      slot="addonAfter"
                      @click="handleSupFilter"
                      type="search"
                    />
                  </a-input>
                </a-row>
                <div class="ctrlBox">
                  <a-tabs
                    v-model="currentTab"
                    size="small"
                    @change="handleTabChange"
                  >
                    <a-tab-pane
                      v-for="tab in tabs"
                      :disabled="tab.disabled"
                      :key="tab.key"
                      :tab="tab.title"
                    >
                    </a-tab-pane>
                  </a-tabs>
                  <vxe-grid
                    v-bind="gridConfig"
                    :cell-style="({ row, column }) => cellStyle(row, column, currentTab)"
                    :columns="tableColumns"
                    :data="tableData"
                    :edit-config="editConfig"
                    :height="vxeGridHeight"
                    :merge-cells="mergeCells"
                    :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
                    :pager-config="tablePage"
                    @page-change="handlePageChange"
                    :ref="currentTab"
                  >
                    <template #grid_operation="{ row }">
                      <a
                        :disabled="reQuoteDisabled"
                        @click="handleReQuote({ reQuoteWay: 'user', row })"
                      >
                        重报价
                      </a>
                    </template>
                    <template #empty>
                      <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
                    </template>
                  </vxe-grid>
                </div>
              </div>
            </multipane>
          </div>
        </div>
      </div>
      <const-form ref="constForm" />
      <const-table ref="constTable" />
      <enquiry-column-setting
        ref="enquiryColumnSetting"
        @ok="columnSettingOk"
      />
      <enquiry-price-notice
        ref="enquiryPriceNotice"
        @success="getData"
      />
      <enquiry-price-submit
        ref="enquiryPriceSubmit"
        @success="getData"
      />
      <enquiry-regret
        ref="enquiryRegret"
        @success="getData"
      />
      <enquiry-re-quote
        ref="enquiryReQuote"
        @success="getData"
      />
      <enquiry-supplier-setting
        ref="enquirySupplierSetting"
        @ok="supplierSettingOk"
      />
      <field-select-modal
        is-emit
        ref="fieldSelectModal"
        @ok="fieldSelectOk"
      />
      <trend-chart-of-historical-quotations ref="trendChartOfHistoricalQuotations" />
      <trend-chart-of-round-quotations ref="trendChartOfRoundQuotations" />
      <submit-priced
        ref="submitPriced"
        @success="getData"
      />
      <!-- 比价报表弹窗 -->
      <report-modal
        ref="reportModal"
        @exportCompare="handleExport"
      />
    </a-spin>
  </div>
</template>

<script lang="jsx">
import { REPORT_ADDRESS } from '@/utils/const'
import { nominalEdgePullWhiteBlack } from '@/utils/util'
import { ajaxFindDictItems } from '@api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { downFile, getAction, postAction } from '@api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import ConstForm from './components/ConstForm'
import ConstTable from './components/ConstTable'
import filterModal from './components/filterModal'
import EnquiryColumnSetting from './components/EnquiryColumnSetting'
import EnquiryHallButton from './components/EnquiryHallButton'
import EnquiryMaterialInfo from './components/EnquiryMaterialInfo'
import EnquiryPriceNotice from './components/EnquiryPriceNotice'
import EnquiryPriceSubmit from './components/EnquiryPriceSubmit'
import EnquiryRegret from './components/EnquiryRegret'
import EnquiryReQuote from './components/EnquiryReQuote'
import EnquirySupplierSetting from './components/EnquirySupplierSetting'
import TrendChartOfHistoricalQuotations from './components/TrendChartOfHistoricalQuotations'
import TrendChartOfRoundQuotations from './components/TrendChartOfRoundQuotations'
import SubmitPriced from '../../../component/SubmitPriced'
import reportModal from '../../../component/reportModal'
import { currency } from '@/filters'
import { add } from '@/utils/mathFloat.js'
import { Multipane, MultipaneResizer } from 'vue-multipane'
import { COMPARE_COLUMN, OPERATION_COLUMN, PAGE_HEADER_TITLE, QUOTA, STATUS, QUOTA_QUANTITY, QUOTA_SCALE, BARGAIN_REMARK, IS_OFFER, LAST_TIME_PRICE } from './constant'

export default {
  components: {
    ConstForm,
    ConstTable,
    EnquiryColumnSetting,
    EnquiryHallButton,
    EnquiryMaterialInfo,
    EnquiryPriceNotice,
    EnquiryPriceSubmit,
    EnquiryRegret,
    EnquiryReQuote,
    EnquirySupplierSetting,
    fieldSelectModal,
    TrendChartOfHistoricalQuotations,
    TrendChartOfRoundQuotations,
    SubmitPriced,
    Multipane,
    MultipaneResizer,
    filterModal,
    reportModal
  },
  computed: {
    reQuoteDisabled() {
      let k = this.dataListMap[this.currentTab]
      let total = this[k]
        .filter((i) => i.itemStatus == '4')
        .reduce((acc, item) => {
          acc = add(acc, item.quotaScale)
          return acc
        }, 0)
      if (total >= 100 && this.form.enquiryStatus == '7') return true
      return !this.$hasOptAuth('enquiry#purchaseEnquiryHead:reQuote')
    },
    vxeGridHeight() {
      return document.documentElement.clientHeight - 204
    },
    vxTableHeight() {
      return document.documentElement.clientHeight - 152
    },
    showReplenishMaterial() {
      return this.$hasOptAuth('enquiry#purchaseEnquiryHead:replenishMaterialNumber')
    }
  },
  created() {
    nominalEdgePullWhiteBlack()
  },
  data() {
    return {
      contentHeaderButton: [
        // {click: this.handleSupplement, disabled: this.allowSupplement, title: '补充物料编码', type: 'primary'},
        {
          click: (key) => this.handleSet(key),
          key: 'dropdown',
          menus: [
            { key: 'enquiryHallCompareItems', title: this.$srmI18n(`${this.$getLangAccount()}#i18n__GRIld_ec8c876e`, '设置对比项') },
            // {key: 'materialInfoFields', title: '物料信息设置'},
            { key: 'supplier', title: this.$srmI18n(`${this.$getLangAccount()}#i18n__GRRdX_ec5c3c9d`, '设置供应商') }
          ],
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_luGR_323dec53`, '比价设置'),
          type: 'primary'
        },
        {
          click: (key) => this.handleTrendChart(key),
          disabled: this.allowTrendChart,
          key: 'dropdown',
          menus: [
            { key: 'roundQuotations', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trendChartOfQuotation`, '本次报价趋势图') },
            { key: 'historicalQuotations', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_historicalPriceTrendChart`, '历史价格趋势图') }
          ],
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trendChartQuotation`, '报价趋势图'),
          type: 'primary'
        },
        {
          click: (key) => this.handleAward({ range: 'material', type: key }),
          key: 'dropdown',
          menus: [
            { key: 'minPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_enxftu_48cb3e13`, '最低含税价') },
            { key: 'minNetPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_enLftu_516dd6b4`, '最低未税单价') },
            { key: 'minPackagePrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_enfsu_d0fd2317`, '最低打包价') }
          ],
          show: this.showAward,
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IIlB_25b1692d`, '一键授标'),
          type: 'primary'
        },
        {
          authorityCode: 'enquiry#purchaseEnquiryHead:reQuote',
          click: (key) => this.handleReQuote({ reQuoteWay: key }),
          key: 'dropdown',
          menus: [
            { key: 'all', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_2f691299`, '整单重报') },
            { key: 'material', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_iSLss_cb3a023c`, '整物料重报') },
            {
              key: 'supplier',
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_iRdXss_ad823c51`, '整供应商重报'),
              disabled: () => {
                let k = this.dataListMap[this.currentTab]
                let total = this[k]
                  .filter((i) => i.itemStatus == '4')
                  .reduce((acc, item) => {
                    acc = add(acc, item.quotaScale)
                    return acc
                  }, 0)
                if (total >= 100 && this.form.enquiryStatus == '7') return true
                return !this.$hasOptAuth('enquiry#purchaseEnquiryHead:reQuote')
              }
            },
            { key: 'allAccept', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_allAccept`, '全部物料接受') },
            { key: 'allReject', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_allReject`, '全部物料拒绝') },
            { key: 'partReject', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_partReject`, '余行全部拒绝') },
            { key: 'selectAccept', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_selectAccept`, '行选中物料接受') },
            { key: 'selectReject', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_selectReject`, '行选中物料拒绝') },
            { key: 'selectRevoke', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_selectRevoke`, '行选中物料撤销') }
          ],
          show: this.showReQuote,
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_restatement1`, '批量操作'),
          type: 'primary'
        },
        // {click: this.handleExport, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), type: 'primary'},
        { click: this.reportModalShow, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表'), type: 'primary' },
        { click: this.exportExcel, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_MksB_32ae1237`, '汇总报表'), type: 'primary', authorityCode: 'enquiry#purchaseEnquirySum:exportExcel' }
        // {click: this.handleReport, title: '报表', type: 'primary'}
      ],
      costList: [],
      currentTab: 'NormalCompare',
      editConfig: { mode: 'cell', trigger: 'click' },
      enquiryHallCompareItems: [],
      fieldSelectType: '',
      form: {},
      gridConfig: {
        border: true,
        resizable: true,
        size: 'mini',
        stripe: true,
        showHeaderOverflow: true
      },
      ladderList: [],
      materialInfoFields: [
        { hidden: '0', columnCode: 'materialNumber', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码') },
        { hidden: '0', columnCode: 'materialName', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称') },
        { hidden: '0', columnCode: 'materialDesc', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述') },
        { hidden: '0', columnCode: 'materialSpec', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格') },
        { hidden: '0', columnCode: 'requireQuantity', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量') },
        { hidden: '0', columnCode: 'futurePrice', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IBtu_37a0e2db`, '目标单价') },
        // { hidden: '0', columnCode: 'factory_dictText', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂') },
        // { hidden: '0', columnCode: 'storageLocation_dictText', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageLocation`, '库存地点') },
        { field: 'quantityUnit_dictText', title: '主单位', width: 75 },
         { hidden: '0', columnCode: 'purchaseUnit_dictText', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位') },
      ],
      materialsFilterList: [],
      materials: [],
      mergeCells: [],
      normalList: [],
      pageHeaderButton: [
        { click: this.handleNoticeSet, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pricingNotice`, '定价通知'), type: 'primary' },
        // {
        //     authorityCode: 'enquiry#purchaseEnquiryHead:generatePriceRecord',
        //     click: this.handleGenerate,
        //     disabled: this.allowGenerate,
        //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatPriceRecord`, '生成价格记录'),
        //     type: 'primary'
        // },
        {
          click: (key) => this.handlePriceSubmit(key),
          key: 'dropdown',
          menus: [
            { key: 'whole', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itDJ_2f636cf5`, '整单提交') },
            { key: 'row', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_pcDJ_2f10de77`, '按行提交') }
          ],
          show: this.showSubmit,
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_DJIu_2e91e671`, '提交定价'),
          type: 'primary'
        },
        {
          click: this.submitEvaluationPrice,
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_DJnu_2e936d93`, '提交核价'),
          type: 'primary'
        },
        {
          authorityCode: 'enquiry#purchaseEnquiryHead:regret',
          click: (key) => this.handleRegret(key),
          key: 'dropdown',
          menus: [
            { key: 'whole', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itMB_2f632214`, '整单悔标') },
            { disabled: this.allowRegretByRow, key: 'material', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_pSLMB_a9213ccc`, '按物料悔标') }
          ],
          show: this.showRegret,
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regretBid`, '悔标'),
          type: 'danger'
        },
        { click: this.getData, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refresh`, '刷新'), type: 'primary' }
      ],
      pageHeaderTitle: PAGE_HEADER_TITLE,
      sourceId: '',
      spinning: false,
      supplierList: [],
      suppliers: [],
      allSuppliers: [],
      tableColumns: [],
      tableData: [],
      tabs: [
        // {disabled: false, key: 'Material', title: '物料行'},
        { cellStyleFn: this.cellStyleNormal, disabled: false, key: 'NormalCompare', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_conventionalPriceComparison`, '常规比价'), quotePriceWay: '0' },
        { cellStyleFn: this.cellStyleLadder, disabled: false, key: 'LadderCompare', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yDlu_45de8c9c`, '阶梯比价'), quotePriceWay: '1' },
        { cellStyleFn: this.cellStyleCost, disabled: false, key: 'CostCompare', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_costPriceComparison`, '成本比价'), quotePriceWay: '2' },
        { cellStyleFn: this.cellStylePackage, disabled: false, key: 'PackageCompare', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packagePriceComparison`, '打包比价') }
      ],
      dataListMap: {
        NormalCompare: 'normalList',
        LadderCompare: 'ladderList',
        CostCompare: 'costList',
        PackageCompare: 'packagesList'
      },
      // old
      url: { detail: '/enquiry/purchaseEnquiryHead/getData' },
      packageOptFrist: null,
      filterBoxHeight: 60,
      compareItems: [],
      itemNumber: '1',
      currentMaterialRow: {},
      selectMaterialRow: {},
      materialColumns: [
        { type: 'checkbox', width: 36, fixed: 'left' },
        { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center', fixed: 'left' },
        { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120, filters: [{ label: '222', value: 2222 }] },
        { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150 },
        // { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200 },
        { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 120 },
        // { field: 'factory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'), width: 140 },
        // { field: 'storageLocation_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageLocation`, '库存地点'), width: 140 },
        { field: 'quantityUnit_dictText', title: '主单位', width: 75 },
        { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 75 },
        { field: 'requireQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireQuantity`, '需求数量'), width: 80 },
        { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireDate`, '需求日期'), width: 90 }
      ],
      keywords: '',
      supKeywords: '',
      materialStatus: undefined,
      tablePage: {
        pageSizes: [10, 20, 30, 50, 100, 200],
        layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
        currentPage: 1,
        perfect: true,
        total: 0,
        pageSize: 10
      },
      suppliersHideAccount: [],
      selectList:[]
    }
  },
  methods: {
    // 請求接口後格式化列表數據
    formatTableData(data) {
        console.log('請求接口後格式化列表數據', data)
        data = data.map((item) => {
          if (item.lastTimePrice === 0 || Math.abs(item.lastTimePrice) > 0) {
            item.lastTimePrice = Number(item.lastTimePrice).toFixed(6)
          }
          if (item.price === 0 || Math.abs(item.price) > 0) {
            item.price = Number(item.price).toFixed(6)
          }
          if (item.netPrice === 0 || Math.abs(item.netPrice) > 0) {
            item.netPrice = Number(item.netPrice).toFixed(6)
          }
          if (item.hisMinPrice === 0 || Math.abs(item.hisMinPrice) > 0) {
            item.hisMinPrice = Number(item.hisMinPrice).toFixed(6)
          }
          if (item.futurePrice === 0 || Math.abs(item.futurePrice) > 0) {
            item.futurePrice = Number(item.futurePrice).toFixed(6)
          }
          if (item.supplierHisMinPrice === 0 || Math.abs(item.supplierHisMinPrice) > 0) {
            item.supplierHisMinPrice = Number(item.supplierHisMinPrice).toFixed(6)
          }


          if (item.taxAmount === 0 || Math.abs(item.taxAmount) > 0) {
            item.taxAmount = Number(item.taxAmount).toFixed(2)
          }
          if (item.netAmount === 0 || Math.abs(item.netAmount) > 0) {
            item.netAmount = Number(item.netAmount).toFixed(2)
          }
          if (item.supplierSumAmount === 0 || Math.abs(item.supplierSumAmount) > 0) {
            item.supplierSumAmount = Number(item.supplierSumAmount).toFixed(2)
          }
          if (item.threeMonthsSumAmount === 0 || Math.abs(item.threeMonthsSumAmount) > 0) {
            item.threeMonthsSumAmount = Number(item.threeMonthsSumAmount).toFixed(2)
          }

          return item
        })
        return data
    },
    exportExcel() {
      this.spinning = true
      const exportUrl = `/enquiry/sumExcel/exportExcel?id=${this.form.id}`
      downFile(exportUrl)
        .then((data) => {
          if (data.type == 'application/json') {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data]), `${this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_MksB_32ae1237`, '汇总报表')}.xlsx`)
          } else {
            let url = window.URL.createObjectURL(new Blob([data]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', `${this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_MksB_32ae1237`, '汇总报表')}.xlsx`)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(url) //释放掉blob对象
          }
        })
        .finally(() => {
          this.spinning = false
        })
    },
    reportModalShow() {
      this.$refs.reportModal.open(this.sourceId, this.materials)
    },
    handleFilter() {
      console.log(this.materialStatus)
      // this.$refs.Material.clearFilter()
      let materials = JSON.parse(JSON.stringify(this.materials))

      // 先过滤状态
      if (!!this.materialStatus) {
        materials = materials.filter(({ itemStatus }) => {
          if (this.materialStatus !== '99') return itemStatus == this.materialStatus
          else return itemStatus != '4' && itemStatus != '5'
        })
      }

      // 再过滤关键字
      if (!!this.keywords) {
        materials = materials.filter(({ materialNumber, materialName }) => {
          return materialNumber.includes(this.keywords.trim()) || materialName.includes(this.keywords.trim())
        })
      }
      this.materialsFilterList = materials
    },
    handleSupFilter() {
      const fn = this[`set${this.currentTab}Grid`]
      fn && fn()
    },
    cellClickEvent({ row }) {
      this.currentMaterialRow = row
      const { itemNumber = '' } = row || {}
      if (itemNumber === this.itemNumber) return
      this.itemNumber = itemNumber
      this.getItemData()
    },
    handleRowClass({ row }) {
      const itemNumber = this.itemNumber || '1'
      if (row.itemNumber === itemNumber) {
        return 'row--current'
      }
    },
    /**
     * 生成价格记录按钮状态控制
     * currentTab PackageCompare-打包比价
     * priceCreateWay 2-手动生成
     * @return {boolean}
     */
    allowGenerate() {
      const { priceCreateWay } = this.form
      return this.currentTab === 'PackageCompare' || priceCreateWay !== '2'
    },
    /**
     * 按行悔标按钮状态控制
     * currentTab PackageCompare-打包比价
     * @return {boolean}
     */
    allowRegretByRow() {
      return this.currentTab === 'PackageCompare'
    },
    /**
     * 按行提交定价按钮状态控制
     * currentTab PackageCompare-打包比价
     * @return {boolean}
     */
    allowSubmitByRow() {
      return this.currentTab === 'PackageCompare'
    },
    /**
     * 补充物流编码按钮状态控制
     * currentTab Material-物料行
     * @return {boolean}
     */
    allowSupplement() {
      return this.currentTab !== 'Material'
    },
    /**
     * 趋势图按钮状态控制
     * currentTab Material-打包比价
     * @return {boolean}
     */
    allowTrendChart() {
      return this.currentTab === 'PackageCompare'
    },
    cellStyle(row, column, key) {
      let k = this.dataListMap[key]
      if (key == 'PackageCompare') {
        const { taxAmount: minTaxAmount, netAmount: minNetAmount } = this[k].reduce(
          (acc, { taxAmount, netAmount }) => ({
            taxAmount: taxAmount !== 0 ? Math.min(acc.taxAmount, taxAmount) : acc.taxAmount,
            netAmount: netAmount !== 0 ? Math.min(acc.netAmount, netAmount) : acc.netAmount
          }),
          { taxAmount: Infinity, netAmount: Infinity }
        )

        const { taxAmount: maxTaxAmount, netAmount: maxNetAmount } = this[k].reduce(
          (acc, { taxAmount, netAmount }) => ({
            taxAmount: taxAmount !== 0 ? Math.max(acc.taxAmount, taxAmount) : acc.taxAmount,
            netAmount: netAmount !== 0 ? Math.max(acc.netAmount, netAmount) : acc.netAmount
          }),
          { taxAmount: -Infinity, netAmount: -Infinity }
        )
        const { taxAmount, netAmount } = row
        if ((column.property === 'taxAmount' && taxAmount == minTaxAmount) || (column.property === 'netAmount' && netAmount == minNetAmount)) {
          return { color: 'green' }
        }
        if ((column.property === 'taxAmount' && taxAmount == maxTaxAmount) || (column.property === 'netAmount' && netAmount == maxNetAmount)) {
          return { color: 'red' }
        }
      } else {
        if (column.property && column.property.indexOf('supplier_') !== -1) {
          const { columnCode, itemNumber, materialNumber } = row
          const toElsAccount = column.property.split('_')[1]
          const { price: minPrice, netPrice: minNetPrice } = this[k].reduce(
            (acc, { price, netPrice }) => ({
              price: price !== 0 ? Math.min(acc.price, price) : acc.price,
              netPrice: netPrice !== 0 ? Math.min(acc.netPrice, netPrice) : acc.netPrice
            }),
            { price: Infinity, netPrice: Infinity }
          )

          const { price: maxPrice, netPrice: maxNetPrice } = this[k].reduce(
            (acc, { price, netPrice }) => ({
              price: price !== 0 ? Math.max(acc.price, price) : acc.price,
              netPrice: netPrice !== 0 ? Math.max(acc.netPrice, netPrice) : acc.netPrice
            }),
            { price: -Infinity, netPrice: -Infinity }
          )
          const item = this[k].find((item) => item.itemNumber === itemNumber && item.materialNumber === materialNumber && item.toElsAccount === toElsAccount)
          if (item) {
            if ((columnCode === 'price' && item.price == minPrice) || (columnCode === 'netPrice' && item.netPrice == minNetPrice)) {
              return { color: 'green' }
            }
            if ((columnCode === 'price' && item.price == maxPrice) || (columnCode === 'netPrice' && item.netPrice == maxNetPrice)) {
              return { color: 'red' }
            }
          }
        }
      }
    },
    /**
     * 拆分数量修改事件
     * @param row 修改行
     * @param toElsAccount 供应商号
     * @param value 修改值
     * @param listName 修改的列表名称
     */
    changeQuotaQuantity(row, toElsAccount, value, listName) {
      const { columnCode, itemNumber, materialNumber } = row
      const currentItem = this[listName].find((item) => item.toElsAccount === toElsAccount && item.itemNumber === itemNumber && item.materialNumber === materialNumber)
      this.$set(currentItem, columnCode, value)
    },
    /**
     * 拆分比例修改事件
     * @param row 修改行
     * @param toElsAccount 供应商号
     * @param value 修改值
     * @param listName 修改的列表名称
     */
    changeQuotaScale(row, toElsAccount, value, listName) {
      const { columnCode, itemNumber, materialNumber } = row
      const currentItem = this[listName].find((item) => item.toElsAccount === toElsAccount && item.itemNumber === itemNumber && item.materialNumber === materialNumber)
      this.$set(currentItem, columnCode, value)
    },
    columnSettingOk({ columnCode, data }) {
      const fn = this[`${columnCode}SettingOk`]
      fn && fn(data)
    },
    /**
     * 对比项显隐设置确认回调
     */
    enquiryHallCompareItemsSettingOk(data = [], reload) {
      if (data.length > 0) this.enquiryHallCompareItems = data
      if (this.enquiryHallCompareItems.length > 0) {
        const fn = this[`set${this.currentTab}Grid`]
        fn && fn(reload)
        return
      }
      let postData = {
        busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
        dictCode: 'srmEnquiryCompare'
      }
      this.spinning = true
      ajaxFindDictItems(postData)
        .then((res) => {
          if (res.success) {
            this.enquiryHallCompareItems = res.result.map((i) => {
              return { columnName: i.text, columnCode: i.value, hidden: '0' }
            })
            const fn = this[`set${this.currentTab}Grid`]
            fn && fn()
          }
        })
        .finally(() => {
          this.spinning = false
        })
    },
    fieldSelectOk(data) {
      if (this.fieldSelectType === 'material') {
        // 补充物料编码选择确认回调
        // const checkboxRecords = this.$refs[this.currentTab].getCheckboxRecords()
        const { materialDesc, materialGroup, materialGroupName, materialModel, materialNumber, materialSpec, id, materialName } = data[0]
        const row = {
          ...this.selectMaterialRow,
          materialId: id,
          materialDesc: this.selectMaterialRow.materialDesc || materialDesc,
          materialGroup,
          materialGroupName,
          materialModel,
          materialNumber,
          materialName: this.selectMaterialRow.materialName || materialName,
          materialSpec
        }
        this.spinning = true
        postAction('/enquiry/purchaseEnquiryHead/replenishMaterialNumber', row)
          .then((res) => {
            if (res.success) {
              this.$notification.success({ description: res.message, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tkLRW_b20577c3`, '操作成功') })
              this.getData()
            } else {
              this.$notification.warning({ description: res.message, message: '警告' })
              this.spinning = false
            }
          })
          .catch(() => {
            this.spinning = false
          })
      }
      if (this.fieldSelectType === 'supplier') {
        // 供应商授标选择确认回调
        this.spinning = true
        const supplierList = data.map((supplier) => ({ ...supplier, headId: this.sourceId }))
        postAction('/enquiry/purchaseEnquiryHead/supplierAward', supplierList)
          .then((res) => {
            if (res.success) {
              this.$notification.success({ description: res.message, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tkLRW_b20577c3`, '操作成功') })
              this.getData()
            } else {
              this.$notification.warning({ description: res.message, message: '警告' })
              this.spinning = false
            }
          })
          .catch(() => {
            this.spinning = false
          })
      }
      if (['allAccept', 'allReject','selectAccept'].includes(this.fieldSelectType)) {
        if (this.fieldSelectType == 'allAccept' || this.fieldSelectType =='selectAccept') {
          let code = this.form.quotaWay == '0' ? 'quotaScale' : 'quotaQuantity'
          let max = this.form.quotaWay == '0' ? 100 : this.currentMaterialRow.requireQuantity
          let title = this.form.quotaWay == '0' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzlvntTEU100_68e91364`, '拆分比例合计须等于100%') : `拆分数量不能超过需求数量：${max}`
          let total = data.reduce((acc, item) => {
            acc = add(acc, item[code])
            return acc
          }, 0)
          if (total > max) {
            return this.$message.warning(title)
          }
        }
        let param = {}
        param['id'] = this.sourceId
        param['packageOpt'] = false
        let k = this.dataListMap[this.currentTab]
        let sup = this[k]
        param['purchaseEnquiryItemList'] = data.map((row) => {
          let d = sup.find((item) => row.toElsAccount == item.toElsAccount)
          d['quotaScale'] = row.quotaScale || undefined
          d['quotaQuantity'] = row.quotaQuantity || undefined
          return d
        })
        if(this.fieldSelectType == 'selectAccept') {
            let itemNumbers = []
            this.selectList.map(item=>{
                itemNumbers.push(item.itemNumber)
            })
            param['itemNumbers'] = itemNumbers
        }
        this.spinning = true
        postAction(`/enquiry/purchaseEnquiryHead/${this.fieldSelectType}`, param)
          .then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.getData()
            } else {
              this.$message.warning(res.message)
              this.spinning = false
            }
          })
          .catch(() => {
            this.spinning = false
          })
      }
    },
    /**
     * 获取供应商动态列信息
     * @param suppliers 参与比价的供应商
     * @param listName 修改的列表名称
     * @return {*}
     */
    getSupplierColumns(suppliers, listName) {
      const initRowLadderJson = function (jsonData) {
        return jsonData ? JSON.parse(jsonData) : []
      }
      return suppliers.map((supplier) => ({
        align: 'center',
        field: `supplier_${supplier.toElsAccount}`,
        slots: {
          default: ({ row, column }) => {
            if (row.columnCode === 'acceptQuota') {
              return [
                <div>
                  <a
                    style='margin:0 4px'
                    {...{
                      on: {
                        click: () => {
                          this.handleContract(row, supplier, false, 'accept', listName)
                        }
                      }
                    }}
                    disabled={row[`${column.property}_accept`]}
                  >
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accept`, '接受')}
                  </a>
                  <a
                    style='margin:0 4px'
                    {...{
                      on: {
                        click: () => {
                          this.handleContract(row, supplier, false, 'reject', listName)
                        }
                      }
                    }}
                    disabled={row[`${column.property}_reject`]}
                  >
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝')}
                  </a>
                  <a
                    style='margin:0 4px'
                    {...{
                      on: {
                        click: () => {
                          this.handleContract(row, supplier, false, 'revoke', listName)
                        }
                      }
                    }}
                    disabled={row[`${column.property}_revoke`]}
                  >
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销')}
                  </a>
                </div>
              ]
            } else if (['quotaQuantity', 'quotaScale'].includes(row.columnCode)) {
              let show = (() => {
                if ('quotaQuantity' == row.columnCode) {
                  return this.form.quotaWay === '1'
                } else {
                  return this.form.quotaWay === '0'
                }
              })()
              const props = { max: row.columnCode == 'quotaQuantity' ? row.requireQuantity : 100, min: 0, type: 'number' }
              const on = {
                change: ({ value }) => {
                  this.changeQuotaQuantity(row, supplier.toElsAccount, value, listName)
                }
              }
              return show && !row[`${column.property}_accept`]
                ? [
                    <vxe-input
                      vModel={row[column.property]}
                      {...{ on, props }}
                    />
                  ]
                : [<span>{row[column.property]}</span>]
            } else if (row.columnCode === 'bargainRemark') {
              const on = {
                change: ({ value }) => {
                  this.changeQuotaScale(row, supplier.toElsAccount, value, listName)
                }
              }
              return row[`${column.property}_accept`]
                ? [<span>{row[column.property]}</span>]
                : [
                    <vxe-input
                      vModel={row[column.property]}
                      {...{ on }}
                    />
                  ]
            } else if (row.columnCode === 'itemStatus_dictText') {
              let color = (() => {
                if (row[column.property] == '接受') {
                  return 'green'
                } else if (['已悔标', '拒绝'].includes(row[column.property])) {
                  return 'red'
                }
                return '#409eff'
              })()
              return [<span style={{ color: color }}>{row[column.property]}</span>]
            } else if (['price', 'netPrice'].includes(row.columnCode) && listName == 'ladderList') {
              return row[column.property] > 0
                ? [
                    <div>
                      <a-tooltip
                        placement='topLeft'
                        overlayClassName='tip-overlay-class'
                      >
                        <template slot='title'>
                          <div>
                            <vxe-table
                              auto-resize
                              border
                              row-id='id'
                              size='mini'
                              data={initRowLadderJson(row[`supplier_${supplier.toElsAccount}_ladderPriceJson`])}
                            >
                              <vxe-table-column
                                type='seq'
                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号')}
                                width='80'
                              ></vxe-table-column>
                              <vxe-table-column
                                field='ladder'
                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')}
                                width='140'
                              ></vxe-table-column>
                              <vxe-table-column
                                field='price'
                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价')}
                                width='140'
                              ></vxe-table-column>
                              <vxe-table-column
                                field='netPrice'
                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价')}
                                width='140'
                              ></vxe-table-column>
                            </vxe-table>
                          </div>
                        </template>
                        <span style={{ 'margin-right': '5px' }}>{row[column.property]}</span>
                        <a-icon type='stock' />
                      </a-tooltip>
                    </div>
                  ]
                : [<span>{row[column.property]}</span>]
            } else if (['price', 'netPrice'].includes(row.columnCode) && listName == 'costList') {
              return row[column.property] > 0
                ? [
                    <div
                      style={{ cursor: 'pointer' }}
                      onClick={() => {
                        this.handleCostTable(row[`supplier_${supplier.toElsAccount}_costFormJson`], row.columnCode, row[`supplier_${supplier.toElsAccount}_taxRate`])
                      }}
                    >
                      <span style={{ 'margin-right': '5px' }}>{row[column.property]}</span>
                      <a-icon type='table' />
                    </div>
                  ]
                : [<span>{row[column.property]}</span>]
            } else {
              return [<span>{row[column.property]}</span>]
            }
          }
        },
        title: supplier.supplierName,
        width: 130
      }))
    },
    showLadderList(row) {
      console.log(row)
    },
    /**
     * 一键授标
     * @param range 授标范围
     * @param type 授标规则
     */
    handleAward({ range, type }) {
      let selectedList  = this.$refs.Material.getCheckboxRecords()
      this.selectList = selectedList
      let itemIds=[]
      if(this.selectList.length>0){
          this.selectList.map(item=>{
              itemIds.push(item.itemNumber)
          })
      }
      let itemNumbers = itemIds.join(',')
        console.log(itemNumbers,'测试')
        console.log(this.selectList,'选中的物料')
      if (range === 'group') {
        // 分组授标
        postAction('/enquiry/purchaseEnquiryHead/saveGroup', { id: this.sourceId, purchaseEnquiryItemList: this.materials }).then((res) => {
          if (res.success) {
            this.spinning = true
            getAction('/enquiry/purchaseEnquiryHead/fastAward', { headId: this.sourceId, range, type })
              .then((res) => {
                if (res.success) {
                  this.$notification.success({ description: res.message, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tkLRW_b20577c3`, '操作成功') })
                  this.getData()
                } else {
                  this.$notification.warning({ description: res.message, message: '警告' })
                  this.spinning = false
                }
              })
              .catch(() => {
                this.spinning = false
              })
          }
        })
      } else if (range === 'supplier') {
        // 供应商授标
        this.fieldSelectType = 'supplier'
        const modalColumns = [
          { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150 },
          { field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150 },
          { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200 },
          { field: 'needCoordination_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), width: 200 }
        ]
        const modalUrl = '/enquiry/purchaseEnquiryHead/reQuoteSupplierList'
        this.$refs.fieldSelectModal.open(modalUrl, { headId: this.sourceId }, modalColumns, 'single')
      } else {
        const callback = () => {
          this.spinning = true
          getAction('/enquiry/purchaseEnquiryHead/oneAward', { headId: this.sourceId, optType: type, itemNumbers: itemNumbers })
            .then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.getData()
              } else {
                this.$message.warning(res.message)
                this.spinning = false
              }
            })
            .catch(() => {
              this.spinning = false
            })
        }
        this.$confirm({
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
          content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLlB_3928214c`, '是否确认授标'),
          onOk() {
            callback && callback()
          }
        })
      }
    },
    // 成本模板
    handleCostForm(row) {
      console.log(row)
      let { templateNumber, templateVersion, templateAccount, templateName } = row.costFormJson ? JSON.parse(row.costFormJson) : {}
      const currentEditRow = {
        busAccount: this.form.busAccount,
        templateAccount,
        templateName,
        templateNumber,
        templateVersion,
        role: 'purchase'
      }
      // const suppliers = this.suppliers.filter(supplier => supplier.hidden === '0')
      let filetrSupplierList = this.initSupplierFilterList()
      this.$refs.constForm.open({ currentEditRow, suppliers: filetrSupplierList, row })
    },
    // 成本模板
    handleCostTable(json, typeOfPrice, taxRate) {
      let data = json ? JSON.parse(json) : {}
      let { templateNumber, templateVersion, templateAccount, templateName } = data
      const currentEditRow = {
        busAccount: this.form.busAccount,
        templateAccount,
        templateName,
        templateNumber,
        templateVersion,
        role: 'purchase'
      }
      // const suppliers = this.suppliers.filter(supplier => supplier.hidden === '0')
      this.$refs.constTable.open({ currentEditRow, data, typeOfPrice, taxRate })
    },
    // 导出
    handleExport() {
      this.spinning = true
      downFile('/enquiry/purchaseEnquiryHead/exportBargain', { headId: this.sourceId })
        .then((data) => {
          if (!data) {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data]), this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表') + '.xlsx')
          } else {
            let url = window.URL.createObjectURL(new Blob([data]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表') + '.xlsx')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(url) //释放掉blob对象
          }
        })
        .finally(() => {
          this.spinning = false
        })
    },
    handleGenerate() {
      let regretList = this.$refs.Material.getCheckboxRecords()
      if (regretList.length <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFkiTPbLumtHjSL_c424cfc0`, '请选择左侧需要生成价格记录的物料'))
        return
      }
      const filterList = regretList.filter((i) => i.itemStatus === '11')
      if (filterList.length > 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IMBSLxqIbLumtH_9d663369`, '已悔标物料不可生成价格记录'))
        return
      }
      let materialName = regretList
        .map((row) => {
          if (row.materialName) return row.materialName
          return row.materialNumber
        })
        .join('，')
      const callback = () => {
        let param = this.form
        param['purchaseEnquiryItemList'] = regretList
        this.spinning = true
        postAction('/enquiry/purchaseEnquiryHead/generatePriceRecord', param)
          .then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.getData()
            } else {
              this.$message.warning(res.message)
              this.spinning = false
            }
          })
          .catch(() => {
            this.spinning = false
          })
      }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, '是否确认将物料')) + ':' + materialName + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_generateInformationRecord`, '生成信息记录'),
        onOk() {
          callback && callback()
        }
      })
    },
    handleManualAward() {
      const that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
        content: '此操作将手动授标, 是否继续',
        onOk: () => {
          const params = {
            id: this.sourceId,
            purchaseEnquiryItemList: [...this.costList, ...this.ladderList, ...this.normalList]
          }
          that.spinning = true
          postAction('/enquiry/purchaseEnquiryHead/manualAward', params)
            .then((res) => {
              if (res.success) {
                that.$notification.success({ description: res.message, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tkLRW_b20577c3`, '操作成功') })
                that.getData()
              } else {
                that.$notification.warning({ description: res.message, message: '警告' })
                this.spinning = false
              }
            })
            .catch(() => {
              that.spinning = false
            })
        }
      })
    },
    handleNoticeSet() {
      this.$refs.enquiryPriceNotice.open({ headId: this.sourceId, pricingNotice: this.form.pricingNotice })
    },
    // 提交定价
    handlePriceSubmit(key) {
      let that = this
      let purchaseEnquiryItemList = []
      if (key === 'whole') {
        purchaseEnquiryItemList = that.materials
      }
      if (key === 'row') {
        const checkboxRecords = that.$refs.Material.getCheckboxRecords()
        if (!checkboxRecords.length) {
          that.$message.warning(that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_ViFkiTPDJIujSL_ef42042a`, '请选择左侧需要提交定价的物料'))
          return
        }
        purchaseEnquiryItemList = checkboxRecords
      }

      if (purchaseEnquiryItemList) {
        let hasNoFinished = false
        purchaseEnquiryItemList.forEach(item => {
          if (item.itemStatus != '4' && item.itemStatus != '5') {
            hasNoFinished = true
          }
        })
        if (hasNoFinished) {
          that.$confirm({
            okText: "继续提交",
            cancelText: '取消',
            title: '请注意',
            content: '存在未处理的报价行, 是否继续? ',
            onOk () {
              that.$refs.submitPriced.awardOpinion = null
              that.$refs.submitPriced.open(that.sourceId, purchaseEnquiryItemList, key)
            }
          })
        } else {
          that.$refs.submitPriced.awardOpinion = null
          that.$refs.submitPriced.open(that.sourceId, purchaseEnquiryItemList, key)
        }
      }
    },
    handleRegret(key) {
      if (key === 'whole') {
        this.$refs.enquiryRegret.openWhole({ headId: this.sourceId })
      }
      if (key === 'material') {
        let regretList = this.$refs.Material.getCheckboxRecords()
        if (regretList.length <= 0) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFkiTPMBnSL_7b867728`, '请选择左侧需要悔标的物料'))
          return
        }
        // let materialDesc = regretList.map(row => row.materialName).join('，')
        // let message = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areYouSureYouWantCancelBid`, '是否确认悔标物料')+'：' + materialDesc
        this.$refs.enquiryRegret.openMaterial({ headId: this.sourceId, purchaseEnquiryItemList: regretList })
      }
    },
    handleReport() {
      const token = this.$ls.get('Access-Token')
      const url = `${REPORT_ADDRESS}/els/report/jmreport/view/824096099903758336?token=${token}&id=${this.sourceId}`
      window.open(url, '_blank')
    },
    handleReQuote({ reQuoteWay, row }) {
      if (reQuoteWay === 'all') {
        this.$refs.enquiryReQuote.openAll({ headId: this.sourceId })
      }
      if (reQuoteWay === 'supplier') {
        if (this.reQuoteDisabled) return false
        this.$refs.enquiryReQuote.openSupplier({ headId: this.sourceId })
      }
      if (reQuoteWay === 'material') {
        this.$refs.enquiryReQuote.openMaterial({ headId: this.sourceId })
      }
      if (reQuoteWay === 'user') {
        const list = JSON.parse(JSON.stringify([...this.normalList, ...this.ladderList, ...this.costList]))
        const enquiryItemList = list.filter((item) => item.materialNumber === row.materialNumber && item.itemNumber === row.itemNumber && (['2', '6', '3', '8'].includes(item.itemStatus) || (item.itemStatus === '11' && item.regretFlag === '2')))
        this.$refs.enquiryReQuote.openUser({ headId: this.sourceId, enquiryItemList })
      }
      if (reQuoteWay == 'package') {
        this.$refs.enquiryReQuote.openOfPackage({ headId: this.sourceId, enquiryItemList: row })
      }
      if (['allAccept', 'allReject', 'partReject'].includes(reQuoteWay)) this.patchOperate(reQuoteWay)
      if(['selectAccept','selectReject','selectRevoke'].includes(reQuoteWay)){
        let selectedList  = this.$refs.Material.getCheckboxRecords()
        if (!selectedList.length) {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFkiTPDJIujSL_ef42042a`, '请选择左侧的物料'))
            return
        }
        this.selectList = selectedList
        this.patchOperate(reQuoteWay)
     }
    },
    patchOperate(type) {
      let itemNumbers = []
      this.selectList.map(item=>{
          itemNumbers.push(item.itemNumber)
      })
      console.log('itemNumbers',itemNumbers)
      let callback = () => {
        // 弹窗选择供应商
        if (['allAccept', 'allReject','selectAccept'].includes(type)) {
          this.fieldSelectType = type
          const modalColumns = [
            { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 140 },
            { field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 140 },
            { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200 },
            { field: 'needCoordination_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), width: 140 }
          ]
          if (type == 'allAccept'||type =='selectAccept') {
            if (this.form.quotaWay == '0') {
              modalColumns.push({
                field: 'quotaScale',
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比'),
                width: 130,
                slots: {
                  default: ({ row, column }) => {
                    const props = { max: 100, min: 0, type: 'number' }
                    return [
                      <vxe-input
                        vModel={row[column.property]}
                        {...{ props }}
                      />
                    ]
                  }
                }
              })
            } else {
              console.log(this.currentMaterialRow)
              modalColumns.push({
                field: 'quotaQuantity',
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitQty`, '拆分数量'),
                width: 130,
                slots: {
                  default: ({ row, column }) => {
                    const props = { max: this.currentMaterialRow.requireQuantity, min: 0, type: 'number' }
                    return [
                      <vxe-input
                        vModel={row[column.property]}
                        {...{ props }}
                      />
                    ]
                  }
                }
              })
            }
          }
          const modalUrl = '/enquiry/purchaseEnquiryHead/reQuoteSupplierList'
            let params = { headId: this.sourceId }
            this.$refs.fieldSelectModal.open(modalUrl, params, modalColumns, 'multiple')
        }else if ( type == 'selectReject' ){
            let param = {}
            param['id'] = this.sourceId
            param['packageOpt'] = true
            param['itemNumbers'] = itemNumbers
            this.spinning = true
            postAction('/enquiry/purchaseEnquiryHead/selectReject', param).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.getData()
                } else {
                    this.$message.warning(res.message)
                    this.spinning = false
                }
            }).catch(() => {
                this.spinning = false
            })
        }else if( type =='selectRevoke' ){
            let param = {}
            param['id'] = this.sourceId
            param['packageOpt'] = false
            param['itemNumbers'] = itemNumbers
            this.spinning = true
            postAction('/enquiry/purchaseEnquiryHead/selectRevoke', param).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.getData()
                } else {
                    this.$message.warning(res.message)
                    this.spinning = false
                }
            }).catch(() => {
                this.spinning = false
            })
        } else {
          let param = {}
          param['id'] = this.sourceId
          param['packageOpt'] = true
          param['purchaseEnquiryItemList'] = this.packagesList
          this.spinning = true
          postAction('/enquiry/purchaseEnquiryHead/partReject', param)
            .then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.getData()
              } else {
                this.$message.warning(res.message)
                this.spinning = false
              }
            })
            .catch(() => {
              this.spinning = false
            })
        }
      }
      let titleMap = {
        allAccept: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_KQRLdRRdXbxSLyl_c6e429a7`, '是否确认相关供应商全部物料接受'),
        allReject: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_KQRLdRRdXbxSLFK_c6e43b20`, '是否确认相关供应商全部物料拒绝'),
        partReject: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_KQRLAvSLcbxFK_6bdd50ed`, '是否确认其它物料行全部拒绝'),
        selectAccept:'是否确认相关供应商选中的物料行全部接受',
        selectReject:'是否确认选中的物料行全部拒绝',
        selectRevoke:'是否确认选中的物料行接受/拒绝状态全部撤销'
      }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
        content: titleMap[type],
        onOk() {
          callback && callback()
        }
      })
    },
    handleSet(columnCode) {
      if (columnCode === 'supplier') {
        this.$refs.enquirySupplierSetting.open({ tableData: this.suppliers })
      } else {
        this.$refs.enquiryColumnSetting.open({ columnCode, tableData: this[columnCode] })
      }
    },
    handleSupplement() {
      this.fieldSelectType = 'material'
      // const checkboxRecords = this.$refs[this.currentTab].getCheckboxRecords()
      // if(checkboxRecords.length !== 1){
      //     this.$notification.warning({description: '请选择一条数据', message: '警告'})
      //     return
      // }
      let regretList = this.$refs.Material.getCheckboxRecords() || []
      if (regretList.length != 1) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectPieceOfData`, '请选择一条数据!'))
        return
      }
      this.selectMaterialRow = regretList[0]
      if (regretList[0].materialNumber) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_material`, '物料') + ':' + regretList[0].materialName + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_codeAlreadyExistsNoNeedSupplement`, '已存在编码，无需补充！'))
        return
      }
      const modalColumns = [
        { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150 },
        { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150 },
        { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200 },
        { field: 'brand', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialBrand`, '物料品牌'), width: 200 },
        { field: 'purchaseCycle', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), width: 200 },
        { field: 'purchaseType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseType`, '采购类型'), width: 200 },
        { field: 'checkWay_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkWay`, '检验方式'), width: 200 },
        { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingUnit`, '采购单位'), width: 200 },
        { field: 'materialModel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialModel`, '物料型号'), width: 200 },
        { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200 }
      ]
      const modalUrl = '/material/purchaseMaterialHead/list'
      this.$refs.fieldSelectModal.open(modalUrl, {}, modalColumns, 'single')
    },
    handleTabChange(activeKey) {
      this.tablePage.currentPage = 1
      const fn = this[`set${activeKey}Grid`]
      fn && fn()
    },
    handleTrendChart(type) {
      if (type === 'roundQuotations') {
        if (this.currentMaterialRow.quotePriceWay == '2' && !JSON.parse(this.currentMaterialRow.costFormJson).groups) {
          this.costList.forEach((item) => {
            if (JSON.parse(item.costFormJson).groups) this.currentMaterialRow.costFormJson = item.costFormJson
          })
        }
        this.$refs.trendChartOfRoundQuotations.open({ headId: this.sourceId, row: this.currentMaterialRow })
      }
      if (type === 'historicalQuotations') {
        const { itemNumber, materialNumber } = this.currentMaterialRow
        const rows = [...this.normalList, ...this.ladderList, ...this.costList].filter((item) => (item.itemNumber = itemNumber && item.materialNumber === materialNumber))
        this.$refs.trendChartOfHistoricalQuotations.open({ materialNumber, rows })
      }
    },
    materialInfoFieldsSettingOk(data) {
      this.materialInfoFields = data
    },
    async getItemData(reload) {
      const itemNumber = this.itemNumber || '1'
      const { quotePriceWay = '0' } = this.purchaseEnquiryItemList.length > 0 && this.purchaseEnquiryItemList.find((n) => n.itemNumber === itemNumber)
      let quotePriceWayMap = {
        0: 'queryDetailsCompare',
        1: 'queryLadderCompare',
        2: 'queryCostCompare'
      }
      // 获取当前物料对应的供应商列表数据
      let fn = getAction(`/enquiry/purchaseEnquiryHead/${quotePriceWayMap[quotePriceWay]}`, {
        headId: this.sourceId,
        itemNumber: itemNumber
      })
      //查询打包比价
      const apiQueryPackageCompare = getAction('/enquiry/purchaseEnquiryHead/queryPackageCompare', {
        headId: this.sourceId
      })

      const handlePromise = (list = []) =>
        list.map((promise) =>
          promise.then(
            (res) => ({ status: res.success ? 'success' : 'error', res }),
            (err) => ({ status: 'error', err })
          )
        )
      const promiseList = [fn, apiQueryPackageCompare]
      this.spinning = true
      Promise.all(handlePromise(promiseList))
        .then((res) => {
          this.spinning = false
          const [resData, packages] = res || []
          let dataList = {
            0: 'normalList',
            1: 'ladderList',
            2: 'costList'
          }
          if (resData && resData.status === 'success') {
            let dataListQuo = resData.res.result;
            dataListQuo = this.formatTableData(dataListQuo);
            this[dataList[quotePriceWay]] = dataListQuo
          }
          if (packages && packages.status === 'success') {
            let packagesList = packages.res.result;
            packagesList = this.formatTableData(packagesList);
            this.packagesList = packagesList;
          }
          this.tabs.forEach((tab) => {
            if (tab.key === 'NormalCompare') {
              this.$set(tab, 'disabled', quotePriceWay != '0')
            }
            if (tab.key === 'CostCompare') {
              this.$set(tab, 'disabled', quotePriceWay != '2')
            }
            if (tab.key === 'LadderCompare') {
              this.$set(tab, 'disabled', quotePriceWay != '1')
            }
            if (tab.key === 'PackageCompare') {
              this.$set(tab, 'disabled', !this.packagesList.length)
            }
            if (tab.quotePriceWay == quotePriceWay) {
              this.currentTab = this.currentTab == 'PackageCompare' ? this.currentTab : tab.key
            }
          })
          this.enquiryHallCompareItemsSettingOk([], reload)
        })
        .catch(() => {
          this.spinning = false
        })
    },
    // 获取数据
    getData(reload = false) {
      const params = {
        headId: this.sourceId
      }
      const apiQueryMaterialList = getAction('/enquiry/purchaseEnquiryHead/queryMaterialList', params)
      const querySupplier = getAction('/enquiry/purchaseEnquiryHead/querySupplier', params) // 当前物料的供应商

      const handlePromise = (list = []) =>
        list.map((promise) =>
          promise.then(
            (res) => ({ status: 'success', res }),
            (err) => ({ status: 'error', err })
          )
        )
      const promiseList = [apiQueryMaterialList, querySupplier]
      this.spinning = true
      Promise.all(handlePromise(promiseList))
        .then((res) => {
          try {
            const [infoRes, supplierRes] = res || []
            if (infoRes && infoRes.status === 'success') {
              this.fixInfoData(infoRes.res)
            }
            if (supplierRes && supplierRes.status === 'success') {
              this.suppliers = supplierRes.res.result.map((supplier) => {
                let hidden = this.suppliersHideAccount.includes(supplier.toElsAccount) ? '1' : '0'
                return { ...supplier, hidden }
              })
            }
            this.getItemData(reload)
          } catch {
            this.spinning = false
          }
        })
        .catch(() => {
          this.spinning = false
        })
    },
    fixInfoData({ result }) {
      const { purchaseEnquiryItemList = [], ...others } = result || {}
      this.purchaseEnquiryItemList = purchaseEnquiryItemList
      this.itemNumber = this.itemNumber || '1'
      this.materialColumns[2].filters = []
      this.materials = purchaseEnquiryItemList.map((material, index) => {
        this.materialColumns[2].filters.push({ label: material.materialNumber + '_' + material.materialName, value: material.materialNumber })
        return { ...material, groupNo: material.groupNo || `${index + 1}` }
      })
      // this.materialsFilterList = this.materials
      this.handleFilter()
      this.currentMaterialRow = this.materialsFilterList.find((item) => item.itemNumber == this.itemNumber)
      this.$refs.Material.loadColumn(this.materialColumns)
      this.$refs.Material.setCurrentRow(this.currentMaterialRow)
      this.form = {
        ...others
      }
    },
    setCostCompareGrid() {
      const costMaterials = this.materials.filter((material) => material.quotePriceWay === '2' && material.itemNumber == this.itemNumber)
      // const filters = costMaterials.map(material => ({label: material.materialName, value: material.materialNumber}))
      let filetrSupplierList = this.initSupplierFilterList()
      const supplierColumns = this.getSupplierColumns(filetrSupplierList, 'costList')
      this.tableColumns = [
        {
          fixed: 'left',
          ...COMPARE_COLUMN
        },
        ...supplierColumns
      ]
      const costData = []
      const enquiryHallCompareItems = [
        { ...QUOTA, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作') },
        { ...STATUS, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态') },
        { ...QUOTA_QUANTITY, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitQty`, '拆分数量') },
        { ...QUOTA_SCALE, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比') },
        ...this.enquiryHallCompareItems
      ]
      // 定义对比项 过滤隐藏项
      const compareItems = enquiryHallCompareItems.filter((item) => item.hidden === '0')
      // 成本物料遍历
      costMaterials.forEach((material) => {
        const { itemNumber, materialNumber } = material
        compareItems.forEach((compareItem, index) => {
          const realMaterial = {
            ...JSON.parse(JSON.stringify(material)),
            ...compareItem,
            id: `${materialNumber}${itemNumber}${index}`
          }
          filetrSupplierList.forEach((supplier) => {
            const currentSupplierItem = this.costList.find((item) => item.materialNumber === materialNumber && item.itemNumber === itemNumber && item.toElsAccount === supplier.toElsAccount)
            if (currentSupplierItem) {
              const key = `supplier_${currentSupplierItem.toElsAccount}`
              // if (compareItem.isCost) {
              //     let JSONData = currentSupplierItem.costFormJson ? JSON.parse(currentSupplierItem.costFormJson) : {}
              //     const {groups = []} = JSONData
              //     let d = groups.filter(group => group.groupCode == compareItem.costValue)[0]
              //     if (compareItem.columnCode === 'price') {
              //         realMaterial[key] = d?.price == 0 ?  '' : d?.price
              //         realMaterial['supplier_priceType'] = compareItem.columnCode
              //     } else if (compareItem.columnCode === 'netPrice') {
              //         realMaterial[key] = d?.netPrice == 0 ?  '' : d?.netPrice || ''
              //         realMaterial['supplier_priceType'] = compareItem.columnCode
              //     }
              //     realMaterial[`supplier_${currentSupplierItem.toElsAccount}_costFormJson`] = JSONData
              // } else {
              //     realMaterial[key] = currentSupplierItem[compareItem.columnCode]
              //     realMaterial[key] = currentSupplierItem[compareItem.columnCode]
              // }
              realMaterial[key] = currentSupplierItem[compareItem.columnCode]
              realMaterial[`supplier_${supplier.toElsAccount}_taxRate`] = currentSupplierItem['taxRate']
              realMaterial[`supplier_${currentSupplierItem.toElsAccount}_costFormJson`] = currentSupplierItem.costFormJson
              realMaterial[`supplier_${supplier.toElsAccount}_accept`] = (() => {
                if (this.form.packageOptFrist === '1') return true
                let f = !(['2', '8'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                return f
              })()
              realMaterial[`supplier_${supplier.toElsAccount}_reject`] = (() => {
                if (this.form.packageOptFrist === '1') return true
                let f = !(['2', '8', '6', '3'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                return f
              })()
              realMaterial[`supplier_${supplier.toElsAccount}_revoke`] = (() => {
                if (this.form.packageOptFrist === '1') return true
                if (currentSupplierItem.evaluationStatus == '4') return true
                // 提交定价后不能撤销
                if (currentSupplierItem.pricedFlag == '1') return true
                // 审批中 不能撤销
                if (currentSupplierItem.auditStatus == '1') return true
                if (currentSupplierItem.itemStatus === '11') return true // 行悔标 不可操作
                if (currentSupplierItem.itemStatus == '4' || currentSupplierItem.itemStatus == '5') return false // 行接受或拒绝 可操作
                return !(['4', '5'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
              })()
            }
          })
          costData.push(realMaterial)
        })
      })
      this.tableData = costData
      // this.mergeCells = [...compareRowCells, ...compareColCells, ...mergeOperationCells]
      this.mergeCells = []
    },
    setLadderCompareGrid() {
      const ladderMaterials = this.materials.filter((material) => material.quotePriceWay === '1' && material.itemNumber == this.itemNumber)
      // const filters = ladderMaterials.map(material => ({label: material.materialName, value: material.materialNumber}))
      let filetrSupplierList = this.initSupplierFilterList()
      const supplierColumns = this.getSupplierColumns(filetrSupplierList, 'ladderList')
      this.tableColumns = [
        {
          fixed: 'left',
          ...COMPARE_COLUMN
        },
        ...supplierColumns
        // OPERATION_COLUMN
      ]
      const materialCompareMap = {}
      const ladderData = []
      const enquiryHallCompareItems = [
        { ...QUOTA, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作') },
        { ...STATUS, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态') },
        { ...QUOTA_QUANTITY, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitQty`, '拆分数量') },
        { ...QUOTA_SCALE, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比') },
        ...this.enquiryHallCompareItems
      ]
      const compareItems = enquiryHallCompareItems.filter((item) => item.hidden === '0')
      ladderMaterials.forEach((material) => {
        const { itemNumber, materialNumber } = material
        materialCompareMap[`${materialNumber}_${itemNumber}`] = compareItems
        compareItems.forEach((compareItem, index) => {
          const realMaterial = {
            ...JSON.parse(JSON.stringify(material)),
            ...compareItem,
            id: `${materialNumber}${itemNumber}${index}`
          }
          filetrSupplierList.forEach((supplier) => {
            const currentSupplierItem = this.ladderList.find((item) => item.materialNumber === materialNumber && item.itemNumber === itemNumber && item.toElsAccount === supplier.toElsAccount)
            if (currentSupplierItem) {
              const key = `supplier_${currentSupplierItem.toElsAccount}`
              realMaterial[key] = currentSupplierItem[compareItem.columnCode]
              realMaterial[`supplier_${supplier.toElsAccount}_ladderPriceJson`] = currentSupplierItem.ladderPriceJson
              realMaterial[`supplier_${supplier.toElsAccount}_accept`] = (() => {
                if (this.form.packageOptFrist === '1') return true
                let f = !(['2', '8'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                return f
              })()
              realMaterial[`supplier_${supplier.toElsAccount}_reject`] = (() => {
                if (this.form.packageOptFrist === '1') return true
                let f = !(['2', '8', '6', '3'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                return f
              })()
              realMaterial[`supplier_${supplier.toElsAccount}_revoke`] = (() => {
                if (this.form.packageOptFrist === '1') return true
                if (currentSupplierItem.evaluationStatus == '4') return true
                // 提交定价后不能撤销
                if (currentSupplierItem.pricedFlag == '1') return true
                // 审批中 不能撤销
                if (currentSupplierItem.auditStatus == '1') return true
                if (currentSupplierItem.itemStatus === '11') return true // 行悔标 不可操作
                if (currentSupplierItem.itemStatus == '4' || currentSupplierItem.itemStatus == '5') return false // 行接受或拒绝 可操作
                return !(['4', '5'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
              })()
            }
          })
          ladderData.push(realMaterial)
        })
      })
      this.tableData = ladderData
      const array = [0]
      const lengthArr = []
      ladderMaterials.forEach((material, index) => {
        const { itemNumber, materialNumber } = material
        const mLength = materialCompareMap[`${materialNumber}_${itemNumber}`].length
        lengthArr.push(mLength)
        array.push(array[index] + mLength)
      })
      const mergeOperationCells = lengthArr.map((item, index) => ({ col: filetrSupplierList.length + 1, colspan: 1, row: array[index], rowspan: item }))
      this.mergeCells = [...mergeOperationCells]
    },
    setNormalCompareGrid(reload = false) {
      this.spinning = true
      // 过滤常规报价物料
      const normalMaterials = this.materials.filter((material) => material.quotePriceWay === '0' && material.itemNumber == this.itemNumber)
      // 根据过滤的供应商显示多次个供应商列
      let filetrSupplierList = this.initSupplierFilterList()
      const supplierColumns = this.getSupplierColumns(filetrSupplierList, 'normalList')
      // 列设置
      this.tableColumns = [
        { ...COMPARE_COLUMN, fixed: 'left' },
        ...supplierColumns
        // OPERATION_COLUMN
      ]
      // 定义列表数据
      const normalData = []
      // 对比项 + 状态列 + 操作列
      const enquiryHallCompareItems = [
        { ...QUOTA, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作') },
        { ...STATUS, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态') },
        { ...QUOTA_QUANTITY, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitQty`, '拆分数量') },
        { ...QUOTA_SCALE, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比') },
        { ...BARGAIN_REMARK, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bargainRemark`, '议价备注') },
        { ...IS_OFFER, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isOffer`, '是否报价') },
        { ...LAST_TIME_PRICE, columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lastTimePrice`, '上期价格') },
        ...this.enquiryHallCompareItems
      ]
      // 过滤隐藏对比项
      const compareItems = enquiryHallCompareItems.filter((item) => item.hidden === '0')
      // 常规报价物料遍历
      normalMaterials.forEach((material) => {
        const { itemNumber, materialNumber } = material
        // 对比项遍历
        compareItems.forEach((compareItem, index) => {
          const realMaterial = {
            ...JSON.parse(JSON.stringify(material)),
            ...compareItem,
            id: `${materialNumber}${itemNumber}${index}`
          }
          // 过滤完的供应商
          filetrSupplierList.forEach((supplier) => {
            // 根据物料编码，物料行，供应商编码 获取到当前物料数据
            const currentItem = this.normalList.find((item) => item.itemNumber === itemNumber && item.materialNumber === materialNumber && item.toElsAccount === supplier.toElsAccount)
            if (currentItem) {
              // 状态为已报价时,对拆分比字段进行格式化
              if (compareItem.columnCode === 'quotaScale' && currentItem.itemStatus === '2') {
                console.log(currentItem.itemStatus, JSON.parse(JSON.stringify(this.tableData[normalData.length] || {})))
                // 当操作了接受或拒绝或撤回，保留原来值
                if (reload) currentItem.quotaScale = this.tableData[normalData.length][`supplier_${supplier.toElsAccount}`]
                // 将拆分比改为默认为100
                else if (currentItem.quotaScale === 0) currentItem.quotaScale = 100
              }

              console.log('currentItem', currentItem, compareItem.columnCode, currentItem[compareItem.columnCode])
              realMaterial[`supplier_${supplier.toElsAccount}`] = currentItem[compareItem.columnCode]
              // realMaterial[`supplier_${supplier.toElsAccount}_disabled`] = currentItem.itemStatus !== '2'
              realMaterial[`supplier_${supplier.toElsAccount}_accept`] = (() => {
                if (this.form.packageOptFrist === '1') return true
                let f = !(['2', '8'].includes(currentItem.itemStatus) || (currentItem.itemStatus === '11' && currentItem.regretFlag === '2'))
                return f
              })()
              realMaterial[`supplier_${supplier.toElsAccount}_reject`] = (() => {
                if (this.form.packageOptFrist === '1') return true
                let f = !(['2', '8', '6', '3'].includes(currentItem.itemStatus) || (currentItem.itemStatus === '11' && currentItem.regretFlag === '2'))
                return f
              })()
              realMaterial[`supplier_${supplier.toElsAccount}_revoke`] = (() => {
                if (this.form.packageOptFrist === '1') return true
                if (currentItem.evaluationStatus == '4') return true
                // 提交定价后不能撤销
                if (currentItem.pricedFlag == '1') return true
                // 审批中 不能撤销
                if (currentItem.auditStatus == '1') return true
                if (currentItem.itemStatus === '11') return true // 行悔标 不可操作
                if (currentItem.itemStatus == '4' || currentItem.itemStatus == '5') return false // 行接受或拒绝 可操作
                return !(['4', '5'].includes(currentItem.itemStatus) || (currentItem.itemStatus === '11' && currentItem.regretFlag === '2'))
              })()
            }
          })
          normalData.push(realMaterial)
        })
      })
      this.tableData = normalData
      const length = compareItems.length
      const rows = normalData.map((item, index) => index).filter((arr) => arr % length === 0)
      const mergeOperationCells = rows.map((row) => ({ col: filetrSupplierList.length + 1, colspan: 1, row, rowspan: length }))
      this.mergeCells = [...mergeOperationCells]
      this.spinning = false
    },
    setPackageCompareGrid() {
      this.tableColumns = [
        // RADIO_COLUMN,
        ...[
          { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center', fixed: 'left' },
          {
            field: 'itemStatus_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
            width: 56,
            slots: {
              default: ({ row, column }) => {
                let color = (() => {
                  if (row[column.property] == '接受') {
                    return 'green'
                  } else if (['已悔标', '拒绝'].includes(row[column.property])) {
                    return 'red'
                  }
                  return '#409eff'
                })()
                return [<span style={{ color: color }}>{row[column.property]}</span>]
              }
            }
          },
          { sortable: true, field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vendorName`, '供应商'), width: 200 },
          {
            sortable: true,
            field: 'payTermsCode_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paymentCondition`, '付款条件'),
            width: 90
          },
          {
            sortable: true,
            field: 'tradeCondition_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tradeCondition`, '国贸条件'),
            width: 90
          },
          {
            sortable: true,
            field: 'supplierStatus_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierStatus`, '供应商状态'),
            width: 100
          },
          {
            sortable: true,
            field: 'quotaScale',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比'),
            width: 112,
            slots: {
              default: ({ row, column }) => {
                let acceptDisabled = (() => {
                  if (this.form.packageOptFrist === '0') return true
                  let f = !(['2', '8'].includes(row.itemStatus) || (row.itemStatus === '11' && row.regretFlag === '2'))
                  return f
                })()
                const props = { disabled: acceptDisabled, max: 100, min: 0, type: 'number' }
                return [
                  <vxe-input
                    vModel={row[column.property]}
                    {...{ props }}
                  />
                ]
              }
            }
          },
          {
            sortable: true,
            field: 'taxAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'),
            width: 120,
            slots: {
              default({ row, column }) {
                return [<span>{currency(row[column.property], '', 6)}</span>]
              }
            }
          },
          {
            sortable: true,
            field: 'netAmount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未含税金额'),
            width: 120,
            slots: {
              default({ row, column }) {
                return [<span>{currency(row[column.property], '', 6)}</span>]
              }
            }
          }
        ],
        {
          ...OPERATION_COLUMN,
          width: 150,
          slots: {
            default: ({ row }) => {
              let acceptDisabled = (() => {
                if (this.form.packageOptFrist === '0') return true
                let f = !(['2', '8'].includes(row.itemStatus) || (row.itemStatus === '11' && row.regretFlag === '2'))
                return f
              })()
              let revokeDisabled = (() => {
                if (this.form.packageOptFrist === '0') return true
                if (row.evaluationStatus == '4') return true
                // 提交定价后不能撤销
                if (row.pricedFlag == '1') return true
                // 审批中 不能撤销
                if (row.auditStatus == '1') return true
                if (row.itemStatus === '11') return true // 行悔标 不可操作
                if (row.itemStatus == '4' || row.itemStatus == '5') return false // 行接受或拒绝 可操作
                return !(['4', '5'].includes(row.itemStatus) || (row.itemStatus === '11' && row.regretFlag === '2'))
              })()
              let rejectDisabled = (() => {
                if (this.form.packageOptFrist === '0') return true
                let f = !(['2', '8', '6', '3'].includes(row.itemStatus) || (row.itemStatus === '11' && row.regretFlag === '2'))
                return f
              })()
              let requoteDisabled = (() => {
                if (this.form.packageOptFrist === '0') return true
                if (['2', '8', '6', '3'].includes(row.itemStatus)) return false
                if (row.itemStatus === '11' && row.regretFlag === '2') return false
                if (this.reQuoteDisabled) return true
                return true
              })()
              return [
                <div>
                  <a
                    style='margin:0 4px'
                    {...{
                      on: {
                        click: () => {
                          this.handleContract(row, {}, true, 'accept')
                        }
                      }
                    }}
                    disabled={acceptDisabled}
                  >
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accept`, '接受')}
                  </a>
                  <a
                    style='margin:0 4px'
                    {...{
                      on: {
                        click: () => {
                          this.handleContract(row, {}, true, 'reject')
                        }
                      }
                    }}
                    disabled={rejectDisabled}
                  >
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝')}
                  </a>
                  <a
                    style='margin:0 4px'
                    {...{
                      on: {
                        click: () => {
                          this.handleContract(row, {}, true, 'revoke')
                        }
                      }
                    }}
                    disabled={revokeDisabled}
                  >
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销')}
                  </a>
                  <a
                    style='margin:0 4px'
                    {...{
                      on: {
                        click: () => {
                          this.handleContract(row, {}, true, 'requote')
                        }
                      }
                    }}
                    disabled={requoteDisabled}
                  >
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_restatement`, '重报')}
                  </a>
                </div>
              ]
            }
          }
        }
      ]
      let toElsAccounts = this.suppliers.filter((supplier) => supplier.hidden === '0').map((sup) => sup.toElsAccount) || []
      let suppliers = this.packagesList.filter((supplier) => toElsAccounts.includes(supplier.toElsAccount))
      this.tableData = this.initSupplierFilterList(suppliers)
      this.mergeCells = []
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageSize = pageSize
      this.tablePage.currentPage = currentPage
      const fn = this[`set${this.currentTab}Grid`]
      fn && fn()
    },
    initSupplierFilterList(suppliers = []) {
      let suppliersIsShow = suppliers.length > 0 ? suppliers : this.suppliers.filter((supplier) => supplier.hidden === '0')
      let supplierList = []
      if (!this.supKeywords) {
        this.tablePage.total = suppliersIsShow.length
        let start = (this.tablePage.currentPage - 1) * this.tablePage.pageSize
        supplierList = suppliersIsShow.slice(start, start + this.tablePage.pageSize)
      } else {
        let f = suppliersIsShow.filter((item) => {
          return item.supplierName.includes(this.supKeywords.trim()) || item.toElsAccount.includes(this.supKeywords.trim())
        })
        this.tablePage.total = f.length
        let start = (this.tablePage.currentPage - 1) * this.tablePage.pageSize
        supplierList = f.slice(start, start + this.tablePage.pageSize)
      }
      return supplierList
    },
    showAward() {
      return this.form.enquiryStatus !== '10'
    },
    showRegret() {
      return this.form.enquiryStatus !== '10'
    },
    showReQuote() {
      return this.form.enquiryStatus !== '10'
    },
    showSubmit() {
      return this.form.enquiryStatus !== '10'
    },
    supplierSettingOk(suppliers) {
      this.suppliers = suppliers
      this.suppliersHideAccount = suppliers.filter((item) => item.hidden === '1').map((item) => item.toElsAccount)
      const fn = this[`set${this.currentTab}Grid`]
      fn && fn()
    },
    // 动态列操作栏 - 接受 拒绝 撤销
    handleContract(row, sup, packageOpt, handleType, listName) {
      if (handleType == 'requote') {
        this.handleReQuote({ reQuoteWay: 'package', row })
        return
      }
      const contentText = {
        accept: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sureToAcceptSelectedRowData`, '是否确认接受选择的行数据'),
        reject: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sureToAcceptSelectedNotRowData`, '是否确认拒绝选择的行数据'),
        revoke: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLqXiFjcWF_d4057e2f`, '是否确认撤销选择的行数据')
      }
      const callback = () => {
        // 检验拆分百分比总和是否为100
        if (handleType === 'accept') {
          let quotaScale = 0
          this[listName].forEach((material) => {
            if (material.itemStatus == '4' || row.supplierId === material.supplierId) quotaScale += Number(material.quotaScale || '0')
          })
          if (quotaScale > 100) {
            return this.$message.warning(`供应商拆分百分比总和超过100，请重新分配`)
          }
        }

        const { id } = sup
        const listNameitem = listName && this[listName].filter((item) => id == item.supplierId)[0]
        let param = {}
        param['id'] = row.headId
        param['packageOpt'] = packageOpt
        param['purchaseEnquiryItemList'] = id
          ? [
              {
                ...row,
                ...listNameitem,
                quotaScale: handleType === 'reject' ? 0 : Number(listNameitem.quotaScale),
                quotaQuantity: handleType === 'reject' ? 0 : Number(listNameitem.quotaQuantity)
              }
            ]
          : [row]
        this.spinning = true
        postAction(`/enquiry/purchaseEnquiryHead/${handleType}`, param).then(async (res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.getData(true)
          } else {
            this.spinning = false
            this.$message.warning(res.message)
          }
        })
      }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
        content: contentText[handleType],
        onOk() {
          callback && callback()
        }
      })
    },
    submitEvaluationPrice() {
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_DJnu_2e936d93`, '提交核价'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLDJnuW_94074dd6`, '确认提交核价?'),
        onOk: () => {
          this.spinning = true
          postAction('/enquiry/purchaseEnquiryHead/submitEvaluationPrice', { id: this.sourceId })
            .then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.getData()
              } else {
                this.$message.warning(res.message)
                this.spinning = false
              }
            })
            .catch(() => {
              this.spinning = false
            })
        }
      })
    }
  },
  name: 'PurchaseEnquiryHall',
  watch: {
    $route: {
      handler({ path, query }) {
        if (path === '/enquiry/purchaseHall') {
          this.sourceId = this.$route.query.id || ''
          this.getData()
        }
      },
      immediate: true
    }
  }
}
</script>
<style lang="less" scoped>
@red: #f41616;
@blue: #178aff;
.purchase-enquiry-hall {
  background-color: #eaeaea;
  height: 100vh;
  .page-header-button {
    align-items: center;
    background-color: #ffffff;
    height: 40px;
    //   padding-right: 40px;
  }
  .page-header-title {
    background-color: #ffffff;
    margin-top: 6px;
    .title__col {
      align-items: center;
      font-size: 18px;
      font-weight: 700;
      height: 34px;
      line-height: 34px;
      padding-left: 20px;
      .title__col-value {
        color: @blue;
        margin-left: 20px;
      }
      .red {
        color: @red;
      }
    }
  }
  .content {
    padding: 0px 8px 8px;
    .gutter {
      display: flex;
      & + .gutter {
        margin-top: 6px;
      }
      .price {
        flex: 1;
      }
      .history,
      .compare {
        overflow-y: auto;
        flex: 1;
        max-width: 100%;
        .ant-btn {
          & + .ant-btn {
            margin-left: 10px;
          }
        }
      }
      .material {
        width: 488px;
        max-width: calc(100vw - 90px);
      }
    }
    .item {
      padding: 8px;
      background: #fff;
      & + .item {
        margin-left: 8px;
      }
    }
    .ctrlBox {
      padding: 0 8px 8px;
      background: #fff;
    }
  }
}
.custom-resizer {
  width: 100vw;
  overflow: hidden;
  > .multipane-resizer {
    margin: 0;
    left: 0;
    width: 6px;
    // margin-top: 20%;
    position: relative;
    &:before {
      display: block;
      content: '';
      width: 3px;
      height: 40px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -20px;
      margin-left: -1.5px;
      border-left: 1px solid #ccc;
      border-right: 1px solid #ccc;
    }
    &:hover {
      &:before {
        border-color: #999;
      }
    }
  }
  .material {
    max-width: calc(100vw - 90px);
  }
  .compare {
    overflow-y: auto;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    max-width: 100%;
  }
  .item {
    padding: 12px;
    background: #fff;
  }
}
.diplay-flex {
  display: flex;
}
.content-header-button {
  .diplay-flex;
  margin-bottom: 6px;
  justify-content: space-between;
  background: #fff;
  padding: 6px 8px;
}
.flex1 {
  flex: 1;
}
</style>
