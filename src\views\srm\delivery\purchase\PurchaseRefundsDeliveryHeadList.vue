<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showDetailPageNew"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <PurchaseRefundsDeliveryHeadModal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
    <ViewRefundsDeliveryItemModalNew
      v-if="showDetailPageNew"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetailPageNew"
    />
    <!-- 查看页面 -->
    <ViewPurchaseRefundsDeliveryModal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :allowEdit="allowViewToEdit"
      @toEdit="handleEditFromViewPage"
      @hide="hideDetailPage"
    />
    <logistics-timeline 
      :show="logisticsVisible"
      @logisticsHandleOk="logisticsHandleOk"
      @logisticsHandleCancel="handleCancel"
    ></logistics-timeline>
  </div>
</template>

<script>
import PurchaseRefundsDeliveryHeadModal from './modules/PurchaseRefundsDeliveryHeadModal'
import ViewRefundsDeliveryItemModalNew from './modules/ViewRefundsDeliveryItemModalNew'
import ViewPurchaseRefundsDeliveryModal from './modules/ViewPurchaseRefundsDeliveryModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import LogisticsTimeline from '@comp/LogisticsTimeline/LogisticsTimeline'
import {getAction, httpAction} from '@/api/manage'
import layIM from '@/utils/im/layIM.js'
import {REPORT_ADDRESS} from '@/utils/const.js'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseRefundsDeliveryHeadModal,
        ViewPurchaseRefundsDeliveryModal,
        ViewRefundsDeliveryItemModalNew,
        LogisticsTimeline
    },
    data () {
        return {
            logisticsVisible: false,
            showDetailPageNew: false,
            currentEditRow: {},
            printVisible: false,
            submitLoading: false,
            printStyle: 'single',
            printRow: {},
            printNumber: undefined,
            logisticsMsg: {},
            pageData: {
                businessType: 'refundsDelivery',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDescOrReturnOrderCode`, '请输入单据描述或退货单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_refundsDeliveryWay`, '退货方式'),
                        fieldName: 'refundsDeliveryWay',
                        dictCode: 'srmRefundsDeliveryWay',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')
                    }
                ],
                form: {
                    keyWord: '',
                    refundsDeliveryWay: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', type: 'primary', clickFn: this.handleAdd, primary: true, authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SMWWWWF_b44eb4e2`, '获取ERP数据'), icon: 'arrow-down', clickFn: this.getDataByErp, authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:getPurchaseRefundsDeliveryData'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'), icon: 'arrow-up', clickFn: this.pushDataToERP, authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:pushPurchaseRefundsDelivery'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting',  clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'order#purchaseRefundsDeliveryHead:queryById'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showEditCondition, authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:edit'},
                    {type: 'confirm', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refunds`, '出库'), clickFn: this.confirm, allow: this.showConfirmCondition, authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:createRefundMsg'},
                    {type: 'cancel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.cancel, allow: this.showCancelCondition, authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:cancel'},
                    {type: 'close', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_Rl_a72da`, '关闭'), clickFn: this.close, allow: this.showCloseCondition, authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:close'},
                    {type: '', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsInfo`, '物流信息'), clickFn: this.logisticsMessage, allow: this.showLogisticsCondition, authorityCode: 'logistics#trace:express'},
                    {type: '', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.showDelCondition, authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:delete'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat, authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:chat'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord},
                    {
                        type: '',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印'),
                        clickFn: this.serachTemplate,
                        allow: this.showPrintCondition,
                        authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:print'
                    },
                ],
                optColumnWidth: 280
            },
            tabsList: [],
            url: {
                add: '/delivery/purchaseRefundsDeliveryHead/add',
                list: '/delivery/purchaseRefundsDeliveryHead/list',
                close: '/delivery/purchaseRefundsDeliveryHead/close',
                cancel: '/delivery/purchaseRefundsDeliveryHead/cancel',
                delete: '/delivery/purchaseRefundsDeliveryHead/delete',
                express: '/api/trace/express',
                columns: 'PurchaseRefundsDeliveryHead',
                getDataByErpUrl: '/delivery/purchaseRefundsDeliveryHead/getPurchaseRefundsDeliveryData',
                pushDataToERPUrl: '/delivery/purchaseRefundsDeliveryHead/pushPurchaseRefundsDelivery'
            }
        }
    },
    computed: {
      allowViewToEdit() {
          if(!!this.viewRow) return !this.showEditCondition(this.viewRow);
          return false;
      }
    },
    watch: {
        viewRow(val) {
            console.log("viewRow", val)
        }
    },
    mounted () {
        // this.serachTabs('srmRefundsDeliveryStatus', 'refundsDeliveryStatus')
        this.serachCountTabs('/delivery/purchaseRefundsDeliveryHead/counts')
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.refundsDeliveryNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'PurchaseRefundsDeliveryHead', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if((row.refundsDeliveryStatus == '0')) {
                return true
            }else {
                return false
            }
        },
        showLogisticsCondition () {
            return false
        },
        showEditCondition (row) {
            if(row.refundsDeliveryStatus == '0'||row.refundsDeliveryStatus == '3') {
                return false
            }else {
                return true
            }
        },
        showDelCondition (row) {
            if(row.refundsDeliveryStatus == '0') {
                return false
            }else {
                return true
            }
        },
        showConfirmCondition (row) {
            if(row.refundsDeliveryStatus == '2'||row.refundsDeliveryStatus=='5'||row.refundsDeliveryStatus=='8') {
                return false
            }else {
                return true
            }
        },
        showCancelCondition (row) {
            if(row.refundsDeliveryStatus == '1'||row.refundsDeliveryStatus == '2'||row.refundsDeliveryStatus == '3' || row.refundsDeliveryStatus == '8') {
                return false
            }else {
                return true
            }
        },
        showCloseCondition (row) {
            if(this.btnInvalidAuth('refundsDelivery#purchaseRefundsDeliveryHead:close')){
                return true
            }
            if(row.refundsDeliveryStatus == '5') {
                return false
            }else {
                return true
            }
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (){
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        confirm (row){
            this.currentEditRow = row
            this.showDetailPageNew = true
        },
        close (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_close`, '关闭'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherClose`, '确认是否关闭?'),
                onOk: function () {
                    that.postUpdateData(that.url.close, row)
                }
            })
        },
        cancel (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_close`, '关闭'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        logisticsMessage (row){
            if (row.trackingNumber == null) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsDocNoIseEmpty`, '物流单号为空'))
                return
            } else {
                if (row.trackingNumber.startsWith('SF') && row.refundsPrincipalPhone == '') {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '顺丰单号查询需要输入发货/收货人手机号'))
                    return
                }
                this.$refs.listPage.confirmLoading = true
                getAction(this.url.express, {
                    'expressNumber': row.trackingNumber,
                    'phone': row.refundsPrincipalPhone
                }, 'get').then((res) => {
                    if (res.success) {
                        this.logisticsMsg = res.result
                        this.logisticsVisible = true
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.$refs.listPage.confirmLoading = false
                })
            }
        },
        hideDetailPage (){
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }else{
                this.$store.dispatch('SetTabConfirm', false)
                this.showDetailPage = false
            }
        },
        hideDetailPageNew (){
            this.showDetailPageNew = false
        },
        logisticsHandleOk () {
            this.logisticsVisible= false
        },
        handleCancel () {
            this.logisticsVisible= false
        },
        showPrintCondition (row) {
            if (row.refundsDeliveryStatus == '5' || row.refundsDeliveryStatus == '6') {
               return false
            } else {
               return true
            }
        },
        serachTemplate (row) {
            this.printRow = row
            this.printStyle = 'single'
            this.openModal(row)
        },
        openModal (row) {
            this.queryPrintTemList(row.busAccount).then(res => {
                if (res.success) {
                    if (res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.id,
                                printId: item.printId,
                                printName: item.printName,
                                title: item.templateName,
                                printType: item.printType,
                                param: item.param
                            }
                        })
                        this.printNumber = ''
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length === 1) {
                            this.printNumber = this.templateOpts[0].value
                            this.selectedPrintTemplate()
                        } else {
                            // 有多个模板先选择在新建
                            this.printVisible = true
                        }
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        queryPrintTemList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: 'purchaseRefunds'}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        selectedPrintTemplate () {
            if (this.printStyle == 'batch') {
                if (this.printNumber) {
                    let that = this
                    that.submitLoading = true
                    let template = this.templateOpts.filter(item => {
                        return item.value == that.printNumber
                    })
                    let param = {
                        objectList: that.$refs.listPage.$refs.listGrid.getCheckboxRecords(),
                        parameter: template[0].param,
                        ureportName: template[0].printName
                    }
                    let urlPrint = this.url.printBatchs
                    if (template[0].printType == 'jimu') {
                        param = {
                            objectList: that.$refs.listPage.$refs.listGrid.getCheckboxRecords(),
                            parameter: template[0].param,
                            excelConfigId: template[0].printId
                        }
                        urlPrint = this.url.printBatchsJimu
                    }
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_zRfW_2ef2f473`, '批量打印'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLPmistFuGWWWfW_a8c51d60`, '确认将此选中单据导出PDF打印'),
                        onOk: function () {
                            //that.currentRow.tacticsObject = 'person'
                            that.downLoadPdfprint(urlPrint, param)
                        }
                    })
                }
            } else {
                if (this.printNumber) {
                    const that = this
                    this.submitLoading = true
                    let template = this.templateOpts.filter(item => {
                        return item.value == that.printNumber
                    })
                    let params = {
                        templateNumber: this.printNumber,
                        printId: template[0].printId,
                        printName: template[0].printName,
                        printType: template[0].printType,
                        param: template[0].param
                    }
                    that.printVisible = false
                    that.submitLoading = false
                    let rowItem = this.printRow
                    this.printRow = {}
                    let urlParam = ''
                    if (params.param) {
                        let json = JSON.parse(params.param)
                        Object.keys(json).forEach((key, i) => {
                            urlParam += '&' + key + '=' + rowItem[json[key]]
                        })
                    }
                    console.log(urlParam)
                    if (params.printType == 'ureport') {
                        const token = this.$ls.get('Access-Token')
                        //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
                        const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:' + params.printName + '&token=' + token + urlParam
                        window.open(url, '_blank')
                    }
                    if (params.printType == 'jimu') {
                        const token = this.$ls.get('Access-Token')
                        const url = REPORT_ADDRESS + '/els/report/jmreport/view/' + params.printId + '?token=' + token + urlParam
                        //const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.title+'&token=' + token+'&id='+rowItem.id
                        window.open(url, '_blank')
                    }
                }
            }
        },
        downLoadPdfprint (url, row) {
            this.$refs.listPage.confirmLoading = true
            postAction(url, row, {
                responseType: 'arraybuffer'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('target', '_blank')
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
                // this.searchEvent()
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
    }
}
</script>
