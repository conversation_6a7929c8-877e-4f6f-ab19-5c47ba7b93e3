<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage "
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 详情界面 -->
    <SaleQualityCheckDetail
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleQualityCheckDetail from './modules/SaleQualityCheckDetail'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        SaleQualityCheckDetail
    },
    data () {
        return {
            currentRow: {},
            pageData: {
                businessType: 'qualityCheck',
                form: {
                    checkNumber: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkNumber`, '检测单号'),
                        fieldName: 'checkNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterCheckNumber`, '请输入检测单号')
                    }
                    // {
                    //     type: 'select',
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                    //     fieldName: 'ebiddingStatus',
                    //     dictCode: 'srmEbiddingStatus',
                    //     placeholder: '请选择单据状态'
                    // }
                ],
                optColumnWidth: 100,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'quanlityCheck#saleQuantityCheckHead:query'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, authorityCode: 'quanlityCheck#saleQuantityCheckHead:chat'}
                ]
            },
            tabsList: [],
            url: {
                list: '/quality/saleQualityCheckHead/list',
                columns: 'PurchaseQualityCheckHead'
            }
        }
    },
    mounted () {
        // this.serachTabs('srmCheckStatus', 'requestStatus')
        // this.serachCountTabs('/quality/saleQualityCheckHead/counts')
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.checkNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SaleQualityCheck', url: this.url || '', recordNumber})
        },
        allowEdit (row) {
            //审批中禁用
            if(row.auditStatus == '1'){
                return true
            }
            //新建不禁用
            return row.checkStatus != '0'
        }
    }
}
</script>
<style lang="less" scoped>
.radio-wrap {
    display: block;
    height: 30px;
}
</style>