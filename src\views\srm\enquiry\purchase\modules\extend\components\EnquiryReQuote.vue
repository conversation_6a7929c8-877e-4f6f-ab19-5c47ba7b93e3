<template>
  <div>
    <a-modal
      centered
      :mask-closable="false"
      :title="title"
      :visible="visible"
      :width="580"
      @cancel="visible = false"
      :confirmLoading="loading"
      @ok="handleOk">
      <a-date-picker
        show-time
        style="width: 100%"
        value-format="YYYY-MM-DD HH:mm:ss"
        v-model="form.quoteEndTime"/>
    </a-modal>
    <field-select-modal ref="fieldSelectModal"/>
    <a-modal
      centered
      :title="modalTitle"
      :width="960"
      :visible="gridVisible"
      :maskClosable="false"
      @cancel="gridVisible = false"
      @ok="selectedOk">
      <vxe-grid
        border
        highlight-hover-row
        max-height="350"
        min-height="200"
        ref="selectGrid"
        resizable
        row-id="id"
        show-overflow
        size="small"
        :checkbox-config="checkedConfig"
        :columns="columns"
        :data="tableData">
        <template #empty>
          <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
        </template>
      </vxe-grid>
    </a-modal>
  </div>
</template>

<script>
import {postAction} from '@api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
export default {
    components: {fieldSelectModal},
    data (){
        return{
            checkedConfig: {highlight: true, reserve: true, trigger: 'row'},
            columns: [],
            form: {quoteEndTime: undefined},
            gridVisible: false,
            modalTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SelectData`, '选择数据'),
            tableData: [],
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterNewReportPriceDeline`, '请输入新的报价截止时间'),
            visible: false,
            loading: false
        }
    },
    methods: {
        fieldSelectOk (checkboxRecords){
            if(this.form.reQuoteWay === 'material'){
                this.$set(this.form, 'purchaseEnquiryItemList', checkboxRecords)
            }
            if(this.form.reQuoteWay === 'supplier'){
                this.$set(this.form, 'enquirySupplierListList', checkboxRecords)
            }
            this.visible = true
        },
        handleOk (){
            const params = this.form
            if(!params.quoteEndTime){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationDeadlineCannotBeBlank`, '报价截止时间不能为空'))
                return
            }
            if(params.purchaseEnquiryItemList && params.purchaseEnquiryItemList.length > 0) {
                params.purchaseEnquiryItemList.map(item => {
                    item.quoteEndTime = params.quoteEndTime
                })
            }
            this.visible = false
            this.$parent.spinning = true
            this.loading = true
            postAction('/enquiry/purchaseEnquiryHead/reQuote', params).then(res => {
                if(res.success){
                    this.$notification.success({description: res.message, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tkLRW_b20577c3`, '操作成功')})
                    this.$emit('success')
                }else{
                    this.$notification.warning({description: res.message, message: '警告'})
                    this.$parent.spinning = false
                }
                this.loading = false
            }).catch(() => {
                this.loading = false
                this.$parent.spinning = false
            })
        },
        openAll ({headId}){
            this.$set(this.form, 'id', headId)
            this.$set(this.form, 'reQuoteWay', 'all')
            this.visible = true
        },
        openMaterial ({headId}){
            this.$set(this.form, 'id', headId)
            this.$set(this.form, 'reQuoteWay', 'material')
            const modalColumns = [
                {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150},
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150},
                {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200},
                {field: 'purchaseCycle', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), width: 200},
                {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200}
            ]
            const modalUrl = '/enquiry/purchaseEnquiryHead/reQuoteMaterialList'
            this.$refs.fieldSelectModal.open(modalUrl, {headId}, modalColumns, 'multiple')
        },
        openSupplier ({headId}){
            this.$set(this.form, 'id', headId)
            this.$set(this.form, 'reQuoteWay', 'supplier')
            const modalColumns = [
                {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150},
                {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200},
                {field: 'needCoordination_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), width: 200}
            ]
            const modalUrl = '/enquiry/purchaseEnquiryHead/reQuoteSupplierList'
            this.$refs.fieldSelectModal.open(modalUrl, {headId}, modalColumns, 'multiple')
        },
        openUser ({enquiryItemList, headId}){
            this.$set(this.form, 'id', headId)
            this.$set(this.form, 'reQuoteWay', 'user')
            this.$set(this.form, 'packageOpt', false)
            this.columns = [
                {fixed: 'left', type: 'checkbox', width: 40},
                {fixed: 'left', type: 'seq', width: 40},
                {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150},
                {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200},
                {field: 'needCoordination_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), width: 200}
            ]
            this.tableData = enquiryItemList
            this.gridVisible = true
        },
        openOfPackage ({enquiryItemList, headId}) {
            this.$set(this.form, 'purchaseEnquiryItemList', enquiryItemList)
            this.$set(this.form, 'reQuoteWay', 'user')
            this.$set(this.form, 'packageOpt', true)
            this.$set(this.form, 'id', headId)
            this.visible = true
        },
        selectedOk (){
            this.gridVisible = false
            const checkboxRecords = this.$refs.selectGrid.getCheckboxRecords()
            this.$set(this.form, 'purchaseEnquiryItemList', checkboxRecords)
            this.visible = true
        }
    },
    name: 'EnquiryReQuote'
}
</script>
