<!--
 * @Author: your name
 * @Date: 2021-12-07 16:43:52
 * @LastEditTime: 2021-12-07 17:24:17
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \srm-frontend\public\pay.html
-->
<html>

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>提交到富友交易系统</title>
</head>
<script type="text/javascript">
	
	function submitForm(){
		var urlParameters = location.search;
		var data = {}
		if (urlParameters.indexOf("?") != -1) {
			// var parameters = decodeURI(urlParameters.substring(1));
			// var parameterArray = parameters.split("&");
			// for (let i = 0; i < parameterArray.length; i++) {
			// 	data[parameterArray[i].split("=")[0]] = parameterArray[i].split("=")[1];
			// }
			// alert(JSON.stringify(data))
			let payDataStr =localStorage.getItem('payDataParams')
			data = JSON.parse(payDataStr)
			for (let key in data) {
				let tag = document.getElementsByName(`${key}`)
				tag[0].value = data[key]
				// alert(key + ':----' + tag[0].value)
			}
			
		}
		document.getElementById("form").submit();
	} 
</script>

<body onload="javascript:submitForm();">
	<form name="pay" method="post" action="https://aggpc-test.fuioupay.com/inteGate.fuiou" id="form">
		<input type="hidden" value = '' name="mchnt_cd"/>
		<input type="hidden" value = '' name="order_date"/>
		<input type="hidden" value = '' name="order_id"/>
		<input type="hidden" value = '' name="order_amt"/>
		<input type="hidden" value = '' name="page_notify_url"/>
		<input type="hidden" value = '' name="back_notify_url"/>
		<input type="hidden" value = '' name="goods_name"/>
		<input type="hidden" value = '' name="pay_type"/>
		<input type="hidden" value = '' name="rem"/>
		<input type="hidden" value = '' name="sign"/>
	</form>
</body>

</html>