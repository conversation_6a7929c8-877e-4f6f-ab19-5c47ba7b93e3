<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage "
      ref="listPage"
      modelLayout="seal"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleElsSealsEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <ElsSealsDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>

</template>
<script>
// 新列表模板
import { ListMixin } from './list/ListMixin'
import SaleElsSealsEdit from './modules/SaleElsSealsEdit'
import ElsSealsDetail from './modules/ElsSealsDetail'
export default {
    mixins: [ListMixin],
    components: {
        SaleElsSealsEdit,
        ElsSealsDetail
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCRLWWeqR_511a0e32`, '(公司名称/印章别名)')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esign#saleEsignSeals:detail2'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sealUpload`, '印章上传'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esign#saleEsignSeals:upload'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/esign/elsSeals/list',
                delete: '/esign/elsSeals/delete',
                columns: 'ElsSealsList'
            }
        }
    },
    methods: {
        allowEdit (row){
            if(row.sealId){
                return true
            }
            return false
        }
    }
}
</script>