<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage && !showAddPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />

    <!-- 编辑 -->
    <PurchaseBidManagerEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      :queryData="queryData"
      @hide="hideEditPage"
    />
    <PurchaseBidManagerAdd
      v-if="showAddPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      :queryData="queryData"
      @hide="hideEditPage"
    />
    <!-- 编辑 -->
    <PurchaseBidManagerDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>

<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import PurchaseBidManagerEdit from './modules/PurchaseBidManagerEdit'
import PurchaseBidManagerAdd from './modules/PurchaseBidManagerAdd'
import PurchaseBidManagerDetail from './modules/PurchaseBidManagerDetail'
import { postAction, getAction} from '@/api/manage'

export default {
    name: 'SignUpManagerList',
    mixins: [ ListMixin ],
    components: {
        PurchaseBidManagerEdit,
        PurchaseBidManagerDetail,
        PurchaseBidManagerAdd
    },
    data () {
        return {
            showAddPage: false,
            showEditPage: false,
            showDetailPage: false,
            pageData: {
                businessType: 'SupplierTenderProjectPurchaseBid',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNYBtLRLWdIAySdIRL_3f47ec27`, '请输入招标单位名称、项目编号或项目名称')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary' },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    // { type: 'edit', title: '购买文件', clickFn: this.handleEdit},
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'tender#supplierTenderProjectPurchaseBid:add', clickFn: this.handleView},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'tender#supplierTenderProjectPurchaseBid:edit', clickFn: this.handleEdit, allow: this.showEdit},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supBiddingHall`, '投标大厅'), authorityCode: 'tender#supplierTenderProjectPurchaseBid:toTenderHall', clickFn: this.handleTender, allow: this.showTender}
                ]
            },
            url: {
                list: '/tender/sale/supplierTenderProjectPurchaseBid/list',
                columns: 'saleTenderProjectPurchaseBidList',
                getNoticeDataUrl: '/tender/sale/supplierTenderProjectPurchaseBid/queryPurchaseBidInfo'

            },
            queryData: null
        }
    },
    created () {
        // const queryData = this.$route.query
        // this.queryData = queryData
        // this.showEditPage = JSON.stringify(queryData) != '{}' ? true : false
    },
    watch: {
        '$route.query': {
            immediate: true,
            handler (value) {
                let {subpackageId} = value
                debugger
                if (subpackageId && !this.queryData) {
                    value['noticeId'] = value['businessId']
                    // 根据公告类型判断是预审还是后审
                    if (value['noticeType'] == '1' || value['noticeType'] == '4') {
                        value['checkType'] = '0'
                    } else {
                        value['checkType'] = '1'
                    }
                    this.queryData = Object.assign({}, value)
                    this.getNoticeData(value)
                }else{
                    if(!value){
                        this.handleView(value)
                    }
                }
            }
        }
    },
    methods: {
        showEdit (row) {
            const {status} = row
            return status != '0' ? true : false
        },
        showTender (row) {
            return row.status != '2'
        },
        hideEditPage () {
            this.showDetailPage = false
            this.showAddPage = false
            this.showEditPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            this.queryData = {}
        },
        handleAdd () {
            this.showAddPage = true
            this.currentEditRow = {}
        },
        getNoticeData (value) {
            console.log(value)
            const checkType = value.checkType
            value.checkType && delete value.checkType
            
            getAction(this.url.getNoticeDataUrl, value, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                if(res.success) {
                    if (res.result.id) {
                        if (res.result.status != '0') {
                            this.handleView(res.result)
                        } else {
                            this.handleEdit(res.result)
                        }
                    } else {
                        this.handleAdd(value)
                    }
                }
            })
        },
        handleTender (row) {
            row['applyRole'] = '2'
            this.$ls.set('SET_TENDERCURRENTROW', row)
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/tenderHall',
                query: {
                    _t
                }
            })
            window.open(routeUrl.href, '_blank')
        }
    },
    mounted () {
        // this.serachTabs('srmReconciliationStatus', 'reconciliationStatus')
        this.serachCountTabs('/tender/sale/supplierTenderProjectPurchaseBid/counts')
    }
}
</script>
