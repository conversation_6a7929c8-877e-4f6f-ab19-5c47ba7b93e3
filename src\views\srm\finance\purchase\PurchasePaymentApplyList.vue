<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showDetailPage && !showEditPage && !showWriteOffPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"
    />
    <!-- 编辑界面 -->
    <PurchasePaymentApplyEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/> 
    <!-- 详情界面 -->
    <PurchasePaymentApplyDetail 
      v-if="showDetailPage"
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
    <!-- 手工核销界面 -->
    <PurchasePaymentApplyWriteOff
      v-if="showWriteOffPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <record-modal
      v-model="recordShowVisible"
      :currentEditRow="currentEditRow"
    />
  </div>
</template>
<script>
import PurchasePaymentApplyDetail from './modules/PurchasePaymentApplyDetail'
import PurchasePaymentApplyEdit from './modules/PurchasePaymentApplyEdit'
import PurchasePaymentApplyWriteOff from './modules/PurchasePaymentApplyWriteOff'
import RecordModal from './components/RecordModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import { httpAction, getAction } from '@/api/manage'
import layIM from '@/utils/im/layIM.js'
export default {
    mixins: [ListMixin],
    components: {
        PurchasePaymentApplyDetail,
        PurchasePaymentApplyEdit,
        PurchasePaymentApplyWriteOff,
        RecordModal
    },
    data () {
        return {
            showEditPage: false,
            recordShowVisible: false,
            showWriteOffPage: false,
            rowPaymentData: [],
            tabsList: [],
            pageData: {
                businessType: 'paymentApply',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNBVUVty_5712f75`, '请输入付款申请单号')
                    }
                ],
                form: {
                    keyWord: '',
                    name: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),  icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'paymentApply#purchasePaymentApplyHead:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'paymentApply#purchasePaymentApplyHead:view'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopyData, authorityCode: 'paymentApply#purchasePaymentApplyHead:copy'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showEditCondition, authorityCode: 'paymentApply#purchasePaymentApplyHead:edit'},
                    {type: 'cancel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.handleCancel, allow: this.showCancelCondition, authorityCode: 'paymentApply#purchasePaymentApplyHead:cancel'},
                    {type: 'synchr', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_synchronization`, '同步'), clickFn: this.handleSynchr, allow: this.showSynchrCondition, authorityCode: 'paymentApply#purchasePaymentApplyHead:synchr'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RBRL_2f35f23f`, '支付确认'), clickFn: this.handleView, allow: this.showPaymentConfirmationCondition, authorityCode: 'paymentApply#purchasePaymentApplyHead:paymentConfirmation'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lRnX_2e1c2242`, '手工核销'), clickFn: this.handleWriteOff, allow: this.showPaymentWriteOff, authorityCode: 'paymentApply#purchasePaymentApplyHead:writeOff'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.showDeleteCondition, authorityCode: 'paymentApply#purchasePaymentApplyHead:delete'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.showHandleRecord}
                ],
                optColumnWidth: 250
            },
            url: {
                add: '/finance/purchasePaymentApplyHead/add',
                copyData: '/finance/purchasePaymentApplyHead/copyData',
                list: '/finance/purchasePaymentApplyHead/list',
                delete: '/finance/purchasePaymentApplyHead/delete',
                cancel: '/finance/purchasePaymentApplyHead/cancel',
                synchr: '/finance/purchasePaymentApplyHead/synchr',
                columns: 'PurchasePaymentApplyHead'
            }
        }
    },
    created (){
        this.Jump()
    },
    mounted () {
        this.serachCountTabs('/finance/purchasePaymentApplyHead/counts')
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.paymentApplyNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'PurchasePaymentApply', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if((row.sendStatus == '1')) {
                return false
            }else {
                return true
            }
        },
        handleCopyData (row) {
            let that = this
            that.$refs.listPage.loading  = true
            getAction(this.url.copyData, { id: row.id })
                .then(res => {
                    if (res.success) {
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BRLR_29bbbf18`, '复制成功'))
                        that.$refs.listPage.handleQuery()
                    } else {
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    that.$refs.listPage.loading = false
                })

        },
        // 返回按钮
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showWriteOffPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
        },
        hideDetailPage (){
            this.showDetailPage = false
        },
        showHandleRecord (row){
            this.currentEditRow = row
            this.recordShowVisible = true
        },
        showEditCondition (row) {
            if(row.paymentApplyStatus=='2'){
                return true
            }            
            if(this.btnInvalidAuth('paymentApply#purchasePaymentApplyHead:edit')){
                return true
            }
            if((row.auditStatus == '0'||row.auditStatus == '4'||row.auditStatus == '3')) {
                return false
            }else {
                return true
            }
        },
        showCancelCondition (row) {
            if(this.btnInvalidAuth('paymentApply#purchasePaymentApplyHead:cancel')){
                return true
            }
            if(row.paymentApplyStatus === '1' || row.paymentApplyStatus === '2') {
                //置灰
                return true
            }else {
                return false
            }
        },
        showSynchrCondition (row) {
            if(this.btnInvalidAuth('paymentApply#purchasePaymentApplyHead:synchr')){
                return true
            }
            if(row.sendStatus == '1') {
                return true
            }else {
                return false
            }
        },
        showPaymentConfirmationCondition (row) {

            if( row.paymentApplyStatus === '0'&& (row.auditStatus ==='2'||row.auditStatus==='4')){
                return false
            }else {
                //置灰
                return true
            }
        },
        showPaymentWriteOff (row) {
            let flag =  row.paymentApplyType!=='0' && row.paymentApplyStatus ==='1' && (row.paymentAmount + row.prePayWriteOffAmount != row.taxTotalAmount)
            return !flag
        },
        showDeleteCondition (row){
            if(this.btnInvalidAuth('paymentApply#purchasePaymentApplyHead:delete')){
                return true
            }
            if((row.auditStatus == '0')){
                return false
            }else{
                return true
            }
        },
        handleCancel (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voidSelectedData`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        handleWriteOff (row) {
            this.currentEditRow = row
            this.showWriteOffPage = true
        },

        handleSynchr (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_synchronization`, '同步'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherSynchronize`, '确认是否将单据同步至供应商?'),
                onOk: function () {
                    that.postUpdateData(that.url.synchr, row)
                }
            })
        },
        postUpdateData (url, row){
            this.$refs.listPage.loading  = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.loading = false
            })
        },
        cancelCallBack () {
            this.showEditPage = true
            this.showDetailPage = false
            this.searchEvent()
        },
        Jump (){
        // 采购对账付款申请跳转打开编辑页
            const _this = this
            if(localStorage.getItem('initiatePayment')){
                let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: this.pageData.businessType}
                this.rowPaymentData.push(JSON.parse(localStorage.getItem('initiatePayment')))
                getAction('/template/templateHead/getListByType', params)
                    .then(res => {
                        if(res.result.length > 0) {
                            let templateList = res.result
                            let chooseList = []
                            let defaultList = templateList.filter(n => n.defaultType === '1')
                            if(defaultList.length > 0){
                            //取默认模板类型,否则取默认第一个模板
                                chooseList = defaultList
                            }else{
                                chooseList = [templateList[0]]
                            }
                            const param = {
                                templateNumber: chooseList[0].templateNumber,
                                templateName: chooseList[0].templateName,
                                templateVersion: chooseList[0].templateVersion,
                                templateAccount: chooseList[0].elsAccount,
                                elsAccount: _this.$ls.get('Login_elsAccount'),
                                toElsAccount: this.rowPaymentData[0].toElsAccount,
                                supplierName: this.rowPaymentData[0].supplierName,
                                company: this.rowPaymentData[0].company,
                                purchaseOrg: this.rowPaymentData[0].purchaseOrg,
                                currency: this.rowPaymentData[0].currency,
                                paymentClause: this.rowPaymentData[0].paymentClause,
                                payWay: this.rowPaymentData[0].payWay
                            }
                            if(this.rowPaymentData[0].sourceType === 'order' || this.rowPaymentData[0].sourceType === 'contact') {
                                param.paymentApplyType = '0'
                            } else if(this.rowPaymentData[0].sourceType === 'performanceReconciliation' || this.rowPaymentData[0].sourceType === 'reconciliation') {
                                param.paymentApplyType = '1'
                            }
                            param.paymentApplyOtherList = []
                            _this.selectedTemplate(param)
                        } else {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                            localStorage.removeItem('initiatePayment')
                        }
                    })
            }
        }
    },
    activated (){
        this.Jump()
    }
}
</script>