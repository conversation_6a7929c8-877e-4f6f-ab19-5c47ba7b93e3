<template>
  <div style="height: 100%">
    <setp-lay-out
      :ref="businessRefName"
      :canchangeStep="canchangeStep"
      :currentEditRow="formData"
      :sourceGroups="sourceGroups"
      :pageStatus="pageStatus">
      <template #bidWinningAffirmPriceItemVoList="{ slotProps }">
        <div
          :is="awardName"
          :pageStatus="pageStatus"
          ref="awardName"
          :resultData="formData">
        </div>
      </template>
      <template #reason="{ slotProps }">
        <div>
          <a-row class="margin-t-20">
            <a-col :span="3">
              <div class="label">
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_AHjWlRW_ba1cc783`, '变更原因说明:') }}
              </div>
            </a-col>
            <a-col :span="12">
              <a-textarea
                :disabled="pageStatus == 'detail'"
                v-model="formData.reason"
                :auto-size="{ minRows: 2, maxRows: 6 }"></a-textarea>
            </a-col>
          </a-row>
          <a-row class="margin-t-20">
            <a-col :span="3">
              <div class="label">
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_BIW_23da548`, '附件:') }}
              </div>
            </a-col>
            <a-col 
              :span="20"
              :offset="3">
              <div
                v-for="fileItem in formData.attachmentList"
                :key="fileItem.id">
                <span>{{ fileItem.fileName }}</span>
                <a-button 
                  @click="preViewEvent(fileItem)"
                  type="link">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a-button>
                <a-button 
                  @click="downloadEvent(fileItem)"
                  type="link" 
                  style="padding-left: 0;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a-button>
              </div>
            </a-col>
          </a-row>
        </div>
      </template>
    </setp-lay-out>
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"/>
  </div>
</template>
  
<script>
import setpLayOut from '@views/srm/bidding_new/BiddingHall/components/setpLayOut'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import flowViewModal from '@comp/flowView/flowView'
import normalAward from './modules/normalAward'
import materialAward from './modules/materialAward'
import supplierAward from './modules/supplierAward'
import finalQuoteList from './finalQuoteList'
export default {
    name: 'BiddingFile',
    components: {
        finalQuoteList,
        flowViewModal,
        setpLayOut,
        normalAward,
        supplierAward,
        materialAward,
        titleTrtl
    },
    computed: {
        pageStatus () {
            return 'detail'
        }
    },
    props: {
        formData: {
            type: Object,
            default () {
                return {}
            }
        },
        canEdit: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            refresh: true,
            showHeader: true,
            canchangeStep: true,
            flowView: false,
            flowId: false,
            awardNameBtnView: {
                'normalAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pRdXlB_8d34dee3`, '按供应商授标'), type: 'primary', click: () => {this.changeAwardName('supplierAward')}, attrs: {type: 'primary'} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pSLRHlB_e8a46290`, '按物料明细授标'), type: 'primary', click: () => {this.changeAwardName('materialAward')}, attrs: {type: 'primary'} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: this.goBack }
                ],
                'materialAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ],
                'supplierAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ]
            },
            sourceGroups: [
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n__RIsBL_cbe8b614`, '确定中标人'),
                    groupNameI18nKey: '',
                    groupCode: 'bidWinningAffirmPriceItemVoList',
                    groupType: 'item',
                    show: true,
                    sortOrder: '1'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjW_27a9ee3d`, '变更原因'),
                    groupNameI18nKey: '',
                    groupCode: 'reason',
                    groupType: 'head',
                    show: true,
                    sortOrder: '2'
                }
            ],
            businessRefName: 'businessRef',
            awardName: 'normalAward',
            status: '',
            confirmLoading: false,
            url: {
                queryById: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryById',
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId'
            }
        }
    },
    methods: {
        goBack () {
            this.$emit('back')
        },
        showFlow (){
            this.flowId = this.formData.flowId
            this.flowView = true
        },
        changeAwardName (name) {
            this.confirmLoading = true
            // 给个切换loading，假装一下加载
            setTimeout(() => {
                this.awardName = name
                this.confirmLoading = false
            }, 100)
        }
    }
}
</script>
<style lang="less">
.margin-t-20{
  margin-top: 20px;
}
.label{
  text-align:right;
  padding-right: 10px;
}
:deep(.ant-upload-list){
    display: none;
}
</style>
  