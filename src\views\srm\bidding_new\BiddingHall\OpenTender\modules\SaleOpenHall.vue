<template>
  <div>
    <a-spin :spinning="loading">
      <navStautsList
        :navStautsList="navStautsList"
        :formData="formData"/>
      <newsBox
        :openInfoRecordsList="openInfoRecordsList"
        :formData="formData"/>
      <floorCtrlBtn :floorBtns="floorBtns">
        <template slot="left">
          <div
            class="deadline"
            v-if="deadline">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_ywutKW_7f0992b6`, "解密倒计时：") }}</span>
            <countdown
              :time="deadline"
              @end="handleFinish"
            >
              <template slot-scope="props"> <span class="fontColor">{{ props.hours }} : {{ props.minutes }} : {{ props.seconds }}</span></template>
            </countdown>
          </div>
        </template>
      </floorCtrlBtn>
    </a-spin>
    <a-modal
      v-drag    
      v-model="showDecrypt"
      width="400px"
      @ok="handleDecrypt"
      :confirmLoading="loading"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_yw_110e43`, '解密')">
      <a-form-model
        :model="form"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_eBQIwo_2278d69c`, '投标文件密码')"
          required>
          <a-input-password
            v-model="password" />
        </a-form-model-item >
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_CAwo_2afdd9`, 'CA密码')"
          required
          v-if="isCa">
          <a-input-password
            v-model="caPassword" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
      v-drag    
      v-model="showPriceOpeningsGrid"
      width="800px"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_i18n_dict_vBIBB_6b38ab39`, '开标一览表')">
      <template slot="footer">
        <a-button @click="() => {this.showPriceOpeningsGrid = false}">{{ $srmI18n(`${$getLangAccount()}#i18n_title_close`, '关闭') }}</a-button>
      </template>
      <priceOpeningsGrid
        v-if="showPriceOpeningsGrid"
        :priceOpeninData="priceOpeninData" />
    </a-modal>
  </div>
</template>
<script>
import moment from 'moment'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { postAction as ukAction } from '@views/srm/bidding_new/TenderHall/DocumentSubmitOfCa/api/request'
import eventBus from '@/utils/eventBus.js'
import MyWebSocket from '../websocket/websocket.js'
import layIM from '@/utils/im/layIM.js'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import priceOpeningsGrid from './components/priceOpeningsGrid'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import countdown from '@/components/countdown/index.js'
import navStautsList from './components/navStautsList'
import newsBox from './components/newsBox'
import floorCtrlBtn from './components/floorCtrlBtn'

const HEART_BEAT_CONFIG = {
    time: 30 * 1000, // time：心跳时间间隔
    timeout: 3 * 1000, // timeout：心跳超时间隔
    reconnect: 10 * 1000 // reconnect：断线重连时
}
export default {
    mixins: [baseMixins],
    components: {
        priceOpeningsGrid,
        countdown,
        navStautsList,
        newsBox,
        floorCtrlBtn
    },
    data () {
        return {
            labelCol: { span: 8 },
            wrapperCol: { span: 16 },
            formData: {},
            news: [],
            loading: false,
            showPriceOpeningsGrid: false,
            showDecrypt: false,
            password: '',
            floorBtns: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KWRe_2987fa32`, '在线沟通'),
                    click: this.handleChat
                },
                // {title: '签到', click: this.signIn},
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yw_110e43`, '解密'),
                    click: this.handleShowDecrypt,
                    key: 'decrypt',
                    disabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_i18n_dict_vBIBB_6b38ab39`, '开标一览表'),
                    click: this.showPriceOpenings,
                    key: 'priceOpenings',
                    disabled: true
                }
            ],
            clearReconnectTimer: false,
            openInfoRecordsList: [],
            priceOpeninData: [],
            deadline: null,
            websocket: null,
            decryptStatus: '0',
            url: {
                signIn: '/tender/sale/supplierTenderProjectMasterInfo/signIn',
                queryBySubpackageId: '/tender/purchaseTenderProjectOpenSettingHead/queryBySubpackageId',
                getDataUrl: '/tender/sale/supplierTenderProjectMasterInfo/selectProjectSupplierBySubpackageId',
                decryptUrl: '/tender/sale/supplierTenderProjectMasterInfo/decrypt',
                getLetterEncryptionInfo: '/tender/sale/supplierTenderProjectMasterInfo/getLetterEncryptionInfoBySubpackageId'
            },
            wsUrl: '',
            letterEncryptionInfo: null,
            letterEncryptionInfoOfDeco: null,
            quoteColumnEncryption: null,
            quoteColumnEncryptionJson: null,
            caPassword: '',
            form: {},
            uKeyInfo: null,
            heartBeatConfig: HEART_BEAT_CONFIG
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'currentSubPackage', 
        'resetCurrentSubPackage'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        isCa () {
            let subpackage = this.currentSubPackage()
            return subpackage.useCa == '1'
        },
        openTimeCode () {
            let code = ''
            switch (this.checkType) {
            case '0':
                code = 'preAnnounceOpenBidTime_DateMaps'
                break
            case '1': 
                switch (this.processType) { 
                case '0':
                    code = 'resultAnnounceOpenBidTime_DateMaps'
                    break
                case '1': 
                    switch (this.currentStep) { 
                    case '0':
                        code = 'announceOpenBidTime_DateMaps'
                        break
                    case '1': 
                        code = 'resultAnnounceOpenBidTime_DateMaps'
                        break
                    default :
                        code = 'announceOpenBidTime_DateMaps'
                    }
                    break
                default :
                    code = 'announceOpenBidTime_DateMaps'
                }
                break
            default :
                code = 'announceOpenBidTime_DateMaps'
            }
            return code
        },
        openBidStatus () {
            let openBidStatus = ''
            switch (this.checkType) {
            case '0':
                openBidStatus = 'preOpenBidStatus'
                break
            case '1':
                switch (this.processType) {
                case '0':
                    openBidStatus = 'resultOpenBidStatus'
                    break
                case '1':
                    switch (this.currentStep) {
                    case '0':
                        openBidStatus = 'openBidStatus'
                        break
                    case '1':
                        openBidStatus = 'resultOpenBidStatus'
                        break
                    default :
                        openBidStatus = 'openBidStatus'
                    }
                    break
                default :
                    openBidStatus = 'openBidStatus'
                }
                break
            default :
                openBidStatus = 'openBidStatus'
            }
            return openBidStatus
        },
        navStautsList () {
            let openBidStatus = `${this.openBidStatus}_dictText`
            let decryptFileName = `${this.openBidStatus}_dictText`
            if (this.checkType == '0') {
                decryptFileName = 'preDecrypt_dictText'
            } else if (this.checkType == '1' && this.processType == '1' && this.currentStep == '0') {
                // 后审两步法第一步
                decryptFileName = 'decrypt_dictText'
            } else {
                // 后审一步法，后审两步法第二步
                decryptFileName = 'resultDecrypt_dictText'
            }
            let columns = [
                {
                    label: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidOpenStatus`, '开标状态')),
                    fieldName: openBidStatus, icon: 'openBidStatus'
                },
                {
                    label: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ywzE_4014cd8e`, '解密状态')),
                    fieldName: decryptFileName, icon: 'decryptNumber'
                }
                // {label: '签名人数 ', fieldName: 'signatureNumber'}
            ]
            // 非加密情况,则不展示签到人数和解密人数
            if(this.currentSubPackage().openBidEncrypt != '1'){
                columns.splice(1, 1)
            }
            return columns
        }
    },
    methods: {
        refresh () {
            this.getData()
        },
        handleChat () {
            layIM.creatGruopChat({
                id: this.subId,
                type: 'SaleTenderOpenBid',
                url: this.url || '',
                recordNumber: this.subId
            })
        },
        // 签到
        signIn () {
            postAction(this.url.signIn, { subpackageId: this.subId })
        },
        // 第一次进入发消息
        firstMessage () {
            let { realname = ''} = this.$ls.get(USER_INFO) || {}
            let {companyName} = this.$ls.get(USER_COMPANYSET)
            let obj = {
                'checkType': this.checkType,
                'processType': this.processType,
                'currentStep': this.currentStep,
                'subpackageId': this.subId,
                'messageContent': `投标人 ${companyName} ${realname} 进入开标大厅`
            }
            if (this.websocket) {
                this.websocket.sendMsg(JSON.stringify(obj))
                // 签到
                this.signIn()
            }
        },
        // 获取解密时间
        getDecryptionTime () {
            return getAction(this.url.queryBySubpackageId, { subpackageId: this.subId, processType: '1' })
        },
        // 设置解密时间
        async setDecryptionTime () {
            let StartTime = moment().valueOf()
            let {result: {decryptionTime}, timestamp} = await this.getDecryptionTime()
            // 响应时间
            let announceOpenBidTime = this.formData[this.openTimeCode] || ''
            let openBidTime = moment(announceOpenBidTime).add(decryptionTime, 'minutes').valueOf()
            // 倒计时时间= 宣布开标时间 + 解密时间 - 当前服务器时间 
            if (timestamp < openBidTime) {
                let ResponseTime = moment().valueOf() - StartTime
                this.deadline = openBidTime - timestamp - ResponseTime
            }
        },
        // 解密倒计时
        handleFinish () {
            this.hideBtnList('decrypt')
        },
        // 按钮可操作
        ableBtnList (codes, disabled = false) {
            if (!Array.isArray(codes)) codes = [codes]
            this.floorBtns.map(btn => {
                if (codes.includes(btn.key)) btn.disabled = disabled
            })
        },
        // 隐藏按钮
        hideBtnList (codes) {
            if (!Array.isArray(codes)) codes = [codes]
            this.floorBtns = this.floorBtns.filter(btn => {
                return !codes.includes(btn.key)
            })
        },
        getLetterEncryptionInfo () {
            this.loading = true
            return getAction(this.url.getLetterEncryptionInfo, { subpackageId: this.subId}).then(res => {
                this.letterEncryptionInfo = res.result.customizeFieldData
                this.quoteColumnEncryptionJson = res.result.quoteColumnEncryption
            }).finally(() => {
                this.loading = false
            })
        },
        getUkeyInfo () { // 获取Uk
            return ukAction('/uk/action', {'type': 100}).then(res => {
                this.uKeyInfo = JSON.parse(res.data)[0]
                console.log(this.uKeyInfo)
            })
        },
        // 解密弹窗
        async handleShowDecrypt () {
            if (this.isCa) await this.getLetterEncryptionInfo()
            this.showDecrypt = true
            this.password = ''
            this.caPassword = ''
        },
        // base64解密
        decode (str) {
            return decodeURIComponent(window.atob(str).split('').map(function (c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
            }).join(''))
        },
        // ca解密
        async handleDecryptOfCa () {
            if (!this.uKeyInfo) {
                await this.getUkeyInfo()
            }
            let params1 = {
                'type': 303, 
                'keySN': this.uKeyInfo.keySN,
                'pin': this.caPassword,
                'raw': this.letterEncryptionInfo
            }
            let params2 = {
                'type': 303, 
                'keySN': this.uKeyInfo.keySN,
                'pin': this.caPassword,
                'raw': this.quoteColumnEncryptionJson
            }
            let p1 = new Promise((resolve, reject) => {
                ukAction('/uk/action', params1).then(res => {
                    console.log(res)
                    if (res.code == '0') {
                        let data = JSON.parse(this.decode(res.data))
                        resolve(data)
                    } else {
                        reject(res)
                    }
                }, (err) => {
                    reject(err)
                })
            })
            let p2 = new Promise((resolve, reject) => {
                ukAction('/uk/action', params2).then(res => {
                    if (res.code == '0') {
                        let data = JSON.parse(this.decode(res.data))
                        resolve(data)
                    } else {
                        reject(res)
                    }
                }, (err) => {
                    reject(err)
                })
            })
            return Promise.all([p1, p2]).then(res => {
                this.letterEncryptionInfoOfDeco = res[1]
                this.quoteColumnEncryption = res[1]
                this.spinning = false
            }, (err) => {
                this.spinning = false
            })
        },
        // 解密
        async handleDecrypt () {
            if (!this.caPassword && this.isCa) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNeBxwo_fb8cad9d`, '请输入投标函密码'))
                return false
            }
            if (!this.password) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNeBQIwo_7eb7c245`, '请输入投标文件密码'))
            if (this.isCa) await this.handleDecryptOfCa()
            const callback = () => {
                let params = {
                    subpackageId: this.subId,
                    processType: this.processType,
                    password: this.password,
                    customizeFieldData: this.isCa ? JSON.stringify(this.letterEncryptionInfoOfDeco) : undefined,
                    quoteColumnEncryption: this.isCa ? JSON.stringify(this.quoteColumnEncryption) : undefined
                }
                this.loading = true
                postAction(this.url.decryptUrl, params).then((res) => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        // 发送一个解密消息
                        this.getData()
                        this.showDecrypt = false
                    }
                }).finally(() => {
                    this.loading = false
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yw_110e43`, '解密'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLyw_392cba10`, '是否确认解密'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        // 开标预览表
        showPriceOpenings () {
            this.loading = true
            postAction(`/tender/sale/supplierTenderProjectMasterInfo/priceOpeningsBySubpackageId?subpackageId=${this.subId}`).then(res => {
                if (res.success) {
                    this.showPriceOpeningsGrid = true
                    this.priceOpeninData = res.result || []
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.loading = false
            })
        },
        getData () {
            let params = {
                subpackageId: this.subId,
                processType: '1'
            }
            this.loading = true
            this.deadline = null
            getAction(this.url.getDataUrl, params).then(async (res) => {
                this.formData = res.result || {}
                this.openInfoRecordsList = this.formData.openInfoRecordsList
                if (this.checkType == '0') {
                    this.decryptStatus = this.formData.preDecrypt
                } else if (this.checkType == '1' && this.processType == '1' && this.currentStep == '0') {
                    // 后审两步法第一步
                    this.decryptStatus = this.formData.decrypt
                } else {
                    // 后审一步法，后审两步法第二步
                    this.decryptStatus = this.formData.resultDecrypt
                }
                if (this.formData[this.openBidStatus] == '1') {
                    this.setDecryptionTime()
                    if (this.decryptStatus !== '1') {
                        this.ableBtnList('decrypt')
                    }
                } else if (this.formData[this.openBidStatus] == '2') {
                    this.ableBtnList('priceOpenings')
                    this.hideBtnList('decrypt')
                } else if (this.formData[this.openBidStatus] == '5') {
                    this.ableBtnList('priceOpenings')
                    this.hideBtnList('decrypt')
                    this.closeWebscoket()
                }
                if (this.decryptStatus == '1') {
                    this.hideBtnList('decrypt')
                }
                if (this.formData[this.openBidStatus] !== '5') {
                    this.setWebscoket()
                }
                // this.$emit('resetCurrentSubPackage')
                this.resetCurrentSubPackage()
            }).finally(() => {
                this.loading = false
            })
        },
        handleWebScoketMessage (e) {
            try {
                let data = e.data
                console.log('ws data==>', data)
                if (data == 'heartCheck') { // 心跳
                    this.websocket.webSocketState = true
                } else {
                    let { type, result } = JSON.parse(data)
                    if (type == 'updateOpenBidStatus') {
                        // 更新开标状态
                        let { openBidStatus } = result
                        this.statusMap(openBidStatus)
                        this.websocket.webSocketState = true
                    } else if (type == 'updateOpenInfo') {
                        // 更新开标信息,供应商不处理
                        this.websocket.webSocketState = true
                    } else {
                        this.websocket.webSocketState = true
                    }
                }
            } catch (err) {
                console.log('err', err)
            }
        },
        // 状态枚举对应方法
        statusMap (status) {
            // let map = {
            //     1: () => {
            //         this.getData()
            //         this.ableBtnList('decrypt')
            //     },
            //     2: () => {
            //         this.getData()
            //     },
            //     4: () => {
            //         this.ableBtnList('decrypt', true)
            //     },
            //     5: () => {
            //         this.getData()
            //     }
            // }
            // map[status]()
            switch (status) {
            case '1':
                this.ableBtnList('decrypt')
                break
            case '4':
                this.ableBtnList('decrypt', true)
                break
            default:
                this.getData()
                break
            }
        },
        connectWebSocket () {
            this.websocket = new MyWebSocket(this.wsUrl)
            this.websocket.init(this.heartBeatConfig, true)
        },
        closeWebscoket () {
            if (this.websocket) {
                this.websocket.webSocketState = false
                this.websocket.closeHandler()
                this.websocket.close()
                this.websocket = null
            }
        },
        handleSockeMessage (data) {
            console.log(data)
            // if (this.spinning) return
            // this.refresh()
        },
        getWebsocketUrl () {
            let { serivceUrl = '', elsAccount = '', subAccount = '' } = this.$ls.get(USER_INFO) || {}
            let url = serivceUrl.replace('https://', 'wss://').replace('http://', 'ws://')
            this.wsUrl = `${url}/els/websocket/tender/online/${this.subId}/${elsAccount}_${subAccount}`
        },
        reconnectWebSocket () {
            if (!this.websocket && !this.clearReconnectTimer) { //第一次执行，初始化
                this.connectWebSocket()
            }
            if (this.websocket && this.websocket.reconnectTimer) { //防止多个websocket同时执行
                clearTimeout(this.websocket.reconnectTimer)
                this.websocket.reconnectTimer = null
                this.connectWebSocket()
            }
        },
        handleOpenWebscoket () {
            this.firstMessage()
        },
        setWebscoket () {
            if (!this.websocket) {
                eventBus.onEvent('reconnect', this.reconnectWebSocket) //
                eventBus.onEvent('handleMessage', this.handleWebScoketMessage) //接收消息
                eventBus.onEvent('handleOpenWebscoket', this.handleOpenWebscoket) //开启消息
                eventBus.emitEvent('reconnect')
            }
        },
        init () {
            this.getWebsocketUrl()
            this.getData()
        }
    },
    beforeDestroy () {
        this.clearReconnectTimer = true
        this.closeWebscoket()
    },
    created () {
        this.init()
    }
}
</script>

<style lang="less" scoped>
.color-blue {
    color: blue;
}

.nav-item {
    display: inline-block;
    font-size: 20px;

    & + & {
        margin-left: 10px;
    }
}

.deadline {
    font-size: 20px;
    margin-left: 10px;
    display: inline-block;

    :deep(.ant-statistic){
        display: inline-block;
    }

    :deep(.ant-statistic-content){
        font-size: 20px;
    }
}

.news-box {
    border: 1px solid #777474;
    background-color: #fff;
    padding: 10px 10px 0;
    height: 400px;
    margin-bottom: 10px;
    overflow: auto;
}

.text-align-l {
    text-align: left;
}

.margin-r-10 {
    margin-right: 10px;
}

.ant-btn {
    & + .ant-btn {
        margin-left: 10px;
    }
}
</style>
