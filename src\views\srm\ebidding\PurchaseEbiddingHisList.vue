<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage"
      ref="listPage"
      :pageData="pageData"
      :url="url" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
export default {
    mixins: [ListMixin],
    data () {
        return {
            pageData: {
                businessType: 'ebidding',
                form: {
                    ebiddingNumber: '',
                    lastQuote: undefined
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', folded: true, clickFn: this.handleExportXls},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号'),
                        fieldName: 'ebiddingNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterBidDocumentNo`, '请输入竞价单号')
                    },
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'),
                        fieldName: 'materialNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterMaterialNumber`, '请输入物料编码')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lastQuote`, '最后一次报价'),
                        fieldName: 'lastQuote',
                        dictCode: 'yn',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectIsLastQuote`, '请选择是否最后一次报价')
                    }
                ],
                showOptColumn: false,
                optColumnWidth: 0,
                optColumnList: []
            },
            url: {
                list: '/ebidding/purchaseEbiddingItemHis/list',
                columns: 'purchaseEbiddingHisList',
                exportXlsUrl: '/ebidding/purchaseEbiddingItemHis/exportXls'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls('竞价报价历史')
        }
    }
}
</script>