<template>
  <a-drawer
    :title="title"
    :width="drawerWidth"
    @close="handleCancel"
    :visible="visible"
    :confirm-loading="confirmLoading"
    :wrap-style="{height: '100%',overflow: 'auto',paddingBottom: '108px'}"
  >
    <div :style="{width: '100%',border: '1px solid #e9e9e9',padding: '10px 16px',background: '#fff',}">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-form-item
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            label="成本编码"
            has-feedback
          >
            <a-input
              placeholder="成本编码"
              v-decorator="['costCode', validatorRules.costCode]"
              :disabled="disableFlag"
            />
          </a-form-item>

          <a-form-item
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            label="成本名称"
            has-feedback
          >
            <a-input
              placeholder="成本名称"
              v-decorator="['costName', validatorRules.costName]"
            />
          </a-form-item>

          <a-form-item
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            label="父级编码"
            has-feedback
          >
            <a-input
              placeholder="父级编码"
              v-decorator="['parentCode']"
              :disabled="disableFlag"
            />
          </a-form-item>

          <a-form-item
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            label="父级名称"
          >
            <a-input
              placeholder="父级名称"
              v-decorator="['parentName']"
              :disabled="disableFlag"
            />
          </a-form-item>

          <a-form-item
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            label="排序"
          >
            <a-input
              placeholder="排序"
              v-decorator="['sort']"
            />
          </a-form-item>

          <a-form-item
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            label="计算公式"
          >
            <a-input
              placeholder="计算公式"
              v-decorator="['formula']"
            />
          </a-form-item>
        </a-form>
      </a-spin>
      <a-row :style="{textAlign:'right'}">
        <a-button
          :style="{marginRight: '8px'}"
          @click="handleCancel"
        >
          关闭
        </a-button>
        <a-button
          :disabled="disableSubmit"
          @click="handleOk"
          type="primary"
        >
          确定
        </a-button>
      </a-row>
    </div>
  </a-drawer>
</template>

<script>
import {queryTreeList} from '@/api/api'
import pick from 'lodash.pick'
import {getLangAccount, srmI18n} from '@/utils/util'

export default {
    name: 'PermissionModal',
    components: {},
    data () {
        return {
            type: '',
            drawerWidth: 700,
            treeData: [],
            treeValue: '0-0-4',
            title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
            visible: false,
            disableSubmit: false,
            disableFlag: false,
            model: {},
            labelCol: {
                xs: { span: 24 },
                sm: { span: 5 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            },

            confirmLoading: false,
            form: this.$form.createForm(this)
        }
    },
    computed: {
        validatorRules: function () {
            return {
                costCode: {rules: [{ required: true, message: '请输入成本编码' }]},
                costName: {rules: [{ required: true, message: '请输入成本名称' }]},
                sort: {initialValue: 1.0}
            }
        }
    },
    created () {},
    methods: {
        loadTree (){
            // var that = this
            // queryTreeList().then((res)=>{
            //     if(res.success){
            //         console.log(res)
            //         that.treeData = []
            //         let treeList = res.result.treeList
            //         for(let a=0;a<treeList.length;a++){
            //             let temp = treeList[a]
            //             temp.isLeaf = temp.leaf
            //             that.treeData.push(temp)
            //         }
            //     }
            // })
        },
        add () {
            this.edit({}, 'new')
        },
        edit (record, type) {
            this.type = type
            if (type === 'edit') this.disableFlag = true
            if (type === 'new') this.disableFlag = false
            this.resetScreenSize() // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
            this.form.resetFields()
            this.model = Object.assign({}, record)
            console.log(record)
            this.visible = true
            this.loadTree()
            let fieldsVal = Object.assign({}, record) // pick(this.model, 'parentCode', 'parentName')
            this.$nextTick(() => {
                this.form.setFieldsValue(fieldsVal)
            })
        },
        close () {
            this.disableSubmit = false
            this.visible = false
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    let formData = Object.assign(that.model, values)
                    that.confirmLoading = true
                    that.$emit('ok', formData, that.type)
                    that.confirmLoading = false
                    // that.close()
                }
            })
        },
        handleCancel () {
            this.close()
        },
        validateNumber (rule, value, callback){
            if(!value || new RegExp(/^[0-9]*[1-9][0-9]*$/).test(value)){
                callback()
            }else{
                callback(srmI18n(`${getLangAccount()}#i18n_title_enterIntegerTips`, '请输入正整数!'))
            }
        },
        // 根据屏幕变化,设置抽屉尺寸
        resetScreenSize (){
            let screenWidth = document.body.clientWidth
            if(screenWidth < 500){
                this.drawerWidth = screenWidth
            }else{
                this.drawerWidth = 700
            }
        }
    }
}
</script>

<style scoped>

</style>