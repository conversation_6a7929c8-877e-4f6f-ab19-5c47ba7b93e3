<template>
  <div class="page-container">
    <detail-page
      ref="detailPage"
      modelLayout="collapse"
      :current-edit-row="currentEditRow"
      :pageData="pageData"></detail-page>
    <!-- 查看流程 -->
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <!-- 审批意见 -->
    <a-modal
    v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
  </div>
</template>

<script>
import detailPage from '@comp/template/detailPage'
import { httpAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'DeliveryRefundAuditModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        flowViewModal,
        detailPage
    },
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedReturnDoc`, '退货单审批'),
            confirmLoading: false,
            flowId: 0,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            auditVisible: false,
            opinion: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            pageData: {
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            form: {
                                deliveryNumber: '',
                                supplierId: '',
                                supplierCode: '',
                                supplierName: '',
                                companyCode: '',
                                companyName: '',
                                factoryCode: '',
                                factoryName: '',
                                deliveryType: '0',
                                deliveryStatus: '0',
                                auditStatus: '0',
                                deliveryDesc: '',
                                deliveryTime: '',
                                planArriveDate: '',
                                receiveTime: '',
                                storageLocationCode: '',
                                storageLocationName: '',
                                deliveryWay: '0',
                                logisticsCompany: '',
                                trackingNumber: '',
                                carNumber: '',
                                driverName: '',
                                driverIdNumber: '',
                                driverPhone: '',
                                deliveryAddress: '',
                                receiveContact: '',
                                receivePhone: '',
                                purchasePrincipal: '',
                                supplierPrincipal: '',
                                purchaseRemark: '',
                                supplierRemark: ''
                            },
                            list: [
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnDocNo`, '退货单号'),
                                    fieldName: 'deliveryNumber', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_systemGeneration`, '系统生成'),
                                    disabled: true
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                                    fieldName: 'deliveryStatus_dictText'
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm65d_auditStatus`, '审批状态'),
                                    fieldName: 'auditStatus_dictText'
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryTime`, '退货日期'),
                                    fieldName: 'deliveryTime'
                                },
                                {
                                    type: 'selectModal',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                                    fieldName: 'supplierName'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaDesc`, '单据描述'),
                                    fieldName: 'deliveryDesc'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司'),
                                    fieldName: 'companyCode'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                                    fieldName: 'factoryCode'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocation`, '库存地点'),
                                    fieldName: 'storageLocationCode'
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_distributionMode`, '配送方式'),
                                    fieldName: 'deliveryWay_dictText',
                                    dictCode: 'isrmDeliveryRefundWay'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsCompany`, '物流公司'),
                                    fieldName: 'logisticsCompany'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trackingNumber`, '物流单号'),
                                    fieldName: 'trackingNumber'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_carNumber`, '车牌号'),
                                    fieldName: 'carNumber'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverName`, '司机姓名'),
                                    fieldName: 'driverName'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverIdNumber`, '司机身份证号'),
                                    fieldName: 'driverIdNumber'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverPhone`, '司机电话'),
                                    fieldName: 'driverPhone'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'purchaseRemark'
                                }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                        content: {
                            type: 'table',
                            ref: 'deliveryRefundBuyItemList',
                            columns: [
                                { 
                                    type: 'seq', width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                                    field: 'orderNumber',
                                    width: 130
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                    field: 'orderItemNumber',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialCode',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                    field: 'materialDesc',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
                                    field: 'materialSpec',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                                    field: 'materialGroupCode',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
                                    field: 'taxCode',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'),
                                    field: 'taxRate',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '净价'),
                                    field: 'netPrice',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'),
                                    field: 'price',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityUnit`, '数量单位'),
                                    field: 'quantityUnit',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryQuantity`, '退货数量'),
                                    field: 'deliveryQuantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_batchNumber`, '批次号'),
                                    field: 'batchNumber',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRemark`, '退货原因'),
                                    field: 'purchaseRemark',
                                    width: 220
                                }
                            ]
                        }
                    }
                ],
                url: {
                    detail: '/delivery/deliveryRefundBuyHead/queryDetailById'
                },
                publicBtn: [
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'), clickFn: this.auditPass, showCondition: this.showAuditBtn},
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'), clickFn: this.auditReject, showCondition: this.showAuditBtn},
                    {type: '', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), clickFn: this.showFlow},
                    {type: 'rollBack', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack}
                ]
            }
            
        }
    },
    mounted () {
       
    },
    created (){
      
    },
    methods: {
        goBack () {
            this.$parent.hideController()
        },
        auditPass (row){
            this.currentRow = row
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (row){
            this.currentRow = row
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        showFlow (){
            this.flowId = this.currentEditRow.rootProcessInstanceId
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        }
    }
}
</script>
