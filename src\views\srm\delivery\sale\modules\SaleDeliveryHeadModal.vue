<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      refresh
      :current-edit-row="currentEditRow"
      :page-data="pageData"
      :url="url"
    />
    <!-- 行明细弹出选择框 -->
    <field-select-modal ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction, getAction } from '@/api/manage'
import { EditMixin } from '@comp/template/edit/EditMixin'
import { SALEATTACHMENTDOWNLOADAPI } from '@/utils/const'
import { cloneDeep } from 'lodash'

export default {
  name: 'SaleDeliveryHeadModal',
  components: {
    fieldSelectModal
  },
  props: {
    currentEditRow: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mixins: [EditMixin],
  data() {
    return {
      currentBasePath: this.$variateConfig['domainURL'],
      flowView: false,
      selectType: 'material',
      materials: {},
      flowId: 0,
      pageData: {
        form: {},
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shippingLineInfo`, '发货行信息'),
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'saleDeliveryItemList',
              columns: [],
              buttons: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.insertGridItem, showCondition: this.showRowAddConditionBtn },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteGridItem },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem },
                { title: '新增赠品', click: this.addGifts }
              ],
              optColumnList: [
                { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_split`, '拆分'), clickFn: this.splitRow },
                { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.splitDelete, allow: this.allowSplitDelete }
              ]
            }
          },
          {
            groupName: '辅料发料',
            groupCode: 'saleDeliverySubList',
            needDynamicsHeight: true,
            type: 'grid',
            custom: {
              ref: 'saleDeliverySubList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'arrivedTime', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_uSKI_277b0df5`, '到货时间'), width: 120, fieldType: 'input' },
                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_PurchaseMassProdHeadList_materialCode`, '物料编码'), width: 120 },
                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}i18n_massProdHead0e15_materialName`, '物料名称'), width: 120 },
                {
                  field: 'deliveryQuantity',
                  title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_hSWR_28388855`, '发货数量'),
                  width: 120,
                  fieldType: 'number',
                  extend: { min: 0 },
                  slots: {},
                  bindFunction: function bindFunction(row, column, val) {
                    row.deliveryQuantity = val
                    row.remainQuantity = row.deliveryQuantity
                    if (!!row.deliveryQuantity) {
                      row.secondaryQuantity = row.deliveryQuantity / (row.conversionRate || 1)
                    } else {
                      row.secondaryQuantity = 0
                    }
                    row.secondaryQuantity = row.secondaryQuantity.toFixed(6)
                  }
                },
                { field: 'quantityUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_dtL_1301213`, '主单位'), width: 120 },
                { field: 'conversionRate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_conversionRate`, '换算率'), width: 120 },
                { field: 'secondaryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_secondaryQuantity`, '辅数量'), width: 120 },
                { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_BtL_22528dd`, '辅单位'), width: 120 },
                { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_lineStatus`, '行状态'), width: 120 },
                { field: 'factory', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_GMVR_2c647268`, '库存组织'), width: 120, slots: { default: 'renderDictLabel' }, dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"' },
                { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_materialSpec`, '物料规格'), width: 120 },
                { field: 'receiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_receiveQuantity`, '收货数量'), width: 120 },
                { field: 'remainQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_bUlSWR_2ed782e0`, '剩余收货数量'), width: 120 },
                { field: 'batchNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_batchNumber`, '批次号'), width: 120 },
                { field: 'batch_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_batch`, '是否批次管理'), width: 120 },
                { field: 'ncQualityDayNum', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_ncQualityDayNum`, '保质天数'), width: 120 },
                {
                  field: 'productionDate',
                  title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_produceDate`, '生产日期'),
                  width: 120,
                  fieldType: 'date',
                  bindFunction: function bindFunction(row, column, value,vm) {
                    if (value) {
                      // 为品牌管理
                      if (!!row.brandManage) {
                        row.batchNumber = (row.supplierMnemonicCode || '') + (row.brandName || '') + value.replaceAll('-', '')
                      }
                      // 不为品牌管理
                      else {
                        row.batchNumber = value.replaceAll('-', '')
                      }
                      const date = new Date(value)

                      if (!!row.ncQualityDayNum) {
                        let ncQualityDayNum = (row.ncQualityDayNum && Number(row.ncQualityDayNum)) > 0 ? row.ncQualityDayNum - 1 : 0
                        date.setDate(date.getDate() + ncQualityDayNum)
                        row.expiryDate = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
                          let nowDate = new Date();
                          if (row.expiryDate < `${nowDate.getFullYear()}-${nowDate.getMonth() + 1}-${nowDate.getDate()}`) {
                              vm.$message.warning('失效日期不能小于当前日期')
                          }
                      }
                    } else {
                      row.batchNumber = null
                      row.expiryDate = null
                    }
                  }
                },
                { field: 'expiryDate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_expiryDate`, '失效日期'), width: 120 },
                { field: 'taxCode', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_taxCode`, '税码'), width: 120 },
                { field: 'taxRate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_taxRate`, '税率'), width: 120 },
                { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_price`, '含税价'), width: 120 },
                { field: 'netPrice', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_netPrice`, '净价'), width: 120 },
                { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_orderNumber`, '订单号'), width: 120 },
                { field: 'orderItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                { field: 'returnQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_returnQuantity`, '退货数量'), width: 120 },
                // { field: "receiveDateNow", title: '本次实际收货时间', width: 200, fieldType: 'date' },
                { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_requireDate`, '需求日期'), width: 120 },
                { field: 'overTolerance', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_overTolerance`, '超量容差率'), width: 120 },
                { field: 'purchaseRemark', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_purchaseRemark`, '需方备注'), width: 120 },
                { field: 'supplierRemark', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_supplierRemark`, '供方备注'), width: 120, fieldType: 'input' },
                { field: 'sourceType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceType`, '来源类型'), width: 120 },
                { field: 'sourceNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceNumber`, '来源单号'), width: 120 },
                { field: 'sourceItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceItemNumber`, '来源单行号'), width: 120 },
                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 120 },
                { field: 'onWayQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_onWayQuantity`, '订单在途数量'), width: 120 },
                { field: 'orderReceiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_receiveQuantity`, '订单收货数量'), width: 120 },
                { field: 'notDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`, '订单未交货数量'), width: 120 },
                {
                  groupCode: '',
                  title: '品牌名称',
                  fieldLabelI18nKey: 'i18n_field_brandName',
                  field: 'brandName',
                  align: '',
                  headerAlign: 'center',
                  dataFormat: '',
                  defaultValue: '',
                  width: '120',
                  dictCode: '',
                  fold: '0',
                  sum: '0',
                  alertMsg: '',
                  bindFunction: function bindFunction(row, data, _self) {
                    row.brandId = data[0].id
                    row.brandCode = data[0].brandCode
                    row.brandName = data[0].brandName
                    row.brandManage = data[0].brandManage
                    row.ncQualityDayNum = data[0].qualityDayNum
                    row.supplierMnemonicCode = data[0].supplierMnemonicCode

                    row.batchNumber = ''
                    row.expiryDate = ''
                    row.productionDate = ''
                  },
                  extend: {
                    modalColumns: [
                      {
                        field: 'supplierMnemonicCode',
                        title: '供应商助记码',
                        fieldLabelI18nKey: 'i18n_field_supplierMnemonicCode'
                      },
                      {
                        field: 'brandName',
                        title: '品牌名称',
                        fieldLabelI18nKey: 'i18n_field_brandName',
                        with: 150
                      },
                      {
                        field: 'qualityDayNum',
                        title: '保质期（天）',
                        fieldLabelI18nKey: 'i18n_field_qualityDayNum',
                        with: 150
                      }
                    ],
                    modalUrl: '/material/purchaseMaterialHead/listBrandInfoByParams',
                    modalParams: function (that, form, row) {
                      return {
                        materialNumber: row.materialNumber,
                        supplierElsAccount: form.elsAccount || row.elsAccount,
                        factory: form.factory || row.factory
                      }
                    },
                    // 表行弹窗清除按钮回调
                    afterRowClearCallBack: function (Vue, row) {
                      row.brandId = ''
                      row.brandCode = ''
                      row.brandName = ''
                      row.brandManage = ''
                      row.ncQualityDayNum = ''
                      row.supplierMnemonicCode = ''
                      row.batchNumber = ''
                      row.expiryDate = ''
                      row.productionDate = ''
                    }
                  },
                  fieldType: 'selectModal',
                  required: '0',
                  mobile: 1,
                  helpText: ''
                },
                {
                  groupCode: '',
                  title: '是否品牌管理',
                  fieldLabelI18nKey: 'i18n_field_brandManage',
                  field: 'brandManage',
                  align: '',
                  headerAlign: 'center',
                  dataFormat: '',
                  defaultValue: '',
                  width: 150,
                  dictCode: 'yn',
                  fold: '0',
                  sum: '0',
                  alertMsg: '',
                  slots: { default: 'renderDictLabel' },
                  required: '0',
                  mobile: 1,
                  helpText: ''
                },
                {
                  groupCode: '',
                  title: '供应商助记码',
                  fieldLabelI18nKey: 'i18n_field_supplierMnemonicCode',
                  field: 'supplierMnemonicCode',
                  align: '',
                  headerAlign: 'center',
                  dataFormat: '',
                  defaultValue: '',
                  width: 150,
                  dictCode: '',
                  fold: '0',
                  sum: '0',
                  alertMsg: '',
                  required: '0',
                  mobile: 1,
                  helpText: ''
                }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                  type: 'primary',
                  click: this.addSubList
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  click: this.deleteSubList
                },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                  key: 'fillDown',
                  type: 'tool-fill',
                  beforeCheckedCallBack: this.fillDownGridItem
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupCode: 'fileInfo',
            type: 'grid',
            needDynamicsHeight: true,
            custom: {
              ref: 'saleAttachmentList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rowIitem`, '行项目'), width: 120 },
                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
              ],
              buttons: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'delivery', attr: this.attrHandle, callBack: this.uploadCallBack },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch }
              ],
              showOptColumn: true,
              optColumnList: [
                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadSaleEvent },
                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
              ]
            }
          }
        ],
        formFields: [],
        footerButtons: [
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent, authorityCode: 'order#saleDeliveryHead:edit' },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publishEvent, authorityCode: 'order#saleDeliveryHead:publish' },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
        ]
      },
      url: {
        add: '/delivery/saleDeliveryHead/add',
        edit: '/delivery/saleDeliveryHead/edit',
        detail: '/delivery/saleDeliveryHead/queryById',
        public: '/delivery/saleDeliveryHead/publish',
        planToDelivery: '/delivery/saleDeliveryHead/planToDeliveryItem',
        upload: '/attachment/saleAttachment/upload',
        material: '/material/purchaseMaterialHead/listByMaterialNumbers'
      },

      fieldSelectType: ''
    }
  },
  watch: {
    currentEditRow: {
      async handler(value) {
        console.log('asdasd', value)
        const res = await getAction(this.url.detail, { id: value.id })
        const materialNumbers = res?.result?.saleDeliveryItemList?.map((v) => v.materialNumber)?.join(',')
        if (materialNumbers) {
          const resp = await getAction(this.url.material, { materialNumbers })
          if (resp.result?.length) {
            setTimeout(() => {
              const fileGrid = this.$refs.editPage.$refs.saleDeliveryItemList[0]
              console.log('fileGrid', fileGrid)
              let { fullData } = fileGrid.getTableData()
              console.log('fullData', fullData)
            for(let detail of res.result.saleDeliveryItemList) {
                for (const meta of resp.result) {
                    this.materials[meta.materialNumber] = meta
                    let dataIndex = fullData.findIndex((i) => i.materialNumber === meta.materialNumber && i.factory === meta.factory && i.materialPublicNumber != 'Y')
                    if (dataIndex > -1) {
                        fullData[dataIndex] = {
                            ...fullData[dataIndex],
                            batch: meta.batch,
                            ncQualityDayNum: meta.brandManage != 1 ? meta.ncQualityDayNum : fullData[dataIndex].ncQualityDayNum || 0
                        }
                    } else {
                        fullData[dataIndex] = {
                            ...fullData[dataIndex],
                            batch: meta.batch,
                            ncQualityDayNum: detail.ncQualityDayNum
                        }
                    }
                    console.log(fullData[dataIndex].ncQualityDayNum,'当前保质期')
                }
            }
              console.log('fullData2', fullData)
              fileGrid.loadData(fullData)
            }, 100)
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    fileSrc() {
      let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
      let elsAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
      let templateVersion = this.currentEditRow.templateVersion
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_delivery_${templateNumber}_${templateVersion}.js?t=` + time
    }
  },
  methods: {
    afterHandleData(data) {
      const _columItem = { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
      data.groups.forEach((v, i) => {
        if (v.groupCode === 'itemInfo') {
          v.custom.columns.splice(2, 0, _columItem)
        }
      })
    },
    // 請求接口後格式化列表數據
    async formatTableData(data) {
      this.setColumnData()
      console.log('請求接口後格式化列表數據', data)
      return new Promise((resolve, reject) => {
        data = data.map((item) => {
          if (item.price === 0 || Number(item.price) > 0) {
            item.price = Number(item.price).toFixed(6)
          }
          if (item.netPrice === 0 || Number(item.netPrice) > 0) {
            item.netPrice = Number(item.netPrice).toFixed(6)
          }

          // 当物料分类前三位为：402、404、413   批次管理为是
          if(!item.productionDate && (item.cateCode.indexOf('402') == 0 || item.cateCode.indexOf('404') == 0 || item.cateCode.indexOf('413') == 0) && item.batch == 1) {
            item.productionDate = dayjs().format("YYYY-MM-DD"); // 生产日期默认取当天

            // 并生成对应的失效日期 （默认取物料的保质天数）
            let ncQualityDayNum = (item.ncQualityDayNum && Number(item.ncQualityDayNum)) > 0 ? item.ncQualityDayNum - 1 : 0
            item.expiryDate = dayjs(item.productionDate).add(ncQualityDayNum, 'day').format("YYYY-MM-DD");
            item.batchNumber = dayjs().format("YYYYMMDD"); // 批次号信息
          }
          return item
        })
        resolve(data)
      })
    },
    // 设置列显示
    setColumnData() {
      let st = setTimeout(() => {
        let itemGrid = this.$refs.editPage.$refs.saleDeliveryItemList[0]
        let { fullData } = itemGrid.getTableData()
        if (fullData.length >= 1) {
          let item = fullData[0]
          if (item.price == item.netPrice && !item.taxRate) {
            let columnsList = itemGrid.getColumns()
            columnsList = columnsList.map((column) => {
              if (column.field == 'taxCode' || column.field == 'taxRate') {
                column.visible = false
              }
              return column
            })
            itemGrid.loadColumn(columnsList)
          }
        }
        clearTimeout(st)
        st = null
      }, 100)
    },
    downloadSaleEvent(row) {
      this.$refs.editPage.handleDownload(row, SALEATTACHMENTDOWNLOADAPI)
    },
    attrHandle() {
      return { sourceNumber: this.currentEditRow.deliveryNumber, businessSourceType: 'standard' }
    },
    preViewEvent(row) {
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    goBack() {
      this.$emit('hide')
    },
    showRowAddConditionBtn() {
      this.$nextTick(() => {
        let itemGrid = this.$refs.editPage.$refs.saleDeliveryItemList[0]
        let { fullData } = itemGrid.getTableData()
        if (fullData.length > 0 && fullData[0].sourceType == 'deliveryNotice') {
          return false
        } else {
          return true
        }
      })
    },
    uploadCallBack(result) {
      let fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
      fileGrid.insertAt(result, -1)
      console.log(result)
    },
    /*deleteFilesEvent () {
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning('请选择数据！')
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },*/
    deleteFilesEvent(row) {
      const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
      getAction('/attachment/saleAttachment/delete', { id: row.id }).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.remove(row)
      })
    },
    deleteBatch() {
      const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
      const checkboxRecords = fileGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      const ids = checkboxRecords.map((n) => n.id).join(',')
      const params = {
        ids
      }
      getAction('/attachment/saleAttachment/deleteBatch', params).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.removeCheckboxRow()
      })
    },
    getCurrentDate(){
        const date = new Date()
        const year = date.getFullYear().toString().padStart(4, '0')
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        const nowDateStr = year + '-' + month + '-' + day
        return nowDateStr
    },
    publishEvent() {
      const handlePromise = (list = []) =>
        list.map((promise) =>
          promise.then(
            (res) => ({
              status: 'success',
              res
            }),
            (err) => ({
              status: 'error',
              err
            })
          )
        )
      let promise = this.$refs.editPage.setPromise()
      Promise.all(handlePromise(promise))
        .then((result) => {
          let flag = false
          for (let i = 0; i < result.length; i++) {
            if (result[i].status === 'success') {
              flag = true
            } else {
              return
            }
          }
          if (flag) {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (params.saleDeliveryItemList.length == 0) {
              this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
              return false
            }
            let proDateErrorStr =''
            let curDate = this.getCurrentDate()
            params.saleDeliveryItemList.map(item=>{
                if(item.productionDate!==''&&item.productionDate>curDate){
                    let mess = item.materialNumber+'_'+item.materialName
                    proDateErrorStr='物料为:'+'['+mess+']所在行,生产日期不得大于当前日期'
                    return
                }
            })
              if(proDateErrorStr!=null && proDateErrorStr!=''){
                  this.$message.warning(proDateErrorStr)
                  return
              }
            let that = this
            this.$confirm({
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sendInvoice`, '发送发货单'),
              content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sendShipmentDocumentPurchaserReceiptSureWantPublish`, '将发货单发送采购方等待收货，是否确认发布?'),
              onOk: function () {
                // 校验生产日期
                console.log('xxxx', params)
                for (const item of params.saleDeliveryItemList || []) {
                  // const material = that.materials[item.materialNumber]
                  const material = item
                  if (!material) {
                    continue
                  }
                  if (material.batch === '1' && !material.productionDate) {
                    return that.$message.warning(`物料 ${material.materialNumber} - ${material.materialName} 需填写生产日期`)
                  }
                }
                let stamp = new Date().getTime() + 8 * 60 * 60 * 1000
                let currentTime = new Date(stamp).toISOString().replace(/T/, ' ').replace(/\..+/, '').substring(0, 19)
                params.deliveryTime = currentTime
                params.deliveryTime_timestamp = stamp
                params.deliveryTime_DateMaps = stamp
                postAction(that.url.public, params).then((res) => {
                  if (res.success) {
                    that.$message.success(res.message)
                    that.$refs.editPage.goBack()
                  } else {
                    if (!!res.message && res.message.indexOf('\n') >= 0) {
                      const h = that.$createElement
                      let strList = res.message.split('\n')
                      strList = strList.map((str, strIndex) => {
                        return h(strIndex === 0 ? 'span' : 'div', strIndex === 0 ? null : { style: 'margin-left: 24px' }, str)
                      })
                      that.$message.warning(h('span', { style: 'text-align: left' }, strList))
                      return
                    }
                    that.$message.warning(res.message)
                  }
                })
              }
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    //新增行
    insertGridItem() {
      this.fieldSelectType = 'lineDelivery'
      let itemGrid = this.$refs.editPage.$refs.saleDeliveryItemList[0]
      let { fullData } = itemGrid.getTableData()
      if (fullData.length > 0 && fullData[0].sourceType == 'deliveryNotice') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_thereIsNoNeedAddLineDeliveryNotice`, '关于送货通知单的发货行无需添加行项目'))
        return
      } else {
        let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
        let url = '/order/saleOrderDeliveryPlan/list'
        let columns = [
          { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_planLineNum`, '计划行号'), width: 80 },
          { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 150 },
          { field: 'orderItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 80 },
          { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 80 },
          { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 80 },
          { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_requiredDeliveryDate`, '要求交期'), width: 100 },
          { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 100 },
          { field: 'planDeliveryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_planDeliveryDate`, '计划交货日期'), width: 100 },
          { field: 'receiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantity`, '收货数量'), width: 100 },
          { field: 'onWayQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_onReciveCout`, '在途数量'), width: 100 },
          { field: 'planToDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shipmentTransferred`, '已转发货数量'), width: 100 }
        ]
        this.$refs.fieldSelectModal.open(url, { toElsAccount: params.toElsAccount, storageLocation: params.storageLocation, factory: params.factory, freeze: '0', deletePlan: '0', close: '0', confirmStatus: '1', allDelivery: '1' }, columns, 'multiple')
      }
    },
    // 新增赠品
    addGifts() {
      let itemGrid = this.$refs.editPage.$refs.saleDeliveryItemList[0]
      let checkboxRecords = cloneDeep(itemGrid.getCheckboxRecords())
      if (!checkboxRecords.length) {
        this.$message.warning('请选择数据！')
        return
      }
      checkboxRecords = checkboxRecords.map(item => {
        delete item._X_ROW_KEY;
        item.id = null;
        item.price = 0;
        item.netPrice = 0;
        item.gift = 1;
        item.gift_dictText = '是';
        return item;
      })
      itemGrid.insertAt(checkboxRecords, -1)
    },
    fieldSelectOk(data) {
      if (this.fieldSelectType === 'lineDelivery') {
        let itemGrid = this.$refs.editPage.$refs.saleDeliveryItemList[0]
        let { fullData } = itemGrid.getTableData()
        let materialList = fullData.map((item) => {
          return item.sourceId
        })
        //过滤已有数据
        let insertData = data.filter((item) => {
          return !materialList.includes(item.id)
        })
        let that = this
        postAction(that.url.planToDelivery, insertData).then((res) => {
          if (res.success) {
            itemGrid.insertAt(res.result, -1)
          } else {
            that.$message.warning(res.message)
          }
        })
      } else if (this.fieldSelectType === 'subOrder') {
        let itemGrid = this.$refs.editPage.$refs.saleDeliverySubList[0]
        let { fullData } = itemGrid.getTableData()
        let orderList = fullData.map((item) => {
          return item.materialNumber
        })
        //过滤已有数据
        let insertData = data.filter((item) => {
          return !orderList.includes(item.materialNumber)
        })

        insertData = insertData.map((item) => {
          item = {
            id: null,
            sourceId: item.sourceId,
            orderNumber: item.orderNumber,
            orderId: item.orderId,
            materialDesc: item.materialDesc,
            purchaseRemark: null, // 采购方传入，供应方自己输入
            sourceItemId: item.sourceItemId,
            supplierRemark: item.supplierRemark,
            netPrice: item.netPrice,
            cateName: item.cateName,
            materialNumber: item.materialNumber,
            price: item.price,
            secondaryQuantity: null,
            requireDate: item.requireDate,
            batch: item.batch,
            materialGroupName: item.materialGroupName,
            taxCode: item.taxCode,
            cateCode: item.cateCode,
            ncQualityDayNum: item.ncQualityDayNum,
            materialName: item.materialName,
            purchaseUnit: item.purchaseUnit,
            taxRate: item.taxRate,
            sourceType: item.sourceType,
            subElsAccount: item.subElsAccount,
            subSupplierName: item.subSupplierName,
            materialGroup: item.materialGroup,
            orderItemNumber: item.orderItemNumber,
            toElsAccount: item.toElsAccount,
            overTolerance: item.overTolerance,
            quantityUnit: item.quantityUnit,
            lackTolerance: item.lackTolerance,
            itemStatus: item.itemStatus,
            deliveryQuantity: item.deliveryQuantity || 0,
            remainQuantity: item.deliveryQuantity || 0,
            currency: item.currency,
            quantity: item.quantity,
            orderItemId: item.orderItemId,
            materialSpec: item.materialSpec,
            sourceItemNumber: item.sourceItemNumber,
            sourceNumber: item.sourceNumber,
            conversionRate: item.conversionRate,
            saleMaterialNumber: item.saleMaterialNumber,
            arrivedTime: null,
            batchNumber: null,
            productionDate: null,
            expiryDate: null,

            storageLocation: null,
            busAccount: null,
            factory: item.factory,
            factory_dictText: item.factory_dictText,
            batch_dictText: item.batch_dictText,
            purchaseUnit_dictText: item.purchaseUnit_dictText,
            quantityUnit_dictText: item.quantityUnit_dictText,
            itemStatus_dictText: item.itemStatus_dictText,
            onWayQuantity: item.onWayQuantity,
            orderReceiveQuantity: item.receiveQuantity,
            notDeliveryQuantity: item.notDeliveryQuantity,
            sourceType_dictText: item.sourceType_dictText
          }

          if (!!item.deliveryQuantity) {
            item.secondaryQuantity = item.deliveryQuantity / (item.conversionRate || 1)
          } else {
            item.secondaryQuantity = 0
          }
          item.secondaryQuantity = item.secondaryQuantity.toFixed(6)

          return item
        })

        itemGrid.insertAt(insertData, -1)
      }
    },
    //删除复选框选定行
    deleteGridItem() {
      let itemGrid = this.$refs.editPage.$refs.saleDeliveryItemList[0]
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning('请选择数据！')
        return
      }
      for (let i = 0; i <= checkboxRecords.length; i++) {
        let item = checkboxRecords[i]
        if (item && item.sourceType && item.sourceType == 'bookDelivery') {
          this.$message.warning('不能删除来源类型是送货预约类型的行!')
          return
        }
      }
      itemGrid.removeCheckboxRow()
    },
    saveEvent() {
        let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
        let proDateErrorStr =''
        let curDate = this.getCurrentDate()
        params.saleDeliveryItemList.map(item=>{
            if(item.productionDate!==''&&item.productionDate>curDate){
                let mess = item.materialNumber+'_'+item.materialName
                proDateErrorStr='物料为:'+'['+mess+']所在行,生产日期不得大于当前日期'
                return
            }
        })
        if(proDateErrorStr!=null && proDateErrorStr!=''){
            this.$message.warning(proDateErrorStr)
            return
        }
        this.$refs.editPage.postData()
    },
    save() {
      let param = this.$refs.editPage ? this.$refs.editPage.form : {}
      if (param.saleDeliveryItemList.length == 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
        return false
      }
      this.saveEvent()
    },

    // 新增辅料发料弹框
    addSubList() {
      this.fieldSelectType = 'subOrder'
      let url = '/delivery/saleDeliverySub/queryDeliverySubOrderByMainSupplier'
      let columns = [
        { field: 'subElsAccount', title: '供应商els账号', width: 150, showOverflow: true },
        { field: 'subSupplierName', title: '供应商名称', width: 150, showOverflow: true },
        { field: 'orderNumber', title: '采购订单号', width: 150, showOverflow: true },
        { field: 'orderItemNumber', title: '订单行号', width: 150, showOverflow: true },
        { field: 'materialNumber', title: '物料编码', width: 150, showOverflow: true },
        { field: 'materialName', title: '物料名称', width: 150, showOverflow: true },
        { field: 'materialDesc', title: '物料描述', width: 150, showOverflow: true },
        { field: 'quantity', title: '订单数量', width: 150, showOverflow: true },
        { field: 'canDeliveryQuantity', title: '剩余可发货数量', width: 150, showOverflow: true },
        { field: 'quantityUnit_dictText', title: '主单位', width: 150, showOverflow: true },
        { field: 'purchaseUnit_dictText', title: '辅单位', width: 150, showOverflow: true }
      ]
      let form = this.$refs.editPage.getPageData()
      console.log(form)

      let params = {
        mainSupplierCode: form.supplierCode, // 主供应商ELS编码（必传，即当前供应商编码）
        factory: form.factory // 库存组织
      }
      let errMsg = ''
      if (!params.mainSupplierCode) errMsg = '主供应商编码'
      else if (!params.factory) errMsg = '库存组织'

      if (!!errMsg) return this.$message.warning(`请先选择${errMsg}`)
      this.$refs.fieldSelectModal.open(url, params, columns, 'multiple')
    },

    // 删除辅料发料
    deleteSubList() {
      let itemGrid = this.$refs.editPage.$refs.saleDeliverySubList[0]
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning('请选择数据！')
        return
      }

      itemGrid.removeCheckboxRow()
    },

    splitRow(row) {
      console.log(123)
      const _row = {
        ...row,
        id: null
      }
      delete _row._X_ROW_KEY
      let itemGrid = this.$refs.editPage.$refs.saleDeliveryItemList[0]
      itemGrid.insertAt(_row, -1)
    },
    allowSplitDelete(row) {
      let id = row.id
      if (id) {
        return true
      }
      return false
    },
    splitDelete(row) {
      let itemGrid = this.$refs.editPage.$refs.saleDeliveryItemList[0]
      itemGrid.remove(row)
    }
  }
}
</script>
