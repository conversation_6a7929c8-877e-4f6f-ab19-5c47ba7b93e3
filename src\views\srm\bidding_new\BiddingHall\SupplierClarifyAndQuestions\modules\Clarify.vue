<template>
  <div class="clarify">
    <div class="title_ctrl clearfix margin-b-10">
      <div class="fl">
        <span >{{ $srmI18n(`${$getLangAccount()}#i18n_field_QILV_2f5d3190`, '文件澄清') }}</span>
      </div>
      <!-- <div
        class="fr">
        <a-button
          type="text"
          size="small"
          @click="handleAddClarify">提出澄清</a-button>
      </div> -->
    </div>
    <div>
      <listTable 
        ref="listTable"
        :showTablePage="false"
        :defaultParams="defaultParams"
        :pageData="pageData"
        :url="url"
        :statictableColumns="tableColumns"
      />
      <!-- <a-modal
        v-model="visible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_warmPrompt`, '提示')"
        @ok="selectedOk">
        <p style="font-size: 18px;text-indent: 2em">{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBxCchbrAVqMeBQI_c6327f12`, '投标函内容发生改变,请撤回投标文件')+'!' }}</p>
      </a-modal> -->
    </div>
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import listTable from '../../components/listTable'
export default {
    components: {
        listTable
    },
    computed: {
        subId (){
            return this.subpackageId()
        }
    },
    inject: ['subpackageId'],
    data () {
        return {
            // visible: false,
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题'),
                    'field': 'title'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hGKI_275a78c7`, '发出时间'),
                    'field': 'publishTime',
                    'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText',
                    'width': 80
                },
                // {
                //     'title': '确认情况（已确认/总数）',
                //     'field': 'confirmNumber&total',
                //     'width': 200,
                //     slots: {
                //         default: ({row}) => {
                //             return [
                //                 `${row.confirmNumber}/${row.total}`
                //             ]
                //         }
                //     }
                // },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleClarifyViewPage},
                    {type: 'confirm', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RL_f20f6`, '确认'), clickFn: this.handleConfirm, allow: this.allowConfirm}

                    // {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit,allow: this.allowEdit},
                    // {type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'), clickFn: this.handlePublish,allow: this.allowPublish},
                    // {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete,allow: this.allowDelete}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            defaultParams: {},
            url: {
                list: '/tender/saleTenderClarificationInfo/list',
                confirm: '/tender/saleTenderClarificationInfo/confirm'
            }
        }
    },
    methods: {
        // selectedOk () {
        //     this.confirmInterface()
        // },
        // handleAddClarify () {
        //     this.$emit('handClarifyEditPage', {})
        // },
        handleClarifyViewPage (row){
            this.$emit('handleClarifyViewPage', row)
        },
        handleConfirm (row){
            let {biddingDocuments='', responseStatus=''} = row
            var that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_warmPrompt`, '温馨提示'),
                content: (biddingDocuments == '1' && responseStatus == '1') ? this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxCchbrAVqMeBQI_c6327f12`, '投标函内容发生改变,请撤回投标文件?') : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRL_c80926d2`, '是否确认?'),
                onOk: () => {
                    that.confirmLoading = true
                    postAction(that.url.confirm+'?id='+row.id).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.$refs.listTable.loadData() // 刷新页面
                        }else{
                            that.$message.error(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        allowConfirm (row){
            return row.status == '1'
        }
       
    },
    created () {
        this.defaultParams= { subpackageId: this.subId }
        console.log('defaultParams', this.defaultParams)
    }
}
</script>
<style lang="less" scoped>
.fl{
  float: left;
}
.fr{
  float: right;
}
.clearfix{
  clear:both;
}
.title_ctrl {
  padding: 8px;
  background: #F2F3F5;
}
.clarify{
  margin-top: 20px;
}
.margin-b-10{
  margin-bottom: 5px;
}
</style>


