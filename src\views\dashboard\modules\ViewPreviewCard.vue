<template>
  <a-card
    size="small"
    class="workplace_card"
    :title="modalData.chartType != 'num' ? modalData.chartName : null"
    style="width:100%;height:100%"
  >
    <!-- <vue-echarts
          v-if="modalData.chartType == 'echart'"
          autoresize
          style="height: 300px;"
          theme="light"
          :auto-resize="true" 
          :option="options"/> -->
    <EchartPreview
      :widget="widget"
      v-if="modalData.chartType == 'echart'"
    />


    <!-- style="height: calc(100% - 37px);overflow: auto; " -->
    <div
      v-if="modalData.chartType == 'list'"
      style="height:100%">
      <vxe-grid
        v-bind="getGridConfig()"
        :data="resultData.length && resultData[0]?.data || []"
        :columns="resultData.length && resultData[0]?.head || []"
      >
      </vxe-grid>

    </div>
        
    <!-- <div
          v-else-if="modalData.chartType == 'num'"
          class="num-panel-item-new">
          <div class="row">
            <div class="title">{{ modalData.chartName }}</div>
            <div class="num-panel-icon">
              <a-icon
                :type="numIcon(modalData)"
                style="font-size: 28px;color: #1890ff"></a-icon>
            </div>
          </div>
          <div class="count">{{ modalData.resultData }}</div>
        </div> -->
    <div
      v-if="modalData.chartType == 'num'"
      class="num-panel-item-new">
      <div
        class="box"
        :key="idx"
        v-for="(el, idx) of resultData">
        <div class="row">
          <div class="title">{{ el.name }}</div>
          <div class="num-panel-icon">
            <a-icon
              type="pay-circle"
              style="font-size: 28px;color: #1890ff"></a-icon>
          </div>
        </div>
        <div class="count">{{ el.value }}</div>
      </div>
    </div>
    <EchartMap
      :options="options"
      :chartStyle="chartStyle"
      :data="resultData"
      :initEarchOptain="initEarchOptain"
      ref="EchartMap"
      v-if="modalData.chartType == 'map'" />

  </a-card>
</template>

<script>
import EchartPreview from '@views/dashboard/modules/echartPreview'
import EchartMap from '@/components/EchartMap/index'

export default {
    components: {
        EchartMap,
        EchartPreview
    },
    props: {
        initOptain: {
            type: Object,
            default: ()=> {
                return {
                    width: 'auto',
                    height: 'auto'
                }
            }
        },
        modalData: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        extraGridConfig: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        widget: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        options: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        resultData: {
            type: [Array, Object],
            default: ()=> {
                return null
            }
        },
        chartStyle: {
            type: Object,
            default: ()=> {
                return  {'width': '100%', 'height': '350px'}
            }
        }
    },
    data () {
        return {
     
            visibleViews: false,
            gridConfig: {
                border: true,
                stripe: true,
                resizable: true,
                autoResize: true,
                keepSource: true,
                height: '100%',
                showOverflow: true,
                showHeaderOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                // size: 'medium',
                align: 'center',
                headerAlign: 'center'
            }
        }
    },
    methods: {
        open () {
            this.visibleViews = true
        },
        getGridConfig () {
            let assignConfig = Object.assign({}, this.gridConfig, this.extraGridConfig)
            return assignConfig
        }
    }
}
</script>

<style lang="less" scoped>
.num-panel-item-new {
      width: 100%;
    height: 100%;
  .box{
         width: 100%;
    padding: 10px;
    height: 100%;
    // min-height: 100px;
    // -webkit-box-shadow: 0px 0px 6px #949494;
    // box-shadow: 0px 0px 6px #949494;
    }
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      border-radius: 30px;
      text-align: center;
      padding: 9px 16px;
      color: white;
      background: #ed2f2f;
      line-height: 100%;
      height: 15px;
      font-weight: 600;
      -webkit-box-sizing: content-box;
      box-sizing: content-box;
    }
  }
  .count {
    padding: 10px 0;
    font-size: 18px;
    color: #494949;
    font-weight: 500;
  }
}
</style>