<template>
  <div class="els-page-comtainer">
    
    <a-page-header
      :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
      :title="title"
    >
      <template slot="extra">
        <a-button
          @click="handleTest"
          type="primary"
          key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <!-- <a-button
          @click="handleTest"
          type="primary"
          key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_iK_dc7ea`, '测试') }}</a-button> -->
        <a-button
          @click="goBack"
          key="3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-page-header>
    <div class="table-page-search-wrapper">
      <a-spin :spinning="confirmLoading">
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            key="1"
            :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
          >
            <a-form-model
              ref="soureceForm"
              :rules="validatorRules"
              :model="form"
            >
              <a-form-model-item 
                required
                prop="sourceCode"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_WFjAo_c451d17d`, '数据源编码')">
                <a-input
                  v-model="form.sourceCode"
                  v-decorator="['sourceCode', validatorRules.sourceCode]"/>
              </a-form-model-item >
              <a-form-model-item 
                required
                prop="sourceName"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_WFjRL_c44c9d55`, '数据源名字')">
                <a-input
                  v-model="form.sourceName" />
              </a-form-model-item >
              <a-form-model-item 
                :labelCol="labelCol"
                prop="sourceType"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_dataType`, '数据源类型')">
                <!-- <j-dict-select-tag
                  v-model="form.sourceType"
                  @change="dataTypeChange"
                  :trigger-change="true"
                  dict-code="srmDataSourceType"
                /> -->
                <m-select
                  v-model="form.sourceType"
                  :configData="item"
                  dictCode="srmDataSourceType"
                  @relation="dataTypeChange" />
              </a-form-model-item >
              <a-form-model-item 
                prop="sourceDesc"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_WFjMW_c44e9bd3`, '数据源描述')">
                <a-input
                  v-model="form.sourceDesc" />
              </a-form-model-item >
              <!-- <a-form-model-item 
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_dataSQL`, '数据源配置')">
                <a-input
                  type="textarea"
                  rows="18"
                  v-decorator="['sourceConfig', validatorRules.sourceConfig]" />
              </a-form-model-item > -->
              <a-form-model-item 
                v-if="form.sourceConfig.length>0"
                v-for="(el, index) in form.sourceConfig"
                :key="index"
                :prop="el.label"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="el.labelValue">
                <a-input
                  v-model="el.value" />
              </a-form-model-item >

            </a-form-model>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </div>
  </div>
  <!-- </a-modal> -->
</template>

<script>
import { postAction } from '@/api/manage'
import pick from 'lodash.pick'
import { duplicateReportCheck, ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'

export default {
    name: 'ChartDataModal',
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    data () {
        return {
            activeKey: ['1'],
            fixPageHeader: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_chartData`, '图表数据'),
            visible: false,
            model: {},
            labelCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            wrapperCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            confirmLoading: false,
            form: {
                sourceCode: '',
                sourceName: '',
                sourceType: '',
                sourceDesc: ''
            },
            url: {
                add: '/report/dataSource/elsReportChartDataSource/add',
                test: '/report/dataSource/elsReportChartDataSource/testConnection',
                edit: '/report/dataSource/elsReportChartDataSource/edit'
            },
            validatorRules: {
                sourceCode: {rules: [{ required: true, message: '请输入数据编码!'}, {max: 50, message: '内容长度不能超过50个字符!'}, { validator: this.validateCode }]},
                // dataCode: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataCodingMsg`, '请输入数据编码!')}, {max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow`, '内容长度不能超过50个字符!')} ]},
                // dataType: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataTypeMsg`, '请选择数据类型!')}, {max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow`, '内容长度不能超过50个字符!')}]},
                // dataDesc: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataDescribeMsg`, '请输入数据描述!') }, {max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow`, '内容长度不能超过50个字符!')}]},
                // dataSql: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataSQLMsg`, '请输入数据SQL!')}]}
            }
        }
    },
    computed: {
        langAccount () {
            return this.$getLangAccount()
        },
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow) {
                if (this.currentEditRow.busAccount || this.currentEditRow.elsAccount) {
                    account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount
                }
            }
            return account
        }

        // validatorRules (){
        //     let account = this.langAccount
        //     const {$srmI18n} = this
        //     return {
        //         dataCode: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_dataCodingMsg`, '请输入数据编码!')}, {max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符!')} ]},
        //         dataType: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_dataTypeMsg`, '请选择数据类型!')}, {max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符!')}]},
        //         dataDesc: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_dataDescribeMsg`, '请输入数据描述!') }, {max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符!')}]},
        //         dataSql: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_dataSQLMsg`, '请输入数据SQL!')}]}
        //     }
        // }

    },
    mounted () {
        this.init()
        window.addEventListener('scroll', this.handleScroll)        
    },
    methods: {
        dataTypeChange ({selectedData}) {
            const sourceConfig = selectedData?.description && JSON.parse(selectedData.description) || []
            if (this.model.id) {
                sourceConfig.forEach(rs => {
                    console.log('this.form.sourceConfig[rs.label]')
                    console.log(this.form.sourceConfig[rs.label])
                    rs.value = this.form.sourceConfig[rs.label] ||  (this.form.sourceConfig[rs.label] == '' ? '' : rs.value)
                })
            }          
            console.log(sourceConfig)
            this.form.sourceConfig = sourceConfig
        },
        init () {
            if(this.currentEditRow) {
                this.edit(this.currentEditRow)
            }else {
                this.add()
            }
        },
        add () {
            this.edit({})
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VaWFj_c2c7caa4`, '新增数据源')
        },
        edit (record) {
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AtWFj_41ab4137`, '编辑数据源')
            // this.form.resetFields()
            this.model = Object.assign({}, record)
            this.visible = true
            this.$nextTick(() => {
                if (this.model.sourceType) {
                    let postData = {
                        busAccount: this.busAccount,
                        dictCode: 'srmDataSourceType'
                    }
                    ajaxFindDictItems(postData).then(res => {
                        if(res.success) {
                            let opt = res.result.find(n => n.value == this.model.sourceType)
                            if (opt) {
                                this.dataTypeChange({selectedData: opt})
                            }
                        } else {

                        }
                    })
                }
                this.model.sourceConfig =  this.model?.sourceConfig ? JSON.parse(this.model.sourceConfig) : []
                console.log(this.model)
                this.form = pick(this.model, 'sourceCode', 'sourceType', 'sourceName', 'sourceDesc', 'sourceConfig')
                console.log(this.form)
            })

        },

        validateCode (rule, value, callback) {
        // 重复校验
            var params = {
                tableName: 'els_report_chart_data_source',
                fieldName: 'source_code',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateReportCheck(params).then((res) => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.$refs.soureceForm.validate(valid => {
                console.log('valid')
                console.log(valid)
                if (valid) {
                    that.confirmLoading = true
                    let httpurl = ''
                    console.log('this.form')
                    console.log(this.form)
                    console.log(this.model)
                    if(!this.model.id){
                        httpurl+=this.url.add
                    }else{
                        httpurl+=this.url.edit
                    }
                    let params = {...this.form}
                    let  sourceConfig = {}
                    if (this.form?.sourceConfig) {
                        let obj = this.form.sourceConfig.map(rs => {return {[rs.label]: rs.value}})
                        console.log(obj)
                        obj.forEach(rs => Object.assign(sourceConfig, rs))
                        params.sourceConfig = sourceConfig ? JSON.stringify(sourceConfig) : ''
                    }
                    if (this.model.id) {
                        params.id = this.model.id
                    }
                    console.log(params)
                    postAction(httpurl, params).then((res)=>{
                        if(res.success){
                            that.$message.success(res.message)
                            that.$emit('ok')
                        }else{
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                        that.goBack()
                    })
                }
            })
        },
        handleTest () {
            const that = this
            // 触发表单验证
            this.$refs.soureceForm.validate(valid => {
                if (valid) {
                    that.confirmLoading = true
                    let httpurl = this.url.test
                    /**
                     * {"sourceConfig":"{\"driverName\":\"\",\"jdbcUrl\":\"*********************************************************************************************************\",\"username\":\"root\",\"password\":\"123456\"}","sourceType":"mysql"}
                     */
                    let sourceConfig = {}
                    console.log(this.form?.sourceConfig)
                    if (this.form?.sourceConfig) {
                        let obj = this.form.sourceConfig.map(rs => {return {[rs.label]: rs.value}})
                        console.log(obj)
                        obj.forEach(rs => Object.assign(sourceConfig, rs))
                    }
                    const formData= {
                        sourceConfig: sourceConfig ? JSON.stringify(sourceConfig) : '',
                        sourceType: this.form.sourceType
                    }
                    postAction(httpurl, formData).then((res)=>{
                        if(res.success){
                            // that.$message.success(res.message)
                            that.handleOk()
                            that.$emit('ok')
                        }else{
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        handleScroll () {
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 130) {
                this.fixPageHeader = true
            }else {
                this.fixPageHeader = false
            }
        }
    }
}
</script>

<style lang="less" scoped>

</style>