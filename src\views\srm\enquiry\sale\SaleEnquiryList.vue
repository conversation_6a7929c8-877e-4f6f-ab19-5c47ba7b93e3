<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-04-27 15:34:49
 * @LastEditors: LokNum
 * @LastEditTime: 2022-05-20 17:50:35
 * @Description: 销售协同/寻源协同/报价管理
-->

<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 详情界面 -->
    <sale-enquiry-detail 
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleEnquiryDetail from './modules/SaleEnquiryDetail'
import layIM from '@/utils/im/layIM.js'
export default {
    name: 'SaleEnquiryList',
    mixins: [ListMixin],
    components: {
        SaleEnquiryDetail
    },
    data () {
        return {
            pageData: {
                form: {
                    enquiryDesc: '',
                    enquiryNumber: ''
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquirySheetNo`, '询价单号'),
                        fieldName: 'enquiryNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterinquirySheetNo`, '请输入询价单号')
                    },
                    { 
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enquiryDesc`, '询价描述'),
                        fieldName: 'enquiryDesc',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterInquiryDescription`, '请输入询价单描述') 
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleSpecialByView},
                    {authorityCode: 'enquiry#SaleEnquiryHead:chat', type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat}
                ]
            },
            url: {
                list: '/enquiry/saleEnquiryHead/list',

                columns: 'saleEnquiryList' 
            }
        }
    },
    created () {
        this.getUrlParam()
    },
    mounted () {
        this.serachCountTabs('/enquiry/saleEnquiryHead/queryTabsCount')
    },
    watch: {
        // '$route': {
        //     handler () {
        //         this.init()
        //     }
        // }
    },
    methods: {
        // init (){
        //     if (!this.$route.query.id && this.$route.path.includes('SaleEnquiryList')){
        //         this.showDetailPage = false
        //         console.log('[$route],不存在阿')
        //         this.$refs.listPage.initColumns()
        //         this.$refs.listPage.loadData()
        //         this.$refs.listPage.columnDrop()
        //     }
        // },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.enquiryNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'SaleEnquiry', url: this.url || '', recordNumber})
        },
        getUrlParam (){
            let templateNumber = this.$route.query.templateNumber
            let templateVersion = this.$route.query.templateVersion
            let templateAccount = this.$route.query.templateAccount
            let busAccount = this.$route.query.busAccount
            let id = this.$route.query.id
            let supplierTaxRate = this.$route.query.supplierTaxRate
            if(templateNumber && templateVersion && id && busAccount){
                let row = {}
                row['templateNumber'] = templateNumber
                row['templateVersion'] = templateVersion
                row['templateAccount'] = templateAccount
                row['id'] = id
                row['busAccount'] = busAccount
                row['supplierTaxRate'] = supplierTaxRate
                this.$nextTick(()=> {
                    this.currentEditRow = row
                    this.showDetailPage = true
                })
            }
        }
    }
}
</script>