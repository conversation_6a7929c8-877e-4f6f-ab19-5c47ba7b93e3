<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-04-27 15:34:49
 * @LastEditors: LokNum
 * @LastEditTime: 2022-05-16 16:56:18
 * @Description: 采购协同/寻源协同/询价管理
-->
<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showBargainPage && !showPrintPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :currentEditRow="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 编辑界面 -->
    <purchase-enquiry-edit
      v-if="showEditPage"
      ref="editPage"
      :currentEditRow="currentEditRow"
      @hide="hideEditPageNew"/>
    <!-- 详情界面 -->
    <purchase-enquiry-detail
      v-if="showDetailPage"
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :allowEdit="allowViewToEdit"
      @hide="hideEditPageNew"
      @toEdit="handleEditFromViewPage"/>
    <!-- 议价界面 -->
    <purchase-enquiry-bargain
      v-if="showBargainPage"
      ref="bargainPage"
      :currentEditRow="currentEditRow"
      @hide="hideEditPageNew"/>
    <!-- 询价打印 -->
    <EnquiryPrint
      v-if="showPrintPage"
      :current-edit-row="currentEditRow"
      ref="printPage"
      @hide="hidePrintPage" />

    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="ebiddingTemplateVisible"
    >
      <template slot="footer">
        <a-button
          key="back"
          @click="handleDemandCancel">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="ebiddingTemplateSubmitLoading"
          @click="selectedEbiddingTemplate">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="ebiddingTemplateNumber"
        :options="ebiddingTemplateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"/>
    </a-modal>

    <a-modal title="废弃处理意见"
             :visible="handleOpinionCancelVisible"
             @ok="handleOpinionOk"
             @cancel="handleOpinionCancel">
        <a-form-model ref="formCancelOpinion"
                      :rules="rulesCancelOpinion"
                      :model="setformCancelOpinion">
            <a-form-model-item
                label="作废事由"
                prop="fbk16">
                <a-input
                    v-model="setformCancelOpinion.fbk16"
                    placeholder="请输入作废事由">
                </a-input>
            </a-form-model-item>
        </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import PurchaseEnquiryBargain from './modules/PurchaseEnquiryBargain.vue'
import PurchaseEnquiryDetail from './modules/PurchaseEnquiryDetail.vue'
import PurchaseEnquiryEdit from './modules/PurchaseEnquiryEdit.vue'
import EnquiryPrint from './modules/EnquiryPrint.vue'
import layIM from '@/utils/im/layIM.js'
import {getAction, postAction} from '@/api/manage'
import {cloneDeep} from 'lodash'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseEnquiryBargain,
        PurchaseEnquiryEdit,
        PurchaseEnquiryDetail,
        EnquiryPrint
    },
    data () {
        return {
            handleOpinionCancelVisible:false,
            rulesCancelOpinion: {
                fbk16: [{required: true, message: '请输入作废事由',trigger: 'blur' }]
            },
            setformCancelOpinion:{},
            cancelRowId:'',
            currentRow: {},
            showBargainPage: false,
            showPrintPage: false,
            ebiddingTemplateSubmitLoading: false,
            ebiddingTemplateVisible: false,
            ebiddingTemplateNumber: '',
            currentEnquiryId: '',
            ebiddingTemplateOpts: [],
            pageData: {
                businessType: 'enquiry',
                form: {
                    enquiryDesc: '',
                    enquiryNumber: '',
                    enquiryStatus: undefined
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquirySheetNo`, '询价单号'),
                        fieldName: 'enquiryNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterinquirySheetNo`, '请输入询价单号')
                    },
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enquiryDesc`, '询价标题'),
                        fieldName: 'enquiryDesc',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterInquiryDescription`, '请输入询价标题')
                    }
                ],
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'enquiry#purchaseEnquiryHead:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationHistory`, '报价历史'),
                        icon: 'snippets',
                        clickFn: this.showHistoryList,
                        authorityCode: 'enquiry#purchaseEnquiryHead:hisList'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        folded: true,
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
                        icon: 'download',
                        folded: true,
                        clickFn: this.handleExportXls,
                        authorityCode: 'enquiry#purchaseEnquiryHead:export'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'chat',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'),
                        clickFn: this.handleChat,
                        allow: this.allowChat,
                        authorityCode: 'enquiry#purchaseEnquiryHead:chat'
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        allow: this.allowView,
                        authorityCode: 'enquiry#purchaseEnquiryHead:query'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.allowEdit,
                        authorityCode: 'enquiry#purchaseEnquiryHead:edit'
                    },
                    { type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制')
                        , clickFn: this.handleCopy, authorityCode: 'enquiry#purchaseEnquiryHead:copy' },
                    {
                        type: 'copy',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_husOu_d94298d0`, '询价转竞价'),
                        clickFn: this.enquiryToEbidding,
                        //allow: this.allowCancel,
                        authorityCode: 'ebidding#purchaseEbiddingHead:enquiryToEbidding'
                    },
                    {
                        type: 'cancel',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                        clickFn: this.handleCancel,
                        allow: this.allowCancel,
                        authorityCode: 'enquiry#purchaseEnquiryHead:cancel'
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.allowEdit,
                        authorityCode: 'enquiry#purchaseEnquiryHead:delete'
                    },
                    {
                        type: 'openLobby',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquiryHall`, '询价大厅'),
                        clickFn: this.openLobby,
                        allow: this.allOpenLobby,
                        authorityCode: 'enquiry#purchaseEnquiryHead:query'
                    },
                    {
                        type: 'print', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印'), 
                        clickFn: this.handlePrint,
                        allow: this.allowPrint,
                        authorityCode: 'enquiry#purchaseEnquiryHead:print'
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ]
            },
            url: {
                add: '/enquiry/purchaseEnquiryHead/add',
                list: '/enquiry/purchaseEnquiryHead/list',
                delete: '/enquiry/purchaseEnquiryHead/delete',
                cancel: '/enquiry/purchaseEnquiryHead/cancel',
                columns: 'PurchaseEnquiryList',
                exportXlsUrl: '/enquiry/purchaseEnquiryHead/exportXls',
                copyData: '/enquiry/purchaseEnquiryHead/copy'
            },
            countTabsUrl: '/enquiry/purchaseEnquiryHead/count'
        }
    },
    computed: {
        allowViewToEdit() {
            if(!!this.currentEditRow && !!this.allowEdit(this.currentEditRow)) return false;
            return true;
        }
    },
    mounted () {
        this.serachCountTabs(this.countTabsUrl)
    },
    created () {
        if (this.$route.query.id) {
            this.pageData.form.id = this.$route.query.id
            this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
        }
    },
    beforeRouteEnter (to, from, next) {
        if (Object.keys(to.query).length) {
            next(vm => vm.init())
        } else {
            next()
        }
    },
    methods: {
        //询价转竞价
        enquiryToEbidding (row) {
            this.currentEnquiryId = row.id
            let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'ebidding'}
            getAction('/template/templateHead/getListByType', params).then((res)=> {
                if (res && res.success) {
                    if (res.result && res.result.length) {
                        let options = res.result.map(item => {
                            return {
                                value: item.templateNumber,
                                title: item.templateName,
                                version: item.templateVersion,
                                account: item.elsAccount
                            }
                        })
                        this.ebiddingTemplateNumber = ''
                        this.ebiddingTemplateOpts= options
                        this.ebiddingTemplateVisible = true
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        // 询价转竞价选择模板-取消
        handleDemandCancel () {
            this.ebiddingTemplateVisible = false
        },
        // 询价转竞价选择模板-确定
        selectedEbiddingTemplate () {
            if(this.ebiddingTemplateNumber) {
                const that = this                
                let template = this.ebiddingTemplateOpts.filter(item => {
                    return item.value == that.ebiddingTemplateNumber
                })
                let params = {
                    templateNumber: this.ebiddingTemplateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version,
                    templateAccount: template[0].account,
                    id: this.currentEnquiryId
                }
                this.ebiddingTemplateSubmitLoading = true
                postAction('/ebidding/purchaseEbiddingHead/enquiryToEbidding', params).then(res=> {
                    if (res && res.success) {
                        this.ebiddingTemplateVisible= false
                        const query = {
                            source: 'demand-pool',
                            result: res.result
                        }
                        this.$router.push({ path: '/srm/ebidding/EbiddingBuyHeadList', query})
                    } else {
                        this.$message.warning(res.message)
                    }
                    that.ebiddingTemplateSubmitLoading = false
                })
            } else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectTemplateType`, '请选择模板'))
            }
        },   
        //询价大厅
        openLobby (row){
            // this.$store.dispatch('SetTabConfirm', false)
            // this.$router.push({
            //     path: '/enquiry/purchaseHall',
            //     query: {
            //         id: row.id
            //     }
            // })
            window.open(`${window.origin}/enquiry/purchaseHall?id=${row.id}`, '_blank')
        },
        allOpenLobby (row){
            let disableList = ['12']
            let publistStatBoolen= disableList.findIndex(item => row.enquiryStatus === item) >= 0
            const { enquiryStatus = '0' } = row
            return publistStatBoolen?publistStatBoolen:enquiryStatus === '0'
        },
        // 复制功能按钮
        handleCopy (row) {
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(row)
                }
            })
        },
        // 复制数据请求接口
        copyData (row) {
            this.confirmLoading = true
            let param  = { id: row.id }
            getAction(this.url.copyData, param).then(res => {
                if (res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功！'))
                    this.searchEvent()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 返回按钮
        hideEditPageNew () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) { 
                // 关闭超链接页面
                this.closeCurrent()
            }
            if (query.source == 'demand-pool') {
                this.$router.replace({path: this.$route.path})
            }
            this.pageData.form.id = ''
            this.showEditPage = false
            this.showDetailPage = false
            this.showBargainPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        init () {
            this.showBargainPage = false
            this.showDetailPage = false
            delete this.pageData.form.id // 附件管理跳转到询价，询价详情返回后依然有id导致查询出错，所以手动删除id
            if (this.$route.query.id && this.$route.path.includes('PurchaseEnquiryList')) {
                this.pageData.form.id = this.$route.query.id
                this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
                this.$refs.listPage.initColumns()
                this.$refs.listPage.loadData()
                this.$refs.listPage.columnDrop()
            }
            this.handleDemandPool()
        },
        handleDemandPool () {
            this.showEditPage = false
            const query = this.$route.query
            if (query.source == 'demand-pool') { // 需求调整过来
                if (query?.result?.length == 1) { // 只有一条
                    this.currentEditRow = query.result[0]
                    this.showEditPage = true
                }
            }
        },
        handleChat (row) {
            let {id} = row
            let recordNumber = row.enquiryNumber || id
            layIM.creatGruopChat({id, type: 'PurchaseEnquiry', url: this.url || '', recordNumber})
        },
        getUrlParam () {
            let templateNumber = this.$route.query.templateNumber
            let templateVersion = this.$route.query.templateVersion
            let id = this.$route.query.id
            if (templateNumber && templateVersion && id) {
                let row = {}
                row['templateNumber'] = templateNumber
                row['templateVersion'] = templateVersion
                row['id'] = id
                this.currentEditRow = row
                this.showDetailPage = true
            }
        },
        allowEdit (row) {
            if (row.auditStatus == '1') {
                return true
            }
            //新建, 发布失败
            let enableList = ['0', '13']
            return enableList.findIndex(item => row.enquiryStatus === item) < 0
        },
        allowView (row) {
            //发布中
            let disableList = ['12']
            return disableList.findIndex(item => row.enquiryStatus === item) >= 0
        },
        allowCancel (row) {
            //报价中  议价中  未报价
            let enableList = ['1', '7', '3']
            return enableList.findIndex(item => row.enquiryStatus === item) < 0 || (row.resultAuditStatus == '1' || row.auditStatus == '1')
        },
        allowChat (row) {
            //新建  作废  发布中  发布失败
            let disableList = ['0', '10', '12', '13']
            return disableList.findIndex(item => row.enquiryStatus === item) >= 0
        },
        allowPrint (row) {
            // 已定价、议价中状态可操作
            return row.enquiryStatus !== '9' && row.enquiryStatus !== '7'
        },
        submitCallBack (row) {
            this.currentEditRow = row
            // 更改申鹤状态
            this.$set(this.currentEditRow, 'auditStatus', '1');
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (row) {
            this.currentEditRow = row
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        handleView (row) {
            let viewRow = cloneDeep(row);
            const { query } = this.$route
            const viewAuth = this.getViewAuth()
            console.log('viewAuth----')
            console.log(viewAuth)
            if (!viewAuth) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BjbWWVKHRvjlb_beee0887`, '没有权限，请联系管理员授权'))
                return
            }
            //新建 || 发布失败
            if ((row.enquiryStatus === '0' || row.enquiryStatus === '13') || (query?.id && query?.open)) {
                //打开详情页
                this.showDetailPage = false
                this.$nextTick(() => {
                    this.viewRow = viewRow;
                    this.currentEditRow = row
                    this.showDetailPage = true
                    // this.$store.dispatch('SetTabConfirm', true)
                })
            } else {
                this.viewRow = viewRow;
                this.currentEditRow = row
                this.showBargainPage = true
            }
        },
        handlePrint (row) {
            // this.showPrintPage = true
            // this.currentEditRow = row
            let url = this.$router.resolve({
                path: '/print/enquiryPrint',
                query: { id: row.id }
            })
            window.open(url.href, '_blank')
        },
        // 已废弃
        handleBargain (row) {
            //新建 || 发布失败
            if (row.enquiryStatus === '0' || row.enquiryStatus === '13') {
                //打开详情页
                this.showDetailPage = false
                this.$nextTick(() => {
                    this.currentEditRow = row
                    this.showDetailPage = true
                    this.$store.dispatch('SetTabConfirm', true)
                })
            } else {
                this.currentEditRow = row
                this.showBargainPage = true
            }
        },
        hideBargainPage () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
            this.showBargainPage = false
            this.pageData.form.id = ''
        },
        hidePrintPage () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }else{
                this.showPrintPage = false
            }
        },
        showHistoryList () {
            let enquiryNumberList = []
            const records = this.$refs.listPage.$refs.listGrid.getCheckboxRecords() || []
            records.forEach(item => {
                enquiryNumberList.push(item.enquiryNumber)
            })
            this.$store.dispatch('SetTabConfirm', false)
            this.$router.push({
                path: '/enquiry/purchaseEnquiryHisList',
                query: {
                    enquiryNumbers: enquiryNumberList.join(',')
                }
            })
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquiryReport`, '确认是否报价'))
        },
        handleCancel (row) {
            this.handleOpinionCancelVisible = true
            this.cancelRowId = row.id
        },
        handleOpinionOk (){
            let that =this
            that.$refs.formCancelOpinion.validate(valid=>{
                if(valid){
                    that.loading = true
                    postAction(that.url.cancel, {id: that.cancelRowId,reason: that.setformCancelOpinion.fbk16}).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.handleOpinionCancelVisible = false
                            that.$refs.listPage.loadData()
                        } else {
                            that.$message.warning(res.message)
                            that.handleOpinionCancelVisible = false
                        }
                    })
                }else{
                    return false
                }
            })
        },
        handleOpinionCancel (){
            this.handleOpinionCancelVisible = false
        }
    }
}
</script>
