import { getAction } from '@/api/manage'
import { srmI18n, getLangAccount } from '@/utils/util'
import modalMixins from './modalMixins.js'
import { getHistoryTableData } from '../../api/analy.js'
import {getCommonAttachmentUrl} from '@/views/srm/bidding_new/utils'

import { EditConfig } from '@/plugins/table/gridConfig'

export default {
    name: 'ApprovalHistoryModal',
    mixins: [modalMixins],
    model: {
        prop: 'visible',
        event: 'change'
    },
    inject: {
        currentEditRow: {
            from: 'currentEditRow',
            default: null
        }
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '审批意见'
        },
        showModal: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            spinning: false,
            tableData: [],
            downloadUrl: '/attachment/purchaseAttachment/download',
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号')
                },
//                 {
//                     'title': srmI18n(`${getLangAccount()}#i18n_field_createTime`, '创建时间'),
//                     'field': 'createTime',
//                     'width': 150
//                 },
                {
                    'title': srmI18n(`${getLangAccount()}#i18n_field_approveTime`, '审批时间'),
                    'field': 'endTime',
                    'width': 150
                },
                {
                    'title': srmI18n(`${getLangAccount()}#i18n_field_nodeName`, '节点名称'),
                    'field': 'taskName',
                    'width': 120
                },
                {
                    'title': srmI18n(`${getLangAccount()}#i18n_field_userDeal`, '处理人'),
                    'field': 'userName',
                    'width': 80
                },
                {
                    'title': srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
                    'field': 'statusVal',
                    'width': 120
                },
                {
                    'title': srmI18n(`${getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见'),
                    'field': 'opinion',
                    'width': 200
                },
                {
                    'title': srmI18n(`${getLangAccount()}#i18n_field_LSRL_2527e109`, '任务名称'),
                    'field': 'title',
                    'width': 250
                },
                {
                    'title': srmI18n(`${getLangAccount()}#i18n_title_attachment`, '附件'),
                    'field': 'attachmentDetailedList',
                    'width': 300,
                    slots: {
                        default: ({ row }) => {
                            let arr = []
                            if (row.attachmentDetailedList && row.attachmentDetailedList) {
                                arr = row.attachmentDetailedList.map(item => {
                                    return <div >
                                        <span
                                            style={{ display: 'inline-block', width: '200px', color: 'blue', cursor: 'pointer', 'margin-right': '8px', overflow: 'hidden', 'text-overflow': 'ellipsis'}}
                                            title={item.name}
                                            onClick={() => this.downloadFile(item)}>{item.name}</span>
                                        <a
                                            style="margin-right: 8px"
                                            onClick={() => this.preViewEvent(item)}>{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览') }</a>
                                        <a
                                            onClick={() => this.downloadFile(item)}
                                        >{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载') }</a>
                                    </div>
                                })
                            }
                            return arr
                        }
                    }
                }
            ]
        }
    },
    watch: {
        visible: {
            immediate: true,
            handler (val) {
                if (!val) return
                this.getHistoryTableData()
            }
        }
    },
    methods: {
        preViewEvent (row) {
            this.$previewFile.open({ params: row })
        },
        getHistoryTableData () {
            this.spinning = true
            getHistoryTableData(this.currentEditRow.id)
                .then((res) => {
                    if (res.code == 0) {
                        this.tableData = res.data || []
                        console.log(this.tableData)
                    }
                })
                .finally(() => {
                    this.spinning = false
                })
        },
        handleCancel () {
            // this.resetData()
            this.$emit('change', false)
        },
        resetData () {
            Object.assign(this.$data, this.$options.data.call(this))
        },
        async downloadFile (row) {
            let {message: url} = await getCommonAttachmentUrl(row)
            this.spinning = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.name)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.spinning = false
            })
        }
    },
    render (h) {
        const props = {
            visible: this.visible,
            title: this.title,
            width: 900,
            keyboard: false,
            maskClosable: false,
            destroyOnClose: true
        }

        const on = {
            cancel: this.handleCancel,
            ok: this.handleCancel
        }

        const spinProps = {
            spinning: this.spinning,
            delayTime: 300
        }

        const scopedSlots = {
            empty: () => (<a-empty />)
        }
        return (
            <div class="approvalHistoryModal">
                {this.showModal ? <a-modal {...{ props, on }}>
                    <div class="content">
                        <a-spin {...{ props: spinProps }} >
                            <div  >
                                <vxe-grid data={this.tableData}
                                    columns={this.tableColumns}
                                    height="400"
                                    size="mini"
                                    resizable={true}
                                    {...EditConfig} scopedSlots={scopedSlots} />
                            </div>
                        </a-spin>
                    </div>
                </a-modal> : <a-spin {...{ props: spinProps }} >
                    <div class="table">
                        <div  >
                            <vxe-grid data={this.tableData}
                                columns={this.tableColumns}
                                resizable={true}
                                height="400"
                                size="mini"
                                {...EditConfig} scopedSlots={scopedSlots} />
                        </div>
                    </div>
                </a-spin>
                }
            </div>
        )
    }
}
