<template>
  <div class="tenderAdmin">
    <titleTrtl>
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_YBLoB_a188e213`, '招标人代表') }}</span>
      <template
        slot="right"
        v-if="pageStatus == 'edit'">
        <a-button
          type="primary"
          size="small"
          style="margin-right: 10px"
          @click="gridAdd">{{ $srmI18n(`${$getLangAccount()}#i18n_field_SuYBLoB_84e9f92e`, '添加招标人代表') }}</a-button>
        <a-button
          type="primary"
          size="small"
          @click="del">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
      </template>
    </titleTrtl>
    <listTable
      ref="listTable"
      :pageStatus="pageStatus"
      :fromSourceData="fromSourceData.tenderAdmin"
      :statictableColumns="statictableColumns"
      :editRulesProps="editRulesProps"
      :showTablePage="false" />
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>

<script>
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import REGEXP from '@/utils/regexp'

export default {
    mixins: [baseMixins],
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        currentRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        fromSourceData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    computed: {
    },
    components: {
        fieldSelectModal,
        listTable,
        titleTrtl
    },
    watch: {
      
    },
    data () {
        return {
            statictableColumns: [
                {
                    type: 'checkbox',
                    width: 50
                },
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n__WWWey_401851d`, '主账号'),
                    field: 'elsAccount',
                    headerAlign: 'center',
                    required: '1',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                    field: 'subAccount',
                    headerAlign: 'center',
                    required: '1',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                    field: 'elsRealname',
                    headerAlign: 'center',
                    width: 150,
                    fieldType: 'input',
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'),
                    field: 'phone',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 150,
                    fieldType: 'input',
			        required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                    field: 'certificateType',
                    headerAlign: 'center',
                    defaultValue: '',
                    dictCode: 'srmCertificateType',
                    width: 150,
                    fieldType: 'select',
			        required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                    field: 'certificateNumber',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 150,
                    fieldType: 'input',
			        required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_workUnit`, '工作单位'),
                    field: 'workUnit',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 150,
                    fieldType: 'input'
                }
            ],
            editRulesProps: {
                phone: [
                    { 
                        validator: ({ cellValue}) => {
                            if (cellValue) {
                                let reg = REGEXP.mobile
                                if (!reg.test(cellValue)) {
                                    return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNiRyo_b9c72f7e`, '请输入正确号码'))
                                }
                            }
                        }
                    }
                ]
            },
            show: false
        }
    },
    methods: {
        getValidate () {
            return this.$refs['listTable'].getValidate()
        },
        getTableData () {
            return this.$refs['listTable'].getTableData()
        },
        del () {
            this.$refs['listTable'].businessGridDelete()
        },
        gridAdd () {
            let url = 'account/elsSubAccount/list'
            let columns = [
                { field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '名称') },
                { field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ltyo_2e3c9979`, '手机号') },
                { field: 'certificateType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型') },
                { field: 'certificateNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号') },
                { field: 'workUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_workUnit`, '工作单位') }
            ]
            this.$refs.fieldSelectModal.open(url, { headId: this.fromSourceData.id }, columns, 'multiple')
        },
        fieldSelectOk (data) {
            let userList = data.map((item) => {
                delete item.id
                return {
                    ...item,
                    memberType: '0',
                    elsAccount: item.elsAccount,
                    elsSubAccount: item.subAccount,
                    elsRealname: item.realname
                }
            })
            this.$refs.listTable.insertAt(userList, -1)
        }
    },
    created () {
    }
}
</script>
<style scoped>
.tenderAdmin{
    margin-bottom: 10px;
}
</style>
