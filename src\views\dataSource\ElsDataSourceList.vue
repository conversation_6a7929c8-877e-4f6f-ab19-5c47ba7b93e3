<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <ElsDataSourceModal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="modalForm"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import ElsDataSourceModal from './modules/ElsDataSourceModal'
import {listPageMixin} from '@comp/template/listPageMixin'
import {getLangAccount, srmI18n} from '@/utils/util'

export default {
    mixins: [listPageMixin],
    components: {
        ElsDataSourceModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: srmI18n(`${getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseEnterCodeDescription`, '请输入编码或描述')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        primary: true
                    }
                ]
            },
            url: {
                list: '/report/dataSource/elsReportChartDataSource/list',
                delete: '/report/dashboard/chartData/delete',
                columns: 'elsReportChartDataSource'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(srmI18n(`${getLangAccount()}#i18n_title_chartData`, '图表数据'))
        }
    }
}
</script>