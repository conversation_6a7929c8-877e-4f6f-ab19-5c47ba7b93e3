<template>
  <div>
    <titleTrtl>
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBsuVMTI_3b175f34`, '评标专家抽取条件') }}</span>
      <template
        slot="right"
        v-if="pageStatus == 'edit'">
        <a-button
          type="primary"
          size="small"
          style="margin-right: 10px"
          @click="gridAdd">{{ $srmI18n(`${$getLangAccount()}#i18n_field_SuVMTI_670bebb3`, '添加抽取条件') }}</a-button>
        <a-button
          type="primary"
          size="small"
          @click="del">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
      </template>
    </titleTrtl>
    <listTable
      ref="listTable"
      :pageData="pageData"
      :pageStatus="pageStatus"
      :fromSourceData="fromSourceData.juryConditionList"
      :edit-config="{trigger: 'click', mode: 'cell', activeMethod: activeRowMethod}"
      :statictableColumns="statictableColumns"
      :checkedConfig="checkedConfig"
      :showTablePage="false" />
    <!-- 抽取轮次展示组件 -->
    <drawRounds
      ref="drawround"
      v-if="fromSourceData['juryMemberRecordList']" 
      :pageStatus="pageStatus"
      :root="root"
      :juryConditionList="fromSourceData.juryConditionList"
      :juryMemberRecordList="fromSourceData.juryMemberRecordList"/>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>
<script lang="jsx">
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import drawRounds from './drawRounds'
import {areas} from '@/store/area'
import { expertsCategory } from '@/store/expertsCategory'
import MTreeSelect from '@comp/treeSelect/mTreeSelect'
export default {
    mixins: [baseMixins],
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        currentRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        fromSourceData: {
            type: Object,
            default: () => {
                return {}
            }
        },
        root: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    computed: {
    },
    components: {
        fieldSelectModal,
        listTable,
        titleTrtl,
        drawRounds,
        MTreeSelect
    },
    watch: {
      
    },
    data () {
        return {
            pageData: {
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VM_c48b9`, '抽取'), clickFn: this.handleExtract }

                ]
            },
            checkedConfig: {
                checkMethod: ({ row }) => {
                    // 存在id不能勾选
                    if (row.id) return false
                    return true
                }
            },
            statictableColumns: [
                {
                    type: 'checkbox',
                    width: 50
                },
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                // {
                //     title: '条件名称',
                //     field: 'conditionName',
                //     headerAlign: 'center',
                //     fieldType: 'input',
                //     width: 150,
                //     required: '0'
                // },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_wjsuG_e4770345`, '来源专家库'),
                    field: 'sourceLibrary',
                    headerAlign: 'center',
                    fieldType: 'select',
                    dictCode: 'srmSpecialistLibrary',
                    optionsFilterValue: ['0'],
                    width: 150,
                    required: '1',
                    enabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistLevel`, '专家级别'),
                    field: 'specialistLevel',
                    headerAlign: 'center',
                    defaultValue: '',
                    fieldType: 'select',
                    dictCode: 'srmSpecialistLevel',
                    width: 150,
                    required: '0',
                    enabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expertType`, '专家类型'),
                    field: 'specialistType',
                    headerAlign: 'center',
                    defaultValue: '',
                    fieldType: 'select',
                    dictCode: 'srmSpecialistType',
                    width: 150,
                    required: '0',
                    enabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suAq_24e54053`, '专家类别'),
                    field: 'specialistClasses',
                    headerAlign: 'center',
                    defaultValue: '',
                    fieldType: 'select',
                    dictCode: 'srmSpecialistClasses',
                    width: 150,
                    required: '0',
                    enabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SLzA_3589d785`, '物料分类'),
                    field: 'cateCode',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 150,
                    fieldType: 'selectModal',
                    special: true,
                    // dictCode: 'srmCateStatus',
                    required: '0',
                    extend: {
                        modalUrl: '/material/purchaseMaterialCode/list',
                        selectModel: 'single',
                        modalParams: {cateStatus: 1},
                        modalColumns: [{
                            field: 'cateCode',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编码'),
                            fieldLabelI18nKey: 'i18n_field_cateCode',
                            with: 150
                        }, {
                            field: 'cateName',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SLzARL_fa7cb1c8`, '物料分类名称'),
                            fieldLabelI18nKey: 'i18n_field_cateName',
                            with: 150
                        }]
                    },
                    bindFunction: function (value, item, fields, that ) {
                        value.cateCode = item[0].cateCode
                        value.cateName = item[0].cateName
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_susE_24df9daa`, '专家专业'),
                    field: 'specializedCode',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 180,
                    slots: {
                        default: ({row}) => {
                            console.log('!!row.id', !!row.id)
                            return [
                                <m-tree-select
                                    disabled={!!row.id}
                                    v-model={row.specializedCode}
                                    allowClear
                                    parentNodeSelectable={true}
                                    multiple={true}
                                    maxTagCount={1}
                                    sourceData={expertsCategory}
                                    treeDefaultExpandAll={false}
                                    treeNodeFilterProp='title'
                                    on-change={(value, label, extra)=> this.hand3(value, label, extra, row)}
                                />
                            ]
                        }
                    },
                    enabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sudWnU_74db2fd0`, '专家所属地域'),
                    field: 'addressCode',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 240,
                    required: '0',
                    slots: {
                        default: ({row}) => {
                            return [
                                <a-cascader options={areas} value={row.addressCode || []}
                                    disabled={!!row.id}
                                    placeholder={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')}
                                    on-change={(val, selectedOptions) => this.onChangeArea(val, selectedOptions, row, areas)}
                                />
                            ]
                        }
                    },
                    enabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VMLW_2e26ec6f`, '抽取人数'),
                    field: 'extractNumber',
                    headerAlign: 'center',
                    defaultValue: '',
                    fieldType: 'number',
                    width: 150,
                    required: '1',
                    fixed: 'right',
                    enabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    fixed: 'right',
                    field: 'ctrl',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 150,
                    slots: { default: 'grid_opration' }
                }
            ],
            url: {
                specialist: '/tender/jury/purchaseTenderProjectJuryHead/extract/specialist'
            },
            show: false
        }
    },
    methods: {
        hand3 (value, label, extra, row){
            row.specializedName = label.triggerNode.title
            // row.specializedCode = row.specialized

            console.log(value, label, extra, row)
        },
        activeRowMethod ({row, rowIndex, column, columnIndex}) {
            if((row.id ?? '') == ''){
                // 行id为空时才可编辑
                return true
            }else{
                return column.field == 'extractNumber'
            }
        },
        getTableData () {
            return this.$refs['listTable'].getTableData()
        },
        getValidate () {
            return this.$refs['listTable'].getValidate()
        },
        gridAdd () {
            this.$refs.listTable.insertAt({}, -1)
        },
        fieldSelectOk (data) {
            let userList = data.map((item) => {
                delete item.id
                return {
                    ...item,
                    memberType: '1',
                    elsAccount: item.elsAccount,
                    elsSubAccount: item.subAccount,
                    elsRealname: item.name,
                    certificateType: item.certificateType,
                    phone: item.mobileTelephone
                }
            })
            this.$refs.listTable.insertAt(userList, -1)
        },
        del () {
            this.$refs['listTable'].businessGridDelete()
        },
        handleExtract (row) {
            this.root.getAllValidate(['juryMember']).then((validate) => {
                if (validate) {
                    let param = this.root.getParams()
                    // let juryConditionList = JSON.parse(JSON.stringify(this.$refs.listTable.getTableData()))
                    // 深拷贝
                    let currentRow = JSON.parse(JSON.stringify(row))
                    // 当前行数据
                    if(currentRow.addressCode && currentRow.addressCode.constructor == Array){
                        console.log(currentRow.addressCode)
                        currentRow.addressCode = currentRow.addressCode[0] ? currentRow.addressCode.join(',') : ''
                    }
                    
                    if(currentRow.sourceLibrary == null){
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_wjsuGxOLV_2472dcd5`, '来源专家库不能为空'))
                        return
                    }
                    if(currentRow.extractNumber == null){
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VMLWxOLV_d45620ff`, '抽取人数不能为空'))
                        return
                    }
                    this.$emit('setLoading', true)
                    // 抽取时只传入当前行
                    param.juryConditionList=[currentRow]
                    postAction(this.url.specialist, param).then(res => {
                        if (res.success) {
                            if(res.result.length != 0){
                                // 成功后刷新页面并回调
                                this.root.queryDetail(() => {
                                    setTimeout(() => {
                                        this.root.setCurrentStep(1)
                                    }, 500)
                                })
                                
                            }else{
                                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjzEuBnjsu_e4ffa7f0`, '没有匹配到符合的专家！'))
                            }
                            
                        }else{
                            this.$message.error(res.message)
                        }
                    }).finally(() => {
                        this.$emit('setLoading', false)
                    })
                }
            })
        },
        onChangeArea (val, selectedOptions, row, areas) {
            console.log('areas', val, selectedOptions, row, areas)
            row.addressCode = val ? [val[0], val[1], val[2]] : []
            let addressName = selectedOptions ? [selectedOptions[0].label, selectedOptions[1].label, selectedOptions[2].label] : []
            if(addressName.length != 0){
                row.addressName = addressName.join(',')
            }
        }
    }
}
</script>
