<template>
  <div class="els-page-comtainer"> 
    <tabs-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url" 
      @goBack="goBack"/>  
  </div>
</template>

<script>
import { editPageMixin } from '@comp/template/tabsCollapse/tabsCollapseMixin'
import { httpAction } from '@/api/manage'
export default {
    name: 'CustomConfigModal',    
    mixins: [editPageMixin],
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_configure`, '配置'),
            confirmLoading: false,
            pageData: {
                dictList: [],
                publicBtn: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_generateConfig`, '生成配置'), type: 'primary', clickFn: this.generatorConfig},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', clickFn: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack }
                ],
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_baseInfo`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            form: {
                                bussModuleKey: '',
                                classCode: '',
                                className: '', 
                                remark: '',
                                version: '1',
                                attachmentList: '1'
                            },
                            list: [
                                {
                                    type: 'collapse',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_configurationInfo`, '配置信息'),
                                    formList: [
                                        {
                                            type: 'select',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessModule`, '业务模块'),
                                            fieldName: 'bussModuleKey',
                                            dictCode: 'isrmBussModuleKey',
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectBusinessModule`, '请选择业务模块')
                                        },
                                        {
                                            fieldName: 'classCode',
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_typeCode`, '类型编码'),
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTypeCodeTips`, '请输入英文字符的类型编码')
                                        },
                                        {
                                            fieldName: 'className',
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_typeName`, '类型名称'),
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTypeName`, '请输入类型名称')
                                        },
                                        {
                                            fieldName: 'attachmentList',
                                            type: 'switch',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentList`, '附件列表'),
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTypeName`, '请输入类型名称'),
                                            closeValue: '0',
                                            openValue: '1'
                                        },
                                        {
                                            type: 'textarea',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remarkDesc`, '配置说明'),
                                            fieldName: 'remark',
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remarkDescMsg`, '请输入配置说明')
                                        }    
                                    ]
                                }
                            ],
                            validRules: {
                                bussModuleKey: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectBusinessModule`, '请选择业务模块!') }
                                ],
                                classCode: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTypeCodeTips`, '请输入英文字符的类型编码!') }
                                ],
                                className: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTypeName`, '请输入类型名称!') }
                                ]
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_headConfiguration`, '抬头配置'),
                        content: {
                            type: 'table',
                            ref: 'customPageConfigList',
                            columns: [{ 
                                type: 'checkbox', width: 40 
                            },                      
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_domId`, '元素ID'),
                                field: 'domId',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_domName`, '元素名称'),
                                field: 'domDesc',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },     
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_domType`, '元素类型'),
                                field: 'domType',
                                width: 120,
                                editRender: {name: 'ASelect',  options: [ 
                                    {value: 'input', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_input`, '输入框')},
                                    {value: 'select', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pullDownList`, '下拉列表')},
                                    {value: 'datepicker', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_date`, '日期')},
                                    {value: 'dateRange', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dateRange`, '日期区间')},
                                    {value: 'timePicker', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_timePicker`, '时间选择')},
                                    {value: 'number', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_number`, '数字')},
                                    {value: 'numberGroup', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_numberGroup`, '数字组')},
                                    {value: 'selectModal', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_popupChoose`, '弹窗选择')},
                                    {value: 'addressCascader', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addressCascader`, '地址选择')},
                                    {value: 'switch', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_switch`, '开关')},
                                    {value: 'textarea', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_textField`, '文本域')},
                                    {value: 'ladderPrice', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yDum_45db271e`, '阶梯价格')}
                                ]}
                            },                     
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sort`, '排序'),
                                field: 'sortOrder',
                                width: 120,
                                editRender: {name: 'AInputNumber'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseCanBeViewed`, '采购可查看'),
                                field: 'purchaseShow',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEditable`, '采购可编辑'),
                                field: 'purchaseEdit',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saleShow`, '销售可查看'),
                                field: 'saleShow',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saleEdit`, '销售可编辑'),
                                field: 'saleEdit',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extendedField`, '扩展字段'),
                                field: 'ext',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            },  
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'),
                                field: 'required',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bindFunction`, '绑定函数'),
                                field: 'bindFunction',
                                width: 120,
                                editRender: {name: 'AInput'}
                            },                          
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataFormat`, '数据格式'),
                                field: 'dataFormat',
                                width: 150,
                                editRender: {name: 'AInput'}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataInitialization`, '数据初始化'),
                                field: 'initData',
                                width: 150,
                                editRender: {name: 'AInput'}

                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_initPage`, '页面初始化'),
                                field: 'initPage',
                                width: 150,
                                editRender: {name: 'AInput'}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extend`, '拓展'),
                                field: 'extendfields',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pageTips`, '页面提示语'),
                                field: 'fbk2',
                                width: 150,
                                editRender: {name: 'AInput'}
                            } 
                            ],
                            validRules: {      
                                domId: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDomId`, '请输入元素ID!') }
                                ], 
                                domType: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDomType`, '请输入元素类型!') }
                                ], 
                                domDesc: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDomName`, '请输入元素名称!') }
                                ]                            
                            }
                        },
                        button: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), type: 'primary', clickFn: this.addPageRow },
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deletePageRow }
                        ]
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_btnConfig`, '按钮配置'),
                        content: {
                            type: 'table',
                            ref: 'buttonConfigList',
                            columns: [{ 
                                type: 'checkbox', width: 40 
                            },                      
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_domId`, '元素ID'),
                                field: 'domId',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_domName`, '元素名称'),
                                field: 'domDesc',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },     
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_domType`, '元素类型'),
                                field: 'domType',
                                width: 120,
                                editRender: {name: 'ASelect',  options: [
                                    {value: 'public', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publicBbtn`, '公共按钮')},
                                    {value: 'line', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineBbtn`, '行项目按钮')}  
                                ]}
                            },                     
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sort`, '排序'),
                                field: 'sortOrder',
                                width: 120,
                                editRender: {name: 'AInputNumber'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseCanBeViewed`, '采购可查看'),
                                field: 'purchaseShow',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            },
                           
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saleShow`, '销售可查看'),
                                field: 'saleShow',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            },                          
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bindFunction`, '绑定函数'),
                                field: 'bindFunction',
                                width: 120,
                                editRender: {name: 'AInput'}
                            },                          
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataFormat`, '数据格式'),
                                field: 'dataFormat',
                                width: 150,
                                editRender: {name: 'AInput'}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataInitialization`, '数据初始化'),
                                field: 'initData',
                                width: 150,
                                editRender: {name: 'AInput'}

                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_initPage`, '页面初始化'),
                                field: 'initPage',
                                width: 150,
                                editRender: {name: 'AInput'}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extend`, '拓展'),
                                field: 'extendfields',
                                width: 150,
                                editRender: {name: 'AInput'}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_adderssUrl`, '地址URL'),
                                field: 'fbk1',
                                width: 150,
                                editRender: {name: 'AInput'}
                            }
                            ],
                            validRules: {      
                                domId: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDomId`, '请输入元素ID!') }
                                ], 
                                domType: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDomType`, '请输入元素类型!') }
                                ], 
                                domDesc: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDomName`, '请输入元素名称!') }
                                ],
                                bindFunction: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterFunction`, '请输入函数!') }
                                ]                             
                            }
                        },
                        button: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), type: 'primary', clickFn: this.addButtonRow },
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteButtonRow }
                        ]
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fieldConfig`, '字段配置'),
                        content: {
                            type: 'table',
                            ref: 'customColumnConfigList',
                            columns: [{ 
                                type: 'checkbox', width: 40 
                            },                     
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title__columnCoding`, '列编码'),
                                field: 'columnCode',
                                width: 120,
                                editRender: {name: 'AInput'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title__columnName`, '列名称'),
                                field: 'columnName',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },   
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_columnWidth`, '列宽'),
                                field: 'columnWidth',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },   
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_alignType`, '对齐方式'),
                                field: 'columnAlign',
                                width: 150,
                                editRender: {name: 'ASelect',  props: {options: [
                                    {value: 'center', text: 'center', title: 'center'},
                                    {value: 'left', text: 'left', title: 'left'},
                                    {value: 'right', text: 'right', title: 'right'}
                                ]}}
                              
                            },                    
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sort`, '排序'),
                                field: 'sortOrder',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extend`, '拓展'),
                                field: 'extendfields',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseCanBeViewed`, '采购可查看'),
                                field: 'purchaseShow',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEditable`, '采购可编辑'),
                                field: 'purchaseEdit',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saleShow`, '销售可查看'),
                                field: 'saleShow',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saleEdit`, '销售可编辑'),
                                field: 'saleEdit',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extendedField`, '扩展字段'),
                                field: 'ext',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            },  
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'),
                                field: 'required',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hidden`, '是否隐藏'),
                                field: 'hidden',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            }, 
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isNeedColumnOrder`, '是否可排序'),
                                field: 'canSort',
                                width: 120,
                                editRender: {name: 'mSwitch', type: 'visible', props: {closeValue: '0', openValue: '1'}}
                            },  
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_renderer`, '渲染方法'),
                                field: 'renderer',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zdyRenderer`, '自定义渲染方法'),
                                field: 'zdyRenderer',
                                width: 150,
                                editRender: {name: 'AInput'}
                            }
                            ],
                            validRules: {      
                                columnCode: [ 
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title__pleaseEnterColumnCoding`, '请输入列编码') }
                                ], 
                                columnName: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title__pleaseEnterColumnName`, '请输入列名称') }
                                ]                      
                            }
                        },
                        button: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), type: 'primary', clickFn: this.addColumRow},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteColumRow}
                        ]
                    }
                ]
            },
            url: {
                add: '/config/customConfig/add',
                edit: '/config/customConfig/edit',
                detail: '/config/customConfig/queryDetailById',
                generatorConfig: '/config/customConfig/generatorConfig'
            },
            selectRow: {}
        }
    },
    mounted () {
       
    },
    created (){
      
    },
    methods: {
        goBack () {
            this.$emit('hide')
        },      
        addPageRow (){
            let addTableData = []
            let customPageConfigListGrid = this.$refs.editPage.$refs.customPageConfigList[0]
            addTableData.push({
                domId: '',
                domDesc: '',
                domType: '',
                fbk1: '',
                purchaseShow: '1',
                purchaseEdit: '1',
                saleShow: '1',
                saleEdit: '1',
                ext: '0',
                required: '-',
                bindFunction: '',
                dataFormat: '',
                initData: '',
                initPage: ''
            })          
            customPageConfigListGrid.insertAt(addTableData, -1)
        },

        deletePageRow () {
            this.$refs.editPage.deleteRow()
        },
        
        addButtonRow (){
            let addTableData = []
            let buttonConfigListGrid = this.$refs.editPage.$refs.buttonConfigList[0]
            addTableData.push({
                domId: '',
                domDesc: '',
                domType: 'public',
                fbk1: '',
                purchaseShow: '1',
                purchaseEdit: '1',
                saleShow: '1',
                saleEdit: '1',
                ext: '0',
                required: '',
                bindFunction: '',
                dataFormat: '',
                initData: '',
                initPage: ''
            })          
            buttonConfigListGrid.insertAt(addTableData, -1)
        },

        deleteButtonRow () {
            this.$refs.editPage.deleteRow()
        },
        addColumRow (){
            let addTableData = []
            let customColumnConfigListGrid = this.$refs.editPage.$refs.customColumnConfigList[0]
            addTableData.push({
                columnCode: '',
                columnName: '',
                dataType: '',
                columnWidth: '',
                columnAlign: '',
                dataFormat: '',
                sortOrder: '',
                purchaseShow: '1',
                purchaseEdit: '1',
                saleShow: '1',
                saleEdit: '1',
                ext: '0',
                required: '0',
                hidden: '0',
                canSort: '0',
                renderer: '',
                zdyRenderer: '',
                extendfields: ''
            })          
            customColumnConfigListGrid.insertAt(addTableData, -1)
        },

        deleteColumRow () {
            this.$refs.editPage.deleteRow()
        },
        generatorConfig (){
            let param = this.$refs.editPage.getParamsData()
            if(param.customPageConfigList.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
                return false
            }
            this.$refs.editPage.$refs.baseForm[0].validate(valid => {
                this.$refs.editPage.$refs.customPageConfigList[0].validate(err => {
                    if(valid && !err) {
                        this.postData()
                    }
                })
            })
        },
        postData (){
            let params = this.$refs.editPage.getParamsData()
            this.confirmLoading = true
            httpAction(this.url.generatorConfig, params, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$emit('ok')
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
       
    }
}
</script>
