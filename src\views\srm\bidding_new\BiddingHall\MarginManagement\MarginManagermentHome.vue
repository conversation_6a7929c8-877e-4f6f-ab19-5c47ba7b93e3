<template>
  <div class="margin-managerment-home">
    <div
      class="main"
      v-if="!showRefundAddPage">
      <ContentHeader :btns="btns"></ContentHeader>
      <div class="content">
        <div
          class="content-tip"
          v-for="(item, index) in contentList"
          :key="index">
          <span>{{ item.title }}</span>
          <span>{{ item.value }}</span>
        </div>
      </div>
      <div class="title">
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBLAB_9daed819`, '投标人列表') }}</span>
        <span style="color: red; font-size: 12px; margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_field_dWKjsWsxCKJpjsiHxtNIJsiHkUfnsiHkUf_4b2b27de`, '注：使用保险保函方式缴纳的保证金不计入已缴保证金总余额和保证金总余额') }}</span>
      </div>

      <!-- :span-method="mergeRowMethod" :merge-cells="mergeCells" -->
      <div class="grid">
        <vxe-grid
          ref="marginGrid"
          :scroll-y="{enabled: false}"
          :merge-cells="mergeCells"
          v-bind="gridConfig"
          :columns="tableColumns"
          :data="tableData"
          :height="gridHeight"
          :loading="loading">

          <template #default_amount="{row, column}">
            <span v-if="row.marginCollectionType == '2'">——</span>
            <span v-else>{{ row[column['property']] }}</span>
          </template>
          <template #grid_opration="{row}">
            <a-button
              type="link"
              size="small"
              @click="handleRefund(row)"
              :disabled="showRefund(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_field_YV_11db3e`, '退款') }}</a-button>
            <a-button
              type="link"
              size="small"
              @click="handleConfirm(row)"
              :disabled="showConfirm(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确认') }}</a-button>
            <a-button
              type="link"
              size="small"
              @click="handleCall(row)"
              :disabled="showCall(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_field_HJ_a4408`, '催缴') }}</a-button>
          </template>
        </vxe-grid>
      </div>
    </div>
    <RefundAdd
      v-if="showRefundAddPage"
      :current-edit-row="currentEditRow" />
  </div>
</template>

<script lang="jsx">
import ContentHeader from '../components/content-header'
import RefundAdd from '../RefundRecord/RefundAdd'
import ListTable from '../components/listTable'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'

export default {
    components: {
        ContentHeader,
        ListTable,
        RefundAdd
    },
    mixins: [tableMixins],
    inject: ['tenderCurrentRow', 'subpackageId'],
    data () {
        return {
            setGridHeight: 300,
            loading: false,
            showRefundAddPage: false,
            contentList: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LJpsiHtLWR_87f94c7b`, '未缴纳保证金单位数量'),
                    code: 'unpaidNumber',
                    value: '0'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IJpsiHtLWR_82ee76b3`, '已缴纳保证金单位数量'),
                    code: 'paidNumber',
                    value: '0'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_oYVsiHtLWR_4fc60061`, '待退款保证金单位数量'),
                    code: 'toRefundNumber',
                    value: '0'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IYVsiHtLWR_c028ec94`, '已退款保证金单位数量'),
                    code: 'refundedNumber',
                    value: '0'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IJsiHkHfWjW_b2087732`, '已缴保证金总金额（元）'),
                    code: 'paidTotalAmount',
                    value: '0.00'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IYsiHkHfWjW_f3a08be6`, '已退保证金总金额（元）'),
                    code: 'refundedAmount',
                    value: '0.00'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHkUfWjW_2633bdfc`, '保证金总余额（元）'),
                    code: 'totalBalance',
                    value: '0.00'
                }
            ],
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JptHmA_4a914eaa`, '缴纳记录查看'), type: 'primary', click: () => {this.viewRecords('payment')}},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVtHmA_e2088c69`, '退款记录查看'), type: 'primary', click: () => {this.viewRecords('refund')}},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nVtHmA_1b2eff79`, '抵扣记录查看'), type: 'primary', click: () => {this.viewRecords('Deduction')}},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HJtHmA_eaab8033`, '催缴记录查看'), type: 'primary', click: () => {this.viewRecords('call')}}
            ],
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBLRL_9daf066b`, '投标人名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHJpCK_748c2f02`, '保证金缴纳方式'),
                    'field': 'marginCollectionType',
                    slots: {
                        default: ({row, column}) => {
                            let span = <span>——</span>
                            if (row.marginCollectionType == '2') {
                                span = <span>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sWsx_268c64ec`, '保险保函')}</span>
                            }
                            if (row.marginCollectionType == '1') {
                                span = <span>{this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_AvCK_263c5496`, '其他方式')}</span>
                            }
                            return [
                                span
                            ]
                        }
                    }
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dJHfWjW_7f083ba2`, '应缴金额（元）'),
                    'field': 'dueAmount'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JpHfWjW_7a343c23`, '缴纳金额（元）'),
                    'field': 'paidAmount',
                    slots: { default: 'default_amount'}
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_oJHfWjW_4bc603d3`, '待缴金额（元）'),
                    'field': 'toPayAmount',
                    slots: { default: 'default_amount'}
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IYHfWjW_aefd4b34`, '已退金额（元）'),
                    'field': 'refundAmount',
                    slots: { default: 'default_amount'}
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UfWjW_a74ef44a`, '余额（元）'),
                    'field': 'availableAmount',
                    slots: { default: 'default_amount'}
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLVcWIRLWkWW_88581feb`, '确认情况（已确认/总数）'),
                    'field': 'confirmNumber_payNumber',
                    slots: {
                        default: ({row, column}) => {
                            return [
                                <span>{row.confirmNumber}/{row.payNumber}</span>
                            ]
                        }
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 160,
                    slots: { default: 'grid_opration' }
                }
            ],
            tableData: [],
            mergeCells: [],
            url: {
                list: '/tender/supplier/purchaseTenderProjectMarginHead/queryList'
            },
            currentEditRow: {}
        }
    },
    computed: {
        subId () {
            return this.subpackageId()
            // return '1532190061912612866'
        },
        gridHeight () {
            let height = '100'
            const clientHeight = document.documentElement.clientHeight
            height = clientHeight - 260
            // 自定义设置表行高
            if (this.setGridHeight) {
                height = this.setGridHeight
            }
            return height + 'px'
        }
    },
    mounted () {
        this.getqueryList()
        this.getNumberPrice()
    },
    methods: {
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            // debugger
            const fields = ['supplierName', 'dueAmount', 'status_dictText']
            const cellValue = row[column.property]
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow[column.property] === cellValue) {
                    return { rowspan: 0, colspan: 0 }
                } else {
                    let countRowspan = 1
                    while (nextRow && nextRow[column.property] === cellValue) {
                        nextRow = visibleData[++countRowspan + _rowIndex]
                    }
                    if (countRowspan > 1) {
                        console.log(countRowspan)
                        return { rowspan: countRowspan, colspan: 1 }
                    }
                }
            }
        },
        getqueryList () { // 获取数据内容
            this.loading = true
            getAction(this.url.list, {subpackageId: this.subId}).then(res => {
                const {code, result} = res
                if (code === 200 && result) {
                    // this.tableData = result ? result : []
                    let data = []
                    result.forEach(item => {
                        if (item.marginList && item.marginList.length > 0) {
                            item.marginList.forEach(margin => {
                                let mg = Object.assign({}, item, margin)
                                data.push(mg)
                            })
                        } else {
                            data.push(item)
                        }
                    })
                    console.log(data)
                    this.tableData = [...data]
                    this.getMergeCells(data)
                }

            }).finally(() => {
                this.loading = false
            })
        },
        getMergeCells (data) { // 计算合并的单元格参数
            const fields = ['supplierAccount', 'dueAmount', 'status_dictText']
            const columns = this.tableColumns
            let cells = []
            console.log(data)
            const columnFn = (columnName) => {
                let col = null
                columns.forEach((item, index) => {
                    if (item.field == columnName) {
                        col = index
                    }
                })
                return col
            }
            const rowspan = (columnName, columnName2, currentAccount) => {
                let rowSpan = 1
                data.forEach((item, index) => {
                    if (data.length - 1 > index) { // 不是数组的最后一个元素
                        const nxetRow = data[index+1]
                        if (item[columnName] == nxetRow[columnName] && item[columnName2] == nxetRow[columnName2] && item['supplierAccount'] == currentAccount) {
                            rowSpan += 1
                        }
                    }
                    if (data.length -1 == index) { // 数组的最后一个元素
                        const prevRow = data[data.length - 2]
                        if (item[columnName] == prevRow[columnName] && item[columnName2] == prevRow[columnName2] && item['supplierAccount'] == currentAccount) {
                            rowSpan += 1
                        }
                    }
                })
                return rowSpan
            }
            const dataFu = (data) => {
                data.forEach((da, index) => {
                    // let currentLength = cells.length
                    if (data.length-1> index) { // 判断数组里面还有最后一个值
                        const nxetRow = data[index+1] //获取下一个
                        if (da['supplierAccount'] == nxetRow['supplierAccount']) {
                            cells.push({row: index, col: columnFn('supplierName'), rowspan: rowspan('supplierName', 'supplierAccount', da['supplierAccount']), colspan: 1})
                        }
                        if (da['supplierAccount'] == nxetRow['supplierAccount'] && da['dueAmount'] == nxetRow['dueAmount']) {
                            cells.push({row: index, col: columnFn('dueAmount'), rowspan: rowspan('supplierAccount', 'dueAmount', da['supplierAccount']), colspan: 1})
                        }
                        if (da['supplierAccount'] == nxetRow['supplierAccount'] && da['status_dictText'] == nxetRow['status_dictText']) {
                            cells.push({row: index, col: columnFn('status_dictText'), rowspan: rowspan('supplierAccount', 'status_dictText', da['supplierAccount']), colspan: 1})
                        }
                    }
                    if (data.length - 1 == index) { // 数组的最后一个元素
                        const prevRow = data[data.length - 2] //获取上一个
                        if (da['supplierAccount'] == prevRow['supplierAccount']) {
                            cells.push({row: index, col: columnFn('supplierName'), rowspan: rowspan('supplierName', 'supplierAccount', da['supplierAccount']), colspan: 1})
                        }
                        if (da['supplierAccount'] == prevRow['supplierAccount'] && da['dueAmount'] == prevRow['dueAmount']) {
                            cells.push({row: index, col: columnFn('dueAmount'), rowspan: rowspan('supplierAccount', 'dueAmount', da['supplierAccount']), colspan: 1})
                        }
                        if (da['supplierAccount'] == prevRow['supplierAccount'] && da['status_dictText'] == prevRow['status_dictText']) {
                            cells.push({row: index, col: columnFn('status_dictText'), rowspan: rowspan('supplierAccount', 'status_dictText', da['supplierAccount']), colspan: 1})
                        }
                    }
                    // 如果当前没匹配上，默认新增一行
                    // if(currentLength == cells.length){
                    //     cells.push({row: index, col: 1, rowspan: 1, colspan: 1})
                    // }
                })
            }
            data && dataFu(data)
            this.mergeCells = cells
            // this.mergeCells = [
            //   { row: 2, col: 1, rowspan: 2, colspan: 1 },
            //   { row: 2, col: 3, rowspan: 2, colspan: 1 },
            //   { row: 2, col: 8, rowspan: 2, colspan: 1 },
            //   { row: 3, col: 1, rowspan: 2, colspan: 1 },
            //   { row: 3, col: 3, rowspan: 2, colspan: 1 },
            //   { row: 3, col: 8, rowspan: 2, colspan: 1 }]
        },
        getNumberPrice () { // 获取数量和金额
            getAction('/tender/supplier/purchaseTenderProjectMarginHead/queryStat', {subpackageId: this.subId}).then(res => {
                if (res.code == 200 && res.result) {
                    const result = res.result
                    this.contentList.forEach(item => {
                        item['value'] = result[item.code]
                    })
                }
            })
        },
        viewRecords (type) {
            console.log(type, '记录查看')
            let url = ''
            switch (type) {
            case 'payment': // 缴纳记录
                url = '/biddingHall/PaymentRecordsView/PaymentRecordsView'
                break
            case 'refund': // 退款记录
                url = '/biddingHall/RefundRecord/RefundRecordView'
                break
            case 'Deduction': // 退款记录
                url = '/biddingHall/Deduction/DeductionView'
                break
            default: // 催缴记录
                url = '/biddingHall/DunningRecordsView/DunningRecordsView'
                break
            }
            this.$router.push({path: url})
            console.log(type, '记录查看')
        },
        showRefund (row) {
            const {status, marginCollectionType} = row
            return status == 2 && marginCollectionType != '2' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1' ? false : true
        },
        showConfirm (row) {
            const {confirmNumber, payNumber} = row
            if (confirmNumber == null || payNumber == null) {
                return true
            }
            return confirmNumber == payNumber ? true : false
        },
        showCall (row) {
            const {status} = row
            return status == 0 && this.$ls.get('SET_TENDERCURRENTROW').applyRole ? false : true
        },
        handleRefund (row) { // 退款操作
            console.log(row)
            this.currentEditRow = row
            this.showRefundAddPage = true
        },
        handleConfirm (row) { // 确认
            this.$router.push({path: '/biddingHall/MarginManagementConfirm/MarginManagementConfirmView'})
        },
        handleCall (row) { // 催缴
            console.log(row)
            const that = this
            this.$confirm({
                title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_warmPrompt`, '温馨提示'),
                content: that.$srmI18n(`${that.$getLangAccount()}#i18n_field_KQRIHVW_d5c0434c`, '是否确定催款！'),
                onOk () {
                    return new Promise((resolve, reject) => {
                        const params = {
                            tenderProjectId: row.tenderProjectId || '',
                            subpackageId: row.subpackageId || '',
                            supplierAccount: row.supplierAccount || '',
                            supplierName: row.supplierName || ''
                        }
                        postAction('/tender/supplier/tenderProjectRemindMessage/add', params).then(res => {
                            const type = res.success ? 'success' : 'error'
                            that.$message[type](res.message)
                            resolve(false)
                        })
                    })
                },
                onCancel () {}
            })
        }
    }
}
</script>

<style lang="less" scoped>
.margin-managerment-home {
  background-color: #fff;
  height: 100%;

  .content {
    padding: 0 10px 10px;

    .content-tip {
      display: inline-block;
      border: 1px solid #ddd;
      border-radius: 10px;
      padding: 10px;
      text-align: center;
      // width: 200px;
      height: 80px;
      line-height: 30px;
      margin-right: 20px;
      margin-top: 10px;

      span {
        display: block;
        font-size: 16px;
        font-weight: 600;
      }

    }
  }

  .title {
    background: #ddd;
    padding: 10px;
    font-weight: 600;
  }
  .grid {
    margin-top: 10px;
  }

}
</style>