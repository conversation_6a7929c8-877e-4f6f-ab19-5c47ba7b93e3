<template>
  <div style="height: 100%;">
    <!-- :tabsList="tabsList" -->
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTabOpt" />

    <!-- 立即评标 -->
    <bid-evaluation-manager-edit 
      v-if="showEditPage" 
      @hide="hideEditPage"
      :current-edit-row="currentEditRow">
    </bid-evaluation-manager-edit>
    <!-- 查看评标 -->
    <bid-evaluation-manager-detail
      v-if="showDetailPage" 
      @hide="hideEditPage"
      :current-edit-row="currentEditRow"/>
  </div>
</template>

<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import BidEvaluationManagerEdit from './modules/BidEvaluationManagerEdit.vue'
import BidEvaluationManagerDetail from './modules/BidEvaluationManagerDetail.vue'

export default {
    mixins: [ListMixin],
    components: {
        BidEvaluationManagerEdit,
        BidEvaluationManagerDetail
    },
    data () {
        return {
            tabsList: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_all`, '全部'), performanceDeduce: null, status: null },
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBs_21991ca`, '评标中'), performanceDeduce: null, status: '0'},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_UByW_411ef2ef`, '评标结束'), performanceDeduce: null, status: '1'}
            ],
            pageData: {
                businessType: 'tenderProjectBidEvaHeadList',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNzsRLWYBdIAySYBdIRL_63cfed02`, '请输入分包名称、招标项目编号或招标项目名称')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vtUB_391c24cb`, '立即评标'), authorityCode: 'tender#purchaseTenderProjectBidEvaHead:evaluation', clickFn: this.handleEdit, allow: this.editShow},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBmA_411c3bc9`, '评标查看'), authorityCode: 'tender#purchaseTenderProjectBidEvaHead:queryById', clickFn: this.handleView, allow: this.detailShow}
                ]
            },
            url: {
                list: '/tender/evaluation/purchaseTenderProjectBidEvaHead/list',
                columns: 'tenderProjectBidEvaHeadList'
            }
        }
    },
    methods: {
        editShow (row) {
            const {evaStatus = '0'} = row
            return evaStatus == '0' ? false : true
        },
        detailShow (row) {
            const {evaStatus = '0'} = row
            return evaStatus != '0' ? false : true
        },
        hideEditPage () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        }
    }
}
</script>
