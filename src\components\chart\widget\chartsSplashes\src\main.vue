<template>
  <vue-echarts
    :style="style"
    :option="widget.option"
    :update-options="{ notMerge: true }"
    autoresize
  />
</template>
<script>
import { chartsMixins } from '@comp/chart/widget/mixins/chartsMixins'
import _ from 'lodash'
export default {
    name: 'ChartsSplashes',
    mixins: [chartsMixins],
    computed: {
        option () {
            return {
                symbolSize: this.widget.option.series[0].symbolSize,
                itemStyle: this.widget.option.series[0].itemStyle
            }
        }
    },
    watch: {
        option: {
            handler (val) {
                for (let i = 1; i < this.widget.option.series.length; i++) {
                    this.widget.option.series[i].symbolSize = val.symbolSize
                    this.widget.option.series[i].itemStyle = _.cloneDeep(val.itemStyle)
                }
            },
            deep: true
        }
    },
    mounted () {
        this.refreshWidgetInfo()
    },
    methods: {}
}
</script>