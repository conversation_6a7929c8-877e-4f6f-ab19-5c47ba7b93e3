<template>
  <div class="ElsFileCompareHead business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="detail"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :remoteJsFilePath="remoteJsFilePath"
      :requestData="requestData"
      :externalToolBar="externalToolBar"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      :handleAfterDealSource="handleAfterDealSource"
      v-on="businessHandler"/>

    <field-select-modal
      isEmit
      ref="fieldSelectModal"
      @ok="fieldSelectOk"/>

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {BUTTON_BACK} from '@/utils/constant.js'

export default {
    name: 'ViewElsFileCompareHeadModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: {
                    url: '/compare/elsFileCompareHead/queryById', args: (that) => {
                        return {id: that.currentEditRow.id}
                    }
                }
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '预览A文件'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.previewFileA
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '预览B文件'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.previewFileB
                },
                BUTTON_BACK
            ],
            url: {
                save: '/compare/elsFileCompareHead/edit',
                audit: '/a1bpmn/audit/api/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                detail: '/contract/purchaseContractHead/queryById'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_fileCompare_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        preViewEvent (row) {
            this.$previewFile.open({params: {}, path: row.filePath})
        },
        preDowloadEvent (row) {
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = row.filePath
            link.setAttribute('download', row.fileName)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(row.filePath) //释放掉blob对象

        },
        previewFileA () {
            debugger
            let pageData = this.getAllData() || {}
            let preViewFile = {id: pageData.fileASourceId, filePath: pageData.fileAPath}
            this.$previewFile.open({params: preViewFile})
        },
        previewFileB () {
            let pageData = this.getAllData() || {}
            let preViewFile = {id: pageData.fileBSourceId, filePath: pageData.fileBPath}
            this.$previewFile.open({params: preViewFile})
        },
        handleBeforeRemoteConfigData () {
            return {
                itemColumns: [
                    {
                        groupCode: 'elsFileCompareResultList',
                        title: '操作',
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: 'sourceType_dictText',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '75',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#`, '预览')}
                                    onClick={() => this.preViewEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#`, '预览')}</a>)
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}
                                    onClick={() => this.preDowloadEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}</a>)
                                return resultArray
                            }
                        }
                    }
                ]
            }
        }
    }
}
</script>
