<template>
    <div class="page-container">
        <business-layout
            v-if="showRemote"
            :ref="businessRefName"
            :currentEditRow="currentEditRow"
            :requestData="requestData"
            :remoteJsFilePath="remoteJsFilePath"
            :pageHeaderButtons="pageHeaderButtons"
            :handleAfterDealSource="handleAfterDealSource"
            :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
            modelLayout="tab"
            pageStatus="edit"
            v-on="businessHandler"> </business-layout>
        <!-- 加载配置文件 -->
        <flowViewModal
            v-model="flowView"
            :flowId="flowId"
            :currentEditRow="currentEditRow" />
    </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction, getAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

export default {
        name: 'SaleViewPurchaseReconciliationDetail',
        mixins: [businessUtilMixin],
        components: {
            flowViewModal,
            BusinessLayout
        },
        data () {
            return {
                showRemote: false,
                businessRefName: 'businessRef',
                requestData: {
                    detail: {
                        url: '/finance/salePaymentApplyHead/queryById',
                        args: (that) => {
                            return { id: that.currentEditRow.id }
                        }
                    }
                },
                pageHeaderButtons: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                        key: 'goBack'
                    }
                ],
                flowView: false,
                flowId: '',
                url: {
                    detail: '/finance/salePaymentApplyHead/queryById',
                    download: '/attachment/saleAttachment/download',
                }
            }
        },
        computed: {
            remoteJsFilePath () {
                let templateNumber = this.currentEditRow.templateNumber
                let templateVersion = this.currentEditRow.templateVersion
                let busAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
                return `${busAccount}/sale_paymentApply_${templateNumber}_${templateVersion}`
            }
        },
        created () {
            if (this.$route.query && this.$route.query.open && this.$route.query.id) {
                getAction(this.url.detail, { id: this.$route.query.id }).then((res) => {
                    if (res && res.success) {
                        this.currentEditRow.templateNumber = res.result.templateNumber
                        this.currentEditRow.templateVersion = res.result.templateVersion
                        this.currentEditRow.templateAccount = res.result.templateAccount
                        this.showRemote = true
                    }
                })
            } else {
                this.showRemote = true
            }

        },
        methods: {
            handleBeforeRemoteConfigData () {
                return {
                    groups: [
                        {
                            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                            groupNameI18nKey: '',
                            groupCode: 'attachmentList',
                            groupType: 'item',
                            sortOrder: '6',
                            extend: {
                                optColumnList: [
                                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                    { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                                ]
                            }
                        }
                    ],
                    formFields: [],
                    itemColumns: [
                        {
                            groupCode: 'attachmentList',
                            field: 'fileType_dictText',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                            fieldLabelI18nKey: '',
                            width: 200
                        },
                        {
                            groupCode: 'attachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                            fieldLabelI18nKey: '',
                            field: 'fileName',
                            width: 180
                        },
                        {
                            groupCode: 'attachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                            fieldLabelI18nKey: '',
                            field: 'uploadTime',
                            width: 120
                        },
                        {
                            groupCode: 'attachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                            fieldLabelI18nKey: '',
                            field: 'uploadElsAccount_dictText',
                            width: 120
                        },
                        {
                            groupCode: 'attachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                            fieldLabelI18nKey: '',
                            field: 'uploadSubAccount_dictText',
                            width: 120
                        },
                        {
                            groupCode: 'attachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                            fieldLabelI18nKey: '',
                            field: 'grid_opration',
                            width: '100',
                            slots: { default: 'grid_opration' }
                        }
                    ]
                }
            },
            preViewEvent (Vue, row) {
                let preViewFile = row
                this.$previewFile.open({ params: preViewFile })
            },
            beforeHandleData (data) {
                data.formFields.forEach((item) => {
                    if (item.fieldName == 'toElsAccount') {
                        item.fieldLabel = '采购方els账号'
                    }
                })
            },
            handleAfterDealSource (pageConfig, resultData) {
                if(!resultData.paymentApplyOtherList.length) {
                    this.hideSingleGroup(this.businessRefName, 'paymentApplyOtherList', true)
                }
            },
            goBack () {
                this.$emit('hide')
            },
            downloadEvent (Vue, row) {
                if (!row.fileName) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                    return
                }
                this.attachmentDownloadEvent(row)
            },
        }
    }
</script>
