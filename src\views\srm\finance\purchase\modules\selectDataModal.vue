<template>
  <div>
    <a-modal
      v-drag    
      centered
      :title="title"
      :width="960"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="close"
    >
      <a-form-model layout="inline">
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_sourceType`, '来源类型')">
          <m-select
            style="width:200px"
            v-model="suorceType"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectSourceType`, '请选择来源类型')"
            @change="suorceTypeChange"
            dict-code="srmDeductCostItemSourceType" />
        </a-form-model-item>
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_keyword`, '关键字')">
          <a-input
            @keyup.enter.native="onSearch"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')"
            v-model="keyWord"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button
            type="primary"
            @click="onSearch">{{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}</a-button>
        </a-form-model-item>
      </a-form-model>
      <vxe-grid
        border
        resizable
        max-height="350"
        row-id="id"
        size="mini"
        :loading="loading"
        ref="selectGrid"
        :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
        :data="tableData"
        :pager-config="tablePage"
        :radio-config="selectModel === 'single' ? checkedConfig : undefined"
        :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
        :columns="currentColumns"
        @page-change="handlePageChange"
      >
      </vxe-grid>
    </a-modal>
  </div>
</template>
<script>
import {getAction} from '@/api/manage'
import {getLangAccount, srmI18n} from '@/utils/util'

export default {
    props: {
        title: {
            type: String,
            default: srmI18n(`${getLangAccount()}#i18n_title_selectDataMsg`, '选择数据')
        },
        columns: {
            type: Array,
            default: () => [
                { type: 'checkbox', width: 40 },
                { type: 'seq', title: srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号'), width: 60 }
            ]
        },
        selectModel: {
            type: String,
            default: 'multiple'
        }
    },
    data () {
        return {
            suorceType: 'order',
            suorceTypeName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_nRIt_446776b9`, '采购订单'),
            keyWord: '',
            visible: false,
            loading: false,
            confirmLoading: false,
            currentUrl: '/order/purchaseOrderItem/financeQueryOrderItem',
            urlList: {
                order: '/order/purchaseOrderItem/financeQueryOrderItem',
                voucher: '/delivery/purchaseVoucherHead/financeQueryVoucherItem',
                qualityCheck: '/quality/purchaseQualityCheckHead/financeQueryQualityCheck',
                sample: '/sample/purchaseSampleHead/financeQuerySample',
                sampleCheck: '/sample/purchaseSampleCheckHead/financeQuerySampleCheck',
                inspection: '/other/purchaseInspectionHead/financeQuerySample',
                performance: '/performance/purchasePerformanceReportHead/financeQuerySample'
            },
            currentColumns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购订单号'), width: 120 },
                { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 120 },
                { field: 'purchaseOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                { field: 'orderDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderDate`, '订单日期'), width: 120 },
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 100 },
                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), width: 120 },
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '数量'), width: 100 },
                { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 100 },
                { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税单价'), width: 120 },
                { field: 'taxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), width: 120 }
            ],
            columnList: {
                order: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购订单号'), width: 120 },
                    { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 120 },
                    { field: 'purchaseOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                    { field: 'orderDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderDate`, '订单日期'), width: 120 },
                    { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 100 },
                    { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                    { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), width: 120 },
                    { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                    { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '数量'), width: 100 },
                    { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 100 },
                    { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税单价'), width: 120 },
                    { field: 'taxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), width: 120 }
                ],
                voucher: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'voucherNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_voucherNumber`, '凭证单号'), width: 120 },
                    { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdPpapItemListf87c_rowNumber`, '行号'), width: 120 },
                    { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 120 },
                    { field: 'purchaseOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                    { field: 'deliveryNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliveryNumber`, '收/退货单号'), width: 120 },
                    { field: 'deliveryItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliveryItemNumber`, '收/退货行号'), width: 120 },
                    { field: 'voucherDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_voucherDate`, '凭证日期'), width: 120 },
                    { field: 'voucherQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_voucherQuantity`, '凭证数量'), width: 120 },
                    { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'), width: 120 },
                    { field: 'netPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_theNetPrice`, '净价'), width: 120 }
                ],
                qualityCheck: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'checkNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_checkNumber`, '检验单号'), width: 160 },
                    { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 160 },
                    { field: 'factory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'), width: 160 },
                    { field: 'checkStartDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_checkStartDate`, '检验开始日期'), width: 160 },
                    { field: 'checkEndDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_checkEndDate`, '检验结束日期'), width: 160 }
                ],
                sample: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'sampleNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sampleApplicationOrderNumber`, '样品申请单号'), width: 120 },
                    { field: 'sampleType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VNUVAc_b7949cfe`, '样品申请类型'), width: 120 },
                    { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 120 },
                    { field: 'factory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'), width: 120 },
                    { field: 'purchaseOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                    { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                    { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), width: 120 },
                    { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                    { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialSpec`, '物料规格'), width: 120 },
                    { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireDate`, '需求日期'), width: 120 },
                    { field: 'requireQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireQuantity`, '需求数量'), width: 120 },
                    { field: 'repertoryUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_repertoryUnit`, '库存单位'), width: 120 },
                    { field: 'deliveryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliveryDate`, '交货日期'), width: 120 },
                    { field: 'trackingNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_trackingNumber`, '物流单号'), width: 120 },
                    { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税单价'), width: 120 },
                    { field: 'taxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), width: 120 }
                ],
                sampleCheck: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'checkNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_billCode`, '单据编码'), width: 120 },
                    { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 120 },
                    { field: 'factory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'), width: 120 },
                    { field: 'purchaseOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                    { field: 'checkType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IizA_31429100`, '检测分类'), width: 120 },
                    { field: 'principal', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IiBFL_f7efbaeb`, '检测负责人'), width: 120 },
                    { field: 'checkStartDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_testStartDate`, '检测开始日期'), width: 120 },
                    { field: 'checkEndDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_testEndDate`, '检测结束日期'), width: 120 },
                    { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                    { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), width: 120 },
                    { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                    { field: 'checkQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_checkQuantity`, '检验数量'), width: 120 },
                    { field: 'qualified', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_testResult`, '检验结果'), width: 120 }
                ],
                inspection: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'documentsSerialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_billCode`, '单据编码'), width: 120 },
                    { field: 'companyCode_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司'), width: 120 },
                    { field: 'factoryCode_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'), width: 120 },
                    { field: 'purchaseOrgCode_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                    { field: 'inspectionReason_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BmjW_3b94345d`, '考察原因'), width: 120 },
                    { field: 'inspectionStep_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Bmxs_3b97591b`, '考察步骤'), width: 120 },
                    { field: 'materialType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cateCode`, '物料分类'), width: 120 },
                    { field: 'inspectionStandardClassify_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BmBrzA_b0968850`, '考察标准分类'), width: 120 },
                    { field: 'inspectionStandardName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BmBr_3b96a7db`, '考察标准'), width: 120 },
                    { field: 'inspectScore', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Bmjz_3b95a28b`, '考察得分'), width: 120 },
                    { field: 'scoreGrade', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jzEt_2cb73a0d`, '得分等级'), width: 120 },
                    { field: 'inspectionConclusion_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BmyV_3b99a483`, '考察结论'), width: 120 },
                    { field: 'abarbeitungLimitTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WAirMLKI_4d4d7d16`, '限期整改完成时间'), width: 120 }
                ],
                performance: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'reportNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_reportCode`, '报表编码'), width: 160 },
                    { field: 'reportName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_reportName`, '报表名称'), width: 160 },
                    { field: 'assessmentCycle_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_assessmentCycle`, '考核周期'), width: 160 },
                    { field: 'assessmentTimeRange', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_assessmentTimeRange`, '考核时间范围'), width: 160 }
                ]
            },
            labelCol: {
                md: { span: 8 },
                lg: { span: 8 },
                xxl: { span: 6 }
            },
            wrapperCol: {
                md: { span: 16 },
                lg: { span: 16 },
                xxl: { span: 18 }
            },
            checkedConfig: {highlight: true, reserve: true, trigger: 'row'},
            tableData: [],
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                pageNo: 1,
                align: 'left',
                pageSizes: [10, 20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            propParams: {}
        }
    },
    methods: {
        suorceTypeChange (newValue, opt) {
            if (newValue != '') {
                this.suorceType = newValue
                this.suorceTypeName = opt.componentOptions.propsData.title
                this.currentUrl = this.urlList[newValue]
                this.currentColumns = this.columnList[newValue]
                this.$refs.selectGrid.reloadColumn(this.currentColumns)
                var p = Object.assign({}, this.propParams, {keyWord: this.keyWord})

                if (newValue == 'order') {
                    p = Object.assign(p, {itemStatus: '1'})
                }
                this.loadData(p)
                this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxRow()
            }
        },
        loadData (params) {
            this.loading = true
            getAction(this.currentUrl, params).then(res => {
                if (res.success) {
                    let list = res.result.records || []
                    this.tableData = list
                    this.tablePage.total = res.result.total
                }
                this.loading = false
            })
        },
        open (params) {
            // 重置第一个
            this.suorceType = 'order'
            let queryParams = {pageSize: this.tablePage.pageSize, pageNo: '1'}
            if (params) {
                queryParams = Object.assign({}, queryParams, params)
            }
            this.propParams = {...queryParams}
            this.loadData(queryParams)
            this.visible = true
            this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxRow()
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            let itemGrid = this.$parent.getItemGridRef('deductCostItemList')
            let selectedData = this.$refs.selectGrid.getCheckboxRecords()
            if (!(selectedData?.length)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            let resultList = []
            for (let i in selectedData) {
                let data = selectedData[i]
                let item = {}

                if (this.suorceType === 'order') {
                    data.relationNumber = data['orderNumber']
                    data.relationItemNumber = data['itemNumber']
                } else if (this.suorceType === 'voucher') {
                    data.relationNumber = data['voucherNumber']
                    data.relationItemNumber = data['itemNumber']
                } else if (this.suorceType === 'qualityCheck') {
                    data.relationNumber = data['checkNumber']
                } else if (this.suorceType === 'sample') {
                    data.relationNumber = data['sampleNumber']
                } else if (this.suorceType === 'sampleCheck') {
                    data.relationNumber = data['checkNumber']
                } else if (this.suorceType === 'inspection') {
                    data.relationNumber = data['documentsSerialNumber']
                } else if (this.suorceType === 'performance') {
                    data.relationNumber = data['reportNumber']
                }
                data.relationType = this.suorceType
                resultList.push(item)
            }
            this.visible = false
            this.$emit('ok', selectedData)
            itemGrid.insert(selectedData)
        },
        handlePageChange ({currentPage, pageSize}) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            let pageNo = currentPage
            let queryParams = Object.assign({}, this.propParams, {pageNo, pageSize, keyWord: this.keyWord})


            /*
        根据不同的url增加入参
        this.suorceType
         */
            let p = queryParams

            if (this.suorceType == 'order') {
                p = Object.assign(p, {itemStatus: '1'})
            }
            this.loadData(p)
        },
        onSearch () {
            console.log(this.suorceType)
            // 提交重置
            this.tablePage.currentPage = 1
            this.tablePage.pageSize = this.propParams.pageSize
            if (this.suorceType == '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectSourceType`, '请选择来源类型'))
            } else {
                let p = Object.assign({}, this.propParams, {keyWord: this.keyWord})
                /*
        根据不同的url增加入参  采购类型的入参有多个，直接在后端写死了
         */
                if (this.suorceType == 'order') {
                    p = Object.assign(p, {itemStatus: '1'})
                }
                this.loadData(p)
            }
        }
    }
}
</script>