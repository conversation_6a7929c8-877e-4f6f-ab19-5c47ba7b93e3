<template>
  <!-- 2022-03-08以前版本采购竞价大厅（批量） -->
  <div class="saleBuyBid">
    <a-spin :spinning="spinning">
      <div class="container">
        <div class="top">
          <div class="breadcrumb">
            <!-- <breadcrumb></breadcrumb> -->
          </div>
          <div class="menu">
            <a-button
              type="default"
              @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_menu_Rl_a72da`, '关闭') }}</a-button>
            <a-button
              type="primary"
              @click="submit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_offer`, '报价') }}</a-button>
            <!-- <a-button
              type="primary"
              @click="visible = true">竞价</a-button> -->
            <a-button
              type="primary"
              @click="openRange">{{ $srmI18n(`${$getLangAccount()}#i18n_title_biddingRangeUpper`, '竞价幅度上限') }}</a-button>
            <a-button
              type="primary"
              @click="refresh">{{ $srmI18n(`${$getLangAccount()}#i18n_title_refresh`, '刷新') }}</a-button>
          </div>
        </div>

        <div class="content">
          <div class="baseInfo">
            <div class="card">
              <a-card
                :title="$srmI18n(`${$getLangAccount()}#i18n_title_bindingSituation`, '竞价情况')"
                :bordered="false"
                :headStyle="headStyle"
                :bodyStyle="bodyStyle"
              >
                <div class="cardContent">
                  <div class="infoList">
                    <ul>
                      <template v-for="el in baseInfo">
                        <li :key="el.key">
                          <template v-if="el.key === 'beforeEndMinute'">
                            <div class="row">
                              <span class="tit">{{ el.label }}</span>
                              <span class="txt">{{ form[el.key] || '' }}</span>
                              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_minutesHaveQuotation`, '分钟内, 有报价') }}</span>
                            </div>
                          </template>
                          <template v-else-if="el.key === 'delayMinute'">
                            <div class="row">
                              <span class="tit">{{ el.label }}</span>
                              <span class="txt">{{ form[el.key] || '' }}</span>
                              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_minutes`, '分钟') }}</span>
                            </div>
                          </template>
                          <template v-else>
                            <div class="row">
                              <span class="tit">{{ el.label }}</span>
                              <span class="txt">{{ form[el.key] || '' }}</span>
                            </div>
                          </template>
                        </li>
                      </template>
                    </ul>
                  </div>
                </div>
              </a-card>
            </div>

            <div class="card">
              <a-card
                :title="$srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息')"
                :bordered="false"
                :headStyle="headStyle"
                :bodyStyle="bodyStyle_2"
              >
                <div class="cardContent">
                  <div class="table">
                    <vxe-grid
                      ref="batchMaterialGrid"
                      height="280"
                      v-bind="batchMaterialGridOptions"
                      :row-class-name="handleRowClass"
                      @cell-click="cellClickEvent"
                    >
                      <template slot="empty">
                        <a-empty />
                      </template>
                    </vxe-grid>
                  </div>
                </div>
              </a-card>
            </div>
          </div>

          <div class="chartBox">
            <a-card :bordered="false">
              <div class="title">
                <div class="row">
                  <div class="countdown">
                    <span class="tit">
                      {{ countdownText }}
                    </span>
                    <span
                      class="icon"
                      v-if="deadline">
                      <a-icon
                        type="clock-circle"
                        theme="outlined" 
                        :style="{ fontSize: '22px', color: '#f41616', marginRight: '8px' }"
                      />
                    </span>
                    <a-statistic-countdown
                      v-if="deadline"
                      :value="deadline"
                      :value-style="valueStyle"
                      @finish="handleFinish"
                    />
                  </div>
                </div>
                <a-divider />
                <div class="row">
                  <div class="list">
                    <ul>
                      <li>{{ $srmI18n(`${$getLangAccount()}#i18n_title_duration`, '持续时间') }} {{ form.keepMinute }} {{ $srmI18n(`${$getLangAccount()}#i18n_title_minutes`, '分钟') }}</li>
                      <li>{{ $srmI18n(`${$getLangAccount()}#i18n_title_intervalTime`, '间隔时间') }} {{ form.intervalMinute }} {{ $srmI18n(`${$getLangAccount()}#i18n_title_minutes`, '分钟') }} </li>
                    </ul>
                  </div>

                </div>
              </div>
              <div class="chartInfo">
                <div class="echart">
                  <line-chart
                    ref="echart"
                    :chartData="chartData">
                  </line-chart>
                </div>
                <div
                  v-if="gt1900"
                  class="table chartTable">
                  <vxe-grid
                    ref="chartGrid"
                    v-bind="batchSaleChartGridOptions"
                  >
                    <template slot="empty">
                      <a-empty />
                    </template>
                    <template #supplierName_default="{ row }">
                      <span class="blue">{{ row.supplierName }}</span>
                    </template>
                    <template #price_default="{ row }">
                      <span class="red">{{ isPack ? row.totalAmount : row.price }}</span>
                    </template>
                    <template #quoteTime_default="{ row }">
                      <span>{{ row.quoteTime }}</span>
                    </template>
                  </vxe-grid>
                </div>
              </div>

              <div class="table quoteTable">
                <vxe-grid
                  ref="batchQuoteGrid"
                  v-bind="batchQuoteGridOptions"
                  :row-class-name="handleRowClass"
                >
                  <template slot="empty">
                    <a-empty />
                  </template>
                  <template #price_header="{ column }">
                    <i class="vxe-icon--edit-outline"></i>
                    <span>{{ column.title }}</span>
                    <span class="red">*</span>
                  </template>

                  <template #price_default="{ row, rowIndex }">
                    <vxe-input
                      v-model="row.price"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价')"
                      type="float"
                      min="0"
                      clearable
                      :disabled="isPriceDisabled"
                      @blur="handlePriceBlur({ row, rowIndex })"
                    >
                    </vxe-input>
                  </template>

                  <template #netPrice_default="{ row, rowIndex }">
                    <vxe-input
                      v-model="row.netPrice"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价')"
                      type="float"
                      min="0"
                      clearable
                      :disabled="!isPriceDisabled"
                      @blur="handleNetPriceBlur({ row, rowIndex })"
                    >
                    </vxe-input>
                  </template>

                  <template #remark_default="{ row }">
                    <vxe-input
                      v-model="row.remark"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_remark`, '备注')"
                    >
                    </vxe-input>
                  </template>
                  <!-- <template #operate>
                    <vxe-button
                      status="primary"
                      size="mini"
                      round
                      @click="submit"
                    >
                      {{ $srmI18n(`${$getLangAccount()}#i18n_title_offer`, '报价') }}
                    </vxe-button>
                  </template> -->
                </vxe-grid>
              </div>

              <div class="table rankTable">
                <vxe-grid
                  ref="rankGrid"
                  v-bind="batchRankGridOptions"
                >
                  <template slot="empty">
                    <a-empty />
                  </template>
                </vxe-grid>
              </div>
            </a-card>
          </div>
        </div>
      </div>
    </a-spin>

    <a-modal
      v-drag    
      v-model="openUpper"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_setAmplitudeUpper`, '设置竞价幅度上限') "
      @ok="setUpper">
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_amplitudeUpper`, '幅度上限') }}：
      <a-input-number
        v-model="changeRangeUpper"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterAmplitudeUpper`, '请输竞价幅度上限')" /><br>
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_rangeUnit`, '幅度单位') }}：<span>{{ form.rangeUnit_dictText }}</span><br>
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_amplitudeLower`, '幅度下限') }}：<span>{{ form.changeRange }}</span>
    </a-modal>
  </div>
</template>

<script>
const resizeChartMethod = '$__resizeMethod'

import { groupBy, debounce } from 'lodash'
import { apiSaleQueryBidLobbyDetail, apiSaleQueryBidLobbyQuote, apiSaleHisQueryBidLobbyDetail, apiSaleQuotePrice, apiSaleSetUpper } from '@/api/apiBidding.js'
import { currency } from '@/filters'
import { batchMaterialGridOptions, batchSaleChartGridOptions, batchRankGridOptions, batchQuoteGridOptions } from '../../gridConfig/sale'
import { isDecimal } from '@/utils/validate.js'
import eventBus from '@/utils/eventBus.js'
import MyWebSocket from '@/utils/websocket.js'
import {USER_INFO} from '@/store/mutation-types'
import { add, mul, div } from '@/utils/mathFloat.js' // 加 减 乘 除
// import Breadcrumb from '@/components/tools/Breadcrumb.vue'

// import WebSocketClass from '@/utils/WebSocketClass'
import LineChart from '../../components/LineChart'
// import QuotePrice from '../../components/QuotePrice'

const HEART_BEAT_CONFIG = {
    time: 30 * 1000, // time：心跳时间间隔
    timeout: 3 * 1000, // timeout：心跳超时间隔
    reconnect: 10 * 1000 // reconnect：断线重连时
}

export default {
    inject: [
        'closeCurrent'
    ],
    components: {
        LineChart
        // QuotePrice
        // Breadcrumb
    },
    filters: {
        currency
    },
    data () {
        return {
            batchMaterialGridOptions,
            batchSaleChartGridOptions,
            batchRankGridOptions,
            batchQuoteGridOptions,
            current: -1,
            currentItemNumber: -1,
            deadline: 0,
            countdownText: '',
            gt1900: false,
            spinning: false,
            delayTime: 300,
            headStyle: {
                borderBottom: 'none',
                padding: '0 6px',
                minHeight: '34px',
                lineHeight: '34px',
                height: '40px'
            },
            valueStyle: {
                fontSize: '24px',
                color: '#f41616'
            },
            bodyStyle: {
                padding: '0 24px 24px'
            },
            bodyStyle_2: {
                padding: '0 6px 24px 6px'
            },
            progress: {
                percent: 78,
                status: 'active',
                strokeColor: {
                    from: '#4892ff',
                    to: '#596fe1'
                }
            },
            endTime: 0,
            minPrice: 1200,
            form: {
                ebiddingNumber: '', // 竞价单号
                ebiddingType: '', // 竞价方式
                changeRange: '', // 价格调整幅度
                rangeUnit: '', // 幅度单位
                beginTime: '', // 竞价开始时间  
                endTime: '', // 竞价结束时间
                keepMinute: '', // 每轮持续时间
                intervalMinute: '', // 每轮间隔时间
                currentRound: '',
                ebiddingWay: '',
                id: '',
                itemNumber: ''
            },
            baseInfo: [
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingNumber`, '竞价单号'), key: 'ebiddingNumber' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'), key: 'ebiddingStatus_dictText' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingType`, '竞价类型'), key: 'ebiddingType_dictText' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quotePriceWay`, '报价方式'), key: 'ebiddingWay_dictText' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_startTime`, '启动时间'), key: 'startTime' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_endTime`, '结束时间'), key: 'endTime' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentBinddingMaterial`, '当前竞价物料'), key: 'currentMaterialDesc' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentRound`, '当前轮次'), key: 'currentRound' },
                // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_startingUnitPrice`, '起拍单价'), key: 'startPrice' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packageStartingUnitPrice`, '打包起拍价金额'), key: 'startTotalAmount' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingRange`, '竞价幅度'), key: 'changeRange' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rangeUnit`, '幅度单位'), key: 'rangeUnit_dictText' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extensionRule`, '延期规则'), key: 'delayRule_dictText' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_NowExtensionNum`, '当前延期次数'), key: 'currentDelayCount' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_beforeOverTime`, '结束时间前'), key: 'beforeEndMinute' }, // 分钟内，有报价
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extensionTime`, '延期时间'), key: 'delayMinute' } // 分钟
            ],
            chartData: {},
            // websocket: null,
            socket: null,
            lockReconnect: false, //是否真正建立连接
            timeout: 30*1000, //30秒一次心跳
            timeoutObj: null, //心跳心跳倒计时
            serverTimeoutObj: null, //心跳倒计时
            timeoutnum: null, //断开 重连倒计时
            visible: false,
            openUpper: false,
            changeRangeUpper: '',
            heartBeatConfig: HEART_BEAT_CONFIG
        }
    },
    computed: {
        // 反否反向竞价
        isReverse () {
            const { ebiddingType = '' } = this.form || {}
            return ebiddingType === '1'
        },
        // 是否打包状态
        isPack () {
            const { ebiddingWay = '' } = this.form || {}
            return ebiddingWay === '0'
        },
        // 含税单价是否可输入
        isPriceDisabled () {
            return this.form.quoteType === '1'
        },
        getProp () {
            /**
             * ebiddingWay, 0: 打包, 1: 逐条
             * quoteType, 0: 含税价, 1: 不含税价
             */
            const { ebiddingWay = '1', quoteType = '0' } = this.form || {}
            const key = `${ebiddingWay + ''}${quoteType + ''}`
            const map = {
                '10': 'price',
                '11': 'netPrice',
                '00': 'totalAmount',
                '01': 'netTotalAmount'
            }
            return map[key] || 'price'
        }
    },
    methods: {
        cellClickEvent ({ row }) {
            const { itemNumber= '' } = row || {}
            if (itemNumber === this.current) return
            this.current = itemNumber
            this.getItemData(this.current)
        },
        getMessage (msg) {
            console.log(msg.data)
            this.refresh()
            this.reset()
        },
        getWebsocketUrl () {
            let { serivceUrl = '' } = this.$ls.get(USER_INFO) || {}
            let url = serivceUrl.replace('https://', 'wss://').replace('http://', 'ws://')
            this.wsUrl = `${url}/els/websocket/bidding/${this.form.ebiddingNumber}`
            // this.wsUrl = `ws://192.168.2.116:8080/els/websocket/bidding/${this.form.ebiddingNumber}`
        },
        reconnectWebSocket () {
            if (!this.websocket) { //第一次执行，初始化
                this.connectWebSocket()
            }
            if (this.websocket && this.websocket.reconnectTimer) { //防止多个websocket同时执行
                clearTimeout(this.websocket.reconnectTimer)
                this.websocket.reconnectTimer = null
                this.connectWebSocket()
            }
        },
        handleWebScoketMessage (e) {
            try {
                let data = e.data
                if (data) {
                    if (data === 'heartcheck') { // 心跳
                        this.websocket.webSocketState = true
                    } else if (data === this.form.ebiddingNumber) {
                        this.getData()
                    }
                }
            } catch (err) {
                console.log('err', err)
            }
        },
        connectWebSocket () {
            this.websocket = new MyWebSocket(this.wsUrl)
            console.log('this.websocket', this.websocket)
            this.websocket.init(this.heartBeatConfig, true)
        },
        closeWebscoket () {
            this.websocket && this.websocket.close()
            this.websocket = null
        },
        handleSockeMessage () {
            if (this.spinning) return
            this.refresh()
        },
        goBack () {
            this.closeCurrent()
        },
        handleSuccess () {
            this.refresh()
        },
        refresh () {
            // Object.assign(this.$data, this.$options.data())
            this.getData()
        },
        handleFinish () {
            this.refresh()
        },
        getData () {
            this.spinning = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))
            const { id } = this.form
            const params = {
                id,
                itemNumber: this.currentItemNumber,
                busAccount: this.form.busAccount
            }
            const promiseList = [
                apiSaleQueryBidLobbyDetail(params)
            ]
            Promise.all(handlePromise(promiseList)).then(res => {
                const [infoRes] = res || []
                if (infoRes && infoRes.status === 'success') {
                    this.fixInfoData(infoRes.res)
                }
                this.getItemData()
                this.getQuoteTableData()
            })
        },
        getItemData (num = '') {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))
            const { id, busAccount } = this.form
            const params = {
                id,
                busAccount,
                itemNumber: num || this.currentItemNumber
            }
            const promiseList = [
                apiSaleQueryBidLobbyQuote(params),  apiSaleHisQueryBidLobbyDetail(params)
            ]
            Promise.all(handlePromise(promiseList)).then(res => {
                this.spinning = false
                const [itemRes, hisRes] = res || []
                if (itemRes && itemRes.status === 'success') {
                    this.fixItemData(itemRes.res)
                }
                if (hisRes && hisRes.status === 'success') {
                    this.fixHisPriceData(hisRes.res)
                }
            })

        },
        // 倒计时计算
        checkTime () {
            let { beginTime, endTime, ebiddingStatus } = this.form
            const { serviceTime } = this.form
            const now = serviceTime
                ? new Date(serviceTime).getTime()
                : +new Date()
            // 服务器时间 - 开始或结束时间 = 实际倒计时差
            beginTime = beginTime && Date.now() + (new Date(beginTime).getTime() - now)
            endTime = endTime &&  Date.now() + (new Date(endTime).getTime() - now)
            if(ebiddingStatus === '3'){//待竞价状态
                if (now < beginTime) {
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vKutK_64c80f5c`, '开始倒计时')
                    this.deadline = beginTime
                }else{
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n__EonRvAOu_6f8beeea`, '等待采购开启竞价')
                    this.deadline = 0
                }
            }else if(ebiddingStatus === '4'){//竞价中状态
                if(now < endTime) {
                    this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yWutK_2b6a279b`, '结束倒计时')
                    this.deadline = endTime
                }else{

                    this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n__EonRyWOu_7e4b6167`, '等待采购结束竞价')
                    this.deadline = 0
                }
            }else{
                this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SutK_2f8d3027`, '无倒计时')
                this.deadline = 0
            }
        },
        fixInfoData ({ result }) {
            const { saleEbiddingItemList = [], ebiddingWay, ...others } = result || {}
            let { currentItemNumber } = result || {}
            
            this.batchMaterialGridOptions.data = saleEbiddingItemList
            this.currentItemNumber = currentItemNumber || '1'
            this.current = this.currentItemNumber
            this.form = {
                ebiddingWay,
                currentItemNumber: this.currentItemNumber,
                ...others
            }
            // 逐条，获取起拍价格
            if (ebiddingWay === '1') {
                let i = this.currentItemNumber - 1
                this.form.startPrice = saleEbiddingItemList[i].startPrice
            }
            this.checkTime()
        },
        fixItemData ({ result }) {
            const { saleEbiddingItemList = [] } = result || {}
            this.batchRankGridOptions.data = saleEbiddingItemList
        },
        fixHisPriceData ({ result = [] }) {
            this.batchSaleChartGridOptions.data = result
            this.setRankTableColumn()
            this.resetChart()
            this.getEchartData(result)
        },
        setRankTableColumn () {
            this.batchRankGridOptions.columns.forEach(item => {
                if (item.field === 'price') {
                    item.visible = !this.isPack
                }
            })
        },
        getEchartData (arr) {
            let legend = {
                data: []
            }
            let xAxis = {
                type: 'category',
                boundaryGap: false,
                data: []
            }
            let result = arr ? arr.sort((a, b) => {
                const timeA = new Date(a.quoteTime).getTime()
                const timeB = new Date(b.quoteTime).getTime()
                return timeA - timeB
            }) : []
            const group = groupBy(result, 'elsAccount')
            legend.data = Object.keys(group).map(key => {
                const arr = group[key]
                const { supplierName } = arr[0] || {}
                return `${supplierName}`
            })
            let length = 0
            const series =  Object.keys(group).map(key => {
                const prop = this.isPack ? 'totalAmount' : 'price'
                const arr = group[key]
                const data = arr.map(n => n[prop])
                // quoteTime
                const { supplierName } = arr[0] || {}
                const name =  `${supplierName}`

                length = arr.length > length
                    ? arr.length
                    : length
                return {
                    name,
                    data,
                    type: 'line'
                }
            })
            for (let i = 0; i < length; i++) {
                xAxis.data.push(i + 1)
            }
            
            this.chartData = {
                legend,
                xAxis,
                series
            }
        },
        getScreenWidth () {
            const clientWidth = document.documentElement.clientWidth
            this.gt1900 = (clientWidth >= 1900)
        },
        getQueryData () {
            const { ebiddingNumber, currentItemNumber, id } = this.$route.query || {}
            this.currentItemNumber = currentItemNumber || '1'
            this.current = this.currentItemNumber
            this.form = Object.assign({}, this.form, {
                ebiddingNumber,
                currentItemNumber,
                id
            })
        },
        handleRowClass ({ row }) {
            const { itemNumber = '' } = row || {}
            if (itemNumber === this.current) {
                return 'row--current'
            }
        },
        resetChart () {
            this.$refs.echart && this.$refs.echart.chart.clear()
        },
        // 通过lodash的防抖函数来控制resize的频率
        [resizeChartMethod]: debounce(function () {
            this.getScreenWidth()
        }, 200),
        getQuoteTableData () {
            this.batchQuoteGridOptions.data = []
            let tableData = this.batchMaterialGridOptions.data
            tableData.forEach(i => {
                let { taxRate = 0, priceUnit = '', currency = '', effectiveDate='', expiryDate='' } = i || {}
                taxRate = isDecimal(taxRate) ? taxRate : 0
                const row = {
                    ...i,
                    taxRate,
                    priceUnit,
                    currency,
                    effectiveDate,
                    expiryDate,
                    price: '',
                    netPrice: ''
                }
                this.batchQuoteGridOptions.data.push(row)
            })
        },
        // 含税单价 price, 实时计算: 不含税价 netPrice
        // 不含税价 = 含税价 / (1 + 税率 / 100)
        handlePriceBlur (data) {
            const { price } = data.row
            const rowIndex = data.rowIndex
            if (!price || !isDecimal(price)) return
            
            let { taxRate = 0 } = this.batchQuoteGridOptions.data[0] || {}
            if (!taxRate || !isDecimal(taxRate)) {
                taxRate = 0
            }
            let tax = add(1, div(taxRate, 100))
            let netPrice = div(price, tax)
            netPrice = netPrice.toFixed(2)
            console.log('netPrice', price, netPrice, rowIndex)
            this.$set(this.batchQuoteGridOptions.data[rowIndex], 'netPrice', netPrice)
        },
        // 未税单价 netPrice, 实时计算: 含税价 price
        // 含税价 = 不含税价 * (1 + 税率 / 100)
        handleNetPriceBlur (data) {
            const { netPrice } = data.row
            const rowIndex = data.rowIndex
            if (!netPrice || !isDecimal(netPrice)) return
            
            let { taxRate = 0 } = this.batchQuoteGridOptions.data[0] || {}
            if (!taxRate || !isDecimal(taxRate)) {
                taxRate = 0
            }
            let tax = add(1, div(taxRate, 100))
            let price = mul(netPrice, tax)
            price = price.toFixed(2)
            this.$set(this.batchQuoteGridOptions.data[rowIndex], 'price', price)
        },
        submit () {
            const netPriceIndexs = []
            const priceIndexs = []
            const effectiveDateIndexs = []
            const expiryDateIndexs = []
            this.batchQuoteGridOptions.data.forEach((i, index) => {
                const { price, netPrice, effectiveDate, expiryDate } = i || {}
                if (this.isPriceDisabled) {
                    if (!netPrice) {
                        netPriceIndexs.push(index + 1)
                    }
                } else {
                    if (!price) {
                        priceIndexs.push(index + 1)
                    }
                }

                if (!effectiveDate) {
                    effectiveDateIndexs.push(index + 1)
                }

                if (!expiryDate) {
                    expiryDateIndexs.push(index + 1)
                }
            })
            if (netPriceIndexs.length > 0) {
                this.$message.error(`${netPriceIndexs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LftulS_e9e2152c`, '未税单价必填')}`)
                return
            }
            if (priceIndexs.length > 0) {
                this.$message.error(`${priceIndexs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_xftulS_7f8320cb`, '含税单价必填')}`)
                return
            }
            if (effectiveDateIndexs.length > 0) {
                this.$message.error(`${effectiveDateIndexs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selecteFfectiveTime`, '生效时间必填')}`)
                return
            }
            if (expiryDateIndexs.length > 0) {
                this.$message.error(`${expiryDateIndexs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KXKILlSd_4431ec2e`, '失效时间必填')}`)
                return
            }

            const callback = () => {
                const saleEbiddingItemList = this.batchQuoteGridOptions.data.map(item => {
                    const { remark, effectiveDate, expiryDate, price, netPrice } = item
                    const currentItem = item
                    currentItem.supplierRemark = remark
                    currentItem.effectiveDate = effectiveDate
                    currentItem.expiryDate = expiryDate
                    // 确定取值为 含税价 与 不含税价
                    const val = ['price', 'totalAmount'].includes(this.getProp)
                        ? price
                        : netPrice

                    currentItem[this.getProp] = val
                    return currentItem
                })
                const params = {
                    ...this.form,
                    saleEbiddingItemList
                }
                this.spinning = true
                apiSaleQuotePrice(params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.spinning = false
                    this.$message[type](res.message)
                    this.$emit('success')
                    // this.getQuoteTableData()
                    this.refresh()
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_RLsu_38d6fc68`, '确认报价'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherQuote`, '确认是否报价'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        openRange (){
            this.openUpper = true
            this.changeRangeUpper = ''
        },
        setUpper (){
            if(this.changeRangeUpper == ''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BzXWxOLV_1bde1ed7`, '幅度上限不能为空'))
                return
            }
            if(this.changeRangeUpper <= this.form.changeRange){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BzXWlTfUOuBzIW_f0bd5fa3`, '幅度上限必须大于竞价幅度下限'))
                return
            }
            let params = {}
            const that = this
            params['id'] = this.form.id
            params['changeRangeUpper'] = this.changeRangeUpper
            apiSaleSetUpper(params).then(res => {
                const type = res.success ? 'success' : 'error'
                that.openUpper = false
                this.spinning = false
                this.$message[type](res.message)
                this.$emit('success')
            }).finally(() => {
                that.refresh()
            })
        },
        init () {
            this.getQueryData()
            this.getData()

        }
    },
    watch: {
        '$route': {
            handler ({ path }) {
                if (path !== '/ebidding/saleLobbyBatch') {
                    return
                }
                this.init()
            },
            immediate: true
        }
    },
    created () {
        this.getScreenWidth()
        this.getWebsocketUrl()
        this.getData()
    },
    beforeDestroy () {
        window.removeEventListener('reisze', this[resizeChartMethod])
        this.closeWebscoket()
        // if (this.socket) {
        //     this.socket.close()
        //     this.lockReconnect = true
        //     this.socket = null
        // }
    },
    mounted () {
        window.addEventListener('resize', this[resizeChartMethod])
        eventBus.onEvent('reconnect', this.reconnectWebSocket) //接收重连消息
        eventBus.onEvent('handleMessage', this.handleWebScoketMessage) //接收重连消息
        eventBus.emitEvent('reconnect')
    }
}
</script>

<style lang="less" scoped>
@red: #f41616;

.saleBuyBid {
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    .red {
      color: #f41616;
    }
    .blue {
      color: #1890ff;
    }
    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px;
        background: #fff;
        .menu {
            text-align: right;
            .ant-btn {
                & +.ant-btn {
                    margin-left: 8px;
                }
            }
        }
    }
    .content {
      display: flex;
      padding: 6px;
      .ant-card {
        border-radius: 8px;
      }
      .baseInfo {
        flex: 0 0 350px;
        max-width: 350px;
        .card {
          & +.card {
            margin-top: 8px;
          }
        }
        .cardContent {
          .list, .infoList {
            line-height: 2.2;
            font-weight: 300;
            color: #666;
          }
          .list {
            margin-top: 20px;
            font-size: 20px;
          }
          .infoList {
            font-size: 12px;
          }
          .tit {
            margin-right: 8px;
            &::after {
              content: ':'
            }
          }
        }
      }
      .chartBox {
        flex-grow: 1;
        flex-shrink: 0;
        margin-left: 10px;
        width: calc(100% - 360px);
        max-width: calc(100% - 360px);
        .title {
            margin: 0 auto;
            width: 90%;;
            .row, .list > ul {
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .tit {
                margin-right: 12px;
                font-size: 20px;
                color: #000;
            }
            .minPrice {
                font-size: 24px;
                color: @red;
                + .countdown {
                  margin-left: 44px;
                }
            }
            .countdown {
                display: flex;
                align-items: center;
            }
            .list {
                ul {
                    li + li {
                        margin-left: 12px;
                    }
                }
            }
        }
        .chartInfo {
          display: flex;
          margin-top: 30px;
          .echart {
            flex: 1;
          }
          .chartTable {
            flex: 0 0 200px;
            // margin-left: 20px;
          }
        }
        .rankTable {
          margin-top: 10px;
          min-height: 208px;
        }
        .quoteTable {
          margin-top: 10px;
        }
      }
    }
}
</style>
