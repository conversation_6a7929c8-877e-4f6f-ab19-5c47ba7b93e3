<template>
  <div
    v-if="currentRow"
    class="materialList">
    <a-tabs
      :activeKey="activeKey"
      @change="callback">
      <a-tab-pane
        v-for="(tabItem, index) in currentRow.quoteColumnList"
        :key="index + ''"
        :tab="tabItem.title">
      </a-tab-pane>
      <template
        slot="tabBarExtraContent"
      >
        <a-button
          size="small"
          @click="back">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-tabs>
    <listTable
      ref="listTable"
      v-if="showListTable"
      :setGridHeight="setGridHeight"
      :pageStatus="'detail'"
      :fromSourceData="currentRow.saleQuoteMaterialDataList[activeKey] ? currentRow.saleQuoteMaterialDataList[activeKey].materialDataList || [] : []"
      :showTablePage="false"
      :statictableColumns="statictableColumns"></listTable>
  </div>
</template>
<script>
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@/views/srm/bidding_new/BiddingHall/components/title-crtl'
export default {
    props: {
        currentRow: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: 'edit',
            type: String
        },
        formData: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    components: {
        listTable,
        titleTrtl
    },
    data () {
        return {
            activeKey: '0',
            showListTable: true,
            currentQuoteItem: {},
            statictableColumns: [
                { 'type': 'seq', 'width': 50, 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), fixed: 'left' },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_printerBrand`, '品牌'), 'field': 'brand', width: 100, fixed: 'left'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_materialCode`, '物料编码'), 'field': 'materialNumber', width: 120, fixed: 'left' },
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 120, fixed: 'left'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), 'field': 'materialDesc', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialModel`, '物料型号'), 'field': 'materialModel', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), 'field': 'materialSpec', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), 'field': 'purchaseCycle', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseType`, '采购类型'), 'field': 'purchaseType_dictText', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireDate`, '需求日期'), 'field': 'requireDate', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireQuantity`, '需求数量'), 'field': 'requireQuantity', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), 'field': 'purchaseUnit_dictText', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税价'), 'field': 'price', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netPrice`, '净价'), 'field': 'netPrice', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxCode`, '税码'), 'field': 'taxCode', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxRate`, '税率'), 'field': 'taxRate', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_targetPrice`, '目标价'), 'field': 'targetPrice', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), 'field': 'currency', 'dictCode': 'srmCurrency', enabled: false, fieldType: 'select', width: 120}
            ]
        }
    },
    
    methods: {
        back () {
            this.$emit('back')
        },
        callback (v) {
            this.activeKey = v
        },
        reloadData () {
            this.$refs.listTable.reloadData(this.currentRow.quoteColumnList[this.activeKey].materialDataList)
        }
    },
    created () {
        this.setGridHeight = document.documentElement.clientHeight - 290
    }
}
</script>

