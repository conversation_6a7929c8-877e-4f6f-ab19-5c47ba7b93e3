<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-content">
          <a-form-model
            ref="baseForm"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            :rules="rules"
            :model="formData">
            <div class="deliveryTime">
              <div class="deliveryTime-title">
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_LVtnJKI_b7736b79`, '澄清及递交时间') }}
              </div>
              <div class="deliveryTime-content">
                <a-row >
                  <a-col :span="12">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_QILVyRKI_218968c6`, '文件澄清截止时间')"
                      required
                      prop="fileClarificationEndTime">
                      <a-date-picker
                        v-if="!check"
                        show-time
                        valueFormat="YYYY-MM-DD hh:mm"
                        v-model="formData.fileClarificationEndTime"/>
                      <span v-else>{{ formData.fileClarificationEndTime }}</span>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_QInJyRKI_f4bdea17`, '文件递交截止时间')"
                      required
                      prop="fileSubmitEndTime">
                      <a-date-picker
                        v-if="!check"
                        show-time
                        valueFormat="YYYY-MM-DD hh:mm"
                        v-model="formData.fileSubmitEndTime" />
                      <span v-else>{{ formData.fileSubmitEndTime }}</span>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_bidOpenTime`, '开标时间')"
                      prop="openBiddingTime"
                      required>
                      <a-date-picker
                        v-if="!check"
                        show-time
                        valueFormat="YYYY-MM-DD hh:mm"
                        v-model="formData.openBiddingTime"/>
                      <span v-else>{{ formData.openBiddingTime }}</span>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </div>
            </div>
            <div class="clarification">
              <div class="clarification-title">
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_QILV_2f5d3190`, '文件澄清') }}
              </div>
              <div class="clarification-content">
                <a-row >
                  <a-col :span="12">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题')"
                      required
                      prop="title">
                      <a-input
                        v-model="formData.title"
                        v-if="!check"></a-input>
                      <span v-else>{{ formData.title }}</span>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row >
                  <a-col :span="12">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_LVCc_34586d95`, '澄清内容')"
                      prop="content"
                      required>
                      <a-textarea
                        v-if="!check"
                        v-model="formData.content"
                        :auto-size="{ minRows: 2, maxRows: 6 }"></a-textarea>
                      <span v-else>{{ formData.content }}</span>
                    </a-form-model-item>
                  </a-col>
                </a-row>

                <a-row v-if="this.currentSubPackage().evalutionType == '1' || this.currentSubPackage().bidOpenType == '1'">
                  <a-col :span="12">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_YBQIAH_99eb0b37`, '招标文件变更')">
                      <!-- <a-button
                        type="link"
                        @click="toBidFile">{{ $srmI18n(`${$getLangAccount()}#i18n_field_AtYBQI_f2a0e596`, '编辑招标文件') }}</a-button> -->
                      <span
                        v-if="ifAlter"
                        style="color: orange">
                        ({{ $srmI18n(`${$getLangAccount()}#i18n_field_Icr_16abc7d`, '已修改') }})
                      </span>
                      <span
                        v-else
                        style="color: orange">({{ $srmI18n(`${$getLangAccount()}#i18n_field_Lcr_18d57b5`, '未修改') }})</span>
                    </a-form-model-item>
                  </a-col>
                </a-row>

                <a-row >
                  <a-col :span="12">
                  </a-col>
                </a-row>
                <a-row >
                  <a-col :span="12">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_title_accessory`, '附件')"
                    >
                      <div
                        class="dropbox"
                        v-if="formData.id">
                        <a-upload
                          name="file"
                          :multiple="true"
                          :action="uploadUrl"
                          :headers="uploadHeader"
                          :accept="accept"
                          :data="{headId: this.formData.id, businessType: 'biddingPlatform', 'sourceNumber': tenderCurrentRow.tenderProjectNumber || tenderCurrentRow.id || '', actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'}"
                          :beforeUpload="beforeUpload"
                          @change="handleUploadChange"
                        >
                          <a-button v-if="!check"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                        </a-upload>
                        <div
                          v-for="(fileItem, index) in formData.purchaseAttachmentList"
                          :key="fileItem.id">
                          <span style="color: blue; cursor:pointer; margin-right:8px" >{{ fileItem.fileName }}</span>
                          <a-icon
                            v-if="!check"
                            type="delete"
                            @click="handleDeleteFile(index)"/>
                          <a
                            v-if="check"
                            style="margin-right:8px"
                            @click="preViewEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                          <a
                            v-if="check"
                            @click="downloadEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>    
                        </div>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-form-model>
        </div>
        <div class="page-footer" >
          <a-button @click="() => {this.$emit('hide')}">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          <a-button
            @click="handleSave"
            v-if="!check">{{ $srmI18n(`${$getLangAccount()}#i18n_field_save`, '保存') }}</a-button>
          <a-button
            @click="handleSubmit"
            v-if="!check">{{ $srmI18n(`${$getLangAccount()}#i18n_field_submit`, '提交') }}</a-button>
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        check: {
            type: Boolean,
            default: ()=> {
                return {}
            }
        }
    },
    inject: [
        'tenderCurrentRow', 'currentSubPackage'
    ],
    components: {
    },
    computed: {
        ifAlter () {
            return this.formData.biddingDocuments == '1'
        }
    },
    data () {
        return {
            labelCol: { span: 8 },
            wrapperCol: { span: 15 },
            confirmLoading: false,
            formData: {},
            rules: { },
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/saleAttachment/upload`,
            requestData: {
                detail: { url: '/tender/saleTenderClarificationInfo/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            url: {
                // add: '/tender/purchaseTenderClarificationInfo/add',
                // edit: '/tender/purchaseTenderClarificationInfo/edit',
                // // fxw：你这里用错了接口，给你从发布换成了提交接口
                // submit: '/tender/purchaseTenderClarificationInfo/submit'
            }
        }
    },
    methods: {
        // toBidFile () {
        //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXOxiTtkYBQI_a2f19ef5`, '供应商端不允许操作招标文件!'))
        // },
        async queryDetail () {
            if (this.requestData.detail) {
                let url = this.requestData.detail.url
                let args = this.requestData.detail.args(this)
                // 有id 才能请求,新建时是没有id的，不用请求查询接口
                if (args && args.id) {
                    this.confirmLoading = true
                    let query = await getAction(url, args)
                    this.confirmLoading = false
                    if (query && query.success) {
                        console.log(query.result)
                        this.formData = Object.assign({}, query.result)
                        this.nodeListData = this.formData.tenderEvaluationTemplateItemVoList
                    } else {
                        this.$message.error(query.message)
                    }
                }
            }
        },
        async downloadEvent (row) {
            console.log(row, '===')
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            this.$previewFile.open({params: row })
        }
        
    },
    mounted () {
        console.log(this.currentSubPackage())
        this.queryDetail()
    }
}
</script>
<style lang="less" scoped>
.deliveryTime-title, .clarification-title{
  padding: 8px;
  background: #F2F3F5;
}
</style>


