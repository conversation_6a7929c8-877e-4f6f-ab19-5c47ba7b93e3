<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-content">
          <a-form-model
            ref="baseForm"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            :rules="rules"
            :model="formData">
            <div class="deliveryTime">
              <div class="deliveryTime-title">
                {{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_baseForm488`, '基本信息')) }}
              </div>
              <div class="deliveryTime-content">
                <a-row>
                  <a-col :span="12">
                    <a-form-model-item
                      :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题'))"
                      required
                      prop="clarificationTitle">
                      <a-input
                        style="width: 90%"
                        v-model="formData.clarificationTitle" />
                    </a-form-model-item>
                  </a-col>

                  <a-col :span="12">
                    <a-form-model-item
                      :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_LVyRKI_87cc05b7`, '澄清截止时间'))"
                      required
                      prop="fileClarificationEndTime">
                      <a-date-picker
                        style="width: 90%"
                        show-time
                        valueFormat="YYYY-MM-DD HH:mm:ss"
                        v-model="formData.fileClarificationEndTime" />
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row class="textAreaClass">
                  <a-form-model-item
                    :label="dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_LVCc_34586d95`, '澄清内容'))"
                    prop="content"
                    required>
                    <a-textarea
                      v-model="formData.content"
                      :auto-size="{ minRows: 3, maxRows: 5 }"></a-textarea>
                  </a-form-model-item>
                </a-row>
              </div>
            </div>

            <div class="receiptPeople">
              <div class="receiptPeopleMsg">
                <titleCrtl>
                  {{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_ylLVH_ab5fa517`, '接收人信息')) }}

                  <template slot="right">
                    <a-button
                      @click="addInviteItem"
                      type="primary">{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增')) }}</a-button>
                  </template>
                </titleCrtl>
              </div>
              <listTable
                ref="receipterList"
                :fromSourceData="formData.purchaseTenderEvaClarificationSupplierList"
                :statictableColumns="receipterColumns"
                :showTablePage="false"
                :pageData="receipterData" />
            </div>

            <div
              class="attachment"
              style="margin-top: 5px">
              <div class="attachmentList">
                <titleCrtl>
                  <span>{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_title_attachment`, '附件')) }}</span>
                  <template slot="right">
                    <a-upload
                      v-if="formData.id"
                      name="file"
                      :multiple="true"
                      :showUploadList="false"
                      :action="uploadUrl"
                      :headers="uploadHeader"
                      :accept="accept"
                      :data="{ headId: formData.id, businessType: 'biddingPlatform', 'sourceNumber': currentEditRow.tenderProjectNumber || currentEditRow.id || '', actionRoutePath: '/BidEvaluationClarify/BidEvaluationClarifyList' }"
                      :beforeUpload="beforeUpload"
                      @change="handleUploadChange">
                      <a-button type="primary"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                      <!-- <a-button
                    @click="handleUploadChange"
                    type="primary"><a-icon type="upload" />上传附件</a-button> -->
                      <!-- <a-button
                    @click="receipterNew"
                    type="primary">批量删除</a-button>
                    <a-button
                    @click="receipterNew"
                    type="primary">批量下载</a-button> -->
                    </a-upload>
                    <a-button
                      v-else
                      type="primary"
                      @click="beforeUpload"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                  </template>
                </titleCrtl>
              </div>
              <listTable
                ref="attachmentList"
                :fromSourceData="formData.attachmentList"
                :statictableColumns="attachmentColumns"
                :showTablePage="false"
                :pageData="attachmentData" />
            </div>
          </a-form-model>
        </div>
        <div class="page-footer">
          <a-button
            @click="handleSave"
            type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
          <a-button
            @click="handleSubmit"
            type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_field_hG_a7849`, '发出') }}</a-button>
          <a-button @click="handleBack">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
        </div>
      </a-spin>
    </div>

    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      :isTree="true"
      @ok="fieldSelectOk" />
    <getDecryptSupplierModal
      ref="getDecryptSupplierModal"
      isEmit
      :isTree="true"
      @ok="fieldSelectOk" />
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { USER_INFO } from '@/store/mutation-types'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import getDecryptSupplierModal from './getDecryptSupplierModal'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import listTable from '../../BiddingHall/components/listTable'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    mixins: [baseMixins],
    components: {
        fieldSelectModal,
        getDecryptSupplierModal,
        listTable,
        titleCrtl
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        routeQuery: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: { span: 8 },
            wrapperCol: { span: 16 },
            confirmLoading: false,
            formData: {
                clarificationTitle: '',
                fileClarificationEndTime: '',
                content: ''
            },

            rules: {
                fileClarificationEndTime: [
                    {
                        required: true,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVyRKIxOLVW_bfed72ba`, '澄清截止时间不能为空!')
                    }
                ],
                clarificationTitle: [
                    {
                        required: true,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LVBDxOLVW_ce2de7ff`, '澄清标题不能为空！')
                    }
                ],
                content: [
                    {
                        required: true,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LVCcxOLVW_48de447c`, '澄清内容不能为空!')
                    }
                ]
            },
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            requestData: {
                detail: {
                    url: '/tender/clarification/purchaseTenderEvaClarificationHead/queryById',
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            url: {
                add: '/tender/clarification/purchaseTenderEvaClarificationHead/add',
                edit: '/tender/clarification/purchaseTenderEvaClarificationHead/edit',
                submit: '/tender/clarification/purchaseTenderEvaClarificationHead/publish'
            },

            // 接收人表格信息
            receipterData: {
                showOptColumn: true,
                optColumnList: [
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete
                    }
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            receipterColumns: [
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                    field: 'supplierName'
                    // 'width': 250
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_to_els_account`, '供应商ELS账号'),
                    field: 'supplierAccount'
                    // 'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    // width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],

            // 附件表格信息
            attachmentData: {
                showOptColumn: true,
                optColumnList: [
                    {
                        type: 'download',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '下载'),
                        clickFn: this.downloadEvent
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        clickFn: this.preViewEvent
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDeleteFile
                    }
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            attachmentColumns: [
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称'),
                    field: 'fileName'
                    // 'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人'),
                    field: 'uploadSubAccount'
                    // 'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                    field: 'uploadTime'
                    // 'width': 180
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    // width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ]
        }
    },
    computed: {},
    methods: {
        addInviteItem ({ pageConfig, groupCode }) {
            let url = '/tender/purchase/supplierTenderProjectMasterInfo/getDecryptSupplier'
            let columns = [
                { field: 'supplierAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierAccount`, '供应商ELS账号') },
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称') }
            ]
            console.log(this.$refs.receipterList.getTableData())
            let { fullData } = this.$refs.receipterList.getTableData()
            let ids = fullData.map((item) => item.supplierAccount)
            // 已选的不能在勾选
            let checkedConfig = {
                visibleMethod: ({ row }) => {
                    let flag = true
                    if (ids.includes(row.supplierAccount)) flag = false
                    return flag
                }
            }

            this.$refs.getDecryptSupplierModal.open(url, { subpackageId: this.currentEditRow.subpackageId, checkType: this.currentEditRow.checkType, processType: this.currentEditRow.processType, currentStep: this.currentEditRow.currentStep }, columns, 'multiple', checkedConfig)
        },
        fieldSelectOk (data) {
            // let itemGrid = this.getItemGridRef('supplierInviteReceiptList')
            let param = []
            var subpackage = {}
            var subpackageList = {}
            let ids = this.$refs.receipterList.getTableData().fullData.map((item) => item.supplierAccount)
            data.map((item) => {
                if (!ids.includes(item.supplierAccount)) {
                    subpackage['supplierName'] = item.supplierName
                    subpackage['supplierAccount'] = item.supplierAccount
                    subpackageList = { ...subpackage }
                    param.push({ ...subpackageList })
                }
            })
            this.$set(this.formData, 'purchaseTenderEvaClarificationSupplierList', this.formData.purchaseTenderEvaClarificationSupplierList ?? [])
            this.formData.purchaseTenderEvaClarificationSupplierList.push(...param)
            this.$refs.receipterList.insertAt(param, -1)
        },
        handleDelete (row) {
            this.formData.purchaseTenderEvaClarificationSupplierList.splice(row, 1)
            this.$refs.receipterList.removeRow(row)
        },
        handleDeleteFile (row) {
            this.formData.attachmentList.splice(row, 1)
            this.$refs.attachmentList.removeRow(row)
        },
        handleSave () {
            console.log(this.formData)
            this.formData.evaInfoId = this.evaInfoId
            let url = this.formData.id ? this.url.edit : this.url.add
            valiStringLength(this.formData, [
                {field: 'clarificationTitle', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题'), maxLength: 100},
                {field: 'content', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVCc_34586d95`, '澄清内容'), maxLength: 100}
            ])
            this.confirmLoading = true
            postAction(url, this.formData, {headers: {xNodeId: `${this.routeQuery.checkType}_${this.routeQuery.processType}_${this.routeQuery.currentStep}`}})
                .then((res) => {
                    if (res.success) {
                        if (!this.formData.id) {
                            this.$set(this.formData, 'id', res.result.id)
                            // inCurrentEditRow表示：当前行的id值
                            this.$set(this.currentEditRow, 'id', res.result.id)
                        }
                        this.$message.success(res.message)
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleSubmit () {
            this.formData.evaInfoId = this.evaInfoId
            valiStringLength(this.formData, [
                {field: 'clarificationTitle', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题'), maxLength: 100},
                {field: 'content', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVCc_34586d95`, '澄清内容'), maxLength: 100}
            ])
            this.confirmLoading = true
            postAction(this.url.submit, this.formData, {headers: {xNodeId: `${this.routeQuery.checkType}_${this.routeQuery.processType}_${this.routeQuery.currentStep}`}})
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.$emit('hide')
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleBack () {
            this.$emit('hide')
        },
        async queryDetail () {
            if (this.requestData.detail) {
                let url = this.requestData.detail.url
                let args = this.requestData.detail.args(this)
                console.log('args', args)
                // 有id 才能请求,新建时是没有id的，不用请求查询接口
                if (args && args.id) {
                    this.confirmLoading = true
                    let query = await getAction(url, args)
                    this.confirmLoading = false
                    if (query && query.success) {
                        console.log(query.result)
                        this.formData = Object.assign({}, query.result)
                        console.log('this.formData', this.formData)
                        this.nodeListData = this.formData.tenderEvaluationTemplateItemVoList
                    } else {
                        this.$message.error(query.message)
                    }
                }
            }
        },
        beforeUpload () {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return false
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if (file.status === 'done') {
                if (file.response.success) {
                    console.log(file.response.result)
                    let { fileName, filePath, fileSize, id, uploadSubAccount, uploadElsAccount, uploadTime } = file.response.result
                    let fileListData = {
                        uploadSubAccount,
                        uploadElsAccount,
                        uploadTime,
                        fileName,
                        filePath,
                        fileSize,
                        id
                    }
                    if (!this.formData.attachmentList) this.$set(this.formData, 'attachmentList', [])
                    this.formData.attachmentList.push(fileListData)
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            } else if (file.status === 'error') {
                this.formData.attachmentList = fileList.map((res) => {
                    let { fileName, filePath, fileSize, id, uploadSubAccount, uploadTime } = res.response.result
                    return {
                        name: fileName,
                        url: filePath,
                        uid: id,
                        fileName,
                        filePath,
                        fileSize,
                        id,
                        uploadSubAccount,
                        uploadTime
                    }
                })
            }
        },
        async downloadEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading=true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                this.confirmLoading=false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            this.$previewFile.open({ params: row })
        }
    },
    mounted () {},
    created () {
        this.subpackageId = this.$route.query.subpackageId
        this.evaInfoId = this.$route.query.evaInfoId
        this.queryDetail()
        // this.currentEditRow = this.$route.query.linkFilter.currentEditRow
    }
}
</script>
<style lang="less" scoped>
.deliveryTime-title {
    padding: 8px;
    background: #f2f3f5;
}
:deep(.textAreaClass .ant-form-item-control-wrapper ){
    width: 80%;
}
:deep(.textAreaClass .ant-form-item-label){
    width: 16.7%;
}
</style>
