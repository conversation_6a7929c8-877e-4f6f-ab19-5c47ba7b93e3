<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleContractAcceptanceEsignEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleContractAcceptanceEsignDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <a-modal
    v-drag    
      v-model="visible"
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reasonsRepealing`, '撤销原因')"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :layout="layout">
        <a-form-model-item :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reasonsRepealing`, '撤销原因')">
          <a-input
            v-model="form.reason" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleContractAcceptanceEsignEdit from './modules/SaleContractAcceptanceEsignEdit'
import SaleContractAcceptanceEsignDetail from './modules/SaleContractAcceptanceEsignDetail'
import { getAction, postAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        SaleContractAcceptanceEsignEdit,
        SaleContractAcceptanceEsignDetail
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            visible: false,
            rowIndex: -1,
            form: {
                reason: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCWWWyWRLWdDWESAo_950f1e9e`, '(供方ELS号/名称/主题/业务编码)')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esign#saleContractAcceptanceEsign:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esign#saleContractAcceptanceEsign:edit'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signingInitiation`, '一步签署发起'), clickFn: this.createFlowOneStep, allow: this.allowCreateFlowOneStep, authorityCode: 'esign#saleContractAcceptanceEsign:initiate'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processtoOpen`, '流程开启'), clickFn: this.startFlow, allow: this.allowStartFlow, authorityCode: 'esign#saleContractAcceptanceEsign:open'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_flowArchiving`, '流程归档'), clickFn: this.archiveFlow, allow: this.allowArchiveFlow, authorityCode: 'esign#saleContractAcceptanceEsign:archive'},
                    {type: 'query', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLmh_33814307`, '流程查询'), clickFn: this.queryFlow, allow: this.allowQueryFlow, authorityCode: 'esign#saleContractAcceptanceEsign:statusQuery'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_drawBack`, '退回'), clickFn: this.sendBack, allow: this.sendBackFlow, authorityCode: 'esign#saleContractAcceptanceEsign:back'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销'), clickFn: this.backout, allow: this.backoutFlow, authorityCode: 'esign#saleContractAcceptanceEsign:cancel'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentDownload`, '流程文档下载'), clickFn: this.flowFileDownload, allow: this.showFlowFileDownload, authorityCode: 'esign#saleContractAcceptanceEsign:down'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/esign/elsContractAcceptanceEsign/saleList',
                delete: '/esign/elsContractAcceptanceEsign/delete',
                createFlowOneStep: '/esign/elsContractAcceptanceEsign/createFlowOneStep',
                startFlow: '/esign/esignOperation/startFlow',
                archiveFlow: '/esign/elsContractAcceptanceEsign/archiveFlow',
                sendBack: '/esign/elsContractAcceptanceEsign/sendBack',
                backout: '/esign/elsContractAcceptanceEsign/backout',
                flowFileDownload: '/esign/elsContractAcceptanceEsign/signFileDownload',
                queryFlow: '/esign/elsContractAcceptanceEsign/flowQuery',
                columns: 'SaleContractAcceptanceEsignList'
            }
        }
    },
    methods: {
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        sendBackFlow (row){
            if(row.sendBack==='1'){
                return true
            }
            //未发起一步签署
            if(row.launch!=='1'){
                return false
            }
            //发起了一步签署，但已撤销
            if(row.launch==='1' && row.esignStatus==='3'){
                return false
            }
            return true
        },
        backoutFlow (row){
            if(row.purchaseEsignStatus==='1' && row.saleEsignStatus==='1'){
                return true
            }
            //已发起，已开启，并且处于签署中（未完成）
            if(row.launch==='1' && row.initiate==='1'&& row.esignStatus==='0'){
                return false
            }
            return true
        },
        allowDelete (row){
            if(row.uploaded==='1'){
                return true
            }
            return false
        },
        showFlowFileDownload (row){
            //流程已经发起
            if(row.launch==='1' && row.archiving==='1'){
                return false
            }
            return true
        },
        allowCreateFlowOneStep (row){
            if(row.onlineSealed!=='1'){
                return true
            }
            //已退回的单不可编辑
            if(row.sendBack==='1'){
                return true
            }
            //维护完成，没有发起
            if(row.signerVindicateStatus==='3' && row.launch!=='1'){
                return false
            }
            //撤销未退回
            if(row.esignStatus==='3' && row.sendBack!=='1'){
                return false
            }
            return true
        },
        allowStartFlow (row){
            //手动开启流程并且还未开启并且已经发起一步签署
            if(row.autoInitiate!=='1' && row.initiate!=='1' && row.launch==='1' && row.esignStatus==='0'){
                return false
            }
            return true
        },
        allowArchiveFlow (row){
            //流程完成，未归档，非自动归档
            if(row.purchaseEsignStatus==='1' && row.saleEsignStatus==='1' && row.archiving !=='1' && row.autoArchiving !==1){
                return false
            }
            return true
        },
        allowEdit (row){
            //已退回的单不可编辑
            if(row.sendBack==='1'){
                return true
            }
            if(row.signerVindicateStatus == '3' && row.onlineSealed!=='1' ){
                return true
            }
            //未发起，可编辑
            if(row.launch!=='1'){
                return false
            }
            //发起后撤销，可编辑
            if(row.launch ==='1' && row.esignStatus==='3'){
                return false
            }
            return true
        },
        createFlowOneStep (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmationStepSignInitiate`, '确认一步签署发起'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherToInitiateAStepSignature`, '是否发起一步签署?'),
                onOk: function () {
                    postAction(that.url.createFlowOneStep, row).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.launch_dictText = '是'
                            row.launch = '1'
                            row.esignStatus = '0'
                            row.esignStatus_dictText = '未完成'
                            row.flowId = res.result.flowId
                        }
                    })
                }
            })
        },
        startFlow (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmTheStartOfTheProcess`, '确认流程开启'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherToEnableTheProcess`, '是否确认开启流程?'),
                onOk: function () {
                    getAction(that.url.startFlow, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.initiate_dictText = '是'
                            row.initiate = '1'
                        }
                    })
                }
            })
        },
        flowFileDownload (row){
            getAction(this.url.flowFileDownload, {id: row.id}).then(res => {
                if(res.success){
                    window.open(res.result[0].fileUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        archiveFlow (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmProcessArchiving`, '确认流程归档'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheArchivingProcessIsConfirmed`, '是否确认归档流程?'),
                onOk: function () {
                    getAction(that.url.archiveFlow, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.archiving_dictText = '是'
                            row.archiving= '1'
                        }
                    })
                }
            })
        },
        sendBack (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmBack`, '确认退回'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmReturn`, '是否确认退回'),
                onOk: function () {
                    getAction(that.url.sendBack, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.sendBack_dictText = '是'
                            row.sendBack= '1'
                        }
                    })
                }
            })
        },
        backout (row, column, $rowIndex){
            this.rowIndex = $rowIndex
            this.visible = true
        },
        handleOk () {
            let param = this.$refs.listPage.tableData[this.rowIndex]
            if(!this.form.reason){
                this.$message.warning('撤销原因不能为空')
                return
            }
            getAction(this.url.backout, {id: param.id, reason: this.form.reason}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    param.esignStatus_dictText = '已撤销'
                    param.esignStatus= '3'
                }
            })
            this.visible = false
        },
        allowQueryFlow (row){
            if(row.launch==='1'){
                return false
            }
            return true
        },
        queryFlow (row){
            getAction(this.url.queryFlow, {id: row.id}).then(res => {
                if(res.success){
                    this.$message.info(res.result.flowDesc)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>