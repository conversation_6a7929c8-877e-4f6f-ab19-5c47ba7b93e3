<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :current-edit-row="currentEditRow"
      refresh
      :page-data="pageData"
      :url="url"
      @cell-click="cellClick"/>
    <!-- 行明细弹出选择框 -->
    <field-select-modal
      ref="fieldSelectModal"/>
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
  </div>
</template>

<script>
import {getAction, postAction} from '@/api/manage'
import {EditMixin} from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'

export default {
    name: 'PurchaseRefundsDeliveryHeadModal',
    components: {
        fieldSelectModal
    },
    mixins: [EditMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            selectType: 'material',
            flowId: 0,
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnLineInfo`, '退货行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseRefundsDeliveryItemList',
                            columns: [],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                                    type: 'primary',
                                    click: this.insertGridItem
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteGridItem
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                                    key: 'fillDown',
                                    type: 'tool-fill',
                                    beforeCheckedCallBack: this.fillDownGridItem
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachmentList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 180
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'uploadSubAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                                    width: 120
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                    type: 'upload',
                                    businessType: 'refundsDelivery',
                                    attr: this.attrHandle,
                                    callBack: this.uploadCallBack
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                                    click: this.deleteBatch
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    clickFn: this.downloadEvent
                                },
                                {
                                    type: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preViewEvent
                                },
                                {
                                    type: 'delete',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    clickFn: this.deleteFilesEvent
                                }
                            ]
                        }
                    }

                ],
                formFields: [],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        type: 'primary',
                        click: this.saveEvent,
                        showCondition: this.showEditConditionBtn,
                        authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:edit'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_examineApprove`, '审批'),
                        type: 'primary',
                        click: this.submitAudit,
                        showCondition: this.showAuditConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                        type: 'primary',
                        click: this.publish,
                        key: 'submit',
                        authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:publish'
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                add: '/delivery/purchaseRefundsDeliveryHead/add',
                edit: '/delivery/purchaseRefundsDeliveryHead/edit',
                detail: '/delivery/purchaseRefundsDeliveryHead/queryById',
                voucherToRefundsItem: '/delivery/purchaseRefundsDeliveryHead/voucherItemToRefundsItem',
                publish: '/delivery/purchaseRefundsDeliveryHead/publish',
                submitAudit: '/a1bpmn/audit/api/submit',
                upload: '/attachment/purchaseAttachment/upload'
            },
            fieldSelectType: null,
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_refundsDelivery_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.refundsDeliveryNumber,
                actionRoutePath: '/srm/delivery/purchase/PurchaseRefundsDeliveryHeadList,/srm/delivery/sale/SaleRefundsDeliveryHeadList'
            }
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        },
        //新增行
        insertGridItem () {
            let that = this;

            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (params.toElsAccount == '') {
                this.$message.warning('请选择供应商！')
                return
            }
            if (params.purchaseOrg == '') {
                this.$message.warning('请选择采购组织！')
                return
            }
            if (params.elsAccount == params.toElsAccount) {
                if (params.toCompany == '') {
                    this.$message.warning('公司内部单据，请选择供应商公司代码')
                    return
                }
            }
            if (params.storageLocation == '') {
                this.$message.warning('请选择库存地点！')
                return
            }

            let parm = {
                toElsAccount: params.toElsAccount,
                voucherStatus: '0',
                loanDirection: '+',
                purchaseOrg: params.purchaseOrg
            }
            if (params.purchaseRefundsDeliveryItemList.length > 0) {

                // 检查头和行的库存地点是否相同
                params.purchaseRefundsDeliveryItemList.forEach(item => {
                    if (params.storageLocation != item.storageLocation) {
                        that.$message.error("头库存地点和行不一致，请调整")
                    }
                })

                parm['factory'] = params.purchaseRefundsDeliveryItemList[0].factory
                if (params.purchaseRefundsDeliveryItemList[0].sourceType == 'orderItem') {
                    parm['sourceType'] = params.purchaseRefundsDeliveryItemList[0].sourceType
                }
                parm['storageLocation'] = params.purchaseRefundsDeliveryItemList[0].storageLocation
            } else {
                parm['storageLocation'] = params.storageLocation
            }

            if (params.toCompany) {
                parm['toCompany'] = params.toCompany
            }
            let url = '/delivery/purchaseVoucherHead/queryRefundDeliveryList'
            let columns = [
                {
                    field: 'voucherNumber',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voucherNo`, '凭证单号'),
                    width: 150
                },
                {
                    field: 'voucherDate',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voucherDate`, '凭证日期'),
                    width: 150
                },
                {
                    field: 'orderNumber',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                    width: 150
                },
                {
                    field: 'orderItemNumber',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                    width: 80
                },
                {
                    field: 'materialNumber',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'),
                    width: 80
                },
                {
                    field: 'materialDesc',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                    width: 80
                },
                {
                    field: 'materialName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
                    width: 80
                },
                {
                    field: 'materialSpec',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),
                    width: 80
                },
                {
                    field: 'materialGroup',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                    width: 80
                },
                {
                    field: 'materialGroupName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroupName`, '物料组名称'),
                    width: 80
                },
                {
                    field: 'cateCode',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cateCode`, '物料分类'),
                    width: 80
                },
                {
                    field: 'cateName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cateName`, '物料分类名称'),
                    width: 80
                },
                {
                    field: 'batchNumber',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_batchNumber`, '批次号'),
                    width: 80
                },
                {
                    field: 'requireDate',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_requiredDeliveryDate`, '要求交期'),
                    width: 100
                },
                {
                    field: 'quantity',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'),
                    width: 100
                },
                {
                    field: 'factory_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_factory`, '工厂'),
                    width: 100
                },
                {
                    field: 'storageLocation_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocation`, '库存地点'),
                    width: 100
                },
                {
                    field: 'voucherQuantity',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_factory`, '凭证数量'),
                    width: 100
                },
                {
                    field: 'refund',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qYSWR_e25aee35`, '可退货数量'),
                    width: 100
                }
            ]
            this.fieldSelectType = 'insertGridItem'
            this.$refs.fieldSelectModal.open(url, parm, columns, 'multiple')
        },
        // 行点击
        cellClick({ row, rowIndex, column, columnIndex }) {
            let form = this.$refs.editPage ? this.$refs.editPage.form : {}
            console.log("行点击", { row, rowIndex, column, columnIndex, form })
            // 点击了【批次号】
            if(column.field === 'batchNumber') {
                let url = '/delivery/purchaseVoucherHead/queryRefundDeliveryListByBatchNumber';
                let params = {
                    toElsAccount: form.toElsAccount,    //供应商ELS账号
                    company: form.company, // 结算组织，必传
                    purchaseOrg: form.purchaseOrg,   //采购组织
                    factory: row.factory,     //工厂代码
                    storageLocation: form.storageLocation,   // 库存地点代码，必传
                    materialNumber: row.materialNumber     //物料编码，必传
                    // orderItemId: row.orderItemId,      //订单行id  （从点击批次的行拿到订单行id）
                    // // batchNumber: row.batchNumber,    //批次号    （从点击批次的行拿到批次号）
                    // batchNumber: '********',
                }
                let columns = [
                    {
                        field: 'voucherNumber',
                        title: '收货凭证单号',
                        width: 150
                    },
                    {
                        field: 'itemNumber',
                        title: '收货凭证单行号',
                        width: 150
                    },
                    {
                        field: 'batchNumber',
                        title: '批次号',
                        width: 150
                    },
                    {
                        field: 'quantity',
                        title: '凭证收货数量',
                        width: 150
                    },
                    {
                        field: 'sourceType_dictText',
                        title: '来源类型',
                        width: 150
                    },
                ]
                this.fieldSelectType = `updateItem_${rowIndex}`
                this.$refs.fieldSelectModal.open(url, params, columns, 'single');
            }
        },
        fieldSelectOk (data) {
            console.log("选择的数据", data);
            if(this.fieldSelectType === 'insertGridItem') {
                let itemGrid = this.$refs.editPage.$refs.purchaseRefundsDeliveryItemList[0]
                let {fullData} = itemGrid.getTableData()
                let materialList = fullData.map(item => {
                    return item.sourceItemId
                })
                //过滤已有数据
                let insertData = data.filter(item => {
                    return !materialList.includes(item.id)
                })
                let that = this
                postAction(that.url.voucherToRefundsItem, insertData).then(res => {
                    if (res.success) {
                        itemGrid.insertAt(res.result, -1)
                    } else {
                        that.$message.warning(res.message)
                    }
                })
            } else if(this.fieldSelectType.includes('updateItem')) {
                let rowIndex = this.fieldSelectType.split('_')[1];
                let itemGrid = this.$refs.editPage.$refs.purchaseRefundsDeliveryItemList[0]
                let {fullData} = itemGrid.getTableData();
                let checkItem = data[0]
                fullData[rowIndex] = {
                    ...fullData[rowIndex],
                    voucherNumber: checkItem.voucherNumber, //收货凭证单号
                    voucherItemNumber: checkItem.itemNumber, //收货凭证单行号
                    voucherId: checkItem.headId, //收货凭证单id
                    voucherItemId: checkItem.id, //收货凭证行id
                    receiveQuantity: checkItem.quantity, //凭证收货数量
                    sourceType: checkItem.sourceType, //来源类型
                    sourceType_dictText: checkItem.sourceType_dictText, //来源类型
                    sourceTypeId: checkItem.sourceTypeId, //来源类型Id
                    sourceNumber: checkItem.voucherNumber, //来源单号（实际上是收货凭证单号）
                    sourceItemNumber: checkItem.itemNumber, //来源单行号（实际上是收货凭证单行号）
                    sourceId: checkItem.headId, //来源单id（实际上是收货凭证单id）
                    sourceItemId: checkItem.id, //来源单行id（实际上是收货凭证行id）
                    orderNumber: checkItem.orderNumber, //订单号 对应字段 orderNumber
                    orderItemNumber: checkItem.orderItemNumber, //订单行号 对应字段 orderItemNumber
                    orderId: checkItem.orderId, //订单头id   对应字段 orderId
                    orderItemId: checkItem.orderItemId, //订单行id 对应字段 orderItemId
                    batchNumber: checkItem.batchNumber, //批次号   对应字段 batchNumber
                    overTolerance: checkItem.overTolerance //超量容差率  对应字段 overTolerance
                }
                itemGrid.loadData(fullData) // 更新数据
            }
            this.fieldSelectType = null;
        },
        //删除复选框选定行
        deleteGridItem () {
            let itemGrid = this.$refs.editPage.$refs.purchaseRefundsDeliveryItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning('请选择数据！')
                return
            }
            itemGrid.removeCheckboxRow()
        },
        showPublishConditionBtn () {
            if (!this.$hasOptAuth('refundsDelivery#purchaseRefundsDeliveryHead:publish')) {
                return true
            }
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let auditStatus = params.auditStatus
            let refundsDeliveryStatus = params.refundsDeliveryStatus
            let audit = params.audit
            if (((audit === '1' && auditStatus === '2') || auditStatus === '4') && (parseFloat(refundsDeliveryStatus) == 0 || parseFloat(refundsDeliveryStatus) == 3)) {
                return true
            } else {
                return false
            }
        },
        showEditConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus === '2' || auditStatus === '1') {
                return false
            } else {
                return true
            }
        },
        showAuditConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let auditStatus = params.auditStatus
            let audit = params.audit
            if (audit != '1') {
                return false
            } else {
                if (auditStatus === '0' || auditStatus === '3') {
                    return true
                }
                return false
            }
        },
        submitAudit () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if ((!params.id)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }
            const _this = this
            const fn = (data, vm) => {
                console.log('data :>> ', data)
                console.log('vm :>> ', vm) // 编辑模板组件实例
                const param = {
                    businessId: data.id,
                    rootProcessInstanceId: data.flowId || '',
                    businessType: 'refundsDelivery',
                    auditSubject: `退货通知单号：${data.refundsDeliveryNumber}`,
                    params: JSON.stringify(data)
                }
                postAction('/a1bpmn/audit/api/submit', param).then(res => {
                    const type = res.success ? 'success' : 'error'
                    _this.$message[type](res.message)
                    //_this.goBack()
                    _this.$parent.submitCallBack(data)
                })

            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saveAndApprove`, '保存并审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterSavingCannotModifiedSureSaveSubmitForApproval`, '保存提交审批后将不能修改，是否确认保存并提交审批?'),
                onOk () {
                    _this.$refs.editPage.handleSend('', fn)
                },
                onCancel () {
                    console.log('onCancel')
                }
            })

        },
        goBack () {
            this.$emit('hide')
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        /*deleteFilesEvent () {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning('请选择数据！')
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },*/
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        deleteBatch () {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        publish () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (params.purchaseRefundsDeliveryItemList.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
                return false
            } else {
                let i = 1
                for (let item of params.purchaseRefundsDeliveryItemList) {
                    const regTelephone = /^((13|14|15|16|17|18)[0-9]{1}\d{8})$/
                    if (item.refundsPrincipalPhone && !regTelephone.test(item.refundsPrincipalPhone)) {
                        this.$message.warning('联系人信息中第' + i + '手机号格式不正确')
                        return false
                    }
                    const regEmail = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
                    if (item.refundsPrincipalMail && !regEmail.test(item.refundsPrincipalMail)) {
                        this.$message.warning('联系人信息中第' + i + '邮箱格式不正确')
                        return false
                    }
                    if (!item.refundsQuantity || item.refundsQuantity <= 0) {
                        this.$message.warning('退货行' + i + '本次退货数量必填，并且大于0')
                        return false
                    }
                    i++
                }
            }
            let that = this
            const fn = (url, params, vm) => {
                console.log('vm :>> ', vm) // 编辑模板组件实例
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmReturn`, '确认退货'),
                    content: '此操作将退货信息发送供应商，是否确认发送?',
                    onOk: function () {
                        that.$refs.editPage.confirmLoading = true
                        postAction(that.url.publish, params).then(res => {
                            if (res.success) {
                                that.$refs.editPage.confirmLoading = false
                                that.$message.success(res.message)
                                that.goBack()
                            } else {
                                that.$message.warning(res.message)
                            }
                        }).finally(() => {
                            that.$refs.editPage.confirmLoading = false
                        })
                    }
                })
            }
            this.$refs.editPage.handValidate(that.url.publish, params, fn)
        },
        saveEvent () {
            let data = this.$refs.editPage.$refs.purchaseRefundsDeliveryItemList[0].getTableData().fullData
            if (!data || data.length == 0) {
                this.$message.warning('至少添加一个收货行项目')
                return
            } else {
                let i = 1
                for (let item of data) {
                    const regTelephone = /^((13|14|15|16|17|18)[0-9]{1}\d{8})$/
                    if (item.refundsPrincipalPhone && !regTelephone.test(item.refundsPrincipalPhone)) {
                        this.$message.warning('联系人信息中第' + i + '手机号格式不正确')
                        return
                    }
                    const regEmail = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
                    if (item.refundsPrincipalMail && !regEmail.test(item.refundsPrincipalMail)) {
                        this.$message.warning('联系人信息中第' + i + '邮箱格式不正确')
                        return
                    }
                    i++
                }
            }

            this.$refs.editPage.postData()
        }
    }
}
</script>
