


let CHAR_CONFIG={
    /*
     data: [
        { value: 1048, name: 'Search Engine' },
        { value: 735, name: 'Direct' },
        { value: 580, name: '<PERSON><PERSON>' },
        { value: 484, name: 'Union Ads' },
        { value: 300, name: 'Video Ads' }
      ]
     */
    'pie': {//饼图  只需data
        'series': {
            'type': 'pie',
            'data': []
        }
    },
    /*
    例子：option = {
        xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
            }
        ]
        };
     */
    'line': {//折线图 xAxis data  series data
        'xAxis': {
            'type': 'category',
            'data': []
        },
        'yAxis': {
            'type': 'value'
        },
        'series': [{
            'name': '',
            'data': [],
            'type': 'line',
            'smooth': true
        }]
    },
    /*
    option = {
        xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
            data: [120, 200, 150, 80, 70, 110, 130],
            type: 'bar'
            }
        ]
        };
    */
    'bar': {//柱状图  xAxis data  series data
        'xAxis': {
            'type': 'category',
            'data': []
        },
        'yAxis': {
            'type': 'value'
        },
        'series': [{
            'data': [],
            'type': 'bar'
        }]
    },
    /*
    option = {
        legend: {},
        tooltip: {},
        dataset: {
            source: [
            ['product', '2015', '2016', '2017'],
            ['Matcha Latte', 43.3, 85.8, 93.7],
            ['Milk Tea', 83.1, 73.4, 55.1],
            ['Cheese Cocoa', 86.4, 65.2, 82.5],
            ['Walnut Brownie', 72.4, 53.9, 39.1]
            ]
        },
        xAxis: { type: 'category' },
        yAxis: {},
        // Declare several bar series, each will be mapped
        // to a column of dataset.source by default.
        series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
        };
    */
    'bars': {//多维柱状图 dataset
        'legend': {},
        'tooltip': {},
        'dataset': {
            'dimensions': [],
            'source': []
        },
        'xAxis': {'type': 'category'},
        'yAxis': {},
        'series': [{ 'type': 'bar' }, { 'type': 'bar' }, { 'type': 'bar' }]
    },
    /*
    
    option = {

            toolbox: {
                feature: {
                dataView: { show: true, readOnly: false },
                magicType: { show: true, type: ['line', 'bar'] },
                restore: { show: true },
                saveAsImage: { show: true }
                }
            },
            legend: {
                data: ['Evaporation', 'Precipitation', 'Temperature']
            },
            xAxis: [
                {
                type: 'category',
                data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                axisPointer: {
                    type: 'shadow'
                }
                }
            ],
            yAxis: [
                {
                type: 'value',
                name: 'Precipitation',

                axisLabel: {
                    formatter: '{value} ml'
                }
                },
                {
                type: 'value',
                name: 'Temperature',
                axisLabel: {
                    formatter: '{value} °C'
                }
                }
            ],
            series: [
                {
                name: 'Evaporation',
                type: 'bar',

                data: [
                    2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
                ]
                },
                {
                name: 'Precipitation',
                type: 'bar',

                data: [
                    2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
                ]
                },
                {
                name: 'Temperature',
                type: 'line',
                yAxisIndex: 1,
                data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2]
                }
            ]
        };
    */
    'lineBar': {//柱状+折线
        'legend': {},
        'tooltip': {},
        'xAxis': {
            'type': 'category',
            'data': []
        },
        'yAxis': {
            'type': 'value'
        },
        'series': [
            {
                'name': '',
                'data': [],
                'type': 'bar'
            },
            {
                'name': '',
                'data': [],
                'type': 'line'
            }
        ]
    },
    /*
    option = {
  xAxis: {},
  yAxis: {},
  series: [
    {
      symbolSize: 20,
      data: [
        [10.0, 8.04],
        [8.07, 6.95],
        [13.0, 7.58],
        [9.05, 8.81],
        [11.0, 8.33],
        [14.0, 7.66],
        [13.4, 6.81],
        [10.0, 6.33],
        [14.0, 8.96],
        [12.5, 6.82],
        [9.15, 7.2],
        [11.5, 7.2],
        [3.03, 4.23],
        [12.2, 7.83],
        [2.02, 4.47],
        [1.05, 3.33],
        [4.05, 4.96],
        [6.03, 7.24],
        [12.0, 6.26],
        [12.0, 8.84],
        [7.08, 5.82],
        [5.02, 5.68]
      ],
      type: 'scatter'
    }
  ]
};
    */
    'scatter': {//散点
        'xAxis': {},
        'yAxis': {},
        'series': [
            {
                'symbolSize': 10,
                'data': [],
                'type': 'scatter'
            }
        ]
    },
    'list': [
        {'type': 'seq', 'width': '50', 'title': '序号'},
        {'field': 'keyName', 'title': 'titleName'}
    ],
    'map': {
        backgroundColor: '#404a59',
        tooltip: {
            trigger: 'item'
        },
        geo: {
            show: true,
            map: 'china',
            label: {
                normal: {
                    show: true,
                    color: '#fff'
                },
                emphasis: {
                    show: true,
                    color: '#fff'
                }
            },
            roam: true,
            itemStyle: {
                normal: {
                    areaColor: '#031525',
                    borderColor: '#3B5077'
                },
                emphasis: {
                    areaColor: '#2B91B7'
                }
            }
        }, 
        series: [
            {
                name: '',
                type: 'scatter',
                coordinateSystem: 'geo',
                symbol: 'pin',
                data: [],
                symbolSize: 20,
                encode: {
                    value: 2
                },
                label: {
                    formatter: (val) => {
                        return val.name
                    },
                    position: 'right',
                    show: false
                },
                emphasis: {
                    label: {
                        show: true
                    }
                },
                itemStyle: {
                    normal: {
                        color: '#ddb926'
                    }
                }
            },
            {
                name: '主要分布',
                type: 'effectScatter',
  
                coordinateSystem: 'geo',
  
                rippleEffect: {
                    color: '#e43961',
                    brushType: 'stroke',
                    number: 2
                },
                data: [],
                symbolSize: 30,
                encode: {
                    value: 2
                },
                showEffectOn: 'render',
  
                label: {
                    formatter: '{b}',
                    position: 'right',
                    show: true
                },
                itemStyle: {
                    normal: {
                        color: '#ddb926'
                    }
                },
                zlevel: 10
            }

        ]
    }
}

export {
    CHAR_CONFIG
}