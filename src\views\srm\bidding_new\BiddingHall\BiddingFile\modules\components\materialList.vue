<template>
  <div
    v-if="currentRow"
    class="materialList">
    <a-tabs
      :activeKey="activeKey"
      @change="callback">
      <a-tab-pane
        v-for="(tabItem, index) in currentRow.quoteColumnList"
        :key="index + ''"
        :tab="tabItem.title">
      </a-tab-pane>
      <template
        slot="tabBarExtraContent"
      >
        <a-button
          v-if="isEdit"
          type="primary"
          size="small"
          @click="handleAddMaterial">{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}</a-button>
        <a-button
          v-if="isEdit"
          type="danger"
          size="small"
          style="margin-left: 10px"
          @click="handleDelectMaterial">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
        <a-button
          size="small"
          style="margin-left: 10px"
          @click="back">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-tabs>
    <listTable
      ref="listTable"
      v-if="showListTable"
      setGridHeight="auto"
      :pageStatus="'detail'"
      :fromSourceData="currentRow.quoteColumnList[activeKey].materialDataList || []"
      :showTablePage="false"
      :statictableColumns="statictableColumns"></listTable>
    <addColumnModal
      ref="addColumnModal"
      isEmit
      @ok="selectedOk"></addColumnModal>
  </div>
</template>
<script>
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@/views/srm/bidding_new/BiddingHall/components/title-crtl'
import addColumnModal from './addColumnModal'
export default {
    inject: {
        tenderCurrentRow: {
            from: 'tenderCurrentRow',
            default: null
        }
    },
    props: {
        currentRow: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: 'edit',
            type: String
        },
        formData: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    components: {
        listTable,
        titleTrtl,
        addColumnModal
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        }
    },
    data () {
        return {
            activeKey: '0',
            showListTable: true,
            currentQuoteItem: {},
            statictableColumns: [
                { 'type': 'checkbox', 'width': 50, fixed: 'left' },
                { 'type': 'seq', 'width': 50, 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), fixed: 'left' },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_printerBrand`, '品牌'), 'field': 'brand', width: 100, fixed: 'left'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_materialCode`, '物料编码'), 'field': 'materialNumber', width: 120, fixed: 'left' },
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 120, fixed: 'left'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), 'field': 'materialDesc', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialModel`, '物料型号'), 'field': 'materialModel', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), 'field': 'materialSpec', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), 'field': 'purchaseCycle', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseType`, '采购类型'), 'field': 'purchaseType_dictText', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireDate`, '需求日期'), 'field': 'requireDate', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireQuantity`, '需求数量'), 'field': 'requireQuantity', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), 'field': 'purchaseUnit_dictText', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税价'), 'field': 'price', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netPrice`, '净价'), 'field': 'netPrice', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxCode`, '税码'), 'field': 'taxCode', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxRate`, '税率'), 'field': 'taxRate', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_targetPrice`, '目标价'), 'field': 'targetPrice', width: 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), 'field': 'currency', 'dictCode': 'srmCurrency', enabled: false, fieldType: 'select', width: 120}
            ]
        }
    },
    
    methods: {
        handleDelectMaterial () {
            let checkboxRecords = this.$refs.listTable.getCheckboxRecords()
            if (checkboxRecords.length < 1) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            let ids = checkboxRecords.map(item => item.id)
            let materialDataList = this.currentRow.quoteColumnList[this.activeKey].materialDataList.filter(item => {
                return !ids.includes(item.id)
            })
            this.$emit('handleDelectMaterial', {activeKey: this.activeKey, data: materialDataList, checkboxRecords})
        },
        back () {
            this.$emit('back')
        },
        callback (v) {
            this.activeKey = v
        },
        handleAddMaterial () {
            // 获取已经选择物料列
            let checkedConfig = {
                // this.tenderCurrentRow.id
                checkMethod: ({ row }) => {
                    let materialIds = this.currentRow.quoteColumnList[this.activeKey].materialDataList.map(item => item.materialId)
                    if (materialIds.includes(row.materialId)) return false
                    return true
                }
            }
            // 暂时使用当前物料接口（有数据）
            let url = '/tender/purchaseTenderProjectHead/material/list'
            let columns = [
                {
                    field: 'cateCode',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
                    width: 150
                },
                {
                    field: 'cateName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
                    width: 150
                },
                {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150},
                {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150},
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200},
                {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200}
            ]
            this.$refs.addColumnModal.open(url, {headId: this.tenderCurrentRow.id}, columns, 'multiple', checkedConfig, 'material')
        },
        reloadData () {
            this.$refs.listTable.reloadData(this.currentRow.quoteColumnList[this.activeKey].materialDataList)
        },
        selectedOk (data) {
            this.$emit('addMaterialList', {activeKey: this.activeKey, data})
            
        }
    },
    created () {
    }
}
</script>

