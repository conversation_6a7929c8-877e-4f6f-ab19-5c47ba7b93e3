<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <sale-barcode-info-head-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <sale-barcode-info-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>
import SaleBarcodeInfoHeadDetail from './modules/SaleBarcodeInfoHeadDetail'
import { ListMixin } from '@comp/template/list/ListMixin'
import { postAction } from '@/api/manage'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        SaleBarcodeInfoHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeInfo',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNtFAyLFAy_2d54c7c2`, '请输入单据编号/规则编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'barcode#saleInfo:detail',
                        clickFn: this.handleView
                    },
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/base/barcode/saleBarcodeInfoHead/list',
                add: '/base/barcode/saleBarcodeInfoHead/add',
                delete: '/base/barcode/saleBarcodeInfoHead/delete',
                deleteBatch: '/base/barcode/saleBarcodeInfoHead/deleteBatch',
                exportXlsUrl: '/base/barcode/saleBarcodeInfoHead/exportXls',
                importExcelUrl: '/base/barcode/saleBarcodeInfoHead/importExcel',
                columns: 'saleBarcodeInfoHeadList'
            }
        }
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.infoNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'SaleBarcodeInfo', url: this.url || '', recordNumber})
        },
        handleResultRecord (row) {
            this.currentEditRow = row
            this.showResultPage = true
            this.showEditPage = false
            this.showDetailPage = false
        },
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        showEdit (row) {
            return row.ruleStatus == 'new' ? false : true
        },
        showEnabled (row) {
            return row.ruleStatus == 'disabled' ? false : true
        },
        showDisabled (row) {
            return row.ruleStatus == 'enabled' ? false : true
        },
        handleEnabled (row) {
            let that = this
            const params = { id: row.id, statusType: '1' }
            postAction(this.url.changeStatus, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_AjLR_28088728`, '启用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        handleDisabled (row) {
            let that = this
            const params = { id: row.id, statusType: '2' }
            postAction(this.url.changeStatus, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_HjLR_38ff8896`, '禁用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        }
    }
}
</script>