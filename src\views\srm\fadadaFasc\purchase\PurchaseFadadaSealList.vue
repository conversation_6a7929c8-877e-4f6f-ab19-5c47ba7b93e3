<template>
  <div
    style="height:100%"
    class="PurchaseFadadaSealList">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showFreeSignPage"
      ref="listPage"
      modelLayout="seal"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <EditPurchaseFadadaSeal-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideEditPage" />
    <ViewPurchaseFadadaSeal-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <EditPurchaseFadadaSealModalFreeSign
      v-if="showFreeSignPage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import EditPurchaseFadadaSealModal from './modules/EditPurchaseFadadaSealModal'
import ViewPurchaseFadadaSealModal from './modules/ViewPurchaseFadadaSealModal'
import EditPurchaseFadadaSealModalFreeSign from './modules/EditPurchaseFadadaSealModalFreeSign'

import {ListMixin} from '../list/ListMixin'
import {httpAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EditPurchaseFadadaSealModal,
        ViewPurchaseFadadaSealModal,
        EditPurchaseFadadaSealModalFreeSign
    },
    data () {
        return {
            showEditPage: false,
            showFreeSignPage: false,
            pageData: {
                businessType: 'fadada',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_sMURgjgo`, '印章别名'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_sMURgjgo`, '印章别名')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'fadada#purchaseFadadaSeal:add', icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    // {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'fadada#purchaseFadadaSeal:view', clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'fadada#purchaseFadadaSeal:edit', clickFn: this.handleEdit, allow: this.showEditCondition},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disable`, '禁用'), authorityCode: 'fadada#purchaseFadadaSeal:status', clickFn: this.handleDisable, allow: this.showDisableCondition},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enable`, '启用'), authorityCode: 'fadada#purchaseFadadaSeal:status', clickFn: this.handleEnable, allow: this.showEnableCondition},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_vALP_2c759940`, '开启免签'), authorityCode: 'fadada#purchaseFadadaSeal:freeSign', clickFn: this.handlefreeSign, allow: this.showfreeSignCondition},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'fadada#purchaseFadadaSeal:delete', clickFn: this.handleDelete, allow: this.showDelCondition}
                    // ,
                    // {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 300,
                showGridLayoutBtn: false
            },
            url: {
                list: '/electronsign/fadada/purchaseFadadaSeal/list',
                add: '/electronsign/fadada/purchaseFadadaSeal/add',
                delete: '/electronsign/fadada/purchaseFadadaSeal/delete',
                disable: '/electronsign/fadada/purchaseFadadaSeal/disable',
                enable: '/electronsign/fadada/purchaseFadadaSeal/enable',
                columns: 'purchaseFadadaSealList'
            }
        }
    },
    methods: {
        // 返回按钮
        hideEditPage () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
            this.showEditPage = false
            this.showDetailPage = false
            this.showFreeSignPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        handleChidCallback (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showFreeSignPage = false
            this.showDetailPage = true
        },
        handlefreeSign (row){
            debugger
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = false
            this.showFreeSignPage = true
        },
        handleAdd () {          
            this.currentEditRow = {}
            this.showDetailPage = false
            this.showFreeSignPage = false
            this.showEditPage = true
        },
        handleView (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showFreeSignPage = false
            this.showDetailPage = true
        },
        showEnableCondition (row){
            if(row.sealStatus==='disable'){
                return false
            }
            return true
        },
        showDisableCondition (row){
            if(row.sealStatus==='enable'){
                return false
            }
            return true
        },
        showfreeSignCondition (row){
            if(row.sealStatus==='enable' && row.visaFree!='1'){
                return false
            }
            return true
        },
        showEditCondition (row) {
            if(row.sealStatus==='enable' || row.sealStatus==='disable'){
                return true
            }
            return false
        },
        showDelCondition (row) {
            if(!row.sealStatus || row.sealStatus =='' || row.sealStatus==='disable'){
                return false
            }
            return true
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showFreeSignPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (){
            this.showDetailPage = false
            this.showFreeSignPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        handleDisable (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disable`, '禁用'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLKQHj_b8387dcb`, '确认是否禁用？'),
                onOk: function () {
                    let param = {
                        id: row.id
                    }
                    that.postUpdateData(that.url.disable, param)
                }
            })
        },
        handleEnable (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enable`, '启用'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLKQAj_b7ac64f9`, '确认是否启用？'),
                onOk: function () {
                    let param = {
                        id: row.id
                    }
                    that.postUpdateData(that.url.enable, param)
                }
            })
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'get').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls('法大大印章（采方）')
        }
    }
}
</script>
<style lang="less" scoped>
.PurchaseFadadaSealList {
    .page-container {
        :deep(.grid-box){
            overflow: auto;
            overflow-x: hidden;
            overflow-y: auto;
        }
    }
}
</style>