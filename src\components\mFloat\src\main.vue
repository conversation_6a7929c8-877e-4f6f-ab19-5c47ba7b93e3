<template>
  <div class="MFloat">
    <span>
      <a-input
        readOnly
        v-show="!showFloat"
        :value="realValue | formatFloat(configData.dataFormat)"
        @click="focusFn">
      </a-input>
      <a-input-number
        style="width:100%"
        v-show="showFloat"
        v-bind="configData.extend || {}"
        ref="number"
        @blur="() => {showFloat = false}"
        :value="realValue"
        @change="changeInputValue"
        :placeholder="placeholder"
      ></a-input-number>
    </span>
  </div>
</template>

<script>
import { formatFloat } from '@/filters'
import { debounce } from 'lodash'

export default {
    name: 'MFloat',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        placeholder: {
            type: String,
            default: ''
        },
        configData: {
            type: Object,
            default () {
                return {}
            }
        },
        disabled: {
            type: Boolean,
            default: false
        },
        value: {
            type: [Number, String],
            default: null
        }
    },
    filters: {
        formatFloat
    },
    data () {
        return {
            showFloat: false,
            realValue: this.value
        }
    },
    watch: {
        value (val) {
            this.realValue = val
        }
    },
    methods: {
        focusFn () {
            // 置灰状态不切换
            if (this.disabled) {
                return
            }
            this.showFloat = true
            this.$nextTick(() => {
                this.$refs.number.focus()
            })
        },
        changeInputValue: debounce(function (val) {
            this.$emit('change', val)
        }, 200)
    }
}
</script>
