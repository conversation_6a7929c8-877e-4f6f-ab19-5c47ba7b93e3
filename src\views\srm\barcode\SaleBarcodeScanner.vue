<template>
  <div class="barcode-scanner">
    <a-form-model
      layout="inline"
      :model="form"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-row :gutter="[12,16]">
        <a-col :span="8">
          <a-form-model-item :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yKiN_40e0b356`, '解释策略')">
            <a-select
              v-model="form.explainStrategy"
              @change="strategyChange"
              :placeholder="$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelect`, '请选择')">
              <a-select-option
                :value="el.value"
                :key="el.value"
                v-for="el of explainStrategyOption">
                {{ el.text }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_KQGRMM_41c01642`, '是否设置回写')">
            <a-switch
              v-model="form.saveOperaRecord"
              :disabled="recordDisabled"/>
          </a-form-model-item>
        </a-col>
        <!-- <a-col :span="8">
          <a-form-model-item label="是否加密条码">
            <a-switch v-model="form.encryption" />
          </a-form-model-item>
        </a-col> -->
      </a-row>
      <a-row :gutter="[0,16]">
        <a-col :span="8">
          <a-form-model-item
            class="bar-code-content"
          >
            <span slot="label">
              {{ $srmI18n(`${$getLangAccount()}#i18n_alert_KmJM_293de900`, '在此扫描') }}:
              <a-tooltip
                :title="$srmI18n(`${$getLangAccount()}#i18n_dict_WNSpMqImh_130a84b6`, '输入后按回车键查询')"
              >
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <a-input
              v-model="form.barCodeContent"
              @pressEnter="submit"
              placeholder="" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <a-form-model-item
            class="bar-code-content"
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_ToRQW_e8e43161`, '条码明文:')"
          >
            <a-input
              v-model="barCodePlainText"
              disabled="disabled"
              placeholder="" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- <a-row class="submit-btn">
        <a-button
          type="primary"
          @click="submit">
          提交
        </a-button>
      </a-row> -->
    </a-form-model>

    <!-- table -->
    <a-row class="scanner-table">
      <a-table
        :columns="columns"
        :pagination="false"
        :loading="tableLoading"
        :scroll="{ y: 600 }"
        :data-source="data">
      </a-table>
    </a-row>
  </div>
</template>

<script>
import { postAction } from '@/api/manage'
import {ajaxFindDictItems} from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
    name: 'Barcodescanner',
    data () {
        return {
            form: {
                encryption: false,
                explainStrategy: '0',
                saveOperaRecord: false,
                barCodeContent: ''
            },
            recordDisabled: true,
            labelCol: { span: 8 },
            wrapperCol: { span: 16 },
            barCodePlainText: '',
            explainStrategyOption: [
              
            ],
            data: [],
            columns: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm132_busDocType`, '业务单据类型'),
                    dataIndex: 'attrType',
                    key: 'attrType'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tFJORL_d790c17a`, this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_tFJORL_d790c17a`, '单据字段名称')),
                    dataIndex: 'attrName',
                    key: 'attrName'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_Io_f6c81`, '简码'),
                    dataIndex: 'shortCode',
                    key: 'shortCode'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm554_originalValue`, '原值'),
                    dataIndex: 'originalValue',
                    key: 'originalValue'
                }
            ],
            tableLoading: false
        }
    },
    mounted () {
        this.initDictData('barcodeResolveType').then((res) => {
            if (res.success) {
                this.explainStrategyOption = res.result
            }
        })
    },

    methods: {
        strategyChange (val) {
            console.log(val)
            if (val == 1) {
                this.recordDisabled = false
            } else {
                this.recordDisabled = true
            }
        },
        submit () {
            if (!this.form.barCodeContent) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ToxOLVW_fc0b7831`, '条码不能为空！'))
                return
            }
            this.tableLoading = true
            const params = Object.assign({}, this.form, {encryption: this.form.encryption ? 1 : 0, saveOperaRecord: this.form.saveOperaRecord ? 1 : 0, barCodeContent: this.form.barCodeContent})
            postAction('base/barcode/saleExplain/barCodeExplain', params).then((data) => {
                if (data.success) {
                    this.data = data.result.itemVOS || []
                    this.barCodePlainText = data.result.barCodePlainText
                } else {
                    this.data =  []
                    this.$message.warning(data.message)
                }
            }).catch((e) => {
                console.error(e)
                this.$message.error(this.srmI18n(`${this.getLangAccount()}#i18n_title_feactHttpError`, '获取HTTP信息失败'))
            }).finally(() => {
                this.loading = false
                this.tableLoading = false
            })
        },
        initDictData (dictCode) {
            //根据字典Code, 初始化字典数组
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode
            }
            return ajaxFindDictItems(postData)
        }
        
    }
}
</script>

<style lang="less" scoped>
.barcode-scanner{
    padding: 16px;
    background: white;
        height: 100%;
    :deep(.ant-form-inline .ant-form-item){
        width: 90%;
    }
    .scanner-table{
        margin-top: 20px;
    }
    .submit-btn{
        padding-right: 50px;
        text-align: right;
    }
    .bar-code-content{
      :deep(.ant-form-item-control-wrapper){
        position: absolute;
        width: 300%;
      }
    } 
}
</style>