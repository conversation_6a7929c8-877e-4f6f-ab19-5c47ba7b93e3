<template>
  <div style="height: 100%">
    <a-spin :spinning="confirmLoading">
      <setp-lay-out 
        v-if="show"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :sourceGroups="sourceGroups"
        :pageStatus="pageStatus"
        :fromSourceData="fromSourceData"
      >
        <template #purchaseTenderProjectAttachmentInfoList="{ slotProps }">
          <attachmentInfoList
            ref="purchaseTenderProjectAttachmentInfoList"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :fromSourceData="fromSourceData"
          ></attachmentInfoList>
        </template>
        <template #tenderBidLetterFormatGroupVo="{ slotProps }">
          <tenderBidLetterFormatGroupVo
            ref="tenderBidLetterFormatGroupVo"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :tenderBidLetterFormatGroupVo="fromSourceData.tenderBidLetterFormatGroupVo"
          ></tenderBidLetterFormatGroupVo>
        </template>
        <template #purchaseTenderEvaluationPrinciples="{ slotProps }">
          <purchaseTenderEvaluationPrinciples
            ref="purchaseTenderEvaluationPrinciples"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :purchaseTenderEvaluationPrinciples="fromSourceData.purchaseTenderEvaluationPrinciples"
          >
          </purchaseTenderEvaluationPrinciples>
        </template>
        <template #tenderEvaluationInfoVoList="{ slotProps }">
          <tenderEvaluationInfoVoList
            ref="tenderEvaluationInfoVoList"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :fromSourceData="fromSourceData"
          ></tenderEvaluationInfoVoList>
        </template>
      </setp-lay-out>
    </a-spin>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import purchaseTenderEvaluationPrinciples from '../BiddingHall/BiddingFile/modules/purchaseTenderEvaluationPrinciples'
import tenderBidLetterFormatGroupVo from '../BiddingHall/BiddingFile/modules/tenderBidLetterFormatGroupVo'
import tenderEvaluationInfoVoList from '../BiddingHall/BiddingFile/modules/tenderEvaluationInfoVoList'
import attachmentInfoList from '../BiddingHall/BiddingFile/modules/attachmentInfoList'
import setpLayOut from '../BiddingHall/components/setpLayOut'

export default {
    name: 'BiddingFile',
    components: {
        fieldSelectModal,
        purchaseTenderEvaluationPrinciples,
        tenderEvaluationInfoVoList,
        attachmentInfoList,
        setpLayOut,
        tenderBidLetterFormatGroupVo
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage', 'resetCurrentSubPackage'],
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    data () {
        return {
            pageStatus: 'detail',
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            show: false,
            rejectForm: {
                node: '',
                reject: ''
            },
            sourceGroups: [
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxmK_9dbd9bbe`, '投标函格式'),
                    groupNameI18nKey: '',
                    groupCode: 'tenderBidLetterFormatGroupVo',
                    groupType: 'head',
                    show: true,
                    sortOrder: '1'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBjF_4119a25d`, '评标原则'),
                    groupNameI18nKey: '',
                    groupCode: 'purchaseTenderEvaluationPrinciples',
                    groupType: 'head',
                    show: true,
                    sortOrder: '2'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBCh_411bee3f`, '评标方法'),
                    groupNameI18nKey: '',
                    groupCode: 'tenderEvaluationInfoVoList',
                    groupType: 'head',
                    show: true,
                    sortOrder: '3'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingDocuments`, '招标文件'),
                    groupNameI18nKey: '',
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    groupType: 'item',
                    show: true,
                    sortOrder: '4'
                }
            ],
            businessRefName: 'businessRef',
            currentGroupCode: {},
            currentEditRow: {}, 
            status: '',
            confirmLoading: false,
            url: {
                detail: '/tender/tenderProjectAttachmentInfo/query',
                add: '/tender/tenderProjectAttachmentInfo/add',
                edit: '/tender/tenderProjectAttachmentInfo/edit',
                publish: '/tender/tenderProjectAttachmentInfo/publish'
            }
        }
    },
    methods: {
        queryDetail () {
            let params = {
                subpackageId: this.subId,
                checkType: '1'
            }
            this.confirmLoading = true
            this.show = false
            getAction(this.url.detail, params)
                .then(res => {
                    if(res.success) {
                        this.fromSourceData = res.result || {}
                        this.show = true
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        setCurrentStep (i) {
            this.$refs[this.businessRefName].currentStep = i
        },
        async saveEvent (arg, cb) {
            let refs = this.sourceGroups.map(ref => ref.groupCode)
            this.getAllValidate(refs).then(async (res) => {
                if (res.validStatus) {
                    let params = await this.getParamsName()
                    let url = params.id ? this.url.edit : this.url.add
                    this.confirmLoading = true
                    postAction(url, params).then(res => {
                        let type = res.success ? 'success' : 'error'
                        if (res.success) {
                            if (cb) {
                                cb()
                            } else {
                                this.$message[type](res.message)
                                this.queryDetail()
                            }
                        } else {
                            this.$message[type](res.message)
                        }
                    })
                        .finally(() => {
                            this.confirmLoading = false
                        })
                } else {
                    this.setCurrentStep(res.currentStep)
                }
            })
        },
        async getParamsName (obj) {
            let purchaseTenderProjectAttachmentInfoList = this.$refs.purchaseTenderProjectAttachmentInfoList.externalAllData()
            let tenderBidLetterFormatGroupVo = this.$refs.tenderBidLetterFormatGroupVo.externalAllData()
            let purchaseTenderEvaluationPrinciples = await this.$refs.purchaseTenderEvaluationPrinciples.externalAllData()
            let tenderEvaluationInfoVoList = await this.$refs.tenderEvaluationInfoVoList.externalAllData()
            let params = {
                ...this.fromSourceData,
                purchaseTenderProjectAttachmentInfoList,
                tenderEvaluationInfoVoList,
                tenderBidLetterFormatGroupVo,
                purchaseTenderEvaluationPrinciples,
                subpackageId: this.subId,
                tenderProjectId: this.tenderCurrentRow.id,
                checkType: '1'
            }
            return params
        },
        handlePublish () {
            if (!this.fromSourceData.id) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VsMKDJW_820cd572`, '请保存再提交'))
            let refs = this.sourceGroups.map(ref => ref.groupCode)
            this.getAllValidate(refs).then(async (res) => {
                if (res.validStatus) {
                    let that = this
                    let cb = (arg) => {
                        this.$confirm({
                            title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                            content: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_confirmSubmitApprovalTips`, '是否确认提交审批'),
                            onOk () {
                                let {id} = that.fromSourceData
                                that.confirmLoading = true
                                postAction(that.url.publish, {id}).then(res => {
                                    let type = res.success ? 'success' : 'error'
                                    that.$message[type](res.message)
                                    if (res.success) {
                                        // this.$emit('resetCurrentSubPackage') || ''
                                        this.resetCurrentSubPackage()
                                        that.queryDetail()
                                    }
                                }).finally(() => {
                                    that.confirmLoading = false
                                })
                            },
                            onCancel () {
                            }
                        })
                        
                    }
                    this.saveEvent({}, cb)
                } else {
                    this.setCurrentStep(res.currentStep)
                }
            })
        },
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef(this.currentGroupCode)
            itemGrid.insertAt([...data], -1)
        },
        getAllValidate (refNames) {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))

            let promiseValidateArr = refNames.map(ref => 
                this.$refs[ref].getValidatePromise()
            ).filter(promise => promise)
            return new Promise((resolve, reject) => {
                Promise.all(handlePromise(promiseValidateArr)).then(result => {
                    let currentStep = null
                    let flag = true
                    for (let i = 0; i < result.length; i++) {
                        if (result[i].status === 'error') {
                            currentStep = i
                            flag = false
                        }
                    }
                    if (flag) {
                        let resolveData = { validStatus: true, currentStep: currentStep, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiLR_48599c04`, '验证成功') }
                        resolve(resolveData)
                    } else {
                        let resolveData = { validStatus: false, currentStep: currentStep, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiKmWVGv_95d29236`, '验证失败，请处理') }
                        resolve(resolveData)
                    }
                }).catch(err => {
                    reject(err)
                })
            })
        }
    },
    created () {
        this.queryDetail()
    }
}
</script>
