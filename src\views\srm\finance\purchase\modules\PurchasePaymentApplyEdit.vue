<template>
  <div class="page-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"></business-layout>
    </a-spin>
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
    <a-modal
      v-drag
      v-model="visible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectDataSourceType`, '选择数据来源类型')"
      @ok="handleOk">
      <a-form-model
        :model="form">
        <a-form-model-item>
          <a-select
            :default-value="selectOption[0]"
            style="width: 300px"
            v-model="form.sourceType">
            <a-select-option
              v-for="item in selectOption"
              :key="item.value"
              :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction, postAction, downFile} from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {BUTTON_SUBMIT, BUTTON_SAVE} from '@/utils/constant.js'
import moment from 'moment'

export default {
    name: 'PurchasePaymentApplyEdit',
    mixins: [businessUtilMixin],
    components: {
        fieldSelectModal,
        BusinessLayout
    },
    data () {
        return {
            businessRefName: 'businessRef',
            addBtnCondition: true,
            form: {
                sourceType: '',
                gridType: 'item'
            },
            rowPaymentData: [],
            selectOption: [
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_order`, '订单'), value: 'order'},
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contract`, '合同'), value: 'contact'},
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GMIe_2c604a32`, '库存对账'), value: 'reconciliation'},
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n__IZIe_2be72aee`, '履约对账'), value: 'performanceReconciliation'}
            ],
            visible: false,
            requestData: {
                detail: {
                    url: '/finance/purchasePaymentApplyHead/queryById',
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                paymentApplyItemList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        attrs: {
                            type: 'primary'
                        },
                        click: this.addItemEvent,
                        show: this.showAddBtnCondition
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                        key: 'gridDelete',
                        show: this.showAddBtnCondition,
                        click: this.businessGridDelete
                    }
                ],
                attachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'paymentApply', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                        click: this.deleteBatch
                    }
                ]

            },
            pageFooterButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/finance/purchasePaymentApplyHead/edit'
                    }
                },
                {
                    ...BUTTON_SUBMIT,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                    args: {
                        url: '/a1bpmn/audit/api/submit'
                    },
                    handleBefore: this.handleBeforeFooterSubmit,
                    click: this.handleCustomSubmit,
                    show: this.showSubmitAuditCondition
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                add: '/finance/purchasePaymentApplyHead/add',
                edit: '/finance/purchasePaymentApplyHead/edit',
                publish: '/finance/purchasePaymentApplyHead/publish',
                isExistFrozen: 'supplier/supplierMaster/isExistFrozenStateData',
                detail: '/finance/purchasePaymentApplyHead/queryById',
                upload: '/attachment/purchaseAttachment/upload'
                // download: '/attachment/purchaseAttachment/download'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let busAccount = this.currentEditRow.templateAccount|| this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${busAccount}/purchase_paymentApply_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        getSubEls (parentRef, value, forms){
            let params={
                companyCode: forms.company,
                orgCode: forms.purchaseOrg,
                elsAccount: forms.toElsAccount
            }
            forms.receiverBankAccount = forms.toElsAccount || ''
            getAction('/supplier/supplierMaster/getBankByElsAccount', {...params}).then(res=>{
                forms.receiverBank = res.result.bankBranchName
                forms.receiverBankBccountName = res.result.bankAccount
                forms.paymentClause = res.result.payConditionCode
                forms.payWay = res.result.paymentMethod

            })
        },
        showAddBtnCondition (){
            return this.addBtnCondition
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'attachmentList',
                        groupType: 'item',
                        sortOrder: '6',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFilesEvent}
                            ]
                        }
                    }
                ],
                formFields: [],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        width: 200
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        width: 180
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount',
                        width: 120
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount',
                        width: 120
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        width: '100',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleAfterDealSource (pageConfig, resultData) {
            this.$nextTick( ()=> {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'baseForm') {
                        group.formModel.paymentApplyDate = group.formModel.paymentApplyDate?group.formModel.paymentApplyDate:moment(new Date().getTime()).format('YYYY-MM-DD')
                    }
                })
                if(localStorage.getItem('initiatePayment')){
                    this.rowPaymentData.push(JSON.parse(localStorage.getItem('initiatePayment')))
                    this.form.sourceType = 'reconciliation'
                    if(this.rowPaymentData[0].sourceType) {
                        this.form.sourceType = this.rowPaymentData[0].sourceType
                    }
                    if (this.rowPaymentData[0].sourceType === 'reconciliation' || this.rowPaymentData[0].sourceType === 'performanceReconciliation'){
                        this.addBtnCondition = false
                    }
                    if(this.rowPaymentData[0].sourceType === 'order') {
                        let id
                        for(let key in this.rowPaymentData){
                            this.rowPaymentData[key].netAmount = this.rowPaymentData[key].totalNetAmount
                            id = this.rowPaymentData[key].id
                        }
                        this.addBtnCondition = false
                        const params = {
                            id: id
                        }
                        getAction('/reconciliation/elsPurchaseStatisticsSourceCaseService/queryBySourceId', params).then(res => {
                            if(res.success && res.result) {
                                this.rowPaymentData[0].sourcePaidAmount = parseFloat(res.result.sourcePaidAmount ||0)
                                this.rowPaymentData[0].sourcePaymentAmount = parseFloat(res.result.sourcePaymentAmount ||0)
                            }
                            localStorage.removeItem('initiatePayment')
                            this.fieldSelectOk(this.rowPaymentData)
                            this.form.sourceType = ''
                        })
                    } else {
                        localStorage.removeItem('initiatePayment')
                        this.fieldSelectOk(this.rowPaymentData)
                        this.form.sourceType = ''
                    }
                }
                // 付款申请行
                if (resultData?.paymentApplyItemList?.length) { // 行信息有值就置灰表头
                    this.handleHeaderFields({pageData: pageConfig, flag: true})
                }
            })
            if(!resultData.paymentApplyOtherList.length) {
                this.hideSingleGroup(this.businessRefName, 'paymentApplyOtherList', true)
            }
        },
        goBack () {
            this.$emit('hide')
        },
        uploadCallBack (result) {
            let fileGrid =this.getItemGridRef('attachmentList')
            fileGrid.insertAt(result, -1)
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            this.attachmentDownloadEvent(row)
        },

        deleteFilesEvent (Vue, row) {
            const fileGrid = this.getItemGridRef('attachmentList')
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        deleteBatch () {
            const fileGrid = this.getItemGridRef('attachmentList')
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        handleBeforeFooterSubmit (args) {
            const { allData = {} } = args || {}
            return new Promise((resolve, reject) => {
                // 提交审批需要的参数
                let formatData = {
                    businessId: allData.id,
                    businessType: 'paymentApply',
                    auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_payRequisitionNo`, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_payRequisitionNo`, '付款申请单号'))}：${allData.paymentApplyNumber}`,
                    params: JSON.stringify(allData)
                }

                // 冻结需要的参数
                const frozen = {
                    toElsAccount: allData.toElsAccount,
                    frozenFunction: '4',
                    orgType: '1',
                    orgCode: allData.company
                }

                // 检测供应商对否被冻结（这个业务逻辑限制不能删除，谁删除谁加上）
                if ((frozen.toElsAccount && frozen.toElsAccount.length>0) && (frozen.orgCode && frozen.orgCode.length>0)) {
                    postAction(this.url.isExistFrozen, frozen).then(rest =>  {
                        if (rest.success) {
                            resolve({ ...args, allData: formatData })
                        } else {
                            this.$message.warning(rest.message)
                            reject(args)
                        }
                    })
                } else {
                    resolve({ ...args, allData: formatData })
                }
            })
        },
        handleCustomSubmit (args) {
            // 获取页面所有数据
            const allData = this.getAllData() || {}
            let paymentApplyItemList = allData.paymentApplyItemList;
            for (let i = 0; i < paymentApplyItemList.length; i++) {
                let paymentApplyItem = paymentApplyItemList[i]
                if(paymentApplyItem.applyAmount > paymentApplyItem.closingAmount){
                    this.$message.error('付款申请行 第' + (i + 1) + '行 付款申请含税金额不得大于来源单的可结算金额')
                    return
                }
            }
            this.composeBusinessSubmit(args)
        },
        handleOk () {
            if (this.form.sourceType === '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectSourceType`, '请选择来源类型！'))
                return
            }
            const params = this.getAllData() || {}
            if (params.paymentApplyType == '0' && this.form.sourceType == 'performanceReconciliation'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBVAcxOiFIZIe_6ef5a458`, '预付款类型不能选择履约对账'))
                return
            }
            if (params.paymentApplyType == '0' && this.form.sourceType == 'reconciliation'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBVAcxOiFGMIe_6f6ec39c`, '预付款类型不能选择库存对账'))
                return
            }
            let url = '/finance/purchasePaymentApplyHead/getBusinessDocuments'
            let param = {company: params.company,  currency: params.currency, toElsAccount: params.toElsAccount, payWay: params.payWay, paymentClause: params.paymentClause, purchaseOrg: params.purchaseOrg, sourceType: this.form.sourceType}
            let columns = [
                {field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 150},
                {field: 'totalTaxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_totalAmountIncludTaxi`, '含税总金额(含税)'), width: 150},
                {field: 'totalNetAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notTotalAmountIncludTaxi`, '未税总金额(未税)'), width: 150},
                {field: 'projectName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ledgerAccount`, '总账科目'), width: 150}
            ]
            if(this.form.sourceType === 'reconciliation'){
                columns = [
                    {field: 'reconciliationNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reconciliationNo`, '对账单号'), width: 150},
                    {field: 'beginDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaBeginDate`, '对账开始日期'), width: 150},
                    {field: 'endDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaEndDate`, '对账结束日期'), width: 150},
                    {field: 'chargeAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_chargeAmount`, '账扣金额'), width: 150},
                    {field: 'closingAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_dBHf_2c3862b0`, '应付金额'), width: 150},
                    {field: 'writtenOffAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBnXkf_ca86de5e`, '预付核销总额'), width: 150},
                    {field: 'paidAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IBHf_2beebe92`, '已付金额'), width: 150},
                    {field: 'remainingUnPaidAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bULBHf_2ed64c6a`, '剩余未付金额'), width: 150},
                    {field: 'paymentRequestedAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IUVBVHf_7d0a2268`, '已申请付款金额'), width: 150},
                    {field: 'totalInvoiceAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hPxfkHf_dc1302ed`, '发票含税总金额'), width: 150},
                    {field: 'notIncludeTaxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hPLfkHf_e7c37e6c`, '发票未税总金额'), width: 150},
                    {field: 'taxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hPkff_d5cc6375`, '发票总额税'), width: 150}
                ]
            }
            if(this.form.sourceType === 'performanceReconciliation'){
                columns = [
                    {field: 'reconciliationNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reconciliationNo`, '对账单号'), width: 150},
                    {field: 'beginDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaBeginDate`, '对账开始日期'), width: 150},
                    {field: 'endDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaEndDate`, '对账结束日期'), width: 150},
                    {field: 'chargeAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_chargeAmount`, '账扣金额'), width: 150},
                    {field: 'closingAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_dBHf_2c3862b0`, '应付金额'), width: 150},
                    {field: 'writtenOffAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBnXkf_ca86de5e`, '预付核销总额'), width: 150},
                    {field: 'paidAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IBHf_2beebe92`, '已付金额'), width: 150},
                    {field: 'remainingUnPaidAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bULBHf_2ed64c6a`, '剩余未付金额'), width: 150},
                    {field: 'paymentRequestedAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IUVBVHf_7d0a2268`, '已申请付款金额'), width: 150},
                    {field: 'totalInvoiceAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hPxfkHf_dc1302ed`, '发票含税总金额'), width: 150},
                    {field: 'notIncludeTaxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hPLfkHf_e7c37e6c`, '发票未税总金额'), width: 150},
                    {field: 'taxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hPkff_d5cc6375`, '发票总额税'), width: 150}
                ]
            }
            if(this.form.sourceType === 'contact'){
                columns = [
                    {field: 'contractNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractNumber`, '合同单号'), width: 200},
                    {field: 'contractName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractName`, '合同名称'), width: 200},
                    {field: 'contractDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractDesc`, '合同描述'), width: 200},
                    {field: 'contractType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractType`, '合同类型'), width: 200}
                ]
            }
            this.visible = false
            this.form.itemType = 'item'
            this.$refs.fieldSelectModal.open(url, param, columns, 'multiple')
        },
        fieldSelectOk (data) {
            const params = this.getAllData() || {}
            let arr
            let itemGrid = this.getItemGridRef('paymentApplyItemList')
            if(this.form.sourceType === 'order'){
                arr = data.map((item) => (
                    { paymentApplyNumber: params.paymentApplyNumber, sourceSystem: 'srm', sourceType: 'order', sourceNumber: item.orderNumber,
                        sourceId: item.id,
                        paymentStatus: '0',
                        shouldTaxAmount: item.totalTaxAmount,
                        shouldNoTaxAmount: item.totalNetAmount,
                        taxAmount: (parseFloat(item.totalTaxAmount)-(item.totalNetAmount)).toFixed(2),
                        // applyAmount: item.totalTaxAmount,
                        applyNoTaxAmount: item.totalNetAmount,
                        applyTaxAmount: (parseFloat(item.totalTaxAmount)-(item.totalNetAmount)).toFixed(2),
                        //已付金额
                        sourcePaidAmount: item.sourcePaidAmount,
                        //未付含税金额
                        unPayIncludeTaxAmount: item.sourceUnPaidAmount,
                        //已申请付款金额
                        itemPaymentAmount: item.sourcePaymentAmount,
                        //结算金额
                        closingAmount: (parseFloat(item.totalTaxAmount) - parseFloat(item.sourcePaidAmount||0) - parseFloat(item.sourcePaymentAmount||0)).toFixed(2),
                        //付款申请含税金额
                        applyAmount: (parseFloat(item.totalTaxAmount) - parseFloat(item.sourcePaidAmount||0) - parseFloat(item.sourcePaymentAmount||0)).toFixed(2)

                    }))
            } else if(this.form.sourceType === 'contact'){
                arr = data.map(({ contractNumber, id, totalTaxAmount, totalNetAmount, sourcePaidAmount, sourceUnPaidAmount, sourcePaymentAmount, settlementAmount}) => (
                    { paymentApplyNumber: params.paymentApplyNumber, sourceSystem: 'srm', sourceType: 'contact', sourceNumber: contractNumber, sourceId: id,
                        shouldTaxAmount: totalTaxAmount, shouldNoTaxAmount: totalNetAmount,
                        taxAmount: (parseFloat(totalTaxAmount)-parseFloat(totalNetAmount||0)).toFixed(2),
                        //已付金额
                        sourcePaidAmount: sourcePaidAmount,
                        //未付含税金额
                        unPayIncludeTaxAmount: sourceUnPaidAmount,
                        //已申请付款金额
                        itemPaymentAmount: sourcePaymentAmount,
                        //结算金额
                        closingAmount: (parseFloat(totalTaxAmount) - parseFloat(sourcePaidAmount||0) - parseFloat(sourcePaymentAmount||0)).toFixed(2)

                    }))
            } else if(this.form.sourceType === 'reconciliation'){
                arr = data.map(({ reconciliationNumber, id, totalTaxAmount, totalNetAmount, shouldTaxAmount, closingAmount, sourcePaidAmount, paymentRequestedAmount, paidAmount, settlementAmount, shouldInvoiceAmount, writtenOffAmount, sourcePaymentAmount}) => (
                    { paymentApplyNumber: params.paymentApplyNumber,
                        sourceSystem: 'srm',
                        sourceType: 'reconciliation',
                        sourceNumber: reconciliationNumber,
                        sourceId: id,
                        //来源单含税金额
                        shouldTaxAmount: shouldInvoiceAmount,
                        //已付金额
                        sourcePaidAmount: sourcePaidAmount,
                        shouldNoTaxAmount: totalNetAmount,
                        //已申请付款金额
                        itemPaymentAmount: sourcePaymentAmount,
                        //结算金额
                        closingAmount: (parseFloat(settlementAmount) - parseFloat(sourcePaidAmount||0) - parseFloat(sourcePaymentAmount||0)).toFixed(2),
                        //预付核销金额
                        preWrittenOffAmount: writtenOffAmount
                    }))
                let reconciliationIds = []
                for (let k in data) {
                    reconciliationIds.push(data[k].id)
                }
                let param = {'ids': reconciliationIds.join()}

                getAction('/reconciliation/purchaseReconciliation/getPurchaseReconciliationByIdList', param).then(res => {
                    if (res.success) {
                        let recChargeGrid = this.getItemGridRef('paymentChargeList')
                        recChargeGrid.insertAt(res.result)
                    }
                }),
                getAction('/finance/purchasePaymentApplyHead/getPurchasePrePaymentWriteOffReconciliationByIdList', param).then(res => {
                    if (res.success) {
                        let recChargeGrid = this.getItemGridRef('paymentApplyWriteOffList')
                        recChargeGrid.insertAt(res.result)
                    }
                })
            }else if(this.form.sourceType === 'performanceReconciliation'){
                arr = data.map(({ reconciliationNumber, id, totalTaxAmount, totalNetAmount, shouldTaxAmount, closingAmount, paidAmount, paymentRequestedAmount, settlementAmount, sourcePaidAmount, writtenOffAmount, sourcePaymentAmount, shouldInvoiceAmount}) => (
                    { paymentApplyNumber: params.paymentApplyNumber, sourceSystem: 'srm', sourceType: 'performanceReconciliation', sourceNumber: reconciliationNumber, sourceId: id,
                        //来源单含税金额
                        shouldTaxAmount: shouldInvoiceAmount,
                        //已付金额
                        sourcePaidAmount: sourcePaidAmount,
                        //已申请付款金额
                        itemPaymentAmount: sourcePaymentAmount,
                        shouldNoTaxAmount: totalNetAmount,
                        //结算金额
                        closingAmount: (parseFloat(settlementAmount) - parseFloat(sourcePaidAmount||0) - parseFloat(sourcePaymentAmount||0)).toFixed(2),
                        //预付核销金额
                        preWrittenOffAmount: writtenOffAmount
                    }))
                let reconciliationIds = []
                for (let k in data) {
                    reconciliationIds.push(data[k].id)
                }
                let param = {'ids': reconciliationIds.join()}
                getAction('/finance/purchasePaymentApplyHead/getPurchasePrePaymentWriteOffReconciliationByIdList', param).then(res => {
                    if (res.success) {
                        let recChargeGrid = this.getItemGridRef('paymentApplyWriteOffList')
                        recChargeGrid.insertAt(res.result)
                    }
                }),
                getAction('/reconciliation/purchaseReconciliation/getPurchaseReconciliationByIdList', param).then(res => {
                    if (res.success) {
                        let recChargeGrid = this.getItemGridRef('paymentChargeList')
                        recChargeGrid.insertAt(res.result)
                    }
                })
            }
            itemGrid = this.getItemGridRef('paymentApplyItemList')
            let { fullData } = itemGrid.getTableData()
            let regulations = fullData.map(item => {
                return item.sourceId
            })
            let insertData = arr.filter(item => {
                return !regulations.includes(item.sourceId)
            })
            itemGrid.insertAt(insertData)
            this.setItemGrid()
        },
        setItemGrid (){
            let { fullData } = this.getItemGridRef('paymentApplyItemList').getTableData()
            const { pageConfig = {} } = this.getBusinessExtendData(this.businessRefName)
            if (fullData.length > 0) {
                this.handleHeaderFields({pageData: pageConfig, flag: true})
            }else {
                this.handleHeaderFields({pageData: pageConfig, flag: false})
            }
        },
        init (){
            let that=this
            if (this.currentEditRow) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id, (data) => {
                    this.editFormData = data
                    if(data.purchasePaymentApplyOtherList==null||!data.purchasePaymentApplyOtherList.length){
                        that.pageData.groups = that.pageData.groups.filter((item)=>{
                            return item.groupCode!='itemOtherInfo'
                        })}

                })
            }
        },
        addItemEvent (){
            const params = this.getAllData() || {}
            if(!params.toElsAccount){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierFirst`, '先选择供应商'))
                return
            }
            if(!params.company){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectCompanyCodeFirst`, '先选择公司代码'))
                return
            }
            if (!params.currency) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWiFlq_937a4bfb`, '请先选择币别'))
                return
            }
            if (params.paymentApplyType === '0'){
                this.selectOption = [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_order`, '订单'), value: 'order'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contract`, '合同'), value: 'contact'}
                ]
                this.form.sourceType = ''
            } else {
                this.selectOption = [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GMIe_2c604a32`, '库存对账'), value: 'reconciliation'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n__IZIe_2be72aee`, '履约对账'), value: 'performanceReconciliation'}
                ]
                this.form.sourceType = ''
            }
            this.visible = true

        },
        // 表格通用删除
        businessGridDelete ({ groupCode = '' }) {
            if (!groupCode) {
                return
            }
            let itemGrid = this.getItemGridRef(groupCode)
            const { pageConfig = {} } = this.getBusinessExtendData(this.businessRefName)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            itemGrid.removeCheckboxRow()
            let {fullData} = itemGrid.getTableData()
            if (fullData?.length == 0) {
                this.handleHeaderFields({pageData: pageConfig, flag: false})
            }
        }
    }
}
</script>