<template>
  <div>
    <div class="LadderPrice">
      <div class="content">
        <slot :openFunc="showModal">
          <a-input
            readOnly
            :disabled="config.disabled"
            :value="value"
            :placeholder="config.placeholder"
            @click="showModal(value)">
            <a-icon
              v-if="!config.disabled"
              slot="suffix"
              type="close-circle"
              @click="clearInputValue"></a-icon>
          </a-input>
        </slot>
      </div>
      <ladder-price-modal
        isEmit
        v-on="$listeners"
        ref="LadderPriceModal"
      />
      <ladder-price-detail-modal
        isEmit
        v-on="$listeners"
        ref="LadderPriceDetailModal"
      />
    </div>
  </div>
</template>

<script>
import { PRIVATE_BEFORE_CHECK_CALLBACK } from '@/utils/constant.js'
import { bindfunctionMiddleware } from '@/utils/util.js'

import LadderPriceModal from './LadderPriceModal'
import LadderPriceDetailModal from './LadderPriceDetailModal'
export default {
    name: 'Main',
    inject: ['tplRootRef'],
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: String,
            default: ''
        },
        pageStatus: {
            type: String,
            default: ''
        },
        config: {
            type: Object,
            default () {
                return {}
            }
        },
        isRow: {
            type: Boolean,
            default: false
        },
        row: {
            type: Object,
            default () {
                return {}
            }
        },
        column: {
            type: Object,
            default () {
                return {}
            }
        },
        form: {
            type: Object,
            default () {
                return {}
            }
        },
        pageData: {
            type: Object,
            default () {
                return {
                    groups: [{
                        custom: {
                            form: {},
                            formFields: [],
                            validateRules: {}
                        },
                        extend: null,
                        groupCode: null,
                        groupName: null,
                        sortOrder: null
                    }]
                }
            }
        },
        isFromTileEditPage: {
            type: Boolean,
            default: false
        }
    },

    data () {
        return {
        }
    },
    components: {LadderPriceModal, LadderPriceDetailModal},
    computed: {
        // 配置下标，当弹窗配置为数组时
        current () {
            return this.config && this.config.extend && this.config.extend.current || 0
        },
        // 当前配置项
        curConfig () {
            let extend = this.config && this.config.extend || {}
            if (extend.modalConfigs && Array.isArray(extend.modalConfigs)) {
                return extend.modalConfigs[this.current]
            }
            return extend
        },
        groupData () {
            if (this.isFromTileEditPage) {
                return this.pageData.panels[this.currentStep] || {}
            }
            return this.pageData.groups[this.currentStep] || {}
        }
    },

    methods: {
        showModal (curFieldValue) {
            let beforeCheckedCallBack = this.curConfig.beforeCheckedCallBack || (() => window.Promise.resolve())
            this.changeSelectModalValue(beforeCheckedCallBack).then(() => {
                const curInfo = this.isRow ? this.row : this.form
                if(this.pageStatus == 'edit' && this.config?.groupCode !== 'rebateCalculationSheetRuleDetails'){
                    this.$refs.LadderPriceModal.open(curInfo, curFieldValue)
                }else{
                    this.$refs.LadderPriceDetailModal.open(curInfo, curFieldValue)
                }
            }).catch((errTxt) => {
                this.$message.error(errTxt)
                if (this.isRow) {
                    this.$emit('error', errTxt)
                }
            })
        },
        handleCancel () {
            // this.visible = false
            // this.$emit('update:visible', false)
            // this.$refs.LadderPriceModal.hide('a')
        },
        clearInputValue () {
            let beforeCheckedCallBack = this.curConfig.beforeCheckedCallBack || (() => window.Promise.resolve())
            let afterClearCallBack = this.curConfig.afterClearCallBack || (f => f)
            this.changeSelectModalValue(beforeCheckedCallBack).then(() => {
                this.$emit('change', '')
                this.$emit('afterClearCallBack', afterClearCallBack)
            }).catch((errTxt) => {
                this.$message.error(errTxt)
                if (this.isRow) {
                    this.$emit('error', errTxt)
                }
            })
        },
        // 统一表头、表行回调参数
        changeSelectModalValue (cb) {
            if (cb.name === PRIVATE_BEFORE_CHECK_CALLBACK) {
                let params = {
                    _pageData: this.pageData,
                    _cacheAllData: this.tplRootRef.getAllData(),
                    _value: this.value,
                    _row: {},
                    _form: {}
                }
                params[this.isRow ? '_row' : '_form'] = this.row
                return bindfunctionMiddleware(this.tplRootRef, cb, params)
            } else {
                if (this.isRow) {
                    return cb && cb(this, this.row, this.column, this.form)
                } else {
                    return cb && cb(this, this.pageData, this.groupData, this.form)
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>

</style>