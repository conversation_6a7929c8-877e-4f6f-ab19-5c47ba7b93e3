<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"/>
    <!-- 编辑界面 -->
    <SaleEsignV3PersonalEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleEsignV3OrgDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <SaleEsignV3OrgAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <employeeManagement
      v-if="employeeManagementVisible"
      :companyName="currentCompanyName"
      :row="companyRow"
      :visible="employeeManagementVisible"
      @close="employeeManagementVisible=false">
    </employeeManagement>
    <a-modal
      v-drag
      centered
      :width="400"
      :visible="authVisible"
      :maskClosable="false"
      @ok="selectedOk"
      @cancel="authVisible = false"
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iFlbd_1c30671e`, '选择授权项')"
    >
      <a-checkbox-group v-model="authData">
        <template v-for="(el, idx) of authSelectData">
          <a-checkbox
            :value="el.value"
            :key="idx">
            {{ el.label }}
          </a-checkbox>
        </template>
      </a-checkbox-group>
    </a-modal>
  </div>

</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import SaleEsignV3OrgAdd from './modules/SaleEsignV3OrgAdd'
import SaleEsignV3OrgDetail from './modules/SaleEsignV3OrgDetail'
import SaleEsignV3PersonalEdit from './modules/SaleEsignV3OrgEdit'
import {getAction, postAction} from '@/api/manage'
import EmployeeManagement from '../modules/SaleEmployeeManagement'

export default {
    mixins: [ListMixin],
    components: {
        SaleEsignV3OrgAdd, SaleEsignV3OrgDetail, SaleEsignV3PersonalEdit, EmployeeManagement
    },
    data () {
        return {
            authSelectData: [
                {
                    value: 'get_org_identity_info',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiTSMAEVRjDjeytvVH_ed72da9e`, '授权允许获取企业/组织用户的账号基本信息')
                },
                {
                    value: 'get_psn_identity_info',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiTSMOrLmLjDjeytvVH_1b681dfc`, '授权允许获取经办人个人用户的账号基本信息')
                },
                {
                    value: 'org_initiate_sign',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiToBAEVRjDhAnePW_4cae94cf`, '授权允许代表企业/组织用户发起合同签署')
                },
                {
                    value: 'psn_initiate_sign',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiToBOrLmLjDhAnePW_77746db1`, '授权允许代表经办人个人用户发起合同签署')
                },
                {
                    value: 'manage_org_resource',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiTSMAEVRjDjWeVRLjEJjjRvbW_416c750`, '授权允许获取企业/组织用户的印章、组织成员等资源的管理权限')
                },
                {
                    value: 'manage_psn_resource',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiTSMOrLmLjDjWeEJjjRvbW_a97155bc`, '授权允许获取经办人个人用户的印章等资源的管理权限')
                },
                {
                    value: 'use_org_order',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiTSMAEVRjDKsItjKjbW_e4460d97`, '授权允许获取企业/组织用户套餐订单的使用权限')
                }
            ],
            authData: [],
            authVisible: false,
            currentCompanyName: '',
            companyRow: null,
            employeeManagementVisible: false,
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                businessType: 'esignV3Personal',
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'esignv3#saleEsignV3Org:add'
                    },

                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'esignv3#saleEsignV3Org:view'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.allowEdit,
                        authorityCode: 'esignv3#saleEsignV3Org:add'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'),
                        clickFn: this.handleCertification,
                        allow: this.allowCertification,
                        authorityCode: 'esignv3#saleEsignV3Org:submitCertification'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLizE_25c88b41`, '刷新认证状态'),
                        clickFn: this.handleRefreshStatus,
                        allow: this.allowRefreshStatus,
                        authorityCode: 'esignv3#saleEsignV3Org:refresh'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jRRv_27c711d2`, '员工管理'),
                        clickFn: this.handleEmployeeManagement,
                        allow: this.allowEmployManagement,
                        authorityCode: 'esignv3#saleEsignV3OrgPsn:management'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_SMlbKy_8f5b4fe1`, '获取授权链接'),
                        clickFn: this.getAuth,
                        allow: this.allowEmployManagement,
                        authorityCode: 'esignv3#saleEsignV3Org:getAuth'
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.allowDelete,
                        authorityCode: 'esignv3#saleEsignV3Org:delete'
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ]
            },
            url: {
                add: '/esignv3/saleEsignV3Org/add',
                list: '/esignv3/saleEsignV3Org/list',
                delete: '/esignv3/saleEsignV3Org/delete',
                columns: 'saleEsignV3Org',
                auth: '/esignv3/saleEsignV3Org/submitCertification',
                refresh: '/esignv3/saleEsignV3Org/refreshRealNameStatus'
            }
        }
    },
    methods: {
        allowEmployManagement (row) {
            if (row.realnameStatus === '1') {
                return false
            }
            return true
        },
        selectedOk () {
            console.log(this.authData)
            if (!this.authData && this.authData.length <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFlbd_f34bbd87`, '请选择授权项'))
                return
            }
            let params = {id: this.companyRow.id, authorizedScopes: this.authData.join(',')}

            postAction('/esignv3/saleEsignV3Org/getAuth', params).then(res => {
                console.log(res)
                if(res.success){
                    this.authVisible = false
                    window.open(res.message)
                }else {
                    this.$message.error(res.message)
                }
            })
        },
        getAuth (row) {
            this.companyRow = row
            this.authVisible = true
        },
        handleEmployeeManagement (row) {
            console.log(row)
            this.currentCompanyName = row.orgName
            this.companyRow = row
            this.employeeManagementVisible = true
        },
        //已认证不能被编辑
        allowEdit (row) {
            if (row.realnameStatus === '1') {
                return true
            }
            return false
        },
        allowCertification (row) {
            if (row.realnameStatus == '1') {
                return true
            }
            return false
        },
        allowRefreshStatus (row) {
            if (row.realnameStatus == '1') {
                return true
            }
            return false
        },
        //已经创建e签宝的账号的不能删除
        allowDelete (row) {
            if (row.realnameStatus === '1') {
                return true
            }
            return false
        },
        handleAdd () {
            this.showAddPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row) {
            this.currentEditRow = row
            this.showEditPage = true
        },
        submitCallBack (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        handleCertification (row) {
            let authUrl = row.authUrl
            let contentText = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLDJLiW_5f843f01`, '是否确认提交认证?')
            if (authUrl && authUrl.length > 0) {
                contentText = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_rVHIDJRLiWKmDJMAbujWKQtTDJW_96191780`, '该信息已提交过认证，再次提交会产生费用，是否继续提交？')
            }
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationSystems`, '确认认证'),
                content: contentText,
                onOk: function () {
                    postAction(that.url.auth+'?needSave=0', {id: row.id}).then(res => {
                        if (res.success) {
                            window.open(res.result.authUrl)
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        handleRefreshStatus (row) {
            getAction(this.url.refresh, {id: row.id}).then(res => {
                if (res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                } else {
                    this.$message.warning(res.message)
                }
                // 刷新列表
                this.$refs.listPage.loadData()
            })
        }
    }
}
</script>