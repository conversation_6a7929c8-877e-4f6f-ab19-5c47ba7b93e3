<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      refresh
      :current-edit-row="currentEditRow"
      :url="url" />
    <bpmn-audit-script-edit
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>

    <bpmn-audit-script-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import BpmnAuditScriptEdit from './modules/BpmnAuditScriptEdit'
import BpmnAuditScriptDetail from './modules/BpmnAuditScriptDetail'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {getAction, postAction} from '@api/manage'

export default {
    mixins: [ListMixin],
    components: {
        BpmnAuditScriptDetail,
        BpmnAuditScriptEdit
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'bpmnScript',
                formField: [
                    {
                        type: 'input',
                        label: '关键字',
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNJvRLJvAo_4aaff9ae`, '请输入脚本名称/脚本编码')
                    }
                ],
                form: {},
                button: [
                    {
                        allow: ()=> {
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'bpmn#auditScript:add',
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'bpmn#auditScript:edit'},
                    { type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_Aj_aa6d9`, '启用'), clickFn: this.handleEnable, allow: this.allowEnable, authorityCode: 'bpmn#auditScript:enable'},
                    { type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disable`, '禁用'), clickFn: this.handleDisable, allow: this.allowDisable, authorityCode: 'bpmn#auditScript:disable'},
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowEnable, authorityCode: 'bpmn#auditScript:delete'},
                    { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/a1bpmn/audit/script/list',
                delete: '/a1bpmn/audit/script/delete',
                columns: 'bpmnAuditScript'
            }
        }
    },
    methods: {
        handleAdd (){
            this.currentEditRow = {
                templateNumber: 'TC2022073101',
                templateVersion: '1',
                templateAccount: '100000',
                elsAccount: this.$ls.get(USER_ELS_ACCOUNT)
            }
            this.showEditPage = true
        },
        handleDelete (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLDK_38d74ae0`, '确认提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_QGPMddiKUzjQLWKQRLQGW_f18ce7ff`, '删除将会影响正在审批的流程，是否确认删除？'),
                onOk: () => {
                    that.confirmLoading = true
                    getAction('/a1bpmn/audit/script/delete', { id: row.id }).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData()
                        } else {
                            that.$message.error(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        handleEnable (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLDK_38d74ae0`, '确认提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLAj_38d5624f`, '确认启用'),
                onOk: () => {
                    that.confirmLoading = true
                    postAction('/a1bpmn/audit/script/enable', row).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData()
                        } else {
                            that.$message.error(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        handleDisable (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLDK_38d74ae0`, '确认提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_JvIAjAPtkMddUzsntFWKQtTW_9827a2b2`, '脚本已启用当前操作会影响审批中的单据，是否继续？'),
                onOk: () => {
                    that.confirmLoading = true
                    postAction('/a1bpmn/audit/script/disable', row).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData()
                        } else {
                            that.$message.error(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        allowEnable (row) {
            return row.enable !== '0'
        },
        allowDisable (row) {
            return row.enable !== '1'
        }
    }
}
</script>