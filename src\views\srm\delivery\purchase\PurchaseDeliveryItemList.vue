<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      table-code="purchaseDeliveryItem"
      v-show="!showEditPage && !showDetailPage && !showRequestToOrderPage"
      :pageData="pageData"
      :url="url"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      @handleView="handleView"/>
    <!-- 详情界面 -->

  </div>
</template>
<script>

import {ListMixin} from '@comp/template/list/ListMixin'
import {postAction, getAction} from '@/api/manage'


export default {
    mixins: [ListMixin],
    components: {},
    data () {
        return {
            showRequestToOrderPage: false,
            showEditPage: false,
            responseData: {}, //上报接口数据
            pageData: {
                businessType: 'delivery',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterOrderNo`, '请输入订单号')
                    }
                ],
                form: {
                    supplierName: '',
                    deliveryDesc: '',
                    deliveryNumber: '',
                    deliveryStatus: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sure`, '确认'),
                        value: 'confirm',
                        icon: 'plus',
                        clickFn: this.confirmOrderItem,
                        type: 'primary',
                        primary: true,
                        hide: true
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'),
                        value: 'reject',
                        icon: 'delete',
                        clickFn: this.refuseOrderItem,
                        hide: true
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), allow: () => {
                            return this.btnInvalidAuth('delivery#purchaseDeliveryItem:export')
                        }, icon: 'download', folded: false, clickFn: this.handleExportXls
                    },


                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        value: 'define',
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                showOptColumn: false
            },
            url: {
                list: '/delivery/purchaseDeliveryItem/list',
                columns: 'purchaseDeliveryItem',
                exportXlsUrl: '/delivery/purchaseDeliveryItem/exportXls'
            },
            tabsList: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_all`, '全部'),
                    itemStatus: null,
                    rejectReason: null
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmed`, '已确认'),
                    itemStatus: '1',
                    rejectReason: null
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_toBeConfirmedPurchase`, '待采购确认'),
                    itemStatus: '4',
                    rejectReason: null
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_waitSupplierSure`, '待供应商确认'),
                    itemStatus: '5',
                    rejectReason: null
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRefuered`, '供应商已拒绝'),
                    itemStatus: '2',
                    rejectReason: null
                }
            ]
        }
    },
    mounted () {
        this.serachCountTabs('/delivery/purchaseDeliveryItem/counts')
    },
    methods: {
        showProcessCheck (row) {
            if (!row.deliveryProgressTemplateId) {
                return true
            }
            return false
        },
        hideOrderProgressModel () {
            this.showEditPage = false
            this.showRequestToOrderPage = false
            this.searchEvent(false)
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dStRH_2eb66187`, '送货单明细'))
        },
        confirmOrderItem () {
            const that = this
            let purchaseDeliveryItemGrid = this.$refs.listPage.$refs.listGrid
            let checkboxRecords = purchaseDeliveryItemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sureOderLine`, '确认订单行'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isSureOderLine`, '是否确认订单行?'),
                onOk: function () {
                    that.$refs.listPage.confirmLoading = true
                    let params = {}
                    params['purchaseDeliveryItemList'] = checkboxRecords
                    postAction(that.url.confirmOrderItem, params).then(res => {
                        if (res.success) {
                            that.$refs.listPage.$message.success(res.message)
                            that.$refs.listPage.confirmLoading = false
                            that.$refs.listPage.loadData()
                        } else {
                            that.$refs.listPage.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.$refs.listPage.confirmLoading = false
                    })

                }
            })
        },
        refuseOrderItem () {
            const that = this
            let purchaseDeliveryItemGrid = this.$refs.listPage.$refs.listGrid
            let checkboxRecords = purchaseDeliveryItemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuerOderLine`, '拒绝订单行'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isRefuerOderLine`, '是否拒绝订单行?'),
                onOk: function () {
                    that.$refs.listPage.confirmLoading = true
                    let params = {}
                    params['purchaseDeliveryItemList'] = checkboxRecords
                    postAction(that.url.refuseOrderItem, params).then(res => {
                        if (res.success) {
                            that.$refs.listPage.$message.success(res.message)
                            that.$refs.listPage.confirmLoading = false
                            that.$refs.listPage.loadData()
                        } else {
                            that.$refs.listPage.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.$refs.listPage.confirmLoading = false
                    })
                }
            })
        },
        handleProgressCheck (row) {
            let url = '/delivery/purchaseOrderProgressReportHead/queryById'
            getAction(url, {id: row.id}).then(res => {
                if (!res.success) {
                    this.$message.warning(res.message)
                    return
                }
                debugger
                this.responseData = {
                    baseForm: res.result,
                    purchaseOrderProgressReportItemList: res.result.purchaseOrderProgressReportItemList || []
                }
                this.showRequestToOrderPage = true
            })
        }
    }
}
</script>