<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url"
      :currentEditRow="currentEditRow" />
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction } from '@/api/manage'
export default {
    name: 'ElsBarcodeParamConfigEdit',
    mixins: [EditMixin, businessUtilMixin],
    components: {
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            selectType: 'seals',
            confirmLoading: false,
            pageData: {
                form: {
                    relationId: '',
                    companyName: '',
                    alias: '',
                    height: '',
                    width: '',
                    transparentFlag: '',
                    filePath: '',
                    sealId: '',
                    subAccount: '',
                    orgId: '',
                    accountId: ''
                },
                groups: [

                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/base/barcode/elsBarCodeParamConfig/add',
                edit: '/base/barcode/elsBarCodeParamConfig/edit',
                detail: '/base/barcode/elsBarCodeParamConfig/queryById'
            }
        }
    },
    computed: {
        fileSrc () {
            console.log(this.currentEditRow)
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let busAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${busAccount}/purchase_barCodeParamConfig_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        showUploadBtn (){
            if(this.currentEditRow.sealId){
                return false
            }
            return true
        },
        saveEvent () {
            const formData = this.$refs.editPage.getPageData()
            let url = this.currentEditRow.id? this.url.edit: this.url.add
            postAction(url, formData).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    if (!this.currentEditRow.id) {
                        this.currentEditRow.id = res.result.id
                    }
                    this.confirmLoading = false
                    this.init()
                    this.$parent.handleList()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }


    }
}
</script>