<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <PurchaseEsignEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <PurchaseEsignDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import PurchaseEsignEdit from './modules/PurchaseEsignEdit'
import PurchaseEsignDetail from './modules/PurchaseEsignDetail'
import {ListMixin} from '@comp/template/list/ListMixin'
import { getAction, postAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        PurchaseEsignEdit,
        PurchaseEsignDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'esign',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAySNRdXAo_94458881`, '业务编号/供应商编码')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'esign#purchaseEsign:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                form: {
                    keyWord: ''
                },
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esign#purchaseEsign:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esign#purchaseEsign:edit'},
                    {type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), clickFn: this.handleSend, allow: this.allowSend, authorityCode: 'esign#purchaseEsign:send'},
                    {type: 'confirm', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVQIRL_c5fb6687`, '回传文件确认'), clickFn: this.handleConfirm, allow: this.allowConfirm, authorityCode: 'esign#purchaseEsign:confirm'},
                    {type: 'query', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QLzEmh_5c8fbdd2`, '流程状态查询'), clickFn: this.queryFlow, allow: this.allowQueryFlow, authorityCode: 'esign#purchaseEsign:statusQuery'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'esign#purchaseEsign:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/esign/purchaseEsign/list',
                add: '/esign/purchaseEsign/add',
                delete: '/esign/purchaseEsign/delete',
                send: '/esign/purchaseEsign/send',
                queryFlow: '/esign/purchaseEsign/flowQuery',
                columns: 'PurchaseEsignList'
            }
        }
    },
    methods: {
        handleAdd (){
            this.showEditPage = true
        },
        allowEdit (row){
            if(row.launch==='1'){
                return true
            }
            return false
        },
        allowDelete (row){
            if(row.launch==='1'){
                return true
            }
        },
        allowSend (row){
            //已上传未发送，签署完成
            if(row.uploaded==='1' && row.sendStatus!=='1' && row.esignStatus==='2'){
                return false
            }
            return true
        },
        allowConfirm (row){
            //已回传，未驳回，未确认
            if(row.returnSignedFile==='1' && row.reject!=='1' && row.returnFileConfirm!=='1'){
                return false
            }
            return true
        },
        handleSend (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'),
                content: '是否确认发送',
                onOk: function () {
                    getAction(that.url.send, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.sendStatus_dictText = '是'
                            row.sendStatus = '1'
                        }
                    })
                }
            })
        },
        handleConfirm (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },

        allowQueryFlow (row){
            if(row.launch==='1'){
                return false
            }
            return true
        },
        queryFlow (row){
            getAction(this.url.queryFlow, {id: row.id}).then(res => {
                if(res.success){
                    this.$message.info(res.result.flowDesc)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>