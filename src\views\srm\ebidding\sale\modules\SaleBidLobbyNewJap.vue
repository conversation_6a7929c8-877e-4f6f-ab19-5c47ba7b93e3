<template>
  <!-- 2022-03-08 新版本采购竞价大厅（逐条、打包）日荷《QQT SRM V5-PRD-竞价管理优化方案-v2.1-********》 -->
  <div class="saleBuyBid">
    <a-spin :spinning="spinning">
      <div class="container">
        <div class="top">
          <div class="breadcrumb">
          </div>
          <div class="menu">
            <a-button
              type="primary"
              @click="refresh(true)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_refresh`, '刷新') }}</a-button>
            <!-- <a-button
              type="default"
              @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_menu_Rl_a72da`, '关闭') }}</a-button> -->
            <a-button
              type="primary"
              @click="windowOpenClose">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
          </div>
        </div>

        <div class="content">
          <div class="gutter">
            <div class="item price">
              <div class="info">
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_field_OuyO_391539d8`, '竞价阶段') }}</b>
                  <b class="value">{{ getCurrentTitle }}</b>
                </span>
                <span 
                 
                  class="inline">
                  <b class="value">{{ getCurrentMaterialStatus }}</b>
                  <!-- form.ebiddingStatus_dictText -->
                </span>
                <span
                  v-if="form.ebiddingStatus === '3'"
                  class="inline"
                  style="float: right;">
                  <b class="label">{{ countdownText }}</b>
                  <b class="value red">
                    <span
                      class="icon"
                      v-if="deadline">
                      <a-icon
                        type="clock-circle"
                        theme="outlined"
                        :style="{ fontSize: '22px', color: '#f41616', marginRight: '8px' }"
                      />
                    </span>
                    <countdown
                      :time="deadline"
                      v-if="deadline"
                      :style="valueStyle"
                      @end="handleFinish"
                    >
                      <template slot-scope="props">{{ props.days ? `${props.days} ${$srmI18n(`${$getLangAccount()}#i18n_dict_S_5929`, '天')} ` : '' }}{{ props.hours }} : {{ props.minutes }} : {{ props.seconds }}</template>
                    </countdown>
                  </b>
                </span>
              </div>
            </div>
          </div>
          <div class="gutter">
            <div class="item price">
              <div class="info">
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号') }}</b>
                  <b class="value m-l-24">{{ form.ebiddingNumber }} </b>
                  <b
                    class="value inline-flot"
                    @click="changeToggle">{{ getToggleStatu }} <a-icon :type="resizeHeight==0?'down':'up'" /></b>
                </span>
                <a-descriptions
                  v-if="resizeHeight==0"
                  class="m-t-12"
                  id="description"
                  bordered
                  size="small"
                  :column="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }">
                  <a-descriptions-item
                    v-for="item in baseInfo"
                    :key="item.key"
                    :label="item.label"
                    :span="item.span || 1">
                    {{ form[item.key] }}
                  </a-descriptions-item>
                </a-descriptions>
              </div>
            </div>
          </div>
          <div class="gutter">
            <multipane
              class="custom-resizer"
              layout="vertical">
              <div class="item material">
                <div class="info">
                  <a-card
                    :title="$srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息')"
                    :bordered="false"
                    :headStyle="headStyle"
                    :bodyStyle="bodyStyle_2"
                  >
                    <div class="cardContent">
                      <div class="table">
                        <!-- 逐条报价 -->
                        <vxe-grid
                          ref="quoteGrid"
                          :height="350+resizeHeight"
                          v-bind="materialGridOptions"
                          :row-class-name="handleRowClass"
                          @cell-click="cellClickEvent"
                        >
                          <template slot="empty">
                            <a-empty />
                          </template>
                        </vxe-grid>
                      </div>
                    </div>
                  </a-card>
                </div>
              </div>
              <multipane-resizer></multipane-resizer>
              <div class="item quota">
                <div class="info">
                  <a-card
                    :title="$srmI18n(`${$getLangAccount()}#i18n_title_offer`, '报价')"
                    :bordered="false"
                    :headStyle="headStyle"
                    :bodyStyle="bodyStyle_2"
                  >
                    <div class="cardContent">
                      <div class="table">
                        <vxe-grid
                          ref="materialGrid"
                          :height="350+resizeHeight"
                          v-bind="quoteGridOption"
                        >
                          <template slot="empty">
                            <a-empty />
                          </template>
                          <template #operation="{ row, rowIndex }">
                            <a-button
                              v-if="!row.accept && form.ebiddingStatus === '4'"
                              type="primary"
                              size="small"
                              :loading="loading"
                              :disabled="row.disabled"
                              @click="acceptQuota({ row, rowIndex })"
                            >
                              {{ $srmI18n(`${$getLangAccount()}#i18n_title_accept`, '接受') }}
                            </a-button>
                            <a-button
                              style="margin-left: 5px"
                              v-if="!row.accept && form.ebiddingStatus === '4'"
                              type="danger"
                              size="small"
                              :loading="loading"
                              :disabled="row.disabled"
                              @click="rejectQuota({ row, rowIndex })"
                            >
                              {{ $srmI18n(`${$getLangAccount()}#i18n_title_refuse`, '拒绝') }}
                            </a-button>
                            <span v-if="row.accept">
                              {{ row.accept === '1' ? $srmI18n(`${$getLangAccount()}#i18n_title_accept`, '接受') : $srmI18n(`${$getLangAccount()}#i18n_title_refuse`, '拒绝') }}
                            </span>
                          </template>
                        </vxe-grid>
                      </div>
                    </div>
                  </a-card>
                </div>
              </div>
            </multipane>
          </div>
          <div
            class="gutter"
            v-if="form.supplierShow">
            <div class="item price">
              <div class="info">
                <vxe-grid
                  ref="rankGrid"
                  :height="350+resizeHeight"
                  v-bind="supplierGridOptions">
                </vxe-grid>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script lang='jsx'>
const resizeChartMethod = '$__resizeMethod'

import { getAction } from '@/api/manage'
import { debounce } from 'lodash'
import { apiSaleQueryBidLobbyDetail, apiSaleHisQuueryQuoteDetail, apiSaleQueryBidLobbyQuote, apiSaleQuoteAccept, apiSaleQuoteReject, apiSetOnlineAccount } from '@/api/apiBidding.js'
import { currency } from '@/filters'
import { materialGridOptions, quoteGridOptions, quotePackGridOptions } from '../../gridConfig/sale/indexJap'
import { chartGridOptions } from '../../gridConfig/sale'
import {USER_INFO} from '@/store/mutation-types'

import LineChart from '../../components/LineChart'
import { mapActions, mapGetters } from 'vuex'
import countdown from '@/components/countdown/index.js'
import {nominalEdgePullWhiteBlack } from '@/utils/util.js'
import { closeWS, createSocket } from '../../websocket.js'
import { Multipane, MultipaneResizer } from 'vue-multipane'
const HEART_BEAT_CONFIG = {
    time: 30 * 1000, // time：心跳时间间隔
    timeout: 3 * 1000, // timeout：心跳超时间隔
    reconnect: 10 * 1000 // reconnect：断线重连时
}

// const PACK = '0' // 打包
const ONEBYONE = '1' // 逐条
// const BATCH = '2' // 批量

export default {
    inject: [
        'closeCurrent'
    ],
    components: {
        LineChart,
        countdown,
        Multipane, 
        MultipaneResizer 
        // QuotePrice
        // Breadcrumb
    },
    filters: {
        currency
    },
    data () {
        return {
            materialGridOptions, // 物料信息列表
            supplierGridOptions: chartGridOptions, // 供应商列表 排名、报价信息
            quoteGridOption: {},
            currentMaterialNumName: null, // 点击物料行当前行物料编码、名称
            currentMaterialStatus: null, // 点击物料行当前行竞价状态
            current: -1,
            currentItemNumber: -1,
            deadline: 0,
            countdownText: '',
            gt1900: false,
            spinning: false,
            delayTime: 300,
            headStyle: {
                borderBottom: 'none',
                padding: '0 6px',
                minHeight: '34px',
                lineHeight: '34px',
                height: '40px'
            },
            valueStyle: {
                fontSize: '24px',
                color: '#f41616'
            },
            bodyStyle: {
                padding: '0 24px 24px'
            },
            bodyStyle_2: {
                padding: '0 6px 24px 6px'
            },
            progress: {
                percent: 78,
                status: 'active',
                strokeColor: {
                    from: '#4892ff',
                    to: '#596fe1'
                }
            },
            endTime: 0,
            minPrice: 1200,
            form: {
                ebiddingNumber: '', // 竞价单号
                ebiddingType: '', // 竞价方式
                changeRange: '', // 价格调整幅度
                rangeUnit: '', // 幅度单位
                beginTime: '', // 竞价开始时间  
                endTime: '', // 竞价结束时间
                keepMinute: '', // 每轮持续时间
                intervalMinute: '', // 每轮间隔时间
                currentRound: '',
                ebiddingWay: '',
                id: '',
                itemNumber: ''
            },
            // websocket: null,
            socket: null,
            lockReconnect: false, //是否真正建立连接
            timeout: 30*1000, //30秒一次心跳
            timeoutObj: null, //心跳心跳倒计时
            serverTimeoutObj: null, //心跳倒计时
            timeoutnum: null, //断开 重连倒计时
            openUpper: false,
            heartBeatConfig: HEART_BEAT_CONFIG,
            url: {
                detail: '/ebidding/saleEbiddingHead/queryById'
            },
            wsOnlineUrl: '',
            loading: false,
            resizeHeight: 0,
            currentEndMaterialItemNumber: 1
        }
    },
    computed: {
        ...mapGetters(['getOnlineSuppliers', 'getOnlineID', 'getPageRefreshTime']),
        getCurrentTitle () {
            if (this.form.ebiddingWay === ONEBYONE && ['3', '4'].includes(this.form.ebiddingStatus)) { // 逐条报价、竞价中
                return this.currentMaterialNumName ? `【 ${ this.currentMaterialNumName } 】` : ''
            } else {
                return ''
            }
        },
        getToggleStatu (){
            let lable=this.resizeHeight==0?this.$srmI18n(`${this.$getLangAccount()}#i18n_title_putAway`, '收起'):this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expand`, '展开')
            return lable
        },
        getCurrentMaterialStatus () {
            if (this.form.ebiddingWay === ONEBYONE && ['3', '4'].includes(this.form.ebiddingStatus)) { // 逐条报价、竞价中
                return this.currentMaterialStatus ? `${ this.currentMaterialStatus }` : ''
            } else {
                return this.form.ebiddingStatus_dictText
            }
        },
        baseInfo () {
            const {  rangeUnit_dictText} = this.form || {}
            return [
                // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingNumber`, '竞价单号'), key: 'ebiddingNumber', show: true, span: '4' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'), key: 'ebiddingStatus_dictText', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingType`, '竞价类型'), key: 'ebiddingType_dictText', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quotePriceWay`, '报价方式'), key: 'ebiddingWay_dictText', show: true },
                { label: (this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingRange`, '竞价幅度'))+`(${rangeUnit_dictText})`, key: 'changeRange', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AOKI_2787c0d7`, '启动时间'), key: 'startTime', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_endTime`, '结束时间'), key: 'endTime', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIzs_63db992`, '持续时间(分钟)'), key: 'keepMinute' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ImKIzs_cd425da6`, '间隔时间(分钟)'), key: 'intervalMinute' }
                // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_beforeOverTime`, '结束时间前'), key: 'beforeEndMinute', show: true }, // 分钟内，有报价
                // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extensionTime`, '延期时间'), key: 'delayMinute', show: true } // 分钟
            ]
        }
        // quoteGridOption () {
        //     const option = this.form.ebiddingWay === '0' ? quotePackGridOptions : quoteGridOptions
        //     option.data = this.quotaData
        //     return option
        // }
    },
    methods: {
        ...mapActions(['SetOnlineWS', 'CloseOnlineWS']),
        //关闭页面
        windowOpenClose (){
            window.close()
        },
        //收取和展开逻辑
        changeToggle (){
            if(document.getElementById('description')){
                let domHeight=document.getElementById('description').clientHeight
                this.resizeHeight=domHeight
            }else{
                this.resizeHeight=0
            }

        },
        getOnlineWebsocketUrl () {
            let { serivceUrl = '', elsAccount = '' } = this.$ls.get(USER_INFO) || {}
            let url = serivceUrl.replace('https://', 'wss://').replace('http://', 'ws://')
            this.wsOnlineUrl = `${url}/els/websocket/online/${this.$route.query.relationId}/${elsAccount}`
            createSocket(this.wsOnlineUrl)
        },
        getSocketData (newVal) {
            console.log('getSocketData newVal.detail', newVal.detail.data)
            const message = newVal.detail.data

            if (message === this.$route.query.relationId) {
                if (this.form && this.form.ebiddingStatus === '3' || this.form && this.form.ebiddingStatus === '4'){
                    let _this=this
                    setTimeout(()=>{
                        _this.refresh(false)
                    }, 1000)
                } 
                else {
                    this.refreshMaterialList() // 更新物料列表
                    this.getCurrentItemQuotas() // 更新当前物料报价列表
                    this.refreshSupplierList() // 更新当前物料供应商排名列表
                }
                return
            }
        },
        sendRefresh (time = 3000) {
            this.setIntervalWesocketPush && clearInterval(this.setIntervalWesocketPush)
            this.setIntervalWesocketPush = setInterval(() => {
                this.sendRefresh()
                apiSetOnlineAccount({ headRelationId: this.$route.query.relationId }).then(res => {
                    console.log('res', res)
                })
            }, time)
        },
        acceptQuota (data) {
            if (this.loading) return
            this.loading = true
            // 日式竞价 判断当前接受报价是否高于原接受价格，仅可接受低(反向如此，正向相反)
            if (this.form.ebiddingMethod === '1' && this.form.ebiddingWay === '1') {
                let result1 = true
                let result0 = true
                const accepts = this.quoteGridOption.data.filter(i => i.accept === '1')
                accepts.forEach(i => {
                    if ((data.row.price >= i.price && this.form.ebiddingType === '1')) { // 反向
                        result1 = false
                    }
                    if (data.row.price <= i.price && this.form.ebiddingType === '0') { // 正向
                        result0 = false
                    }
                })
                this.spinning = false
                if (!result1) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VylHnjum_80aa9f26`, '请接受更低的价格'))
                    this.loading = false
                    return
                }
                if (!result0) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VylHxjum_a3012f5c`, '请接受更高的价格'))
                    this.loading = false
                    return
                }
            } else if  (this.form.ebiddingMethod === '1' && this.form.ebiddingWay === '0') {
                let result1 = true
                let result0 = true
                const accepts = this.quoteGridOption.data.filter(i => i.accept === '1')
                accepts.forEach(i => {
                    if ((data.row.totalAmount >= i.totalAmount && this.form.ebiddingType === '1')) { // 反向
                        result1 = false
                    }
                    if (data.row.totalAmount <= i.totalAmount && this.form.ebiddingType === '0') { // 正向
                        result0 = false
                    }
                })
                this.spinning = false
                if (!result1) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VylHnjum_80aa9f26`, '请接受更低的价格'))
                    this.loading = false
                    return
                }
                if (!result0) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VylHxjum_a3012f5c`, '请接受更高的价格'))
                    this.loading = false
                    return
                }
            } else if  (this.form.ebiddingMethod === '1' && this.form.ebiddingWay === '0') {
                let result1 = true
                let result0 = true
                const accepts = this.quoteGridOption.data.filter(i => i.accept === '1')
                accepts.forEach(i => {
                    if ((data.row.totalAmount >= i.totalAmount && this.form.ebiddingType === '1')) { // 反向
                        result1 = false
                    }
                    if (data.row.totalAmount <= i.totalAmount && this.form.ebiddingType === '0') { // 正向
                        result0 = false
                    }
                })
                if (!result1) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VylHnjum_80aa9f26`, '请接受更低的价格'))
                    this.loading = false
                    return
                }
                if (!result0) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VylHxjum_a3012f5c`, '请接受更高的价格'))
                    this.loading = false
                    return
                }
            }
            apiSaleQuoteAccept({ itemHisId: data.row.id }).then((res) => {
                res.success ? this.$message.success(res.message) : this.$message.error(res.message)
                if (res.success) {
                    this.getCurrentItemQuotas()
                    this.refreshMaterialList() // 更新物料列表
                }
                this.loading = false
            })
        },
        rejectQuota (data) {
            if (this.loading) return
            this.loading = true
            apiSaleQuoteReject({ itemHisId: data.row.id }).then(res => {
                res.success ? this.$message.success(res.message) : this.$message.error(res.message)
                if (res.success) {
                    this.getCurrentItemQuotas()
                    this.refreshMaterialList() // 更新物料列表
                }
                this.loading = false
            })
        },
        async getCurrentItemQuotas () {
            const params = {
                headId: this.$route.query.id,
                itemNumber: this.current || '1'
            }
            const res = await apiSaleHisQuueryQuoteDetail(params)
            this.loading = false
            if (res.success) {
                this.fixItemData(res)
            }
        },
        refreshMaterialList () {
            const params = {
                id: this.$route.query.id,
                itemNumber: this.currentItemNumber
            }
            apiSaleQueryBidLobbyDetail(params).then(res => {
                this.form = res.result
                this.materialGridOptions.data = res.result.saleEbiddingItemList
                const currentMaterial = this.materialGridOptions.data.find(i => i.itemNumber === this.current)
                if (currentMaterial) {
                    const { materialNumber, materialName, itemStatus_dictText } = currentMaterial
                    this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
                }
            })
        },
        async refreshSupplierList () {
            const paramss = {
                id: this.$route.query.id,
                itemNumber: this.currentItemNumber || '1'
            }
            const res = await apiSaleQueryBidLobbyQuote(paramss)
            if (res && res.success) {
                const { saleEbiddingItemList = [] } = res.result || {}
                this.supplierGridOptions.data = JSON.parse(JSON.stringify(saleEbiddingItemList))
            }
        },
        handleRowClass ({ row }) {
            if (this.form.ebiddingWay === '0') return
            const { itemNumber = '' } = row || {}
            if (itemNumber === this.current) {
                return 'row--current'
            }
        },
        cellClickEvent ({ row }) {
            if (this.form.ebiddingWay === '0') return
            const { itemNumber= '', materialNumber, materialName, itemStatus_dictText } = row || {}
            // if (itemNumber === this.current) return
            this.current = itemNumber
            this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
            this.getItemData(this.current)
        },
        setCurrentMaterialInfo (materialNumber = null, materialName = null, itemStatus_dictText = null) {
            this.currentMaterialNumName = `${materialNumber} - ${materialName}`
            this.currentMaterialStatus = itemStatus_dictText
        },
        getMessage (msg) {
            console.log(msg.data)
            this.refresh()
            this.reset()
        },
        goBack () {
            closeWS()
            // this.CloseOnlineWS()
            this.closeCurrent()
        },
        handleSuccess () {
            this.refresh()
        },
        refresh: debounce(function (spinning = true) {
            this.getData(spinning)
        }, 1000 ),
        handleFinish () {
            this.refresh(true)
        },
        getData (spinning=true) {

            this.getQueryData().then(res=>{

                this.sendRefresh()
                if(res.result.ebiddingStatus==='5'){
                    this.$notification.open({
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                        description: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingOver`, '竞价结束')}`,
                        icon: <a-icon type="sound" style="color: #108ee9" />,
                        duration: 10
                    })
                }else{
                    if(this.currentEndMaterialItemNumber!=this.currentItemNumber){
                        // this.currentSupplierAccount = -1
                        // this.currentSupplierStatus = -1
                        // this.currentSupplierItemId = ''
                        if(this.currentMaterialNumName&&this.currentItemNumber!='1'){
                            let materNumber=this.materialGridOptions.data.find(val=> this.currentEndMaterialItemNumber==val.itemNumber  )
                            this.$notification.open({
                                message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                                description: `${materNumber.materialNumber}-${materNumber.materialDesc}${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingOver`, '竞价结束')},切换到下一个物料`,
                                icon: <a-icon type="sound" style="color: #108ee9" />,
                                duration: 10
                            })
                        }
                        this.currentEndMaterialItemNumber=this.currentItemNumber
                    }
                }

                if (spinning) this.spinning = true
                const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))
                const params = {
                    id: this.$route.query.id,
                    itemNumber: this.currentItemNumber
                }
                const promiseList = [
                    apiSaleQueryBidLobbyDetail(params)
                ]
                Promise.all(handlePromise(promiseList)).then(res => {
                    const [infoRes] = res || []
                    if (infoRes && infoRes.status === 'success') {
                        this.fixInfoData(infoRes.res)
                    }
                    this.getItemData()
                })

            })
  
        },
        async getItemData (num) {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))
            const params = {
                headId: this.$route.query.id,
                itemNumber: num || this.current || '1'
            }
            const promiseList = [ apiSaleHisQuueryQuoteDetail(params) ]
            const paramss = {
                id: this.$route.query.id,
                itemNumber: num || this.current || '1'
            }
            const res = await apiSaleQueryBidLobbyQuote(paramss)
            if (res && res.success) {
                const { saleEbiddingItemList = [] } = res.result || {}
                this.supplierGridOptions.data = JSON.parse(JSON.stringify(saleEbiddingItemList))
            }
            Promise.all(handlePromise(promiseList)).then((res) => {
                this.spinning = false
                const [itemRes] = res || []
                if (itemRes && itemRes.status === 'success') {
                    this.fixItemData(itemRes.res)
                    this.quotaData = itemRes.res.result
                }
            })
        },
        fixItemData ({ result }) {
            this.quoteGridOption.data = result
            if (this.quoteGridOption.data && this.quoteGridOption.data.length > 0) {
                // 获取所有已接受报价
                const counts = this.quoteGridOption.data
                    .filter(i => i.accept)
                    // .map((i) => { return { ...i, quoteTime: new Date(i.quoteTime).getTime() }})
                // 获取最后报价 下标
                const lastQuote = counts && counts.length > 0 ? counts.sort().reverse()[0] : null
                const lastQuoteId = lastQuote ? lastQuote.id : null
                const lastQuoteIndex = lastQuoteId ? this.quoteGridOption.data.findIndex(i => i.id === lastQuoteId) : null
                // 最后报价下标 + 1 === 可报价下标，或无报价时默认0 下标
                const ableQuoteIndex = lastQuoteIndex || lastQuoteIndex === 0 ? lastQuoteIndex + 1 : 0
                this.quoteGridOption.data = this.quoteGridOption.data.map((i, index) => {
                    // const quoteTime = new Date(i.quoteTime).getTime()
                    // if (lastQuote.quoteTime && quoteTime < lastQuote.quoteTime) i.disabled = true
                    // else i.disabled = false
                    // 可报价
                    if (ableQuoteIndex !== index) i.disabled = true
                    else i.disabled = false
                    return i
                })
            }
        },
        // 倒计时计算
        checkTime () {
            let { beginTime, beginTime_DateMaps,  endTime, endTime_DateMaps, ebiddingStatus } = this.form
            const { serviceTime } = this.form
            const now = serviceTime || Date.now()
            beginTime = beginTime ? beginTime_DateMaps : Date.now()
            endTime = endTime ? endTime_DateMaps : Date.now()
            if(ebiddingStatus === '3'){ // 待竞价状态
                if (now < beginTime) {
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OuvKutK_bc29df23`, '竞价开始倒计时')
                    this.deadline = beginTime - now
                }else{
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n__EonRvAOu_6f8beeea`, '等待采购开启竞价')
                    this.deadline = 0
                }
            }else if(ebiddingStatus === '4'){ // 竞价中状态
                if(now < endTime) {
                    this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yWutK_2b6a279b`, '结束倒计时')
                    this.deadline = endTime - now
                }else{
                    this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n__EonRyWOu_7e4b6167`, '等待采购结束竞价')
                    this.deadline = 0
                }
            }else{
                this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SutK_2f8d3027`, '无倒计时')
                this.deadline = 0
            }
        },
        fixInfoData (res) {
            const { saleEbiddingItemList = [], ebiddingWay, currentItemNumber, ...others } = res.result || {}
            this.materialGridOptions.data = saleEbiddingItemList
            this.currentItemNumber = currentItemNumber || '1'
            if (this.current === -1 || !this.current) this.current = this.currentItemNumber
            const itemNumber = this.current === -1 ? this.currentItemNumber : this.current
            const currentMaterial = saleEbiddingItemList.find(i => i.itemNumber === itemNumber)
            if (currentMaterial) {
                const { materialNumber, materialName, itemStatus_dictText } = currentMaterial
                this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
            }
            this.form = {
                ebiddingWay,
                currentItemNumber: this.currentItemNumber,
                ...others,
                serviceTime: res.timestamp
            }
            this.quoteGridOption = this.form.ebiddingWay === '0' ? quotePackGridOptions : quoteGridOptions
            // 逐条，获取起拍价格
            if (ebiddingWay === ONEBYONE) {
                let i = this.currentItemNumber - 1
                this.form.startPrice = saleEbiddingItemList[i].startPrice
            }
            this.checkTime()
        },
        setRankTableColumn () {
        },
        getScreenWidth () {
            const clientWidth = document.documentElement.clientWidth
            this.gt1900 = (clientWidth >= 1900)
        },
        async getQueryData () {
            const res = await getAction(this.url.detail, { id: this.$route.query.id })
            if (res && res.success) {
                this.currentItemNumber = res.result.currentItemNumber
                const { ebiddingNumber, id } = this.$route.query || {}
                this.current = this.currentItemNumber
                this.form = Object.assign({}, this.form, {
                    ebiddingNumber,
                    currentItemNumber: this.currentItemNumber,
                    id
                })
            }
            return res
        },
        // 通过lodash的防抖函数来控制resize的频率
        [resizeChartMethod]: debounce(function () {
            this.getScreenWidth()
        }, 200),
        init () {
            // await this.getQueryData()
            this.getData()
        },
        setOnline () {
            apiSetOnlineAccount({ headRelationId: this.$route.query.relationId }).then(res => {
                console.log('res', res)
            })
        }
    },
    watch: {
        '$route': {
            handler ({ path }) {
                if (path !== '/ebidding/saleLobbyNewJap') {
                    return
                }
                console.log('/ebidding/saleLobbyNewJap')
                this.init()
            },
            immediate: true
        },
        getOnlineID: {
            handler (newVal, oldVal) {
                console.log('getOnlineID newVal refresh', newVal)
                if (newVal && oldVal && newVal.id === this.$route.query.relationId && oldVal.time !== newVal.time) {
                    this.refreshMaterialList() // 更新物料列表
                    this.getCurrentItemQuotas() // 更新当前物料报价列表
                    this.refreshSupplierList() // 更新当前物料供应商排名列表
                }
            },
            immediate: true,
            deep: true
        },
        getPageRefreshTime: {
            handler (newVal) {
                console.log('getPageRefreshTime newVal', newVal)
                if (newVal && newVal.id === this.$route.query.relationId) {
                    this.refresh()
                }
                this.setOnline()
            },
            immediate: true,
            deep: true
        }
    },
    created () {
        this.getScreenWidth()
        this.getOnlineWebsocketUrl()
        // this.getData()
        nominalEdgePullWhiteBlack()
        this.$nextTick(()=>{
            this.changeToggle()
        })
    },
    // beforeDestroy () {
    //     window.removeEventListener('reisze', this[resizeChartMethod])
    // },
    // activated () {
    //     window.addEventListener('resize', this[resizeChartMethod])
    //     this.SetOnlineWS({ wsOnlineUrl: this.wsOnlineUrl, id: this.$route.query.relationId }) // 是否在线WS
    // }
    activated () {
        console.log('activated ------------------------- activated')
        this.getOnlineWebsocketUrl()
        this.sendRefresh()
        window.addEventListener('resize', this[resizeChartMethod])
    },
    mounted () {
        console.log('mounted ------------------------- mounted')
        window.addEventListener('onmessageWS', this.getSocketData)
    },
    beforeDestroy () {
        console.log('beforeDestroy ------------------------- beforeDestroy')
        window.removeEventListener('onmessageWS', this.getSocketData)
        window.removeEventListener('reisze', this[resizeChartMethod])
    }
}
</script>

<style lang="less" scoped>
@red: #f41616;
@blue: #1690ff;
.m-l-24{
  margin-left:36px!important;
}
.m-t-12{
  margin-top:12px
}
.inline-flot{
  display: inline-block;
  float: right;
  font-size: 14px;
}
.saleBuyBid {
            background-color: #eaeaea;
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    .redTxt {
      color: @red;
    }
    .blue {
      color: #1890ff;
    }
    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px;
        background: #fff;
        position: fixed;
        width: 100%;
        position: absolute;
        height: 44px;
        z-index: 99;
        .menu {
            text-align: right;
            .ant-btn {
                & +.ant-btn {
                    margin-left: 8px;
                }
            }

        }
    }
	.content {
		padding: 8px;
    padding-top: 52px;
		.gutter {
			display: flex;
			& + .gutter {
				margin-top: 8px;
			}
			.price {
				max-width: 100%;
				flex: 1;
			}
			.history,
			.compare {
				overflow-y: auto;
				flex: 1;
				max-width: 100%;
                .ant-btn {
                    & + .ant-btn {
                        margin-left: 8px;
                    }
                }
			}
			.material {
			
			width: 70%;
			}
			.quota {
				flex: 1;
				width: 30%;
			}
		}
		.item {
			padding: 12px;
			background: #fff;
            border-radius: 4px;
			// & + .item {
			// 	margin-left: 10px;
			// }
		}
		.info {
			font-size: 20px;
			color: #000;
			.inline {
        .tit {
          margin-right: 8px;
          &::after {
            content: ':'
          }
        }
				&:first-of-type {
					border-left: 4px solid @blue;
				}
				& + .inline {
					margin-left: 24px;
				}
				.label {
					margin-left: 12px;
					&::after {
						content: ":";
					}
				}
				.value {
					margin-left: 12px;
					color: @blue;
				}
				.red {
					color: @red;
				}
			}
		}
		.currentTrendEchart {
			width: 100%;
			height: 400px;
		}
		.hisPriceEchart {
			width: 100%;
			height: 446px;
		}
		// .table {
		// 	height: 420px;
		// }
    .material-table {
        height: 480px;
    }
	}
}
.ant-modal-root
.ant-modal-wrap
.ant-modal
.ant-modal-content
.ant-modal-body
.chartInfo {
  display: flex;
  margin-top: 30px;
  .echart {
    flex: 1;
    min-height: 320px;
  }
  .echart.unShow {
      // background-size: 100px 100px;
      position: relative;
      background-color: #fff;
      background-image: url(~@/assets/img/ebidding/x1.png);
      background-repeat: no-repeat;
      background-position: center;
  }
  .chartTable {
    flex: 0 0 260px;
    margin-left: 20px;
  }
  .red {
    color: @red;
    position: absolute;
    left: 50%;
    bottom: 20px;
    transform: translateX(-50%);
  }
}
:deep(.ant-descriptions-item-content) {
  width: 20%;
}
:deep(.ant-card-head-title) {
  padding: 0;
}
.custom-resizer{
    width: 100vw;
    overflow: hidden;
     > .multipane-resizer {
    margin: 0;
    left: 0;
    // margin-top: 20%;
    position: relative;
    &:before {
      display: block;
      content: "";
      width: 3px;
      height: 40px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -20px;
      margin-left: -4px;
      border-left: 1px solid #ccc;
      border-right: 1px solid #ccc;
    }
    &:hover {
      &:before {
        border-color: #999;
      }
    }
  }
}
</style>
