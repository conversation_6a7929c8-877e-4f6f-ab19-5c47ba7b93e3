<template>
  <iframe
    :id="id"
    :src="url"
    frameborder="0"
    width="100%"
    height="800px"
    title=""
    scrolling="auto"
  />
</template>

<script>
// import PageLayout from '../page/PageLayout'
// import RouteView from './RouteView'
export default {
    name: 'IframePageContent',
    inject: ['closeCurrent'],
    data () {
        return {
            url: '',
            id: ''
        }
    },
    created () {
        this.goUrl()
    },
    updated () {
        this.goUrl()
    },
    watch: {
        $route () {
            this.goUrl()
        }
    },
    methods: {
        goUrl () {
            let url = this.$route.meta.url
            let query = this.$route.query
            if(url) {
                url += url.indexOf('?') > -1 ? '&token=' + this.$store.getters.token : '?token=' + this.$store.getters.token
                if(query){
                    Object.keys(query).forEach(key =>{
                        url += '&'+key+'='+query[key]   
                    })
                }
            }
            let id = this.$route.path
            this.id = id
            if (url !== null && url !== undefined) {
                this.url = url

                if(this.$route.meta.internalOrExternal != undefined && this.$route.meta.internalOrExternal==true){
                    this.closeCurrent()
                    window.open(this.url)
                }
            }
        }
    }
}
</script>

<style>
</style>