<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @loadSuccess="handleLoadSuccess" />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {getAction} from '@api/manage'
export default {
    mixins: [DetailMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            showRemote: false,
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contactInfo`, '联系人信息'), groupCode: 'contactsInfo', type: 'grid', custom: {
                        ref: 'supplierContactsInfoList',
                        columns: [
                            {
                                type: 'checkbox', width: 40
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                                field: 'itemNumber',
                                disable: true,
                                width: 50
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_toElsAccount`, '采购维护账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                                field: 'subAccount',
                                width: 150
                            },
                            {
                                required: '0',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_function`, '职能'),
                                field: 'functionName',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_position`, '职位'),
                                field: 'position',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                                field: 'name',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话号码'),
                                field: 'telphone',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'),
                                field: 'email',
                                width: 150
                            },
                            {
                                title: 'QQ',
                                field: 'qqNo',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_weChat`, '微信'),
                                field: 'wxNo',
                                width: 150
                            }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addressInfo`, '地址信息'), groupCode: 'addressInfo', type: 'grid', custom: {
                        ref: 'supplierAddressInfoList',
                        columns: [
                            {
                                type: 'checkbox', width: 40
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                                field: 'itemNumber',
                                disable: true,
                                width: 50
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_toElsAccount`, '采购维护账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                required: '0',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_country`, '国家'),
                                field: 'country',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_province`, '省份'),
                                field: 'province',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_city`, '城市'),
                                field: 'city',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_address`, '详细地址'),
                                field: 'address',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话'),
                                field: 'telphone',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zipCode`, '邮编'),
                                field: 'fax',
                                width: 150
                            }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankInfo`, '银行信息'), groupCode: 'bankInfo', type: 'grid', custom: {
                        ref: 'supplierBankInfoList',
                        columns: [
                            {
                                type: 'checkbox', width: 40
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                                field: 'itemNumber',
                                disable: true,
                                width: 50
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_toElsAccount`, '采购维护账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                required: '0',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCountry`, '银行国家'),
                                field: 'bankCountry',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankProvince`, '银行省份'),
                                field: 'bankProvince',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCity`, '银行城市'),
                                field: 'bankCity',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankBranchName`, '开户行全称'),
                                field: 'bankBranchName',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankAccount`, '银行账号'),
                                field: 'bankAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankAccountName`, '银行账号名称'),
                                field: 'bankAccountName',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cooperationBankType`, '合作银行类型'),
                                field: 'cooperationBankType',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCode`, '银行代码'),
                                field: 'bankCode',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCodeIBAN`, 'IBAN（国际银行帐户号码）'),
                                field: 'iban',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_swiftCode`, 'SWIFT CODE（银行国际代码）'),
                                field: 'swiftCode',
                                width: 150
                            }
                            // ,
                            // {
                            //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedName`, '开户资料扫描件名称'),
                            //     field: 'fileName',
                            //     width: 150
                            // },
                            // {
                            //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedPath`, '开户资料扫描件地址'),
                            //     field: 'filePath',
                            //     width: 150
                            // }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'supplierInfoChangeAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/supplier/supplierMaster/queryById',
                confirm: '/ebidding/purchaseEbiddingHead/confirmBid',
                download: '/attachment/purchaseAttachment/download'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount
            if(!elsAccount || elsAccount==''){
                elsAccount = this.currentEditRow.elsAccount
            }
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_supplierMasterData_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                } else {
                    this.$message.error(res.message)
                }
            })
        }else if (this.currentEditRow.changeNumber && this.currentEditRow.initiatorElsAccount){
            this.url.detail = '/supplier/supplierInfoChangeHead/queryAfterInfoById'
            this.showRemote = true
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        // 下载企业信息变更文件
        downloadEvent (row) {
            const params = {id: row.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        // 预览企业信息变更文件
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        }
    }
}
</script>