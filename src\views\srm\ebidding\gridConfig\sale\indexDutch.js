import { srmI18n, getLangAccount } from '@/utils/util'


export const materialGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showHeaderOverflow: true,
    showOverflow: true,
    highlightHoverRow: true,
    loading: false,
    headerAlign: 'center',
    editConfig: {
        trigger: 'click',
        mode: 'row'
    },
    columns: [
        { field: 'materialNumber', title: srmI18n(`${getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120, align: 'center', showOverflow: true },
        { field: 'materialName', title: srmI18n(`${getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialDesc', title: srmI18n(`${getLangAccount()}#i18n_title_materialDescription`, '物料描述'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialSpec', title: srmI18n(`${getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 120, align: 'center', showOverflow: true },
        { field: 'factory_dictText', title: srmI18n(`${getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'), width: 120, align: 'center', showOverflow: true },
        { field: 'storageLocation_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_storageLocation`, '库存地点'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireQuantity', title: srmI18n(`${getLangAccount()}#i18n_title_needCout`, '需求数量'), width: 120, align: 'center', showOverflow: true },
        { field: 'purchaseUnit_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireDate', title: srmI18n(`${getLangAccount()}#i18n_title_demandDate`, '需求日期'), width: 120, align: 'center', showOverflow: true },
        { field: 'startPrice', title: srmI18n(`${getLangAccount()}#i18n_field_AAu_21f5181`, '起拍价'), width: 120, showOverflow: true }
    ],
    data: []
}

export const quoteGridOptions = {
    size: 'mini',
    border: true,
    showOverflow: true,
    loading: false,
    headerAlign: 'center',
    editConfig: {
        trigger: 'click',
        mode: 'row'
    },
    columns: [
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价')
        },
        {
            field: 'bidNumber',
            title: srmI18n(`${getLangAccount()}#i18n_field_eBWR_2e6375f1`, '投标数量'),
            align: 'center',
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'bidNumber_default', header: 'price_header' }
        },
        { title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'), align: 'center', slots: { default: 'operation' }, fixed: 'right' }
    ],
    data: []
}

export const quotePackGridOptions = {
    size: 'mini',
    border: true,
    showOverflow: true,
    loading: false,
    headerAlign: 'center',
    editConfig: {
        trigger: 'click',
        mode: 'row'
    },
    columns: [
        {
            field: 'totalAmount',
            title: srmI18n(`${getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额')
        },
        {
            field: 'bidNumber',
            title: srmI18n(`${getLangAccount()}#i18n_field_eBWR_2e6375f1`, '投标数量'),
           
            slots: { default: 'bidNumber_default', header: 'price_header' }
        },
        { title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'), align: 'center', slots: { default: 'operation' }, fixed: 'right' }
    ],
    data: []
}


export const quotaPackGridDeOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    height: 320,
    loading: false,
    headerAlign: 'center',
    addBtn: true,
    columns: [
        {
            field: 'netTotalAmount',
            title: srmI18n(`${getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未税金额'),
            slots: {
                default: 'netTotalAmount_default',
                header: 'price_header'
            }
        },
        {
            field: 'totalAmount',
            title: srmI18n(`${getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'),
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: {
                default: 'totalAmount_default',
                header: 'price_header'
            }
        },
        {
            field: 'taxCode',
            title: srmI18n(`${getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'enterTaxCode_default', header: 'price_header' }
        },
        {
            field: 'taxRate',
            title: srmI18n(`${getLangAccount()}#i18n_field_fIW_1d82c07`, '税率'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'enterTaxRate_default', header: 'price_header' }
        },
        {
            field: 'bidNumber',
            title: srmI18n(`${getLangAccount()}#i18n_field_eBWR_2e6375f1`, '投标数量'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'bidNumber_default', header: 'price_header' }
        }
      
    ],

    data: []
}


export const quotaGridDeOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    height: 320,
    loading: false,
    headerAlign: 'center',
    addBtn: true,
    columns: [
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'price_default', header: 'price_header' }
        },
        {
            field: 'netPrice',
            title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'netPrice_default', header: 'price_header' }
        },
        {
            field: 'taxCode',
            title: srmI18n(`${getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'enterTaxCode_default', header: 'price_header' }
        },
        {
            field: 'taxRate',
            title: srmI18n(`${getLangAccount()}#i18n_field_fIW_1d82c07`, '税率'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'enterTaxRate_default', header: 'price_header' }
        },
        {
            field: 'bidNumber',
            title: srmI18n(`${getLangAccount()}#i18n_field_eBWR_2e6375f1`, '投标数量'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'bidNumber_default', header: 'price_header' }
        }
       
    ],
   
    data: []
}