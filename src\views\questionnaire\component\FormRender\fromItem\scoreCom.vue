<template>
  <a-form-item
    :label="index +'.' + field.label"
    validate-status="error"
    :help="field.validateOption.message"
    :required="!!field.rules && field.rules.length > 0"
  >
    <a-rate
      :count="10"
      :disabled="field.disabled"
      v-decorator="[field.id, { initialValue: field.initialValue }]" />
  </a-form-item>
</template>

<script>
export default {
    name: 'InputCom',
    props: {
        field: {
            type: Object,
            required: true
        }
    },
    inject: ['index'],
    created () {
    }
}
</script>

<style scoped>

</style>
