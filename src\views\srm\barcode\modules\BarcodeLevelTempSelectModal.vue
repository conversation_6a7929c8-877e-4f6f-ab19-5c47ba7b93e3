<template>
  <div>
    <a-modal
    v-drag     
      v-model="visible" 
      title=""
      @ok="handleOk">
      <a-form-model
        ref="formDom"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :model="form"
        :loading="loading">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_purchaser`,'采购方')"
          prop="purchase" >
          <a-select
            v-model="form.purchase" 
            @change="purchaseChange"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectPurchaser`,'请选择采购方')">
            <a-select-option 
              v-for="el of purchaseArr"
              :key="el.elsAccount"
              :value="el.elsAccount"
            >
              {{ el.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_template`,'模板')"
          prop="temp"
          v-show="tempArr.length > 0">
          <a-select
            v-model="form.temp" 
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')">
            <a-select-option
              v-for="el of tempArr"
              :key="el.id"
              :value="el.id"
            >
              {{ el.templateName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'FieldSelectModal',
    props: ['pageData'],
    data () {
        return {
            visible: false,
            form: {
                purchase: '',
                temp: ''
            },
            labelCol: { span: 4 },
            wrapperCol: { span: 14 },
            tempArr: [],
            purchaseArr: [],
            loading: false,
            rules: {
                purchase: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectAPurchase`, '请选择一个采购')}],
                temp: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_youNeedToSelectABoard`, '模板需要选择一个')}]
            }
        }
    },
    methods: {
        handleOk () {
            if(this.form.temp) {
                const that = this
                let params = {
                    toElsAccount: this.form.purchase,
                    templateNumber: this.form.temp
                }
                console.log(params)
                postAction('/base/barcode/saleBarcodeLevelHead/add', params).then(res => {
                    if(res.success) {
                        if(res.result) {
                            that.$emit('success', res.result)
                        }
                    }else {
                        that.$message.warning(res.message)
                    }
                    that.visible = false
                    that.submitLoading = false
                })
            }
        },
        loadData (data) {
            this.loading = true
            getAction(data.url, data.params).then((res) => {
                if(res.success) {
                    let list = res.result.records || []
                    this.purchaseArr = list
                }
                this.loading = false
            })
        },
        queryTemplateList (elsAccount) {
            let params = {pageSize: 100, elsAccount, templateStatus: '1', businessType: this.pageData.businessType, pageNo: 1}
            return getAction('/template/templateHead/getListByType', params)
        },
        async purchaseChange (val) {
            console.log(val)
            let res = await this.queryTemplateList(val)
            let list = res.result || []
            this.tempArr = list
            console.log(res)
        },
        open (data) {
            this.visible = true
            this.loadData(data)
        }
    }

}
</script>

<style>

</style>