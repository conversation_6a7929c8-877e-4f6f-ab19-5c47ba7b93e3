<template>
  <div
    class="SaleDeliveryNoticeOrderEdit"
    v-if="showPage"
  >
    <a-spin :spinning="confirmLoading">
      <a-tabs
        class="tabs"
        :hideAdd="true"
        type="editable-card"
        v-model="activeKey"
        @tabClick="tabCallBack"
        @edit="remove"
      >
        <a-tab-pane
          v-for="pane in tabs"
          :key="`${pane.id}`"
          :tab="pane.title"
          forceRender
        >
          <div class="page-container">
            <edit-layout
              :ref="`editPage${pane.id}`"
              :page-data="pageData"
              :url="url"
              :headerTitle="$srmI18n(`${$getLangAccount()}#i18n_title_createShipment`, '创建发货单')"
              :currentEditRow="currentEditRow"
              :collapseActiveKey="collapseActiveKey"
              useLocalModelLayout
              :singleGroupCoverConfig="singleGroupCoverConfig"
              modelLayout="collapse"
              @stepChange="stepChange"
              @nextStepHandle="stepChange"
              @preStepHandle="stepChange"
              @collapseChange="stepChange"
            >
            </edit-layout>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
    <!-- 行明细弹出选择框 -->
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { getAction, postAction } from '@/api/manage'
import { SALEATTACHMENTDOWNLOADAPI } from '@/utils/const'
import { cloneDeep } from 'lodash'
import fieldSelectModal from '@comp/template/fieldSelectModal'
export default {
  mixins: [EditMixin],
  components: {
    fieldSelectModal
  },
  data() {
    return {
      collapseActiveKey: [],
      confirmLoading: false,
      showRemote: false,
      activeKey: '0',
      pageData: {
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shippingLineInfo`, '发货行信息'),
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'saleDeliveryItemList',
              columns: [],
              buttons: [{ title: '新增赠品', click: this.addGifts }],
              showOptColumn: true,
              optColumnList: [
                { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_split`, '拆分'), clickFn: this.splitRow },
                { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.splitDelete, allow: this.allowSplitDelete }
              ]
            }
          },
          {
            groupName: '辅料发料',
            groupCode: 'saleDeliverySubList',
            type: 'grid',
            custom: {
              ref: 'saleDeliverySubList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'arrivedTime', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_uSKI_277b0df5`, '到货时间'), width: 120, fieldType: 'input' },
                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_PurchaseMassProdHeadList_materialCode`, '物料编码'), width: 120 },
                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}i18n_massProdHead0e15_materialName`, '物料名称'), width: 120 },
                {
                  field: 'deliveryQuantity',
                  title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_hSWR_28388855`, '发货数量'),
                  width: 120,
                  fieldType: 'number',
                  extend: { min: 0 },
                  slots: {},
                  bindFunction: function bindFunction(row, column, val) {
                    row.deliveryQuantity = val
                    if (!!row.deliveryQuantity) {
                      row.secondaryQuantity = row.deliveryQuantity / (row.conversionRate || 1)
                    } else {
                      row.secondaryQuantity = 0
                    }
                    row.secondaryQuantity = row.secondaryQuantity.toFixed(6)
                  }
                },
                { field: 'quantityUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_dtL_1301213`, '主单位'), width: 120 },
                { field: 'conversionRate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_conversionRate`, '换算率'), width: 120 },
                { field: 'secondaryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_secondaryQuantity`, '辅数量'), width: 120 },
                { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_BtL_22528dd`, '辅单位'), width: 120 },
                { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_lineStatus`, '行状态'), width: 120 },
                { field: 'factory', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_GMVR_2c647268`, '库存组织'), width: 120, slots: { default: 'renderDictLabel' }, dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"' },
                { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_materialSpec`, '物料规格'), width: 120 },
                { field: 'receiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_receiveQuantity`, '收货数量'), width: 120 },
                { field: 'remainQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_bUlSWR_2ed782e0`, '剩余收货数量'), width: 120 },
                { field: 'batchNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_batchNumber`, '批次号'), width: 120 },
                { field: 'batch_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_batch`, '是否批次管理'), width: 120 },
                { field: 'ncQualityDayNum', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_ncQualityDayNum`, '保质天数'), width: 120 },
                {
                  field: 'productionDate',
                  title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_produceDate`, '生产日期'),
                  width: 120,
                  fieldType: 'date',
                  bindFunction: function bindFunction(row, column, val) {
                    if (!!val && row.batch != '0') {
                      row.batchNumber = val.replaceAll('-', '')
                      const date = new Date(val)

                      if (!!row.ncQualityDayNum) {
                        let ncQualityDayNum = (row.ncQualityDayNum && Number(row.ncQualityDayNum)) > 0 ? row.ncQualityDayNum - 1 : 0
                        date.setDate(date.getDate() + ncQualityDayNum)
                        row.expiryDate = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
                      }
                    } else {
                      row.batchNumber = null
                      row.expiryDate = null
                    }
                  }
                },
                { field: 'expiryDate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_expiryDate`, '失效日期'), width: 120 },
                { field: 'taxCode', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_taxCode`, '税码'), width: 120 },
                { field: 'taxRate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_taxRate`, '税率'), width: 120 },
                { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_price`, '含税价'), width: 120 },
                { field: 'netPrice', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_netPrice`, '净价'), width: 120 },
                { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_orderNumber`, '订单号'), width: 120 },
                { field: 'orderItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                { field: 'returnQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_returnQuantity`, '退货数量'), width: 120 },
                // { field: "receiveDateNow", title: '本次实际收货时间', width: 200, fieldType: 'date' },
                { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_requireDate`, '需求日期'), width: 120 },
                { field: 'overTolerance', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_overTolerance`, '超量容差率'), width: 120 },
                { field: 'purchaseRemark', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_purchaseRemark`, '需方备注'), width: 120, fieldType: 'input' },
                { field: 'supplierRemark', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_supplierRemark`, '供方备注'), width: 120 },
                { field: 'sourceType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceType`, '来源类型'), width: 120 },
                { field: 'sourceNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceNumber`, '来源单号'), width: 120 },
                { field: 'sourceItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceItemNumber`, '来源单行号'), width: 120 },
                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 120 },
                { field: 'onWayQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_onWayQuantity`, '订单在途数量'), width: 120 },
                { field: 'orderReceiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantitytitle`, '订单收货数量'), width: 120 },
                { field: 'notDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`, '订单未交货数量'), width: 120 },
                { field: 'gift', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_gift`, '是否赠品'), width: 120, dictCode: 'yn' }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                  type: 'primary',
                  click: this.addSubList
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  click: this.deleteSubList
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupCode: 'fileInfo',
            type: 'grid',
            custom: {
              ref: 'saleAttachmentList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rowIitem`, '行项目'), width: 120 },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
              ],
              buttons: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'delivery', attr: this.attrHandle, openOnce: true, callBack: this.uploadCallBack },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch }
              ],
              showOptColumn: true,
              optColumnList: [
                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadSaleEvent },
                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
              ]
            }
          }
        ],
        footerButtons: [
          // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save1`, '保存发货单'), type: 'primary', click: this.saveEvent},
          // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit2`, '保存并发布发货单'), type: 'primary', click: this.publishEvent}
        ],
        publicBtn: [
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IIsM_25aefac9`, '一键保存'), type: 'primary', click: this.allSave },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IIsMGhx_46ff41df`, '一键保存并发布'), type: 'primary', click: this.allPublish },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
        ]
      },
      url: {
        upload: '/attachment/saleAttachment/upload',
        material: '/material/purchaseMaterialHead/listByMaterialNumbers'
      },
      tabs: [],
      cacheTabList: [],
      validateIndex: 0,

      showPage: false
    }
  },
  props: {
    currentEditRow: {
      required: true,
      type: Object,
      default: () => {
        return {}
      }
    },
    sourceData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    fileSrc() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${account}/sale_delivery_${templateNumber}_${templateVersion}.js?t=` + time
    }
  },
  watch: {
    currentEditRow: {
      async handler(value) {
        console.log('test')
        let saleDeliveryItemList = cloneDeep(this.sourceData[0].saleDeliveryItemList || [])
        const materialNumbers = saleDeliveryItemList.map((v) => v.materialNumber)?.join(',')
        if (materialNumbers) {
          const resp = await getAction(this.url.material, { materialNumbers })
          if (resp.result?.length) {
            for (const meta of resp.result) {
              let dataIndex = saleDeliveryItemList.findIndex((i) => i.materialNumber === meta.materialNumber)
              if (dataIndex > -1)
                saleDeliveryItemList[dataIndex] = {
                  ...saleDeliveryItemList[dataIndex],
                  batch: meta.batch,
                  ncQualityDayNum: meta.ncQualityDayNum || 0
                }
            }
            console.log('saleDeliveryItemList', saleDeliveryItemList)
            saleDeliveryItemList = saleDeliveryItemList.map((item) => {
              if (item.price === 0 || Math.abs(item.price) > 0) {
                item.price = Number(item.price).toFixed(6)
              }
              if (item.netPrice === 0 || Math.abs(item.netPrice) > 0) {
                item.netPrice = Number(item.netPrice).toFixed(6)
              }
              return item
            })
            this.sourceData[0].saleDeliveryItemList = saleDeliveryItemList
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.createdFun()
    // this.showRemote = true
    // if(this.sourceData && this.sourceData.length >0){
    //     this.sourceData.forEach((v, i)=>{
    //         this.tabs.push({
    //             title: v.deliveryNumber,
    //             id: i
    //         })
    //         this.cacheTabList.push(`${i}`)
    //         console.log(this.cacheTabList, 'this.cacheTabList==')
    //     })
    // }
  },
  methods: {
    async createdFun() {
      if (this.sourceData && this.sourceData.length > 0) {
        this.confirmLoading = true
        let list = this.sourceData[0].saleDeliveryItemList || []

        // 初始化辅数量
        console.log('list', list)
        list = list.map((item) => {
          if (item.price === 0 || Math.abs(item.price) > 0) {
            item.price = Number(item.price).toFixed(6)
          }
          if (!!item.deliveryQuantity) {
            item.secondaryQuantity = item.deliveryQuantity / (item.conversionRate || 1)
          } else {
            item.secondaryQuantity = 0
          }
          item.secondaryQuantity = item.secondaryQuantity.toFixed(6)
          return item
        })

        const materialNumbers = list.map((v) => v.materialNumber)?.join(',')
        let res = await getAction(this.url.material, { materialNumbers })
        console.log('res::======>', res)
        if (res.code == 200) {
          list.forEach((item) => {
            let materialNumber = item.materialNumber
            let resItem = (res.result || []).find((i) => i.materialNumber == materialNumber)
            if (!!resItem) item.ncQualityDayNum = resItem.ncQualityDayNum || 0
          })
        }
        this.confirmLoading = false
      }
      this.showPage = true
      this.showRemote = true
      if (this.sourceData && this.sourceData.length > 0) {
        this.sourceData.forEach((v, i) => {
          this.tabs.push({
            title: v.deliveryNumber,
            id: i
          })
          this.cacheTabList.push(`${i}`)
          console.log(this.cacheTabList, 'this.cacheTabList==')
        })
      }
    },
    downloadSaleEvent(row) {
      this.$refs[`editPage${this.activeKey}`][0].handleDownload(row, SALEATTACHMENTDOWNLOADAPI)
    },
    singleGroupCoverConfig(config, group, displayModel) {
      if (displayModel === 'tab') {
        config.height = config.height - 46
      }
      return config
    },
    attrHandle() {
      return {
        sourceNumber: this.currentEditRow.noticeNumber,
        actionRoutePath: '/srm/delivery/PurchaseDeliveryNoticeList,/srm/delivery/sale/SaleDeliveryNoticeList'
      }
    },
    deleteFilesEvent(row) {
      const fileGrid = this.$refs[`editPage${this.activeKey}`][0].$refs.saleAttachmentList[0]
      getAction('/attachment/saleAttachment/delete', { id: row.id }).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.remove(row)
      })
    },
    deleteBatch() {
      const fileGrid = this.$refs[`editPage${this.activeKey}`][0].$refs.saleAttachmentList[0]
      const checkboxRecords = fileGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      const ids = checkboxRecords.map((n) => n.id).join(',')
      const params = {
        ids
      }
      getAction('/attachment/saleAttachment/deleteBatch', params).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.removeCheckboxRow()
      })
    },
    downloadEvent(row) {
      this.$refs[`editPage${this.activeKey}`][0].handleDownload(row)
    },
    uploadCallBack(result) {
      let fileGrid = this.$refs[`editPage${this.activeKey}`][0].$refs.saleAttachmentList[0]
      fileGrid.insertAt(result, -1)
    },
    splitRow(row) {
      const _row = {
        ...row,
        id: null
      }
      delete _row._X_ROW_KEY
      let deliveryPlanItemGrid = this.$refs[`editPage${this.activeKey}`][0].$refs.saleDeliveryItemList[0]
      deliveryPlanItemGrid.insertAt(_row, -1)
    },
    allowSplitDelete(row) {
      let id = row.id
      if (id) {
        return true
      }
      return false
    },
    splitDelete(row) {
      let deliveryPlanItemGrid = this.$refs[`editPage${this.activeKey}`][0].$refs.saleDeliveryItemList[0]
      deliveryPlanItemGrid.remove(row)
    },
    afterHandleData(data) {
      const _columItem = { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
      data.groups.forEach((v, i) => {
        setTimeout(() => {
          if (v.groupCode === 'baseForm') {
            if (v.custom.validateRules.logisticsCompany) {
              v.custom.validateRules.logisticsCompany[0].message = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SQRClS_6583da4a`, '物流公司必填')
            }
            if (v.custom.validateRules.trackingNumber) {
              v.custom.validateRules.trackingNumber[0].message = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SQtylS_66622000`, '物流单号必填')
            }
          }
        }, 2000)
        if (v.groupCode === 'itemInfo') {
          v.custom.columns.splice(2, 0, _columItem)
        }
        this.collapseActiveKey.push(`${i}`)
      })
    },
    preViewEvent(row) {
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    saveEvent() {
      this.confirmLoading = true
      const pageData = this.$refs[`editPage${this.activeKey}`][0].getPageData()
      console.log(pageData, 'pageData====')
      postAction('/delivery/saleDeliveryHead/planToDeliverySave', pageData)
        .then((res) => {
          this.confirmLoading = false
          if (res.success) {
            this.$message.success(res.message)
          } else {
            this.$message.warning(res.message)
          }
        })
        .catch(() => {
          this.confirmLoading = false
        })
    },
    publishEvent() {
      const handlePromise = (list = []) =>
        list.map((promise) =>
          promise.then(
            (res) => ({
              status: 'success',
              res
            }),
            (err) => ({
              status: 'error',
              err
            })
          )
        )
      let promise = this.$refs[`editPage${this.activeKey}`][0].setPromise()
      Promise.all(handlePromise(promise))
        .then((result) => {
          let flag = false
          for (let i = 0; i < result.length; i++) {
            if (result[i].status === 'success') {
              flag = true
            } else {
              return
            }
          }
          if (flag) {
            this.confirmLoading = true
            const pageData = this.$refs[`editPage${this.activeKey}`][0].getPageData()
            postAction('/delivery/saleDeliveryHead/planToDeliveryPush', pageData)
              .then((res) => {
                this.confirmLoading = false
                if (res.success) {
                  this.$message.success(res.message)
                } else {
                  this.$message.warning(res.message)
                }
              })
              .catch(() => {
                this.confirmLoading = false
              })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getCurrentDate(){
          const date = new Date()
          const year = date.getFullYear().toString().padStart(4, '0')
          const month = (date.getMonth() + 1).toString().padStart(2, '0')
          const day = date.getDate().toString().padStart(2, '0')
          const nowDateStr = year + '-' + month + '-' + day
          return nowDateStr
    },
    allSave() {
      this.confirmLoading = true
      const tabs = this.tabs
      let allData = []
      let proDateErrorStr = ''
      tabs.forEach((v) => {
        const pageData = this.$refs[`editPage${v.id}`][0].getPageData()
        //let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}

        let saleDeliveryItemList = this.$refs[`editPage${v.id}`][0].$refs.saleDeliveryItemList[0]
        let { fullData } = saleDeliveryItemList.getTableData()
          let curDate = this.getCurrentDate()
          fullData.map(item=>{
              if(item.productionDate!==''&&item.productionDate>curDate){
                  let mess =item.materialNumber+'_'+ item.materialName
                  proDateErrorStr = '物料为:'+'['+mess+']所在行,生产日期不得大于当前日期'
                  return
              }
          })
        pageData.saleDeliveryItemList = fullData

        allData.push(pageData)
      })
      console.log(allData.saleDeliveryItemList, 'allData===')
      if(proDateErrorStr!=null && proDateErrorStr!=''){
          this.confirmLoading =false
         this.$message.warning(proDateErrorStr)
         return
      }
      postAction('/delivery/saleDeliveryHead/planToDeliverySaveList', allData)
        .then((res) => {
          this.confirmLoading = false
          if (res.success) {
            this.$message.success(res.message)
          } else {
            this.$message.warning(res.message)
          }
        })
        .catch(() => {
          this.confirmLoading = false
        })
    },
    allPublish() {
      const handlePromise = (list = []) =>
        list.map((promise) =>
          promise.then(
            (res) => ({
              status: 'success',
              res
            }),
            (err) => ({
              status: 'error',
              err
            })
          )
        )
      let promise = this.$refs[`editPage${this.tabs[this.validateIndex].id}`][0].setPromise()
      Promise.all(handlePromise(promise))
        .then((result) => {
          let flag = false
          for (let i = 0; i < result.length; i++) {
            if (result[i].status === 'success') {
              flag = true
              console.log('true')
            } else {
              if (i === 0) {
                this.$message.error('表单数据必填项没填写完成')
              }
              break
            }
          }
          if (flag) {
            if (this.validateIndex < this.tabs.length - 1) {
              this.validateIndex++
              this.allPublish()
              return
            }
            this.confirmLoading = true
            const tabs = this.tabs
            let allData = []
            let proDateErrorStr = ''
            tabs.forEach((v) => {
              const pageData = this.$refs[`editPage${v.id}`][0].getPageData()
              let saleDeliveryItemList = this.$refs[`editPage${v.id}`][0].$refs.saleDeliveryItemList[0]
              let { fullData } = saleDeliveryItemList.getTableData()
                let curDate = this.getCurrentDate()
                fullData.map(item=>{
                    if(item.productionDate!==''&&item.productionDate>curDate){
                        let mess = item.materialNumber+'_'+item.materialName
                        proDateErrorStr='物料为:'+'['+mess+']所在行,生产日期不得大于当前日期'
                        return
                    }
                })
              pageData.saleDeliveryItemList = fullData
              let stamp= new Date().getTime() + 8 * 60 * 60 * 1000;
              let currentTime = new Date(stamp).toISOString().replace(/T/, ' ').replace(/\..+/, '').substring(0, 19);
              pageData.deliveryTime = currentTime
              allData.push(pageData)
            })
              if(proDateErrorStr!=null && proDateErrorStr!=''){
                  this.confirmLoading = false
                  this.$message.warning(proDateErrorStr)
                  return
              }
            postAction('/delivery/saleDeliveryHead/planToDeliveryPushList', allData)
              .then((res) => {
                this.confirmLoading = false
                if (res.success) {
                  this.goBack()
                  this.$message.success(res.message)
                } else {
                  let resMsg = res.message
                  if (!!resMsg && resMsg.indexOf('\n') >= 0) {
                    const h = this.$createElement
                    let strList = resMsg.split('\n')
                    strList = strList.map((str, strIndex) => {
                      return h(strIndex === 0 ? 'span' : 'div', null, str)
                    })
                    resMsg = h('span', null, strList)
                  }
                  this.$message.warning(resMsg)
                }
              })
              .catch(() => {
                this.confirmLoading = false
              })
          } else {
            this.activeKey = this.tabs[this.validateIndex].id.toString()
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    init() {
      if (this.sourceData && this.sourceData.length > 0) {
        console.log('sourceData', this.sourceData)
        this.sourceData.forEach((v, i) => {
          console.log(this.$refs[`editPage${i}`], 'this.$refs[`editPage${i}`]')
          this.$refs[`editPage${i}`] && this.$refs[`editPage${i}`][0].setDetailData(v)
        })
      }
    },
    tabCallBack(activeKey) {
      console.log(activeKey)
    },
    remove(key) {
      if (this.tabs.length === 1) {
        this.$message.warning('这是最后一页，不能再关闭了!')
        return false
      }
      this.tabs = this.tabs.filter((item) => item.id != key)
      let index = this.cacheTabList.indexOf(key)
      this.cacheTabList = this.cacheTabList.filter((item) => item != key)
      index = index >= this.cacheTabList.length ? this.cacheTabList.length - 1 : index
      this.activeKey = this.cacheTabList[index]
      console.log(this.activeKey, 'this.activeKey===')
    },

    // 新增辅料发料弹框
    addSubList() {
      this.fieldSelectType = 'subOrder'
      let url = '/delivery/saleDeliverySub/queryDeliverySubOrderByMainSupplier'
      let columns = [
        { field: 'subElsAccount', title: '供应商els账号', width: 150, showOverflow: true },
        { field: 'subSupplierName', title: '供应商名称', width: 150, showOverflow: true },
        { field: 'orderNumber', title: '采购订单号', width: 150, showOverflow: true },
        { field: 'orderItemNumber', title: '订单行号', width: 150, showOverflow: true },
        { field: 'materialNumber', title: '物料编码', width: 150, showOverflow: true },
        { field: 'materialName', title: '物料名称', width: 150, showOverflow: true },
        { field: 'materialDesc', title: '物料描述', width: 150, showOverflow: true },
        { field: 'quantity', title: '订单数量', width: 150, showOverflow: true },
        { field: 'canDeliveryQuantity', title: '剩余可发货数量', width: 150, showOverflow: true },
        { field: 'quantityUnit_dictText', title: '主单位', width: 150, showOverflow: true },
        { field: 'purchaseUnit_dictText', title: '辅单位', width: 150, showOverflow: true }
      ]
      let form = this.$refs[`editPage${this.activeKey}`][0].getPageData()
      console.log(form)

      let params = {
        mainSupplierCode: form.supplierCode, // 主供应商ELS编码（必传，即当前供应商编码）
        factory: form.factory // 库存组织
      }
      let errMsg = ''
      if (!params.mainSupplierCode) errMsg = '主供应商编码'
      else if (!params.factory) errMsg = '库存组织'

      if (!!errMsg) return this.$message.warning(`请先选择${errMsg}`)
      this.$refs.fieldSelectModal.open(url, params, columns, 'multiple')
    },

    // 删除辅料发料
    deleteSubList() {
      let itemGrid = this.$refs[`editPage${this.activeKey}`][0].$refs.saleDeliverySubList[0]
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning('请选择数据！')
        return
      }

      itemGrid.removeCheckboxRow()
    },

    // 回填辅料信息
    fieldSelectOk(data) {
      let itemGrid = this.$refs[`editPage${this.activeKey}`][0].$refs.saleDeliverySubList[0]
      let { fullData } = itemGrid.getTableData()
      let orderList = fullData.map((item) => {
        return item.materialNumber
      })
      //过滤已有数据
      let insertData = data.filter((item) => {
        return !orderList.includes(item.materialNumber)
      })

      insertData = insertData.map((item) => {
        item = {
          id: null,
          sourceId: item.sourceId,
          orderNumber: item.orderNumber,
          orderId: item.orderId,
          materialDesc: item.materialDesc,
          purchaseRemark: null, // 采购方传入，供应方自己输入
          sourceItemId: item.sourceItemId,
          supplierRemark: item.supplierRemark,
          netPrice: item.netPrice,
          cateName: item.cateName,
          materialNumber: item.materialNumber,
          price: item.price,
          secondaryQuantity: null,
          requireDate: item.requireDate,
          batch: item.batch,
          materialGroupName: item.materialGroupName,
          taxCode: item.taxCode,
          cateCode: item.cateCode,
          ncQualityDayNum: item.ncQualityDayNum,
          materialName: item.materialName,
          purchaseUnit: item.purchaseUnit,
          taxRate: item.taxRate,
          sourceType: item.sourceType,
          subElsAccount: item.subElsAccount,
          subSupplierName: item.subSupplierName,
          materialGroup: item.materialGroup,
          orderItemNumber: item.orderItemNumber,
          toElsAccount: item.toElsAccount,
          overTolerance: item.overTolerance,
          quantityUnit: item.quantityUnit,
          lackTolerance: item.lackTolerance,
          itemStatus: item.itemStatus,
          deliveryQuantity: item.deliveryQuantity || item.quantity || 0,
          remainQuantity: item.deliveryQuantity || 0,
          currency: item.currency,
          quantity: item.quantity,
          orderItemId: item.orderItemId,
          materialSpec: item.materialSpec,
          sourceItemNumber: item.sourceItemNumber,
          sourceNumber: item.sourceNumber,
          conversionRate: item.conversionRate,
          saleMaterialNumber: item.saleMaterialNumber,
          arrivedTime: null,
          batchNumber: null,
          productionDate: null,
          expiryDate: null,

          storageLocation: null,
          busAccount: null,
          factory: item.factory,
          factory_dictText: item.factory_dictText,
          batch_dictText: item.batch_dictText,
          purchaseUnit_dictText: item.purchaseUnit_dictText,
          quantityUnit_dictText: item.quantityUnit_dictText,
          itemStatus_dictText: item.itemStatus_dictText,
          onWayQuantity: item.onWayQuantity,
          orderReceiveQuantity: item.receiveQuantity,
          notDeliveryQuantity: item.notDeliveryQuantity,
          sourceType_dictText: item.sourceType_dictText
        }

        if (!!item.deliveryQuantity) {
          item.secondaryQuantity = item.deliveryQuantity / (item.conversionRate || 1)
        } else {
          item.secondaryQuantity = 0
        }
        item.secondaryQuantity = item.secondaryQuantity.toFixed(6)

        if (item.price === 0 || Math.abs(item.price) > 0) {
          item.price = Number(item.price).toFixed(6)
        }

        return item
      })

      itemGrid.insertAt(insertData, -1)
    },

    stepChange(data) {
      console.log('更改辅料发料可编辑状态', data)
      if (data.groupData?.groupCode === 'saleDeliverySubList') {
        let itemGrid = this.$refs[`editPage${this.activeKey}`][0].$refs.saleDeliverySubList[0]
        let columns = itemGrid.getColumns()
        let needRefresh = false
        columns.forEach((col) => {
          if (col.field == 'deliveryQuantity' && !col.editRender) {
            needRefresh = true
          }
        })
        console.log('needRefresh', needRefresh)
        if (needRefresh) {
          let { fullData } = itemGrid.getTableData()
          this.$refs[`editPage${this.activeKey}`][0].pageContentKey++
          setTimeout(() => {
            itemGrid.reloadData(fullData)
          }, 500)
        }
      }
    },
    // 新增赠品
    addGifts() {
      let itemGrid = this.$refs[`editPage${this.activeKey}`][0].$refs.saleDeliveryItemList[0]
      let checkboxRecords = cloneDeep(itemGrid.getCheckboxRecords())
      if (!checkboxRecords.length) {
        this.$message.warning('请选择数据！')
        return
      }
      checkboxRecords = checkboxRecords.map(item => {
        delete item._X_ROW_KEY;
        item.id = null;
        item.price = 0;
        item.netPrice = 0;
        item.gift = 1;
        item.gift_dictText = '是';
        return item;
      })
      itemGrid.insertAt(checkboxRecords, -1)
    },
  }
}
</script>

<style lang="scss" scoped>
.SaleDeliveryNoticeOrderEdit {
  height: 100%;

  .ant-spin-nested-loading {
    height: 100%;

    :deep(.ant-spin-container) {
      height: 100%;
    }
  }

  .tabs {
    background: #fff;
  }

  :deep(.ant-tabs-bar) {
    margin: 0;
  }

  :deep(.edit-grid-box) {
    position: static !important;
  }

  :deep(.ant-tabs) {
    height: 100%;
  }

  :deep(.ant-tabs-content) {
    height: 100%;
  }

  :deep(.ant-tabs-tabpane) {
    height: 100%;
  }

  :deep(.ant-tabs-no-animation > .ant-tabs-content > .ant-tabs-tabpane-inactive) {
    height: 0;
  }

  .page-container {
    height: calc(100% - 40px) !important;
  }
}
</style>
