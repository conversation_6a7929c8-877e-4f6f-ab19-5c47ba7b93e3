<template>
  <div class="iot-step-container">
    <div class="tips">{{ $srmI18n(`${$getLangAccount()}#i18n_field_DKW_1865cf0`, '提示：') }}<span class="blue">{{ $srmI18n(`${$getLangAccount()}#i18n_field_By_109935`, '蓝色') }}</span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_LQLRIROyCW_3d708618`, '为流程关键功能节点，') }}<span class="grey">{{ $srmI18n(`${$getLangAccount()}#i18n_field_My_e2002`, '灰色') }}</span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_LuRIROyC_b8ae4b34`, '为非关键功能节点') }}</div>
    <div class="iot-step-wrapper">
      <div
        @click="handleClickStepItem(item,index)"
        class="iot-step-item"
        :class="[{'isFinish': currentStep > (index+1)},{'active': currentStep === (index+1)}]"
        v-for="(item, index) in stepList"
        :key="index">
        <!-- <div class="iot-step-number">{{ currentStep <= (index+1) ? (index + 1) : '' }}</div> -->
        <div class="iot-step-number"></div>
        <div class="iot-step-item-title">{{ stepList[index] }}</div>
        <div class="iot-step-line"></div>
        <div class="nodeListBox">
          <div
            class="nodeList"
            v-for="nodeItem in allNodeMap[index]"
            :key="nodeItem.nodeName+'_'+nodeItem.id">
            <div
              class="nodeItem"
              :class="[nodeItem.mustNode =='1' ? 'mustNode': 'notMustNode']">{{ nodeItem.nodeName }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="nodeListBox">
      <div
        class="nodeList"
        v-for="(nodeList, listIndex) in allNodeMap"
        :key="listIndex">
        <div
          v-for="nodeItem in nodeList"
          :key="nodeItem.nodeName+'_'+nodeItem.id">
          <div
            class="nodeItem"
            :class="[nodeItem.mustNode =='1' ? 'mustNode': 'notMustNode']">{{ nodeItem.nodeName }}</div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
export default {
    name: 'IotStep',
    props: {
        currentStep: { 
            default: 0,
            type: [Number, String]
        },
        stepList: {
            type: [Array, Object]
        },
        allNodeMap: {
            type: [Array, Object]
        }
    },
    methods: {
        handleClickStepItem (item, index) {
            console.log('item', item)
            console.log('index', index)
        }
    }
}
</script>

<style lang="scss" scoped>
$blue: #1890ff;
$grey: #CED1DB;
.blue{
  color: $blue;
}
.grey{
  color: $grey;
}
.iot-step-container {
  width: 100%;
  margin-bottom: 10px;
  overflow: hidden;
  min-height: 600px;
}
.iot-step-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
.iot-step-item {
  flex: 1;
  position: relative;
  margin-right: 4px;
  box-sizing: border-box;
  // overflow: hidden;
  transform: translateX(41%);
  &.active {
    .iot-step-item-title {
      color: #0C1014;
      font-weight: 600;
    }
    .iot-step-number {
      background-color: $blue;
    }
  }
  &.isFinish {
    .iot-step-item-title {
      color: #4B5869;
    }
    .iot-step-number {
      width: 22px;
      height: 22px;
      // background: url(../../assets/image/step-check.png);
      background-size: 100%;
    }
    .iot-step-line {
      top: 12px;
      background-color: $blue;
    }
  }
}
.iot-step-item:last-child {
  .iot-step-line {
    width: 0;
  }
}
.iot-step-number {
  background-color: #999EB1;
  width: 18px;
  margin-left: 5px;
  height: 18px;
  border-radius: 50%;
  font-size: 12px;
  color: #fff;
  text-align: center;
  line-height: 18px;
}
.iot-step-item-title {
  position: relative;
  margin-top: 10px;
  font-size: 14px;
  color: #999EB1;
}
.iot-step-line {
  position: absolute;
  left: 30px;
  top: 10px;
  width: 75%;
  box-sizing: border-box;
  height: 1px;
  background-color: $grey;
}
.tips{
  margin-bottom: 20px;
}
.nodeListBox{
  width: 100px;
  position:absolute;
  height: 520px;
  overflow-y: auto;
  top: 56px;
  left: -34px;
}
// .nodeListBox{
//   margin-top: 20px;
//   display: flex;
//   // align-items: center;
//   justify-content: center;
// }
.nodeList{
  // flex: 1;
  // position: relative;
  text-align: center;
  font-size: 14px;
}
          
.nodeItem {
  text-align: center;
  margin-bottom: 20px;
  width:96%;
  padding: 10px 0;
}
.mustNode{
  border: 1px solid $blue;
  color: $blue;
}
.notMustNode{
  border: 1px solid $grey;
  color: $grey; 
}
</style>
