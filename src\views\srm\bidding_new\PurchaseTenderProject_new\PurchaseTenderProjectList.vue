<template>
  <div style="height: 100%">
    <list-layout
      :tabsList="tabsList"
      ref="listPage"
      v-show="!showEditPage && !showDetailPage && !showMaterial"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <purchase-tender-project-edit
      v-if="showEditPage"
      ref="supplierCapacity"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <purchase-tender-project-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <materialComplement
      v-if="showMaterial"
      ref="materialPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <a-modal
      :visible="modalVisible"
      width="1000px"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_dILjGR_e8e9c8cd`, '项目成员设置')"
      :okText="okText"
      @ok="handleOk"
      @cancel="handleCancel"
      :confirmLoading="confirmLoading"
      :footer="hideFooter">
      <titleCrtl>
        {{ $srmI18n(`${$getLangAccount()}#i18n_field_dILjAB_e8e2d68e`, '项目成员列表') }}

        <template slot="right">
          <a-button
            v-if="rolePermission == '1'"
            @click="addProjectMember">{{ $srmI18n(`${$getLangAccount()}#i18n_field_SudILj_7ffc2a22`, '添加项目成员') }}</a-button>
        </template>
      </titleCrtl>
      <list-table
        ref="memberListTable"
        :statictableColumns="statictableColumns"
        :pageData="modalPageData"
        setGridHeight="250"
        :fromSourceData="memberList"
        :showTablePage="false"> </list-table>

    </a-modal>
    <!-- <budgetChangeModal
      :modalVisible="showBudgetModal"
      :subpackageList="subpackageList"
      :modalStatus="getModalStatus"></budgetChangeModal> -->
    <a-modal
      :visible="modalBudgetVisible"
      width="1000px"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_UdAH_472ee8ef`, '预算变更')"
      @cancel="handleBudgetCancel"
      :footer="null">
      <BudgetChange
        v-if="modalBudgetVisible"
        :root="root"
        :currentEditRow="currentEditRow"></BudgetChange>
    </a-modal>
    

    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>
<script>
import listTable from '../BiddingHall/components/listTable'
import titleCrtl from '../BiddingHall/components/title-crtl'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
import { USER_INFO } from '@/store/mutation-types'
import PurchaseTenderProjectEdit from './modules/PurchaseTenderProjectEdit'
import PurchaseTenderProjectDetail from './modules/PurchaseTenderProjectDetail'
import materialComplement from './modules/materialComplement'
import BudgetChange from './modules/BudgetChange'

import { postAction, getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        PurchaseTenderProjectEdit,
        PurchaseTenderProjectDetail,
        BudgetChange,
        titleCrtl,
        listTable,
        materialComplement
    },
    data () {
        return {
            showMaterial: false,
            subpackageList: [],
            modalBudgetVisible: false,
            hideFooter: true,
            memberList: [],
            modalVisible: false,
            confirmLoading: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
            showEditPage: false,
            pageData: {
                businessType: 'biddingPlatform',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNdIvdRLWYBdIAySYBdIRL_42de5066`, '请输入项目立项名称、招标项目编号或招标项目名称')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                        fieldName: 'tenderProjectType',
                        dictCode: 'tenderProjectType'
                    }
                ],
                form: {
                },
                button: [
                    {
                        allow: ()=> {
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'add',
                        icon: 'plus',
                        authorityCode: 'tender#purchaseTenderProjectHead:add',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'tender#purchaseTenderProjectHead:edit', clickFn: this.handleEditSingle, allow: this.allowEdit, key: 'edit'},
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'tender#purchaseTenderProjectHead:queryById', clickFn: this.handleView },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingHall`, '招标大厅'), clickFn: this.toTender, allow: this.allowHall },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'tender#purchaseTenderProjectHead:delete', clickFn: this.handleDeleteSingle, allow: this.allowDel, key: 'delete'},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dILjGR_e8e9c8cd`, '项目成员设置'), clickFn: this.showModal},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dIUdAH_2370404`, '项目预算变更'), clickFn: this.showBudgetChangeModal, allow: this.allowChange},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xVSLVH_6e338e7e`, '补充物料信息'), clickFn: this.handleMaterial}
                ],
                optColumnWidth: 270,
                superQueryShow: false 
            },
            modalPageData: {
                optColumnList: [
                    // {
                    //     key: 'delete',
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    //     clickFn: this.modalDelete,
                    //     authorityCode: 'tender#purchaseTenderProjectHead:delete',
                    //     allow: this.allowDelete
                    // }
                ]
            },
            url: {
                list: '/tender/purchaseTenderProjectSubpackageInfo/list',
                columns: 'tenderProjectSubpackageList',
                add: '/tender/purchaseTenderProjectHead/add',
                edit: '/tender/purchaseTenderProjectHead/edit',
                queryById: '/tender/purchaseTenderProjectHead/queryById',
                delete: '/tender/purchaseTenderProjectHead/delete',
                memberListUrl: '/tender/project/purchaseTenderProjectMember/list',
                getExecutorAuthorityUrl: '/tender/purchaseTenderProjectHead/getExecutorAuthority'
                // cancel: '/tender/purchaseTenderProjectHead/cancel',
                // publish: '/tender/purchaseTenderProjectHead/publish'
            },
            rolePermission: 0
        }
    },
    computed: {
        statictableColumns () {
            let columns = [
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ey_116b91`, '账号'),
                    fieldLabelI18nKey: '',
                    field: 'subAccount',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_realname`, '姓名'),
                    fieldLabelI18nKey: '',
                    field: 'name',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberType`, '成员类型'),
                    fieldLabelI18nKey: '',
                    fieldType: 'select',
                    dictCode: 'memberType',
                    field: 'memberType',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    enabled: false
                },
                {
                    groupCode: 'memberList',
                    fieldType: 'select',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberRole`, '成员角色'),
                    fieldLabelI18nKey: '',
                    dictCode: 'memberRole',
                    field: 'memberRole',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ltyo_2e3c9979`, '手机号'),
                    fieldLabelI18nKey: '',
                    field: 'mobileTelephone',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '电子邮件'),
                    fieldLabelI18nKey: '',
                    field: 'email',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    fixed: 'right',
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    field: 'title',
                    width: 100,
                    slots: { default: 'grid_opration' }
                }
            ]
            
            return columns
        }
    },
    methods: {
        hideEditPage () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
            this.showEditPage = false
            this.showDetailPage = false
            this.showMaterial = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        // 重写监听路由改变事件
        handleRouterChange (query) {
            if (query.linkFilter) {
                this.$nextTick(() => {
                    const listPageObj = this.$refs.listPage
                    let isOrder = listPageObj.pageData.isOrder || listPageObj.isOrder
                    //不为id，则说明点了页面的排序
                    if(listPageObj.isOrder.column != 'id'){
                        isOrder = listPageObj.isOrder
                    }
                    let params = Object.assign(
                        {
                            pageSize: listPageObj.tablePage.pageSize,
                            pageNo: listPageObj.tablePage.currentPage,
                            filter: listPageObj.filter,
                            dataSource: listPageObj.dataSource
                        }, isOrder, query)
                    listPageObj.loading = true
                    getAction(this.url.list, params).then((res) => {
                        if(res.success) {
                            let list = [...res.result.records]
                            let row = list[0]
                            listPageObj.tableData = list
                            listPageObj.tablePage.total = res.result.total
                            this.toTender(row)
                        }
                    }).finally(() => {
                        listPageObj.loading = false
                    })
                })
            }
        },
        // 项目成员弹窗内的新增后确认按钮
        fieldSelectOk (data) {
            var that = this
            let param = {}
            let params = []
            let { fullData } = this.$refs.memberListTable.getTableData()
            let ids = fullData.map((item) => item.subAccount)
            data.forEach(item =>{
                let {subAccount, realname, email, phone} = item
                if (!ids.includes(item.subAccount)) {
                    param={
                        headId: that.currentEditRow.projectId,
                        subAccount,
                        name: realname,
                        email,
                        memberType: '2',
                        mobileTelephone: phone
                    }
                    params.push(param)
                    this.memberList.push(param)
                }
                // params.push(param)
            })
            // 插入到table中
            this.$refs.memberListTable.insertAt(params, -1)
        },
        // 项目成员弹窗内的新增
        addProjectMember () {
            // 此处接口使用采购执行人企业用户
            let url = '/account/elsSubAccount/list'
            let columns = [
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号') },
                { field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RL_aa783`, '名称') }
            ]
            let { fullData } = this.$refs.memberListTable.getTableData()
            let ids = fullData.map((item) => item.subAccount)
            // 已选的不能在勾选
            let checkedConfig = {
                visibleMethod: ({ row }) => {
                    let flag = true
                    if (ids.includes(row.subAccount)) flag = false
                    return flag
                }
            }
            this.$refs.fieldSelectModal.open(url, {}, columns, 'multiple', checkedConfig)
        },
        // 项目成员设置
        showModal (row) {
            this.currentEditRow = row
            this.modalPageData.optColumnList=[
                {
                    key: 'delete',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    clickFn: this.modalDelete,
                    authorityCode: 'tender#purchaseTenderProjectHead:delete',
                    allow: this.allowDelete
                }
            ]
            // 默认角色为0，只能查看
            this.rolePermission = '0'
            let {purchaseExecutorAccount, purchaseExecutorSubAccount} = row
            let {elsAccount, subAccount} = this.$ls.get(USER_INFO)
            console.log(purchaseExecutorAccount, purchaseExecutorSubAccount, elsAccount, subAccount)
            // 判断当前登录用户subAccount是否为单据采购执行账号是purchaseExecutorSubAccount是否相同，相同applyRole=1，否则applyRole=0
            if (purchaseExecutorAccount == elsAccount && purchaseExecutorSubAccount == subAccount) {
                this.rolePermission = '1'
                this.hideFooter = undefined
                console.log('展示新增按钮和确认按钮')
            }else{
                this.hideFooter = true
            }

            // 请求memberlist列表数据
            getAction(this.url.memberListUrl, {headId: row.projectId}).then(res => {
                if (res.success) {
                    console.log('resres2', res)
                    this.memberList = res.result || []
                    // 展示弹窗
                    this.modalVisible = true
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })

        },
        handleOk (){

            if(this.rolePermission == '1'){
                this.confirmLoading = true
                let projectMemberList = {
                    projectMemberList: this.memberList,
                    ...this.currentEditRow,
                    id: this.currentEditRow.projectId
                }
                postAction('/tender/project/purchaseTenderProjectMember/batchAdd', projectMemberList ).then(res => {
                    if(res.success) {
                        this.$message.success(res.message)
                        this.modalVisible = false
                    }else{
                        this.$message.error(res.message)
                    }
                }).finally(()=>{
                    this.confirmLoading = false
                })
            }else{
                this.modalVisible = false
            }

        },
        handleCancel (){
            this.modalVisible = false
        },
        // handleAdd (){
        //     this.currentEditRow = {
        //         templateNumber: 'TC2022032203',
        //         templateName: 'biddingPlatform',
        //         templateVersion: '1',
        //         templateAccount: '100000',
        //         elsAccount: this.$ls.get(USER_ELS_ACCOUNT)
        //     }
        //     this.showEditPage = true
        //     this.$store.dispatch('SetTabConfirm', true)
        //     console.log('handleAdd')
        // },
        handleView (row){
            // row.templateNumber='TC2022032203'
            // row.templateName='biddingPlatform'
            // row.templateVersion='1'
            // row.templateAccount='100000'
            // row.elsAccount=this.$ls.get(USER_ELS_ACCOUNT)
            // 招标项目分包把id从分包id换回projectid
            row.id = row.projectId
            this.currentEditRow = {
                ...row
            }
            console.log(row)
            this.showDetailPage = true
        },
        allowDelete () {
            console.log(this.rolePermission)
            return !(this.rolePermission == 1)
        },
        allowDel (row){
            if (row.tenderStatus == '1' || row.auditStatus == '1'){
                return true
            }
            return false
        },
        allowEdit (row){
            if (row.tenderStatus == '1' || row.auditStatus == '1'){
                return true
            }
            return false
        },
        allowHall (row){
            return !(row.tenderStatus == '1')
        },
        getExecutorAuthority (row) {
            return getAction(this.url.getExecutorAuthorityUrl, {id: row.projectId})
        },
        getCurrentProjectRow (row) {
            return getAction(this.url.queryById, {id: row.projectId})
        },
        async toTender (row) {
            // 默认角色为0，只能查看
            this.$refs.listPage.loading = true
            let {result: applyRole = '0'} = await this.getExecutorAuthority(row)
            let {result: currentProjectRow} = await this.getCurrentProjectRow(row)
            this.$refs.listPage.loading = false
            currentProjectRow['applyRole'] = applyRole > 1 ? 0 : applyRole
            currentProjectRow['subpackageId'] = row.subpackageId
            this.$ls.set('SET_TENDERCURRENTROW', currentProjectRow)
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/biddingHall',
                query: {
                    _t
                }
            })
            window.open(routeUrl.href, '_blank')
        },
        handleEditSingle (row) {
            // 招标项目分包把id从分包id换回projectid
            // row.id = row.projectId
            this.handleEdit({...row, id: row.projectId})
        },
        handleDeleteSingle (row) {
            // 招标项目分包把id从分包id换回projectid
            // row.id = row.projectId
            this.handleDelete({...row, id: row.projectId})
        },
        modalDelete (row){
            this.$refs.memberListTable.removeRow(row)
            this.memberList.forEach((item, index, arr)=>{
                if(item.subAccount == row.subAccount){
                    arr.splice(index, 1)
                }
            })
        },
        showBudgetChangeModal (row){
            // row.id = row.projectId
            this.currentEditRow = {
                ...row,
                id: row.projectId
            }
            this.root = this
            this.modalBudgetVisible = true
        },
        handleBudgetCancel () {
            this.modalBudgetVisible = false
        },
        allowChange (row){
            return row.tenderStatus != '1'
        },
        // 单个变更
        modalChange (row){
            let url = '/tender/purchaseTenderProjectSubpackageInfo/saveSubpackagePriceChange'
            let params = {
                projectId: row.headId,
                subpackageId: row.id,
                subpackageChangeBudget: row.budget,
                changeDesc: row.changeDesc
            }
            this.$refs.subpackageListTable.loading = true
            postAction(url, params).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.modalBudgetVisible = false
                    this.$refs.listPage.loadData()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.subpackageListTable.loading = false
            })
        },
        // 批量变更
        batchChange () {
            let param = {}
            let params = []
            let { fullData } = this.$refs.subpackageListTable.getTableData()
            if(fullData){
                fullData.forEach(item=>{
                    let {headId, id, budget, changeDesc} = item
                    console.log(item)
                    param={
                        projectId: headId,
                        subpackageId: id, 
                        subpackageChangeBudget: budget, 
                        changeDesc
                    }
                    // param.projectId=projectId
                    // param.subpackageId=subpackageId
                    // param.subpackageChangeBudget=subpackageChangeBudget
                    // param.changeDesc=changeDesc
                    params.push(param)
                })
            }
            let url = '/tender/purchaseTenderProjectSubpackageInfo/batchSubpackagePriceChange'
            this.$refs.subpackageListTable.loading = true
            postAction(url, params).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.modalBudgetVisible = false
                    this.$refs.listPage.loadData()

                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.subpackageListTable.loading = false
            })
        },
        async getSubpackageList (row){
            let url ='/tender/purchaseTenderProjectHead/subpackage'
            console.log(row)
            await getAction(url, {id: row.projectId}).then(res=>{
                if(res.result){
                    this.subpackageList = res.result
                }else{
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mSzsVH_c130fc8`, '查无分包信息'))
                }
            }).finally(()=>{
            })
        },
        handleMaterial (row){
            row.id = row.projectId
            this.currentEditRow = {
                ...row
            }
            this.showMaterial = true
        }
    },
    
    mounted () {
        console.log(this.currentEditRow)
        // this.serachTabs('srmReconciliationStatus', 'reconciliationStatus')
        this.serachCountTabs('/tender/purchaseTenderProjectSubpackageInfo/counts')
    }
}
</script>