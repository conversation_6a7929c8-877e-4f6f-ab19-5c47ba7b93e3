/**
 * 拖拽定位组件
 */

export default {
    install (Vue) {
        Vue.directive('drag', {
            inserted: function (el, binding, vnode) {
                let config = binding.value || {}
                config.drag = config.drag || {
                    enable: true,
                    type: 'modal', // 拖拽类型
                    dragElClass: '.ant-modal-root .ant-modal .ant-modal-content .ant-modal-header',
                    moveElClass: '.ant-modal-root .ant-modal',
                    dragOutX: false,
                    dragOutY: false
                }
                config.resize = config.resize || {
                    enable: true,
                    type: 'modal', // 拖拽类型
                    // 指定缩放把手元素，支持一个或多个把手
                    handler: {
                        'top-left': '.m-resize-top-left',
                        'top-right': '.m-resize-top-right',
                        'bottom-left': '.m-resize-bottom-left',
                        'bottom-right': '.m-resize-bottom-right',
                        'top-border': '.m-resize-top-border',
                        'right-border': '.m-resize-right-border',
                        'bottom-border': '.m-resize-bottom-border',
                        'left-border': '.m-resize-left-border'
                    },
                    // 缩放不同阶段 className
                    class: {
                        start: 'x-resize-start',
                        move: 'x-resize-move',
                        done: 'x-resize-done',
                        main: 'x-resize'
                    },
                    limit: {
                        maxWidth: 1200,
                        minWidth: 300,
                        maxHeight: 800,
                        minHeight: 300
                    },
                    resizeElClass: '.ant-modal-root .ant-modal',
                    // 回调
                    callback: {
                        start: null,
                        move: null,
                        done: (style) => {
                            // console.log('resize done', style)
                        }
                    }
                }
                // 拖拽
                if (config.drag && config.drag.enable) {
                    initDarg(el, config.drag, vnode)
                } else {
                    console.log('drag Info: drag not enabled!')
                }
                // 缩放
                if (config.resize && config.resize.enable) {
                    const dragEL = config?.drag?.dragElClass ? el.querySelector(config.drag.dragElClass) : null
                    const resizeEl = config?.resize?.resizeElClass ? el.querySelector(config.resize.resizeElClass) : null
                    if (!dragEL && !resizeEl) {
                        console.log('drag Info: No configuration elements!')
                        return
                    }
                    initResize(el, config.resize, vnode, config)
                } else {
                    console.log('drag Info: resize not enabled!')
                }
                   
            },
            componentUpdated (el, binding, vnode) {
                console.log('componentUpdated')
                let config = binding.value || {}
                config.drag = config.drag || {
                    enable: true,
                    type: 'modal', // 拖拽类型
                    dragElClass: '.ant-modal-root .ant-modal .ant-modal-content .ant-modal-header',
                    moveElClass: '.ant-modal-root .ant-modal',
                    dragOutX: false,
                    dragOutY: false
                }
                const type = config.drag?.type ? config.drag.type : 'modal' // 弹窗类型             
                if (config.drag && config.drag.enable && type === 'modal') {
                    updatePosition(el, binding, vnode)
                }    
            }
        })
    }
}
const initDarg = (el, dragConfig, vnode) => {
    const { componentOptions } = vnode
    const type = dragConfig?.type ? dragConfig.type : 'modal' // 弹窗类型
    // 如果拖动元素非元素本身el
    const dargEl = dragConfig.dragElClass ? el.querySelector(dragConfig.dragElClass) : el.querySelector('.ant-modal-root .ant-modal .ant-modal-content .ant-modal-header')
    const moveEl = dragConfig.moveElClass ? el.querySelector(dragConfig.moveElClass) : el.querySelector('.ant-modal-root .ant-modal')
    // 拖拽时的手势
    if (!dargEl || !moveEl) {
        return
    }
    dargEl.style.cursor = dragConfig?.cursor ? dragConfig.cursor : 'move'
    // 如果容器为设置position属性，默认为 position = 'relative'
    // if (getComputedStyle(moveEl.parentNode, null).position === 'static') {
    //     moveEl.parentNode.style.position = 'relative'
    // }
    const winW = document.documentElement.clientWidth || document.body.clientWidth
    const winH = document.documentElement.clientHeight || document.body.clientHeight
    // 为拖动元素添加定位
    if (type === 'modal') {
        const isCentered = componentOptions.propsData.hasOwnProperty('centered')
        const {width, dialogStyle } = componentOptions.propsData
        moveEl.style.position = 'fixed'
        const innerBox = el.querySelector('.ant-modal-root .ant-modal .ant-modal-content')
        if (isCentered) {
            // 弹窗类型调整 样式 上下垂直居中
            moveEl.style.left = winW - moveEl.offsetWidth <= 0 ? 0 : (winW - moveEl.offsetWidth) / 2 + 'px'
            moveEl.style.top = winH - moveEl.offsetHeight <= 0 ? 0 : (winH - moveEl.offsetHeight) / 2 + 'px'
            let moveElHeight = getComputedStyle(innerBox, null).height
            if (moveElHeight) {
                el.moveElHeight = moveElHeight
            }
        } else if (!dialogStyle) {
            const modalWidth = width ? width : 520 // 520 为ui库默认宽度
            if (typeof modalWidth === 'number') {
                moveEl.style.left = (winW - modalWidth) / 2 + 'px'
            } else {
                moveEl.style.left = `calc((${winW}px - ${modalWidth})/ 2)`
            }
            moveEl.style.top = '100px' // 系统默认  
        }
        if (winH - innerBox.offsetHeight <= 0) {
            // 弹窗高度大于浏览器高度
            innerBox.style.height = winH + 'px'
            moveEl.style.top = 0
            innerBox.style.overflowY = 'auto'
        }
        // 特殊处理a-modal样式
        moveEl.style.paddingBottom = 0
    } else {
        moveEl.style.position = 'absolute'
    }
    
    
    const mouseDownFn = function (e) {
        //.shaow复制节点，并且插入容器中原来位置
        let newNode
        if (dragConfig?.modifiers?.shaow) {
            newNode = moveEl.cloneNode(true)
            moveEl.style.opacity = '0.5'
            moveEl.parentNode.appendChild(newNode)
        }
        let disX, disY
        if (!dragConfig?.modifiers?.dragY) disX = e.clientX - moveEl.offsetLeft
        if (!dragConfig?.modifiers?.dragX) disY = e.clientY - moveEl.offsetTop
        const mouseMoveFn = function (e) {
            e.preventDefault()
            let left = e.clientX - disX
            let top = e.clientY - disY

            // 可以拖出去的元素的剩余宽度
            // dragOutX
            let limitWidth = dragConfig.dragOutX ? moveEl.offsetWidth - dragConfig.dragOutX : 0
            // dragOutY
            let limitHeigth = 0
            let limitHeigthTop = 0
            if (dragConfig.dragOutY) {
                // 防止可拖拽区域被拖出容器区域
                limitHeigth = moveEl.offsetHeight - dragConfig.dragOutY
                // 拖拽元素在顶部
                limitHeigthTop = el.offsetHeight - dragConfig.dragOutY
            }

            if (left < 0 - limitWidth) {
                left = 0 - limitWidth
            } else if (left > winW - moveEl.offsetWidth + limitWidth) {
                left = winW - moveEl.offsetWidth + limitWidth
            }

            if (top < 0 - limitHeigthTop) {
                top = 0 - limitHeigthTop
            } else if (top >winH - moveEl.offsetHeight + limitHeigth) {
                top = winH - moveEl.offsetHeight + limitHeigth
            }
            moveEl.style.left = left + 'px'
            moveEl.style.top = top + 'px'

            // 拖拽事件
            if (dragConfig.ondrag) {
                if (typeof dragConfig.ondrag != 'function') throw 'ondrag: should be a function'
                dragConfig.ondrag(e, { left: left, top: top })
            }

        }
        // mousemove
        document.addEventListener('mousemove', mouseMoveFn)

        const mouseUpFn = function () {
            // 移除临时shaow节点
            if (newNode) {
                moveEl.style.opacity = '1'
                newNode.parentNode.removeChild(newNode)
            }
            document.removeEventListener('mousemove', mouseMoveFn)
            document.removeEventListener('mouseup', mouseUpFn)
        }
        //  mouseup
        document.addEventListener('mouseup', mouseUpFn)
    }

    // mousedown
    dargEl.addEventListener('mousedown', mouseDownFn)
}
const initResize = (el, resizeConfig, vnode) => {
    // 处理 target
    let target = resizeConfig.resizeElClass ?  el.querySelector(resizeConfig.resizeElClass) : el
    if (typeof resizeConfig.handler === 'string') {
        console.log('Drag Warning:: resize handler config error!')
        return
    }
    // 处理函数
    let handler = function (target, bar, direction) {
        let resizeInfo = {
            flag: false,
            position: {
                left: 0,
                top: 0
            },
            start: {
                x: 0,
                y: 0
            },
            direction: direction,
            done: {}
        }
        // 绑定事件
        bar.onmousedown = function (event) {
            let disabledResize = el.getAttribute('disabled-resize')
            if (disabledResize) {
                console.log('Drag Info:: resize not enabled!')
                return
            }
            if (event.stopPropagation) {
                event.stopPropagation()
            }
            if (event.preventDefault) {
                event.preventDefault()
            }
            resizeInfo.flag = true
            target.style.overflow = 'hidden'
            // 重置滚动条
            // const bottomBar = ['bottom-border', 'bottom-left', 'bottom-right']
            // if (bottomBar.includes(direction)) {
            //     target.scrollTop = 0
            // }

            // 添加class
            target.classList.add(resizeConfig.class.start, resizeConfig.class.main)
            resizeInfo.start = {
                x: event.clientX,
                y: event.clientY
            }
            resizeInfo.position = {
                left: parseFloat(target.offsetLeft),
                top: parseFloat(target.offsetTop),
                width: parseFloat(getStyle(target, 'width')),
                height: parseFloat(getStyle(target, 'height'))
            }
            if (resizeConfig.callback && typeof resizeConfig.callback.start === 'function') {
                resizeConfig.callback.start(resizeInfo.position)
            }
            // 绑定mousemove事件
            document.onmousemove = function (event) {
                if (event.stopPropagation) {
                    event.stopPropagation()
                }
                if (event.preventDefault) {
                    event.preventDefault()
                }
                if (resizeInfo.flag) {
                    if (target.classList.contains(resizeConfig.class.start)) {
                        target.classList.remove(resizeConfig.class.start)
                    }
                    if (!target.classList.contains(resizeConfig.class.move)) {
                        target.classList.add(resizeConfig.class.move)
                    }
                    let dis = {
                        x: event.clientX - resizeInfo.start.x,
                        y: event.clientY - resizeInfo.start.y
                    }
                    let style
                    let contentStyle = {}
                    // 上拉缩放更新bar初始化坐标
                    // const bottomBar = ['bottom-border', 'bottom-left', 'bottom-right']
                    // console.log(resizeInfo.direction)
                    // let content = el.querySelector('.ant-modal-root .ant-modal .ant-modal-content')
                    // if (bottomBar.includes(resizeInfo.direction)){
                    // for (let direction of Object.keys(resizeConfig.handler)) {
                    //     let item = resizeConfig.handler[direction]
                            
                    //     console.log('item')
                    //     console.log(item)
                    //     let bars = item ? target.querySelector(item) : el
                    //     console.log(bars)
                    //     // console.log(parseFloat(getStyle(target, 'height')))
                    //     // let content = el.querySelector('.ant-modal-root .ant-modal .ant-modal-content')
                    //     bars.style.bottom = parseFloat(getStyle(content, 'height')) - parseFloat(getStyle(target, 'height')) + 'px'
                    // }
                    // console.log( 'parseFloat(getStyle(content, )' )
                    // console.log( parseFloat(getStyle(content, 'height')) )
                    // console.log( parseFloat(getStyle(target, 'height')) )
                    // console.log( bar.style.bottom )
                    // }
                    
                    switch (resizeInfo.direction) {
                    case 'top-left': {
                        const { width } = limitResize({curWidth: resizeInfo.position.width - dis.x, resizeConfig})
                        const { height } = limitResize({curHeight: resizeInfo.position.height - dis.y, resizeConfig})
                        style = {
                            width: width + 'px',
                            height: height + 'px',
                            left: resizeInfo.position.left + dis.x + 'px',
                            top: resizeInfo.position.top + dis.y + 'px'
                        }
                        contentStyle = {
                            height: height + 'px'
                        }
                        break
                    }
                    case 'top-right': {
                        const { width } = limitResize({curWidth: resizeInfo.position.width + dis.x, resizeConfig})
                        const { height } = limitResize({curHeight: resizeInfo.position.height - dis.y, resizeConfig})
                        style = {
                            width: width + 'px',
                            height: height + 'px',
                            top: resizeInfo.position.top + dis.y + 'px'
                        }
                        contentStyle = {
                            height: resizeInfo.position.height - dis.y + 'px'
                        }
                        break
                    }
                    case 'bottom-left': {
                        const { height } = limitResize({curHeight: resizeInfo.position.height + dis.y, resizeConfig})
                        const { width } = limitResize({curWidth: resizeInfo.position.width - dis.x, resizeConfig})
                        style = {
                            width: width + 'px',
                            height: height + 'px',
                            left: resizeInfo.position.left + dis.x + 'px'
                        }
                        contentStyle = {
                            height: height + 'px'
                        }
                        break
                    }
                    case 'bottom-right': {
                        const { height } = limitResize({curHeight: resizeInfo.position.height + dis.y, resizeConfig})
                        const { width } = limitResize({curWidth: resizeInfo.position.width + dis.x, resizeConfig})
                        style = {
                            width: width + 'px',
                            height: height + 'px'
                        }
                        contentStyle = {
                            height: height + 'px'
                        }
                        break
                    }
                    case 'top-border': {
                        const { height } = limitResize({curHeight: resizeInfo.position.height - dis.y, resizeConfig})
                        style = {
                            height: height + 'px',
                            top: resizeInfo.position.top + dis.y + 'px'
                        }
                        contentStyle = {
                            height: height + 'px'
                        }
                        break
                    }
                    case 'right-border': {
                        const { width } = limitResize({curWidth: resizeInfo.position.width + dis.x, resizeConfig})
                        style = {
                            width: width + 'px'
                        }
                        break
                    }
                    case 'bottom-border': {
                        const { height } = limitResize({curHeight: resizeInfo.position.height + dis.y, resizeConfig})
                        style = {
                            height: height+ 'px'
                        }
                        contentStyle = style
                        // bar.style.bottom = - dis.y + 'px'
                        break
                    }
                    case 'left-border': {
                        const { width } = limitResize({curWidth: resizeInfo.position.width - dis.x, resizeConfig})
                        style = {
                            width: width + 'px',
                            left: resizeInfo.position.left + dis.x + 'px'
                        }
                      
                        break
                    }
                    }
                    resizeInfo.done = {
                        ...style
                    }
                    Object.keys(resizeInfo.done).map(function (key) {
                        target.style[key] = resizeInfo.done[key]
                    })
                    if (resizeConfig?.type == 'modal') {
                        Object.keys(contentStyle).map(function (key) {
                            target.querySelector('.ant-modal-content').style[key] = resizeInfo.done[key]
                        })
                    }
                    if (resizeConfig.callback && typeof resizeConfig.callback.move === 'function') {
                        resizeConfig.callback.move(resizeInfo.done)
                    }
                }
            }
            // 绑定mouseup事件
            document.onmouseup = function (event) {
                if (event.stopPropagation) {
                    event.stopPropagation()
                }
                if (event.preventDefault) {
                    event.preventDefault()
                }
                resizeInfo.flag = false
                Object.values(resizeConfig.class).map(function (className) {
                    target.classList.remove(className)
                })
                if (resizeConfig.callback && typeof resizeConfig.callback.done === 'function') {
                    resizeConfig.callback.done(resizeInfo.done)
                }
                bar.onmouseup = null
                document.onmousemove = null
                document.onmouseup = null
            }
        }

    }
    // target.addEventListener('scroll', function (e) {
    //     console.log('resizeConfig.handler')
    //     console.log(resizeConfig.handler)
    //     // debugger
    //     // 处理 bar
    //     console.log(el.querySelector('.m-resize-top-left'))
    //     for (let direction of Object.keys(resizeConfig.handler)) {
    //         let item = resizeConfig.handler[direction]
    //         let bar = item ? target.querySelector(`.${item}`) : el
    //         console.log(parseFloat(getStyle(target, 'height')))
    //         let content = el.querySelector('.ant-modal-root .ant-modal .ant-modal-content')
    //         bar.style.bottom = parseFloat(getStyle(content, 'height')) - parseFloat(getStyle(target, 'height')) - parseFloat(e.target.scrollTop )+ 'px'
    //     }
    // })
    // 处理 bar
    for (let direction of Object.keys(resizeConfig.handler)) {
        let item = resizeConfig.handler[direction]
        let bar = item ? target.querySelector(item) : el
        bar = bar ? bar : createBolder({bolderClassName: item, bolderType: direction, resizeConfig, target})
        handler(target, bar, direction)
    }
}
const getCss = function (element) {
    return element.currentStyle ? element.currentStyle : document.defaultView.getComputedStyle(element, null)
}
const getStyle = function (element, key) {
    return getCss(element)[key]
}
const createBolder = ({bolderClassName, bolderType, resizeConfig, target}) => {
    // 特殊处理弹窗组件
    target = resizeConfig?.type == 'modal' ?  target.querySelector('.ant-modal-root .ant-modal .ant-modal-content') : target
    const dom = document.createElement('i')
    bolderClassName = bolderClassName && bolderClassName.replace('.', '')
    dom.setAttribute('class', bolderClassName)
    // 公共css提前
    dom.style.cssText = 'width: 20px;height: 20px;position: absolute;background: transparent; z-index:999'
    switch (bolderType) {
    case 'top-left':
        dom.style.cssText += 'cursor: nw-resize;top: 0;left: 0;'   
        break
    case 'top-right':
        dom.style.cssText += 'cursor: ne-resize;top: 0;right: 0;'   
        break
    case 'bottom-left':
        dom.style.cssText += ' cursor: sw-resize; bottom: 0;left: 0;'   
        break
    case 'bottom-right':
        dom.style.cssText += ' cursor: se-resize; bottom: 0;right: 0;'   
        break
    case 'top-border':
        dom.style.cssText += 'cursor: ns-resize;top: 0;width: 100%; height: 2px;'    
        break
    case 'right-border':
        dom.style.cssText += 'cursor: ew-resize;right: 0; width: 3px;height: 100%;'
        break
    case 'bottom-border':
        dom.style.cssText += 'cursor: ns-resize;bottom: 0;width: 100%;height: 2px;'    
        break
    case 'left-border':
        dom.style.cssText += 'cursor: ew-resize;left: 2;width: 3px;height: 100%;'
        break
    
    default:
        break
    }
    return target.insertBefore(dom, target.children[0])
}
const updatePosition = (el, binding, vnode) => {
    const {context, componentOptions} = vnode
    if (componentOptions.propsData.hasOwnProperty('centered')) { // props是否设置了居中
        context.$nextTick(() => {
            // 如果拖动元素非元素本身el 
            const moveEl =  el.querySelector('.ant-modal-root .ant-modal')
            const innerBox = el.querySelector('.ant-modal-root .ant-modal .ant-modal-content')
            const diffBox = diffBoxFn(el.moveElHeight)
            function diffBoxFn (oldH) {
                let diffBox = false
                const newMoveElHeight = getComputedStyle(innerBox, null).height || ''
                if (newMoveElHeight && newMoveElHeight != 'auto') {
                    diffBox = oldH === newMoveElHeight ? false : true
                }
                return diffBox
            }
            // 弹窗没有渲染成功 或者非改改变列表高度 不更新位置
            if (!el.updatePosition && diffBox) {
                const winW = document.documentElement.clientWidth || document.body.clientWidth
                const winH = document.documentElement.clientHeight || document.body.clientHeight
                // 为拖动元素添加绝对定位
                moveEl.style.position = 'fixed'
                // 弹窗类型调整 样式 上下垂直居中
                moveEl.style.left = winW - moveEl.offsetWidth <= 0 ? 0 : (winW - moveEl.offsetWidth) / 2 + 'px'
                moveEl.style.top = winH - moveEl.offsetHeight <=0 ? 0 : (winH - moveEl.offsetHeight) / 2 + 'px'
                // 仅首次更新一次位置
                el.updatePosition = true
            }
        })
    } else {
        context.$nextTick(() => {
            const moveEl =  el.querySelector('.ant-modal-root .ant-modal')
            const innerBox =  el.querySelector('.ant-modal-root .ant-modal .ant-modal-content')
            if (!moveEl) {
                return
            }
            const winH = document.documentElement.clientHeight || document.body.clientHeight
            if (winH - innerBox.offsetHeight <= 0) {
                // 弹窗高度大于浏览器高度
                innerBox.style.height = winH + 'px'
                moveEl.style.top = 0
                innerBox.style.overflowY = 'auto'
            }
        })
    }
}
// 限制最大最小值
const limitResize = (info) => {
    const {curHeight, curWidth, resizeConfig} = info
    const { maxWidth, minWidth, maxHeight, minHeight } = resizeConfig?.limit || {}
    const minmax = (value, min, max) => Math.min(Math.max(value, min), max)
    const height = curHeight && minHeight && maxHeight ? minmax(curHeight, minHeight, maxHeight) : ''
    const width = curWidth && minWidth && maxWidth  ? minmax(curWidth, minWidth, maxWidth) : ''
    return {
        height,
        width
    }
}