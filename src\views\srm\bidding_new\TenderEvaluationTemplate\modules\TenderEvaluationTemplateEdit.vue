<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-content">
          <div class="baseInfo">
            <Dataform
              ref="dataform"
              :formData="formData"
              :validateRules="validateRules"
              :fields="fields" />
          </div>
          <template-node-box
            ref="nodeBox"
            :nodeListData="nodeListData"/>
        </div>
        <div class="page-footer">
          
          <a-button
            type="primary"
            @click="handleAddNode">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VayC_2f9663e5`, '新增节点') }}</a-button>
          <a-button
            type="primary"
            @click="handleSave">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
          <a-button
            type="primary"
            @click="handlePublish">{{ $srmI18n(`${$getLangAccount()}#i18n_title_release`, '发布') }}</a-button>
          <a-button
            @click="() => {this.$emit('hide')}">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import {getAction, postAction} from '@/api/manage'
import TemplateNodeBox from './components/TemplateNodeBox'
import Dataform from '../../BiddingHall/components/Dataform'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
export default {
    name: 'PurchaseSupplierCapacityHead',
    components: {
        Dataform,
        TemplateNodeBox
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: { span: 9 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            confirmLoading: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                detail: { url: '/tender/tenderEvaluationTemplateHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            url: {
                save: '/tender/tenderEvaluationTemplateHead/edit',
                publish: '/tender/tenderEvaluationTemplateHead/publish',
                queryByGruop: '/tender/tenderEvaluationTemplateHead/queryNodeByGruop'
            },
            formData: {},
            nodeListData: [],
            fields: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_templateName`, '模板名称'),
                    field: 'evaluationName',
                    defaultValue: '',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_templateDesc`, '模板描述'),
                    field: 'evaluationDescribe',
                    defaultValue: '',
                    fieldType: 'input'
                }/*,
                {
                    title: '评标办法',
                    fieldLabelI18nKey: '',
                    field: 'evaluationMethod',
                    defaultValue: '',
                    fieldType: 'select',
                    dictCode: 'tenderEvaluationMethod'
                }*/
            ],
            validateRules: {
                evaluationName: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterATemplateName`, '请输入模板名称'), trigger: 'blur' },
                    { max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '100'}
                ],
                evaluationDescribe: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterContractTemplateDesc`, '请输入模板描述'), trigger: 'blur' },
                    { max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '100'}
                ],
                evaluationMethod: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFUBrh_8870e8b1`, '请选择评标办法'), trigger: 'blur' }
                ]
            }
        }
    },
    methods: {
        // 通过接口获取数据
        async queryDetail () {
            if (this.requestData.detail) {
                let url = this.requestData.detail.url
                if (url) {
                    this.confirmLoading = true
                    let method = 'get'
                    let args = this.requestData.detail.args(this)
                    // 有id 才能请求,新建时是没有id的，不用请求查询接口
                    if (args && args.id) {
                        if (this.requestData.detail.method) {
                            method= this.requestData.detail.method.toLowerCase()
                        }
                        let query = method==='get'?await getAction(url, args): await postAction(url, args)
                        this.confirmLoading = false
                        if (query && query.success) {
                            this.formData = Object.assign({}, query.result)
                            this.nodeListData = this.formData.tenderEvaluationTemplateItemVoList
                        } else {
                            this.$message.error(query.message)
                        }
                    } else {
                        this.confirmLoading = false
                        this.$nextTick(()=> {
                            this.dealSource(this.currentEditRow)
                            this.formData = {}
                            this.nodeListData = []
                        })
                    }
                }
            }
        },
        dealSource (data) {
            console.log(data)
        },
        handleAddNode () {
            this.$refs.nodeBox.handleAddNode()
        },
        async handleSave (arg, cb) {
            let isValidate = arg.isValidate || false
            let params = await this.$refs.dataform.externalAllData(isValidate)
            let flag = await this.$refs.nodeBox.getValidatePromise().then(res => res)
            if (!flag) return
            let tenderEvaluationTemplateItemVoList = await this.$refs.nodeBox.getAllData().then(res => res)
            params['tenderEvaluationTemplateItemVoList'] = tenderEvaluationTemplateItemVoList
            let url = this.formData.id ? '/tender/tenderEvaluationTemplateHead/edit':'/tender/tenderEvaluationTemplateHead/add'
            valiStringLength(params, [
                {field: 'evaluationName', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_templateName`, '模板名称'), maxLength: 100},
                {field: 'evaluationDescribe', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_templateDesc`, '模板描述'), maxLength: 100}
            ])
            this.confirmLoading = true
            postAction(url, params).then(res => {
                if (res.success) {
                    if (!this.formData.id) {
                        this.$set(this.formData, 'id', res.result.id)
                    }
                    if (cb) {
                        cb()
                    } else {
                        this.$message.success(res.message)
                    }
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handlePublish () {
            let cb = async () => {
                this.confirmLoading = true
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                    onOk: () => {
                        valiStringLength(this.formData, [
                            {field: 'evaluationName', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_templateName`, '模板名称'), maxLength: 100},
                            {field: 'evaluationDescribe', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_templateDesc`, '模板描述'), maxLength: 100}
                        ])
                        postAction(this.url.publish, this.formData).then(res => {
                            let type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            if (res.success) {
                                this.$emit('hide')
                            }
                        }).finally(() => {
                            this.confirmLoading = false
                        })
                    }
                })
            }
            this.handleSave({isValidate: true}, cb)
        }
    },
    mounted () {
        this.queryDetail()
    }
}
</script>
