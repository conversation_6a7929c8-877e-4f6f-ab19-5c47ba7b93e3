import { MallLayout } from '@/components/layouts'
import { PoolMallLayout } from '@/components/layouts'
const router = {
    MallRouter: {
        path: '/mall',
        name: 'mall',
        meta: {
            title: '商城',
            titleI18nKey: 'i18n_menu_XL_aab48',
            icon: 'sliders',
            keepAlive: false
        },
        component: MallLayout,
        children: [
            {
                path: 'productList',
                name: 'productList',
                meta: {
                    keepAlive: false,
                    title: '商城',
                    titleI18nKey: 'i18n_menu_XL_aab48'
                },
                component: () => import('@views/srm/mall/product/mall')
            },
            {
                path: 'myFav',
                name: 'myFav',
                meta: {
                    keepAlive: false,
                    title: '我的收藏',
                    titleI18nKey: 'i18n_title_myFav'
                },
                component: () => import('@views/srm/mall/product/myFav')
            },
            {
                path: 'comparisonDetail',
                name: 'comparisonDetail',
                meta: {
                    keepAlive: false,
                    title: '比价',
                    titleI18nKey: 'i18n_menu_lu_d5da3'
                },
                component: () => import('@views/srm/mall/product/comparisonDetail')
            },
            {
                path: 'goodsDetail',
                name: 'goodsDetail',
                meta: {
                    keepAlive: false,
                    title: '商品详情',
                    titleI18nKey: 'i18n_field_XNdV_2812d71a'
                },
                component: () => import('@views/srm/mall/product/goodsDetail')
            },
            {
                path: 'cart',
                name: 'cart',
                meta: {
                    keepAlive: false,
                    title: '购物车',
                    titleI18nKey: 'i18n_title_shoppingCart'
                },
                component: () => import('@views/srm/mall/product/cart')
            }
        ]
    },
    poolMallRouter: {
        path: '/poolMall',
        name: 'poolMall',
        meta: {
            title: '商城',
            titleI18nKey: 'i18n_menu_XL_aab48',
            icon: 'sliders',
            keepAlive: false
        },
        component: PoolMallLayout,
        children: [
            {
                path: 'poolList',
                name: 'poolList',
                meta: {
                    keepAlive: false,
                    title: '需求池商城',
                    titleI18nKey: ''
                },
                component: () => import('@views/srm/mall/product/Pool')
            },
            {
                path: 'myFav',
                name: 'poolMyFav',
                meta: {
                    keepAlive: false,
                    title: '我的收藏',
                    titleI18nKey: 'i18n_title_myFav'
                },
                component: () => import('@views/srm/mall/product/myFav')
            },
            {
                path: 'comparisonDetail',
                name: 'poolComparisonDetail',
                meta: {
                    keepAlive: false,
                    title: '比价',
                    titleI18nKey: 'i18n_menu_lu_d5da3'
                },
                component: () => import('@views/srm/mall/product/comparisonDetail')
            },
            {
                path: 'goodsDetail',
                name: 'poolGoodsDetail',
                meta: {
                    keepAlive: false,
                    title: '商品详情',
                    titleI18nKey: 'i18n_field_XNdV_2812d71a'
                },
                component: () => import('@views/srm/mall/product/goodsDetail')
            },
            {
                path: 'cart',
                name: 'poolCart',
                meta: {
                    keepAlive: false,
                    title: '购物车',
                    titleI18nKey: 'i18n_title_shoppingCart'
                },
                component: () => import('@views/srm/mall/product/cart')
            },
            {
                path: 'orderSettlement',
                name: 'orderSettlement',
                meta: {
                    keepAlive: false,
                    title: '订单结算',
                    titleI18nKey: 'i18n_title_shoppingCar'
                },
                component: () => import('@views/srm/mall/product/orderSettlement')
            }
        ]
    }
}


export default router