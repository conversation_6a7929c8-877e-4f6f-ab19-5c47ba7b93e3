<template>
  <a-modal
    centered
    :mask-closable="false"
    :title="title"
    :visible="visible"
    :width="1120"
    @cancel="visible = false">
    <a-alert
      style="margin-bottom: 10px;"
      type="info"
      :description="$srmI18n(`${$getLangAccount()}`, '选供应商显示隐藏')"
      :message="$srmI18n(`${$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示')"/>
    <div
      class="grid-box"
      style="overflow: hidden; position:relative">
      <vxe-grid
        auto-resize
        border
        height="300"
        ref="columnGrid"
        row-id="id"
        size="small"
        :columns="tableColumn"
        :data="tableData"
        :edit-config="{trigger: 'click', mode: 'cell'}">
        <template #empty>
          <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
        </template>
      </vxe-grid>
    </div>
    <template slot="footer">
      <a-button
        key="reset"
        @click="reset">
        {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reset`, '重置') }}
      </a-button>
      <a-button
        key="back"
        @click="visible = false">
        {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cancle`, '取消') }}
      </a-button>
      <a-button
        key="submit"
        type="primary"
        @click="selectedOk">
        {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认') }}
      </a-button>
    </template>    
  </a-modal>
</template>
<script lang="jsx">

export default {
    data () {
        return {
            tableColumn: [
                {fixed: 'left', type: 'checkbox', width: 40},
                {
                    align: 'center',
                    field: 'toElsAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '供应商编码'),
                    width: 220
                },
                {
                    align: 'center',
                    field: 'supplierName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '供应商名称'),
                    width: 220
                },
                {
                    align: 'center',
                    editRender: {
                        name: 'mSwitch',
                        props: {closeValue: '0', openValue: '1'},
                        type: 'visible'
                    },
                    field: 'hidden',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hidden`, '是否隐藏')
                }
            ],
            tableData: [],
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n__GRRdX_ec5c3c9d`, '设置供应商'),
            visible: false
        }
    },
    methods: {
        open ({tableData}){
            this.tableData = JSON.parse(JSON.stringify(tableData))
            this.visible = true
        },
        reset (){
            let {fullData} = this.$refs.columnGrid.getTableData()
            fullData.forEach(element => {
                element.hidden = '0'
            })
            this.$emit('ok', fullData)
            this.visible = false
        },
        selectedOk (){
            const {fullData} = this.$refs.columnGrid.getTableData()
            this.$emit('ok', fullData)
            this.visible = false
        }
    },
    name: 'ColumnSetting'
}
</script>
