<template>
  <div style="height: 100%">
    <a-spin :spinning="confirmLoading">
      <setp-lay-out
        v-if="show"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :pageFooterButtons="pageFooterButtons"
        :pageHeaderButtons="pageHeaderButtons"
        :sourceGroups="sourceGroups"
        :pageStatus="pageStatus"
        :canchangeStep="canchangeStep"
        @nextStepHandle="nextStepHandle"
        @preStepHandle="preStepHandle"
        :fromSourceData="fromSourceData">
        <template #purchaseTenderProjectAttachmentInfoList="{ slotProps }">
          <attachmentInfoList
            ref="purchaseTenderProjectAttachmentInfoList"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :fromSourceData="fromSourceData"></attachmentInfoList>
        </template>
        <template #tenderBidLetterFormatGroupVo="{ slotProps }">
          <tenderBidLetterFormatGroupVo
            ref="tenderBidLetterFormatGroupVo"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :tenderBidLetterFormatGroupVo="fromSourceData.tenderBidLetterFormatGroupVo"></tenderBidLetterFormatGroupVo>
        </template>
        <template #purchaseTenderEvaluationPrinciples="{ slotProps }">
          <purchaseTenderEvaluationPrinciples
            ref="purchaseTenderEvaluationPrinciples"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :fromSourceData="fromSourceData"
            :purchaseTenderEvaluationPrinciples="fromSourceData.purchaseTenderEvaluationPrinciples"> </purchaseTenderEvaluationPrinciples>
        </template>
        <template #tenderEvaluationInfoVoList="{ slotProps }">
          <tenderEvaluationInfoVoList
            ref="tenderEvaluationInfoVoList"
            :pageStatus="pageStatus"
            :slotProps="slotProps"
            :fromSourceData="fromSourceData"></tenderEvaluationInfoVoList>
        </template>
      </setp-lay-out>
    </a-spin>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow" />
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import purchaseTenderEvaluationPrinciples from './modules/purchaseTenderEvaluationPrinciples'
import tenderBidLetterFormatGroupVo from './modules/tenderBidLetterFormatGroupVo'
import tenderEvaluationInfoVoList from './modules/tenderEvaluationInfoVoList'
import attachmentInfoList from './modules/attachmentInfoList'
import setpLayOut from '../components/setpLayOut'
import flowViewModal from '@comp/flowView/flowView'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import { cloneDeep } from 'lodash'

export default {
    name: 'BiddingFile',
    components: {
        fieldSelectModal,
        purchaseTenderEvaluationPrinciples,
        tenderEvaluationInfoVoList,
        attachmentInfoList,
        setpLayOut,
        tenderBidLetterFormatGroupVo,
        flowViewModal
    },
    mixins: [baseMixins],
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        },
        canchangeStep () {
            return this.pageStatus == 'detail'
        },
        sourceGroups () {
            let g = this.handleInitSourceGroups()
            return g
        }
    },
    data () {
        return {
            flowView: false,
            flowId: '0',
            pageStatus: 'edit',
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            show: false,
            rejectForm: {
                node: '',
                reject: ''
            },
            sourceGroupsDemo: [
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxmK_9dbd9bbe`, '投标函格式'),
                    groupNameI18nKey: '',
                    groupCode: 'tenderBidLetterFormatGroupVo',
                    groupType: 'head',
                    show: true,
                    sortOrder: '1'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBjF_4119a25d`, '评标原则'),
                    groupNameI18nKey: '',
                    groupCode: 'purchaseTenderEvaluationPrinciples',
                    groupType: 'head',
                    show: true,
                    sortOrder: '2'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBCh_411bee3f`, '评标方法'),
                    groupNameI18nKey: '',
                    groupCode: 'tenderEvaluationInfoVoList',
                    groupType: 'head',
                    show: true,
                    sortOrder: '3'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingDocuments`, '招标文件'),
                    groupNameI18nKey: '',
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    groupType: 'item',
                    show: true,
                    sortOrder: '4'
                }
            ],
            businessRefName: 'businessRef',
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    type: 'primary',
                    click: this.saveEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/tender/tenderProjectAttachmentInfo/publish'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    click: this.handlePublish
                }
            ],
            pageHeaderButtons: [],
            currentGroupCode: {
                groupCode: 'tenderBidLetterFormatGroupVo'
            },
            currentEditRow: {},
            status: '',
            confirmLoading: false,
            url: {
                detail: '/tender/tenderProjectAttachmentInfo/query',
                add: '/tender/tenderProjectAttachmentInfo/add',
                edit: '/tender/tenderProjectAttachmentInfo/edit',
                publish: '/tender/tenderProjectAttachmentInfo/publish'
            }
        }
    },
    methods: {
        // 角色判断 投标单位不能查看评标方法和评标规则
        checkApplyRole () {
            let sourceGroups = cloneDeep(this.sourceGroupsDemo)
            let applyRole = this.$route.path.indexOf('/tenderHall') !== -1 ? '2' : '1'
            if (applyRole == '2') {
                sourceGroups = cloneDeep(this.sourceGroupsDemo).filter(item => {
                    return ['tenderBidLetterFormatGroupVo', 'purchaseTenderProjectAttachmentInfoList'].includes(item.groupCode)
                })
            }
            return sourceGroups
        },
        handleInitSourceGroups () {
            let sourceGroups = []
            // 开标方式  0-全部、1-线上、2-线下*/   评标方式 0-全部、1-线上、2-线下*/
            let {bidOpenType, evaluationType} = this.subPackageRow
            let methodsMap = {
                '1_2': () => {
                    sourceGroups = cloneDeep(this.sourceGroupsDemo).filter(item => {
                        return ['tenderBidLetterFormatGroupVo', 'purchaseTenderProjectAttachmentInfoList'].includes(item.groupCode)
                    })
                },
                '2_2': () => {
                    sourceGroups = cloneDeep(this.sourceGroupsDemo).filter(item => {
                        return ['purchaseTenderProjectAttachmentInfoList'].includes(item.groupCode)
                    })
                },
                '1_1': () => {
                    sourceGroups = this.checkApplyRole()
                },
                '2_1': () => {
                    sourceGroups = this.checkApplyRole()
                }
            }
            methodsMap[`${bidOpenType}_${evaluationType}`]()
            return sourceGroups
        },
        setPageStatus () {
            let status = ['1', '2'].includes(this.fromSourceData.status) ? 'detail' : 'edit'
            console.log('@@@@@', this.tenderCurrentRow.applyRole)
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') status = 'detail'
            this.pageStatus = status
        },
        // 数据更新
        processingResData (res, refreshChildren) {
            if (res.success) {
                this.fromSourceData = Object.assign({}, res.result)
                this.show = true
                this.setPageStatus()
                // 更新子组件数据
                if (refreshChildren) {
                    this.$nextTick(() => {
                        this.childrenInitData(this.fromSourceData)
                    })
                }
            } else {
                this.$message.error(res.message)
            }
        },
        queryDetail (refreshChildren = false) {
            let params = {
                subpackageId: this.subId
            }
            this.confirmLoading = true
            getAction(this.url.detail, params)
                .then((res) => {
                    this.processingResData(res, refreshChildren)
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        setCurrentStep (i) {
            this.$refs[this.businessRefName].currentStep = i
        },
        async saveEvent (arg) {
            // 保存不用校验附件
            let ValidateArr = ['tenderBidLetterFormatGroupVo', 'purchaseTenderEvaluationPrinciples', 'tenderEvaluationInfoVoList']
            this.getAllValidate(ValidateArr).then(async (res) => {
                if (res.validStatus) {
                    let params = await this.getParamsName()
                    let url = params.id ? this.url.edit : this.url.add
                    this.confirmLoading = true
                    let valid = true
                    if(params.purchaseTenderProjectAttachmentInfoList?.length !=0){
                        params.purchaseTenderProjectAttachmentInfoList.forEach(item=>{
                            valid = item.fileName.length > 250 ? false:valid
                        })
                    }
                    if(!valid){
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QIRLHzBG250_5c6bca1f`, '文件名称长度超出250'))
                        this.confirmLoading = false
                        return
                    }
                    postAction(url, params)
                        .then((res) => {
                            let type = res.success ? 'success' : 'error'
                            if (res.success) {
                                this.$message[type](res.message)
                                // this.queryDetail(true)
                                this.processingResData(res, true)
                                // this.$emit('resetCurrentSubPackage')
                                this.resetCurrentSubPackage()
                            } else {
                                this.$message[type](res.message)
                            }
                        })
                        .finally(() => {
                            this.confirmLoading = false
                        })
                } else {
                    this.setCurrentStep(res.currentStep)
                }
            })
        },
        async getParamsName () {
            let purchaseTenderProjectAttachmentInfoList = this.$refs.purchaseTenderProjectAttachmentInfoList.externalAllData()
            let params = {
                ...this.fromSourceData,
                purchaseTenderProjectAttachmentInfoList,
                subpackageId: this.subId,
                tenderProjectId: this.tenderCurrentRow.id
            }
            // 开标方式  0-全部、1-线上、2-线下*/   评标方式 0-全部、1-线上、2-线下*/
            let {bidOpenType, evaluationType} = this.subPackageRow
            if (bidOpenType == '1') {
                params['tenderBidLetterFormatGroupVo'] = this.$refs.tenderBidLetterFormatGroupVo.externalAllData()
            }
            if (evaluationType == '1') {
                params['purchaseTenderEvaluationPrinciples'] = await this.$refs.purchaseTenderEvaluationPrinciples.externalAllData()
                params['tenderEvaluationInfoVoList'] = await this.$refs.tenderEvaluationInfoVoList.externalAllData()
            }
            return params
        },
        async handlePublish () {
            let refs = this.sourceGroups.map((ref) => ref.groupCode)
            let that = this
            let data 
            let summaryCalRules
            await this.getParamsName().then(res=>{
                console.log('res', res)
                data = res
            })
            // 如果存在purchaseTenderEvaluationPrinciples才校验里面的值是否为空
            if(data.purchaseTenderEvaluationPrinciples){
                summaryCalRules = data.purchaseTenderEvaluationPrinciples.summaryCalRules
                let { highestScoreNumber, lowestScoreNumber, offBase, scoreRanking} = data.purchaseTenderEvaluationPrinciples
                console.log('@@::@@', summaryCalRules)
                // 汇总分计算规则校验
                if(!summaryCalRules || (summaryCalRules == '1' && (!highestScoreNumber||!lowestScoreNumber)) || (summaryCalRules == '2' && !offBase)){
                    this.$message.error(this.$srmI18n(`${that.$getLangAccount()}#i18n_field_MkztdLFViFGPiFcSMMi_72f388b8`, '汇总分计算规则请选择并将选择行填写完整'))
                    this.setCurrentStep(1)
                    return
                }

                // 这种情况下才会展示入围规则
                if ((this.processType == '1' || this.checkTypeValue == '0') || (this.$ls.get('changeBidFile') && this.currentSubPackage().processType == '1')|| (this.$ls.get('changeBidFile') && this.checkTypeValue == '0')) {
                // 入围规则校验
                    if(!this.fromSourceData.purchaseTenderEvaluationPrinciples.shortlistedRules  || !scoreRanking){
                        this.$message.error(this.$srmI18n(`${that.$getLangAccount()}#i18n_field_ViFGSMNLLF_786146b1`, '请选择并填写入围规则'))
                        this.setCurrentStep(1)
                        return
                    }
                }
            }

            
            // 发布时候默认保存招标函数据
            await this.nextStepHandle()
            this.getAllValidate(refs).then(async (res) => {
                if (res.validStatus) {
                    this.$confirm({
                        title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_submit`, '提交'),
                        content: that.$srmI18n(`${that.$getLangAccount()}#i18n_field_KQRLDJ_392810a1`, '是否确认提交'),
                        onOk: async () => {
                            let params = await this.getParamsName()
                            that.confirmLoading = true
                            let valid = true
                            if(params.purchaseTenderProjectAttachmentInfoList?.length !=0){
                                params.purchaseTenderProjectAttachmentInfoList.forEach(item=>{
                                    valid = item.fileName.length > 250 ? false:valid
                                })
                            }
                            if(!valid){
                                that.$message.error(that.$srmI18n(`${that.$getLangAccount()}#i18n_field_QIRLHzBG250_5c6bca1f`, '文件名称长度超出250'))
                                that.confirmLoading = false
                                return
                            }
                            postAction(that.url.publish, params)
                                .then((res) => {
                                    let type = res.success ? 'success' : 'error'
                                    that.$message[type](res.message)
                                    if (res.success) {
                                        // that.$emit('resetCurrentSubPackage')
                                        this.resetCurrentSubPackage()
                                        // that.queryDetail(true)
                                        this.processingResData(res, true)
                                    }
                                })
                                .finally(() => {
                                    that.confirmLoading = false
                                })
                        },
                        onCancel () {}
                    })
                } else {
                    this.setCurrentStep(res.currentStep)
                }
            })
        },
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef(this.currentGroupCode)
            itemGrid.insertAt([...data], -1)
        },
        getAllValidate (refNames) {
            // 定义Promise回调处理
            const handlePromise = (list = []) =>
                list.map((promise) =>
                    promise.then(
                        (res) => ({
                            status: 'success',
                            res
                        }),
                        (err) => ({
                            status: 'error',
                            err
                        })
                    )
                )
            // 获取子组件的抛出的校验promise
            let promiseValidateArr = refNames.map((ref) => {
                if (this.$refs[ref]) return this.$refs[ref].getValidatePromise()
            }).filter((promise) => promise)
            return new Promise((resolve, reject) => {
                Promise.all(handlePromise(promiseValidateArr))
                    .then((result) => {
                        let currentStep = null
                        let flag = true
                        for (let i = 0; i < result.length; i++) {
                            if (result[i].status === 'error') {
                                currentStep = i
                                flag = false
                            }
                        }
                        if (flag) {
                            let resolveData = { validStatus: true, currentStep: currentStep, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiLR_48599c04`, '验证成功') }
                            resolve(resolveData)
                        } else {
                            let resolveData = { validStatus: false, currentStep: currentStep, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiKmWVGv_95d29236`, '验证失败，请处理') }
                            resolve(resolveData)
                        }
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        },
        nextStepHandle (data) {
            if (this.currentGroupCode.groupCode == 'tenderBidLetterFormatGroupVo') {
                if (this.pageStatus == 'edit') {
                    // 只需要校验当前投标函组件数据
                    this.getAllValidate(['tenderBidLetterFormatGroupVo']).then(async (res) => {
                        if (res.validStatus) {
                            this.confirmLoading = true
                            let params = await this.getParamsName()
                            let url = params.id ? this.url.edit : this.url.add
                            let p = new Promise((resolve, reject) => {
                                postAction(url, params)
                                    .then((res) => {
                                        if (res.success) {
                                            this.processingResData(res, true)
                                            resolve(res)
                                        } else {
                                            this.setCurrentStep(0)
                                            reject(res)
                                        }
                                    })
                                    .finally(() => {
                                        if (data) {
                                            this.currentGroupCode = data.groupData
                                        }
                                        this.confirmLoading = false
                                    })
                            })
                            return p
                        } else {
                            this.setCurrentStep(0)
                        }
                    })
                }
            } else {
                if (data) {
                    this.currentGroupCode = data.groupData
                }
            }
        },
        preStepHandle (data) {
            this.currentGroupCode = data.groupData
        },
        // 子组件数据更新调用
        childrenInitData (data) {
            this.sourceGroups.map((ref) => {
                this.$refs[ref.groupCode].init(data)
            })
        }
    },
    created () {
        this.queryDetail()
        this.$ls.set('changeBidFile', false)
    }
}
</script>
