<template>
  <detail-page
    ref="detailPage"
    :current-edit-row="currentEditRow"
    :pageData="pageData"></detail-page>
</template>

<script>
import detailPage from '@comp/template/detailPage'

export default {
    name: 'ViewDeliverySaleModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        detailPage
    },
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shipment`, '发货单'),
            confirmLoading: false,
            pageData: {
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            form: {
                                enquiryNumber: '',
                                supplierId: '',
                                supplierCode: '',
                                supplierName: '',
                                enquiryScope: '',
                                enquiryType: 'material',
                                enquiryStatus: '0',
                                enquiryDesc: '',
                                enquiryDate: '',
                                quoteEndTime: '',
                                purchaseOrgCode: '',
                                purchaseOrgName: '',
                                purchaseGroupCode: '',
                                purchaseGroupName: '',
                                companyCode: '',
                                companyName: '',
                                purchasePrincipal: '',
                                supplierPrincipal: '',
                                purchaseRemark: '',
                                supplierRemark: ''
                            },
                            list: [
              
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shipmentNo`, '发货单号'),
                                    fieldName: 'deliveryNumber', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_systemGeneration`, '系统生成'),
                                    disabled: true
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                                    fieldName: 'deliveryStatus_dictText',
                                    dictCode: 'isrmDeliveryStatus',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaDesc`, '单据描述'),
                                    fieldName: 'deliveryDesc',
                                    placeholder: ''
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryTime`, '发货时间'),
                                    fieldName: 'deliveryTime',
                                    placeholder: '',
                                    disabled: true
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_planArriveDate`, '计划到货日期'),
                                    fieldName: 'planArriveDate',
                                    placeholder: '',
                                    disabled: false
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'),
                                    fieldName: 'companyCode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectCompanyCodeTips`, '请选择公司代码'),
                                    dictCode: 'isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code="company"'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                                    fieldName: 'factoryCode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectFactoryTips`, '请选择工厂'),
                                    dictCode: 'isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code="factory"'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocation`, '库存地点'),
                                    fieldName: 'storageLocationCode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectInventoryLocationlTips`, '请选择库存地点'),
                                    dictCode: 'isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code="storageLocation"'
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_distributionMode`, '配送方式'),
                                    fieldName: 'deliveryWay_dictText',
                                    dictCode: 'isrmDeliveryWay',
                                    disabled: false
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsCompany`, '物流公司'),
                                    fieldName: 'logisticsCompany',
                                    placeholder: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trackingNumber`, '物流单号'),
                                    fieldName: 'trackingNumber',
                                    placeholder: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_carNumber`, '车牌号'),
                                    fieldName: 'carNumber',
                                    placeholder: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverName`, '司机姓名'),
                                    fieldName: 'driverName',
                                    placeholder: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverIdNumber`, '司机身份证号'),
                                    fieldName: 'driverIdNumber',
                                    placeholder: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverPhone`, '司机电话'),
                                    fieldName: 'driverPhone',
                                    placeholder: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryAddress`, '交货地址'),
                                    fieldName: 'deliveryAddress',
                                    placeholder: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receiveContact`, '收货联系人'),
                                    fieldName: 'receiveContact',
                                    placeholder: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系人电话'),
                                    fieldName: 'receivePhone',
                                    placeholder: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRemarks`, '供方备注'),
                                    fieldName: 'supplierRemark',
                                    placeholder: ''
                                }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                        content: {
                            type: 'table',
                            ref: 'deliverySaleItemList',
                            columns: [
                                { 
                                    type: 'seq', width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                                    field: 'orderNumber',
                                    width: 130
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                    field: 'orderItemNumber',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                                    field: 'factoryCode',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialCode',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                    field: 'materialDesc',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
                                    field: 'materialSpec',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                                    field: 'materialGroupCode',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_demandDate`, '需求日期'),
                                    field: 'requireDate',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'),
                                    field: 'quantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityUnit`, '数量单位'),
                                    field: 'quantityUnit',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
                                    field: 'taxCode',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'),
                                    field: 'taxRate',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '净价'),
                                    field: 'netPrice',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'),
                                    field: 'price',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryQuantity`, '发货数量'),
                                    field: 'deliveryQuantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantity`, '收货数量'),
                                    field: 'receiveQuantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remainQuantity`, '剩余数量'),
                                    field: 'remainQuantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRemarks`, '供方备注'),
                                    field: 'supplierRemark',
                                    width: 220
                                }]
                        }
                    }
                ],
                url: {
                    detail: '/delivery/deliverySaleHead/queryDetailById'
                }
            }
            
        }
    },
    mounted () {
       
    },
    created (){
      
    },
    methods: {
        init (row) {
            let params = {id: row.id}
            this.$refs.detailPage.getPageDetail(params)
        },
        hide () {
            this.$emit('hide')
        }
    }
}
</script>
