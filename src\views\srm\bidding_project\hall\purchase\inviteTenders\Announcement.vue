<template>
  <div class="announcement">
    <div
      v-if="vuex_currentEditRow.id"
      class="container">
      <component
        :is="curComp"
        :current-edit-row="vuex_currentEditRow"
        @routerRefreshEvent="handleRouterRefresh" />
    </div>
  </div>
</template>

<script>
import PurchaseBiddingEdit from './components/PurchaseBiddingEdit'
import PurchaseBiddingDetail from './components/PurchaseBiddingDetail'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    name: 'ProjectAnnouncement',
    components: {
        'purchase-bidding-edit': PurchaseBiddingEdit,
        'purchase-bidding-detail': PurchaseBiddingDetail
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'getBiddingOptions'
    ],
    data () {
        return {}
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        isEdit () {
            // 审批状态
            // 0: 未审批, 1: 审批中, 2: 审批通过, 3: `审批拒绝`, 4: 无需审批
            let { auditStatus = '', biddingStatus = '' } = this.vuex_currentEditRow || {}
            let enableArr = ['0', '3', '4']
            return enableArr.includes(auditStatus) && biddingStatus === '0'
        },
        curComp () {
            return this.isEdit ? 'purchase-bidding-edit' : 'purchase-bidding-detail'
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleRouterRefresh () {
            this.getBiddingOptions()
        }
    },
    // 使用 beforeRouteUpdate 导航守卫监听路由变化
    // 对路由销毁重建
    beforeRouteUpdate (to, from, next) {
        this.routerRefresh() //路由销毁重建方法
        next()
    }
}
</script>

<style lang="less" scoped>
.announcement {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
}
</style>
