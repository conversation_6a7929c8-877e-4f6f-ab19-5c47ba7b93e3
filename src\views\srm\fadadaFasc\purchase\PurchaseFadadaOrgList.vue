<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <EditPurchaseFadadaOrg-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideEditPage" />
    <ViewPurchaseFadadaOrg-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import EditPurchaseFadadaOrgModal from './modules/EditPurchaseFadadaOrgModal'
import ViewPurchaseFadadaOrgModal from './modules/ViewPurchaseFadadaOrgModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import { httpAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EditPurchaseFadadaOrgModal,
        ViewPurchaseFadadaOrgModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'fadada',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'fadada#purchaseFadadaOrg:add', icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'fadada#purchaseFadadaOrg:view', clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'fadada#purchaseFadadaOrg:edit', clickFn: this.handleEdit, allow: this.showEditCondition},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'fadada#purchaseFadadaOrg:delete', clickFn: this.handleDelete, allow: this.showDelCondition},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_XVlbLizE_6dfc183c`, '刷新授权认证状态'), authorityCode: 'fadada#purchaseFadadaOrg:updateAuthStatus', clickFn: this.handleUpdate, allow: this.showUpdateCondition},
                    {type: 'edit', title: '获取企业授权认证信息', authorityCode: 'fadada#purchaseFadadaOrg:getAuthInfo', clickFn: this.getAuthInfo, allow: this.showGetAuthInfo},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 300
            },
            url: {
                list: '/electronsign/fadada/purchaseFadadaOrg/list',
                add: '/electronsign/fadada/purchaseFadadaOrg/add',
                delete: '/electronsign/fadada/purchaseFadadaOrg/delete',
                deleteBatch: '/electronsign/fadada/purchaseFadadaOrg/deleteBatch',
                update: '/electronsign/fadada/purchaseFadadaOrg/updateAuthStatus',
                getAuthInfo: '/electronsign/fadada/purchaseFadadaOrg/getAuthInfo',
                columns: 'purchaseFadadaOrgList'
            }
        }
    },
    methods: {
        showGetAuthInfo (row){            
            //授权成功的情况下不允许编辑操作
            if(row.authFailedReason==='exist'){
                return false
            }
            return true
        },
        handleChidCallback (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleAdd () {          
            this.currentEditRow = {}
            this.showEditPage = true
        },
        handleView (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },
        showCancelCondition (row){
            return false
        },
        showEditCondition (row) {
            //授权成功的情况下不允许编辑操作
            if(row.authResult==='success' || row.authResult==='authorized'){
                return true
            }
            return false
        },
        showDelCondition (row) {
            //未生成法大大个人账号的情况下允许删除操作
            if(!row.openCorpId || row.openCorpId==''){
                return false
            }else{
                return true
            }
        },
        showUpdateCondition (row){
            //未生成法大大账号的情况下不允许刷新操作
            if(!row.openCorpId || row.openCorpId==''){
                return true
            }else{
                return false
            }
        },
        submitAuthCallBack (){
            this.showDetailPage = true
            this.showEditPage = false
            this.searchEvent()
        },
        handleUpdate (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_XVlbLizE_6dfc183c`, '刷新授权认证状态'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLKQXV_60c32a66`, '确认是否刷新?'),
                onOk: function () {
                    that.postUpdateData(that.url.update, row)
                }
            })
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls('法大大机构（采方）')
        },
        getAuthInfo (row){
            this.postUpdateData(this.url.getAuthInfo, row)
        }
    }
}
</script>