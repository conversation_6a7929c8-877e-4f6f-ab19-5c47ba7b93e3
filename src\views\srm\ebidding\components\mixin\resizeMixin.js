// 混入代码 resize-mixins.js
import { debounce } from 'lodash'
const resizeChartMethod = '$__resizeChartMethod'

export default {
    data () {
        // 在组件内部将图表init的引用映射到chart属性上
        return {
            chart: null
        }
    },
    created () {
        window.addEventListener('resize', this[resizeChartMethod])
    },
    beforeD<PERSON>roy () {
        window.removeEventListener('reisze', this[resizeChartMethod])
        this.chart && this.chart.dispose()
        this.chart = null
    },
    methods: {
        // 通过lodash的防抖函数来控制resize的频率
        [resizeChartMethod]: debounce(function () {
            if (this.chart) {
                this.chart.resize()
            }
        }, 100)
    }
}
