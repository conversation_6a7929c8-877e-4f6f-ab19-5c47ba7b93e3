<!--
 * @Author: your name
 * @Date: 2021-03-30 15:39:46
 * @LastEditTime: 2022-01-14 17:50:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\views\srm\contract\purchase\modules\HisPurchaseContractItemModal.vue
-->
<template>
  <a-modal
    v-drag
    v-model="madalVisible"
    :title="modalTitle"
    :width="1100"
    @cancel="cancelEvent"
    @ok="editOk">
    <a-spin :spinning="confirmLoading">
      <div>
        <a-form-model
          :model="form"
          layout="inline">
          <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_projectType`, '项目类型')">
            {{ form.itemType_dictText }}
          </a-form-model-item>
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_SaleMassProdHeadList_projectName`, '项目名称')">
            {{ form.itemName }}
          </a-form-model-item>
        </a-form-model>
        <a-form-model
          :model="form">
          <a-form-model-item
            label="">
            <div
              style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
              v-html="form.itemContent"></div>
          </a-form-model-item>
        </a-form-model>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>

export default {
    name: 'HisContractItemModal',
    components: {},
    data () {
        return {
            modalTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
            confirmLoading: false,
            madalVisible: false,
            labelCol: {
                xs: {span: 24},
                sm: {span: 5}
            },
            wrapperCol: {
                xs: {span: 24},
                sm: {span: 16}
            },
            form: {
                itemContent: '', id: '', itemName: '', itemType: '3', itemVersion: ''
            }
        }
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            this.madalVisible = true
            this.form = this.currentEditRow
        },
        goBack () {
            this.$emit('hide')
            this.madalVisible = false
        },
        editOk () {
            this.madalVisible = false
            this.$emit('hide')
        },
        cancelEvent () {
            this.madalVisible = false
        }
    }
}
</script>
