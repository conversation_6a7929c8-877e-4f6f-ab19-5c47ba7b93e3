<template>
  <div>
    <a-spin :spinning="spinning">
      <a-modal
        centered
        :footer="null"
        :mask-closable="false"
        :title="title"
        :visible="visible"
        :width="1200"
        @cancel="visible = false">
        <div
          class="content-container"
          v-if="visible && !spinning">
          <a-tabs
            :default-active-key="activeKey"
            @change="changeTab"
          >
            <a-tab-pane
              v-for="(group) in groups"
              :key="group.groupCode"
              :tab="group.groupName">
            </a-tab-pane>
          </a-tabs>
          <vxe-grid
            ref="selectedGrid"
            v-bind="gridConfig"
            :data="tableDatas"
            :columns="columns"
            :height="500"
            :loading="spinning">
            <template #empty>
              <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
            </template>
            <template #bottom>
              <div
                class="summary-message">
                <span class="summary-message-content">
                  {{ $srmI18n(`${busAccount}#${groupsMap[activeKey].groupNameI18nKey}`, groupsMap[activeKey].groupName) }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_kMk_176ba4f`, '总汇总') }}：<span class="total-num">{{ totalValue }}</span>
                </span>
              </div>
            </template>
          </vxe-grid>
        </div>
      </a-modal>
    </a-spin>
    <remote-js
      v-if="visible"
      :current-edit-row="currentEditRow"
      :src="src"
      @remote-js-load-success="loadRemoteJsSuccess"
      @remote-js-load-error="loadRemoteJsError"/>
  </div>
</template>
    
<script lang="jsx">
import RemoteJs from '@comp/template/business/remote-js'
import {add, div} from '@/utils/mathFloat.js'
import {ajaxFindDictItems} from '@/api/api'
export default {
    components: {RemoteJs},
    data (){
        return{
            allColumns: {},
            currentEditRow: {},
            gridConfig: {
                autoResize: true,
                border: true,
                columnKey: true,
                highlightHoverRow: true,
                resizable: true,
                round: true,
                rowKey: true,
                showFooter: true,
                showHeaderOverflow: 'title',
                showOverflow: 'title',
                size: 'mini'
            },
            title: '成本模板',
            activeKey: '',
            groups: [],
            groupsMap: {},
            dataList: {},
            groupCode: '',
            src: '',
            spinning: false,
            visible: false,
            busAccount: '',
            dictCodeMap: {},
            typeOfPrice: '',
            taxRate: ''
        }
    },
    computed: {
        tableDatas () {
            let d = this.dataList[this.activeKey] || []
            return d
        },
        columns () {
            return this.allColumns[this.activeKey] || []
        },
        totalValue () {
            let total = this.typeOfPrice == 'netPrice' ? this.calculatePreTaxPrice(this.groupsMap[this.activeKey].totalValue) : this.groupsMap[this.activeKey].totalValue.toFixed(6)
            return total
        }
    },
    methods: {
        loadRemoteJsError (err){
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMIrKmWWVImW_2e550783`, '获取模板失败, 请检查：') + err)
        },
        loadRemoteJsSuccess (){
            const configData = getPageConfig() // eslint-disable-line
            configData.groups.forEach((item, i) => {
                this.groups[i].groupNameI18nKey = item.groupNameI18nKey
                this.groupsMap[item.groupCode] = this.groups[i]
            })
            this.activeKey = this.groups[0].groupCode
            let dictCodeItems = []
            let dictOfField = []
            let promises = []
            configData.itemColumns.forEach(element => {
                let {field, title, groupCode, dictCode} = element
                if (!this.allColumns[groupCode]) this.allColumns[groupCode] = [{fixed: 'left', type: 'seq', width: 50}]
                if (dictCode) {
                    if (!dictCodeItems.includes(dictCode)) {
                        dictCodeItems.push(dictCode)
                        dictOfField.push(field)
                    }
                    this.allColumns[groupCode].push({field, title, width: 100, slots: {
                        default: ({ row, column }) => {
                            return [<span>{ this.getDictLabel(row[column.property], dictCode) }</span>]
                        }
                    }})
                } else {
                    this.allColumns[groupCode].push({field, title, width: 100})
                }
            })
            promises = dictCodeItems.map((n) => {
                let params = {
                    busAccount: this.busAccount,
                    dictCode: n
                }
                return ajaxFindDictItems(params)
            })
            if (this.typeOfPrice == 'netPrice') {
                Object.values(this.dataList).map(item => {
                    item.map(row => {
                        for(let k in row) {
                            if (!['_X_ROW_KEY', '_X_ID', ...dictOfField].includes(k)) {
                                row[k] = this.calculatePreTaxPrice(row[k])
                            }
                        }
                    })
                }) 
            }
            if (dictCodeItems.length > 0) {
                Promise.all(promises).then((res) => {
                    res.map((n) => n.result).map((list,  i) => {
                        list.map(item => {
                            let code = dictCodeItems[i]
                            if (!this.dictCodeMap[code]) this.dictCodeMap[code] = {}
                            this.dictCodeMap[code][item.value] = item.title
                        })
                    })
                    console.log(this.dictCodeMap)
                    this.spinning = false
                })
            } else {
                this.spinning = false
            }
        },
        calculatePreTaxPrice (priceWithTax) {
            return div(priceWithTax, add(1, div(this.taxRate, 100))).toFixed(6)
        },
        getDictLabel (v, code) {
            return this.dictCodeMap[code] ? this.dictCodeMap[code][v] : v
        },
        changeTab (v) {
            this.activeKey = v
        },
        open ({currentEditRow, data, typeOfPrice, taxRate}){
            console.log('data', data)
            this.typeOfPrice = typeOfPrice
            this.taxRate = taxRate
            this.spinning = true
            this.dataList = data.data
            this.groups = data.groups
            this.currentEditRow = currentEditRow
            let {
                busAccount,
                templateNumber,
                templateVersion } = currentEditRow
            this.busAccount = busAccount
            const time = new Date().getTime()
            this.allColumns = {}
            this.src = `${this.$variateConfig['configFiles']}/${busAccount}/purchase_costForm_${templateNumber}_${templateVersion}.js?t=${time}`
            this.visible = true
        }
    },
    name: 'CostComparison'
}
</script>
    
<style lang="less" scoped>
.summary-message {
  height: 14px;
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.summary-message-content {
  flex-grow: 1;
  font-weight: bolder;

  .total-num {
    font-size: 16px;
    color: red;
  }
}
:deep(.ant-modal-body) {
    padding: 0
}
.content-container {
    height: 600px;
    padding: 10px;
}
</style>