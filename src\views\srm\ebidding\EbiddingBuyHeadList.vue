<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showNewRoundPage && !showPrintPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <ebidding-buy-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPageNew"/>
    <!-- 详情界面 -->
    <ebidding-buy-detail 
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
    <!-- 轮次界面 -->
    <Purchase-Ebidding-New-Round 
      v-if="showNewRoundPage" 
      ref="newRoundPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideNewRoundPage" />

    <!-- 竞价打印 -->
    <EbiddingPrint
      v-if="showPrintPage"
      :current-edit-row="currentEditRow"
      ref="printPage"
      @hide="hidePrintPage" />
    <!-- 变更弹窗 -->
    <a-modal
      v-drag    
      v-model="regretVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_tip`, '提示')"
      @ok="handleRegret">
      <div>
        <a-radio-group
          v-model="regretValue"> 
          <a-radio
            class="radio-wrap"
            :value="0">{{ $srmI18n(`${$getLangAccount()}#i18n_title_noMoreFulfillment`, '悔标，不再寻源') }}</a-radio> 
          <a-radio
            class="radio-wrap"
            :value="1">{{ $srmI18n(`${$getLangAccount()}#i18n_title_replaceSourcing`, '悔标，重新寻源') }}</a-radio> 
          <a-radio
            class="radio-wrap"
            :value="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_priceComparison`, '重新比价/定标') }}</a-radio>
        </a-radio-group>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import EbiddingBuyEdit from './modules/EbiddingBuyEdit'
import EbiddingBuyDetail from './modules/EbiddingBuyDetail'
import PurchaseEbiddingNewRound from './modules/PurchaseEbiddingNewRound'
import EbiddingPrint from './modules/EbiddingPrint.vue'
import layIM from '@/utils/im/layIM.js'
import { handleDemandPool } from '@/utils/util'
import { postAction, getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        EbiddingBuyEdit,
        EbiddingBuyDetail,
        PurchaseEbiddingNewRound,
        EbiddingPrint
    },
    data () {
        return {
            regretValue: 0,
            regretVisible: false,
            currentRow: {},
            showNewRoundPage: false,
            showPrintPage: false,
            pageData: {
                businessType: 'ebidding',
                form: {
                    ebiddingDesc: '',
                    ebiddingNumber: '',
                    ebiddingStatus: undefined
                },
                button: [
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary',
                        authorityCode: 'ebidding#purchaseEbiddingHead:add' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationHistory`, '报价历史'), icon: 'snippets', clickFn: this.showHistoryList,
                        authorityCode: 'ebidding#purchaseEbiddingHead:hisList' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: true, clickFn: this.settingColumns },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', folded: true, clickFn: this.handleExportXls,
                        authorityCode: 'ebidding#purchaseEbiddingHead:exportXls' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF }
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号'),
                        fieldName: 'ebiddingNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterBidDocumentNo`, '请输入竞价单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingStatus`, '竞价单状态'),
                        fieldName: 'ebiddingStatus',
                        dictCode: 'srmEbiddingStatus',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDocumentStatus`, '请选择竞价单状态')
                    }
                ],
                optColumnWidth: 290,
                optColumnList: [
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView,
                        authorityCode: 'ebidding#purchaseEbiddingHead:queryById'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit,
                        authorityCode: 'ebidding#purchaseEbiddingHead:edit'},
                    {type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制')
                        , clickFn: this.handleCopy, authorityCode: 'ebidding#purchaseEbiddingHead:copy'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_newRound`, '新轮次'), clickFn: this.createNew, allow: this.allowCreateNew,
                        authorityCode: 'ebidding#purchaseEbiddingHead:createNewRound'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowEdit,
                        authorityCode: 'ebidding#purchaseEbiddingHead:delete'},
                    {
                        type: 'print', 
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印'), 
                        clickFn: this.handlePrint,
                        allow: this.allowPrint,
                        authorityCode: 'ebidding#purchaseEbiddingHead:print'
                    },
                    {
                        type: 'openLobby', 
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_OufY_390dbcd7`, '竞价大厅'),
                        clickFn: this.openLobby,
                        allow: this.allowOpenLobby,
                        authorityCode: 'ebidding#purchaseEbiddingHead:queryById'
                    },
                    {type: 'regret', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regretBid`, '悔标'), clickFn: this.showRegret, allow: this.allowRegret,
                        authorityCode: 'ebidding#purchaseEbiddingHead:regret'},
                    {type: 'cancel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.cancel, allow: this.allowCancel,
                        authorityCode: 'ebidding#purchaseEbiddingHead:cancel'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/ebidding/purchaseEbiddingHead/add',
                list: '/ebidding/purchaseEbiddingHead/list',
                delete: '/ebidding/purchaseEbiddingHead/delete',
                regret: '/ebidding/purchaseEbiddingHead/regret',
                cancel: '/ebidding/purchaseEbiddingHead/cancel',
                columns: 'purchaseEbiddingHead',
                excelCode: 'ebidding',
                exportXlsUrl: '/ebidding/purchaseEbiddingHead/exportXls',
                copyData: '/ebidding/purchaseEbiddingHead/copy'
            },
            countTabsUrl: '/ebidding/purchaseEbiddingHead/count'
        }
    },
    created () {
        if (this.$route.query.id) {
            this.pageData.form.id = this.$route.query.id
            this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
        } else{
            this.pageData.form.id = ''
            this.countTabsUrl = '/ebidding/purchaseEbiddingHead/count'
        }
    },
    mounted () {
        this.serachCountTabs(this.countTabsUrl)
    },
    // activated (){
    //     this.init()
    // },
    beforeRouteEnter (to, from, next) {
        if (Object.keys(to.query).length) {
            next(vm => vm.init())
        } else {
            next()
        }
    },
    methods: {
        // 返回按钮
        hideEditPageNew () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) { 
                // 关闭超链接页面
                this.closeCurrent()
            }
            if (query.source == 'demand-pool') {
                this.$router.replace({path: this.$route.path})
            }
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        //竞价大厅
        openLobby (row){
            const {
                ebiddingMethod = '0', ebiddingWay,
                ebiddingNumber,
                currentItemNumber
            } = row || {}
            this.$store.dispatch('SetTabConfirm', false)
            // 竞价方式 - ebiddingMethod '0' || null 英式，'1' 日式，'2' 荷式
            if (ebiddingMethod === '0') {
                // this.$router.push({
                //     path: '/ebidding/buyLobbyNew',
                //     query: {
                //         id: row.id,
                //         ebiddingNumber,
                //         currentItemNumber
                //     }
                // })
                window.open(`${window.origin}/ebidding/buyLobbyNew?id=${row.id}&ebiddingNumber=${ebiddingNumber}&currentItemNumber=${currentItemNumber}`, '_blank')
            } else if (ebiddingMethod === '1' || ebiddingWay === '3') { // 英式 或 一次性
                // this.$router.push({
                //     path: '/ebidding/buyLobbyNewJap',
                //     query: {
                //         id: row.id,
                //         ebiddingNumber,
                //         currentItemNumber
                //     }
                // })
                window.open(`${window.origin}/ebidding/buyLobbyNewJap?id=${row.id}&ebiddingNumber=${ebiddingNumber}&currentItemNumber=${currentItemNumber}`, '_blank')
            } else if (ebiddingMethod === '2') {
                window.open(`${window.origin}/ebidding/buyLobbyNewDutch?id=${row.id}&ebiddingNumber=${ebiddingNumber}&currentItemNumber=${currentItemNumber}`, '_blank')
            }
        },
        // 复制功能按钮
        handleCopy (row) {
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(row)
                }
            })
        },
        // 复制数据请求接口
        copyData (row) {
            this.confirmLoading = true
            let param  = { id: row.id }
            getAction(this.url.copyData, param).then(res => {
                if (res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功！'))
                    this.searchEvent()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        init (){
            if (this.$route.query.id && this.$route.path.includes('EbiddingBuyHeadList')) {
                this.pageData.form.id = this.$route.query.id
                this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
            }else{
                this.pageData.form.id = ''
                this.countTabsUrl = '/ebidding/purchaseEbiddingHead/count'
            }
            this.$refs.listPage.initColumns()
            this.$refs.listPage.loadData()
            this.$refs.listPage.columnDrop()
            handleDemandPool(this)
        },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.ebiddingNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'EbiddingBuy', url: this.url || '', recordNumber})
        }, 
        allowChat (row){
            let ebiddingStatus = row.ebiddingStatus
            return ebiddingStatus == '0' || ebiddingStatus == '11'
        },
        allowEdit (row) {
            //审批中禁用
            if(row.auditStatus == '1'){
                return true
            }
            //新建不禁用
            return row.ebiddingStatus != '0'
        },
        allowCreateNew (row) {
            //竞价状态 结束 5 且 结果审批 拒绝 3、无需 4、未审批 0，则允许点击“新轮次”(此处allow 方法判断反，disabled属性)
            return !(row.ebiddingStatus === '5' && ['0', '3', '4'].includes(row.resultAuditStatus))
        },
        // 悔标：已授标状态可操作
        allowRegret (row) {
            return row.ebiddingStatus !== '6' // ？？判断反了？
        },
        allowCancel (row){
            // 待应标 && 待竞价
            return row.ebiddingStatus != '1' && row.ebiddingStatus != '3'
        },
        allowOpenLobby (row){
            let {ebiddingStatus}=row
            return ebiddingStatus=='0'
        },
        allowPrint (row){
            // 已授标状态可操作
            return row.ebiddingStatus !== '6'
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (row){
            this.currentEditRow = row
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        createNew (row){
            this.currentEditRow = row
            this.showNewRoundPage = true
        },
        hideNewRoundPage (){
            this.showNewRoundPage = false
        },
        hidePrintPage (){
            this.showPrintPage = false
        },  
        handlePrint (row) {
            this.showPrintPage = true
            this.currentEditRow = row
        },
        showHistoryList (){
            this.$store.dispatch('SetTabConfirm', false)
            this.$router.push({
                path: '/ebidding/purchaseEbiddingHisList'
            })
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls('竞价')
        },
        showRegret (row){
            this.regretVisible = true
            this.currentRow = row
        },
        handleRegret (){
            let headId = this.currentRow.id
            postAction(this.url.regret, {id: headId, regretFlag: this.regretValue}).then(res => {
                if(res.success) {
                    this.regretVisible = false
                    this.$message.success(res.message)
                    this.$refs.listPage.loadData()
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        cancel (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoid`, '确认作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQkumOutW_7334b1ed`, '是否作废此竞价单?'),
                onOk: function () {
                    that.loading = true
                    getAction(that.url.cancel, {id: row.id}).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData()
                        }else {
                            that.$message.warning(res.message)
                        }
                    })   
                }
            })
        }
    }
}
</script>
<style lang="less" scoped>
.radio-wrap {
    display: block;
    height: 30px;
}
</style>
