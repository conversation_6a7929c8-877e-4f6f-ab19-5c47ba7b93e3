<template>
  <div
    class="business-container"
    :style="{height: pageContentHeight}">

    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="show"
        :ref="businessRefName"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :currentEditRow="{}"
        :pageFooterButtons="pageFooterButtons"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        :handleAfterDealSource="handleAfterDealSource"
        :fromSourceData="fromSourceData"
        :pageStatus="pageStatus" 
        v-on="businessHandler">
        <template #noticeInfo="{ slotProps }">
          <j-editor
            v-if="pageStatus == 'edit'"
            ref="ueditor"
            v-model="noticeContent" />
          <div
            v-else
            class="ueditorDetail">
            <div v-html="noticeContent"></div>
          </div>
        </template>
      </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk"
        isTree
        needSearch />
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRowData" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@views/srm/bidding_new/BiddingHall/components/fieldSelectModal'
import JEditor from '@/components/els/JEditor'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { baseMixins } from '../../plugins/baseMixins.js'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { BUTTON_PUBLISH } from '@/utils/constant.js'
import flowViewModal from '@comp/flowView/flowView'

import { merge } from 'lodash'
export default {
    name: 'TenderNoticeHead',
    components: {
        BusinessLayout,
        JEditor,
        fieldSelectModal,
        flowViewModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage', 'resetCurrentSubPackage'],
    mixins: [businessUtilMixin, baseMixins],
    data () {
        return {
            businessRefName: 'businessRef',
            flowView: false,
            flowId: 0,
            id: '',
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            show: false,
            externalToolBar: {
                purchaseTenderNoticeItemList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    type: 'primary',
                    click: this.saveEvent
                },
                {
                    ...BUTTON_PUBLISH,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                    args: {
                        url: '/tender/purchaseTenderNoticeHead/publish'
                    },
                    click: this.publishEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bL_e90d1`, '生成'),
                    click: this.generateTemplateEvent
                }
            ],
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.cancelAudit,
                    show: this.cancelAuditShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.viewButtonShow
                }
            ],
            currentGroupCode: {},
            noticeStatus: '',
            fromSourceData: {},
            noticeContent: '',
            url: {
                detail: '/tender/purchaseTenderNoticeHead/queryBySubpackageId',
                add: '/tender/purchaseTenderNoticeHead/add',
                edit: '/tender/purchaseTenderNoticeHead/edit',
                publish: '/tender/purchaseTenderNoticeHead/publish',
                generateTemplate: '/tender/purchaseTenderNoticeHead/generator'
            },
            remoteJsFilePath: '',
            // 解决页面控制台报 currentEditRow 错误
            currentEditRowData: {}
        }
    },
    computed: {
        pageStatus () {
            let status = this.noticeStatus == '0' ? 'edit' : 'detail'
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') status = 'detail'
            return status
        },
        subId () {
            return this.subpackageId()
        },
        subpackage () {
            return this.currentSubPackage()
        },
        pageContentHeight () {
            let height = document.documentElement.clientHeight - 60
            return height + 'px'
        }
    },
    methods: {
        generateTemplateEvent () {
            let params = this.getParamsData()
            params.id = this.id ? this.id : ''
            params.bidding = this.bidding
            params.signUp = this.signUp
            if(!params.templateLibraryId){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFRxIr_987441cd`, '请先选择公告模板'))
                return 
            }
            this.confirmLoading = true
            postAction(this.url.generateTemplate, params)
                .then((res) => {
                    // 刷新
                    if (res.success) {
                        this.$message.success(res.message)
                        // this.noticeContent = res.result.content
                        this.init()
                    }else{
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
        },
        publishEvent () {
            let params = this.getParamsData()
            try {
                //购标开始时间必须小于购标结束时间
                if (params.biddingEndTime && new Date(params.biddingBeginTime).getTime() >= new Date(params.biddingEndTime).getTime()) {
                    if(this.checkType == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUlBvKKIlTXUUUlByWKI_7071cce0`, '预审授标开始时间必须小于预审授标结束时间'))
                    }else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBvKKIlTXUlByWKI_bdc4b7e6`, '购标开始时间必须小于购标结束时间'))
                    }
                    return
                }
                //报名开始时间必须小于报名结束时间
                if (params.signUpEndTime && new Date(params.signUpBeginTime).getTime() >= new Date(params.signUpEndTime).getTime()) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sRvKKIlTXUsRyWKI_1366c8b8`, '报名开始时间必须小于报名结束时间'))
                    return
                }
                //文件澄清截止时间必须小于等于开标时间
                if (new Date(params.fileClarificationEndTime).getTime() > new Date(params.openBiddingTime).getTime()) {
                    if(this.checkType == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUQILVyRKIlTXUEUUUvBKI_9f7201df`, '预审文件澄清截止时间必须小于等于预审开标时间'))
                    }else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QILVyRKIlTXUEUvBKI_1889c9a5`, '文件澄清截止时间必须小于等于开标时间'))
                    }
                    return
                }
                //递交截止时间必须大于等于售标结束时间
                if (params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime() < new Date(params.biddingEndTime).getTime()) {
                    if(this.checkType == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUnJyRKIlTfUEUUUlByWKI_1c8ce607`, '预审递交截止时间必须大于等于预审售标结束时间'))
                    }else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTfUEUlByWKI_803238d`, '递交截止时间必须大于等于售标结束时间'))
                    }
                    return
                }
                //递交截止时间必须小于等于开标时间
                if (params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime() > new Date(params.openBiddingTime).getTime()) {
                    if(this.checkType == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUnJyRKIlTXUEUUUvBKI_975bf061`, '递交截止时间必须小于等于开标时间'))
                    }else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTXUEUvBKI_59297d67`, '递交截止时间必须小于等于开标时间'))
                    }
                    return
                }
                //公告内容不能为空
                if (this.noticeContent == '') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOKmRxCcxOLV_c478a5c4`, '校验失败！公告内容不能为空'))
                    return
                }
                // // 发布招标公告时必填项通知标题校验
                // if (!params.noticeTitle) {
                //     this.$message.warning('请选择公告标题')
                //     return
                // }
                // // 发布招标公告时必填项通知范围校验
                // if (!params.noticeScope) {
                //     this.$message.warning('请选择通知范围')
                //     return
                // }
                var that = this
                let flag = false
                params.purchaseTenderNoticeItemList.forEach((v, index) => {
                    // 选的保证金收取方式不为 不收取 同时保证金为空
                    if (v.marginCollectionType !== '4' && (v.margin ?? '') === '') {
                        that.$message.warning(`第${index + 1}行的保证金不能为空`)
                        flag = true
                    }
                })
                if (flag) {
                    return
                }
                // 调用模板上的数据校验方法
                // this.stepValidate(this.$refs[this.businessRefName])
            } catch (err) {
                console.log(err)
            }

            params.account = this.currentEditRowData.templateAccount || ''
            // if (!this.id) {
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
            //     return false
            // }
            params.id = this.id

            params.bidding = this.bidding
            params.signUp = this.signUp
            params.tenderProjectName = this.tenderCurrentRow.tenderProjectName
            
            this.confirmLoading = true
            postAction(this.url.publish, params)
                .then((res) => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    // 刷新
                    if (res.success) {
                        // this.$emit('resetCurrentSubPackage')
                        this.resetCurrentSubPackage()
                        this.init()
                    }
                })
                .catch((err) => {
                    this.$message.error(err)
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        saveEvent (cb) {
            let params = this.getParamsData()
            try {
                //购标开始时间必须小于购标结束时间
                if (params.biddingEndTime && new Date(params.biddingBeginTime).getTime() >= new Date(params.biddingEndTime).getTime()) {
                    if(this.checkType == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUlBvKKIlTXUUUlByWKI_7071cce0`, '预审授标开始时间必须小于预审授标结束时间'))
                    }else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBvKKIlTXUlByWKI_bdc4b7e6`, '购标开始时间必须小于购标结束时间'))
                    }
                    return
                }
                //报名开始时间必须小于报名结束时间
                if (params.signUpEndTime && new Date(params.signUpBeginTime).getTime() >= new Date(params.signUpEndTime).getTime()) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sRvKKIlTXUsRyWKI_1366c8b8`, '报名开始时间必须小于报名结束时间'))
                    return
                }
                //文件澄清截止时间必须小于等于开标时间
                if (new Date(params.fileClarificationEndTime).getTime() > new Date(params.openBiddingTime).getTime()) {
                    if(this.checkType == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUQILVyRKIlTXUEUUUvBKI_9f7201df`, '预审文件澄清截止时间必须小于等于预审开标时间'))
                    }else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QILVyRKIlTXUEUvBKI_1889c9a5`, '文件澄清截止时间必须小于等于开标时间'))
                    }
                    return
                }
                //递交截止时间必须大于等于售标结束时间
                if (params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime() < new Date(params.biddingEndTime).getTime()) {
                    if(this.checkType == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUnJyRKIlTfUEUUUlByWKI_1c8ce607`, '预审递交截止时间必须大于等于预审售标结束时间'))
                    }else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTfUEUlByWKI_803238d`, '递交截止时间必须大于等于售标结束时间'))
                    }
                    return
                }
                //递交截止时间必须小于等于开标时间
                if (params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime() > new Date(params.openBiddingTime).getTime()) {
                    if(this.checkType == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUnJyRKIlTXUEUUUvBKI_975bf061`, '递交截止时间必须小于等于开标时间'))
                    }else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTXUEUvBKI_59297d67`, '递交截止时间必须小于等于开标时间'))
                    }
                    return
                }
                // // 发布招标公告时必填项通知标题校验
                // if (!params.noticeTitle) {
                //     this.$message.warning('请选择公告标题')
                //     return
                // }
                // // 发布招标公告时必填项通知范围校验
                // if (!params.noticeScope) {
                //     this.$message.warning('请选择通知范围')
                //     return
                // }
            } catch (err) {
                console.log(err)
            }

            let url = this.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            params.id = this.id ? this.id : ''
            params.bidding = this.bidding
            params.signUp = this.signUp
            // let {templateLibraryId, templateTitle} = this.$refs.businessRef.$refs.purchaseTenderNoticeItemListgrid[0].pageConfig.groups[5].formModel
            // params.templateLibraryId = templateLibraryId ||  params.templateLibraryId
            // params.templateTitle = templateTitle ||  params.templateTitle

            
            postAction(url, params)
                .then((res) => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    // 刷新
                    if (res.success) {
                        // 如果新增，走的add接口，则this.id=add请求成功后返回的id
                        if (!this.id) {
                            this.id = res.result.id
                        }
                        if (cb && typeof(cb) == 'function') {
                            cb()
                        } else {
                            this.init()
                        }
                    }
                })
                .catch((err) => {
                    this.$message.error(err)
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        getParamsData () {
            let { purchaseTenderNoticeItemList, fileSubmit, getFile, openTenderInfo, noticeInfo, ...baseForm } = this.$refs[this.businessRefName].extendAllData().allData
            let {
                nodeId,
                extend: { checkType, processType, currentStep, noticeType }
            } = this.currentNode()
            let params = baseForm
            // 文件获取
            params['signUpType'] = getFile.signUpType
            params['signUpBeginTime'] = getFile.signUpBeginTime
            params['signUpEndTime'] = getFile.signUpEndTime
            params['biddingType'] = getFile.biddingType
            params['bidding'] = getFile.bidding
            params['biddingBeginTime'] = getFile.biddingBeginTime
            params['biddingEndTime'] = getFile.biddingEndTime
            params['fileTenderObtainDesc'] = getFile.fileTenderObtainDesc
            params['offlineSaleAccount'] = getFile.offlineSaleAccount
            params['fileClarificationEndTime'] = getFile.fileClarificationEndTime
            // 文件递交
            params['contactsPhone'] = fileSubmit.contactsPhone
            params['fileSubmitEndTime'] = fileSubmit.fileSubmitEndTime
            params['fileSubmitAddress'] = fileSubmit.fileSubmitAddress
            // 开标信息
            params['openBiddingTime'] = openTenderInfo.openBiddingTime
            params['openBiddingAddress'] = openTenderInfo.openBiddingAddress
            // 公告信息
            params['noticeTitle'] = noticeInfo.noticeTitle
            params['noticeScope'] = noticeInfo.noticeScope
            params['noticeContent'] = noticeInfo.noticeContent
            params['templateName'] = noticeInfo.templateName
            params['templateLibraryId'] = noticeInfo.templateLibraryId || this.currentEditRowData.templateLibraryId || ''
            params['templateTitle'] = noticeInfo.templateTitle || this.currentEditRowData.templateTitle || ''
            // 关联分包
            params['purchaseTenderNoticeItemList'] = purchaseTenderNoticeItemList
            params['subpackageId'] = this.subId
            params['tenderProjectId'] = this.tenderCurrentRow.id
            params['noticeType'] = noticeType // checkType == '0' ? '1' : '2'
            params['noticeContent'] = this.noticeContent
            params['tenderTaskId'] = this.$ls.get('SET_TENDERCURRENTROW').tenderTaskId
            params.templateNumber = params.templateNumber || this.currentEditRowData.templateNumber || ''
            params.templateVersion = params.templateVersion || this.currentEditRowData.templateVersion || ''
            params.templateAccount = params.templateAccount || this.currentEditRowData.templateAccount || ''
            params.templateName = params.templateName || this.currentEditRowData.templateName || ''
            // params.templateLibraryId = params.templateLibraryId || this.currentEditRowData.templateLibraryId || ''
            // params.templateTitle = params.templateTitle || this.currentEditRowData.templateTitle || ''
            return params
        },
        businessGridAdd ({ pageConfig, groupCode }) {
            this.currentGroupCode = groupCode
            console.log('groupCode', groupCode)
            let url = '/tender/purchaseTenderProjectHead/subpackage/listAll'
            let columns = [
                { field: 'subpackageName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称') },
                { field: 'subpackageNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsty_268b3941`, '单据行号') }
            ]
            let { fullData } = this.getItemGridRef(this.currentGroupCode).getTableData()
            let ids = fullData.map((item) => item.subpackageId)
            // 已选的不能在勾选
            let checkedConfig = {
                visibleMethod: ({ row }) => {
                    let flag = true
                    if (ids.includes(row.id)) flag = false
                    return flag
                }
            }
            //let superQueryParams = [{'logicSymbol': 'lt', 'fieldCode': 'status', 'fieldType': '', 'dictCode': '', 'fieldValue': '3110', 'joiner': 'AND'}]
            //superQueryParams=encodeURI(JSON.stringify(superQueryParams))
            this.$refs.fieldSelectModal.open(url, { headId: this.tenderCurrentRow.id, signUp: this.signUp, bidding: this.bidding, tenderType: this.tenderType, checkType: this.checkType, status: 3110}, columns, 'multiple', checkedConfig)
        },
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef(this.currentGroupCode)
            let ids = itemGrid.getTableData().fullData.map((item) => item.subpackageId)
            var subpackageList = new Array()
            data.map(({ id, subpackageName }) => {
                if (!ids.includes(id)) {
                    subpackageList.push({
                        subpackageId: id,
                        subpackageName
                    })
                }
            })
            itemGrid.insertAt([...subpackageList], -1)
        },
        init () {
            this.confirmLoading = true
            this.show = false
            let {
                nodeId,
                extend: { checkType, processType, currentStep, noticeType }
            } = this.currentNode()
            // let noticeType = checkType == '0' ? '1' : '2'
            getAction(this.url.detail, { subpackageId: this.subId, noticeType })
                .then(async (res) => {
                    if (res.success) {
                        console.log('res.result::', res.result)
                        
                        if (res.result) {
                            this.fromSourceData = res.result || {}
                            this.noticeStatus = res.result.noticeStatus
                            this.noticeContent = res.result.noticeContent
                            this.id = res.result.id || ''
                            if (this.noticeStatus != '0' || this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') this.externalToolBar = {}
                        } else {
                            this.fromSourceData = {
                                purchaseTenderNoticeItemList: [
                                    {
                                        subpackageId: this.subpackage.id,
                                        subpackageName: this.subpackage.subpackageName
                                    }
                                ]
                            }
                            this.noticeStatus = '0'
                        }
                        this.currentEditRowData = res.result && res.result.templateNumber && res.result.templateAccount ? {
                            templateNumber: res.result.templateNumber,
                            templateName: res.result.templateName,
                            templateVersion: res.result.templateVersion,
                            templateAccount: res.result.templateAccount
                        } : await this.getBusinessTemplate('purchaseTenderNotice')
                        if (!this.currentEditRowData) {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                            return
                        }
                        this.remoteJsFilePath = `${this.currentEditRowData['templateAccount']}/purchase_purchaseTenderNotice_${this.currentEditRowData['templateNumber']}_${this.currentEditRowData['templateVersion']}`
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.show = true
                    this.confirmLoading = false
                })
        },
        handleAfterDealSource (pageConfig, resultData) {
            // 公告需要进行下拉选项的过滤操作
            pageConfig.groups.forEach(group => {
                if (group.groupCode == 'noticeInfo') {
                    group.formFields.forEach(field => {
                        if (field.fieldName == 'noticeScope') {
                            field['filterSelectList'] = [this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RIRdX_9e53ebba`, '指定供应商')]
                        }
                    })
                }
            })
            
            var that = this
            this.$nextTick(() => {
                let itemGrid = this.getItemGridRef('purchaseTenderNoticeItemList')
                let ids = itemGrid.getTableData().fullData.map((item) => item.subpackageId)
                // 若不包含本分包，则默认插入本分包
                if (!ids.includes(that.subId)) {
                    itemGrid.loadData([
                        {
                            subpackageId: that.subpackage.id,
                            subpackageName: that.subpackage.subpackageName
                        }
                    ])
                }
            })

            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }
            let signUpFlag = this.signUp !== '1'
            let biddingFlag = this.bidding !== '1'
            // let none = (signUpFlag && biddingFlag)
            // 隐藏数组内的字段
            let arr = []
            // 非报名情况
            if (signUpFlag) {
                arr = arr.concat('signUpBeginTime', 'signUpEndTime', 'signUpType')
            }
            //非购标情况
            if (biddingFlag) {
                arr = arr.concat('biddingBeginTime', 'biddingEndTime', 'biddingType', 'offlineSaleAccount')
            }
            // 不报名不购标情况
            // if(none){
            //     arr=arr.concat('fileSubmitEndTime')
            // }
            let formFields = []
            pageConfig.groups[1].formFields.forEach((item) => {
                if (arr.indexOf(item.fieldName) == -1) {
                    formFields.push(item)
                }
            })
            pageConfig.groups[1].formFields = formFields

            setDisabledByProp('signUpBeginTime', signUpFlag)
            setDisabledByProp('signUpEndTime', signUpFlag)
            setDisabledByProp('signUpType', signUpFlag)
            setDisabledByProp('biddingBeginTime', biddingFlag)
            setDisabledByProp('biddingEndTime', biddingFlag)
            // setDisabledByProp('fileSubmitEndTime', none)

            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }

                rule[prop] = [
                    {
                        required: flag,
                        message: `${fieldLabel}必填`
                    }
                ]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            //此处写根据分包信息作为判断展示位
            let signUpValidateFlag = this.signUp == '1'
            let biddingValidateFlag = this.bidding == '1'
            // let Or = (signUpValidateFlag || biddingValidateFlag)
            setValidateRuleByProp('signUpBeginTime', signUpValidateFlag)
            setValidateRuleByProp('signUpEndTime', signUpValidateFlag)
            setValidateRuleByProp('signUpType', signUpValidateFlag)

            setValidateRuleByProp('biddingBeginTime', biddingValidateFlag)
            setValidateRuleByProp('biddingEndTime', biddingValidateFlag)
            // setValidateRuleByProp('fileSubmitEndTime', Or)
        },
        cancelAudit ({ Vue, pageConfig, btn, groupCode }) {
            this.auditStatus = pageConfig.groups[0].formModel.auditStatus
            let that = this
            let params = this.fromSourceData
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData('/a1bpmn/audit/api/cancel', params)
                    // that.$refs.businessRefName.loadData()
                }
            })
        },
        cancelAuditShow ({ pageData }) {
            // 判断是否为审批中，审批中才展示撤销审批按钮
            return pageData.auditStatus == '1' ? true : false
        },
        viewButtonShow ({ pageData }) {
            // 判断是否需要审批且有flowid，都具备才展示查看流程按钮
            if (pageData.audit != '1') {
                return false
            }

            if (pageData.noticeStatus != '0' && pageData.flowId) {
                return true
            }
            return false
        },
        // auditButtonShow  ({ pageData }) {
        //     const {flowId} = pageData
        //     // return (pageData.auditStatus !== '0')
        //     console.log('pagedata', pageData)
        //     return flowId ? true : false
        // },
        showFlow ({ Vue, pageConfig, btn, groupCode }) {
            this.flowId = pageConfig.groups[0].formModel.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_APUziNxOmAQLW_25fa4a00`, '当前审批策略不能查看流程！'))
                return
            }
            this.flowView = true
        },
        postAuditData (invokeUrl, fromSourceData) {
            let param = {}
            param['rootProcessInstanceId'] = fromSourceData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param)
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.init()
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    created () {
        let url = '/tender/purchaseTenderProjectHead/querySubpackageInfoBySubpackageId'
        getAction(url, { subpackageId: this.subId }).then((res) => {
            if (res.success) {
                this.signUp=res.result.signUp
                this.bidding=res.result.bidding
                this.tenderType=res.result.tenderType
                this.subpackageName=res.result.subpackageName
                this.checkType=res.result.checkType
            }
            this.init()
        })
    }
}
</script>
<style lang="less" scoped>
.ueditorDetail {
    margin-top: 10px;
    border: 1px solid #e8e8e8;
    padding: 6px;
}
:deep(.page-container .edit-page .page-content){
    flex: auto;
}
</style>
