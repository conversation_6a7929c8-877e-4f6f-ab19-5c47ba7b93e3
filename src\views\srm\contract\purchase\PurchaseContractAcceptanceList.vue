<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      :url="url"/>
    <!-- 编辑界面 -->
    <PurchaseContractAcceptanceEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>

    <!-- 详情页面 -->
    <PurchaseContractAcceptanceDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <record-modal
      v-model="recordShowVisible"
      :currentEditRow="currentEditRow"
    />
  </div>
</template>
<script>
import PurchaseContractAcceptanceEdit from './modules/PurchaseContractAcceptanceEdit'
import PurchaseContractAcceptanceDetail from './modules/PurchaseContractAcceptanceDetail'
import {ListMixin} from '@comp/template/list/ListMixin'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {getAction, postAction} from '@/api/manage'
import RecordModal from '@comp/recordModal'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseContractAcceptanceEdit,
        PurchaseContractAcceptanceDetail,
        RecordModal
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            recordShowVisible: false,
            pageData: {
                businessType: 'contractAcceptance',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNOlty_ceeee6d5`, '请输入验收单号')
                    }
                ],
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'contractAcceptance#purchaseContractAcceptance:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                form: {
                    keyWord: ''
                },
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'contractAcceptance#purchaseContractAcceptance:view'
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'),
                        clickFn: this.handleCopyData,
                        allow: this.allowCopy,
                        authorityCode: 'contractAcceptance#purchaseContractAcceptance:copy'
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.allowEdit,
                        authorityCode: 'contractAcceptance#purchaseContractAcceptance:edit'
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.allowDelete,
                        authorityCode: 'contractAcceptance#purchaseContractAcceptance:delete'
                    },
                    {
                        type: 'invalid',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                        clickFn: this.handleInvalid,
                        allow: this.allowInvalid,
                        authorityCode: 'contractAcceptance#purchaseContractAcceptance:cancellation'
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQL_1184c464`, '查看签署流程'),
                        clickFn: this.handleESignView,
                        allow: this.allowCheckSign,
                        authorityCode: 'contractAcceptance#contractAcceptance:viewEsignFlow'
                    },
                    {
                        type: 'download',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IKCPQI_49ebfaca`, '下载电签文件'),
                        clickFn: this.flowFileDownload,
                        allow: this.allowDownSign,
                        authorityCode: 'contractAcceptance#contractAcceptance:down'
                    },
                    {
                        type: 'chat',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'),
                        clickFn: this.handleChat,
                        allow: this.allowChat
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ]
            },
            tabsList: [],
            url: {
                list: '/contract/purchaseContractAcceptance/list',
                add: '/contract/purchaseContractAcceptance/add',
                copyData: '/contract/purchaseContractAcceptance/copyData',
                delete: '/contract/purchaseContractAcceptance/delete',
                invalid: '/contract/purchaseContractAcceptance/invalid',
                exportXlsUrl: '/contract/purchaseContractAcceptance/exportXls',
                getPurchaseAccount: '/enterprise/elsEnterpriseInfo/getPurchaseAccount',
                columns: 'purchaseContractAcceptanceList',
                flowFileDownload: '/esign/elsContractAcceptanceEsign/signFileDownload'
            }
        }
    },
    mounted () {
        this.serachCountTabs('/contract/purchaseContractAcceptance/counts')
        this.imJumpDetailFun()
    },
    methods: {
        flowFileDownload (row) {
            getAction(this.url.flowFileDownload, {id: row.relationId}).then(res => {
                if (res.success) {
                    window.open(res.result[0].fileUrl)
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        allowDownSign (row) {
            return row.signStatus !== '5'
        },
        allowCopy (row) {
            if (row.createAccount == this.$ls.get(USER_ELS_ACCOUNT)) {
                return false
            }
            return true
        },
        allowCheckSign (row) {
            if (row.signInitiate == '1') {
                return false
            }
            return true
        },
        handleCopyData (row) {
            let that = this
            getAction(this.url.copyData, {id: row.id})
                .then(res => {
                    if (res.success) {
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BRLR_29bbbf18`, '复制成功'))
                        that.$refs.listPage.handleQuery()
                    } else {
                        this.$message.error(res.message)
                    }
                })
        },
        handleESignView (row) {
            getAction('/esign/elsContractAcceptanceEsign/list', {relationId: row.relationId}).then((res) => {
                if (res.success) {
                    this.$router.push({
                        path: '/srm/esign/ContractAcceptanceEsignList',
                        query: {id: res.result.records[0].id, pageShow: 'true'}
                    })
                    this.visible = false
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        handleChat (row) {
            let {id} = row
            let recordNumber = row.promiseNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'PurchaseContractAcceptance', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if (row.promiseStatus != '0') {
                return false
            } else {
                return true
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_salesContractHeader`, '销售合同头'))
        },
        hideDetailPage () {
            this.showDetailPage = false
        },
        allowEdit (row) {
            let documentStatus = row.documentStatus
            if (documentStatus && documentStatus === '1' && row.createAccount == this.$ls.get(USER_ELS_ACCOUNT)) {
                return false
            } else {
                return true
            }
        },
        allowDelete (row) {
            let documentStatus = row.documentStatus
            if (documentStatus && documentStatus === '1' && row.createAccount == this.$ls.get(USER_ELS_ACCOUNT)) {
                return false
            } else {
                return true
            }
        },
        allowInvalid (row) {
            let documentStatus = row.documentStatus
            let costStatus = row.costStatus
            if ((costStatus == '0' || costStatus == null || costStatus == '' && (documentStatus == '1' || documentStatus == '2')) && row.createAccount == this.$ls.get(USER_ELS_ACCOUNT)) {
                return false
            } else {
                //置灰
                return true
            }
        },
        handleInvalid (row) {
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoid`, '确认作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voidSelectedData`, '是否作废选中数据?'),
                onOk: function () {
                    that.loading = true
                    postAction(that.url.invalid, row).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData() // 刷新页面
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        }
    }
}
</script>