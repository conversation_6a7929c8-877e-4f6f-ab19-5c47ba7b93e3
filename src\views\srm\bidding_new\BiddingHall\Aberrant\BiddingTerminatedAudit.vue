<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-header">
          <a-row>
            <a-col
              class="desc-col"
              :span="6">
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_details`, '详情') }}</span>
              <span
                v-if="subpackageTitle"
                style="position:absolute;margin-left:10px;"><a-tag
                  color="blue"
                  style="font-size:15px">{{ subpackageTitle }}</a-tag></span>
            </a-col>
            <a-col
              class="btn-col"
              :span="18">
              <taskBtn
                v-if="taskInfo.taskId"
                :currentEditRow="currentEditRow"
                :pageHeaderButtons="pageHeaderButtons"
                v-on="$listeners" />
            </a-col>
          </a-row>
        </div>
        <div class="page-content">
          <a-form-model
            ref="baseForm"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            :rules="rules"
            :model="formData">
            <a-row>
              <a-col :span="8">
                <a-form-model-item
                  :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_sRjW_3b3f3e3b`, '终止原因')}`"
                  prop="terminationReason">
                  <m-select
                    v-model="formData['terminationReason']"
                    :disabled="!isEdit"
                    :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                    dict-code="tenderTerminationReason" />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item
                  :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_KQUz_2fba950f`, '是否审批')}`"
                  prop="audit">
                  <m-select
                    v-model="formData['audit']"
                    :disabled="!isEdit"
                    :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                    dict-code="yn" />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_sRdRBI_78783087`, '终止相关附件')}`">
                  <div
                    v-for="(fileItem, index) in formData.purchaseAttachmentDTOList"
                    :key="fileItem.id">
                    <span style="color: blue; cursor: pointer; margin-right: 8px">{{ fileItem.fileName }}</span>
                    <span>
                      <a
                        style="margin: 0 8px"
                        @click="preViewEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                      <a @click="downloadEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
                    </span>
                  </div>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row class="textAreaClass">
              <a-form-model-item
                :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_sRjWtH_6879e520`, '终止原因记录')}`"
                prop="terminationReasonDetail">
                <a-textarea
                  v-model="formData['terminationReasonDetail']"
                  :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_field_sRjWtH_6879e520`, '终止原因记录')}`"
                  :disabled="!isEdit"
                  :auto-size="{ minRows: 3, maxRows: 5 }" />
              </a-form-model-item>
            </a-row>
          </a-form-model>
        </div>
      </a-spin>
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow" />
    </div>
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
import flowViewModal from '@comp/flowView/flowView'
import { mapGetters } from 'vuex'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    props: {
        currentEditRow: {
            default: () => {},
            type: Object
        }
    },
    components: {
        flowViewModal,
        taskBtn
    },
    data () {
        return {
            subpackageTitle: '',
            labelCol: { span: 9 },
            wrapperCol: { span: 15 },
            confirmLoading: false,
            formData: {},
            flowView: false,
            flowId: '',
            rules: {},
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    type: '',
                    click: this.showFlow,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'rollBack',
                    click: this.goBackAudit,
                    showCondition: () => {
                        return true
                    }
                }
            ],
            url: {
                queryById: '/tender/terminate/subpackageTerminateHead/queryById'
            }
        }
    },
    computed: {
        ...mapGetters(['taskInfo']),
        isEdit () {
            return false
        }
    },
    methods: {
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程'))
                return
            }
            this.flowView = true
        },
        /**
         * 审批方法
         */
        goBackAudit () {
            this.$parent.hideController()
        },
        queryDetail () {
            this.confirmLoading = true
            let params = {
                id: this.currentEditRow.id
            }
            getAction(this.url.queryById, params)
                .then((res) => {
                    console.log(res.result)
                    this.formData = res.result || {}
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        async downloadEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            this.$previewFile.open({ params: row })
        }
    },
    mounted () {
        this.subpackageTitle = this.currentEditRow.subject || ''
        this.queryDetail()
    }
}
</script>
<style lang="less" scoped>
.deliveryTime-title,
.clarification-title {
    padding: 8px;
    background: #f2f3f5;
}

:deep(.edit-page .textAreaClass .ant-form-item-control-wrapper){
    width: 87.5%;
}
:deep(.edit-page .textAreaClass .ant-form-item-label){
    width: 12.5%;
}
</style>
