<template>
  <div v-if="ifshow">
    <titleCrtl>
      {{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_vBtHQI_17441c3b`, '开标记录文件')) }}

      <template slot="right">
        <a-upload
          name="file"
          :multiple="true"
          :showUploadList="false"
          :action="uploadUrl"
          :headers="uploadHeader"
          :data="{ headId: formData.id || '', businessType: 'biddingPlatform', sourceNumber: formData.tenderProjectNumber || formData.id || '', actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开' }"
          :accept="accept"
          :beforeUpload="beforeUpload"
          @change="handleUploadChange"
        >
          <a-button
            v-if="formData.status != '1' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == 1"
            type="primary"
            @click="beforeUpload"> <a-icon type="upload" /> {{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件')) }}</a-button>
        </a-upload>
      </template>
    </titleCrtl>
    <list-table
      ref="tenderOpenBidRecordAttachmentList"
      groupCode="tenderOpenBidRecordAttachmentList"
      :statictableColumns="statictableColumns"
      :pageData="pageData"
      setGridHeight="500"
      :fromSourceData="formData.tenderOpenBidRecordAttachmentList"
      :showTablePage="false"> </list-table>
  </div>
</template>
<script lang="jsx">
import listTable from '../../components/listTable'
import titleCrtl from '../../components/title-crtl'
import { getAction } from '@/api/manage'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    mixins: [baseMixins],
    props: {
        // groupCode: {
        //     default: '',
        //     type: String
        // },
        formData: {
            default: () => {},
            type: Object
        }
        // pageStatus: {
        //     default: 'edit',
        //     type: String
        // }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    components: {
        listTable,
        titleCrtl
    },
    data () {
        return {
            statictableColumns: [
                { type: 'checkbox', width: 40, fixed: 'left' },
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                    groupCode: 'tenderOpenBidRecordAttachmentList',
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型')),
                    fieldLabelI18nKey: '',
                    field: 'fileType',
                    fieldType: 'select',
                    dictCode: 'tenderOfflineOpenBidFileType',
                    optionsFilterValue: [],
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'tenderOpenBidRecordAttachmentList',
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称')),
                    fieldLabelI18nKey: '',
                    field: 'fileName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    slots: {
                        default: ({ row, column }) => {
                            console.log(row)
                            return [
                                <div>
                                    <span style="color: blue">{row.fileName}</span>
                                    <a style="margin: 0 8px" onClick={() => this.preViewEvent(row)}>
                                        {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览')}
                                    </a>
                                </div>
                                // row['purchaseAttachmentDemandDTOList'].length> 0 ? <div ><span style='color: blue' onClick={() => this.preViewEvent(row)}>{row['purchaseAttachmentDemandDTOList'][0]['fileName']} </span><a-icon type="delete" onClick={() => row.purchaseAttachmentDemandDTOList = []}/> </div>: ''
                            ]
                        }
                    }
                },
                {
                    fixed: 'right',
                    groupCode: 'tenderOpenBidRecordAttachmentList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    field: 'title',
                    width: 100,
                    slots: { default: 'grid_opration' }
                }
            ],
            ifshow: false,
            tenderOpenBidRecordAttachmentList: [],
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            pageData: {
                optColumnList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        clickFn: this.handleDeleteFile,
                        allow: this.allowDelete
                    }
                ]
            }
        }
    },
    methods: {
        beforeUpload (e) {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
                e.stopPropagation()
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            console.log(file)
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if (this.formData.id) {
                this.$refs.tenderOpenBidRecordAttachmentList.loading = true
                if (file.status === 'done') {
                    if (file.response.success) {
                        let { fileName, filePath, fileSize, id, headId } = file.response.result
                        let fileListData = {
                            uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                            uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName,
                            filePath,
                            fileSize,
                            id,
                            headId
                        }
                        this.formData.tenderOpenBidRecordAttachmentList.push(fileListData)
                        this.$refs.tenderOpenBidRecordAttachmentList.loading = false
                        // 仅对表格数据进行操作，再在父组件获取子组件table表格数据进行保存发布
                        // this.$refs.tenderOpenBidRecordAttachmentList.insertAt(fileListData, -1)
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                    this.$refs.tenderOpenBidRecordAttachmentList.loading = false
                }
            }
        },
        handleDeleteFile (deleteItem) {
            console.log('1', this.formData.tenderOpenBidRecordAttachmentList, deleteItem)
            // 仅对表格数据进行操作，再在父组件获取子组件table表格数据进行保存发布
            // this.$refs.tenderOpenBidRecordAttachmentList.fromSourceData.splice(index, 1)
            let list = this.formData.tenderOpenBidRecordAttachmentList
            let targetIndex

            list.forEach((item, index) => {
                if (item.id == deleteItem.id) targetIndex = index
            })
            this.formData.tenderOpenBidRecordAttachmentList.splice(targetIndex, 1)
        },
        allowDelete () {
            return this.formData.status == '1'
        },  
        async downloadEvent (row) {
            let {message: url} = await getAttachmentUrl(row)
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.subpackageId()
            let params = {
                id: row.uid || row.attachmentId,
                subpackageId: row.subpackageId
            }
            this.$previewFile.open({ params: params })
            // this.$previewFile.open({ params: row })
        }
        // init (data) {
        //     this.formData = data
        //     this.tenderOpenBidRecordAttachmentList = data.tenderOpenBidRecordAttachmentList || []
        //     console.log(data, this.tenderOpenBidRecordAttachmentList)

        //     // if (['1', '2'].includes(data.status) || this.pageStatus == 'detail') this.externalToolBar = []
        // }
    },
    // watch: {
    //     fromSourceData: {
    //         immediate: true,
    //         handler (val) {
    //             // this.externalToolBar[0].args.headId = val? val.id : ''
    //         }
    //     }
    // },
    created () {
        // if (this.currentNode().extend.checkType != '0') {
        //     this.statictableColumns.forEach(item=>{
        //         if(item.field == 'fileType') {
        //             item.optionsFilterValue = ['0', '1', '2', '3', '6']
        //         } else {
        //             item.optionsFilterValue = ['4', '5']
        //         }
        //     })
        // }
        this.statictableColumns.forEach(item=>{
            if(item.field == 'fileType'){
                item.optionsFilterValue = this.currentNode().extend.checkType == '0' ? ['4', '5'] : ['0', '1', '2', '3', '6']
            }
        })
    },
    mounted () {
        console.log(this.currentNode().extend.checkType)

        // this.tenderOpenBidRecordAttachmentList = this.formData.tenderOpenBidRecordAttachmentList
        this.ifshow = true
        console.log('1', this.formData.id)
        // this.tenderOpenBidRecordAttachmentList = this.fromSourceData.tenderOpenBidRecordAttachmentList || []
        // if (['1', '2'].includes(this.fromSourceData.status) || this.pageStatus == 'detail') this.externalToolBar = []
    }
}
</script>
<style lang="less" scoped></style>
