<template>
  <div class="page-container" style="margin-right: 2px;">
    <a-spin :spinning="confirmLoading">
      <div
        class="page-main"
        id="printPage"
      >
        <div
          class="print-title"
          style="height: 50px; line-height: 50px; font-weight: normal; font-size: 32px; text-align: center; background-color: #fff; color: rgba(0, 0, 0, 0.85);"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_field_hutHB_d93177ee`, '询价记录表') }}
        </div>
        <!-- 表单 -->
        <div class="form">
          <a-descriptions
            size="small"
            :column="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }"
            bordered
          >
            <a-descriptions-item
              v-for="item in formData.custom.formFields"
              :key="item.fieldName"
              :label="item.fieldLabel"
            >
              {{ form[item.fieldName] }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <br />

        <!-- 表格 -->
        <div
          class="grid"
          v-for="grid in pageData.groups"
          :key="grid.groupCode"
        >
          <vxe-grid
            :ref="grid.custom.ref"
            v-bind="defaultGridOption"
            :scroll-y="{ enabled: false }"
            :columns="grid.custom.columns"
            :headerCellStyle="cellStyle"
            :cellStyle="cellStyle"
          >
            <template #grid_opration="{ row, column }">
              <a
                v-for="(item, i) in grid.custom.optColumnList"
                :key="'opt_' + row.id + '_' + i"
                :title="item.title"
                style="margin: 0 4px"
                :disabled="item.allow ? item.allow(row) : false"
                v-show="item.showCondition ? item.showCondition(row) : true"
                @click="item.clickFn(row, column)"
                >{{ item.title }}</a
              >
            </template>
          </vxe-grid>
          <br />
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'EnquiryPrint',

  data() {
    return {
      confirmLoading: false,
      form: {},
      cellStyle: ({ row, rowIndex, $rowIndex, column, columnIndex, $columnIndex }) => {
        return {
          'font-weight': 'normal !important',
          color: 'rgba(0, 0, 0, 0.85)'
        }
      },
      defaultGridOption: {
        border: true,
        resizable: true,
        autoResize: true,
        showOverflow: false,
        showHeaderOverflow: false,
        columnKey: true,
        size: 'mini',
        align: 'center',
        headerAlign: 'center',
        columns: [],
        data: []
      },
      formData: {
        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tvVH_298b46a0`, '基础信息'),
        groupCode: 'baseForm',
        custom: {
          formFields: [
            {
              fieldType: 'input',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enquiryNumber`, '询价单号'),
              fieldName: 'enquiryNumber'
            },
            {
              fieldType: 'input',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sender`, '发布人'),
              fieldName: 'publishUser'
            },
            {
              fieldType: 'input',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitTime`, '发布日期'),
              fieldName: 'publishTime'
            },
            {
              fieldType: 'input',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司代码'),
              fieldName: 'company_dictText'
            },
            {
              fieldType: 'input',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRVR_44661349`, '采购组织'),
              fieldName: 'purchaseOrg_dictText'
            },
            {
              fieldType: 'input',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enquiryScope`, '询价范围'),
              fieldName: 'enquiryScope_dictText'
            },
            {
              fieldType: 'input',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zECK_27774abd`, '分配方式'),
              fieldName: 'quotaWay_dictText'
            },
            //{
            //    fieldType: 'input',
            //    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yRIW_3cdd292b`, '节支率%'),
            //    fieldName: 'savingRate'
            //},
            // {
            //     fieldType: 'input',
            //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_publishNewSupplier`, '是否允许发布新供应商'),
            //     fieldName: 'publishNewSupplier_dictText'
            // },
            // {
            //     fieldType: 'input',
            //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_openBidBefore`, '是否可提前开标'),
            //     fieldName: 'openBidBefore_dictText'
            // },
            {
              fieldType: 'input',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_seePrice`, '报价截前是否可查看价格'),
              fieldName: 'seePrice_dictText'
            },
            {
              fieldType: 'switch',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierScope`, '供应商范围'),
              fieldName: 'supplierScope_dictText'
            },
            {
              fieldType: 'select',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXWR_8e14292c`, '供应商数量'),
              fieldName: 'supplierCounts'
            },
            // {
            //     fieldType: 'select',
            //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_budgetAmount`, '预算金额'),
            //     fieldName: 'projectBudget'
            // },
            // {
            //     fieldType: 'select',
            //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_economyAmount`, '节支金额'),
            //     fieldName: 'savingAmount'
            // },
            {
              fieldType: 'select',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enquiryDesc`, '询价描述'),
              fieldName: 'enquiryDesc'
            },
            {
              fieldType: 'select',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_huyRUzzEW_d49bd4e8`, '询价结果审批状态'),
              fieldName: 'resultAuditStatus_dictText'
            },
            {
              fieldType: 'select',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_huyRII_473f7450`, '询价结果意见'),
              fieldName: 'resultOpinion'
            },
            {
              fieldType: 'select',
              fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_huyRUzL_a09640c4`, '询价结果审批人'),
              fieldName: 'resultOpinionPeople'
            },
            {
              fieldType: 'input',
              fieldLabel: '报价截止时间',
              fieldName: 'quoteEndTime'
            }
          ]
        }
      },
      pageData: {
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseEnquiryItemList',
              columns: [
                //{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SLzAAo_fa81e5f0`, '物料分类编码'), field: 'cateCode', width: 45 },
                //{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationName`, '物料分类名称'), field: 'cateName', width: 70 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead9ab6_materialCode`, '物料编码'), field: 'materialNumber', width: 83 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialDesc`, '物料名称'), field: 'materialName', width: 95 },
                // { title: '行状态', field: 'itemStatus_dictText', width: 45 },
                { title: '主', field: 'quantityUnit_dictText', width: 30 },
                // { title: '辅', field: 'purchaseUnit_dictText', width: 30 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TVWR_46474721`, '需求数量'), field: 'requireQuantity', width: 70 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fIWWW_f03b2eb3`, '税率(%)'), field: 'taxRate', width: 70 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'), field: 'supplierName', width: 90 },
                { title: '含税单价', field: 'price', width: 85 },
                { title: '未税单价', field: 'netPrice', width: 85 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quota`, '配额'), field: 'quotaQuantity', width: 70 },
                { title: '价格生效日期', field: 'effectiveDate', width: 95 },
                { title: '价格失效日期', field: 'expiryDate', width: 95 },
                { title: '上期中标单价', field: 'lastTimePrice', width: 95 },
                { title: '定价时间', field: 'pricedTime', width: 83 }
              ]
            }
          }
        ]
      }
    }
  },
  methods: {
    back() {
      this.$emit('hide')
    },
    getData() {
      this.confirmLoading = true
      getAction('/enquiry/purchaseEnquiryHead/print', { id: this.$route.query.id })
        .then((res) => {
          if (res.success) {
            this.form = res.result

            // 过滤接受状态
            if (this.form.purchaseEnquiryItemList && this.form.purchaseEnquiryItemList != null) {
              this.form.purchaseEnquiryItemList = this.form.purchaseEnquiryItemList.filter(item => 
                item.itemStatus && item.itemStatus == '4'
              )
            }

            this.pageData.groups.forEach((group) => {
              if (group.type == 'grid') {
                let ref = group.custom.ref
                this.$refs[ref][0].loadData(res.result[ref])
              }
            })
            this.$nextTick(() => {
              let printPage = document.getElementById('printPage').innerHTML
              document.body.innerHTML = printPage
              setTimeout(() => {
                window.print()
              }, 2000)
            })
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    }
  },
  created() {
    this.getData()
  }
}
</script>
<style lang="less" scoped>
@primary-color: #1890ff;
.page-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 14px;
    background: #fff;
    border-bottom: 1px solid #e8eaec;
    .btnGroups {
      text-align: right;
      .ant-btn {
        & + .ant-btn {
          margin-left: 10px;
        }
      }
    }
  }
  .page-main {
    position: relative;
  }
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
  background-color: #f8feff;
}
:deep(.ant-descriptions-item-content) {
  width: 16.66%;
  max-width: 16.66%;
  background-color: #fff;
}
:deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title),
:deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description) {
  color: @primary-color;
}
:deep(.rich-editor-display-box) p {
  margin-bottom: 0px;
}

:deep(.vTable-table .vxe-table .vxe-table--header .vxe-header--row .vxe-header--column .vxe-cell--title) {
  white-space: pre-wrap;
  word-wrap: break-word;
  border: 5px solid black !important;
  font-weight: normal !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

:deep(.ant-descriptions-item-content) {
  color: rgba(0, 0, 0, 0.85) !important;
  font-weight: normal !important;
}

.item-content {    
  padding: 8px 16px;
  min-height:30px;
  line-height:16px;
}

:deep(.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-label){
  width: 14.66%;
}
:deep(.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-label),
:deep( .ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-content) {
    padding: 0 16px !important;
}
</style>