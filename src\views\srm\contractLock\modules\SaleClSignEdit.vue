<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :confirmLoading = "confirmLoading"
      :url="url" />
    <!-- 加载配置文件 -->
    <field-select-modal 
      ref="fieldSelectModal" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction,postAction } from '@/api/manage'
export default {
    name: 'EsignFlowAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            confirmLoading: false,
            selectType: 'esignFlow',
            visible: false,
            signerBtn: false,
            form: {
                keyword: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: '业务编号',
                                    fieldName: 'busNumber',
                                    disabled: true
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: '采方ELS账号',
                                    fieldName: 'elsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: '采方公司名称',
                                    fieldName: 'purchaseName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'subject',
                                    disabled: true
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjXKI_ef65ec11`, '签署有效时间'),
                                    fieldName: 'expireTime',
                                    dataFormat: 'YYYY-MM-DD',
                                    disabled: true
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWsRKI_fa482f8c`, '签署终止时间'),
                                    fieldName: 'endTime',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    disabled: true

                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: '是否回传签署文档',
                                    fieldName: 'returnSignedFile',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: '是否被驳回',
                                    fieldName: 'reject',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: '驳回原因',
                                    fieldName: 'rejectReason',
                                    disabled: true
                                }
                            ]
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'uploaded', title: '是否上传契约锁', width: 180, visible: false },
                            { field: 'uploaded_dictText', title: '是否上传契约锁', width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 220, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载'), clickFn: this.downloadEvent },
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_by6FtppjfaHaxK4d`, '签署文件下载'), clickFn: this.flowFileDownload, allow: this.allowDownload },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } },
                    { groupName: '回传文件', groupCode: 'saleAttachmentList', type: 'grid', custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_upload`, '上传文件'), type: 'upload', businessType: 'contractLock', callBack: this.uploadCallBack},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFile}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '下载'), clickFn: this.downloadEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), type: 'primary', click: this.sendBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/contractLock/elsClContract/queryById',
                upload: '/attachment/purchaseAttachment/upload',
                uploadLogUrl: '/attachment/purchaseAttachment/uploadLog',
                download: '/attachment/purchaseAttachment/download'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        flowFileDownload (row){
            let str = row.signFilePath
            if(str){
                let fileName = row.fileName
                let arr =  fileName.split('.')
                fileName = arr[0] + '.pdf'
                getAction(str, {}, {
                    responseType: 'blob'
                }).then(res => {
                    let url = window.URL.createObjectURL(new Blob([res]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', fileName)
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                })
            }else {
                let fileName = row.fileName
                let arr =  fileName.split('.')
                fileName = arr[0] + '.pdf'
                console.log(arr)
                getAction('/contractLock/elsClContract/downSignFileByDocumentId', {
                    'fileName': fileName,
                    'id': this.currentEditRow.id,
                    'documentId': row.fileId
                }, {
                    responseType: 'blob'
                }).then(res => {
                    let url = window.URL.createObjectURL(new Blob([res]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', fileName)
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                })
            }
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        allowDownload (){
            if(this.currentEditRow.contractStatus == 'COMPLETE'){
                return false
            }
            if(row.signFilePath){
                return false
            }
            return true
        },
        uploadCallBack (result) {
            let itemGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            itemGrid.insertAt(result, -1)
        },
        deleteFile (){
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/esign/saleSignReturnAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        esignFIledownloadEvent (row){
            this.downloadFile(row, this.url.esignFIledownload, row.uploadFileName)
        },
        downloadFile (data, url, fileName){
            let id = data.id
            const params = {
                id
            }
            getAction(url, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        sendBtn (){
            this.$refs.editPage.confirmLoading = true
            const form = this.$refs.editPage.getPageData()
            if(!form.saleAttachmentList || form.saleAttachmentList.length<1){
                this.$message.warning('回传文件不能为空')
                this.confirmLoading = false
                return
            }
            postAction('/contractLock/elsClContract/saleSend', form).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                this.$refs.editPage.confirmLoading = false
                if(type){
                    this.goBack()
                }
            })
        }
    }
}
</script>