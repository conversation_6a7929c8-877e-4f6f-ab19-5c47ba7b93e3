<template>
  <div>
    <a-modal
      v-drag
      v-model="visible"
      :title="title"
      :confirmLoading="loading"
      width="46%"
      @ok="confirm">
      <a-spin :spinning="loading">
        <a-textarea
          style="width:100%"
          :auto-size="{ minRows: 3, maxRows: 5 }"
          v-model="awardOpinion" />
        <div
          class="itemBox"
          style="margin-top:16px;">
          <div class="title dark">
            <div class="supplier_list_upload">
              <custom-upload
                :title="$srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件')"
                :visible.sync="modalVisible"
                :itemInfo="itemInfo"
                :action="uploadUrl"
                :accept="accept"
                :dictCode="dictCode"
                :multiple="true"
                :headers="tokenHeader"
                :data="uploadData"
                :itemNumbeValueProp="'itemNumber'"
                itemNumberSelectIndex
                @change="(info) => handleUploadChange(info)">
              </custom-upload>
            </div>
          </div>
          <div class="table">
            <vxe-grid
              ref="submitAttachmentList"
              v-bind="defaultGridOption"
              :columns="accessoryListColumns"
              :data="submitAttachmentList">
              <template slot="empty">
                <a-empty />
              </template>
              <template #grid_opration="{ row, column }">
                <a
                  v-for="(item, i) in optColumnList"
                  :key="i"
                  :title="item.title"
                  v-show="item.key == 'download' || (item.key =='delete' && row.sendStatus == 0)"
                  style="margin:0 4px"
                  @click="item.clickFn(row, column, 'purchaseBiddingSpecialistList')" >{{ item.title }}</a>
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import uploadList from '@comp/uploadList/uploadList'
import CustomUpload from '@comp/template/CustomUpload'
export default {
    name: 'SubmitPriced',
    components: { uploadList, CustomUpload },
    data () {
        return {
            loading: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_lBII_2ed15c51`, '授标意见'),
            awardOpinion: null,
            visible: false,
            headId: undefined,
            getsubmitAttachmentList: [],
            itemInfo: [],
            purchaseEnquiryItemList: null,
            modalVisible: false,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                columns: [],
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'dblclick', mode: 'cell' }
            },
            optColumnList: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), key: 'delete', clickFn: this.deleteItemEvent },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), key: 'download', clickFn: this.downloadFile }
            ],
            uploadUrl: '/attachment/purchaseAttachment/upload',
            tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            dictCode: 'srmFileType',
            accessoryListColumns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), field: 'fileType_dictText', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), field: 'fileName', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), field: 'uploadTime', width: 150 },
                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                {
                    field: 'itemNumber',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                    width: 120
                },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
            ],
            submitAttachmentList: []
        }
    },
    computed: {
        uploadData () {
            return { businessType: 'enquiry', headId: this.headId }
        }
    },
    methods: {
        // 文件下载
        handleDownload ({ id, fileName }) {
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        downloadFile (row) {
            this.handleDownload(row)
        },
        deleteItemEvent (row) {
            let params = { id: row.id }
            getAction('/attachment/purchaseAttachment/delete', params).then(res => {
                if (res.success) {
                    const index = this.submitAttachmentList.findIndex(i => i.id === row.id)
                    this.submitAttachmentList.splice(index, 1)
                } else this.$message.error(res.message)
            })
        },
        // 批量删除
        deleteBatch () {
            const fileGrid = this.$refs.submitAttachmentList
            const checkboxRecords = fileGrid.getTableData().tableData
            let a=checkboxRecords.filter(item=>{
                return  this.getsubmitAttachmentList.some(item2=>{return item.id==item2.id})?false:true
            })
            const ids = a.map((n) => n.id).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then((res) => {
                if (res.success){
                    this.submitAttachmentList=this.getsubmitAttachmentList
                    this.visible = false
                }
            })
        },
        //附件上传
        handleUploadChange (info) {
            this.submitAttachmentList = [...this.submitAttachmentList, ...info]
        },
        open (headId, row, type) {
            this.headId = headId

            const index = this.submitAttachmentList.findIndex(i => i.headId === this.headId)
            if (index === -1) this.submitAttachmentList = []
            if (type === 'row') {
                const nums = row.map(i => { return i.itemNumber })
                this.submitAttachmentList = this.submitAttachmentList.filter(i => (i.itemNumber && nums.includes(i.itemNumber)) || !i.itemNumber)
            }
            this.getsubmitAttachmentList=JSON.parse(JSON.stringify(this.submitAttachmentList))
            this.purchaseEnquiryItemList = []
            if(row !== null && type === 'row'){
                this.purchaseEnquiryItemList = row
                this.itemInfo = this.purchaseEnquiryItemList
            } else if (row.length > 0 && type !== 'row') {
                this.purchaseEnquiryItemList = row
                this.itemInfo = row
            }
            this.visible = true
        },
        confirm (){
            if(!this.awardOpinion || this.awardOpinion == '') {
                this.$message.warning('请填写授标意见')
                return
            }

            let param = {}
            param['headId'] = this.headId
            param['awardOpinion'] = this.awardOpinion
            param['purchaseEnquiryItemList'] = this.purchaseEnquiryItemList
            this.loading = true
            postAction('/enquiry/purchaseEnquiryHead/submitPriced', param).then(res => {
                if(res.success === true){
                    this.$message.success(res.message)
                    this.$emit('success')
                    this.$parent.refresh()
                }else{
                    this.deleteBatch()

                    if(!!res.message && res.message.indexOf("\n") >= 0) {
                        const h = this.$createElement;
                        let strList = res.message.split("\n");
                        strList = strList.map((str, strIndex) => {
                            return h(strIndex === 0? 'span' : 'div', strIndex === 0? null : { style: 'margin-left: 24px' } ,str);
                        })
                        this.$message.warning(h('span', { style: 'text-align: left' }, strList))
                        return;
                    }
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.loading = false
                this.visible = false
            })
        }
    }
}
</script>