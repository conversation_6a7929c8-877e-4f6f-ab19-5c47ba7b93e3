<template>
  <div class="control-wrap">
    <a-button> <a-icon
      :type="data.icon"
    />{{ data.name }}</a-button>
  </div>
</template>

<script>
export default {
    name: 'Control',
    props: {
        data: {
            type: Object,
            required: true
        }
    }
}
</script>

<style lang="less" scoped>
  .control-wrap {
    display: inline-block;
    margin-bottom: 8px;
    margin-right: 8px;
    :deep(.ant-btn){
      cursor: move;
    }
  }
  .control-wrap:last-child{
    margin-right: 0px;
  }
</style>
