<template>
  <div>
    <a-modal
      v-drag    
      v-model="showNode"
      width="800px"
      @cancel="close"
      @ok="confirmChangeNode"
      :title="`${$srmI18n(`${$getLangAccount()}#i18n_alert_Sy_e84b3`, '环节')}`">
      <div>
        <a-form-model
          ref="form"
          :model="formData"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          :rules="rules">
          <a-row :getterr="12">
            <a-col :span="12">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_SyRLW_9acfa264`, '环节名称：')"
                prop="groupName"
                required>
                <a-input
                  :disabled="isView"
                  v-model="formData.groupName"></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_SyAq_368f8903`, '环节类别')"
                prop="groupType"
                required>
                <m-select
                  v-model="formData.groupType"
                  :disabled="isView"
                  :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                  @change="tenderItemTypeChange"
                  dict-code="tenderItemType" />
              </a-form-model-item>
            </a-col>
            <a-col
              :span="12"
              v-if="['1', '2', '3'].includes(formData.groupType)">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_zR_a3ef6`, '分值')"
                prop="score"
                required>
                <a-input-number
                  :disabled="isView"
                  v-model="formData.score"
                  style="width: 100%"></a-input-number>
              </a-form-model-item>
            </a-col>
            <a-col
              :span="12"
              v-if="['1', '2', '3'].includes(formData.groupType)">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_bsWWW_f52a6d62`, '权重（%）')"
                prop="weights"
                required>
                <a-input-number
                  :disabled="isView"
                  v-model="formData.weights"
                  style="width: 100%"></a-input-number>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_AT_c6d3d`, '排序')"
                prop="orderBy"
                required>
                <a-input-number
                  :disabled="isView"
                  v-model="formData.orderBy"
                  style="width: 100%"></a-input-number>
              </a-form-model-item>
            </a-col>
            <!-- <a-col :span="12" >
              <a-form-model-item
                label="评审阶段"
                v-if="showCurrentStep"
                prop="currentStep">
                <m-select
                  v-model="formData.currentStep"
                  :disabled="isView"
                  :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                  dict-code="resultTenderEvaluationPeriod" />
              </a-form-model-item>
            </a-col> -->
            <a-col :span="12">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_UUsuAq_b0311230`, '评审专家类别')"
                prop="expertCategory">
                <m-select
                  v-model="formData.expertCategory"
                  :disabled="isView"
                  mode="multiple"
                  :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                  dict-code="srmSpecialistClasses" />
              </a-form-model-item>
            </a-col>
            <!-- <a-col
              :span="12"
              v-if="['1', '2'].includes(formData.groupType)">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_KQpB_2fbbee67`, '是否暗标')"
                prop="closedBid"
                required>
                <m-select
                  v-model="formData.closedBid"
                  :disabled="isView"
                  :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                  dict-code="yn" />
              </a-form-model-item>
            </a-col> -->
            <a-col
              :span="12"
              v-if="['1'].includes(formData.groupType)">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_KQLQKd_25269fa9`, '是否为否决项')"
                prop="veto"
                required>
                <m-select
                  v-model="formData.veto"
                  :disabled="isView"
                  :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                  dict-code="yn" />
              </a-form-model-item>
            </a-col>
            <a-col
              :span="12"
              v-if="['1'].includes(formData.veto) && ['1'].includes(formData.groupType)">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_MkszxVWKQK_62030894`, '汇总得分不足X时否决')"
                prop="vetoCondition"
                required>
                <a-input-number
                  :disabled="isView"
                  v-model="formData.vetoCondition"
                  style="width: 100%"></a-input-number>
              </a-form-model-item>
            </a-col>
            <a-col
              :span="12"
              v-if="['2'].includes(formData.groupType)">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_xBnK_2553b767`, '不符合时')"
                required
                prop="reviewGradingType">
                <a-radio-group
                  :disabled="isView"
                  v-model="formData.reviewGradingType">
                  <a-radio :value="1">{{ $srmI18n(`${$getLangAccount()}#i18n_field_QKeB_277f7b1f`, '否决投标') }}</a-radio>
                  <a-radio :value="0">{{ $srmI18n(`${$getLangAccount()}#i18n_field_jWz_1672dad`, '得0分') }}</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col
              :span="12"
              v-if="['2'].includes(formData.groupType)">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_xBnTIWTvmWW_d922fe15`, '不符合条件（条例个数）')"
                required
                prop="vetoCondition">
                <a-input-number
                  :disabled="isView"
                  v-model="formData.vetoCondition"
                  style="width: 100%"></a-input-number>
              </a-form-model-item>
            </a-col>
            <a-col
              :span="12"
              v-if="['0'].includes(formData.groupType)">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_xBnKQKAeBWmWW_ecce40cf`, '不符合时否决其投标（个数）')"
                prop="vetoCondition2">
                <a-input-number
                  v-model="formData.vetoCondition2"
                  :disabled="isView">
                </a-input-number>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { valitNumberLength } from '@views/srm/bidding_new/utils/index'
export default {
    props: {
        currentNodeItme: {
            default: () => {
                return {}
            },
            type: Object
        },
        tenderEvaluationTemplateItemVoList: {
            type: Array,
            default: () => []
        }
    },
    inject: {
        currentSubPackage: {
            from: 'currentSubPackage',
            default: null
        },
        currentNode: {
            from: 'currentNode',
            default: null
        }
    },
    data () {
        let checkNumber = (rule, value, callback) => {
            if (value) {
                if (!valitNumberLength(value, 8)){
                    callback(new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '8'))
                }
            }
            callback()
        }
        return {
            showNode: false,
            labelCol: { span: 12 },
            wrapperCol: { span: 12 },
            formData: {},
            pageType: 'edit',
            rules: {
                groupName: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNSyRL_bdb28d5f`, '请输入环节名称') },
                    { max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')}
                ],
                groupType: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFSyAq_7de6d3ba`, '请选择环节类别') }],
                score: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNzR_f6db671f`, '请输入分值') },
                    { validator: checkNumber, trigger: 'change' }
                ],
                weights: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNbs_f6de3b13`, '请输入权重') },
                    { validator: checkNumber, trigger: 'change' }
                ],
                orderBy: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNAT_f6dd9566`, '请输入排序') },
                    { validator: checkNumber, trigger: 'change' }
                ],
                // currentStep: [
                //     { required: true, message: '请选择评审阶段' }
                // ],
                // expertCategory: [
                //     { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFUUsuAq_7ee08b27`, '请选择评审专家类别') }
                // ],
                // closedBid: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFKQpB_7713391e`, '请选择是否暗标') }],
                veto: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNKQLQKd_81658b52`, '请输入是否为否决项') }],
                vetoCondition: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNxBnjQKTI_b54761a0`, '请输入不符合的否决条件') },
                    { validator: checkNumber, trigger: 'change' }
                ],
                reviewGradingType: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViF_21f3e37`, '请选择') }],
                vetoCondition2: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNSyCUUTvWmW_fb5c7c17`, '请输入环节内评审条例（个）') },
                    { validator: checkNumber, trigger: 'change' }
                ]
            }
        }
    },
    computed: {
        isView () {
            return this.pageType == 'detail'
        }
        // showCurrentStep() {
        //     let {extend:{checkType = ''} = {}} = this.currentNode ? this.currentNode() : {}
        //     let {processType} = this.currentSubPackage ? this.currentSubPackage() : {}
        //     return checkType == '1' && processType == '1'
        // }
    },
    methods: {
        close (){
            this.$refs.form.resetFields()
            this.showNode = false
        },
        open (data, pageType) {
            this.pageType = pageType
            this.formData = Object.assign({}, data)
            this.numberToString(this.formData, ['closedBid', 'veto'])
            if (this.formData.vetoCondition) this.formData.vetoCondition2 = this.formData.vetoCondition
            if(!this.formData.orderBy){
                for(let i = 0; i < this.tenderEvaluationTemplateItemVoList.length+1; i++) {
                    this.formData.orderBy=i+1
                } 
            }
            this.showNode = true
        },
        numberToString (data, code = []) {
            code.map(k => {
                data[k] = data[k] ? data[k] + '' : ''
            })
        },
        confirmChangeNode () {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    let params = Object.assign({}, this.formData)
                    if (['0'].includes(this.formData.groupType)) params.vetoCondition = params.vetoCondition2
                    this.$emit('confirmChangeNode', params)
                    this.showNode = false
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },
        tenderItemTypeChange (value) {
            let params = {
                veto: '',
                score: '',
                weights: '',
                closedBid: '',
                vetoCondition: '',
                groupType: value,
                groupName: this.formData.groupName || '',
                orderBy: this.formData.orderBy || '',
                reviewGradingType: ''
            }
            this.$set(this, 'formData', params)
            if (value == '1') {
                this.formData.veto = '0'
            } else if (value == '0') {
                this.formData.veto = '1'
            }
        }
    }
}
</script>
