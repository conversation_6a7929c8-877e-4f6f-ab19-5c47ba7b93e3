<template>
  <a-modal
    v-drag
    :maskClosable="false"
    :keyboard="false"
    v-model="visible"
    :width="width"
    :height="height"
    @cancel="close"
    :footer="null"
    :z-index="1999"
    :class="{'full-screen': isFullScreen}"
    class="preview-frame"
  >
    <template slot="title">
      <div class="ant-modal-title">
        <a-row
          type="flex">
          <a-col flex="auto">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a-col>
          <a-col
            flex="30px"
            style="cursor: pointer;;"
          >
            <a-tooltip
              v-if="!videoSrc"
              title="新窗口打开"
              placement="bottom">
              <a
                style="color: rgba(0,0,0,0.85); margin-right: 8px;"
                :href="iframeUrl"
                target="_blank"><a-icon
                  type="snippets" />
              </a>              
            </a-tooltip>
          </a-col>
          <a-col
            flex="40px"
            style="cursor: pointer;"
            @click="fullScreen"
          >            
            <a-tooltip
              v-if="!isFullScreen"
              title="全屏"
              placement="bottom">
              <a-icon
                type="fullscreen"
              />
            </a-tooltip>
            <a-tooltip
              v-if="isFullScreen"
              title="还原"
              placement="bottom">
              <a-icon
                type="fullscreen-exit"/>
            </a-tooltip>
          </a-col>
        </a-row>
          
      </div>
    </template>
    <video
      v-if="videoSrc"
      class="video"
      ref="videoRef"
      :src="videoSrc"
      controls="controls">
      do not support video
    </video>
    <iframe
      v-else
      :src="iframeUrl" 
      id="previewDialogFrame"
      style="width:100%; height:100%;"
      frameborder="0"></iframe>
  </a-modal>
</template>
<script>
import {postAction} from '@/api/manage'
import { Base64 } from 'js-base64'
import { getAction } from '../../../api/manage'
export default {
    name: 'Preview',
    props: {
  
    },
  
    data () {
        return {
            width: '90%',
            height: '700px',
            isFullScreen: false,
            visible: false,
            url: '/attachment/purchaseAttachment/getSignature',
            iframeUrl: '',
            params: {},
            videoSrc: ''
        }
    },
  
    mounted () {
          
    },
    computed: {
    },
  
    methods: {
        close () {
            if(this.videoSrc) {
                this.$refs.videoRef.pause()
            } 
            this.isFullScreen = false
        },
        open (data) {
            this.visible = true
        },
        getUrl (params, path) {
            this.iframeUrl = ''
            this.videoSrc = ''
            if (path) {
                this.visible = true
                this.iframeUrl =process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(path))}&reDownload=true`
                return
            }
            //兼容招投标平台的附件预览功能
            let newUrl
            if(params.subpackageId){
                newUrl = '/tender/common/download/getSignature'
                getAction(newUrl, params).then(res => {
                    if(res.success) {
                        this.visible = true
                        var url = res.message//要预览文件的访问地址
                        this.iframeUrl =process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}&reDownload=true`
                    }
                }).finally(() => {
              
                })
            }else{
                newUrl = this.url
                postAction(newUrl, params).then(res => {
                    if(res.success) {
                        this.visible = true
                        var url = res.message//要预览文件的访问地址
                        this.iframeUrl =process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}&reDownload=true`
                    }
                }).finally(() => {
                })
            }
            
        },
        // 全屏/还原
        fullScreen () {
            this.isFullScreen = !this.isFullScreen     
        }
    }
}
</script>
  
  <style lang="less" scoped>
  .preview-frame{
    :deep(.ant-modal-close-x){
      width: 37px;
    }
  }
  .video {
      width: 100%;
  }
  :deep(.ant-modal-content),
  :deep(.ant-modal-body) {
      height: 100%;
  }
  .full-screen {
      :deep(.ant-modal) {
          width: 100vw !important;
          height: 100vh !important;
          position: fixed !important;
          left: 0 !important;
          top: 0 !important;
          z-index: 20000 !important;
      }
  }
  
  </style>