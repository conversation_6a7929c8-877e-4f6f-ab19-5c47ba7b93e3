<template>
  <div style="height: 100%">
    <list-layout
      :tabsList="tabsList"
      ref="listPage"
      v-show="!showEditPage && !showDetailPage "
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <Tender-Evaluation-Template-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <Tender-Evaluation-Template-Detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import TenderEvaluationTemplateEdit from './modules/TenderEvaluationTemplateEdit'
import TenderEvaluationTemplateDetail from './modules/TenderEvaluationTemplateDetail'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
import { postAction, getAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        TenderEvaluationTemplateEdit,
        TenderEvaluationTemplateDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'biddingPlatform',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNIrAyWRL_12c9f76a`, '请输入模板编号/名称')
                    }
                    // {
                    //     type: 'select',
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_UBrh_41199dfa`, '评标办法'),
                    //     fieldName: 'evaluationMethod',
                    //     dictCode: 'tenderEvaluationMethod'
                    // }
                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    {
                        allow: ()=> {
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'tender#tenderEvaluationTemplateHead:add',
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), authorityCode: 'tender#tenderProject:copy', clickFn: this.handleCp },
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'tender#tenderEvaluationTemplateHead:queryById', clickFn: this.handleView },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'tender#tenderEvaluationTemplateHead:edit', clickFn: this.handleEdit, allow: this.allowEdit },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'tender#tenderEvaluationTemplateHead:delete', clickFn: this.handleDelete, allow: this.allowEdit }
                    // { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 270
            },
            url: {
                list: '/tender/tenderEvaluationTemplateHead/list',
                delete: '/tender/tenderEvaluationTemplateHead/delete',
                columns: 'tenderEvaluationTemplateHead'
            }
        }
    },
    methods: {
        allowEdit (row){
            return row.status == '1'
        },
        handleCp (row) {
            let param = {id: row.id}
            getAction('/tender/tenderEvaluationTemplateHead/copy', param).then(res=>{
                if(res.success){
                    this.$message.success(res.message)
                    this.$refs.listPage.loadData()
                }else{
                    this.$message.error(res.message)
                }
            })
        },  
        handleAdd (){
            this.currentEditRow = {
                elsAccount: this.$ls.get(USER_ELS_ACCOUNT)
            }
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleEdit (row) {
            this.currentEditRow = {
                elsAccount: this.$ls.get(USER_ELS_ACCOUNT),
                ...row
            }
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleView (row) {
            this.currentEditRow = {
                elsAccount: this.$ls.get(USER_ELS_ACCOUNT),
                ...row
            }
            this.showDetailPage = true
        }
    },
    mounted () {
        // this.serachTabs('srmReconciliationStatus', 'reconciliationStatus')
        this.serachCountTabs('/tender/tenderEvaluationTemplateHead/counts')
    }
}
</script>