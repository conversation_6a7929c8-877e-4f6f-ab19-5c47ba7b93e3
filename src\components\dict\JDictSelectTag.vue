<template>
  <a-radio-group
    v-if="tagType === 'radio'"
    :value="realValue"
    :disabled="disabled"
    @change="handleInput"
  >
    <a-radio
      v-for="(item, key) in dictOptions"
      :key="key"
      :value="item.value"
    >
      {{ item.label }}
    </a-radio>
  </a-radio-group>

  <a-select
    v-else-if="tagType === 'select'"
    :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_ViF_21f3e37`, '请选择')"
    :value="realValue"
    :disabled="disabled"
    :mode="mode"
    showSearch
    allowClear
    optionFilterProp="children"
    @change="handleInput"
  >
    <a-select-option
      v-for="(item, key) in dictOptions"
      :key="key"
      :value="item.value"
    >
      {{ item.label }}
    </a-select-option>
  </a-select>
</template>

<script>
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
    name: 'JDictSelectTag',
    props: {
        dictCode: {
            type: String,
            default: ''
        },
        mode: {
            type: String,
            default: 'default'
        },
        placeholder: {
            type: String,
            default: ''
        },
        triggerChange: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        },
        value: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        }
    },
    data () {
        return {
            dictOptions: [],
            tagType: ''
        }
    },
    computed: {
        realValue () {
            if (this.mode === 'multiple') {
                return this.value ? this.value.split(',') : []
            }
            return this.value
        }
    },
    watch: {
        dictCode: {
            immediate: true,
            handler () {
                this.initDictData()
            }
        }
    },
    created () {
        if (!this.type || this.type === 'list') {
            this.tagType = 'select'
        } else {
            this.tagType = this.type
        }
    },
    methods: {
        initDictData () {
            //根据字典Code, 初始化字典数组
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: this.dictCode
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    const result = res.result || []
                    this.dictOptions = result.map(n => ({
                        ...n, label: n.title
                    }))
                }
            })
        },
        handleInput (e) {
            let val
            if (this.tagType === 'radio') {
                val = e.target.value
            } else if (this.mode === 'multiple') {
                val = e.join(',')
            } else {
                val = e
            }
            if (this.triggerChange) {
                var option = this.getOptionByValue(val)
                option.dictName = this.name
                this.$emit('change', val, option)
                this.$emit('input', val)
            } else {
                this.$emit('input', val)
            }
        },
        getOptionByValue (val) {
            var opt = {}
            this.dictOptions.forEach(item => {
                if (item.value == val) {
                    opt = item
                }
            })
            return opt
        }
    }
}
</script>