<template>
  <div
    style="height: 650px"
    v-if="this.subId && ifshow">
    <titleTrtl v-if="!showDetailList">
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBLRt_7e1f3f68`, '中标人名单') }}</span>
    </titleTrtl>
    <listTable 
      ref="listTable"
      v-show="!showDetailList"
      :pageData="pageData"
      :url="url"
      :statictableColumns="tableColumns"
      :showTablePage="true"
    />
    <BidServiceDetail
      v-if="showDetailList"
      ref="supplierCapacity"
      :projectMemberPermission="projectMemberPermission"
      :current-edit-row="currentEditRow"
      @hide="hidePage"
    />
  </div>
</template>
<script lang='jsx'>
// import { ListMixin } from '@comp/template/list/ListMixin'
import listTable from '../components/listTable'
import BidServiceDetail from './modules/BidServiceDetail'
import { postAction, getAction } from '@/api/manage'
import titleTrtl from '../components/title-crtl'

export default {
    // mixins: [ListMixin],
    components: {
        BidServiceDetail,
        listTable,
        titleTrtl
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    data () {
        return {
            ifshow: false,
            showDetailList: false,
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    { type: 'manage', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JpRv_3bbdcec4`, '缴纳管理'), clickFn: this.handleManage, allow: this.allowManage}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            tableColumns: [
                {
                    'type': 'seq',
                    // 'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                    // 'width': 250
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    'field': 'rank'
                    // 'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHf_252229e6`, '中标金额'),
                    'field': 'bidWinningAmount'
                    // 'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BSulMCK_470609db`, '服务费收取方式'),
                    'field': 'paymentType',
                    'fieldType': 'select',
                    'dictCode': 'tenderBidWinningServiceFeeHeadPaymentType',
                    'enabled': false
                    // 'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BSuHf_d3e01a31`, '服务费金额'),
                    'field': 'dueAmount'
                    // 'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BSuJpzE_5379424f`, '服务费缴纳状态'),
                    'field': 'status',
                    'fieldType': 'select',
                    'dictCode': 'tenderMarginHeadStatus',
                    'enabled': false
                    // 'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IJpHf_679cb81d`, '已缴纳金额'),
                    'field': 'paidAmount'
                    // 'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    // width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            url: {
                // 列表数据展示
                list: '/tender/sale/purchaseTenderBidWinningServiceFeeHead/list?subpackageId='+this.subpackageId()
            }
        }
    },
    methods: {
        // 返回按钮，隐藏页面，返回list列表
        hidePage (){
            this.showDetailList = false
            this.$refs.listTable.loadData()
        },
        async handleManage (row){
            await this.checkProjectMemberPermission(row.tenderProjectId)
            this.currentEditRow = row
            this.showDetailList = true
        },
        allowManage () {
            return this.$ls.get('SET_TENDERCURRENTROW').applyRole != '1'
        },
        //校验是否为项目成员 负责人或采购执行人
        async checkProjectMemberPermission (tenderProjectId){
            let url ='/tender/project/purchaseTenderProjectMember/checkProjectMemberPermission'
            console.log(this.tenderCurrentRow)
            await getAction(url, {projectId: tenderProjectId}).then(res=>{
                if (res.result) {
                    this.projectMemberPermission = res.result
                }
            }).finally(()=>{

            })
        }
    },
    
    async mounted () {
        let url = '/tender/tenderProjectAttachmentInfo/queryBidLetterFormatGroup'
        let subpackageId = this.subId
        let checkType = 1
        await getAction(url, {subpackageId, checkType}).then(res => {
            if(res.success) {
                let {quoteType} = res.result
                if(quoteType == '1'){
                    this.tableColumns = this.tableColumns.filter(item=>{
                        return item.field != 'rank'
                    })
                }
            }
        }).finally(()=>{
            this.ifshow = true
        })
    }
    
}
</script>
<style lang="less" scoped>
</style>