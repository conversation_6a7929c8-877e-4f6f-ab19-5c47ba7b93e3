<template>
    

  <div>
    <div
      
      style="width: 100%;height:calc(100vh - 300px);">
      <SeeksRelationGraph
        ref="seeksRelationGraph"
        :options="graphOptions"
        :on-node-expand="onNodeExpand" />
    </div>
    
  </div>

</template>

<script>
import SeeksRelationGraph  from 'relation-graph'
export default {
    name: 'RelationGraph',
    props: {
        graphData: {//图表数据
            type: Object,
            default: ()=>{}
        }   
    },
    components: {
        SeeksRelationGraph
    },
    data () {
        return {
            g_loading: true,
            demoname: '---',
            graphOptions: {
                'backgrounImageNoRepeat': true,
                'layouts': [
                    {
                        'label': '中心',
                        'layoutName': 'tree',
                        'layoutClassName': 'seeks-layout-tree',
                        'defaultJunctionPoint': 'border',
                        'defaultNodeShape': 0,
                        'defaultLineShape': 1,
                        'centerOffset_x': -300,
                        'centerOffset_y': 0,
                        'min_per_width': '60',
                        'min_per_height': '400'
                    }
                ],
                'defaultExpandHolderPosition': 'bottom',
                'defaultLineShape': 4,
                'defaultJunctionPoint': 'tb',
                'defaultNodeShape': 1,
                'defaultNodeWidth': '50',
                'defaultNodeHeight': '250',
                'defaultNodeBorderWidth': 0
            }
        }
    },
    created () {
    },
    mounted () {
        this.demoname = this.$route.params.demoname
        // this.setGraphData()
    },
    methods: {
        setGraphData () {
            var __graph_json_data =this.graphData
            console.log(__graph_json_data)
            if(__graph_json_data.nodes){
                __graph_json_data.nodes.forEach(thisNode => {
                    if (thisNode.id === __graph_json_data.rootId) {
                        thisNode.width = 300
                        thisNode.height = 100
                        thisNode.offset_x = -80
                    }
                    if (thisNode.text === '暂无数据') {
                        thisNode.width = 300
                        thisNode.height = 100
                        thisNode.offset_x = -80
                        thisNode.expandHolderPosition = 'bottom'
                        thisNode.expanded = false
                    }
                })
            }
            setTimeout(function () {
                this.g_loading = false
                this.$refs.seeksRelationGraph.setJsonData(__graph_json_data, () => {
                    // 这些写上当图谱初始化完成后需要执行的代码
                })
            }.bind(this), 1000)
        },
        onNodeExpand (node) {
            //模拟动态加载数据
            if (node.data && node.data.asyncChild === true && node.data.loaded === false) {
                this.g_loading = true
                node.data.loaded = true
                setTimeout(function () {
                    this.g_loading = false
                    var _new_json_data = {
                        nodes: [
                            { id: node.id + '-child-1', text: node.id + '-的子节点1'},
                            { id: node.id + '-child-2', text: node.id + '-的子节点2'},
                            { id: node.id + '-child-3', text: node.id + '-的子节点3'}
                        ],
                        links: [
                            { from: node.id, to: node.id + '-child-1', text: '动态子节点'},
                            { from: node.id, to: node.id + '-child-2', text: '动态子节点'},
                            { from: node.id, to: node.id + '-child-3', text: '动态子节点'}
                        ]
                    }
                    this.$refs.seeksRelationGraph.appendJsonData(_new_json_data, () => {
                        // 这些写上当图谱初始化完成后需要执行的代码
                    })
                }.bind(this), 1000)
                return
            }
        }
    }
}
</script>

<style lang="less" scoped>
.table-content{
  display: flex;
  flex-direction: column;
  flex: 1;
  :deep(.ant-form){
    display: none;
  }
  :deep(.grid-box){
    top: 0px;
    bottom: 0px;
  }
  :deep(.ant-tabs-nav-wrap){
    background-color: #fff;
  }
  .red{
    color:#F71515;
  }
}
</style>