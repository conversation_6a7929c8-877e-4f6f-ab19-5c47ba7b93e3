import DetailLayout from './DetailLayout.vue'
import { BatchDownloadBtn } from '@comp/template/business/class/batchDownloadBtn'
export const DetailMixin = {
    components: {
        DetailLayout,
        remoteJs: {
            render (createElement) {
                var self = this
                return createElement('script', {
                    attrs: { type: 'text/javascript', src: this.src, async: true },
                    on: {
                        load: function (event) {
                            self.$emit('load', event)
                        },
                        error: function (event) {
                            self.$emit('error', event)
                        }
                    }
                })
            },
            props: {
                src: { type: String, required: true }
            }
        }
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => { }
        }
    },
    data () {
        return {
            pageConfig: {}
        }
    },
    mounted () {
        // this.init()
        this.initBatchDownloadBtn()
    },
    methods: {
        init () {
            if (this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }
        },
        //批量下载 注入
        initBatchDownloadBtn () {
            this.pageData.groups.forEach(group => {
                new BatchDownloadBtn().handelCreateBatchDownload(group)
            })
        },
        //配置加载成功执行
        loadSuccess () {
            this.pageConfig = getPageConfig() // eslint-disable-line
            console.log(this.pageConfig)
            this.handlePageData(this.pageConfig)
            this.init()
        },
        //配置加载异常执行
        loadError (err) {
            console.log(err)
        },
        // 提取表格编辑配置
        // extractGridEditConfig (groups) {
        //     const that = this
        //     let editConfig = null
        //     groups.forEach((groupItem)=> {
        //         // 表格编辑规则判断字段是配置项的值为gridEditConfig唯一，groupCode可任意取值，方便扩展gridEditConfig
        //         if (groupItem.groupType==='gridEditConfig' && groupItem.groupCode==='gridEditConfig') {
        //             if (groupItem.extend) {
        //                 editConfig= {}
        //                 editConfig.trigger= groupItem.extend.editConfig.trigger || 'click'
        //                 editConfig.mode= groupItem.extend.editConfig.mode || 'cell'
        //                 editConfig.showStatus= groupItem.extend.editConfig.showStatus || true
        //                 if (groupItem.extend.editConfig.activeMethod) {
        //                     editConfig.activeMethod= (gridData)=> { return that.gridActiveMethod(gridData, groupItem.extend.editConfig.activeMethod) }
        //                 }
        //             }
        //         }
        //     })
        //     return editConfig
        // },
        // 处理当前pageData数据和配置数据
        handlePageData (data) {
            this.beforeHandleData(data)
            this.pageData.groups = data.groups.concat(this.pageData.groups)
            // 区分表格行的编辑规则和表头分组
            // let gridLineRule = this.pageData.groups.filter((groupItem)=> {
            //     if (groupItem.groupType==='gridEditConfig') {
            //         return true
            //     }
            // })
            this.pageData.groups = this.pageData.groups.filter((groupItem) => {
                if (groupItem.groupType !== 'gridEditConfig') {
                    return true
                }
            })
            this.pageData.formFields = data.formFields
            let fields = []
            this.pageData.groups.forEach(item => {
                //行表列信息，目前固定为itemInfo
                if (item.groupType === 'item') {
                    item.type = 'grid'
                    if (!item.custom) {
                        item.custom = {}
                    }
                    let columns = data.itemColumns.filter(column => {
                        if (item.groupCode === 'itemInfo') {
                            return !column.groupCode
                        }
                        return column.groupCode == item.groupCode
                    })
                    if (!item.custom.ref) {
                        item.custom.ref = item.groupCode
                    }
                    if (!item.custom.notShowTableSeq) {
                        item.custom.columns = [{ type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') }, { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_crAc_25e0e6bb`, '修改类型'), field: 'updateType_dictText', width: 80 }].concat(columns)
                    } else {
                        item.custom.columns = [{ type: 'checkbox', width: 40 }].concat(columns)
                    }
                }
                //表单信息根据分组编码分类
                if (item.type !== 'grid') {
                    fields = data.formFields.filter(item2 => {
                        return item.groupCode == item2.groupCode
                    })
                    // 过滤配置为图片类型的字段，并塞在数组最后面
                    let imageFields = fields.filter(n => n.fieldType === 'image')
                    let otherFields = fields.filter(n => n.fieldType !== 'image')
                    fields = otherFields.concat(imageFields)

                    item.custom = {
                        formFields: fields,
                        form: {},
                        validateRules: {}
                    }
                } else {
                    // item.custom.editConfig= this.extractGridEditConfig(gridLineRule)
                    item.custom.columns.forEach(sub => {
                        if (sub.fieldLabelI18nKey) {
                            sub.title = this.$srmI18n(`${this.$getLangAccount()}#${sub.fieldLabelI18nKey}`, sub.title)
                        }
                        if (sub.dictCode) {
                            sub.field += '_dictText'
                        }
                        if (sub.slots) {
                            sub.slots.header = ({ column }) => {
                                const flag = !!(sub.helpText)
                                const dom = flag
                                    ? (<vxe-tooltip content={sub.helpText}>
                                        <span style="display: flex; alignItems: center;">
                                            <i class="vxe-icon--question"></i>
                                            <span style="marginLeft: 6px;">{column.title}</span>
                                        </span>
                                    </vxe-tooltip>)
                                    : (<span>{column.title}</span>)
                                return [
                                    dom
                                ]
                            }
                        }
                    })
                }
            })
            this.afterHandleData(this.pageData)
        },
        goBack () {
            this.$emit('hide')
        },
        prevEvent () {
            this.$refs.detailPage.prevStep()
        },
        nextEvent () {
            this.$refs.detailPage.nextStep()
        },
        downloadEvent (row) {
            this.$refs.detailPage.handleDownload(row)
        },
        beforeHandleData (data) {
            console.log('beforeHandleData', data)
        },
        afterHandleData (data) {
            console.log('afterHandleData', data)
        },
        showAuditBtn () {
            return this.$refs.detailPage && this.$refs.detailPage.form && this.$refs.detailPage.form.auditStatus === '1'
        }
    }
}