<template>
    <div class="business-container" :style="{ height: pageContentHeight }">

        <a-spin :spinning="confirmLoading">
            <business-layout v-if="show" :ref="businessRefName" :remoteJsFilePath="remoteJsFilePath"
                :externalToolBar="externalToolBar" :currentEditRow="{}" :pageFooterButtons="pageFooterButtons"
                :pageHeaderButtons="pageHeaderButtons" modelLayout="masterSlave"
                :handleAfterDealSource="handleAfterDealSource" :fromSourceData="fromSourceData" :pageStatus="pageStatus"
                v-on="businessHandler">
                <template #noticeInfo>
                    <j-editor ref="ueditor" v-model="noticeContent" />
                </template>
            </business-layout>

            <field-select-modal ref="fieldSelectModal" isEmit :handleListData="handleListData" @ok="fieldSelectOk" isTree />
            <field-select-modal ref="fieldSelectModal2" isEmit :handleListData="handleListData" @ok="fieldSelectOk" />
            <flowViewModal v-model="flowView" :flowId="flowId" :currentEditRow="currentEditRow" />
        </a-spin>
    </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import JEditor from '@/components/els/JEditor'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { baseMixins } from '../../plugins/baseMixins.js'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { BUTTON_PUBLISH } from '@/utils/constant.js'
import flowViewModal from '@comp/flowView/flowView'
import { USER_INFO } from '@/store/mutation-types'


export default {
    name: 'InvitaionBid',
    components: {
        BusinessLayout,
        JEditor,
        fieldSelectModal,
        flowViewModal
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage', 'resetCurrentSubPackage'],
    mixins: [businessUtilMixin, baseMixins],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            businessRefName: 'businessRef',
            confirmLoading: false,
            flowView: false,
            flowId: 0,
            id: '',
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            show: false,
            // externalToolBar: {},
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    type: 'primary',
                    click: this.saveEvent
                },
                {
                    ...BUTTON_PUBLISH,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                    args: {
                        url: '/tender/purchaseTenderNoticeHead/publish'
                    },
                    click: this.publishEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bL_e90d1`, '生成'),
                    click: this.generateTemplateEvent
                }
            ],
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.cancelAudit,
                    show: this.cancelAuditShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.viewButtonShow
                }
            ],
            currentGroupCode: {},
            noticeStatus: '',
            fromSourceData: {},
            noticeContent: '',
            url: {
                detail: '/tender/purchaseTenderNoticeHead/queryBySubpackageId',
                add: '/tender/purchaseTenderNoticeHead/add',
                edit: '/tender/purchaseTenderNoticeHead/edit',
                publish: '/tender/purchaseTenderNoticeHead/publish',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                generateTemplate: '/tender/purchaseTenderNoticeHead/generator'
            },
            remoteJsFilePath: '',
            signUp: '',
            bidding: '',
            tenderType: '',
            handleListData: null

        }
    },
    computed: {
        // remoteJsFilePath () {
        //     // let templateNumber = this.currentEditRow.templateNumber
        //     // let templateVersion = this.currentEditRow.templateVersion
        //     // let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
        //     let templateNumber = 'TC2022041301'
        //     let templateVersion = 1
        //     let account = 100000
        //     return `${account}/purchase_purchaseTenderNotice_${templateNumber}_${templateVersion}`
        // },
        externalToolBar () {
            let purchaseTenderNoticeItemList = [],
                purchaseTenderSupplierInvitation = []
            const currentSubPackage = this.currentSubPackage()
            if (this.noticeStatus == '0') {
                purchaseTenderNoticeItemList = [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            }
            if (currentSubPackage.checkType != '0' && this.noticeStatus == '0') {
                purchaseTenderSupplierInvitation = [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.handleInvite,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            }
            return {
                purchaseTenderNoticeItemList: purchaseTenderNoticeItemList,
                purchaseTenderSupplierInvitation: purchaseTenderSupplierInvitation
            }
        },
        pageStatus () {
            let status = this.noticeStatus == '0' ? 'edit' : 'detail'
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') status = 'detail'
            return status
        },
        subId () {
            return this.subpackageId()
        },
        subpackage () {
            return this.currentSubPackage()
        },
        pageContentHeight () {
            let height = document.documentElement.clientHeight - 60
            return height + 'px'
        }
    },
    methods: {
        generateTemplateEvent () {
            // let cb = () => {
            //     let params = this.getParamsData()
            //     let templateLibraryId
            //     this.$refs.businessRef.$refs.purchaseTenderNoticeItemListgrid[0].pageConfig.groups.forEach(group=>{
            //         if(group.groupCode == 'noticeInfo'){
            //             templateLibraryId = group.formModel.templateLibraryId || ''
            //         }
            //     })
            //     postAction(this.url.generateTemplate, {'businessType': 'tender', templateLibraryId, 'tenderNoticeId': this.id, 'businessId': params.subpackageId})
            //         .then((res) => {
            //             // 刷新
            //             if (res.success) {
            //                 this.noticeContent = res.result.content
            //             }else{
            //                 this.$message.error(res.message)
            //             }
            //         }).finally(() => {
            //             this.confirmLoading = false
            //         })
            // }
            // if (this.id) {
            //     // cb()
            // } else {
            //     // 未保存前先保存
            //     this.saveEvent(cb)
            // }

            let params = this.getParamsData()
            params.id = this.id ? this.id : ''
            params.bidding = this.bidding
            params.signUp = this.signUp
            if (!params.templateLibraryId) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFRxIr_987441cd`, '请先选择公告模板'))
                return
            }
            this.confirmLoading = true
            // let templateLibraryId
            // this.$refs.businessRef.$refs.purchaseTenderNoticeItemListgrid[0].pageConfig.groups.forEach(group=>{
            //     if(group.groupCode == 'noticeInfo'){
            //         templateLibraryId = group.formModel.templateLibraryId || ''
            //     }
            // })
            postAction(this.url.generateTemplate, params)
                .then((res) => {
                    // 刷新
                    if (res.success) {
                        this.$message.success(res.message)
                        //this.noticeContent = res.result.content
                        this.init()
                    } else {
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
        },
        publishEvent () {
            const pageConfig = this.$refs[this.businessRefName].extendAllData()
            // 校验数据
            this.stepValidate(pageConfig).then((res) => {
                let params = this.getParamsData()
                try {
                    //购标开始时间大于结束时间的处理
                    if (this.bidding == '1' && new Date(params.biddingBeginTime).getTime() >= new Date(params.biddingEndTime).getTime()) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_lBvKKIlTXUlByWKI_f8edab9a`, '售标开始时间必须小于售标结束时间'))
                        return
                    }
                    //报名开始时间大于结束时间的处理
                    const checkType = this.subpackage.checkType
                    if (this.signUp == '1' && checkType == '1' && new Date(params.signUpBeginTime).getTime() >= new Date(params.signUpEndTime).getTime()) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sRvKKIlTXUsRyWKI_1366c8b8`, '报名开始时间必须小于报名结束时间'))
                        return
                    }
                    //文件澄清截止时间必须小于等于开标时间
                    if (new Date(params.fileClarificationEndTime).getTime() > new Date(params.openBiddingTime).getTime()) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QILVyRKIlTXUEUvBKI_1889c9a5`, '文件澄清截止时间必须小于等于开标时间'))
                        return
                    }
                    //递交截止时间必须大于等于售标结束时间
                    if (params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime() < new Date(params.biddingEndTime).getTime()) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTfUEUlByWKI_803238d`, '递交截止时间必须大于等于售标结束时间'))
                        return
                    }
                    //递交截止时间必须小于等于开标时间
                    if (params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime() > new Date(params.openBiddingTime).getTime()) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTXUEUvBKI_59297d67`, '递交截止时间必须小于等于开标时间'))
                        return
                    }

                    // this.stepValidate(this.$refs[this.businessRefName])
                } catch (err) {
                    console.log(err)
                }
                //公告内容不能为空
                if (this.noticeContent == '') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOKmRxCcxOLV_c478a5c4`, '校验失败！公告内容不能为空'))
                    return
                }
                params.bidding = this.bidding
                params.signUp = this.signUp
                params.templateNumber = params.templateNumber || this.currentEditRow.templateNumber || ''
                params.templateVersion = params.templateVersion || this.currentEditRow.templateVersion || ''
                params.templateAccount = params.templateAccount || this.currentEditRow.templateAccount || ''
                params.templateName = params.templateName || this.currentEditRow.templateName || ''
                params.tenderProjectName = this.tenderCurrentRow.tenderProjectName
                this.confirmLoading = true
                postAction(this.url.publish, params)
                    .then((res) => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        // 刷新
                        if (res.success) {
                            // this.$emit('resetCurrentSubPackage')
                            this.resetCurrentSubPackage()
                            this.init()
                        }
                    })
                    .finally(() => {
                        this.confirmLoading = false
                    })
            })
        },
        saveEvent (cb) {
            // const pageConfig = this.$refs[this.businessRefName].extendAllData()
            // // 校验数据
            // this.stepValidate(pageConfig).then(res => {
            let params = this.getParamsData()
            try {
                //购标开始时间大于结束时间的处理
                if (this.bidding == '1' && params.biddingBeginTime && params.biddingEndTime && new Date(params.biddingBeginTime).getTime() >= new Date(params.biddingEndTime).getTime()) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_lBvKKIlTXUlByWKI_f8edab9a`, '售标开始时间必须小于售标结束时间'))
                    return
                }
                //报名开始时间大于结束时间的处理
                const checkType = this.subpackage.checkType
                if (this.signUp == '1' && checkType == '1' && params.signUpBeginTime && params.signUpEndTime && new Date(params.signUpBeginTime).getTime() >= new Date(params.signUpEndTime).getTime()) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sRvKKIlTXUsRyWKI_1366c8b8`, '报名开始时间必须小于报名结束时间'))
                    return
                }
                //文件澄清截止时间必须小于等于开标时间
                if (new Date(params.fileClarificationEndTime).getTime() > new Date(params.openBiddingTime).getTime()) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QILVyRKIlTXUEUvBKI_1889c9a5`, '文件澄清截止时间必须小于等于开标时间'))
                    return
                }
                //递交截止时间必须大于等于售标结束时间
                if (params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime() < new Date(params.biddingEndTime).getTime()) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTfUEUlByWKI_803238d`, '递交截止时间必须大于等于售标结束时间'))
                    return
                }
                //递交截止时间必须小于等于开标时间
                if (params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime() > new Date(params.openBiddingTime).getTime()) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTXUEUvBKI_59297d67`, '递交截止时间必须小于等于开标时间'))
                    return
                }
            } catch (err) {
                console.log(err)
            }
            if (params.purchaseTenderNoticeItemList.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFRKjzs_5b73428b`, '请选择关联的分包'))
                return
            }
            let url = this.id ? this.url.edit : this.url.add
            params.bidding = this.bidding
            params.signUp = this.signUp
            this.confirmLoading = true
            params.templateNumber = params.templateNumber || this.currentEditRow.templateNumber || ''
            params.templateVersion = params.templateVersion || this.currentEditRow.templateVersion || ''
            params.templateAccount = params.templateAccount || this.currentEditRow.templateAccount || ''
            params.templateName = params.templateName || this.currentEditRow.templateName || ''
            // let {templateLibraryId, templateTitle} = this.$refs.businessRef.$refs.purchaseTenderNoticeItemListgrid[0].pageConfig.groups[5].formModel
            // params.templateLibraryId = templateLibraryId ||  params.templateLibraryId
            // params.templateTitle = templateTitle ||  params.templateTitle

            postAction(url, params)
                .then((res) => {
                    console.log('res', res)
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if (!this.id) {
                        this.id = res.result.id
                    }
                    if (cb && typeof (cb) == 'function') {
                        cb()
                    } else {
                        this.init()
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
            // })
        },
        getParamsData () {
            let { purchaseTenderSupplierInvitation, purchaseTenderNoticeItemList, fileSubmit, getFile, openTenderInfo, noticeInfo, ...baseForm } = this.$refs[this.businessRefName].extendAllData().allData
            let params = baseForm
            // 文件获取
            params['signUpType'] = getFile.signUpType
            params['signUpBeginTime'] = getFile.signUpBeginTime
            params['signUpEndTime'] = getFile.signUpEndTime
            params['biddingType'] = getFile.biddingType
            params['biddingBeginTime'] = getFile.biddingBeginTime
            params['biddingEndTime'] = getFile.biddingEndTime
            params['contactsPhone'] = getFile.contactsPhone
            params['fileTenderObtainDesc'] = getFile.fileTenderObtainDesc
            params['offlineSaleAccount'] = getFile.offlineSaleAccount
            params['fileClarificationEndTime'] = getFile.fileClarificationEndTime
            params['receipt'] = getFile.receipt || ''

            // 文件递交
            params['fileSubmitEndTime'] = fileSubmit.fileSubmitEndTime
            params['fileSubmitAddress'] = fileSubmit.fileSubmitAddress
            // 开标信息
            params['openBiddingTime'] = openTenderInfo.openBiddingTime
            params['openBiddingAddress'] = openTenderInfo.openBiddingAddress
            // 公告信息
            params['noticeTitle'] = noticeInfo.noticeTitle
            params['noticeScope'] = noticeInfo.noticeScope
            params['noticeContent'] = noticeInfo.noticeContent
            params['templateName'] = noticeInfo.templateName
            params['templateLibraryId'] = noticeInfo.templateLibraryId || ''
            params['templateTitle'] = noticeInfo.templateTitle || ''
            // 邀请名单
            params['purchaseTenderSupplierInvitation'] = purchaseTenderSupplierInvitation
            // 关联分包
            params['purchaseTenderNoticeItemList'] = purchaseTenderNoticeItemList
            params['subpackageId'] = this.subId
            params['tenderProjectId'] = this.tenderCurrentRow.id
            params['noticeType'] = '0'
            params['noticeContent'] = this.noticeContent
            params['tenderTaskId'] = this.$ls.get('SET_TENDERCURRENTROW').tenderTaskId
            // console.log(params.templateLibraryId)
            // params.templateLibraryId = params.templateLibraryId || this.currentEditRow.templateLibraryId || ''
            // params.templateTitle = params.templateTitle || this.currentEditRow.templateTitle || ''
            return params
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log(this.$ls.get(USER_INFO).elsAccount, this.tenderCurrentRow.purchaseExecutorAccount, (this.$ls.get(USER_INFO).elsAccount == this.tenderCurrentRow.purchaseExecutorAccount))
            if (this.$ls.get(USER_INFO).elsAccount != this.tenderCurrentRow.purchaseExecutorAccount) {
                pageConfig.groups = pageConfig.groups.filter(item => {
                    return item.groupCode != 'purchaseTenderSupplierInvitation'
                })
            }
            if (pageConfig.groups[0].formModel.consortiumBidding != '1') {
                pageConfig.groups[0].formModel.consortiumBidding = 0
            }
            var that = this
            this.$nextTick(() => {
                let itemGrid = this.getItemGridRef('purchaseTenderNoticeItemList')
                let ids = itemGrid.getTableData().fullData.map((item) => item.subpackageId)
                // 若不包含本分包，则默认插入本分包
                if (!ids.includes(that.subId)) {
                    itemGrid.loadData([
                        {
                            subpackageId: that.subpackage.id,
                            subpackageName: that.subpackage.subpackageName
                        }
                    ])
                }
            })

            // 如果分包状态是预审环节需要隐藏一些字段
            const checkType = that.subpackage.checkType
            if (checkType == '0') {
                let formFields = [],
                    arr = ['signUpType', 'signUpBeginTime', 'signUpEndTime']
                let formFieldsOld = []
                pageConfig.groups.forEach((group) => {
                    if (group.groupCode == 'getFile') {
                        formFieldsOld = group.formFields
                    }
                })
                formFieldsOld.forEach((form) => {
                    if (arr.indexOf(form.fieldName) == -1) {
                        formFields.push(form)
                    }
                })
                pageConfig.groups.forEach((group) => {
                    if (group.groupCode == 'getFile') {
                        group.formFields = formFields
                    }
                    if (group.groupCode == 'noticeInfo') {
                        // 投标邀请公告-范围给死
                        group.formModel.noticeScope = '4'
                    }
                })
            }

            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }

            let signUpFlag = this.signUp !== '1'
            let biddingFlag = this.bidding !== '1'
            // let none = (signUpFlag && biddingFlag)
            // 隐藏数组内的字段
            let arr = []
            // 非报名情况
            if (signUpFlag) {
                arr = arr.concat('signUpBeginTime', 'signUpEndTime', 'signUpType')
            }
            //非购标情况
            if (biddingFlag) {
                arr = arr.concat('biddingBeginTime', 'biddingEndTime', 'biddingType', 'offlineSaleAccount')
            }
            // 不报名不购标情况
            // if(none){
            //     arr=arr.concat('fileSubmitEndTime')
            // }
            let formFields = []
            let formFieldsOld = []
            pageConfig.groups.forEach((group) => {
                if (group.groupCode == 'getFile') {
                    formFieldsOld = group.formFields
                }
            })
            formFieldsOld.forEach((form) => {
                if (arr.indexOf(form.fieldName) == -1) {
                    formFields.push(form)
                }
            })
            pageConfig.groups.forEach((group) => {
                if (group.groupCode == 'getFile') {
                    group.formFields = formFields
                }
                if (group.groupCode == 'noticeInfo') {
                    // 投标邀请公告-范围给死
                    group.formModel.noticeScope = '4'
                }
            })
            // console.log('pageConfig', pageConfig)
            // console.log(this.signUp, this.bidding)
            // pageConfig.groups[1].formFields.forEach(item => {
            //     if (arr.indexOf(item.fieldName) == -1) {
            //         formFields.push(item)
            //     }
            // })
            // pageConfig.groups[1].formFields = formFields

            setDisabledByProp('signUpBeginTime', signUpFlag)
            setDisabledByProp('signUpEndTime', signUpFlag)
            setDisabledByProp('signUpType', signUpFlag)
            setDisabledByProp('biddingBeginTime', biddingFlag)
            setDisabledByProp('biddingEndTime', biddingFlag)
            // setDisabledByProp('fileSubmitEndTime', none)

            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }

                rule[prop] = [
                    {
                        required: flag,
                        message: `${fieldLabel}必填`
                    }
                ]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            //此处写根据分包信息作为判断展示位
            let signUpValidateFlag = this.signUp == '1'
            let biddingValidateFlag = this.bidding == '1'
            // let Or = (signUpValidateFlag || biddingValidateFlag)
            setValidateRuleByProp('signUpBeginTime', signUpValidateFlag)
            setValidateRuleByProp('signUpEndTime', signUpValidateFlag)
            setValidateRuleByProp('signUpType', signUpValidateFlag)

            setValidateRuleByProp('biddingBeginTime', biddingValidateFlag)
            setValidateRuleByProp('biddingEndTime', biddingValidateFlag)
        },
        businessGridAdd ({ pageConfig, groupCode }) {
            console.log(pageConfig, groupCode)
            this.currentGroupCode = groupCode
            let url = '/tender/purchaseTenderProjectHead/subpackage/listAll'
            let columns = [
                { field: 'subpackageName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称') },
                { field: 'subpackageNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsAo_2690a9aa`, '分包编码') }
            ]
            // this.$refs.fieldSelectModal.open(url, { headId: this.tenderCurrentRow.id }, columns, 'multiple')
            let { fullData } = this.getItemGridRef(this.currentGroupCode).getTableData()
            let ids = fullData.map((item) => item.subpackageId)
            // 已选的不能在勾选 并且 一步法和两步法的分包应不能关联分包
            const currentSubPackage = this.currentSubPackage()
            this.handleListData = (that, res) => {
                const flag = that.isTree
                    ? res.success && res.result && res.result.length
                    : res.success && res.result && res.result.records && res.result.records.length
                console.log(that.isTree, res.success, res.result, res.result.length)
                if (flag) {
                    let result = that.isTree ? res.result : res.result.records
                    result = result || []
                    console.log('currentSubPackage.processType', currentSubPackage)
                    let d = result.filter(item => {
                        return currentSubPackage.processType == item.processType
                    })
                    that.tableData = d
                    that.tablePage.total = d.length
                } else {
                    that.tableData = []
                    that.tablePage.total = 0
                    that.tablePage.currentPage = 1
                }
            }
            let checkedConfig = {
                checkMethod: ({ row }) => {
                    let flag = true
                    if (ids.includes(row.id)) flag = false
                    // if (currentSubPackage.processType != row.processType) flag = false
                    return flag
                }
            }
            //let superQueryParams = [{ logicSymbol: 'lt', fieldCode: 'status', fieldType: '', dictCode: '', fieldValue: '3110', joiner: 'AND' }]
            //superQueryParams = encodeURI(JSON.stringify(superQueryParams))
            this.$refs.fieldSelectModal.open(url, { headId: this.tenderCurrentRow.id, signUp: this.signUp, bidding: this.bidding, tenderType: this.tenderType, status: 3110 }, columns, 'multiple', checkedConfig)
        },
        handleInvite ({ pageConfig, groupCode }) {
            this.currentGroupCode = groupCode
            let { fullData } = this.getItemGridRef(this.currentGroupCode).getTableData()
            let ids = fullData.map((item) => {
                return item.supplierAccount || item.toElsAccount
            })
            let url = '/supplier/supplierMaster/contactList?supplierStatus=2&frozenFunctionValue=2&functionName=' + encodeURI('报价')
            let columns = [
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '供应商名称') },
                { field: 'functionName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contact`, '联系人') },
                { field: 'functionTelphone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contactPhone`, '联系电话') }
            ]
            // 已选的不能在勾选
            let checkedConfig = {
                checkMethod: ({ row }) => {
                    let flag = true
                    if (ids.includes(row.toElsAccount)) flag = false
                    return flag
                }
            }
            this.handleListData = null
            this.$refs.fieldSelectModal2.open(url, { headId: this.currentEditRow.id }, columns, 'multiple', checkedConfig)
        },
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef(this.currentGroupCode)
            var subpackageList = new Array()
            console.log('itemGrid', itemGrid)
            if (this.currentGroupCode == 'purchaseTenderNoticeItemList') {
                // 判断是否是关联分包的操作
                data.forEach((item) => {
                    var subpackage = {}
                    subpackage['subpackageId'] = item.id
                    subpackage['subpackageName'] = item.subpackageName
                    subpackageList.push(subpackage)
                })
            } else {
                data.forEach((item) => {
                    var subpackage = {}
                    subpackage['supplierName'] = item.supplierName
                    subpackage['contacts'] = item.functionName
                    subpackage['contactsPhone'] = item.functionTelphone
                    subpackage['supplierAccount'] = item.toElsAccount
                    subpackageList.push(subpackage)
                })
            }
            itemGrid.insertAt([...subpackageList], -1)
        },
        copyRow (data) {
            let itemGrid = this.getItemGridRef(data.groupCode)
            let rows = itemGrid.getCheckboxRecords()
            if (rows.length == 1) {
                let item = Object.assign({}, rows[0])
                itemGrid.insertAt([item], -1)
            } else {
                return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRiIc_dc3c48ce`, '请勾选一行'))
            }
        },
        init () {
            this.confirmLoading = true
            this.show = false
            return getAction(this.url.detail, { subpackageId: this.subId, noticeType: '0' })
                .then((res) => {
                    if (res.success) {
                        if (res.result) {
                            this.fromSourceData = res.result || {}
                            this.noticeStatus = res.result.noticeStatus
                            this.noticeContent = res.result.noticeContent
                            this.id = res.result.id || ''
                            if (this.noticeStatus != '0') this.externalToolBar = {}

                            this.fromSourceData.purchaseTenderNoticeItemList.length == 0 &&
                                (() => {
                                    this.fromSourceData = Object.assign(this.fromSourceData, {
                                        purchaseTenderNoticeItemList: [
                                            {
                                                subpackageId: this.subpackage.id,
                                                subpackageName: this.subpackage.subpackageName
                                            }
                                        ]
                                    })
                                })()
                        } else {
                            this.fromSourceData = {
                                purchaseTenderNoticeItemList: [
                                    {
                                        subpackageId: this.subpackage.id,
                                        subpackageName: this.subpackage.subpackageName
                                    }
                                ]
                            }
                            this.noticeStatus = '0'
                        }
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.show = true
                })
        },
        cancelAudit ({ Vue, pageConfig, btn, groupCode }) {
            this.auditStatus = pageConfig.groups[0].formModel.auditStatus
            if (this.auditStatus != '1') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APxOqXUz_5fba973e`, '当前不能撤销审批'))
                return
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit, pageConfig.groups[0].formModel)
                    // that.$refs.businessRefName.loadData()
                }
            })
        },
        cancelAuditShow ({ pageData }) {
            // 判断是否为审批中，审批中才展示撤销审批按钮
            return pageData.auditStatus == '1' ? true : false
        },
        viewButtonShow ({ pageData }) {
            // 判断是否需要审批且有flowid，都具备才展示查看流程按钮
            if (pageData.audit != '1') {
                return false
            }

            if (pageData.noticeStatus != '0' && pageData.flowId) {
                return true
            }
            return false
        },

        showFlow ({ Vue, pageConfig, btn, groupCode }) {
            this.flowId = pageConfig.groups[0].formModel.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_APUziNxOmAQLW_25fa4a00`, '当前审批策略不能查看流程！'))
                return
            }
            this.flowView = true
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['rootProcessInstanceId'] = formData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param)
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.init()
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    async created () {
        await this.init()
        // this.noticeStatus = '0'
        const currentEditRow = this.fromSourceData.templateNumber && this.fromSourceData.templateAccount ? {
            templateNumber: this.fromSourceData.templateNumber,
            templateName: this.fromSourceData.templateName,
            templateVersion: this.fromSourceData.templateVersion,
            templateAccount: this.fromSourceData.templateAccount
        } : await this.getBusinessTemplate('purchaseTenderNotice')
        if (!currentEditRow) {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
            return
        }
        this.currentEditRow = Object.assign(this.currentEditRow, currentEditRow)
        this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/purchase_purchaseTenderNotice_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`

        this.signUp = this.subpackage.signUp
        this.bidding = this.subpackage.bidding
        this.tenderType = this.subpackage.tenderType
    }
}
</script>
<style lang="less" scoped>
:deep(.page-container .edit-page .page-content) {
    flex: auto;
}
</style>
