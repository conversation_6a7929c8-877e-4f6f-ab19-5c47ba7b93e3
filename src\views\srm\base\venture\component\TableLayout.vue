<!--
    create by wyc 2021-7-13
    desc:用于不用接口获取列表数据的表格，接口返回列名
-->
<template>
  <div class="table-layout">
      
    <vxe-grid
      border
      resizable
      height="505"
      :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
      :columns="tableColumn"
      :data="tableData"
      v-bind="tableConfig"
    >
      
      <template #num1_default="{ row }">
 
        <img
          style="width:40px;height:40px;border-radius:50%;margin-right:20px;"
          :src="row.logo"
          alt=""
          v-if="row.logo"/>  <span>{{ row.name }}  </span>
      </template>

      <template #holding_face="{ row }">   
        <div
          v-for="(item,idx) in row.chainlist"
          :key="idx+'chainlist'"
          class="chainlist">
          <div
            v-for="(it,index) in item"
            :key="index+'chainlist_children'">
            <p v-if="index==0">{{ it.title }}（占比约{{ it.value }}）</p>
            <b v-else> <span v-if="it.type!='percent'">{{ it.value }}</span> <span v-else>（{{ it.value }}） -></span>  </b>
          </div>
        </div> 
      </template>

      <template #a_link="{ row }">   
           
        <a
          :href="row.lawsuitUrl"
          class="row-link"
          target="_blank"
          v-if="row.lawsuitUrl"> 查看</a>

      </template>

      
      <template #txt_txt="{ row }">   
           
        <b
          v-for="(item,index) in JSON.parse(row.applicant)"
          :key="index+'txt_txt'">{{ item.applicantName }}</b>

      </template>
      
   
      <template #empty>
        <m-empty
          :displayModel="'tab'"
          :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"/>
      </template>

    


      <!--操作列-->
      <template
        #operation="{ row }">
        <a-button
          v-for="(it,id) in tableConfig.operationColunm"
          :key="id+'a-button'"
          type="link"
          @click="it.callback && it.callback(row)">
          {{ it.name }}
        </a-button>

      </template>

      <template #pager>

        <vxe-pager
          :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
          :current-page.sync="tablePage.currentPage"
          :page-size.sync="tablePage.pageSize"
          :total="tablePage.total"
          @page-change="handlePageChange">
        </vxe-pager>

      </template>

    </vxe-grid>

  </div>
</template>

<script>

//接口
// import { getAction } from '@/api/manage'
import { ajaxGetColumns } from '@/api/api'
export default {
    name: 'TableLayout',
    props: {
        tableConfig: {
            type: Object,
            default: ()=>{
                return {
 
                    // toolbarConfig: {
                    //     // export: true,
                    //     // zoom: true
                    // },
                    operationColunm: [],
                    url: {}
                }
            }
        }
    },  
    data () {
        return {
            tableDataTemp: [],
            tableData: [],
            tableColumn: [],
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 20
            }


        }
    },
    mounted () {


    },
    methods: {
        //设置列表数据
        setTableData (res, tableData){

            let tableColumn=['chainlist', 'chainList']
            let tableColumnLink=['lawsuitUrl']
            let tableColumnTxt=['applicant']
            tableColumn=res.map((v)=>{
                if(res.length<=7){
                    v.width=''
                }else{
                    v.width='150'
                }
                
                if(tableColumn.includes(v.dataIndex)){
                    v.showOverflow=false
                }else{
                    v.showOverflow=true
                }
                if(tableColumnLink.includes(v.dataIndex)){
                    v.slots={default: 'a_link'}     
                }
                if(tableColumnTxt.includes(v.dataIndex)) {
                    v.slots={default: 'txt_txt'}  
                }

                return {
                    field: v.dataIndex,
                    title: v.title,
                    showHeaderOverflow: true,
                    ...v
                }
            })

            if(this.tableConfig.operationColunm.length>0){//如果有操作列
                tableColumn.push( { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 80, showOverflow: true, slots: { default: 'operation' } })
            }
            this.tableColumn=tableColumn
            this.tablePage.total=tableData?tableData.length:0
            this.tableDataTemp=tableData?tableData:[]
            this.tableData=tableData?tableData.slice((this.tablePage.currentPage - 1) * this.tablePage.pageSize, this.tablePage.currentPage * this.tablePage.pageSize):[]
        },
        //根据自定义列来设置列表
        getCustomList (tableData){
            const custonColounm=this.tableConfig.customColumn[this.tableConfig.url.colunm] 
            console.log('tableData', tableData)
            this.setTableData(custonColounm, tableData)
            this.tableConfig.loading=false 
        },
        //使用接口设置列表
        getUrlList (tableData) {
            for(let i in this.tableConfig.url){
                if(i==='colunm'){
                    ajaxGetColumns(this.tableConfig.url[i], null).then((res=>{
                        const { success = false, message = '' } = res || {}
                        const type = success ? 'success' : 'error'
                        if (success) {
                            this.setTableData(res.result, tableData)
                          
                        }else{
                            this.$message[type](message)
                        }
                    })).finally(()=>{
                        this.tableConfig.loading=false 
                    })
                }
            }
        },
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            const tableData=this.tableDataTemp
            this.tableData=tableData.slice((this.tablePage.currentPage - 1) * this.tablePage.pageSize, this.tablePage.currentPage * this.tablePage.pageSize)
        }
    }
}
</script>

<style lang="less" scoped>
.chainlist{
    div{
        display: inline-block;
        &:first-child{
            display: block;
        }
    }
}

</style>