<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showEsignPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleContractAcceptanceEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>

    <!-- 详情页面 -->
    <SaleContractAcceptanceDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />

    <!-- 新增弹窗 -->
    <TemSelectModal
      ref="temSelectModal"
      :pageData="pageData"
      @success="handleEdit"></TemSelectModal>

    <!-- 发起签章 -->
    <SaleContractAcceptanceEsignEdit
      v-if="showEsignPage"
      :current-edit-row="currentEditRow"
      @hide="hideEsignPage" />
    <record-modal
      v-model="recordShowVisible"
      :currentEditRow="currentEditRow"
    />
  </div>
</template>
<script>
import SaleContractAcceptanceEdit from './modules/SaleContractAcceptanceEdit'
import SaleContractAcceptanceDetail from './modules/SaleContractAcceptanceDetail'
import SaleContractAcceptanceEsignEdit from '@views/srm/esign/sale/modules/SaleContractAcceptanceEsignEdit'
import TemSelectModal from './modules/TemSelectModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, postAction} from '@/api/manage'
import RecordModal from '@comp/recordModal'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        SaleContractAcceptanceEdit,
        SaleContractAcceptanceDetail,
        TemSelectModal,
        SaleContractAcceptanceEsignEdit,
        RecordModal
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            showEsignPage: false,
            recordShowVisible: false,
            pageData: {
                businessType: 'contractAcceptance',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#`, '请输入验收单号')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'contractAcceptance#saleContractAcceptance:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                form: {
                    keyWord: ''
                },
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractAcceptance#saleContractAcceptance:view'},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'contractAcceptance#saleContractAcceptance:edit'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'contractAcceptance#saleContractAcceptance:delete'},
                    {type: 'invalid', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.handleInvalid, allow: this.allowInvalid, authorityCode: 'contractAcceptance#saleContractAcceptance:cancellation'},
                    {type: 'esit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_initSignature`, '发起签署'), clickFn: this.esignBtn, allow: this.allowSign, authorityCode: 'contractAcceptance#saleContractAcceptance:initiate'},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQL_1184c464`, '查看签署流程'), clickFn: this.handleESignView, allow: this.allowCheckSign, authorityCode: 'contractAcceptance#saleContractAcceptance:viewEsignFlow'},
                    {type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IKCPQI_49ebfaca`, '下载电签文件'), clickFn: this.flowFileDownload, allow: this.allowDownSign, authorityCode: 'contractAcceptance#saleContractAcceptance:down'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            tabsList: [],
            url: {
                list: '/contract/saleContractAcceptance/list',
                add: '/contract/saleContractAcceptance/add',
                delete: '/contract/saleContractAcceptance/delete',
                invalid: '/contract/saleContractAcceptance/invalid',
                exportXlsUrl: '/contract/saleContractAcceptance/exportXls',
                getPurchaseAccount: '/enterprise/elsEnterpriseInfo/getPurchaseAccount',
                columns: 'saleContractAcceptanceList',
                flowFileDownload: '/esign/elsContractAcceptanceEsign/signFileDownload'
            }
        }
    },
    mounted () {
        this.serachCountTabs('/contract/saleContractAcceptance/counts')
    },
    methods: {
        handleESignView (row) {
            getAction('/esign/elsContractAcceptanceEsign/saleList', { relationId: row.id }).then((res) => {
                if (res.success) {
                    this.$router.push({ path: '/srm/esign/sale/SaleContractAcceptanceEsignList', query: { id: res.result.records[0].id, pageShow: 'true' } })
                    this.visible = false
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        flowFileDownload (row){
            getAction(this.url.flowFileDownload, {id: row.id}).then(res => {
                if(res.success){
                    window.open(res.result[0].fileUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        allowCheckSign (row){
            if(row.signInitiate == '1'){
                return false
            }
            return true
        },
        allowDownSign (row){
            return row.signStatus !== '5'
        },
        handleAdd () {
            let toElsAccount = this.$ls.get('Login_elsAccount')
            getAction(this.url.getPurchaseAccount, {toElsAccount: toElsAccount}).then((res) => {
                if(res.success) {
                    let list = res.result.records || []
                    if (list && list.length===1) {
                        let params = {pageSize: 100, elsAccount: list[0].elsAccount, templateStatus: '1', businessType: this.pageData.businessType, pageNo: 1}
                        getAction('/template/templateHead/getListByType', params).then((rest) => {
                            if(rest.success) {
                                let templateList = rest.result || []
                                if (templateList && templateList.length===1) {
                                    debugger
                                    // 走不弹出框逻辑
                                    let params2 = {
                                        toElsAccount: list[0].elsAccount,
                                        templateAccount: templateList[0].elsAccount,
                                        templateNumber: templateList[0].templateNumber,
                                        templateVersion: templateList[0].templateVersion,
                                        templateName: templateList[0].templateName,
                                        purchaseName: list[0].name,
                                        busAccount: list[0].elsAccount
                                    }
                                    this.currentEditRow = params2
                                    this.showEditPage = true
                                } else {
                                    // 走弹出框逻辑
                                    let data = {
                                        url: '/enterprise/elsEnterpriseInfo/getPurchaseAccount',
                                        params: {
                                            toElsAccount: this.$ls.get('Login_elsAccount')
                                        }
                                    }
                                    this.$refs.temSelectModal.open(data)
                                }
                            } else {
                                this.$message.warning(rest.message)
                            }
                        })
                    } else {
                        // 走弹出框逻辑
                        let data = {
                            url: '/enterprise/elsEnterpriseInfo/getPurchaseAccount',
                            params: {
                                toElsAccount: this.$ls.get('Login_elsAccount')
                            }
                        }
                        this.$refs.temSelectModal.open(data)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        hideEsignPage (){
            this.showEsignPage = false
        },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.promiseNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SaleContractAcceptance', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if(row.promiseStatus != '0') {
                return false
            }else {
                return true
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_salesContractHeader`, '销售合同头'))
        },
        hideDetailPage (){
            this.showDetailPage = false
        },
        allowEdit (row) {
            let documentStatus = row.documentStatus
            if (documentStatus && documentStatus==='0') {
                return false
            } else {
                return true
            }
        },
        allowDelete (row) {
            let documentStatus = row.documentStatus
            if (documentStatus && documentStatus==='0') {
                return false
            } else {
                return true
            }
        },
        allowSign (row) {
            if(row.sign == '1' && row.signStatus == '1' && row.documentStatus == '2'){
                return false
            }
            return true
        },
        allowInvalid (row) {
            let documentStatus = row.documentStatus
            let costStatus = row.costStatus
            if (costStatus=='0' || costStatus =='' && (documentStatus=='1' || documentStatus == '2'|| documentStatus == '3')) {
                return false
            } else {
                //置灰
                return true
            }
        },
        handleInvalid (row) {
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoid`, '确认作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voidSelectedData`, '是否作废选中数据?'),
                onOk: function () {
                    that.loading = true
                    postAction(that.url.invalid, row).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData() // 刷新页面
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        esignBtn (row){
            const param = {
                relationId: row.id,
                toElsAccount: row.elsAccount,
                purchaseName: row.purchaseName,
                supplierName: row.supplierName,
                busType: 'contractAcceptance',
                filesName: row.contractName,
                busNumber: row.acceptanceNumbers,
                elsAccount: row.toElsAccount
            }
            postAction('/esign/elsContractAcceptanceEsign/add', param).then((res) => {
                if (res.success) {
                    this.currentEditRow = res.result
                    this.showEsignPage = true
                } else {
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>