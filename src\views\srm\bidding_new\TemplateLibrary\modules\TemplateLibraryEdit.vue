<template>
  <div class="TemplateLibraryEdit">
    <a-spin :spinning="confirmLoading">
      <div v-show="!showTemplate">
        <div class="templateInfo">
          <div class="templateInfo-title">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_IrVH_31cce6ac`, '模板信息') }}</span>
          </div>
          <div class="templateInfo-content">
            <Dataform
              ref="dataform"
              :formData="formData"
              :fields="formFields" />
          </div>
          <div class="templateInfo-title">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_IrCc_31cd14d2`, '模板内容') }}</span>
          </div>
          <div
            class="templateInfo-content">
            <div class="content">
              <div class="content-left">
                <j-editor
                  ref="jEditor"
                  v-model="formData.templateContent"
                  v-bind="{plugins,coustomInit}"/>
              </div>
              <div class="content-right" >
                <TreeComponent @onClick="onClick" />
              </div>
            </div>
          </div>
        </div>
        <div class="templateFooter">
          <business-button
            :buttons="pageConfig.pageFooterButtons"
            :pageConfig="pageConfig"
            :resultData="formData"
          />
        </div>
      </div>
      <div v-if="showTemplate">
        <titleTrtl>
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_IrUB_31d5dbc2`, '模板预览') }}</span>
          <template
            slot="right"
          >
            <a-button
              type="primary"
              size="small"
              @click="showTemplate = false">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </template>
        </titleTrtl>
        <div
          v-html="templateHtml"
          style="padding: 10px">
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import JEditor from '@comp/els/JEditor'
import {DEFAULT_COLOR, USER_ELS_ACCOUNT} from '@/store/mutation-types'
import TreeComponent from '../components/TreeComponent.vue'
import Dataform from '@views/srm/bidding_new/BiddingHall/components/Dataform.vue'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import { postAction, getAction} from '@/api/manage'
import BusinessButton from '@comp/template/business/components/BusinessButton'

export default {
    components: {
        TreeComponent,
        titleTrtl,
        JEditor,
        BusinessButton,
        Dataform
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        pageStatus () {
            let status = this.formData.status == '0' ? 'edit' : 'detail'
            return status
        }
    },
    data () {
        return {
            confirmLoading: false,
            showTemplate: false,
            templateHtml: null,
            formData: {
                templateContent: ''
            },
            formFields: [
                {
                    field: 'templateTitle',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_templateName`, '模板名称'),
                    fieldLabelI18nKey: '',
                    fieldType: 'input',
                    disabled: false,
                    required: '1'
                },
                {
                    field: 'templateType',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_templateType`, '模板类型'),
                    fieldLabelI18nKey: '',
                    fieldType: 'select',
                    dictCode: 'tenderTemplateType',
                    required: '1'
                },
                {
                    field: 'purchaseType',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseMethod`, '采购方式'),
                    fieldLabelI18nKey: '',
                    fieldType: 'select',
                    dictCode: 'templatePurchaseType',
                    required: '1'
                },
                {
                    field: 'projectType',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                    fieldLabelI18nKey: '',
                    fieldType: 'select',
                    dictCode: 'tenderTemplateProjectType',
                    required: '1'
                },
                {
                    field: 'industryCategory',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EEzA_3f249f03`, '行业分类'),
                    fieldLabelI18nKey: '',
                    fieldType: 'select',
                    dictCode: 'tenderIndustryCategory'
                },
                {
                    field: 'remark',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_remark`, '备注'),
                    fieldLabelI18nKey: '',
                    fieldType: 'input'
                }
            ],
            pageConfig: {
                pageFooterButtons: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        attrs: {
                            type: 'primary'
                        },
                        click: this.handleSave
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                        attrs: {
                            type: 'primary'
                        },
                        click: this.handlePush
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IrUB_31d5dbc2`, '模板预览'),
                        attrs: {
                            type: 'primary'
                        },
                        click: this.templateView
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BRIr_29bcce07`, '复制模板'),
                        attrs: {
                            type: 'primary'
                        },
                        show: () => {
                            if (this.formData.status == '1') return true
                            return false
                        },
                        click: this.copyTemplate
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                        attrs: {
                            type: 'default'
                        },
                        click: this.goBack
                    }
                ] // 底部按钮
            },
            plugins: 'lists image media table wordcount fullscreen code powerpaste',
            coustomInit: {},
            url: {
                add: '/tender/template/purchaseTenderTemplateLibrary/add',
                edit: '/tender/template/purchaseTenderTemplateLibrary/edit',
                detail: '/tender/template/purchaseTenderTemplateLibrary/queryById',
                publish: '/tender/template/purchaseTenderTemplateLibrary/publish',
                templatePreview: '/tender/template/purchaseTenderTemplateLibrary/templatePreview'
            }
        }
    },
    methods: {
        onClick (content) {
            this.$refs.jEditor.insertContent(`<span >${content}</span>`)
        },
        changeSelectValue (data) {
            console.log(data)
        },
        getAllValidate () {
            this.$refs.dataform.externalAllData()
        },
        queryDetail () {
            this.confirmLoading = true
            getAction(this.url.detail, {id: this.currentEditRow.id}).then(res => {
                if (res.success) {
                    this.formData = res.result
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        post (url, params) {
            this.confirmLoading = true
            postAction(url, params).then((res) => {
                let type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.formData = res.result || {}
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleSave () {
            this.$refs.dataform.getValidatePromise().then(() => {
                let params = Object.assign({}, this.formData)
                let url = params.id ? this.url.edit : this.url.add
                this.post(url, params)
            })
        },
        handlePush () {
            this.$refs.dataform.getValidatePromise().then(() => {
                let params = Object.assign({}, this.formData)
                if (!params.templateContent) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IrCcxOLV_ce1b5fe2`, '模板内容不能为空'))
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布?'),
                    onOk: () => {
                        this.post(this.url.publish, params)
                    }
                })
            })
        },
        templateView () {
            let params = Object.assign({}, this.formData)
            if (!params.templateContent) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IrCcxOLV_ce1b5fe2`, '模板内容不能为空'))
            this.confirmLoading = true
            postAction(this.url.templatePreview, params).then(res => {
                if (res.success) {
                    this.templateHtml = res.result.content
                    this.showTemplate = true
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        copyTemplate () {
            this.$emit('copyRow', this.formData)
        },
        goBack () {
            this.$emit('hidden')
        }
    },
    created () {
        if (this.currentEditRow.id) {
            this.queryDetail()
        } else {
            this.formData = Object.assign({}, this.currentEditRow)
        }
    }
}
</script>

<style lang="less" scoped>

.TemplateLibraryEdit {
  // height: 100%;
  background: #fff;
  margin: 8px;
  padding-top: 10px;

  .templateInfo-title {
    background: #eee;
    height: 40px;
    line-height: 40px;
    padding-left: 15px;
  }
  .templateInfo-content {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .templateInfo-content:nth-last-child(1) {
    padding-top: 5px;
    .content {
      display: flex;
      padding-bottom: 38px;
      .content-left {
        padding: 1px;
        flex: 1;
      }
      .content-right {
        width: 230px;
        box-sizing: border-box;
        border: 2px solid #eee;
        min-height: 300px;
        overflow-y: auto;
      }
    }
  }
}
:deep(.ant-form-item){
  display: flex;
  margin-bottom: 0;
  height: 55px;
  line-height: 55px;
        }
.required-field {
  :deep(.ant-form-item-control) {
        input, .ant-select-selection {
            background-color: #fff9f7;
            border: 1px solid #fdaf96;
            border-radius: 4px;
        }
        
    }
}
.templateFooter {
  text-align: center;
  position: fixed;
  bottom: 0px;
  width: calc(100% - 200px);
  background-color: #eee;
  height: 56px;
  line-height: 56px;
  button {
    margin-right: 10px;
  }
}

</style>
