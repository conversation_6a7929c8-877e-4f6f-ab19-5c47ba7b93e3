<template>
  <div class="offlineEvaluation">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      :deadline="evaEndTime"
      :renderExtra="renderExtra"
      @content-header-submit="handleSubmit"
    />

    <div
      class="container"
      :style="style">
      <a-tabs default-active-key="tab_1">
        <a-tab-pane
          key="tab_1"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理')">

          <!-- 线下评标 -->
          <offline ref="offline" />
                    
        </a-tab-pane>

        <a-tab-pane
          key="tab_2"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_dict_vBIBB_6b38ab39`, '开标一览表')"
          force-render>

          <!-- 招标分标管理明细 -->
          <biddine-detail />

        </a-tab-pane>
      </a-tabs>
    </div>
        
  </div>
</template>

<script lang="jsx">
import { getAction, postAction } from '@/api/manage'
import ContentHeader from '@/views/srm/bidding_project/hall/components/content-header'
import Offline from './components/Offline.vue'
import BiddingDetail from '../bidEvaluation/components/BiddingDetail.vue'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'
import countdown from '@/components/countdown'
import { isDef } from '@/utils/util.js'

export default {
    components: {
        'content-header': ContentHeader,
        'biddine-detail': BiddingDetail,
        countdown,
        Offline
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'getBiddingOptions'
    ],
    data () {
        return {
            serverTime: null,
            evaEndTime: null,
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'), type: 'primary', event: 'submit' }
            ],
            showHeader: true,
            height: 0
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        getBiddingHeadData () {
            const { id = '' } = this.vuex_currentEditRow || {}
            const url = '/bidding/purchaseBiddingHead/queryById'

            getAction(url, { id }).then(res => {
                if (!res.success) {
                    return
                }
                const { timestamp = '', result = {} } = res || {}
                let evaEndTime = result.evaEndTime_DateMaps || ''
                this.serverTime = timestamp
                if (evaEndTime) {
                    if (timestamp < evaEndTime) {
                        this.evaEndTime = evaEndTime - timestamp
                    }
                }
            })
        },
        renderExtra (deadline, fn) {
            const scopedSlots = {
                default: (row) => {
                    // return (<span>{ row.hours } : { row.minutes } : { row.seconds }</span>)
                    return (<span>{ row.totalHours } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_time`, '时') } { row.minutes } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_branch`, '分') } { row.seconds } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_second`, '秒') }</span>)
                }
            }
            return (
                <div class="countdown" style="display: flex; align-items: center;">
                    <a-icon type="info-circle" />
                    <span style="margin: 0 8px;">{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRutK_9fc18dac`, '评标截止倒计时') }：</span>
                    <countdown 
                        time={ deadline }
                      
                        scopedSlots={scopedSlots}
                        style={ { fontSize: '12px', color: '#ee1d1d' } }
                        end={ fn }
                    >
                    </countdown>
                </div>
            )
        },
        handleSubmit () {
            let allData = this.$refs.offline.getAllData() || {}
            const { biddingSupplierVOList = [] } = allData || {}
            for (const [idx, item] of biddingSupplierVOList.entries()) {
                for (const sub of item.purchaseBiddingEvaResultList) {
                    console.log('sub.score :>> ', sub.score)
                    if (!isDef(sub.score) || sub.score === '') {
                        let tip = [
                            '行',
                            `${idx + 1}`,
                            sub.regulationName || '',
                            '不能为空'
                        ]
                        this.$message.error(tip.join(''))
                        return
                    }
                }
            }

            const callback = () => {
                let cacheAllData = this.$refs.offline.cacheAllData || {}
                console.log('allData :>> ', allData)
                let params = Object.assign({}, {
                    ...cacheAllData,
                    ...allData
                })
                console.log('params :>> ', params)
                
                const url = '/bidding/purchaseBiddingEvaResult/saveBidEvaOfOffLine'
                
                this.confirmLoading = true
                postAction(url, params)
                    .then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.getBiddingOptions()
                        }
                    })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApprovalTips`, '是否确认提交审批'),
                onOk () {
                    callback && callback()
                }
            })
        }
    },
    // 使用 beforeRouteUpdate 导航守卫监听路由变化
    // 对路由销毁重建
    beforeRouteUpdate (to, from, next) {
        this.routerRefresh() //路由销毁重建方法
        next()
    },
    created () {
        this.getBiddingHeadData()
    }
}
</script>

<style lang="less" scoped>
.offlineEvaluation {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
}
</style>

