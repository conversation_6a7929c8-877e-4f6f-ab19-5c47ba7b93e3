<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <ClContractEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <ClContractDetail
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
    <CLContractAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>   
    <a-modal
      v-drag    
      v-model="visible"
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reasonsRepealing`, '撤销原因')"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :layout="layout">
        <a-form-model-item :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reasonsRepealing`, '撤销原因')">
          <a-input
            v-model="form.reason" />
        </a-form-model-item>
      </a-form-model>
    </a-modal> 
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import CLContractAdd from './modules/CLContractAdd'
import ClContractEdit from './modules/ClContractEdit'
import ClContractDetail from './modules/ClContractDetail'
import { getAction, postAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        CLContractAdd,
        ClContractDetail,
        ClContractEdit
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            visible: false,
            rowIndex: -1,
            form: {
                reason: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'), icon: 'download', clickFn: this.batchDownload, authorityCode: 'contractLock#signFlow:down'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCWWWyWRLWdDWESAo_950f1e9e`, '(供方ELS号/名称/主题/业务编码)')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractLock#signFlow:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'contractLock#signFlow:edit'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signingInitiation`, '一步签署发起'), clickFn: this.createFlowOneStep, allow: this.allowCreateFlowOneStep, authorityCode: 'contractLock#signFlow:initiate'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMPWOKy_b84d5f41`, '获取签署短链接'), clickFn: this.getShortUrl, allow: this.allowGetShortUrl, authorityCode: 'contractLock#signFlow:shortUrl'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_flowArchiving`, '流程归档'), clickFn: this.archiveFlow, allow: this.allowArchiveFlow, authorityCode: 'contractLock#signFlow:archive'},
                    {type: 'query', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLmh_33814307`, '流程查询'), clickFn: this.queryFlow, allow: this.allowQueryFlow, authorityCode: 'contractLock#signFlow:statusQuery'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_drawBack`, '退回'), clickFn: this.sendBack, allow: this.sendBackFlow, authorityCode: 'contractLock#signFlow:back'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销'), clickFn: this.backout, allow: this.backoutFlow, authorityCode: 'contractLock#signFlow:cancel'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentDownload`, '流程文档下载'), clickFn: this.flowFileDownload, allow: this.showFlowFileDownload, authorityCode: 'contractLock#signFlow:down'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/contractLock/elsClContract/add',
                list: '/contractLock/elsClContract/list',
                delete: '/contractLock/elsClContract/delete',
                createFlowOneStep: '/contractLock/elsClContract/initiate',
                startFlow: '/esign/esignOperation/startFlow',
                archiveFlow: '/contractLock/elsClContract/archive',
                sendBack: '/contractLock/elsClContract/sendBack',
                backout: '/contractLock/elsClContract/backout',
                flowFileDownload: '/contractLock/elsClContract/downloadArchive',
                queryFlow: '/contractLock/elsClContract/flowQuery',
                columns: 'ElsClSignContract'
            }
        }
    },
    methods: {
        batchDownload (){
            let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords() || []
            if(selectedRows.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFTPIKjWF_7090178`, '请选择需要下载的数据'))
                return
            }
            selectedRows.forEach(row=>{
                const tips = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_warmPrompt`, '温馨提示')
                if(row.archiving !== '1'){
                    this.$notification.warn({
                        message: tips,
                        description: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_warmPrompt`, '业务编号:') + row.busNumber + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jtFQLLLAIKKm_9093393e`, '的单据流程未归档，下载失败')
                    })
                }else {
                    this.flowFileDownload(row)
                    this.$notification.success({
                        message: tips,
                        description: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_warmPrompt`, '业务编号:')  + row.busNumber + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IKLAQILR_18887541`, '下载归档文件成功')
                    })
                }

            })
        },
        //已认证不能被编辑
        handleAdd () {
            this.showAddPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        sendBackFlow (row){
            //发起了一步签署，但已撤销
            if(row.sendBack== '1'){
                return true
            }
            if(row.launch==='1' && row.contractStatus=='RECALLED'){
                return false
            }
            //未发起一步签署
            if(row.launch!=='1'){
                return false
            }
            return true
        },
        backoutFlow (row){
            if(row.launch==='1' && row.signStatus!=='2' && row.contractStatus !== 'RECALLED' && row.contractStatus !== 'DRAFT'){
                return false
            }
            return true
        },
        allowDelete (row){
            if(row.uploaded==='1'){
                return true
            }
            return false
        },
        showFlowFileDownload (row){
            //流程已经发起
            if(row.signStatus==='2' && row.archiving == '1'){
                return false
            }
            return true
        },
        allowGetShortUrl (row){
            if(row.launch == '1' && row.documentId && row.signStatus !== '1' && row.contractStatus !== 'RECALLED'){
                return false
            }
            return true
        },
        allowCreateFlowOneStep (row){
            if(row.sendBack == '1'){
                return true
            }
            if((row.launch == '1' && row.contractStatus == 'RECALLED')   || !row.documentId){
                return true
            }
            if(row.contractStatus !== 'DRAFT'){
                return true
            }
            return false
        },
        allowStartFlow (row){
            //手动开启流程并且还未开启并且已经发起一步签署
            if(row.autoInitiate!=='1' && row.initiate!=='1' && row.launch==='1' && row.signStatus==='0'){
                return false
            }
            return true
        },
        allowArchiveFlow (row){
            if(row.signStatus == '2' && row.archiving !== '1'){
                return false
            }
            return true
        },
        allowEdit (row){
            //已退回的单不可编辑
            if(row.sendBack==='1'){
                return true
            }
            //未发起，可编辑
            if(row.launch!=='1'){
                return false
            }
            return true
        },
        getShortUrl (row){
            getAction('/contractLock/elsClContract/getSignPage', {id: row.id}).then(res => {
                if(res.success){
                    alert(res.result)
                }
            })
        },
        createFlowOneStep (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmationStepSignInitiate`, '确认一步签署发起'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherToInitiateAStepSignature`, '是否发起一步签署?'),
                onOk: function () {
                    getAction(that.url.createFlowOneStep, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.launch_dictText = '是'
                            row.contractStatus = 'SIGNING'
                            row.contractStatus_dictText = '签署中'
                            row.launch = '1'
                            row.signStatus = '0'
                            row.signStatus_dictText = '未完成'
                            row.flowId = res.result.flowId
                        }
                    })
                }
            })
        },
        startFlow (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmTheStartOfTheProcess`, '确认流程开启'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherToEnableTheProcess`, '是否确认开启流程?'),
                onOk: function () {
                    getAction(that.url.startFlow, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.initiate_dictText = '是'
                            row.initiate = '1'
                        }
                    })
                }
            })
        },
        flowFileDownload (row){
            getAction(this.url.flowFileDownload, {id: row.id}).then(res => {
                if(res.success){
                    getAction(res.result.remoteFilePath, {}, {
                        responseType: 'blob'
                    }).then(ref => {
                        let url = window.URL.createObjectURL(new Blob([ref]))
                        let link = document.createElement('a')
                        link.style.display = 'none'
                        link.href = url
                        link.setAttribute('download', res.result.documentName)
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link) //下载完成移除元素
                        window.URL.revokeObjectURL(url) //释放掉blob对象
                    })
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        archiveFlow (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmProcessArchiving`, '确认流程归档'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheArchivingProcessIsConfirmed`, '是否确认归档流程?'),
                onOk: function () {
                    getAction(that.url.archiveFlow, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.archiving_dictText = '是'
                            row.archiving= '1'
                        }
                    })
                }
            })
        },
        sendBack (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmBack`, '确认退回'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmReturn`, '是否确认退回'),
                onOk: function () {
                    getAction(that.url.sendBack, {id: row.id}).then(res => {
                        if(res.success){
                            row.sendBack_dictText = '是'
                            row.sendBack= '1'
                            that.$message['success']('退回成功')
                        }else {
                            that.$message['error'](res.message)
                        }
                    })
                }
            })
        },
        backout (row, column, $rowIndex){
            this.rowIndex = $rowIndex
            this.visible = true
        },
        handleOk () {
            let param = this.$refs.listPage.tableData[this.rowIndex]
            if(!this.form.reason){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_qXjWxOLV_2175b28d`, '撤销原因不能为空'))
                return
            }
            getAction(this.url.backout, {id: param.id, reason: this.form.reason}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    param.signStatus_dictText = '已撤销'
                    param.signStatus= '3'
                    param.contractStatus= 'RECALLED'
                    param.contractStatus_dictText = '已撤回'
                }
            })
            this.visible = false
            this.searchEvent()
        },
        allowQueryFlow (row){
            if(row.launch==='1'){
                return false
            }
            return true
        },
        queryFlow (row){
            getAction(this.url.queryFlow, {id: row.id}).then(res => {
                if(res.success){
                    this.$message.info('合同状态：'+res.message)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>