<template>
  <div class="insurance-guarantee-edit">
    <a-spin :spinning="confirmLoading">
      <div class="btns">
        <a-button
          @click="handleSave"
          type="primary"
          style="margin-right: 10px"
          v-if="this.fromSourceData.status != '1' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <a-button
          @click="handleSubmit"
          type="primary"
          style="margin-right: 10px"
          v-if="this.fromSourceData.status != '1' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
        <a-button
          @click="returnPage">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </div>
      <business-layout
        v-if="layoutShow"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :fromSourceData="fromSourceData"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    inject: ['tenderCurrentRow', 'subpackageId', 'resetCurrentSubPackage'],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        } 
    },
    data () {
        return {
            layoutShow: false,
            pageStatus: 'edit',
            confirmLoading: false,
            businessRefName: 'businessRef',
            externalToolBar: {
                attachmentDTOList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'tenderProjectMargin', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: '关联tab',
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            },
            // pageFooterButtons: [
            //     {
            //         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
            //         args: {
            //             url: '/tender/supplier/purchaseTenderProjectMarginHead/addMargin'
            //         },
            //         attrs: {
            //             type: 'primary'
            //         },
            //         click: this.handleSave
            //     },
            //     {
            //         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
            //         args: {
            //             url: '/tender/supplier/purchaseTenderProjectMarginHead/submitMargin'
            //         },
            //         attrs: {
            //             type: 'primary'
            //         },
            //         key: 'publish2',
            //         // showMessage: true
            //         click: this.handleSubmit
            //     },
            //     {
            //         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
            //         key: 'goBack',
            //         click: this.returnPage
            //     }
            // ],
            remoteJsFilePath: '',
            fromSourceData: {},
            url: {
                queryById: '/tender/supplier/purchaseTenderProjectMarginHead/queryItemById',
                queryProjectInfo: '/tender/purchaseTenderProjectHead/queryProjectInfo',
                add: '/tender/supplier/purchaseTenderProjectMarginHead/addMargin',
                edit: '/tender/supplier/purchaseTenderProjectMarginHead/editMargin',
                submit: '/tender/supplier/purchaseTenderProjectMarginHead/submitMargin'
            }
        }
    },
    created () {
        this.init()
    },
    methods: {
        async init () {
            await this.getData()
            await this.getBusinessTemplate()
        },
        attrHandle () {
            return {
                'sourceNumber': this.tenderCurrentRow.tenderProjectNumber || this.tenderCurrentRow.id || '',
                'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'
            }
        },
        uploadCallBack (result) {
            // 插入分包ID分包名称项目ID项目名称
            result.map(item => {
                item['fileType'] = ''
            })
            console.log(this.$refs.businessRef)
            this.$refs.businessRef.$refs.attachmentDTOListgrid[0].$refs.attachmentDTOList.insertAt(result, -1)
        },
        // 获取业务模板信息
        getBusinessTemplate () {
            let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'tenderProjectMargin'}
            this.confirmLoading = true
            if (this.fromSourceData.templateNumber && this.fromSourceData.templateAccount) {
                this.currentEditRow = Object.assign(this.currentEditRow, 
                    {
                        templateNumber: this.fromSourceData.templateNumber,
                        templateName: this.fromSourceData.templateName,
                        templateVersion: this.fromSourceData.templateVersion,
                        templateAccount: this.fromSourceData.templateAccount
                    }
                )
                this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/purchase_tenderProjectMargin_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
            } else{
                return getAction('/template/templateHead/getListByType', params).then(res => {
                    if(res.success) {
                        if(res.result.length > 0) {
                            let options = res.result.map(item => {
                                return {
                                    templateNumber: item.templateNumber,
                                    templateName: item.templateName,
                                    templateVersion: item.templateVersion,
                                    templateAccount: item.elsAccount
                                }
                            })
                            this.currentEditRow = Object.assign(this.currentEditRow, options[0])
                            this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/purchase_tenderProjectMargin_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
                        } else {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                        }
                    }else {
                        this.$message.warning(res.message)
                    }
                    this.confirmLoading = false
                })
            }
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentDTOList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentDTOList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentDTOList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    },
                    {
                        groupCode: 'attachmentDTOList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            // 保存过之后，投标单位名称的值不允许再修改

            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }

            let flag = (this.fromSourceData.status)
            setDisabledByProp('supplierName', flag)


            console.log(pageConfig, resultData)
            if(pageConfig.groups[1].loadData){
                pageConfig.groups[1].loadData.forEach(item=>{
                    if(item.fileType == '5'){
                        item.fileType = ''
                    }
                })
            }   
            // 保函缴纳不用显示这些指定
            const noField = ['guarantor']
            let formFields = []
            pageConfig.groups[0].formFields.forEach(item => { // 因为保函保单和其他方式的缴纳原型不一样对此做数据筛查
                if (noField.indexOf(item.fieldName) == -1) {
                    if (item.fieldName == 'amount') {
                        item.fieldLabel = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HfWjW_4ff93ac2`, '金额（元）')
                    }
                    formFields.push(item)
                }
            })
            pageConfig.groups[0].formFields = formFields
            pageConfig.groups[1]['extend'] = {
                optColumnList: [
                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                    { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                ]
            }

            let that = this
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            // 上传 附件需要 headId
            that.externalToolBar['attachmentDTOList'][0].args.headId = resultData.id || ''
            that.externalToolBar['attachmentDTOList'][0].args.itemInfo = itemInfo
        },
        preViewEvent (Vue, row){
            row.subpackageId = this.subpackageId()
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.subpackageId()
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        checkItemSelectOk (data) {
            console.log(data)
        },
        handleSave () { //保存
            let params = Object.assign(this.currentEditRow, this.$refs[this.businessRefName].extendAllData().allData)
            console.log(params)
            let flag = false
            if(params.attachmentDTOList){
                flag = params.attachmentDTOList.some(item=>item.fileType == '')
            }
            if(flag) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VPQIAcxVMi_ba35f656`, '请将文件类型补充完整'))
                return
            }
            let url = params.id ? this.url.edit : this.url.add
            params['marginCollectionType'] =  '1'
            params.templateNumber = params.templateNumber || this.currentEditRow.templateNumber || ''
            params.templateVersion = params.templateVersion || this.currentEditRow.templateVersion || ''
            params.templateAccount = params.templateAccount || this.currentEditRow.templateAccount || ''
            params.templateName = params.templateName || this.currentEditRow.templateName || ''
            this.confirmLoading = true
            postAction(url, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if(type == 'error') {
                    this.confirmLoading = false
                    return
                }
                if (res.success) {
                    this.currentEditRow.id = res.result.id
                    this.externalToolBar['attachmentDTOList'][0].args.headId = res.result.id || ''
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })

        },
        handleSubmit () { // 提交 
            const pageConfig = this.$refs[this.businessRefName].extendAllData()
            console.log(pageConfig)
            let flag = false
            if(pageConfig.pageConfig.groups[1].loadData){
                flag = pageConfig.pageConfig.groups[1].loadData.some(item=>item.fileType == '')
            }
            if(flag) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VPQIAcxVMi_ba35f656`, '请将文件类型补充完整'))
                return
            }
            // 校验数据
            this.stepValidate(pageConfig).then(res => {
                let params = Object.assign(this.currentEditRow, this.$refs[this.businessRefName].extendAllData().allData)
                let url = params.id ? this.url.edit : this.url.add
                
                params['marginCollectionType'] =  '1'
                this.confirmLoading = true
                
                postAction(url, params).then(res => {
                    let type = res.success ? 'success': 'error'
                    type == 'error' && this.$message[type](res.message)
                    if(type == 'error') {
                        this.confirmLoading = false
                        return
                    }
                    if (res.success) {
                        params['id'] = res.result.id
                        postAction(this.url.submit, params).then(res => {
                            let type = res.success ? 'success': 'error'
                            this.$message[type](res.message)
                            if (res.success) {
                                // this.$emit('resetCurrentSubPackage') || ''
                                this.resetCurrentSubPackage()
                                this.returnPage()
                                // this.$parent.showAddPage = false
                                // this.$store.dispatch('SetTabConfirm', false)
                                // this.$parent.searchEvent(false)
                            }else{
                                this.confirmLoading = false

                            }
                        })
                    }
                }).finally(() => {
                })
            }, error => {
                console.log('最后有一个没有填', error)
            })
        },
        returnPage () { // 返回
            this.$route.meta && (() => {
                this.$route.meta.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHRvJptH_de868af6`, '保证金管理缴纳记录')
                this.$parent.otShowEditPage = false
            })()
        },
        getData () {
            this.layoutShow = false
            let url = ''
            let params = {}
            if (this.currentEditRow.id) {
                url = this.url.queryById
                params = {id: this.currentEditRow.id}
            } else {
                url = this.url.queryProjectInfo
                params = {subpackageId: this.subpackageId()}
            }
            return getAction(url, params).then(res => {
                if (res.success) {
                    if (res.result) {
                        this.fromSourceData = res.result
                        this.$forceUpdate()
                        console.log(this.fromSourceData)
                    }
                }
            }).finally(() => {
                this.layoutShow = true
                this.confirmLoading = false
            })
        }
    }
}
</script>

<style lang="less" scoped>
.insurance-guarantee-edit {
  :deep(.edit-page){
    margin-top: 10px;
  }
  :deep(.page-header ){
    display: none;
  }
}
:deep(.main){
    position:relative
  }
.btns {
    position: absolute;
    right: 15px;
    top: -35px;
}
</style>