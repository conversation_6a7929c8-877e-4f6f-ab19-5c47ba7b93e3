<template>
  <div class="page-container">
    <slot
      name="content"
      :activeKey="activeKey"
      :tabsList="tabsListData"
      :formFields="computedFormFields"
      :columns="tableColumns"
      :tableData="tableData"
      :loadDataFunc="loadData"
      :pagerConfig="tablePage"
      :getNewRouterFunc="getNewRouter"
      :loading="loading"
    >
      <div>
        <slot
          name="tabs"
          :activeKey="activeKey"
          :tabsList="tabsListData"
        >
          <a-tabs
            v-if="tabsListData && tabsListData.length"
            v-model="activeKey"
            class="list-tabs-wrap"
            tab-position="top"
            @change="changeTab"
          >
            <a-tab-pane
              v-for="(pane, index) in tabsListData"
              :key="`${index}`"
            >
              <template #tab>
                <span :style="{ color: index == activeKey ? '#1890ff' : '#B0ADB7' }">{{ pane.title }}</span>
                <span :style="{ color: index == activeKey ? '#1890ff' : '#E96466', paddingLeft: '8px' }">({{ pane.total && pane.total > 99 ? '99+' : pane.total }})</span>
              </template>
            </a-tab-pane>
          </a-tabs>
        </slot>
        <slot
          name="query"
          :formFields="computedFormFields"
        >
          <div class="query">
            <div class="query-wrap">
              <a-form-model
                ref="queryForm"
                :model="form"
                @keyup.enter.native="handleQuery"
                v-bind="layout"
              >
                <a-row
                  type="flex"
                  :gutter="12"
                >
                  <template v-for="(item, i) in computedFormFields">
                    <a-col
                      v-if="i === 2"
                      :key="`field_${i}`"
                      :span="8"
                    >
                      <div class="search-btn-groups">
                        <a-button-group>
                          <a-button
                            type="primary"
                            icon="search"
                            @click="handleQuery"
                            >{{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}</a-button
                          >
                          <a-button
                            @click="superQuery"
                            v-if="pageData.superQueryShow"
                            ><span>{{ $srmI18n(`${$getLangAccount()}#i18n_dict_xt_133ecf`, '高级') }}<a-icon type="double-right" /></span>
                            <span
                              class="search-cout"
                              v-if="superQueryNumber > 0"
                              >{{ superQueryNumber }}</span
                            >
                          </a-button>
                        </a-button-group>

                        <a-button
                          type="default"
                          icon="reload"
                          style="margin-right: 8px"
                          @click="handleReset"
                          >{{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}</a-button
                        >
                        <a-tooltip
                          v-if="!$slots.grid && !$scopedSlots.grid"
                          :title="isAggregate ? $srmI18n(`${$getLangAccount()}#i18n_dict_ULKP_2d2f6d1f`, '平铺视图') : $srmI18n(`${$getLangAccount()}#i18n_dict_FnKP_3ba4e166`, '聚合视图')"
                        >
                          <a-button
                            class="search-btn"
                            type="default"
                            v-if="pageData.showGridLayoutBtn"
                            :icon="isAggregate ? 'fullscreen' : 'fullscreen-exit'"
                            @click="toggleGridLayout"
                          />
                        </a-tooltip>
                        <slot name="query_extra"></slot>
                        <!-- <a
                            v-if="computedFormFields.length > 3"
                            @click="toggleExpand">
                            {{ expand ? $srmI18n(`${$getLangAccount()}#i18n_title_putAway`, '收起') : $srmI18n(`${$getLangAccount()}#i18n_title_open`, '展开') }}
                            <a-icon :type="expand ? 'up' : 'down'" />
                            </a> -->

                        <template v-if="modelLayout === 'seal' && normalBtnList && normalBtnList.length">
                          <template v-for="(sub, Si) in normalBtnList">
                            <template v-if="sub.type !== 'uploadFile'">
                              <a-button
                                v-if="sub.type !== 'check'"
                                :key="`button_${Si}`"
                                v-show="showCustomBtnOpt(sub)"
                                :disabled="sub.allow ? sub.allow() : false"
                                :type="sub.type"
                                :icon="sub.icon"
                                v-debounce="{ method: sub.clickFn, event: 'click' }"
                              >
                                {{ sub.label }}
                              </a-button>
                              <a-button
                                v-else-if="sub.type === 'check'"
                                :key="`button_${Si}`"
                                :type="sub.type"
                                :disabled="(sub.disabled && sub.disabled(sub)) || false"
                                @click="checkedGridSelect(sub, 'listGrid', sub.beforeCheckedCallBack)"
                              >
                                {{ sub.label }}
                              </a-button>
                            </template>
                            <template v-else>
                              <custom-upload
                                isList
                                :disabled="sub.allow ? sub.allow() : false"
                                :key="`button_${Si}`"
                                :single="sub.single"
                                :disabledItemNumber="sub.disabledItemNumber"
                                :requiredFileType="sub.requiredFileType"
                                :property="sub.property"
                                :visible.sync="sub.modalVisible"
                                :title="sub.title"
                                :itemInfo="itemInfo"
                                :action="sub.url || uploadUrl"
                                :dictCode="sub.dictCode || ''"
                                :accept="accept"
                                :headers="tokenHeader"
                                :data="{ businessType: sub.businessType, headId: sub.id }"
                                @change="(info) => handleUploadChange(info, sub, 'listGrid')"
                              >
                                <a-button
                                  v-if="sub.beforeChecked"
                                  type="primary"
                                  icon="cloud-upload"
                                  @click="checkedGridSelect(sub, 'listGrid', sub.beforeCheckedCallBack)"
                                  >{{ sub.title }}</a-button
                                >
                              </custom-upload>
                            </template>
                          </template>
                        </template>
                      </div>
                    </a-col>
                    <a-col
                      v-else
                      :key="`field_${i}`"
                      :span="8"
                    >
                      <template v-if="item.type">
                        <a-form-model-item
                          v-show="i < 2 ? true : expand"
                          :prop="item.fieldName"
                        >
                          <template #label>
                            <a-tooltip
                              placement="bottomLeft"
                              :title="item.label"
                            >
                              {{ item.label }}
                            </a-tooltip>
                          </template>
                          <template v-if="item.type === 'select'">
                            <m-select
                              v-model="pageData.form[item.fieldName]"
                              :dict-code="item.dictCode"
                              :placeholder="item.placeholder"
                            />
                          </template>

                          <template v-else-if="item.type === 'datepicker'">
                            <a-date-picker
                              v-model="pageData.form[item.fieldName]"
                              :placeholder="item.placeholder"
                              :valueFormat="item.dataFormat || 'YYYY-MM-DD'"
                            />
                          </template>

                          <!-- 时间范围 -->
                          <template v-else-if="item.type === 'dateRange'">
                            <a-range-picker
                              :value="[pageData.form[item.startProp || 'beginTime'], pageData.form[item.endProp || 'endTime']]"
                              :placeholder="item.placeholder"
                              :valueFormat="item.dataFormat || 'YYYY-MM-DD'"
                              @change="(dates) => handleDateRangePickerChange(dates, item)"
                            />
                          </template>

                          <template v-else-if="item.type === 'treeSelect'">
                            <m-tree-select
                              v-model="pageData.form[item.fieldName]"
                              allowClear
                              :disabled="item.disabled"
                              :multiple="(item.extend && item.extend.multiple) || false"
                              :maxTagCount="(item.extend && item.extend.maxTagCount) || 1"
                              :sourceUrl="item.sourceUrl"
                            />
                          </template>

                          <template v-else-if="item.type === 'radio'">
                            <m-radio
                              v-model="pageData.form[item.fieldName]"
                              :dictCode="item.dictCode"
                            />
                          </template>

                          <template v-else-if="item.type === 'checkbox'">
                            <m-checkbox
                              v-model="pageData.form[item.fieldName]"
                              :options="item.options || []"
                              :dictCode="item.dictCode"
                            />
                          </template>

                          <template v-else>
                            <a-input
                              v-model="pageData.form[item.fieldName]"
                              allow-clear
                              @change="onChange($event, item)"
                              :placeholder="item.placeholder"
                            >
                              <a-icon
                                type="audio"
                                slot="suffix"
                                v-show="item.isAudio"
                                style="color: rgba(0, 0, 0, 0.45)"
                                @click="handleAudio(item.fieldName)"
                              />
                            </a-input>
                          </template>
                        </a-form-model-item>
                      </template>
                    </a-col>
                  </template>
                </a-row>
              </a-form-model>
              <search-extend
                :fieldList="fieldList"
                ref="superQueryModal"
                :tableCode="url.columns"
                @handleSuperQuery="handleSuperQuery"
                @synQueryParams="synQueryParams"
                @queryParamsModelLength="queryParamsModelLength"
              ></search-extend>
            </div>
          </div>
        </slot>
      </div>
      <slot
        name="grid"
        :columns="tableColumns"
        :tableData="tableData"
        :loadDataFunc="loadData"
        :pagerConfig="tablePage"
        :activeKey="activeKey"
        :tabsList="tabsListData"
        :formFields="computedFormFields"
        :getNewRouterFunc="getNewRouter"
        :loading="loading"
      >
        <div
          class="grid-box"
          :class="{ 'reset-grid-box': tabsList && tabsList.length }"
        >
          <vxe-grid
            v-if="modelLayout === 'list'"
            row-id="id"
            ref="listGrid"
            v-bind="gridConfig"
            :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
            :loading="loading"
            :columns="isAggregate ? groupColumns : tableColumns"
            :data="tableData"
            :border="!isAggregate"
            :pager-config="tablePage"
            :edit-config="editConfig"
            :edit-rules="editRules"
            :filter-config="filterConfig"
            :row-style="rowStyle"
            :cell-class-name="isAggregate ? 'aggregateStyle' : ''"
            :row-class-name="setRowClass"
            @sort-change="sortChangeEvent"
            @filter-change="filterChangeEvent"
            @resizable-change="resizableChange"
            @page-change="handlePageChange"
            @checkbox-change="handleCheckboxChange"
            @checkbox-all="handleCheckboxChange"
            @cell-click="cellClickEvent"
          >
            <template slot="empty">
              <a-empty />
            </template>
            <!-- 列 - 操作列seq_header-->
            <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
              <template v-for="(item, i) in optColumnList">
                <a
                  :key="'opt_' + row.id + '_' + i"
                  v-if="item.allow ? !item.allow(row) : true"
                  :title="item.title"
                  style="margin: 0 4px"
                  v-show="showCustomBtnOpt(item, row)"
                  @click="toolbarButtonsCallBack(row, column, $rowIndex, $columnIndex, item.clickFn)"
                >
                  {{ item.title }}
                </a>
                <a
                  :key="'opt_' + row.id + '_' + i"
                  v-else
                  :title="item.title"
                  style="margin: 0 4px"
                  :disabled="item.allow ? item.allow(row) : false"
                  v-show="showCustomBtnOpt(item, row)"
                >
                  {{ item.title }}
                </a>
              </template>

              <a-dropdown v-if="optColumnListMore.length > 0">
                <a
                  class="ant-dropdown-link"
                  @click="(e) => e.preventDefault()"
                >
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_more`, '更多') }}
                  <a-icon type="down" />
                </a>
                <a-menu slot="overlay">
                  <a-menu-item
                    v-show="showCustomBtnOpt(itemMore, row)"
                    :key="'opt_' + row.id + '_' + more_index"
                    v-for="(itemMore, more_index) in optColumnListMore"
                  >
                    <a-button
                      type="link"
                      :title="itemMore.title"
                      style="margin: 0 4px"
                      :disabled="itemMore.allow ? itemMore.allow(row) : false"
                      @click="toolbarButtonsCallBack(row, column, $rowIndex, $columnIndex, itemMore.clickFn)"
                    >
                      {{ itemMore.title }}
                    </a-button>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
            <!-- 左上角按钮行 -->
            <template #toolbar_buttons>
              <template v-for="(item, i) in normalBtnList">
                <template v-if="item.type !== 'uploadFile'">
                  <a-select
                    v-if="item.type == 'MASelect'"
                    v-model="item.values"
                    :key="i"
                    @change="changeAselect"
                    placeholder="请选择"
                    style="marginright: 6px; marginbottom: 4px; width: 80px"
                  >
                    <a-select-option
                      v-for="(option, keys) in item.dropdowns"
                      :key="'option_' + keys"
                      :value="option.code"
                    >
                      {{ option.name }}
                    </a-select-option>
                  </a-select>
                  <a-button
                    v-if="item.type !== 'check' && item.type !== 'MASelect'"
                    :key="`button_${i}`"
                    v-show="showCustomBtnOpt(item)"
                    :disabled="item.allow ? item.allow() : false"
                    :type="item.type"
                    :icon="item.icon"
                    v-debounce="{ method: item.clickFn, event: 'click' }"
                  >
                    {{ item.label }}
                  </a-button>
                  <a-button
                    v-else-if="item.type === 'check'"
                    :type="item.type"
                    :key="`button_${i}`"
                    :disabled="(item.disabled && item.disabled(item)) || false"
                    @click="checkedGridSelect(item, 'listGrid', item.beforeCheckedCallBack)"
                  >
                    {{ item.label }}
                  </a-button>
                </template>
                <template v-else>
                  <custom-upload
                    isList
                    :disabled="item.allow ? item.allow() : false"
                    :key="`button_${i}`"
                    :single="item.single"
                    :disabledItemNumber="item.disabledItemNumber"
                    :requiredFileType="item.requiredFileType"
                    :property="item.property"
                    :visible.sync="item.modalVisible"
                    :title="item.title"
                    :itemInfo="itemInfo"
                    :action="item.url || uploadUrl"
                    :dictCode="item.dictCode || ''"
                    :accept="accept"
                    :headers="tokenHeader"
                    :data="{ businessType: item.businessType, headId: item.id }"
                    @change="(info) => handleUploadChange(info, item, 'listGrid')"
                  >
                    <a-button
                      v-if="item.beforeChecked"
                      type="primary"
                      icon="cloud-upload"
                      @click="checkedGridSelect(item, 'listGrid', item.beforeCheckedCallBack)"
                      >{{ item.title }}</a-button
                    >
                  </custom-upload>
                </template>
              </template>

              <a-dropdown v-if="foldBtnList.length">
                <a-button icon="more">{{ $srmI18n(`${$getLangAccount()}#i18n_title_more`, '更多') }}</a-button>
                <a-menu slot="overlay">
                  <a-menu-item
                    v-for="(item, i) in foldBtnList"
                    :key="'menu_button_' + i"
                  >
                    <a-upload
                      v-if="item.type === 'upload'"
                      name="file"
                      :show-upload-list="false"
                      :multiple="false"
                      :headers="tokenHeader"
                      :action="importExcelUrl"
                      @change="handleImportExcel"
                    >
                      <a-icon
                        :type="item.icon"
                        style="margin-right: 10px"
                      ></a-icon>
                      {{ $srmI18n(`${$getLangAccount()}#i18n_title_Improt`, '导入') }}
                    </a-upload>
                    <div
                      v-else
                      @click="item.clickFn"
                    >
                      <a-icon
                        :type="item.icon"
                        style="margin-right: 10px"
                      ></a-icon
                      >{{ item.label }}
                    </div>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
            <template #custom_render="{ row, column }">
              <slot
                name="custom_column_render"
                :row="row"
                :column="column"
              />
            </template>
            <!-- 富文本编辑器插槽 -->
            <template #rich_editor_dispaly_render="{ row, column }">
              <Popover
                :content="row[column.property]"
                class="rich-editor-display-box"
              />
            </template>
            <!-- 阶梯价格 -->
            <template #ladder_price_json_render="{ row, column }">
              <a-tooltip
                placement="top"
                v-if="row[column.property]"
                overlayClassName="tip-overlay-class"
              >
                <template slot="title">
                  <vxe-table
                    auto-resize
                    border
                    row-id="id"
                    size="mini"
                    :data="initRowLadderJson(row[column.property])"
                  >
                    <vxe-table-column
                      type="seq"
                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_alert_seq`, '序号')}`"
                      width="80"
                    ></vxe-table-column>
                    <vxe-table-column
                      field="ladder"
                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')}`"
                      width="140"
                    ></vxe-table-column>
                    <vxe-table-column
                      field="price"
                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_price`, '含税价')}`"
                      width="140"
                    ></vxe-table-column>
                    <vxe-table-column
                      field="netPrice"
                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_netPrice`, '不含税价')}`"
                      width="140"
                    ></vxe-table-column>
                  </vxe-table>
                </template>
                <div class="json-box">
                  <a href="javascript: void(0)">{{ defaultRowLadderJson(row[column.property]) }}</a>
                </div>
              </a-tooltip>
            </template>
            <!-- 页码左边渲染 -->
            <template #pagerLeftRender>
              <div>已选择{{ checkNumber }}条</div>
            </template>
          </vxe-grid>
          <div
            class="card-list"
            v-if="modelLayout === 'seal'"
          >
            <a-row
              class="card-row-box"
              type="flex"
              v-if="tableData && tableData.length"
            >
              <a-col
                :span="8"
                v-for="(el, index) in tableData"
                :key="'card-list' + index"
              >
                <div class="card-box">
                  <a-row
                    type="flex"
                    justify="center"
                    :gutter="16"
                    class="item-info"
                  >
                    <a-col :span="12">
                      <div class="item-img">
                        <preview-img
                          v-if="el.filePath"
                          :src="el.filePath"
                          :styleOption="{ height: '160px', margin: '0 auto', width: '160px' }"
                        />
                      </div>
                    </a-col>
                    <a-col :span="12">
                      <div class="item-text">
                        <div class="title alias">{{ el.alias || '' }}</div>
                        <div
                          class="title"
                          v-if="!el.busAccount"
                        >
                          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_nRCELSey_feb832ca`, '采购方ELS账号') }}：</span><span class="color-gary">{{ el.elsAccount || '' }}</span>
                        </div>
                        <div
                          class="title"
                          v-else
                        >
                          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_RdXELSey_86ca730`, '供应商ELS账号') }}：</span><span class="color-gary">{{ el.elsAccount || '' }}</span>
                        </div>
                        <div class="title">
                          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_companyName`, '公司名称') }}：</span><span class="color-gary">{{ el.companyName || el.orgName || '' }}</span>
                        </div>
                        <div class="title">
                          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_WeqR_27c540b2`, '印章别名') }}：</span><span class="color-gary">{{ el.sealName || '' }}</span>
                        </div>
                        <div class="title">
                          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_UnzE_2b3c7302`, '审核状态') }}：</span><span class="color-gary">{{ el.audit_dictText || el.sealStatus_dictText || '否' }}</span>
                        </div>
                      </div>
                    </a-col>
                  </a-row>
                  <div class="opt-button-list">
                    <a-row
                      type="flex"
                      justify="space-around"
                      :gutter="10"
                    >
                      <a-col
                        :span="4"
                        v-for="(item, i) in sealOptList"
                        :key="'opt_' + el.id + '_' + i"
                        :style="{ 'text-align': 'center' }"
                      >
                        <a-button
                          :class="{ 'item-button': item.allow ? !item.allow(el) : true }"
                          type="link"
                          :title="item.title"
                          :disabled="item.allow ? item.allow(el) : false"
                          @click="toolbarButtonsCallBack(el, {}, index, '', item.clickFn)"
                        >
                          {{ item.title }}
                        </a-button>
                      </a-col>
                    </a-row>
                  </div>
                </div>
              </a-col>
            </a-row>
            <div
              v-else
              class="card-none"
            >
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据') }}
            </div>
            <div
              class="pagination-box"
              v-if="tableData.length > 0"
            >
              <div class="vxe-pagin-new">
                <vxe-pager
                  align="right"
                  :current-page.sync="tablePage.currentPage"
                  :page-size.sync="tablePage.pageSize"
                  @page-change="
                    () => {
                      loadData()
                    }
                  "
                  :total="tablePage.total"
                >
                </vxe-pager>
              </div>
            </div>
          </div>
        </div>
      </slot>
    </slot>
    <column-setting ref="columnSettingModal"></column-setting>
    <a-modal
      v-drag
      centered
      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')}`"
      :width="360"
      v-model="visible"
      @ok="selectedTemplate"
    >
      <template slot="footer">
        <a-button
          key="back"
          @click="handleCancel"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedTemplate"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplateType`, '请选择模板')"
      />
    </a-modal>
    <import-excel
      ref="importExcel"
      :excelCode="url.excelCode"
    />
    <help-modal
      v-model="helpVisible"
      :helpDesc="helpDesc"
    />
    <record-modal
      v-model="recordVisible"
      v-if="recordVisible"
      :currentEditRow="currentEditRow"
    />
  </div>
</template>
<script lang="jsx">
import { postDownFile, getAction, postAction } from '@/api/manage'
import { ajaxFindDictItems, ajaxGetColumns } from '@/api/api'
import { ACCESS_TOKEN, DEFAULT_COLOR, DEFAULT_LANG, USER_ELS_ACCOUNT } from '@/store/mutation-types'
import Sortable from 'sortablejs'
import columnSetting from '@comp/template/columnSetting'
import { ListConfig } from '@/plugins/table/gridConfig'
import ImportExcel from '@comp/template/import/ImportExcel'
import { mapState } from 'vuex'
import SearchExtend from '@/components/template/list/components/searchExtend.vue'
import PreviewImg from '@/views/srm/mall/purchaseProduct/template/PreviewImg'
import CustomUpload from '@comp/template/CustomUpload'
import RecordModal from './components/RecordModal'
import HelpModal from './components/HelpModal'
import { getLangAccount, srmI18n } from '@/utils/util'
import { cloneDeep, uniqBy } from 'lodash'
import { currency, formatFloat } from '@/filters'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import { formatDate } from '@/utils/util.js'
import eventBus from '@/utils/eventBus.js'
import { Base64 } from 'js-base64'

export default {
  name: 'ListLayout',
  props: {
    pageData: {
      //页面数据
      type: Object,
      default() {
        return {}
      }
    },
    // 模式布局方式： list和 seal
    modelLayout: {
      type: String,
      default: 'list'
    },
    url: {
      //后台接口
      type: Object,
      default() {
        return {}
      }
    },
    tableCode: {
      type: String,
      default: ''
    },
    extraListConfig: {
      type: Object,
      default() {}
    },
    tabsList: {
      type: Array,
      default() {
        return []
      }
    },
    activeMethod: {
      type: Function,
      default: () => true
    },
    currentEditRow: {
      type: Object,
      default() {
        return {}
      }
    },
    customReset: {
      type: Boolean,
      default: false
    },
    customDict: {
      type: Boolean,
      default: () => false
    },
    // rowStyle: 用于设置行样式
    rowStyle: {
      type: Function,
      default: null
    },
    tabCurrent: {
      type: [Number, String],
      default: 0
    }
  },
  components: {
    PreviewImg,
    columnSetting,
    ImportExcel,
    // JSuperQuery,
    SearchExtend,
    CustomUpload,
    RecordModal,
    HelpModal,
    Popover: () => import('@comp/Popover/index.vue')
  },
  data() {
    return {
      checkNumber: 0,
      superQueryNumber: 0,
      activeKey: 0,
      helpVisible: false,
      helpDesc: '',
      recordVisible: false,
      localLinkFilterParams: {},
      form: {},
      fieldList: [],
      superQueryFlag: false,
      superQueryParams: '',
      superQueryMatchType: '',
      // gridConfig: ListConfig,
      expand: false,
      visible: false,
      submitLoading: false,
      layout: {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 }
      },
      loading: false,
      tokenHeader: { 'X-Access-Token': this.$ls.get(ACCESS_TOKEN) },
      accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
      uploadUrl: '/attachment/purchaseAttachment/upload',
      filter: {},
      isOrder: {
        column: 'id',
        order: 'desc'
      },
      tableColumns: [],
      tableData: [],
      toolbarConfig: {
        slots: { buttons: 'toolbar_buttons' },
        // import: true,
        // export: true,
        print: true,
        zoom: true,
        // custom: true,
        perfect: true
      },
      tablePage: {
        total: 0,
        pages: 0,
        currentPage: 1,
        pageSize: 500,
        align: 'right',
        pageSizes: [20, 50, 100, 200, 500],
        slots: {
          left: 'pagerLeftRender'
        },
        layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
        perfect: true
      },
      filterConfig: {
        remote: true
      },
      templateNumber: undefined,
      templateOpts: [],
      editRules: {},
      editConfig: {
        trigger: 'click',
        mode: 'cell',
        showStatus: true,
        icon: 'fa fa-file-text-o',
        activeMethod: this.activeMethod
      },
      isAggregate: false, // 是否聚合视图
      groupColumns: [],
      groupDataSub: {},
      tableGroupData: [],
      storeListGridPosition: 0
    }
  },
  watch: {
    tabCurrent(val) {
      this.activeKey = Number(val) || 0
    }
  },
  computed: {
    ...mapState({
      // 后台菜单
      permissionMenuList: (state) => state.user.permissionList
    }),
    // 计算适配搜索框内容
    // 不足时填满三列
    computedFormFields() {
      let formField = this.pageData.formField || []
      for (const field of formField) {
        field.isAudio = true
      }
      let arr = cloneDeep(formField)
      let length = formField.length
      let fllArr = []
      if (length < 3) {
        fllArr = new Array(3 - length).fill({})
        arr = arr.concat(fllArr)
      } else {
        arr.splice(2, 0, {})
      }
      return arr
    },
    //自定也列表配置
    gridConfig() {
      return {
        printConfig: {
          dataFilterMethod: this.dataFilterMethod,
          beforePrintMethod: this.beforePrintMethod
        },
        ...ListConfig,
        ...{ showOverflow: !this.isAggregate },
        ...this.extraListConfig
      }
    },
    // 获取项目的主题色
    defaultColor() {
      return this.$ls.get(DEFAULT_COLOR)
    },
    tabsListData() {
      return this.tabsList || []
    },
    normalBtnList() {
      let btns = this.getAuthCodeBtns(this.pageData.button)
      return btns.filter((item) => {
        return !item.folded
      })
    },
    foldBtnList() {
      let pageDocuments = this.$store.state.user.pageDocuments
      let routerPath = this.$route.path
      let button = this.pageData.button.concat(this.pageData.defaultButton)
      let btns = this.getAuthCodeBtns(button)
      return btns.filter((item) => {
        let isDoc = true
        let isShow = true
        if (item.isDocument) {
          pageDocuments.forEach((doc) => {
            if (doc.path === routerPath && doc.hasDoc !== '1') {
              isDoc = false
            }
          })
        }
        if (item.allow) {
          isShow = !item.allow()
          // console.log(item, isShow)
        }
        if (item.showCondition) {
          isShow = item.showCondition(item)
        }
        return isDoc && isShow && item.folded
      })
    },
    importExcelUrl: function () {
      return `${this.$variateConfig['domainURL']}/${this.url.importExcelUrl}`
    },
    itemInfo() {
      let itemInfo = []
      const groups = this.pageData.groups || []
      const group = groups.find((n) => n.groupCode === 'itemInfo')
      if (group) {
        const refName = group.custom.ref
        itemInfo = this.$refs[refName][0].getTableData().fullData || []
      }
      return itemInfo
    },
    // 操作列按钮
    optColumnList() {
      let btns = this.getAuthCodeBtns(this.pageData.optColumnList)
      let newArr = [...btns]
      return newArr.length > 4 ? newArr.slice(0, 3) : newArr
    },
    // 操作列按钮（更多）
    optColumnListMore() {
      let btns = this.getAuthCodeBtns(this.pageData.optColumnList)
      let newArr = [...btns]
      return newArr.length > 4 ? newArr.slice(3) : []
    },
    // 印章列表操作按钮
    sealOptList() {
      let btns = this.getAuthCodeBtns(this.pageData.optColumnList)
      let newArr = [...btns]
      return newArr
    }
  },
  beforeDestroy() {
    if (this.sortable) {
      this.sortable.destroy()
    }

    eventBus.offEvent('reset', this.handleReset)
    this.$store.dispatch('SetTabCloseStatus', false) // 重置tag关闭标识
  },
  created() {
    this.initColumns()
    if (!this.$route.query.linkFilter) {
      this.loadData()
    }
    this.columnDrop()

    eventBus.onEvent('reset', this.handleReset)
  },
  methods: {
    setRowClass({ row, rowIndex }) {
      // console.log(this.url?.columns, row)
      if (this.url?.columns == 'PurchaseVoucherItem' && row.showEditStatus == 1) return 'rowHighLight'
      return ''
    },

    handleCheckboxChange({ records, reserves }) {
      const reservesNum = reserves.length ?? 0
      this.checkNumber = records.length + reservesNum
    },
    queryParamsModelLength(value) {
      this.superQueryNumber = value
    },
    handleEmitReset() {
      this.checkNumber = 0
      // 先清空better-scroll 卡片布局页面数据
      eventBus.emitEvent('scrollLayoutReset')
    },
    handleDateRangePickerChange(dates, item) {
      console.log('dates :>> ', dates)
      let beginTime = item.startProp || 'beginTime'
      let endTime = item.endProp || 'endTime'
      this.pageData.form[beginTime] = dates[0] || ''
      this.pageData.form[endTime] = dates[1] || ''
    },
    //aSelect
    changeAselect(val) {
      this.$emit('changeAselects', val)
    },
    // 获取非权限码按钮组
    getAuthCodeBtns(btns) {
      let authBtns = []
      if (btns && btns.length) {
        btns.forEach((item) => {
          // 配置authorityCode做权限控制
          if (item && item.authorityCode) {
            // 有权限
            if (this.$hasOptAuth(item.authorityCode)) {
              authBtns.push(item)
            }
          } else {
            // 不配置authorityCode就不做权限控制
            authBtns.push(item)
          }
        })
      }
      return authBtns
    },
    cellClickEvent({ row, rowIndex, column, columnIndex }) {
      this.$emit('cell-click', { row, rowIndex, column, columnIndex })
    },
    toggleGridLayout() {
      if (!this.groupColumns.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_AzVLVWVKAJIIvER_8e767c83`, '列分组为空，请在列自定义里配置'))
        return
      }
      this.isAggregate = !this.isAggregate
    },
    // 关联field
    relationFieldChange({ realValue, fields, relationData, formModel }) {
      let getFieldArr = fields.filter((item) => {
        return item.fieldName === relationData.fieldName
      })
      if (getFieldArr && getFieldArr.length) {
        let relationField = getFieldArr[0]
        // 清空关联项
        formModel[relationField.fieldName] = ''
        if (realValue) {
          relationField.dictCode = 'message_' + realValue
        } else {
          relationField.dictCode = relationField.sourceDictCode
        }
      }
    },
    // 打印前过滤,每次一行数据过滤
    dataFilterMethod(tableOpt) {
      let { row } = tableOpt
      let listGrid = this.$refs.listGrid
      if (listGrid && listGrid.getCheckboxRecords() && listGrid.getCheckboxRecords().length) {
        let flag = false
        let selectRows = listGrid.getCheckboxRecords()
        selectRows.forEach((item) => {
          if (item.id === row.id) {
            flag = true
          }
        })
        return flag
      } else {
        return true
      }
    },
    showHelpPDF() {
      const url = '/account/permission/queryByUrl'
      const params = {
        url: this.$route.fullPath || ''
      }
      getAction(url, params).then((res) => {
        if (res.success == true) {
          const filePath = res.result.filePath || ''
          if (filePath) {
            window.open(process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(filePath))}&reDownload=true`)
            // window.open(filePath)
          } else {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noSetHelpAttachment`, '没有配置说明附件'))
          }
        } else {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noSetHelpAttachment`, '没有配置说明附件'))
        }
      })
    },
    showHelpVideo() {
      const url = '/account/permission/queryByUrl'
      const params = {
        url: this.$route.fullPath || ''
      }
      getAction(url, params).then((res) => {
        if (res.success == true) {
          const videoPath = res.result.videoPath || ''
          if (videoPath) {
            this.$previewFile.open({ params: '', path: videoPath })
          } else {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_BjERKNlR_837c12e`, '没有配置视频说明'))
          }
        } else {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_BjERKNlR_837c12e`, '没有配置视频说明'))
        }
      })
    },
    showHelpText() {
      const url = '/account/permission/queryByUrl'
      const params = {
        url: this.$route.fullPath || ''
      }
      getAction(url, params).then((res) => {
        if (res.success == true) {
          this.helpDesc = res.result.helpDesc || ''
          if (this.helpDesc) {
            this.helpVisible = true
          } else {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noSetHelpWord`, '没有配置帮助说明'))
          }
        } else {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noSetHelpWord`, '没有配置帮助说明'))
        }
      })
    },
    showRecordModal() {
      this.recordVisible = true
    },
    //附件上传
    handleUploadChange(info, btn, refName) {
      btn.callBack && btn.callBack(info, refName)
    },
    checkedGridSelect(btn, refName, cb) {
      let selectData = null
      let that = this
      // console.log(this.$refs[refName])
      if (this.$refs[refName] && this.$refs[refName][0]) {
        selectData = this.$refs[refName][0].getCheckboxRecords() || this.$refs[refName][0].getRadioRecord()
      } else if (this.$refs[refName]) {
        selectData = this.$refs[refName].getCheckboxRecords() || this.$refs[refName].getRadioRecord()
      }
      if (selectData && selectData.length) {
        if (cb && typeof cb === 'function') {
          if (btn.key !== 'batchDownload') {
            cb(selectData, that).then((res) => {
              if (res) {
                btn.modalVisible = true
              }
            })
          } else {
            cb(selectData, that)
          }
        } else {
          this.modalVisible = true
        }
      } else {
        if (btn.msgType === 'batchDownload') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRiTIKBIcW_10289077`, '请勾选需下载附件行！'))
        } else {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterChoose`, '请先选择'))
        }
      }
    },
    superQuery() {
      let flag = !this.$refs.superQueryModal.visible
      this.$refs.superQueryModal.show(flag)
    },
    handleSuperQuery(arg, superQueryMatchType) {
      this.superQueryParams = ''
      if (arg) {
        this.superQueryParams = JSON.stringify(arg)
      }
      // 清空列表勾选
      if (this.$refs.listGrid && this.$refs.listGrid.clearCheckboxReserve) {
        this.$refs.listGrid.clearCheckboxReserve()
      }
      if (this.$refs.listGrid && this.$refs.listGrid.clearCheckboxRow) {
        this.$refs.listGrid.clearCheckboxRow()
      }
      this.superQueryMatchType = superQueryMatchType
      this.tablePage.currentPage = 1

      this.handleEmitReset()

      this.loadData()
    },
    // 同步查询条件，防止高级查询时条件不同步
    synQueryParams(arg, superQueryMatchType) {
      this.superQueryParams = ''
      if (arg) {
        this.superQueryParams = JSON.stringify(arg)
      }
      this.superQueryMatchType = superQueryMatchType
    },
    // 导出
    handleExportXls(fileName, exportType, templateId, templateNumberVersion, itemGroupCode, jsonParam, defineColumnCode, exportXlsUrl) {
      this.loading = true
      fileName = fileName || this.$srmI18n(`${this.$getLangAccount()}#i18n_title_exportFile`, '导出文件')
      let record = this.$refs.listGrid.getCheckboxRecords() || []
      const records = record.filter((item) => item.id && item.id.indexOf('row_') === -1)
      let paramEntity = Object.assign({}, this.pageData.form)
      if (records.length > 0) {
        paramEntity['selections'] = records.map((n) => n.id)
      }
      let params = Object.assign({}, this.pageData.form)
      if (defineColumnCode) {
        this.$set(params, 'defineColumnCode', defineColumnCode)
      } else if (this.url.columns) {
        this.$set(params, 'defineColumnCode', this.url.columns)
      }
      let isOrder = this.isOrder || this.pageData.isOrder
      this.$set(params, 'column', isOrder.column)
      this.$set(params, 'order', isOrder.order)
      this.$set(params, 'fileName', fileName)
      // 针对采购申请试一下exportType head_item,templateId
      this.$set(params, 'exportType', exportType)
      this.$set(params, 'templateId', templateId)
      this.$set(params, 'templateNumberVersion', templateNumberVersion)
      this.$set(params, 'itemGroupCode', itemGroupCode)
      let _this = this
      if (jsonParam) {
        jsonParam.forEach((item) => {
          _this.$set(params, item.key, item.value)
          _this.$set(paramEntity, item.key, item.value)
        })
      }
      postDownFile(exportXlsUrl ? exportXlsUrl : this.url.exportXlsUrl, params, paramEntity)
        .then((data) => {
          // responseType为blob的请求，统一获取错误信息
          if (data.type === 'application/json') {
            const fileReader = new FileReader()
            fileReader.onloadend = () => {
              const jsonData = JSON.parse(fileReader.result)
              this.$message.error(jsonData.message)
            }
            fileReader.readAsText(data)
            return
          }
          if (!data) {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data]), fileName + '.xls')
          } else {
            let url = window.URL.createObjectURL(new Blob([data]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', fileName + '.xls')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(url) //释放掉blob对象
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /* 导入 */
    handleImportExcel(info) {
      // if (info.file.status !== 'uploading') {
      //     console.log(info.file, info.fileList)
      // }
      if (info.file.status === 'done') {
        if (info.file.response.success) {
          if (info.file.response.code === 201) {
            let {
              message,
              result: { msg, fileUrl, fileName }
            } = info.file.response
            let href = this.$variateConfig['domainURL'] + fileUrl
            this.$warning({
              title: message,
              content: (
                <div>
                  <span>{msg}</span>
                  <br />
                  <span>
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_detailContent`, '具体详情请')}
                    <a
                      href={href}
                      target='_blank'
                      download={fileName}
                    >
                      {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '点击下载')}
                    </a>
                  </span>
                </div>
              )
            })
          } else {
            let message = info.file.response.message || this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadSuccess`, '文件上传成功')
            this.$message.success(message)
          }
          this.loadData()
        } else {
          this.$message.error(`${info.file.name} ${info.file.response.message}.`)
        }
      } else if (info.file.status === 'error') {
        this.$message.error(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadError`, '文件上传失败')} : ${info.file.msg}`)
      }
    },
    queryTemplateList(elsAccount) {
      let params = { elsAccount: elsAccount, businessType: this.pageData.businessType }
      return getAction('/template/templateHead/getListByType', params)
    },
    //获取数据
    loadData(tabData, linkFilterParams) {
      this.loading = true
      let form = this.pageData.form
      let { query } = this.$route
      if (tabData) {
        this.isTableData = true
      }
      if (tabData && !tabData.hasOwnProperty('status')) {
        // tab是全部的时候，如果没有status ,则清除
        form.status && delete form.status
      }
      let isOrder = this.pageData.isOrder || this.isOrder
      //不为id，则说明点了页面的排序
      if (this.isOrder.column !== 'id') {
        isOrder = this.isOrder
      }
      let params = Object.assign(
        form,
        {
          pageSize: this.tablePage.pageSize,
          pageNo: this.tablePage.currentPage,
          filter: this.filter,
          dataSource: this.dataSource
        },
        isOrder
      )

      // 搜索时取一次高级查询条件参数数据
      if (this.$refs.superQueryModal) {
        this.$refs.superQueryModal.emitSearchParams()
      }
      if (this.superQueryParams) {
        params['superQueryParams'] = encodeURI(this.superQueryParams)
        params['superQueryMatchType'] = encodeURI(this.superQueryMatchType)
      } else {
        params['superQueryParams'] = null
        params['superQueryMatchType'] = null
      }

      if (this.$route.query.toElsAccount) {
        params.toElsAccount = this.$route.query.toElsAccount
      } else {
        params.toElsAccount && delete params.toElsAccount
      }
      if (linkFilterParams) {
        this.localLinkFilterParams = linkFilterParams
        params = Object.assign({}, params, linkFilterParams)
      }
      // 首页，待办业务跳转
      if (query && query.toList && !this.isTableData) {
        params = Object.assign({}, params, query)
        setTimeout(() => {
          if (this.tabsListData.length > 0) {
            for (let [i, v] of this.tabsListData.entries()) {
              if (v[v.proName] == query[this.tabsListData[0].proName]) {
                this.activeKey = i
              }
            }
          }
        }, 2000)

        params.toList && delete params.toList
      }
      if (tabData) {
        for (let key in tabData) {
          if (key !== 'title') {
            params[key] = tabData[key]
          }
        }
      }

      let url = this.url.list
      /**
       * 列表插槽使用时，某些页面需要查询另外的列表接口
       * 如返利管理-返利进度管理
       */
      if (this.$slots.grid || this.$scopedSlots.grid) {
        if (this.url.cardList) {
          url = this.url.cardList
        }
      }

      getAction(url, params)
        .then((res) => {
          if (res.success) {
            this.$parent.refreshCountTabs && this.$parent.refreshCountTabs() // 更新list页面tab count
            let list = res.result.records

            try {
              if (!!this.$parent && !!this.$parent.formatTableData) {
                list = this.$parent.formatTableData(list || [])
              }
            } catch (error) {}

            if (url === '/price/purchaseInformationRecords/list') {
              console.log(list)
              list = list.map((i) => {
                if (!!i.price || i.price === 0) i.price = Number(i.price || '0').toFixed(6)
                return i
              })
            }
            const query = this.$route.query
            if ((list.length == 1 && query.linkFilter && !query.ifToList) || (list.length > 0 && query.selectFirstList === '1')) {
              //列表过来，只有一条直接打开
              this.$parent.handleView(list[0])
              return
            }
            list.forEach((item) => {
              if (item.extendFields) {
                Object.assign(item, JSON.parse(item.extendFields))
              }
            })
            this.tableData = list
            this.$emit('loadDataSuccess')
            console.log('tableData', this.tableData)
            this.tablePage.total = res.result.total
            this.tablePage.pages = res.result.pages
          } else {
            this.$emit('showWraningInfo', res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    beforeHandleData(columns) {
      this.$emit('beforeHandleData', columns)
    },
    // 获取表格列信息
    initColumns() {
      const that = this
      ajaxGetColumns(this.url.columns, null).then((res) => {
        let columns = []
        if (res.success) {
          // 过滤隐藏的字段
          if (res?.result?.length) {
            const remoteColumns = res.result
            that.initTiled(columns, remoteColumns)
            that.initAggregate(columns, remoteColumns)
          }
        }
      })
    },
    getColumnGroup() {
      let postData = {
        busAccount: this.$ls.get(USER_ELS_ACCOUNT),
        dictCode: 'srmColumnGroup'
      }
      return ajaxFindDictItems(postData)
    },
    baseColumns(columns) {
      if (!this.pageData.notShowTableSeq) {
        columns.push({ type: 'checkbox', width: 36, fixed: 'left', headerAlign: 'left' })
        columns.push({ type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center', fixed: 'left' })
      } else {
        columns.push({ type: 'checkbox', width: 36, fixed: 'left', headerAlign: 'left' })
      }
    },
    async initAggregate(columns, remoteColumns) {
      try {
        // 过滤无配置组
        remoteColumns = remoteColumns.filter((rs) => rs.columnGroup)
        let groupColumns
        if (this.customDict) {
          this.isAggregate = true
          groupColumns = uniqBy(remoteColumns, 'columnGroup').map((rs) => {
            return {
              description: rs.title,
              key: rs.id,
              text: rs.title,
              textI18nKey: '',
              title: rs.columnGroup,
              value: rs.columnGroup
            }
          })
        } else {
          let fillRemoteColumns = remoteColumns.map((it) => it.columnGroup)
          let { result } = await this.getColumnGroup() // 列分组
          groupColumns = result || []
          groupColumns = groupColumns.filter((rs) => fillRemoteColumns.includes(rs.value))
        }
        groupColumns = groupColumns.map((rs) => {
          return {
            title: this.$srmI18n(`${this.$getLangAccount()}#${rs.textI18nKey}`, rs.title),
            field: rs.value,
            align: 'center',
            slots: {
              default({ row, column }) {
                row.key = rs.key
                let groupField = remoteColumns.filter((item) => item.columnGroup == rs.value && item.hidden == '0') // 处理隐藏字段
                const $d = groupField.map((field) => {
                  return (
                    <div style='text-align: left;'>
                      <span>{field.title}</span> :<span>{row[field.dataIndex]}</span>
                    </div>
                  )
                })
                return [<div>{$d}</div>]
              }
            }
          }
        })
        // 操作列
        if (this.pageData.showOptColumn) {
          let width = 0
          try {
            // 简体 繁体
            let lang = this.$ls.get(DEFAULT_LANG) || 'zh'
            let strWidth = ['zh', 'cht'].includes(lang) ? 12 : 6
            // 操作列表宽度修改
            width = 0
            let strLength = 0
            let optColumnList = this.getAuthCodeBtns(this.pageData.optColumnList)
            let optColumnListLength = optColumnList.length
            for (let i = 0; i < optColumnListLength; i++) {
              if (i == 4) break
              strLength += optColumnList[i].title.length
            }
            if (optColumnListLength > 4) {
              //按钮数量的边框marging + 40的左右padding
              let otherWidth = 4 * 8 + 40
              let fourItemLength = optColumnList[3].title.length
              width = fourItemLength > 2 ? (strLength - fourItemLength + 2) * strWidth + otherWidth : strLength * strWidth + otherWidth
              if (this.$ls.get(DEFAULT_LANG) === 'en') width = width + 10
            } else {
              //按钮数量的边框marging + 26的左右padding
              width = strLength * strWidth + optColumnListLength * 8 + 34
            }
          } catch {
            width = this.pageData.optColumnWidth
          }
          columns.push({
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
            // width: this.pageData.optColumnWidth,
            width: 300,
            fixed: 'right',
            showOverflow: true,
            align: this.pageData.optColumnAlign,
            slots: { default: 'grid_opration' }
          })
        }
        this.groupColumns = groupColumns.length > 0 ? [...columns, ...groupColumns] : []
      } catch (error) {
        console.log(error)
      }
    },
    // 遍历当前的配置颜色
    filterCurrentColor(item, row, fieldName) {
      let filterColor = ''
      // 有dictCode && fieldType==='dict'说明是字典类型
      if (item.dictCode && item.fieldType && item.fieldType === 'dict') {
        if (item && item.fieldColors && row && fieldName) {
          item.fieldColors.forEach((color) => {
            if (fieldName.indexOf('_dictText') !== -1) {
              let len = fieldName.lastIndexOf('_dictText')
              let dictCode = fieldName.substring(0, len)
              // 返回可能是数字也可能是数字字符串
              if (color.fieldName == row[dictCode]) {
                filterColor = color.fieldColor
              }
            } else {
              // 防止不带_dictText后缀的数字字典
              if (color.fieldName == row[fieldName]) {
                filterColor = color.fieldColor
              }
            }
          })
        }
      } else {
        // 不是字典类型，直接赋值字段颜色
        filterColor = item.fieldColors[0].fieldColor
      }
      return filterColor
    },
    //设置冻结
    setFrozenColumn(listArry) {
      let temRestArry = [...listArry]
      temRestArry.reverse()
      let resetIndex = temRestArry.findIndex((val) => val.fixed)
      let lastFrozenIndex = resetIndex > -1 ? listArry.length - resetIndex - 1 : resetIndex
      this.curLastFrozenIndex = lastFrozenIndex //打开时的最后一个设置fixtype的
      if (lastFrozenIndex > -1) {
        for (let [idx, v] of listArry.entries()) {
          if (idx < lastFrozenIndex && !v.fixed) {
            v.fixed = listArry[lastFrozenIndex].fixed
          }
        }
      }
      return listArry
    },
    initTiled(columns, remoteColumns) {
      this.baseColumns(columns)
      //设置冻结
      remoteColumns = this.setFrozenColumn(remoteColumns)
      columns = columns.concat(remoteColumns)
      this.beforeHandleData(columns)
      let that = this
      columns.forEach((item) => {
        item.field = item.dataIndex
        item.columnId = item.id
        if (item.sorter) {
          item.sortable = item.sorter
        }
        if (item.filters && item.filters.length) {
          item.filters.forEach((item2) => {
            item2.label = item2.text
          })
        }
        if (item.fixType) {
          item.fixType === 'unfrozen' ? delete item.fixed : (item.fixed = item.fixType)
        }

        if (item.fixed == 'unfrozen') {
          delete item.fixed
        }
        // 处理隐藏字段
        if (item.hidden == '1') {
          item.visible = false
        } else {
          item.visible = true
        }

        if (item.scopedSlots) {
          item.editRender = { type: 'default' }
          item.slots = item.scopedSlots
        }
        if (item.dictCode && item.fieldType === 'select') {
          let postData = {
            busAccount: that.$ls.get(USER_ELS_ACCOUNT),
            dictCode: item.dictCode
          }
          ajaxFindDictItems(postData).then((res) => {
            if (res.success) {
              let options = res.result.map((item) => {
                return {
                  value: item.value,
                  label: item.title,
                  title: item.title
                }
              })
              if (item.editRender instanceof Object) {
                item.editRender.options = options
              }
            }
          })
        }
        // 处理超链接
        if (item.link === '1') {
          item.slots = this.linkModalSlots(item)
        }
        if (item.thousands) {
          if (item.thousands === '1') {
            //  // 货币千分位
            item.slots = Object.assign({}, item.slots, {
              default: ({ row, column }, h) => {
                let extend = item.extend || {}
                let symbol = (extend && extend.symol) || ''
                let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
                return [<span>{currency(row[column.property], symbol, decimals)}</span>]
              }
            })
          } else if (item.thousands === '2') {
            // 小数位置
            item.slots = Object.assign({}, item.slots, {
              default: ({ row, column }, h) => {
                let decimals = item?.dataFormat ? Number(item.dataFormat) : 4
                return [<span>{formatFloat(row[column.property], null, decimals)}</span>]
              }
            })
          }
        }
        // 处理自定义配置颜色
        if (!item.slots && item.fieldColors && item.fieldColors.length) {
          item.slots = Object.assign(
            {},
            {
              default: ({ row, column }, h) => {
                let fieldName = column.property
                let color = that.filterCurrentColor(item, row, fieldName)
                return row.recordStatus==6?[<span style={{ fontWeight:column.field=='recordStatus_dictText'?'bold':'normal',color }}>{row[column.property]}</span>]:[<span style={{ color}}>{row[column.property]}</span>]
              }
            }
          )
        }

        // 阶梯价格
        if (item.field == 'ladderPriceJson') {
          item['slots'] = { default: 'ladder_price_json_render' }
        }
      })
      if (this.pageData.showOptColumn) {
        let width = 0
        try {
          // 简体 繁体
          let lang = this.$ls.get(DEFAULT_LANG) || 'zh'
          let strWidth = ['zh', 'cht'].includes(lang) ? 12 : 6
          // 操作列表宽度修改
          width = 0
          let strLength = 0
          let optColumnList = this.getAuthCodeBtns(this.pageData.optColumnList)
          let optColumnListLength = optColumnList.length
          for (let i = 0; i < optColumnListLength; i++) {
            if (i == 4) break
            strLength += optColumnList[i].title.length
          }
          if (optColumnListLength > 4) {
            //按钮数量的边框marging + 40的左右padding
            let otherWidth = 4 * 8 + 40
            let fourItemLength = optColumnList[3].title.length
            width = fourItemLength > 2 ? (strLength - fourItemLength + 2) * strWidth + otherWidth : strLength * strWidth + otherWidth
            if (this.$ls.get(DEFAULT_LANG) === 'en') width = width + 10
          } else {
            //按钮数量的边框marging + 26的左右padding
            width = strLength * strWidth + optColumnListLength * 8 + 34
          }
        } catch (err) {
          console.log('err :>> ', err)
          width = this.pageData.optColumnWidth
        }
        columns.push({
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
          width: width || 180,
          fixed: 'right',
          align: this.pageData.optColumnAlign,
          slots: { default: 'grid_opration' }
        })
      }
      this.tableColumns = columns
    },

    setBaseQuery(linkConfig, row) {
      let query = {
        templateName: row.templateName,
        templateNumber: row.templateNumber,
        templateVersion: row.templateVersion
      }
      if (row.id) query.id = row.id
      linkConfig.otherQuery = Object.assign({}, linkConfig.otherQuery, query)
    },
    //超链接slots
    linkModalSlots(col) {
      // console.log('[slots执行]')
      const that = this
      let linkConfig = {
        primaryKey: 'id', //查询用的主键名
        bindKey: 'fbk1', //绑定的主键key
        actionPath: '', //目标路由
        otherQuery: { open: true } //其余参数
      }
      col.extendLink = JSON.parse(col.extendLink, function (k, v) {
        if (v.indexOf && v.indexOf('function') > -1) {
          // 返回方法
          return eval('(function (){ return ' + v + ' }) ()')
        }
        return v
      })
      if (col.extendLink) linkConfig = { ...linkConfig, ...col.extendLink.linkConfig }
      return Object.assign({}, col.slots, {
        default: ({ row, column }) => {
          return [
            <a
              onClick={() => {
                that.getNewRouter(col, row, column, linkConfig, { pageConfig: this.pageConfig })
              }}
            >
              {row[column.property]}
            </a>
          ]
        }
      })
    },
    checkLinkAuth({ linkConfig }) {
      let result = true
      if (linkConfig.actionPath) {
        const allMenus = this.$router.getRoutes() || [] // 包含接口返回和本地自定义的路由
        const currentMenut = allMenus.find((rs) => rs.path === linkConfig.actionPath.trim())
        result = currentMenut ? true : false
      }
      return result
    },
    // 递归获取系统菜单url
    findSupLink(data, condition, queryKey) {
      let key = queryKey || 'path'
      //传入
      const it = (i, n) => {
        if (i && i.length > 0) {
          for (let v of i) {
            if (v[key] == n) {
              return v
            } else {
              if (v.children && v.children.length > 0) {
                let re = it(v.children, n)
                if (re) {
                  return re
                }
              }
            }
          }
        }
      }
      let ret = it(data, condition)
      return ret
    },
    //超链接跳转方法
    async getNewRouter(col, row, column, linkConfig) {
      if (col.extendLink.handleBefore && typeof col.extendLink.handleBefore === 'function') {
        let callbackObj = (await col.extendLink.handleBefore(row, column, linkConfig, this)) || {}
        linkConfig = { ...linkConfig, ...callbackObj }
        const getLinkAuth = this.checkLinkAuth({ linkConfig })
        if (!getLinkAuth) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BjbWWVKHRvjlb_beee0887`, '没有权限，请联系管理员授权'))
          return
        }
      }

      if (row[column.property] && linkConfig.actionPath && linkConfig.bindKey) {
        let query = {
          [linkConfig.primaryKey]: row[linkConfig.bindKey],
          ...linkConfig.otherQuery,
          t: new Date().getTime()
        }
        this.$router.push({ path: linkConfig.actionPath.trim(), query: { ...query } })
      }
    },
    handleQuery(flag = true) {
      // 点击查询默认跳到第一页
      this.expand = false
      if (flag) {
        this.tablePage.currentPage = 1
        if (this.$refs.listGrid && this.$refs.listGrid.clearCheckboxReserve) {
          this.$refs.listGrid.clearCheckboxReserve()
        }
        if (this.$refs.listGrid && this.$refs.listGrid.clearCheckboxRow) {
          this.$refs.listGrid.clearCheckboxRow()
        }
      }

      this.handleEmitReset()
      this.loadData()
    },
    handleReset(flag = true) {
      if (flag) {
        this.tablePage.currentPage = 1
      }
      if (this.$refs.listGrid) {
        this.$refs.listGrid.clearCheckboxReserve()
        this.$refs.listGrid.clearCheckboxRow()
      }
      // 重置还原查询条件
      this.computedFormFields.forEach((item) => {
        if (item.type === 'dateRange') {
          let beginTime = item.startProp || 'beginTime'
          let endTime = item.endProp || 'endTime'
          this.pageData.form[beginTime] = ''
          this.pageData.form[endTime] = ''
        } else if (item.type == 'input') {
          this.pageData.form[item.fieldName] = ''
          item.isAudio = true
        } else if (item?.fieldName) {
          this.pageData.form[item.fieldName] = ''
        }
      })

      this.$refs.superQueryModal.queryParamsModel = [{ logicSymbol: 'like' }]

      this.handleEmitReset()
      this.superQueryNumber = 0
      if (this.customReset) {
        this.$emit('customReset')
      } else {
        //this.loadData()
          if(this.$route.name=='srm-contract-sale-SaleContractHeadList'){
              this.$router.push({path:'/srm/contract/sale/SaleContractHeadList',query:{}})
              this.loadData()
          }else if(this.$route.name=='srm-order-sale-SaleOrderHeadList'){
              this.$router.push({path:'/srm/order/sale/SaleOrderHeadList',query:{}})
              this.loadData()
          }else{
              this.loadData()
          }
      }
    },
    deleteRows(row) {
      const that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmDelete`, '确认删除'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteChosedData`, '是否删除选中数据'),
        onOk: function () {
          that.loading = true
          getAction(that.url.delete, { id: row.id })
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
                that.$emit('refreshCountTabs')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.loading = false
            })
        }
      })
    },
    upgradeVersion(row) {
      const callback = () => {
        getAction(this.url.upgrade, { id: row.id }).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.loadData()
          } else {
            this.$message.warning(res.message)
          }
        })
      }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_upgradeVersion`, '升级'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmToUpgradeVersion`, '是否确认升级版本'),
        onOk() {
          callback && callback()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.loadData()
    },
    toggleExpand() {
      this.expand = !this.expand
    },
    //点击排序触发
    sortChangeEvent({ column, property, order }) {
      if (column.sortable) {
        this.isOrder = {
          order: order,
          column: property
        }
        this.loadData()
      }
    },
    // 点击筛选触发
    filterChangeEvent({ column, property, values }) {
      let reg = /(?=(_dictText$))/g
      if (reg.test(property)) {
        property = property.replace('_dictText', '')
      }
      if (column.filters && column.filters.length) {
        this.filter[property] = values.join(',')
        this.loadData()
      }
    },
    //报错列配置
    saveColumnsConfig(params) {
      let url = '/base/userColumnDefine/saveCurrentUserColumnDefine/' + this.url.columns
      postAction(url, params)
    },
    //修改列配置
    resizableChange($rowIndex) {
      // 聚合拖动不需要保存
      if (this.isAggregate) {
        return true
      }
      let { fullColumn } = this.$refs.listGrid.getTableColumn()
      // debugger
      let newColumns = []
      fullColumn.forEach((item, i) => {
        let currentCol = this.tableColumns.find((rs) => rs.dataIndex === item.property)
        if (currentCol && currentCol.id) {
          let columnWidth = item.resizeWidth || item.width
          // debugger
          console.log(' item.fixed', item.fixed)
          newColumns.push({
            columnId: currentCol.id,
            hidden: currentCol.hidden || '0',
            alignType: 'center',
            fieldColors: currentCol.fieldColors ? JSON.stringify(currentCol.fieldColors) : '',
            columnWidth: columnWidth,
            fixType: item.fixed
          })
        }
      })
      this.saveColumnsConfig(newColumns)
    },
    //列自定义弹窗
    settingColumnConfig() {
      this.$refs.columnSettingModal.open(this.url.columns)
    },
    columnDrop() {
      this.$nextTick(() => {
        let listGrid = this.$refs.listGrid
        if (listGrid && listGrid.$el) {
          this.sortable = Sortable.create(listGrid.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
            handle: '.vxe-header--column:not(.col--fixed)',
            onEnd: ({ item, newIndex, oldIndex }) => {
              let { fullColumn, tableColumn } = listGrid.getTableColumn()
              let targetThElem = item
              let wrapperElem = targetThElem.parentNode
              // console.log('wrapperElem', wrapperElem)
              let newColumn = fullColumn[newIndex]
              if (newColumn.fixed) {
                // 错误的移动
                if (newIndex > oldIndex) {
                  wrapperElem.insertBefore(targetThElem, wrapperElem.children[oldIndex])
                } else {
                  wrapperElem.insertBefore(wrapperElem.children[oldIndex], targetThElem)
                }
                return this.$XModal.message({ message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cannoDragFixedColumn`, '固定列不允许拖动'), status: 'error' })
              }
              // 转换真实索引
              let oldColumnIndex = listGrid.getColumnIndex(tableColumn[oldIndex])
              let newColumnIndex = listGrid.getColumnIndex(tableColumn[newIndex])
              // 移动到目标列
              let currRow = fullColumn.splice(oldColumnIndex, 1)[0]
              fullColumn.splice(newColumnIndex, 0, currRow)
              listGrid.loadColumn(fullColumn)
              let newColumns = []
              fullColumn.forEach((item) => {
                let currentCol = this.tableColumns.find((rs) => rs.dataIndex === item.property)
                if (currentCol.id) {
                  newColumns.push({
                    columnId: currentCol.id,
                    hidden: currentCol.hidden || '0',
                    alignType: 'center',
                    fieldColors: currentCol.fieldColors ? JSON.stringify(currentCol.fieldColors) : '',
                    columnWidth: item.width,
                    fixType: item.fixed
                  })
                }
              })
              this.saveColumnsConfig(newColumns)
            }
          })
        }
      })
    },
    openTemplateModal() {
      this.queryTemplateList(this.$ls.get('Login_elsAccount')).then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            let options = res.result.map((item) => {
              return {
                value: item.templateNumber,
                title: item.templateName,
                version: item.templateVersion,
                account: item.elsAccount
              }
            })
            this.templateOpts = options
            // 只有单个模板直接新建
            if (this.templateOpts && this.templateOpts.length === 1) {
              this.templateNumber = this.templateOpts[0].value
              this.selectedTemplate()
            } else {
              // 有多个模板先选择在新建
              this.visible = true
            }
          } else {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    closeTemplateModal() {
      this.visible = false
    },
    selectedTemplate() {
      if (this.templateNumber) {
        const that = this
        // this.submitLoading = true
        let template = this.templateOpts.filter((item) => {
          return item.value == that.templateNumber
        })
        let params = {
          templateNumber: this.templateNumber,
          templateName: template[0].title,
          templateVersion: template[0].version,
          templateAccount: template[0].account,
          elsAccount: this.$ls.get(USER_ELS_ACCOUNT)
        }
        that.$parent.selectedTemplate(params)
        // that.visible = false
        // that.submitLoading = false
        // postAction(this.url.add, params).then(res => {
        //     if(res.success) {
        //         that.$parent.selectedTemplate(res.result)
        //     }else {
        //         that.$message.warning(res.message)
        //     }
        //     that.visible = false
        //     that.submitLoading = false
        // })
      }
    },
    handleCancel() {
      this.visible = false
    },
    // 判断是否有查看的权限
    getViewBtn() {
      let opt = this.pageData.optColumnList || []
      let viewBtn = opt.filter((item) => item.type === 'view')
      return viewBtn
    },
    // 详情页
    detailPage(row) {
      let viewAuthBtn = this.getAuthCodeBtns(this.getViewBtn())
      if (viewAuthBtn && viewAuthBtn.length) {
        this.$emit('handleView', row)
      }
    },
    // tab页签改变时
    changeTab(active) {
      let data = this.tabsList[active]
      this.$emit('afterChangeTab', { _this: this, activeTabData: data, pageData: this.pageData, listGrid: this.$refs.listGrid, tablePage: this.tablePage })
    },
    // toolbar_buttons方法封装，暴露更多属性外部使用
    toolbarButtonsCallBack(row, column, rowIndex, columnIndex, cb) {
      if (cb) {
        window.setTimeout(() => {
          cb(row, column, rowIndex, columnIndex, this.tableData)
        }, 50)
      }
    },
    // 阶梯报价json数据组装
    initRowLadderJson(jsonData) {
      let arr = []
      if (jsonData) {
        arr = JSON.parse(jsonData)
      }
      return arr
    },
    // 阶梯报价默认显示
    defaultRowLadderJson(jsonData) {
      let arrString = ''
      if (jsonData) {
        let arr = JSON.parse(jsonData)
        if (Array.isArray(arr)) {
          arr.forEach((item, index) => {
            let ladder = item.ladder
            let price = item.price || ''
            let str = price ? `${ladder},${price}` : ladder
            let separator = index === arr.length - 1 ? '' : ';'
            arrString += str + separator
          })
        }
      }
      return arrString
    },
    // 是否可以显示隐藏按
    showCustomBtnOpt(item, row) {
      return this.showCondition(item, row)
    },
    // 版本迭代使用的方法
    showCondition(item, row) {
      if (item && item.showCondition) {
        // 有showCondition 逻辑
        return item.showCondition(row)
      } else if (item.hide) {
        // 有hide逻辑
        return false
      } else {
        return true
      }
    },
    onChange(e, item) {
      if (e.type == 'click') {
        item.isAudio = true
      } else {
        item.isAudio = false
      }
    },
    handleAudio(field) {
      const self = this
      var layerIndex = window.layui.layer.open({
        type: 2,
        shade: 0.1,
        shadeClose: true,
        title: '语音助手',
        btn: ['开始说话', srmI18n(`${getLangAccount()}#i18n_field_ML_b7804`, '完成')],
        btnAlign: 'c',
        yes: function (index, layero) {
          const iframeWin = window[layero.find('iframe')[0]['name']]
          iframeWin && iframeWin.recStart()
        },
        btn2: function (index, layero) {
          var iframeWin = window[layero.find('iframe')[0]['name']]
          iframeWin &&
            iframeWin.recStop(function (data) {
              if (data?.error && data.error == 1) {
                window.layer.msg(data.text)
              } else {
                const { result } = data
                let txt = result.Result || ''
                self.$set(self.pageData.form, field, txt)
                window.layer.close(layerIndex)
              }
            })
          return false
        },
        success: function (layero, index) {
          layero.find('.layui-layer-btn0').css({
            'pointer-events': 'none',
            opacity: 0.3
          })
          var iframeWin = window[layero.find('iframe')[0]['name']]
          iframeWin &&
            iframeWin.recOpen(function () {
              layero.find('.layui-layer-btn0').css({
                'pointer-events': 'auto',
                opacity: 1
              })
            })
        },
        area: ['350px', '230px'],
        closeBtn: 1,
        resize: false,
        content: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/voiceMorphing.html` //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
      })
    },
    beforePrintMethod({ content }) {
      let contentString = content

      if (this.$ls.get(USER_INFO)) {
        let userInfo = this.$ls.get(USER_INFO)
        let waterMarkTip = userInfo.elsAccount + '-' + userInfo.subAccount + '-' + formatDate(new Date().getTime(), 'yyyy/MM/dd')
        let companySet = this.$ls.get(USER_COMPANYSET) || {}
        if (companySet.watermark && companySet.watermark === '1') {
          contentString = this.setWatermark(contentString, waterMarkTip)
        }
      }
      function sanitizeHtml(html) {
        // 替换 onclick、onerror 等事件处理器
        const sanitizedHtml = html.replace(/on(click|dblclick|mousedown|mouseup|mouseover|mousemove|mouseout|keydown|keypress|keyup|focus|blur|submit|reset|input|change|contextmenu|error)\s*=\s*[^"]*/gi, '')

        return sanitizedHtml
      }
      return sanitizeHtml(contentString)
    },
    setWatermark(joinHtmlString, str) {
      let id = '1.***********.*********'
      if (document.getElementById(id) !== null) {
        document.body.removeChild(document.getElementById(id))
      }
      let can = document.createElement('canvas')
      can.width = 500
      can.height = 200
      let cans = can.getContext('2d')
      cans.rotate((-20 * Math.PI) / 180)
      cans.font = '20px Vedana'
      cans.fillStyle = 'rgba(0, 0, 0,0.5)'
      cans.textAlign = 'left'
      cans.textBaseline = 'Middle'
      cans.fillText(str, can.width / 20, can.height)
      let div = document.createElement('div')
      div.id = id
      div.style.pointerEvents = 'none'
      div.style.top = '3px'
      div.style.left = '0px'
      div.style.position = 'fixed'
      div.style.zIndex = '100000'

      div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat'
      // document.body.appendChild(div)
      const container = document.createElement('div')
      //  div.innerHTML= joinHtmlString
      container.innerHTML = joinHtmlString
      div.style.width = document.body.scrollWidth + 'px'
      div.style.height = document.body.scrollHeight * 1.5 + 'px'
      console.log(document.body.scrollHeight, document.body.scrollWidth, document.body.clientHeight, document.body.clientWidth)
      container.appendChild(div)
      return container.innerHTML
    }
  }
}
</script>

<style lang="less" scoped>
.page-container {
  .search-cout {
    min-width: 18px;
    height: 18px;
    border-radius: 50%;
    position: absolute;
    right: 0px;
    top: -11px;
    background-color: #ff4d4f;
    font-size: 12px;
    line-height: 17px;
    text-align: center;
    color: #fff;
  }
  .query {
    padding-top: 6px;
    padding-right: 6px;
    padding-left: 6px;
    padding-bottom: 6px;
    min-height: 66px;
    .query-wrap {
      position: relative;
      border: solid 1px #e8eaec;
      border-radius: 4px;
      background-color: #fff;
    }

    :deep(.ant-form) {
      min-width: 860px !important;
      padding: 6px 15px;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0px;
    }
    :deep(.ant-form-item-label) {
      // text-align: left;
      text-overflow: ellipsis;
    }
  }
  .search-btn-groups {
    text-align: right;
    padding-top: 4px;
    // min-width: 350px;
    :deep(.ant-btn-group) {
      margin-right: 8px;
      .ant-btn {
        margin-left: 0px;
      }
    }
  }
  .list-tabs-wrap {
    min-height: 45px;
    background-color: #fff;
    :deep(.ant-tabs-bar) {
      margin: 0;
    }
    :deep(.ant-tabs-tab) {
      margin: 0;
      &:not(:last-child) {
        &:after {
          content: '';
          position: absolute;
          right: 0;
          top: calc(50% - 8px);
          width: 1px;
          height: 16px;
          background-color: #d9d9d9;
        }
      }
    }
  }
  :deep(.vxe-table--render-default .vxe-body--row.row--stripe) {
    background-color: #f9feff;
  }
}
.reset-grid-box {
  margin-bottom: 0px;
}
.rich-editor-display-box {
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.rich-editor-display-box p) {
  margin-bottom: 0px;
  display: inline;
}
:deep(.vxe-table--render-default .vxe-body--row.row--checked, .vxe-table--render-default .vxe-body--row.row--radio) {
  background-color: #fff3e0 !important;
}
:deep(.vxe-grid--pager-wrapper .vxe-pager--wrapper .vxe-pager--left-wrapper) {
  float: left;
}
.json-box {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.disabled {
  color: #4f93f186;
}
.grid-box {
  overflow: hidden;
  :deep(.vxe-table--render-default.size--mini .vxe-body--column.aggregateStyle > .vxe-cell) {
    max-height: unset;
  }
  :deep(.vxe-table--render-default .vxe-body--column.aggregateStyle:not(.col--actived) > .vxe-cell) {
    overflow: unset;
    text-overflow: unset;
    white-space: unset;
  }
}
.card-none {
  color: #606266;
  width: 98%;
  margin-left: 0px;
  justify-content: center;
  align-items: center;
  height: 82px;
  display: flex;
}
.card-list {
  min-width: 1280px;
  .card-box {
    height: 272px;
    padding: 24px;
    margin-right: 16px;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    transition: box-shadow 0.5s;
  }
  .card-box:hover {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.16);
  }
  .item-info {
    padding-bottom: 16px;
    height: 196px;
    .item-img {
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      text-align: center;
      padding: 6px;
    }
    .item-text {
      color: #383838;
      padding: 2px 0;
      line-height: 30px;
      .title {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .alias {
        height: 30px;
        color: #102d5d;
        font-size: 16px;
        font-weight: 700;
      }
      .color-gary {
        color: #747474;
      }
    }
  }
  .opt-button-list {
    margin: 0 auto;
    text-align: center;
    font-size: 12px;
    .item-button {
      background-color: #f0f0f0;
      color: #5c5c5c;
      cursor: pointer;
      border-radius: 4px;
    }
    .item-button:hover {
      background-color: #dae6fc;
      color: #4e85ff;
    }
    :deep(.ant-btn-link[disabled]) {
      background-color: #f0f0f0;
      color: #5c5c5c;
      border-radius: 4px;
      opacity: 0.7;
    }
  }
  .pagination-box {
    width: 100%;
    position: absolute;
    bottom: 0;
    padding: 5px 20px;
    background-color: #fff;
    box-shadow: 3px 1px 5px #050505;
  }
  .card-row-box {
    margin-bottom: 58px;
  }
  .pagin {
    position: absolute;
    top: 13px;
    right: 20px;
    left: 50%;
    margin-left: -135px;
  }
}
</style>
