<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      refresh
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"/>
    <!-- 行明细弹出选择框 -->
    <field-select-modal
      ref="fieldSelectModal"/>
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'
import {EditMixin} from '@comp/template/edit/EditMixin'

export default {
    name: 'SaleRefundsDeliveryHeadModal',
    components: {
        fieldSelectModal
    },
    mixins: [EditMixin],
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            selectType: 'material',
            flowId: 0,
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnLineInfo`, '退货行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleRefundsDeliveryItemList',
                            columns: [],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                                    key: 'fillDown',
                                    type: 'tool-fill',
                                    beforeCheckedCallBack: this.fillDownGridItem
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleAttachmentList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 180
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'uploadSubAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                                    width: 120
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    clickFn: this.downloadEvent
                                },
                                {
                                    type: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preViewEvent
                                }
                            ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        type: 'primary',
                        click: this.saveEvent,
                        authorityCode: 'order#saleRefundsDeliveryHead:edit'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                        type: 'primary',
                        click: this.confrimRefunds,
                        authorityCode: 'order#saleRefundsDeliveryHead:confrimRefunds'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'),
                        type: 'primary',
                        click: this.returnRefunds,
                        authorityCode: 'order#saleRefundsDeliveryHead:returnRefunds'
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                edit: '/delivery/saleRefundsDeliveryHead/edit',
                confrimRefunds: '/delivery/saleRefundsDeliveryHead/confrimRefunds',
                returnRefunds: '/delivery/saleRefundsDeliveryHead/returnRefunds',
                detail: '/delivery/saleRefundsDeliveryHead/queryById',
                upload: '/attachment/saleAttachment/upload'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let elsAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_refundsDelivery_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    methods: {
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        },
        deleteFilesEvent () {
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        confrimRefunds () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (params.saleRefundsDeliveryItemList.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
                return false
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areYouSureHaveReceivedReturnOrder`, '确认退货单'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notifyPurchaserConfirmationTheReturnOrderWaitReturnSure`, '将退货单确认通知采购方等待退货, 是否确认?'),
                onOk: function () {
                    postAction(that.url.confrimRefunds, params).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.$refs.editPage.goBack()
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        returnRefunds () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (params.saleRefundsDeliveryItemList.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
                return false
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnOrder`, '退回退货单'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notifyPurchaserConfirmationTheReturnOrderWaitReturnBack`, '将采购方发送的退货单进行退回, 是否退回?'),
                onOk: function () {
                    postAction(that.url.returnRefunds, params).then(res => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.$refs.editPage.goBack()
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })

        },
        saveEvent () {
            this.$refs.editPage.postData()
        }
    }
}
</script>
