<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'EsignFlowAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            confirmLoading: false,
            createFlag: true,
            rowIndex: -1,
            rowSignerIndex: -1,
            selectType: 'esignFlow',
            labelCol: { span: 4 },
            wrapperCol: { span: 14 },
            visible: false,
            peopleNumber: 1,
            form: {
                number: 1
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {

                form: {
                    busType: 'single',
                    toElsAccount: '',
                    supplierName: '',
                    esignNumber: null,
                    businessScene: null,
                    filesName: null,
                    filesId: null,
                    uploaded: null,
                    autoArchiving: '1',
                    autoInitiate: null,
                    remark: null,
                    cutOffTime: null,
                    contractRemind: null,
                    noticeType: null,
                    effectiveTime: null,
                    firstSeal: 'purchase'
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [

                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAy_24c6a9a8`, '业务编号'),
                                    fieldName: 'busNumber',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'subject'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QIKQXVAZd_18c96370`, '文件是否上传契约锁'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '1')
                                            setDisabledByProp('toElsAccount', flag)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjXKI_ef65ec11`, '签署有效时间'),
                                    fieldName: 'expireTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWsRKI_fa482f8c`, '签署终止时间'),
                                    fieldName: 'endTime'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCWWWey_1fb0ab8b`, '采方ELS账号'),
                                    fieldName: 'toElsAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.$set(Vue.form, 'toElsAccount', data[0].elsAccount)
                                        Vue.$set(Vue.form, 'purchaseName', data[0].name)
                                    },
                                    extend: {
                                        modalColumns: [
                                            {field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCAo_43d1fbfd`, '采方编码'), fieldLabelI18nKey: 'i18n_field_toCompanyCode', with: 150},
                                            {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCRL_43ccc7d5`, '采方名称'), fieldLabelI18nKey: 'i18n_massProdHeade95_supplierName', with: 150}
                                        ],
                                        modalUrl: '/supplier/supplierMaster/querySupplierData',
                                        modalParams: {}
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCRL_43ccc7d5`, '采方名称'),
                                    fieldName: 'purchaseName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_gwarjw3QgSxzQUiQ`, '签署完成是否自动发送'),
                                    fieldName: 'autoSend',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isOpen`, '是否已开启'),
                                    fieldName: 'initiate',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isFile`, '是否已归档'),
                                    fieldName: 'archiving',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                subject: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_QIdDxOLV_a427525c`, '文件主题不能为空')}],
                                toElsAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRCxOLV_2d7f6cc3`, '采购方不能为空')}]
                            }
                        }

                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'), groupCode: 'purchaseSigners', type: 'grid', custom: {
                        ref: 'purchaseSigners',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'tenantTypeArr', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`,
                                this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型')),
                            width: 120, editRender: {
                                name: '$select',
                                props: { multiple: true, clearable: true },
                                options: [
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RC_a300c`, '公司'), value: 'COMPANY'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: 'PERSONAL'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPersonName`, '法人'), value: 'LP'}
                                ]
                            }
                            },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 230, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'signatoryName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWLcR_75c3ca0`, '签署人姓名'), width: 120 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuPWRC_72eda045`, '添加签署公司'),  type: 'primary', click: this.addPurchaseSignEvent, showCondition: this.showAddPurchaseSignEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QGPWRC_a5504d04`, '删除签署公司'), click: this.deleteSaleSignEvent, showCondition: this.showDeleteSaleSignEvent}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRWe_416ee4e0`, '设置印章'), clickFn: this.getSeal, allow: this.allowSeal },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWL_ed04f016`, '设置签署人'), clickFn: this.addPurchaseSignerEvent}
                        ],
                        rules: {
                            subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWWWeyWxOLV_18bfbcf9`, '[签署人SRM账号]不能为空')}],
                            tenantTypeArr: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WPWCAcWxOLV_d16c9bf3`, '[签署方类型]不能为空')}]
                        }
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'uploaded', title: '是否上传契约锁', width: 180, visible: false },
                            { field: 'uploaded_dictText', title: '是否上传契约锁', width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'contractLock', attr: this.attrHandle, callBack: this.uploadCallBack, showCondition: this.showFileDelBtn}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent, showCondition: this.showFileDelBtn}
                        ]
                    } }
                ],
                formFields: [],
                footerButtons: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAnetx_f7de10e0`, '发起草稿'), type: 'primary', click: this.createDraft, showCondition: this.showSignSendBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileUpload`, '文件上传'), type: 'primary', click: this.fileUpload, showCondition: this.showUploadBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_hAIxPW_f576ec9f`, '发起一步签署'), type: 'primary', click: this.launchOneStepEsignEvent, showCondition: this.showFlowBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/contractLock/elsClContract/add',
                edit: '/contractLock/elsClContract/edit',
                detail: '/contractLock/elsClContract/queryById',
                createDraft: '/contractLock/elsClContract/createDraft',
                upload: '/attachment/purchaseAttachment/upload',
                uploadLogUrl: '/attachment/purchaseAttachment/uploadLog',
                download: '/attachment/purchaseAttachment/download',
                launchOneStepEsign: '/esign/purchaseEsign/launchOneStepEsign',
                getSignature: '/attachment/purchaseAttachment/getSignature'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.busNumber,
                actionRoutePath: '/srm/contractLock/SaleClSignList'
            }
        },
        showFileDelBtn (){
            const form = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if(form.uploaded == '1'){
                return false
            }
            return true
        },
        showSignSendBtn (){
            const form = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (form.signerVindicateStatus == '3' && form.contractStatus  !== 'DRAFT' && form.launch !== '1'  ) {
                return true
            }
            return false
        },
        showUploadBtn (){
            const form = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (form.contractStatus  == 'DRAFT' && form.uploaded != '1'  ) {
                return true
            }
            return false
        },
        showFlowBtn (){
            const form = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (form.uploaded == '1' && form.launch != '1'  ) {
                return true
            }
            return false
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        initEdit ( ){
            if(this.currentEditRow && this.currentEditRow.id) {
                getAction(this.url.detail, {id: this.currentEditRow.id}).then((res) => {
                    if (res.success) {
                        this.currentEditRow = res.result
                    }
                })
            }
        },
        createDraft (){
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            const that = this
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    if(!params.purchaseSigners || params.purchaseSigners.length == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSuPWRCnGRPWL_6c367d06`, '请添加签署公司和设置签署人'))
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                    getAction(this.url.createDraft, {id: params.id}).then(res=>{
                        if(res.success){
                            this.$message.success('合同创建成功')
                            this.$refs.editPage.confirmLoading = false
                            this.init()
                        }else {
                            this.$refs.editPage.confirmLoading = false
                            if(res.message == '网络异常，请检查网络连接'){
                                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAQLAEsLHcAZdROlbWVWKAELiHcneROjlb_421c7ed2`, '发起流程企业还未进行契约锁功能授权，请先在企业认证进行合同功能的授权'))
                            }else {
                                this.$message.error(res.message)
                            }
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
                that.$refs.editPage.confirmLoading = false
            })
        },
        showAddPurchaseSignEvent (){
            return true
        },
        showDeleteSaleSignEvent (){
            return true
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.pageData.form = {}
                this.$refs.editPage.queryDetail('')
            }
        },
        deleteBatch (){
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/esign/purchaseEsignAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        deleteSaleSignEvent (){
            this.createFlag = false
            let itemGrid = this.$refs.editPage.$refs.purchaseSigners[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        deleteFilesEvent (row) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            getAction('/contractLock/signAttachment/delete', {id: row.id}).then(res => {
                if(res.success){
                    getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) fileGrid.remove(row)
                    })
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        getSeal (row, column, $rowIndex){
            this.createFlag = false
            this.selectType = 'addSigner'
            this.rowIndex = $rowIndex
            let url = '/contractLock/saleClSeals/saleList'
            let columns = [
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 200 },
                { field: 'sealId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeWW_39f03bbd`, '签章id'), width: 180 },
                { field: 'sealName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'), width: 180 }
            ]
            let params = {companyId: row.companyId, operateStatus: 'ENABLE'}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addPurchaseSignEvent () {
            this.createFlag = false
            this.selectType = 'purchaseSign'
            const form = this.$refs.editPage.getPageData()
            let url = '/contractLock/saleCLCompanyInfo/getSignList'
            let columns = [
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称'), width: 180 },
                { field: 'companyId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCAZdWW_99b0ddbb`, '公司id'), width: 120 },
                { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态')}
            ]
            let params = {}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        allowSeal (row){
            const arr = row.tenantTypeArr || []
            if(arr.includes('COMPANY')){
                return false
            }
            return true
        },
        addPurchaseSignerEvent (row, column, $rowIndex){
            this.createFlag = false
            this.selectType = 'purchaseSigner'
            this.rowSignerIndex = $rowIndex
            const form = this.$refs.editPage.getPageData()
            if(!row.tenantTypeArr){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFPWCAcW_752c471d`, '请先选择签署方类型！'))
                return
            }
            let url = '/contractLock/saleClPersonalInfo/saleList'
            let columns = [
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 100 },
                { field: 'applyUserName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), width: 100 },
                { field: 'applyContactType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'), width: 100 },
                { field: 'applyContact', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'), width: 100 },
                { field: 'roleStr_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_role`, '角色'), width: 100, disabled: true },
                { field: 'realName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQKRLi_2b438163`, '是否实名认证'), width: 100, editRender: {name: '$select', options: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LLi_194b947`, '未认证'), value: '0'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ILi_1721e0f`, '已认证'), value: '1'}
                ], disabled: true} }
            ]
            let params = {companyId: row.companyId, realName: '1'}
            if(row.tenantTypeArr.includes('COMPANY')){
                params['roleStr'] = 'ADMIN'
            }
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        fieldSelectOk (data) {
            const form = this.$refs.editPage.getPageData()
            if(this.selectType == 'purchaseSign'){
                let itemGrid = this.$refs.editPage.$refs.purchaseSigners[0]
                let insertData = []
                let row ={}
                row['companyId'] = data[0].companyId
                row['companyName'] = data[0].companyName
                row['elsAccount'] = data[0].elsAccount
                form.companyId = data[0].companyId
                form.companyName = data[0].companyName
                form.elsAccount = data[0].elsAccount
                insertData.push(row)
                itemGrid.insertAt(insertData)
            }else if(this.selectType === 'addSigner'){
                let ids = data.map(n => n.sealId).join(',')
                form.purchaseSigners[this.rowIndex].sealIds = ids
            }else if(this.selectType === 'keyWord'){
                if(data && data.length>0){
                    const { pageNo = '', posx = '', posy = '' } = data[0] || {}
                    let result = `${pageNo}_${posx}_${posy}`
                    // const form = this.$refs.editPage.getPageData()
                    let param = form.purchaseSigners[this.sealAeraIndex]
                    param.signArea = result
                }
            }else if(this.selectType === 'purchaseSigner'){
                let flag = false
                form.purchaseSigners.forEach(x=>{
                    if(x.companyId == data[0].companyId && x.signatoryContact == data[0].applyContact
                        && x.signatoryContactType == data[0].applyContactType){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IjdeRCdePWL_ed4f45b`, '已有相同公司相同签署人！'))
                        flag = true
                        return
                    }
                })
                if(flag){
                    return
                }
                form.purchaseSigners[this.rowSignerIndex].subAccount = data[0].subAccount
                form.purchaseSigners[this.rowSignerIndex].accountId = data[0].accountId
                form.purchaseSigners[this.rowSignerIndex].signatoryName = data[0].applyUserName
                form.purchaseSigners[this.rowSignerIndex].signatoryContact = data[0].applyContact
                form.purchaseSigners[this.rowSignerIndex].signatoryContactType = data[0].applyContactType
                form.purchaseSigners[this.rowSignerIndex].roleType = '0'
            }
        },
        uploadCallBack (result) {
            let itemGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            itemGrid.insertAt(result, -1)
        },
        deletePurchaseSignEvent (){
            let itemGrid = this.$refs.editPage.$refs.purchaseEsignSignersList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        saveEvent () {
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            const that = this
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                }
                if (flag) {
                    if(params.needCheck == '1' || params.autoSend == '1'){
                        if(!params.toElsAccount){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQTPMVQISPWMLKQJOhdLKKWRdXxOLV_459c36c8`, '是否需要回传文件或签署完成是否自动发送为是时，供应商不能为空'))
                            this.$refs.editPage.confirmLoading = false
                            return
                        }
                    }

                    if(typeof params.id == 'undefined'){
                        postAction(this.url.add, params).then(res => {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            if(res.success){
                                this.$refs.editPage.confirmLoading = false
                                this.currentEditRow = res.result
                                this.init()
                            }
                        })
                    }else {
                        postAction(this.url.edit, params).then(res => {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            if(res.success){
                                this.$refs.editPage.confirmLoading = false
                                this.init()
                            }
                        })
                    }
                }
            }).catch(err => {
                console.log(err)
                that.$refs.editPage.confirmLoading = false
            })
        },
        launchOneStepEsignEvent (){
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            if(!params.purchaseSigners || params.purchaseSigners.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSuPWRCnGRPWL_6c367d06`, '请添加签署公司和设置签署人'))
                this.$refs.editPage.confirmLoading = false
                return
            }
            getAction('/contractLock/elsClContract/initiate', {id: params.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                this.$refs.editPage.confirmLoading = false
                if(res.success){
                    this.goBack()
                }
            })
        },
        fileUpload (){
            const params = this.$refs.editPage.getPageData()
            if(params.uploaded==='1'){
                this.$message.warning('文件已上传')
                return
            }
            if(!params.purchaseAttachmentList || params.purchaseAttachmentList.length<1){
                this.$message.warning('上传文件不能为空')
                return
            }
            this.$refs.editPage.confirmLoading = true
            postAction('/contractLock/elsClContract/uploadFileToContractLock', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                this.$refs.editPage.confirmLoading = false
                if(res.success){
                    this.init()
                }
            })

        },
        esignFileDown (){
            const params = this.$refs.editPage.getPageData()
            getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                if(res.success){
                    window.open(res.result.downloadUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>