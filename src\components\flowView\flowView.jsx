import { ACCESS_TOKEN } from '@/store/mutation-types'
import { getAction } from '@/api/manage'
export default {
    name: 'FlowView',
    model: {
        prop: 'visible',
        event: 'change'
    },
    // functional: true,
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        flowId: {
            type: String,
            default: '' 
        },
        title: {
            type: String,
            default: ''
        },
        width: {
            type: [String, Number]
        },
        // 是否流程设计器查看操作
        isDesign: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        visible: {
            immediate: true,
            handler (val) {
                if (val) {
                    this.getType()
                }
            }
        }
    },
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            tokenHeader: this.$ls.get(ACCESS_TOKEN),
            id: '',
            workFlowType: '',
            spinning: false
        }
    },
    methods: {
        closeFlowView () {
            this.$emit('change', false)
        },
        getType () {
            if (this.flowId) {
                this.id = this.flowId
                this.spinning = true
                getAction('/a1bpmn/audit/api/bpmn/type', {processInstanceId: this.flowId}).then(res => {
                    if (res.code == 200) {
                        this.workFlowType = res.result
                    }
                }).finally(() => {
                    this.spinning = false
                })
            }
        }
    },
    render () {
        let url = ''
        let width = ''
        if (this.isDesign) {
            url = `/els/bpmns/view/tab.html?fireHoverAction=true&table=false&modelId=${this.currentEditRow.id}&messageId=${this.tokenHeader}`
            width = 1200
        } else {
            if (this.workFlowType === 'bpmn') {
                url = `/els/bpmns/view/tab.html?fireHoverAction=true&table=false&instId=${this.id}&messageId=${this.tokenHeader}`
                width = 1200
            } else {
                url = `${this.currentBasePath}/uflo/diagram?processInstanceId=${this.id}`
                width = 960
            }
        }
        const props = {
            visible: this.visible,
            title: this.title ? this.title : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLP_1a93f54`, '流程图'),
            width: this.width ? this.width : width,
            keyboard: false,
            forceRender: true,
            maskClosable: false,
            destroyOnClose: true
        }
        const on = {
            cancel: this.closeFlowView,
            ok: this.closeFlowView
        }
        let spinProps = {
            spinning: this.spinning,
            delayTime: 300
        }
        const directives = [{ name: 'drag' }]
        return (
            <a-modal { ...{ props, on, directives } }>
                <a-spin { ...{ props: spinProps } } >
                    <div class="content">
                        <iframe
                            style={'width:100%;height:560px'}
                            src={url}
                            frameborder={'0'}></iframe>
                    </div>
                </a-spin>
            </a-modal>
        )
    }
}
