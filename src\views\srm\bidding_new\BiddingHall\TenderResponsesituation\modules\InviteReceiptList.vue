<template>
  <div>
    <listTable
      ref="listTable"
      :fromSourceData="fromSourceData"
      :statictableColumns="tableColumns"
      :showTablePage="false"
      :pageData="pageData"
    />
  </div>
</template>
<script>
import listTable from '../../components/listTable'

export default {
    inject: ['currentSubPackage'],
    props: {
        fromSourceData: {
            default: () => {
                return []
            },
            type: Array
        }
    },
    components: {
        listTable
    },
    computed: {
        subpackage (){
            return this.currentSubPackage()
        }
    },
    data () {
        return {
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {
                        type: 'confirm',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hG_a7849`, '发出'),
                        clickFn: this.handleConfirm,
                        allow: this.allowConfirm
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        allow: this.allowView
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.allowDelete
                    }
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MRzE_28fc7d14`, '回执状态'),
                    'field': 'receiptStatus_dictText',
                    'width': 120
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MRKI_28fb27c7`, '回执时间'),
                    'field': 'receiptTime',
                    'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataSource`, '数据来源'),
                    'field': 'sourceType_dictText',
                    'width': 120
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                    'field': 'contacts'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
                    'field': 'contactsPhone'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ]
        }
    },
    methods: {
        handleView (row) {
            this.$emit('handleInviteViewPage', row)
        },
        handleConfirm (row) {
            this.$emit('handleInviteConfirmPage', row)
        },
        handleDelete (row) {
            this.$emit('handleInviteDeletePage', row)
        },
        allowDelete (row){
            // 来源为邀请公告同时新建状态可删除
            return !(row.sourceType == '1' && row.receiptStatus =='0')
        },
        allowConfirm (row) {
            return !(row.receiptStatus == '0' || row.receiptStatus == null)
        },
        allowView (row) {
            return row.receiptStatus == '0'
        },
        insertAt (data, i) {
            this.$refs.listTable.insertAt(data, i)
        }
    },
    created () {
        
    }
}
</script>

