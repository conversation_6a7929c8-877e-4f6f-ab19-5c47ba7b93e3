export default function (date, format) {
    date = date || ''
    // 对后台传递的 /Date(1464689890000)/ 值做兼容处理
    // if (date.indexOf('/Date(') > -1) date = parseInt(date.replace('/Date(', '').replace(')/', ''))
    format = format || 'yyyy-MM-dd hh:mm:ss'
    if (!date) return ''
    try {
        date = new Date(date)
    } catch (e) {
        return ''
    }
    var arr = '日一二三四五六'.split('')
    var map = {
        M: date.getMonth() + 1, // 月份
        d: date.getDate(), // 日
        D: date.getDate(), // 日
        h: date.getHours(), // 小时
        H: date.getHours(), // 小时
        m: date.getMinutes(), // 分
        s: date.getSeconds(), // 秒
        q: Math.floor((date.getMonth() + 3) / 3), // 季度
        S: date.getMilliseconds(), // 毫秒
        z: arr[date.getDay()]
    }
    format = format.replace(/([yYMdDhHmsqSz])+/g, function (all, t) {
        var v = map[t]
        if (v !== undefined) {
            if (all.length > 1) {
                v = '0' + v
                v = v.substr(v.length - 2)
            }
            return v
        } else if (t === 'y' || t === 'Y') {
            return (date.getFullYear() + '').substr(4 - all.length)
        }
        return all
    })
    return format
}
