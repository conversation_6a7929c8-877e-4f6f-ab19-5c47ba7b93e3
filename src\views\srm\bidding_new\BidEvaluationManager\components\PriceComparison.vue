<template>
  <!-- 价格评分 -->
  <div>
    <a-spin :spinning="confirmLoading">
      <div v-if="!confirmLoading">
        <div
          :is="PriceScoreName"
          @goBack="goBack"
          :currentRow="currentRow"
          :resultDataProp="resultData"></div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import SubPriceComparison from './SubPriceComparison'
import TotalPriceComparison from './TotalPriceComparison' 
export default {
    props: {
        currentRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    components: {
        SubPriceComparison,
        TotalPriceComparison
    },
    computed: {
        headParams () {
            let {checkType, currentStep, processType } = this.currentRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return {xNodeId}
        }
    },
    data () {
        return {
            resultData: {},
            PriceScoreName: 'SubPriceComparison',
            confirmLoading: false
        }
    },
    created () {
        this.getData()
    },
    methods: {
        getData () {
            const {evaGroupId, id} = this.currentRow
            const params = {
                evaGroupId: evaGroupId,
                judgesTaskItemId: id
            }
            this.confirmLoading = true
            getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/querySupplierEvaGroupResultByGroupId', params, {headers: this.headParams}).then(res => {
                if (res.code == 200) {
                    const {result = {} } = res
                    this.resultData = result
                    this.PriceScoreName = result.bidLetterFormatGroup.quoteType === '0' ? 'TotalPriceComparison' : 'SubPriceComparison'
                    console.log(result)
                }else{
                    this.$message.error(res.message)
                }
                this.confirmLoading = false
            })
        },
        // 返回
        goBack () {
            this.$parent.priceComparisonStatus = false
        }
    }
}
</script>

<style lang="less" scoped>

</style>