<template>
  <a-modal
    v-drag    
    :title="title"
    :width="800"
    :visible="visible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :cancel-text="$srmI18n(`${$getLangAccount()}#i18n_title_close`, '关闭')"
  >
    <a-spin :spinning="confirmLoading">
      <a-form>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateTitle`, '模板标题')"
        >
          <a-input
            disabled
            v-model="templateName"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateContent`, '模板内容')"
        >
          <a-textarea
            disabled
            v-model="templateContent"
            :autosize="{ minRows: 5, maxRows: 8 }"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_testData`, '测试数据')"
        >
          <a-textarea
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_inputJsonTestData`, '请输入json格式测试数据')"
            v-model="testData"
            :autosize="{ minRows: 5, maxRows: 8 }"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_msgType`, '消息类型')"
        >
          <j-dict-select-tag
            v-model="msgType"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectMsgType`, '请选择消息类型')"
            dict-code="msgType"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_msgReceiver`, '消息接收方')"
        >
          <a-input
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_inputMsgReceiver`, '请输入消息接收方')"
            v-model="receiver"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import {postAction} from '@/api/manage'

export default {
    name: 'SysMessageTestModal',
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
            visible: false,
            model: {},
            labelCol: {
                xs: {span: 24},
                sm: {span: 5}
            },
            wrapperCol: {
                xs: {span: 24},
                sm: {span: 16}
            },

            confirmLoading: false,
            url: {
                send: '/message/sysMessageTemplate/sendMsg'
            },
            templateName: '',
            templateContent: '',
            receiver: '',
            msgType: '',
            testData: '',
            sendParams: {}
        }
    },
    methods: {
        open (record) {
            this.sendParams.templateCode = record.templateCode
            this.templateName = record.templateName
            this.templateContent = record.templateContent
            this.testData = record.templateTestJson
            this.msgType =record.templateType
            this.visible = true
        },
        close () {
            this.receiver = ''
            this.msgType = ''
            this.sendParams = {}
            this.visible = false
        },
        handleOk () {
            let httpurl = this.url.send
            this.sendParams.testData = this.testData
            this.sendParams.receiver = this.receiver
            this.sendParams.msgType = this.msgType
            postAction(httpurl, this.sendParams).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
                this.close()
            })
        },
        handleCancel () {
            this.close()
        }
    }
}
</script>

<style scoped>

</style>