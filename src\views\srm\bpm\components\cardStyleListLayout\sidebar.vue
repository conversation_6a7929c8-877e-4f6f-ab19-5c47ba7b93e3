<template>
  <div class="tabs">
    <div class="wrapper">
      <ul>
        <li
          v-for="(el, idx) in tabsList"
          :key="idx"
          :class="['li', idx == current ? 'active' : '', idx === 0 ? 'marginBottom8' : '']"
          @click="() => handleClick(idx)">
          <div
            :class="['item', idx === 0 ? 'first' : '']">
            <template v-if="idx === 0">
              <strong class="block">{{ el.title }}</strong>
              <span class="info">
                <span class="i18n">{{ i18nTxt }}</span>
                <span class="total">{{ el.total && el.total > 99 ? '99+' : el.total }}</span>
              </span>
            </template>
            <template v-else>
              <span class="title">{{ el.title }}</span>
            </template>
            <span
              class="total"
              v-if="idx !== 0">({{ el.total && el.total > 99 ? '99+' : el.total }})</span>
            <a-icon
              v-if="idx !== 0"
              class="icon"
              type="right"/>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
    name: 'SidebarTabs',
    props: {
        tabsList: {
            type: Array,
            default () {
                return []
            }
        },
        activeKey: {
            type: [Number, String],
            default: 0
        }
    },
    data () {
        return {
            current: this.activeKey,
            i18nTxt: ''
        }
    },
    methods: {
        handleClick (idx) {
            this.current = idx
            let tabData = this.tabsList[idx]
            this.$emit('sidebar-click', { tab: tabData, idx })
        }
    },
    created () {
        let title = this.$route.meta.title || ''
        let pre = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_xj_a03a9`, '你有')
        this.i18nTxt = title ? `${pre}${title}` : ''
    }
}
</script>

<style lang="less" scoped>
.marginBottom8 {
	margin-bottom: 8px;
}
.tabs {
	font-weight: 400;
	font-size: 12px;
	.wrapper {
		padding: 12px 8px;
		max-width: 200px;
	}
	.item {
		display: flex;
		align-items: center;
		padding: 8px 6px;
		border-radius: 8px;
		min-height: 36px;
		cursor: pointer;
		.title {
			flex: 1;
			line-height: 1.2;
			color: #454f59;
		}
		.total {
			color: #f93c00;
			margin-left: 6px;
		}
		.icon {
			margin-left: 4px;
			color: rgba(0, 0, 0, .9);
		}
	}
	.item.first {
		padding: 4px 6px;
		background: #f4f7fb;
		flex-direction: row;
		justify-content: flex-start;
		transition: all 0.3s ease-in-out;
		.block {
			display: flex;
			align-items: center;
			padding: 0 8px;
			border-radius: 8px;
			height: 30px;
			background: #fff;
			box-shadow: 0 0 8px rgba(0, 0, 0, .16);
			color: #263040;
		}
		.info {
			margin-left: 12px;
		}
		.i18n {
			color: #b5b9bd;
		}
		.total {
			margin-left: 6px;
		}
	}
	.li {
		&:hover,
		&.active {
			.item {
				background: rgba(21, 112, 255, .08);
			}
			.title,
			.icon {
				color: #1570ff;
			}
		}
	}
	.li {
		&.active {
			.item.first {
				background: #4290f7;
				color: #fff;
				flex-direction: row-reverse;
				justify-content: space-between;
				.block {
					box-shadow: 0 0 8px rgba(0, 0, 0, .16);
					color: #4290f7;
				}
				.i18n {
					color: #fff;
				}
				.info {
					margin-left: 0;
				}
				.total {
					color: #3bdbcf;
				}
			}
		}
	}
}
</style>