<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      refresh
      :page-data="pageData"
      @nextStepHandle="handleNextStep"
      :url="url"
      :gridFooterMethod="gridFooterMethod"
      :showGridFooter="true"
      :beforeHandleData="beforeHandleData"
      @afterLoadData="afterLoadData"
    />
    <!-- 行明细弹出选择框 -->
    <field-select-modal ref="fieldSelectModal" />

    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@comp/template/edit/EditMixin'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { sumNum } from '@/utils/number'
import dayjs from 'dayjs'

export default {
  name: 'PurchaseDeliveryHeadModal',
  components: { fieldSelectModal },
  mixins: [EditMixin],
  data() {
    return {
      currentBasePath: this.$variateConfig['domainURL'],
      flowView: false,
      selectType: 'material',
      flowId: 0,
      pageData: {
        form: {},
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivingBankInfo`, '收货行信息'),
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseDeliveryItemList',
              columns: [],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                  key: 'fillDown',
                  type: 'tool-fill',
                  beforeCheckedCallBack: this.fillDownGridItem
                }
              ]
            }
          },
          {
            groupName: '辅料发料',
            groupCode: 'purchaseDeliverySubList',
            type: 'grid',
            custom: {
              ref: 'purchaseDeliverySubList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'arrivedTime', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_uSKI_277b0df5`, '到货时间'), width: 120, fieldType: 'input' },
                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_PurchaseMassProdHeadList_materialCode`, '物料编码'), width: 120 },
                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}i18n_massProdHead0e15_materialName`, '物料名称'), width: 120 },
                { field: 'deliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_hSWR_28388855`, '发货数量'), width: 120 },
                {
                  field: 'receiveQuantityNow',
                  title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_hSWR_28388855`, '本次收货数量'),
                  required: '1',
                  width: 120,
                  fieldType: 'number',
                  extend: { min: 0 },
                  slots: {},
                  bindFunction: function bindFunction(row, column, val) {
                    row.receiveQuantityNow = val
                    if (!!row.receiveQuantityNow) {
                      row.secondaryQuantity = row.receiveQuantityNow / (row.conversionRate || 1)
                    } else {
                      row.secondaryQuantity = 0
                    }
                    row.secondaryQuantity = row.secondaryQuantity.toFixed(6)
                  }
                },
                { field: 'quantityUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_dtL_1301213`, '主单位'), width: 120 },
                { field: 'conversionRate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_conversionRate`, '换算率'), width: 120 },
                { field: 'secondaryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_secondaryQuantity`, '辅数量'), width: 120 },
                { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_BtL_22528dd`, '辅单位'), width: 120 },
                { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_lineStatus`, '行状态'), width: 120 },
                { field: 'factory', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_GMVR_2c647268`, '库存组织'), width: 120, slots: { default: 'renderDictLabel' }, dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"' },
                { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_materialSpec`, '物料规格'), width: 120 },
                { field: 'receiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_receiveQuantity`, '收货数量'), width: 120 },
                { field: 'remainQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_bUlSWR_2ed782e0`, '剩余收货数量'), width: 120 },
                { field: 'batchNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_batchNumber`, '批次号'), width: 120 },
                { field: 'batch_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_batch`, '是否批次管理'), width: 120 },
                { field: 'ncQualityDayNum', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_ncQualityDayNum`, '保质天数'), width: 120 },
                {
                  field: 'productionDate',
                  title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_produceDate`, '生产日期'),
                  width: 120,
                  fieldType: 'date',
                  bindFunction: function bindFunction(row, column, val) {
                    if (!!val) {
                      // 为品牌管理
                      if (!!row.brandManage) {
                        row.batchNumber = (row.supplierMnemonicCode || '') + (row.brandName || '') + value.replaceAll('-', '')
                      }
                      // 不为品牌管理
                      else {
                        row.batch == '1' && (row.batchNumber = val.replaceAll('-', ''))
                      }

                      const date = new Date(val)

                      if (!!row.ncQualityDayNum) {
                        let ncQualityDayNum = (row.ncQualityDayNum && Number(row.ncQualityDayNum)) > 0 ? row.ncQualityDayNum - 1 : 0
                        date.setDate(date.getDate() + ncQualityDayNum)
                        row.expiryDate = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
                      }
                    } else {
                      row.batchNumber = null
                      row.expiryDate = null
                    }
                  }
                },
                { field: 'expiryDate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_expiryDate`, '失效日期'), width: 120 },
                { field: 'taxCode', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_taxCode`, '税码'), width: 120 },
                { field: 'taxRate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_taxRate`, '税率'), width: 120 },
                { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_price`, '含税价'), width: 120 },
                { field: 'netPrice', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_netPrice`, '净价'), width: 120 },
                { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_orderNumber`, '订单号'), width: 120 },
                { field: 'orderItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                { field: 'returnQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_returnQuantity`, '退货数量'), width: 120 },
                { field: 'receiveDateNow', title: '本次实际收货时间', width: 200, fieldType: 'date', required: '1' },
                { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_requireDate`, '需求日期'), width: 120 },
                { field: 'overTolerance', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_overTolerance`, '超量容差率'), width: 120 },
                { field: 'purchaseRemark', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_purchaseRemark`, '需方备注'), width: 120, fieldType: 'input' },
                { field: 'supplierRemark', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_supplierRemark`, '供方备注'), width: 120 },
                { field: 'sourceType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceType`, '来源类型'), width: 120 },
                { field: 'sourceNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceNumber`, '来源单号'), width: 120 },
                { field: 'sourceItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceItemNumber`, '来源单行号'), width: 120 },
                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 120 },
                { field: 'onWayQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_onWayQuantity`, '在途数量'), width: 120 },
                { field: 'orderReceiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantitytitle`, '收货数量'), width: 120 },
                { field: 'notDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`, '未交货数量'), width: 120 },
                {
                  groupCode: '',
                  title: '品牌名称',
                  fieldLabelI18nKey: 'i18n_field_brandName',
                  field: 'brandName',
                  align: '',
                  headerAlign: 'center',
                  dataFormat: '',
                  defaultValue: '',
                  width: '120',
                  dictCode: '',
                  fold: '0',
                  sum: '0',
                  alertMsg: '',
                  bindFunction: function bindFunction(row, data, _self) {
                    row.brandId = data[0].id
                    row.brandCode = data[0].brandCode
                    row.brandName = data[0].brandName
                    row.brandManage = data[0].brandManage
                    row.ncQualityDayNum = data[0].qualityDayNum
                    row.supplierMnemonicCode = data[0].supplierMnemonicCode

                    row.batchNumber = ''
                    row.expiryDate = ''
                    row.productionDate = ''
                  },
                  extend: {
                    modalColumns: [
                      {
                        field: 'supplierMnemonicCode',
                        title: '供应商助记码',
                        fieldLabelI18nKey: 'i18n_field_supplierMnemonicCode'
                      },
                      {
                        field: 'brandName',
                        title: '品牌名称',
                        fieldLabelI18nKey: 'i18n_field_brandName',
                        with: 150
                      },
                      {
                        field: 'qualityDayNum',
                        title: '保质期（天）',
                        fieldLabelI18nKey: 'i18n_field_qualityDayNum',
                        with: 150
                      }
                    ],
                    modalUrl: '/material/purchaseMaterialHead/listBrandInfoByParams',
                    modalParams: function (that, form, row) {
                      return {
                        materialNumber: row.materialNumber,
                        supplierElsAccount: form.toElsAccount,
                        factory: form.factory || row.factory
                      }
                    },
                    // 表行弹窗清除按钮回调
                    afterRowClearCallBack: function (Vue, row) {
                      row.brandId = ''
                      row.brandCode = ''
                      row.brandName = ''
                      row.brandManage = ''
                      row.ncQualityDayNum = ''
                      row.supplierMnemonicCode = ''
                      row.batchNumber = ''
                      row.expiryDate = ''
                      row.productionDate = ''
                    }
                  },
                  fieldType: 'selectModal',
                  required: '0',
                  mobile: 1,
                  helpText: ''
                },
                {
                  groupCode: '',
                  title: '是否品牌管理',
                  fieldLabelI18nKey: 'i18n_field_brandManage',
                  field: 'brandManage',
                  align: '',
                  headerAlign: 'center',
                  dataFormat: '',
                  defaultValue: '',
                  width: 150,
                  dictCode: 'yn',
                  fold: '0',
                  sum: '0',
                  alertMsg: '',
                  slots: { default: 'renderDictLabel' },
                  required: '0',
                  mobile: 1,
                  helpText: ''
                },
                {
                  groupCode: '',
                  title: '供应商助记码',
                  fieldLabelI18nKey: 'i18n_field_supplierMnemonicCode',
                  field: 'supplierMnemonicCode',
                  align: '',
                  headerAlign: 'center',
                  dataFormat: '',
                  defaultValue: '',
                  width: 150,
                  dictCode: '',
                  fold: '0',
                  sum: '0',
                  alertMsg: '',
                  required: '0',
                  mobile: 1,
                  helpText: ''
                }
              ],
              buttons: [
                 {
                     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                     key: 'fillDown',
                     type: 'tool-fill',
                     beforeCheckedCallBack: this.fillDownGridItem
                 }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupCode: 'fileInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseAttachmentList',
              columns: [
                { type: 'checkbox', width: 40 },
                {
                  type: 'seq',
                  width: 60,
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                  field: 'fileName',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                  width: 120
                },
                {
                  field: 'uploadTime',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                  width: 180
                },
                {
                  field: 'uploadElsAccount_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                  width: 120
                },
                {
                  field: 'uploadSubAccount_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                  width: 120
                },
                {
                  field: 'grid_opration',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                  width: 120,
                  align: 'center',
                  slots: { default: 'grid_opration' }
                }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                  type: 'upload',
                  businessType: 'delivery',
                  callBack: this.uploadCallBack
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                  click: this.deleteBatch
                }
              ],
              showOptColumn: true,
              optColumnList: [
                {
                  type: 'download',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                  clickFn: this.downloadEvent
                },
                {
                  type: 'preView',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                  clickFn: this.preViewEvent
                },
                {
                  type: 'delete',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  clickFn: this.deleteFilesEvent
                }
              ]
            }
          }
        ],
        formFields: [],
        publicBtn: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receive`, '收货'),
            type: 'primary',
            click: this.receive,
            showCondition: this.showReceiveCondition
          },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
        ]
      },
      url: {
        add: '/delivery/purchaseDeliveryHead/add',
        edit: '/delivery/purchaseDeliveryHead/edit',
        detail: '/delivery/purchaseDeliveryHead/queryById',
        receive: '/delivery/purchaseDeliveryHead/receive',
        upload: '/attachment/purchaseAttachment/upload',
        material: '/material/purchaseMaterialHead/listByMaterialNumbers'
      }
    }
  },
  watch: {
    currentEditRow: {
      async handler(value) {
        const res = await getAction(this.url.detail, { id: value.id })
        const materialNumbers = res?.result?.purchaseDeliveryItemList?.map((v) => v.materialNumber)?.join(',')
        if (materialNumbers) {
          const resp = await getAction(this.url.material, { materialNumbers })
          if (resp.result?.length) {
            const fileGrid = this.$refs.editPage.$refs.purchaseDeliveryItemList[0]
            let { fullData } = fileGrid.getTableData()
            for(let detail of res.result.purchaseDeliveryItemList) {
                for (const meta of resp.result) {
                    let dataIndex = fullData.findIndex((i) => i.materialNumber === meta.materialNumber && i.factory === meta.factory && meta.materialPublicNumber != 'Y')
                    if (dataIndex != -1) {
                        fullData[dataIndex] = {
                            ...fullData[dataIndex],
                            batch: meta.batch,
                            ncQualityDayNum: meta.brandManage != 1 ? meta.ncQualityDayNum : fullData[dataIndex].ncQualityDayNum || 0
                        }
                    }else{
                        fullData[dataIndex] = {
                            ...fullData[dataIndex],
                            batch: meta.batch,
                            ncQualityDayNum: detail.ncQualityDayNum
                        }
                    }
                }
            }
            // 格式化含税单价
            fullData = fullData.map((i) => {
              console.log(i)
              if (!!i.price || i.price === 0) {
                i.price = Number(i.price).toFixed(6)
              }
              return i
            })
            console.log('fullData', fullData)
            fileGrid.loadData(fullData)
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    fileSrc() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_delivery_${templateNumber}_${templateVersion}.js?t=` + time
    }
  },
  methods: {
    beforeHandleData(data) {
      data.itemColumns.forEach((item) => {
        if (item.field == 'materialNumber') {
          item.sortable = true
        }
      })
    },
    afterLoadData(data) {
      let itemInfoTabDom = document.getElementsByClassName('itemInfoTab')[0]
      // 固定內容
      let vxeTableFixedWrapperDom = itemInfoTabDom.getElementsByClassName('vxe-table--fixed-wrapper')[0]
      let vxeTableFixedFooterWrapperDom = vxeTableFixedWrapperDom.getElementsByClassName('vxe-table--footer-wrapper')[0]

      // 重設固定table位置(跟vxe卡時間)
      let time = 0
      let si = setInterval(() => {
        time += 0.5
        if (time >= 5) {
          clearInterval(si)
          si = null
        }
        vxeTableFixedFooterWrapperDom.style.top = `auto`;
        vxeTableFixedFooterWrapperDom.style.bottom = `0px`;
      }, 500)
    },
    formatTableData(data, ref) {
      this.setColumnData()
      console.log('請求接口後格式化列表數據', data)
      data = data.map((item) => {
        if (item.price === 0 || Math.abs(item.price) > 0) {
          item.price = Number(item.price).toFixed(6)
        }
        if (item.netPrice === 0 || Math.abs(item.netPrice) > 0) {
          item.netPrice = Number(item.netPrice).toFixed(6)
        }

        if (ref === 'purchaseDeliverySubList') {
          item.receiveQuantityNow = item.deliveryQuantity || 0
        }

        // 当物料分类前三位为：402、404、413   批次管理为是
        if (!item.productionDate && (item.cateCode.indexOf('402') == 0 || item.cateCode.indexOf('404') == 0 || item.cateCode.indexOf('413') == 0) && item.batch == 1) {
          item.productionDate = dayjs().format('YYYY-MM-DD') // 生产日期默认取当天

          // 并生成对应的失效日期 （默认取物料的保质天数）
          let ncQualityDayNum = (item.ncQualityDayNum && Number(item.ncQualityDayNum)) > 0 ? item.ncQualityDayNum - 1 : 0
          item.expiryDate = dayjs(item.productionDate).add(ncQualityDayNum, 'day').format('YYYY-MM-DD')
          item.batchNumber = dayjs().format('YYYYMMDD') // 批次号信息
        }

        return item
      })
      return data
    },
    // 设置列显示
    setColumnData() {
      let st = setTimeout(() => {
        let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryItemList[0]
        let { fullData } = itemGrid.getTableData()
        if (fullData.length >= 1) {
          let item = fullData[0]
          if (item.price == item.netPrice && !item.taxRate) {
            let columnsList = itemGrid.getColumns()
            columnsList = columnsList.map((column) => {
              if (column.field == 'taxCode' || column.field == 'taxRate') {
                column.visible = false
              }
              return column
            })
            itemGrid.loadColumn(columnsList)
          }
        }
        clearTimeout(st)
        st = null
      }, 100)
    },
    gridFooterMethod({ group, columns, data }) {
      let footerData = []
      if (group.groupCode === 'itemInfo') {
        const sumField = ['deliveryQuantity', 'receiveQuantityNow', 'secondaryQuantity', 'receiveQuantity', 'realReceiveQuantity', 'remainQuantity', 'returnQuantity', 'quantity'] // 需要处理的字段
        if (columns) {
          footerData = [
            columns.map((column, _columnIndex) => {
              if (_columnIndex === 0) {
                return '合计'
              }
              if (sumField.includes(column.property)) {
                return sumNum(data, column.property)
              }
              return ''
            })
          ]
        }
      }
      return footerData
    },
    preViewEvent(row) {
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    showReceiveCondition() {
      if (this.currentEditRow.deliveryStatus === '1' || this.currentEditRow.deliveryStatus === '2' || this.currentEditRow.deliveryStatus === '4') {
        return true
      } else {
        return false
      }
    },
    goBack() {
      this.$emit('hide')
    },
    uploadCallBack(result) {
      let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
      fileGrid.insertAt(result, -1)
    },
    /*deleteFilesEvent () {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning('请选择数据！')
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },*/
    deleteFilesEvent(row) {
      const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
      getAction('/attachment/purchaseAttachment/delete', { id: row.id }).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.remove(row)
      })
    },
    deleteBatch() {
      const elsAccount = this.$ls.get(USER_ELS_ACCOUNT) || '100000'
      const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
      const checkboxRecords = fileGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      debugger
      if (checkboxRecords.length > 0) {
        const uploadElsAccount = checkboxRecords.find((rs) => rs.uploadElsAccount != elsAccount)
        if (uploadElsAccount) {
          this.$message.warning('选中数据存在非本方上传的数据,非本方数据不允许删除')
          return
        }
      }
      const ids = checkboxRecords.map((n) => n.id).join(',')
      const params = {
        ids
      }
      getAction('/attachment/purchaseAttachment/deleteBatch', params).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.removeCheckboxRow()
      })
    },
    receive() {
      const date = new Date()
      const year = date.getFullYear().toString().padStart(4, '0')
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const nowDateStr = year + '-' + month + '-' + day

      let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
      if (params.multipleReceipt != '1') {
        /*// 不是多次收货 本次收货数量都为剩余数量
                if(params.purchaseDeliveryItemList.length>0){
                    params.purchaseDeliveryItemList.forEach((item)=>{
                        item.receiveQuantityNow = item.remainQuantity
                    })
                }*/
        params.multipleReceipt = '0'
      }

      if (params.storageLocation == null || params.storageLocation == '') {
        this.$message.warning('请选择【基本信息-库存地点】')
        return
      }

      //时间提示
      // let arr = []
      let receiveQuantityNowParamErrorStr = ''
      let proDateErrorStr = ''
      if (params.purchaseDeliveryItemList.length > 0) {
        params.purchaseDeliveryItemList.forEach((item, index) => {
          if (item.receiveDateNow == '' || item.receiveDateNow == null) {
            // arr.push(index + 1)
            item.receiveDateNow = nowDateStr
          }
          if (item.receiveQuantityNow == '' || item.receiveQuantityNow == null || item.receiveQuantityNow < 0) {
            receiveQuantityNowParamErrorStr = '本次收货数量不能小于0'
            return
          }
          if (item.productionDate !== '' && item.productionDate > nowDateStr) {
            let mess = item.materialNumber + '_' + item.materialName
            proDateErrorStr = '物料为：[' + mess + ']所在行,生产日期不得大于当前日期'
            return
          }
        })
      }

      if (params.purchaseDeliverySubList.length > 0) {
        params.purchaseDeliverySubList.forEach((item, index) => {
          if (item.receiveDateNow == '' || item.receiveDateNow == null) {
            // arr.push(index + 1)
            item.receiveDateNow = nowDateStr
            return
          }
          if (item.receiveQuantityNow === '' || item.receiveQuantityNow === null || item.receiveQuantityNow < 0) {
            receiveQuantityNowParamErrorStr = '辅料发料本次收货数量不能小于0'
            return
          }
        })
      }
      if (proDateErrorStr != null && proDateErrorStr != '') {
        this.$message.warning(proDateErrorStr)
        return
      }
      if (receiveQuantityNowParamErrorStr != null && receiveQuantityNowParamErrorStr != '') {
        this.$message.warning(receiveQuantityNowParamErrorStr)
        return
      }

      //页面没有填写时间，则弹窗提示
      // if (arr && arr.length > 0) {
      //     let str = arr.join(',')
      //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_n_7b2c`, '第') + str + this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cVWNKtlSKI_b275f0f3`, '行请输入实际收货时间'))
      //     return
      // }
      console.log('params', params)

      let that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLlS_38d78a27`, '确认收货'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLlSW_ebe6d581`, '是否确认收货?'),
        onOk: function () {
          that.$refs.editPage.confirmLoading = true
          postAction(that.url.receive, params)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$refs.editPage.queryDetail('', (result) => {
                  if (result) {
                    that.currentEditRow.deliveryStatus = result.deliveryStatus
                  }
                })
              } else {
                that.$refs.editPage.confirmLoading = false

                if (!!res.message && res.message.indexOf('\n') >= 0) {
                  const h = that.$createElement
                  let strList = res.message.split('\n')
                  strList = strList.map((str, strIndex) => {
                    return h(strIndex === 0 ? 'span' : 'div', strIndex === 0 ? null : { style: 'margin-left: 24px' }, str)
                  })
                  that.$message.warning(h('span', { style: 'text-align: left' }, strList))
                  return
                }
                that.$message.warning(res.message)
              }
            })
            .catch(() => {
              that.$refs.editPage.confirmLoading = false
            })
        }
      })
    },
    saveEvent() {
      this.$refs.editPage.postData()
    },
    save() {
      let param = this.$refs.editPage ? this.$refs.editPage.form : {}
      if (param.purcaseRequestItemList.length == 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
        return false
      }
      this.saveEvent()
    },
    handleNextStep(data) {
      if (data.form.multipleReceipt === '0') {
        let rowData = data.gridCompnent.getTableData().fullData
        rowData = rowData.filter((item) => {
          item.receiveQuantityNow = item.remainQuantity
          return item
        })
        data.gridCompnent.loadData(rowData)
      }
    }
  }
}
</script>
