<template>
  <div
    style="height: 100%">
    <list-layout
      ref="listPage"
      @beforeHandleData="beforeHandleData"
      v-show="!showEditList && !showDetailList"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url"
    >
      <template
        #query
      >
        <div></div>
      </template>
    </list-layout>

    <BidEvaluationClarifyEdit
      v-if="showEditList"
      ref="supplierCapacity"
      :current-edit-row="currentEditRow"
      :routeQuery="pageData.form"
      @hide="hidePage"
    />
    <BidEvaluationClarifyDetail
      v-if="showDetailList"
      ref="supplierCapacity"
      :current-edit-row="currentEditRow"
      @hide="hidePage"
    />
    <a-modal
      v-drag    
      :visible="modalVisible"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_AHVH_27a983ca`, '变更信息')"
      :okText="okText"
      @ok="handleOk"
      @cancel="handleCancel"
      :confirmLoading="confirmLoading">

      <a-form-model
        ref="baseForm"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :rules="rules"
        :model="formData">
        <a-row>
          <a-col :span="22">
            <a-form-model-item
              :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_LVyRKI_87cc05b7`, '澄清截止时间')}`"
              required
              prop="fileClarificationEndTime">
              <a-date-picker
                show-time
                valueFormat="YYYY-MM-DD HH:mm:ss"
                v-model="formData.fileClarificationEndTime"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row >
          <a-col :span="22">
            <a-form-model-item
              :label="`${$srmI18n(`${$getLangAccount()}#i18n_title_changeReason`, '变更原因')}`"
              required
              prop="remark">
              <a-textarea
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNAHjW_aed1b026`, '请输入变更原因')"
                v-model="formData.remark"
                :auto-size="{ minRows: 3, maxRows: 5 }"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>

    </a-modal>
  </div>
</template>
<script lang="jsx">
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import BidEvaluationClarifyEdit from './modules/BidEvaluationClarifyEdit'
import BidEvaluationClarifyDetail from './modules/BidEvaluationClarifyDetail'
import { postAction, getAction } from '@/api/manage'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        BidEvaluationClarifyEdit,
        BidEvaluationClarifyDetail
    },
    props: {
    },
    data () {
        return {
            confirmLoading: false,
            rules: {
                fileClarificationEndTime: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVyRKIxOLVW_bfed72ba`, '澄清截止时间不能为空!')
                }],
                remark: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjWxOLVW_4b2aecd4`, '变更原因不能为空!')
                }]
            },
            formData: {
                fileClarificationEndTime: '',
                remark: ''
            },
            labelCol: { span: 8 },
            wrapperCol: { span: 16 },
            modalVisible: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
            showEditList: false,
            showDetailList: false,
            pageData: {
                businessType: 'tender',
                form: {
                    keyword: ''
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNdIRLSUVAy_bf134dba`, '请输入项目名称或申请编号')
                    }
                ],
                
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary'},  
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdits, allow: this.allowEdit},
                    { type: 'change', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_change`, '变更'), clickFn: this.handleChange, allow: this.allowChange},
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_view`, '查看'), clickFn: this.handleDetail}
                ],
                optColumnWidth: 270,
                tabsList: []
            }, 
            url: {
                // 列表数据展示
                list: '/tender/clarification/purchaseTenderEvaClarificationHead/list',
                columns: 'tenderEvaClarificationList',
                Change: '/tender/clarification/purchaseTenderEvaClarificationHead/changeRecord'
            }
        }
    },
    methods: {
        beforeHandleData (Columns){
            console.log(Columns)
            Columns.forEach(v=>{
                if(v.dataIndex == 'clarificationTitle'){
                    console.log(v, '==')
                    delete v.width
                }
                if(v.title == '接收人'){
                    console.log(1)

                    v.slots = {
                        default: ({row}) => {
                            let list = row.purchaseTenderEvaClarificationSupplierList ? row.purchaseTenderEvaClarificationSupplierList.map(item => {
                                return <div class={'marging_bottom_4'} style={'text-align:left'}>
                                    <span class={'marging_right_10'}>{item.supplierName}</span>
                                    {item.status === '1' ? <span class={'marging_right_10'} style={'color: blue'}>({item.status_dictText})</span> : <span class={'marging_right_10'} style={'color: red'}>({item.status_dictText})</span>}
                                </div>
                            }) : []
                            return [...list]
                        }
                    }
                }
            })
            console.log(Columns, '+++')
        },
        // 返回按钮，隐藏页面，返回list列表
        hidePage (){
            this.showEditList = false
            this.showDetailList = false
            console.log('this.$route.query', this.$route.query)
            console.log('this.pageData.form', this.pageData.form)
            this.pageData.form = this.$route.query
            this.$refs.listPage.loadData()
        },
        // 新增
        handleAdd (){
            let {subpackageId, checkType, processType, currentStep} = this.$route.query
            this.currentEditRow = {subpackageId, checkType, processType, currentStep}
            this.showEditList = true
        },
        // 编辑
        handleEdits (row){
            this.currentEditRow = row
            this.showEditList = true
        },
        // 详情
        handleDetail (row){
            this.currentEditRow = row
            this.showDetailList = true
        },
        // 变更
        handleChange (row){
            // 弹窗＋请求
            this.modalVisible = true
            this.changeId = row.id
        },
        handleOk (){
            valiStringLength(this.formData, [
                {field: 'remark', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeReason`, '变更原因'), maxLength: 1000}
            ])
            this.confirmLoading = true
            postAction(this.url.Change, {headId: this.changeId, ...this.formData}, {headers: {xNodeId: `${this.pageData.form.checkType}_${this.pageData.form.processType}_${this.pageData.form.currentStep}`}}).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                }else{
                    this.$message.error(res.message)
                }
            }).finally(()=>{
                this.confirmLoading = false
                this.$refs.listPage.loadData()
                this.modalVisible = false
            })
        },
        handleCancel (){
            this.modalVisible = false
        },
        //允许变更
        allowChange (row){
            // 发出后才可变更
            return row.status !== '1'
        },
        allowEdit (row){
            // 新建状态0才可编辑，发布后为1了就不可编辑
            return row.status !== '0'
        }
    },
    mounted (){
        console.log(this.$route.query)
        this.pageData.form = this.$route.query
    },
    created () {
        console.log(this.$route.query)
        this.pageData.form = this.$route.query
    },
    activated (){
        console.log(this.$route.query)
        this.pageData.form = this.$route.query
    }
}
</script>
<style lang="less" scoped>
    :deep(.vxe-table--render-default .vxe-body--column.col--ellipsis>.vxe-cell){
        max-height: none;
    }
     :deep(.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis>.vxe-cell){
        max-height: none;
     }
</style>