<template>
  <div>
    <a-form
      :form="form"
      layout="horizontal"
      v-if="activeKey !== ''">
      <a-form-item
        v-if="checkAttrType('id') && visble"
        :label="$srmI18n(`${$getLangAccount()}#i18n_title_formIdTips`, '表单id(提交表单时表单项的key值)')"
      >
        <a-input
          v-decorator="['id']"
        />
      </a-form-item>
      <a-form-item
        v-if="checkAttrType('label') "
        :label="$srmI18n(`${$getLangAccount()}#i18n_title_questionsSubject`, '问卷题目：')"
      >
        <a-input
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_questionsSubjectMsg`, '请输入问卷标题')"
          v-decorator="['label']"
        />
      </a-form-item>
      <a-form-item
        v-if="activeType === 'file'"
        :label="$srmI18n(`${$getLangAccount()}#i18n_title_attachment`, '说明附件') + ':'"
      >
        <a-upload
          name="file"
          :multiple="false"
          v-decorator="[
            'sampleAttach',
            {
              valuePropName: 'sampleAttach',
              getValueFromEvent: normFile,
            },
          ]"
          @change="handleUploadChange"
          :before-upload="beforeUpload"
          :default-file-list="attrObj.sampleAttach || []"
          v-bind="attachmentInfo"
        >
          <a-button> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_field_BIXV_4580bf08`, '附件上传') }} </a-button>
        </a-upload>
      </a-form-item>
      <a-form-item
        v-if="questionType == 1"
        :label="$srmI18n(`${$getLangAccount()}#i18n_title_questionScore`, '题目分数：')"
      >
        <a-input
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_questionScoreMsg`, '请输入分数')"
          v-decorator="['score']"
        />
      </a-form-item>
      <a-form-item
        v-if="checkAttrType('placeholder') && visble"
        label="placeholder"
      >
        <a-input
          v-decorator="['placeholder']"
        />
      </a-form-item>
      <option-setting
        ref="optionDom"
        v-if="activeType === 'radio' || activeType === 'checkbox' || activeType === 'select'"
        :key="activeKey"
        @update="updateDataByKey"
      />
      <rule-setting v-if="checkAttrType('rules')" />
      <!-- 参考问卷星，直接给默认值，方便预览操作 -->
      <div
        style="text-align: center;margin-top: 10px;"
        v-if="false">
        <a-button
          @click="saveOptions"
          icon="save"
          type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
      </div>
    </a-form>
    <div v-else>
      
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_selectTypeMsg`, '请选择一个类型') }}
    </div>
  </div>
</template>

<script>
import RuleSetting from '../common/RuleSetting'
import OptionSetting from '../common/OptionSetting'
import { mapState, mapMutations } from 'vuex'
import { deepClone } from '../../utils'
import { findIndexWithKey } from '../../utils/core'

export default {
    name: 'ControlAttr',
    props: {
        attachmentInfo: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    components: {
        RuleSetting,
        OptionSetting
    },
    beforeCreate () {
        this.form = this.$form.createForm(this, {
            onValuesChange: (props, values) => {
                this.setAttr(values)
            }
        })
    },
    data () {
        return {
            activeType: '',
            activeArr: [],
            attrObj: {},
            attrArr: [],
            marks: { 0: '0', 1: '1', 2: '2', 3: '3', 4: '4', 6: '6', 8: '8', 12: '12' },
            visble: false,
            localDefaultFile: []
        }
    },
    computed: mapState({
        formData: state => state.formDesigner.formData,
        questionType: state => state.formDesigner.questionType,
        activeKey: state => state.formDesigner.activeKey
    }),
    watch: {
        activeKey: function (val) {
            this.updateDataByKey(val)
        }
    },
    created () {
        this.updateDataByKey(this.activeKey)
    },
    methods: {
        beforeUpload (file) {
            let Size=100
            if (this.localDefaultFile.length >= 1) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RiTXVImBI_8522573`, '只允许上传一个附件'))
                return window.Promise.reject(false)
            }
            if (Size) {
                const limitSize = file.size / 1024 / 1024 > Size
                if (limitSize) {
                    const tips = `${this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_iTXVjBIfXefL_d2cdd62`, '允许上传的附件大小最大为')}${Size}M`
                    this.$message.error(tips)
                    return window.Promise.reject(false)
                }
            }
        },
        handleUploadChange ({ file, fileList, event }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    this.form.setFieldsValue({
                        sampleAttach: file.response
                    })
                    this.updateDataByKey(this.activeKey)
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        },
        normFile (e) {
            console.log('Upload event:', e)
            if (Array.isArray(e)) {
                return e
            }
            const fileList = e && e.fileList || []
            this.localDefaultFile = fileList
            return fileList
        },
        saveOptions () {
            if (!this.form.getFieldValue('label')) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_questionsSubjectMsg`, '标题不能为空'))
                return false
            }
            // 选项校验
            const type = ['radio', 'checkbox', 'select']
            if (type.includes(this.activeType)) {
                const check = this.$refs.optionDom.settingOptions()
                if (!check) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionContentMsg`, '选项内容不能为空'))
                    return check
                }
            }
        },
        updateDataByKey (val) {
            this.activeType = val.split('-')[0]
            const activeArr = findIndexWithKey(this.formData, val)
            let obj = {}
            if (activeArr.length === 1) {
                obj = this.formData[activeArr[0]].attr
            }
            if (activeArr.length === 3) {
                obj = this.formData[activeArr[0]].columns[activeArr[1]].children[activeArr[2]].attr
            }
            this.activeArr = activeArr
            this.attrObj = obj
            this.localDefaultFile = this.attrObj?.sampleAttach || []
            this.attrArr = Object.keys(obj)
            this.form = this.$form.createForm(this, { // 此处因为默认值是后续加载的，此时form需要重新初始化
                onValuesChange: (props, values) => {
                    this.setAttr(values)
                }
            })
            this.$nextTick(function () {
                const formAttr = deepClone(obj)
                delete formAttr.options // 删除额外属性
                delete formAttr.rules
                this.form.setFieldsValue(formAttr)
            })
        },
        setAttr (values) {
            const formData = deepClone(this.formData)
            const vKey = Object.keys(values)[0]
            if (this.activeArr.length === 1) {
                formData[this.activeArr[0]].attr[vKey] = values[vKey]
                // grid 同步columns熟练
                if (vKey === 'count') {
                    const oldCol = formData[this.activeArr[0]].columns
                    const msgTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteFromMsg`, '此操作将会删除已存在的表单项，确定要删除么？')
                    if (oldCol.length > values[vKey]) {
                        const goDelCol = oldCol.slice(values[vKey])
                        let confirmDel = true
                        for (let i = 0; i < goDelCol.lengh; i++) {
                            if (goDelCol[i].children.length) {
                                this.$confirm({
                                    title: msgTitle,
                                    onOk () {
                                        confirmDel = true
                                    },
                                    onCancel () {
                                        confirmDel = false
                                    }
                                })
                                break
                            }
                        }
                        if (!confirmDel) {
                            return
                        } else {
                            oldCol.splice(values[vKey], oldCol.length - values[vKey])
                            const span = 24 / values[vKey]
                            for (let i = 0; i < oldCol.length; i++) {
                                oldCol[i].span = span
                            }
                            formData[this.activeArr[0]].columns = oldCol
                        }
                    } else {
                        const span = 24 / values[vKey]
                        for (let i = 0; i < oldCol.length; i++) {
                            oldCol[i].span = span
                        }
                        const n = values[vKey] - oldCol.length
                        for (let i = 0; i < n; i++) {
                            oldCol.push({
                                span,
                                children: []
                            })
                        }
                        formData[this.activeArr[0]].columns = oldCol
                    }
                }
            }
            if (this.activeArr.length === 3) {
                formData[this.activeArr[0]].columns[this.activeArr[1]].children[this.activeArr[2]].attr[vKey] = values[vKey]
            }
            this.updateFormData(formData)
        },
        checkAttrType (type) {
            if (type === 'id' && this.activeType === 'grid') return false // 栅格不需要设置id
            if (this.attrArr.includes(type)) return true
            return false
        },
        ...mapMutations({
            updateFormData: 'setFormData'
        })
    }
}
</script>

<style lang="less" scoped>

</style>
