<template>
  <div class="ResultNotice">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      @content-header-prev="handlePrev"
      @content-header-next="handleNext"
      @content-header-save="() => handleSave('save')"
      @content-header-publish="() => handleSave('publish')"
      @content-header-template="handleTemplate"
    />

    <div
      class="container"
      :style="style">
    
      <a-spin :spinning="confirmLoading">
        <div class="steps">
          <a-steps :current="current">
            <a-step
              v-for="(item, idx) in steps"
              :key="idx"
              :title="item" />
          </a-steps>
        </div>

        <div class="steps-content">
          <component 
            :is="stepCom"
            ref="stepCom"
            :userInfo="userInfo"
            :parentData.sync="parentData"
            :templateContent="templateContent"
            :current="current"
            @deleteFile="handleDeleteFile"
            @updateFile="handleUpdateFile"
          ></component>
        </div>
      </a-spin>
    </div>
    
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>

<script lang="jsx">
import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import step1 from './components/step1'
import step2 from './components/step2'
import step3 from './components/step3'

import { getAction, postAction } from '@/api/manage'

import { USER_INFO, SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

const ALIAS = '__$$__'

export default {
    name: 'Step1',
    components: {
        'content-header': ContentHeader,
        'field-select-modal': fieldSelectModal,
        step1,
        step2,
        step3
    },
    data () {
        return {
            selectType: '',
            userInfo: {},
            current: 0,
            templateContent: '',
            parentData: {
                bidWinNotice: '0', // 是否发布中标公告，0：否、1：是
                bidWinScope: '', // 中标公告范围
                bidWinContent: '', // 中标公告内容
                bidWinEndTime: '',
                noticeSupplier: '',
                bidWinNotification: '0', // 是否发布中标通知书，0：否、1：是
                bidWinNotificationContent: '', // 中标通知书内容
                bidFailNotification: '0', // 是否发布未中标通知书，0：否、1：是
                bidFailNotificationContent: '', // 未中标通知书内容
                attachment__$$__1: [],
                attachment__$$__2: [],
                attachment__$$__3: []
            },
            steps: [ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidWinAnnouncement`, '中标公告'), this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidWinNotice`, '中标通知书'), this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notBidWinNotice`, '未中标通知书') ],
            confirmLoading: false,
            showHeader: true,
            height: 0,
            showBtn: true
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        btns () {
            let arr = [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectTemplate`, '选择模板'), type: 'primary', event: 'template', authorityCode: 'bidding#purchaseBiddingHead:select' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', event: 'save', authorityCode: 'bidding#purchaseBiddingHead:save' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', event: 'publish', authorityCode: 'bidding#purchaseBiddingHead:publish' }
            ]
            if (!this.showBtn) arr = []
            let btns = [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_prevStep`, '上一步'), type: '', event: 'prev' },
                ...arr,
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nextStep`, '下一步'), type: '', event: 'next' }
            ]
            if (this.current === 0) {
                btns = [
                    ...arr,
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nextStep`, '下一步'), type: '', event: 'next' }
                ]
            }
            if (this.current === this.steps.length - 1) {
                btns = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_prevStep`, '上一步'), type: '', event: 'prev' },
                    ...arr
                ]
            }
            return btns
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        stepCom () {
            let c = 'step1'
            switch (this.current) {
            case 0:
                c = 'step1'
                break
            case 1:
                c = 'step2'
                break
            case 2:
                c = 'step3'
                break
            case 3:
                c = 'step3'
                break
            default:
                break
            }
            return c
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        setPropData (key, property, data) {
            this.$set(this[key], property, data)
        },
        handleDeleteFile ({ id, _property }) {
            const url = '/attachment/purchaseAttachment/delete'
            const params = { id }
            getAction(url, params).then(res => {
                const type = res.success ? 'success' : 'error'
                let attachment = JSON.parse(JSON.stringify(this.parentData[_property]))
                attachment = attachment.filter(n => n.id !== id)
                this.setPropData('parentData', _property, attachment)
                this.$message[type](res.message)
            })
        },
        handleUpdateFile ({ info, _property }) {
            let attachment = JSON.parse(JSON.stringify(this.parentData[_property]))
            attachment = attachment.concat(info)
            this.setPropData('parentData', _property, attachment)
        },
        handleNext () {
            // this.$refs.stepCom.$refs.form && this.$refs.stepCom.$refs.form.validate(valid => {
            //     if (!valid) return
            // })
            this.$refs.stepCom.updateParent()
            this.current++
        },
        handlePrev () {
            // this.$refs.stepCom.$refs.form && this.$refs.stepCom.$refs.form.validate(valid => {
            //     if (!valid) return
            // })
            this.$refs.stepCom.updateParent()
            this.current--
        },
        handleTemplate () {
            if (this.current > 2) return
            let url = '/notice/purchaseNoticeTemplate/list'
            let columns = [
                { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60, align: 'center'},
                { field: 'templateName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_templateName`, '模板名称'), width: 150, align: 'center'},
                // { field: 'templateContent', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_templateContent`, '模板内容'), 
                //     width: 300, 
                //     align: 'center',
                //     slots: {
                //         default: ({ row, column }) => {
                //             const div = row.templateContent
                //             return [<div domPropsInnerHTML={div}></div>]
                //         }
                //     }
                // },
                { field: 'createBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_elsBarcodeRuleHeadList_createBy`, '创建人'), width: 150, align: 'center'},
                { field: 'createTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_createTime`, '创建时间'), width: 150, align: 'center'}
            ]   
            this.$refs.fieldSelectModal.open(url, {}, columns, '')
        },
        fieldSelectOk (data = []){
            const { templateContent = '' } = data[0] || {}
            this.templateContent = templateContent
        },
        getData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingNotice/queryById'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        this.showBtn = false
                        return
                    }
                    this.parentData = res.result || {}
                    this.getAttachmentListData()
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        getAttachmentListData () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))

            const getPromises = () => {
                const url = '/attachment/purchaseAttachment/list'
                const { id = '' } = this.vuex_currentEditRow || {}
                const { elsAccount } = this.$ls.get(USER_INFO)
                
                const types = [
                    'winBidNotice', // 中标公告
                    'winBidNotification', // 中标通知书
                    'failBidNotification' // 未中标通知书
                ]
                
                return types.map(prop => {
                    const params = {
                        headId: id,
                        businessType: prop,
                        uploadElsAccount: elsAccount,
                        pageNo: 1,
                        pageSize: 20
                    }
                    return getAction(url, params)
                })
            }

            this.confirmLoading = true
            Promise.all(handlePromise(getPromises()))
                .then(result => {
                    result.forEach(({ status, res }, i) => {
                        if (status === 'success') {
                            const _property = `attachment${ALIAS}${i + 1}`
                            const records = res.result.records || []
                            this.setPropData('parentData', _property, records)
                        }
                    })
                })
                .finally(() => {
                    this.confirmLoading = false
                })
            
        },
        handleSave (type = 'save') {
            this.$refs.stepCom.$refs.form && this.$refs.stepCom.$refs.form.validate(valid => {
                if (!valid) return
                this.$refs.stepCom.updateParent()
                const validate = [
                    { prop: 'bidWinScope', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_announcementScope`, '中标公告范围') },
                    { prop: 'bidWinContent', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_announcementContent`, '中标公告内容') },
                    { prop: 'bidWinEndTime', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_announcementStopTime`, '中标截止时间') },
                    { prop: 'bidWinNotificationContent', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_announcementLetterContent`, '中标通知书内容') },
                    { prop: 'bidFailNotificationContent', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notAnnouncementLetterContent`, '未中标通知书内容') }
                ]

                for (const { prop, label } of validate) {
                    if (!this.parentData[prop]) {
                        this.$message.error(`${label}不能为空`)
                        return
                    }
                }

                const callback = () => {
                    let params = { ...this.parentData }

                    Object.keys(params).forEach(prop => {
                        if (prop.indexOf(ALIAS) !== -1) {
                            delete params[prop]
                        }
                    })

                    const url = type === 'save'
                        ? '/bidding/purchaseBiddingNotice/edit'
                        : '/bidding/purchaseBiddingNotice/publish'
                
                    this.confirmLoading = true
                    postAction(url, params)
                        .then(res => {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            if (res.success) {
                                this.updateVuexCurrentEditRow()
                            }
                        })
                        .finally(() => {
                            this.confirmLoading = false
                        })
                }


                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_calibration`, '定标'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmCalibration`, '是否确认当前定标结果?'),
                    onOk () {
                        callback && callback()
                    },
                    onCancel () {
                        console.log('Cancel')
                    }
                })
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
        }
    },
    created () {
        this.init()
    }
}
</script>

<style lang="less">
.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.ResultNotice {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .steps {
        padding-bottom: 12px;
        position: relative;
        &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 2px;
            background: #f2f2f2;
        }
    }
    .steps-content {
        padding-top: 20px;
    }
    .steps-content-warp {
        .file {
            position: relative;
            padding: 0 40px 0 20px;
            line-height: 22px;
            max-width: 50%;
            .ellipsis;
            &:hover {
                background:rgba(64, 169, 255, .8);
                color: #fff;
                a {
                    color: #fff;
                }
                &::after {
                    background: #fff;
                }
            }
            &::after {
                content: '';
                position: absolute;
                left: 5px;
                top: 9px;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: #40a9ff;
            }
            .icon {
                position: absolute;
                right: 4px;
                top: 4px;
                &.del {
                    cursor: pointer;
                }
            }
        }
    }
}
</style>