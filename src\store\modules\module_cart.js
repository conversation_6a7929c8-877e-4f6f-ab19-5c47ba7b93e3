/**
 * 购物车
 */
import {
    MODULE_CART_PUSHTOCAR,
    M<PERSON><PERSON><PERSON>_CART_INCREMENT,
    MODULE_CART_DECREMENT,
    MODULE_CART_SETLOADING,
    MODULE_CART_SETDATA,
    MOD<PERSON><PERSON>_CART_SETPAGEDATA,
    ADD_SELECTIONDATA,
    SUB_SELECTIONDATA,
    SET_SELECTIONDATA,
    MODULE_CART_INCREATECOMPARISONPANEL,
    MODULE_CART_DECEATECOMPARISONPANEL,
    MODULE_CART_SETCOMPARISONPANEL,
    MODULE_CART_CLEARCOMPARISONPANEL,
    MOD<PERSON><PERSON>_CART_SETSHOWPANEL,
    MODULE_CART_SETPAGESIZE,
    MODULE_CART_INITDATAPAGE
} from '../mutation-types'

import { postAction, getAction } from '@/api/manage'

const moduleLogin = {
    namespaced: true, // 命名空间
    state() {
        return {
            dataPage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                align: 'right',
                pageSizes: [10, 20, 30, 40, 50],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            products: [],
            selectionData: [], // 已选商品
            loading: false,
            comparisonPanel: [], // 比价面板商品数据
            showPanel: false
        }
    },
    getters: {
        num(state) {
            return state.products.length
        }
    },
    actions: {
        // 加入比价
        async addToPannel({
            commit
        }, el) {
            commit(MODULE_CART_SETSHOWPANEL, true)

            try {
                let payload = {
                    product: el
                }
                commit(MODULE_CART_INCREATECOMPARISONPANEL, payload)
                commit(MODULE_CART_SETLOADING, false)
            } catch (err) {
                commit(MODULE_CART_SETLOADING, false)
            }
        },
        // 购物车查询
        async getCartData({
            commit
        }, val) {
            try {
                commit(MODULE_CART_SETLOADING, true)
                let params = {
                    pageSize: val && val.pageSize || '',
                    pageNo: val && val.currentPage || ''
                }
                const res = await getAction('/product/purchaseUserCart/list', params)
                let payload = {
                    products: res.result.records || []
                }
                let pagePayload = {
                    currentPage: res.result.current,
                    total: res.result.total,
                    pageSize: res.result.size
                }
                commit(MODULE_CART_SETDATA, payload)
                commit(MODULE_CART_SETPAGEDATA, pagePayload)
                commit(MODULE_CART_SETLOADING, false)
            } catch (err) {
                commit(MODULE_CART_SETLOADING, false)
            }
        },
        async deleteCart({
            commit,
            dispatch
        }, ids) {
            // 编辑后数量
            try {
                commit(MODULE_CART_SETLOADING, true)
                let str = ids.join(',')
                await getAction('product/purchaseUserCart/deleteBatch', { ids: str })
                commit(MODULE_CART_SETLOADING, false)
                // dispatch('getCartData')
            } catch (err) {
                commit(MODULE_CART_SETLOADING, false)
            }
        }
    },
    mutations: {
        // 添加新商品
        [MODULE_CART_PUSHTOCAR](state, {
            row
        }) {
            state.products.push(Object.assign({}, row))
        },
        // 自增一
        [MODULE_CART_INCREMENT](state, {
            id
        }) {
            const item = state.products.find(n => n.id === id)
            item.productCount++
        },
        // 自减一
        [MODULE_CART_DECREMENT](state, {
            id
        }) {
            const item = state.products.find(n => n.id === id)
            item.productCount--
        },
        [MODULE_CART_SETLOADING](state, bool) {
            state.loading = !!bool
        },
        [MODULE_CART_SETDATA](state, {
            products
        }) {
            state.products = JSON.parse(JSON.stringify(products))
        },
        [MODULE_CART_SETPAGEDATA](state,
            dataPage) {
            const _dataPage = state.dataPage
            state.dataPage = {
                ..._dataPage,
                ...dataPage
            }
        },
        [MODULE_CART_SETPAGESIZE](state,
            dataPage) {
            const _dataPage = state.dataPage
            state.dataPage = {
                ..._dataPage,
                ...dataPage
            }
        },
        [MODULE_CART_INITDATAPAGE](state
        ) {
            state.dataPage = {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                align: 'right',
                pageSizes: [10, 20, 30, 40, 50],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            }
        },
        [ADD_SELECTIONDATA](state, {
            id
        }) {
            state.selectionData = [...state.selectionData, id]
            // console.log('add + selectionData',state.selectionData)
        },
        [SUB_SELECTIONDATA](state, {
            id
        }) {
            state.selectionData = state.selectionData.filter(n => n !== id)
            // console.log('sub + selectionData',state.selectionData)
        },
        // 设置已选数据
        [SET_SELECTIONDATA](state, {
            selectionData
        }) {
            state.selectionData = JSON.parse(JSON.stringify(selectionData))
            // console.log('selectionData', selectionData)
        },
        // 增加比价产品数据
        [MODULE_CART_INCREATECOMPARISONPANEL](state, {
            product
        }) {
            state.comparisonPanel.push(product)
        },
        // 删除比价产品数据
        [MODULE_CART_DECEATECOMPARISONPANEL](state, { id }) {
            state.comparisonPanel = state.comparisonPanel.filter(n => n.id !== id)
        },
        // 设置比价产品数据
        [MODULE_CART_SETCOMPARISONPANEL](state, {
            data = []
        }) {
            state.comparisonPanel = data
        },
        // 清空比价产品数据
        [MODULE_CART_CLEARCOMPARISONPANEL](state) {
            state.comparisonPanel = []
        },
        [MODULE_CART_SETSHOWPANEL](state, bool) {
            state.showPanel = !!bool
        }
    }
}

export default moduleLogin