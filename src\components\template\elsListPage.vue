<template>
  <div class="els-page-container">
    <a-spin
      class="progress-spinning"
      :spinning="confirmLoading"
      size="large"
      :indicator="indicator">
      <div class="table-page-search-wrapper">
        <a-form-model
          class="ant-advanced-search-form"
          layout="inline"
          :model="pageData.form"
          @keyup.enter.native="pageData.publicBtn[0].clickFn"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol">
          <a-row
            :gutter="24"
            style="padding: 0 12px;">
            <a-col
              v-for="(item, i) in pageData.formField"
              :key="'field_'+i"
              :md="8"
              :lg="8"
              :style="{ display: i < count ? 'block' : 'none' }">
              <a-form-model-item
                v-if="item.type==='input'"
                :label="item.label">
                <a-input
                  v-model="pageData.form[item.fieldName]"
                  :placeholder="item.placeholder" />
              </a-form-model-item>
              <a-form-model-item
                v-if="item.type==='select'"
                :label="item.label">
                <m-select
                  :mode="item.mode"
                  :disabled="item.disabled"
                  v-model="pageData.form[item.fieldName]"
                  :placeholder="item.placeholder"
                  :show-opt-value="item.showOptValue"
                  :options="item.options"
                  :source-url="item.sourceUrl"
                  :source-map="item.sourceMap"
                  :dict-code="item.dictCode"
                  :noEmptyOpt="item.noEmptyOpt"
                  @change="item.changeEvent" />
              </a-form-model-item>
              <a-form-model-item
                :label="item.label"
                v-else-if="item.type==='dateRange'">
                <a-range-picker
                  :showTime="item.showTime"
                  :value="[pageData.form[item.start],pageData.form[item.end]]"
                  :valueFormat="item.valueFormat || 'YYYY-MM-DD'"
                  @change="(dates) => changeDateRangePicker(dates, item)" />
              </a-form-model-item>
              <a-form-model-item
                :label="item.label"
                v-else-if="item.type==='date'">
                <a-date-picker
                  v-model="pageData.form[item.fieldName]"
                  :showTime="item.showTime"
                  :valueFormat="item.valueFormat || 'YYYY-MM-DD'"
                  @change="item.changeFn" />
              </a-form-model-item>
              <a-form-model-item
                :label="item.label"
                v-else-if="item.type==='autoComplete'">
                <a-auto-complete
                  :placeholder="item.placeholder"
                  @search="(value) => handleSearch(value, item.dictCode)"
                  v-model="pageData.form[item.fieldName]"
                  :data-source="dataSource"
                  @focus="handleFocus"
                />
              </a-form-model-item>
            </a-col>
            <template v-if="pageData.formField.length == 3">
              <a-col
                :md="8"
                :lg="8"></a-col>
              <a-col
                :md="8"
                :lg="8"></a-col>
            </template>
            <a-col
              :md="8"
              :lg="8"
              style="height:48px;"
            >
              <a-button 
                v-for="(item, index) in pageData.publicBtn"
                :key="'public_btn_' + index"
                :type="item.type" 
                style="margin-right: 6px"
                @click="item.clickFn" 
                :icon="item.icon">{{ item.label }}</a-button>
              <a
                v-if="pageData.formField.length > 2"
                @click="handleToggleSearch"
                style="margin-left: 6px"
              >
                {{ expand ? $srmI18n(`${$getLangAccount()}#i18n_title_putAway`, '收起') : $srmI18n(`${$getLangAccount()}#i18n_title_open`, '展开') }}
                <a-icon :type="expand ? 'up' : 'down'" />
              </a>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <div class="els-table-box">
        <div class="els-alert-info">
          <div style="line-height:32px;margin-left:6px">
            <em class="anticon anticon-info-circle ant-alert-icon" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_selected`, '已选择') }} <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>{{ $srmI18n(`${$getLangAccount()}#i18n_title_term`, '项') }}
            <a
              style="margin-left: 24px"
              @click="onClearSelected"
            >{{ $srmI18n(`${$getLangAccount()}#i18n_title_clear`, '清空') }}</a>
          </div>
          <div class="table-operator">
            <template v-if="normalBtnList.length">
              <a-button
                v-for="(item, i) in normalBtnList"
                :type="item.primary ? 'primary' : 'default'"
                :icon="item.icon"
                :key="'button_'+i"
                @click="item.clickFn"
              >
                {{ item.label }}
              </a-button>
            </template>
            <template v-if="foldBtnList.length">
              <a-dropdown>
                <a-button icon="more">{{ $srmI18n(`${$getLangAccount()}#i18n_title_more`, '更多') }}</a-button>
                <a-menu slot="overlay">
                  <a-menu-item
                    v-for="(item, i) in foldBtnList"
                    :key="'menu_button_'+i">
                    <a-upload
                      v-if="item.type==='upload'"
                      name="file"
                      :show-upload-list="false"
                      :multiple="false"
                      :headers="tokenHeader"
                      :action="importExcelUrl"
                      @change="handleImportExcel"
                    >
                      <a-icon
                        :type="item.icon"
                        style="margin-right:10px"></a-icon>{{ $srmI18n(`${$getLangAccount()}#i18n_title_improt`, '导入') }}
                    </a-upload>
                    <div
                      v-else
                      @click="item.clickFn">
                      <a-icon
                        :type="item.icon"
                        style="margin-right:10px"></a-icon>{{ item.label }}
                    </div>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </template>
          </div>
        </div>
        <vxe-grid
          border
          auto-resize 
          resizable
          column-key
          row-id="id"
          highlight-hover-row
          show-overflow
          ref="listGrid"
          :height="pageData.tableHeight || tableHeight"
          size="mini"
          :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
          :checkbox-config="{highlight: true, reserve: true, trigger: 'row'}"
          :pager-config="tablePage"
          :loading="loading"
          :columns="tableColumns"
          :data="tableData"
          :sort-config="{remote: true}"
          :filter-config="{remote: true}"
          header-align="center"
          @sort-change="sortChangeEvent"
          @filter-change="filterChangeEvent"
          @checkbox-change="checkboxChangeEvent"
          @checkbox-all="checkboxChangeEvent"
          @page-change="handlePageChange"
          @resizable-change="resizableChange">
          <!--自定义插槽 seq_header-->
          <template #grid_opration="{ row, column }">
            <a
              v-for="(item, i) in pageData.optColumnList"
              :key="'opt_'+ row.id + '_' + i"
              :title="item.title"
              style="margin:0 4px"
              v-show="item.showCondition ? item.showCondition(row) : true"
              @click="item.clickFn(row,column)"><a-icon :type="item.type"></a-icon></a>
          </template>
          <template #grid_view="{ row, column }">
            <a
              @click="viewPage(row, column)"
              v-html="row[column.property]"></a>
          </template>
          <template slot="empty">
            <a-empty />
          </template>
          <template #custom_render="{ row, column }">
            <slot 
              name="custom_column_render" 
              :row="row" 
              :column="column"/>
          </template>
          <template #custom_active_render="{ row, column }">
            <a @click="activeHandle(row, column)">
              {{ row[column.property] }}
            </a>
          </template>
        </vxe-grid>
      </div>
      <column-setting ref="columnSettingModal"></column-setting>
    </a-spin>
  </div>
</template>
<script lang="jsx">
import { getAction, downFile, postAction } from '@/api/manage'
import { ajaxGetColumns, ajaxGetKeyword, ajaxFindDictItems } from '@/api/api'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { AutoComplete, Empty, Progress } from 'ant-design-vue'
import Sortable from 'sortablejs'
import columnSetting from '@comp/template/columnSetting'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
export default {
    name: 'NtiListPage',
    props: {
        pageData: { //页面数据
            type: Object,
            default: null
        },
        url: {  //后台接口
            type: Object,
            default: null
        },
        customActiveColumn: {
            type: String,
            default: null
        }
    },
    components: {
        AAutoComplete: AutoComplete,
        AEmpty: Empty,
        AProgress: Progress,
        columnSetting
    },
    beforeDestroy () {
        if (this.sortable) {
            this.sortable.destroy()
        }
    },
    data () {
        return {
            expand: false,
            confirmLoading: false,
            showProgress: false,
            selectedRowKeys: [],
            tableData: [],
            filter: {},
            loading: false,
            tokenHeader: {'X-Access-Token': this.$ls.get(ACCESS_TOKEN)},
            dataSource: [],
            isOrder: {
                column: 'id',
                order: 'desc'
            },
            labelCol: {
                md: { span: 8 },
                lg: { span: 8 },
                xxl: { span: 6 }
            },
            wrapperCol: {
                md: { span: 16 },
                lg: { span: 16 },
                xxl: { span: 18 }
            },
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 500,
                align: 'right',
                pageSizes: [20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            tableColumns: [],
            progress: 0
            
        }
    },
    computed: {
        count () {
            return this.expand ? this.pageData.formField.length : 2
        },
        tableHeight () {
            return document.documentElement.clientHeight - 198
        },
        importExcelUrl: function (){
            return `${this.$variateConfig['domainURL']}/${this.url.importExcelUrl}`
        },
        normalBtnList () {
            return this.pageData.button.filter(item => {
                return !item.folded
            })
        },
        foldBtnList () {
            return this.pageData.button.filter(item => {
                return item.folded
            })
        },
        indicator () {
            if(this.showProgress) {
                return <div class="progress-info">{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inExecution`, '执行中')}...<a-progress percent={ this.progress } /></div>
            }else {
                return this.showProgress
            }
        }
    },
    created () {
        // this.initDictData()
        this.initColumns()
        this.loadData()
        this.columnDrop()
    },
    methods: {
        loadData () {
            this.loading = true
            let form = this.pageData.form
            let isOrder = this.pageData.isOrder || this.isOrder
            let params = Object.assign(form, {pageSize: this.tablePage.pageSize, pageNo: this.tablePage.currentPage}, {filter: this.filter}, isOrder)
            getAction(this.url.list, params).then((res) => {
                if(res.success) {
                    let list = [...res.result.records]
                    list.forEach(item => {
                        if(item.extendFields) {
                            Object.assign(item, JSON.parse(item.extendFields))
                        }
                    })
                    this.tableData = list
                    this.tablePage.total = res.result.total
                    this.loading = false
                }
            })
        },
        // 获取表格列信息
        initColumns (){
            ajaxGetColumns(this.url.columns, null).then((res) => {
                if(res.success) {
                    let columns = [
                        { type: 'checkbox', width: 36, fixed: 'left', headerAlign: 'left' },
                        { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center', fixed: 'left' }
                    ]
                    columns = columns.concat(res.result)
                    // 字段转换
                    columns.forEach(item => {
                        item.field = item.dataIndex
                        item.columnId = item.id
                        if(item.sorter) {
                            item.sortable = item.sorter
                        }
                        if(item.filters && item.filters.length) {
                            item.filters.forEach(item2 => {
                                item2.label = item2.text
                            })
                        }
                        if(item.scopedSlots) {
                            item.slots = item.scopedSlots
                        }
                        // 处理从外部传过来自定义渲染的字段
                        if (this.customActiveColumn === item.fieldName) {
                            item.slots = {default: 'custom_active_render'}
                        }
                        if(item.fixType) {
                            item.fixed = item.fixType
                        }
                    })
                    if(this.pageData.showOptColumn) {
                        columns.push({
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                            width: this.pageData.optColumnWidth,
                            fixed: 'right',
                            align: this.pageData.optColumnAlign,
                            slots: { default: 'grid_opration' }
                        })
                    }
                    this.tableColumns = columns
                }
            })
        },
        // 查询
        searchQuery () {
            // 点击查询默认跳到第一页
            this.tablePage.currentPage = 1
            this.loadData()
        },
        // 重置
        searchReset () {
            this.selectedRowKeys = []
            this.pageData.form = {}
            this.$refs.listGrid.clearCheckboxReserve()
            this.$refs.listGrid.clearCheckboxRow()
            this.loadData()
        },
        // 展开、收起
        handleToggleSearch (){
            this.expand = !this.expand
        },
        onClearSelected () {
            this.$refs.listGrid.clearCheckboxRow()
            this.selectedRowKeys = []
        },
        //分页
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            this.loadData()
        },
        //点击排序触发
        sortChangeEvent ({ column, property, order }) {
            if (column.sortable) {
                this.isOrder = {
                    order: order,
                    column: property
                }
                this.loadData()
            }
        },
        // 点击筛选触发
        filterChangeEvent ({ column, property, values}) {
            if (column.filters && column.filters.length) {
                this.filter[property] = values.join(',')
                this.loadData()
            }
        },
        //checkbox选中事件
        checkboxChangeEvent ({ records }) {
            this.selectedRowKeys = records.map(item => {
                return item.id
            })
        },
        handleEdit (row) {
            this.selectedRowKeys = []
            this.selectedRowKeys.push(row.id)
        },
        handleDelete: function (id) {
            if(!this.url.delete){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSetTheUrlDeleteProperty`, '请设置url.delete属性!'))
                return
            }
            var that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmDelete`, '确认删除'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmDeletion`, '是否删除选中数据?'),
                onOk: function () {
                    that.loading = true
                    getAction(that.url.delete, {id: id}).then((res) => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.selectedRowKeys = []
                            that.loadData()
                        } else {
                            that.$message.warning(res.message)
                        }
                        that.loading = false
                    })
                }
            })
        },
        removeRows () {
            if(!this.url.deleteBatch){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSetTheUrlDeleteBatchProperty`, '请设置url.deleteBatch属性!'))
                return
            }
            if (this.selectedRowKeys.length <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaceInputOrganizationType`, '请选择一条记录！'))
                return
            } else {
                var ids = this.selectedRowKeys.join(',')
                var that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmDelete`, '确认删除'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmDeletion`, '是否删除选中数据?'),
                    onOk: function () {
                        that.loading = true
                        getAction(that.url.deleteBatch, {ids: ids}).then((res) => {
                            if (res.success) {
                                that.$message.success(res.message)
                                that.loadData()
                                that.onClearSelected()
                            } else {
                                that.$message.warning(res.message)
                            }
                        }).finally(() => {
                            that.loading = false
                        })
                    }
                })
            }
        },
        // 导出
        handleExportXls (fileName){
            if(!fileName || typeof fileName != 'string'){
                fileName = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_exportFile`, '导出文件')
            }
            let param = {...this.pageData.form}
            if(this.selectedRowKeys && this.selectedRowKeys.length>0){
                param['selections'] = this.selectedRowKeys.join(',')
            }
            downFile(this.url.exportXlsUrl, param).then((data)=>{
                if (!data) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), fileName+'.xls')
                }else{
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', fileName+'.xls')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            })
        },
        /* 导入 */
        handleImportExcel (info){
            if (info.file.status !== 'uploading') {
                console.log(info.file, info.fileList)
            }
            if (info.file.status === 'done') {
                if (info.file.response.success) {
                    if (info.file.response.code === 201) {
                        let { message, result: { msg, fileUrl, fileName } } = info.file.response
                        let href = this.$variateConfig['domainURL'] + fileUrl
                        this.$warning({
                            title: message,
                            content: (
                                <div>
                                    <span>{msg}</span><br/>
                                    <span>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_detailContent`, '具体详情请')} <a href={href} target="_blank" download={fileName}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '点击下载')} </a> </span>
                                </div>
                            )
                        })
                    } else {
                        this.$message.success(info.file.response.message || `${info.file.name} ${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadSuccess`, '文件上传成功')}`)
                    }
                    this.loadData()
                } else {
                    this.$message.error(`${info.file.name} ${info.file.response.message}.`)
                }
            } else if (info.file.status === 'error') {
                this.$message.error(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadError`, '文件上传失败')}: ${info.file.msg} `)
            }
        },
        handleSearch (value, dictCode) {
            if (value != '') {
                ajaxGetKeyword(dictCode + value, null).then((res) => {
                    if (res.success) {
                        this.dataSource = res.result
                    }
                })
            }
        },
        handleFocus () {
            this.dataSource = []
        },
        updateRelationSelect (dictCode, fieldName) {
            let index = this.getFormFieldIndex(fieldName)
            let that = this
            let postData = {
                busAccount: that.$ls.get(USER_ELS_ACCOUNT),
                dictCode: dictCode
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    that.pageData.formField[index].options = res.result
                    that.$forceUpdate()
                }
            })
        },
        getFormFieldIndex (fieldName) {
            let index = 0
            this.pageData.formField.forEach((item, i) => {
                if(item.fieldName === fieldName) {
                    index = i
                }
            })
            return index
        },
        viewPage (row, column) {
            this.$parent['view_'+column.property](row)
        },
        // custom_active_render
        activeHandle (row, column) {
            this.$emit('customActiveHandle', {row: row, column: column})
        },
        columnDrop () {
            this.$nextTick(() => {
                let listGrid = this.$refs.listGrid
                this.sortable = Sortable.create(listGrid.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
                    handle: '.vxe-header--column:not(.col--fixed)',
                    onEnd: ({ item, newIndex, oldIndex }) => {
                        let { fullColumn, tableColumn } = listGrid.getTableColumn()
                        let targetThElem = item
                        let wrapperElem = targetThElem.parentNode
                        let newColumn = fullColumn[newIndex]
                        if (newColumn.fixed) {
                            // 错误的移动
                            if (newIndex > oldIndex) {
                                wrapperElem.insertBefore(targetThElem, wrapperElem.children[oldIndex])
                            } else {
                                wrapperElem.insertBefore(wrapperElem.children[oldIndex], targetThElem)
                            }
                            return this.$XModal.message({ message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cannoDragFixedColumn`, '固定列不允许拖动！'), status: 'error' })
                        }
                        // 转换真实索引
                        let oldColumnIndex = listGrid.getColumnIndex(tableColumn[oldIndex])
                        let newColumnIndex = listGrid.getColumnIndex(tableColumn[newIndex])
                        // 移动到目标列
                        let currRow = fullColumn.splice(oldColumnIndex, 1)[0]
                        fullColumn.splice(newColumnIndex, 0, currRow)
                        listGrid.loadColumn(fullColumn)
                        let newColumns = []
                        fullColumn.forEach((item) => {
                            if(item.own.id) {
                                newColumns.push({
                                    columnId: item.own.id,
                                    hidden: 0,
                                    alignType: 'center',
                                    columnWidth: item.width,
                                    fixType: item.fixed
                                })
                            }
                        })
                        this.saveColumnsConfig(newColumns)
                    }
                })
            })
        },
        resizableChange ($rowIndex) {
            let columns = this.$refs.listGrid.getColumns()
            let index = $rowIndex.columnIndex
            let newColumns = []
            columns.forEach((item, i) => {
                if(item.own.id) {
                    let columnWidth = item.width
                    if(index == i) {
                        columnWidth = item.resizeWidth
                    }
                    newColumns.push({
                        columnId: item.own.id,
                        hidden: 0,
                        alignType: 'center',
                        columnWidth: columnWidth,
                        fixType: item.fixed
                    })
                }
            })
            this.saveColumnsConfig(newColumns)
        },
        saveColumnsConfig (params) {
            let url = '/base/userColumnDefine/saveCurrentUserColumnDefine/' + this.url.columns
            postAction(url, params)
        },
        settingColumnConfig () {
            this.$refs.columnSettingModal.open(this.url.columns)
        },
        getSelectedRows () {
            let rows = this.$refs.listGrid.getCheckboxRecords()
            return rows
        },
        // 日期区间事件
        changeDateRangePicker (dates, item) {
            this.pageData.form[item.start] = dates[0]
            this.pageData.form[item.end] = dates[1]
            this.$forceUpdate()
        },
        // 构建进度条
        buildProgreseBar (url, params) {
            let that = this
            this.showProgress = true
            this.confirmLoading = true
            function getProgress () {
                getAction(url, params, {timeout: 3000}).then(res => {
                    if(res.success) {
                        if(res.result.progress < 1) {
                            that.progress = parseInt(res.result.progress * 100)
                            setTimeout(function (){getProgress()}, 500)
                        }else {
                            that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功'))
                            that.loadData()
                            that.confirmLoading = false
                            that.showProgress = false
                            that.progress = 0
                        }
                    }else {
                        that.$message.warning(res.message)
                        that.confirmLoading = false
                        that.showProgress = false
                        that.progress = 0
                    }
                }).catch(err => {
                    console.log(err)
                    that.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_networkException`, '网络异常'))
                    that.confirmLoading = false
                    that.showProgress = false
                    that.progress = 0
                })
            }
            getProgress()
        }
    }
}
</script>
<style lang="less">
.vxe-header--row .vxe-header--column.sortable-chosen {
  background-color: #dfecfb;
}
.els-page-container  {
    .progress-spinning {
        .ant-spin-spinning {
            max-height: inherit;
            .progress-info {
                width: 300px;
                margin-left: -150px;
                margin-top: -50px;
            }
        }
    }
    
}
</style>