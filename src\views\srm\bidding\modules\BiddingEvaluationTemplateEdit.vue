<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :currentEditRow="currentEditRow"
      :url="url"
      refresh/>
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { ajaxFindDictItems } from '@/api/api'
export default {
    name: 'BiddingEvaluationTemplateEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            selectType: 'biddingEvaluationTemplate',
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_UBTv_411c044d`, ' 评标条例'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'biddingEvaluationTemplateItemList',
                        columns: [],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteItemEvent}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regulationsAnnex`, '条例附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'attachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'fbk1', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_correspondingRegulations`, '对应条例'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), type: 'primary', click: this.downloadFile}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DJ_c64d4`, '提交'), type: 'primary', click: this.publishEvent, showCondition: this.showAuditBtn, authorityCode: 'bidding#biddingEvaluationTemplateHead:submit' },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/bidding/biddingEvaluationTemplateHead/add',
                edit: '/bidding/biddingEvaluationTemplateHead/edit',
                detail: '/bidding/biddingEvaluationTemplateHead/queryById',
                upload: '/attachment/purchaseAttachment/upload',
                public: '/bidding/biddingEvaluationTemplateHead/publish',
                submitAudit: '/a1bpmn/audit/api/submit'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount
            if(!elsAccount || elsAccount==''){
                elsAccount = this.$ls.get('Login_elsAccount')
            }
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_biddingEvaluationTemplate_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        addItem () {
            this.selectType = 'biddingEvaluationRegulation'
            const form = this.$refs.editPage.getPageData()
            let regulationTypes = []
            //获取条例类型
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEvaluationBiddingType'
            }
            ajaxFindDictItems(postData).then(res => {
                const type = res.success ? 'success' : 'error'
                if(type==='success' && res.result.length>0){
                    console.log(res.result)
                    for(let item of res.result){
                        regulationTypes.push({label: item.text, value: item.value})
                    }
                }
            })
            let url = '/bidding/biddingEvaluationRegulation/list'
            let columns = [
                {field: 'regulationNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rulesEncoding`, '条例编码'), width: 150},
                {field: 'regulationName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regulationName`, '条例名称'), width: 150},
                {field: 'regulationType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regulationType`, '条例类型'), width: 200, editRender: { name: '$select', options: regulationTypes }},
                {field: 'uploadFileFlag', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needToUploadAttachments`, '是否需要上传附件'), width: 200, editRender: { name: '$select', options: [
                    { value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_need`, '需要') },
                    { value: '0', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notNeed`, '不需要') },
                    { value: '', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notNeed`, '不需要') }
                ] }},
                {field: 'inputType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_topicType`, '题目类型'), width: 200, editRender: { name: '$select', options: [
                    { value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_handLose`, '手输') },
                    { value: '2', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_radioType`, '单选') },
                    { value: '3', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkboxType`, '多选') }
                ] }},
                {field: 'must', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), width: 200, editRender: { name: '$select', options: [
                    { value: '0', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否') },
                    { value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是') }
                ] }},
                {field: 'fullMark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fullMark`, '满分'), width: 150}
            ]
            let params = { elsAccount: form.elsAccount, companyCode: form.company, purchaseOrgCode: form.purchaseOrg}
            this.$refs.fieldSelectModal.open(url, params, columns, 'multiple')
        },
        fieldSelectOk (data) {
            let subAccount = this.$ls.get('Login_subAccount')
            let arr = data.map(({ uploadFileFlag, uploadFileFlag_dictText, id, regulationNumber, regulationName, regulationDetail, must, regulationType, fullMark, inputType, inputValue, companyCode, purchaseOrgCode}) => (
                { uploadFile: uploadFileFlag, uploadFile_dictText: uploadFileFlag_dictText, uploadFileFlag, regulationId: id, regulationNumber, regulationName, regulationDetail, must, regulationType, fullMark, inputType, inputValue, company: companyCode, purchaseOrg: purchaseOrgCode, createBy: subAccount}))
            let form = this.$refs.editPage.getPageData()
            if(this.selectType == 'biddingEvaluationRegulation'){
                let itemGrid = this.$refs.editPage.$refs.biddingEvaluationTemplateItemList[0]
                let { fullData } = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.regulationNumber
                })
                let insertData = arr.filter(item => {
                    item.evaluationNumber = form.evaluationNumber
                    return !regulations.includes(item.regulationNumber)
                })
                itemGrid.insertAt(insertData)
                //附件添加
                for(let item of insertData){
                    const params = {id: item.regulationId}
                    getAction('/bidding/biddingEvaluationRegulation/queryById', params).then(res => {
                        if (res.success) {
                            if(res.result && res.result.attachmentList && res.result.attachmentList.length>0){
                                let fileGrid = this.$refs.editPage.$refs.attachments[0]
                                for(let item of res.result.attachmentList){
                                    item.fbk1 = res.result.regulationName
                                    fileGrid.insertAt(item, -1)
                                }
                            }
                        }
                    })
                }
            }
        },
        deleteItemEvent () {
            let itemGrid = this.$refs.editPage.$refs.biddingEvaluationTemplateItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.attachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent () {
            const fileGrid = this.$refs.editPage.$refs.attachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        saveEvent () {
            this.$refs.editPage.postData()
        },
        publishEvent () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (params.biddingEvaluationTemplateItemList.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n__UBTvxOLVW_eee420c4`, '评标条例不能为空！'))
                return
            }
            this.$refs.editPage.confirmLoading = true
            postAction(this.url.edit, params).then(res => {
                if (res.success) {
                    if (params.needAudit == '1') {
                        this.submitAuditEvent()
                        return
                    }
                    this.$refs.editPage.form.dataVersion += 1
                    this.$refs.editPage.handleSend()
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        submitAuditEvent (){
            const params = this.$refs.editPage.getPageData()
            if(params.needAudit==='0'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_templateNotNeedApproved`, '该评标模板无需审批'))
                return
            }
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    //先保存再提交审批
                    this.$refs.editPage.confirmLoading = true
                    // postAction(this.url.edit, params).then(res => {
                    //     if (res.success) {
                    let param = {}
                    param['businessId'] = params.id
                    param['rootProcessInstanceId'] = params.flowId
                    param['businessType'] = 'biddingEvaluationTemplate'
                    param['auditSubject'] = '评标模板审批：'+params.evaluationNumber+' '+params.evaluationName||''
                    param['params'] = JSON.stringify(params)
                    const _this = this
                    postAction(this.url.submitAudit, param).then(res => {
                        if (res.success) {
                            this.$message.info('提交审批成功')
                            _this.$parent.submitCallBack(params)
                        } else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.$refs.editPage.confirmLoading = false
                    })
                    //     } else {
                    //         this.$message.warning(res.message)
                    //     }
                    // }).finally(() => {
                    //     this.$refs.editPage.confirmLoading = false
                    // })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        showAuditBtn (){
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if(params.id){
                return true
            }else{
                return false
            }
        }

    }
}
</script>