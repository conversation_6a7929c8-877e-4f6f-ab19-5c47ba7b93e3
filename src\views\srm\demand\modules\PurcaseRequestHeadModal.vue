<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :current-edit-row="currentEditRow"
      refresh
      :page-data="pageData"
      :url="url"
    />
    <!-- 行明细弹出选择框 -->
    <field-select-modal
      :pageConfigData="pageConfig"
      :afterReqHandle="afterReqHandle"
      ref="fieldSelectModal"
    />
    <!-- <a-modal
                    v-drag
                      centered
                      :width="960"
                      :maskClosable="false"
                      :visible="flowView"
                      @ok="closeFlowView"
                      @cancel="closeFlowView">
                      <iframe
                        style="width:100%;height:560px"
                        title=""
                        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
                        frameborder="0"></iframe>
                    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"
    />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
    <ItemImportExcel
      ref="itemImportExcel"
      @importCallBack="importCallBack"
    />
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
import { downFile, getAction, httpAction, postAction } from '@/api/manage'
import { ButtonComponent } from '@comp/template/business/class/ComponentFactory'
import { EditMixin } from '@comp/template/edit/EditMixin'
import flowViewModal from '@comp/flowView/flowView'
import { formatDate } from '@/filters'

export default {
  name: 'PurcaseRequestHeadModal',
  components: {
    flowViewModal,
    fieldSelectModal,
    ItemImportExcel
  },
  mixins: [EditMixin],
  data() {
    return {
      currentBasePath: this.$variateConfig['domainURL'],
      flowView: false,
      selectType: 'material',
      flowId: 0,
      pageData: {
        form: {},
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionInfo`, '采购申请行信息'),
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseRequestItemList',
              columns: [],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                  type: 'primary',
                  click: this.insertGridItem
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  click: this.deleteGridItem
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                  key: 'fillDown',
                  type: 'tool-fill',
                  beforeCheckedCallBack: this.fillDownGridItem
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
                  params: this.importParams,
                  click: this.importExcel
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupCode: 'fileInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseAttachmentList',
              columns: [
                { type: 'checkbox', width: 40 },
                {
                  type: 'seq',
                  width: 60,
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                  field: 'materialName',
                  width: 120,
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称')
                },
                {
                  field: 'fileName',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                  width: 120
                },
                {
                  field: 'uploadTime',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                  width: 180
                },
                {
                  field: 'uploadElsAccount_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                  width: 120
                },
                {
                  field: 'uploadSubAccount_dictText',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                  width: 120
                },
                {
                  field: 'grid_opration',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                  width: 140,
                  align: 'center',
                  slots: { default: 'grid_opration' }
                }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                  type: 'upload',
                  businessType: 'purchaseRequest',
                  attr: this.attrHandle,
                  callBack: this.uploadCallBack
                },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                  click: this.deleteBatch
                }
              ],
              showOptColumn: true,
              optColumnList: [
                {
                  type: 'download',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                  clickFn: this.downloadEvent
                },
                {
                  type: 'preView',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                  clickFn: this.preViewEvent
                },
                {
                  type: 'delete',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  clickFn: this.deleteFilesEvent
                }
              ]
            }
          },
          {
            groupName: '退回历史记录',
            groupCode: 'purchaseRequestItemReturnHistoryList',
            type: 'grid',
            custom: {
              ref: 'purchaseRequestItemReturnHistoryList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'requestNumber', title: '采购申请号', width: 120 },
                { field: 'itemNumber', title: '采购申请行号', width: 120 },
                { field: 'materialNumber', title: '物料编码', width: 120 },
                { field: 'materialName', title: '物料名称', width: 120 },
                { field: 'originalQuantity', title: '退回前数量', width: 120 },
                { field: 'partReturnQuantity', title: '退回数量', width: 120 },
                { field: 'backRemark', title: '退回意见', width: 120 },
                { field: 'createTime', title: '创建时间', width: 120 },
                { field: 'createBy', title: '创建用户', width: 120 }
              ]
            }
          }
        ],
        formFields: [],
        publicBtn: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
            authorityCode: 'purchaseRequest#purchaseRequestHead:edit',
            type: 'primary',
            click: this.saveEvent
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
            authorityCode: 'purchaseRequest#purchaseRequestHead:submit',
            type: 'primary',
            click: this.submitAudit,
            id: 'submitAudit',
            showCondition: this.showAuditConditionBtn
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitDemandPool`, '提交需求池'),
            authorityCode: 'purchaseRequest#purchaseRequestHead:toDemandPool',
            type: 'primary',
            click: this.publishEvent,
            showCondition: this.showPublishConditionBtn
          },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
        ]
      },
      url: {
        add: '/demand/purchaseRequestHead/add',
        edit: '/demand/purchaseRequestHead/edit',
        detail: '/demand/purchaseRequestHead/queryById',
        public: '/demand/purchaseRequestHead/toDemandPool',
        upload: '/attachment/purchaseAttachment/upload',
        import: '/els/base/excelByConfig/importExcel',
        submitAudit: '/a1bpmn/audit/api/submit',
        cancelAudit: '/a1bpmn/audit/api/cancel'
      }
    }
  },
  computed: {
    fileSrc() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_purchaseRequest_${templateNumber}_${templateVersion}.js?t=` + time
    }
  },
  methods: {
    beforeHandleData(config) {
      if (this.currentEditRow.id) {
        return
      }
      const formFields = config.formFields || ''
      formFields.forEach((n) => {
        if (n.fieldName === 'applyDate') {
          let dataFormat = n.dataFormat || 'yyyy-MM-dd'
          n.defaultValue = formatDate(this.currentEditRow._timestamp, dataFormat) || ''
        }
      })
    },
    fillDownGridItem(info, otherParams) {
      new ButtonComponent().gridFillDown(info, otherParams)
    },
    attrHandle() {
      return {
        sourceNumber: this.currentEditRow.requestNumber,
        actionRoutePath: '/srm/demand/PurchaseRequestHeadList'
      }
    },
    goBack() {
      this.$emit('hide')
    },

    init() {
      // queryDetail方法已经处理了id,可以直接调用
      if (this.currentEditRow) {
        this.$refs.editPage.queryDetail(this.currentEditRow.id, (data) => {
          this.editFormData = data
          let attachmentList = this.editFormData.purchaseAttachmentList || []
          let purchaseRequestItemList = this.editFormData.purchaseRequestItemList || []
          let materialMap = purchaseRequestItemList.reduce((acc, obj) => {
            acc[obj.itemNumber] = obj.materialNumber + '_' + obj.materialName
            return acc
          }, {})
          attachmentList.forEach((item) => {
            let number = item.itemNumber
            if (number && materialMap[number] && !item.materialName) {
              item.materialNumber = materialMap[number].split('_')[0]
              item.materialName = materialMap[number].split('_')[1]
            }
          })
        })
      }
    },
    uploadCallBack(result) {
      let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
      // let pageData = this.$refs.editPage.getPageData()
      let purchaseRequestItemList = this.getItemGridRef('purchaseRequestItemList').getTableData().fullData
      let materialMap = purchaseRequestItemList.reduce((acc, obj) => {
        acc[obj.itemNumber] = obj.materialNumber + '_' + obj.materialName
        return acc
      }, {})
      result.forEach((item) => {
        let number = item.itemNumber
        if (number && materialMap[number] && !item.materialName) {
          item.materialNumber = materialMap[number].split('_')[0]
          item.materialName = materialMap[number].split('_')[1]
        }
      })
      fileGrid.insertAt(result, -1)
    },
    deleteFilesEvent(row) {
      const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
      getAction('/attachment/purchaseAttachment/delete', { id: row.id }).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.remove(row)
      })
    },
    deleteBatch() {
      const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
      const checkboxRecords = fileGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      const ids = checkboxRecords.map((n) => n.id).join(',')
      const params = {
        ids
      }
      getAction('/attachment/purchaseAttachment/deleteBatch', params).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.removeCheckboxRow()
      })
    },
    preViewEvent(row) {
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    publishEvent() {
      if (this.$refs.editPage.confirmLoading) return
      let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
      if (!params.id) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
        return
      }
      if (params.audit == '1' && params.auditStatus != '2') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentDocumentNeedsSubmittedApprovalSubmittedDemandPoolAfterApproval`, '当前单据需要提交审批,且审批通过后提交需求池！'))
        return
      }
      if (!params.purchaseRequestItemList && params.purchaseRequestItemList.length == 0) {
        this.$message.warning('提交到需求池至少有一个行项目')
        return
      }
      let that = this
      const fn = (url, params, vm) => {
        // console.log('vm :>> ', vm) // 编辑模板组件实例
        this.$confirm({
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitRequirements`, '提交需求'),
          content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitPurchaseRequisitionLineDemaIsureSubmit`, '将采购申请行提交需求池，是否确认提交?'),
          onOk() {
            let pro = new Promise((resolve, reject) => {
              let data = {
                purchaseRequestHeadId: params.id
              }
              that.$refs.editPage.confirmLoading = true
              let flag = false
              params.purchaseRequestItemList &&
                params.purchaseRequestItemList.map((item) => {
                  if (item.checkBudget == '1') flag = true
                })
              if (flag) {
                getAction('/budget/budgetManage/budgetCheck', data)
                  .then((res) => {
                    if (res.success) {
                      resolve()
                    } else {
                      that.$refs.editPage.confirmLoading = false
                      return that.$message.error(res.message)
                    }
                  })
                  .catch(() => {
                    that.$refs.editPage.confirmLoading = false
                  })
              } else {
                resolve()
              }
            })
            pro.then(() => {
              that.$refs.editPage.confirmLoading = true
              postAction(that.url.public, params)
                .then((res) => {
                  const type = res.success ? 'success' : 'error'
                  that.$message[type](res.message)
                  that.$refs.editPage.confirmLoading = false
                  if (type === 'success') {
                    that.goBack()
                  } else {
                    that.$refs.editPage.queryDetail()
                  }
                })
                .catch(() => {
                  that.$refs.editPage.confirmLoading = false
                })
            })
          },
          onCancel() {
            console.log('onCancel')
          }
        })
      }
      this.$refs.editPage.handValidate(that.url.public, params, fn)
    },
    showAuditConditionBtn() {
      let params = this.$refs.editPage ? this.$refs.editPage.form : {}
      let auditStatus = params.auditStatus
      let audit = params.audit
      if (audit == '1' && (auditStatus == '0' || auditStatus == '3')) {
        return true
      } else {
        return false
      }
    },
    showPublishConditionBtn() {
      let params = this.$refs.editPage ? this.$refs.editPage.form : {}
      if (params.id) {
        return true
      } else {
        return false
      }
    },
    downloadTemplate() {
      const form = this.$refs.editPage.getPageData()
      let params = {
        id: form.id,
        templateAccount: form.templateAccount,
        templateNumber: form.templateNumber,
        templateVersion: form.templateVersion,
        handlerName: 'purchaseRequestItemImportRpcServiceImpl',
        roleCode: 'purchase'
      }
      downFile('/base/excelByConfig/downloadTemplate', params)
        .then((data) => {
          if (!data) {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data]), 'template.xlsx')
          } else {
            let url = window.URL.createObjectURL(new Blob([data]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', 'template.xlsx')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(url) //释放掉blob对象
          }
        })
        .finally(() => {
          this.gridLoading = false
        })
    },
    importParams() {
      const form = this.$refs.editPage.getPageData()
      return {
        id: form.id,
        templateAccount: form.templateAccount,
        templateNumber: form.templateNumber,
        templateVersion: form.templateVersion,
        handlerName: 'purchaseRequestItemImportRpcServiceImpl',
        roleCode: 'purchase',
        excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionInfo`, '采购申请行信息')
      }
    },

    importExcel() {
      const form = this.$refs.editPage.getPageData()
      let params = {
        id: form.id,
        templateAccount: form.templateAccount,
        templateNumber: form.templateNumber,
        templateVersion: form.templateVersion,
        handlerName: 'purchaseRequestItemImportRpcServiceImpl',
        roleCode: 'purchase',
        excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionInfo`, '采购申请行信息')
      }
      this.$refs.itemImportExcel.open(params, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionInfo`, '采购申请行信息'), 'purchaseRequestItemList')
    },
    importCallBack(result) {
      let response = result.file.response
      if (response.result.failCount > 0) {
        this.$message.warning(response.message)
      } else {
        let itemGrid = this.$refs.editPage.$refs.purchaseRequestItemList[0]
        let insertData = response.result.dataList
        this.pageConfig.itemColumns.forEach((item) => {
          if (item.defaultValue) {
            debugger
            insertData.forEach((insert) => {
              if (!insert[item.field]) {
                insert[item.field] = item.defaultValue
              }
            })
          }
        })
        itemGrid.insertAt(insertData, -1)
        this.$refs.itemImportExcel.visible = false
      }
    },
    afterReqHandle(self, tableList) {
      const form = this.$refs.editPage.getPageData()
      let itemPurchaseGroup = null

      if (form.requestType == '1') {
        itemPurchaseGroup = 'YCL0101' // 原料采购
      } else if (form.requestType == '2') {
        itemPurchaseGroup = 'YHP0101' // 易耗品采购
      } else if (form.requestType == '3') {
        itemPurchaseGroup = '' // 维修服务类采购申请
      } else if (form.requestType == '4') {
        itemPurchaseGroup = 'BC0101' // 包材采购
      } else if (form.requestType == '5') {
        itemPurchaseGroup = '' // 工程项目采购申请
      } else if (form.requestType == '6') {
        itemPurchaseGroup = 'WX0101' // 外协采购
      } else if (form.requestType == '7') {
        itemPurchaseGroup = 'WJPJ0101' //五金配件采购
      }

      if (tableList && itemPurchaseGroup) {
        tableList.forEach((item) => {
          item.purchaseGroup = itemPurchaseGroup
        })
      }
    },
    //新增行
    // 允许添加相同物料 2022-12-23
    // 提交审批、提交需求池校验，相同物料编码且物料描述相同，工厂或要求交期必须不同，否则提示“提交失败，相同物料${物料编码 物料描述}，工厂或交期必须不同。
    insertGridItem() {
      this.selectType = 'material'
      const form = this.$refs.editPage.getPageData()
      const { mustMaterialNumber = '1' } = form

      if (mustMaterialNumber == '1') {
        let url = '/material/purchaseMaterialHead/list-head-or-item'
        let columns = [
          {
            field: 'materialNumber',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'),
            width: 150
          },
          {
            field: 'materialDesc',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
            width: 150
          },
          {
            field: 'materialName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
            width: 200
          },
          {
            field: 'cateName',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类'),
            width: 200
          },
          {
            field: 'factory_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_factoryCode`, '工厂编码'),
            width: 200
          },
          {
            field: 'materialGroup_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroup`, '物料组'),
            width: 200
          },
          {
             field: 'purchaseCycle',
             title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'),
             width: 200
          },
          {
            field: 'leadTime',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_leadTime`, '提前期'),
            width: 200
          },
          {
            field: 'ifLeadTime_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQDPA_c7b5370b`, '是否提前期'),
            width: 200
          },
          {
            field: 'checkQuality_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qualityTest`, '是否质检'),
            width: 200
          },
          {
            field: 'catalog_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_catalog`, '是否目录物料'),
            width: 200
          },
          {
            field: 'accountType_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#`, '记账方式'),
            width: 200
          }
        ]
        this.$refs.fieldSelectModal.open(
          url,
          {
            blocDel: '0',
            freeze: '0',
            filterPurchaseType: '1,3'
          },
          columns,
          'multiple'
        )
      } else {
        let itemGrid = this.$refs.editPage.$refs.purchaseRequestItemList[0]
        let itemData = {}
        this.pageConfig.itemColumns.forEach((item) => {
          if (item.defaultValue) {
            itemData[item.field] = item.defaultValue
          }
        })
        if (form.taxCode) {
          itemData['taxCode'] = form.taxCode
          itemData['taxRate'] = form.taxRate
        }

        if (form.requestType == '1') {
          itemData['purchaseGroup'] = 'YCL0101' // 原料采购
        } else if (form.requestType == '2') {
          itemData['purchaseGroup'] = 'YHP0101' // 易耗品采购
        } else if (form.requestType == '3') {
          itemData['purchaseGroup'] = '' // 维修服务类采购申请
        } else if (form.requestType == '4') {
          itemData['purchaseGroup'] = 'BC0101' // 包材采购
        } else if (form.requestType == '5') {
          itemData['purchaseGroup'] = '' // 工程项目采购申请
        } else if (form.requestType == '6') {
          itemData['purchaseGroup'] = 'WX0101' // 外协采购
        } else if (form.requestType == '7') {
          itemData['purchaseGroup'] = 'WJPJ0101' //五金配件采购
        }

        itemGrid.insertAt([itemData], -1)
      }
    },
    //?
    fieldSelectOk(data) {
      if (this.selectType == 'material') {
        let itemGrid = this.$refs.editPage.$refs.purchaseRequestItemList[0]
        let { fullData } = itemGrid.getTableData()
        // let materialList = fullData.map(item => {
        //     return item.materialNumber
        // })
        let itemNumber = fullData.length > 0 ? Number(fullData[fullData.length - 1]['itemNumber']) + 1 : 1
        //过滤已有数据
        let insertData = data.filter((item, i) => {
          // 过滤时间
          item.createTime = null
          item.createBy = null
          item.updateTime = null
          item.updateBy = null
          item.purchaseOrg = null
          item.company = null
          item.purchaseType = 'BZ'
          item.itemNumber = itemNumber + i
          if (!item.accountType) {
            //记账方式为空时默认为标准
            item.accountType = 'bz'
          }
          item.unitQuantity = item.baseUnit
          item.fbk2 = item.purchaseCycle  //采购周期
          item.remark = null //备注不要带_close
          // return !materialList.includes(item.materialNumber)
          return true
        })

        insertData = insertData.map((item) => {
          item['itemStatus'] = '0'
          item['id'] = null
          //属性置空
          // item['purchaseType'] = null
          return item
        })
        itemGrid.insertAt(insertData, -1)
      }
    },
    //删除复选框选定行
    deleteGridItem() {
      let itemGrid = this.$refs.editPage.$refs.purchaseRequestItemList[0]
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据!'))
        return
      }
      let arr = []
      checkboxRecords.forEach((item, index) => {
        let itemStatus = item.itemStatus
        if (itemStatus !== '-1' && itemStatus !== '0') {
          arr.push(item.itemNumber)
        }
      })
      if (arr.length > 0) {
        let str = arr.join(',')
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGzELIYMWVIjtF_bd7c561f`, '只能删除状态为已退回，新建的单据。') + ' ' + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_n_7b2c`, '第') + str + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cxOQG_a6151b20`, '行不能删除'))
        return
      }
      checkboxRecords.forEach((item) => {
        //如果是退回状态的单设置为为12 退回已删除
        if (item.itemStatus == '-1') {
          item.itemStatus = '12'
        }
      })
      //移除状态为新建的单
      let rows = checkboxRecords.filter((rs) => rs.itemStatus == '0')
      itemGrid.remove(rows)
    },
    saveEvent(type) {
      let formData = this.$refs.editPage ? this.$refs.editPage.form : {}
      if (formData.requestStatus == '5' || formData.requestStatus == '6') {
        return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterBudgetVerificationitCannotEditeSaved`, '预算校验后不可编辑保存！'))
      }
      const form = this.$refs.editPage.getPageData().purchaseRequestItemList
      if (form && form.length > 0) {
        let flag = true
        let arr = []
        form.forEach((item, index) => {
          let materialNumber = item.materialNumber
          if (formData.mustMaterialNumber == 1) {
            if (materialNumber == null || materialNumber == '') {
              arr.push(index + 1)
              flag = false
            }
          }
          if (formData.mustMaterialNumber == 0) {
            //物料编码为空时校验记账方式是必填的
            if (item.accountType == '' || item.accountType == null) {
              this.$message.error('第' + (index + 1) + '行的申请行的记账方式不能为空')
              flag = false
              return
            }
            //物料组必填
            // if(item.materialGroup=='' || item.materialGroup==null){
            //     this.$message.error( '第'+(index+1)+'行的申请行的物料组不能为空' )
            //     flag = false
            //     return
            // }
            //采购单位必填
            if (item.purchaseUnit == '' || item.purchaseUnit == null) {
              this.$message.error('第' + (index + 1) + '行的申请行的采购单位不能为空')
              flag = false
              return
            }
            if (materialNumber != '' || materialNumber != null) {
              arr.push(index + 1)
              flag = false
            }
          }
        })
        /**
         * 成本中心：采购申请“记账方式”为费用时，成本中心必填
         * 资产中心：采购申请“记账方式”为资产时，资产中心必填
         * 项目编号：采购申请“记账方式”为项目时，项目编号必填
         */
        form.forEach((item, index) => {
          if (item.accountType == 'fy') {
            if (!item.costCenter) {
              this.$message.error('第' + (index + 1) + '行的申请行的记账方式为费用，成本中心不能为空')
              return
            }
          }
          if (item.accountType == 'zc') {
            if (!item.assertCenter) {
              this.$message.error('第' + (index + 1) + '行的申请行的记账方式为资产，资产中心不能为空')
              return
            }
          }
          if (item.accountType == 'xm') {
            if (!item.projectNumber) {
              this.$message.error('第' + (index + 1) + '行的申请行的记账方式为项目，项目编号不能为空')
              return
            }
          }
        })
        if (flag) {
          this.$refs.editPage.postData()
        } else {
          let str = arr.join(',')
          if (formData.mustMaterialNumber == 1) {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tFSLAoIiFlSW_4440b2fd`, '单据物料编码已选择必填！') + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRUVcn_bf17a1ca`, '采购申请行第') + str + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cSLAoLSM_45078791`, '行物料编码未填写'))
            return
          } else if (formData.mustMaterialNumber == 0) {
            this.$refs.editPage.postData()
          }
        }
      } else {
        this.$refs.editPage.postData()
      }
    },
    save() {
      let param = this.$refs.editPage ? this.$refs.editPage.form : {}
      if (param.purchaseRequestItemList.length == 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
        return false
      }
      this.saveEvent()
    },
    showFlow() {
      this.flowId = this.$refs.editPage.form.flowId
      if (!this.flowId) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
        return
      }
      this.flowView = true
    },
    closeFlowView() {
      this.flowView = false
    },
    getBudgetCheck(id, data) {
      return new Promise((resolve, reject) => {
        let data = {
          purchaseRequestHeadId: id
        }
        getAction('/budget/budgetManage/budgetCheck', data).then((res) => {
          if (res.success) {
            resolve(id)
          } else {
            this.$message.error(res.message)
            this.$refs.editPage.confirmLoading = false
          }
        })
        // }
      })
    },
    getDataInfo(id) {
      return new Promise((resolve, reject) => {
        let data = {
          id: id
        }
        getAction('/demand/purchaseRequestHead/queryById', data).then((res) => {
          if (res.success) {
            resolve(res.result)
          } else {
            this.$message.error(res.message)
            this.$refs.editPage.confirmLoading = false
          }
        })
      })
    },
    async handleApproval(formData) {
      let that = this
      
      let defaultAduitSubject = that.$srmI18n(`${that.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请单号') + '：' + formData.requestNumber
      let auditSubject = await that.$FlowUtil.convertFlowTitle(formData, defaultAduitSubject)
      
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterSubmittingApprovalItcannotIsurSubmitApproval?`, '提交审批后将不能修改，是否确认提交审批?'),
        onCancel: () => {
          that.$refs.editPage.confirmLoading = false
        },
        onOk: function () {
          console.log(formData)
          debugger
          let param = {}
          param['businessId'] = formData.id
          param['rootProcessInstanceId'] = formData.flowId
          param['businessType'] = 'purchaseRequest'
          param['auditSubject'] = auditSubject
          param['params'] = JSON.stringify(formData)
          httpAction(that.url.submitAudit, param, 'post')
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$parent.submitCallBack(formData)
                that.init()
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.$refs.editPage.confirmLoading = false
            })
        }
      })
    },
    submitAudit() {
      let formData = this.$refs.editPage ? this.$refs.editPage.form : {}
      let params = this.$refs.editPage.getPageData()
      params.cateCode = params.purchaseRequestItemList[0].cateCode // 流程使用，用户说明细行的物料类型都是一样的
      if (formData.requestNumber == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
        return
      }
      const _this = this
      this.$refs.editPage.handValidate(null, null, function () {
        let itemGrid = _this.$refs.editPage.$refs.purchaseRequestItemList[0]
        let { fullData } = itemGrid.getTableData()
        if (fullData.length <= 0) {
          return _this.$message.warning(_this.$srmI18n(`${_this.$getLangAccount()}#i18n_alert_empty_addLine`, '请添加行项目'))
        }
        _this.$refs.editPage.confirmLoading = true
        postAction(_this.url.edit, params).then((res) => {
          _this.$refs.editPage.confirmLoading = false
          if (res.success) {
            _this.$refs.editPage.confirmLoading = true
            let flag = false
            fullData.map((item) => {
              if (item.checkBudget == '1') flag = true
            })
            if (flag) {
              _this.getBudgetCheck(formData.id, params).then((id) => {
                _this.getDataInfo(id).then((res) => {
                  _this.handleApproval(res)
                })
              })
            } else {
              console.log(res.result)
              _this.handleApproval(res.result)
            }
            _this.$refs.editPage.queryDetail()
          } else {
            _this.$message.warning(res.message)
          }
          _this.$refs.editPage.queryDetail()
        })
      })
    }
  }
}
</script>
