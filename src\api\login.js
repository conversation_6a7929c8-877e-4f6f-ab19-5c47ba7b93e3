import api from './index'
import { axios } from '@/utils/request'

/**
 * login func
 * parameter: {
 *     username: '',
 *     password: '',
 *     remember_me: true,
 *     captcha: '12345'
 * }
 * @param parameter
 * @returns {*}
 */
export function login (parameter) {
    return axios({
        url: '/account/login',
        method: 'post',
        data: parameter
    })
}

export function loginByToken (parameter) {
    return axios({
        url: '/account/loginByToken',
        method: 'get',
        params: parameter
    })
}

export function phoneLogin (parameter) {
    return axios({
        url: '/sys/phoneLogin',
        method: 'post',
        data: parameter
    })
}

export function getSmsCaptcha (parameter) {
    return axios({
        url: api.SendSms,
        method: 'post',
        data: parameter
    })
}

export function getInfo () {
    return axios({
        url: '/api/user/info',
        method: 'get',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    })
}

export function logout (logoutToken) {
    return axios({
        url: '/account/logout',
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'X-Access-Token': logoutToken
        }
    })
}
export function getUserInfo (parameter) {
    return axios({
        url: '/account/elsTenant/noToken/queryByParam',
        method: 'get',
        params: parameter
    })
}