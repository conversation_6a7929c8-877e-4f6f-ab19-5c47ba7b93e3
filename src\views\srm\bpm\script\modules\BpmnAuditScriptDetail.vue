<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'

export default {
    name: 'BpmnAuditScriptDetail',
    components: {
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            confirmLoading: false,
            refresh: true,
            requestData: {
                detail: { url: '/a1bpmn/audit/script/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ]
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = 'TC2022073101'
            let templateVersion = '1'
            let account = '100000'
            return `${account}/purchase_bpmnScript_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
    }
}
</script>