<template>
  <div
    class="clearfix newsBox"
  >
    <div class="title">{{ $srmI18n(`${$getLangAccount()}#i18n_title_messageRecord`, '消息记录') }}</div>
    <div
      class="news-box"
      :style="{height: `${height}px`}">
      <ul>
        <li
          v-for="(newItem, index) in newsList"
          :key="index">
          <span class="margin-r-10">{{ newItem.createTime }}</span>
          <span>{{ newItem.content }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
    props: {
        formData: {
            default: () => {
                return {}
            },
            type: Object
        },
        openInfoRecordsList: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    computed: {
        newsList () {
            return this.openInfoRecordsList
        }
    },
    data () {
        return {
            height: 400
        }
    },
    created () {
        this.height = document.documentElement.clientHeight - 250
    }
}
</script>
<style lang="less" scoped>
ul, li{
    margin: 0;
    padding: 0;
}
li{
    list-style-type: none;
}
.fl{
    float: left;
}
.fr{
    float: right;
}
.clearfix {
    clear: both;
}
.margin-r-10{
    margin-right: 10px;
}
.text-align-left{
    text-align:left;
}
.title{
    padding: 5px;
    background: #eee;
    margin-bottom: 5px;
}
.newsBox{
    padding: 0 10px;
    .news-box{
    background: #F0F2F5;
    border: 1px solid #DBDBDB;
    padding: 10px 10px 0;
    overflow: auto;
  }
}
</style>