<template>
  <div style="height:100%">

    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <els-barcode-template-head-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <els-barcode-template-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <div
      class="shadow-wrap"
      v-if="confirmLoading">
      <a-spin></a-spin>
      <span>printing</span>
    </div>
  </div>
</template>
<script>
import ElsBarcodeTemplateHeadEdit from './modules/ElsBarcodeTemplateHeadEdit'
import ElsBarcodeTemplateHeadDetail from './modules/ElsBarcodeTemplateHeadDetail'
import {getAction, postAction } from '@/api/manage'
import { ListMixin } from '@comp/template/list/ListMixin'
import { getLodop } from '@/utils/LodopFuncs'

var LODOP, P_ID='', JobOk, JobExit, timeOutFunt, count=0 //声明为全局变量
function getStatusValue (ValueType, ValueIndex, oResultOB){
    LODOP=getLodop()
    if (LODOP.CVERSION) LODOP.On_Return=function (TaskID, Value){oResultOB.value=Value}
    var strResult=LODOP.GET_VALUE(ValueType, ValueIndex)
    if (!LODOP.CVERSION) {
        return strResult
    } else {
        return ''
    }
}
export default {
    mixins: [ListMixin],
    components: {
        ElsBarcodeTemplateHeadEdit,
        ElsBarcodeTemplateHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            confirmLoading: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeTemplate',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.showEdit
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_fWUB_2dfe5201`, '打印预览'),
                        clickFn: this.preview
                    }
                ]
            },
            url: {
                list: '/base/barcode/elsBarcodeTemplateHead/list',
                add: '/base/barcode/elsBarcodeTemplateHead/add',
                delete: '/base/barcode/elsBarcodeTemplateHead/delete',
                deleteBatch: '/base/barcode/elsBarcodeTemplateHead/deleteBatch',
                templatePreview: '/base/barcode/elsBarcodeTemplateHead/templatePreview',
                savePoolRecordByPrint: '/base/barcode/purchaseBarcodePoolHead/savePoolRecordByPrint',
                columns: 'elsBarcodeTemplateHeadList'
            }
        }
    },
    methods: {
        handleAdd () {
            this.currentEditRow = {}
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
            this.visible = false
            this.submitLoading = false
        },
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        preview (row) {
            if (count>0) {
                that.$message.warning('正打印中...')
                return
            }
            let that = this
            getAction(this.url.templatePreview+'?id='+row.id, {}).then((res) => {
                if (res.success) {
                    LODOP = getLodop()//调用getLodop获取LODOP对象
                    eval(res.result.template)
                    LODOP.SET_PRINT_COPIES(res.result.printNumber) //打印份数
                    LODOP.SET_PRINT_MODE('CATCH_PRINT_STATUS', true)
                    if (LODOP.CVERSION) {
                        LODOP.On_Return=function (TaskID, Value){
                            P_ID=Value
                            if (P_ID!='') {	count=0; that.C_WaitFor(P_ID, row)}
                        }
                        LODOP.PREVIEW()
                    } else {
                        P_ID=LODOP.PREVIEW()
                        if (P_ID!='') {	count=0; that.WaitFor(P_ID, that, row) }
                    }
                    // let P_ID = LODOP.PRINTA()
                    // if (P_ID!='') {	that.WaitFor(that, P_ID)}
                    // LODOP.PREVIEW()

                    // if (LODOP.CVERSION) {
                    //     LODOP.On_Return=function (TaskID, Value){
                    //         P_ID=Value
                    //         document.getElementById('T12A').value=P_ID
                    //         if (P_ID!='') {	c=0;C_WaitFor()}
                    //     }
                    //     LODOP.PRINTA()
                    // } else {
                    //     P_ID=LODOP.PRINTA()
                    //     document.getElementById('T12A').value=P_ID
                    //     if (P_ID!='') {	c=0; WaitFor()}
                    // }
                } else {
                    that.confirmLoading = false
                    that.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        C_WaitFor (P_ID, row){debugger
            if (!this.confirmLoading){
                this.confirmLoading = true
            }
            let that = this
            count++
            console.log('正等待(JOB代码是'+P_ID+')打印结果：'+count+'秒')
            timeOutFunt=setTimeout(() => {this.C_WaitFor(P_ID, row)}, 1000)
            LODOP.On_Return_Remain=true
            LODOP.On_Return=function (TaskID, Value){
                console.log('进入onReturn: task:'+TaskID+'  JobExit:'+JobExit+'   Value:'+Value)
                // console.log('进入onReturn: task:'+TaskID+'   JobOk:'+JobOk+'  JobExit:'+JobExit+'   Value:'+Value)
                //TODO 判断打印状态是否成功，不同打印机状态码可能不一样，需要对应调整
                if (TaskID==JobExit){
                    if (Value==0){
                        clearTimeout(timeOutFunt)
                        console.log('打印成功！')
                        count=0

                        // 回写使用记录
                        if ( row.record == 1){
                            const params = {
                                templateId: row.id,
                                businessType: '条码打印'
                            }
                            postAction(that.url.savePoolRecordByPrint, params).then((res) => {

                                if (res.success) {
                                    that.$message.success('打印成功！')
                                } else {
                                    that.$message.warning(res.message)
                                }
                            }).finally(() => {
                                that.confirmLoading = false
                            })
                        }
                    }
                }
                // if (TaskID==JobOk){
                //     if (Value==1 || Value==12288){alert(TaskID+' '+Value)
                //         clearTimeout(timeOutFunt)
                //         console.log('打印成功！')
                //         count=0
                //         // 回写使用记录
                //         if ( row.record == 1){
                //             const params = {
                //                 templateId: row.id,
                //                 businessType: '条码打印'
                //             }
                //             postAction(this.url.savePoolRecordByPrint, params).then((res) => {
                //                 if (res.success) {
                //                     this.$message.success('打印成功！')
                //                 } else {
                //                     this.$message.warning(res.message)
                //                 }
                //             }).finally(() => {
                //             })
                //         }
                //     }
                // } else
                // if (TaskID==JobExit){
                //     if (Value==0){
                //         clearTimeout(timeOutFunt)
                //         console.log('打印任务被删除！')
                //         count=0
                //         alert('打印任务被删除！')
                //     }
                // }
            }
            // JobOk=LODOP.GET_VALUE('PRINT_STATUS_OK', P_ID)
            JobExit=LODOP.GET_VALUE('PRINT_STATUS_EXIST', P_ID)
            // console.log('taskId1:'+JobOk)
            console.log('taskId2:'+JobExit)
            if (count>120){
                clearTimeout(timeOutFunt)
                console.log('打印超时(120秒)！')
                count=0
                alert('打印超过120秒没捕获到成功状态！')
            }
        },
        WaitFor (P_ID, row){
            if (!this.confirmLoading){
                this.confirmLoading = true
            }
            count=count+1
            console.log('正等待(JOB代码是'+P_ID+')打印结果：'+count+'秒')
            timeOutFunt=setTimeout(() => {this.WaitFor(P_ID, row)}, 1000)
            if (LODOP.GET_VALUE('PRINT_STATUS_OK', P_ID)) {
                clearTimeout(timeOutFunt)
                console.log('打印成功！')
                count=0
                alert('打印成功！')
            }if ((!LODOP.GET_VALUE('PRINT_STATUS_EXIST', P_ID))&&(count>0)) {
                clearTimeout(timeOutFunt)
                console.log('打印任务被删除！')
                count=0
                alert('打印任务被删除！')
            } else if (count>30){
                clearTimeout(timeOutFunt)
                console.log('打印超时(30秒)！')
                count=0
                alert('打印超过30秒没捕获到成功状态！')
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.shadow-wrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  z-index: 10000;
  background-color: rgba(0,0,0,0.1);
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  color: #fff;
}
</style>