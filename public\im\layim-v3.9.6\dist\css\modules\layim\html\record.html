<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">

<title></title>
<script>
</script>

<script src="/im/layim-v3.9.6/dist/lay/modules/recorder.mp3.min.js"></script>
<script src="/im/layim-v3.9.6/dist/lay/modules/frequency.histogram.view.js"></script>
<script src="/im/layim-v3.9.6/dist/lay/modules/lib.fft.js"></script>


</head>
<body>

<!-- 【2】构建界面 -->
<div class="main">
	
	<div class="mainBox">
		
		<!-- 波形绘制区域 -->
		<div class="pd recpower">
			<div style="height:0px;width:332px;background:#999;position:relative;">
				<div class="recpowerx" style="height:40px;background:#0B1;position:absolute;"></div>
				<div class="recpowert" style="padding-left:50px; line-height:40px; position: relative;"></div>
			</div>
		</div>
		<div class="pd waveBox">
			<div ><div style="height:100px;width:332px;" class="recwave"></div></div>
		</div>
	</div>
	
	<!-- 日志输出区域 -->
	<div class="mainBox">
		<div class="reclog"></div>
	</div>
</div>


<!-- 【3】实现录音逻辑 -->
<script>
var rec,wave,recBlob;
var token = localStorage.getItem('t_token');
const mglobalSrmI18n = window.parent.globalSrmI18n ? window.parent.globalSrmI18n : function(){}
const mglobalGetLangAccount = window.parent.globalGetLangAccount ? window.parent.globalGetLangAccount : function(){}
/**调用open打开录音请求好录音权限**/
var recOpen=function(cb){//一般在显示出录音按钮或相关的录音界面时进行此方法调用，后面用户点击开始录音时就能畅通无阻了
	rec=null;
	wave=null;
	recBlob=null;
	var newRec=Recorder({
		type:"mp3",sampleRate:16000,bitRate:16 //mp3格式，指定采样率hz、比特率kbps，其他参数使用默认配置；注意：是数字的参数必须提供数字，不要用字符串；需要使用的type类型，需提前把格式支持文件加载进来，比如使用wav格式需要提前加载wav.js编码引擎
		,onProcess:function(buffers,powerLevel,bufferDuration,bufferSampleRate,newBufferIdx,asyncEnd){
			//录音实时回调，大约1秒调用12次本回调
			// document.querySelector(".recpowerx").style.width=powerLevel+"%";
			// document.querySelector(".recpowert").innerText=bufferDuration+" / "+powerLevel;
			//可视化图形绘制
			wave.input(buffers[buffers.length-1],powerLevel,bufferSampleRate)
		}
	});

	createDelayDialog(); //我们可以选择性的弹一个对话框：为了防止移动端浏览器存在第三种情况：用户忽略，并且（或者国产系统UC系）浏览器没有任何回调，此处demo省略了弹窗的代码
	newRec.open(function(){//打开麦克风授权获得相关资源
		dialogCancel(); //如果开启了弹框，此处需要取消
		
		rec=newRec;
		
		//此处创建这些音频可视化图形绘制浏览器支持妥妥的
		wave=Recorder.FrequencyHistogramView({elem:".recwave"});
		cb && cb({type: 'success'})
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_IfvHWWqICtHRvKHWr_9334b6b5", "已打开录音，可以点击录制开始录音了"),2);
	},function(msg,isUserNotAllow){//用户拒绝未授权或不支持
		dialogCancel(); //如果开启了弹框，此处需要取消
		reclog((isUserNotAllow?"UserNotAllow，":"")+mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_fvHWKmW_eb912c9b", "打开录音失败：")+msg,1);
	});
	
	window.waitDialogClick=function(){
		dialogCancel();
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_fvKmWbWVVqDN_815fc122", "打开失败：权限请求被忽略")+"，<span style='color:#f00'>"+ mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_jDdOCtjJc_b4add7c4", "用户主动点击的弹窗") + "</span>",1);
	};
};

/**关闭录音，释放资源**/
function recClose(){
	if(rec){
		rec.close();
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_IRl_16b1c4c", "已关闭"));
	}else{
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_LfvHW_dbe79d35", "未打开录音"),1);
	};
};



/**开始录音**/
function recStart(){//打开了录音后才能进行start、stop调用

	if(rec&&Recorder.IsOpen()){
		recBlob=null;
		rec.start();
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_IvKHWWWW_2e577853", "已开始录音..."));
	}else{
		recOpen()
		alert(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_UWQBALlbW_415a0d85", "语音浏览器未授权！"))
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_LfvHW_dbe79d35", "未打开录音"),1);
	};
};

/**暂停录音**/
function recPause(){
	if(rec&&Recorder.IsOpen()){
		rec.pause();
	}else{
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_LfvHW_dbe79d35", "未打开录音"),1);
	};
};
/**恢复录音**/
function recResume(){
	if(rec&&Recorder.IsOpen()){
		rec.resume();
	}else{
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_LfvHW_dbe79d35", "未打开录音"),1);
	};
};

/**结束录音，得到音频文件**/
function recStop(cb){
	if(!(rec&&Recorder.IsOpen())){
		cb({error: 1, text: mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_LfvHW_dbe79d35", "未打开录音")})
		// reclog("未打开录音",1);
		return;
	};
	rec.stop(function(blob,duration){
		console.log(blob,(window.URL||webkitURL).createObjectURL(blob),"时长:"+duration+"ms");
		
		recBlob=blob;
		recUpload(cb)
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_IHRWWWW_d94e0b9d", "已录制mp3：")+duration+"ms "+blob.size+mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_JyWqICtrCWXVr_a57103d9", "字节，可以点击播放、上传了"),2);
	},function(msg){
		cb({error: 1, text: mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_LvKPE_da568ca0", "未开始讲话")})
		// reclog("录音失败:"+msg,1);
	});
};









/**播放**/
function recPlay(){
	if(!recBlob){
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_VWHWWHSYRSKrC_336ae58b", "请先录音，然后停止后再播放"),1);
		return;
	};
	var cls=("a"+Math.random()).replace(".","");
	reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_rCs_1867e1c", "播放中")+': <span class="'+cls+'"></span>');
	var audio=document.createElement("audio");
	audio.controls=true;
	document.querySelector("."+cls).appendChild(audio);
	//简单利用URL生成播放地址，注意不用了时需要revokeObjectURL，否则霸占内存
	audio.src=(window.URL||webkitURL).createObjectURL(recBlob);
	audio.play();
	
	setTimeout(function(){
		(window.URL||webkitURL).revokeObjectURL(audio.src);
	},5000);
};

/**上传**/
function recUpload(cb){
	var blob=recBlob;
	if(!blob){
		reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_VWHWWHSYRSKXV_336811b0", "请先录音，然后停止后再上传"),1);
		return;
	};
	const api = `${window.location.origin}/els/im/user/uploadFiles?X-Access-Token=${token}`
	var onreadystatechange=function(){
		return function(){
			if(xhr.readyState==4){
				if(xhr.status==200){
					console.log(xhr)
					var json = JSON.parse(xhr.responseText)
					cb && cb(json.data)
				}else{
					reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_BjMLXVWOKXVnRSTRdXVyRWRPQBAVRECWWWWWWWLrCAujVVWFyRKUAjOWWrW_5affd5ea", "没有完成上传，演示上传地址无需关注上传结果，只要浏览器控制台内Network面板内看到的请求数据结构是预期的就ok了。"), "#d8c1a0")
					console.error(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_XVKm_24addeea", "上传失败"),xhr.status, xhr.responseText)
				};
			};
		};
	};
	reclog(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_vKXVu_64b907ef", "开始上传到")+api+"，"+mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_VVRSWWW_a6734ae2", "请求稍后..."));
	var form=new FormData();
	var name = new Date().getTime() + '.mp3'
	form.append("upfile",blob, name);
	
	var xhr=new XMLHttpRequest();
	xhr.open("POST", api);
	xhr.onreadystatechange=onreadystatechange();
	xhr.send(form);
};










//recOpen我们可以选择性的弹一个对话框：为了防止移动端浏览器存在第三种情况：用户忽略，并且（或者国产系统UC系）浏览器没有任何回调
var showDialog=function(){
	if(!/mobile/i.test(navigator.userAgent)){
		return;//只在移动端开启没有权限请求的检测
	};
	dialogCancel();
	
	//显示弹框，应该使用自己的弹框方式
	var div=document.createElement("div");
	document.body.appendChild(div);
	div.innerHTML=(''
		+'<div class="waitDialog" style="z-index:99999;width:100%;height:100%;top:0;left:0;position:fixed;background:rgba(0,0,0,0.3);">'
			+'<div style="display:flex;height:100%;align-items:center;">'
				+'<div style="flex:1;"></div>'
				+'<div style="width:240px;background:#fff;padding:15px 20px;border-radius: 10px;">'
					+'<div style="padding-bottom:10px;">'+mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_HWROTPxqGbWWViTWNRLAuLnVVWVCtDN_e9f497ef", "录音功能需要麦克风权限，请允许；如果未看到任何请求，请点击忽略")+'</div>'
					+'<div style="text-align:center;"><a onclick="waitDialogClick()" style="color:#0B1">'+mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_DN_c1508", "忽略")+'</a></div>'
				+'</div>'
				+'<div style="flex:1;"></div>'
			+'</div>'
		+'</div>');
};
var createDelayDialog=function(){
	dialogInt=setTimeout(function(){//定时8秒后打开弹窗，用于监测浏览器没有发起权限请求的情况，在open前放置定时器利于收到了回调能及时取消（不管open是同步还是异步回调的）
		showDialog();
	},8000);
};
var dialogInt;
var dialogCancel=function(){
	clearTimeout(dialogInt);
	
	//关闭弹框，应该使用自己的弹框方式
	var elems=document.querySelectorAll(".waitDialog");
	for(var i=0;i<elems.length;i++){
		elems[i].parentNode.removeChild(elems[i]);
	};
};
//recOpen弹框End
</script>








<script>
function reclog(s,color){
	var now=new Date();
	var t=("0"+now.getHours()).substr(-2)
		+":"+("0"+now.getMinutes()).substr(-2)
		+":"+("0"+now.getSeconds()).substr(-2);
	console.log('['+t+']', s)
};
window.onerror=function(message, url, lineNo, columnNo, error){
	//https://www.cnblogs.com/xianyulaodi/p/6201829.html
	reclog('<span style="color:red">【Uncaught Error】'+message+'<pre>'+"at:"+lineNo+":"+columnNo+" url:"+url+"\n"+(error&&error.stack||"不能获得错误堆栈")+'</pre></span>');
};

</script>



<style>
body{
	word-wrap: break-word;
	background:#f5f5f5 center top no-repeat;
	background-size: auto 680px;
}
pre{
	white-space:pre-wrap;
}
a{
	text-decoration: none;
	color:#06c;
}
a:hover{
	color:#f00;
}


.btns button{
	display: inline-block;
	cursor: pointer;
	border: none;
	border-radius: 3px;
	background: #f60;
	color:#fff;
	padding: 0 15px;
	margin:3px 20px 3px 0;
	line-height: 36px;
	height: 36px;
	overflow: hidden;
	vertical-align: middle;
}
.btns button:active{
	background: #f00;
}

.pd{
	padding:0 0 6px 0;
}
.lb{
	display:inline-block;
	vertical-align: middle;
	background:#00940e;
	color:#fff;
	font-size:14px;
	padding:2px 8px;
	border-radius: 99px;
}
</style>

</body>
</html>