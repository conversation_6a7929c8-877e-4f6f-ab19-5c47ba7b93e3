<template>
  <div class="CustomUpload">
    <slot
      :openFunc="showModal">
      <a-button
        :disabled="disabled"
        type="primary"
        icon="cloud-upload"
        @click="showModal">
        {{ title }}
      </a-button>
    </slot>
    <custom-upload-form
      ref="customUploadForm"
      :isList="isList"
      :visible="visible"
      :isGridUpload="isGridUpload"
      :srmFileType="srmFileType"
      :factoryOptions="factoryOptions"
      :single="single"
      :requiredFileType="requiredFileType"
      :disabledItemNumber="disabledItemNumber"
      :property="property"
      :accept="localAccept"
      :currentEditRow="currentEditRow" 
      v-bind="$attrs"
      @custom_upload_cancel="handleCancel"
      @custom_upload_create="handleCreate"
    />
  </div>
</template>

<script>
/**
 * CustomUpload
 * @description 自定义上传组件
 * @property {Boolean} single 是否单选
 * @property {Boolean} requiredFileType 是否校验文件类型, 默认为 false
 * @property {Boolean} disabledItemNumber 是否置灰文件所属行项目单选选项
 * @property {String} property 获取行项目下拉列表 label 名称，物料行项目取值, 默认取 'materialDesc'
 * @property {Boolean} visible 控制上传组件 modal 显隐
 * @property {String} title 标题
 * @property {Number} limitSize 限制大小 单位为m，不传默认是 100m
 * @property {Array} itemInfo 父级物料行项目信息
 * @property {String} accept 接受上传的文件类型
 * @property {String} headers 配置 X-Access-Token
 * @property {Object} data 配置 businessType 各模块上传类型，headId 表头 id, 必填
 * @event {Function} beforeCheckedCallBack 上传前校验，须返回一个 promise
 * @event {Function} callback 点击确定上传文件, 完成后回调, 须绑定 callback 方法
 */
import {ajaxFindDictItems} from '@/api/api'
import {USER_INFO} from '@/store/mutation-types'
import CustomUploadForm from './CustomUploadForm.vue'
import { srmI18n, getLangAccount} from '@/utils/util'
let i18nTitle = srmI18n(`${getLangAccount()}#i18n_title_UpAtachments`, '附件上传')

export default {
    components: {CustomUploadForm},
    props: {
        disabled: {
            type: Boolean,
            default: false
        },
        accept: {
            type: String,
            default: ''
        },
        acceptDictCode: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: i18nTitle
        },
        isList: {
            type: Boolean,
            default: false
        },
        visible: {
            type: Boolean,
            default: false
        },
        isGridUpload: {
            type: Boolean,
            default: false
        },
        single: {
            type: Boolean,
            default: false
        },
        requiredFileType: {
            type: Boolean,
            default: false
        },
        disabledItemNumber: {
            type: Boolean,
            default: false
        },
        property: {
            type: String,
            default: 'materialDesc'
        },
        dictCode: {
            type: String,
            default: ''
        },
        useLocalAccept: {
            type: Boolean,
            default: false
        },
        limitSize: {
            type: Number,
            default: () => {
                return 100
            }
        },
        openOnce: { // 兼容供应商交货计划，遍历多个editLayout，上传按钮触发多次
            type: Boolean,
            default: false
        },
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            userInfo: {},
            localAccept: this.accept,
            srmFileType: [],
            factoryOptions: []
        }
    },
    watch: {
        visible (flag) {
            if (flag && !this.openOnce) {
                this.showModal()
            }
        }
    },
    created () {
        this.getUserInfo()
        this.getSrmFileType()
        this.getFactoryOptions()
        if (!this.useLocalAccept) {
            this.getAccept()
        }
    },
    methods: {
        getAccept () {
            let busAccount = this.currentEditRow.busAccount || this.userInfo.elsAccount
            const params = {
                busAccount,
                dictCode: this.acceptDictCode
            }
            if(!this.acceptDictCode) return
            ajaxFindDictItems(params).then(res => {
                if (res.success) {
                    const result = res.result || []
                    this.localAccept = result.map(rs=> rs.value).join(', ')
                }
            })
        },
        getUserInfo () {
            this.userInfo = this.$ls.get(USER_INFO)
        },
        getSrmFileType () {
            let busAccount = this.currentEditRow.busAccount || this.userInfo.elsAccount
            console.log('customUploadForm busAccount :>> ', busAccount)
            const params = {
                busAccount,
                dictCode: this.dictCode
            }
            if(!this.dictCode) return
            ajaxFindDictItems(params).then(res => {
                if (res.success) {
                    this.srmFileType = res.result || []
                }
            })
        },
        getFactoryOptions () {
            let busAccount = this.currentEditRow.busAccount || this.userInfo.elsAccount
            const params = {
                busAccount,
                dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"'
            }
            ajaxFindDictItems(params).then(res => {
                console.log('res :>> ', res)
                if (res.success) {
                    this.factoryOptions = res.result || []
                }
            })
        },
        showModal () {
            if (this.$attrs.data && !this.$attrs.data.headId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                this.$emit('update:visible', false)
                this.$emit('changeStatus', false)
                return
            }
            this.$refs.customUploadForm.open()
            this.$emit('update:visible', true)
            this.$emit('changeStatus', true)
        },
        handleCancel () {
            this.$emit('update:visible', false)
            this.$emit('changeStatus', false)
        },
        handleCreate (result) {
            this.$emit('change', result)
            this.$refs.customUploadForm.form.resetFields()
            this.handleCancel()
        }
    }
}
</script>

<style lang="less">
.CustomUpload {
  display: inline-block;
}
</style>
