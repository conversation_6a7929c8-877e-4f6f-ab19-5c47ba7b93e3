<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="masterSlave"
        :pageStatus="pageStatus"
        @handleTabChange="handleTabChange"
        @stepChange="stepHandle"
        v-on="businessHandler">
        <template #tenderProcessModelItemListTab="{ slotProps }">
          <nodeCheckBox
            ref="nodeCheckBox"
            :currentEditRow="currentEditRow"
            :nodeData="nodeData"></nodeCheckBox>
        </template>
      </business-layout>
      <a-modal
        v-drag    
        v-model="showNode"
        :footer="null"
        width="1200px"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_QLIrUB_58cb0bac`, '流程模板预览')">
        <IotStep
          :stepList="periodTypeInfoArray"
          :allNodeMap="allNodeMap"> </IotStep>
      </a-modal>
      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import nodeCheckBox from './components/nodeCheckBox'
import IotStep from './components/IotStep'
import { getAction, postAction, httpAction } from '@/api/manage'

export default {
    name: 'PurchaseSupplierCapacityHead',
    components: {
        BusinessLayout,
        nodeCheckBox,
        IotStep,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            businessRefName: 'businessRef',
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            showNode: false,
            requestData: {
                detail: {
                    url: '/tender/tenderProcessModelHead/queryById',
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {},
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/tenderProcessModelHead/edit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save',
                    showMessage: true,
                    click: this.handleSave
                    // handleBefore: this.handleSaveBefore,
                    // handleAfter: this.handleSaveAfter
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLUB_3387242e`, '流程预览'),
                    click: this.handleShowNode,
                    show: this.showNodeView,
                    key: 'preview'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            nodeData: {},
            groupCode: '',
            periodTypeInfoArray: {},
            allNodeMap: {},
            step: 0,
            url: {
                add: '/tender/tenderProcessModelHead/add',
                edit: '/tender/tenderProcessModelHead/edit',
                publish: '/tender/tenderProcessModelHead/publish',
                queryByGruop: '/tender/tenderProcessModelHead/queryNodeByGruop'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_tenderProcessModel_${templateNumber}_${templateVersion}`
        },
        pageStatus () {
            return this.currentEditRow.status == '1' ? 'detail' : 'edit'
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            pageConfig.groups.forEach(group => {
                if (group.groupCode == 'baseForm') {
                    group.formFields.forEach(field => {
                        if (field.fieldName == 'bidOpenType' || field.fieldName == 'evaluationType') {
                            // field['filterSelectList'] = [this.$srmI18n(`${this.$getLangAccount()}#i18n_title_all`, '全部'), this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_WI_fa72c]`, '线下')]
                            field['filterSelectList'] = [this.$srmI18n(`${this.$getLangAccount()}#i18n_title_all`, '全部')]
                        }
                    })
                }
            })
        },
        stepHandle (step) {
            this.step = step
            if (step == 1) this.getNodeGruop()
        },
        handleTabChange (tabCode) {
            this.groupCode = tabCode
            console.log(tabCode)
            if (tabCode == 'tenderProcessModelItemList') {
                this.getNodeGruop()
            }
        },
        getNodeGruop () {
            let { allData } = this.$refs[`${this.businessRefName}`].extendAllData()
            this.confirmLoading = true
            postAction(this.url.queryByGruop, allData)
                .then((res) => {
                    if (res.success) {
                        this.nodeData = res.result || {}
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        showNodeView () {
            if (this.step == 1 || this.groupCode == 'tenderProcessModelItemList') return true
            return false
        },
        fieldSelectOk (data) {},
        getAllData () {
            let checkNode = this.$refs.nodeCheckBox.extendNodeData() || []
            let mustNode = this.nodeData.mustNode || []
            let mustNodeArray = []
            if (mustNode.length > 0) {
                // 对象转数组
                Object.values(mustNode).map((el) => {
                    el.map((item) => {
                        mustNodeArray.push(item.id)
                    })
                })
            }
            let nodeData = [...mustNodeArray, ...checkNode]
            let { allData } = this.$refs[`${this.businessRefName}`].extendAllData()
            delete allData.tenderProcessModelItemList
            return {
                ...allData,
                nodeIds: nodeData
            }
        },
        handleSave () {
            let params = this.getAllData()
            let url = this.currentEditRow.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            console.log(this.$refs.businessRef.$refs.baseFormform[0].$refs.baseForm)
            this.$refs.businessRef.$refs.baseFormform[0].$refs.baseForm.validate((valid) => {
                if (valid) {
                    return postAction(url, params)
                        .then((res) => {
                            if (res.success) {
                                const message = this.$message[`${res.success ? 'success' : 'error'}`]
                                message(res.message)
                                // 保存成功，赋值id进行查询
                                if (!this.currentEditRow.id) {
                                    this.currentEditRow.id = res.result.id
                                }
                                if (this.refresh) {
                                    this.$nextTick(() => {
                                        this.$refs[this.businessRefName].queryDetail()
                                    })
                                }
                            } else {
                                this.$message.error(res.message)
                            }
                        })
                        .finally(() => {
                            this.confirmLoading = false
                        })
                } else {
                    console.log('error save!!')
                    this.confirmLoading = false
                    return false
                }
            })
            
        },
        handleShowNode () {
            let checkNode = this.$refs.nodeCheckBox.extendNodeData() || []
            let { mustNode, periodTypeInfo, notMustNode } = this.nodeData
            this.allNodeMap = {}
            this.periodTypeInfoArray = periodTypeInfo
            Object.values(mustNode).map((el) => {
                el.map((item) => {
                    if (!this.allNodeMap[item.periodType]) this.allNodeMap[item.periodType] = []
                    this.allNodeMap[item.periodType].push(item)
                })
            })
            Object.values(notMustNode).map((el) => {
                el.map((item) => {
                    if (checkNode.includes(item.id)) {
                        if (!this.allNodeMap[item.periodType]) this.allNodeMap[item.periodType] = []
                        this.allNodeMap[item.periodType].push(item)
                    }
                })
            })
            this.showNode = true
        }
    }
}
</script>
