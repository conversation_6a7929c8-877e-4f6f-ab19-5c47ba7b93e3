<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_YMyC_42cbc5d5`, '退回节点')"
          prop="newActivityId">
          <a-select
            style="width: 120px"
            v-model="form.newActivityId"
            @change="backToNodeChange">
            <a-select-option
              :key="item.id + index"
              :value="item.id"
              v-for="(item, index) in form.nodeArray"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
          <!-- <a-button @click="freedomChoice">{{ $srmI18n(`${$getLangAccount()}#i18n__JjiF_3cd7fec7`, '自由选择') }}</a-button> -->
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_userDeal`, '处理人')"
          prop="usersInfo">
          <!-- <a-tag
            v-if="singleUserData != null && singleUserData.id != null"
            size="large"
            color="blue"
            @close="delSingleUsers"
          >{{ singleUserData.fullName }}</a-tag -->
          <a-tag
            v-for="(item, i) in singleUserData"
            :key="i"
            size="large"
            color="blue"
            @close="delSingleUsers"
          >{{ item.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectSingleShowUsers"></a-button>
          </div>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_priorityLevel`, '优先级')"
          prop="priority">
          <a-radio-group
            name="radioGroup"
            defaultValue="50"
            v-model="form.priority">
            <a-radio
              v-for="(item, index) in priorityMap"
              :key="index"
              :value="item.value">{{
                item.title
              }}</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_taskName`, '任务标题')"
          prop="taskTitle">
          <a-input
            v-model="form.taskTitle"
            clearable />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见')"
          prop="option">
          <a-textarea
            show-word-limit
            v-model="form.option"
            allowClear
            type="textarea"
            autoSize />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { getBackNodeUser, getBackNode, getTransactionUrge, changeActivity } from '../../api/analy.js'
export default {
    mixins: [modalMixins],
    data () {
        return {
            rules: {
                newActivityId: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMYMyC_ae044c1a`, '请填写退回节点'), trigger: 'change' } ],
                taskTitle: [
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                option: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMqdWII_13f544fb`, '请填写退回节点'), trigger: 'change' },
                    { max: 600, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IIjHzxOBRWWW_a19cfb2b`, '意见的长度不能超过600'), trigger: 'change' }
                ]
                // usersInfo: [{ required: true, message: '请选择处理人' } ]
            },
            singleUserData: []
        }
    },
    methods: {
        freedomChoice () {
            getTransactionUrge(this.taskId).then(response => {
                if (response.code == 0) {
                    this.$set(this.form, 'nodeArray', response.data)
                }
            })
        },
        backToNodeChange (nodeId) {
            getBackNodeUser({ taskId: this.taskId, nodeId }).then(response => {
                if (response.code == 0) {
                    this.singleUserData = []
                    this.form.usersInfo = {}
                    if (response.data && response.data.length > 0) {
                        // let {assigneeName, assignee} = response.data[0]
                        // if (assigneeName && assignee) {
                        //     this.singleUserData = {
                        //         id: assignee,
                        //         fullName: assigneeName
                        //     }
                        //     this.$set(this.form, 'usersInfo', this.singleUserData)
                        // }
                        this.singleUserData = response.data.map(item => {
                            return {
                                id: item.assignee,
                                fullName: item.assigneeName
                            }
                        })
                        this.$set(this.form, 'usersInfo', this.singleUserData)
                    }
                }
            })
        },
        selectSingleShowUsers () {
            // this.showUserSelectModal({ selectModel: 'single' })
        },
        fieldSelectOk (data) {
            this.$set(this.form, 'usersInfo', data[0])
            this.singleUserData = data[0]
        },
        delSingleUsers () {
            this.singleUserData = {}
        },
        handleConfirm () {
            this.cheackValidate().then(() => {
                let {taskTitle, option, taskId, priority, usersInfo, newActivityId} = this.form
                let userId = usersInfo.map(item => item.id).join(',')
                let userName = usersInfo.map(item => item.fullName).join(',')
                let params = {
                    operate: 'backToNode',
                    newActivityId,
                    taskTitle,
                    option,
                    taskId,
                    priority,
                    userId,
                    userName
                }
                this.loading = true
                changeActivity(params).then(res => {
                    if (res.code == 200) {
                        this.$emit('success')
                        this.$message.success(res.message)
                    } else {
                        this.$message.error(res.message)
                    }
                    this.loading = false
                }).finally(() => {
                    this.loading = false
                    this.$emit('closeLoading')
                })
            })
        }
    },
    created () {
        this.loading = true
        getBackNode(this.taskId).then(response => {
            if (response.code == 0) {
                let nodeArray = response.data.filter(item => {
                    return item && item.id
                })
                this.$set(this.form, 'nodeArray', nodeArray)
                if (nodeArray.length == 1) {
                    this.form.newActivityId = nodeArray[0].id
                    this.backToNodeChange(nodeArray[0].id)
                }
            }
            this.loading = false
        })
        this.getDictData('taskPriority', 'priorityMap')
    }
}
</script>
