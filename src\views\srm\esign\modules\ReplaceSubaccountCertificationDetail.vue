<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
export default {
    name: 'ReplaceSubaccountCertificationDetail',
    mixins: [DetailMixin],
    data () {
        return {
            selectType: 'esignPersonCertification',
            pageData: {
                form: {
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_UzSQNJOHrA0MPEQA`, '个人认证信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                                    fieldName: 'elsAccount'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                                    fieldName: 'companyName'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    fieldName: 'subAccount',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'),
                                    fieldName: 'name',
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                                    fieldName: 'idType',
                                    dictCode: 'srmEsignIdType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    fieldName: 'idNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'),
                                    fieldName: 'mobile',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'),
                                    fieldName: 'email',
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型'),
                                    fieldName: 'certificationType',
                                    dictCode: 'srmCertificationType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankCardNo`, '个人银行卡(银联卡)'),
                                    fieldName: 'bankCardNo'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_wRmEgGGAjjZKOiUf`, 'e签宝个人账户id'),
                                    fieldName: 'accountId'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationStatus`, '认证状态'),
                                    fieldName: 'certificationStatus',
                                    dictCode: 'srmEsignCertificationStatus'
                                },
                                {
                                    
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_gnInnKUD9cArHf6j`, '实名认证短链接(有效期30天)'),
                                    fieldName: 'shortLink',
                                    extend: {
                                        linkConfig: {
                                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                            titleI18nKey: 'i18n_title_authenticationLink'
                                        },
                                        exLink: true
                                    }                                   
                                },
                                {
                                    
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_longLink`, '实名认证长链接'),
                                    fieldName: 'longLink',
                                    extend: {
                                        linkConfig: {
                                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                            titleI18nKey: 'i18n_title_authenticationLink'
                                        },
                                        exLink: true
                                    }                                   
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZjSujW3cSLbNiv5E`, '实名认证长链接'),
                                    fieldName: 'createdAccount'
                                }
                            ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/esign/elsSubaccountCertificationInfo/queryById'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>
