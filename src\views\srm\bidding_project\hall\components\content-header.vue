<script lang="jsx">
import Breadcrumb from '@/components/tools/Breadcrumb.vue'
export default {
    name: 'ContentHeader',
    props: {
        btns: {
            type: Array,
            default () {
                return []
            }
        },
        deadline: {
            type: Number,
            default: 0
        },
        renderExtra: {
            type: Function
        }
    },
    components: {
        Breadcrumb
    },
    methods: {
        handleClick (e, { event = 'click' }) {
            // console.log('e', e)
            this.$emit(`content-header-${event}`)
        }
    },
    render (h) {
        let { btns, renderExtra, deadline = 0 } = this
        const btnsEl = btns.map(n => (
            <a-tooltip placement="bottom">
                <template slot="title">
                    { n.helpText ? <span domPropsInnerHTML={ n.helpText }></span> : '' }
                </template>
                <a-button
                    class="ant-btn"
                    icon={ n.icon || ''}
                    type={ n.type || '' }
                    vShow={ n.showCondition ? n.showCondition() : true }
                    onClick={ (event) => this.handleClick(event, n) }>
                    { n.title }
                </a-button>
            </a-tooltip>
        ))
        
        let extraEl = renderExtra ? renderExtra(deadline) : (null)

        return (
            <div class="content-Header">
                <div class="topWrap">
                    <div class="breadcrumb">
                        <breadcrumb></breadcrumb>
                    </div>
                    <div class="box">
                        <div class="extra">
                            { extraEl }
                        </div>
                        <div class="menu">
                            { btnsEl }
                        </div>
                    </div>
                </div>
            </div>
        )
    }
}
</script>

<style lang="less" scoped>
.content-Header {
    width: 100%;
    &.posA {
        position: absolute;
        left: 0;
        top: 0;
    }
	.topWrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 6px 16px 6px 26px;
		background: #fff;
        margin: -8px;
        min-height: 44px;
        .box {
            display: flex;
            align-items: center;
        }
        .extra {
            & +.menu {
                margin-left: 24px;
            }
        }
		.menu {
			text-align: right;
			.ant-btn {
				& + .ant-btn {
					margin-left: 10px;
				}
			}
		}
	}
}
</style>