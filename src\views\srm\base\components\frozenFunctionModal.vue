<!--
 * @Author: your name
 * @Date: 2022-03-10 16:06:56
 * @LastEditTime: 2022-03-17 16:26:46
 * @LastEditors: Please set LastEditors
 * @FilePath: \srm-frontend\src\views\srm\supplier\components\customSelectModal.vue
-->
<template>
  <div>
    <a-modal
    v-drag    
      centered
      :title="title"
      :width="1000"
      :visible="visible"
      :maskClosable="false"
      @ok="selectedOk"
      @cancel="close"
      :z-index="100"
      v-bind="footer"
    >
      <a-tabs v-model="activeKey">
        <a-tab-pane
          key="1"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_title_frozenFunction`, '冻结功能')">
          <div v-if="isEdit">
            <a-button
              type="primary"
              @click="add">{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}</a-button>
            <a-button
              style="margin-left: 10px"
              type="primary"
              @click="del">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
          </div>
          <vxe-grid
            border
            :height="300"
            ref="selectGrid"
            showOverflow="tooltip"
            showHeaderOverflow="tooltip"
            v-bind="gridConfig"
            :tooltip-config="{
              showAll: true
            }"
            :data="tableData"
            :edit-rules="grid_rules"
            :edit-config="{
              trigger: 'click',
              mode: 'cell',
              showStatus: true,
              activeMethod: activeMethod
            }"
            :columns="currentColumns"
          >
          </vxe-grid>
        </a-tab-pane>
        <a-tab-pane
          key="2"
          v-if="showHistoryList"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_title_numberOfHistory`, '历史记录')">
          <vxe-grid
            border
            :height="320"
            ref="historyList"
            showOverflow="tooltip"
            showHeaderOverflow="tooltip"
            v-bind="gridConfig"
            :tooltip-config="{
              showAll: true
            }"
            :data="historyList"
            :columns="historyListColumns"
          >
          </vxe-grid>  
        </a-tab-pane>
        
      </a-tabs>
      
    </a-modal>
  </div>
</template>
<script>
import {getLangAccount, srmI18n} from '@/utils/util'
import {ajaxFindDictItems} from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { cloneDeep } from 'lodash'
import { ListConfig } from '@/plugins/table/gridConfig'
let cloneListConfig = cloneDeep(ListConfig)
if (cloneListConfig.toolbarConfig) {
    delete cloneListConfig.toolbarConfig
}
export default {
    props: {
        title: {
            type: String,
            default: srmI18n(`${getLangAccount()}#i18n_title_selectDataMsg`, '选择数据')
        },
        selectModel: {
            type: String,
            default: 'single'
        },
        isEdit: {
            type: Boolean,
            default: true
        },
        footer: {
            type: Object,
            default: () => {
                return {}
            }
        },
        showHistoryList: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            currentUrl: '',
            checkStatus: '1',
            keyWord: '',
            visible: false,
            activeKey: '1',
            gridConfig: cloneListConfig,
            optionsMap: {
                frozenFunction: [],
                orgCode1: [],
                orgCode2: []
            },
            currentColumns: [
                {
                    type: 'checkbox',
                    width: 40
                },
                {
                    field: 'frozenFunction',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_ROdj_277ed3d2`, '功能应用'),
                    width: 120,
                    editRender: {
                        enabled: true
                    },
                    slots: {
                        default: ({row}) => {
                            return [
                                <span>
                                    { this.getDictLabel(row['frozenFunction'], 'frozenFunction') }
                                </span>
                            ]
                        },
                        edit: ({row}) => {
                            return [
                                <m-select  configData={row} getPopupContainer={triggerNode => {
                                    return triggerNode.parentNode || document.body
                                }}
                                onChange={() => {this.changeSelectValue(row)}}
                                v-model={row.frozenFunction}
                                placeholder={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')}
                                dict-code='srmFreezeFunction' />
                            ]
                        }
                    }
                },
                {
                    field: 'orgCode',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgNumber`, '组织编号'),
                    width: 180,
                    editRender: {
                        enabled: true
                    }, 
                    slots: {
                        default: ({row}) => {
                            let dictcode = ['0', '2', '3'].includes(row.frozenFunction) ? 'orgCode1': 'orgCode2'
                            return [
                                row['orgCode'] ? this.getDictLabel(row['orgCode'], dictcode) : ''
                            ]
                        },
                        edit: ({row}) => {
                            let dictcode = ['0', '2', '3'].includes(row.frozenFunction) ? 'purchase_organization_info#org_name#org_code#org_category_code="purchaseOrganization" && status="1"': 'purchase_organization_info#org_name#org_code#org_category_code="companyCode" && status="1"'
                            let disabled = true
                            if (row.frozenFunction && row.isSelectAll == '0') {
                                disabled = false
                            }
                            return [
                                <m-select  configData={row} getPopupContainer={triggerNode => {
                                    return triggerNode.parentNode || document.body
                                }}
                                disabled={disabled}
                                v-model={row.orgCode}
                                mode="multiple"
                                placeholder={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')}
                                dict-code={dictcode} />
                            ]
                        }
                    }
                },
                {field: 'isSelectAll', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bxVR_27312503`, '全部组织'), 
                    editRender: {
                        name: '$select',
                        options: [
                            { value: '0', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否') },
                            { value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是') }
                        ],
                        events: {
                            change: (currentRow) => {this.changeSelectValue(currentRow.row)}
                        }
                    },
                    
                    slots: {
                        header: () => {
                            return [
                                <div>
                                    <span>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bxVR_27312503`, '全部组织')}</span>
                                    <a-tooltip title={this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bxVRLKWxsxVauVR_7b90d956`, '全部组织为是，不包含新增加组织')}>
                                        <i class="vxe-icon--question" style='vertical-align: inherit;'></i>
                                    </a-tooltip>
                                </div>
                            ]
                        }
                    }
                },
                {field: 'frozenStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_holdStatus`, '冻结状态'), 
                    editRender: {name: '$select', options: [
                        { value: '0', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否') },
                        { value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是') }
                    ]}
                },
                {field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_explain`, '说明'), editRender: { name: '$input'}},
                {field: 'createBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人')}, 
                {field: 'createTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_createTime`, '创建时间')}
            ],
            historyListColumns: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_ROdj_277ed3d2`, '功能应用'),
                    field: 'frozenFunction',
                    width: 120,
                    slots: {
                        default: ({row}) => {
                            return [
                                <span>
                                    { this.getDictLabel(row['frozenFunction'], 'frozenFunction') }
                                </span>
                            ]
                        }
                    }
                },
                {
                    field: 'orgCode',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgNumber`, '组织编号'),
                    width: 180,
                    showOverflow: 'tooltip',
                    slots: {
                        default: ({row}) => {
                            let dictcode = ['0', '2', '3'].includes(row.frozenFunction) ? 'orgCode1': 'orgCode2'
                            return [
                                row['orgCode'] ? this.getDictLabel(row['orgCode'], dictcode) : ''
                            ]
                        }
                    }
                },
                {field: 'isSelectAll', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bxVR_27312503`, '全部组织'), editRender: { name: 'select', options: [
                    { value: '0', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否') },
                    { value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是') }
                ]}},
                {field: 'frozenStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_holdStatus`, '冻结状态'), editRender: { name: 'select', options: [
                    { value: '0', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否') },
                    { value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是') }
                ]}},
                {field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_explain`, '说明')},
                {field: 'updateBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cancellationSubAccount`, '作废人')},
                {field: 'updateTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cancellationTime`, '作废时间')},
                {field: 'createTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_createTime`, '创建时间')}
            ],
            tableData: [],
            historyList: [],
            propParams: {},
            grid_rules: {
                frozenFunction: [
                    {message: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_ROdj_277ed3d2`, '功能应用') + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_required`, '必填'), required: true}
                ],
                frozenStatus: [{
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_holdStatus`, '冻结状态') + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_required`, '必填'),
                    required: true
                }]
            }
        }
    },
    methods: {
        activeMethod ({row}) {
            if (row.canEdit) return true
            return false
        },
        add () {
            let itemData = {
                canEdit: true,
                frozenFunction: '',
                orgCode: '',
                frozenStatus: '1',
                isSelectAll: '0'
            }
            this.$refs.selectGrid.insertAt([itemData], -1)
        },
        del () {
            this.$refs.selectGrid.removeCheckboxRow()
        },
        changeSelectValue (row) {
            row.orgCode = ''
        },
        open (data) {
            let {supplierMasterFrozenHistoryList, frozenFunction} = data
            this.historyList = supplierMasterFrozenHistoryList
            this.tableData = frozenFunction || []
            this.getDictData()
            this.visible = true
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            let selectedData = this.$refs.selectGrid.getTableData().fullData
            if (selectedData.length > 0) {
                for (let i = 0; i <selectedData.length; i++) {
                    console.log(selectedData[i])
                    if (selectedData[i].isSelectAll == '0') {
                        console.log(selectedData[i].orgCode)
                        if (!selectedData[i].orgCode) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgNumber`, '组织编号') + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_required`, '必填'))
                    }
                }
                this.$refs.selectGrid.fullValidate().then(() => {
                    selectedData.map(item => {
                        delete item['canEdit']
                    })
                    this.visible = false
                    this.$emit('ok', selectedData)
                })
            } else {
                this.$emit('ok', selectedData)
            }
        },
        getDictData () {
            let dictCodeArr = [
                {code: 'frozenFunction', dict: 'srmFreezeFunction'},
                {code: 'orgCode1', dict: 'purchase_organization_info#org_name#org_code#org_category_code="purchaseOrganization" && status="1"'},
                {code: 'orgCode2', dict: 'purchase_organization_info#org_name#org_code#org_category_code="companyCode" && status="1"'}
            ]
            dictCodeArr.map(item => {
                let postData = {
                    busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: item.dict
                }
                ajaxFindDictItems(postData).then(res => {
                    if(res.success) {
                        let options = res.result.map(item2 => {
                            return {
                                value: item2.value,
                                label: item2.text,
                                title: item2.title
                            }
                        })
                        this.optionsMap[item.code] = options
                    }
                })
            })
        },
        // 通过value显示label
        getDictLabel (value, dict) {
            let currentValueArr = value.split(',') || []
            if (dict) {
                let dictItem = this.optionsMap[dict].filter((opt) => {
                    return currentValueArr.includes(opt.value)
                }).map(item => item.label)
                return dictItem.length ? dictItem.join('；'): currentValueArr[0]
            } else {
                return value
            }
        }
    }
}
</script>