<template>
  <div style="height:100%">
    <list-layout
      key="PurchaseContractTemplateHisListModal"
      ref="listPage"
      v-show=" !showDetailPage"
      :pageData="pageData"
      :url="url"/>

    <!-- 表单区域 -->
    <viewPurchaseContractTemplate-modal
      ref="detailPage"
      v-if="showDetailPage"
      :current-edit-row="currentEditNewRow"
      @hide="hideDetail"
    />
    <viewItemDiff-modal ref="viewDiffModal" />
  </div>
</template>
<script>

import ViewContractTemplateModal from './modules/ViewPurchaseContractTemplateModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import ViewItemDiffModal from './modules/ViewItemDiffModal'
import { getAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        'viewPurchaseContractTemplate-modal': ViewContractTemplateModal,
        'viewItemDiff-modal': ViewItemDiffModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            showViewDiffPage: false,
            currentEditNewRow: '',
            pageData: {
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_comparisons`, '比对'), icon: '', clickFn: this.viewDiffItem, type: 'primary'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: '', icon: '', clickFn: this.goBack}
                ],
                publicBtn: [

                ],
                optColumnWidth: 100,
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTemplateNo`, '合同模板编号'),
                        fieldName: 'contractTemplateNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterTheTemplateNumber`, '请输入模板编号')
                    },
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_templateName`, '模板名称'),
                        fieldName: 'contractTemplateName',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterContractTemplateName`, '请输入模板名称')
                    }
                ],
                form: {
                    templateId: ''                 
                },
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_comparisons`, '比对'), clickFn: this.viewDiff},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.showDetail}
                ]
            },
            url: {
                list: '/contract/purchaseContractTemplateHeadHis/list',
                columns: 'purchaseContractTemplateHeadList'
            }
        }
    },
    created () {
        this.pageData.form.templateId = this.currentEditRow.id
    },
    methods: {
        goBack (){
            this.$emit('hide')
        },
        showDetail (row){
            this.currentEditNewRow = row
            this.currentEditNewRow['viewType']='his'
            this.showDetailPage = true
        },
        handleView (row) {
            this.currentEditNewRow = row
            this.currentEditNewRow['viewType']='his'
            this.showDetailPage = true
        },
        hideDetail (){
            this.showDetailPage = false
        },
        viewDiff (row) {
            if (row.type!='head'){
                row.type='item'
            }
            getAction('/contract/purchaseContractTemplateHead/diffContractContent', {id: row.id, newId: row.templateId, type: row.type}).then(res => {
                if(res.success) {
                    this.$refs.viewDiffModal.open(res.result)
                }
            })
        },
        viewDiffItem () {
            let itemGrid =  this.$refs.listPage.$refs.listGrid
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(checkboxRecords.length!=2) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTwoRowsDataToCompare`, '请选择两行数据进行比对！'))
                return
            }
            this.viewDiff({id: checkboxRecords[0].id, templateId: checkboxRecords[1].id, type: 'head'})
        }
    }
}
</script>