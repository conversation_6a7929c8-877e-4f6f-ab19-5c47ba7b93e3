<template>
  <global-layout @dynamicRouterShow="dynamicRouterShow">
    <contextmenu
      :item-list="menuItemList"
      :visible.sync="menuVisible"
      style="z-index: 9999;"
      @select="onMenuSelect"
    />

    <a-tabs
      @contextmenu.native="e => onContextmenu(e)"
      v-if="multipage"
      :active-key="activePage"
      class="tab-layout-tabs"
      :hide-add="true"
      type="editable-card"
      @change="changePage"
      @tabClick="tabCallBack"
      @edit="editPage"
      id="tab-layout-tabs-js"
    >
      <a-tab-pane
        :id="page.fullPath"
        :key="page.fullPath"
        v-for="page in pageList"
        :closable="page.fullPath !== indexKey"
      >
        <span
          slot="tab"
          :pagekey="page.fullPath"
        >{{ handleTitle(page.meta) }}</span>
      </a-tab-pane>
    </a-tabs>
    <div
      ref="tabContentBox"
      class="tab-content-box content-box-wrap"
      :style="{ height: height ? `${height}px` : '100%' }">
      <keep-alive v-if="multipage">
        <router-view v-if="reloadFlag" />
      </keep-alive>
      <template v-else>
        <router-view v-if="reloadFlag" />
      </template>
    </div>
  </global-layout>
</template>

<script>
import GlobalLayout from '@/components/page/GlobalLayout'
import Contextmenu from '@/components/menu/Contextmenu'
import { mixin, mixinDevice } from '@/utils/mixin.js'
import {
    DEFAULT_FIXED_HEADER,
    DEFAULT_COLOR
} from '@/store/mutation-types'
import { mapActions } from 'vuex'
import { throttle } from 'lodash'

const indexKey = '/dashboard-srm'
const EVENT_RESIZE_CHANGE = 'resize'
const resizeMethod = '$__resizeMethod'

function getRouteSamePath (pathList, path){
    let curIndex=-1
    if(pathList.length>0){
        for(let [idx, v] of pathList.entries()){
            const url= v.split('?')[0]
            if(url!==path) continue
            curIndex=idx
            break
        }
    }
    return curIndex

}


export default {
    name: 'TabLayout',
    components: {
        GlobalLayout,
        Contextmenu
    },
    mixins: [mixin, mixinDevice],
    data () {
        return {
            indexKey,
            height: 0,
            pageList: [],
            linkList: [],
            activePage: '',
            menuVisible: false,
            menuItemList: [
                { key: '4', icon: 'reload', text: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refresh`, '刷 新') },
                { key: '1', icon: 'arrow-left', text: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_closeLeft`, '关闭左侧') },
                { key: '2', icon: 'arrow-right', text: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_closeRight`, '关闭右侧') },
                { key: '3', icon: 'close', text: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_closeOther`, '关闭其它') }
            ],
            reloadFlag: true
        }
    },

    provide (){
        return{
            closeCurrent: this.closeCurrent,
            routeReload: this.routeReload
        }
    },

    computed: {
        multipage () {
        //判断如果是手机模式，自动切换为单页面模式
            if (this.isMobile()) {
                return false
            } else {
                return this.$store.state.app.multipage
            }
        }
    },
    created () {
        this.pageList.push(this.$route)
        this.linkList.push(this.$route.fullPath)
        this.activePage = this.$route.fullPath
        
        this.getContentHeight()
    },
    mounted () {
        window.addEventListener(EVENT_RESIZE_CHANGE, this[resizeMethod])
    },
    beforeDestroy () {
        window.removeEventListener(EVENT_RESIZE_CHANGE, this[resizeMethod])
    },
    watch: {
        '$route': function (newRoute) {
            this.activePage = newRoute.fullPath
            let {path: newPath}=newRoute
            console.log('newPath', newPath, newRoute.fullPath)
            if (!this.multipage) {
                this.linkList = [newRoute.fullPath]
                this.pageList = [Object.assign({}, newRoute)]
            } else if (this.linkList.indexOf(newRoute.fullPath) < 0) {
                // this.linkList.push(newRoute.fullPath)
                // this.pageList.push(Object.assign({}, newRoute))
                //判断是否有相同path，有用fullPage替换
                let curIndex=getRouteSamePath( this.linkList, newPath)
        
                if(curIndex < 0){
                    this.linkList.push(newRoute.fullPath)
                    this.pageList.push(Object.assign({}, newRoute))
                }else{
                    //如果存在相同的path，但是fullpatch不同，替换最新的
                    let oldPositionRoute = this.pageList[curIndex]
                    this.linkList.splice(curIndex, 1, newRoute.fullPath)
                    this.pageList.splice(curIndex, 1, Object.assign({}, newRoute, {meta: oldPositionRoute.meta}))
                }

            } else if (this.linkList.indexOf(newRoute.fullPath) >= 0) {
                let oldIndex = this.linkList.indexOf(newRoute.fullPath)
                let oldPositionRoute = this.pageList[oldIndex]
                this.pageList.splice(oldIndex, 1, Object.assign({}, newRoute, {meta: oldPositionRoute.meta}))
            }
        },
        'activePage': function (key) {
            let index = this.linkList.lastIndexOf(key)
            console.log('activePage', this.linkList, index)
            let waitRouter = this.pageList[index]
            this.$router.push(Object.assign({}, waitRouter))
            this.activeTabBg()
        },
        'multipage': function (newVal) {
            if(this.reloadFlag){
                if (!newVal) {
                    this.linkList = [this.$route.fullPath]
                    this.pageList = [this.$route]
                }
            }
        }
    },
    methods: {
        // 通过lodash的节流函数来控制resize的频率
        [resizeMethod]: throttle(function () {
            this.getContentHeight()
        }, 300, { 'trailing': true }),
        getContentHeight () {
            let fixedHeader = this.$ls.get(DEFAULT_FIXED_HEADER)
            let clientHeight = document.documentElement.clientHeight
            let count = fixedHeader ? 60 : 94
            let height = clientHeight - count
            if (height < 500) {
                height = 500
            }
            this.height = height
        },
        // tab添加背景色
        activeTabBg () {
            this.$nextTick(() => {
                const $obj = document.querySelector('#tab-layout-tabs-js')
                if ($obj) {
                    let actDom = $obj.querySelector('.ant-tabs-nav-wrap .ant-tabs-tab-active')
                    let tabsDom = [...$obj.querySelectorAll('.ant-tabs-nav-wrap [role="tab"]')]
                    tabsDom.forEach(rs => {
                        rs.style.cssText = ''
                    })
                    // console.log(this.$ls.get(DEFAULT_COLOR))
                    let themeColor = this.$ls.get(DEFAULT_COLOR)
                    // 17 为 16进制颜色透明度 0.1
                    if (/^#/.test(themeColor)) {
                        actDom.style.cssText = `background: ${themeColor}17 !important;`
                    }
                }
            })
        },
        changePage (key) {
            let that = this
            if(this.$store.state.app.changeTabConfirm) {
                this.$confirm({
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentIsEditingStatusSureToLeave`, '当前处于编辑状态，是否确定离开'),
                    onOk: () => {
                        that.activePage = key
                        this.$store.dispatch('SetTabConfirm', false)
                    }
                })
            }else {
                this.activePage = key
            }
        },
        tabCallBack () {
            // this.$nextTick(() => {
            //     triggerWindowResizeEvent()
            // })
        },
        editPage (key, action) {
            this.isEdit=true
            this[action](key)
        },
        ...mapActions(['CloseOnlineWS']),
        removeHandle (key){
            this.$store.dispatch('SetTabCloseStatus', true)
            this.pageList = this.pageList.filter(item => item.fullPath !== key)
            let index = this.linkList.indexOf(key)
            this.linkList = this.linkList.filter(item => item !== key)
            index = index >= this.linkList.length ? this.linkList.length - 1 : index
            this.activePage = this.linkList[index]
            this.isEdit=false
        },
        remove (key) {
            if (this.pageList.length === 1) {
                this.pageList = []
                this.linkList = []
                this.pageList.push({
                    name: 'dashboard-srm',
                    path: indexKey,
                    fullPath: indexKey,
                    meta: {
                        icon: 'dashboard',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_home`, '首页')
                    }
                })
                this.linkList.push(indexKey)
                this.activePage = indexKey
                return
            }
            

            // 删除处理弹窗标记
            this.$store.dispatch('SetTabConfirm', false)
            if(key.indexOf('linkFilter')>-1||key.indexOf('open')>-1){
                if(!this.isEdit)   this.activePage=this.$route.path
                else       this.removeHandle(key)
            }else{
                this.removeHandle(key)
            }
            // 竞价大厅需单独关闭在线WS
            if (key.includes('buyLobbyNew') || key.includes('buyLobbyNewJap') || key.includes('saleLobbyNew') || key.includes('saleLobbyNewJap')) {
                this.CloseOnlineWS()
            }
        },
        onContextmenu (e) {
            const pagekey = this.getPageKey(e.target)
            if (pagekey !== null) {
                e.preventDefault()
                this.menuVisible = true
            }
        },
        handleTitle (meta) {
            let t = meta.title
            if (meta.titleI18nKey) {
                t = this.$srmI18n(`${this.$getLangAccount()}#${meta.titleI18nKey}`, meta.title)
            }
            return t
        },
        getPageKey (target, depth) {
            depth = depth || 0
            if (depth > 2) {
                return null
            }
            let pageKey = target.getAttribute('pagekey')
            pageKey = pageKey || (target.previousElementSibling ? target.previousElementSibling.getAttribute('pagekey') : null)
            return pageKey || (target.firstElementChild ? this.getPageKey(target.firstElementChild, ++depth) : null)
        },
        onMenuSelect (key, target) {
            let pageKey = this.getPageKey(target)
            switch (key) {
            case '1':
                this.closeLeft(pageKey)
                break
            case '2':
                this.closeRight(pageKey)
                break
            case '3':
                this.closeOthers(pageKey)
                break
            case '4':
                this.routeReload()
                break
            default:
                break
            }
        },

        closeCurrent (){
            this.remove(this.activePage)
        },

        closeOthers (pageKey) {
            let index = this.linkList.indexOf(pageKey)
            if (pageKey == indexKey || pageKey.indexOf('?ticke=')>=0) {
                this.linkList = this.linkList.slice(index, index + 1)
                this.pageList = this.pageList.slice(index, index + 1)
                this.activePage = this.linkList[0]
            } else {
                // let indexContent = this.pageList.slice(0, 1)[0]
                this.linkList = this.linkList.slice(index, index + 1)
                this.pageList = this.pageList.slice(index, index + 1)
                console.log(this.linkList)
                // this.linkList.unshift(indexContent.fullPath)
                // this.pageList.unshift(indexContent)
                this.activePage = this.linkList[0]
            }
        },
        closeLeft (pageKey) {
            if (pageKey == indexKey ) return
            // let tempList = [...this.pageList]
            // let indexContent = tempList.slice(0, 1)[0]
            let index = this.linkList.indexOf(pageKey)
            this.linkList = this.linkList.slice(index)
            this.pageList = this.pageList.slice(index)
            // this.linkList.unshift(indexContent.fullPath)
            // this.pageList.unshift(indexContent)
            if (this.linkList.indexOf(this.activePage) < 0) {
                this.activePage = this.linkList[0]
                // this.activePage = pageKey
            }
        },
        closeRight (pageKey) {
            let index = this.linkList.indexOf(pageKey)
            this.linkList = this.linkList.slice(0, index + 1)
            this.pageList = this.pageList.slice(0, index + 1)
            if (this.linkList.indexOf(this.activePage < 0)) {
                this.activePage = this.linkList[this.linkList.length - 1]
            }
        },

        dynamicRouterShow (key, title){
            let keyIndex = this.linkList.indexOf(key)
            if(keyIndex>=0){
                let currRouter = this.pageList[keyIndex]
                let meta = Object.assign({}, currRouter.meta, {title: title})
                this.pageList.splice(keyIndex, 1, Object.assign({}, currRouter, {meta: meta}))
            }
        },



        routeReload (){
            this.reloadFlag = false
            let ToggleMultipage = 'ToggleMultipage'
            this.$store.dispatch(ToggleMultipage, false)
            this.$nextTick(()=>{
                this.$store.dispatch(ToggleMultipage, true)
                this.reloadFlag = true
            })
        }

    }
}
</script>
<style lang="less" scoped>
  /*
 * The following styles are auto-applied to elements with
 * transition="page-transition" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the page transition by editing
 * these styles.
 */

  :deep(.page-transition-enter){
    opacity: 0;
  }

  :deep(.page-transition-leave-active){
    opacity: 0;
  }

    :deep(.page-transition-enter .page-transition-container),
  :deep(.page-transition-leave-active .page-transition-container){
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }

  /*美化弹出Tab样式*/
  :deep(.tab-layout-tabs.ant-tabs.ant-tabs-card .ant-tabs-nav-container){
    margin-top: 4px;
    margin-bottom: 0;
  }

  /* 修改 ant-tabs 样式 */
  :deep(.tab-layout-tabs.ant-tabs){
    background-color: white;
    .ant-tabs-bar {
      margin: 0;
    }

  }

  :deep(.tab-layout-tabs.ant-tabs){

    &.ant-tabs-card .ant-tabs-tab {

      padding: 0 24px !important;
      background-color: white !important;
      margin-right: 10px !important;

      .ant-tabs-close-x {
        width: 12px !important;
        height: 12px !important;
        opacity: 0 !important;
        cursor: pointer !important;
        font-size: 12px !important;
        margin: 0 !important;
        position: absolute;
        top: 36%;
        right: 6px;
      }

      &:hover .ant-tabs-close-x {
        opacity: 1 !important;
      }

    }

  }

  :deep(.tab-layout-tabs.ant-tabs.ant-tabs-card > .ant-tabs-bar ){
    .ant-tabs-tab {
      border: none !important;
      border-bottom: 1px solid transparent !important;
    }
    .content-box-wrap {
        margin: 6px 6px 0;
    }
    .ant-tabs-tab-active {
        // background-color: @primary-color;
    //   border-color: @primary-color !important;
    } 
  }
  /* .tab-layout-tabs.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-ink-bar {
      visibility: visible;
  } */

</style>