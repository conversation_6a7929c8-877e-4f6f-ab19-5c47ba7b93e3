<template>
  <a-modal
    v-drag    
    v-model="madalVisible"
    centered
    :width="800"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderSetting`, '阶梯设置')"
    @ok="setLadderOk">
    <a-spin :spinning="confirmLoading">
      <div class="compare-page-container">
        <div class="page-content-right">
          <div style="margin-bottom: 12px">
            <vxe-grid
              border
              ref="headerGrid"
              row-id="ladderQuantity"
              show-overflow
              size="small"
              height="350"
              :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderInfo`, '阶梯信息')"
              :columns="tableHeaderColumn"
              :data="tableHeaderData"
              :edit-config="{trigger: 'click', mode: 'cell'}"
              :radio-config="{highlight: true, reserve: true}"
              :toolbar="{ slots: { buttons: 'toolbar_buttons' }}"
            >
              <template #toolbar_buttons>
                <div style="margin-top:-8px;text-align:right">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_ladderCount`, '阶梯数量') }}：
                  <a-input-number
                    v-model="ladderQuantity"
                    :placeholder="$srmI18n(`${$getLangAccount()}#i18n_alert_VWNfU0jyDWR_ffbcea1c`, '请输入大于0的阶梯数量')"
                    style="width:200px"/>
                  <a-button
                    @click="addLadderQuantity"
                    type="primary"
                    style="margin-left:8px"
                    slot="tabBarExtraContent">  {{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '添加') }}
                  </a-button>
                  <a-button
                    @click="deleteLadder"
                    type=""
                    style="margin-left:8px"
                    slot="tabBarExtraContent">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}
                  </a-button>
                </div>
              </template>
            </vxe-grid>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import {List} from 'ant-design-vue'
export default {
    name: 'SetLadderModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        isEmit: {
            type: Boolean,
            default: false
        },
        form: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            headObj: {},
            ladderQuantity: '',
            confirmLoading: false,
            listData: [],
            madalVisible: false,
            currentRow: null,
            tableHeaderData: [],
            tableHeaderColumn: [
                { type: 'radio', width: 40, align: 'center'},
                { field: 'ladder', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级'), width: 170},
                { field: 'ladderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderCount`, '阶梯数量'), width: 170}
                // { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'), width: 170, editRender: {name: '$input', props: {type: 'number'}, events: {change: (row, rowIndex) => this.priceChangeEvent(row, rowIndex, 'price')}}},
                // { field: 'netPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价'), editRender: { name: '$input', props: {type: 'number', disabled: true}, events: {change: (row, rowIndex) => this.priceChangeEvent(row, rowIndex, 'netPrice')}}}
            ]
        }
    },
    mounted () {
    },
    methods: {
        open (row, curFieldValue) {
            let that = this
            this.madalVisible = true
            this.currentRow = row
            let ladderJson = curFieldValue || row.ladderPriceJson || ''
            if(ladderJson){
                const list = JSON.parse(ladderJson)
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData(list)
                })
            }else{
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData([])
                })
            }
        },
        priceChangeEvent (tdata, curVal, type) {
            if(!curVal.value){
                if (type == 'netPrice') {
                    tdata.row.price = ''
                } else {
                    tdata.row.netPrice = ''
                }
            }else{
                if (type == 'netPrice') {
                    tdata.row.price = (curVal.value * (1 + this.currentRow.taxRate/100)).toFixed(6)
                } else {
                    tdata.row.netPrice = (curVal.value /  (1 + this.currentRow.taxRate/100)).toFixed(6)
                }
            }

        },
        goBack () {
            this.$emit('hide')
        },
        addLadderQuantity (){
            if(!this.ladderQuantity){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterladderCount`, '请输入阶梯数量！'))
                return
            }
            if(this.ladderQuantity <= 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DWRlTfU0_99c7e07c`, '梯数量必须大于0！'))
                return
            }
            let grid = this.$refs.headerGrid
            let itemList = grid.getTableData().fullData
            const {price, taxRate} = this.currentRow
            let netPrice = price && taxRate ? (price /  (1 + taxRate/100)).toFixed(6) : ''
            if(itemList == 0){
                var item = {'ladderQuantity': 0, 'ladder': 0+' < x < '+this.ladderQuantity, price, netPrice}
                grid.insert(item)
            }
            itemList = grid.getTableData().fullData
            if(itemList.length >= 1){
                let currentLastIndex = itemList.length-1
                let item = itemList[currentLastIndex]
                if(Number(item.ladderQuantity) >= Number(this.ladderQuantity) ){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderMostLastLadderCount`, '阶梯数量必须大于上一阶梯数量'))
                    return
                }
                item['ladder']=item.ladderQuantity+' <= x < '+this.ladderQuantity
            }
            let item2 = {'ladderQuantity': this.ladderQuantity, 'ladder': this.ladderQuantity+' <= x ', price, netPrice}
            grid.insertAt(item2, -1)
            this.ladderQuantity = ''
        },
        deleteLadder (){
            let currentItem = this.$refs.headerGrid.getRadioRecord()
            if(!currentItem){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDataDelete`, '请选择需要删除的数据！'))
                return
            }
            let itemList = this.$refs.headerGrid.getTableData().fullData
            if(currentItem.ladderQuantity != itemList[itemList.length - 1].ladderQuantity){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseDeleteFollowLadderLineFirst`, '请先删除后面的阶梯行！'))
            }else{
                this.$refs.headerGrid.removeRadioRow()
            }
        },
        setLadderOk (){
            let itemList = this.$refs.headerGrid.getTableData().fullData
            if(itemList.length == 0 ){
                // this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseAddLadderInfo`, '请添加阶梯信息！'))
                itemList = null
            }
            // for(let i in itemList){
            //     let item = itemList[i]
            //     if(!item.price){
            //         this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPriceNotEmpty`, '含税价不能为空！') )
            //         return
            //     }
            //     if(!item.netPrice){
            //         this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_empty_notNetPrice`, '不含税价不能为空！') )
            //         return
            //     }
            //     if(item.price<0||item.netPrice<0){
            //         this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_xfuxOXUEUWW_3bbae979`, '含税价不能小于等于0！') )
            //         return
            //     }
            // }
            // 获取选中的值
            this.madalVisible = false
            if (this.isEmit) {
                this.$emit('ok', itemList)
            } else {
                this.$parent.fieldSelectOk(itemList)
            }
        }
    }
}
</script>
<style lang="less">
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 1000px;
            background-color: #fff
        }
    }
</style>