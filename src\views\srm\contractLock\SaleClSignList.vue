<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showSelfEditPage && !showSelfDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      :url="url" />
    <!-- 详情界面 -->
    <SaleClSignDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <!-- 详情界面 -->
    <SaleClSignEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <SaleClSelfSignEdit
      v-if="showSelfEditPage"
      ref="selfEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideSelfEditPage" />
    <SaleClSelfSignDetail
      v-if="showSelfDetailPage"
      ref="selfEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideSelfEditPage" />
  </div>
</template>
<script>
import SaleClSelfSignDetail from './modules/SaleClSelfSignDetail'
import SaleClSignDetail from './modules/SaleClSignDetail'
import SaleClSignEdit from './modules/SaleClSignEdit'
import SaleClSelfSignEdit from './modules/SaleClSelfSignEdit'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, postAction} from '@api/manage'
export default {
    mixins: [ListMixin],
    components: {
        SaleClSignDetail,
        SaleClSignEdit,
        SaleClSelfSignEdit,
        SaleClSelfSignDetail
    },
    mounted () {
        this.serachCountTabs('/contractLock/elsClContract/counts/0')
    },
    data () {
        return {
            showEditPage: false,
            showSelfEditPage: false,
            showSelfDetailPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: '业务编号'
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'contractLock#saleSign:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                form: {
                    keyWord: ''
                },
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleSelfView, authorityCode: 'contractLock#saleSign:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleSelfEdit, allow: this.allowSelfEdit, authorityCode: 'contractLock#saleSign:edit'},
                    {type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), clickFn: this.handleSend, allow: this.allowSend, authorityCode: 'contractLock#saleSign:send'},
                    {type: 'confirm', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVQIRL_c5fb6687`, '回传文件确认'), clickFn: this.handleConfirm, allow: this.allowConfirm, authorityCode: 'contractLock#saleSign:confirm'},
                    {type: 'query', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QLzEmh_5c8fbdd2`, '流程状态查询'), clickFn: this.queryFlow, allow: this.allowQueryFlow, authorityCode: 'contractLock#saleSign:queryFlow' },
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMPWOKy_b84d5f41`, '获取签署短链接'), clickFn: this.getShortUrl, allow: this.allowGetShortUrl, authorityCode: 'contractLock#saleSign:getShortUrl'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'contractLock#saleSign:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/contractLock/elsClContract/saleSingleList/1',
                add: '/contractLock/elsClContract/add',
                delete: '/contractLock/elsClContract/delete',
                send: '/contractLock/elsClContract/send',
                queryFlow: '/contractLock/elsClContract/flowQuery',
                columns: 'SaleClSingleSignContract'
            },
            tabsList: []
        }
    },
    methods: {
        handleSelfView (row){
            this.showSelfDetailPage = true
            this.currentEditRow = row
        },
        hideSelfEditPage () {
            this.showEditPage = false
            this.showSelfEditPage = false
            this.showDetailPage = false
            this.showSelfDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
        },
        allowGetShortUrl (row){
            if(row.launch == '1' && row.documentId && row.signStatus !== '1' && row.contractStatus !== 'RECALLED'){
                return false
            }
            return true
        },
        getShortUrl (row){
            getAction('/contractLock/elsClContract/getSignPage', {id: row.id}).then(res => {
                if(res.success){
                    alert(res.result)
                }
            })
        },
        allowDelete (row){
            if(row.launch==='1'){
                return true
            }
        },
        allowSend (row){
            //已上传未发送，签署完成
            if(row.uploaded==='1' && row.sendStatus!=='1' && row.signStatus==='2'){
                return false
            }
            return true
        },
        allowConfirm (row){
            //已回传，未驳回，未确认
            if(row.returnSignedFile==='1' && row.reject!=='1' && row.returnFileConfirm!=='1'){
                return false
            }
            return true
        },
        handleSend (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'),
                content: '是否确认发送',
                onOk: function () {
                    getAction('/contractLock/elsClContract/queryById', {id:row.id}).then((res) => {
                        if (res.success) {
                            postAction(that.url.send, res.result).then(res => {
                                const type = res.success ? 'success' : 'error'
                                that.$message[type](res.message)
                                if(res.success){
                                    row.sendStatus_dictText = '是'
                                    row.sendStatus = '1'
                                }
                            })
                        }
                    })
                }
            })
        },
        handleConfirm (row){
            this.currentEditRow = row
            this.showSelfDetailPage = true
        },
        allowQueryFlow (row){
            if(row.launch==='1'){
                return false
            }
            return true
        },
        queryFlow (row){
            this.$refs.listPage.loading = true
            getAction(this.url.queryFlow, {id: row.id}).then(res => {
                if(res.success){
                    this.$refs.listPage.loading = false
                    this.$message.info('合同状态：'+res.message)
                }else{
                    this.$refs.listPage.loading = false
                    this.$message.warning(res.message)
                }
            })
        },
        handleEdit (row) {
            this.showEditPage = true
            this.currentEditRow = row
        },
        handleSelfEdit (row) {
            this.showSelfEditPage = true
            this.currentEditRow = row
        },
        handleAdd () {
            this.showSelfEditPage = true
            this.currentEditRow = {}
        },
        // tab页签改变前
        handleAfterChangeTab ({ _this, activeTabData, pageData, listGrid, tablePage }) {
            if(activeTabData.proName == 'other'){
                let allColumns = listGrid.getTableColumn()
                allColumns['tableColumn'].forEach(x=>{
                    if(x.field == 'toElsAccount'){
                        x.field = 'elsAccount'
                        x.property = 'elsAccount'
                    }
                })
                this.url.list = '/contractLock/elsClContract/saleSingleList/2'
                pageData.optColumnList = [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractLock#saleSign:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVPWQA_d0ab0d72`, '回传签署文档'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'contractLock#saleSign:return'}
                ]

            }else {
                this.url.list = '/contractLock/elsClContract/saleSingleList/1'
                pageData.optColumnList = [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleSelfView, authorityCode: 'contractLock#saleSign:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleSelfEdit, allow: this.allowSelfEdit, authorityCode: 'contractLock#saleSign:edit'},
                    {type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), clickFn: this.handleSend, allow: this.allowSend, authorityCode: 'contractLock#saleSign:send'},
                    {type: 'confirm', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVQIRL_c5fb6687`, '回传文件确认'), clickFn: this.handleConfirm, allow: this.allowConfirm, authorityCode: 'contractLock#saleSign:confirm'},
                    {type: 'query', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QLzEmh_5c8fbdd2`, '流程状态查询'), clickFn: this.queryFlow, allow: this.allowQueryFlow, authorityCode: 'contractLock#saleSign:queryFlow' },
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMPWOKy_b84d5f41`, '获取签署短链接'), clickFn: this.getShortUrl, allow: this.allowGetShortUrl, authorityCode: 'contractLock#saleSign:getShortUrl'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'contractLock#saleSign:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            }
            if (activeTabData.hasOwnProperty('rejectReason') || activeTabData.hasOwnProperty('itemStatus')) {
                if (activeTabData.itemStatus === '4') {
                    pageData.button.map(btn => {
                        if (btn.value === 'confirm' || btn.value === 'reject') {
                            btn.hide = false
                        }
                    })
                } else {
                    pageData.button.map(btn => {
                        if (btn.value === 'confirm' || btn.value === 'reject') {
                            btn.hide = true
                        }
                    })
                }
            }

            if (activeTabData.appealStatus == null || activeTabData.appealStatus !== '1') {
                pageData.button.map(btn => {
                    if (btn.value === 'approval' || btn.value === 'reject') {
                        btn.hide = true
                    }
                })
            } else if (activeTabData.appealStatus === '1') {
                pageData.button.map(btn => {
                    if (btn.value === 'approval' || btn.value === 'reject') {
                        btn.hide = false
                    }
                })
            }
            if (tablePage) {
                tablePage.currentPage = 1
            }
            if (listGrid) {
                listGrid.clearCheckboxReserve()
                listGrid.clearCheckboxRow()
                _this.loadData(activeTabData)
            }
        },
        allowSelfEdit (row){
            if(row.launch == '1'){
                return true
            }
            return false
        },
        allowEdit (row){
            if(row.needCheck!=='1'){
                return true
            }
            if(row.returnSignedFile =='1' && row.reject=='0'){
                return true
            }
            return false
        }
    }
}
</script>