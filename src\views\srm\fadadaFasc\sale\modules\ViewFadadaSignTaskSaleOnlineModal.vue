<template>
  <div class="FadadaSignTaskSale business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="detail"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />
  </div>
</template>
  
<script lang="jsx">
  
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {  getAction, httpAction } from '@/api/manage'
import { BUTTON_BACK } from '@/utils/constant.js'
  
export default {
    name: 'EditFadadaSignTaskSaleOnlineModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/fadadaSignTaskSale/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            url: {               
                download: '/attachment/purchaseAttachment/download',
                signFileDownload: '/electronsign/fadada/fadadaSignTaskSale/signFileDownload',
                detail: '/electronsign/fadada/fadadaSignTaskSale/queryById'
            }
        }
    },
    methods: {
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dDVH_24c39726`, '主体信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'),
                        groupNameI18nKey: '',
                        groupCode: 'fadadaTaskActorSaleList',
                        groupType: 'item',
                        sortOrder: '2'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PWQIBI_dc74fdbe`, '签署文件/附件'),
                        groupNameI18nKey: '',
                        groupCode: 'fadadaSignAttachmenSaleList',
                        groupType: 'item',
                        sortOrder: '3'
                    }
                ],
                formFields: [                    
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEsNo`, '采购ELS号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'toElsAccount',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRRL_4460e249`, '采购名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'purchaseName',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_standardType`, '单据类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'busType',
                        dictCode: 'electronicSignatureBusType',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_levelNumber`, '单据编号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'busNumber',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWdD_3a0b6871`, '签署主题'),
                        fieldLabelI18nKey: '',
                        fieldName: 'signTaskSubject',
                        required: '1',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQAAc_eec498e0`, '签署文档类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'signDocType',
                        dictCode: 'fadadaSignDocType',
                        defaultValue: 'contract',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LSRAKI_95a080fc`, '任务过期时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'expiresTime',
                        required: '1',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pICWre_61090d0f`, '哪一方先盖章'),
                        fieldLabelI18nKey: '',
                        fieldName: 'firstSeal',
                        dictCode: 'srmSignatoryType',
                        defaultValue: 'sale',
                        required: '1',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherToStampOnline`, '是否线上盖章'),
                        fieldLabelI18nKey: '',
                        fieldName: 'onlineSealed',
                        dictCode: 'yn',
                        defaultValue: '0',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWLSzE_e431f745`, '签署任务状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'signTaskStatus',
                        dictCode: 'fadadaSignTaskStatus',
                        defaultValue: '',
                        disabled: true
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldType: 'select',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_esignStatus`, '签署状态'),
                        fieldLabelI18nKey: '',
                        field: 'signTaskStatus',
                        dictCode: 'fadadaSignTaskStatus',
                        defaultValue: '',
                        width: '120',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'corpName',
                        fieldType: 'selectModal',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'useName',
                        fieldType: 'selectModal',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCdDAc_e00692ed`, '签署方主体类型'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'actorType',
                        fieldType: 'select',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'corp',
                        width: '120',
                        dictCode: 'fadadaActorType',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeR_1486c9d`, '印章名'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'sealName',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'selectModal',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_sUCbW_c152e4da`, '参与方权限'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'permissions',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'select',
                        defaultValue: 'sign',
                        width: '120',
                        dictCode: 'fadadaPermissions',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQTPhffUEdf_6327e6d7`, '是否需要法大大平台送达'),
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'sendNotification',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'true',
                        width: '120',
                        dictCode: 'fadadaJudge',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dfCK_439e2d13`, '送达方式'),
                        fieldType: 'select',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'notifyWay',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'mobile',
                        width: '120',
                        dictCode: 'fadadaAccountType',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话号码'),
                        fieldType: 'input',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'mobile',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'),
                        fieldType: 'input',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'email',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    // {
                    //     title: '签章类型',
                    //     fieldType: 'select',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'fieldType',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: 'corp_seal',
                    //     width: '120',
                    //     dictCode: 'fadadaFieldType',
                    //     required: '1'
                    // },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`, '是否自动落章'),
                        fieldType: 'select',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'requestVerifyFree',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'false',
                        width: '120',
                        dictCode: 'fadadaJudge',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'),
                        fieldType: 'selectModal',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: ''
                    },
                    {
                        title: '文件ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'fieldDocId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    // {
                    //     title: '定位模式',
                    //     fieldType: 'select',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionMode',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: 'keyword',
                    //     width: '120',
                    //     dictCode: 'fadadaPositionMode',
                    //     required: '1'
                    // },
                    // {
                    //     title: '定位页码',
                    //     fieldType: 'number',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionPageNo',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '1',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '1'
                    // },
                    // {
                    //     title: '关键字',
                    //     fieldType: 'input',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionKeyword',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: ''
                    // },
                    // {
                    //     title: 'Y轴值(px)',
                    //     fieldType: 'number',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionY',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '0'
                    // },
                    // {
                    //     title: 'X轴值(px)',
                    //     fieldType: 'number',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionX',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '0'
                    // },
                    {
                        title: '企业id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'clientCorpId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '法大大企业id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'openCorpId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '用户id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'clientUserId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '法大大用户id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'openUserId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '成员ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'owning',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'sale',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '成员ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'memberId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '印章ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'sealId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称'),
                        // fieldType: 'input',
                        groupCode: 'fadadaSignAttachmenSaleList',
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'),
                        // fieldType: 'select',
                        groupCode: 'fadadaSignAttachmenSaleList',
                        fieldLabelI18nKey: '',
                        field: 'signType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'doc',
                        width: '120',
                        dictCode: 'fadadaFileType',
                        required: '1'
                    },
                    {
                        title: '文件表文件ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaSignAttachmenSaleList',
                        fieldLabelI18nKey: '',
                        field: 'relationId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        groupCode: 'fadadaSignAttachmenSaleList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: 'sourceType_dictText',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载')}
                                    onClick={() => this.downloadEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载')}</a>)
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jQIUB_c9c1b072`, '原文件预览')}
                                    style="margin-left:8px"
                                    onClick={() => this.preViewEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jQIUB_c9c1b072`, '原文件预览')}</a>)  
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQI_1183a9c9`, '查看签署文件')}
                                    style="margin-left:8px"
                                    onClick={() => this.downloadSignEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQI_1183a9c9`, '查看签署文件')}</a>)                              
                                return resultArray
                            }
                        }
                    }
                ]
            }
        },
        downloadEvent (row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownloadi18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction(this.url.download, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        downloadSignEvent (row){
            const allData = this.getAllData()     
            if(row.existSignFile!='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_xMKPWQI_bb284020`, '不存在签署文件'))
                return
            }     
            httpAction(this.url.signFileDownload, {id: allData.relationId, fileId: row.id}, 'get').then((res) => {
                if (res.success) {
                    window.open(res.message, '_blank')
                } else {
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>
  