# 表格自动高度功能使用指南

## 📋 功能概述

表格自动高度功能允许根据数据条数动态计算表格高度，提供更好的用户体验：
- ✅ 避免固定高度造成的空白浪费
- ✅ 根据实际数据量调整显示区域
- ✅ 支持最大最小高度限制
- ✅ 完全向下兼容现有功能

## 🎯 适用场景

### ✅ 推荐使用的场景
- 数据量变化较大的表格（1-50条记录）
- 详情页面的子表格
- 需要紧凑显示的页签
- 用户体验要求较高的界面

### ❌ 不推荐使用的场景
- 数据量巨大且稳定的表格（>100条记录）
- 需要固定高度的特殊布局
- 已有自定义高度逻辑的表格

## 🚀 快速启用

### 步骤1：找到配置文件
```
文件路径：{account}/purchase_supplierMasterData_{templateNumber}_{templateVersion}.js
```

### 步骤2：添加配置
在目标页签的 `extend` 配置中添加：
```javascript
extend: {
    autoHeightByData: true  // 仅此一行即可启用！
}
```

### 步骤3：验证效果
保存配置文件，刷新页面即可看到效果。

## ⚙️ 详细配置

### 基础配置
```javascript
extend: {
    autoHeightByData: true,     // 启用自动高度
    rowHeight: 36,              // 每行高度（默认36px）
    headerHeight: 50,           // 表头高度（默认50px）
    paddingHeight: 20,          // 内边距（默认20px）
    maxHeight: 600,             // 最大高度（默认600px）
    minHeight: 150              // 最小高度（默认150px）
}
```

### 参数说明
| 参数 | 类型 | 默认值 | 范围 | 说明 |
|------|------|--------|------|------|
| `autoHeightByData` | Boolean | false | - | 是否启用自动高度 |
| `rowHeight` | Number | 36 | 20-100 | 每行数据的高度（px） |
| `headerHeight` | Number | 50 | 30-100 | 表头高度（px） |
| `paddingHeight` | Number | 20 | 0-50 | 内边距高度（px） |
| `footerHeight` | Number | 0 | 0-50 | 页脚高度（px） |
| `maxHeight` | Number | 600 | ≥200 | 最大高度限制（px） |
| `minHeight` | Number | 150 | 100-maxHeight | 最小高度限制（px） |

## 📊 计算公式

```
计算高度 = 数据条数 × rowHeight + headerHeight + footerHeight + paddingHeight
最终高度 = Math.max(minHeight, Math.min(计算高度, maxHeight))
```

## 🎛️ 配置方案推荐

### 方案A：紧凑型
适合数据较少、空间有限的场景
```javascript
extend: {
    autoHeightByData: true,
    rowHeight: 32,
    headerHeight: 40,
    paddingHeight: 15,
    maxHeight: 400,
    minHeight: 120
}
```

### 方案B：标准型（推荐）
适合大多数业务场景
```javascript
extend: {
    autoHeightByData: true,
    rowHeight: 36,
    headerHeight: 50,
    paddingHeight: 20,
    maxHeight: 600,
    minHeight: 150
}
```

### 方案C：宽松型
适合数据较多、屏幕较大的场景
```javascript
extend: {
    autoHeightByData: true,
    rowHeight: 40,
    headerHeight: 60,
    paddingHeight: 25,
    maxHeight: 800,
    minHeight: 200
}
```

## 📈 效果示例

### 场景1：3条数据
- 计算：3 × 36 + 50 + 20 = 178px
- 结果：178px（紧凑显示）

### 场景2：10条数据
- 计算：10 × 36 + 50 + 20 = 430px
- 结果：430px（适中高度）

### 场景3：20条数据
- 计算：20 × 36 + 50 + 20 = 790px
- 结果：600px（受最大高度限制，出现滚动条）

### 场景4：无数据
- 结果：150px（最小高度，显示"暂无数据"）

## 🔧 调试和验证

### 开发环境调试
启用自动高度后，开发环境下会在控制台输出调试信息：
```
[supplierContactsInfoList] 自动高度计算: {
  dataLength: 5,
  calculatedHeight: 250,
  config: { rowHeight: 36, headerHeight: 50, ... },
  formula: "5 × 36 + 50 + 0 + 20 = 250px"
}
```

### 验证清单
- [ ] 配置文件已正确修改
- [ ] 页面刷新后生效
- [ ] 不同数据量下高度合理
- [ ] 最大最小高度限制正常
- [ ] 控制台无错误信息

## 🛠️ 故障排除

### 问题1：配置不生效
**可能原因：**
- 配置文件路径错误
- 语法错误
- 缓存问题

**解决方案：**
- 检查文件路径和语法
- 清除浏览器缓存
- 检查控制台错误信息

### 问题2：高度计算异常
**可能原因：**
- 参数配置超出范围
- 数据格式异常

**解决方案：**
- 检查参数是否在合理范围内
- 查看控制台调试信息
- 使用默认配置测试

### 问题3：性能问题
**可能原因：**
- 数据量过大
- 频繁的高度重计算

**解决方案：**
- 考虑使用分页
- 调整maxHeight限制
- 关闭自动高度功能

## 📞 技术支持

如遇到问题，请提供以下信息：
1. 配置文件内容
2. 控制台错误信息
3. 数据量和预期效果
4. 浏览器和版本信息

---

**更新时间：** 2025-06-23  
**版本：** v1.0  
**作者：** Augment Agent
