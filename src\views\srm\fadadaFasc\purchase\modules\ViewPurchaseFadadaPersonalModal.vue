<template>
  <div class="PurchaseFadadaPersonal business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="detail"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { BUTTON_BACK } from '@/utils/constant.js'

export default {
    name: 'EditPurchaseFadadaPersonalModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/purchaseFadadaPersonal/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            url: {
                detail: '/contract/purchaseContractHead/queryById'
            }
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccountId`, '用户id'),
                        fieldLabelI18nKey: '',
                        fieldName: 'clientUserId'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_userAccount`, '用户账号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'subAccount'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '用户名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userName'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jDdieyAc_c344b054`, '用户注册账号类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'accountType',
                        dictCode: 'fadadaAccountType',
                        defaultValue: 'phone'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hffjDey_658023d5`, '法大大用户账号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'accountName'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userIdentType',
                        dictCode: 'fadadaUserIdentType'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userIdentNo'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_phone`, '手机号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'mobile'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'),
                        fieldLabelI18nKey: '',
                        fieldName: 'email'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_WEmy_45c0d14c`, '银行卡号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'bankAccountNo',
                        required: ''
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'multiple',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jDLiCK_94dc6462`, '用户认证方式'),
                        fieldLabelI18nKey: '',
                        fieldName: 'identMethod',
                        dictCode: 'fadadaUserIdentMethod',
                        required: ''
                    },
                    // {
                    //     groupCode: 'baseForm',
                    //     fieldType: 'multiple',
                    //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiKELxqAtVH_596dbf71`, '认证时页面不可编辑信息'),
                    //     fieldLabelI18nKey: '',
                    //     fieldName: 'nonEditableInfo',
                    //     dictCode: 'fadadaUserNonEditableInfo',
                    //     required: ''
                    // },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'multiple',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbvL_2ed2664c`, '授权范围'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authScopes',
                        dictCode: 'fadadaUserAuthScopes',
                        required: ''
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'identProcessStatus_dictText'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbyR_2ed1f524`, '授权结果'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authResult_dictText'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbLiKy_c896ceff`, '授权认证链接'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authUrl',
                        extend: {
                            linkConfig: {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                titleI18nKey: 'i18n_title_authenticationLink'
                            },
                            exLink: true
                        }
                    }
                ]
            }
        }
    }
}
</script>
