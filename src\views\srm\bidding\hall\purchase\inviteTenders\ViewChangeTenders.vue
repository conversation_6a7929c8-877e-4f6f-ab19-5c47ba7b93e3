<template>
  <div>
    <change-tenders
      v-if="viewChangeTender"
      @create-tender="handleCreateTender"
      @edit-tender="handleEditTender"></change-tenders>
    <new-and-edit-tender 
      v-else
      :id="tenderId"
      :relationId="relationId"
      :tender-type="tenderType"
      @go-back="goBack"
      @save-tender="saveTender"
      @approval-tender="approvalTender"
    ></new-and-edit-tender>
  </div>
</template>
<script>
import changeTenders from '@/views/srm/bidding/hall/modules/ChangeTenders'
import newAndEditTender from '@/views/srm/bidding/hall/modules/NewAndEditTender'
import { postAction } from '@/api/manage'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        changeTenders,
        newAndEditTender
    },
    data () {
        return {
            viewChangeTender: true,
            tenderId: null,
            tenderType: null,
            relationId: null
        }
    },
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        })
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleCreateTender () {
            this.tenderId = ''
            this.tenderType = 'new'
            this.relationId = this.vuex_currentEditRow.id
            this.viewChangeTender= false
        },
        handleEditTender ({ row }) {
            this.tenderId = row.id
            this.tenderType = 'edit'
            this.relationId = this.vuex_currentEditRow.id
            this.viewChangeTender= false
        },
        goBack () {
            this.viewChangeTender= true
        },
        // 审批
        approvalTender (tenderForm) {
            if(tenderForm.id == ''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWsMKDJUzW_eaeb8654`, '请先保存再提交审批！'))
                return
            }
            let url = 'elsUflo/audit/submit'
            let param = {}
            tenderForm['relationId'] = this.relationId
            param['businessId'] = tenderForm.id
            param['businessType'] = 'changeBidding'
            param['auditSubject'] = '招标变更单' + tenderForm.changeNumber
            param['params'] = JSON.stringify(tenderForm)
            postAction(url, param).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        saveTender () {
            
        }
    }
  
}
</script>