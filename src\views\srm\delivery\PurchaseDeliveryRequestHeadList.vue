<!--
 * @Author: gongzhirong
 * @Date: 2022-06-14 10:07:27
 * @LastEditTime: 2022-06-16 10:43:06
 * @LastEditors: gongzhirong
 * @Description: 
-->
<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
    />
    <!-- 编辑界面 -->
    <purchase-delivery-request-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <!-- 详情界面 -->
    <purchase-delivery-request-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import PurchaseDeliveryRequestEdit from './modules/PurchaseDeliveryRequestEdit'
import PurchaseDeliveryRequestDetail from './modules/PurchaseDeliveryRequestDetail'
import { ListMixin } from '@comp/template/list/ListMixin'
import { USER_INFO } from '@/store/mutation-types'
import { getAction } from '@/api/manage'
export default {
  mixins: [ListMixin],
  components: {
    PurchaseDeliveryRequestEdit,
    PurchaseDeliveryRequestDetail
  },
  data() {
    return {
      showEditPage: false,
      pageData: {
        businessType: 'deliveryRequest',
        formField: [
          {
            type: 'input',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
            fieldName: 'keyWord',
            placeholder: '请输入关键字'
          },
          {
            type: 'select',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
            fieldName: 'requestStatus',
            dictCode: 'srmDeliveryRequestStatus'
          },
          {
            type: 'input',
            label: '业务账号',
            fieldName: 'busAccount',
            placeholder: '请输入业务账号'
          },
          {
            type: 'input',
            label: '关联id',
            fieldName: 'relationId',
            placeholder: '请输入关联id'
          },
          {
            type: 'input',
            label: '模板名称',
            fieldName: 'templateName',
            placeholder: '请输入模板名称'
          },
          {
            type: 'input',
            label: '模板编号',
            fieldName: 'templateNumber',
            placeholder: '请输入模板编号'
          },
          {
            type: 'input',
            label: '版本',
            fieldName: 'templateVersion',
            placeholder: '请输入版本'
          },
          {
            type: 'input',
            label: '模板账号',
            fieldName: 'templateAccount',
            placeholder: '请输入模板账号'
          },
          {
            type: 'input',
            label: '送货申请单号',
            fieldName: 'requestNumber',
            placeholder: '请输入送货申请单号'
          },
          {
            type: 'input',
            label: '送货申请单名称',
            fieldName: 'requestName',
            placeholder: '请输入送货申请单名称'
          },
          {
            type: 'input',
            label: '采购方名称',
            fieldName: 'purchaseName',
            placeholder: '请输入采购方名称'
          },
          {
            type: 'input',
            label: '公司代码',
            fieldName: 'company',
            placeholder: '请输入公司代码'
          },
          {
            type: 'input',
            label: '公司名称',
            fieldName: 'companyName',
            placeholder: '请输入公司名称'
          },
          {
            type: 'input',
            label: '采购组织',
            fieldName: 'purchaseOrg',
            placeholder: '请输入采购组织'
          },
          {
            type: 'input',
            label: '采购组织名称',
            fieldName: 'purchaseOrgName',
            placeholder: '请输入采购组织名称'
          },
          {
            type: 'input',
            label: '采购组',
            fieldName: 'purchaseGroup',
            placeholder: '请输入采购组'
          },
          {
            type: 'input',
            label: '采购组名称',
            fieldName: 'purchaseGroupName',
            placeholder: '请输入采购组名称'
          },
          {
            type: 'input',
            label: '采购负责人',
            fieldName: 'purchasePrincipal',
            placeholder: '请输入采购负责人'
          },
          {
            type: 'input',
            label: '工厂',
            fieldName: 'factory',
            placeholder: '请输入工厂'
          }
        ],
        form: {
          keyWord: '',
          busAccount: '',
          relationId: '',
          templateName: '',
          templateNumber: '',
          templateVersion: '',
          templateAccount: '',
          requestNumber: '',
          requestName: '',
          purchaseName: '',
          requestStatus: '',
          company: '',
          companyName: '',
          purchaseOrg: '',
          purchaseOrgName: '',
          purchaseGroup: '',
          purchaseGroupName: '',
          purchasePrincipal: '',
          factory: '',
          factoryName: ''
        },
        button: [
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', hideCode: 'deliveryNotice:hideBtn:add' },
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns }
        ],
        optColumnList: [
          { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView },
          { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, hideCode: 'deliveryNotice:hideBtn:edit' },
          { type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#`, '复制'), clickFn: this.handleCopy },
          { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDel, hideCode: 'deliveryNotice:hideBtn:delete' },
          { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operaRecord`, '操作记录'), clickFn: this.handleRecord },
          { type: 'history', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_auditRecord`, '审批记录'), clickFn: this.auditHis }
        ],
        optColumnWidth: 200
      },
      url: {
        list: '/delivery/purchaseDeliveryRequestHead/list',
        copy: '/delivery/purchaseDeliveryRequestHead/copyById',
        transfer: '/delivery/purchaseDeliveryRequestHead/tacticsTransfer',
        add: '/delivery/purchaseDeliveryRequestHead/add',
        delete: '/delivery/purchaseDeliveryRequestHead/delete',
        deleteBatch: '/delivery/purchaseDeliveryRequestHead/deleteBatch',
        importExcelUrl: 'delivery/purchaseDeliveryRequestHead/importExcel',
        changeVersion: '/delivery/purchaseDeliveryRequestHead/upgradeVersion',
        columns: 'PurchaseDeliveryRequestHeadList',
        exportXlsUrl: '/delivery/purchaseDeliveryRequestHead/exportXls',
        getOrgInfo: '/account/permissionData/getOrgInfo'
      }
    }
  },
  methods: {
    handleAdd() {
      if (this.$refs.listPage.loading) return
      this.$refs.listPage.loading = true
      let userinfo = this.$ls.get(USER_INFO)
      let subAccount = this.$ls.get('Login_subAccount')
      let elsAccount = this.$ls.get('Login_elsAccount')
      this.$refs.listPage.openTemplateModal()
      return
      getAction(this.url.getOrgInfo, { elsAccount: elsAccount, subAccount: subAccount })
        .then((res) => {
          this.$refs.listPage.loading = false

          this.$refs.listPage.openTemplateModal({
            ...res.result,
            purchasePrincipal: userinfo.superiorLeader
          })
        })
        .catch(() => {
          this.$refs.listPage.loading = false
        })
    },
    handleCopy(row) {
      let that = this
      this.$confirm({
        content: this.$srmI18n(`${this.$getLangAccount()}#`, '确认复制该送货申请单？'),
        onOk: function () {
          that.$refs.listPage.confirmLoading = true
          getAction(that.url.copy, { id: row.id })
            .then((res) => {
              if (res.success) {
                that.$refs.listPage.loadData()
                that.$message.success(res.message)
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.$refs.listPage.confirmLoading = false
            })
        }
      })
    },
    allowEdit(row) {
      if (row.requestStatus == '1') {
        return true
      }
      return false
    },
    allowDel(row) {
      if (row.requestStatus == '0' || row.requestStatus == '3') {
        return false
      }
      return true
    }
  }
}
</script>
