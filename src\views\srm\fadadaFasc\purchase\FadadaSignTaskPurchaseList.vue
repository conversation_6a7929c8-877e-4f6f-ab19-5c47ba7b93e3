<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showEditOnePage && !showDetailOnePage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <EditFadadaSignTaskPurchase-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideEditPage" />
    <ViewFadadaSignTaskPurchase-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <EditFadadaSignTaskPurchaseOneModal
      v-if="showEditOnePage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideEditPage" />
    <ViewFadadaSignTaskPurchaseOneModal
      v-if="showDetailOnePage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    
    <a-modal
      v-drag    
      v-model="visible"
      :title="$srmI18n(`${this.$getLangAccount()}#i18n_title_reasonsRepealing`, '撤销原因')"
      @ok="handleOk">
      <a-form-model
        :model="form">
        <a-form-model-item :label="$srmI18n(`${this.$getLangAccount()}#i18n_title_reasonsRepealing`, '撤销原因')">
          <a-input
            v-model="form.reason" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import EditFadadaSignTaskPurchaseModal from './modules/EditFadadaSignTaskPurchaseModal'
import EditFadadaSignTaskPurchaseOneModal from './modules/EditFadadaSignTaskPurchaseOneModal'
import ViewFadadaSignTaskPurchaseOneModal from './modules/ViewFadadaSignTaskPurchaseOneModal'
import ViewFadadaSignTaskPurchaseModal from './modules/ViewFadadaSignTaskPurchaseModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {httpAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EditFadadaSignTaskPurchaseModal,
        ViewFadadaSignTaskPurchaseModal,
        EditFadadaSignTaskPurchaseOneModal,
        ViewFadadaSignTaskPurchaseOneModal
    },
    data () {
        return {
            visible: false,
            showEditPage: false,
            showEditOnePage: false,
            showDetailOnePage: false,
            form: {
                id: null,
                reason: null
            },
            pageData: {
                businessType: 'fadada',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWdD_3a0b6871`, '签署主题'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWdD_3a0b6871`, '签署主题')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_VaRCWIPWLS_34cfb8d2`, '新增供方线下签署任务'), authorityCode: 'fadada#fadadaSignTaskPurchase:add', icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'fadada#fadadaSignTaskPurchase:view', clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'fadada#fadadaSignTaskPurchase:edit', clickFn: this.handleEdit, allow: this.showEditCondition},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销'), authorityCode: 'fadada#fadadaSignTaskPurchase:cancel', clickFn: this.handleCancel, allow: this.showCancelCondition},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_EStFYM_fa2b039e`, '业务单据退回'), authorityCode: 'fadada#fadadaSignTaskPurchase:voucherSendBackFlag', clickFn: this.voucherSendBackFlag, allow: this.showVoucherSendBackFlag},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), authorityCode: 'fadada#fadadaSignTaskPurchase:send', clickFn: this.handleSend, allow: this.showSendCondition},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_PWzEXV_f49e4158`, '签署状态刷新'), authorityCode: 'fadada#fadadaSignTaskPurchase:refresh', clickFn: this.handleRefresh, allow: this.showRefreshCondition},
                    {type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQA_1183c336`, '查看签署文档'), authorityCode: 'fadada#fadadaSignTaskPurchase:signFileDownload', clickFn: this.handleDownload, allow: this.showDownloadCondition},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'fadada#fadadaSignTaskPurchase:delete', clickFn: this.handleDelete, allow: this.showDelCondition},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 300
            },
            url: {
                list: '/electronsign/fadada/fadadaSignTaskPurchase/list',
                add: '/electronsign/fadada/fadadaSignTaskPurchase/add',
                refresh: '/electronsign/fadada/fadadaSignTaskPurchase/refresh',
                send: '/electronsign/fadada/fadadaSignTaskPurchase/listSend',
                cancel: '/electronsign/fadada/fadadaSignTaskPurchase/cancel',
                signFileDownload: '/electronsign/fadada/fadadaSignTaskPurchase/signFileDownload',
                voucherSendBackFlag: '/electronsign/fadada/fadadaSignTaskPurchase/voucherSendBackFlag',
                delete: '/electronsign/fadada/fadadaSignTaskPurchase/delete',
                columns: 'fadadaSignTaskPurchaseList'
            }
        }
    },
    mounted () {
        this.serachCountTabs('/electronsign/fadada/fadadaSignTaskPurchase/counts')
    },
    methods: {
        showVoucherSendBackFlag (row){
            //已退回的单据直接置灰
            if(row.voucherSendBackFlag==='1'){
                return true
            }
            //业务单据ID不为空
            if(row.busId && row.busId!=null){
                //任务创建中并且未开启，按钮亮色
                if(row.signTaskStatus==='task_created' && row.isAutoStart!='1'){
                    return false
                }
                //任务已发起但处于撤回(即终止)状态
                if(row.signTaskStatus==='task_terminated' && row.isAutoStart==='1'){
                    return false
                }
            }
            return true
        },
        showCancelCondition (row){
            // debugger
            //已退回的单据直接置灰
            if(row.voucherSendBackFlag==='1'){
                return true
            }
            //已撤销状态，按钮置灰
            if(row.signTaskStatus==='task_terminated'){
                return true
            }
            //签署已发起，未签署完成
            if(row.isAutoStart==='1' && row.signTaskStatus!='task_finished'){
                return false
            }
            return true
        },
        showDownloadCondition (row){
            //已退回的单据直接置灰
            if(row.voucherSendBackFlag==='1'){
                return true
            }
            if(row.signTaskStatus==='task_finished'){
                return false
            }
            return true
        },
        showSendCondition (row){
            //若无方法参与，直接置空
            if(row.saleTakePartIn!='1'){
                return true
            }
            
            //已退回的单据直接置灰
            if(row.voucherSendBackFlag==='1'){
                return true
            }
            //供方线下签署，采购先签并签署完成，发送按钮显示
            if(row.isAutoFinish==='1' && row.onlineSealed!='1' && row.firstSeal==='purchase' && row.sendStatus!='1'){
                return false
            }
            //供方线下签署，供方先签，未发送，未开启
            if( row.onlineSealed!='1' && row.isAutoStart!='1' && row.firstSeal==='sale' && row.sendStatus!='1'){
                return false
            }
            //双方线上签署，签署任务未开启，法大大文件已上传，发送按钮显示
            if(row.onlineSealed==='1' && row.isAutoStart != '1'&& row.sendStatus!='1' && row.signFileUploaded==='1'){
                return false
            }
            return true
        },
        showRefreshCondition (row){
            //已退回的单据直接置灰
            if(row.voucherSendBackFlag==='1'){
                return true
            }
            if(row.isAutoStart==='1'){
                return false
            }
            return true
        },
        handleChidCallback () {
            // this.showDetailOnePage = false
            // this.showEditPage = false
            // this.showDetailPage = false
            // this.showEditOnePage = false
            this.hideEditPage()
        },
        // 返回按钮
        hideEditPage () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
            this.showDetailOnePage= false
            this.showEditPage = false
            this.showDetailPage = false
            this.showEditOnePage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
            this.$router.replace({
            })
        },
        handleAdd () {          
            this.currentEditRow = {}
            this.showEditPage = false
            this.showDetailPage = false
            this.showDetailOnePage = false
            this.showEditOnePage = true
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showDetailPage = false
            if(row.onlineSealed==='1'){  
                //双方线上盖章              
                this.showEditOnePage = false
                this.showEditPage = true
            }else{
                //供方线下盖章
                this.showEditPage = false
                this.showEditOnePage = true
            }
        },
        handleView (row){
            this.currentEditRow = row
            this.showEditOnePage = false
            this.showEditPage = false
            if(row.onlineSealed==='1'){ 
                this.showDetailPage = false
                this.showDetailPage = true
            }else{
                this.showDetailOnePage = false
                this.showDetailOnePage = true
            }
        },
        showEditCondition (row) {
            //已退回的单据直接置灰
            if(row.voucherSendBackFlag==='1'){
                return true
            }
            if(row.isAutoStart==='1'){
                return true
            }
            return false
        },
        showDelCondition (row) {
            //已退回的单据直接置灰
            if(row.voucherSendBackFlag==='1'){
                return true
            }
            if(row.isAutoStart==='1'){
                return true
            }
            return false
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (){
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        handleCancel (row){
            this.form.id = row.id
            this.visible = true            
        },
        handleOk (){
            if(!this.form.reason || this.form.reason=='' ){
                this.$message.warning('撤销原因不能为空')
                return
            }
            this.visible = false
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RIqXhAjPWQL_efc68d4b`, '确定撤销发起的签署流程？'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, that.form, 'get')
                }
            })
        },
        postUpdateData (url, row, type){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, type).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        handleRefresh (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_XVPWLSzE_899b6ade`, '刷新签署任务状态'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLKQXV_b7a32179`, '确认是否刷新？'),
                onOk: function () {
                    that.postUpdateData(that.url.refresh, {id: row.id}, 'get')
                }
            })
        },
        handleSend (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLKQhd_b7ae4462`, '确认是否发送？'),
                onOk: function () {
                    that.postUpdateData(that.url.send, {id: row.id}, 'get')
                }
            })

        },
        handleDownload (row){            
            httpAction(this.url.signFileDownload, {id: row.id}, 'get').then((res) => {
                if (res.success) {
                    window.open(res.message, '_blank')
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        voucherSendBackFlag (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_YMEStF_a6b8949e`, '退回业务单据'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLKQYM_b8894594`, '确认是否退回？'),
                onOk: function () {
                    that.postUpdateData(that.url.voucherSendBackFlag, {id: row.id}, 'get')
                }
            })
        }
    }
}
</script>
