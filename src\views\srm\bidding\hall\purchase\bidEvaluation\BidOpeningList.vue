<template>
  <div class="BidOpeningList">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      :deadline="evaEndTime"
      :renderExtra="renderExtra"
      @content-header-export="handleExport"
    />

    <div
      class="container"
      :style="style">

      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="table">
            <vxe-grid
              ref="grid"
              v-bind="defaultGridOption"
              :columns="columns">
              <template slot="empty">
                <a-empty />
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>

  </div>
</template>

<script lang="jsx">
import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import countdown from '@/components/countdown'
import { getAction, downFile } from '@/api/manage'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        'content-header': ContentHeader,
        countdown
    },
    data () {
        return {
            evaEndTime: null,
            serverTime: null,
            confirmLoading: false,
            supplierEvaScoreList: [],
            form: {},
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), type: 'primary', event: 'export' }
            ],
            showHeader: true,
            height: 0,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'dblclick', mode: 'cell' }
            },
            columns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'), field: 'supplierName', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHL_1f12c73`, '联系人'), field: 'contacts', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contactNumber`, '联系电话'), field: 'phone', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), field: 'email', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationResponse`, '报价响应'), field: 'quoteEcho', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_totalAmount`, '总报价'), field: 'totalAmount', width: 150 },
                /*{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XSum_28038380`, '商务价格'), field: 'targetPrice', width: 150 },*/
                /*{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Bnum_30b6cc13`, '标底价格'), field: 'refPrice', width: 150 },*/
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quoteRank`, '报价排名'), field: 'quoteRank', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_busScore`, '商务分'), field: 'busScore', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XSbsW_d8cd2a80`, '商务权重%'), field: 'busWeights', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XSbsz_d8cd7c61`, '商务权重分'), field: 'busScoreByWeights', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_busRank`, '商务排名'), field: 'busRank', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tecScore`, '技术分'), field: 'tecScore', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tWbsW_9c88036c`, '技术权重%'), field: 'tecWeights', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tWbsz_9c88554d`, '技术权重分'), field: 'tecScoreByWeights', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tecRank`, '技术排名'), field: 'tecRank', width: 150 },

                { title: '价格分', field: 'priceScore', width: 150 },
                { title: '价格权重%', field: 'priceWeight', width: 150 },
                { title: '价格权重分', field: 'priceByWeights', width: 150 },
                { title: '价格排名', field: 'priceRank', width: 150 },

                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_totalScore`, '总分'), field: 'totalScoreNew', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_synthesisRank`, '综合排名'), field: 'synthesisRank', width: 150 }
            ]
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        renderExtra (deadline, fn) {
            const scopedSlots = {
                default: (row) => {
                    // return (<span>{ row.hours } : { row.minutes } : { row.seconds }</span>)
                    return (<span>{ row.totalHours } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_time`, '时') } { row.minutes } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_branch`, '分') } { row.seconds } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_second`, '秒') }</span>)
                }
            }
            return (
                <div class="countdown" style="display: flex; align-items: center;">
                    <a-icon type="info-circle" />
                    <span style="margin: 0 8px;">{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRutK_9fc18dac`, '评标截止倒计时') }：</span>
                    <countdown

                        time={ deadline }
                        scopedSlots={scopedSlots}
                        style={ { fontSize: '12px', color: '#ee1d1d' } }
                        end={ fn }
                    >
                    </countdown>
                </div>
            )
        },
        deleteItemEvent (row, column, ref) {
            const grid = this.$refs[ref]
            grid.remove(row)
        },
        getData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingEvaResult/queryEvaResultByBiddingId'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const result = res.result || []
                    this.$refs.grid.loadData(result)
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        getBiddingHeadData () {
            const { id = '' } = this.vuex_currentEditRow || {}
            const url = '/bidding/purchaseBiddingHead/queryById'

            getAction(url, { id }).then(res => {
                if (!res.success) {
                    this.btns = []
                    return
                }
                const { timestamp = '', result = {} } = res || {}
                let evaEndTime = result.evaEndTime_DateMaps || ''
                this.serverTime = timestamp
                if (evaEndTime) {
                    if (timestamp < evaEndTime) {
                        this.evaEndTime = evaEndTime - timestamp
                    }
                }
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getBiddingHeadData()
            this.getData()
        },
        handleExport () {
            let params = {
                id: this.vuex_currentEditRow.id,
                type: 'bidding'
            }
            let fileName = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_exportFile`, '导出文件')
            downFile('/bidding/purchaseBiddingEvaResult/exportProjectEvaResult', params).then((data) => {
                if (!data) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), fileName+'.xls')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', fileName+'.xls')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            })
        }
    },
    created () {
        this.init()
    }
}
</script>

<style lang="less" scoped>
.BidOpeningList {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
}
</style>

