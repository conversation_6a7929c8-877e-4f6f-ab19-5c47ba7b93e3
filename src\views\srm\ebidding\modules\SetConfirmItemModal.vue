<template>
  <a-modal
    v-drag    
    v-model="madalVisible"
    width="600px"
    :title="$srmI18n(`${$getLangAccount()}#i18n_field_URid_469b0f42`, '预制选项')"
    @ok="setConfirmItemOk">
    <a-spin :spinning="confirmLoading">
      <div class="compare-page-container">
        <div class="page-content-right">
          <div style="margin-bottom: 12px">
            <vxe-grid
              border
              ref="headerGrid"
              row-id="confirmItem"
              show-overflow
              size="small"
              height="350"
              :title="$srmI18n(`${$getLangAccount()}#i18n_field_URid_469b0f42`, '预制选项')"
              :columns="tableHeaderColumn"
              :edit-config="{trigger: 'click', mode: 'cell'}"
              :data="tableHeaderData"
              :edit-rules="rules"
              :check-config="{highlight: true, reserve: true, trigger: 'row'}"
              :toolbar="{ slots: { buttons: 'toolbar_buttons' }}"
            >
              <template #toolbar_buttons>
                <div
                  v-if="!view"
                  style="margin-top:-8px;text-align:right"
                >
                  <a-button
                    @click="addItem"
                    type="primary"
                    :disabled="btnDisable || view"
                    style="margin-left:8px"
                    slot="tabBarExtraContent">  {{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '添加') }}
                  </a-button>
                  <a-button
                    @click="deleteItem"
                    type=""
                    :disabled="btnDisable || view"
                    style="margin-left:8px"
                    slot="tabBarExtraContent">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}
                  </a-button>
                </div>
              </template>
            </vxe-grid>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import {List} from 'ant-design-vue'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
    name: 'SetConfirmItemModal',
    props: {
        currentRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        btnDisable: {
            type: Boolean,
            default: false
        },
        view: {
            type: Boolean,
            default: false
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            headObj: {},
            ladderQuantity: '',
            confirmLoading: false,
            listData: [],
            madalVisible: false,
            tableHeaderData: [],
            tableHeaderColumn: [
                { type: 'checkbox', width: 40, align: 'center'},
                { field: 'optionsCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsNumExample`, '选项编号'), width: 150, editRender: {name: '$input', props: {disabled: this.view} }},
                { field: 'optionsName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_idRL_43c08d13`, '选项名称'), width: 150, editRender: {name: '$input', props: {disabled: this.view} }}
            ],
            rules: {
                optionsCode: [{required: true, message: '每项为必填!'}],
                optionsName: [{required: true, message: '每项为必填!'}]
            },
            curRowData: {}
        }
    },
    mounted () {
    },
    computed: {
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentRow) {
                if (this.currentRow.busAccount || this.currentRow.elsAccount) {
                    account = this.currentRow.busAccount || this.currentRow.elsAccount
                }
            }
            return account
        }
    },
    methods: {
        deleteItem (){
            let currentItem = this.$refs.headerGrid.getCheckboxRecords()
            if(!currentItem.length){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDataDelete`, '请选择需要删除的数据！'))
                return
            }
            this.$refs.headerGrid.removeCheckboxRow()
        },
        open (row) {
            let that = this
            this.madalVisible = true
            this.curRowData = row
            let itemDetailList = row.confirmItemList || []
            let itemDetailCheckItem=itemDetailList.find((val)=>val.optionsCode==row.content)
            if(itemDetailList){
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData(itemDetailList)
                    setTimeout(()=>{
                        that.$refs.headerGrid.toggleCheckboxRow(itemDetailCheckItem)
                    }, 500)
                       
                
                })
            } else {
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData([])
                })
            }
        },
        goBack () {
            this.$emit('hide')
        },
        addItem () {
            let grid = this.$refs.headerGrid
            let item2 = {}
            grid.insertAt(item2, -1)
            this.$forceUpdate()
        },
        
        async setConfirmItemOk (){
            if (this.view) {
                this.madalVisible = false
                return
            }
            const $table = this.$refs.headerGrid
            const errMap = await $table.validate(true).catch(errMap => errMap)
            if (errMap) {
                return false
            }
            let itemList = this.$refs.headerGrid.getTableData().fullData || []
            let arr = [...new Set(itemList.map(rs => rs.optionsCode))]
            if (arr.length !== itemList.length) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_idAyxOVB_a5d2ae21`, '选项编号不能重复！'))
                return
            }
            this.$parent.$refs.editPage.$parent.confirmItemOk(itemList)
            this.madalVisible = false
        }
    }
}
</script>
<style lang="less" scoped>
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 1000px;
            background-color: #fff
        }
    }
</style>