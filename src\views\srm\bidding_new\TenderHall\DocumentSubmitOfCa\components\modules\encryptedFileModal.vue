<template>
  <div>
    <a-modal
    v-drag    
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_eBQIuw_22779ec7`, '投标文件加密')"
      :visible="visible"
      :width="800"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      :okText="$srmI18n(`${$getLangAccount()}#i18n_field_uw_a5d26`, '加密')"
      :cancelText="$srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭')"
      @ok="selectedOk"
      @cancel="close">
      <a-alert
        :message="$srmI18n(`${$getLangAccount()}#i18n_field_tklRWQIuwKQIRxiTsxsQSsQByWuwMLSWVPuwQIXVW_d5788a5f`, '操作说明：文件加密时文件名不允许包含中文或中文符号，加密完成后，请将加密文件上传。')"
        banner />
      <titleTrtl>
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_QIVt_2f5ce87f`, '文件清单') }}</span>
        <template slot="right">
          <input
            @change="selecteFileAfter"
            style="display: none;"
            type="file"
            ref="file"
            id="file"
            :value="$srmI18n(`${$getLangAccount()}#i18n_field_iFouwQI_8ef206fa`, '选择待加密文件')">
          <a-button @click="selecteFile">{{ $srmI18n(`${$getLangAccount()}#i18n_field_SuIc_333eb111`, '添加一行') }}</a-button>
        </template>
      </titleTrtl>
      <listTable
        ref="listTable"
        :pageData="pageData"
        :fromSourceData="attachmentDTOList"
        :statictableColumns="tableColumns"
        :editRulesProps="editRulesProps"
        :showTablePage="false" />
      <a-modal
    v-drag    
        centered
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_VWNuwJO_ae1ae8cd`, '请输入加密字段')"
        :visible="showPw"
        :width="300"
        :confirmLoading="confirmLoading"
        @ok="encryptedFile"
        @cancel="() => {this.showPw = false}">
        <a-input
          v-model="pin"
          :maxLength="100"></a-input>
      </a-modal>
    </a-modal>
    
  </div>
</template>

<script lang="jsx">
import { postAction } from '../../api/request'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
export default {
    name: 'EncryptedFileModal',
    props: {
    },
    components: {
        titleTrtl,
        listTable
    },
    data () {
        return {
            visible: false,
            showPw: false,
            loading: false,
            confirmLoading: false,
            attachmentDTOList: [],
            pageData: {
                optColumnList: [{ type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleTableDel }]
            },
            editRulesProps: {
                path: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMjKQIHO_51776f15`, '请填写原始文件路径')}]
            },
            tableColumns: [
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jKQIHO_d2476610`, '原始文件路径'),
                    field: 'path',
                    required: '1',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uwQIHO_3b0c2c2a`, '加密文件路径'),
                    field: 'encpath'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uwzE_26f5efb1`, '加密状态'),
                    field: 'status',
                    slots: {
                        default: ({row}) => {
                            return [
                                <span> {row.status == '0' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Luw_18da1d0`, '未加密') : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Iuw_16b0698`, '已加密')}</span>   
                            ]
                        }
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    fieldLabelI18nKey: '',
                    field: 'grid_opration',
                    defaultValue: '',
                    slots: { default: 'grid_opration' }
                }
            ],
            pin: ''
        }
    },
    methods: {
        open () {
            this.visible = true
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            this.$refs['listTable'].getValidate().then(res => {
                let data = this.$refs['listTable'].getTableData().fullData
                if (data.length == 0) return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFuwQI_6e4b90ac`, '请选择加密文件'))
                this.showPw = true
            })
            
        },
        encryptedFile () {
            let fullData = this.$refs['listTable'].getTableData().fullData.filter(item => item.status == '0')
            let p = fullData.map(item => {
                let params = {
                    raw: item.path,
                    pin: this.pin,
                    type: 311
                }
                let p = new Promise((resolve, reject) => {
                    postAction('/uk/action', params).then(res => {
                        try {
                            let path = res.data.replace(/\\/g, '/')
                            let data = JSON.parse(path)
                            item.encpath = data.encpath.replace(/\//g, '\\')
                            item.status = '1'   
                        } catch {
                            this.confirmLoading = false

                        }
                        resolve(res)
                    }, (err) => {
                        reject(err)
                    })
                })
                return p 
            })
            this.confirmLoading = true
            Promise.all(p).then(res => {
                this.confirmLoading = false
                this.showPw = false
            }, (err) => {
                console.log(err)
                this.confirmLoading = false
            })
            
        },
        selecteFile () {
            // this.$refs.file.click()
            let fileItem = {
                path: '',
                encpath: '',
                status: '0'
            }
            this.$refs['listTable'].insertAt(fileItem, -1)
        },
        selecteFileAfter (file) {
            let path = document.getElementById('file').value
            console.log(this.$refs['file'].value)
            let fileItem = {
                path,
                encpath: '',
                status: '0'
            }
            this.$refs['listTable'].insertAt(fileItem, -1)
        },
        // 删除行
        handleTableDel (row) {
            console.log(row)
            this.$refs['listTable'].removeRow(row)
        }
    }
}
</script>