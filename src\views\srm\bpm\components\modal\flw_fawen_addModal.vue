<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_IImyC_706eecac`, '下一个节点')"
          prop="nextNode">
          <a-tag
            v-for="(item, index) in form.nextNode"
            :key="index"
            color="blue">{{ item.taskName }}</a-tag>
        </a-form-model-item>
        <a-form-model-item
          v-for="(item, index) in form.nextNode"
          :key="index"
          :label="item.taskName"
          prop="usersIds"
        >
          <a-tag
            v-for="(users, userIndex) in item.extObj"
            :key="userIndex"
            size="large"
            color="blue"
            closable
            @close="delUsers(index, userIndex)"
          >{{ users.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectUsers(index)"></a-button>
          </div>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_KQujor_2ee0ce38`, '是否持有待办')"
          prop="pass">
          <a-select
            style="width: 120px"
            v-model="form.completed">
            <a-select-option :value="true">
              {{ $srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是') }}
            </a-select-option>
            <a-select-option :value="false">
              {{ $srmI18n(`${$getLangAccount()}#i18n_title_no`, '否') }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { getNextNodeFaWen } from '../../api/analy.js'
export default {
    mixins: [modalMixins],
    data () {
        return {
            existData: [],
            addSign: {}
        }
    },
    methods: {
        delUsers (index, userIndex) {
            this.form.nextNode[index].extObj.splice(userIndex, 1)
        },
        selectUsers (index) {
            this.form.nodeIndex = index
            let users = this.form.nextNode[index].extObj
            this.existData = []
            this.existData = users
            this.showUserSelectModal({ selectModel: 'multiple' })
        },
        fieldSelectOk (data) {
            this.existData = data
            this.form.nextNode[this.form.nodeIndex].extObj = data
        }
    },
    created () {
        this.loading = true
        getNextNodeFaWen(this.taskId).then(res => {
            if (res && res.code == 0) {
                this.form.nextNode = res.data
            }
            this.loading = false
        })
    }
}
</script>
