<template>
  <div
    class="page-container"
    :style="{height: pageContentHeight}">
    <div class="edit-page">
      <div
        class="page-content list" 
        v-if="!showQuestionsNewPage && !showQuestionsReplyPage && !showClarifyEditPage"
      >
        <Questions
          @handQuestionsEditPage="handQuestionsEditPage"
          @handleReply="handleReply"
          @handleView="handleView"
          @handleEdit="handleEdit"/>
        <Clarify @handleClarifyViewPage="handleClarifyViewPage"/>
      </div>
      <ClarifyEditPage
        v-if="showClarifyEditPage"
        :current-edit-row="currentEditRow"
        :check="check"
        @hide="hideEditPage"
      ></ClarifyEditPage>
      <!-- 答疑回复、查看页面 -->
      <QuestionsEditPage
        v-if="showQuestionsReplyPage"
        :current-edit-row="currentEditRow"
        :check="check"
        @hide="hideEditPage"
      ></QuestionsEditPage>
      <!-- 答疑提出问题、编辑页面 -->
      <QuestionsNewPage
        v-if="showQuestionsNewPage"
        :in-current-edit-row="currentEditRow"
        :check="check"
        :isEdit="isEdit"
        @hide="hideEditPage"
      ></QuestionsNewPage>
    </div>
  </div>
</template>
<script>
import Clarify from './modules/Clarify.vue'
import Questions from './modules/Questions.vue'
import ClarifyEditPage from './modules/ClarifyEditPage.vue'
import QuestionsEditPage from './modules/QuestionsEditPage.vue'
import QuestionsNewPage from './modules/QuestionsNewPage.vue'
// import TrendVue from '../../../../../components/Trend/Trend.vue'
export default {
    components: {
        Clarify,
        Questions,
        ClarifyEditPage,
        QuestionsEditPage,
        QuestionsNewPage
    },
    computed: {
        pageContentHeight () {
            let height = document.body.clientHeight -70
            return height + 'px'
        }
    },
    data () {
        return {
            showQuestionsReplyPage: false,
            showClarifyEditPage: false,
            showQuestionsNewPage: false,
            currentEditRow: {},
            check: false,
            isEdit: false
        }
    },
    methods: {
        hideEditPage () {
            this.showClarifyEditPage = false
            this.showQuestionsReplyPage = false
            this.showQuestionsNewPage = false
            this.check=false
        },
        handleReply (row) {
            this.currentEditRow = row
            this.showQuestionsReplyPage = true
            this.check=false
        },
        handleView (row){
            this.currentEditRow = row
            this.showQuestionsReplyPage = true
            this.check=true
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showQuestionsNewPage = true
            this.check=false
            this.isEdit=true
        },
        handQuestionsEditPage (row) {
            this.currentEditRow = row
            this.showQuestionsNewPage = true
            this.check=false
            this.isEdit=false
        },
        handleClarifyViewPage (row){
            this.currentEditRow = row
            this.showClarifyEditPage = true
            this.check=true
        }
    }
}
</script>
<style lang="less" scoped>
.ClarifyAndQuestions{
  height: 100%;
}
.page-content{
flex: 1;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    overflow: auto;
    padding: 6px;
    background-color: #fff;
}
</style>


