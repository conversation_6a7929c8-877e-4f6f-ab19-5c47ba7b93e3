<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')"
          required
          prop="newActivityId">
          <a-select
            style="width: 120px"
            v-model="form.newActivityId">
            <a-select-option
              :key="item.id + index"
              :value="item.id"
              v-for="(item, index) in form.nodeArray"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_userDeal`, '处理人')"
          required
          prop="usersInfo">
          <a-tag
            v-if="singleUserData != null && singleUserData.userNo != null"
            size="large"
            color="blue"
            closable
            @close="delSingleUsers"
          >{{ singleUserData.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectSingleShowUsers"></a-button>
          </div>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见')"
          required
          prop="option">
          <a-textarea
            show-word-limit
            v-model="form.option"
            allowClear
            type="textarea"
            autoSize />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_taskName`, '任务标题')"
          prop="taskTitle">
          <a-input
            v-model="form.taskTitle"
            clearable />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_priorityLevel`, '优先级')"
          prop="priority">
          <a-radio-group
            defaultValue="50"
            name="radioGroup"
            v-model="form.priority">
            <a-radio
              v-for="(item, index) in priorityMap"
              :key="index"
              :value="item.value">{{
                item.title
              }}</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { getTransactionUrge, changeActivity } from '../../api/analy.js'
export default {
    mixins: [modalMixins],
    data () {
        return {
            rules: {
                newActivityId: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMYMyC_ae044c1a`, '请填写退回节点') } ],
                taskTitle: [
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                option: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMqdWII_13f544fb`, '请填写备注/意见'), trigger: 'change' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                usersInfo: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFGvL_f32585e1`, '请选择处理人') } ]
            },
            singleUserData: {}
        }
    },
    methods: {
        selectSingleShowUsers () {
            this.showUserSelectModal({ selectModel: 'single' })
        },
        fieldSelectOk (data) {
            this.$set(this.form, 'usersInfo', data[0])
            this.singleUserData = data[0]
        },
        delSingleUsers () {
            this.singleUserData = {}
        },
        handleConfirm () {
            this.cheackValidate().then(() => {
                let {taskId, usersInfo, newActivityId, taskTitle, option, priority} = this.form
                let params = {
                    newActivityId,
                    taskTitle,
                    option,
                    taskId,
                    priority,
                    operate: 'transactionUrge',
                    userId: usersInfo.id,
                    userName: usersInfo.userName
                }
                this.loading = true
                changeActivity(params).then(res => {
                    if (res.code == 200) {
                        this.$emit('success')
                    } else {
                        this.$message.error(res.msg)
                    }
                    this.loading = false
                }).finally(() => {
                    this.loading = false
                    this.$emit('closeLoading')
                })
            })
        }
        
    },
    created () {
        this.loading = true
        getTransactionUrge(this.taskId).then(res => {
            if (res.code == 0) {
                this.$set(this.form, 'nodeArray', res.data)
            }
            this.loading = false
        })
        this.getDictData('taskPriority', 'priorityMap')
    }
}
</script>
