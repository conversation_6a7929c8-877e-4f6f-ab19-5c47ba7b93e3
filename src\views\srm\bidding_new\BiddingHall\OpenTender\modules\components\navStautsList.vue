<template>
  <div class="clearfix navStautsList">
    <div
      v-for="(item, index) in navList"
      :key="index"
      class="navItem">
      <div class="fl">
        <img
          :src="require(`./image/${item.icon}.png`)"
          alt="">
      </div>
      <div class="fl ml-10 text-align-left">
        <div>{{ item.label }}</div>
        <div class="fontColor">{{ formData[item.fieldName] }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
    props: {
        formData: {
            default: () => {
                return {}
            },
            type: Object
        },
        navStautsList: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    computed: {
        navList () {
            return this.navStautsList
        }
    },
    data () {
        return {
            
        }
    }
}
</script>
<style lang="less" scoped>
.fl{
    float: left;
}
.fr{
    float: right;
}
.clearfix {
    clear: both;
}
.ml-10{
    margin-left: 10px;
}
.text-align-left{
    text-align:left;
}
.navStautsList{
    height: 76px;
    padding: 16px 10px;
    .navItem{
        width: 20%;
        .fl;
        .fontColor {
            color: rgba(249, 60, 0, 0.8);
        }
    }
    .navItem:not(:nth-last-child(1)) {
        border-right: 2px solid #e4e4e4;
        margin-right: 10px;
    }
}
</style>