<template>
  <!-- , width: fixedHeader ? `calc(100% - ${sidebarOpened ? 256 : 80}px)` : '100%'  -->
  <a-layout-header
    v-if="!headerBarFixed"
    :class="[ sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed', ]"
    :style="{ padding: '0' }"
  >
    <div
      v-if="mode === 'sidemenu'"
      class="header"
      :class="{'dark':setDarkThemeDefaulColor}"
    >
      <a-icon
        class="trigger"
        :type="collapsed ? 'menu-unfold' : 'menu-fold'"
        @click="toggle"
      />
      <user-menu
        @customerTap="customerTap"
        :theme="theme" />
    </div>
    <!-- 顶部导航栏模式 -->
    <div
      v-else
      :class="['top-nav-header-index', theme]"
    >
      <div class="header-index-wide">
        <div
          class="header-index-left"
          :style="topMenuStyle.headerIndexLeft"
        >
          <logo
            class="top-nav-header"
            :show-title="device !== 'mobile'"
            :style="topMenuStyle.topNavHeader"
          />
          <div
            v-if="device !== 'mobile'"
            :style="topMenuStyle.topSmenuStyle"
          >
            <s-menu
              mode="horizontal"
              :menu="menus"
              :theme="theme"
            />
          </div>
          <a-icon
            v-else
            class="trigger"
            :type="collapsed ? 'menu-fold' : 'menu-unfold'"
            @click.native="toggle"
          />
        </div>
        <user-menu
          class="header-index-right"
          :theme="theme"
          @customerTap="customerTap"
          :style="topMenuStyle.headerIndexRight"
        />
      </div>
    </div>
  </a-layout-header>
</template>

<script>
import UserMenu from '../tools/UserMenu'
import SMenu from '../menu/'
import Logo from '../tools/Logo'
import { Layout } from 'ant-design-vue'
import { mixin } from '@/utils/mixin.js'
import layIM from '@/utils/im/layIM.js'
import { iframeUrl } from '@/utils/im/tools.js'
import { onloadCallback } from '@/utils/util'
import { getAction } from '@/api/manage'

export default {
    name: 'GlobalHeader',
    components: {
        UserMenu,
        SMenu,
        Logo,
        ALayoutHeader: Layout.Header
    },
    mixins: [mixin],
    props: {
        mode: {
            type: String,
            // sidemenu, topmenu
            default: 'sidemenu'
        },
        menus: {
            type: Array,
            required: true
        },
        theme: {
            type: String,
            required: false,
            default: 'dark'
        },
        collapsed: {
            type: Boolean,
            required: false,
            default: false
        },
        device: {
            type: String,
            required: false,
            default: 'desktop'
        }
    },
    data () {
        return {
            headerBarFixed: false,
            token: this.$ls.get('Access-Token'),
            topMenuStyle: {
                headerIndexLeft: {},
                topNavHeader: {},
                headerIndexRight: {},
                topSmenuStyle: {}
            },
            chatContainerStyle: null
        }
    },
    watch: {
        /** 监听设备变化 */
        device () {
            if (this.mode === 'topmenu') {
                this.buildTopMenuStyle()
            }
        },
        /** 监听导航栏模式变化 */
        mode (newVal) {
            if (newVal === 'topmenu') {
                this.buildTopMenuStyle()
            }
        }
    },

    mounted () {
        const that = this
        window.addEventListener('scroll', this.handleScroll)
        if (this.mode === 'topmenu') {
            that.buildTopMenuStyle()
        }
        window.addEventListener('message', function (msg) {
            if(msg.data.type == 'changeSize') {
                that.chatContainerStyle = msg.data.data
            }
        })
        this.$nextTick(() => {
            onloadCallback(() => {
                layIM.init()
            })
        })
        // let userId = this.$ls.get('Login_Userinfo').id
        // if (!userId.includes('1286142373594481851')) { // 100000号作为客服使用,不需要聊天沟通功能
        //     // 初始化聊天插件
        //     layIM.init()
        // }
    },
    methods: {
        // 获取iframe url
        url (info) {
            const obj = {
                token: this.$ls.get('Access-Token'),
                id: this.$ls.get('Login_Userinfo').id
                // wsUrl: 'ws://localhost:9326/els/imChat', // 本地调试
                // baseUrl: 'http://localhost:11888/els/im' // 本地调试
            }
            if (info) {
                obj.customerName =  info.realname || ''
                obj.customerAvatar = info.avatar || ''
                obj.customerID = info.id || ''
            }
            const url = iframeUrl(obj, '/user')
            return url
        },
        customerTap () {
            this.$nextTick(async () => {
                let $box = document.getElementById('layerCustomerChat')
                if ($box) {
                    $box.parentNode.style.display = 'block'
                } else {
                    let customerUserInfo = await this.getCustomerUser()
                    console.log(customerUserInfo)
                    const url = this.url(customerUserInfo)
                    window.layer.open({
                        type: 2,
                        title: [' ', 'background-color:#1890ff'],
                        offset: 'auto',
                        id: 'layerCustomerChat',
                        content: [url, 'no'],
                        area: ['600px', '563px'],
                        closeBtn: 1,
                        resize: false,
                        shade: 0, //不显示遮罩
                        cancel: function (){ 
                            document.getElementById('layerCustomerChat').parentNode.style.display = 'none'
                            return false
                        },
                        success: function (layero){
                            let ifr = layero.find('#layerCustomerChat').children('iframe')
                            let id = ifr[0] && ifr[0].id
                            var frame = document.getElementById(id)
                            let obj = {
                                page: 'userChatPage',
                                type: 'addClass',
                                data: 'user-chat-page'
                            }
                            frame.contentWindow.postMessage(obj, '*')
                        }
                    })
                }
            })
        },
        getCustomerUser () {
            return new Promise (function (resolve, reject) {
                let param = {
                }
                getAction('/account/elsSubAccount/getCustomerUser', param).then(res=>{
                    if(res.success && res.result){
                        resolve(res.result)
                    } else {
                        reject()
                    }
                })
            })
        },
        handleScroll () {
            if (this.autoHideHeader) {
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
                if (scrollTop > 100) {
                    this.headerBarFixed = true
                } else {
                    this.headerBarFixed = false
                }
            } else {
                this.headerBarFixed = false
            }
        },
        toggle () {
            this.$emit('toggle')
        },

        buildTopMenuStyle () {
            if (this.mode === 'topmenu') {
                if (this.device === 'mobile') {
                    // 手机端需要清空样式，否则显示会错乱
                    this.topMenuStyle.topNavHeader = {}
                    this.topMenuStyle.topSmenuStyle = {}
                    this.topMenuStyle.headerIndexRight = {}
                    this.topMenuStyle.headerIndexLeft = {}
                } else {
                    let rightWidth = '464px'
                    this.topMenuStyle.topNavHeader = { 'min-width': '165px' }
                    this.topMenuStyle.topSmenuStyle = { 'width': 'calc(100% - 170px)' }
                    this.topMenuStyle.headerIndexRight = { 'min-width': rightWidth }
                    this.topMenuStyle.headerIndexLeft = { 'width': `calc(100% - ${rightWidth})` }
                }
            }
        }

    }
}
</script>

<style lang="scss" scoped>


  $height: 48px;

  .layout {

    .top-nav-header-index {

      .header-index-wide {
        margin-left: 10px;

        .ant-menu.ant-menu-horizontal {
          height: $height;
          line-height: $height;
        }
      }
      .trigger {
        line-height: 64px;
        &:hover {
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }

    .header {
      z-index: 2;
      color: white;
      height: $height;
      background-color: #1890ff;
      transition: background 300ms;
      justify-content: space-between;
      display: flex;

      /* dark 样式 */
      &.dark {
        color: #000000;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
        background-color: white!important;
      }
    }

    .header, .top-nav-header-index {
      &.dark .trigger:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .ant-layout-header {
    height: $height;
    line-height: $height;
    background: #213859;
  }

  .els-chat-online {
    display: block;
    position: fixed;
    top: 50%;
    right: 50%;
    z-index: 9999;
    width: 600px;
    height: 520px;
    margin-top: -300px;
    margin-right: -300px;
    border-radius: 2px;
    box-shadow: 1px 1px 50px rgba(0,0,0,.4);
    .els-chat-topbar{
      width: 100%;
      height: 80px;
      z-index: 7;
      background: transparent;
      position: absolute;
      left: 0;
    }
  }
  .els-chat-online-close{
    font-size: 16px;
    padding: 10px;
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    z-index: 8;
  }
</style>