import { imUrl } from './tools'
const { BASE_URL } = imUrl()
import { srmI18n, getLangAccount } from '../util'
//重命名好友分组面板
const popModify = (odata, layim) => {
    console.log(popModify.index)
    window.layer.close(popModify.index)
    // 初始化输入框
    return popModify.index = window.layer.prompt({
        formType: 0,
        value: odata.curName,
        title: srmI18n(`${getLangAccount()}#i18n_title_pleaseEnterVaules`, '请输入值'),
        shade: false
        , resize: false
        , btn: [srmI18n(`${getLangAccount()}#i18n_field_crqd_25dcb10c`, '修改备注'), srmI18n(`${getLangAccount()}#i18n_field_Rl_a72da`, '关闭')]
    }, function (value, index, elem) {
        console.log(value) //得到value
        odata.curName = value
        let url = `${BASE_URL}/userGroup/rename`
        let token = localStorage.getItem('t_token')
        let params = {
            groupName: odata.curName,
            groupId: odata.id
        }
        window.layui.$.ajax({
            headers: {
                'X-Access-Token': token
            },
            url,
            type: 'post',
            data: params,
            success: (res) => {
                if (res.success) {
                    renameContextMenu(odata, layim)
                } else {
                    window.layer.msg(res.message)
                }
                window.layer.close(index)
            }
        })
    })
}

//好友或者好友分组重命名
const renameContextMenu = function (data, layim) {
    let layimMain = layim.getLayimMain()
    let listElem = layimMain.find('.layim-list-friend')
    //好友分组重命名
    // 更新缓存
    layim.cache().friend[data.index].groupname = data.curName
    // 更新dom
    var h5span = listElem.find('>li').eq(data.index).find('>h5').eq(0).find('span').eq(0)
    h5span.html(data.curName)
}

// 分组模板
let GroupAddTpl = [
    '<li data-groupid="{{d.id}}">',
    `<h5 data-groupid="{{d.id}}" data-groupname="{{d.groupname}}" layim-event="spread" lay-type="{{ d.spread }}"><i class="layui-icon">{{# if(d.spread === "true"){ }}&#xe61a;{{# } else {  }}&#xe602;{{# } }}</i><span>{{ d.groupname||${srmI18n(`${getLangAccount()}#i18n_field_LRRzV_d572b2d8`, '未命名分组')} }}</span><em>(<cite class="layim-count"> {{ (d.list||[]).length }}</cite>)</em></h5>`,
    '<ul class="layui-layim-list {{# if(d.spread === "true"){ }}',
    ' layui-show',
    '{{# } }}">',
    `<li class="layim-null">${srmI18n(`${getLangAccount()}#i18n_field_rzVIPSyj_f9256454`, '该分组下暂无好友')}</li>`,
    '</ul>',
    '</li>'
].join('')
//新增分组
let friendGroupAdd = function (layim) {
    let url = `${BASE_URL}/userGroup/add`
    let token = localStorage.getItem('t_token')
    const cache = layim.cache()
    let params = {
        groupName: srmI18n(`${getLangAccount()}#i18n_field_LRR_18dd3da`, '未命名') + cache.friend.length
    }
    window.layui.$.ajax({
        headers: {
            'X-Access-Token': token
        },
        url,
        type: 'post',
        data: params,
        success: (res) => {
            if (res.success) {
                creatGroupFn(layim, res.result.id)
            } else {
                window.layer.msg(res.message)
            }
        }
    })
}
const creatGroupFn = function (layim, id = '666') {
    let layimMain = layim.getLayimMain()
    let listElem = layimMain.find('.layim-list-friend')
    let cache = layim.cache()
    console.log(cache)
    if (cache['friend']) {
        let friendGroup = window.layui.laytpl(GroupAddTpl).render({
            'groupname': srmI18n(`${getLangAccount()}#i18n_field_LRR_18dd3da`, '未命名') + cache.friend.length
            , 'index': cache.friend.length
            , 'id': id
            , 'spread': false
            , 'list': []
        })
        listElem.append(friendGroup)
        const cg = {
            groupname: srmI18n(`${getLangAccount()}#i18n_field_LRR_18dd3da`, '未命名') + cache.friend.length,
            id,
            list: []
        }
        cache.friend.push(cg)
    }
}

// 删除分组
const friendGroupRemoveClear = function (data, layim) {
    let url = `${BASE_URL}/userGroup/delete`
    let token = localStorage.getItem('t_token')
    let params = {
        groupId: data.id
    }
    window.layui.$.ajax({
        headers: {
            'X-Access-Token': token
        },
        url,
        type: 'post',
        data: params,
        success: (res) => {
            if (res.success) {
                friendGroupRemoveClearFn(data, layim)
            } else {
                window.layer.msg(res.message)
            }
        }
    })
}
const friendGroupRemoveClearFn = function (data, layim) {
    let layimMain = layim.getLayimMain()
    let cache = layim.cache()
    let listElem = layimMain.find('.layim-list-friend')
    // 删除后默认到我的好友分组
    let checkIdx = cache.friend.findIndex(x => x.id == '88ff196412696751de40c690cad7a438')
    let defaultGroupIdx = checkIdx != -1 ? checkIdx : 0

    let lir = listElem.find('>li').eq(defaultGroupIdx)
    let licurrent = listElem.find('>li').eq(data.index)
    if (cache['friend']) {
        if (cache.friend[defaultGroupIdx].list.length === 0) {
            lir.find('.layui-layim-list').html('')
        }
        let currentList = cache.friend[data.index]
        window.layui.each(currentList.list, function (index, item) {
            let obj = {}
            // 删除后好友默认放在第一个分组中
            cache.friend[defaultGroupIdx].list.push(item)
            item.groupIndex = defaultGroupIdx
            item.groupid = cache.friend[defaultGroupIdx].id
            obj[cache.friend[defaultGroupIdx].list.length] = item
            let list = window.layui.laytpl(listTpl({
                type: 'friend'
                , item: 'd.data'
                , index: 'data.index'
            })).render({ data: obj })
            lir.find('.layui-layim-list').append(list)
        })
        if (cache.friend[defaultGroupIdx].list.length === 0) {
            lir.find('.layui-layim-list').html(`<li class="layim-null">${srmI18n(`${getLangAccount()}#i18n_field_rzVIISyjr_b2de3422`, '该分组下已无好友了')}</li>`)
        }
        lir.find('.layim-count').html(cache.friend[defaultGroupIdx].list.length) //刷新好友数量
        //移除数组
        cache.friend.splice(data.index, 1)
        licurrent.remove()
    }
}
// 主模板
// 当前 DOM 节点存放明细数据在 data-info 上
var listTpl = function (options) {
    var nodata = {
        friend: srmI18n(`${getLangAccount()}#i18n_field_rzVIISyjr_b2de3422`, '该分组下已无好友了'),
        group: srmI18n(`${getLangAccount()}#i18n_field_PSaV_302753be`, '暂无群组'),
        history: srmI18n(`${getLangAccount()}#i18n_field_PSvKME_aefcc6cd`, '暂无历史会话')
    }
    options = options || {}
    options.item = options.item || 'd.' + options.type
    // 针对friend 修改源码
    return [
        '{{# var length = 0, info; layui.each(' + options.item + ', function(i, data) { length++; info = encodeURIComponent(JSON.stringify(data)); }}',
        '<li layim-event="chat" data-info={{ info }} data-type="' + options.type + '" data-index="{{ ' + (options.index || 'i') + ' }}" class="layim-' +
        (options.type === 'history' ? '{{i}}' : options.type + '{{data.id}}') +
        ' {{ data.status === "offline" ? "layim-list-gray" : "" }}">',
        '<img src="{{ data.avatar }}"><span>{{ data.username||data.groupname||data.name||"佚名" }}</span><p>{{ data.remark||data.sign||"" }}</p><span class="layim-msg-status">new</span></li>',
        '{{# }); if(length === 0){ }}',
        '<li class="layim-null">' + (nodata[options.type] || srmI18n(`${getLangAccount()}#i18n_field_PSaV_302753be`, '暂无群组')) + '</li>',
        '{{# } }}'
    ].join('')
}

const moveToGroup = function (data, layim) {
    let url = `${BASE_URL}/userGroup/moveFriendToGroup`
    let token = localStorage.getItem('t_token')
    let params = {
        groupId: data.toGroupId,
        friendId: data.curFirendId
    }
    window.layui.$.ajax({
        headers: {
            'X-Access-Token': token
        },
        url,
        type: 'post',
        data: params,
        success: (res) => {
            if (res.success) {
                moveToGroupFn(data, layim)
            } else {
                window.layer.msg(res.message)
            }
        }
    })
}

// 好友移动到另外一个分组
const moveToGroupFn = function (data, layim) {
    let curFirendIdx = null // 当前移动好友index
    let curFirendGroupIdx = null // 当前移动好友组index
    let toFirendGroupIdx = null // 对应组index
    let layimMain = layim.getLayimMain()
    let cache = layim.cache()

    const listElem = layimMain.find('.layim-list-friend')
    console.log(data.curlistInfo)
    console.log(cache)
    // 缓存操作
    // 新分组导入
    toFirendGroupIdx = cache.friend.findIndex(x => x.id == data.toGroupId)
    data.curlistInfo.groupId = data.toGroupId //更新转移人的群组id
    cache.friend[toFirendGroupIdx].list.push(data.curlistInfo)
    // 原分组去除
    curFirendGroupIdx = cache.friend.findIndex(s => s.id == data.curGroupId)
    if (curFirendGroupIdx == -1) { return false }
    curFirendIdx = cache.friend[curFirendGroupIdx].list.findIndex(s => s.id == data.curlistInfo.id)
    cache.friend[curFirendGroupIdx].list.splice(curFirendIdx, 1)

    // dom操作
    //删除当前好友操作
    var li = listElem.find('>li').eq(curFirendGroupIdx)
    li.find('.layui-layim-list>li').eq(data.curFirendIndex).remove()
    li.find('.layim-count').html(cache.friend[curFirendGroupIdx].list.length) //刷新好友数量  
    if (cache.friend[curFirendGroupIdx].list.length === 0) {
        li.find('.layui-layim-list').html(`<li class="layim-null">${srmI18n(`${getLangAccount()}#i18n_field_rzVIISyjr_b2de3422`, '该分组下已无好友了')}</li>`)
    }
    let newInfo = data.curlistInfo
    newInfo.index = toFirendGroupIdx
    let insertObj = {
        data: newInfo
    }
    // 新增对应组好友
    let flist = window.layui.laytpl(listTpl({
        type: 'friend'
        , item: 'd.data'
        , index: 'd.index' // 组index，需要阅读源码研究
    })).render({ data: insertObj, index: toFirendGroupIdx })
    var lir = listElem.find('>li').eq(toFirendGroupIdx)
    if (cache.friend[toFirendGroupIdx].list.length === 1) {
        lir.find('.layui-layim-list').html('')
    }
    lir.find('.layui-layim-list').append(flist)
    lir.find('.layim-count').html(cache.friend[toFirendGroupIdx].list.length) //刷新好友数量
    console.log(cache)
}

export { popModify, friendGroupAdd, friendGroupRemoveClear, moveToGroup }