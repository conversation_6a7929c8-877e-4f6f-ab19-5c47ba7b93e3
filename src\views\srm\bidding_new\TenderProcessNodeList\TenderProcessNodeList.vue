<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage "
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <Tender-Process-Node-edit
      v-if="showEditPage"
      ref="editlPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <Tender-Process-Node-Detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import TenderProcessNodeEdit from './modules/TenderProcessNodeEdit'
import TenderProcessNodeDetail from './modules/TenderProcessNodeDetail'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        TenderProcessNodeEdit,
        TenderProcessNodeDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'biddingPlatform',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YByO_2e8909cb`, '所属阶段'),
                        fieldName: 'periodType',
                        dictCode: 'tenderPeriodType'
                    }
                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    {
                        allow: ()=> {
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit},
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete}
                ],
                optColumnWidth: 270
            }, 
            url: {
                list: '/tender/tenderProcessNode/list',
                delete: '/tender/tenderProcessNode/delete',
                columns: 'TenderProcessNode'
            }
        }
    },
    methods: {
        allowEdit (row){
            return false
        },
        handleAdd (){
            this.currentEditRow = {
                templateNumber: 'TC2022032819',
                templateName: 'tenderProcessNode',
                templateVersion: '1',
                templateAccount: '100000',
                elsAccount: this.$ls.get(USER_ELS_ACCOUNT)
            }
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleEdit (row) {
            this.currentEditRow = {
                templateNumber: 'TC2022032819',
                templateName: 'tenderProcessNode',
                templateVersion: '1',
                templateAccount: '100000',
                elsAccount: this.$ls.get(USER_ELS_ACCOUNT),
                ...row
            }
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleView (row) {
            this.currentEditRow = {
                templateNumber: 'TC2022032819',
                templateName: 'tenderProcessNode',
                templateVersion: '1',
                templateAccount: '100000',
                elsAccount: this.$ls.get(USER_ELS_ACCOUNT),
                ...row
            }
            this.showDetailPage = true
            this.$store.dispatch('SetTabConfirm', true)
        } 
    }
}
</script>