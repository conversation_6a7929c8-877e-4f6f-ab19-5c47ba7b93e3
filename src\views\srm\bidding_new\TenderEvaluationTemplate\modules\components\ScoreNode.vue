<template>
  <div>
    <vxe-grid
      v-bind="gridConfig"
      :height="250"
      ref="table"
      :data="tableData"
      :edit-rules="editRules"
      :columns="tableColumns"
      show-overflow="title" />
  </div>
</template>
<script lang="jsx">
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { add } from '@/utils/mathFloat.js'
export default {
    props: {
        tenderEvaluationTemplateRegulationInfoList: {
            type: Array,
            default: () => {
                return []
            }
        },
        pageStatus: {
            type: String,
            default: 'edit'
        },
        dealScore: {
            type: [String, Number],
            default: '0'
        },
        currentItem: {
            type: Object,
            default: () => {
                return {}
            }
        },
        itemIndex: {
            type: [String, Number],
            default: '0'
        }
    },
    mixins: [tableMixins],
    computed: {
        tableColumns () {
            let columns =
                this.pageStatus == 'edit'
                    ? [
                        {
                            type: 'seq',
                            width: 50,
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TARL_303d20d9`, '条列名称'),
                            field: 'regulationName',
                            editRender: { enabled: true, name: '$input' }
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TvMW_30358fcb`, '条例描述'),
                            field: 'regulationDesc',
                            editRender: { enabled: true, name: '$input' }
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRvL_26868dc7`, '分值范围'),
                            field: 'scoreRange',
                            editRender: { enabled: true, name: '$input' }
                            // editRender: {enabled: true},
                            // slots: {
                            //     default: ({row}) => {
                            //         return [
                            //             <span>{row.scoreRange}</span>
                            //         ]
                            //     },
                            //     edit: ({row}) => {
                            //         return [
                            //             <a-input vModel={row.scoreRange} onChange={() => this.scoreRangeChange({row})} />
                            //         ]
                            //     }
                            // }
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNKQqRd_256de619`, '请输入是否客观项'),
                            field: 'objective',
                            editRender: {
                                enabled: true,
                                name: '$select',
                                options: [
                                    { value: '0', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Q_5426`, '否') },
                                    { value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_K_662f`, '是') }
                                ]
                            }
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                            field: 'title',
                            width: 100,
                            slots: {
                                default: ({ row }) => {
                                    return [<a-button onClick={() => this.handleDelete(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除')}</a-button>]
                                }
                            }
                        }
                    ]
                    : [
                        {
                            type: 'seq',
                            width: 50,
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TARL_303d20d9`, '条列名称'),
                            field: 'regulationName'
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TvMW_30358fcb`, '条例描述'),
                            field: 'regulationDesc'
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRvL_26868dc7`, '分值范围'),
                            field: 'scoreRange'
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNKQqRd_256de619`, '请输入是否客观项'),
                            field: 'objective_dictText'
                        }
                    ]
            return columns
        }
    },
    data () {
        let checkScoreRange = ({ cellValue, rule, rules, row }) => {
            if (cellValue === '') {
                return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNzRvL_adae4fb0`, '请输入分值范围'))
            } else {
                let strList = cellValue.split(',')
                for (let i = 0; i < strList.length; i++) {
                    if (strList[i].indexOf('~') !== -1) {
                        if (!/^\d+([.][0-9]{0,8})?~{0,1}\d+([.][0-9]{0,8})?$/g.test(strList[i])) {
                            return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mKL1589S1357LdQXW8L_bbaf0942`, '格式为1~5,8~9或1,3,5,7;~为英文;小数8位'))
                            // return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mKLWWWWWWWSWWWWWWW_17d64cbe`, '格式为1~5,8~9或1,3,5,7'))
                        }
                    } else {
                        if (!/^\d+([.][0-9]{0,8})?$/g.test(strList[i])) {
                            return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mKL1589S1357LdQXW8L_bbaf0942`, '格式为1~5,8~9或1,3,5,7;~为英文;小数8位'))
                            // return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mKLWWWWWWWSWWWWWWW_17d64cbe`, '格式为1~5,8~9或1,3,5,7'))
                        }
                    }
                }
            }
        }
        return {
            tableData: [],
            editRules: {
                regulationName: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNTARL_b764e2c2`, '请输入条列名称') },
                    { max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')}
                ],
                regulationDesc: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNTvMW_b75d51b4`, '请输入条例描述') },
                    { max: 1000, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow1000`, '内容长度不能超过1000个字符')}
                ],
                scoreRange: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNzRvL_adae4fb0`, '请输入分值范围') },
                    { validator: checkScoreRange, trigger: 'change' }
                ],
                objective: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNKQqRd_256de619`, '请输入是否客观项') }]
            }
        }
    },
    methods: {
        addItem (data) {
            // Array.isArray(data) ? this.$refs.table.insertAt(...data) : this.$refs.table.insertAt(data)
            this.$refs.table.insertAt(data, -1)
        },
        handleDelete (row) {
            this.$refs.table.remove(row)
        },
        getValidatePromise () {
            let p = new Promise((resolve, reject) => {
                this.$refs.table.validate(true).then(res => {
                    let fullData = this.getAllData()
                    let total = 0
                    fullData.map(item => {
                        // scoreRange 格式为1~5,8~9或1,3,5,7;~为英文
                        let scoreRangeList = item.scoreRange.split(',')
                        let lastScoreRange = 0
                        // 只有一个分值范围
                        if (scoreRangeList.length == 1) {
                            // 格式带不带~
                            if (scoreRangeList[0].indexOf('~') !== -1) {
                                lastScoreRange = scoreRangeList[0].split('~')[1]
                            } else {
                                lastScoreRange = scoreRangeList[0]
                            }
                        } else {
                            // 格式带不带~
                            if (scoreRangeList[scoreRangeList.length-1].indexOf('~') !== -1) {
                                lastScoreRange = scoreRangeList[scoreRangeList.length-1].split('~')[1]
                            } else {
                                lastScoreRange = scoreRangeList[scoreRangeList.length-1]
                            }
                        }
                        total = add(total, lastScoreRange)
                    })
                    if (this.dealScore !== total) {
                        this.$message.error(`第${add(this.itemIndex, 1)}个环节[${this.currentItem.groupName}]:下条例分值合计不等于该环节总分`)
                        reject(`第${add(this.itemIndex, 1)}个环节[${this.currentItem.groupName}]:下条例分值合计不等于该环节总分`)
                    } else {
                        resolve(true)
                    }
                })
            })
            return p
        },
        getAllData () {
            let { fullData } = this.$refs.table.getTableData()
            return fullData
        },
        scoreRangeChange (row) {
            console.log(row)
        }
    },
    mounted () {
        this.$refs.table.loadData(this.tenderEvaluationTemplateRegulationInfoList)
    }
}
</script>
