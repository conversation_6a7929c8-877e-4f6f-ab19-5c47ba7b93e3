import { getAction, postAction } from '@api/manage'
const formDesigner = {
    state: {
        formData: [],
        formDataHeader: {},
        questionType: 0, // 0 默认普通问卷， 1 试卷问卷
        activeKey: '',
        patterns: [{
            desc: 'test',
            message: '大写',
            name: 'upper',
            regexp: /^[A-Z]+$/
        }],
        renderData: []
    },
    mutations: {
        setFormData (state, newData) {
            state.formData = newData
        },
        setFormHeader (state, newData) {
            state.formDataHeader = newData
        },
        setQuestionType (state, newData) {
            state.questionType = newData
        },
        setActiveKey (state, key) {
            state.activeKey = key
        },
        setPatterns (state, patterns) {
            state.patterns = patterns
        },
        setRenderData (state, data) {
            state.renderData = data
        }
    },
    actions: {
        updateRenderData ({ commit }, data) {
            commit('setRenderData', data)
        },
        updateFormHd ({ commit }, data) {
            commit('setFormHeader', data)
        },
        // 问卷初始化
        questionDataInit ({ commit }, data) {
            return new Promise((resolve, reject) => {
                let params = {
                    id: '1403291563131404289'
                }
                let url = '/survey/purchaseSurveyHead/queryById'
                getAction(url, params).then(rs => {
                    console.log(rs)
                    if (rs.success) {
                        let hd = {
                            surveyName: rs.result.surveyName,
                            surveyDesc: rs.result.surveyDesc
                        }
                        // 设置表头数据 
                        commit('setFormHeader', hd)
                        // 设置表数据 
                        // let option =  rs.result.extendField
                        // commit('setFormData', rs.result.purchaseSurveyLibraryList)
                        // 当前类型
                        // let qTypeArr = ['surveyType', '']
                        // let t = qTypeArr.findIndex(rs => rs == rs.surveyType)
                        // t = t && (t + 1)
                        // console.log(t)
                        // commit('questionType', t)
                    }
                    resolve(rs)
                }).catch(error => {
                    reject(error)
                })
            })
        }
    }
}

export default formDesigner
