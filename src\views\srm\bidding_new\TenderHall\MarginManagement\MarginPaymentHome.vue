<template>
  <div class="margin-payment-home">
    <ContentHeader></ContentHeader>
    <!-- <a-steps
          :current="0"
          size="small">
          <a-step>
            <template slot="title">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_siHJp_a725b18c`, '保证金缴纳') }}
            </template>
          </a-step>
        </a-steps> -->
    <div
      v-if="!inShowEditPage && !otShowEditPage && !inShowDetailPage && !otShowDetailPage"
      class="content">
      <div class="content-title">
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_baseForm`, '基本信息') }}</span>
      </div>
      <div class="content-form">
        <a-form-model
          :label-col="labelCol"
          :wrapper-col="wrapperCol">
          <a-row>
            <a-col :span="8">
              <a-form-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_dIAy_9c95e9e4`, '项目编号：')">
                <a-input
                  v-model="form.tenderProjectNumber"
                  disabled/>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_projectName`, '项目名称：')">
                <a-input
                  v-model="form.tenderProjectName"
                  disabled/>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_zsRL_aae439d8`, '分包名称：')">
                <a-input
                  v-model="form.subpackageName"
                  disabled/>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_tLRL_bba43a5f`, '单位名称：')">
                <a-input
                  v-model="form.supplierName"
                  disabled/>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_siHHfj_f82a59a5`, '保证金金额（元）：')">
                <a-input
                  v-model="form.dueAmount"
                  disabled/>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_JpzE_3bd92210`, '缴纳状态：')">
                <span>{{ form.status_dictText }}</span>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <div class="content-title">
        <span>{{
          $srmI18n(`${$getLangAccount()}#i18n_field_siHJpCKW_1cfab046`, '保证金缴纳方式（')
        }}<i>{{
          $srmI18n(`${$getLangAccount()}#i18n_field_pYBQIPVWiFIsCK_748f872a`, '按招标文件要求，选择一种方式')
        }}</i>{{ $srmI18n(`${$getLangAccount()}#i18n_field_JpW_1edd8ea`, '缴纳）') }}</span>
      </div>
      <div class="content-grid">
        <a-tabs
          default-active-key="1"
          @change="callback">
          <a-tab-pane
            key="1"
            :tab="$srmI18n(`${$getLangAccount()}#i18n_field_sWsxJp_b516bfeb`, '保险保函缴纳')">
            <div class="add">
              <a-button
                type="primary"
                @click="addPayment('2')">{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}
              </a-button>
            </div>
            <div class="grid">
              <ListTable
                ref="listTable"
                :url="url"
                :defaultParams="{subpackageId:this.subId, marginCollectionType: '2'}"
                :statictableColumns="tableColumns2"
                :pageData="pageData2"
                :showTablePage="false">
              </ListTable>
            </div>
          </a-tab-pane>
          <a-tab-pane
            key="2"
            :tab="$srmI18n(`${$getLangAccount()}#i18n_field_AvCKJp_88896d15`, '其他方式缴纳')"
            force-render>
            <div class="add">
              <a-button
                type="primary"
                @click="addPayment('1')">{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}
              </a-button>
            </div>
            <div class="grid">
              <ListTable
                ref="listTable"
                :url="url"
                :defaultParams="{subpackageId:this.subId, marginCollectionType: '1'}"
                :statictableColumns="tableColumns"
                :pageData="pageData"
                :showTablePage="false">
              </ListTable>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <InsuranceGuaranteeEdit
      v-if="inShowEditPage"
      :currentEditRow="currentEditRow"/>
    <InsuranceGuaranteeDetail
      v-if="inShowDetailPage"
      :currentEditRow="currentEditRow"/>
    <OtherMethodsEdit
      v-if="otShowEditPage"
      :currentEditRow="currentEditRow"/>
    <OtherMethodsDetail
      v-if="otShowDetailPage"
      :currentEditRow="currentEditRow"/>
  </div>
</template>

<script lang="jsx">
import ContentHeader from '../../BiddingHall/components/content-header'
import ListTable from '../../BiddingHall/components/listTable'
import InsuranceGuaranteeEdit from './modules/InsuranceGuaranteeEdit'
import OtherMethodsEdit from './modules/OtherMethodsEdit'
import InsuranceGuaranteeDetail from './modules/InsuranceGuaranteeDetail'
import OtherMethodsDetail from './modules/OtherMethodsDetail'
import {getAction} from '@/api/manage'
import {USER_INFO} from '@/store/mutation-types'

export default {
    components: {
        ContentHeader,
        ListTable,
        InsuranceGuaranteeEdit,
        OtherMethodsEdit,
        InsuranceGuaranteeDetail,
        OtherMethodsDetail
    },
    inject: ['tenderCurrentRow', 'subpackageId'],
    data () {
        return {
            labelCol: {span: 9},
            wrapperCol: {span: 12},
            form: {},
            inShowEditPage: false,
            otShowEditPage: false,
            inShowDetailPage: false,
            otShowDetailPage: false,
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBLRL_9daf066b`, '投标人名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVey_32dc59a8`, '汇款账号'),
                    'field': 'payAccount'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVeyDR_ed38c85e`, '汇款账号户名'),
                    'field': 'payName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVeyvDc_b9d6714d`, '汇款账号开户行'),
                    'field': 'bankName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HfWjW_4ff93ac2`, '金额（元）'),
                    'field': 'amount'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JuCK_3bf197bb`, '缴费方式'),
                    'field': 'payType_dictText'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                    'field': '',
                    slots: {
                        default ({row, column}) {
                            const {attachmentDTOList} = row
                            const span = attachmentDTOList.map(item => {
                                return <span>{item.fileName}</span>
                            })
                            return span
                        }
                    }
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 120,
                    slots: {default: 'grid_opration'}
                }
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleDetail
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.showEdit
                    }
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            tableColumns2: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBLRL_9daf066b`, '投标人名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tsL_17cc002`, '担保人'),
                    'field': 'guarantor'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                    'field': 'updateTime'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'),
                    'field': 'updateBy'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sxHfWjW_ef47d502`, '保函金额（元）'),
                    'field': 'amount'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sxBI_25940ef2`, '保函附件'),
                    'field': '',
                    slots: {
                        default ({row, column}) {
                            const {attachmentDTOList} = row
                            const span = attachmentDTOList.map(item => {
                                return <span>{item.fileName}</span>
                            })
                            return span
                        }
                    }
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 120,
                    slots: {default: 'grid_opration'}
                }
            ],
            pageData2: {
                showOptColumn: true,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleDetail2
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit2,
                        allow: this.showEdit
                    }
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            url: {
                list: '/tender/sale/saleTenderProjectMarginHead/queryItem'
            },
            userInfo: {},
            currentEditRow: {}
        }
    },
    computed: {
        subId () {
            return this.subpackageId()
        }
    },
    mounted () {
        this.userInfo = this.$ls.get(USER_INFO)
        console.log(this.$ls.get(USER_INFO))
        this.getHadeInfo()
    },
    methods: {
        showEdit ({status}) {
            return status == 0 ? false : true
        },
        handleEdit (row) {
            this.currentEditRow = row
            this.otShowEditPage = true
        },
        handleDetail (row) {
            this.currentEditRow = row
            this.otShowDetailPage = true
        },
        handleEdit2 (row) {
            this.currentEditRow = row
            this.inShowEditPage = true
        },
        handleDetail2 (row) {
            this.currentEditRow = row
            this.inShowDetailPage = true
        },
        callback () {

        },
        getHadeInfo () {
            const params = {
                subpackageId: this.subpackageId() || '',
                supplierAccount: this.userInfo.elsAccount || ''
            }
            getAction('/tender/sale/saleTenderProjectMarginHead/queryBySubpackageId', params).then(res => {
                if (res.code == 200) {
                    res.result && (() => {
                        this.form = res.result
                    })()
                }
            })
        },
        addPayment (type) {
            if (type == '1') {
                this.otShowEditPage = true
            }
            if (type == '2') {
                this.inShowEditPage = true
            }
        }
    }
}
</script>

<style lang="less" scoped>
.margin-payment-home {
    background-color: #fff;

    .ant-steps {
        padding-left: 20px;
        padding-bottom: 10px;
    }

    .content-title {
        padding: 10px 20px;
        background-color: #ddd;
        margin: 10px;

        i {
            color: red;
        }
    }

    .content-grid {
        margin: 10px;
    }

    .grid {
        margin-top: 10px;
    }
}
</style>