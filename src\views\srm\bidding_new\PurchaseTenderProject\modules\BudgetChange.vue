<template>
  <div
    class="purchaseSupplierCapacityHead"
    v-if="ifReady">
    <a-page-header
      v-if="taskInfo.taskId"
    >
      <template slot="extra">
        <taskBtn
          :currentEditRow="currentEditRow"
          :pageHeaderButtons="publicBtn"
          v-on="$listeners"/>
      </template>
    </a-page-header>
    <a-spin
      :spinning="confirmLoading"
      @mouseover="mouseOver"
      @mouseleave="mouseLeave">
      <titleCrtl>
        {{ $srmI18n(`${$getLangAccount()}#i18n_field_zsVH_268adaad`, '分包信息') }} <span
          style="color:red">{{ titleHint }}</span>

        <template slot="right">
          <a-button
            v-if="(!taskInfo.taskId && projectMemberPermission)"
            @click="batchChange">{{ $srmI18n(`${$getLangAccount()}#i18n_field_zRAH_2ef14712`, '批量变更') }}
          </a-button>

          <a-button
            v-if="(!taskInfo.taskId && !projectMemberPermission)"
            disabled
            @click="batchChange">{{ $srmI18n(`${$getLangAccount()}#i18n_field_zRAH_2ef14712`, '批量变更') }}
          </a-button>
        </template>
      </titleCrtl>
      <list-table
        ref="subpackageListTable"
        :statictableColumns="statictableColumns"
        :pageData="modalPageData"
        setGridHeight="250"
        :pageStatus="pageStatus"
        :fromSourceData="subpackageList"
        :showTablePage="false"></list-table>

      <a-modal
        v-model="auditVisible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
        :okText="okText"
        @ok="handleOk">
        <a-textarea
          v-model="opinion"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-modal>
      <a-modal
        v-drag
        :visible="bugetAuditVisible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_warmPrompt`, '提示')"
        @ok="handleAuditOk"
        @cancel="handleCancel">
        <p>{{
          $srmI18n(`${$getLangAccount()}#i18n_field_AELGRUdAHUzKQRyAH_4fc6c757`, '企业未设置预算变更审批，是否直接变更？')
        }}</p>
      </a-modal>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import flowViewModal from '@comp/flowView/flowView'
import {getAction, postAction} from '@/api/manage'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
import titleCrtl from '../../BiddingHall/components/title-crtl'
import {mapGetters} from 'vuex'
import listTable from '../../BiddingHall/components/listTable'

export default {
    name: 'PurchaseTenderProjectHead',
    components: {
        flowViewModal,
        titleCrtl,
        taskBtn,
        listTable
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        },
        root: {
            type: Object,
            default: () => {
                return {}
            }
        },
        // 页面的状态
        pageStatus: {
            type: String,
            default: () => {
                return 'edit'
            }
        }
    },
    data () {
        return {
            bugetAuditVisible: false,
            ifReady: false,
            publicBtn: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    type: 'primary',
                    click: this.auditPass,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    type: '',
                    click: this.auditReject,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    type: '',
                    click: this.showFlow,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'rollBack',
                    click: this.goBackAudit,
                    showCondition: () => {
                        return true
                    }
                }
            ],
            subpackageTitle: '',
            titleHint: '',
            auditVisible: false,
            currentUrl: '',
            opinion: '',
            okText: '',
            flowView: false,
            flag: false,
            confirmLoading: false,
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            projectMemberPermission: false,
            modalPageData: {
                optColumnList: [
                    // {
                    //     key: 'delete',
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    //     clickFn: this.modalDelete,
                    //     authorityCode: 'tender#purchaseTenderProjectHead:delete',
                    //     allow: this.allowDelete
                    // }
                ]
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.cancelAudit,
                    show: this.cancelButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.auditButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                queryById: '/tender/project/subpackagePriceChange/querySubpackageChangePriceById'
            }
        }
    },
    computed: {
        style () {
            const offset = this.showHeader ? 120 : 66
            return {minHeight: `${this.height - offset}px`}
        },
        ...mapGetters([
            'taskInfo'
        ]),
        statictableColumns () {
            let columns = [
                {
                    type: 'seq',
                    width: 60,
                    fixed: 'left',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                    fieldLabelI18nKey: '',
                    field: 'tenderProjectName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsAo_2690a9aa`, '分包编码'),
                    fieldLabelI18nKey: '',
                    field: 'subpackageNumber',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称'),
                    fieldLabelI18nKey: '',
                    field: 'subpackageName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UdHf_47369bbf`, '预算金额'),
                    fieldLabelI18nKey: '',
                    field: 'subpackageChangeBudget',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    enabled: false
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UAHHf_8cea6e8c`, '预变更金额'),
                    fieldLabelI18nKey: '',
                    field: 'preSubpackageChangeBudget',
                    fieldType: 'number',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    dontCheck: true
                },
                {
                    groupCode: 'memberList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHlR_27b0cfb6`, '变更说明'),
                    fieldLabelI18nKey: '',
                    field: 'changeDesc',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    fieldType: 'input'
                },
                {
                    fixed: 'right',
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    field: 'title',
                    width: 100,
                    // slots: { default: 'grid_opration' }
                    slots: {
                        default: ({row}) => {
                            if (row.auditStatus == '1') {
                                return [
                                    <span
                                        style="color:#d2d2d5">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Uzs_eaef5819`, '审批中...')}</span>
                                ]
                            } else {
                                if (this.projectMemberPermission) {
                                    return [
                                        <div>
                                            <a-button type="link"
                                                onClick={() => this.modalChange(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AH_a8e1c`, '变更')}</a-button>
                                        </div>
                                    ]
                                } else {
                                    return [
                                        <div>
                                            <a-button type="link" disabled
                                                onClick={() => this.modalChange(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AH_a8e1c`, '变更')}</a-button>
                                        </div>
                                    ]
                                }
                            }

                        }
                    }
                }
            ]


            return columns
        }

    },
    methods: {
        async getSubpackageList () {
            let url = '/tender/project/subpackagePriceChange/querySubpackageChangePriceById'
            await getAction(url, {id: this.currentEditRow.id}).then(res => {
                if (res.result) {
                    this.subpackageList = res.result
                    if (this.taskInfo.taskId) {
                        this.subpackageList = this.subpackageList.filter(item => {
                            return item.auditStatus == '1'
                        })
                        this.statictableColumns.splice(this.statictableColumns.length - 1, 1)
                    }
                    this.ifReady = true
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mSzsVH_c130fc8`, '查无分包信息'))
                }
            }).finally(() => {
            })
        },
        //校验是否为项目成员 负责人或采购执行人
        checkProjectMemberPermission () {
            let url = '/tender/project/purchaseTenderProjectMember/checkProjectMemberPermission'
            console.log(this.currentEditRow)
            getAction(url, {projectId: this.currentEditRow.id}).then(res => {
                this.projectMemberPermission = res.result
            }).finally(() => {

            })
        },
        mouseOver () {
            if (!this.projectMemberPermission) {
                this.titleHint = '（仅采购执行人与项目组成员可操作）'
            }
        },
        mouseLeave () {
            this.titleHint = ''
        },
        singleChange (row) {
            let url = '/tender/project/subpackagePriceChange/saveSubpackagePriceChange'
            let params = row
            // 校验金额12位整数.6位小数
            var reg = /(^[0-9]{1,12}$)|(^[0-9]{1,12}[\.]{1}[0-9]{1,6}$)/
            if (!reg.test(row.preSubpackageChangeBudget)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UdAHHfHRu12LiWIt6LXW_3c59b269`, '预算变更金额仅支持12位整数以及6位小数'))
                return false
            }
            this.$refs.subpackageListTable.loading = true
            postAction(url, params).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.root.modalBudgetVisible = false
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.subpackageListTable.loading = false
            })
        },
        multiChange () {
            let params = []
            let {fullData} = this.$refs.subpackageListTable.getTableData()
            // 校验金额12位整数.6位小数
            var reg = /(^[0-9]{1,12}$)|(^[0-9]{1,12}[\.]{1}[0-9]{1,6}$)/
            let flag = false
            if (fullData) {
                params = fullData.filter(item => {
                    if (item.preSubpackageChangeBudget && !reg.test(item.preSubpackageChangeBudget)) {
                        flag = true
                    }
                    return item.auditStatus != '1'
                })
            }
            if (flag) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UdAHHfHRu12LiWIt6LXW_3c59b269`, '预算变更金额仅支持12位整数以及6位小数'))
                return
            }

            let url = '/tender/project/subpackagePriceChange/batchSubpackagePriceChange'
            this.$refs.subpackageListTable.loading = true
            postAction(url, params).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.root.modalBudgetVisible = false
                    // this.modalBudgetVisible = false
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.subpackageListTable.loading = false
            })
        },
        // 单个变更
        async modalChange (row) {
            await this.queryIfAudit()
            console.log(this.auditFlag)
            if (!this.auditFlag) {
                this.change = '1'
                this.row = row
                this.bugetAuditVisible = true
            } else {
                this.singleChange(row)
            }

        },
        // 接口查询是否配置了审批（预算变更功能变更时使用）
        async queryIfAudit () {
            await getAction('/tender/project/subpackagePriceChange/checkAuditConfig').then(res => {
                if (res.success) {
                    this.auditFlag = res.result
                }
            }).finally(() => {
            })
        },
        // 批量变更
        async batchChange () {
            await this.queryIfAudit()
            if (!this.auditFlag) {
                this.change = '2'
                this.bugetAuditVisible = true
            } else {
                this.multiChange()
            }
        },
        handleAuditOk () {
            if (this.change == '1') {
                this.singleChange(this.row)
            } else {
                this.multiChange()
            }
            this.bugetAuditVisible = false
        },
        handleCancel () {
            this.bugetAuditVisible = false
        },
        // init () {
        //     this.height = document.documentElement.clientHeight
        //     console.log('this.taskInfo', this.taskInfo)
        //     this.getData()
        // },
        /**
         * 审批方法
         */
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass () {
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject () {
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk () {
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = 'purchaseProduct'
            param['businessId'] = this.currentEditRow.businessId
            param['taskId'] = this.currentEditRow.taskId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            this.confirmLoading = true
            postAction(this.currentUrl, param)
                .then(res => {
                    if (res.success) {
                        this.auditVisible = false
                        this.$message.success(res.message)
                        this.$parent.reloadAuditList()
                        this.goBackAudit()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    mounted () {
        // this.subpackageTitle = this.currentEditRow.subject || ''
        // this.init()
        this.checkProjectMemberPermission()
        this.getSubpackageList()
    }
}
</script>
<style lang="less" scoped>
.container {
    // padding-left: 6px;
    // padding-top: 6px;
    // padding-right: 6px;
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
</style>