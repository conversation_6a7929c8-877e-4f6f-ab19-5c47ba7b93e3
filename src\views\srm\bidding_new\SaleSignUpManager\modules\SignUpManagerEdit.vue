<template>
  <div class="SignUpManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
        
      </business-layout>
      <a-modal
        v-drag     
        v-model="visible" 
        :width="800"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_sRUJtH_20284311`, '报名审查记录')"
        :cancel-text="$srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭')"
      >
        <template slot="footer">
          <a-button @click="() => {this.visible=false}">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
        </template>
        <vxe-grid
          v-bind="gridOptions"></vxe-grid>
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    name: 'SignUpManagerEdit',
    components: {
        BusinessLayout,
        EditGridLayout,
        fieldSelectModal,
        titleTrtl
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        queryData: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            console.log('editrow', this.currentEditRow)
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_SupplierTenderProjectSignUp_${templateNumber}_${templateVersion}`
            // return '100000/sale_SupplierTenderProjectSignUp_TC2022042101_1'
        }
    },
    data () {
        return {
            visible: false,
            gridOptions: {
                border: true,
                resizable: true,
                showOverflow: true,
                height: 300,
                editConfig: {
                    trigger: 'click',
                    mode: 'cell'
                },
                align: 'center',
                toolbarConfig: {
                    enabled: false // 禁用自定义工具
                },
                columns: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), type: 'seq', width: 50},
                    {field: 'createBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJL_164dad6`, '审查人'), width: 150},
                    {field: 'createTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJKI_2b39e622`, '审查时间'), width: 150},
                    {field: 'status_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_reviewStatus`, '审查状态'), width: 140},
                    {field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJII_2b3941f6`, '审查意见')}
                ],
                data: []
            },
            rejectResult: '',
            style () {
                const offset = this.showHeader ? 120 : 66
                return { minHeight: `${this.height - offset}px` }
            },
            pageStatus: 'edit',
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            requestData: {
                detail: { url: '/tender/sale/supplierTenderProjectSignUp/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                attachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'SupplierTenderProjectSignUp', // 必传,
                            disabledItemNumber: true,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectSignUp/edit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save2',
                    click: this.handleSave
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectSignUp/submit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish2',
                    // showMessage: true,
                    click: this.handleSubmit
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJtH_2b3e4109`, '审查记录'),
                    click: this.reviewRecords,
                    key: 'record'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            attachmentListData: {},
            url: {
                submit: '/tender/sale/supplierTenderProjectSignUp/submit',
                add: '/tender/sale/supplierTenderProjectSignUp/add',
                edit: '/tender/sale/supplierTenderProjectSignUp/edit'
            },
            userInfo: {},
            projectObj: {}
        }
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.tenderProjectNumber || this.currentEditRow.id || '',
                actionRoutePath: '采购商与供应商的路径逗号隔开,/bidder/SignUpManagerList'
            }
        },
        reviewRecords () { // 审查记录
            this.visible = true
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            
            this.gridOptions.data = resultData.tenderProjectSignUpRejectList || []
            
            let {consortiumBidding} = resultData
            if(consortiumBidding == '0'){
                pageConfig.groups[0].formModel.combination='0'
            }
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }
            let flag = (consortiumBidding == '0')
            setDisabledByProp('combination', flag)
            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }


                rule[prop] = [{
                    required: !flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            let validateFlag = (consortiumBidding == '0')
            setValidateRuleByProp('combination', validateFlag)

            pageConfig.groups[1]['extend'] = {
                optColumnList: [
                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                    { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                ]
            }
            
            let that = this
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            // 上传 附件需要 headId
            that.externalToolBar['attachmentList'][0].args.headId = resultData.id || ''
            that.externalToolBar['attachmentList'][0].args.itemInfo = itemInfo
        },
        checkItemSelectOk (data) {
            console.log(data)
        },
        preViewEvent (Vue, row){
            row.subpackageId = this.currentEditRow.subpackageId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleSave () {
            // let params = {
            //     ...this.currentEditRow,
            //     ...this.$refs[this.businessRefName].extendAllData().allData
            // }
            const {templateAccount='', templateName='', templateNumber='', templateVersion=1, id=''} = this.currentEditRow
            let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, {templateAccount, templateName, templateNumber, templateVersion, id})
            
            let url = params.id ? this.url.edit : this.url.add
            // params['noticeId'] =  this.queryData.businessId || ''
            this.confirmLoading = true
            postAction(url, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.currentEditRow.id = res.result.id
                    this.externalToolBar['attachmentList'][0].args.headId = res.result.id || ''
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleSubmit () {
            const pageConfig = this.$refs[this.businessRefName].extendAllData()
            // 校验数据
            this.stepValidate(pageConfig).then(res => {
                // let params = {
                //     ...this.currentEditRow,
                //     ...this.$refs[this.businessRefName].extendAllData().allData
                // }
                console.log(this.currentEditRow)
                const {templateAccount='', templateName='', templateNumber='', templateVersion=1, id=''} = this.currentEditRow
                let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, {templateAccount, templateName, templateNumber, templateVersion, id})
            
                // let url = params.id ? this.url.edit : this.url.add
                
                // params['noticeId'] =  this.queryData.businessId || ''
                this.confirmLoading = true
                postAction(this.url.submit, params).then(res => {
                    let type = res.success ? 'success': 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        this.$parent.showEditPage = false
                        this.$store.dispatch('SetTabConfirm', false)
                        this.$parent.searchEvent(false)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
                // postAction(url, params).then(res => {
                //     let type = res.success ? 'success': 'error'
                //     this.$message[type](res.message)
                //     if (res.success) {
                //         params['id'] = res.result.id
                        
                //         // this.currentEditRow.id = res.result.id
                //         // this.externalToolBar['attachmentList'][0].args.headId = res.result.id || ''
                //         // this.getData()
                //     }
                // }).finally(() => {
                //     this.confirmLoading = false
                // })
            }, error => {
                console.log('最后有一个没有填', error)
            })

        },
        async downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        uploadCallBack (result, ref) {
            result.forEach(res => {
                res['fileType'] = null
                res['fileType_dictText'] = null
            })
            let fileGrid = this.getItemGridRef(ref)
            fileGrid.insertAt(result, -1)
        }
    }
}
</script>

<style lang="less" scoped>
.registration-title{
    background-color: #eee;
    padding: 5px 10px;
    margin-bottom: 10px;
}
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
:deep(.page-container .edit-page .page-content){
    flex: auto;
}
</style>


