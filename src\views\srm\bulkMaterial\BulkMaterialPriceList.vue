<template>
  <div
    style="height:100%"
    class="BulkMaterialPriceList">
    <list-layout
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url" />

    <a-modal
      v-drag
      v-model="scanShow"
      :title="$srmI18n(`${$getLangAccount()}#i18n_alert_eMWF_2e1692e1`, '抓取数据')"
      @ok="scanOk">
      <a-form-model
        layout="horizontal"
        v-model="form"
        v-bind="formItemLayout">
        <a-form-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_jingpinDate`, '时间')">
          <a-date-picker
            style="width: 100%"
            format="YYYY-MM-DD"
            v-model="form.time"/>
        </a-form-item>

        <a-form-item>
          <span slot="label">
            {{ $srmI18n(`${$getLangAccount()}#i18n_field_language`, '语言') }}
            <a-tooltip
              :title="'若获取亚洲金属网数据时，该字段不能为空，且目前只有亚洲金属网数据存在中英文，其他网站只存在中文'">
              <a-icon type="question-circle-o" />
            </a-tooltip>
          </span>
          <m-select
            v-model="form.language"
            dict-code="bulkMaterialPriceLanguage" />
        </a-form-item>
        <a-form-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_alert_fkSLwj_62135f6b`, '大宗物料来源')">
          <m-select
            v-model="form.priceSource"
            dict-code="bulkMaterialPriceSourceType" />
        </a-form-item>
      </a-form-model>
    </a-modal>

    <a-modal
      v-drag
      v-model="chartBoxShow"
      :title="chartTitle"
      @ok="handleOk"
      @cancel="handleCancel"
      :maskClosable="false"
      width="1200px">
      <template slot="footer">
        <div style="text-align: center;">
          <a-button
            key="back"
            @click="handleCancel">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
          </a-button>
          <a-button
            @click="handleReset"
            v-if="searchShow">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}
          </a-button>
          <a-button
            :loading="NextLoading"
            type="primary"
            @click="handleNext"
            v-if="searchShow">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_see`, '查看') }}
          </a-button>
        </div>
      </template>
      <div>
        <a-form-model
          :model="chartSearch"
          ref="chartBox"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
          :rules="rules">
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                prop="materialListType"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_nLzA_307e653e`, '材料分类')"
                style="width: 100%;">
                <a-select
                  style="width:100%"
                  show-search
                  option-filter-prop="children"
                  :filter-option="filterOption"
                  mode="default"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_ViF_21f3e37`, '请选择')"
                  :value="chartSearch.materialListType"
                  v-model="chartSearch.materialListType"
                  :allowClear="true">
                  <a-select-option
                    v-for="(option, key) in materialListTypeOpts"
                    :key="'option_' + key"
                    :title="option.materialListType"
                    :value="option.materialListType">
                    {{ option.materialListType }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_zA_a6b35`, '分类')"
                prop="classification"
                style="width: 100%;">
                <a-select
                  style="width:100%"
                  show-search
                  option-filter-prop="children"
                  :filter-option="filterOption"
                  mode="default"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_ViF_21f3e37`, '请选择')"
                  :value="chartSearch.classification"
                  v-model="chartSearch.classification"
                  :allowClear="true">
                  <a-select-option
                    v-for="(option, key) in classificationOpts"
                    :key="'option_' + key"
                    :title="option.classification"
                    :value="option.classification">
                    {{ option.classification }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                prop="categoryName"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_NR_a976c`, '品名')"
                style="width: 100%;">
                <a-select
                  style="width:100%"
                  show-search
                  option-filter-prop="children"
                  :filter-option="filterOption"
                  mode="default"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_ViF_21f3e37`, '请选择')"
                  :value="chartSearch.categoryName"
                  v-model="chartSearch.categoryName"
                  :allowClear="true">
                  <a-select-option
                    v-for="(option, key) in categoryNameOpts"
                    :key="'option_' + key"
                    :title="option.categoryName"
                    :value="option.categoryName">
                    {{ option.categoryName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                prop="materialSpec"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_materialSpec`, '物料规格')"
                style="width: 100%;">
                <a-select
                  style="width:100%"
                  show-search
                  option-filter-prop="children"
                  :filter-option="filterOption"
                  mode="default"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_ViF_21f3e37`, '请选择')"
                  :value="chartSearch.materialSpec"
                  v-model="chartSearch.materialSpec"
                  :allowClear="true">
                  <a-select-option
                    v-for="(option, key) in materialSpecOpts"
                    :key="'option_' + key"
                    :title="option.materialSpec"
                    :value="option.materialSpec">
                    {{ option.materialSpec }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12">
              <a-form-model-item
                prop="makeCompany"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_bAAE_366ea2a1`, '生产企业')">
                <a-select
                  style="width:100%"
                  show-search
                  option-filter-prop="children"
                  :filter-option="filterOption"
                  mode="multiple"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_ViF_21f3e37`, '请选择')"
                  :value="chartSearch.makeCompany"
                  v-model="chartSearch.makeCompany"
                  :allowClear="true">
                  <a-select-option
                    v-for="(option, key) in makeCompanyOpts"
                    :key="'option_' + key"
                    :title="option.makeCompany"
                    :value="option.makeCompany">
                    {{ option.makeCompany }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item
                prop="priceDate"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_umBA_257940bf`, '价格日期')">
                <a-range-picker
                  style="width: 100%;"
                  v-model="chartSearch.priceDate"
                  @change="onChangeTime"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <div>
        <div class="charts">
          <vue-echarts
            style="width: 100%; height: 300px"
            autoresize
            theme="light"
            :option="echartsConfig"
            :auto-resize="true"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script>

import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, postAction} from '@/api/manage'
import { groupBy } from 'lodash'
import moment from 'moment'

export default {
    mixins: [ListMixin],
    components: {
    },
    data () {
        return {
            rules: {
                materialListType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_nLzAxOLV_79969a4e`, '材料分类不能为空')}],
                classification: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zAxOLV_f730ccc5`, '分类不能为空')}],
                categoryName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_NRxOLV_6642057c`, '品名不能为空')}],
                materialSpec: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LmcyxOLV_1ee228d4`, '规格型号不能为空')}],
                priceDate: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BAxOLV_b2cf44ea`, '日期不能为空')}]
                // makeCompany: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_bAAExOLV_68936e31`, '生产企业不能为空')}]
            },
            NextLoading: false,
            categoryNameOpts: [],
            materialListTypeOpts: [],
            classificationOpts: [],
            materialSpecOpts: [],
            makeCompanyOpts: [],
            priceDate: [],
            chartOpts: [],
            chartSearch: {
                priceDate_begin: '',
                priceDate_end: '',
                priceDate: []
            },
            chartBoxShow: false,
            chartTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umMKP_8a418ca5`, '价格趋势图'),
            searchShow: true,
            showEditPage: false,
            scanShow: false,
            form: {
                time: null,
                language: '1',
                priceSource: null
            },
            formItemLayout: {
                labelCol: { span: 6 },
                wrapperCol: { span: 18 }
            },
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    }
                ],
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_eMWF_2e1692e1`, '抓取数据'),
                        icon: 'plus',
                        clickFn: this.handleScan,
                        type: 'primary',
                        authorityCode: 'bulkMaterialPrice#bulkMaterialPrice:scan'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umMKP_8a418ca5`, '价格趋势图'),
                        icon: 'setting',
                        clickFn: this.handleShowChart,
                        type: 'primary',
                        authorityCode: 'bulkMaterialPrice#bulkMaterialPrice:chart'
                    },
                  
                    {authorityCode: 'bulkMaterialPrice#bulkMaterialPrice:getDataByErp', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SMWWWWF_b44eb4e2`, '获取ERP数据'), icon: 'arrow-down', clickFn: this.getDataByErp},
                    {authorityCode: 'bulkMaterialPrice#bulkMaterialPrice:pushDataToErp', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'), icon: 'arrow-up', clickFn: this.pushDataToERP},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                form: {
                    keyWord: ''
                },
                showOptColumn: false
            },
            url: {
                list: '/thirddata/bulkMaterialPrice/list',
                scan: '/thirddata/bulkMaterialPrice/scan',
                scanFromIPass: '/thirddata/bulkMaterialPrice/scanFromIPass',
                columns: 'bulkMaterialPriceList',
                getDataByErpUrl: '/thirddata/bulkMaterialPrice/getDataByErp',
                pushDataToERPUrl: '/thirddata/bulkMaterialPrice/pushDataToErp'
            },
            echartsConfig: {
                title: {
                    text: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umrOP_89c6ef73`, '价格波动图'),
                    textStyle: {
                        fontSize: 16
                    }
                },
                legend: {
                    data: [this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umkK_257df634`, '价格走势')]
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: []
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    // {
                    //     name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_um_9f825`, '价格'),
                    //     data: [],
                    //     type: 'line',
                    //     lineStyle: {
                    //         // color: '#5470C6',
                    //         width: 4,
                    //         type: 'dashed'
                    //     }
                    // }
                ]
            }
        }
    },
    created (){
        this.getSearchOpts()
    },
    methods: {
        getSearchOpts (){
            let params = {
                pageSize: 9999,
                pageNo: 1
            }
            getAction(this.url.list, params).then((res) => {
                if (res.success) {
                    this.chartOpts = res.result.records
                    this.categoryNameOpts = res.result.records
                    this.materialListTypeOpts = res.result.records
                    this.classificationOpts = res.result.records
                    this.materialSpecOpts = res.result.records
                    this.makeCompanyOpts = res.result.records
                    // 品名
                    this.categoryNameOpts = this.handleDuplicateRemoval(this.categoryNameOpts, 'categoryName')
                    // 材料分类
                    this.materialListTypeOpts = this.handleDuplicateRemoval(this.materialListTypeOpts, 'materialListType')
                    // 分类
                    this.classificationOpts = this.handleDuplicateRemoval(this.classificationOpts, 'classification')
                    // 规格型号
                    this.materialSpecOpts = this.handleDuplicateRemoval(this.materialSpecOpts, 'materialSpec')
                    // 工厂 没有
                    this.makeCompanyOpts = this.handleDuplicateRemoval(this.makeCompanyOpts, 'makeCompany')

                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        handleReset (){
            this.chartSearch = {
                priceDate: []
            }
        },
        // 去重
        handleDuplicateRemoval (nowArr, code){
            let obj = {}
            let newArr = []
            newArr = nowArr.reduce((item, next) => {
                obj[next[code]] ? ' ' : obj[next[code]] = true && item.push(next)
                return item 
            }, [])
            return newArr
        },
        handleNext (){
            if (!this.chartSearch.priceDate_begin && !this.chartSearch.priceDate_end) {
                return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFumKI_6cd0bc5a`, '请选择价格时间'))
            }
            this.$refs.chartBox.validate(valid => {
                if (!valid) return
                this.NextLoading = true
                let text = [
                    this.chartSearch.priceDate_begin,
                    '_',
                    this.chartSearch.priceDate_end,
                    ' ',
                    this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umrOP_89c6ef73`, '价格波动图')
                ].join('')
                
                let params = Object.assign({}, this.chartSearch, {
                    pageSize: 9999,
                    pageNo: 1
                })
                delete params.priceDate
                // console.log(params)

                if (params.makeCompany && params.makeCompany.length > 0) {
                    params.makeCompany = params.makeCompany.join(',')
                }
                getAction(this.url.list, params).then((res) => {
                    if (!res.success) {
                        this.$message.warning(res.message)
                        return
                    }
                    let records = res.result.records || []
                    if (!records.length) {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据'))
                        return
                    }

                    this.echartsConfig.title.text = text
                    let priceDates = records.map(n => n.priceDate)
                    priceDates = [ ...new Set(priceDates) ]
                    this.echartsConfig.xAxis.data = priceDates

                    const group = groupBy(records, 'region')
                    console.log('group :>> ', group)

                    let legend = { data: [] }
                    let series = []

                    for (const key of Object.keys(group)) {
                        legend.data.push(key)
                        let obj = {
                            name: key,
                            type: 'line',
                            stack: 'Total',
                            data: []
                        }
                        obj.data = group[key].map(n => ([n.priceDate, n.price]))
                        series.push(obj)
                    }
                    this.echartsConfig.legend = legend
                    this.echartsConfig.series = series
                    // this.chartTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PB_b112a`, '图表')
                    // this.searchShow = false 
                }).finally(() => {
                    this.NextLoading = false
                })
            })
        },
        handleOk (){
            this.chartSearch = {
                priceDate: []
            }
            this.chartBoxShow = false
        },
        handleCancel (){
            this.chartSearch = {
                priceDate: []
            }
            this.chartBoxShow = false
            this.searchShow = true
        },
        filterOption (input, option) {
            return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        },
        handleShowChart (){
            this.chartBoxShow = true
        },
        onChangeTime (date, dateString){
            this.chartSearch.priceDate_begin = dateString[0]
            this.chartSearch.priceDate_end = dateString[1]
        },
        handleScan () {
            this.form.language = '1'
            this.form.time = null
            this.form.priceSource = null
            this.scanShow = true
        },
        scanOk (){
            if(!this.form.priceSource || this.form.priceSource === ''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '大宗物料来源不能为空！'))
                return
            }
            let param = this.form
            param.time = moment(param.time).format('YYYY-MM-DD')
            const that = this
            this.confirmLoading = true
            postAction(that.url.scanFromIPass, param).then(res => {
                that.confirmLoading = false
                if(res.success) {
                    this.scanShow = false
                    that.$message.success(res.message)
                    that.searchEvent()
                }else {
                    that.$message.warning(res.message)
                }   
            })
        }
    }
}
</script>