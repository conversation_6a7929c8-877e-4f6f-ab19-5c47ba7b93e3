<template>
  <div class="PurchaseBarcodeInfoHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="detail"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkSupplierSelectOk"
        ref="SupplierFieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'

export default {
    name: 'PurchaseBarcodeTemplateHeadDetail',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            requestData: {
                detail: {
                    url: '/base/barcode/purchaseBarcodeTemplateHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/purchaseBarcodeTemplateHead/edit',
                templateResolve: '/base/barcode/purchaseBarcodeTemplateHead/templateResolve'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_barcodeTemplate_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hxId_2786675a`, '发布对象'),
                        groupNameI18nKey: 'i18n_field_hxId_2786675a',
                        groupCode: 'supplierList',
                        groupType: 'item',
                        sortOrder: '3'
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'supplierList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'),
                        fieldLabelI18nKey: 'i18n_title_supplierELSAccount',
                        field: 'toElsAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'supplierList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXWWWAo_86dc641`, '供应商ERP编码'),
                        fieldLabelI18nKey: 'i18n_field_RdXWWWAo_86dc641',
                        field: 'supplierCode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'supplierList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                        fieldLabelI18nKey: 'i18n_field_RdXRL_8e11f650',
                        field: 'supplierName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        }
        // goBack () {
        //     this.$parent.hideEditPage()
        // },
    }
}
</script>