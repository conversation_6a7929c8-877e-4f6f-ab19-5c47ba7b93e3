<!--
 * @Author: fzb
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2021-08-02 19:34:43
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\purchase\modules\PurchaseEditCost.vue
-->
<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <business-layout
        ref="containerRef"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
        <template
          v-slot:eightDisciplinesThreeList="{ slotProps }"
        >
          <detail-form-layout
            v-if="threeListSlotData.groupType==='head'"
            :ref="threeListSlotData.groupCode+ 'form'"
            :busAccount="slotProps.busAccount"
            :group="threeListSlotData"
            modelLayout="masterSlave"
            :pageConfig="slotProps.pageConfig"></detail-form-layout>
        </template>
        <template
          v-slot:eightDisciplinesSixList="{ slotProps }"
        >
          <detail-form-layout
            v-if="sixListSlotData.groupType==='head'"
            :ref="sixListSlotData.groupCode+ 'form'"
            :busAccount="slotProps.busAccount"
            :group="sixListSlotData"
            modelLayout="masterSlave"
            :pageConfig="slotProps.pageConfig"></detail-form-layout>
        </template>
        <template
          v-slot:eightDisciplinesTwo="{slotProps}">
          <!-- 上传 -->
          <div style="margin-top: 10px;"></div>
          <detail-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD2ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD2ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :pageConfig="slotProps.pageConfig"
            :group="purchaseAttachmentD2ListSlotData"
            :loadData="purchaseAttachmentD2ListSlotData.loadData"></detail-grid-layout>
         
        </template>
        <template
          v-slot:eightDisciplinesFour="{slotProps}">
          <!-- 上传 -->
          <div style="margin-top: 10px;"></div>
          <detail-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD4ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD4ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :pageConfig="slotProps.pageConfig"
            :group="purchaseAttachmentD4ListSlotData"
            :loadData="purchaseAttachmentD4ListSlotData.loadData"></detail-grid-layout>
         
        </template>
        <template
          v-slot:eightDisciplinesSeven="{slotProps}">
          <!-- 上传 -->
          <div style="margin-top: 10px;"></div>
          <detail-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD7ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD7ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :pageConfig="slotProps.pageConfig"
            :group="purchaseAttachmentD7ListSlotData"
            :loadData="purchaseAttachmentD7ListSlotData.loadData"></detail-grid-layout>
         
        </template>
        <template
          v-slot:eightDisciplinesEight="{slotProps}">
          <!-- 上传 -->
          <div style="margin-top: 10px;"></div>
          <detail-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD8ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD8ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :pageConfig="slotProps.pageConfig"
            :group="purchaseAttachmentD8ListSlotData"
            :loadData="purchaseAttachmentD8ListSlotData.loadData"></detail-grid-layout>
         
        </template>
      </business-layout>
    </a-spin>
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import DetailFormLayout from '@comp/template/business/DetailFormLayout.vue'
import DetailGridLayout from '@comp/template/business/DetailGridLayout.vue'
import {getAction, postAction} from '@/api/manage'

export default {
    name: 'SaleEightDisciplinesHeadDetail',
    components: {
        DetailFormLayout,
        DetailGridLayout,
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            requestData: {
                export: { url: '/base/excelByConfig/downloadTemplate', args: () => {} },
                import: { url: '/base/excelByConfig/importExcel', args: () => {}  },
                detail: { url: '/eightReport/saleEightDisciplinesPoc/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                purchaseEightDisciplinesTeamList: {add: true, delete: true}
            },
            threeListSlotData: {
            },
            sixListSlotData: {
            },
            purchaseAttachmentD2ListSlotData: {},
            purchaseAttachmentD4ListSlotData: {},
            purchaseAttachmentD7ListSlotData: {},
            purchaseAttachmentD8ListSlotData: {},
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                save: '/eightReport/saleEightDisciplinesPoc/edit',
                publish: '/eightReport/saleEightDisciplinesPoc/publish',
                reject: '/eightReport/saleEightDisciplinesPoc/reject'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_eightDisciplines_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleAfterDealSource (pageConfig, resultData){
            let formModel = pageConfig.groups[0].formModel
            pageConfig.groups.forEach((rs, index) => {
                if (rs.groupCode == 'eightDisciplinesThreeListSlot') {
                    rs.show = false
                    rs.verify = true
                    this.threeListSlotData = rs
                }
                if (rs.groupCode == 'eightDisciplinesSixListSlot') {
                    rs.show = false
                    rs.verify = true
                    this.sixListSlotData = rs
                }
                if (rs.groupCode == 'saleAttachmentD2List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD2ListSlotData = rs
                }
                if (rs.groupCode == 'saleAttachmentD4List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD4ListSlotData = rs
                }
                if (rs.groupCode == 'saleAttachmentD7List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD7ListSlotData = rs
                }
                if (rs.groupCode == 'saleAttachmentD8List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD8ListSlotData = rs
                }

            })
            for(let key in resultData){
                formModel[key] = resultData[key]
            }
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory1`, '附件2'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentD2List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentD4List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentD7List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentD8List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_10010011`, 'd3slot'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'eightDisciplinesThreeListSlot',
                        groupType: 'head',
                        show: false,
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_10010011`, 'd6slot'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'eightDisciplinesSixListSlot',
                        groupType: 'head',
                        show: false,
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '拟制',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk17',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'number',
                        fieldLabel: '审核',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk16',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'number',
                        fieldLabel: '批准',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk15',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '拟制',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk14',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    }, {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '审核',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk13',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    }, {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '批准',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk12',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'saleAttachmentList',
                        title: '关联Tab',
                        fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'select',
                        defaultValue: '',
                        dictCode: 'SRMEightAttachmentRelationTab',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'saleAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'saleAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'saleAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'saleAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }, {
                        groupCode: 'saleAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction('/attachment/saleAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }
    }
}
</script>