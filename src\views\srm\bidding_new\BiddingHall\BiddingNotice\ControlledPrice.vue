<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <content-header
        v-if="showHeader"
        :btns="btns"
      />
      <div
        class="container"
        :style="style">
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_baseInfo`, '基本信息') }}</span>
          </titleTrtl>
          <Dataform
            ref="dataform"
            :pageStatus="pageStatus"
            :formData="formData"
            :fields="fields" />
        </div>
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_VRuGR_a2e10ad8`, '控制价设置') }}</span>
          </titleTrtl>
          <list-table
            ref="bidEvaSupplierRecordList"
            :statictableColumns="statictableColumns"
            :mergeRowMethod="mergeRowMethod"
            :setGridHeight="clientHeight"
            :pageStatus="pageStatus"
            :fromSourceData="formData.controlPriceSettingList"
            :showTablePage="false" />
        </div>
      </div>
    </a-spin>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '@views/srm/bidding_new/BiddingHall/components/content-header'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import Dataform from '@views/srm/bidding_new/BiddingHall/components/Dataform.vue'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins.js'
  
export default {
    name: 'DetermineTheWinner',
    components: {
        titleTrtl,
        ContentHeader,
        listTable,
        Dataform
    },
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        pageStatus () {
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                console.log(this.$ls.get('SET_TENDERCURRENTROW').applyRole)
                return 'detail'
            } else {
                if (!this.isChangeStatus) return 'detail'
                if (this.formData.status && this.formData.status !== '0' && this.isChange) return 'detail'
                return 'edit'
            }
        },
        statictableColumns () {
            let columns = [
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    width: 120,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxRL_9dbb44ee`, '投标函名称'),
                    field: 'tenderBidLetterName'
                },
                {
                    width: 120,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_exu_195b61f`, '最高价'),
                    field: 'highestPrice',
                    fieldType: 'number'
                },
                {
                    width: 120,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enu_18c9069`, '最低价'),
                    field: 'lowestPrice',
                    fieldType: 'number'
                }
            ]
            // 分项
            if (this.formData.quoteType == '1') {
                let arr = [
                    {
                        width: 160,
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
                        field: 'materialName'
                    },
                    {
                        width: 160,
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'),
                        field: 'materialNumber'
                    }
                ]
                columns.splice(2, 0, ...arr)
            } else {
                columns.splice(2, 0, {
                    width: 120,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suARL_933891a8`, '报价列名称'),
                    field: 'quoteColumnName'
                })
            }
            return columns
        },
        isChangeStatus () {
            if (this.subPackageRow.status >= 3050 && this.subPackageRow.status <= 3151 ) return true
            return false
        },
        btns () {
            let btn = []
            if (!this.isChangeStatus) return []
            if (this.isChange && this.formData.status && this.formData.status !== '0') {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_change`, '变更'), type: 'primary', click: this.change }
                ]
            } else {
                btn = [
                    // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refresh`, '刷新'), type: 'primary', click: this.getData },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish }
                ]
            }
            if(this.$ls.get('SET_TENDERCURRENTROW').applyRole != '1') btn = []
            console.log(this.$ls.get('SET_TENDERCURRENTROW').applyRole)
            return btn
        }
    },
    mixins: [baseMixins],
    data () {
        return {
            confirmLoading: false,
            show: false,
            formData: {},
            showHeader: true,
            clientHeight: 0,
            isChange: true,
            fields: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectNumber`, '项目编号'),
                    field: 'tenderProjectNumber',
                    disabled: true,
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                    field: 'tenderProjectName',
                    disabled: true,
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称'),
                    field: 'subpackageName',
                    disabled: true,
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suCK_2e0c7ce8`, '报价方式'),
                    disabled: true,
                    field: 'quoteType',
                    fieldType: 'select',
                    dictCode: 'tenderQuoteType'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xUexu_ab52a3a9`, '高于最高价'),
                    field: 'aboveHighPriceStrategy',
                    fieldType: 'select',
                    dictCode: 'controlPriceStrategy',
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nUenu_82ce0769`, '低于最低价'),
                    field: 'belowThePriceStrategy',
                    fieldType: 'select',
                    dictCode: 'controlPriceStrategy',
                    required: '1'
                }
            ],
            needGetNodeParams: false,
            url: {
                queryById: '/tender/price/purchaseTenderControlPriceHead/queryBySubpackageId',
                add: '/tender/price/purchaseTenderControlPriceHead/add',
                edit: '/tender/price/purchaseTenderControlPriceHead/edit',
                publish: '/tender/price/purchaseTenderControlPriceHead/publish'
            }
        }
    },
    
    methods: {
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            // const fields = ['supplierAccount']
            const fields = ['tenderBidLetterName']
            const cellValue = row['tenderBidLetterName']
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow['tenderBidLetterName'] === cellValue) {
                    return { rowspan: 0, colspan: 0 }
                } else {
                    let countRowspan = 1
                    while (nextRow && nextRow['tenderBidLetterName'] === cellValue) {
                        nextRow = visibleData[++countRowspan + _rowIndex]
                    }
                    if (countRowspan > 1) {
                        console.log(row, _rowIndex, column, visibleData, countRowspan)
                        return { rowspan: countRowspan, colspan: 1 }
                    }
                }
            }
        },
        init () {
            this.getData()
        },
        change () {
            this.isChange = false
        },
        getData (ifPublish) {
            let params = {
                subpackageId: this.subId
            }
            this.confirmLoading = true
            getAction(this.url.queryById, params).then(res => {
                if (res.success) {
                    this.formData = res.result || {}
                    if(ifPublish){
                        this.isChange = true
                    }
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        save () {
            let params = Object.assign({}, this.formData)
            let url = params.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            postAction(url, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                // 为了更新版本号
                if (res.success) {
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
                this.resetCurrentSubPackage()
            })
        },
        publish () {
            this.$refs.dataform.getValidatePromise().then(res => {
                if (res) {
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                        onOk: () => {
                            let params = Object.assign({}, this.formData)
                            let flag = false
                            if(params.controlPriceSettingList){
                                params.controlPriceSettingList.forEach(item=>{
                                    if(item.highestPrice<item.lowestPrice){
                                        flag = true
                                    }
                                })
                            }
                            if(flag){
                                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_exuxqnUenu_39949a28`, '最高价不可低于最低价'))
                                return
                            }
                            console.log('params', params)
                            this.confirmLoading = true
                            postAction(this.url.publish, params).then(res => {
                                let type = res.success ? 'success': 'error'
                                this.$message[type](res.message)
                                if (res.success) {
                                    this.getData('ifPublish')
                                    this.resetCurrentSubPackage()
                                }
                            }).finally(() => {
                                this.confirmLoading = false
                            })
                        }
                    })
                }
            })
        }
    },
    created () {
        this.clientHeight = document.documentElement.clientHeight - 360
    },
    mounted () {
        this.init()
    }
}
</script>
  <style lang="less" scoped>
  .container{
      background-color: #fff;
      margin-left: 6px;
      margin-right: 6px;
  }
  </style>
  
  
  
  
  