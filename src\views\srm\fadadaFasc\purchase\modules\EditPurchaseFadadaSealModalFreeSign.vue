<template>
  <div class="PurchaseFadadaSeal business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="edit"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { BUTTON_BACK} from '@/utils/constant.js'
import { postAction } from '@/api/manage'

export default {
    name: 'EditPurchaseFadadaSealModalFreeSign',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/purchaseFadadaSeal/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    title: '获取印章设置免验证签链接',
                    key: 'save',
                    attrs: {
                        type: 'primary'
                    },
                    args: {
                        url: '/electronsign/fadada/purchaseFadadaSeal/freeSign'
                    },
                    click: this.handleCustomPublish,
                    authorityCode: 'fadada#purchaseFadadaSeal:freeSign'
                },
                BUTTON_BACK
            ],
            url: {
                detail: '/electronsign/fadada/purchaseFadadaSeal/queryById',
                freeSign: '/electronsign/fadada/purchaseFadadaSeal/freeSign'
            }
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: '机构',
                        fieldLabelI18nKey: '',
                        fieldName: 'orgName',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: '印章类型',
                        fieldLabelI18nKey: '',
                        dictCode: 'fadadaCategoryType',
                        fieldName: 'categoryType',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: '印章名称',
                        fieldLabelI18nKey: '',
                        fieldName: 'sealName',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: '印章标签',
                        fieldLabelI18nKey: '',
                        fieldName: 'sealTag',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: '法大大机构ID',
                        fieldLabelI18nKey: '',
                        fieldName: 'openCorpId',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: '通知邮箱',
                        fieldLabelI18nKey: '',
                        fieldName: 'email'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: '免验证签授权的有效期',
                        fieldLabelI18nKey: '',
                        fieldName: 'expiresTime',
                        required: '1'     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: '免签场景',
                        fieldLabelI18nKey: '',
                        fieldName: 'sceneCode',
                        dictCode: 'fadadaBusinessId',
                        required: '1',
                        disabled: false
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'image',
                        fieldLabel: '印章',
                        fieldLabelI18nKey: '',
                        fieldName: 'picFileUrl',
                        disabled: true
                    }
                ]
            }
        },
        handleCustomPublish (args){
            // 获取页面所有数据
            const allData = this.getAllData() || {}

            // 默认保存方法不会校验当前业务模板必填字段
            // 此处手动触发校验方法
            this.stepValidate(args).then(() => {
                postAction(this.url.freeSign, allData).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.$emit('handleChidCallback', res.result)
                    }
                })

            })
        }
    }
}
</script>
