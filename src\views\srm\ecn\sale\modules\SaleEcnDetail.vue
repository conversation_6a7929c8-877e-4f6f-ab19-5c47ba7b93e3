<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url"
      @loadSuccess="handleLoadSuccess" />

    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal>   -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import { getAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
export default {
    name: 'SaleEcnDetail',
    mixins: [DetailMixin],
    components: {
        flowViewModal
    },
    data () {
        return {
            showRemote: false,
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_ItRH_40bee5ab`, '订单明细'), groupCode: 'orderDetails', type: 'grid', custom: {
                        ref: 'orderDetails',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEsNo`, '采购ELS号'), width: 120 },
                            { field: 'purchaseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserAccountName`, '采购方名称'), width: 120 },
                            { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 120 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                            { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lineStatus`, '行状态'), width: 120 },
                            { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                            { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 120 },
                            { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 120 },
                            { field: 'receiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantity`, '收货数量'), width: 120 },
                            { field: 'onWayQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_onWayQuantity`, '在途数量'), width: 120 },
                            { field: 'notDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`, '未交货数量'), width: 120 },
                            { field: 'orderCreateBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreator`, '订单创建人'), width: 120 },
                            { field: 'orderCreateTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreatTime`, '订单创建时间'), width: 120 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'attachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/ecn/saleEcn/queryById',
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${account}/sale_ecn_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open  && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        }else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    mounted () {
    },
    methods: {
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id, this.getItemNumberOptions)
            }
        }
    }
}
</script>