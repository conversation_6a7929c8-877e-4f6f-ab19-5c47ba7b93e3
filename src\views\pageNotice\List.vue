<template>
  <div class="PageNoticeList">
    <a-spin :spinning="spinning">
      <a-card :bodyStyle="bodyStyle">
        <div
          slot="title"
          class="title row space">
          <div class="tit">
            <i class="icon msg"></i>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_announcement`, '平台公告') }}</span>
          </div>
          <a-button
            icon="outlined"
            type="link"
            @click="getNoticeData">
            <a-icon
              type="sync"
              theme="outlined">
            </a-icon>
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_refresh`, '刷新') }}
          </a-button>
        </div>

        <div class="list">
          <ul>
            <template v-for="el in publicNotice">
              <li :key="el.id">
                <router-link
                  class="row space item"
                  :to="{ name: `pageNoticeDetail`, query: { id: el.id } }">
                  <span class="lab">{{ el.noticeTitle }}</span>
                  <span class="val">{{ el.publishTime }}</span>
                </router-link>
              </li>
            </template>
          </ul>
        </div>

        <div class="pagination">
          <a-pagination
            :total="total"
            :current="pageNo"
            :show-total="showTotal(total)"
            show-size-changer
            show-quick-jumper
            @showSizeChange="onShowSizeChange"
            @change="onChange"
          />
        </div>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
    data () {
        return {
            spinning: false,
            delayTime: 300,
            publicNotice: [],
            pageNo: 1,
            pageSize: 10,
            total: 0,
            rowCount: 0,
            bodyStyle: {
                padding: '0 24px 24px 24px'
            }
        } 
    },
    methods: {
        showTotal (total){
                   
            let totalLabel=`${ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_commonTotal`, '共') } ${total} ${ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_strip`, '条') }` 
            return totalLabel

        },
        getNoticeData () {
            const url = '/notice/purchaseNotice/getHomePageNotice'
            const params = {
                pageNo: this.pageNo,
                pageSize: this.pageSize
            }
            this.spinning = true
            getAction(url, params).then(res => {
                this.publicNotice = res.result.records || []
                this.total = res.result.total
            }).finally(() => {
                this.spinning = false
            })
        },
        onShowSizeChange (page, pageSize) {
            this.pageSize = pageSize
            this.getNoticeData()
        },
        onChange (pageNo) {
            this.pageNo = pageNo
            this.getNoticeData()
        }
    },
    watch: {
        $route: {
            immediate: true,
            handler ({ path }) {
                if (path !== '/pageNotice/list') return
                this.getNoticeData()
            }
        }
    }
}
</script>

<style lang="less" scoped>
@gray: #8c8c8c;
@blue: #178aff;
@icon: "~@/assets/img/login/icon_1.png";
.ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.PageNoticeList {
	margin: 20px 20px 0 20px;
	font-size: 14px;
	ul {
		list-style: none;
		margin: 0;
		padding: 0;
	}
	.row {
		display: flex;
		align-items: center;
		line-height: 1;
	}
	.space {
		justify-content: space-between;
	}
	.title {
		.tit {
			display: flex;
			align-items: center;
			color: @blue;
			.icon {
				margin-right: 16px;
				width: 28px;
				height: 28px;
				background-repeat: no-repeat;
				background-position: center;
				background-size: 100%;
				&.msg {
					background-image: url(~@/assets/img/login/icon_1.png);
				}
			}
		}
		.sync {
			color: #666;
			span {
				margin-left: 6px;
			}
		}
	}
	.list {
		min-height: 300px;
		font-weight: 300;
		li + li {
			border-top: 1px dotted #e5e5ee;
		}
    li {
      &:hover {
        background: #f5f9ff;
        .item {
          &::before {
            background: #3f9fff;
            border-color: #3f9fff;
          }
        }
      }
    }
		.item {
			position: relative;
			padding: 0 18px 0 30px;
			height: 50px;
			color: #606060;
			&::before {
				position: absolute;
				left: 12px;
				top: 50%;
				border: 1px solid #e5e5e5;
				border-radius: 50%;
				width: 8px;
				height: 8px;
				background: #fff;
				content: "";
				transform: translateY(-50%);
			}
			.lab {
				flex: 1;
				font-weight: 700;

				.ellipsis;
			}
			.val {
				flex: 0 0 180px;
        text-align: right;
			}
		}
	}
	.pagination {
		margin-top: 50px;
		text-align: right;
	}
}
</style>

<style lang="less">
.PageNoticeList {
	.ant-card-head {
		padding: 0 10px 0 24px;
	}
	.ant-card-head-title {
		padding: 12px 0;
	}
}
</style>

