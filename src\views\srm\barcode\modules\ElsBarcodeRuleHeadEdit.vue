<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {
    BUTTON_SAVE,
    BUTTON_PUBLISH,
    BUTTON_SUBMIT,
    BUTTON_BACK,
} from '@/utils/constant.js'
import {getLangAccount, srmI18n} from "@/utils/util";

export default {
    name: 'ElsBarcodeRuleHeadEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            requestData: {
                detail: {
                    url: '/base/barcode/elsBarcodeRuleHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                elsBarcodeRuleItemList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary'},
                    click: this.businessGridAddPopup
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete',
                    click: this.businessGridDelete
                }]
            },
            pageFooterButtons: [
                {
                    ...BUTTON_SAVE,
                    title: this.$srmI18n(`${getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/base/barcode/elsBarcodeRuleHead/edit'
                    }
                },
                {
                    ...BUTTON_SUBMIT,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQDJW_c7b45eb4`, '是否提交?'),
                    args: {
                        url: '/base/barcode/elsBarcodeRuleHead/submit'
                    },
                    handleBefore: this.handlePublishBefore,
                    handleAfter: this.handlePublishAfter
                },
                {
                    ...BUTTON_BACK,
                    title: this.$srmI18n(`${getLangAccount()}#i18n_title_back`, '返回'),
                }
            ]
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_barcodeRule_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        // 处理发布回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            const { elsBarcodeRuleItemList } = args.allData || {}
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!elsBarcodeRuleItemList || !elsBarcodeRuleItemList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cVHxOLV_c6f5290a`, '行信息不能为空！'))
                    reject(args)
                } else {
                    resolve(args)
                }
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            console.log('handleSaveAfter', args)
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        handleShowFn () {
            let rs = true
            if (this.currentEditRow && this.currentEditRow.id) {
                rs = true
            } else {
                rs = false
            }
            return rs
        },
        goBack () {
            this.$parent.hideEditPage()
        },
        //新增行
        businessGridAdd ({ Vue, pageConfig, btn, groupCode }) {
            const { allData = {} } = this.getBusinessExtendData(this.businessRefName)
            console.log('groupCode', groupCode)
            let itemGrid = this.getItemGridRef(groupCode)
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)

            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {

                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            // if (allData.sampleNumber!='' || row.sampleNumber!=''){

            // }
            itemGrid.insertAt([row], -1)
        },
        //新增弹窗
        businessGridAddPopup ({ Vue, pageConfig, btn, groupCode }) {
            this.curGroupCode = groupCode
            this.fieldSelectType = 'material'
            let url = '/base/barcode/elsBarcodeAttribute/enableList'
            let columns = [
                { field: 'businessType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm132_busDocType`, '业务单据类型') },
                { field: 'businessField', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_EStFJO_fa24b7fe`, '业务单据字段') },
                { field: 'businessFieldName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_tFJORL_d790c17a`, '单据字段名称') },
                { field: 'attributeType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WcAc_2b753bb9`, '属性类型') }
            ]
            this.$refs.fieldSelectModal.open(url, {}, columns, 'multiple')
        },
        checkItemSelectOk (data) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            let oldBusinessTypeArr = new Array()
            if(allData.businessType){
                oldBusinessTypeArr = allData.businessType.split(',')
            }
            let itemGrid = this.getItemGridRef('elsBarcodeRuleItemList')
            data.forEach(item => {
                item['ruleNumber'] = allData['ruleNumber']
                item['coverCode'] = '0'
                item['id'] = null
                if (item['businessType_dictText']){
                    oldBusinessTypeArr.push(item['businessType_dictText'])
                }
            })

            this.getBusinessExtendData(this.businessRefName).pageConfig.groups[0].formModel.businessType = Array.from(new Set(oldBusinessTypeArr)).toString()
            itemGrid.insertAt(data, -1)
        },
        businessGridDelete (Vue){debugger
            let itemGrid = this.getItemGridRef(Vue.groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            itemGrid.removeCheckboxRow()
            let businessTypeArr = new Array()
            this.getBusinessExtendData(this.businessRefName).allData.elsBarcodeRuleItemList.forEach( item =>{
                if (item['businessType_dictText']){
                    businessTypeArr.push(item['businessType_dictText'])
                }
            })
            this.getBusinessExtendData(this.businessRefName).pageConfig.groups[0].formModel.businessType = Array.from(new Set(businessTypeArr)).toString()
        }
    }
}
</script>