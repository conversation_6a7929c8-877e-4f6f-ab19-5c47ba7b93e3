<template>
  <div class="els-page-comtainer"> 
    <tabs-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url" 
      @goBack="goBack"/>
    <!-- 行明细弹出选择框 -->
    <select-modal
      ref="pickList"
      :url="url.selectMaterial"
      :columns="selectMaterialColumns"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectOrder`, '选择订单')"
      @ok="selectOk"/>
  </div>
</template>

<script>
import { httpAction } from '@/api/manage'
import selectModal from '@comp/selectModal/selectModal'
import { editPageMixin } from '@comp/template/tabsCollapse/tabsCollapseMixin'

export default {
    name: 'DeliverySaleHeadModal',
    components: {
        selectModal
    },
    mixins: [editPageMixin],
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatNewShipmentDoc`, '新建发货单'),
            confirmLoading: false,
            disabledFlag: false,
            pageData: {
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            form: {
                                deliveryNumber: '',
                                supplierId: '',
                                supplierCode: '',
                                supplierName: '',
                                companyCode: '',
                                companyName: '',
                                factoryCode: '',
                                factoryName: '',
                                deliveryType: '0',
                                deliveryStatus: '0',
                                deliveryDesc: '',
                                deliveryTime: '',
                                planArriveDate: '',
                                receiveTime: '',
                                storageLocationCode: '',
                                storageLocationName: '',
                                deliveryWay: '0',
                                logisticsCompany: '',
                                trackingNumber: '',
                                carNumber: '',
                                driverName: '',
                                driverIdNumber: '',
                                driverPhone: '',
                                deliveryAddress: '',
                                receiveContact: '',
                                receivePhone: '',
                                purchasePrincipal: '',
                                supplierPrincipal: '',
                                purchaseRemark: '',
                                supplierRemark: ''
                            },
                            list: [
                                {
                                    type: 'collapse',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                                    formList: [
                                        {
                                            type: 'input', 
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shipmentNo`, '发货单号'),
                                            fieldName: 'deliveryNumber', 
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_systemGeneration`, '系统生成'),
                                            disabled: true
                                        },
                                        {
                                            type: 'select',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                                            fieldName: 'deliveryStatus',
                                            dictCode: 'isrmDeliveryStatus',
                                            disabled: true
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaDesc`, '单据描述'),
                                            fieldName: 'deliveryDesc',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'date',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryTime`, '发货时间'),
                                            fieldName: 'deliveryTime',
                                            placeholder: '',
                                            disabled: true
                                        },
                                        {
                                            type: 'date',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_planArriveDate`, '计划到货日期'),
                                            fieldName: 'planArriveDate',
                                            placeholder: '',
                                            disabled: false
                                        },
                                        {
                                            type: 'select',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'),
                                            fieldName: 'companyCode',
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectCompanyCodeTips`, '请选择公司代码'),
                                            dictCode: 'isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code="company"'
                                        },
                                        {
                                            type: 'select',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                                            fieldName: 'factoryCode',
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectFactoryTips`, '请选择工厂'),
                                            dictCode: 'isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code="factory"'
                                        },
                                        {
                                            type: 'select',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocation`, '库存地点'),
                                            fieldName: 'storageLocationCode',
                                            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectInventoryLocationlTips`, '请选择库存地点'),
                                            dictCode: 'isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code="storageLocation"'
                                        },
                                        {
                                            type: 'select',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_distributionMode`, '配送方式'),
                                            fieldName: 'deliveryWay',
                                            dictCode: 'isrmDeliveryWay',
                                            disabled: false
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsCompany`, '物流公司'),
                                            fieldName: 'logisticsCompany',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trackingNumber`, '物流单号'),
                                            fieldName: 'trackingNumber',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_carNumber`, '车牌号'),
                                            fieldName: 'carNumber',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverName`, '司机姓名'),
                                            fieldName: 'driverName',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverIdNumber`, '司机身份证号'),
                                            fieldName: 'driverIdNumber',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverPhone`, '司机电话'),
                                            fieldName: 'driverPhone',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryAddress`, '交货地址'),
                                            fieldName: 'deliveryAddress',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receiveContact`, '收货联系人'),
                                            fieldName: 'receiveContact',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系人电话'),
                                            fieldName: 'receivePhone',
                                            placeholder: ''
                                        },
                                        {
                                            type: 'input',
                                            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRemarks`, '供方备注'),
                                            fieldName: 'supplierRemark',
                                            placeholder: ''
                                        }
                                    ]
                                }
                            ],
                            validRules: {
                                supplierName: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFRdX_f2ffa076`, '请选择供应商!') }
                                ],
                                companyCode: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectCompanyCodeTips`, '请选择公司代码!') }
                                ]
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                        content: {
                            type: 'table',
                            ref: 'deliverySaleItemList',
                            columns: [{ 
                                type: 'checkbox', width: 40 
                            },
                            { 
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                                field: 'orderNumber',
                                width: 130
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                field: 'orderItemNumber',
                                width: 80
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                                field: 'factoryCode',
                                width: 80
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                field: 'materialCode',
                                width: 120
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                field: 'materialDesc',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
                                field: 'materialSpec',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                                field: 'materialGroupCode',
                                width: 80
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_demandDate`, '需求日期'),
                                field: 'requireDate',
                                width: 120
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'),
                                field: 'quantity',
                                width: 120
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityUnit`, '数量单位'),
                                field: 'quantityUnit',
                                width: 100
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
                                field: 'taxCode',
                                width: 100
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'),
                                field: 'taxRate',
                                width: 100
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '净价'),
                                field: 'netPrice',
                                width: 100
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'),
                                field: 'price',
                                width: 100
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remainQuantity`, '剩余数量'),
                                field: 'remainQuantity',
                                width: 120
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryQuantity`, '发货数量'),
                                field: 'deliveryQuantity',
                                width: 120,
                                editRender: {name: 'AInputNumber', events: {change: this.validDeliveryQuantity}}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRemarks`, '供方备注'),
                                field: 'supplierRemark',
                                width: 220,
                                editRender: {name: 'AInput'}
                            }],
                            validRules: {
                                deliveryQuantity: [
                                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryQuantityCantBeNone`, '发货数量不能为空') }
                                ]
                            }
                        },
                        button: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), type: 'primary', clickFn: this.addRow},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteRow}
                        ]
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        content:
                        {
                            type: 'upload',
                            ref: 'fileList',
                            relatedIdMap: 'id',
                            relatedType: 'delivery',
                            roleName: 'SU'
                        }
                    }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', clickFn: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', clickFn: this.publish },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack }
                ]
            },
            url: {
                add: '/delivery/deliverySaleHead/add',
                edit: '/delivery/deliverySaleHead/edit',
                publish: '/delivery/deliverySaleHead/publish',
                confirmPlan: '/delivery/deliverySaleHead/confirmDeliveryPlan',
                detail: '/delivery/deliverySaleHead/queryDetailById',
                selectMaterial: '/delivery/deliveryNoticeSaleHead/itemList'
            },
            selectMaterialColumns: [
                { type: 'checkbox', width: 40, align: 'center'},
                { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 130, align: 'center'},
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 80, align: 'center'},
                { field: 'factoryCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_factory`, '工厂'), width: 80, align: 'center'},
                { field: 'factoryName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_factoryName`, '工厂名称'), width: 100, align: 'center'},
                { field: 'materialCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 100, align: 'center'},
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150, align: 'center'},
                { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'), width: 150, align: 'center'},
                { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_demandDate`, '需求日期'), width: 100, align: 'center'},
                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 100, align: 'center'},
                { field: 'remainQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remainQuantity`, '剩余数量'), width: 100, align: 'center'},
                { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'), width: 100, align: 'center'},
                { field: 'netPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '净价'), width: 100, align: 'center'}

            ]
        }
    },
    mounted () {
       
    },
    created (){
      
    },
    methods: {
        goBack () {
            this.$emit('hide')
        },
        addRow (){          
            let companyCode= this.$refs.editPage.getParamsData().companyCode
            this.$refs.pickList.open({'companyCode': companyCode})
        },
        selectOk (data) {
            let addTableData = []
            let asnDetailGrid = this.$refs.editPage.$refs.deliverySaleItemList[0]
            let tableData =  asnDetailGrid.getTableData().fullData
            data.forEach((item, i) => {
                let lineNum = tableData.length + (i + 1)
                addTableData.push({
                    itemNumber: lineNum,
                    orderNumber: item.orderNumber,
                    orderItemNumber: item.itemNumber,
                    orderId: item.orderId,
                    orderItemId: item.orderItemId,
                    quantity: item.quantity,
                    requireDate: item.requireDate,
                    quantityUnit: item.quantityUnit,
                    remainQuantity: item.remainQuantity,
                    factoryCode: item.factoryCode,
                    factoryName: item.factoryName,
                    materialCode: item.materialCode,
                    materialDesc: item.materialDesc,
                    materialSpec: item.materialSpec,
                    materialGroupCode: item.materialGroupCode,
                    materialGroupName: item.materialGroupName,
                    purchaseCycle: item.purchaseCycle,
                    taxCode: item.taxCode,
                    taxRate: item.taxRate,
                    netPrice: item.netPrice,
                    currency: item.currency,
                    price: item.price,
                    sourceType: 'deliveryNotice',
                    sourceNumber: item.headId,
                    sourceItemNumber: item.id,
                    netAmount: '',
                    taxAmount: '',
                    itemStatus: '0'
                })
            })
            asnDetailGrid.insert(addTableData)
        },
        selectSupplierName (data){
            let ky={'supplierId': data[0].id, 'supplierCode': data[0].supplierCode, 'supplierName': data[0].supplierName}
            this.$refs.editPage.setFormFieldsValue(ky)        
        },
        validDeliveryQuantity ({row}){
            if(row.deliveryQuantity > row.remainQuantity){
                row.deliveryQuantity = 0
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shipmentQuantityCannotGreaterThanRemainingQuantity`, '发货数量不能大于剩余数量！'))
            }
        },
        publish (){
            let param = this.$refs.editPage.getParamsData()
            if(param.deliverySaleItemList.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addLineTips`, '至少添加一个行项目！'))
                return false
            }
            this.$refs.editPage.$refs.baseForm[0].validate(valid => {
                this.$refs.editPage.$refs.deliverySaleItemList[0].validate(err => {
                    if(valid && !err) {
                        this.postData()
                    }
                })
            })
        },
        postData (){
            let params = this.$refs.editPage.getParamsData()
            this.$refs.editPage.confirmLoading = true
            httpAction(this.url.publish, params, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$emit('ok')
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        confirmPlan (){
            let params = this.$refs.editPage.getParamsData()
            this.$refs.editPage.confirmLoading = true
            httpAction(this.url.confirmPlan, params, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$emit('ok')
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        }
    }
}
</script>
