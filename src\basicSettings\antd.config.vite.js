import Vue from 'vue'
// 引入ant-design-vue组件
import { Icon, Modal, message, notification } from 'ant-design-vue'

import MRemoteSelect from '@comp/mRemoteSelect'
import MSelect from '@comp/mSelect'
import mFloat from '@comp/mFloat'
import MSwitch from '@comp/mSwitch'
import MRadio from '@comp/mRadio'
import MChcekBox from '@comp/mCheckbox'
import MCascader from '@comp/mCascader'
// import CodeEditorModel from '@comp/codeEditorModel'
import MSelectModal from '@comp/mSelectModal'
import renderHtmlModal from '@comp/renderHtmlModal'

Vue.use(Modal)
Vue.use(notification)

Vue.use(MSelect)
Vue.use(MRemoteSelect)
Vue.use(MSwitch)
Vue.use(MRadio)
Vue.use(MChcekBox)
Vue.use(MCascader)
// Vue.use(CodeEditorModel)
Vue.use(MSelectModal)
Vue.use(renderHtmlModal)
Vue.use(mFloat)

Vue.prototype.$confirm = Modal.confirm
Vue.prototype.$info = Modal.info
Vue.prototype.$success = Modal.success
Vue.prototype.$error = Modal.error
Vue.prototype.$warning = Modal.warning
Vue.prototype.$message = message
Vue.prototype.$notification = notification

// 全局引用
const IconFont = Icon.createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_3056538_sy3dzv5mjga.js',
})

Vue.component('IconFont', IconFont)
