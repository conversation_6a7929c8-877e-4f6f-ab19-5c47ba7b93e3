<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage" 
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :singleGroupCoverConfig="singleGroupCoverConfig"
      :url="url"
      @loadSuccess="handleLoadSuccess" />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <a-modal
      v-drag    
      :width="860"
      v-if="bidModalFlag"
      :visible="bidModalFlag"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_bidResponse`, '应标')"
      :maskClosable="false"
      @cancel="bidModalFlag=false" 
      @ok="submitBid"
    >
      <a-form-model
        style="min-height: 90px;"
        ref="bidForm"
        layout="horizontal"
        :rules="bidformRules"
        :model="bidForm"
        v-bind="{
          labelCol: { span: 8 },
          wrapperCol: { span: 16 },
        }">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_contact`, '联系人')" prop="contacts">
              <a-select v-model="bidForm.contacts" defaultValue="请选择" allow-clear :dropdownMatchSelectWidth="false"
                        @change="supplierAccountSelectChangeHandle">
                <a-select-option v-for="item in supplierAccountListByEls" :key="item.subAccount">
                  {{ item.realname }}
                </a-select-option>
              </a-select>
<!--              <a-input v-model="bidForm.contacts" :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterContactName`, '请输入联系人姓名')" />-->
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_phone`, '手机号')"
              prop="phone">
              <a-input
                v-model="bidForm.phone"
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterYourCellPhoneNumber`, '请输入手机号')" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_mail`, '邮件')"
              prop="email">
              <a-input
                v-model="bidForm.email"
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseWriteEmailAddress`, '请输入邮件地址')" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <vxe-grid
        ref="bidGrid"
        v-bind="bidGridOptions"></vxe-grid>
    </a-modal>
  </div>
</template>
<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import { postAction, getAction } from '@/api/manage'
import { mapMutations } from 'vuex'
import { SET_CACHE_VUEX_CURRENT_EDIT_ROW, SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
export default {
    mixins: [DetailMixin],
    data () {
        return {
            supplierAccountListByEls: [],
            showRemote: false,
            bidModalFlag: false,
            bidForm: {
                id: null,
                contacts: null,
                phone: null,
                email: null
            },
            bidformRules: {
                contacts: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_required`, '必填') }],
                phone: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_required`, '必填') },
                    { pattern: /^1[3|4|5|7|8|9]\d{9}$/, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterCorrectPhoneNumber`, '请输入正确的手机号码')}
                ],
                email: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_required`, '必填')},
                    { pattern: /^(\w)+(\.\w+)*@(\w)+((\.\w+)+)$/, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseWriteCorrectEmail`, '请输入正确的邮件地址')}
                ]
            },
            bidGridOptions: {
                border: true,
                resizable: true,
                showHeaderOverflow: true,
                showOverflow: true,
                highlightHoverRow: true,
                columns: [
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 80 },
                    { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码')},
                    { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述')},
                    { field: 'materialModel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialModel`, '物料型号')},
                    { field: 'replyStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shouldMarked`, '是否应标'), width: 120, cellRender: {name: '$switch', props: { openValue: '1', closeValue: '0' }}}
                ],
                data: []
            },
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'saleBiddingItemList',
                        columns: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shouldInformation`, '应标信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                        ref: 'biddingSupplierList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'), field: 'replyStatus_dictText', width: 100},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'), field: 'replyTime', width: 140},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'), field: 'contacts', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'), field: 'phone', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_mail`, '邮件'), field: 'email', width: 150},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewBidsPermission`, '查看标书权限'), field: 'bidCheck_dictText', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidAuthority`, '投标权限'), field: 'bidQuote_dictText', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'), field: 'sourceType_dictText', width: 120}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120 },
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 140 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'sendStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_comfigSend`, '是否发送'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadFile },
                            {
                                type: 'edit',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                clickFn: this.preViewEvent
                            }
                        ]
                    } }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidResponse`, '应标'),  click: this.openBid, type: 'primary', showCondition: this.showPublishConditionBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supBiddingHall`, '投标大厅'),  click: this.handleTender, type: 'primary'},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/bidding/saleBiddingHead/queryById',
                confirm: '/bidding/saleBiddingHead/confirmBid',
                submit: '/a1bpmn/audit/api/submit',
                generate: '/bidding/saleBiddingHead/generatePriceRecord',
                download: '/attachment/saleAttachment/download',
                findSubAccountByElsAccount: '/bidding/saleBiddingHead/findSubAccountByElsAccount'
            },
            singleGroupCoverConfig: (config, group, displayModel) => {
                console.log(group)
                if (group.groupCode == 'fileInfo') {
                    config['checkboxConfig'] = {
                        highlight: true,
                        trigger: 'row',
                        checkMethod: ({ row }) => {
                            let fromData = this.$refs.detailPage.getPageData()
                            let supplier = fromData.biddingSupplierList[0]
                            let elsAccount = this.$ls.get('Login_elsAccount')
                            if(row.uploadElsAccount != elsAccount){
                                if(supplier.bidCheck != '1'){
                                    return false
                                }
                            }
                            return true
                        }
                    }
                }
                return config
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let account = this.currentEditRow.templateAccount ? this.currentEditRow.templateAccount :  this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${account}/sale_bidding_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        let that = this
        getAction(that.url.findSubAccountByElsAccount, {elsAccount: that.currentEditRow.elsAccount}).then((res) => {
            if (res && res.success) {
              that.supplierAccountListByEls = res.result
            }
        })

        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        } else {
            this.showRemote = true
        }
    },
    methods: {
        supplierAccountSelectChangeHandle(value) {
          let that = this
          if (that.supplierAccountListByEls) {
            that.supplierAccountListByEls.forEach(item => {
              if (item.subAccount == value) {
                that.bidForm.phone = item.phone
                that.bidForm.email = item.email
              }
            })
          }
        },
        formatPageData(item) {
            console.log('請求接口後格式化页面數據', item)
            if (item.securityCost === 0 || Math.abs(item.securityCost) > 0) {
                item.securityCost = Number(item.securityCost).toFixed(2)
            }
            if (item.bidingDocCost === 0 || Math.abs(item.bidingDocCost) > 0) {
                item.bidingDocCost = Number(item.bidingDocCost).toFixed(2)
            }
            return item;
        },
        formatTableData(data) {
            console.log('請求接口後格式化列表數據', data)
            data = data.map((item) => {
                if (item.price === 0 || Math.abs(item.price) > 0) {
                    item.price = Number(item.price).toFixed(6)
                }
                if (item.netPrice === 0 || Math.abs(item.netPrice) > 0) {
                    item.netPrice = Number(item.netPrice).toFixed(6)
                }
                if (item.targetPrice === 0 || Math.abs(item.targetPrice) > 0) {
                    item.targetPrice = Number(item.targetPrice).toFixed(6)
                }
                
                return item
            })
            return data
        },
        // 缓存当前行数据
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW,
            setCacheVuexCurrentEditRow: SET_CACHE_VUEX_CURRENT_EDIT_ROW
        }),
        showPublishConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            if ((params.replyStatus == '0')) {
                return true
            }else{
                return false
            }
        },
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        downloadFile (row){
            let fromData = this.$refs.detailPage.getPageData()
            let supplier = fromData.biddingSupplierList[0]
            let elsAccount = this.$ls.get('Login_elsAccount')
            if(row.uploadElsAccount != elsAccount){
                if(supplier.bidCheck != '1'){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidNotDownload`, '暂无标书下载权限！'))
                    return 
                }
            }
            this.downloadEvent(row)
        },
        // 打开应标
        openBid () {
            let that = this
            let supplier = this.$refs.detailPage.getPageData().biddingSupplierList[0]
            if(supplier.replyStatus != '0' && supplier.replyStatus != '3'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operatedAgain`, '已经响应的单据不能再次操作！'))
                return 
            }
            let saleBiddingItemListGridData = this.$refs.detailPage.$refs.saleBiddingItemList[0].getTableData().tableData
            let basicFormId = this.$refs.detailPage.form.id
            this.bidModalFlag = true
            this.$nextTick(()=> {
                // 清空数据
                that.bidForm.contacts= null
                that.bidForm.phone = null
                that.bidForm.email= null
                that.bidForm.id= basicFormId
                that.$refs.bidGrid.loadData(JSON.parse(JSON.stringify(saleBiddingItemListGridData)))                
            })
        },
        // 提交应标
        submitBid () {
            let that = this
            this.$refs.bidForm.validate(valid => {
                if (valid) {
                    let bidGridData =  that.$refs.bidGrid.getTableData().tableData
                    let url = 'bidding/saleBiddingHead/replyBidding'
                    let postData= {
                        id: that.bidForm.id,
                        contacts: that.bidForm.contacts,
                        phone: that.bidForm.phone,
                        email: that.bidForm.email,
                        saleBiddingItemList: bidGridData
                    }
                    postAction(url, postData).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            that.bidModalFlag= false
                            that.$refs.detailPage.queryDetail(that.bidForm.id)
                        }
                    })
                } else {
                    return false
                }
            })
        },
        handleTender () {
            let currentRow = Object.assign({}, this.currentEditRow, { role: 'sale' })
            this.setVuexCurrentEditRow({})
            this.setCacheVuexCurrentEditRow({ row: currentRow })
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/applyHall',
                query: {
                    _t,
                    type: 'tender' // 投标
                }
            })
            window.open(routeUrl.href, '_blank')
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        }
    }
}
</script>