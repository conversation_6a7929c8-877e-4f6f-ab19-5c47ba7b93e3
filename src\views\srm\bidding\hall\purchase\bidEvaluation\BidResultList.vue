<template>
  <div class="BidResultList">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      :deadline="evaEndTime"
      :renderExtra="renderExtra"
      @content-header-save="handleSave"
    />

    <div
      class="container"
      :style="style">
    
      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="table">
            <vxe-grid
              ref="biddingRegulationList"
              v-bind="defaultGridOption"
              :columns="columns">
              <template #custom_render="{row,$rowIndex,column}">
                <template v-if="row.inputType === '1'">
                  <template v-if="row.id">
                    <span>{{ row[column.property] }}</span>
                  </template>
                  <template v-else>
                    <vxe-input
                      clearable
                      size="small"
                      type="number"
                      min="0"
                      :max="row.fullMark"
                      v-model="row[column.property]"
                      @change="inputChange(row,column ,$event)"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')"/>
                  </template>
                </template>
                <template v-else>
                  <template v-if="row.id">
                    <span>{{ row[column.property] }}</span>
                  </template>
                  <template v-else>
                    <a
                      @click="() => choose(row,$rowIndex,column)">{{ row[column.property] || row[column.property] === 0 ?row[column.property]:$srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择') }}</a>
                    <a-icon
                      v-show="row[column.property] || row[column.property] === 0"
                      style="margin-left:6px"
                      type="close-circle"
                      @click="() => remove(row,$rowIndex,column)"></a-icon>
                  </template>
                </template>
                
              </template>
              <template slot="empty">
                <a-empty />
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>
    <SelectModal
      :tableData="currentTableData"
      @ok="biddingSelectOk"
      ref="fieldSelectModal" />
  </div>
</template>

<script lang="jsx">
import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import countdown from '@/components/countdown'
import { getAction, postAction } from '@/api/manage'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        'content-header': ContentHeader,
        countdown,
        SelectModal: ()=>import('./selectModal')
    },
    data () {
        return {
            serverTime: null,
            evaEndTime: null,
            currentTableData: [],
            currentCell: {},
            confirmLoading: false,
            supplierEvaScoreList: [],
            form: {},
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', event: 'save', authorityCode: 'bidding#purchaseBiddingHead:save'}
            ],
            showHeader: true,
            height: 0,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'dblclick', mode: 'cell' }
            },
            columns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_indexName`, '指标名称'), field: 'regulationName', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regulationDesc`, '指标说明'), field: 'regulationDetail', width: 250 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_evaluationType`, '评估类型'), field: 'regulationType_dictText', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fullMark`, '满分'), field: 'fullMark', width: 120 }
            ],
            columnsBiddingItem: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), field: 'supplierName', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), field: 'materialNumber', width: 250 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), field: 'materialNamber', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), field: 'materialDesc', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量'), field: 'requireQuantity', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), field: 'quantityUnit', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PVJSBA_bf78d67e`, '要求交货日期'), field: 'deliveryDate', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), field: 'currency_dictText', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'), field: 'taxCode', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), field: 'taxRate', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'), field: 'netPrice', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'), field: 'price', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netAmount`, '未税金额'), field: 'netAmount', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), field: 'taxAmount', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_effectiveDate`, '生效时间'), field: 'effectiveDate', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_expiryDate`, '失效时间'), field: 'expiryDate', width: 120 }
            ]
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        }
        // evaEndTime () {
        //     return this.vuex_currentEditRow.evaEndTime
        // }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        inputChange (row, column, e){
            row.supplierEvaScoreList.some(item=>{
                if(item.toElsAccount == column.property){
                    item.score = row[column.property]
                    return true
                }
            })
        },
        choose (row, rowIndex, column) {
            if(row[column.property] || row[column.property] === 0){
                return false
            }
            this.currentCell = {
                rowIndex: rowIndex,
                property: column.property
            }
            this.currentTableData = row.regulationSelections || []
            const columns = [
                {field: 'selectName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsNumExample`, '选项编号（如：A、B、C等）'), width: 150},
                {field: 'selectValue', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsValue`, '选项值'), width: 150},
                {field: 'score', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsScore`, '选项分值'), width: 150}
            ]
            const selectType = row.inputType === '2'?'single':'multiple'
            this.$refs.fieldSelectModal.open(columns, selectType)
        },
        remove (row, rowIndex, column) {
            row[column.property] = ''
            this.defaultGridOption.data.forEach((item, index)=>{
                if(rowIndex === index){
                    item.supplierEvaScoreList.some(subItem=>{
                        if(subItem.toElsAccount == column.property){
                            subItem.score = null
                            return true
                        }  
                    })
                }
            })
        },
        biddingSelectOk (data) {
            const {rowIndex, property} = this.currentCell
            let score = 0
            data.forEach(v=>{
                score += v.score
            })
            this.defaultGridOption.data.forEach((item, index)=>{
                if(rowIndex === index){
                    item[property] = score
                    item.supplierEvaScoreList.some(subItem=>{
                        if(subItem.toElsAccount == property){
                            subItem.score = score
                            return true
                        }  
                    })
                }
            })
            // console.log(this.defaultGridOption.data, 'this.defaultGridOption.data====')
        },
        renderExtra (deadline, fn) {
            const scopedSlots = {
                default: (row) => {
                    // return (<span>{ row.hours } : { row.minutes } : { row.seconds }</span>)
                    return (<span>{ row.totalHours } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_time`, '时') } { row.minutes } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_branch`, '分') } { row.seconds } { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_second`, '秒') }</span>)
                }
            }
            return (
                <div class="countdown" style="display: flex; align-items: center;">
                    <a-icon type="info-circle" />
                    <span style="margin: 0 8px;">{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRutK_9fc18dac`, '评标截止倒计时') }：</span>
                    <countdown 
                        time={ deadline }
                      
                        scopedSlots={scopedSlots}
                        style={ { fontSize: '12px', color: '#ee1d1d' } }
                        end={ fn }
                    >
                    </countdown>
                </div>
            )
        },
        deleteItemEvent (row, column, ref) {
            const grid = this.$refs[ref]
            grid.remove(row)
        },
        getData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingEvaResult/queryByBiddingId'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    let { biddingRegulationList = [] } = res.result || {}
                    let canSave = true
                    biddingRegulationList = biddingRegulationList.map(item => {
                        if(item.id){
                            canSave = false
                        }
                        let obj = {}
                        const { supplierEvaScoreList = [], ...others } = item
                        if (!this.supplierEvaScoreList.length) {
                            this.supplierEvaScoreList = supplierEvaScoreList
                            this.setColumns()
                        }
                        supplierEvaScoreList.forEach(sub  => {
                            obj[sub.toElsAccount] = sub.score
                        })

                        return { supplierEvaScoreList, ...others, ...obj }
                    })
                    if(!canSave){ // 已经保存过的，不能再保存，隐藏按钮
                        this.btns = []
                    }
                    console.log(biddingRegulationList, 'biddingRegulationList')
                    this.defaultGridOption.data = biddingRegulationList
                    // this.$refs.biddingRegulationList.loadData(biddingRegulationList)
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        setColumns () {
            const supColumns = this.supplierEvaScoreList.map((item, i) => {
                return {
                    title: item.supplierName || `supplierName_${i}`,
                    field: item.toElsAccount,
                    width: 150,
                    slots: { default: 'custom_render' }
                }
            })
            this.columns = this.columns.concat(supColumns)
        },
        handleSave () {
            const callback = () => {
                const accounts = this.supplierEvaScoreList.map(n => n.toElsAccount)
                console.log('accounts', accounts)

                let biddingRegulationList = this.$refs.biddingRegulationList.getTableData().fullData
                biddingRegulationList = biddingRegulationList.map(item => {
                    accounts.forEach(prop => {
                        if (item[prop]) {
                            delete item[prop]
                        }
                    })
                    return item
                })

                const params = {
                    id: this.vuex_currentEditRow.id,
                    biddingRegulationList
                }
                
                const url = '/bidding/purchaseBiddingEvaResult/saveEvaResult'
                
                this.confirmLoading = true
                postAction(url, params)
                    .then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                    })
                    .finally(() => {
                        this.confirmLoading = false
                        this.init()
                    })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_UBHN_411b0cb3`, '评标录入'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLAPHNyRW_d8025ebf`, '是否确认当前录入结果?'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        getBiddingHeadData () {
            const { id = '' } = this.vuex_currentEditRow || {}
            const url = '/bidding/purchaseBiddingHead/queryById'

            getAction(url, { id }).then(res => {
                if (!res.success) {
                    return
                }
                const { timestamp = '', result = {} } = res || {}
                let evaEndTime = result.evaEndTime_DateMaps || ''
                this.serverTime = timestamp
                if (evaEndTime) {
                    if (timestamp < evaEndTime) {
                        this.evaEndTime = evaEndTime - timestamp
                    }
                }
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getBiddingHeadData()
            this.getData()
        }
    },
    created () {
        this.init()
    }
}
</script>

<style lang="less" scoped>
.BidResultList {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
}
</style>

