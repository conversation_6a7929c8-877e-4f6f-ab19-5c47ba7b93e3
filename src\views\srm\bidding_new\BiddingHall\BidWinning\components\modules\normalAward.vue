<template>
  <div class="normalAward">
    <div
      class="searchLeftBox"
      :style="{height: `${height}px`}">
      <searchbox
        v-if="showSearchbox"
        :Format="{field: 'materialId', title: 'materialName'}"
        @searchChange="searchChange"
        :searchData="searchData"
        :height="height"/>
    </div>
    <div
      class="infoRightBox"
      :style="{height: `${height}px`}">
      <div class="columns">
        <div>
          <div class="title">
            <div class="line"></div>
            <span class="supplierInfoTitle">{{ $srmI18n(`${$getLangAccount()}#i18n_field_RdX_1369e0d`, '供应商') }}</span>
            <span class="materialInfoTitle">{{ $srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息') }}</span>
          </div>
          <div class="columnsList">
            <div
              class="columnsItem"
              v-for="(mater, index) in columnsList"
              :key="index">
              <span class="columnsItemText">
                <a-tooltip>
                  <template slot="title">
                    {{ mater.materialName }}
                  </template>
                  <div class="materItemText" >
                    {{ mater.materialName }}
                  </div>
                </a-tooltip>
              </span>
              <span
                class="columnsItemSorter"
                :class="index == active ? 'active' : ''"
                :title="$srmI18n(`${$getLangAccount()}#i18n_field_AT_c6d3d`, '排序')">
                <a-icon
                  @click="sorter('up', mater, index)"
                  type="caret-up"
                  :class="sorterType == 'up' ? 'active_up' : ''"
                  class="up"/>
                <a-icon
                  @click="sorter('down', mater, index)"
                  type="caret-down"
                  :class="sorterType == 'down' ? 'active_down' : ''"
                  class="down"/>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="dataList">
        <a-spin :spinning="tableLoading">
          <div class="supplierList">
            <div
              class="supplierItem"
              v-for="(item, supplierIndex) in tableData"
              :key="item.supplierAccount + supplierIndex">
              <div class="title">
                <div class="supplierNameTitle">{{ item.supplierName }}</div>
                <div class="supplierTableColumns">
                  <div
                    class="supplierTableColumnsItem"
                    v-for="(row, index) in supplierTableColumns"
                    :key="row.title + index">{{ row.title }}</div>
                </div>
              </div>
              <div class="content">
                <div
                  class="contentItem"
                  v-for="(mater, materIndex) in item.winningAffirmMaterialList"
                  :key="materIndex">
                  <div
                    class="materItem"
                    v-for="(columns, columnsIndex) in supplierTableColumns"
                    :key="columnsIndex">
                    <div
                      v-if="columns.type == 'text'"
                    >
                      <a-tooltip>
                        <template slot="title">
                          {{ mater[columns.field] }}
                        </template>
                        <div
                          class="materItemText"
                          :class="mater.orderBy == 1 ? 'red': ''">
                          {{ mater[columns.field] }}
                        </div>
                      </a-tooltip>
                    </div>
                    <a-switch
                      v-else-if="columns.type == 'switch'"
                      @change="(v) => handleSwitch(v, mater, item.supplierAccount)"
                      default-checked
                      :disabled="!isEdit"
                      v-model="mater[columns.field]"/>
                    <a-input-number
                      v-else-if="columns.type == 'number'"
                      v-model="mater[columns.field]"
                      @change="(v) => handleChangeNumber(v, mater, item.supplierAccount)"
                      :min="0"
                      :max="100"
                      :disabled="!isEdit || !mater['award']"/>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>
<script lang="jsx">
import searchbox from './searchbox'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import { uniqBy } from 'lodash'
export default {
    props: {
        resultData: {
            type: Object,
            default: () => {
                return {}
            }
        },
        pageStatus: {
            type: String,
            default: 'edit'
        }
    },
    components: {
        searchbox,
        listTable
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        }
    },
    watch: {
        'resultData': {
            handler (newV, oldV){
                this.init()
            },
            deep: true,
            immediate: true
        }
    },
    data () {
        return {
            height: 0,
            keyWord: [],
            sorterType: null,
            active: null,
            supplierList: [],
            tableData: [],
            showSearchbox: false,
            columnsList: [],
            tableLoading: false,
            statictableColumns: [
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'), 'field': 'materialName'}
            ],
            supplierTableColumns: [
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_su_c40f2`, '报价'), 'field': 'quote', 'type': 'text'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lB_c757f`, '授标'), 'field': 'award', 'type': 'switch'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzlv_2e27a637`, '拆分比例'), 'field': 'quotaScale', 'type': 'number'}
            ]
        }
    },
    methods: {
        searchChange (key = []) {
            this.tableLoading = true
            this.keyWord = key
            this.handleInitData()
            this.handleInitColumnsList()
            setTimeout(() => {
                this.tableLoading = false
            }, 300)
        },
        sorter (type, row, index) {
            if (this.sorterType == type && this.active == index) {
                this.sorterType = null
                this.active = null
                this.handleInitData()
                return
            }
            this.sorterType = type
            this.active = index
            this.tableLoading = true
            this.tableData.sort((a, b) => {
                let x = ''
                let y = ''
                for (let item of a.winningAffirmMaterialList) {
                    if (item.materialId == row.materialId) {
                        x = item['evaPrice']
                        break
                    }

                }
                for (let item of b.winningAffirmMaterialList) {
                    if (item.materialId == row.materialId) {
                        y = item['evaPrice']
                        break
                    }
                }
                let rev = type == 'up' ? (x > y ? 0 : x < y ? -1 : 0) : (x > y ? -1 : x < y ? 1 : 0)
                return rev
            })
            setTimeout(() => {
                this.tableLoading = false
            }, 300)
        },
        handleInitColumnsList () {
            this.columnsList = this.tableData[0].winningAffirmMaterialList
        },
        handleInitData () {
            this.tableData = JSON.parse(JSON.stringify(this.supplierList))
            if (this.keyWord.length > 0) {
                this.tableData.map(item => {
                    item.winningAffirmMaterialList = item.winningAffirmMaterialList.filter(row => {
                        return this.keyWord.includes(row.materialId)
                    })
                })
            }
        },
        handleSwitch (v, row, supplierAccount) {
            for (let item of this.supplierList) {
                if (item.supplierAccount == supplierAccount) {
                    for (let materialItem of item.winningAffirmMaterialList) {
                        if (materialItem.materialId == row.materialId) {
                            this.$set(materialItem, 'award', v)
                            row.quotaScale = !v ? '' : 0
                            this.$set(materialItem, 'quotaScale', !v ? '' : 0)
                        }
                    }
                }
            }
        },
        handleChangeNumber (v, row, supplierAccount) {
            for (let item of this.supplierList) {
                if (item.supplierAccount == supplierAccount) {
                    for (let materialItem of item.winningAffirmMaterialList) {
                        if (materialItem.materialId == row.materialId) {
                            this.$set(materialItem, 'quotaScale', v)
                            break
                        }
                    }
                    break
                }
            }
        },
        init () {
            // 物料行以供应商为分组
            let supplierAccountMap = {}
            let resultData = JSON.parse(JSON.stringify(this.resultData))
            for (let item of resultData.winningAffirmMaterialList) {
                if (!supplierAccountMap[item.supplierAccount]) supplierAccountMap[item.supplierAccount] = {winningAffirmMaterialList: [], supplierName: item.supplierName, supplierAccount: item.supplierAccount}
                supplierAccountMap[item.supplierAccount]['winningAffirmMaterialList'].push(item)
            }
            this.supplierList = Object.values(supplierAccountMap)
            this.supplierList.map(item => {
                item['winningAffirmMaterialList'].map(row => {
                    row.award = row.award === '1' ? true : false
                })
            })
            this.searchChange()
            // 获取物料类
            this.searchData = this.supplierList[0]['winningAffirmMaterialList'].map(({materialName, materialId}) => {
                return {materialId, materialName}
            })
            // 数组去重
            this.searchData = uniqBy(this.searchData, 'materialId')
            this.showSearchbox = true
        },
        // 向外抛数据
        externalAllData () {
            let params = []
            let data = JSON.parse(JSON.stringify(this.supplierList))
            data.map(item => {
                item.winningAffirmMaterialList.map(row => {
                    row.award = row.award ? '1' : '0'
                    params.push(row)
                })
            })
            return params
        }
    },
    created () {
        this.height = document.documentElement.clientHeight - 180
        this.init()
    }
}
</script>
<style lang="less" scoped>
.red {
    color: red;
}
.fl{
    float: left;
}
.fr{
    float: right;
}
.clearFix{
    clear: both;
}
.normalAward{
    display: flex;
}
.searchLeftBox{
    min-width: 200px;
    padding: 5px;
    border: 1px solid #e8e8e8;
    flex: 10%;
    margin-right: 10px;
}
.borderB{
    border-bottom: 1px solid #e8e8e8;

}
.cell{
    height: 40px;
    line-height: 40px;
}

.infoRightBox{
    flex: 88%;
    padding: 5px;
    border: 1px solid #e8e8e8;
    min-width: 400px;
    display: flex;
    overflow:auto;
    .columns{
        flex-basis: 120px;
        border: 1px solid #e8e8e8;
        border-right: none;
        .title{
            position: relative;
            height: 60px;
            border-bottom: 1px solid #e8e8e8;
            width: 120px;
        }
        .line{
            transform: rotate(26deg);
            position: absolute;
            top: 30px;
            left: -6px;
            height: 1px;
            width: 132px;
            background: #e8e8e8;
        }
        .columnsList{
            .columnsItem{
                text-align: center;
                .borderB;
                .cell;
                .columnsItemText{
                    display: inline-block;
                    .materItemText{
                        max-width: 102px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        word-break: break-all;
                    }

                }
                .columnsItemSorter{
                        float: right;
                        padding-top: 8px;
                        padding-right: 4px;
                    .up,.down{
                        cursor: pointer;
                        display: block;
                        height: 10px;
                        line-height: 10px;
                    }
                }
            }
        }
        .supplierInfoTitle{
            position: absolute;
            top: 5px;
            right: 5px;
        }
        .materialInfoTitle{
            position: absolute;
            left: 5px;
            bottom: 5px;
        }
    }
    .dataList{
        width: 100%;
    }
    .supplierList{
        display:flex;
        border: 1px solid #e8e8e8;
        .clearFix;
    }
    .supplierItem{
        text-align: center;
        border-left: 1px solid #e8e8e8;
        &:first-child{
            border-left: none;
        }
        .title{
            width: 300px;
            height: 60px;
            .borderB;
        }
        .supplierNameTitle{
            height: 29px;
            line-height: 29px;
            .borderB;
        }
        .supplierTableColumns{
            height: 30px;
            line-height: 30px;
            display: flex;
            .supplierTableColumnsItem{
                flex: 1;
            }
        }
    }
    .content{
        .contentItem{
            display: flex;
            .cell;
            .borderB;
            .materItem{
                flex: 1;
            }
        }
        .materItemText{
            overflow: hidden;
            max-width: 98px;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: break-all;
        }
    }
    .active{
        .active_up, .active_down{
            color: #1890ff;
        }
    }
}
</style>
