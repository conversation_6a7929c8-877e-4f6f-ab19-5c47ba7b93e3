<template>
  <div class="PurchaseEightDisciplinesHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        v-if="isView"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :collapseHeadCode="['baseForm','busRule','personFrom']"
        modelLayout="masterSlave"
        pageStatus="edit"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
      <a-modal
        v-drag
        centered
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
        :width="360"
        v-model="templateVisible"
        @ok="selectedTemplateAfter">
        <template slot="footer">
          <a-button
            key="back"
            @click="handleTempCancel">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
          </a-button>
          <a-button
            key="submit"
            type="primary"
            :loading="submitLoading"
            @click="selectedTemplateAfter">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
          </a-button>
        </template>
        <m-select
          v-model="templateNumber"
          :options="templateOpts"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"/>
      </a-modal>
      <a-modal
        v-drag
        v-model="previewModal"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
        :footer="null"
        :width="1000">
        <div
          style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
          v-html="previewContent"></div>
      </a-modal>

      <a-modal
        v-drag
        v-model="auditVisible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_contractReturn`, '合同退回')"
        :okText="okText"
        @ok="handleOk">
        <a-textarea
          v-model="opinion"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterContractReturnDesc`, '请输入合同退回备注')"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-modal>
      <view-item-diff-modal ref="viewDiffModal"/>
      <His-Contract-Item-Modal ref="hisContractItemModal"/>
    </a-spin>
  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import HisContractItemModal from './SaleHisContractItemModal'
import ViewItemDiffModal from './SaleViewItemDiffModal'
import {getAction, httpAction} from '@/api/manage'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import {axios} from '@/utils/request'


export default {
    name: 'SaleContractHeadModalNew',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout,
        HisContractItemModal,
        ViewItemDiffModal
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            isView: false,
            labelCol: {span: 4},
            wrapperCol: {span: 15},
            templateVisible: false,
            submitLoading: false,
            nextOpt: true,
            currentRow: {},
            templateNumber: undefined,
            templateOpts: [],
            businessType: '',
            refresh: true,
            showHelpTip: false,
            editRowModal: false,
            auditVisible: false,
            previewModal: false,
            notShowTableSeq: true,
            opinion: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractReturn`, '合同退回'),
            editItemRow: {},
            currentItemContent: '',
            previewContent: '',
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            requestData: {
                detail: {
                    url: '/contract/saleContractHead/queryById', args: (that) => {
                        return {id: that.currentEditRow.id}
                    }
                }
            },
            externalToolBar: {
                purchaseContractItemList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cIIZt_b19ad3f5`, '创建履约单'),
                        key: 'gridCreate',
                        click: () => this.createPromise('item'),
                        authorityCode: 'contract#saleContractHead:createPromiseByItems',
                        show: this.showCreatePromiseItemBtn,
                        attrs: {
                            type: 'primary'
                        }
                    }
                ],
                purchaseContractContentItemList: [],
                purchaseContractPromiseList: [],
                contractItemCustom1List: [
                    /*{
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXAHUz_5119c1a1`, '创建履约单'),
                            key: 'gridCreate',
                            click: ()=>this.createPromise('itemCustom1'),
                            show: this.showCreatePromiseBtn,
                            authorityCode: 'contract#saleContractHead:createPromiseByItems',
                            attrs: {
                                type: 'primary'
                            }
                        },*/
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cIOlt_b2810fcc`, '创建验收单'),
                        key: 'gridCreate',
                        click: this.createPromise2,
                        attrs: {
                            type: 'primary'
                        }
                    }
                ],
                contractItemCustom2List: [],
                contractItemCustom3List: [],
                contractItemCustom4List: [],
                contractItemCustom5List: [],
                saleAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: this.businessType, // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrParams,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                        click: this.deleteBatch
                    },
                    {...new BatchDownloadBtn({pageCode: 'saleAttachmentList'}).setUrl('sale').btnConfig}
                ]
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                    attrs: {
                        type: 'primary'
                    },
                    authorityCode: 'contract#saleContractHead:getPreviewData',
                    click: this.previewPdf
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                    attrs: {
                        type: 'primary'
                    },
                    authorityCode: 'contract#saleContractHead:download',
                    click: this.downloadFile
                },
                // {
                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IKWord_12f184fc`, '下载Word'),
                //     attrs: {
                //         type: 'primary'
                //     },
                //     authorityCode: 'contract#purchaseContractHead:download',
                //     click: this.downloadWordFile
                // },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractConfirmation`, '合同确认'),
                    type: 'primary',
                    click: this.confirm,
                    authorityCode: 'contract#saleContractHead:confirmed',
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractReturn`, '合同退回'),
                    type: 'primary',
                    click: this.refund,
                    authorityCode: 'contract#saleContractHead:refund',
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                save: '/contract/saleContractHead/edit',
                detail: '/contract/saleContractHead/queryById',
                createPromise: '/contract/saleContractHead/createPromiseByItems',
                confirm: '/contract/saleContractHead/confirmed',
                downloadUrl: '/attachment/saleAttachment/download',
                download: '/attachment/purchaseAttachment/download',
                downloadContract: '/contract/saleContractHead/download',
                downloadDoc: '/contract/saleContractHead/downloadDoc',
                refund: '/contract/saleContractHead/refund'
            }
        }
    },

    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            if (this.currentEditRow.contractType == '1' || this.currentEditRow.contractType == '2') {
                return `${account}/sale_contract_${templateNumber}_${templateVersion}`
            } else {
                return `${account}/sale_contractSimple_${templateNumber}_${templateVersion}`
            }
        }
    },
    mounted () {
        const that = this
        getAction('/contract/saleContractHead/queryById', {id: this.currentEditRow.id}).then(res => {
            if (res.success) {
                if (res.result) {
                    that.currentEditRow.templateNumber = res.result.templateNumber
                    that.currentEditRow.templateVersion = res.result.templateVersion
                    that.currentEditRow.templateAccount = res.result.templateAccount
                    that.currentEditRow.contractType = res.result.contractType
                    that.isView = true
                } else {
                    that.isView = true
                }
            }
        })
        if (this.currentEditRow.contractType == '1' || this.currentEditRow.contractType == '2') {
            this.externalToolBar.saleAttachmentList[0].args.businessType = 'contract'
        } else {
            this.externalToolBar.saleAttachmentList[0].args.businessType = 'contractSimple'
        }
    },
    beforeDestroy () {
        if (this.sortable) {
            this.sortable.destroy()
        }
    },
    methods: {
        attrParams () {
            const params = this.getAllData()
            const {contractNumber} = params
            if (contractNumber) {
                return {
                    sourceNumber: contractNumber,
                    actionRoutePath: '/srm/contract/purchase/PurchaseContractHeadList,/srm/contract/sale/SaleContractHeadList'
                }
            }
        },
        downloadFile () {
            let params = {id: this.paramIntegrate().id}
            this.confirmLoading = true
            axios({
                url: this.url.downloadContract,
                responseType: 'blob',
                params: params
            }).then(res => {
                this.confirmLoading = false
                let fieldName = this.currentEditRow.contractNumber + '_' + this.currentEditRow.contractVersion + '.pdf'
                //console.log(res)
                const blob = new Blob([res])
                const blobUrl = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.style.display = 'none'
                a.href = blobUrl
                a.download = fieldName
                a.click()
            })
        },
        downloadWordFile () {
            let params = {id: this.paramIntegrate().id}
            this.confirmLoading = true
            axios({
                url: this.url.downloadDoc,
                responseType: 'blob',
                params: params
            }).then(res => {
                this.confirmLoading = false
                let fieldName = this.currentEditRow.contractNumber + '_' + this.currentEditRow.contractVersion + '.doc'
                //console.log(res)
                const blob = new Blob([res])
                const blobUrl = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.style.display = 'none'
                a.href = blobUrl
                a.download = fieldName
                a.click()
            })
        },
        delDisable (row) {
            const elsAccount = this.$ls.get(USER_ELS_ACCOUNT)
            return elsAccount != row.uploadElsAccount
        },
        deleteAttachment (Vue, row) {
            console.log(row.id, '====')
            let url = '/attachment/saleAttachment/delete'
            let params = {
                id: row.id
            }
            let attachmentListGrid = this.getItemGridRef('saleAttachmentList')
            getAction(url, params).then((res) => {
                if (res && res.success) {
                    attachmentListGrid.remove(row)
                    this.$message.success(res.message)
                } else {
                    this.$message.success(res.message)
                }
            })
        },
        deleteBatch ({pageConfig, groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let arr = []
            let delArr = []
            //大B
            let user = this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')

            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            if (checkboxRecords && checkboxRecords.length > 0) {
                checkboxRecords.forEach(row => {
                    //当前登录人等账号于查询的账号 大B
                    if (user == row.uploadElsAccount) {
                        if (subAccount == row.uploadSubAccount) {
                            delArr.push(row)
                        } else {
                            arr.push(row.fileName)
                        }
                    } else {
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if (arr && arr.length > 0) {
                    let str = arr.join(',')
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') + str + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }

            itemGrid.removeCheckboxRow().then(() => {
                const tableData = itemGrid && itemGrid.getTableData().fullData
                console.log(tableData, 'tableData')
                if (tableData && tableData.length > 0) {
                    checkboxRecords.forEach((row) => {
                        if (user == row.uploadElsAccount) {
                            delArr.push(row)
                        } else {
                            arr.push(row.id)
                        }
                    })
                }
            })
        },
        preViewEvent (Vue, row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        },
        syncShow ({pageData}) {
            console.log('pageData', (pageData))
            return (pageData.contractStatus === '2')
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                {
                                    key: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    click: this.downloadEvent
                                },
                                {
                                    key: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    click: this.preViewEvent
                                },
                                {
                                    key: 'delete',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteAttachment,
                                    disabled: this.delDisable
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractLibrary`, '合同条款库'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseContractContentItemList',
                        groupType: 'item',
                        sortOrder: '4',
                        extend: {
                            optColumnList: [
                                {
                                    key: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    click: this.downloadEvent
                                }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '项目编号',
                        fieldLabelI18nKey: 'i18n_title_projectNumber',
                        field: 'itemId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        dictCode: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '项目名称',
                        fieldLabelI18nKey: 'i18n_title_projectName',
                        field: 'itemName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '项目类型',
                        fieldLabelI18nKey: 'i18n_title_itemType',
                        field: 'itemType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '项目版本',
                        fieldLabelI18nKey: 'i18n_title_projectVersion',
                        field: 'itemVersion',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '变更标识',
                        fieldLabelI18nKey: 'i18n_title_changeIdentification',
                        field: 'changeFlag',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        cellRender: {
                            name: '$switch',
                            type: 'visible',
                            props: {closeValue: '0', openValue: '1', disabled: true}
                        },
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '来源类型',
                        fieldLabelI18nKey: 'i18n_title_sourceType',
                        field: 'sourceType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '操作',
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: 'sourceType_dictText',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                    onClick={() => this.viewDetail(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')} </a>)
                                if (row.changeFlag == '1') {
                                    resultArray.push(<a
                                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Il_b8efb`, '对比')}
                                        onClick={() => this.viewDiff(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Il_b8efb`, '对比')}</a>)
                                }
                                return resultArray
                            }
                        }
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: {default: 'grid_opration'}
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
            this.externalToolBar['saleAttachmentList'][0].args.headId = resultData.id || ''
            let itemInfo = pageConfig.groups
                .map(n => ({label: n.groupName, value: n.groupCode}))
            //this.externalToolBar['saleAttachmentList'][0].args.itemInfo = itemInfo
            if (formModel.showCustom1 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', false)
            }
            if (formModel.showCustom2 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', false)
            }
            if (formModel.showCustom3 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', false)
            }
            if (formModel.showCustom4 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', false)
            }
            if (formModel.showCustom5 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', false)
            }
            if (formModel.showItem == '1') {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', false)
            } else {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', true)
            }
            if ((formModel.contractStatus == '3' || formModel.contractStatus == '8' || formModel.contractStatus == '6')
                && (formModel.promiseType == 'promisePurchase' || formModel.promiseType == 'promiseSale')) {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', false)
            } else {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', true)
            }
        },
        showCreatePromiseBtn ({pageData, pageConfig}) {
            console.log(pageData)
            console.log(pageConfig)
            if ((pageData.contractStatus == '3' || pageData.contractStatus == '8' || pageData.contractStatus == '6')
                && pageData.promiseType == 'promiseSale' && (pageData.contractType == '1' || pageData.contractType == '2') &&
                (pageData.sign === '1' && pageData.endSign === '2') || pageData.sign === '0') {
                return true
            } else {
                return false
            }
        },
        showCreatePromiseItemBtn ({pageData}) {
            // 1. 履约方式 供应商创建履约单，且 电子签章 开启 1，且 状态 已归档 6，且 签署状态 已完成 2
            // 2. 履约方式 供应商创建履约单，且 电子签章 未开启 !1，且 (状态 供应商已确认 3 或 已归档 6)
            // 则 显示“创建履约单”
            if (
                pageData.promiseType === 'promiseSale'
                &&
                (
                    (pageData.sign === '1' && pageData.contractStatus == '6' && pageData.endSign === '2')
                    ||
                    (pageData.sign !== '1' && ['3', '6'].includes(pageData.contractStatus))
                )
            ) return true

            // 否则，均 不显示 “创建履约单”
            return false

            // if (pageData.promiseType === 'promiseSale' && (pageData.contractStatus == '3'|| pageData.contractStatus == '6')) return true
            // if (pageData.sign !== '1' && ['1', '2'].includes(pageData.contractType) && ['order', 'promisePurchase'].includes(pageData.promiseType)) return false
            // if ((pageData.contractStatus == '3'||pageData.contractStatus == '8'||pageData.contractStatus == '6')
            //         && pageData.promiseType == 'promiseSale' && (pageData.contractType =='1' || pageData.contractType =='2') &&
            //         (pageData.sign === '1' && pageData.endSign === '2') || pageData.sign === '0') {
            //     return true
            // } else {
            //     return false
            // }
        },
        createPromise (createWay) {
            this.nextOpt = true
            this.serachTemplate('contractPromise', createWay)
        },
        refund () {
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractReturn`, '合同退回')
        },
        handleOk () {
            let pdate = this.getAllData()
            this.getData(this.url.refund, {
                id: this.currentEditRow.id,
                refundRemark: this.opinion,
                saleRemark: pdate.saleRemark
            })
        },
        confirm () {
            let that = this
            let pdate1 = this.getAllData()
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractConfirmation`, '合同确认')
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_thisOperationWillConfirmContractConfirm`, '此操作将确认合同，是否确认?'),
                onOk: function () {
                    that.getData(that.url.confirm, {id: that.currentEditRow.id, saleRemark: pdate1.saleRemark})
                }
            })
        },
        getData (url, param) {
            let that = this;
            let params = this.getAllData()
            if (!param) {
                param = {}
            }
            param['id'] = params.id;
            that.confirmLoading = true;
            getAction(url, param).then(res => {
                if (res.success) {
                    this.form = res.result
                    this.auditVisible = false
                    this.opinion = ''
                    this.$message.success(res.message)
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                that.confirmLoading = false;
                this.auditVisible = false
                this.init()
                this.$parent.modalFormOk()
            })
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        },
        paramIntegrate () {
            let pageData = this.getAllData() || {}
            let {busRule = {}, personFrom = {}} = pageData || {}

            // pageData = { ...pageData, ...busRule, ...personFrom}
            delete pageData.busRule
            delete pageData.personFrom

            pageData = Object.assign({}, pageData, busRule, personFrom)
            return pageData
        },
        createPromise2 () {
            this.$message.success('操作成功')
        },
        preview () {
            let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.confirmLoading = true
            getAction('/contract/saleContractHead/getPreviewData', {id: this.currentEditRow.id}).then((res) => {
                if (res.success) {
                    this.previewModal = true
                    this.previewContent = res.result
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        previewPdf () {
            let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.confirmLoading = true
            axios({
                url: this.url.downloadContract,
                responseType: 'blob',
                params: {id: this.currentEditRow.id}
            }).then((res) => {
                if (res) {
                    let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                    window.open(url)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        uploadCallBack (result) {
            let fileGrid = this.getItemGridRef('saleAttachmentList')
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent () {
            const fileGrid = this.getItemGridRef('saleAttachmentList')
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        goBack () {
            this.$emit('hide')
        },
        serachTemplate (businessType, createWay) {
            this.currentRow = {}
            let parm = this.getAllData()
            if (createWay == 'item') {
                let selectedRows = this.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
                if (selectedRows.length <= 0) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
                    return
                } else {
                    if (this.nextOpt) {
                        this.businessType = businessType
                        this.openModal()
                    }
                }
            } else if (createWay == 'itemCustom1') {
                let selectedRows = this.getItemGridRef('contractItemCustom1List').getCheckboxRecords()
                if (selectedRows.length <= 0) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
                    return
                } else {
                    /*selectedRows.forEach((item, i) => {
                            if (item.sourceType == 'order') {
                                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行来源为订单行')
                                this.nextOpt = false
                                return
                            }
                        })*/
                    if (this.nextOpt) {
                        this.businessType = businessType
                        this.openModal()
                    }
                }
            } else {
                if (parm.contractType == '1') {
                    let selectedRows = this.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
                    if (selectedRows.length <= 0) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
                        return
                    } else {
                        if (this.nextOpt) {
                            this.businessType = businessType
                            this.openModal()
                        }
                    }
                } else {
                    let selectedRows = this.getItemGridRef('contractItemCustom1List').getCheckboxRecords()
                    if (selectedRows.length <= 0) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
                        return
                    } else {
                        /*selectedRows.forEach((item, i) => {
                                if (item.sourceType == 'order') {
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行来源为订单行')
                                    this.nextOpt = false
                                    return
                                }
                            })*/
                        if (this.nextOpt) {
                            this.businessType = businessType
                            this.openModal()
                        }
                    }
                }

            }
        },
        queryTemplateList (elsAccount) {
            let parm = this.getAllData()
            let params = {elsAccount: parm.busAccount, businessType: this.businessType}
            return getAction('/template/templateHead/getListByType', params)
        },
        openModal () {
            this.queryTemplateList(this.$ls.get('Login_elsAccount')).then(res => {
                if (res.success) {
                    if (res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.templateNumber,
                                title: item.templateName,
                                version: item.templateVersion,
                                account: item.elsAccount
                            }
                        })
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length === 1) {
                            this.templateNumber = this.templateOpts[0].value
                            this.selectedTemplateAfter()
                        } else {
                            // 有多个模板先选择在新建
                            this.templateVisible = true
                        }
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        handleTempCancel () {
            this.templateVisible = false
        },
        selectedTemplateAfter () {
            if (this.templateNumber) {
                const that = this
                let parm = this.getAllData()
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version,
                    templateAccount: template[0].account,
                    purchaseContractItemList: [],
                    contractItemCustom1List: []
                }
                this.url.temp = this.url.createPromise
                if (parm.showItem == '1') {
                    params.purchaseContractItemList = that.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
                }
                if (parm.showCustom1 == '1') {
                    params.contractItemCustom1List = that.getItemGridRef('contractItemCustom1List').getCheckboxRecords()
                }
                that.templateVisible = false
                that.submitLoading = false
                if (this.url.temp == '') {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
                    return
                }
                that.postUpdateData(this.url.temp, params)
            }
        },
        postUpdateData (url, row) {
            this.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.init()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        downloadEvent (Vue, row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id || row.relationId
            const fileName = row.fileName
            const params = {
                id
            }
            getAction(this.url.downloadUrl, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        saveEvent () {
            this.$refs.editPage.postData()
        }
    }
}
</script>