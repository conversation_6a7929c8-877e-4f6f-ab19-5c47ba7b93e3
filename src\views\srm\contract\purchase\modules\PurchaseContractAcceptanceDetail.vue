<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterRemoteConfigData="handleAfterRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>

  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@api/manage'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'

export default {
    name: 'PurchaseContractAcceptanceDetail',
    components: {
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
            }
        }
    },
    data () {
        return {
            requestData: {
                detail: {
                    url: '/contract/purchaseContractAcceptance/queryById', args: (that) => {
                        return {id: that.currentEditRow.id}
                    }
                }
            },
            externalToolBar: {
                purchaseAttachmentList: [
                    {...new BatchDownloadBtn({pageCode: 'purchaseAttachmentList'}).btnConfig}
                ]
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                    args: {
                        url: '/contract/purchaseContractAcceptance/purchaseConfirm'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'confirm',
                    click: this.purchaseConfirm,
                    show: this.showConfirmAndRefuse
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'),
                    args: {
                        url: '/contract/purchaseContractAcceptance/purchaseRefuse'
                    },
                    attrs: {
                        type: 'danger'
                    },
                    key: 'refuse',
                    click: this.purchaseRefuse,
                    show: this.showConfirmAndRefuse
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ]
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_contractAcceptance_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        // 确认、拒绝按钮显隐
        showConfirmAndRefuse ({pageConfig}) {
            let pageData = pageConfig.groups[0].formModel
            let documentStatus = pageData.documentStatus
            if (documentStatus && documentStatus==='1' && pageData.createAccount!=this.$ls.get(USER_ELS_ACCOUNT)) {
                return true
            } else {
                return false
            }
        },

        // 确认
        purchaseConfirm ({pageConfig}) {
            let pageData = pageConfig.groups[0].formModel

            let that = this
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认')
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isSure`, '是否确认?'),
                onOk: function () {
                    that.postDataDeal(pageData.id, '2')
                }
            })
        },

        // 拒绝
        purchaseRefuse ({pageConfig}) {
            let pageData = pageConfig.groups[0].formModel

            let that = this
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝')
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isRefer`, '是否拒绝?'),
                onOk: function () {
                    that.postDataDeal(pageData.id, '3')
                }
            })
        },

        // 确认、拒绝接口发送数据
        postDataDeal (id, documentStatus) {
            this.$refs[this.businessRefName].confirmLoading = true
            let param = {}
            param['id'] = id
            param['documentStatus'] = documentStatus
            let url = documentStatus==='2' ? '/contract/purchaseContractAcceptance/purchaseConfirm':'/contract/purchaseContractAcceptance/purchaseRefuse'
            postAction(url, param, 'post').then((res) => {
                this.$refs[this.businessRefName].confirmLoading = false
                if (res.success) {
                    this.$refs[this.businessRefName].queryDetail()
                    this.$message.success(res.message)
                } else {
                    this.$message.warning(res.message)
                }
            }).catch(() => {
                this.$refs[this.businessRefName].confirmLoading = false
            })

        },

        // 文件下载
        downloadEvent (vue, row) {
            console.log(row)
            if (!row.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseUploadFile`, '请上传文件'))
                return
            }
            const params = {id: row.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        // 文件预览
        preViewEvent (vue, row) {
            if (!row.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseUploadFile`, '请上传文件'))
                return
            }
            let preViewFile = {id: row.id, fileName: row.fileName}
            this.$previewFile.open({params: preViewFile})
        },
        // 配置数据组装前，传入需要组装数据
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {

        },
        handleAfterRemoteConfigData (data) {
            return data
        }
    }
}
</script>
