const TenderRouter =
    [
        {
            path: '/biddingHall/bidding/PreBiddingNotice',
            name: 'PreBiddingNotice',
            meta: {
                title: '资格预审公告',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/BiddingNotice/BiddingNotice')
        },
        {
            path: '/biddingHall/bidding/BiddingNotice',
            name: 'biddingNotice',
            meta: {
                title: '招标公告',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/BiddingNotice/BiddingNotice')
        },
        {
            path: '/biddingHall/bidding/InvitationBid',
            name: 'InvitationBid',
            meta: {
                title: '投标邀请',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'invitationBid' */ '@/views/srm/bidding_new/BiddingHall/BiddingNotice/InvitationBid')
        },
        // {
        //     path: '/biddingHall/bidding/ChangeInvitationBid',
        //     name: 'ChangeInvitationBid',
        //     meta: {
        //         title: '投标邀请变更',
        //         keepAlive: false
        //     },
        //     component: () => import(/* webpackChunkName: 'ChangeInvitationBid' */ '@/views/srm/bidding_new/BiddingHall/BiddingNotice/ChangeInvitationBid')
        // },
        {
            path: '/biddingHall/bidding/PreChangeTenderNoticeList',
            name: 'PreChangeTenderNoticeList',
            meta: {
                title: '资格预审变更公告',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'ChangeTenderNoticeList' */ '@/views/srm/bidding_new/BiddingHall/ChangeTenderNotice/ChangeTenderNoticeList')
        },
        {
            path: '/biddingHall/bidding/ChangeTenderNoticeList',
            name: 'ChangeTenderNoticeList',
            meta: {
                title: '变更公告',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'ChangeTenderNoticeList' */ '@/views/srm/bidding_new/BiddingHall/ChangeTenderNotice/ChangeTenderNoticeList')
        },
        {
            path: '/biddingHall/bidding/PrePurchaseTenderProjectJury',
            name: 'PrePurchaseTenderProjectJury',
            meta: {
                title: '资格预审评委会',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'PurchaseTenderProjectJury' */ '@/views/srm/bidding_new/BiddingHall/PurchaseTenderProjectJury/PurchaseTenderProjectJury')
        },
        {
            path: '/biddingHall/bidding/PurchaseTenderProjectJury',
            name: 'PurchaseTenderProjectJury',
            meta: {
                title: '组建评委会',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'PurchaseTenderProjectJury' */ '@/views/srm/bidding_new/BiddingHall/PurchaseTenderProjectJury/PurchaseTenderProjectJury')
        },
        {
            path: '/biddingHall/bidding/PreClarifyAndQuestions',
            name: 'PreClarifyAndQuestions',
            meta: {
                title: '资格预审澄清答疑',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'ClarifyAndQuestions' */ '@/views/srm/bidding_new/BiddingHall/ClarifyAndQuestions/ClarifyAndQuestions')
        },
        {
            path: '/biddingHall/bidding/ClarifyAndQuestions',
            name: 'ClarifyAndQuestions',
            meta: {
                title: '澄清答疑',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'ClarifyAndQuestions' */ '@/views/srm/bidding_new/BiddingHall/ClarifyAndQuestions/ClarifyAndQuestions')
        },
        // {
        //     path: '/biddingHall/bidding/SupplierClarifyAndQuestions',
        //     name: 'SupplierClarifyAndQuestions',
        //     meta: {
        //         title: '供应商澄清答疑',
        //         keepAlive: false
        //     },
        //     component: () => import('@/views/srm/bidding_new/BiddingHall/SupplierClarifyAndQuestions/SupplierClarifyAndQuestions')
        // },
        {
            path: '/biddingHall/bidding/PreBiddingFile',
            name: 'PreBiddingFile',
            meta: {
                title: '招标文件',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'BiddingFile' */ '@/views/srm/bidding_new/BiddingHall/BiddingFile/BiddingFile')
        },
        {
            path: '/biddingHall/bidding/BiddingFile',
            name: 'BiddingFile',
            meta: {
                title: '招标文件',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'BiddingFile' */ '@/views/srm/bidding_new/BiddingHall/BiddingFile/BiddingFile')
        },
        {
            path: '/biddingHall/bidding/TenderResponsesituationList',
            name: 'TenderResponsesituationList',
            meta: {
                title: '响应情况',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'TenderResponsesituationList' */ '@/views/srm/bidding_new/BiddingHall/TenderResponsesituation/TenderResponsesituationList')
        },
        {
            path: '/biddingHall/bidding/BinddingOpenSetting',
            name: 'BinddingOpenSetting',
            meta: {
                title: '开标设置',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'BinddingOpenSetting' */ '@/views/srm/bidding_new/BiddingHall/OpenTender/BinddingOpenSetting')
        },
        {
            path: '/biddingHall/bidding/PreBinddingOpenSetting',
            name: 'PreBinddingOpenSetting',
            meta: {
                title: '资格预审开标设置',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'BinddingOpenSetting' */ '@/views/srm/bidding_new/BiddingHall/OpenTender/BinddingOpenSetting')
        },
        {
            path: '/biddingHall/bidding/FirstBinddingOpenSetting',
            name: 'FirstBinddingOpenSetting',
            meta: {
                title: '第一步开标设置',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'BinddingOpenSetting' */ '@/views/srm/bidding_new/BiddingHall/OpenTender/BinddingOpenSetting')
        },
        {
            path: '/biddingHall/bidding/SecondBinddingOpenSetting',
            name: 'SecondBinddingOpenSetting',
            meta: {
                title: '第二步开标设置',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'BinddingOpenSetting' */ '@/views/srm/bidding_new/BiddingHall/OpenTender/BinddingOpenSetting')
        },
        {
            path: '/biddingHall/bidding/FirstPurchaseOpen',
            name: 'FirstPurchaseOpen',
            meta: {
                title: '第一步开标',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'PurchaseOpen' */ '@/views/srm/bidding_new/BiddingHall/OpenTender/PurchaseOpen')
        },
        {
            path: '/biddingHall/bidding/SecondPurchaseOpen',
            name: 'SecondPurchaseOpen',
            meta: {
                title: '第二步开标',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'PurchaseOpen' */ '@/views/srm/bidding_new/BiddingHall/OpenTender/PurchaseOpen')
        },
        {
            path: '/biddingHall/bidding/PurchaseOpen',
            name: 'PurchaseOpen',
            meta: {
                title: '开标',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'PurchaseOpen' */ '@/views/srm/bidding_new/BiddingHall/OpenTender/PurchaseOpen')
        },
        {
            path: '/biddingHall/BidEvaluationManagement/PretaskAssignment',
            name: 'PretaskAssignment',
            meta: {
                title: '资格预审评标设置',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'taskAssignment' */ '@/views/srm/bidding_new/BiddingHall/BidEvaluationManagement/taskAssignment')
        },
        {
            path: '/biddingHall/BidEvaluationManagement/taskAssignment',
            name: 'taskAssignment',
            meta: {
                title: '评标设置',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'taskAssignment' */ '@/views/srm/bidding_new/BiddingHall/BidEvaluationManagement/taskAssignment')
        },
        {
            path: '/biddingHall/BidEvaluationManagement/FirstTaskAssignment',
            name: 'FirstTaskAssignment',
            meta: {
                title: '第一步评标设置',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'taskAssignment' */ '@/views/srm/bidding_new/BiddingHall/BidEvaluationManagement/taskAssignment')
        },
        {
            path: '/biddingHall/BidEvaluationManagement/SecondTaskAssignment',
            name: 'SecondTaskAssignment',
            meta: {
                title: '第二步评标设置',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'taskAssignment' */ '@/views/srm/bidding_new/BiddingHall/BidEvaluationManagement/taskAssignment')
        },
        {
            path: '/biddingHall/BidWinning/WinningCandidatePublicity',
            name: 'WinningCandidatePublicity',
            meta: {
                title: '中标公示',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'WinningCandidatePublicity' */ '@/views/srm/bidding_new/BiddingHall/BidWinning/WinningCandidatePublicity')
        },
        {
            path: '/biddingHall/BidWinning/DetermineTheWinner',
            name: 'DetermineTheWinner',
            meta: {
                title: '确定中标人',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'DetermineTheWinner' */ '@/views/srm/bidding_new/BiddingHall/BidWinning/DetermineTheWinner')
        },
        {
            path: '/biddingHall/BidWinning/BidWinningAnnouncement',
            name: 'BidWinningAnnouncement',
            meta: {
                title: '中标公告',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'BidWinningAnnouncement' */ '@/views/srm/bidding_new/BiddingHall/BidWinning/BidWinningAnnouncement')
        },
        {
            path: '/biddingHall/BidWinning/ReDetermineTheWinningBidderList',
            name: 'ReDetermineTheWinningBidderList',
            meta: {
                title: '重新确定中标单位',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'ReDetermineTheWinningBidder' */ '@/views/srm/bidding_new/BiddingHall/BidWinning/ReDetermineTheWinningBidderList')
        },
        {
            path: '/biddingHall/BidWinning/BidWinningNotice',
            name: 'BidWinningNotice',
            meta: {
                title: '中标通知书',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'BidWinningNotice' */ '@/views/srm/bidding_new/BiddingHall/BidWinning/BidWinningNotice')
        },
        {
            path: '/biddingHall/BidWinning/BidRejectionNotice',
            name: 'BidRejectionNotice',
            meta: {
                title: '落标通知书',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'BidRejectionNotice' */ '@/views/srm/bidding_new/BiddingHall/BidWinning/BidRejectionNotice')
        },
        {
            path: '/biddingHall/BidWinning/PreBidEvaluationResultDetail',
            name: 'PreBidEvaluationResultDetail',
            meta: {
                title: '资格预审评标结果',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BidEvaluationManager/modules/BidEvaluationResultDetail')
        },
        {
            path: '/biddingHall/BidWinning/BidEvaluationResultDetail',
            name: 'BidEvaluationResultDetail',
            meta: {
                title: '评标结果',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BidEvaluationManager/modules/BidEvaluationResultDetail')
        },
        {
            path: '/biddingHall/BidWinning/FirstBidEvaluationResultDetail',
            name: 'FirstBidEvaluationResultDetail',
            meta: {
                title: '第一步评标结果',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BidEvaluationManager/modules/BidEvaluationResultDetail')
        },
        {
            path: '/biddingHall/BidWinning/SecondBidEvaluationResultDetail',
            name: 'SecondBidEvaluationResultDetail',
            meta: {
                title: '第二步评标结果',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BidEvaluationManager/modules/BidEvaluationResultDetail')
        },
        {
            path: '/biddingHall/MarginManagerment/MarginManagermentHome',
            name: 'MarginManagermentHome',
            meta: {
                title: '保证金管理',
                titleI18nKey: 'i18n_field_siHRv_a7253812',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/MarginManagement/MarginManagermentHome')
        },
        {
            path: '/biddingHall/PaymentRecordsView/PaymentRecordsView',
            name: 'PaymentRecordsView',
            meta: {
                title: '保证金管理缴纳记录查看',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/PaymentRecordsView/PaymentRecordsView')
        },
        {
            path: '/biddingHall/RefundRecord/RefundRecordView',
            name: 'RefundRecordView',
            meta: {
                title: '保证金管理退款记录查看',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/RefundRecord/RefundRecordView')
        },
        {
            path: '/biddingHall/Deduction/DeductionView',
            name: 'DeductionView',
            meta: {
                title: '保证金管理抵扣记录查看',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/Deduction/DeductionView')
        },
        {
            path: '/biddingHall/DunningRecordsView/DunningRecordsView',
            name: 'DunningRecordsView',
            meta: {
                title: '保证金管理催缴记录查看',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/DunningRecordsView/DunningRecordsView')
        },
        {
            path: '/biddingHall/MarginManagementConfirm/MarginManagementConfirmView',
            name: 'MarginManagementConfirmView',
            meta: {
                title: '保证金管理缴纳记录确认',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/MarginManagementConfirm/MarginManagementConfirmView')
        },
        {
            path: '/biddingHall/BidServiceFee/BidServiceList',
            name: 'BidServiceList',
            meta: {
                title: '服务费管理',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/BidServiceFee/BidServiceList')
        },
        {
            path: '/biddingHall/Aberrant/BiddingTerminated',
            name: 'BiddingTerminated',
            meta: {
                title: '招标终止',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/Aberrant/BiddingTerminated')
        },
        {
            path: '/biddingHall/Aberrant/TenderAgain',
            name: 'TenderAgain',
            meta: {
                title: '重新招标',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/Aberrant/TenderAgain')
        },
        {
            path: '/biddingHall/FileAchive/FileList',
            name: 'FileList',
            meta: {
                title: '文件归档',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/FileAchive/FileList')
        },
        {
            path: '/biddingHall/OpenBidRecordEnter',
            name: 'OpenBidRecordEnter',
            meta: {
                title: '开标记录录入',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/OpenTenderRecordEnter/OpenBidRecordEnter')
        },
        {
            path: '/biddingHall/PreOpenBidRecordEnter',
            name: 'PreOpenBidRecordEnter',
            meta: {
                title: '预审开标记录录入',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/OpenTenderRecordEnter/OpenBidRecordEnter')
        },
        {
            path: '/biddingHall/EvalutionRecordEnter',
            name: 'EvalutionRecordEnter',
            meta: {
                title: '评标记录录入',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/BidEvalutionRecordEnter/EvalutionRecordEnter')
        },
        {
            path: '/biddingHall/EvalutionRecordEnter',
            name: 'EvalutionRecordEnter',
            meta: {
                title: '预审评标记录录入',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/BidEvalutionRecordEnter/EvalutionRecordEnter')
        },
        {
            path: '/biddingHall/ControlledPrice',
            name: 'ControlledPrice',
            meta: {
                title: '控制价管理',
                keepAlive: false
            },
            component: () => import('@/views/srm/bidding_new/BiddingHall/BiddingNotice/ControlledPrice')
        }
    ]


export default TenderRouter