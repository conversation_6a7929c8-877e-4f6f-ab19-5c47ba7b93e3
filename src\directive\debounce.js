import { isFunction, isObject, makeMap } from '@/utils/util.js'
import { debounce } from 'lodash'
const directives = {}
// 存储指令所需参数
let eventParams = {}
// 支持事件对象
const hasEventKey = makeMap('click, dblclick, keyup, keydown, keypress, mouseup, mousedown, mouseover, mouseleave, scroll')

directives.install = function (Vue) {
    Vue.directive('debounce', {
        bind (el, binding, vnode) {
            let defaultConfig = initEventParams(binding)
            eventParams = defaultConfig
            bindElementEvent(el, vnode.context, 'debounce')
        },
        update (el, binding, vnode) {
            let defaultConfig = initEventParams(binding)
            eventParams = defaultConfig
            bindElementEvent(el, vnode.context, 'debounce')
        }
    })
}
// 初始化指令参数
function initEventParams (binding) {
    let defaultConfig = {
        method: '',
        event: 'click',
        args: null,
        wait: 200,
        modifiers: {}
    }
    let modifierList = Object.keys(binding.modifiers).filter(key => binding.modifiers[key])
    defaultConfig.modifiers = binding.modifiers
    if (modifierList.length > 0) { // 事件修饰符
        let eventArr = modifierList.filter(mod => hasEventKey(mod))
        defaultConfig.event = eventArr.length === 0 ? 'click' : modifierList[0]
    }
    if (isObject(binding.value)) {
        Object.assign(defaultConfig, binding.value)
    } else if (isFunction(binding.value)) {
        defaultConfig.method = binding.expression
    }
    return defaultConfig
}

function bindElementEvent (el, context, type) {
    let {method, event, args, wait, modifiers} = eventParams
    let fireEvents = {
        leading: false,
        trailing: true
    }
    // 事件修饰符 before(防抖延迟前触发) all(防抖延迟前后都触发) 日后可拓展
    if (modifiers.before) {
        fireEvents = {
            leading: true,
            trailing: false
        }
    }
    if (modifiers.all) {
        fireEvents = {
            leading: true,
            trailing: true
        }
    }
    el[`on${event}`] = debounce(handleBindingEvent, wait, fireEvents)

    function handleBindingEvent (e) {
        if (modifiers.stop) e.stopPropagation()
        if (modifiers.prev) e.preventDefault()
        if (isFunction(context[method])){ // 组件内方法
            pushParams(context[method], e)
        } else if (typeof(method) === 'function') { 
            pushParams(method, e)
        } else {
            console.warn(`${method} it's not a method！`)
        }
    }
    function pushParams (method, e) {
        if (Array.isArray(args)) {
            // 兼容现有的方法，往最后压一个event
            args.push(e)
            method.call(null, ...args)
        } else {
            method.call(null, args, e)
        }
    }

}

export default directives