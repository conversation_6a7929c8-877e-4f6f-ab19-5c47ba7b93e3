<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
    <field-select-modal ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction } from '@/api/manage'
export default {
    name: 'ElsBarcodeShortCodeDetail',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'esignEnterpriseCertification',
            pageData: {
                form: {
                    loadingCompany: '',
                    companyCode: '',
                    companyName: '',
                    subAccount: '',
                    accountId: '',
                    idNumber: '',
                    idType: '',
                    orgLegalIdNumber: '',
                    orgLegalName: '',
                    authType: ''
                },
                groups: [

                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/base/barcode/elsBarCodeShortCode/queryById'

            }
        }
    },
    computed: {
        fileSrc () {
            console.log(this.currentEditRow)
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let busAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${busAccount}/purchase_barCodeShortCode_${templateNumber}_${templateVersion}.js?t=` + time

        }
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if (this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            } else {
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>