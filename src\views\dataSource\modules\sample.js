

// ##############  js 脚本案例:  ####################
export const javaScriptContent = `function dataTransform(data){
    //自定义脚本内容
    return data;
}`
// ##############  自定义校验规则js:  ####################
export const validationRulesContent = `function verification(data) { 
    //自定义脚本内容
    //可返回true/false单纯校验键入的data正确性
    //可返回文本，实时替换,比如当前时间等
    //return "2099-01-01 00:00:00";
    return ture
}`
// ##############  javaBean 脚本案例:  ####################
export const javaBeanContent = 'package com;\n' +
  '\n' +
  'import com.alibaba.fastjson.JSONObject;\n' +
  'import com.els.modules.dataSource.service.IGroovyHandler;\n' +
  '\n' +
  'import java.util.List;\n' +
  '\n' +
  '/**\n' +
  ' * 建议在idea写好复制整个类到此处，位置report-core/src/test/java/com/DemoGroovyHandler.java\n' +
  ' */\n' +
  'public class DemoGroovyHandler implements IGroovyHandler {\n' +
  '\n' +
  '    @Override\n' +
  '    public List<JSONObject> transform(List<JSONObject> data) {\n' +
  '\n' +
  '        return data;\n' +
  '    }\n' +
  '}'

export const javaDictContent = '{model:{"a":"测试a","b":"测试b"}}'

