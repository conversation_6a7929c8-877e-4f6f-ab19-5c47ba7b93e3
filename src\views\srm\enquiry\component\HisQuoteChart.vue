<template>
  <a-modal
    v-drag    
    v-if="showVisible"
    :visible="showVisible"
    :width="800"
    :height="160"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_historicalPriceTrendChart`, '历史价格趋势图')"
    :footer="null"
    @cancel="showVisible=false">
    <!-- <div>
      <vue-echarts
        ref="priceEchart"
        autoresize
        theme="light"
        :options="options"
        :auto-resize="true" />
    </div> -->
    <div
      v-if="showVisible"
      id="his-chart"
      style="height: 330px; width: 700px" />
  </a-modal>
</template>
<script>
import { postAction } from '@/api/manage'
import * as echarts from 'echarts'

export default {
    name: 'HisQuoteChart',
    data () {
        return {
            chart: null,
            showVisible: false,
            options: {
                // title: { text: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_historicalPriceTrendChart`, '历史价格趋势图') },
                tooltip: { trigger: 'item' },
                legend: { type: 'scroll', top: '24', data: [] },
                grid: { top: '90', left: '5%', bottom: '5%', containLabel: true }, // , width: '100%'
                toolbox: {
                    feature: {
                        magicType: {
                            type: ['line', 'bar'],
                            title: {
                                line: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_FSLNWP_96e1d8f8`, '切换为折线图'),
                                bar: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_FSLdzP_96f476fa`, '切换为柱状图')
                            }
                        },
                        restore: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_Sj_11bec7`, '还原') },
                        saveAsImage: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_sMLPO_902181c8`, '保存为图片') }
                    }
                },
                xAxis: {
                    name: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_date`, '日期'),
                    type: 'category',
                    boundaryGap: true,
                    data: []
                },
                yAxis: {
                    name: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
                    type: 'value'
                },
                series: []
            },
            materialNumber: '',
            toElsAccountList: []
        }
    },
    methods: {
        initChart () {
            this.chart = echarts.init(document.getElementById('his-chart'))
            this.chart.setOption(this.options)
        },
        clear () {
            this.chart && this.chart.clear()
        },
        open (records) {
            this.clear()
            let toElsAccountList = []
            records.forEach(item => {
                toElsAccountList.push(item.toElsAccount)
            })
            this.materialNumber = records[0].materialNumber
            this.toElsAccountList = toElsAccountList
            this.getData()
        },
        async getData () {
            let param = {'materialNumber': this.materialNumber, 'toElsAccountList': this.toElsAccountList}
            //查询历史价格趋势图
            const res = await postAction('/price/purchaseInformationRecords/queryPriceByMaterial', param)
            let { result } = res
            const { legendData = [], series = [], xAxisData = [], xaxisData = [] } = result || {}
            this.options.legend.data = legendData
            this.options.series = series
            this.options.xAxis.data = xAxisData.length > 0 ? xAxisData : xaxisData
            this.showVisible = true
            this.$nextTick(() => {
                this.initChart()
            })
        }
    }
}
</script>