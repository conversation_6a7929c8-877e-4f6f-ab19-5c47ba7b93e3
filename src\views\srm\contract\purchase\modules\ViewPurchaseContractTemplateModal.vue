<template>
  <div class="els-page-comtainer">
    <a-spin :spinning="confirmLoading">
      <a-page-header
        :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_neIrdV_36ee8aa1`, '合同模板详情') "
      >
        <template slot="extra">
          <a-button
            type="default"
            style="margin-left:8px"
            v-if="auditShow"
            slot="tabBarExtraContent"
            @click="submitAudit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_examineApprove`, '审批') }}
          </a-button>
          <a-button
            type="default"
            style="margin-left:8px"
            v-if="cancelAuditShow"
            slot="tabBarExtraContent"
            @click="cancelAudit">{{
              $srmI18n(`${$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批')
            }}
          </a-button>
          <a-button
            type="default"
            style="margin-left:8px"
            v-if="showFlowShow"
            slot="tabBarExtraContent"
            @click="showFlow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_viewProcess`, '查看流程') }}
          </a-button>
          <a-button
            type="default"
            style="margin-left:8px"
            slot="tabBarExtraContent"
            @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}
          </a-button>
        </template>
      </a-page-header>
      <a-collapse
        v-model="activeKey"
        expandIconPosition="right">
        <a-collapse-panel
          key="collapse_panel_1"
          :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
        >
          <a-row
            style="margin-bottom:-12px;color:#222"
            :gutter="24">
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">{{
                    $srmI18n(`${$getLangAccount()}#i18n_title_contractTemplateNo`, '合同模板编号')
                  }}:
                </a-col>
                <a-col :span="15">{{ form.contractTemplateNumber }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_field_contractTemplateVersion`, '合同模板版本') }}:
                </a-col>
                <a-col :span="15">{{ form.contractTemplateVersion }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right"><span class="item-required-icon">*</span>{{
                    $srmI18n(`${$getLangAccount()}#i18n_field_contractTemplateName`, '合同模板名称')
                  }}:
                </a-col>
                <a-col :span="15">
                  <div class="item-required-text">{{ form.contractTemplateName }}</div>
                </a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right"><span class="item-required-icon">*</span>{{
                    $srmI18n(`${$getLangAccount()}#i18n_title_contractTemplateType`, '合同模板类型')
                  }}:
                </a-col>
                <a-col :span="15">
                  <div class="item-required-text">{{ form.templateType_dictText }}</div>
                </a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">{{
                    $srmI18n(`${$getLangAccount()}#i18n_title_contractTemplateDesc`, '合同模板描述')
                  }}:
                </a-col>
                <a-col :span="15">{{ form.templateDesc }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">{{
                    $srmI18n(`${$getLangAccount()}#i18n_title_contractApprovalStatus`, '合同审批状态')
                  }}:
                </a-col>
                <a-col :span="15">{{ form.auditStatus_dictText }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_field_company`, '公司') }}:
                </a-col>
                <a-col :span="15">{{ form.company_dictText }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">{{
                    $srmI18n(`${$getLangAccount()}#i18n_massProdHeadc98_purchaseOrgCode`, '采购组织')
                  }}:
                </a-col>
                <a-col :span="15">{{ form.purchaseOrg_dictText }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_field_purchaseGroupCode`, '采购组') }}:
                </a-col>
                <a-col :span="15">{{ form.purchaseGroup_dictText }}</a-col>
              </a-row>
            </a-col>
          </a-row>
        </a-collapse-panel>
        <a-collapse-panel
          key="collapse_panel_2"
          :header="$srmI18n(`${$getLangAccount()}#i18n_title_lineItem`, '行项目')"
        >
          <vxe-grid
            border
            ref="scoreGrid"
            show-overflow
            size="small"
            height="300"
            :edit-config="{trigger: 'click', mode: 'cell'}"
            :columns="itemColumns"
            :data="itemTableData">
          </vxe-grid>
        </a-collapse-panel>
        <a-collapse-panel
          key="collapse_panel_3"
          :header="$srmI18n(`${$getLangAccount()}#i18n_title_templatePreview`, '模板预览')"
        >
          <div
            style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
            v-html="checkEditContractTemplate(contractTemplate)"></div>
        </a-collapse-panel>
      </a-collapse>
    </a-spin>
    <!-- <a-modal
            v-drag
              centered
              :width="960"
              :maskClosable="false"
              :visible="flowView"
              @ok="closeFlowView"
              @cancel="closeFlowView">
              <iframe
                style="width:100%;height:560px"
                title=""
                :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
                frameborder="0"></iframe>
            </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <view-item-diff-modal ref="viewDiffModal"/>
    <His-Contract-Item-Modal ref="hisContractItemModal"/>
  </div>
</template>
<script lang="jsx">
import {getAction, httpAction} from '@/api/manage'
import ViewItemDiffModal from './ViewTemplateItemDiffModal'
import HisContractItemModal from './HisPurchaseContractTemplateItemModal'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'ViewContractTemplate',
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    components: {
        flowViewModal,
        ViewItemDiffModal,
        HisContractItemModal
    },
    data () {
        return {
            flowId: '',
            flowView: false,
            cancelAuditShow: false,
            auditShow: false,
            row: {},
            showFlowShow: false,
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            activeKey: ['collapse_panel_1', 'collapse_panel_2', 'collapse_panel_3'],
            fixPageHeader: false,
            contractTemplate: '',
            form: {},
            itemTableData: [],
            itemColumns: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                    field: 'itemNumber',
                    width: 80
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                    field: 'itemType_dictText',
                    width: 100
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                    field: 'itemName',
                    align: 'left',
                    width: 300
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_itemVersion`, '项目版本'),
                    field: 'itemVersion',
                    width: 100
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isChangeFlag`, '是否更改'),
                    field: 'changeFlag',
                    width: 120,
                    editRender: {
                        name: 'mSwitch',
                        type: 'visible',
                        props: {closeValue: '0', openValue: '1', disabled: true}
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 100,
                    align: 'left',
                    slots: {
                        default: ({row}) => {
                            let resultArray = []
                            resultArray.push(<a
                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                onClick={() => this.viewDetail(row)}>
                                <a-icon type="profile"></a-icon>
                            </a>)
                            if (row.changeFlag == '1') {
                                resultArray.push(<a title="比对" style="margin-left:8px"
                                    onClick={() => this.viewDiff(row)}>
                                    <a-icon type="diff"></a-icon>
                                </a>)
                            }
                            return resultArray
                        }
                    }
                }
            ]
        }
    },
    created () {
        this.getContractTemplateDetail()
    },
    methods: {
        // 检查是否可编辑-增加可编辑区域
        checkEditContractTemplate (content) {
            content = content.replace(/\$\[edit]/g, '<span style="min-width: 100px; background-color:#e5e5e5;display: inline-block;" contenteditable="true"> </span>')
            return content
        },
        initBtn () {
            if (this.row.auditStatus == '1') {
                this.cancelAuditShow = true
            } else {
                this.cancelAuditShow = false
            }
            if (this.row.auditStatus == '0') {
                this.auditShow = true
            } else {
                this.auditShow = false
            }
            if (this.row.flowId) {
                this.showFlowShow = true
            }
        },
        goBack () {
            this.$emit('hide')
        },
        getContractTemplateDetail () {
            let url = '/contract/purchaseContractTemplateHead/queryById'
            if (this.currentEditRow['viewType'] == 'his') {
                url = '/contract/purchaseContractTemplateHeadHis/queryById'
            }
            getAction(url, {id: this.currentEditRow.id}).then(res => {
                if (res.success) {
                    Object.assign(this.form, res.result)
                    this.row = res.result
                    let content = ''
                    let itemList = []
                    if (this.currentEditRow['viewType'] == 'his') {
                        itemList = res.result.purchaseContractTemplateItemHisList
                        content = res.result.purchaseContractTemplateItemHisList.map(item => {
                            return item.itemContent
                        })
                    } else {
                        itemList = res.result.purchaseContractTemplateItemList
                        content = res.result.purchaseContractTemplateItemList.map(item => {
                            return item.itemContent
                        })
                    }
                    this.$refs.scoreGrid.loadData(itemList)
                    this.contractTemplate = content.join('')
                    this.initBtn()
                }
            })
        },
        showFlow () {
            if (!this.currentEditRow.flowId) {
                this.$set(this.currentEditRow, 'flowId', this.form.flowId)
                this.$forceUpdate()
            }
            this.flowId = this.currentEditRow.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        cancelAudit () {
            let auditStatus = this.row.auditStatus
            if (auditStatus != '1') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvacCannotrevokedCurrenStatus`, '当前状态不能撤销审批！'))
                return
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postData('/a1bpmn/audit/api/cancel')
                }
            })
        },
        submitAudit () {
            let auditStatus = this.row.auditStatus
            if (auditStatus == '1' || auditStatus == '2') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentStatusCannotSubmittedApproval`, '当前状态不能提交审批！'))
                return
            }
            if (this.row.purchaseContractTemplateItemList.length <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSucdIW_8572587c`, '请添加行项目！'))
                return
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApproval`, '确认提交审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApprovalTips`, '是否确认提交审批?'),
                onOk: function () {
                    that.postData('/a1bpmn/audit/api/submit')
                }
            })
        },
        postData (invokeUrl) {
            this.confirmLoading = true
            let formData = this.row
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'contractTemplate'
            param['auditSubject'] = '合同模板编号：' + formData.contractTemplateNumber + ' ' + formData.contractTemplateName || ''
            param['params'] = JSON.stringify(formData)
            httpAction(invokeUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.getContractTemplateDetail()
                    this.initBtn()
                    this.$parent.init()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        }
    }
}
</script>
<style lang='less' scoped>

.ant-col {
    font-size: 13px;
}

.item-required-icon {
    color: #f5222d;
    font-size: 14px;
}

.item-required-text {
    background-color: #fff9f7;
    border: 1px solid #fdaf96;
    border-radius: 2px;
    color: #454f59;
    line-height: 13px;
    padding: 8px 8px;
    min-height: 28px;
}

</style>