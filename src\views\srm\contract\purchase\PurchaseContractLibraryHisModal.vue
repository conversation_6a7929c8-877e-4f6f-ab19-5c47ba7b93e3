<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"/>
    <viewPurchaseContractLibraryHis-modal
      :current-edit-row="currentEditRow"
      ref="viewPurchaseContractLibraryHisModal"
    />
  </div>
</template>

<script>
import viewPurchaseContractLibraryHisModal from './modules/viewPurchaseContractLibraryHisModal'
import {ListMixin} from '@comp/template/list/ListMixin'

export default {
    name: 'PurchaseContractLibraryHisModal',
    mixins: [ListMixin],
    components: {
        'viewPurchaseContractLibraryHis-modal': viewPurchaseContractLibraryHisModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                        type: '',
                        icon: '',
                        clickFn: this.goBack
                    }
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                        fieldName: 'itemType',
                        dictCode: 'srmItemType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectItemType`, '请选择项目类型')
                    }
                ],
                form: {
                    itemId: ''
                },
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleShow
                    }
                ]
            },
            url: {
                list: '/contract/purchaseContractLibraryHis/list',
                columns: 'purchaseContractLibraryList'
            }
        }
    },
    computed: {},
    created () {
        let itemNumber = this.currentEditRow.id
        this.pageData.form.itemNumber = itemNumber
    },
    methods: {
        handleShow (row) {
            //this.showEditPage = true
            /*this.$nextTick(() => {

            })*/
            this.$refs.viewPurchaseContractLibraryHisModal.open(row)

        },
        goBack () {
            this.$emit('hide')
        }
    }
}
</script>
