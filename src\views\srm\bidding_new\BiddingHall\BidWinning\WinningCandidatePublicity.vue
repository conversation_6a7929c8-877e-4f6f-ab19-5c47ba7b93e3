<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <content-header
        v-if="showHeader"
        :btns="btns"
      />
      <div
        class="container"
        :style="style">
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBSiLRt_a1dce8d8`, '中标候选人名单') }}</span>
          </titleTrtl>
          <vxe-grid
            v-bind="gridConfig"
            :height="250"
            ref="table"
            :data="tableData"
            :columns="tableColumns"
            show-overflow="title" >
          </vxe-grid>
        </div>
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_basicInfo`, '基本信息') }}</span>
          </titleTrtl>
          <div>
            <Dataform
              ref="dataform"
              :formData="formData"
              :pageStatus="pageStatus"
              :validateRules="validateRules"
              :fields="fields" />
          </div>
          <div>
            <j-editor
              ref="ueditor"
              :disabled="pageStatus== 'detail'"
              v-model="formData.publicityContent" />
          </div>
        </div>
      </div>
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"/>
    </a-spin>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '../components/content-header'
import Dataform from '../components/Dataform'
import titleTrtl from '../components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import JEditor from '@/components/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
export default {
    mixins: [baseMixins],
    name: 'WinningCandidatePublicity',
    components: {
        JEditor,
        Dataform,
        titleTrtl,
        ContentHeader,
        flowViewModal
    },
    mixins: [tableMixins],
    data () {
        return {
            ifRequired: false,
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    'type': 'seq'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQSiL_c7729673`, '是否候选人'),
                    'field': 'candidateStatus',
                    slots: {
                        default: ({row, column}) => {
                            return [
                                <a-checkbox disabled={this.pageStatus == 'detail'} v-model={row[column.property]} ></a-checkbox>
                            ]
                        }
                    }
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            fields: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRK_2fb96f65`, '是否公示'),
                    fieldLabelI18nKey: '',
                    field: 'publicity',
                    defaultValue: '0',
                    fieldType: 'select',
                    dictCode: 'yn'
                },  
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKvKKI_c9b04117`, '公示开始时间'),
                    fieldLabelI18nKey: '',
                    field: 'publicityBeginTime',
                    dataFormat: 'YYYY-MM-DD HH:mm:ss',
                    defaultValue: '',
                    fieldType: 'dateTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKyRKI_cb627d84`, '公示截止时间'),
                    fieldLabelI18nKey: '',
                    field: 'publicityEndTime',
                    defaultValue: '',
                    dataFormat: 'YYYY-MM-DD HH:mm:ss',
                    fieldType: 'dateTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKBD_26d76b3f`, '公示标题'),
                    fieldLabelI18nKey: '',
                    field: 'publicityTitle',
                    defaultValue: '',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFRKvL_6e31b8d6`, '请选择公示范围'),
                    fieldLabelI18nKey: '',
                    field: 'publicityScope',
                    defaultValue: '',
                    dictCode: 'srmNoticeScope',
                    filterSelectList: [this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RIRdX_9e53ebba`, '指定供应商')],
                    fieldType: 'select'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQUz_2fba950f`, '是否审批'),
                    fieldLabelI18nKey: '',
                    field: 'audit',
                    fieldType: 'select',
                    dictCode: 'yn'
                }
            ],
            // btns: [
            //     { title: '保存', type: 'primary', click: this.save },
            //     { title: '发布', type: 'primary', click: this.submit }
            // ],
            url: {
                queryById: '/tender/purchaseTenderProjectBidWinningCandidatePublicity/queryBidWinningCandidate',
                add: '/tender/purchaseTenderProjectBidWinningCandidatePublicity/add',
                edit: '/tender/purchaseTenderProjectBidWinningCandidatePublicity/edit',
                submit: '/tender/purchaseTenderProjectBidWinningCandidatePublicity/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    watch: {
        'formData.publicity': {
            handler (newV, oldV) {
                // 是否必填
                this.ifRequired = (newV == '1') 
                // this.validateRules.publicity[0]['required'] = (newV == '1') 
                console.log(this.ifRequired)
            },
            deep: true
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId', 
        'resetCurrentSubPackage'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        btns () {
            let btn = []
            // 非当前执行人，不允许操作，不给按钮
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                btn = []
                return btn
            }
            if (this.formData.status == '0' || !this.formData.status) {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.submit }
                ]
            }

            // if (!this.formData.status) {
            //     return btn
            // }

            if(this.formData.status == '1' && this.formData.auditStatus == '1'){
                btn = [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                        type: 'primary',
                        click: this.cancelAudit
                    }
                ]
            }
            if(this.formData.audit == '1' && this.formData.status != '0' && this.formData.flowId != null){
                btn.push(
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                        type: 'primary',
                        click: this.showFlow
                    }
                )
            }
            return btn
        },
        pageStatus () {
            console.log('this.tenderCurrentRow', this.tenderCurrentRow)
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                return 'detail'
            } else {
                if (this.formData.status == '0' || !this.formData.status) {
                    return 'edit'
                } else {
                    return 'detail'
                }
            }
        },
        validateRules (){
            let rules = {}
            rules={
                publicity: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRKxOLVW_59e4e5cc`, '是否公示不能为空')}],
                publicityBeginTime: [{required: this.ifRequired, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RKvKKIxOLV_2c54e1a7`, '公示开始时间不能为空')}],
                publicityEndTime: [{required: this.ifRequired, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RKyRKIxOLVW_b89ad30d`, '公示截止时间不能为空')}],
                publicityTitle: [{required: this.ifRequired, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RKBDxOLVW_114c4232`, '公示标题不能为空')}],
                publicityScope: [{required: this.ifRequired, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RKvLxOLVW_83180b52`, '公示范围不能为空')}],
                audit: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQUzxOLV_1ca4799f`, '是否审批不能为空')}]
            }
            return rules
            
        }
    },
    methods: {
        async save () {
            let params = await this.$refs.dataform.externalAllData()
            params['bidWinningCandidateIds'] = this.tableData.filter(item => {
                return item.candidateStatus
            }).map(item => item.id)
            params['subpackageId'] = this.subId
            params['tenderProjectId'] = this.tenderCurrentRow.id
            let url = params.id ? this.url.edit : this.url.add
            valiStringLength(params, [
                {field: 'publicityTitle', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKBD_26d76b3f`, '公示标题'), maxLength: 100},
                {field: 'publicityContent', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKCc_26d474a2`, '公示内容'), maxLength: 1000}
            ])
            this.confirmLoading = true
            postAction(url, params).then(res => {
                let type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        async submit () {
           

            let params = await this.$refs.dataform.externalAllData()
            params['bidWinningCandidateIds'] = this.tableData.filter(item => {
                return item.candidateStatus
            }).map(item => item.id)
            params['subpackageId'] = this.subId
            params['tenderProjectId'] = this.tenderCurrentRow.id
            valiStringLength(params, [
                {field: 'publicityTitle', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKBD_26d76b3f`, '公示标题'), maxLength: 100},
                {field: 'publicityContent', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKCc_26d474a2`, '公示内容'), maxLength: 1000}
            ])
            this.confirmLoading = true
            postAction(this.url.submit, params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.getData()
                }
            }).catch(err => {
                this.$message.error(err)
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        getData () {
            let params = {
                subpackageId: this.subId
            }
            this.confirmLoading = true
            getAction(this.url.queryById, params).then(res => {
                if(res.success) {
                    let {bidWinningCandidateVOList = [], ...others} = res.result || {}
                    this.tableData = bidWinningCandidateVOList && bidWinningCandidateVOList.map(item => {
                        item.candidateStatus = item.candidateStatus != '0' ? true : false
                        return item
                    })
                    this.formData = others
                }
            }).finally(() => {
                this.confirmLoading = false
                // this.$emit('resetCurrentSubPackage')
                this.resetCurrentSubPackage()
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
        },
        cancelAudit () {
            let that = this
            let params = this.formData
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit, params)
                    // that.$refs.businessRefName.loadData()
                }
            })
            

        },
        showFlow (){
            console.log('jinlaile')
            this.flowId = this.formData.flowId
            this.flowView = true
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'barcodeInfoAudit'
            param['auditSubject'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_TotUzWtyW_1dc23b59`, '条码单审批，单号：') + formData.trialNumber
            param['params'] = JSON.stringify(formData)
            this.confirmLoading = true
            postAction(invokeUrl, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.init()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    },
    mounted () {
        this.init()
    }
}
</script>
<style lang="less" scoped>
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
</style>




