<template>
  <div class="edit-page">
    <a-spin
      v-if="!reviewStatus && !scoringStatus && !reviewScoringStatus && !priceScoreStatus && !priceComparisonStatus && !showQuotes && !showOpenBid"
      :spinning="confirmLoading">
      <div class="page-header">
        <a-steps
          :current="currentStep"
          size="small">
          <template v-for="step in stepList">
            <a-step
              :key="step.id"
              :title="step.name" />
          </template>
        </a-steps>
      </div>
      <div class="page-content">

        <titleCrtl>
          <span>{{ titleFn }}</span>

          <template slot="right">

            <a-button
              class="m-l-10"
              @click="handleEvaluationClarify"
              v-if="titleFn == pingbus"
              style="float: right">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBLV_411d1f04`, '评标澄清') }}</a-button>
            <a-button
              @click="handleOpenBid"
              v-if="titleFn == pingbus"
              style="float: right">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_mAvBVH_43588bb`, '查看开标信息') }}</a-button>
          </template>
        </titleCrtl>

        <div class="page-content-grid">
          <template v-for="(grid, index) in gridList">
            <vxe-grid
              :loading="loading"
              :ref="grid.gridCode"
              :key="grid.gridCode"
              v-show="index == currentStep"
              :height="setGridHeight"
              @checkbox-all="checkedAll"
              :data="grid.gridData"
              :columns="grid.columns"
              v-bind="gridConfig"
            >
              <template
                #expert_default="{ row }"
                v-if="index == 0">
                <span>{{ row.expert }}</span>
                <span
                  v-if="row.judgesGroupLeader == '1'"
                  style="color: blue; margin-left: 5px">{{ $srmI18n(`${$getLangAccount()}#i18n_field_[Ve]_2175647`, '[组长]') }}</span>
              </template>
              <template #data_default="{ row, column }">
                <span v-if="row.invalid == '1'">/</span>
                <span v-else>{{ row[column['property']] }}</span>
              </template>
              <template #checkbox_default="{ row }">
                <!-- v-model="row.checkboxValue" -->
                <vxe-checkbox
                  v-if="row.invalid != '1'"
                  @change="checkBoxChange(row, $event)"
                  v-model="row.candidate"
                  checked-value="1"
                  unchecked-value="0"
                  :disabled="(row.invalid == '1' || !leaderStatus) && currentEditRow.judgesGroupLeader != '1'"></vxe-checkbox>
              </template>
              <template
                #file_default="{ row }"
                v-if="index == 2">
                <span v-if="row.fileType == '1'">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBBm_41201cd7`, '评标表格') }}</span>
                <span v-if="row.fileType == '2'">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBsx_411b7648`, '评标报告') }}</span>
              </template>
              <template
                #cz_default="{ row, rowIndex, $rowIndex }"
                v-if="index == 2">
                <a-button
                  v-if="leaderStatus || currentEditRow.judgesGroupLeader == '1'"
                  type="link"
                  style="margin:0 4px"
                  @click="handleOnlineEdit(row)">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_field_KWAt_298a3612`, '在线编辑') }}
                </a-button>
                <a-upload
                  name="file"
                  v-if="leaderStatus || currentEditRow.judgesGroupLeader == '1'"
                  :showUploadList="false"
                  :action="uploadUrl"
                  :headers="uploadHeader"
                  :accept="accept"
                  :data="{ headId: currentEditRow.id, businessType: 'tender', fileType: row.fileType, 'sourceNumber': currentEditRow.tenderProjectNumber || currentEditRow.id || '', 'actionRoutePath': '/srm/bidder/BidEvaluationManagerList,采购商与供应商的路径逗号隔开' }"
                  :beforeUpload="beforeUpload"
                  @change="(file) => handleUploadChange(file, row, rowIndex)">
                  <a-button type="link">{{ $srmI18n(`${$getLangAccount()}#i18n_field_RyXV_37928547`, '直接上传') }}</a-button>
                </a-upload>
                <a-button
                  v-if="row.fileName"
                  type="link"
                  @click="preViewEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a-button>
                <a-button
                  v-if="row.fileName"
                  type="link"
                  @click="removeFiled(row, rowIndex, $rowIndex)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
              </template>
            </vxe-grid>
          </template>

        </div>
      </div>
      <div class="page-footer">
        <a-button
          :type="customPageFooterPreBtn.type"
          v-if="currentStep"
          @click="customPageFooterPreBtn.click">{{ customPageFooterPreBtn.title }}</a-button>
        <a-button
          :type="customPageFooterNextBtn.type"
          v-if="currentStep < stepList.length - 1"
          @click="customPageFooterNextBtn.click">{{ customPageFooterNextBtn.title }}</a-button>
        <a-button
          type="primary"
          v-if="currentEditRow.judgesGroupLeader == '1'"
          v-show="currentStep == stepList.length - 1"
          @click="evaluationEnd">{{ $srmI18n(`${$getLangAccount()}#i18n_dict_UByW_411ef2ef`, '评标结束') }}</a-button>
        <a-button
          @click="handleQuotes"
          v-if="currentEditRow.checkType == '1'"
          type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_field_OVsu_2aa78586`, '多轮报价') }}</a-button>
        <a-button @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </div>
    </a-spin>
    <!-- 在线编辑弹窗 -->
    <OnlineModal
      v-if="evaModalVisible"
      :evaModalVisible.sync="evaModalVisible"
      :row="row"
      :supplierList="supplierList"
      :root="root"></OnlineModal>
    <!-- 评审项 -->
    <ReviewItems
      v-if="reviewStatus"
      :currentRow="currentRow" />
    <!-- 评分项 -->
    <ScoringItem
      v-if="scoringStatus"
      :currentRow="currentRow" />
    <!-- 评审评分项 -->
    <ReviewScoringItem
      v-if="reviewScoringStatus"
      :currentRow="currentRow" />
    <!-- 价格评分项 -->
    <PriceScore
      v-if="priceScoreStatus"
      :currentRow="currentRow" />
    <PriceComparison
      v-if="priceComparisonStatus"
      :currentRow="currentRow"
    />

    <multipleQuotes
      v-if="showQuotes"
      :pageStatus="'edit'"
      :show="showQuotes"
      :currentEditRow="currentEditRow"
      :canMultipleQuotes="canMultipleQuotes" />

    <!-- 查看开标信息 -->
    <OpenBidMessage
      v-if="showOpenBid"
      :currentEditRow="currentEditRow"
    />
    <QuoteDetailsModal ref="QuoteDetailsModal" />
    <LeaderOpinionModal
      ref="LeaderOpinionModal"
      pageStatus="edit"
      typeNum="leader"/>
  </div>
</template>

<script lang="jsx">
import { getAction, postAction } from '@/api/manage'
// import { gridOptionsMixin } from '../public/gridOptionsMixin.js'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import ReviewItems from '../components/ReviewItems.vue'
import ScoringItem from '../components/ScoringItem.vue'
import ReviewScoringItem from '../components/ReviewScoringItem.vue'
import PriceScore from '../components/PriceScore.vue'
import PriceComparison from '../components/PriceComparison.vue'
import OpenBidMessage from '../components/OpenBidMessage.vue'
import QuoteDetailsModal from '../components/QuoteDetailsModal.vue'
import OnlineModal from '../components/OnlineModal.vue'
import LeaderOpinionModal from '../components/LeaderOpinionModal.vue'
import { USER_INFO } from '@/store/mutation-types'
import multipleQuotes from '../multipleQuotes/multipleQuotes.vue'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import JEditor from '@/components/els/JEditor'

export default {
    mixins: [tableMixins],
    computed: {
        titleFn () {
            let title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理')
            switch (this.currentStep) {
            case 1:
                title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUAR_40ed07d8`, '评审排名')
                break
            case 2:
                title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRnL_753bc075`, '评标结果材料')
                break
            default:
                title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理')
                break
            }
            return title
        },
        headParams () {
            let { checkType, currentStep, processType } = this.currentEditRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return { xNodeId }
        },
        canMultipleQuotes () {
            // 预审没有多轮报价
            if (this.formData && this.formData.stageQuote == '1' && this.currentEditRow.checkType == '1') {
                // 多轮报价发起人:0-全部，1-采购方，2-评标组长
                if (this.formData.stageQuoteOperator == '1') {
                    let { elsAccount } = this.$ls.get(USER_INFO)
                    // if (this.formData.purchaseExecutorAccount == elsAccount) return true
                    return (this.formData.purchaseExecutorAccount == elsAccount)
                } else if (this.formData.stageQuoteOperator == '2') {
                    // if (this.leaderStatus || this.currentEditRow.judgesGroupLeader == '1') return true
                    return (this.leaderStatus || this.currentEditRow.judgesGroupLeader == '1')
                } else {
                    return true
                }
            } else {
                return false
            }
        }
    },
    provide () {
        return {
            propsCurrentNode: () => this.propsCurrentNode
        }
    },
    watch: {
        reviewStatus (val) {
            if (!val) {
                this.getBidEvaluationData()
                this.getIsEvaFinish()
            }
        },
        scoringStatus (val) {
            if (!val) {
                this.getBidEvaluationData()
                this.getIsEvaFinish()
            }
        },
        reviewScoringStatus (val) {
            if (!val) {
                this.getBidEvaluationData()
                this.getIsEvaFinish()
            }
        },
        priceScoreStatus (val) {
            if (!val) {
                this.getBidEvaluationData()
                this.getIsEvaFinish()
            }
        },
        priceComparisonStatus (val) {
            if (!val) {
                this.getBidEvaluationData()
                this.getIsEvaFinish()
            }
        },
        showQuotes (val) {
            if (!val) {
                // 防止进入多轮报价后返回评审排名导致之前勾选的数据没有渲染上的问题；
                this.$nextTick(() => {
                    const GRIDOBJ = this.$refs.ReviewRanking[0]
                    GRIDOBJ && this.gridList[1]['gridData'].forEach(data => {
                        if (data.candidate === '1') {
                            GRIDOBJ.setCheckboxRow(data, true)
                        }
                    })
                })
            }
        }
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {}
        }
    },
    components: {
        ReviewItems,
        ScoringItem,
        ReviewScoringItem,
        PriceScore,
        multipleQuotes,
        titleCrtl,
        PriceComparison,
        OpenBidMessage,
        QuoteDetailsModal,
        LeaderOpinionModal,
        JEditor,
        OnlineModal
    },
    data () {
        return {
            pingbus: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理'),
            ifSave: true,
            supplierList: [],
            evaModalVisible: false,
            confirmLoading: false,
            setGridHeight: 300,
            showOpenBid: false,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            reviewStatus: false, // 评审项组件控制状态
            scoringStatus: false, // 评分项组件控制状态
            reviewScoringStatus: false, // 评审评分项组件控制状态
            priceScoreStatus: false, // 价格评分项
            priceComparisonStatus: false, // 价格排名比较项
            showQuotes: false, // 发起多轮报价
            loading: false,
            currentStep: 0,
            customPageFooterPreBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_prevStep`, '上一步'), type: 'primary', belong: 'preStep', click: this.prevStep },
            customPageFooterNextBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nextStep`, '下一步'), type: 'primary', belong: 'nextStep', click: this.nextStep },
            stepList: [
                { id: 1, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理') },
                { id: 2, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUAR_40ed07d8`, '评审排名') },
                { id: 3, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRnL_753bc075`, '评标结果材料') }
            ],
            gridList: [
                {
                    gridCode: 'BidEvaluation',
                    columns: [{ field: 'expert', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_evaluationExpert`, '评标专家'), slots: { default: 'expert_default' } }],
                    gridData: []
                },
                {
                    gridCode: 'ReviewRanking',
                    columns: [],
                    gridData: []
                },
                {
                    gridCode: 'BidEvaluationResults',
                    columns: [
                        { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 50 },
                        { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nLAc_30836479`, '材料类型'), slots: { default: 'file_default' } },
                        { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称') },
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), slots: { default: 'cz_default' } }
                    ],
                    gridData: [
                        { fileType: '1', fileName: '', id: '' },
                        { fileType: '2', fileName: '', id: '' }
                    ]
                }
            ],
            columns1: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), type: 'seq', width: 50 },
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBtL_2e61022a`, '投标单位') }
            ],
            columns2: [
                { field: 'weightScore', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_totalScore`, '总分'), slots: { default: 'data_default' } },
                { field: 'order', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'), slots: { default: 'data_default' } }
            ],
            currentRow: {},
            rankingColumns: [],
            leaderStatus: false, // 是否是组长
            isEvaFinish: false, // 是否全部完成评审
            formData: {},
            propsCurrentNode: {extend: {checkType: '1'}}
        }
    },
    async mounted () {
        console.log(this.currentEditRow)
        await this.getBidEvaluationData()
        await this.getIsEvaFinish()
        // this.getSupplierEvaRanking()
    },
    methods: {
        // 跳转到评标澄请列表页
        handleEvaluationClarify () {
            let params = { linkFilter: true, subpackageId: this.currentEditRow.subpackageId, evaInfoId: this.currentEditRow.evaInfoId, ifToList: true, checkType: this.currentEditRow.checkType, processType: this.currentEditRow.processType, currentStep: this.currentEditRow.currentStep }
            this.$router.push({ name: 'BidEvaluationClarifyList', query: params })
        },
        handleOpenBid () {
            this.showOpenBid = true
        },
        checkBoxChange (row, $event) {
            // 单个多选点击事件
            const { checked } = $event
            const gridObj = this.$refs.ReviewRanking
            if (gridObj && gridObj.length > 0) {
                gridObj[0].setCheckboxRow(row, checked)
            }
        },
        checkedAll (gridObj) {
            if (this.currentEditRow.judgesGroupLeader == '0') {
                return
            }
            const { checked, $grid, records } = gridObj
            if (checked) {
                // 是全选
                records.forEach((record) => {
                    // record['checkboxValue'] = true
                    console.log(record)
                    // 非废标供应商
                    if(record.invalid == '0'){
                        record['candidate'] = '1'
                    }else{
                        // 废标供应商
                        record['candidate'] = '0'
                    }
                })
            } else {
                console.log($grid.getData())
                $grid.getData().forEach((record) => {
                    // record['checkboxValue'] = false
                    record['candidate'] = '0'
                })
            }
            console.log(checked)
        },
        // 获取是否完成评标
        async getIsEvaFinish () {
            const params = {
                // subpackageId: this.currentEditRow.subpackageId || ''
                evaInfoId: this.currentEditRow.evaInfoId || ''
            }
            await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/isEvaFinish', params, { headers: this.headParams }).then((res) => {
                if (res.code == 200) {
                    let { result = false } = res
                    if (result && !this.isEvaFinish) {
                        this.getSupplierEvaRanking()
                    }
                    this.isEvaFinish = result
                }
            })
        },
        // 获取评标管理数据
        async getBidEvaluationData () {
            const params = {
                id: this.currentEditRow.id || ''
            }
            const params2 = {
                subpackageId: this.currentEditRow.subpackageId || '',
                requestType: 1
            }
            this.confirmLoading = true
            try {
                // 获取评标专家组数据
                const res = await getAction('/tender/evaluation/purchaseTenderProjectEvaSettingHead/queryEvaGroupBySubpackageId', params2, { headers: this.headParams })
                if (res.code != 200) return
                const resultList = res.result
                this.rankingColumns = resultList
                let columns = []
                resultList.forEach((rl) => {
                    if (rl) {
                        let titleTip = '',
                            type = rl.type || '0'
                        switch (type) {
                        case '0':
                            titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WUUdW_4326c815`, '(评审项)')
                            break
                        case '1':
                            titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n__WUzdW_4302b93a`, '(评分项)')
                            break
                        case '2':
                            titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WUUUzdW_********`, '(评审评分项)')
                            break
                        case '3':
                            titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WumUUdW_143bbba`, '(价格评审项)')
                            break
                        case '4':
                            titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umARlJd_ab2fb307`, '(价格排名比较项)')
                            break
                        }
                        const column = {
                            field: rl.id,
                            checkType: rl.checkType,
                            currentStep: rl.currentStep,
                            title: `${rl.name}${titleTip}`,
                            slots: {
                                header: () => {
                                    return [
                                        <span>{`${rl.name}${titleTip}`}<a-icon type="edit" theme="twoTone" onClick={() => this.showLeaderOpinion(rl)}/></span>
                                    ]
                                },
                                // 使用 JSX 渲染  row, column
                                default: ({ row, column }) => {
                                    if (row[column.property]) {
                                        let span = null
                                        if (row['summaryStatus']) {
                                            // 是否是显示汇总的操作按钮判断
                                            if (rl.type != '3'  && rl.type != '4') {
                                                // 价格评审项没有汇总操作
                                                span =
                                                    row[column.property] == 0 ? (
                                                        <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LMk_190c17e`, '未汇总')}</span>
                                                    ) : (
                                                        <span style="color: blue; cursor: pointer;" onClick={() => this.pbclick({ row: row, type: rl.type, id: rl.id, status: false, sum: true, summaryCalType: rl.summaryCalType, title: rl.name })}>
                                                            {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IMk_16e2646`, '已汇总')}
                                                        </span>
                                                    )
                                            } else {
                                                span = <span></span>
                                            }
                                        } else {
                                            if (row['btnStatus']) {
                                                // 属于自己的评审项
                                                span =
                                                    row[column.property] == 0 ? (
                                                        <span style="color: blue; cursor: pointer;" onClick={() => this.pbclick({ row: row, type: rl.type, id: rl.id, status: true, title: rl.name })}>
                                                            {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CtUU_3482b03f`, '点击评审')}
                                                        </span>
                                                    ) : (
                                                        <div>
                                                            <span style="color: blue; cursor: pointer;" onClick={() => this.pbclick({ row: row, type: rl.type, id: rl.id, status: false, title: rl.name })}>
                                                                {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUML_40ec1d21`, '评审完成')}
                                                            </span>
                                                            <span onClick={() => this.recall(row, rl.type, rl.id, false, rl.name)} style="color: #F59A23; margin-left: 10px; cursor: pointer;">
                                                                {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_[qM]_1adaea8`, '[撤回]')}
                                                            </span>
                                                        </div>
                                                    )
                                            } else {
                                                if (row['leaderStatus'] || this.currentEditRow.judgesGroupLeader == '1') {
                                                    // 组长可以查看其他评审完成的信息
                                                    span =
                                                        row[column.property] == 0 ? (
                                                            <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LUU_1948d07`, '未评审')}</span>
                                                        ) : (
                                                            <span style="color: blue; cursor: pointer;" onClick={() => this.pbclick({ row: row, type: rl.type, id: rl.id, status: false, title: rl.name })}>
                                                                {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUML_40ec1d21`, '评审完成')}
                                                            </span>
                                                        )
                                                } else {
                                                    span = row[column.property] == 0 ? <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LUU_1948d07`, '未评审')}</span> : <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUML_40ec1d21`, '评审完成')}</span>
                                                }
                                            }
                                        }

                                        return [span]
                                    }
                                    return []
                                }
                            }
                        }
                        columns.push(column)
                    }
                })
                console.log(columns)
                // 由于返回值中processType为空，所以不作为条件过滤
                let {checkType, currentStep} = this.currentEditRow
                if(checkType == '1' && currentStep == '1'){
                    columns=columns.filter(item=>{
                        if(item.checkType == '1' && item.currentStep == '1'){
                            return true
                        }
                    })
                }
                this.gridList.forEach((grid) => {
                    if (grid.gridCode == 'BidEvaluation') {
                        grid.columns = [grid.columns[0]] // 清空原有数据，只留第一条
                        grid.columns = [...grid.columns, ...columns] // 合并最终需要的数据
                    }
                })

                // 查询专家内容
                const res2 = await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryById', params, { headers: this.headParams })
                if (res2.code != 200) return
                const resultList2 = res2.result
                this.formData = resultList2
                const expertTaskList = resultList2.expertTaskList || {}
                let gridData = []
                let leaderStatus = false
                const { subAccount } = this.$ls.get(USER_INFO)
                // 连续替换，记录是否全部评标完成
                let record = {}
                Object.keys(expertTaskList).forEach((key) => {
                    const {judgesName, judgesElsAccount, judgesElsSubAccount } = expertTaskList[key][0]
                    const btnStatus = subAccount == judgesElsSubAccount
                    let obj = {
                        expert: judgesName,
                        elsAccount: judgesElsAccount,
                        elsSubAccount: judgesElsSubAccount,
                        btnStatus,
                        summaryStatus: false
                    }

                    expertTaskList[key].forEach((item, index) => {
                        if (index == 0) {
                            obj['judgesGroupLeader'] = item['judgesGroupLeader']
                            // 标识当前账号是否是组长
                            if (item['judgesGroupLeader'] == '1' && btnStatus) {
                                leaderStatus = true
                                this.leaderStatus = true
                            }
                        }
                        obj[item.evaGroupId] = item.evaGroupStatus
                        record[item.evaGroupId] = record[item.evaGroupId] == 0 ? '0' : item.evaGroupStatus // 记录最后一次状态
                        record[`${item.evaGroupId}_ID`] = item.id
                        obj[`${item.evaGroupId}_ID`] = item.id
                    })
                    if (obj['judgesGroupLeader'] == '1') {
                        gridData.unshift(obj)
                    } else {
                        gridData.push(obj)
                    }
                })
                gridData.push(Object.assign({}, record, { expert: '汇总', summaryStatus: true }))
                gridData.forEach((grid, index, array) => {
                    grid['leaderStatus'] = leaderStatus
                })
                this.gridList.forEach((grid) => {
                    if (grid.gridCode == 'BidEvaluation') {
                        grid.gridData = gridData // 合并最终需要的数据
                    }
                })
            } catch (error) {
                console.log(error)
            }
            this.confirmLoading = false
        },
        // 获取评审排名和评标结果材料数据
        async getSupplierEvaRanking () {
            const params = {
                // subpackageId: this.currentEditRow.subpackageId || ''
                evaInfoId: this.currentEditRow.evaInfoId || ''
            }
            await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/getSupplierEvaRanking', params, { headers: this.headParams }).then((res) => {
                if (res.code == 200) {
                    let { result = {} } = res
                    let flag = false
                    let supplierEvaRanking = result && result.supplierEvaRanking
                    const evaBidAttachmentInfoList = result.evaBidAttachmentInfoList
                    let oldData = {}
                    let col = []
                    supplierEvaRanking &&
                        supplierEvaRanking.forEach((item, index) => {
                            let resultItem = item.result
                            // debugger
                            if (resultItem) {
                                flag = item.valid == '0' ? true : flag
                                const resultData = Object.keys(resultItem).map((rl) => {
                                    return resultItem[rl]
                                })
                                // let dataObj = {'checkboxValue': false}
                                let dataObj = {}
                                resultData.forEach((data) => {
                                    const evaGroupResult = data['evaGroupResult'] == '1' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_passed`, '通过') : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xeR_13701ba`, '不通过')
                                    dataObj[data['evaGroupId']] = data['evaGroupType'] == '0' ? evaGroupResult : data['weightScore']
                                })
                                item = Object.assign(item, dataObj)
                                if (index === 0) {
                                    // 第一次组装好完整的 表头信息
                                    // oldData[data['evaGroupId']] = '——'
                                    this.rankingColumns.forEach((rank) => {
                                        // 判断评审不通过时的操作
                                        oldData[rank['id']] = rank['type'] == '0' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xeR_13701ba`, '不通过') : '/'
                                    })
                                }
                            } else {
                                item = Object.assign(item, oldData)
                            }
                        })
                        // let newarr = supplierEvaRanking.filter(item=>{
                        //     return iitem.valid == '0'
                        // })
                    // type:3价格分,4价格比较项
                    let typeflag = false
                    this.rankingColumns.forEach((rank) => {
                        if (rank.type == '4') typeflag = true
                        // 延用评标管理的表头顺序
                        col.push({
                            field: rank['id'],
                            title: rank['name'],
                            width: 100
                        })
                    })
                    // 防止数据重复，做去重处理
                    this.columns2.forEach((column, index, array) => {
                        if (['candidate', 'totalQuote', 'summaryRegulationResultList'].includes(column.field)) {
                            array.splice(index, 1)
                        }
                    })
                    if ((this.leaderStatus || this.currentEditRow.judgesGroupLeader == '1') && flag) {
                        // 当前账号是组长可以操作推荐勾选
                        this.columns2.push({ field: 'candidate', type: 'checkbox', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQYI_2fbbaebf`, '是否推荐'), width: 100, slots: { checkbox: 'checkbox_default' } })
                    } else {
                        this.columns2.push({ field: 'candidate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQYI_2fbbaebf`, '是否推荐'), width: 100, slots: { default: 'checkbox_default' } })
                    }
                    let bidLetterFormatGroup = result && result.bidLetterFormatGroup
                    // 是否是分项且是否是比较项
                    if (bidLetterFormatGroup.quoteType == '1' && typeflag) {
                        this.columns2.unshift(...[
                            // { field: 'totalQuote', title: '分项报价合计', width: 100 },
                            { field: 'summaryRegulationResultList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sudV_2e111e11`, '报价详情'), width: 100,
                                slots: {
                                    default: ({ row }) => {
                                        return [
                                            <a-button type="link" onClick={() => this.view(row)}>
                                                {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                            </a-button>
                                        ]
                                    }
                                }
                            }
                        ])
                    }
                    col = [...this.columns1, ...col, ...this.columns2]
                    // this.gridList.forEach((grid) => {
                    //     if (grid.gridCode == 'ReviewRanking') {
                    //         grid.gridData = supplierEvaRanking // 合并最终需要的数据
                    //     }
                    // })
                    this.gridList.forEach((grid) => {
                        if (grid.gridCode == 'ReviewRanking') {
                            grid.gridData = supplierEvaRanking
                            grid.columns = col // 合并最终需要的数据
                        }
                        if (grid.gridCode == 'BidEvaluationResults') {
                            grid.gridData.forEach((res) => {
                                res['tenderProjecId'] = this.currentEditRow.tenderProjectId || ''
                                res['subpackageId'] = this.currentEditRow.subpackageId || ''
                            })
                        }
                    })
                    if (evaBidAttachmentInfoList && evaBidAttachmentInfoList.length > 0) {
                        this.gridList.forEach((grid) => {
                            if (grid.gridCode == 'BidEvaluationResults') {
                                let newlist = []
                                grid.gridData.forEach(item=>{
                                    item=evaBidAttachmentInfoList.filter(item2=>{
                                        return item2.fileType == item.fileType
                                    })[0] || item
                                    newlist.push(item)
                                })
                                grid.gridData = newlist
                            }
                        })
                    }
                } else {
                    this.$message.error(res.message)
                    this.confirmLoading = false
                }
            })

            let gridObj = this.$refs.ReviewRanking[0]
            this.gridList[1].gridData &&
                this.gridList[1].gridData.forEach((grid) => {
                    if (grid.candidate == '1') {
                        gridObj.setCheckboxRow(grid, true) // 设置默认勾选项
                    }
                })

        },
        // 上传文件前的钩子函数
        beforeUpload (file, fileList) {
            console.log(file, fileList)
            return true
        },
        handleUploadChange ({ file, fileList, event }, row, rowIndex) {
            if (file.status === 'done') {
                if (file.response.success) {
                    if (fileList.length > 0 && fileList[0].response.result) {
                        let rowId = row.id
                        let rowHeadId = row.headId
                        let obj = fileList[fileList.length - 1].response.result
                        row = Object.assign(row, obj)
                        row.attachmentId = row.id
                        row.id = rowId
                        row.headId = rowHeadId
                        postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/saveEvaAttachmentInfo', row).then(res=>{
                            console.log('保存结果：', res)
                        })
                        this.$forceUpdate()
                    }
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        },
        // 预览文件
        preViewEvent (row) {
            let preViewFile = {
                id: row.id,
                sourceType: row.sourceType,
                filePath: row.filePath,
                subpackageId: this.currentEditRow.subpackageId
            }
            if(row.attachmentId){
                preViewFile.id = row.attachmentId
            }
            this.$previewFile.open({ params: preViewFile })
        },
        // 删除文件
        removeFiled (row, rowIndex, rowIndexObj) {
            delete row.attachmentId
            delete row.fileName
            this.handleSave('del')
            // row 当前行数据，rowIndexObj 当前行下标值
            // const gridObj = this.$refs.BidEvaluationResults[0]
            // gridObj && gridObj.remove(row)
            // let index = null
            if (rowIndexObj === 0) {
                // row = { fileType: '1', fileName: '', attachmentId: '' }
                row.fileType = '1'
                row.fileName = ''
                row.attachmentId = ''
                // index = null
            } else {
                // row = { fileType: '2', fileName: '', attachmentId: '' }
                row.fileType = '2'
                row.fileName = ''
                row.attachmentId = ''
                // index = -1
            }
            // gridObj && gridObj.insertAt(row, index)
        },
        // 在线编辑按钮功能（展示弹窗）
        async handleOnlineEdit (row){
            console.log('row::::', row)
            this.row = {...row, evaInfoId: this.currentEditRow.evaInfoId }
            this.root = this
            let url = '/tender/template/purchaseTenderTemplateLibrary/queryTemplateLibraryForEva'
            // 获取评标模板下拉列表数据
            await getAction(url, {businessType: 'tender', templateType: 'evaReport'}).then(res=>{
                if(res.result && res.result.length != 0){
                    this.supplierList = res.result || []
                }
            })
            // 获取当前行id
            await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryAttachmentInfo', {fileType: row.fileType, evaInfoId: this.currentEditRow.evaInfoId}).then(res=>{
                if(res.success){
                    this.row = res.result.fileType ? res.result : this.row
                }
            })
            console.log('row2::::', this.row, this.supplierList)
            this.evaModalVisible = true
        },
        // 点击评标触发该函数
        async pbclick (rowData) {
            let { row, type, id, status, sum = false, summaryCalType = null, title } = rowData
            console.log(row, type, id, status)
            let { checkType, currentStep, processType, subpackageId, evaInfoId } = this.currentEditRow
            this.currentRow = {
                editStatus: status,
                summary: sum,
                summaryCalType: summaryCalType,
                title: title,
                evaGroupId: id,
                elsAccount: row.elsAccount,
                elsSubAccount: row.elsSubAccount,
                expert: row.expert,
                judgesTaskHeadId: this.currentEditRow.id,
                id: row[`${id}_ID`] || ''
            }

            if (status) {
                // 未评标的需要进行校验
                let params2 = {
                    evaGroupId: this.currentRow.evaGroupId,
                    judgesTaskItemId: this.currentRow.id
                }
                const res = await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/isArrowEva', params2, { headers: this.headParams })
                if (res.code != 200) {
                    this.$message['error'](res.message)
                    return
                }
            }
            this.currentRow = Object.assign(this.currentRow, { checkType, currentStep, processType, subpackageId, evaInfoId })
            switch (type) {
            case '0':
                this.reviewStatus = true
                break
            case '1':
                this.scoringStatus = true
                break
            case '2':
                this.reviewScoringStatus = true
                break
            case '3':
                this.priceScoreStatus = true
                break
            case '4':
                this.priceComparisonStatus = true
                break
            }
        },
        // 评审意见
        showLeaderOpinion (item) {
            this.confirmLoading = true
            getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryLeaderOpinion', {evaGroupId: item.id, evaInfoId: this.formData.evaInfoId}).then(res => {
                if (res.success) {
                    let data = res.result
                    this.$refs.LeaderOpinionModal.open({data, title: item.name, leaderStatus: this.leaderStatus})
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 撤回
        recall (row, type, id, status, name) {
            console.log(row, type, id)
            const params = {
                judgesTaskItemId: row[`${id}_ID`] || '',
                evaGroupId: id || ''
            }
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DKW_1865cf0`, '提示'),
                content: `请确定是否撤回${name}的评审信息！`,
                async onOk () {
                    return new Promise((resolve, reject) => {
                        postAction('/tender/evaluation/purchaseBidEvaJudgesWithdrawRecord/withdraw', params, { headers: that.headParams })
                            .then((res) => {
                                const tp = res.success ? 'success' : 'error'
                                that.$message[tp](res.message)
                                if (res.code == 200 && tp == 'success') {
                                    that.getBidEvaluationData()
                                    that.getIsEvaFinish()
                                }
                            })
                            .finally(() => {
                                resolve(false)
                            })
                    })
                }
            })
        },
        // 由于需要在评审排名处点击下一步进行保存，所以禁用直接点击step标签的跳转，只允许通过点击上一步下一步切换标签
        // changeStep (step) {
        //     if (!this.isEvaFinish) {
        //         this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MKLMLjUULSSMkLS_142afc07`, '存在未完成的评审任务或汇总任务'))
        //         return false
        //     }
        //     this.currentStep = step
        // },
        prevStep () {
            this.currentStep > 0 && this.currentStep--
        },
        async nextStep () {
            if (!this.isEvaFinish) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MKLMLjUULSSMkLS_142afc07`, '存在未完成的评审任务或汇总任务'))
                return false
            }
            // 从评审排名点击下一步时，保存一次数据
            if(this.currentStep == 1){
                // console.log('this.gridList', this.gridList)
                // 非废标供应商数组
                let arr1 = this.gridList[1].gridData.filter(item=>{
                    return item.invalid == '0'
                })

                // 对勾选的进行判断
                const gridObj = this.$refs.ReviewRanking[0]
                const checkData = gridObj.getCheckboxRecords(true)

                // 如果存在非废标供应商且未进行勾选
                if(arr1.length != 0 && checkData.length == 0){
                    this.$message.warning('请勾选是否推荐！')
                    return
                }

                // 拿到勾选的非废标供应商
                let arr2 = checkData.filter(item=>{
                    return item.invalid == '0'
                })
                console.log(this.$refs.ReviewRanking, checkData)
                // 勾选选项中存在非废标供应商才执行保存
                if(arr2.length != 0){
                    await this.handleSave('next')
                    if(!this.ifSave) return 
                }

            }

            if (this.currentStep < this.stepList.length - 1) {
                this.currentStep++
            }
        },
        //评标结束
        evaluationEnd () {
            // 材料文件必须上传
            let fileStatus = true
            const gridData = this.gridList[2].gridData
            gridData.forEach((item) => {
                fileStatus = item.fileName ? false : true
            })
            if (fileStatus) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRnLlTbxXVW_b3d8c140`, '评标结果材料必须全部上传！'))
                return
            }
            this.handleSave()
        },
        // 保存方法
        async handleSave (flag){
            const gridData = this.gridList[2].gridData
            // 获取推荐的投标单位
            const gridObj = this.$refs.ReviewRanking[0]
            let checkData = gridObj.getCheckboxRecords(true)
            checkData=checkData.filter((item) => {
                item.subpackageId = this.currentEditRow['subpackageId']
                item.evaInfoId = this.currentEditRow['evaInfoId']
                return (item.invalid == '0') 
            })
            console.log(checkData)
            const params = {
                headId: this.currentEditRow['id'] || '',
                bidWinningCandidateList: checkData,
                evaBidAttachmentInfoList: gridData
            }
            if(flag == 'next') {
                params.save = 1
                params.evaBidAttachmentInfoList=params.evaBidAttachmentInfoList.filter(item=>{
                    return item.attachmentId
                })
            }else if(flag == 'del'){
                params.save = 1
            }
            this.confirmLoading = true
            await postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/saveEvaResult', params, { headers: this.headParams })
                .then((res) => {
                    this.confirmLoading = false
                    if (res.code == 200) {
                        this.$message.success(res.message)
                        // 执行的是评标终止按钮的保存
                        if(this.currentStep == 2 && params.save != 1) this.goBack()
                        this.ifSave = true
                    } else {
                        this.$message.error(res.message)
                        this.ifSave = false
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        // 返回
        goBack () {
            this.$emit('hide')
        },
        // 发起多轮报价
        handleQuotes () {
            this.showQuotes = true
        },
        view (row) {
            let data = []
            for (let key in row.result) {
                if (row.result[key]['evaGroupType'] == '4') {
                    data = row.result[key]['summaryRegulationResultList'] && row.result[key]['summaryRegulationResultList'].filter(item => {
                        return item['supplierAccount'] == row['supplierAccount']
                    })
                }
            }
            this.$refs.QuoteDetailsModal.open(data)
        }
    },
    created () {
        console.log(this.gridConfig)
        this.setGridHeight = document.documentElement.clientHeight - 260
    }
}
</script>

<style lang="less" scoped>
//编辑界面
.edit-page {
    height: 100%;
    .ant-spin-nested-loading {
        height: 100%;
        .ant-spin-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
    }
    .page-header {
        padding: 6px 14px;
        margin-bottom: 6px;
        background-color: #fff;
        .desc-col {
            text-align: left;
            line-height: 32px;
        }
        .btn-col {
            text-align: right;
        }
    }
    .page-content {
        flex: 1;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: auto;
        padding-left: 6px;
        padding-top: 6px;
        padding-right: 6px;
        background-color: #fff;
        margin-left: 6px;
        margin-right: 6px;
        .edit-form-box {
            // position: absolute;
        }
        .ant-advanced-rule-form {
            padding: 12px;
            .ant-form-item {
                display: flex;
                margin-bottom: 0;
                height: 55px;
                line-height: 55px;
                .ant-form-item-label {
                    text-overflow: ellipsis;
                }
            }
            .ant-form-item-control-wrapper {
                flex: 1;
            }
        }
        .edit-grid-box {
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            left: 0;
            overflow: auto;
            .vxe-toolbar {
                padding: 0 12px;
                .vxe-toolbar.size--mini {
                    height: 42px;
                }
                .tools-btn {
                    margin-left: 6px;
                }
            }
            .ant-input {
                height: 28px;
            }
        }
        .page-content-title {
            background-color: #eee;
            padding: 10px;
        }
        .page-content-grid {
            margin-top: 10px;
        }
    }
    .page-footer {
        border-top: 1px solid #e8eaec;
        background-color: #fff;
        margin-top: -1px;
        margin-left: 6px;
        margin-right: 6px;
        padding: 6px;
        text-align: center;
        .ant-btn:not(:first-child) {
            margin-left: 6px;
        }
    }
}

    .m-l-10 {
        margin-left: 10px
    }

:deep(.ant-modal-footer){
    text-align: center;
}
</style>
