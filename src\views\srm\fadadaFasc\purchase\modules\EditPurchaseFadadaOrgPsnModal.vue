<template>
  <div class="PurchaseFadadaOrgPsn business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="edit"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
// import { downFile, getAction, postAction } from '@/api/manage'
import { BUTTON_BACK} from '@/utils/constant.js'
import { postAction } from '@/api/manage'

export default {
    name: 'EditPurchaseFadadaOrgPsnModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/purchaseFadadaOrgPsn/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saveAndSumbit`, '提交'),
                    key: 'save',
                    attrs: {
                        type: 'primary'
                    },
                    args: {
                        url: '/electronsign/fadada/purchaseFadadaOrgPsn/edit'
                    },
                    click: this.composeBusinessSubmit,
                    authorityCode: 'fadada#purchaseFadadaOrgPsn:edit'
                },
                BUTTON_BACK
            ],
            url: {
                edit: '/electronsign/fadada/purchaseFadadaOrgPsn/edit',
                detail: '/electronsign/fadada/purchaseFadadaOrgPsn/queryById'
            }
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'),
                        fieldLabelI18nKey: '',
                        fieldName: 'orgName',
                        required: '1',
                        initFunction: 
                        function initFunction (Vue, { _pageData, _form, _row, _value, _cacheAllData, _data }) {
                            let { orgName = '', clientCorpId = ''} = _data[0] || {}
                            _form.orgName = orgName
                            _form.clientCorpId = clientCorpId
                        },
                        bindFunction: function bindFunction (Vue, data) {
                            if (Vue.pageConfig) {
                                let formModel = Vue.pageConfig.groups[0].formModel
                                formModel.orgName = data[0].corpName
                                formModel.clientCorpId = data[0].clientCorpId
                            }
                        },
                        extend: {
                            modalColumns: [
                                {field: 'orgCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRoo_307b3288`, '机构代码'), with: 150},
                                {field: 'corpName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), with: 150}
                            ], modalUrl: '/electronsign/fadada/purchaseFadadaOrg/listOrg', modalParams: {}
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberName',
                        required: '1',
                        initFunction: 
                        function initFunction (Vue, { _pageData, _form, _row, _value, _cacheAllData, _data }) {
                            let { memberName = '', internalIdentifier = '', clientUserId = '', memberMobile = '', memberEmail = ''} = _data[0] || {}
                            _form.memberName = memberName
                            _form.internalIdentifier = internalIdentifier
                            _form.clientUserId = clientUserId
                            _form.memberMobile = memberMobile
                            _form.memberEmail = memberEmail
                        },
                        bindFunction: function bindFunction (Vue, data) {
                            if (Vue.pageConfig) {
                                let formModel = Vue.pageConfig.groups[0].formModel
                                formModel.memberName = data[0].userName
                                formModel.internalIdentifier = data[0].subAccount
                                formModel.memberMobile = data[0].mobile
                                formModel.memberEmail = data[0].email
                                formModel.clientUserId = data[0].clientUserId
                            }
                        },
                        extend: {
                            modalColumns: [
                                {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SRMJey_93b0f653`, 'SRM子账号'), with: 150},
                                {field: 'userName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'), with: 150},
                                {field: 'mobile', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_phone`, '手机号'), with: 150},
                                {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'), with: 150}
                            ], modalUrl: '/electronsign/fadada/purchaseFadadaPersonal/listMember', modalParams: {},
                            beforeCheckedCallBack (parentRef, pageData, groupData, form) {
                                debugger
                                return new Promise((resolve, reject) => {
                                    !form.clientCorpId ? reject('先选择机构') : resolve('success')
                                })
                            }
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRBK_27c4c9ec`, '员工标识'),
                        fieldLabelI18nKey: '',
                        fieldName: 'internalIdentifier',
                        required: '1',
                        disabled: true                        
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRjd_27c9a610`, '员工邮箱'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberEmail',
                        required: '1',
                        disabled: true      
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRCEyo_4fc2b43f`, '员工电话号码'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberMobile',
                        required: '1',
                        disabled: true      
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQeRjdtS_43c5cc82`, '是否通过邮箱激活'),
                        fieldLabelI18nKey: '',
                        dictCode: 'yn',
                        fieldName: 'notifyActiveByEmail',
                        required: '1',
                        defaultValue: '1'     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_srmtReyID_7d49fa64`, 'srm机构账号ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'clientCorpId',
                        disabled: true       
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SRMjRID_de875936`, 'SRM员工ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'clientUserId',
                        disabled: true       
                    }
                ]
            }
        },
        composeBusinessSubmit (args){
            // 获取页面所有数据
            const allData = this.getAllData() || {}

            // 默认保存方法不会校验当前业务模板必填字段
            // 此处手动触发校验方法
            this.stepValidate(args).then(() => {
                postAction(this.url.edit, allData).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    debugger
                    if(res.success){
                        this.$emit('handleChidCallback', res.result)
                    }
                })

            })
        }
    }
}
</script>
