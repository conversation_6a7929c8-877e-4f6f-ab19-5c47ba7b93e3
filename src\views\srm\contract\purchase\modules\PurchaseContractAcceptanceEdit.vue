<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :remoteJsFilePath="remoteJsFilePath"
        :pageFooterButtons="pageFooterButtons"
        :externalToolBar="externalToolBar"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
    <field-select-modal
      ref="fieldSelectModal" />
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
export default {
    name: 'PurchaseContractAcceptanceEdit',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {url: '/contract/purchaseContractAcceptance/edit'},
                    attrs: {type: 'primary'},
                    key: 'save',
                    showMessage: true,
                    show: this.showSave
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                    args: {
                        url: '/contract/purchaseContractAcceptance/confirm'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish',
                    handleBefore: this.handlePublishBefore,
                    handleAfter: this.handlePublishAfter,
                    show: this.showSave
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            externalToolBar: {
                purchaseAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'contractAcceptance', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: '关联tab',
                            fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    },
                    {...new BatchDownloadBtn({pageCode: 'purchaseAttachmentList'}).btnConfig}
                ]
            },
            requestData: {
                detail: {
                    url: '/contract/purchaseContractAcceptance/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            confirmLoading: false,
            url: {
                add: '/contract/purchaseContractAcceptance/add',
                edit: '/contract/purchaseContractAcceptance/edit',
                detail: '/contract/purchaseContractAcceptance/queryById',
                upload: '/attachment/purchaseAttachment/upload'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_contractAcceptance_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.acceptanceNumbers,
                actionRoutePath: '/srm/contract/purchase/PurchaseContractAcceptanceList,/srm/contract/sale/SaleContractAcceptanceList'
            }
        },
        // 保存按钮显隐
        showSave ({pageConfig}) {
            return true
        },
        // 处理最后的数据
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
            // 附件设置headId
            this.externalToolBar['purchaseAttachmentList'][0].args.headId = resultData.id || ''
            // 附件设置数据
            this.externalToolBar['purchaseAttachmentList'][0].args.itemInfo = formModel.purchaseAttachmentList
                
            pageConfig.groups[0].formModel.sign = '0'
            pageConfig.groups[0].formModel.signStatus = '4'
        },
        // 配置数据组装前，返回所有的的组装数据
        handleBeforeRemoteConfigData (data) {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                { key: 'gridDelete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteAttachment }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        // 文件下载
        downloadEvent (vue, row) {
            console.log(row)
            if (!row.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseUploadFile`, '请上传文件'))
                return
            }
            const params = {id: row.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                debugger
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        // 文件预览
        preViewEvent (vue, row) {
            if (!row.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseUploadFile`, '请上传文件'))
                return
            }
            let preViewFile = {id: row.id, fileName: row.fileName}
            this.$previewFile.open({params: preViewFile})
        },

        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            return new Promise((resolve, reject) => {
                return resolve(args)
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            console.log('handleSaveAfter', args)
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        deleteAttachment (Vue, row) {
            const fileGrid = Vue.$refs.purchaseAttachmentList
            fileGrid.remove(row)
        }

    }
}
</script>
