<template>
    <div class="page-container">
        <edit-layout
            ref="editPage"
            :current-edit-row="currentEditRow"
            refresh
            :page-data="pageData"
            :url="url"/>
        <!-- 行明细弹出选择框 -->
        <field-select-modal
            ref="fieldSelectModal"/>
        <a-modal
            v-drag
            v-model="auditVisible"
            :title="$srmI18n(`${$getLangAccount()}#i18n_title_contractReturn`, '合同退回')"
            :okText="okText"
            @ok="handleOk">
            <a-textarea
                v-model="opinion"
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterContractReturnDesc`, '请输入合同退回备注')"
                :auto-size="{ minRows: 3, maxRows: 5 }"
            />
        </a-modal>
        <!-- 加载配置文件 -->
        <remote-js
            :src="fileSrc"
            @load="loadSuccess"
            @error="loadError"/>
    </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction} from '@/api/manage'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {EditMixin} from '@comp/template/edit/EditMixin'

export default {
    name: 'PurcaseContractPromiseModal',
    components: {
        fieldSelectModal
    },
    mixins: [EditMixin],
    data() {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            auditVisible: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZYM_2bed417f`, '履约退回'),
            opinion: '',
            flowId: 0,
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZcVH_519d8b79`, '履约行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchasePromiseItemList',
                            columns: [],
                            buttons: []
                        }
                    }
                    /* { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCbL_713646eb`, '上传方全称'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'purchaseRequest', callBack: this.uploadCallBack}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }*/

                ],
                formFields: [],
                footerButtons: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IZRL_2bea9b97`, '履约确认'),
                        authorityCode: 'contractPromise#saleContractPromise:confirmed',
                        type: 'primary',
                        click: this.confirm,
                        showCondition: this.showOptConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZYM_2bed417f`, '履约退回'),
                        authorityCode: 'contractPromise#saleContractPromise:refund',
                        type: 'primary',
                        click: this.refund,
                        showCondition: this.showOptConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        authorityCode: 'createPromise#saleContractPromise:edit',
                        type: 'primary',
                        click: this.saveEvent,
                        showCondition: this.showPublishConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                        authorityCode: 'contractPromise#saleContractPromise:publish',
                        type: 'primary',
                        click: this.publishEvent,
                        showCondition: this.showPublishConditionBtn
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                add: '/contract/saleContractPromise/add',
                edit: '/contract/saleContractPromise/edit',
                detail: '/contract/saleContractPromise/queryById',
                confirm: '/contract/saleContractPromise/confirmed',
                refund: '/contract/saleContractPromise/refund',
                public: '/contract/saleContractPromise/publish',
                upload: '/attachment/saleAttachment/upload'
            }
        }
    },
    computed: {
        fileSrc() {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_contractPromise_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    methods: {
        goBack() {
            this.$emit('hide')
        },
        loadSuccess() {
            this.pageConfig = getPageConfig() // eslint-disable-line
            if (this.currentEditRow.sourceType != 'item') {
                this.pageConfig.groups.splice(1, 1)
            }
            this.handlePageData(this.pageConfig)
        },
        afterHandleData(data) {
            if (this.currentEditRow.sourceType != 'item') {
                data.groups.splice(1, 1)
            }
            this.init()
        },
        showOptConditionBtn() {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}

            let promiseStatus = params.promiseStatus
            if (promiseStatus == '1' && params.createAccount != this.$ls.get(USER_ELS_ACCOUNT)) {
                return true
            } else {
                return false
            }
        },
        showPublishConditionBtn() {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            let promiseStatus = params.promiseStatus
            if ((promiseStatus == '0' || promiseStatus == '3' || promiseStatus == '4') && params.createAccount == this.$ls.get(USER_ELS_ACCOUNT)) {
                return true
            } else {
                return false
            }
        },
        saveEvent() {
            this.$refs.editPage.postData()
        },
        refund() {
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZYM_2bed417f`, '履约退回')
        },
        handleOk() {
            this.getData(this.url.refund, {refundRemark: this.opinion})
        },
        confirm() {
            let that = this
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IZRL_2bea9b97`, '履约确认')
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mtkPRLtFWKQRL_647bb8d7`, '此操作将确认单据，是否确认?'),
                onOk: function () {
                    that.getData(that.url.confirm, null)
                }
            })
        },
        publishEvent() {
            const form = this.$refs.editPage.getPageData()
            this.$refs.editPage.handleSend()
        },
        getData(url, param) {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (!param) {
                param = {}
            }
            param['id'] = params.id
            getAction(url, param).then(res => {
                if (res.success) {
                    this.form = res.result
                    this.auditVisible = false
                    this.opinion = ''
                    this.$message.success(res.message)
                    this.goBack
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.auditVisible = false
                this.init()
                this.$parent.modalFormOk()
            })
        }
    }
}
</script>
