import { getAction, postAction } from '@/api/manage'

export const templatePublic = {
    methods: {
        async getBusinessTemplate (businessType, noticeType) {
            let params = { elsAccount: this.$ls.get('Login_elsAccount'), businessType: businessType }
            this.confirmLoading = true
            const res = await getAction('/template/templateHead/getListByType', params)
            let currentEditRow = null
            if (res.success) {
                const result = res.result
                if (result.length > 0) {
                    if (result.length == 1) {
                        // 一个业务模板无需匹配
                        currentEditRow = {
                            templateNumber: result[0].templateNumber,
                            templateName: result[0].templateName,
                            templateVersion: result[0].templateVersion,
                            templateAccount: result[0].elsAccount
                        }
                    } else {
                        for (let i = 0; i < result.length; i++) {
                            const extendObj = result[i].extend ? JSON.parse(result[i].extend) : null
                            if (extendObj) {
                                // 先判断是否是预审还是后审
                                // 如果是后审，就判断是否是一步法还是二步法
                                // 如果二步法，就判断是否是一步还是二步
                                // checkType:0-预审、1-后审；processType：0-一步法、1-二步法；
                                // currentStep：0-一步、1-二步；noticeType：0-邀请公告，1-预审公告，2-招标公告, 3-招标变更公告, 4-预审变更公告, 5-邀请变更公告
                                // 判断是否是公告类型业务模板

                                // 如果是预审变更公告的节点，就拿预审公告业务模板
                                if (extendObj['noticeType'] == '1' && noticeType == '4') {
                                    currentEditRow = {
                                        templateNumber: result[i].templateNumber,
                                        templateName: result[i].templateName,
                                        templateVersion: result[i].templateVersion,
                                        templateAccount: result[i].elsAccount
                                    }
                                    // 找到了直接跳出整个循环
                                    break
                                }
                                // 如果是投标邀请变更的节点，就拿投标邀请业务模板
                                if (extendObj['noticeType'] == '0' && noticeType == '5') {
                                    currentEditRow = {
                                        templateNumber: result[i].templateNumber,
                                        templateName: result[i].templateName,
                                        templateVersion: result[i].templateVersion,
                                        templateAccount: result[i].elsAccount
                                    }
                                    // 找到了直接跳出整个循环
                                    break
                                }
                                // 如果是招标公告变更的节点，就拿招标公告业务模板
                                if (extendObj['noticeType'] == '2' && noticeType == '3') {
                                    currentEditRow = {
                                        templateNumber: result[i].templateNumber,
                                        templateName: result[i].templateName,
                                        templateVersion: result[i].templateVersion,
                                        templateAccount: result[i].elsAccount
                                    }
                                    // 找到了直接跳出整个循环
                                    break
                                }
                                if (noticeType == extendObj['noticeType']) {
                                    currentEditRow = {
                                        templateNumber: result[i].templateNumber,
                                        templateName: result[i].templateName,
                                        templateVersion: result[i].templateVersion,
                                        templateAccount: result[i].elsAccount
                                    }
                                    // 找到了直接跳出整个循环
                                    break
                                }
                            }
                        }
                    }
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                }
            } else {
                this.$message.warning(res.message)
            }
            this.confirmLoading = false
            return currentEditRow
        }
    }
}