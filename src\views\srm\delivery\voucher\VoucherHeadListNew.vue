<template>
  <div style="height: 100%">
    <list-layout
      v-show="!showDetailPage && !showEditPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"
      @cell-click="cellClickEvent"
      @loadDataSuccess="loadDataSuccess"
    />
    <View-Voucher-Modal
      ref="detailPage"
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <!-- 编辑界面 -->
    <!-- <Edit-Voucher-Modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    /> -->
    <a-modal
      v-drag
      v-model="quantityCover"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_writeQuantity`, '冲销数量')"
      :okText="okText"
      @ok="handleOk"
    >
      <a-input-number
        v-model="coverQuantity"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterWriteQuantity`, '请输入冲销数量')"
        size="large"
        :style="{ width: '80%' }"
      />
    </a-modal>
    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="refundsVisible"
      @ok="selectedTemplate"
    >
      <template slot="footer">
        <a-button
          key="back"
          @click="handleRefundsCancel"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedRefundsTemplate"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"
      />
    </a-modal>

    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>

<script>
import ViewVoucherModal from './modules/ViewVoucherModalNew'
import EditVoucherModal from './modules/EditVoucherModal'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import { httpAction } from '@/api/manage'
import { debounce } from 'lodash'
import { getAction, postAction } from '@/api/manage'
import { cloneDeep } from 'lodash'

export default {
  name: 'VoucherHeadList',
  mixins: [ListMixin],
  components: {
    ViewVoucherModal,
    EditVoucherModal,
    fieldSelectModal
  },
  data() {
    return {
      showEditPage: false,
      quantityCover: false,
      showDetailPage: false,
      submitLoading: false,
      refundsVisible: false,
      coverQuantity: '',
      nextOpt: true,
      okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_writeOff`, '冲销'),
      templateNumber: undefined,
      templateOpts: [],
      pageData: {
        button: [
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatReturnSalesMemo`, '创建退货通知单'),
            authorityCode: 'refundsDelivery#purchaseRefundsDeliveryHead:voucherToRefundsDelivery',
            icon: '',
            clickFn: this.createRefundsDelivery
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SMWWWWF_b44eb4e2`, '获取ERP数据'),
            icon: 'arrow-down',
            clickFn: this.getDataByErp,
            authorityCode: 'voucher#purchaseVoucherHead:getDataByErp'
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'),
            icon: 'arrow-up',
            clickFn: this.pushDataToERP,
            authorityCode: 'voucher#purchaseVoucherHead:pushDataToErp'
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
            icon: 'setting',
            folded: false,
            clickFn: this.settingColumns
          },
          //{label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), allow: ()=> { return this.btnInvalidAuth('PurchaseVoucherItem:export') }, icon: 'download', clickFn: this.handleExportXls},
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
            authorityCode: 'voucher#purchaseVoucherItem:export',
            icon: 'download',
            clickFn: this.handleExportXls
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '批量价格匹配'),
            authorityCode: 'order#purchaseVoucherHead:editInfoRecordNumber',
            icon: '',
            clickFn: this.batchPriceMatching
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
            icon: 'file-text',
            folded: true,
            isDocument: true,
            clickFn: this.showHelpText
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
            icon: 'file-pdf',
            folded: true,
            isDocument: true,
            clickFn: this.showHelpPDF
          }
        ],
        formField: [
          {
            type: 'input',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voucherNo`, '凭证号'),
            fieldName: 'keyWord',
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterVoucherNoOrName`, '请输入凭证号或供应商名称')
          },
          {
            type: 'select',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '是否需要匹配价格'),
            fieldName: 'matchPrice',
            dictCode: 'yn'
          }
        ],
        form: {
          keyWord: ''
        },
        optColumnList: [
          {
            type: 'view',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
            clickFn: this.showDetail,
            authorityCode: 'order#purchaseVoucherHead:queryById'
          },
          // {
          //   type: 'edit',
          //   title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
          //   clickFn: this.handleEdit,
          //   allow: this.allowEdit,
          //   authorityCode: 'order#purchaseVoucherHead:editInfoRecordNumber'
          // },
          {
            type: 'view',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_writeOff`, '冲销'),
            clickFn: this.cover,
            allow: this.showCoverCondition,
            authorityCode: 'voucher#purchaseVoucherHead:chargeAgainst'
          },
          {
            type: 'record',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
            clickFn: this.handleRecord
          }
        ]
      },
      tabsList: [],
      url: {
        list: '/delivery/purchaseVoucherHead/list',
        cover: '/delivery/purchaseVoucherHead/chargeAgainst',
        createRefunds: '/delivery/purchaseRefundsDeliveryHead/voucherToRefundsDelivery',
        columns: 'PurchaseVoucherItem',
        getDataByErpUrl: '/delivery/purchaseVoucherHead/getDataByErp',
        exportXlsUrl: '/delivery/purchaseVoucherHead/exportXls',
        pushDataToERPUrl: '/delivery/purchaseVoucherHead/pushDataToErp'
      }
    }
  },
  mounted() {
    // this.serachTabs('voucherMoveType', 'moveType')
    this.serachCountTabs('/delivery/purchaseVoucherHead/counts')
  },
  computed: {},
  created() {},
  methods: {
    // 格式化指定字段
    loadDataSuccess() {
      try {
        console.log(123)
        let listPage = this.$refs.listPage
        let tableData = cloneDeep(listPage.tableData)
        tableData = tableData.map((item) => {
          if (!!item.price) item.price = item.price.toFixed(6).toString() // 含税价
          if (!!item.netPrice) item.netPrice = item.netPrice.toFixed(6).toString() // 净价
          return item
        })
        console.log('tableData', tableData)
        listPage.tableData = tableData
      } catch (error) {
        console.log('error', error)
      }
    },
    // 单个弹窗匹配价格
    cellClickEvent({ row, column }) {
      console.log('column', column)
      if (column.field === 'infoRecordNumberExtend' && row.showEditStatus == 1) {
        let grid = this.$refs.listPage.$refs.listGrid
        grid.clearCheckboxRow()
        grid.setCheckboxRow(row, true)
        this.requestMatching()
      }
    },
    // 批量匹配价格
    batchPriceMatching() {
      this.requestMatching(true)
    },

    requestMatching(isBatch = false) {
      let grid = this.$refs.listPage.$refs.listGrid
      let checkData = grid.getCheckboxRecords()
      console.log('已选择数据', checkData)

      if (checkData.length == 0) {
        return this.$message.error('请选择数据')
      }
      if (!isBatch && checkData.length != 1) {
        return this.$message.error('只可选择一条数据')
      }

      // 单条匹配价格
      if (!isBatch) {
        let singleData = checkData[0]
        let url = '/price/purchaseInformationRecords/voucherAvliabledPrice'
        let params = {
          purchaseOrg: singleData.purchaseOrg, // 采购组织
          toElsAccount: singleData.toElsAccount, // 供应商ELS号
          company: singleData.company, // 公司代码
          materialNumber: singleData.materialNumber, // 物料编码
          price: singleData.price, // 凭证含税单价，此处取凭证行中 含税价 price字段赋值
          balanceRemainExecutableQuantity: singleData.voucherBalanceLackQuantity // 凭证对账数量， 取值为凭证行凭证不足结算数量 字段 voucherBalanceLackQuantity
        }
        let columns = [
          {
            field: 'infoRecordNumber',
            title: '价格记录号',
            fieldLabelI18nKey: 'i18n_title_infoRecordNumber'
            // width: 150
          },
          {
            field: 'materialNumber',
            title: '物料编码',
            fieldLabelI18nKey: 'i18n_title_materialNumber'
            // width: 150
          },
          {
            field: 'materialName',
            title: '物料名称',
            fieldLabelI18nKey: 'i18n_title_materialName'
            // width: 150
          },
          {
            field: 'balanceRemainExecutableQuantity',
            title: '价格剩余可结算数量',
            fieldLabelI18nKey: 'i18n_title_balanceRemainExecutableQuantity'
            // width: 150
          }
        ]
        this.$refs.fieldSelectModal.open(url, params, columns, 'single')
      }
      // 多条数据匹配
      else {
        postAction('/price/purchaseInformationRecords/batchVoucherAvliabledPrice', {
          toElsAccount: '',
          vorcherItemList: checkData.map((item) => {
            return {
              toElsAccount: item.toElsAccount,
              voucherItemId: item.id,
              materialNumber: item.materialNumber,
              company: item.company,
              purchaseOrg: item.purchaseOrg,
              price: item.price,
              voucherBalanceLackQuantity: item.voucherBalanceLackQuantity
            }
          })
        }).then((res) => {
          if (res.success) {
            let { fullData } = grid.getTableData()
            let resultData = res.result
            fullData.forEach((item) => {
              if (!!resultData[item.id]) item.infoRecordNumberExtend = resultData[item.id]
            })
            grid.loadData(fullData)
            this.$message.success('匹配成功')
            this.save()
          } else {
            this.$message.warning('匹配失败')
          }
        })
      }
    },

    // 匹配价格弹窗回填
    fieldSelectOk(data) {
      console.log(data)
      if (!data || !data[0] || !data[0].id) return

      let grid = this.$refs.listPage.$refs.listGrid
      let { fullData } = grid.getTableData()
      let checkData = grid.getCheckboxRecords()
      let checkDataIndex = fullData.findIndex((i) => i.id === checkData[0].id)
      if (checkDataIndex > -1) {
        fullData[checkDataIndex].infoRecordNumberExtend = data[0].infoRecordNumber
        console.log(fullData)
        grid.loadData(fullData)
        this.$message.success('匹配成功')
        this.save()
      }
    },

    // 匹配价格后保存
    save() {
      let grid = this.$refs.listPage.$refs.listGrid
      let checkData = grid.getCheckboxRecords()
      postAction(
        '/delivery/purchaseVoucherHead/editInfoRecordNumber',
        checkData.map((item) => {
          return {
            id: item.id,
            infoRecordNumberExtend: item.infoRecordNumberExtend,
            voucherBalanceLackQuantity: item.voucherBalanceLackQuantity
          }
        })
      ).then((res) => {
        if (res.success) this.searchEvent()
        else this.$message.warning(res.message)
      })
    },

    allowEdit(row) {
      //   true 不可编辑
      if (!!row.showEditStatus) return false
      return true
    },
    showDetail(row) {
      ///row.id = row.headId
      this.currentEditRow = row
      this.currentEditRow.id = row.headId
      this.showDetailPage = true
    },
    // 编辑
    handleEdit(row) {
      ///row.id = row.headId
      this.currentEditRow = row
      this.currentEditRow.id = row.headId
      this.showEditPage = true
    },
    handleView(row) {
      //row.id = row.headId
      // 打开详情页
      this.showDetailPage = false
      this.$nextTick(() => {
        this.currentEditRow = row
        this.currentEditRow.id = row.headId
        this.showDetailPage = true
        // this.$store.dispatch('SetTabConfirm', true)
      })
    },
    handleOk() {
      if (this.coverQuantity == '' || this.coverQuantity <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reversalQuantityCannotEmptyLessThanEqualZero`, '冲销数量不能为空或者小于等于0'))
        return
      } else if (this.coverQuantity > this.currentRow.voucherQuantity) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reversalQuantityGreaterThanVoucherQuantityLine`, '冲销数量大于行凭证数量'))
        return
      } else {
        this.quantityCover = false
        this.currentRow.voucherQuantity = this.coverQuantity
        let that = this
        let selectedRows = []
        selectedRows.push(this.currentRow)
        this.$confirm({
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receiptReversal`, '收货冲销'),
          content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherReversalSelected`, '确认是否选中行冲销?'),
          onOk: function () {
            that.postUpdateData(that.url.cover, selectedRows)
          }
        })
      }
    },
    showCoverCondition(row) {
      if (this.btnInvalidAuth('voucher#purchaseVoucherHead:chargeAgainst')) {
        return true
      }
      if (row.reconciliation == 1) {
        return true
      } else {
        if (row.moveType == '102' || row.moveType == '123' || row.moveType == '162' || row.voucherStatus == '1') {
          return true
        } else {
          if (row.voucherStatus == '2' || row.sourceType == '7') {
            return true
          } else {
            return false
          }
        }
      }
    },
    handleExportXls() {
      this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ui_a7774`, '凭证'))
    },
    cover(row) {
      if (row.voucherStatus != '0') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSelectedRowIsFrozen`, '选中的行已冻结'))
        return
      } else {
        this.currentRow = row
        this.quantityCover = true
      }
    },
    postUpdateData(url, params) {
      this.$refs.listPage.loading = true
      httpAction(url, params, 'post')
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.searchEvent()
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.listPage.loading = false
        })
    },
    createRefundsDelivery: debounce(function () {
      if (this.$refs.listPage.loading) return
      this.nextOpt = true
      this.serachTemplate('refundsDelivery')
    }, 1000),
    serachTemplate(businessType) {
      this.currentEditRow = {}
      let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
      if (selectedRows.length <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
        return
      } else {
        selectedRows.forEach((item, i) => {
          if (item.moveType != '101') {
            if (item.sourceType == '7') {
              this.nextOpt = false
              return
            } else {
              this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行非收货行')
              this.nextOpt = false
              return
            }
          }
          if (item.voucherStatus != '0') {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行已冻结')
            this.nextOpt = false
            return
          }
          if (item.factory + '_' + item.storageLocation != selectedRows[0].factory + '_' + selectedRows[0].storageLocation) {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedLineFactoryAndInventorylocationInconsistent`, '选中的行工厂+库存地点不一致'))
            this.nextOpt = false
            return
          }
          /*if (item.reconciliation ==1){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第')+(i+1)+'行已对账')
                        this.nextOpt = false
                        return
                    }*/
          if (item.sourceType == '0' && selectedRows[0].sourceType != '0') {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderLineReceiptVoucherNotReturnedTogetherWithothervouchers`, '订单行收货凭证不与其他凭证一起退货'))
            this.nextOpt = false
            return
          }
        })
        if (this.nextOpt) {
          this.pageData.businessType = businessType
          this.openModal()
        }
      }
    },
    openModal() {
      this.$refs.listPage.loading = true
      this.$refs.listPage
        .queryTemplateList(this.$ls.get('Login_elsAccount'))
        .then((res) => {
          if (res.success) {
            if (res.result.length > 0) {
              let options = res.result.map((item) => {
                return {
                  value: item.templateNumber,
                  title: item.templateName,
                  version: item.templateVersion,
                  account: item.elsAccount
                }
              })
              this.templateOpts = options
              // 只有单个模板直接新建
              if (this.templateOpts && this.templateOpts.length === 1) {
                this.templateNumber = this.templateOpts[0].value
                this.selectedRefundsTemplate()
              } else {
                // 有多个模板先选择在新建
                this.refundsVisible = true
                this.$refs.listPage.loading = false
              }
            } else {
              this.$refs.listPage.loading = false
              this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
            }
          } else {
            this.$refs.listPage.loading = false
            this.$message.warning(res.message)
          }
        })
        .catch(() => {
          this.$refs.listPage.loading = false
        })
    },
    handleRefundsCancel() {
      this.refundsVisible = false
    },
    selectedRefundsTemplate() {
      if (this.templateNumber) {
        const that = this
        this.submitLoading = true
        let template = this.templateOpts.filter((item) => {
          return item.value == that.templateNumber
        })
        let params = {
          templateNumber: this.templateNumber,
          templateName: template[0].title,
          templateVersion: template[0].version,
          templateAccount: template[0].account,
          purchaseVoucherItemList: []
        }
        if (this.pageData.businessType == 'refundsDelivery') {
          this.url.temp = this.url.createRefunds
        }
        that.refundsVisible = false
        that.submitLoading = false
        if (this.url.temp == '') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
          return
        }
        params.purchaseVoucherItemList = that.$refs.listPage.$refs.listGrid.getCheckboxRecords()
        that.postUpdateData(this.url.temp, params)
      } else [(this.$refs.listPage.loading = false)]
    },
    hideDetail() {
      this.showDetailPage = false
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.rowHighLight td) {
  background-color: #cbd4f4;
}
</style>
