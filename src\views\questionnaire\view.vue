<template>
  <div
    class="questionnaire-view"
    :style="modleStyle">

    <a-spin
      class="warp"
      :spinning="loading">
      <div
        class="view-header">
        <div class="title">
          {{ this.title }}
        </div>
        <div class="des">
          {{ this.desc }}
        </div>
      </div>
      <a-form
        :form="form"
        :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }"
        :style="formDesc.ui.style">
        <a-row
          v-for="(row, index) in rows"
          :gutter="8"
          :key="row.key"
        >
          <!-- <template v-if="row.type === 'group'">
            <a-col :span="24">
            <divide-com :title="row.content"/>
            </a-col>
        </template> -->
          <template>
            <a-col
              v-for="item in row.content"
              :key="item.id"
            >
              <field-com
                :field="item"
                :answerId="answerId"
                :answerAllData="answerAllData"
                :isPre="isPre"
                :isDownload="isDownload"
                :index="index" />
            </a-col>
          </template>
        </a-row>
        <!-- <footer-com
            v-if="isPre != 1 && formDesc.footer.buttons.length>0"
            :data="formDesc.footer"
            @click-btn="clickBtn"
        /> -->
        <a-form-item
          v-if="isPre != 1 && isPre != 2"
          class="foot-btns"
        >
          <a-button
            type="primary"
            @click="clickBtn(0, true)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import FieldCom from './component/FormRender/FieldCom'
import DivideCom from './component/FormRender/other/DivideCom'
import FooterCom from './component/FormRender/other/FooterCom'
import * as core from './component/FormRender/core/index'
import { deepClone } from './utils'
import { formRenderData } from './utils/core'
import { getAction, postAction } from '@api/manage'
export default {
    name: 'FormRender',
    components: {
        FieldCom,
        DivideCom,
        FooterCom
    },
    props: {
        isPre: {
            type: Number,
            default: 0
        },
        answerId: {
            type: String,
            default: ''
        },
        initData: {
            type: Object,
            default: () => {}
        },
        formHd: {
            type: Object,
            default: () => {}
        },
        isDownload: {
            type: Number,
            default: 0
        }
    },
    data () {
        return {
            viewData: {
                formDesc: {
                    rows: {},
                    ui: {
                        style: '',
                        gutter: ''
                    },
                    footer: {
                        buttons: [
                            {
                                text: '提交',
                                type: 'primary',
                                size: 'large',
                                disabled: false,
                                ifValidateForm: true
                            }]
                    }
                }
            },
            title: '',
            desc: '',
            requestFormData: [],
            loading: false,
            answerAllData: {}
        }
    },
    beforeCreate () {
        this.form = this.$form.createForm(this, {
            onValuesChange: (props, values) => {
                const currentViewData = core.updateViewDataByField(values, this.viewData)
                const fieldName = Object.keys(values)[0]
                const resultViewData = core.validateForm(currentViewData, fieldName)
                this.viewData = resultViewData
            }
        })
    },
    computed: {
        ...mapState({ 
            formData: state => state.formDesigner.formData
        }),
        modleStyle () {
            let sH = document.body.clientHeight - 50
            let s = {}
            if (this.isPre == 1) {
                s ={
                    'max-height': sH + 'px',
                    'overflow-y': 'auto'
                }
            }
            return s
        },
        rows: function () {
            return this.viewData.formDesc.rows
        },
        formDesc: function () {

            return this.viewData.formDesc || ''
        },
        surveyId () {
            return this.answerId || this.$route.query.businessId
        },
        businessType () { //  问卷公开、内部
            return this.$route.query.businessType ? this.$route.query.businessType : null
        }
    },
    created () {
        if (this.isPre == 1) {
            this.title = this.formHd.surveyName
            this.desc = this.formHd.surveyDesc
            this.dataHandle(this.formData)
        } else if (this.isPre == 3) { // 内部展示, 设计器已保存，没有答案
            this.designerInit()
        }else {
            this.answerInit()
        }
    },
    methods: {
        // 非预览模式 pre：2 问卷调查查看或答题
        answerInit () {
            if (!this.surveyId) {
                return
            }
            this.loading = true
            let params = {
                id: this.surveyId
            }
            let url = '/survey/purchaseSurveyItem/queryById'
            if (this.businessType) { // 公开
                url = '/survey/purchaseSurveyHead/noToken/queryById'
            }

            getAction(url, params).then(rs => {
                if (rs.success) {
                    this.title = rs.result.surveyName
                    this.desc = rs.result.surveyDesc
                    this.answerAllData = rs.result
                    let data = rs.result
                   
                    if (this.businessType) {
                        data = data.purchaseSurveyLibraryList.map(el => el.libraryContent && JSON.parse(el.libraryContent))
                    } else {
                        data = data.purchaseSurveyAnswerLibraryList.map(el => el.libraryContent && JSON.parse(el.libraryContent))
                    }
                    this.requestFormData = data
                    this.dataHandle(data)
                    this.loading = false
                }
            }).catch(error => {
               
            })
        },
        // 设计器预览
        designerInit () {
            let id = this.answerId
            if (!id) {
                return
            }
            this.loading = true
            let params = {
                id
            }
            let url = '/survey/purchaseSurveyHead/noToken/queryById'

            getAction(url, params).then(rs => {
                if (rs.success) {
                    this.title = rs.result.surveyName
                    this.desc = rs.result.surveyDesc
                    this.answerAllData = rs.result
                    let data = rs.result
                    data = data.purchaseSurveyLibraryList.map(el => el.libraryContent && JSON.parse(el.libraryContent))
                    this.requestFormData = data
                    this.dataHandle(data)
                    this.loading = false
                }
            }).catch(error => {
               
            })
        },
        dataHandle (data) {
            // 如果是接口返回则是数据转换后的，不用经过formRenderData
            let formData = deepClone(data)
            formData = formRenderData(formData)
            this.viewData = core.metaData2ViewData(formData, {})
        },
        clickBtn (index, ifValidateForm) { // 一个按钮默认为保存
            let error = null
            if (ifValidateForm) {
                this.viewData = core.validateForm(this.viewData)
                const errors = this.viewData.formDesc.errors
                error = errors.length ? errors : null
            } else {
                error = null
            }
            const values = this.form.getFieldsValue()
            // this.$emit('click-btn', error, values, index)
            if (!error) {
                this.submit(values)
            }
        },
        transData (saveData) {
            let purchaseSurveyLibraryList = []
            let commomLibraryList = this.businessType ? 'purchaseSurveyLibraryList' : 'purchaseSurveyAnswerLibraryList' // 二级opiton
            let commonSurveyOption = this.businessType ? 'purchaseSurveyOptionList' : 'purchaseSurveyAnswerOptionList' // 三级opiton
            for (let i = 0; i < this.requestFormData.length; i++) {
                let row = this.requestFormData[i]
                // 保存值插入报模板
                Object.keys(saveData).forEach(tems => {
                    if (row.attr.id == tems) {
                        row.attr.initialValue = saveData[tems]
                    }
                })
                let obj = {}
                obj.itemName = row.attr.label
                obj.itemType = row.type
                // 二级id
                // libraryListObj = this.answerAllData.purchaseSurveyAnswerLibraryList.find(els => els.fbk1 == row.key)
                let libraryListObj = null
                libraryListObj = this.answerAllData[commomLibraryList].find(els => els.fbk1 == row.key)
                // if (this.businessType) {
                // } else {
                //     libraryListObj = this.answerAllData.purchaseSurveyAnswerLibraryList.find(els => els.fbk1 == row.key)
                // }
                obj.id = libraryListObj && libraryListObj.id || ''
                obj.headId = libraryListObj && libraryListObj.headId || ''
                obj.fbk1 = row.key // fbk1 记录key
                obj.libraryContent = JSON.stringify(row) // libraryContent 记录全部信息
                obj.purchaseSurveyAnswerOptionList = []
                if (row.attr.options) {
                    for (let j = 0; j < row.attr.options.length; j++) {
                        let options = row.attr.options[j]
                        let opt = {}
                        opt.optionType = row.type
                        opt.optionName = options.label
                        opt.id = libraryListObj[commonSurveyOption][j].id //利用子项耦合下标
                        opt.headId = libraryListObj[commonSurveyOption][j].headId //利用子项耦合下标
                        opt.selected = 0 // 默认值都为0未选
                        opt.optionComment = options.value
                        obj.purchaseSurveyAnswerOptionList.push(opt)
                    }
                }
                let that_ = this
                // 如果有opitons更新option  select
                Object.keys(saveData).forEach(el => {
                    if (el == row.key) {
                        let index = saveData[el] // 耦合选中的值作为下标
                        if (index) {
                            switch (row.type) {
                            case 'checkbox':
                                // 返回值是数组
                                index.forEach(ss => {
                                    obj.purchaseSurveyAnswerOptionList[ss].selected = 1
                                })
                                break
                            case 'radio':
                                obj.purchaseSurveyAnswerOptionList[index].selected = 1
                                break
                            case 'select':
                                obj.purchaseSurveyAnswerOptionList[index].selected = 1
                                break
                            
                            default: {
                            // input or score 走默认
                                let r = that_.answerAllData[commomLibraryList].find(els => els.fbk1 == el)[commonSurveyOption][0]
                                let o = { 
                                    optionComment: index,
                                    headId: r.headId,
                                    optionType: r.optionType,
                                    id: r.id
                                }
                                obj.purchaseSurveyAnswerOptionList[0] = o
                                break
                            }
                            }
                        }
                    }
                })
                purchaseSurveyLibraryList.push(obj)
            }
            return purchaseSurveyLibraryList
        },
        // 数据提交
        submit (values) {
            let elsAccount = this.$ls.get('Login_elsAccount')
            let purchaseSurveyLibraryList = this.transData(values) // 转换成接口使用字段
            let url = 'survey/purchaseSurveyItem/submitSys'
                        
            if (this.businessType) {
                url = 'survey/purchaseSurveyItem/noToken/submitOther'
            }
            let params = {
                elsAccount,
                id: this.surveyId,
                headId: this.answerAllData.headId,
                surveyDesc: this.title,
                surveyName: this.desc,
                purchaseSurveyAnswerLibraryList: purchaseSurveyLibraryList
            }
            postAction(url, params).then(res => {
                if(res.success) {
                    if (this.businessType) { // 外部问卷
                        this.$router.push({path: '/questionnaire/result'})
                    } else {
                        this.$emit('goback')
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitSuccess`, '提交成功'))
                    }
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>

<style scoped lang="less">
.questionnaire-view{
    margin: 8px;
    padding: 8px;
    min-height: 66px;
    min-width: 118px;
    width: 100%;
    .view-header{
        .title{
            color: #1890ff;
            text-shadow: 0 0 0.25px currentColor;
            vertical-align: middle;
            margin: 0;
            padding: 12px 0;
            line-height: 30px;
            text-align: center;
        }
        .des{
            display: none;
            margin: 0;
        }
    }
    .foot-btns{
      text-align: center;
    }
}
@media only screen and (min-width: 118px) {
    .questionnaire-view{
        min-width: 118px;
    }
}
</style>
