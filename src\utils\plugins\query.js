// 兼容jquery api 不建议过度使用,尽量使用原生写法。一般复杂的dom操作，或者代理事件绑定时可以考虑

let $ = null
// 循环
const forEach = (arr, callback) => {
    for (let i = 0; i < arr.length; i++) {
        callback && callback(arr[i], i)
    }
}

// 节点位置寻找
const indexOfEle = (eles, ele) => {
    if (!eles || !ele) {
        return -1
    }
    for (let i = 0; i < eles.length; i++) {
        if (eles[i] === ele) return i
    }
    return -1
}
// 操作函数
const operation = {
    // class remove,add,toggle操作
    classOperation (self, type, className) {
        if (typeof className !== 'string') {
            return self
        }
        forEach(className.split(/\s+/), str => {
            forEach(self, ele => {
                if (ele && ele.classList && str) {
                    ele.classList[type] && ele.classList[type](str)
                }
            })
        })
        return self
    },
    // props set get 属性，样式等设置
    propsOperation (self, callback, attrs, value) {
        if (!attrs) {
            return self
        }
        if (typeof value === 'undefined') {
            if (typeof attrs === 'string') {
                if (self[0]) {
                    return callback(self[0], attrs)
                } else {
                    return undefined
                }
            } else {
                forEach(Object.keys(attrs), str => {
                    forEach(self, ele => {
                        callback(ele, str, attrs[str])
                    })
                })
            }
        } else {
            forEach(self, ele => {
                callback(ele, attrs, value)
            })
        }
        return self
    },
    // 插入节点
    appendElement (self, name, content) {
        const append = (ele, newEle) => {
            if (name === 'append') {
                ele.appendChild(newEle)
            } else if (name === 'before') {
                ele.parentNode.insertBefore(newEle, ele)
            } else if (name === 'after') {
                ele.parentNode.insertBefore(newEle, ele.nextSibling)
            } else if (name === 'prepend') {
                ele.insertBefore(newEle, ele.childNodes[0])
            }
        }
        // 数组反转标识
        const reverseSign = ['after', 'prepend'].indexOf(name) > -1

        forEach(self, (ele, n) => {
            if (typeof content === 'string') {
                const tempDiv = document.createElement('div')
                tempDiv.innerHTML = content
                for (;;) {
                    // 反转
                    const child = reverseSign ? tempDiv.lastChild : tempDiv.firstChild
                    if (!child) break
                    append(ele, child)
                }
            } else if (content instanceof $.Class) {
                const arr = Array.prototype.slice.apply(content)
                // 反转
                if (reverseSign) arr.reverse()
                forEach(arr, el => {
                    append(ele, el)
                })
            } else if (content.nodeType === 1) {
                append(ele, content)
            } else if (typeof content === 'function') {
                const txt = content(n, ele.innerHTML)
                if (typeof txt === 'string') {
                    $(ele)[name] && $(ele)[name]($(txt))
                }
            }
        })
        return self
    },
    nextPrevFn (self, selector, sign) {
        if (self.length > 0) {
            const ele = self[0]
            const next = sign ? ele.nextElementSibling : ele.previousElementSibling
            if (selector) {
                if (next && next.matches(selector)) {
                    return new $.Class([next])
                }
                return new $.Class([])
            }
            if (next) return new $.Class([next])
            return new $.Class([])
        }
        return new $.Class([])
    },
    nextPrevAllFn (self, selector, sign) {
        const arr = []
        let ele = self[0]
        if (!ele) return new $.Class([])
        for (;;) {
            const next = sign ? ele.nextElementSibling : ele.previousElementSibling
            if (!next) break
            if (selector && next.matches(selector)) {
                arr.push(next)
            } else {
                arr.push(next)
            }
            ele = next
        }
        return new $.Class(arr)
    }
}

// 构建Dom类
class Dom {
    constructor (arr) {
        arr = arr || []
        const that = this
        arr.forEach((ele, i) => {
            that[i] = ele
        })
        that.length = arr.length
        return this
    }

    // 判断浏览器是否支持某一个CSS3属性
    static supportCss3 (style) {
        const prefix = ['webkit', 'Moz', 'ms', 'o']
        const humpString = []
        const htmlStyle = document.documentElement.style
        const toHumb = (string) => string.replace(/-(\w)/g, (str, $1) => $1.toUpperCase())
        for (let i in prefix) {
            humpString.push(toHumb(prefix[i] + '-' + style))
        }
        humpString.push(toHumb(style))
        for (let i in humpString) {
            if (humpString[i] in htmlStyle) {
                return true
            }
        }
        return false
    }

    // 驼峰转换
    static toCamelCase (string) {
        const reg = /[\:\-\_]+(.)/g
        return string.replace(reg, (str, $1) => $1.toUpperCase())
    }

    // 判断class是否存在
    hasClass (className) {
        if (!this[0]) return false
        return this[0].classList.contains(className)
    }

    // 新增class
    addClass (className) {
        return operation.classOperation(this, 'add', className)
    }

    // 删除class
    removeClass (className) {
        return operation.classOperation(this, 'remove', className)
    }

    // class开关
    toggleClass (className) {
        return operation.classOperation(this, 'toggle', className)
    }

    // 获取，设置属性
    attr (attrs, value) {
        return operation.propsOperation(this, (ele, str, val) => {
            if (typeof val === 'undefined') {
                return ele.getAttribute(str)
            } else {
                ele.setAttribute(str, val)
            }
        }, attrs, value)
    }

    // 删除属性
    removeAttr (attr) {
        if (typeof attr === 'string') {
            forEach(this, ele => {
                ele.removeAttribute(attr)
            })
        }
        return this
    }

    // 内置属性
    prop (props, value) {
        return operation.propsOperation(this, (ele, str, val) => {
            if (typeof val === 'undefined') {
                return ele[str]
            } else {
                ele[str] = val
            }
        }, props, value)
    }

    // 删除内置属性
    removeProp (attr) {
        if (typeof attr === 'string') {
            forEach(this, ele => {
                ele[attr] = undefined
            })
        }
        return this
    }

    // 数据缓存
    data (key, value) {
    // 获取
        if (typeof value === 'undefined') {
            const ele = this[0]
            if (ele) {
                if (ele.domStorageData && (key in ele.domStorageData)) {
                    return ele.domStorageData[key]
                }
                const dataKey = ele.getAttribute(`data-${key}`)
                if (dataKey) {
                    return dataKey
                }
                return undefined
            }
            return undefined
        }
        // 设置
        forEach(this, ele => {
            if (!ele.domStorageData) ele.domStorageData = {}
            ele.domStorageData[key] = value
        })
        return this
    }

    // 删除数据缓存
    removeData (key) {
        forEach(this, ele => {
            if (ele.domStorageData && ele.domStorageData[key]) {
                ele.domStorageData[key] = null
                delete ele.domStorageData[key]
            }
        })
    }

    // 样式设置
    css (attrs, value) {
        return operation.propsOperation(this, (ele, str, val) => {
            if (typeof val === 'undefined') {
                const obj = window.getComputedStyle(ele, null)
                return obj.getPropertyValue(str) || obj[str] || ''
            } else {
                ele.style[Dom.toCamelCase(str)] = val
            }
        }, attrs, value)
    }

    // 获取宽度
    width () {
        if (this[0] === window) {
            return window.innerWidth
        }
        if (this.length > 0) {
            return parseFloat(this.css('width'))
        }
        return null
    }

    // 外部宽度
    outerWidth (includeMargins) {
        if (this.length > 0) {
            const ele = this[0]
            if (includeMargins) {
                return ele.offsetWidth + parseFloat(this.css('margin-right')) + parseFloat(this.css('margin-left'))
            }
            return ele.offsetWidth
        }
        return null
    }

    // 获取高度
    height () {
        if (this[0] === window) {
            return window.innerHeight
        }
        if (this.length > 0) {
            return parseFloat(this.css('height'))
        }
        return null
    }

    // 外部高度
    outerHeight (includeMargins) {
        if (this.length > 0) {
            const ele = this[0]
            if (includeMargins) {
                return ele.offsetHeight + parseFloat(this.css('margin-top')) + parseFloat(this.css('margin-bottom'))
            }
            return ele.offsetHeight
        }
        return null
    }

    // 位置偏移
    offset () {
        if (this.length > 0) {
            const ele = this[0]
            const rect = ele.getBoundingClientRect()
            const body = document.body
            const clientTop = ele.clientTop || body.clientTop || 0
            const clientLeft = ele.clientLeft || body.clientLeft || 0
            const scrollTop = ele === window ? window.scrollY : ele.scrollTop
            const scrollLeft = ele === window ? window.scrollX : ele.scrollLeft
            return {
                top: (rect.top + scrollTop) - clientTop,
                left: (rect.left + scrollLeft) - clientLeft
            }
        }
        return null
    }

    // 获取，设置节点innerHTML
    html (html) {
        if (typeof html === 'undefined') {
            return this[0] ? this[0].innerHTML : undefined
        }
        forEach(this, ele => {
            ele.innerHTML = html
        })
        return this
    }

    // 获取，设置节点textContent
    text (text) {
        if (typeof text === 'undefined') {
            return this[0] ? this[0].textContent.trim() : undefined
        }
        forEach(this, ele => {
            ele.textContent = text
        })
        return this
    }

    // 获取， 设置value值
    val (value) {
        const mulSelect = (ele) => ele.multiple && ele.nodeName.toLowerCase() === 'select'
        if (typeof value === 'undefined') {
            const ele = this[0]
            if (ele) {
                if (mulSelect(ele) && ele.selectedOptions) {
                    const values = []
                    forEach(ele.selectedOptions, el => {
                        values.push(el.value)
                    })
                    return values
                }
                return ele.value
            }
            return undefined
        }
        forEach(this, ele => {
            if (Array.isArray(value) && mulSelect(ele)) {
                forEach(ele.options, el => {
                    el.selected = value.indexOf(el.value) >= 0
                })
            } else {
                ele.value = value
            }
        })
        return this
    }

    // 隐藏节点
    hide (...args) {
        if (args[0]) {
            return this.slideUp(...args)
        }
        forEach(this, ele => {
            ele.style.display = 'none'
        })
        return this
    }

    // 显示节点
    show (...args) {
        if (args[0]) {
            return this.slideDown(...args)
        }
        forEach(this, ele => {
            if (ele.style.display === 'none') {
                ele.style.display = ''
            }
            if (window.getComputedStyle(ele, null).getPropertyValue('display') === 'none') {
                ele.style.display = 'block'
            }
        })
        return this
    }

    // 返回某个位置的dom对象
    eq (index) {
        if (typeof index !== 'number') return this
        const length = this.length
        if (index > length - 1) {
            return new Dom([])
        }
        if (index < 0) {
            const rIndex = length + index
            if (rIndex < 0) return new Dom([])
            return new Dom([this[rIndex]])
        }
        return new Dom([this[index]])
    }

    // 获取节点位置
    index (selector) {
        const ele = this[0]
        if (!ele) {
            return -1
        }
        if (typeof selector === 'string') {
            return indexOfEle(document.querySelectorAll(selector), ele)
        } else if (selector instanceof Dom) {
            return indexOfEle(this, selector[0])
        } else {
            let child = ele
            let i = 0
            for (;;) {
                child = child.previousSibling
                if (!child) break
                if (child.nodeType === 1) i++
            }
            return i
        }
    }

    // 节点匹配
    is (selector) {
        const ele = this[0]
        if (!ele || !selector) return false
        if (typeof selector === 'string') {
            if (ele.matches) {
                return ele.matches(selector)
            } else {
                return false
            }
        } else if (selector === document) {
            return ele === document
        } else if (selector === window) {
            return ele === window
        } else if (selector.nodeType || selector instanceof Dom) {
            let compareWith = selector.nodeType ? [selector] : selector
            for (let i = 0; i < compareWith.length; i++) {
                if (compareWith[i] === ele) return true
            }
            return false
        }
        return false
    }

    // 删除节点
    remove (selector) {
        forEach(this, ele => {
            if ((selector && ele.matches(selector)) || !selector) {
                ele.parentNode && ele.parentNode.removeChild(ele)
            }
        })
        return this
    }

    // 删除所有子节点
    empty () {
        forEach(this, ele => {
            ele.innerHTML = ''
        })
    }

    // 串联
    add (...args) {
        const self = this
        forEach(args, ele => {
            $(ele).each((n, el) => {
                self[self.length] = el
                self.length += 1
            })
        })
        return self
    }

    // 节点查找
    find (selector) {
        const elements = []
        if (typeof selector !== 'string') {
            return new Dom(elements)
        }
        forEach(this, ele => {
            const found = ele.querySelectorAll(selector)
            forEach(found, el => {
                elements.push(el)
            })
        })
        return new Dom(elements)
    }

    // 追加节点
    append (content) {
        return operation.appendElement(this, 'append', content)
    }

    // 新增节点
    appendTo (parent) {
        $(parent).append(this)
        return this
    }

    // 内部插入
    prepend (content) {
        return operation.appendElement(this, 'prepend', content)
    }

    // 内部插入到
    prependTo (parent) {
        $(parent).prepend(this)
        return this
    }

    // 之前追加节点
    before (content) {
        return operation.appendElement(this, 'before', content)
    }

    // 节点之前插入
    insertBefore (selector) {
        $(selector).before(this)
        return this
    }

    // 之后追加节点
    after (content) {
        return operation.appendElement(this, 'after', content)
    }

    // 节点之后插入
    insertAfter (selector) {
        $(selector).after(this)
        return this
    }

    // 下一个节点
    next (selector) {
        return operation.nextPrevFn(this, selector, true)
    }

    // 同级下一个全部节点
    nextAll (selector) {
        return operation.nextPrevAllFn(this, selector, true)
    }

    // 下一个节点
    prev (selector) {
        return operation.nextPrevFn(this, selector)
    }

    // 同级上一个全部节点
    prevAll (selector) {
        return operation.nextPrevAllFn(this, selector)
    }

    // 所有同级节点
    siblings (selector) {
        return this.nextAll(selector).add(this.prevAll(selector))
    }

    // 父节点
    parent (selector) {
        const parents = []
        forEach(this, ele => {
            const parent = ele.parentNode
            if (parent) {
                if ((selector && $(parent).is(selector)) || !selector) {
                    parents.push(parent)
                }
            }
        })
        // 去重
        return $([...new Set(parents)])
    }

    // 所有父节点
    parents (selector) {
        const parents = []
        forEach(this, ele => {
            let parent = ele.parentNode
            while (parent) {
                if (selector) {
                    if ($(parent).is(selector)) parents.push(parent)
                } else {
                    parents.push(parent)
                }
                parent = parent.parentNode
            }
        })
        return $([...new Set(parents)])
    }

    // 查找本身，或者往上查找
    closest (selector) {
        if (!selector) {
            return new Dom([])
        }
        for (let i = 0; i < this.length; i++) {
            const ele = $(this[i])
            if (ele.is(selector)) return ele
        }
        return this.parents(selector).eq(0)
    }

    // 寻找子节点
    children (selector) {
        const children = []
        forEach(this, ele => {
            forEach(ele.childNodes, el => {
                if (el.nodeType === 1 && (!selector || el.matches(selector))) {
                    children.push(el)
                }
            })
        })
        return new Dom([...new Set(children)])
    }

    // 转换dom为数组
    toArray () {
        const arr = []
        forEach(this, ele => {
            arr.push(ele)
        })
        return arr
    }

    // 循环
    each (callback) {
        if (typeof callback !== 'function') {
            return this
        }
        for (let i = 0; i < this.length; i++) {
            if (callback.call(this[i], i, this[i]) === false) {
                return this
            }
        }
        return this
    }

    // 过滤
    filter (callback) {
        const arr = []
        forEach(this, (ele, i) => {
            if (callback.call(ele, i, ele)) {
                arr.push(ele)
            }
        })
        return new Dom(arr)
    }

    // 历遍
    map (callback) {
        const arr = []
        forEach(this, (ele, i) => {
            arr.push(callback.call(ele, i, ele))
        })
        return new Dom(arr)
    }
}

// 节点解析
$ = (selector, context) => {
    const resArr = []
    const pusArrFn = (arr) => {
        arr = arr || []
        forEach(arr, (ele) => {
            if (ele) resArr.push(ele)
        })
    }
    if (selector && !context) {
        if (selector instanceof Dom) {
            return selector
        }
    }
    if (selector) {
        if (typeof selector === 'string') {
            const html = selector.trim()
            // html字符串
            if (/^\<.*\>$/.test(html)) {
                let cTag = 'div'
                const tag = ['tr', 'th', 'td', 'tbody', 'option']
                const pTag = ['tbody', 'tr', 'tr', 'table', 'select']
                tag.find((str, n) => {
                    const res = html.indexOf('<' + str) === 0
                    if (res) {
                        cTag = pTag[n]
                    }
                    return res
                })
                const $div = document.createElement(cTag)
                $div.innerHTML = html
                pusArrFn($div.childNodes)
            } else {
                // 获取contxt
                if (context) {
                    if (context instanceof Dom) {
                        context = context[0]
                    }
                } else {
                    context = document
                }
                try {
                    const els = context.querySelectorAll(selector.trim())
                    pusArrFn(els)
                } catch (error) {
                    // error
                }
            }
        } else if (selector.nodeType || selector === window || selector === document) {
            // dom 节点
            resArr.push(selector)
        } else if (selector.length > 0 && selector[0].nodeType) {
            // 节点数组
            pusArrFn(selector)
        }
    }
    return new Dom(resArr)
}

$.fn = Dom.prototype
$.Class = Dom

// 获取事件参数
const eventArgs = (args) => {
    let [eventType, targetSelector, listener, capture] = args
    if (typeof args[1] === 'function') {
        [eventType, listener, capture] = args
        targetSelector = undefined
    }
    if (!capture) capture = false
    return [eventType, targetSelector, listener, capture]
}
// 处理eventType
const eventTypeHanlde = (eventType, callback) => {
    eventType.split(/\s+/).forEach(str => {
        const arr = str.split(/[\:\-\_\.]+/)
        const type = arr[0]
        const name = arr[1] || '_default_'
        callback && callback(type, name)
    })
}
// 事件处理
const eventMethods = {
    // 绑定
    on (...args) {
        const self = this
        let [eventType, targetSelector, listener, capture] = eventArgs(args)
        // live
        const handleLiveEvent = (e) => {
            const target = e.target
            if (!target) return
            const eventData = e.target.domEventData || []
            if (eventData.indexOf(e) < 0) {
                eventData.unshift(e)
            }
            // expr表达式
            if ($(target).is(targetSelector)) {
                listener.apply(target, eventData)
            } else {
                $(target).parents().each((i, ele) => {
                    if ($(ele).is(targetSelector)) {
                        listener.apply(ele, eventData)
                    }
                })
            }
        }
        // on
        const handleEvent = (e) => {
            // 获取传入的参数数据
            const eventData = e && e.target ? e.target.domEventData || [] : []
            if (eventData.indexOf(e) < 0) {
                eventData.unshift(e)
            }
            listener.apply(this, eventData)
        }
        // 绑定
        const handleFn = targetSelector ? handleLiveEvent : handleEvent
        const domListeners = targetSelector ? 'domLiveListeners' : 'domListeners'
        eventTypeHanlde(eventType, (type, name) => {
            forEach(self, ele => {
                if (!ele[domListeners]) ele[domListeners] = {}
                if (!ele[domListeners][type]) ele[domListeners][type] = {}
                if (!ele[domListeners][type][name]) ele[domListeners][type][name] = [] // 命名空间
                ele[domListeners][type][name].push({
                    listener,
                    proxyListener: handleFn
                })
                ele.addEventListener(type, handleFn, capture)
            })
        })
        return this
    },
    // 解除事件绑定
    off (...args) {
        const self = this
        let [eventType, targetSelector, listener, capture] = eventArgs(args)
        eventTypeHanlde(eventType, (type, name) => {
            forEach(self, ele => {
                const domListeners = targetSelector ? 'domLiveListeners' : 'domListeners'
                let handlers = ele[domListeners] && ele[domListeners][type] && ele[domListeners][type][name]
                handlers = handlers || []
                for (let k = handlers.length - 1; k >= 0; k -= 1) {
                    const handler = handlers[k]
                    if ((listener && handler.listener === listener) || !listener) {
                        ele.removeEventListener(type, handler.proxyListener, capture)
                        handlers.splice(k, 1)
                    }
                }
            })
        })
        return this
    },
    // 只绑定一次
    one (...args) {
        const self = this
        let [eventType, targetSelector, listener, capture] = eventArgs(args)
        function proxy (...eventArgs) {
            listener.apply(this, eventArgs)
            self.off(eventType, targetSelector, proxy, capture)
        }
        return this.on(eventType, targetSelector, proxy, capture)
    },
    // 触发事件
    trigger (...args) {
        const self = this
        let [eventType, eventData] = args
        if (!eventType || typeof eventType !== 'string') return this
        eventTypeHanlde(eventType, type => {
            forEach(self, ele => {
                let evt
                try {
                    evt = new window.CustomEvent(type, {
                        detail: eventData, // 这里如果浏览器支持，直接传递信息
                        bubbles: true,
                        cancelable: true
                    })
                } catch (e) {
                    // err
                    evt = document.createEvent('Event')
                    evt.initEvent(type, true, true)
                    evt.detail = eventData
                }
                // 传递参数
                ele.domEventData = args.filter((a, i) => i > 0)
                ele.dispatchEvent(evt)
                // 清除参数信息
                ele.domEventData = []
                delete ele.domEventData
            })
        })
        return this
    }
}
// 常用事件添加
const elementEventType = 'click blur focus focusin focusout keyup keydown keypress submit change mousedown mousemove mouseup mouseenter mouseleave mouseout mouseover touchstart touchend touchmove resize scroll'.split(' ')
const noTrigger = 'resize scroll'.split(' ')
elementEventType.forEach(type => {
    $.fn[type] = function (...args) {
    // 没有参数时
        if (!args[0]) {
            this.each((n, ele) => {
                if (noTrigger.indexOf(type) < 0) {
                    if (type in ele) {
                        ele[type]() // 系统自带
                    } else {
                        $(ele).trigger(type)
                    }
                }
            })
            return this
        }
        return this.on(type, ...args)
    }
})

// 简单动画操作
// const animeType = ['fadeIn', 'fadeOut', 'fadeToggle', 'slideDown', 'slideUp', 'slideToggle']
// animeType.forEach(str => {
//   $.fn[str] = function(duration, easing, complete) {
//     if (duration === 'slow') duration = 600
//     if (duration === 'normal') duration = 400
//     if (duration === 'fast') duration = 200
//     if (typeof duration === 'string') {
//       easing = duration
//       duration = ''
//     }
//     if (typeof duration === 'function') {
//       complete = duration
//       duration = ''
//     }
//     if (typeof easing === 'function') {
//       complete = easing
//       easing = ''
//     }
//     if (typeof duration !== 'number') {
//       duration = 400
//     }
//     if (typeof easing !== 'string' || !easing) {
//       easing = 'linear'
//     }
//     const attr = str.indexOf('fade') > -1 ? 'opacity' : 'height'
//     const toggleSign = str.indexOf('Toggle') > -1 ? 1 : 0
//     const callback = (ele, sign) => {
//       if (sign === false) {
//         ele.style.display = 'none'
//       }
//       if (attr === 'opacity') {
//         ele.style.opacity = ''
//       } else {
//         ele.style.height = ''
//       }
//       complete && complete(ele)
//     }
//     forEach(this, ele => {
//       let sign = false // 获取当前状态
//       let eHt = 0
//       if (['fadeIn', 'slideDown'].indexOf(str) > -1) {
//         sign = true
//       }
//       if (toggleSign) {
//         if (attr === 'opacity') {
//           if (parseInt($(ele).css('opacity'), 10) !== 1) {
//             sign = true
//           }
//         } else {
//           if (ele.offsetHeight === 0) {
//             sign = true
//           }
//         }
//         if ($(ele).css('display') === 'none') {
//           sign = true
//         }
//       }
//       $(ele).show()
//       if (attr === 'opacity') {
//         ele.style.opacity = sign ? 0 : 1
//       } else {
//         eHt = ele.offsetHeight
//         ele.style.height = sign ? 0 : ''
//       }
//       if (duration > 0) {
//         const json = {
//           targets: ele,
//           easing: easing,
//           duration: duration,
//           complete() {
//             callback(ele, sign)
//           }
//         }
//         if (attr === 'opacity') {
//           json.opacity = sign ? 1 : 0
//         } else {
//           json.height = sign ? eHt + 'px' : 0
//         }
//         anime(json)
//       } else {
//         callback(ele, sign)
//       }
//     })
//     return this
//   }
// })

// prototype绑定
Object.keys(eventMethods).forEach(key => {
    $.fn[key] = eventMethods[key]
})

const query = $

export default query
