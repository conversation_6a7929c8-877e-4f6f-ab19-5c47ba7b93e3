<template>
  <a-skeleton
    active
    :loading="loading"
    :paragraph="{rows: 17}"
  >
    <a-card :bordered="false">
      <a-alert
        type="info"
        :show-icon="true"
      >
        <div slot="message">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_lastUpdated`, '上次更新时间') }}：{{ this.time }}
          <a-divider type="vertical" />
          <a @click="handleClickUpdate">{{ $srmI18n(`${$getLangAccount()}#i18n_title_nowUpdated`, '立即更新') }}</a>
        </div>
      </a-alert>

      <a-table
        row-key="id"
        size="middle"
        :columns="columns"
        :data-source="dataSource"
        :pagination="false"
        :loading="tableLoading"
        style="margin-top: 20px;"
      >
        <template
          slot="param"
          slot-scope="text, record"
        >
          <a-tag :color="textInfo[record.param].color">
            {{ text }}
          </a-tag>
        </template>

        <template
          slot="text"
          slot-scope="text, record"
        >
          {{ textInfo[record.param].text }}
        </template>

        <template
          slot="value"
          slot-scope="text, record"
        >
          {{ text }} {{ textInfo[record.param].unit }}
        </template>
      </a-table>
    </a-card>
  </a-skeleton>
</template>
<script>
import moment from 'moment'
import { getAction } from '@/api/manage'
import { Tag } from 'ant-design-vue'
import { srmI18n, getLangAccount } from '@/utils/util'

moment.locale('zh-cn')

export default {
    components: {
        ATag: Tag
    },
    data () {
        return {
            time: '',
            loading: true,
            tableLoading: true,
            columns: [{
                title: srmI18n(`${getLangAccount()}#i18n_title_params`, '参数'),
                width: '30%',
                dataIndex: 'param',
                scopedSlots: { customRender: 'param' }
            }, {
                title: srmI18n(`${getLangAccount()}#i18n_title_describe`, '描述'),
                width: '40%',
                dataIndex: 'text',
                scopedSlots: { customRender: 'text' }
            }, {
                title: srmI18n(`${getLangAccount()}#i18n_title_currentValue`, '当前值'),
                width: '30%',
                dataIndex: 'value',
                scopedSlots: { customRender: 'value' }
            }],
            dataSource: [],
            // 列表通过 textInfo 渲染出颜色、描述和单位
            textInfo: {
                'jvm.memory.max': { color: 'purple', text: srmI18n(`${getLangAccount()}#i18n_title_maxMemory`, 'JVM 最大内存'), unit: 'MB' },
                'jvm.memory.committed': { color: 'purple', text: srmI18n(`${getLangAccount()}#i18n_title_availableMemory`, 'JVM 可用内存'), unit: 'MB' },
                'jvm.memory.used': { color: 'purple', text: srmI18n(`${getLangAccount()}#i18n_title_usedMemory`, 'JVM 已用内存'), unit: 'MB' },
                'jvm.buffer.memory.used': { color: 'cyan', text: srmI18n(`${getLangAccount()}#i18n_title_usedBufferMemory`, 'JVM 缓冲区已用内存'), unit: 'MB' },
                'jvm.buffer.count': { color: 'cyan', text: srmI18n(`${getLangAccount()}#i18n_title_bufferCount`, '当前缓冲区数量'), unit: srmI18n(`${getLangAccount()}#i18n_title_individual`, '个') },
                'jvm.threads.daemon': { color: 'green', text: srmI18n(`${getLangAccount()}#i18n_title_threadsDaemon`, 'JVM 守护线程数量'), unit: srmI18n(`${getLangAccount()}#i18n_title_individual`, '个') },
                'jvm.threads.live': { color: 'green', text: srmI18n(`${getLangAccount()}#i18n_title_threadsLive`, 'JVM 当前活跃线程数量'), unit: srmI18n(`${getLangAccount()}#i18n_title_individual`, '个') },
                'jvm.threads.peak': { color: 'green', text: srmI18n(`${getLangAccount()}#i18n_title_threadsPeak`, 'JVM 峰值线程数量'), unit: srmI18n(`${getLangAccount()}#i18n_title_individual`, '个') },
                'jvm.classes.loaded': { color: 'orange', text: srmI18n(`${getLangAccount()}#i18n_title_threadsLoaded`, 'JVM 已加载 Class 数量'), unit: srmI18n(`${getLangAccount()}#i18n_title_individual`, '个') },
                'jvm.classes.unloaded': { color: 'orange', text: srmI18n(`${getLangAccount()}#i18n_title_threadsUnloaded`, 'JVM 未加载 Class 数量'), unit: srmI18n(`${getLangAccount()}#i18n_title_individual`, '个') },
                'jvm.gc.memory.allocated': { color: 'pink', text: srmI18n(`${getLangAccount()}#i18n_title_memoryAllocated`, 'GC 时, 年轻代分配的内存空间'), unit: 'MB' },
                'jvm.gc.memory.promoted': { color: 'pink', text: srmI18n(`${getLangAccount()}#i18n_title_memoryPromoted`, 'GC 时, 老年代分配的内存空间'), unit: 'MB' },
                'jvm.gc.max.data.size': { color: 'pink', text: srmI18n(`${getLangAccount()}#i18n_title_gcMaxDataSize`, 'GC 时, 老年代的最大内存空间'), unit: 'MB' },
                'jvm.gc.live.data.size': { color: 'pink', text: srmI18n(`${getLangAccount()}#i18n_title_liveDataSize`, 'FullGC 时, 老年代的内存空间'), unit: 'MB' },
                'jvm.gc.pause.count': { color: 'blue', text: srmI18n(`${getLangAccount()}#i18n_title_pauseCount`, 'FullGC 时, 系统启动以来GC 次数'), unit: srmI18n(`${getLangAccount()}#i18n_title_count`, '次')},
                'jvm.gc.pause.totalTime': { color: 'blue', text: srmI18n(`${getLangAccount()}#i18n_title_pauseTotalTime`, '系统启动以来GC 总耗时'), unit: srmI18n(`${getLangAccount()}#i18n_title_second`, '秒') }
            },
            // 当一条记录中需要取出多条数据的时候需要配置该字段
            moreInfo: {
                'jvm.gc.pause': ['.count', '.totalTime']
            }
        }
    },
    mounted () {
        this.loadTomcatInfo()
    },
    methods: {

        handleClickUpdate () {
            this.loadTomcatInfo()
        },

        loadTomcatInfo () {
            this.tableLoading = true
            this.time = moment().format('YYYY年MM月DD日 HH时mm分ss秒')
            Promise.all([
                getAction('actuator/metrics/jvm.memory.max'),
                getAction('actuator/metrics/jvm.memory.committed'),
                getAction('actuator/metrics/jvm.memory.used'),
                getAction('actuator/metrics/jvm.buffer.memory.used'),
                getAction('actuator/metrics/jvm.buffer.count'),
                getAction('actuator/metrics/jvm.threads.daemon'),
                getAction('actuator/metrics/jvm.threads.live'),
                getAction('actuator/metrics/jvm.threads.peak'),
                getAction('actuator/metrics/jvm.classes.loaded'),
                getAction('actuator/metrics/jvm.classes.unloaded'),
                getAction('actuator/metrics/jvm.gc.memory.allocated'),
                getAction('actuator/metrics/jvm.gc.memory.promoted'),
                getAction('actuator/metrics/jvm.gc.max.data.size'),
                getAction('actuator/metrics/jvm.gc.live.data.size'),
                getAction('actuator/metrics/jvm.gc.pause')
            ]).then((res) => {

                let info = []
                res.forEach((value, id) => {
                    let more = this.moreInfo[value.name]
                    if (!(more instanceof Array)) {
                        more = ['']
                    }
                    more.forEach((item, idx) => {
                        let param = value.name + item
                        let val = value.measurements[idx].value

                        if (param === 'jvm.memory.max'
                || param === 'jvm.memory.committed'
                || param === 'jvm.memory.used'
                || param === 'jvm.buffer.memory.used'
                || param === 'jvm.gc.memory.allocated'
                || param === 'jvm.gc.memory.promoted'
                || param === 'jvm.gc.max.data.size'
                || param === 'jvm.gc.live.data.size'
                        ) {
                            val = this.convert(val, Number)
                        }
                        info.push({ id: param + id, param, text: 'false value', value: val })
                    })
                })
                this.dataSource = info


            }).catch((e) => {
                console.error(e)
                this.$message.error(srmI18n(`${getLangAccount()}#i18n_title_feactJvmError`, '获取JVM信息失败'))
            }).finally(() => {
                this.loading = false
                this.tableLoading = false
            })
        },

        convert (value, type) {
            if (type === Number) {
                return Number(value / 1048576).toFixed(3)
            } else if (type === Date) {
                return moment(value * 1000).format('YYYY-MM-DD HH:mm:ss')
            }
            return value
        }
    }
}
</script>
<style></style>
