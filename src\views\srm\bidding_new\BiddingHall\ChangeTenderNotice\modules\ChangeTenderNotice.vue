<template>
  <div class="ChangeTenderNotice">
    <titleTrtl class="margin-b-10">
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_AHRx_27a9a77a`, '变更公告') }}</span>
      <template slot="right">
        <a-button
          v-if="typePage != 'detail' && applyRoleCanEdit"
          type="primary"
          @click="handleAddNotice">{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}</a-button>
      </template>
    </titleTrtl>
    <div>
      <listTable
        ref="listTable"
        :setGridHeight="pageContentHeight"
        :pageData="pageData"
        :url="url"
        :defaultParams="defaultParams"
        :statictableColumns="tableColumns"
        :showTablePage="false" />
    </div>
  </div>
</template>
<script>
import titleTrtl from '../../components/title-crtl'
import listTable from '../../components/listTable'
export default {
    name: 'ChangeTenderNotice',
    components: {
        titleTrtl,
        listTable
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentNode'],
    props: {
        typePage: {
            type: String,
            default () {
                return ''
            }
        }
    },
    data () {
        return {
            tableColumns: [
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeOrderNo`, '变更单号'),
                    field: 'noticeChangeNumber'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHRxBD_e3eae26b`, '变更公告标题'),
                    field: 'noticeTitle'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeTimes`, '变更时间'),
                    field: 'updateTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    field: 'noticeStatus_dictText'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQUz_2fba950f`, '是否审批'),
                    field: 'audit_dictText'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm925b_auditStatus`, '审批状态'),
                    field: 'auditStatus_dictText'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 200,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit }
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            }
            // url: {
            //     list: '/tender/purchaseTenderNoticeHead/list'
            // }
        }
    },
    computed: {
        url () {
            const list = this.typePage == 'detail' ? '/tender/purchaseTenderNoticeHead/sale/queryList' : '/tender/purchaseTenderNoticeHead/list'
            return { list: list }
        },
        pageContentHeight () {
            let height = document.documentElement.clientHeight - 170
            return height + 'px'
        },
        applyRoleCanEdit () {
            return this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'
        },
        defaultParams () {
            let {
                nodeId,
                extend: { checkType, processType, currentStep, noticeType }
            } = this.currentNode()
            // const noticeType = checkType == '0' ? '4' : '3'
            if (this.typePage == 'detail') {
                return { noticeType: noticeType, subpackageId: this.subpackageId() }
            } else {
                return { noticeType: noticeType, subpackageId: this.subpackageId() }
            }
        }
    },
    methods: {
        handleAddNotice () {
            this.$emit('handNoticeEditPage', {})
        },
        handleEdit (row) {
            this.$emit('handNoticeEditPage', row)
        },
        handleView (row) {
            this.$emit('handleNoticeViewPage', row)
        },
        // handlePublish (row) {
        //     var that=this
        //     this.confirmLoading = true
        //     postAction(that.url.publish+'?id='+row.id).then(res => {
        //         if (res.success) {
        //             this.$message.success(res.message)
        //             this.$refs.listTable.loadData()
        //             // this.$emit('hide')
        //         }
        //     }).finally(() => {
        //         this.confirmLoading = false
        //     })

        // },
        // handleDelete (row){
        //     this.confirmLoading = true
        //     getAction(this.url.delete, {id: row.id}).then(res => {
        //         if (res.success) {
        //             this.$message.success(res.message)
        //             this.$refs.listTable.loadData()
        //         }
        //     }).finally(() => {
        //         this.confirmLoading = false
        //     })
        // },
        allowEdit ({ noticeStatus }) {
            // 招标公告状态:0-新建,1-待发布,2-已发布,3-已变更
            if (noticeStatus === '0' && this.typePage != 'detail') {
                if (!this.applyRoleCanEdit) return true
                return false
            } else {
                return true
            }
        }
        // allowPublish ({status}){
        //     // 单据状态为“新建0”、或“未发布1” ，则允许发布
        //     if ((status==='1')) {
        //         return false
        //     } else {
        //         return true
        //     }
        // },
        // allowDelete ({status}){
        //     // 单据状态为“新建0”、或“未发布1” ，则允许删除
        //     if ((status==='0' || status==='1')) {
        //         return false
        //     } else {
        //         return true
        //     }
        // }
    },
    created () {
        console.log('typePage', this.typePage)
    }
}
</script>
<style lang="less" scoped>
.margin-b-10 {
    margin-bottom: 5px;
}
</style>
