<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      :pageData="pageData"
      :url="url" />

  </div>
</template>
<script>

import {ListMixin} from '@comp/template/list/ListMixin'

export default {
    mixins: [ListMixin],
    components: {
    },
    data () {
        return {
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    }
                ],
                button: [
                    {authorityCode: 'bulkMaterialRelation#bulkMaterialRelation:import', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt`, '导入'), icon: 'import', type: 'upload', clickFn: this.importExcel},
                    {authorityCode: 'bulkMaterialRelation#bulkMaterialRelation:getDataByErp', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SMWWWWF_b44eb4e2`, '获取ERP数据'), icon: 'arrow-down', clickFn: this.getDataByErp},
                    {authorityCode: 'bulkMaterialRelation#bulkMaterialRelation:pushDataToErp', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'), icon: 'arrow-up', clickFn: this.pushDataToERP},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                showOptColumn: false,
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/thirddata/bulkMaterialRelation/list',
                excelCode: 'bulkMaterialRelationImportRpcImpl',
                columns: 'bulkMaterialRelationList',
                getDataByErpUrl: '/thirddata/bulkMaterialRelation/getDataByErp',
                pushDataToERPUrl: '/thirddata/bulkMaterialRelation/pushDataToErp'
            }
        }
    },
    methods: {

    }
}
</script>