<template>
  <div class="page detail-page">
    <div class="page-header">
      <a-page-header :ghost="false"
      >
        <template slot="extra">
          <taskBtn
            v-if="taskInfo.taskId"
            :currentEditRow="currentEditRow"
            :pageHeaderButtons="publicBtn"
            v-on="$listeners"/>
        </template>
        <template slot="tags">
          <a-tag
            color="blue" 
            style="font-size:16px">
            {{ subpackageTitle }}
          </a-tag>
        </template>
      </a-page-header>
    </div>
    <a-spin :spinning="confirmLoading">
      <div
        class="container"
        :style="style">
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBLVH_7e1ecb2e`, '中标人信息') }}</span>
          </titleTrtl>
          <vxe-grid
            v-bind="gridConfig"
            ref="table"
            :data="tableData"
            :columns="tableColumns"
            show-overflow="title" >
          </vxe-grid>
        </div>
      </div>
    </a-spin>

    <a-modal
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '@views/srm/bidding_new/BiddingHall/components/content-header'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import {getAction, postAction} from '@/api/manage'
import { mapGetters } from 'vuex'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
export default {
    name: 'DetermineTheWinner',
    components: {
        titleTrtl,
        ContentHeader,
        taskBtn
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        },
        resultData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            gridConfig: {
                border: true,
                resizable: true,
                autoResize: true,
                keepSource: true,
                showOverflow: true,
                showHeaderOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                data: [],
                mouseConfig: {
                    area: true, // 是否开启单元格区域选取
                    extension: true // 是否开启右下角延伸按钮
                },
                clipConfig: {
                    isCut: false,
                    isPaste: false
                },
                keyboardConfig: {
                    isClip: true, // 是否开启复制粘贴功能
                    isEdit: true, // 是否开启单元格选择编辑
                    isTab: true, // 是否开启TAB键左右移动功能
                    isArrow: true, // 是否开启非编辑状态下，上下左右移动功能
                    isEnter: true, // 是否开启回车移动上下行移动
                    isDel: true, // 是否开启删除键功能
                    isMerge: true, // 是否开合并和取消合并功能
                    isFNR: true, // 是否开启查找和替换功能
                    isChecked: true, // 是否开启空格键切换复选框和单选框状态
                    enterToTab: false // 是否将回车键行为改成 Tab 键行为
                },
                checkboxConfig: {
                    highlight: true,
                    reserve: true,
                    trigger: 'cell'
                },
                editConfig: {
                    trigger: 'click',
                    mode: 'cell',
                    showStatus: true
                }
            },
            subpackageTitle: '',
            auditVisible: false,
            currentUrl: '',
            opinion: '',
            okText: '',
            flowView: false,
            flag: false,
            confirmLoading: false,
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    field: 'scopeSort',
                    width: '80'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQsBL_c7668749`, '是否中标人'),
                    'field': 'affirm',
                    width: 120,
                    slots: {
                        default: ({row, column}) => {
                            return [
                                <a-checkbox disabled={this.pageStatus == 'detail'}v-model={row[column.property]} ></a-checkbox>
                            ]
                        }
                    }
                }
            ],
            publicBtn: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    type: 'primary',
                    click: this.auditPass,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    type: '',
                    click: this.auditReject,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    type: '',
                    click: this.showFlow,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'rollBack',
                    click: this.goBackAudit,
                    showCondition: () => {
                        return true
                    }
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            url: {
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryById: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryById'
            }
        }
    },
    computed: {
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        ...mapGetters([
            'taskInfo'
        ]),
        pageStatus () {
            
            if (this.formData.status == '0' || !this.formData.status) {
                return 'edit'
            } else {
                return 'detail'
            }
            
        }
    },
    methods: {
        getColumn () {
            let params = {
                'affirmType': '0',
                subpackageId: this.formData.subpackageId || ''
            }
            this.confirmLoading = true
            getAction(this.url.queryPrice, params).then(res => {
                if (res.code == 200 && res.result) {
                    const resultData = res.result
                    let columns = []
                    resultData.forEach(data => {
                        let obj = {
                            title: data.title,
                            children: []
                        }
                        let columnChildren = []
                        data.quoteColumnList.forEach(column => {
                            column['field'] = `${column['field']}_${data['id']}`
                            columnChildren.push(column)
                        })
                        obj.children = columnChildren
                        columns.push(obj)
                    })
                    
                    this.tableColumns = this.tableColumns.filter(column => {
                        if (!column.hasOwnProperty('children')) {
                            return column
                        }
                    })
                    this.tableColumns.splice(2, 0, ...columns)
                }
                this.confirmLoading = false
            }, () => {
                this.confirmLoading = false
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            console.log('this.resultData', this.resultData)
            let {bidWinningAffirmPriceItemVoList = [], ...others} = this.resultData
            this.formData = others
            bidWinningAffirmPriceItemVoList && bidWinningAffirmPriceItemVoList.forEach(item => {
                item.affirm = item.affirm == '1' ? true : false
                item.saleQuoteColumnVOS.forEach(vos => {
                    item[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                })
            })
            this.tableData = bidWinningAffirmPriceItemVoList
            console.log('*********', this.tableData)
            //评标方式 0-全部、1-线上、2-线下*/
            let {evaluationType} = this.resultData
            if (evaluationType !== '2') {
                this.getColumn()
            } else {
                this.tableColumns.splice(1, 0, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                    field: 'quote'
                })
            }
        },
        /**
     * 审批方法
     */
        goBackAudit () {
            this.$emit('goBackAudit')
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass () {
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject () {
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk () {
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = 'purchaseProduct'
            param['businessId'] = this.currentEditRow.businessId
            param['taskId'] = this.currentEditRow.taskId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            this.confirmLoading = true
            postAction(this.currentUrl, param)
                .then(res => {
                    if (res.success) {
                        this.auditVisible = false
                        this.$message.success(res.message)
                        this.$parent.reloadAuditList()
                        this.goBackAudit()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    mounted () {
        this.subpackageTitle = this.currentEditRow.subject || ''
        this.init()
    }
}
</script>
<style lang="less" scoped>
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
</style>




