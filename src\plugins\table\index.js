/*
 * @Author: your name
 * @Date: 2021-03-29 14:02:20
 * @LastEditTime: 2021-06-25 16:33:33
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\plugins\table\index.js
 */
import Vue from 'vue'
import XEClipboard from 'xe-clipboard'
import VXETable from 'vxe-table'
import VXETablePluginAntd from 'vxe-table-plugin-antd'
import 'vxe-table/lib/style.css'
import 'vxe-table-plugin-antd/dist/style.css'
import './pro/vxe-table-pro.es6.min'
import './pro/vxe-table-pro.min.css'
import './defaults'

import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/pie'
import 'echarts/lib/chart/line'
import 'echarts/lib/component/grid'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/legendScroll'

import VXETablePluginMenus from 'vxe-table-plugin-menus'
import VXETablePluginCharts from 'vxe-table-plugin-charts'
import 'vxe-table-plugin-charts/dist/style.css'

Vue.use(VXETable)
import '@/basicSettings/vt.config'
// 给 vue 实例挂载内部对象
// Vue.prototype.$XModal = VXETable.modal
// Vue.prototype.$XPrint = VXETable.print
// Vue.prototype.$XSaveFile = VXETable.saveFile
// Vue.prototype.$XReadFile = VXETable.readFile

// 给右键菜单的复制按钮设置为剪贴板操作函数
VXETable.use(VXETablePluginMenus, {
    copy: XEClipboard.copy
})
VXETable.use(VXETablePluginCharts)
VXETable.use(VXETablePluginAntd)
