<template>
  <div>
    <a-modal
      centered
      v-model="showNode"
      :width="800"
      :footer="null"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_sudV_2e111e11`, '报价详情')">
      <listTable
        ref="listTable"
        :fromSourceData="tableData"
        :mergeRowMethod="mergeRowMethod"
        :showTablePage="false"
        :statictableColumns="statictableColumns"></listTable>
    </a-modal>
  </div>
</template>
<script lang="jsx">
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
export default {
    props: {
        fromSourceData: {
            default: () => {
                return []
            },
            type: Array
        }
    },
    components: {
        listTable
    },
    data () {
        return {
            statictableColumns: [
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'), 'field': 'supplierName'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), 'field': 'materialName'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBsu_2e62dc84`, '投标报价'), 'field': 'quote'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBu_2199294`, '评标价'), 'field': 'evaPrice'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_esAR_30ba8883`, '最终排名'), 'field': 'orderBy'}
            ],
            showNode: false,
            tableData: []
        }
    },
    methods: {
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            // const fields = ['supplierAccount']
            const fields = ['supplierName']
            const cellValue = row['supplierAccount']
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow['supplierAccount'] === cellValue) {
                    return { rowspan: 0, colspan: 0 }
                } else {
                    let countRowspan = 1
                    while (nextRow && nextRow['supplierAccount'] === cellValue) {
                        nextRow = visibleData[++countRowspan + _rowIndex]
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 }
                    }
                }
            }
        },
        open (data) {
            this.tableData = data
            this.showNode = true
        }
    }
}
</script>