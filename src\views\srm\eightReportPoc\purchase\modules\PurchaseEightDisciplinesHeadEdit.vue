<!--
 * @Author: fzb
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2022-08-19 16:44:44
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\purchase\modules\PurchaseEditCost.vue
-->
<template>
  <div class="business-container purchase-eight-disciplines">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        modelLayout="masterSlave"
        pageStatus="edit"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
        <template
          #eightDisciplinesThreeList="{ slotProps }"
        >
          <edit-form-layout 
            v-if="threeListSlotData.groupType==='head'"
            :ref="threeListSlotData.groupCode+ 'form'"
            :busAccount="slotProps.busAccount"
            :currentEditRow="currentEditRow"
            :group="threeListSlotData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners"
          ></edit-form-layout>
        </template>
        <template
          #eightDisciplinesSixList="{ slotProps }"
        >
          <edit-form-layout 
            v-if="sixListSlotData.groupType==='head'"
            :ref="sixListSlotData.groupCode+ 'form'"
            :busAccount="slotProps.busAccount"
            :currentEditRow="currentEditRow"
            :group="sixListSlotData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners"
          ></edit-form-layout>
        </template>
        <!-- "eightDisciplinesTwo" "eightDisciplinesFour" "eightDisciplinesSeven" "eightDisciplinesEight" -->
        <template
          #eightDisciplinesTwo="{slotProps}">
          <!-- 上传 -->
          <edit-grid-layout 
            :style="{height: '200px',overflow: 'auto'}"
            v-if="purchaseAttachmentD2ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD2ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            setGridHeight="200"
            :resultData="slotProps.resultData"
            :currentEditRow="currentEditRow"
            :gridLoading="false"
            :group="purchaseAttachmentD2ListSlotData"
            :loadData="purchaseAttachmentD2ListSlotData.loadData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners">
          </edit-grid-layout>
        </template>
        <template #eightDisciplinesSeven="{slotProps}">
          <!-- 上传 -->
          <edit-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD7ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD7ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :currentEditRow="currentEditRow"
            :gridLoading="false"
            :group="purchaseAttachmentD7ListSlotData"
            :loadData="purchaseAttachmentD7ListSlotData.loadData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners">
          </edit-grid-layout>
        </template>
        <template #eightDisciplinesEight="{slotProps}">
          <!-- 上传 -->
          <edit-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD8ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD8ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :currentEditRow="currentEditRow"
            :gridLoading="false"
            :group="purchaseAttachmentD8ListSlotData"
            :loadData="purchaseAttachmentD8ListSlotData.loadData"
            :pageConfig="slotProps.pageConfig"
          >
          </edit-grid-layout>
        </template>
        <template #eightDisciplinesFour="{slotProps}">
          <!-- 上传 -->
          <edit-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD4ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD4ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :currentEditRow="currentEditRow"
            :gridLoading="false"
            :group="purchaseAttachmentD4ListSlotData"
            :loadData="purchaseAttachmentD4ListSlotData.loadData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners">
          </edit-grid-layout>
        </template>
      </business-layout>

      <a-modal
    v-drag    
        v-model="rejectVisible"
        :title="rejectModelTitle"
        okText="确定"
        @ok="handleOk">
        <a-form-model
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :model="rejectForm">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_rMyC_478bc282`, '驳回节点')">
            <a-select
              style="width: 100%"
              v-model="rejectForm.node">
              <a-select-option
                v-for="(item, i) in nodeList"
                :key="i"
                :value="item.val"
              >
                {{ item.key }}
              </a-select-option>
            </a-select>
          </a-form-model-item >
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_rMvj_478a05f6`, '驳回理由')"
          >
            <a-textarea
              v-model="rejectForm.reject"
              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNrMvj_ceb1c7df`, '请输入驳回理由')"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </a-form-model-item>
        </a-form-model>
      </a-modal>

      <a-modal
    v-drag    
        v-model="agreeVisible"
        title="ICQ审核"
        :okText="$srmI18n(`${$getLangAccount()}#i18n_field_RL_f20f6`, '确认')"
        @ok="agreeHandleOk">
        <a-form-model
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :model="rejectForm">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_rMyC_478bc28233`, 'ICQ审核人')">
            <a-select
              style="width: 100%"
              v-model="agreeForm.node">
              <a-select-option
                v-for="(item, i) in agreeNodeList"
                :key="i"
                :value="item.val"
              >
                {{ item.val }}
              </a-select-option>
            </a-select>
          </a-form-model-item >
        </a-form-model>
      </a-modal>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk" /> 
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import {USER_INFO} from '@/store/mutation-types'
import DetailFormLayout from '@comp/template/business/DetailFormLayout.vue'
import DetailGridLayout from '@comp/template/business/DetailGridLayout.vue'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import EditFormLayout from '@comp/template/business/EditFormLayout.vue'

import {
    BUTTON_SAVE,
    BUTTON_PUBLISH,
    BUTTON_SUBMIT,
    BUTTON_BACK
} from '@/utils/constant.js'

export default {
    name: 'PurchaseEightDisciplinesHeadEdit',
    components: {
        DetailFormLayout,
        DetailGridLayout,
        EditFormLayout,
        EditGridLayout,
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    created () {
        // 监听syncRow 事件，D5纠正措施字段改变，D6跟着改变
        this.$root.$on('syncRow', msg=> {
            const {groupCode, property, content, id} = msg
            if(groupCode === 'eightDisciplinesFiveList'){
                let syncItemGrid = this.getItemGridRef('eightDisciplinesSixList')
                const tableData = syncItemGrid && syncItemGrid.getTableData().fullData
                tableData.forEach(syncRow=>{
                    if(Number(id.split('_')[1]) +1  === Number(syncRow.id.split('_')[1])){
                        syncRow[property] = content
                    }
                })
                syncItemGrid.loadData(tableData)
            }
        })
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNrMvj_ceb1c7df`, '请输入驳回理由'),
            rejectModelTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIyC_2e6c072a`, '指定节点'),
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'),
            rejectVisible: false,
            agreeVisible: false,
            refresh: true,
            nodeList: [
                {key: 'D0:问题提出', val: 'D0'},
                {key: 'D1:小组成立', val: 'D1'},
                {key: 'D2:问题界定', val: 'D2'},
                {key: 'D3:围堵措施', val: 'D3'},
                {key: 'D4:原因分析', val: 'D4'},
                {key: 'D5:纠正措施', val: 'D5'},
                {key: 'D6:效果验证', val: 'D6'},
                {key: 'D7:预防再发生', val: 'D7'},
                {key: 'D8:结案评价', val: 'D8'}
            ],
            agreeNodeList: [],
            agreeForm: {
                node: ''
            },
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                detail: { url: '/eightReport/purchaseEightDisciplinesPoc/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                eightDisciplinesThreeList: [

                ],
                eightDisciplinesFiveList: [

                ],
                eightDisciplinesSixList: [

                ],
                purchaseAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'eightDisciplines', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: '关联tab',
                            fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ],
                purchaseAttachmentD2List: [
                ],
                purchaseAttachmentD4List: [
                ],
                purchaseAttachmentD7List: [
                ],
                purchaseAttachmentD8List: [
                ]
            },
            pageFooterButtons: [
                {
                    ...BUTTON_SAVE,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/eightReport/purchaseEightDisciplinesPoc/edit'
                    },
                    show: this.showSave
                },
                {
                    ...BUTTON_PUBLISH,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/eightReport/purchaseEightDisciplinesPoc/publis'
                    },
                    show: this.showPublish
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_agree`, '同意'),
                    args: {
                        url: '/eightReport/purchaseEightDisciplinesPoc/agree'
                    },
                    key: 'agree',
                    click: this.agree,
                    show: this.showArgee
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'),
                    attrs: {
                        type: 'danger'
                    },
                    args: {
                        url: '/eightReport/purchaseEightDisciplinesPoc/reject'
                    },
                    key: 'reject',
                    show: this.showReject,
                    click: this.reject
                },
                {
                    ...BUTTON_BACK,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回')
                }
                
            ],
            threeListSlotData: {
            },
            sixListSlotData: {
            },
            purchaseAttachmentD2ListSlotData: {},
            purchaseAttachmentD4ListSlotData: {},
            purchaseAttachmentD7ListSlotData: {},
            purchaseAttachmentD8ListSlotData: {},
            url: {
                save: '/eightReport/purchaseEightDisciplinesPoc/edit',
                publish: '/eightReport/purchaseEightDisciplinesPoc/publis',
                reject: '/eightReport/purchaseEightDisciplinesPoc/reject', 
                agree: '/eightReport/purchaseEightDisciplinesPoc/agree'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_eightDisciplines_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.eightDisciplinesNumber,
                actionRoutePath: '/srm/eightReportPoc/purchase/PurchaseEightDisciplinesHeadList,/srm/eightReportPoc/sale/SaleEightDisciplinesHeadList'
            }
        },
        tableBtnShow ({ pageConfig }){
            let toElsAccount = pageConfig.groups[0].formModel.toElsAccount
            let loginElsAccount = this.$ls.get('Login_elsAccount')
            if (toElsAccount!=''&&toElsAccount!=loginElsAccount) {
                return false
            }
            return true
        },
        
        // 业务需要重写表格删除
        businessGridDelete ({ pageConfig, groupCode }) {
            if('D0'==pageConfig.groups[0].formModel.eightDisciplinesStatus){
                return true
            }
            let itemGrid = this.getItemGridRef(groupCode)
            let syncItemGrid = ''
            if(groupCode === 'eightDisciplinesFiveList'){
                syncItemGrid = this.getItemGridRef('eightDisciplinesSixList')
            }
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.srmI18n(`${this.getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            let delArr = []
            itemGrid.removeCheckboxRow().then(()=>{
                const tableData = syncItemGrid && syncItemGrid.getTableData().fullData
                console.log(tableData, 'tableData')
                if(tableData && tableData.length>0){
                    checkboxRecords.forEach(row=>{
                        tableData.forEach(syncRow=>{
                            if(Number(row.id.split('_')[1]) +1  === Number(syncRow.id.split('_')[1])){
                                delArr.push(syncRow)
                            }
                        })
                    })
                }
                syncItemGrid && syncItemGrid.remove(delArr)
            })
        },
        businessSlotGridDelete ({ pageConfig, groupCode }) {
            // if('D0'==pageConfig.groups[0].formModel.eightDisciplinesStatus){
            //     return true
            // }
            let gridRef = `${groupCode}grid`
            let itemGrid = this.$refs[gridRef].$refs[groupCode]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.srmI18n(`${this.getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        // 业务需要重写表格新增
        businessGridAdd ({ pageConfig, groupCode }) {
            if('D0'==pageConfig.groups[0].formModel.eightDisciplinesStatus){
                return true
            }
            console.log(pageConfig)
            console.log(groupCode, '===')
            let itemGrid = this.getItemGridRef(groupCode)
            let syncItemGrid = ''
            if(groupCode === 'eightDisciplinesFiveList'){
                syncItemGrid = this.getItemGridRef('eightDisciplinesSixList')
            }
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)
            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {

                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            itemGrid.insertAt([row], -1)
            syncItemGrid && syncItemGrid.insertAt([row], -1)
        },

        showPublish ({ pageConfig }){
            //采购审核状态
            let fbk3 = pageConfig.groups[0].formModel.fbk3
            if(fbk3 != '0'){
                return false
            }
            return true
        },
        showSave ({ pageConfig }){
            //采购审核状态
            let fbk3 = pageConfig.groups[0].formModel.fbk3
            if(fbk3 != '0'){
                return false
            }
            return true
        },
        showReject ({ pageConfig }){
            //采购审核状态
            let fbk3 = pageConfig.groups[0].formModel.fbk3
            //是否临时围堵 需要围堵且采购确认状态
            let fbk2 = pageConfig.groups[0].formModel.fbk2
            if(fbk2 == '1'&& fbk3 == '1'){
                return true
            }
            //采购方审核状态
            if(fbk3 == '4'){
                return true
            }
            //SQE
            if(fbk3 == '6'){
                return true
            }
            //ICQ
            if(fbk3 == '7'){
                return true
            }
            return false
        },
        showArgee ({ pageConfig }){
            //采购审核状态
            let fbk3 = pageConfig.groups[0].formModel.fbk3
            //是否临时围堵 需要围堵且采购确认状态
            let fbk2 = pageConfig.groups[0].formModel.fbk2
            const  account  = this.$ls.get(USER_INFO)
            if(fbk2 == '1'&& fbk3 == '1'){
                return true
            }
            //采购方审核状态
            if(fbk3 == '4'){
                return true
            }
            //SQE
            if(fbk3 == '6'&&account.subAccount==pageConfig.groups[0].formModel.fbk11.split('_')[1]){
                return true
            }
            //ICQ
            if(fbk3 == '7'&&account.subAccount==pageConfig.groups[0].formModel.fbk10.split('_')[1]){
                return true
            }
            return false
        },
        initTeamGridData (){
            let itemTeamGrid = this.$refs.businessRef.$refs.eightDisciplinesTeamListgrid[0].$refs.eightDisciplinesTeamList
            let itemTeamDataList = itemTeamGrid.getTableData().fullData

            if(itemTeamDataList.length==0){
                for(let i=1;i<=8;i++){
                    let item2 = {'elsAccount': this.$ls.get('Login_elsAccount'), 'toElsAccount': this.$ls.get('Login_elsAccount'), 'subAccount': this.$ls.get('Login_subAccount'), 'phone': this.$ls.get(USER_INFO).phone, 'mail': this.$ls.get(USER_INFO).email, 'name': this.$ls.get(USER_INFO).realname, 'itemNumber': '', 'reportFlowStep': 'D'+i, 'teamRole': 'member'}
                    itemTeamGrid.insertAt(item2, -1)
                }

            }
        },
        // 按钮显示
        // 绑定同步方法示例
        // 可选入参 { Vue, pageConfig, btn, pageData, groupCode }
        syncShow ({ pageData }) {
            console.log('pageData.createBy', (pageData.createBy))
            return (pageData.createBy === '307000')
        },
        // 按钮显示
        // 绑定同步方法示例
        // 可选入参 { Vue, pageConfig, btn, pageData, groupCode }
        asyncShow () {
            return getAction('/eightReport/purchaseEightDisciplinesPoc/queryById', { id: '1423530080946163714' })
                .then(res => {
                    let flag = res.result.id !== '1423530080946163714'
                    console.log('flag', flag)
                    if (flag) {
                        return window.Promise.resolve(res)
                    } else {
                        return window.Promise.reject(res)
                    }
                })
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory1`, '附件2'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentD2List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentD4List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentD7List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentD8List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_10010011`, 'd3slot'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'eightDisciplinesThreeListSlot',
                        groupType: 'head',
                        show: false,
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_10010011`, 'd6slot'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'eightDisciplinesSixListSlot',
                        groupType: 'head',
                        show: false,
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '拟制',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk17',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '审核',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk16',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '批准',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk15',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '拟制',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk14',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    }, {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '审核',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk13',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    }, {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '批准',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk12',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联Tab'),
                        fieldLabelI18nKey: '',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'select',
                        defaultValue: '',
                        dictCode: 'SRMEightAttachmentRelationTab',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120' 
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'purchaseAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'purchaseAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'purchaseAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'purchaseAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }, {
                        groupCode: 'purchaseAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData){
            debugger
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
            }
            const purchaseAttachmentLists = ['purchaseAttachmentList', 'purchaseAttachmentD2List', 'purchaseAttachmentD4List', 'purchaseAttachmentD7List', 'purchaseAttachmentD8List' ]
            let that = this
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            purchaseAttachmentLists.forEach(rs => {
                if (that.externalToolBar[rs].length) {
                    that.externalToolBar[rs][0].args.headId = resultData.id || ''
                    that.externalToolBar[rs][0].args.itemInfo = itemInfo
                }
            })
            pageConfig.groups.forEach((rs, index) => {
                if (rs.groupCode == 'eightDisciplinesThreeListSlot') {
                    rs.show = false
                    rs.verify = true
                    this.threeListSlotData = rs
                }
                if (rs.groupCode == 'eightDisciplinesSixListSlot') {
                    rs.show = false
                    rs.verify = true
                    this.sixListSlotData = rs
                }
                if (rs.groupCode == 'purchaseAttachmentD2List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD2ListSlotData = rs
                }
                if (rs.groupCode == 'purchaseAttachmentD4List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD4ListSlotData = rs
                }
                if (rs.groupCode == 'purchaseAttachmentD7List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD7ListSlotData = rs
                }
                if (rs.groupCode == 'purchaseAttachmentD8List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD8ListSlotData = rs
                }

            })
            this.$nextTick(() => {
                this.initTeamGridData()
            })
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        uploadCallBack (result, ref) {
            let fileGrid = this.getItemGridRef(ref)
            fileGrid.insertAt(result, -1)
        },
        slotUploadCallBack (result, ref) {
            debugger
            let gridRef = `${ref}grid`
            let fileGrid = this.$refs[gridRef].$refs[ref]
            fileGrid.insertAt(result, -1)
        },
        businessGridAddInModal ({ Vue, pageConfig, btn, groupCode }) {
            this.curGroupCode = groupCode
            console.log('groupCode', groupCode)
            this.fieldSelectType = 'material'
            let url = '/material/purchaseMaterialHead/list'
            let columns = [
                {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150},
                {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150},
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200},
                {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200}
            ]
            this.$refs.fieldSelectModal.open(url, {}, columns, 'multiple')
        },
        fieldSelectOk (data) {
            if (this.fieldSelectType === 'material') {

                let itemGrid = this.getItemGridRef(this.curGroupCode)

                let { fullData } = itemGrid.getTableData()
                
                // 过滤表格已有数据
                let filterData = data.filter(item => {
                    return !fullData.some(n => n.materialNumber === item.materialNumber)
                })

                const { pageConfig = {} } = this.getBusinessExtendData(this.businessRefName)
                let { columns = [] } = pageConfig.groups.find(n => n.groupCode === this.curGroupCode)

                // 获取默认表格行行配置
                let row = columns
                    .filter(n => n.field)
                    .reduce((acc, obj) => {
                        acc[obj.field] = obj.defaultValue || ''
                        return acc
                    }, {})
                let itemDatas = filterData.map(item => {
                    let anoRow = Object.assign({}, row, {
                        itemNumber: fullData.length + 1
                    })
                    return anoRow
                })
                
                itemGrid.insertAt(itemDatas, -1)
            }
        },
        reject (){
            const that = this
            let pageAllData = this.getAllData()
            postAction(this.url.reject, pageAllData).then(res => {
                if(res.success) {
                    that.$message.success(res.message)
                }else {
                    that.$message.warning(res.message)
                }
                that.stepBusinessRefresh()
            })
        },
        handleOk () {
            const that = this
            let pageAllData = this.getAllData()
            //驳回理由
            pageAllData.rejectReason = this.rejectForm.reject
            //退回到的节点
            pageAllData.fbk1 = this.rejectForm.node
            postAction(this.url.reject, pageAllData).then(res => {
                if(res.success) {
                    that.$message.success(res.message)
                }else {
                    that.$message.warning(res.message)
                }
                that.stepBusinessRefresh()
            })
            this.rejectVisible = false
        },
        agreeHandleOk () {
            const that = this
            let pageAllData = this.getAllData()
            pageAllData.fbk10 = that.agreeForm.node
            postAction(this.url.agree, pageAllData).then(res => {
                if(res.success) {
                    that.$message.success(res.message)
                }else {
                    that.$message.warning(res.message)
                }
                that.stepBusinessRefresh()
            })
            this.agreeVisible = false
        },
        agree (){
            const that = this
            let pageAllData = this.getAllData()
            debugger
            if(pageAllData.fbk3=='6'){
                let url = 'account/elsSubAccount/list'
                getAction(url).then(rs => {
                    if (rs.code == 200) {
                        that.agreeNodeList = rs.result.records.map(item => ( {key: item.id, val: `${item.realname}_${item.subAccount}`}))
                    }
                })
                this.agreeVisible = true
            }else{
                postAction(this.url.agree, pageAllData).then(res => {
                    if(res.success) {
                        that.$message.success(res.message)
                    }else {
                        that.$message.warning(res.message)
                    }
                    that.stepBusinessRefresh()
                })
            }
        }
    }
}
</script>

<style lang="less" scoped>
.purchase-eight-disciplines{
     :deep(.eightDisciplinesTwo .ant-advanced-rule-form>.ant-row>.ant-col){
         width: 100%;
     }
     :deep(.eightDisciplinesTwo .ant-advanced-rule-form>.ant-row>.ant-col .ant-col-9){
         width: 21%;
     }
     :deep(.eightDisciplinesTwo .ant-advanced-rule-form>.ant-row>.ant-col .ant-col-15){
         width: 100%;
     }
     :deep(.eightDisciplinesTwo .ant-advanced-rule-form>.ant-row>.ant-col){
        margin-bottom: 95px;
     }


     :deep(.eightDisciplinesFour .ant-advanced-rule-form>.ant-row>.ant-col){
         width: 100%;
     }
     :deep(.eightDisciplinesFour .ant-advanced-rule-form>.ant-row>.ant-col .ant-col-9){
         width: 12.5%;
     }
     :deep(.eightDisciplinesFour .ant-advanced-rule-form>.ant-row>.ant-col .ant-col-15){
         width: 100%;
     }
     :deep(.eightDisciplinesFour .ant-advanced-rule-form>.ant-row>.ant-col){
        margin-bottom: 95px;
     }

      :deep(.eightDisciplinesSeven>.ant-advanced-rule-form>.ant-form-item){
         min-height: 161px !important;
     }
      :deep(.eightDisciplinesEight>.ant-advanced-rule-form>.ant-form-item){
         min-height: 161px !important;
     }
}
</style>