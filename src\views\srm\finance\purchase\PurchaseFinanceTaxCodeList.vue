<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showAddPage && !showDetailPage && !showEditPage"
      :pageData="pageData"
      :url="url" />
    <purchase-finance-tax-code-Add
      v-if="showAddPage"
      :current-edit-row="currentEditRow"
      @hide="handListPage" />
    <!-- 查看页面 -->
    <PurchaseFinanceTaxCodeDetail
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="handListPage" />
    <PurchaseFinanceTaxCodeEdit
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="handListPage" />
  </div>
</template>
<script>

import { ListMixin } from '@comp/template/list/ListMixin'
import purchaseFinanceTaxCodeAdd from './modules/PurchaseFinanceTaxCodeAdd.vue'
import PurchaseFinanceTaxCodeDetail from './modules/PurchaseFinanceTaxCodeDetail.vue'
import PurchaseFinanceTaxCodeEdit from './modules/PurchaseFinanceTaxCodeEdit.vue'

export default {
    mixins: [ListMixin],
    components: {
        purchaseFinanceTaxCodeAdd,
        PurchaseFinanceTaxCodeDetail,
        PurchaseFinanceTaxCodeEdit
    },
    data () {
        return {
            showAddPage: false,
            showDetailPage: false,
            showEditPage: false,
            pageData: {
                businessType: 'TaxCode',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: '请输入商品和服务名称'
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), authorityCode: 'finance#purchaseFinanceTaxCode:add',
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                        
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt`, '导入'), icon: 'import', clickFn: this.importExcel}

                ],
                optColumnList: [
                    {
                        type: 'detail',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'finance#purchaseFinanceTaxCode:view',
                        clickFn: this.handleDetail
                    },
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showEditCondition, authorityCode: 'finance#purchaseFinanceTaxCode:edit'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, authorityCode: 'finance#purchaseFinanceTaxCode:delete'}
                ],
                optColumnWidth: 230
            }, 
            url: {
                list: '/reconciliation/purchaseFinanceTaxCode/list',
                delete: '/reconciliation/purchaseFinanceTaxCode/delete',
                deleteBatch: '/reconciliation/purchaseFinanceTaxCode/deleteBatch',
                exportXlsUrl: 'reconciliation/purchaseFinanceTaxCode/exportXls',
                importExcelUrl: 'reconciliation/purchaseFinanceTaxCode/importExcel',
                excelCode: 'PurchaseFinanceTaxCodeImportExcel',
                columns: 'purchaseFinanceTaxCodeList'
            }
        }
    },
    methods: {
        handleAdd (){
            this.currentEditRow = {}
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handListPage (){
            this.showAddPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleDetail (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        }
    }
}
</script>