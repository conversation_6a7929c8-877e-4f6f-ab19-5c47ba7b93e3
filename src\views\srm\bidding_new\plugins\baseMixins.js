import { getAction, postAction } from '@/api/manage'

export const baseMixins = {
    data () {
        return {
            needGetNodeParams: true,
            xNodeId: null,
            checkType: null,
            processType: null,
            currentStep: null,
            noticeType: null
        }
    },
    inject: {
        tenderCurrentRow: {
            from: 'tenderCurrentRow',
            default: null
        },
        subpackageId: {
            from: 'subpackageId',
            default: null
        },
        currentSubPackage: {
            from: 'currentSubPackage',
            default: null
        },
        currentNode: {
            from: 'currentNode',
            default: null
        },
        propsCurrentNode: {
            from: 'propsCurrentNode',
            default: null
        },
        resetCurrentSubPackage: {
            from: 'resetCurrentSubPackage',
            default: null
        }
    },
    methods: {
        getNodeParams () {
            let {
                nodeId,
                extend: { checkType, processType, currentStep, noticeType }
            } = !this.propsCurrentNode ? this.currentNode() : this.propsCurrentNode()
            this.xNodeId = nodeId
            this.checkType = checkType
            this.processType = processType
            this.currentStep = currentStep
            this.noticeType = noticeType
            return {
                nodeId,
                checkType,
                processType,
                currentStep
            }
        },
        dealLabel (str) {
            if (this.checkType !== null && this.processType !== null && !this.currentStep) this.getNodeParams()
            let prefix = ''
            switch (this.checkType) {
            case '0':
                prefix = '预审'
                break
            case '1':
                switch (this.processType) {
                case '0':
                    prefix = ''
                    break
                case '1':
                    switch (this.currentStep) {
                    case '0':
                        prefix = '第一步'
                        break
                    case '1':
                        prefix = '第二步'
                        break
                    default:
                        prefix = ''
                    }
                    break
                default:
                    prefix = ''
                }
                break
            default:
                prefix = ''
            }
            return `${prefix}${str}`
        },
        async getBusinessTemplate (businessType) {
            // 模板拓展校验白名单
            const BUSINESSTYPELIST = ['purchaseTenderNotice', 'tenderFileSubmit', 'tenderOpenSetting']
            let params = { elsAccount: this.$ls.get('Login_elsAccount'), businessType: businessType }
            this.confirmLoading = true
            const res = await getAction('/template/templateHead/all/getListByType', params)
            let currentEditRow = null
            if (res.success) {
                const result = res.result
                if (result.length > 0) {
                    if (result.length == 1 && !BUSINESSTYPELIST.includes(businessType)) {
                        // 一个业务模板无需匹配
                        currentEditRow = {
                            templateNumber: result[0].templateNumber,
                            templateName: result[0].templateName,
                            templateVersion: result[0].templateVersion,
                            templateAccount: result[0].elsAccount
                        }
                    } else {
                        const { extend = {} } = this.currentNode()
                        const cpc = `${extend.checkType}-${extend.processType || 'a'}-${extend.currentStep || 'a'}`
                        const checkType = extend.checkType
                        const processType = extend.processType

                        JSON.stringify(extend) != '{}' &&
                            (() => {
                                for (let i = 0; i < result.length; i++) {
                                    const extendObj = result[i].extend ? JSON.parse(result[i].extend) : null
                                    if (extendObj) {
                                        // 先判断是否是预审还是后审
                                        // 如果是后审，就判断是否是一步法还是二步法
                                        // 如果二步法，就判断是否是一步还是二步
                                        // checkType:0-预审、1-后审；processType：0-一步法、1-二步法；
                                        // currentStep：0-一步、1-二步；noticeType：0-邀请公告，1-预审公告，2-招标公告, 3-招标变更公告, 4-预审变更公告, 5-邀请变更公告
                                        // 判断是否是公告类型业务模板

                                        // 如果是预审变更公告的节点，就拿预审公告业务模板
                                        if (extend.hasOwnProperty('noticeType') && extend['noticeType'] == '4' && extendObj['noticeType'] == '1') {
                                            currentEditRow = {
                                                templateNumber: result[i].templateNumber,
                                                templateName: result[i].templateName,
                                                templateVersion: result[i].templateVersion,
                                                templateAccount: result[i].elsAccount
                                            }
                                            // 找到了直接跳出整个循环
                                            break
                                        }
                                        // 如果是投标邀请变更的节点，就拿投标邀请业务模板
                                        if (extend.hasOwnProperty('noticeType') && extend['noticeType'] == '5' && extendObj['noticeType'] == '0') {
                                            currentEditRow = {
                                                templateNumber: result[i].templateNumber,
                                                templateName: result[i].templateName,
                                                templateVersion: result[i].templateVersion,
                                                templateAccount: result[i].elsAccount
                                            }
                                            // 找到了直接跳出整个循环
                                            break
                                        }
                                        // 如果是招标公告变更的节点，就拿招标公告业务模板
                                        if (extend.hasOwnProperty('noticeType') && extend['noticeType'] == '3' && extendObj['noticeType'] == '2') {
                                            currentEditRow = {
                                                templateNumber: result[i].templateNumber,
                                                templateName: result[i].templateName,
                                                templateVersion: result[i].templateVersion,
                                                templateAccount: result[i].elsAccount
                                            }
                                            // 找到了直接跳出整个循环
                                            break
                                        }
                                        if (extend.hasOwnProperty('noticeType') && extend['noticeType'] == extendObj['noticeType']) {
                                            currentEditRow = {
                                                templateNumber: result[i].templateNumber,
                                                templateName: result[i].templateName,
                                                templateVersion: result[i].templateVersion,
                                                templateAccount: result[i].elsAccount
                                            }
                                            // 找到了直接跳出整个循环
                                            break
                                        } else if (extend.hasOwnProperty('checkType') && !extend.hasOwnProperty('noticeType')) {
                                            let cpcj = `${extendObj.checkType}-${extendObj.processType || 'a'}-${extendObj.currentStep || 'a'}`
                                            if (checkType == '0') {
                                                // 预审
                                                cpcj = `${extendObj.checkType}-a-a`
                                            } else if (checkType == '1' && processType == '0') {
                                                // 后审一步法
                                                cpcj = `${extendObj.checkType}-${extendObj.processType || 'a'}-a`
                                            }

                                            console.log(cpcj)
                                            if (cpc === cpcj) {
                                                currentEditRow = {
                                                    templateNumber: result[i].templateNumber,
                                                    templateName: result[i].templateName,
                                                    templateVersion: result[i].templateVersion,
                                                    templateAccount: result[i].elsAccount
                                                }
                                                // 找到了直接跳出整个循环
                                                break
                                            }
                                        }
                                    }
                                }
                            })()
                    }
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                }
            } else {
                this.$message.warning(res.message)
            }
            this.confirmLoading = false

            return currentEditRow
        }
    },
    created () {
        if(this.needGetNodeParams) {
            this.getNodeParams()
        }
    }
}
