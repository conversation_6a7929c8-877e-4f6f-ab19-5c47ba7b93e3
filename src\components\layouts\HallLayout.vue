<template>
  <div class="hall-layout">
    <a-layout>
      <a-layout-header class="header">
        <div class="logo">{{ title }}</div>
        <div
          class="fl">
          <span class="subStatus">{{ $srmI18n(`${$getLangAccount()}#i18n_field_zszE_268f120a`, '分包状态') }}: {{ vuex_currentEditRow.biddingStatus_dictText }}</span>
        </div>
        <div class="info">
          <div class="item">
            <span class="tit">{{ $srmI18n(`${$getLangAccount()}#i18n_SaleMassProdHeadList_projectName`, '项目名称') }}</span>
            <span
              class="titContent"
              :title="vuex_currentEditRow.projectName">{{ vuex_currentEditRow.projectName }}</span>
          </div>
          <div class="item">
            <span class="tit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_projectNumber`, '项目编号') }}</span>
            {{ vuex_currentEditRow.biddingNumber }}
          </div>
        </div>
      </a-layout-header>
      <a-layout
        class="siderLayout"
        style="min-height: calc(100vh - 50px)">
        <a-layout-sider
          width="200">
          <s-menu
            :collapsed="false"
            :menu="menus"
            :theme="navTheme"
            @select="onSelect"
            :style="style"
            :userIconFont="true"
          />
        </a-layout-sider>
        <a-layout style="padding: 8px;">
          <a-layout-content :style="{ position: 'relative' }">
            <div
              class="routeView"
              v-if="routerAlive">
              <route-view v-if="vuex_currentEditRow.id" />
            </div>
          </a-layout-content>
        </a-layout>
      </a-layout>
    </a-layout>

  </div>
</template>

<script>
import RouteView from '@/components/layouts/RouteView'
import SMenu from '@/components/menu/index.jsx'
import { getAction } from '@/api/manage'

import { SET_CACHE_VUEX_CURRENT_EDIT_ROW, SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    name: 'HallLayout',
    components: {
        SMenu,
        RouteView
    },
    computed: {
        ...mapState({
            navTheme: state => state.app.theme,
            vuex_currentEditRow: state => state.app.vuex_currentEditRow,
            cache_vuex_currentEditRow: state => state.app.cache_vuex_currentEditRow
        }),
        style () {
            return {
                padding: 0,
                height: '100%',
                overflowX: 'hidden',
                overflowY: 'auto'
            }
        }
    },
    data () {
        return {
            routerAlive: true,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_JdYBfY_2d6065db`, '自主招标大厅'),
            menus: [],
            collapsed: false,
            currentRow: {}
        }
    },
    provide () {
        return {
            routerRefresh: this.routerRefresh,
            updateVuexCurrentEditRow: this.updateVuexCurrentEditRow
        }
    },
    watch: {
        $route: {
            immediate: true,
            handler ({ path }) {
                this.title = path.indexOf('/applyHall') !== -1
                    ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supBiddingHall`, '投标大厅')
                    : this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_JdYBfY_2d6065db`, '自主招标大厅')
                this.updateVuexCurrentEditRow()
            }
        }
    },
    methods: {
        ...mapMutations({
            setCacheVuexCurrentEditRow: SET_CACHE_VUEX_CURRENT_EDIT_ROW,
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        /**
         * @description: 路由销毁重建（解决跳转同一路由不刷新问题）
         * @param {type}
         * @return:
         */
        routerRefresh () {
            this.routerAlive = false
            this.$nextTick(() => {
                this.routerAlive = true
            })
        },
        getMenu () {
            let HALLROUTERNAME = this.$route.path.includes('hall') ? 'hall' : 'applyHall'
            const { children = [] } = this.$router.options.routes.find(n => n.name === HALLROUTERNAME) || {}
            this.menus = children
        },
        onSelect (obj) {
            console.log('obj', obj)
        },
        updateVuexCurrentEditRow () {
            const { id = '', role = '' } = this.cache_vuex_currentEditRow || {}
            if (!id) return 
            const url = role === 'sale' ? '/bidding/saleBiddingHead/queryById' : '/bidding/purchaseBiddingHead/queryById'
            getAction(url, { id }).then(res => {
                if (!res.success) {
                    this.$message.error(res.message)
                    return
                }
                let result = res.result || {}
                this.setVuexCurrentEditRow({ row: result })
            })
        }
    },
    created () {
        this.getMenu()
    },
    mounted () {
        // 获取自主招标缓存行数据
        if (!this.vuex_currentEditRow.id) {
            let row = this.$ls.get(SET_CACHE_VUEX_CURRENT_EDIT_ROW) || null
            console.log('row :>> ', row)
            if (row) {
                // 重置vuex行缓存数据
                this.setCacheVuexCurrentEditRow({ row })
                // 通过接口获取新明细数据而不使用行缓存原因为：
                // 列表页单据可能长时间未刷新，但跳转至投标大厅时，当前单据状态已更改的情况
                this.updateVuexCurrentEditRow()
            }
        }
    }
}
</script>

<style lang="less" scoped>
.hall-layout {
	.header {
		display: flex;
		// justify-content: space-between;
		padding: 0 16px 0 0;
		height: 50px;
		background: #4e85ff;
		line-height: 50px;
		.logo {
			width: 200px;
			text-align: center;
			font-weight: 400;
			font-size: 25px;
			color: #fff;
		}
        .fl {
            flex: 2;
            text-align: center;
            font-weight: 400;
            font-size: 25px;
            color: #fff;
            padding: 0;
            height: 100%;
        }
        .subStatus {
            padding-left:30%;
        }
		.info {
            align-items: flex-end;
            width: 33%;
            display: flex;
			font-weight: 400;
			font-size: 14px;
			color: #fff;
            text-align: right;
            .item:nth-child(1) {
                max-width: 56%;
                display: flex;
            }
            .item:nth-child(2) {
                width: 44%;
            }
			.item {
				+ .item {
					position: relative;
					margin-left: 23px;
					&::before {
						position: absolute;
						left: -8%;
						top: 34%;
						width: 1px;
						height: 16px;
						background-color: #fff;
						content: "";
					}
				}
				.tit {
					&::after {
						margin-right: 8px;
						content: ":";
					}
				}
                .titContent {
                    display:block;
                    width: calc(100% - 80px);
                    white-space:nowrap;
                    overflow:hidden;
                    text-overflow:ellipsis;
                }
			}
		}
	}
  // 菜单高度修改(因为SMenu是一个js组件)
  :deep(.ant-menu-inline > .ant-menu-item){
      height: 28px;
      line-height: 28px;
  }
  :deep(.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title){
      height: 28px;
      line-height: 28px;
  }
  :deep(.ant-menu-sub.ant-menu-inline > .ant-menu-item, .ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title){
      height: 28px;
      line-height: 28px;
  }
}
</style>
