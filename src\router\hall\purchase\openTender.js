import { RouteView } from '@/components/layouts'

const openTenderRouter = {
    path: '/hall/openTender',
    name: 'openTender',
    meta: {
        title: '开标',
        titleI18nKey: 'i18n_title_opening',
        icon: 'icon-111-05',
        keepAlive: false
    },
    component: RouteView,
    children: [
        {
            path: '/hall/openTender/open',
            name: 'open',
            meta: {
                title: '开启',
                titleI18nKey: 'i18n_menu_vA_bd52f',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'open' */ '@/views/srm/bidding/hall/purchase/openTender/Open.vue')
        }
    ]
}

export default openTenderRouter