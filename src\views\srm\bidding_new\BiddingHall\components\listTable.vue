<template>
  <div class="listTable">
    <vxe-grid
      ref="table"
      v-bind="gridConfigs"
      :scroll-y="{enabled: false}"
      auto-resize
      :editConfig="editConfig"
      :pager-config="tablePage"
      :data="currentData"
      :columns="tableColumns"
      :span-method="mergeRowMethod ? mergeRowMethod : undefined"
      :loading="loading"
      :height="gridHeight"
      :merge-cells="mergeRowMethod ? undefined : mergeCells"
      :show-header="showHeader"
      :radio-config="selectModel === 'single' ? checkedConfig : undefined"
      :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
      @page-change="handlePageChange"
      :footer-method="footerMethod"
      :show-footer="footerMethod ? true : false"
      v-on="$listeners"
      :edit-rules="editRules"
      show-overflow="title"
    >
      <template
        #toolbar
        v-if="externalToolBar.length > 0">
        <business-button
          :buttons="externalToolBar"
          :groupCode="groupCode"
          isToolbarButtons
          v-bind="$attrs"
          v-on="$listeners" />
      </template>
      <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
        <a
          v-for="(item, i) in optColumnList"
          :key="'opt_' + row.id + '_' + i"
          :title="item.title"
          style="margin: 0 4px"
          :disabled="item.allow ? item.allow(row) : false"
          v-show="showCustomBtnOpt(item, row, $rowIndex)"
          @click="toolbarButtonsCallBack(row, column, $rowIndex, $columnIndex, item.clickFn)">
          {{ item.title }}
        </a>
      </template>
    </vxe-grid>
  </div>
</template>
<script lang="jsx">
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import BusinessButton from '@comp/template/business/components/BusinessButton'
import { ajaxFindDictItems } from '@/api/api'
import { valitNumberLength } from '@views/srm/bidding_new/utils/index'
import { isArray, isObject, isFunction } from 'lodash'

export default {
    mixins: [tableMixins],
    components: {
        BusinessButton
    },
    props: {
        pageData: {
            //页面数据
            type: Object,
            default: () => {
                return {
                    showOptColumn: true,
                    optColumnList: [],
                    optColumnWidth: 120,
                    optColumnAlign: 'center',
                    superQueryShow: true,
                    isOrder: {
                        column: 'id',
                        order: 'desc'
                    }
                }
            }
        },
        url: {
            //后台接口
            type: Object,
            default: null
        },
        // 静态列
        statictableColumns: {
            type: Array,
            default: () => {
                return []
            }
        },
        // 是否显示表格分分页
        showTablePage: {
            type: Boolean,
            default: true
        },
        // 默认参数
        defaultParams: {
            type: Object,
            default: null
        },
        // 列表数据
        fromSourceData: {
            type: Array,
            default: () => {
                return []
            }
        },
        // 头部按钮
        externalToolBar: {
            type: Array,
            default: () => {
                return []
            }
        },
        groupCode: {
            type: String,
            default: ''
        },
        // 自定义设置表行高
        setGridHeight: {
            type: [String, Number],
            default: '300'
        },
        enclosure: {
            type: Boolean,
            default: false
        },
        // 页面的状态
        pageStatus: {
            type: String,
            default: () => {
                return 'edit'
            }
        },
        // 表格单选多选
        selectModel: {
            type: String,
            default: 'multiple'
        },
        // 复选框勾选配置
        checkedConfig: {
            type: Object,
            default: () => {
                return { highlight: true, reserve: true, trigger: 'row' }
            }
        },
        editRulesProps: {
            type: Object,
            default: () => {
                return {}
            }  	
        },
        mergeCells: {
            type: Array,
            default: () => {
                return []
            }
        },
        mergeRowMethod: {
            type: Function,
            default: undefined
        },
        editConfig: {
            type: Object,
            default: () => {
                return {
                    trigger: 'click', mode: 'cell'
                }
            }  	
        },
        showHeader: {
            type: Boolean,
            default: true
        },
        footerMethod: {
            type: Function,
            default: undefined
        }
    },
    data () {
        return {
            tableColumns: [],
            tableData: [],
            loading: false,
            editRules: {},
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                align: 'right',
                pageSizes: [10, 50, 100, 200, 500],
                layouts: ['Sizes', 'PrevPage', 'Number', 'NextPage', 'FullJump', 'Total'],
                perfect: true,
                enabled: this.showTablePage
            }
        }
    },
    watch: {
        statictableColumns () {
            this.initColumns()
        },
        pageStatus () {
            this.initColumns()
        }
    },
    computed: {
        currentData () {
            return this.url ? this.tableData : this.fromSourceData
        },
        gridConfigs () {
            // 保证金管理-确认单中附件高度的从新计算
            if (this.enclosure) {
                let num = 1
                this.tableData.forEach((data) => {
                    if (num < data.attachmentDTOList.length) {
                        // 把附件个数最多的进行赋值
                        num = data.attachmentDTOList.length
                    }
                })
                return Object.assign(this.gridConfig, { rowConfig: { height: 40 * num } })
            } else {
                return Object.assign(this.gridConfig, this.$atters)
            }
        },
        // 操作列按钮
        optColumnList () {
            let newArr = [...this.pageData.optColumnList]
            return newArr
        },
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow) {
                if (this.currentEditRow.busAccount || this.currentEditRow.elsAccount) {
                    account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount
                }
            }
            return account
        },
        gridHeight () {
            let height = '100'
            const clientHeight = document.documentElement.clientHeight
            height = clientHeight - 260
            // 自定义设置表行高
            if (this.setGridHeight) {
                if (this.setGridHeight == 'auto') {
                    let length = (this.fromSourceData.length || this.tableData.length)
                    if (length !== 0) {
                        height = 36 * length + 50
                    }
                } else {
                    height = this.setGridHeight
                }
            }
            return height + 'px'
        }
    },
    methods: {
        beforeHandleData (columns) {
            this.$emit('beforeHandleData', columns)
        },
        reloadColumn (columns) {
            this.$refs['table'] && this.$refs['table'].reloadColumn(columns)
        },
        initColumns () {
            let that = this
            this.tableColumns = this.statictableColumns
            if (this.editRulesProps) {
                this.editRules = Object({}, this.editRulesProps)
            }
            let checkNumber = ({ cellValue, rule, rules}) => {
                if (cellValue) {
                    if (!valitNumberLength(cellValue, 8)){
                        return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '8')
                    }
                }
            }
            this.tableColumns.map((col) => {
                if (!this.editRules[col.field]) this.editRules[col.field] = []
                if (!col.props) col.props = {}
                switch (col.fieldType) {
                case 'input':
                    col.editRender = {
                        name: '$input',
                        enabled: (col.enabled !== undefined && col.enabled !== null) ? col.enabled : (that.pageStatus == 'edit'),
                        props: {...col.props},
                        events: {
                            change: (currentRow, currentValue) => {
                                that.changeGridItem(currentRow, currentValue, col.bindFunction)
                            }
                        }
                    }
                    this.editRules[col.field].push({ max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符') })
                    break
                case 'textArea':
                    col.editRender = {
                        name: 'srmTextArea',
                        enabled: (col.enabled !== undefined && col.enabled !== null) ? col.enabled : (that.pageStatus == 'edit'),
                        props: {...col.props},
                        events: {
                            change: (currentRow, currentValue) => {
                                that.changeGridItem(currentRow, currentValue, col.bindFunction)
                            }
                        }
                    }
                    this.editRules[col.field].push({ max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符') })
                    break
                case 'select':
                    col.editRender = {
                        name: 'srmSelect',
                        options: that.queryDictData(col),
                        // enabled: col.enabled == false ? false : that.pageStatus == 'edit',
                        // enabled: (col.enabled ?? '' != '') ? !!col.enabled : (that.pageStatus == 'edit'),
                        enabled: (col.enabled !== undefined && col.enabled !== null) ? col.enabled : (that.pageStatus == 'edit'),
                        props: {...col.props},
                        defaultValue: col.defaultValue || '',
                        events: {
                            change: (currentRow, currentValue) => {
                                that.changeGridItem({ row: currentRow }, { value: currentValue }, col.bindFunction)
                            }
                        }
                    }
                    break
                case 'switch':
                    col.cellRender = {
                        name: '$switch',
                        enabled: (col.enabled !== undefined && col.enabled !== null) ? col.enabled : (that.pageStatus == 'edit'),
                        props: { openValue: '1', closeValue: '0', ...col.props},
                        events: {
                            change: (currentRow, currentValue) => {
                                that.changeGridItem(currentRow, currentValue, col.bindFunction)
                            }
                        }
                    }
                    break
                case 'date':
                    col.editRender = {
                        name: 'mDatePicker',
                        enabled: (col.enabled !== undefined && col.enabled !== null) ? col.enabled : (that.pageStatus == 'edit'),
                        props: {...col.props},
                        events: {
                            change: (currentRow, currentValue) => {
                                that.changeGridItem(currentRow, currentValue, col.bindFunction)
                            }
                        }
                    }
                    break
                case 'number':
                    col.editRender = { 
                        enabled: (col.enabled !== undefined && col.enabled !== null) ? col.enabled : (that.pageStatus == 'edit'), 
                        autofocus: '.custom-cell-number input'
                    }
                    col.slots = Object.assign({}, col.slots, {
                        default: ({ row, rowIndex, column, columnIndex }, h) => {
                            return [<span>{row[column.property]}</span>]
                        },
                        edit: ({ row, rowIndex, column, columnIndex }, h) => {
                            const props = {
                                type: 'number',
                                disabled: col.disabled,
                                min: 0,
                                ...col.props
                            }
                            const on = {
                                // change: currentValue => {that.changeGridFloatItem(row, column, currentValue, col.bindFunction)},
                                change: (currentValue) => {
                                    that.changeGridItem({row: row}, { value: currentValue }, col.bindFunction)
                                }
                            }
                            return [<a-input-number class='custom-cell-number'  vModel={row[column.property]} {...{ props, on }} />]
                        }
                    })
                    if(!col.dontCheck){
                        this.editRules[col.field].push({ validator: checkNumber, trigger: 'change' })
                    }
                    break
                case 'selectModal':
                    col.slots = this.selectModalSlots(col)
                    break
                }
                // 是否必填
                if (col.required == '1') {
                    let ruleRequired = that.$srmI18n(`${that.$getLangAccount()}#i18n_title_required`, '必填')
                    this.editRules[col.field].push({ required: true, message: `${col.title}${ruleRequired}!` })
                }
            })
            this.reloadColumn(this.tableColumns)
        },
        // 表行编辑change事件
        changeGridItem (currentRow, currentValue, cb) {
            console.log(currentRow)
            cb && cb(currentRow.row, currentRow.column, currentValue.value, this.$parent)
        },
        reloadData (data) {
            this.$refs['table'].reloadData(data)
        },
        loadData () {
            this.loading = true
            let params = this.showTablePage
                ? {
                    pageSize: this.tablePage.pageSize,
                    pageNo: this.tablePage.currentPage
                }
                : ''
            if (this.defaultParams) {
                params = Object.assign(params, this.defaultParams)
            }
            params = Object.assign(params, this.pageData.form||{})
            getAction(this.url.list, params)
                .then((res) => {
                    if (res.success) {
                        let list = res.result.records ? [...res.result.records] : [...res.result]
                        list.forEach((item) => {
                            if (item.extendFields) {
                                Object.assign(item, JSON.parse(item.extendFields))
                            }
                        })
                        let data = []
                        if (this.groupCode == 'margin') {
                            // 保证金管理-投标人列表数据要进行拆分重组
                            list.forEach((item) => {
                                item.marginList &&
                                    ((marginList) => {
                                        marginList.forEach((margin) => {
                                            const mg = Object.assign(item, margin)
                                            data.push(mg)
                                        })
                                    })(item.marginList)
                            })
                        }
                        // this.tableData = data.length > 0 ? data : list
                        this.tableData = [...list, ...data]
                        this.tablePage.total = res.result.total
                    }
                })
                .finally(() => {
                    this.loading = false
                })
        },
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            this.loadData()
        },
        // 是否可以显示隐藏按
        showCustomBtnOpt (item, row, rowIndex) {
            return this.showCondition(item, row, rowIndex)
        },
        // 版本迭代使用的方法
        showCondition (item, row, rowIndex) {
            if (item && item.showCondition) {
                // 有showCondition 逻辑
                return item.showCondition(row, rowIndex)
            } else if (item.hide) {
                // 有hide逻辑
                return false
            } else {
                return true
            }
        },
        // toolbar_buttons方法封装，暴露更多属性外部使用
        toolbarButtonsCallBack (row, column, rowIndex, columnIndex, cb) {
            if (cb) {
                window.setTimeout(() => {
                    cb(row, column, rowIndex, columnIndex, this.tableData)
                }, 50)
            }
        },
        insertAt (data, i) {
            this.$refs['table'].insertAt(data, i)
        },
        // 删除复选框
        removeCheckboxRow () {
            this.$refs['table'].removeCheckboxRow()
        },
        // 删除单选框
        removeRadioRow () {
            this.$refs['table'].removeRadioRow()
        },
        // 删除指定行数据，指定 row 或 [row, ...] 删除多条数据，如果为空则删除所有数据
        removeRow (rows) {
            this.$refs['table'].remove(rows)
        },
        getTableData () {
            let p = this.$refs['table'].getTableData()
            return p
        },
        getCheckboxRecords (isFull = false) {
            let data = this.$refs['table'].getCheckboxRecords(isFull)
            return data
        },
        // 获取表格下拉字典
        queryDictData (column) {
            const that = this
            if (column && column.dictCode) {
                let postData = {
                    busAccount: that.busAccount || that.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: column.dictCode
                }
                ajaxFindDictItems(postData).then((res) => {
                    if (res.success) {
                        let options = res.result.map((dictItem) => {
                            return {
                                value: dictItem.value,
                                label: dictItem.text,
                                title: dictItem.title
                            }
                        })
                        if (column.editRender) {
                            column.editRender.options = options.filter(item => {
                                if (column.optionsFilterValue && isArray(column.optionsFilterValue)) return !column.optionsFilterValue.includes(item.value)
                                return true
                            })
                        }
                        // dictOptions初始化数据字典的字段会用到
                        column.dictOptions = options.filter(item => {
                            if (column.optionsFilterValue && isArray(column.optionsFilterValue)) return !column.optionsFilterValue.includes(item.value)
                            return true
                        })
                        that.$forceUpdate()
                    } else {
                        if (column.editRender) {
                            column.editRender.options = []
                        }
                        // dictOptions初始化数据字典的字段会用到
                        column.dictOptions = []
                        that.$forceUpdate()
                    }
                })
            } else {
                if (column.editRender) {
                    column.editRender.options = []
                }
                // dictOptions初始化数据字典的字段会用到
                column.dictOptions = []
                that.$forceUpdate()
            }
        },
        clearCheckboxRow () {
            this.$refs.table.clearCheckboxRow()
        },
        businessGridDelete (cb) {
            let itemGrid = this.$refs.table
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            this.$refs.table.removeCheckboxRow()
            if (cb) cb(checkboxRecords)
        },
        getValidate () {
            return this.$refs['table'].validate(true)
        },
        init () {
            this.initColumns()
            // 有后台接口就直接请求
            if (this.url) {
                this.loadData()
            }
        },
        // type=selectModal 弹窗的slots 逻辑抽取
        selectModalSlots (col) {
            const that = this
            col.editRender = {}
            let afterRowClearCallBack = (that, row, column, rowIndex, columnIndex) => {
                row[column.property] = ''
            }
            if (col.extend && col.extend.afterRowClearCallBack) afterRowClearCallBack = col.extend.afterRowClearCallBack
            const commonSlot = ({row, rowIndex, column, columnIndex}, status) => {
                const scopedSlots = {
                    default: ({ openFunc }) => {
                        const text = status === 'edit' ? [(<a-input title={row[column.property]} disabled style="text-align: center;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;" vModel={row[column.property]} bordered={false}/>)] : [(<span>{row[column.property]}</span>)]
                        const closeIcon = row[column.property] && status === 'edit'?
                            [<a-icon
                                type="close-circle" 
                                style="position: absolute;display: inline-block;font-size: 14px;right: 27px;top: 50%;transform:translateY(-50%);z-index: 2; cursor:pointer"
                                onClick={(event) => {
                                    event.stopPropagation()
                                    isFunction(afterRowClearCallBack) && afterRowClearCallBack(that, row, column, rowIndex, columnIndex)
                                }}
                            />]
                            : []
                        const tpl = (
                            <div title={row[column.property]} style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                                {text}
                                {closeIcon}
                                <a style="position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 2;">
                                    <a-icon type="file-search" onClick={() => { openFunc && openFunc() }} />
                                </a>
                            </div>
                        )
                        const detailTpl = (
                            <div style="position: relative;min-height: 30px;padding-right: 20px;">
                                {row[column.property]}
                            </div>
                        )
                        return that.pageStatus == 'edit' ? tpl : detailTpl
                    }
                }
                const props = {
                    config: col,
                    row: row,
                    form: that.editFormData,
                    isRow: true
                }
                const on = {
                    afterClearCallBack: (cb) => {
                        cb && isFunction(cb) && cb(that, row, col, rowIndex, columnIndex )
                    },
                    ok: (data) => {
                        col.bindFunction && isFunction(col.bindFunction) && col.bindFunction(row, data, that, that.tplRootRef, rowIndex)
                    }
                }
                const $selectModalBox = (<m-select-modal scopedSlots={ scopedSlots } { ...{ props, on } } />)
                return [$selectModalBox]
            }
            return Object.assign({}, col.slots, {
                default: (info) => {
                    return commonSlot(info, 'default')
                },
                edit: (info) => {
                    return commonSlot(info, 'edit')
                }
            })
        }
    },
    created () {
        this.init()
    }
}
</script>
