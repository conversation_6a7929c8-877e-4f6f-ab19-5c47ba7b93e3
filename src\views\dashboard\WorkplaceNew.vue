<template>
  <div
    class="panel-container-workplace"
    :style="'min-height:' + minHeight">
    <a-spin :spinning="pageLoading">
      <grid-layout
        style="width:100%"
        :layout.sync="panelData"
        :col-num="12"
        :row-height="30"
        :is-draggable="true"
        :is-resizable="true"
        :is-mirrored="false"
        :vertical-compact="true"
        :margin="[10, 10]"
        :use-css-transforms="true"
        @layout-updated="layoutUpdatedEvent"
      >
        <grid-item
          v-for="(item, i) in panelData"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          style="background:#fff"  
          :class="[item.chartType == 'num' ? 'data-statis-item' : '']"
          :dragAllowFrom="item.chartType == 'num' ? '.data-statis-item' : '.ant-card-head'"
          :key="item.id + i"
        >
          <viewPreviewCard
            :modalData="item.modalData"
            :widget="item.widget"
            :ref="item.chartType"
            :options="item.options || {}"
            :resultData="item.resultData"
          />
          <!-- <a-card
            size="small"
            class="workplace_card"
            :loading="item.loading"
            :title="item.chartType != 'num' ? item.chartName : null"
            style="width:100%;height:100%"
          >
            <vue-echarts
              v-if="item.chartType == 'echart'"
              autoresize
              theme="light"
              :option="item.config"
              :auto-resize="true"
            />
            <div
              v-else-if="item.chartType == 'list'"
              style="height:100%;width:100%">
              <vxe-grid
                border="inner"
                auto-resize
                resizable
                highlight-hover-row
                show-overflow
                size="mini"
                height="auto"
                :checkbox-config="{ highlight: true }"
                :columns="item.columns"
                :data="item.resultData"
              >
              </vxe-grid>
            </div>
            <div
              v-else-if="item.chartType == 'num'"
              class="num-panel-item-new">
              <div class="row">
                <div class="title">{{ item.chartName }}</div>
                <div class="num-panel-icon">
                  <a-icon
                    :type="numIcon(item)"
                    style="font-size: 28px;color: #1890ff"></a-icon>
                </div>
              </div>
              <div class="count">{{ item.resultData }}</div>
            </div>
          </a-card> -->
        </grid-item>
        <a-empty v-if="!panelData.length" />
      </grid-layout>
    </a-spin>
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import VueGridLayout from 'vue-grid-layout'
import ViewPreviewCard from '@views/dashboard/modules/ViewPreviewCard'
import { initWidgetInfo } from '@/components/chart/widget/utils/widgetUtils'
import { Empty } from 'ant-design-vue'
export default {
    components: {
        AEmpty: Empty,
        ViewPreviewCard,
        GridLayout: VueGridLayout.GridLayout,
        GridItem: VueGridLayout.GridItem
    },
    data () {
        return {
            panelData: [],
            pageLoading: true,
            url: {
                list: '/report/dashboard/dashboard/getDashboard/',
                data: '/report/dataSource/elsReportChartDataSet/getDataById',
                update: '/report/dashboard/chartUserConfig/saveUserConfig',
                dashboardChart: {
                    list: '/report/dashboard/dashboard/queryDashboardChartByMainId',
                    mapList: '/report/dataSource/elsReportChartDataSet/getDataById'
                    
                }
            }
        }
    },
    computed: {
        minHeight () {
            return document.body.clientHeight - 136 + 'px'
        },
        numIcon () {
            return function (item) {
                let rs = 'pay-circle'
                if (item.chartType == 'num') {
                    switch (item.chartCode) {
                    case 'fund': {
                        rs = 'pay-circle'
                        break
                    }
                    case 'growthRateForOrder': {
                        rs = 'fund'
                        break
                    }
                    case 'supplierQuantity': {
                        rs = 'shop'
                        break
                    }
                    default:
                        break
                    }
                }
                return rs
            }
        }
    },
    // created () {
    //     this.getDashboardList()
    // },
    created () {
        // console.log('123=======================')
        this.getDashboardList()
        let event = document.createEvent('HTMLEvents')
        event.initEvent('resize', true, true)
        event.eventType = 'message'
        window.dispatchEvent(event)
    },
    methods: {
        getDashboardList () {
            let that = this
            let pageCode = this.$route.name
            getAction(this.url.list + pageCode, {}).then(res => {
                if (res.success) {
                    let list = res.result
                    //字段转换
                    list.forEach(item => {
                    //数据必须包含x,y,w,h,i字段
                        item.x = item.xCoord
                        item.y = item.yCoord
                        item.w = item.chartWidth
                        item.h = item.chartHeight
                        item.i = item.dashboardChartId
                        item.modalData = item 
                        item.resultData = []
                        item.loading = true
                        if (item.chartType == 'echart') {
                        // 具体图表类型 配置
                            item.widget = initWidgetInfo(item.chartSubType)
                        }
                    
                    }) 
                    that.panelData = list
                    that.pageLoading = false
                    that.getResultData()
                }
            })
        },
        handlePercenByData (data) {
            data.forEach(rs => {
                if (rs.value && typeof(rs.value) == 'string') {
                    rs.value = parseInt(rs.value)
                }
            })
            return data
        },
        getResultData () {
            let that = this
            this.panelData.forEach(panel => {
                let getUrl=panel.chartType=='map'?this.url.dashboardChart.mapList+'?id='+panel.dataId:this.url.data + '?id='+panel.dataId
                getAction(getUrl, {}).then(res => {
                    if (panel?.chartType == 'list') {
                        panel.resultData = res.result.data
                    } else if(panel?.chartType=='num'){
                        panel.resultData = res.result.data?.length ? res.result.data.slice(0, 1) : []
                    } else if(panel?.chartType=='map'){
                        panel.resultData = res.result.data[0].result
                        panel.options = JSON.parse(panel.chartExt)
                    } else if (panel?.chartType == 'echart') {
                        panel.resultData = this.handlePercenByData(res.result.data)
                        panel.widget.data = panel.resultData // 过滤后的数据
                    }
                    that.$forceUpdate()
                })
            })
        },
        updateConfig (params) {
            let that = this
            this.pageLoading = true
            postAction(this.url.update, params).then(res => {
                let updateList = res.result
                that.panelData.forEach(item => {
                    updateList.forEach(update => {
                        if (item.dashboardChartId == update.dashboardChartId) {
                            item.id = update.id
                        }
                    })
                })
                that.$forceUpdate()
                that.pageLoading = false
            })
        },
        layoutUpdatedEvent (newLayout) {
            console.log(newLayout)
            let params = []
            newLayout.forEach(item => {
                params.push({
                    id: item.id,
                    dashboardChartId: item.dashboardChartId,
                    xCoord: item.x,
                    yCoord: item.y,
                    chartWidth: item.w,
                    chartHeight: item.h
                })
            })
            this.updateConfig(params)
        }
    }
}
</script>
<style lang="less">
.panel-container-workplace {
  .workplace_card {
    &:hover {
      box-shadow: 0px 0px 9px rgba(0, 0, 0, 0.5);
    }
  }
  .echarts {
    width: 100%;
    height: 100%;
  }
  .ant-card-body {
    width: 100%;
    height: calc(100% - 37px);
  }
  .num-panel-item-new {
    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        border-radius: 30px;
        text-align: center;
        padding: 9px 16px;
        color: white;
        background: #ed2f2f;
        line-height: 100%;
        height: 15px;
        font-weight: 600;
        -webkit-box-sizing: content-box;
        box-sizing: content-box;
      }
    }
    .count {
      padding: 10px 0;
      font-size: 18px;
      color: #494949;
      font-weight: 500;
    }
    // .num-panel-content {
    //   flex: 1;
    //   text-align: center;
    //   font-size: 32px;
    //   font-weight: bold;
    // }
  }
}
</style>
