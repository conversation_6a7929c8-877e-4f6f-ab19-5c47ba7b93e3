<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-04-27 15:34:49
 * @LastEditors: LokNum
 * @LastEditTime: 2022-07-25 17:32:00
 * @Description: 成本报价编辑页组件
-->
<template>
  <a-modal
    v-drag    
    v-if="showVisible"
    :visible="showVisible"
    :width="800"
    :height="160"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_costTemplate`, '成本模板')"
    :footer="null"
    @cancel="showVisible = false"
    @ok="setCostOk">
    <div
      class="page-container"
      style="height: 460px;overflow:scroll">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :hasTitle="false"
        :remoteJsFilePath="remoteJsFilePath"
        :fromSourceData="fromSourceData"
        :pageStatus="containerStatus"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        setGridHeight="370"
        v-on="businessHandler">
      </business-layout>
    </div>
  </a-modal>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { handValidate } from '@/utils/util'
export default {
    name: 'PurchaseEditCost',
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    mixins: [businessUtilMixin],
    data () {
        return {
            containerStatus: 'detail',
            showVisible: false,
            fromSourceData: {},
            requestData: {
                export: { url: '/base/excelByConfig/downloadTemplate', args: () => { } },
                import: { url: '/base/excelByConfig/importExcel', args: () => { } }
            },
            externalToolBar: {
                'all': [
                ]
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_costForm_${templateNumber}_${templateVersion}`
        }
      
    },
    methods: {
        open (data, containerStatus) {
            debugger
            this.containerStatus = containerStatus || 'edit'
            this.$nextTick(() => {
                this.fromSourceData = data
                this.showVisible = true
            })
        },
        setCostOk () {
            let that = this
            let extendAllData = this.$refs[`${this.businessRefName}`].extendAllData()
            handValidate(this.$refs[`${this.businessRefName}`], extendAllData.pageConfig).then(res => {
                if (!res.validStatus) {
                    that.$refs[`${this.businessRefName}`].currentStep = res.currentStep
                    return
                }
                this.$emit('costCallBack', extendAllData)
                // that.$parent.$refs.editPage.$parent.costCallBack(extendAllData)
                that.showVisible = false
            })
        }
    }
}
</script>
