<template>
  <div class="nodeBox">
    <div class="mustNode">
      <div class="title mr_B">
        <span class="letf_title">{{$srmI18n(`${$getLangAccount()}#i18n_field_QLyO_3386bea9`, '流程阶段')}}</span>
        <span>{{$srmI18n(`${$getLangAccount()}#i18n_field_lTyCWxqHrW_cebc96a7`, '必须节点（不可更改）')}}</span>
      </div>
      <div
        class="checkBoxgroup mr_B"
        v-for="(item, i) in periodTypeInfo"
        :key="i">
        <span class="letf_title">{{ item }}</span>
        <div class="right_box">
          <a-checkbox-group
            disabled
            v-model="mustNodeCheckedList[i]"
            :options="mustNodePlainOptions[i]">
          </a-checkbox-group>
        </div>
      </div>
    </div>
    <div class="notMustNode">
      <div class="title mr_B">
        <span class="letf_title">{{$srmI18n(`${$getLangAccount()}#i18n_field_QLyO_3386bea9`, '流程阶段')}}</span>
        <a-checkbox
          v-model="notMustNodeCheckAll"
          :disabled="disabled"
          :indeterminate="indeterminate"
          @change="onCheckAllChange"
        >
          <span class="title">{{$srmI18n(`${$getLangAccount()}#i18n_field_ulTyC_82df2aab`, '非必须节点')}}</span>
        </a-checkbox>
      </div>
      <div
        class="checkBoxgroup mr_B"
        v-for="(item, i) in periodTypeInfo"
        :key="i">
        <span class="letf_title">{{ item }}</span>
        <div class="right_box">
          <a-checkbox-group
            :disabled="disabled"
            v-model="notMustNodeCheckedMap[i]"
            :options="notMustNodePlainOptions[i]"
          >
          </a-checkbox-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
    name: 'NodeCheckBox',
    props: {
        nodeData: {
            type: Object,
            default: () => {
                return {}
            }
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            indeterminate: false,
            notMustNodeCheckAll: false,
            allNodeLength: 0,
            notMustNodeIds: {},
            notMustNodePlainOptions: {},
            notMustNodeCheckedMap: {},
            mustNodePlainOptions: {},
            mustNodeCheckedList: {},
            notMustNode: [],
            mustNode: [],
            periodTypeInfo: {}
        }
    },
    methods: {
        // 全选
        onCheckAllChange (e) {
            this.indeterminate = false
            for (let k in this.notMustNodeIds) {
                let ids = e.target.checked ? this.notMustNodeIds[k] : []
                this.$set(this.notMustNodeCheckedMap, k, ids)
            }
        },
        // 初始化node
        initnotMustNodePlainOptions (data) {
            this.notMustNodePlainOptions = {}
            this.notMustNodeCheckedMap = {}
            this.notMustNodeIds = {}
            for (let k in data) {
                if (!this.notMustNodePlainOptions[k]) this.notMustNodePlainOptions[k] = []
                if (!this.notMustNodeCheckedMap[k]) this.$set(this.notMustNodeCheckedMap, k, [])
                if (!this.notMustNodeIds[k]) this.$set(this.notMustNodeIds, k, [])
                let ids = data[k].map(({ nodeName, id, checkState }) => {
                    this.allNodeLength += 1
                    this.notMustNodeIds[k].push(id)
                    if (checkState) {
                        this.notMustNodeCheckedMap[k].push(id)
                    }
                    return {
                        label: nodeName,
                        value: id
                    }
                })
                this.notMustNodePlainOptions[k].push(...ids)
            }
        },
        initMustNodePlainOptions (data) {
            this.mustNodeCheckedList = []
            this.mustNodePlainOptions = {}
            for (let k in data) {
                if (!this.mustNodePlainOptions[k]) this.mustNodePlainOptions[k] = []
                if (!this.mustNodeCheckedList[k]) this.mustNodeCheckedList[k] = []
                let ids = data[k].map(({ nodeName, id }) => {
                    this.mustNodeCheckedList[k].push(id)
                    return {
                        label: nodeName,
                        value: id
                    }
                })
                this.mustNodePlainOptions[k].push(...ids)
            }
        },
        // 向外暴露勾选数据
        extendNodeData () {
            let ids = []
            Object.values(this.notMustNodeCheckedMap).map(item => {
                item.map(id => {
                    ids.push(id)
                })
            })
            return ids
        }
    },
    watch: {
        nodeData ({ mustNode, notMustNode, periodTypeInfo }) {
            this.mustNode = mustNode
            this.notMustNode = notMustNode
            this.periodTypeInfo = periodTypeInfo
            this.initnotMustNodePlainOptions(notMustNode)
            this.initMustNodePlainOptions(mustNode)
        },
        // 监听已经勾选的
        notMustNodeCheckedMap: {
            handler () {
                let checkedListLength = this.extendNodeData().length
                this.notMustNodeCheckAll = checkedListLength == this.allNodeLength
                this.indeterminate = !!checkedListLength && checkedListLength < this.allNodeLength
            },
            deep: true
        }
    }
}
</script>
<style lang="less" scoped>
.nodeBox {
    display: flex;
    .mr_B {
        margin-bottom: 10px;
    }
    .mustNode,
    .notMustNode {
        padding: 10px;
        flex: 1;
    }
    .title {
        font-size: 16px;
        font-weight: 600;
    }
    .letf_title {
        width: 100px;
        margin-right: 30px;
        display: inline-block;
        text-align: right;
        vertical-align: top;
    }
    .right_box{
        max-width: 420px;
        display: inline-block;
    }
}
</style>
