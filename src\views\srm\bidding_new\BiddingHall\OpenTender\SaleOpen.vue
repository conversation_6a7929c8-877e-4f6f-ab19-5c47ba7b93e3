<template>
  <div class="open">
    <div
      class="container"
      :style="style">
      <openTime
        v-if="setps == 1"
        role="sale"
        @changeSetp="changeSetp"></openTime>
    </div>
    <openHall
      v-if="setps == 2"
      v-on="$listeners"></openHall>
  </div>
</template>

<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import openTime from './modules/openTime'
import openHall from './modules/SaleOpenHall'
export default {
    data () {
        return {
            showHeader: false,
            height: 0,
            setps: 1
        }
    },
    inject: [
        'tenderCurrentRow'
    ],
    computed: {
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        }
    },
    components: {
        openTime,
        openHall
    },
    methods: {
        changeSetp (setp){
            this.setps = setp
        }
    },
    created () {
    }
}
</script>

<style lang="less" scoped>
.open {
	.posA {
		& + .container {
			margin-top: 44px;
		}
	}
	.container {
		margin-left: -8px;
		margin-top: -8px;
		padding: 12px;
		// background: #e9e9e9;
    width:1000px;
    margin: 0 auto;
		text-align: center;
		font-weight: 400;
		font-size: 14px;
	}
}
</style>
