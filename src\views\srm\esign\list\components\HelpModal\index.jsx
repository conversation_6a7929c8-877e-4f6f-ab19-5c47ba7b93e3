import {getLangAccount, srmI18n} from '@/utils/util'
export default {
    name: 'HelpModal',
    model: {
        prop: 'visible',
        event: 'change'
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: srmI18n(`${getLangAccount()}#i18n_title_helpText`, '帮助说明')
        },
        helpDesc: {
            type: String,
            default: ''
        }
    },
    data () {
        return {}
    },
    methods: {
        handleCancel () {
            this.resetData()
            this.$emit('change', false)
        },
        resetData () {
            Object.assign(this.$data, this.$options.data.call(this))
        }
    },
    render (h) {
        const props = {
            visible: this.visible,
            title: this.title,
            width: 900,
            keyboard: false,
            maskClosable: false,
            destroyOnClose: true,
            footer: null
        }

        const on = {
            cancel: this.handleCancel,
            ok: this.handleCancel
        }

        return (
            <div class="RecordModal">
                <a-modal { ...{ props, on } }>
                    <div class="content" domPropsInnerHTML={ this.helpDesc }></div>
                </a-modal>
            </div>
        )
    }
}
