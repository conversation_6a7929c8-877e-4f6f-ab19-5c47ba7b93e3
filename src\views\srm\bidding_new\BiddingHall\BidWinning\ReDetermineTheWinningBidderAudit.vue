<template>
  <div class="page detail-page">
    <a-spin :spinning="confirmLoading">
      <div class="page-header">
        <a-page-header :ghost="false"
        >
          <template slot="extra">
            <template v-if="isShowSub">
              <a-button
                v-for="(item, index) in awardNameBtnView[awardName]"
                :key="index"
                @click="item.click">{{ item.title }}</a-button>
            </template>
            <taskBtn
              v-if="taskInfo.taskId"
              :currentEditRow="currentEditRow"
              :pageHeaderButtons="publicBtn"
              v-on="$listeners"/>
          </template>
          <template slot="tags">
            <a-tag
              color="blue"
              style="font-size:16px">
              {{ subpackageTitle }}
            </a-tag>
          </template>
        </a-page-header>

        <div
          class="container"
          v-if="!isShowSub"
          :style="style">
          <div>
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBLVH_7e1ecb2e`, '中标人信息') }}</span>
            </titleTrtl>
            <vxe-grid
              v-bind="gridConfig"
              :height="250"
              ref="table"
              :data="tableData"
              :columns="tableColumns"
              show-overflow="title" >
            </vxe-grid>
          </div>
          <div>
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_AHjW_27a9ee3d`, '变更原因') }}</span>
            </titleTrtl>
            <div>
              <a-row class="margin-t-20">
                <a-col :span="3">
                  <div class="label">
                    {{ $srmI18n(`${$getLangAccount()}#i18n_field_AHjWlRW_ba1cc783`, '变更原因说明:') }}
                  </div>
                </a-col>
                <a-col :span="12">
                  <a-textarea
                    :disabled="pageStatus == 'detail'"
                    v-model="formData.reason"
                    :auto-size="{ minRows: 2, maxRows: 6 }"></a-textarea>
                </a-col>
              </a-row>
              <a-row class="margin-t-20">
                <a-col :span="3">
                  <div class="label">
                    {{ $srmI18n(`${$getLangAccount()}#i18n_field_BIW_23da548`, '附件:') }}
                  </div>
                </a-col>
                <a-col :span="3">
                </a-col>
                <a-col
                  :span="20"
                  :offset="3">
                  <div
                    v-for="fileItem in formData.attachmentList"
                    :key="fileItem.id">
                    <span>{{ fileItem.fileName }}</span>
                    <a-button
                      @click="preViewEvent(fileItem)"
                      type="link">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a-button>
                    <a-button
                      @click="downloadEvent(fileItem)"
                      type="link"
                      style="padding-left: 0;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a-button>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
        <div v-else>
          <ReDetermineTheWinningSubAudit
            :formData="formData"
            ref="ReDetermineTheWinningSubAudit"></ReDetermineTheWinningSubAudit>
        </div>
      </div>
    </a-spin>
  </div></template>
<script lang="jsx">
import ContentHeader from '../components/content-header'
import titleTrtl from '../components/title-crtl'
import {getAction, postAction} from '@/api/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { USER_INFO } from '@/store/mutation-types'
import flowViewModal from '@comp/flowView/flowView'
import { mapGetters } from 'vuex'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
import ReDetermineTheWinningSubAudit from './components/ReDetermineTheWinningSubAudit'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    name: 'ReDetermineTheWinningBidder',
    components: {
        titleTrtl,
        ContentHeader,
        flowViewModal,
        taskBtn,
        ReDetermineTheWinningSubAudit
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    mixins: [tableMixins],
    data () {
        return {
            isShowSub: false,
            subpackageTitle: '',
            publicBtn: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'rollBack',
                    click: this.goBackAudit,
                    showCondition: () => {
                        return true
                    }
                }
            ],
            flowId: 0,
            flowView: false,
            confirmLoading: false,
            awardNameBtnView: {
                'normalAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pRdXlB_8d34dee3`, '按供应商授标'), type: 'primary', click: () => {this.changeAwardName('supplierAward')}, attrs: {type: 'primary'} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pSLRHlB_e8a46290`, '按物料明细授标'), type: 'primary', click: () => {this.changeAwardName('materialAward')}, attrs: {type: 'primary'} }
                ],
                'materialAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ],
                'supplierAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ]
            },
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    field: 'scopeSort',
                    width: '80'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQsBL_c7668749`, '是否中标人'),
                    'field': 'affirm',
                    width: 120,
                    slots: {
                        default: ({row, column}) => {
                            return [
                                <a-checkbox disabled={this.pageStatus == 'detail'}v-model={row[column.property]} ></a-checkbox>
                            ]
                        }
                    }
                }
            ],
            tableData: [],
            formData: {},
            showHeader: true,
            height: 0,
            awardName: 'normalAward',
            url: {
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryById: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryById'
            }
        }
    },
    computed: {
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        ...mapGetters([
            'taskInfo'
        ]),
        pageStatus () {
            return 'detail'
        }
    },
    methods: {
        async getData () {
            this.confirmLoading = true
            let params = {}
            let res2 = {}
            params['id'] = this.currentEditRow.id
            res2 = await getAction(this.url.queryById, params)
            if (res2.code == 200 && res2.result) {
                let {bidWinningAffirmPriceItemVoList = [], ...others} = res2.result || {}
                this.isShowSub = others.quoteType == '1' ? true : false
                this.formData = others
                // 总项
                if (others.quoteType == '0') {
                    bidWinningAffirmPriceItemVoList && bidWinningAffirmPriceItemVoList.forEach(item => {
                        item.saleQuoteColumnVOS.forEach(vos => {
                            item[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                            item.affirm = item.affirm == '1' ? true : false
                        })
                        item.affirm = item.affirm == '1' ? true : false

                    })
                    this.tableData = bidWinningAffirmPriceItemVoList
                    params['subpackageId'] = this.formData.subpackageId
                    if (this.formData.evaluationType == '1') {
                        const res = await getAction(this.url.queryPrice, params)
                        if (res.code == 200 && res.result) {
                            const resultData = res.result
                            let columns = []
                            resultData.forEach(data => {
                                let obj = {
                                    title: data.title,
                                    children: []
                                }
                                let columnChildren = []
                                data.quoteColumnList.forEach(column => {
                                    column['field'] = `${column['field']}_${data['id']}`
                                    columnChildren.push(column)
                                })
                                obj.children = columnChildren
                                columns.push(obj)
                            })
                            this.tableColumns = this.tableColumns.filter(column => {
                                if (!column.hasOwnProperty('children')) {
                                    return column
                                }
                            })
                            this.tableColumns.splice(2, 0, ...columns)
                        }
                    } else {
                        this.tableColumns.splice(1, 0, {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                            field: 'quote'
                        })
                    }
                }
            }
            this.confirmLoading = false
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
        },
        changeAwardName (name) {
            this.awardName = name
            this.$refs.ReDetermineTheWinningSubAudit.changeAwardName(name)
        },
        /**
         * 审批方法
        */
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        preViewEvent (row){ // 文件预览
            row.subpackageId = this.formData.subpackageId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (row) {
            const fileName = row.fileName
            row.subpackageId = this.formData.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    },
    mounted () {
        this.subpackageTitle = this.currentEditRow.subject || ''
        this.init()
    }
}
</script>
<style lang="less" scoped>
.container{

    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
.margin-t-20{
  margin-top: 20px;
}
.label{
  text-align:right;
  padding-right: 10px;
}
</style>




