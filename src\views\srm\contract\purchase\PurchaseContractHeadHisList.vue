<template>
  <div style="height:100%">
    <list-layout
      v-show="!showDetailPage && !showViewDiffPage"
      ref="listPage"
      :pageData="pageData"
      :url="url" />
    <!-- 表单区域 -->
    <viewPurchaseContractHeadHis-modal
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <viewItemDiff-modal ref="viewDiffModal" />
  </div>
</template>
<script>
import ViewPurchaseContractHeadHisModal from './modules/ViewPurchaseContractHeadHisModal'

import {ListMixin} from '@comp/template/list/ListMixin'
import ViewItemDiffModal from './modules/ViewItemDiffModal'
import { getAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        'viewPurchaseContractHeadHis-modal': ViewPurchaseContractHeadHisModal,
        'viewItemDiff-modal': ViewItemDiffModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            showDetailPage: false,
            showViewDiffPage: false,
            pageData: {
                businessType: 'contract',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_comparisons`, '比对'), icon: '', clickFn: this.viewDiffItem, type: 'primary'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), icon: '', clickFn: this.goBack, type: 'primary'}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_comparisons`, '比对'), clickFn: this.viewDiff},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.showDetail}
                ]
            },
            url: {
                list: '/contract/purchaseContractHeadHis/list',
                columns: 'purchaseContractHeadList'
            }
        }
    },
    created () {
        this.pageData.form.contractId = this.currentEditRow.id
    },
    mounted () {
        // this.serachTabs('srmContractStatus', 'requestStatus')
        this.serachCountTabs('/contract/purchaseContractHeadHis/counts')
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserContractHeader`, '采购方合同头'))
        },
        showDetail (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },
        viewDiff (row) {
            if (row.type!='head'){
                row.type='item'
            }
            getAction('/contract/purchaseContractHead/diffContractContent', {id: row.id, newId: row.contractId, type: row.type}).then(res => {
                if(res.success) {
                    row.originalContent = 'dadadadadadw'
                    row.itemContent = 'dadadadw'
                    this.$refs.viewDiffModal.open(res.result)
                }
            })
        },
        viewDiffItem () {
            let itemGrid =  this.$refs.listPage.$refs.listGrid
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(checkboxRecords.length!=2) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTwoRowsDataToCompare`, '请选择两行数据进行比对！'))
                return
            }
            this.viewDiff({id: checkboxRecords[0].id, contractId: checkboxRecords[1].id, type: 'head'})
        },
        hideDetail (){
            this.showDetailPage = false
        },
        goBack (){
            this.$emit('hide')
        }
    }
}
</script>