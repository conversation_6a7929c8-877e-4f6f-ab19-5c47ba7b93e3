<template>
  <div>
    <a-form-model
      :label-col="labelCol"
      ref="form"
      :wrapper-col="wrapperCol"
      :rules="rules"
      :model="formData">
      <a-row :getterr="12">
        <a-col :span="colSapn">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_tdCK_4155670c`, '计算方式')"
            prop="calType"
            required>
            <m-select
              v-if="isEdit"
              v-model="formData.calType"
              @change="calTypeChange"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
              dict-code="tenderCalType" />
            <span v-else>{{ formData.calType_dictText }}</span>
          </a-form-model-item>
        </a-col>
        <a-col
          :span="colSapn"
          v-if="formData.calType == '0'">
          <a-form-model-item
            prop="pricePointsCalFormula"
            required>
            <span slot="label">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_umztdRK_d3c327ba`, '价格分计算公式') }}
              <a-tooltip :title="pricePointsCalFormulaTitle">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-select
              v-if="isEdit"
              v-model="formData.pricePointsCalFormula"
              @change="pricePointsCalFormulaChange"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
              dict-code="tenderPricePointsCalFormula" />
            <span v-else>{{ formData.pricePointsCalFormula_dictText }}</span>
          </a-form-model-item>
        </a-col>
        <a-col
          :span="colSapn"
          v-if="['operationMinPricePositiveDeviationStrategy', 'operationStandardPriceStrategy'].includes(formData.pricePointsCalFormula)">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_suBxUtruWWVWzW_70417194`, '报价每高于基准价1%扣（分）')"
            prop="aboveScore"
            required>
            <a-input-number
              style="width: 100%"
              :min="0"
              v-model="formData.aboveScore"
              v-if="isEdit"></a-input-number>
            <span v-else>{{ formData.aboveScore }}</span>
          </a-form-model-item>
        </a-col>
        <a-col
          :span="colSapn"
          v-if="formData.pricePointsCalFormula == 'operationStandardPriceStrategy'">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_BnUtruWWVWzW_a5122498`, '每低于基准价1%扣（分）')"
            prop="belowScore"
            required>
            <a-input-number
              style="width: 100%"
              v-model="formData.belowScore"
              :min="0"
              v-if="isEdit"></a-input-number>
            <span v-else>{{ formData.belowScore }}</span>
          </a-form-model-item>
        </a-col>
        <a-col
          :span="colSapn"
          v-if="['operationMinPricePositiveDeviationStrategy', 'operationStandardPriceStrategy'].includes(formData.pricePointsCalFormula)">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_enVRWzW_9f17514d`, '最低扣至（分）')"
            prop="lowestScore"
            required>
            <a-input-number
              style="width: 100%"
              v-model="formData.lowestScore"
              v-if="isEdit"
              :min="0"></a-input-number>
            <span v-else>{{ formData.lowestScore }}</span>
          </a-form-model-item>
        </a-col>
        <a-col
          :span="colSapn"
          v-if="formData.calType == '0'">
          <a-form-model-item
            prop="baseCalRules"
            required>
            <span slot="label">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_truMRLF_f17f1066`, '基准价取值规则') }}
              <a-tooltip :title="baseCalRulesTitle">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-select
              v-if="isEdit"
              :disabled="['operationMinPricePositiveDeviationStrategy', 'operationMinPriceStrategy', 'operationMinRatePriceStrategy'].includes(formData.pricePointsCalFormula)"
              v-model="formData.baseCalRules"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
              @change="handleChangeBaseCalRules"
              dict-code="tenderBaseCalRules" />
            <span v-else>{{ formData.baseCalRules_dictText }}</span>
          </a-form-model-item>
        </a-col>
        <a-col
          :span="colSapn"
          v-if="formData.calType == '1'">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_tdMW_41555c97`, '计算描述')"
            prop="calArtificialRulesDesc"
            required>
            <a-textarea
              v-model="formData.calArtificialRulesDesc"
              v-if="isEdit"></a-textarea>
            <span v-else>{{ formData.calArtificialRulesDesc }}</span>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col
          :span="colSapn"
          v-if="showTenderBidLette">
          <a-form-model-item prop="tenderBidLetterId">
            <span slot="label">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_eBx_17efc2b`, '投标函') }}
              <a-tooltip :title="$srmI18n(`${$getLangAccount()}#i18n_field_VWsMnOSMeBx_3486e1f0`, '请先保存才能获取投标函')">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <a-select
              v-model="formData.tenderBidLetterId"
              v-if="isEdit"
              allowClear
              @dropdownVisibleChange="getTenderBidTetterVoList"
              @change="changeTenderBidLetterId"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`">
              <a-select-option
                v-for="item in tenderBidTetterVoList"
                :value="item.value"
                :key="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
            <span
              v-else
              :title="formData.bidLetterName">{{ formData.bidLetterName }}</span>
          </a-form-model-item>
        </a-col>
        <a-col
          :span="colSapn"
          v-if="showTenderBidLette">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_suAR_2e0a1148`, '报价列名')"
            prop="quoteColumnName">
            <a-select
              v-model="formData.quoteColumnName"
              v-if="isEdit"
              allowClear
              :disabled="!formData.tenderBidLetterId"
              @dropdownVisibleChange="getTenderBidTetterVoListColumn"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`">
              <a-select-option
                v-for="item in quoteColumnNameOptions"
                :value="item.value"
                :key="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
            <span v-else>{{ formData.bidLetterFieldName }}</span>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { valitNumberLength } from '@views/srm/bidding_new/utils/index'
export default {
    props: {
        currentItem: {
            type: Object,
            default: () => {
                return {}
            }
        },
        tenderEvaluationTemplatePriceRegulationInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        pageStatus: {
            type: String,
            default: 'edit'
        },
        isBiddingFileModule: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        tenderEvaluationTemplatePriceRegulationInfo: {
            handler (data) {
                if (data) {
                    this.formData = Object.assign({}, data)
                }
            },
            immediate: true,
            deep: true
        }
    },
    inject: {
        subpackageId: {
            from: 'subpackageId',
            default: ''
        }
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        showTenderBidLette () {
            return this.isBiddingFileModule
        },
        subId () {
            return this.subpackageId() || ''
        }
    },
    data () {
        let checkNumber = (rule, value, callback) => {
            if (value) {
                if (!valitNumberLength(value, 8)){
                    callback(new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '8'))
                }
            }
            callback()
        }
        return {
            labelCol: {
                span: 12
            },
            wrapperCol: {
                span: 12
            },
            colSapn: 8,
            rules: {
                calType: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFtdCK_88acb1c3`, '请选择计算方式') }],
                // tenderBidLetterId: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFeBx_f347fe94`, '请选择投标函')}],
                // quoteColumnName: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFsuAR_75615bff`, '请选择报价列名')}],
                pricePointsCalFormula: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFumztdRK_db02cda3`, '请选择价格分计算公式') }],
                baseCalRules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFtruMRLF_f8beb64f`, '请选择基准价取值规则') }],
                aboveScore: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMsuBxUtruWWVWzW_caea599`, '请填写报价每高于基准价1%扣（分）') },
                    { validator: checkNumber, trigger: 'change' }
                ],
                belowScore: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMBnUtruWWVWzW_20d695dd`, '请填写每低于基准价1%扣（分）') },
                    { validator: checkNumber, trigger: 'change' }
                ],
                lowestScore: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMenVRWzW_5e868e8`, '请填写最低扣至（分）') },
                    { validator: checkNumber, trigger: 'change' }
                ],
                calArtificialRulesDesc: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMtdMW_ac8de2dc`, '请填写计算描述') },
                    { max: 1000, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow1000`, '内容长度不能超过1000个字符')}
                ]
            },
            url: {
                queryTenderBidLetter: '/tender/tenderProjectAttachmentInfo/queryTenderBidLetter',
                queryPriceColumn: '/tender/tenderProjectAttachmentInfo/queryPriceColumn'
            },
            tenderBidTetterVoList: [],
            quoteColumnNameOptions: [],
            getTenderBidTetterLoading: false,
            formData: {},
            pricePointsCalFormulaTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iFumztdRKSXKFDRKKvtMW_1eedc8a2`, '选择价格分计算公式后展示具体公式示例及描述'),
            baseCalRulesTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iFtruMRLFSXKFDRKKvtMW_7a160f4e`, '选择基准价取值规则后展示具体公式示例及描述')
        }
    },
    methods: {
        getValidatePromise () {
            return this.$refs.form.validate()
        },
        getAllData () {
            return this.formData
        },
        pricePointsCalFormulaChange (v) {
            this.$set(this.formData, 'aboveScore', '')
            this.$set(this.formData, 'belowScore', '')
            this.$set(this.formData, 'lowestScore', '')
            if (['operationMinPricePositiveDeviationStrategy', 'operationMinPriceStrategy', 'operationMinRatePriceStrategy'].includes(v)) {
                this.$set(this.formData, 'baseCalRules', '0')
                this.baseCalRulesTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IenjXeBLtru_c4c4b115`, '以最低有效投标为基准价')
            }
            this.setPricePointsCalFormulaTitle(v)
        },
        setPricePointsCalFormulaTitle (v) {
            switch (v) {
            case 'operationMinPriceStrategy':
                this.pricePointsCalFormulaTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n__enjXeBuLtruWjzWeneBuWeBuWkz_bc576493`, '最低有效投标价为基准价，得分=最低投标价/投标价*总分')
                break
            case 'operationMinPricePositiveDeviationStrategy':
                this.pricePointsCalFormulaTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n__enjXeBuLtruWjCzWsuBxUtruWWKVWWzWenVRWz_61c5b05b`, '最低有效投标价为基准价，得满分。报价每高于基准价1%时扣x 分，最低扣至y分')
                break
            case 'operationStandardPriceStrategy':
                this.pricePointsCalFormulaTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n__trujCzWsuBxUtruWWVWzWBnUtruWWVWzWenVRWz_f5776158`, '基准价得满分，报价每高于基准价1%扣X分，每低于基准价1%扣Y分，最低扣至Z分')
                break
            case 'operationMinRatePriceStrategy':
                this.pricePointsCalFormulaTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enjXeBuLtrujz1eBueneBueneBukz_ad33a47d`, '最低有效投标价为基准价， 得分 =【1- （投标价-最低投标价）/最低投标价】 * 总分')
                break
            default:
                this.pricePointsCalFormulaTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n__enjXeBuLtruWjCzWsuBxUtruWWKVWWzWenVRWz_61c5b05b`, '选择价格分计算公式后展示具体公式示例及描述')
            }
        },
        getTenderBidTetterVoList (v) {
            if (v) {
                this.getTenderBidTetterLoading = true
                let params = {
                    subpackageId: this.subId
                }
                let url
                // 二步法时候需要添加currentStep参数
                if (['0', '1'].includes(this.currentItem.currentStep)) params.currentStep = this.currentItem.currentStep
                if(this.$ls.get('changeBidFile') && this.$ls.get('clarificationId')){
                    url = '/tender/purchaseTenderClarificationInfo/queryTenderBidLetter'
                    params.clarificationId = this.$ls.get('clarificationId')
                }else{
                    url = this.url.queryTenderBidLetter
                }
                getAction(url, params)
                    .then((res) => {
                        let data = res.result || []
                        this.tenderBidTetterVoList = data.map(({ name, id }) => {
                            return {
                                text: name,
                                value: id
                            }
                        })
                        this.$forceUpdate()
                    })
                    .finally(() => {
                        this.getTenderBidTetterLoading = false
                    })
            }
        },
        async getTenderBidTetterVoListColumn (v) {
            if (v) {
                this.getTenderBidTetterLoading = true
                let url
                if(this.$ls.get('changeBidFile')){
                    url = '/tender/purchaseTenderClarificationInfo/queryPriceColumn'
                }else{
                    url = this.url.queryPriceColumn
                }
                await getAction(url, {
                    subpackageId: this.subId,
                    bidLetterId: this.formData.tenderBidLetterId
                })
                    .then((res) => {
                        let data = res.result || []
                        this.quoteColumnNameOptions = data.map(({ title, field }) => {
                            return {
                                text: title,
                                value: field
                            }
                        })
                    })
                    .finally(() => {
                        this.getTenderBidTetterLoading = false
                    })
            }
        },
        changeTenderBidLetterId () {
            this.$set(this.formData, 'quoteColumnName', '')
        },
        handleChangeBaseCalRules (v) {
            this.setBaseCalRulesTitle(v)
        },
        setBaseCalRulesTitle (v) {
            switch (v) {
            case '0':
                this.baseCalRulesTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IenjXeBLtru_c4c4b115`, '以最低有效投标为基准价')
                break
            case '1':
                this.baseCalRulesTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n__djjXeBsujUTRLtru_b48bb9e1`, '所有有效投标报价的平均值为基准价')
                break
            default:
                this.baseCalRulesTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iFtruMRLFSXKFDRKKvtMW_7a160f4e`, '选择基准价取值规则后展示具体公式示例及描述')
            }
        },
        calTypeChange (v) {
            this.$set(this.formData, 'pricePointsCalFormula', '')
            this.$set(this.formData, 'operationStandardPriceStrategy', '')
        }
    },
    async mounted () {
        // 获取招标函信息
        if (this.formData.tenderBidLetterId) {
            await this.getTenderBidTetterVoList(1)
            await this.getTenderBidTetterVoListColumn(1)
        }
        // 提示语初始化
        let { baseCalRules, pricePointsCalFormula } = this.formData
        this.setPricePointsCalFormulaTitle(pricePointsCalFormula)
        this.setBaseCalRulesTitle(baseCalRules)
    }
}
</script>
<style lang="less" scoped>
    :deep(.ant-form-item-control){
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>