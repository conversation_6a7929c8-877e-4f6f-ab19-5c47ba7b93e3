<template>
  <div class="registration-status">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      @content-header-check="checkRisk"
    />

    <div
      class="container"
      :style="style">
      <a-spin :spinning="confirmLoading">
        <remote-js
          v-if="vuex_currentEditRow.id"
          :src="fileSrc"
          @load="loadSuccess"
          @error="loadError" />

        <detail-layout
          ref="detailPage"
          mode="tiled"
          :page-data="pageData"
          :url="url" />
      </a-spin>
    </div>
    <a-modal
      v-drag    
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果')"
      v-model="riskShow"
      @ok="confirm"
      @cancel="confirm"
    >
      <div
        v-for="(data, index) in riskList"
        :key="data">
        <p v-if="data.type==='0'">{{ index + 1 }}、{{ data.sourceName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KCbVeMKdeyR_3727867`, '在股权穿透存在相同结果') }}：{{ data.result
        }}</p>
        <p v-if="data.type==='1'">{{ index + 1 }}、{{ data.sourceName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KeslILMKdeyR_85d14c24`, '在最终受益人存在相同结果') }}：{{ data.result
        }}</p>
        <p v-if="data.type==='2'">{{ index + 1 }}、{{ data.sourceName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KKVLMKdeyR_46ebba36`, '在实控人存在相同结果') }}：{{ data.result
        }}</p>
        <p
          :key="index"
          v-if="data.type==='4'">{{ index + 1 }}、{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_KsuWWMKdeyR_17c9510e`, '在报价ip存在相同结果') }}：{{ data.result
          }}</p>
        <p 
          v-if="data.type === '5' || data.type === '6' || data.type === '7' || data.type === '8'">{{ index + 1 }}、{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_MKW_162724a`, '存在：') }}{{ data.result }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_RH_a59e8`, '关系') 
        }}</p>
      </div>
    </a-modal>
  </div>
</template>

<script>
import ContentHeader from '@/views/srm/bidding_project/hall/components/content-header'
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'
import { getAction } from '@/api/manage'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    mixins: [
        DetailMixin
    ],
    components: {
        'content-header': ContentHeader
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh'
    ],
    data () {
        return {
            confirmLoading: false,
            riskShow: false,
            riskList: [],
            isLocal: true,
            btns: [
                { 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_LBJi_2919d4bc`, '围标探测'), 
                    type: 'primary', 
                    event: 'check', 
                    icon: 'question-circle-o', 
                    helpText: `1、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yVLIxDjWVOSmhyR_c7532315`, '接口为异步调用，请静候查询结果')}<br> 2、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lnICdjWRWefRdXWRxOfUWWu_f2d40ee9`, '受第三方应用限制，最大供应商数量不能大于20家')}`
                }
            ],
            showHeader: true,
            height: 400,
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_participatingSuppliers`, '参与供应商'),
                        groupCode: 'supplierInfo',
                        type: 'grid',
                        custom: {
                            ref: 'biddingSupplierList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'),
                                    field: 'toElsAccount',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'),
                                    field: 'supplierCode',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                                    field: 'supplierName',
                                    width: 300
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'),
                                    field: 'replyStatus_dictText',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'),
                                    field: 'replyTime',
                                    width: 140
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                                    field: 'contacts',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'),
                                    field: 'phone',
                                    width: 120
                                },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mail`, '邮件'), field: 'email', width: 150 },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewBidsPermission`, '查看标书权限'),
                                    field: 'bidCheck_dictText',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidAuthority`, '投标权限'),
                                    field: 'bidQuote_dictText',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sourceType`, '来源类型'),
                                    field: 'sourceType_dictText',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    showOverflow: true,
                                    slots: { default: 'grid_opration' }
                                }
                            ],
                            optColumnList: [
                                {
                                    type: 'risk',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_suppliersRisk`, '供应商风险'),
                                    clickFn: this.handleRisk
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJitH_4a08df61`, '围标探测记录'),
                        groupCode: 'probeResultList',
                        type: 'grid',
                        custom: {
                            ref: 'probeResultList',
                            columns: [
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'supplierName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                                    width: 120
                                },
                                {
                                    field: 'probeResult',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果'),
                                    width: 500
                                },
                                {
                                    field: 'createTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                                    width: 150
                                },
                                {
                                    field: 'createBy',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'),
                                    width: 120
                                }
                            ],
                            showOptColumn: false
                        }
                    }
                ]
            },
            url: {
                detail: '/bidding/purchaseBiddingHead/queryById',
                submit: '/a1bpmn/audit/api/submit'
            }
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        fileSrc () {
            const {
                templateNumber = '',
                templateVersion = '',
                templateAccount = '',
                busAccount = ''
            } = this.vuex_currentEditRow || {}

            const configFiles = this.$variateConfig['configFiles']
            const time = +new Date()
            const url = `${configFiles}/${templateAccount || busAccount}/purchase_bidding_${templateNumber}_${templateVersion}.js?t=`+time
            return url
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        init () {
            if (this.vuex_currentEditRow.projectId) {
                this.$refs.detailPage.queryDetail(this.vuex_currentEditRow.id, this.handleAfterDealSource)
            }
        },
        confirm () {
            this.riskShow = false
        },
        //查看风险
        handleRisk ({ toElsAccount }) {
            sessionStorage.setItem('cache_elsAccout', toElsAccount)
            this.$router.push({
                path: '/srm/base/SupplierVenture'
            })
        },
        checkRisk () {
            const url = '/bidding/purchaseBiddingHead/queryRisk?id=' + this.vuex_currentEditRow.id
            this.confirmLoading = true
            getAction(url)
                .then(res => {
                    if (res && res.success) {
                        if (res.result && res.result.length) {
                            this.riskList = res.result
                            this.riskShow = true
                        } else {
                            this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IiRCISReWF_3b256afc`, '检测公司间无共同数据'))
                        }
                    }else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.init()
                })
        },
        beforeHandleData (data) {
            console.log('beforeHandleData', data)
            data.groups = []
        },
        handleAfterDealSource (data) {
            if (data?.showProbeList == '0') {
                this.pageData.groups = this.pageData.groups.filter(v=>v.groupCode !== 'probeResultList')
            }
        }
    },
    // 使用 beforeRouteUpdate 导航守卫监听路由变化
    // 对路由销毁重建
    beforeRouteUpdate (to, from, next) {
        this.routerRefresh() //路由销毁重建方法
        next()
    },
    created () {
        this.height = document.documentElement.clientHeight
    }
}
</script>

<style lang="less" scoped>
.registration-status {
  .posA {
    & + .container {
      margin-top: 44px;
    }
  }

  .container {
    background: #fff;
    padding: 12px;
  }
}
</style>
