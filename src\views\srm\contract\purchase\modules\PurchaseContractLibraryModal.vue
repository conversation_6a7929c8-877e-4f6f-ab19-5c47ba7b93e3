<template>
  <div class="els-page-comtainer">
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      @goBack="goBack"
    />
  </div>
</template>

<script>
import {duplicateCheck} from '@/api/api'
import {tileEditPageMixin} from '@comp/template/tileStyle/tileEditPageMixin'

export default {
    name: 'PurchaseContractLibraryModal',

    mixins: [tileEditPageMixin],
    data () {
        return {
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_clauseLibrary`, '条款库'),
                form: {
                    itemName: '',
                    itemType: '',
                    itemContent: '',
                    itemVersion: '',
                    auditStatus: '',
                    flowId: '',
                    fbk1: '',
                    fbk2: '',
                    fbk3: '',
                    fbk4: '',
                    fbk5: '',
                    fbk6: '',
                    fbk7: '',
                    fbk8: '',
                    fbk9: '',
                    fbk10: '',
                    extendField: ''
                },
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            ref: 'baseForm',
                            colSpan: '24',
                            type: 'form',
                            form: {},
                            list: [
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                                    fieldName: 'itemName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterProjectName`, '请输入项目名称')
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                                    fieldName: 'itemType',
                                    dictCode: 'srmItemType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterProjectType`, '请输入项目类型')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectContent`, '项目内容'),
                                    fieldName: 'itemContent',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterProjectContent`, '请输入项目内容')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectVersion`, '项目版本'),
                                    fieldName: 'itemVersion',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterProjectVersion`, '请输入项目版本')
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm65d_auditStatus`, '审批状态'),
                                    fieldName: 'auditStatus',
                                    dictCode: 'srmAuditStatus'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processId`, '流程id'),
                                    fieldName: 'flowId',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterProcessId`, '请输入流程id')
                                },
                                {
                                    type: 'input',
                                    label: 'fbk1',
                                    fieldName: 'fbk1',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk1'
                                },
                                {
                                    type: 'input',
                                    label: 'fbk2',
                                    fieldName: 'fbk2',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk2'
                                },
                                {
                                    type: 'input',
                                    label: 'fbk3',
                                    fieldName: 'fbk3',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk3'
                                },
                                {
                                    type: 'input',
                                    label: 'fbk4',
                                    fieldName: 'fbk4',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk4'
                                },
                                {
                                    type: 'input',
                                    label: 'fbk5',
                                    fieldName: 'fbk5',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk5'
                                },
                                {
                                    type: 'input',
                                    label: 'fbk6',
                                    fieldName: 'fbk6',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk6'
                                },
                                {
                                    type: 'input',
                                    label: 'fbk7',
                                    fieldName: 'fbk7',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk7'
                                },
                                {
                                    type: 'input',
                                    label: 'fbk8',
                                    fieldName: 'fbk8',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk8'
                                },
                                {
                                    type: 'input',
                                    label: 'fbk9',
                                    fieldName: 'fbk9',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk9'
                                },
                                {
                                    type: 'input',
                                    label: 'fbk10',
                                    fieldName: 'fbk10',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnter`, '请输入') + 'fbk10'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_extendedField`, '扩展字段'),
                                    fieldName: 'extendField',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterExtendedField`, '请输入扩展字段')
                                }
                            ]
                        }
                    }
                ],
                validRules: {
                    itemName: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    itemType: [{
                        max: 20,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow20`, '内容长度不能超过20个字符')
                    }],
                    itemVersion: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    auditStatus: [{
                        max: 20,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow20`, '内容长度不能超过20个字符')
                    }],
                    flowId: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk1: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk2: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk3: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk4: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk5: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk6: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk7: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk8: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk9: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    fbk10: [{
                        max: 50,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')
                    }],
                    extendField: [{
                        max: 500,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')
                    }]
                }
            },
            url: {
                add: '/contract/purchaseContractLibrary/add',
                edit: '/contract/purchaseContractLibrary/edit',
                detail: '/contract/purchaseContractLibrary/queryById'
            }
        }
    },
    methods: {
        validateCode (rule, value, callback) {
            // 重复校验
            var params = {
                tableName: '',
                fieldName: '',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then(res => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        selectCallBack (item) {
            this.pageData.form.dictCode = item[0].dictCode
            this.$refs.editPage.$forceUpdate()
        }
    }
}
</script>

<style lang="less" scoped>
</style>