<template>
  <div style="height: 100%">
    <list-layout
      v-show="!showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 详情页面 -->
    <saleDeductCost-detail
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import SaleDeductCostDetail from './modules/SaleDeductCostDetail'
import { ListMixin } from '@comp/template/list/ListMixin'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        SaleDeductCostDetail
    },
    data () {
        return {
            pageData: {
                businessType: 'deductCost',
                button: [
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF }
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleSpecialByView, authorityCode: 'deductCost#saleDeductCost:view'},
                    { type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat },
                    { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord }
                ],
                optColumnWidth: 100,
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNVVty_b57e1fa6`, '请输入扣款单号')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/finance/saleDeductCost/list',
                exportXlsUrl: 'finance/saleDeductCost/exportXls',
                columns: 'saleDeductCostList'
            },
            tabsList: []
        }
    },
    mounted () {
        this.serachCountTabs('/finance/saleDeductCost/counts')
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.deductNumber || id
            // 创建
            layIM.creatGruopChat({ id, type: 'SaleDeductCost', url: this.url || '', recordNumber })
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls('销售扣款费用管理')
        }
    }
}
</script>