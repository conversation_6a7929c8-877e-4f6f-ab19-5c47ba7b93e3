/**
 * 邮箱
 * @param {*} s
 */
export function isEmail (s) {
    return /^([a-zA-Z0-9._-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)
}

/**
 * 手机号码
 * @param {*} s
 */
export function isMobile (s) {
    return /^1[0-9]{10}$/.test(s)
}

/**
 * 是否整数
 * @param {*} s
 */
export function isInteger (s) {
    return /^\d+$/.test(s)
}

/**
 * 电话号码
 * @param {*} s
 */
export function isPhone (s) {
    return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL (s) {
    return /^http[s]?:\/\/.*/.test(s)
}

/**
 * 传真号码
 * @param {*} s
 */
export function isFax (s){
    return /^(?:\d{3,4}-)?\d{7,8}(?:-\d{1,6})?$/.test(s)
}

/**
 * 邮政编码
 * @param {*} s
 */
export function isPostCode (s) {
    return /^\d{6}$/.test(s)
}

/**
 * Decimal(18,4)
 * @param {*} s
 */
export function isDecimal (s){
    // return /^\d{1,14}(\.[0-9]{1,4})?$/.test(s.replace(/^0{2,}\./, '0.').replace(/^0{2,}/, '0'))
    return /^\d{1,14}(\.[0-9]{1,4})?$/.test(s)
}