<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showOtherEdit && !showOtherDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      :url="url" />
    <!-- 编辑界面 -->
    <PurchaseClSignEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <PurchaseClSignDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <PurchaseClOtherSignDetail
      v-if="showOtherDetailPage"
      ref="otherDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideOtherEditPage" />
    <PurchaseClOtherSignEdit
      v-if="showOtherEdit"
      ref="oterEdit"
      :current-edit-row="currentEditRow"
      @hide="hideOtherEditPage"
    />
  </div>
</template>
<script>
import PurchaseClSignEdit from './modules/PurchaseClSignEdit'
import PurchaseClSignDetail from './modules/PurchaseClSignDetail'
import PurchaseClOtherSignEdit from './modules/PurchaseClOtherSignEdit'
import PurchaseClOtherSignDetail from './modules/PurchaseClOtherSignDetail'
import {ListMixin} from '@comp/template/list/ListMixin'
import {downFile, getAction, postAction, postDownFile} from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        PurchaseClSignEdit,
        PurchaseClSignDetail,
        PurchaseClOtherSignEdit,
        PurchaseClOtherSignDetail
    },
    mounted () {
        this.serachCountTabs('/contractLock/elsClContract/counts/1')
    },
    data () {
        return {
            showOtherDetailPage: false,
            tabsList: [],
            showOtherEdit: false,
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAySNRdXAo_94458881`, '业务编号/供应商编码')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'contractLock#purchaseSign:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
                        icon: 'download',
                        folded: true,
                        authorityCode: 'contractLock#purchaseSign:export',
                        clickFn: this.handleExportXls
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                form: {
                    keyWord: ''
                },
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractLock#purchaseSign:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'contractLock#purchaseSign:edit'},
                    {type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), clickFn: this.handleSend, allow: this.allowSend, authorityCode: 'contractLock#purchaseSign:send'},
                    {type: 'confirm', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVQIRL_c5fb6687`, '回传文件确认'), clickFn: this.handleConfirm, allow: this.allowConfirm, authorityCode: 'contractLock#purchaseSign:confirm'},
                    {type: 'query', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QLzEmh_5c8fbdd2`, '流程状态查询'), clickFn: this.queryFlow, allow: this.allowQueryFlow, authorityCode: 'contractLock#purchaseSign:queryFlow'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMPWOKy_b84d5f41`, '获取签署短链接'), clickFn: this.getShortUrl, allow: this.allowGetShortUrl, authorityCode: 'contractLock#purchaseSign:getShortUrl'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'contractLock#purchaseSign:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/contractLock/elsClContract/singleList/1',
                add: '/contractLock/elsClContract/add',
                delete: '/contractLock/elsClContract/delete',
                send: '/contractLock/elsClContract/send',
                queryFlow: '/contractLock/elsClContract/flowQuery',
                columns: 'ElsClSingleSignContract',
                exportXlsUrl: '/contractLock/elsClContract/exportXls'
            }
        }
    },
    methods: {

        handleExportXls (fileName, exportType, templateId, templateNumberVersion, itemGroupCode, jsonParam, defineColumnCode, exportXlsUrl){
            this.loading = true
            let _fileName =this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jWWF_36877d46`, '用印数据')
            const record = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            const records = record.filter((item)=>item.id.indexOf("row_")===-1)
            let paramEntity = !records.length
                ? Object.assign({}, this.pageData.form)
                : { selections: records.map(n => n.id)}
            let params = Object.assign({}, this.pageData.form)
            this.$set(params, 'defineColumnCode', 'contractLockExportXls' )
            let isOrder = this.pageData.isOrder || this.isOrder
            this.$set(params, 'column', isOrder.column)
            this.$set(params, 'order', isOrder.order)
            this.$set(params, 'fileName', _fileName)
            // 针对采购申请试一下exportType head_item,templateId
            this.$set(params, 'exportType', exportType)
            this.$set(params, 'templateId', templateId)
            this.$set(params, 'templateNumberVersion', templateNumberVersion)
            this.$set(params, 'itemGroupCode', itemGroupCode)
            let _this = this
            if(jsonParam){
                jsonParam.forEach(item=>{
                    _this.$set(params, item.key, item.value)
                    _this.$set(paramEntity, item.key, item.value)
                })
            }
            postDownFile(this.url.exportXlsUrl, params, paramEntity).then((data) => {
                // responseType为blob的请求，统一获取错误信息
                if (data.type === 'application/json') {
                    const fileReader = new FileReader()
                    fileReader.onloadend = () => {
                        const jsonData = JSON.parse(fileReader.result)
                        this.$message.error(jsonData.message)
                    }
                    fileReader.readAsText(data)
                    return
                }
                if (!data) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), fileName+'.xls')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jWWFsBxls_adf33aba`, '用印数据报表.xls'))
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            }).finally(() => {
                this.loading = false
            })
        },
        hideOtherEditPage (){
            this.showOtherEdit = false
            this.showOtherDetailPage = false
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
        },
        allowReturnEdit (row){
            if(row.returnSignedFile =='1' && row.reject=='0'){
                return true
            }
            return false
        },
        allowConfirm (row){
            if(row.needCheck !== '1'){
                return true
            }
            //已回传，未驳回，未确认
            if(row.returnSignedFile==='1' && row.reject!=='1' && row.returnFileConfirm!=='1'){
                return false
            }
            return true
        },
        handleOtherEdit (row){
            this.showOtherEdit = true
            this.currentEditRow = row
        },
        // tab页签改变前
        handleAfterChangeTab ({ _this, activeTabData, pageData, listGrid, tablePage }) {
            if(activeTabData.proName == 'other'){
                this.url.list = '/contractLock/elsClContract/singleList/2'
                let allColumns = listGrid.getTableColumn()
                allColumns['tableColumn'].forEach(x=>{
                    if(x.field == 'toElsAccount'){
                        x.field = 'elsAccount'
                        x.property = 'elsAccount'
                    }
                })
                pageData.optColumnList = [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleOtherView, authorityCode: 'contractLock#purchaseSign:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVPWQA_d0ab0d72`, '回传签署文档'), clickFn: this.handleOtherEdit, allow: this.allowReturnEdit, authorityCode: 'contractLock#purchaseSign:return'}
                ]
                pageData.button = [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'contractLock#purchaseSign:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ]

            }else {
                this.url.list = '/contractLock/elsClContract/singleList/1'
                pageData.button = [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'contractLock#purchaseSign:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
                        icon: 'download',
                        folded: true,
                        authorityCode: 'contractLock#purchaseSign:export',
                        clickFn: this.handleExportXls
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ]
                pageData.optColumnList = [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractLock#purchaseSign:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'contractLock#purchaseSign:edit'},
                    {type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), clickFn: this.handleSend, allow: this.allowSend, authorityCode: 'contractLock#purchaseSign:send'},
                    {type: 'confirm', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVQIRL_c5fb6687`, '回传文件确认'), clickFn: this.handleConfirm, allow: this.allowConfirm, authorityCode: 'contractLock#purchaseSign:confirm'},
                    {type: 'query', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QLzEmh_5c8fbdd2`, '流程状态查询'), clickFn: this.queryFlow, allow: this.allowQueryFlow, authorityCode: 'contractLock#purchaseSign:queryFlow'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMPWOKy_b84d5f41`, '获取签署短链接'), clickFn: this.getShortUrl, allow: this.allowGetShortUrl, authorityCode: 'contractLock#purchaseSign:getShortUrl'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'contractLock#purchaseSign:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            }
            if (activeTabData.hasOwnProperty('rejectReason') || activeTabData.hasOwnProperty('itemStatus')) {
                if (activeTabData.itemStatus === '4') {
                    pageData.button.map(btn => {
                        if (btn.value === 'confirm' || btn.value === 'reject') {
                            btn.hide = false
                        }
                    })
                } else {
                    pageData.button.map(btn => {
                        if (btn.value === 'confirm' || btn.value === 'reject') {
                            btn.hide = true
                        }
                    })
                }
            }
            if (activeTabData.appealStatus == null || activeTabData.appealStatus !== '1') {
                pageData.button.map(btn => {
                    if (btn.value === 'approval' || btn.value === 'reject') {
                        btn.hide = true
                    }
                })
            } else if (activeTabData.appealStatus === '1') {
                pageData.button.map(btn => {
                    if (btn.value === 'approval' || btn.value === 'reject') {
                        btn.hide = false
                    }
                })
            }
            if (tablePage) {
                tablePage.currentPage = 1
            }
            if (listGrid) {
                listGrid.clearCheckboxReserve()
                listGrid.clearCheckboxRow()
                _this.loadData(activeTabData)
            }
        },
        allowGetShortUrl (row){
            if(row.launch == '1' && row.documentId && row.signStatus !== '1' && row.contractStatus !== 'RECALLED'){
                return false
            }
            return true
        },
        handleAdd (){
            this.showEditPage = true
            this.currentEditRow = {}
        },
        getShortUrl (row){
            getAction('/contractLock/elsClContract/getSignPage', {id: row.id}).then(res => {
                if(res.success){
                    alert(res.result)
                }
            })
        },
        allowEdit (row){
            if(row.launch==='1'){
                return true
            }
            return false
        },
        allowDelete (row){
            if(row.launch==='1'){
                return true
            }
        },
        allowSend (row){
            //已上传未发送，签署完成
            if(row.uploaded==='1' && row.sendStatus!=='1' && row.signStatus==='2' && row.needCheck === '1'){
                return false
            }
            return true
        },
        handleSend (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'),
                content: '是否确认发送',
                onOk: function () {
                    getAction('/contractLock/elsClContract/queryById', {id:row.id}).then((res) => {
                        if (res.success) {
                            postAction(that.url.send, res.result).then(res => {
                                const type = res.success ? 'success' : 'error'
                                that.$message[type](res.message)
                                if(res.success){
                                    row.sendStatus_dictText = '是'
                                    row.sendStatus = '1'
                                }
                            })
                        }
                    })
                }
            })
        },
        handleOtherView (row){
            this.showOtherDetailPage = true
            this.currentEditRow = row
        },
        handleConfirm (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },
        allowQueryFlow (row){
            if(row.launch==='1'){
                return false
            }
            return true
        },
        queryFlow (row){
            getAction(this.url.queryFlow, {id: row.id}).then(res => {
                if(res.success){
                    this.$message.info('合同状态：'+res.message)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>