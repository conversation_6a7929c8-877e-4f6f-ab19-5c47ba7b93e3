<!--
 * @Author: fzb
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2021-08-05 16:11:40
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\purchase\modules\PurchaseEditCost.vue
-->
<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <business-layout
        ref="businessRef"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
        <template
          v-slot:eightDisciplinesThreeList="{ slotProps }"
        >
          <detail-form-layout
            v-if="threeListSlotData.groupType==='head'"
            :ref="threeListSlotData.groupCode+ 'form'"
            :busAccount="slotProps.busAccount"
            :group="threeListSlotData"
            modelLayout="masterSlave"
            :pageConfig="slotProps.pageConfig"></detail-form-layout>
        </template>
        <template
          v-slot:eightDisciplinesSixList="{ slotProps }"
        >
          <detail-form-layout
            v-if="sixListSlotData.groupType==='head'"
            :ref="sixListSlotData.groupCode+ 'form'"
            :busAccount="slotProps.busAccount"
            :group="sixListSlotData"
            modelLayout="masterSlave"
            :pageConfig="slotProps.pageConfig"></detail-form-layout>
        </template>
        <template
          v-slot:eightDisciplinesTwo="{slotProps}">
          <!-- 上传 -->
          <div style="margin-top: 10px;"></div>
          <detail-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD2ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD2ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :pageConfig="slotProps.pageConfig"
            :group="purchaseAttachmentD2ListSlotData"
            :loadData="purchaseAttachmentD2ListSlotData.loadData"></detail-grid-layout>
         
        </template>
        <template
          v-slot:eightDisciplinesFour="{slotProps}">
          <!-- 上传 -->
          <div style="margin-top: 10px;"></div>
          <detail-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD4ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD4ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :pageConfig="slotProps.pageConfig"
            :group="purchaseAttachmentD4ListSlotData"
            :loadData="purchaseAttachmentD4ListSlotData.loadData"></detail-grid-layout>
         
        </template>
        <template
          v-slot:eightDisciplinesSeven="{slotProps}">
          <!-- 上传 -->
          <div style="margin-top: 10px;"></div>
          <detail-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD7ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD7ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :pageConfig="slotProps.pageConfig"
            :group="purchaseAttachmentD7ListSlotData"
            :loadData="purchaseAttachmentD7ListSlotData.loadData"></detail-grid-layout>
         
        </template>
        <template
          v-slot:eightDisciplinesEight="{slotProps}">
          <!-- 上传 -->
          <div style="margin-top: 10px;"></div>
          <detail-grid-layout 
            :style="{height: '200px' }"
            v-if="purchaseAttachmentD8ListSlotData.groupType==='item'"
            :ref="purchaseAttachmentD8ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :pageConfig="slotProps.pageConfig"
            :group="purchaseAttachmentD8ListSlotData"
            :loadData="purchaseAttachmentD8ListSlotData.loadData"></detail-grid-layout>
         
        </template>
      </business-layout>

      <!-- <a-modal
    v-drag    
        centered
        :width="960"
        :maskClosable="false"
        :visible="flowView"
        @ok="closeFlowView"
        @cancel="closeFlowView">
        <iframe
          style="width:100%;height:560px"
          title=""
          :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
          frameborder="0"></iframe>
      </a-modal> -->
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"/>
    </a-spin>
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import DetailFormLayout from '@comp/template/business/DetailFormLayout.vue'
import DetailGridLayout from '@comp/template/business/DetailGridLayout.vue'
import {getAction, postAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'PurchaseEightDisciplinesHeadEdit',
    components: {
        flowViewModal,
        DetailFormLayout,
        DetailGridLayout,
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            requestData: {
                detail: { url: '/eightReport/purchaseEightDisciplinesPoc/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                purchaseEightDisciplinesTeamList: {add: true, delete: true},
                purchaseAttachmentD2List: [
                ]
            },
            threeListSlotData: {
            },
            sixListSlotData: {
            },
            purchaseAttachmentD2ListSlotData: {},
            purchaseAttachmentD4ListSlotData: {},
            purchaseAttachmentD7ListSlotData: {},
            purchaseAttachmentD8ListSlotData: {},
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.cancelAudit,
                    show: this.showCancelAudit
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.showFlowAudit
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                save: '/eightReport/purchaseEightDisciplinesPoc/edit',
                publish: '/eightReport/purchaseEightDisciplinesPoc/publis',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_eightDisciplines_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory1`, '附件2'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentD2List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentD4List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentD7List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentD8List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_10010011`, 'd3slot'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'eightDisciplinesThreeListSlot',
                        groupType: 'head',
                        show: false,
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_10010011`, 'd6slot'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'eightDisciplinesSixListSlot',
                        groupType: 'head',
                        show: false,
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '拟制',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk17',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'number',
                        fieldLabel: '审核',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk16',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'number',
                        fieldLabel: '批准',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk15',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '拟制',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk14',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    }, {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '审核',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk13',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    }, {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '批准',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk12',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: '关联Tab',
                        fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'select',
                        defaultValue: '',
                        dictCode: 'SRMEightAttachmentRelationTab',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {   
                        groupCode: 'purchaseAttachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120' 
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'purchaseAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'purchaseAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'purchaseAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'purchaseAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }, {
                        groupCode: 'purchaseAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData){
            let formModel = pageConfig.groups[0].formModel
            debugger
            pageConfig.groups.forEach((rs, index) => {
                if (rs.groupCode == 'eightDisciplinesThreeListSlot') {
                    rs.show = false
                    rs.verify = true
                    this.threeListSlotData = rs
                }
                if (rs.groupCode == 'eightDisciplinesSixListSlot') {
                    rs.show = false
                    rs.verify = true
                    this.sixListSlotData = rs
                }
                if (rs.groupCode == 'purchaseAttachmentD2List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD2ListSlotData = rs
                }
                if (rs.groupCode == 'purchaseAttachmentD4List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD4ListSlotData = rs
                }
                if (rs.groupCode == 'purchaseAttachmentD7List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD7ListSlotData = rs
                }
                if (rs.groupCode == 'purchaseAttachmentD8List') {
                    rs.show = false
                    rs.verify = true
                    this.purchaseAttachmentD8ListSlotData = rs
                }

            })
            for(let key in resultData){
                formModel[key] = resultData[key]
            }
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        showFlow ({ Vue, pageConfig, btn, groupCode }){
            this.flowId = pageConfig.groups[0].formModel.flowId
            this.flowView = true
        },
        cancelAudit ({ Vue, pageConfig, btn, groupCode }) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit, pageConfig.groups[0].formModel)
                }
            })
        },
        closeFlowView (){
            this.flowView = false
        },
        showCancelAudit ({ pageConfig }){
        //审批中的状态才显示
            if(pageConfig.groups[0].formModel.auditStatus=='1'){
                return true
            }
            return false
        },
        showFlowAudit ({ pageConfig }){
        //审批中的状态才显示
            if(pageConfig.groups[0].formModel.auditStatus=='1'){
                return true
            }
            return false
        },
        postAuditData (invokeUrl, formData){
            const that = this
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'eightDisciplines'
            param['auditSubject'] = '8D单号：'+formData.eightDisciplinesNumber
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    this.$parent.cancelAuditCallBack(formData)
                }else {
                    this.$message.warning(res.message)
                }
            })

        }
    }
}
</script>