<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="show"
        :ref="businessRefName"
        :currentEditRow="currentEditRowData"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="tab"
        :fromSourceData="fromSourceData"
        :pageStatus="pageStatus"
        v-on="businessHandler"> </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { baseMixins } from '../../plugins/baseMixins.js'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
export default {
    name: 'PurchaseTenderProjectJury',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'resetCurrentSubPackage'],
    mixins: [businessUtilMixin, baseMixins],
    props: {
        currentEditRow: {
            default: () => {},
            type: Object
        }
    },
    data () {
        return {
            fromSourceData: {},
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            show: false,
            externalToolBar: {
                tenderAdmin: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.tenderAdminAdd,
                        attrs: {
                            type: 'primary'
                        },
                        show: this.showBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete,
                        show: this.showBtn
                    }
                ],
                evaluationExperts: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.evaluationExpertsAdd,
                        attrs: {
                            type: 'primary'
                        },
                        show: this.showBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete,
                        show: this.showBtn
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.saveEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.publishEvent
                }
            ],
            currentGroupCode: {},
            url: {
                detail: '/tender/jury/purchaseTenderProjectJuryHead/queryByCondition',
                add: '/tender/jury/purchaseTenderProjectJuryHead/add',
                edit: '/tender/jury/purchaseTenderProjectJuryHead/edit',
                publish: '/tender/jury/purchaseTenderProjectJuryHead/publish',
                juryChange: '/tender/jury/purchaseTenderProjectJuryHead/change'
            },
            remoteJsFilePath: '',
            currentEditRowData: {}
        }
    },
    computed: {
        pageStatus () {
            let status = this.fromSourceData.headStatus == '1' ? 'detail' : 'edit'
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') status = 'detail'
            return status
        },
        subId () {
            return this.subpackageId()
        },
        pageHeaderButtons () {
            const { status } = this.currentSubPackage()
            const notStatusList = [2310, 2320, 2330, 4110, 4120, 4130, 4310, 4320, 4330, 4510, 4520, 4530]
            const statusList = [2320, 4520, 4120, 4320]
            if (this.fromSourceData.headStatus == '1' && (statusList.includes(status) || !notStatusList.includes(status))) {
                return [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ULMAH_db95ee26`, '评委会变更'),
                        attrs: {
                            type: 'primary'
                        },
                        key: 'jury',
                        click: this.juryChange
                    }
                ]
            }
            return []
        }
    },
    methods: {
        // 评委会变更
        juryChange () {
            this.confirmLoading = true
            const params = {
                id: this.fromSourceData.id
            }
            postAction(this.url.juryChange, params)
                .then((res) => {
                    const resType = res.success ? 'success' : 'error'
                    this.$message[resType](res.message)
                    if (res.code == 200) {
                        // this.$emit('resetCurrentSubPackage') || ''
                        this.resetCurrentSubPackage()
                        this.init()
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        showBtn () {
            return !this.fromSourceData.headStatus || this.fromSourceData.headStatus == '0'
        },
        saveEvent () {
            let url = this.fromSourceData.id ? this.url.edit : this.url.add
            const allData = this.getAllData()
            let params = Object.assign({}, allData)
            params.subpackageId = this.subId
            params.tenderProjectId = this.tenderCurrentRow.id
            params.juryType = 1
            params.purchaseTenderProjectJuryMemberList = [...params.tenderAdmin, ...params.evaluationExperts]
            params.templateNumber = params.templateNumber || this.currentEditRowData.templateNumber || ''
            params.templateVersion = params.templateVersion || this.currentEditRowData.templateVersion || ''
            params.templateAccount = params.templateAccount || this.currentEditRowData.templateAccount || ''
            params.templateName = params.templateName || this.currentEditRowData.templateName || ''
            this.confirmLoading = true
            postAction(url, params)
                .then((res) => {
                    const type = res.success ? 'success' : 'error'
                    if (res.success) {
                        // 刷新
                        this.init()
                    }
                    this.$message[type](res.message)
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },

        publishEvent () {
            const allData = this.getAllData()
            allData.subpackageId = this.subId
            allData.tenderProjectId = this.tenderCurrentRow.id
            allData.juryType = 1
            allData.purchaseTenderProjectJuryMemberList = [...allData.tenderAdmin, ...allData.evaluationExperts]
            this.confirmLoading = true
            // postAction(this.url.publish, allData).then(res => {
            //     const type = res.success ? 'success' : 'error'
            //     if (res.success) {
            //         // 刷新
            postAction(this.url.publish, allData)
                .then((res) => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    // 刷新
                    if (type === 'success') {
                        // this.$emit('resetCurrentSubPackage') || ''
                        this.resetCurrentSubPackage()
                        this.init()
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
            //     } else {
            //         this.$message[type](res.message)
            //     }
            // }).finally(() => {
            //     this.confirmLoading = false
            // })
        },

        tenderAdminAdd ({ pageConfig, groupCode }) {
            // 获取编辑的是哪个组
            this.currentGroupCode = groupCode
            let url = 'account/elsSubAccount/list'
            let columns = [
                { field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '名称') },
                { field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ltyo_2e3c9979`, '手机号') },
                { field: 'certificateType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型') },
                { field: 'certificateNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号') },
                { field: 'workUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_workUnit`, '工作单位') }
            ]
            this.$refs.fieldSelectModal.open(url, { headId: this.fromSourceData.id }, columns, 'multiple')
        },
        evaluationExpertsAdd ({ pageConfig, groupCode }) {
            // 获取编辑的是哪个组
            this.currentGroupCode = groupCode
            let url = 'specialist/specialistInfo/list'
            let columns = [
                { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '名称'), width: 70 },
                { field: 'mobileTelephone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ltyo_2e3c9979`, '手机号'), width: 120 },
                { field: 'workUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_workUnit`, '工作单位') },
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n__WWWey_401851d`, 'ELS账号') },
                { field: 'elsSubAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号') },
                { field: 'specialistClasses_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suAq_24e54053`, '专家类别'), width: 180 }
            ]
            this.$refs.fieldSelectModal.open(url, { headId: this.fromSourceData.id }, columns, 'multiple')
        },
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef(this.currentGroupCode)
            if (this.currentGroupCode == 'tenderAdmin') {
                let userList = data.map((item) => {
                    item.memberType = '0'
                    delete item.id
                    return {
                        ...item,
                        elsAccount: item.elsAccount,
                        elsSubAccount: item.subAccount,
                        elsRealname: item.realname
                    }
                })
                itemGrid.insertAt(userList, -1)
            } else {
                let userList = data.map((item) => {
                    item.memberType = '1'
                    delete item.id
                    return {
                        ...item,
                        elsAccount: item.elsAccount,
                        elsSubAccount: item.subAccount,
                        elsRealname: item.name,
                        certificateType: item.certificateType,
                        phone: item.mobileTelephone
                    }
                })
                itemGrid.insertAt(userList, -1)
            }
        },
        init () {
            this.confirmLoading = true
            this.show = false
            getAction(this.url.detail, { subpackageId: this.subId, juryType: '1' })
                .then(async (res) => {
                    if (res.success) {
                        this.fromSourceData = res.result || {}
                        if (res.result) {
                            // 类型为0的就是代表  类型为1的就是专家；筛选出来后放到对应表
                            this.fromSourceData.tenderAdmin = res.result.purchaseTenderProjectJuryMemberList.filter((item) => item.memberType == '0')
                            this.fromSourceData.evaluationExperts = res.result.purchaseTenderProjectJuryMemberList.filter((item) => item.memberType == '1')
                        } else {
                            this.fromSourceData.status = '0'
                        }
                        if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') this.externalToolBar = {}
                    } else {
                        this.$message.warning(res.message)
                    }
                    this.currentEditRowData = res.result && res.result.templateNumber && res.result.templateAccount ? {
                        templateNumber: res.result.templateNumber,
                        templateName: res.result.templateName,
                        templateVersion: res.result.templateVersion,
                        templateAccount: res.result.templateAccount
                    } : await this.getBusinessTemplate('tenderJury')
                    this.remoteJsFilePath = `${this.currentEditRowData['templateAccount']}/purchase_tenderJury_${this.currentEditRowData['templateNumber']}_${this.currentEditRowData['templateVersion']}`
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.show = true
                })
        }
    },
    async mounted () {
        this.init()
    }
}
</script>
<style lang="less" scoped>
:deep(.page-container .edit-page .page-content){
    flex: auto;
}
</style>
