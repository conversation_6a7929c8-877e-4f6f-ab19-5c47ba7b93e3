<template>
  <div >
    <vxe-grid
      v-if="this.currentRow"
      :span-method="mergeRowMethod"
      height="200"
      v-bind="gridConfig"
      ref="customizeFieldData"
      :data="currentRow.customizeFieldData"
      :edit-rules="editRules"
      :columns="currentRow.customizeFieldModel"
      show-overflow="title" >
      <template #grid_opration="{ row, column, rowIndex }">
        <div v-if="optColumnList && isEdit">
          <span
            v-for="(opt, optIndex) in optColumnList"
            :key="'opt_'+ row.id + '_' + optIndex">
            <a
              :title="opt.title"
              style="margin:0 4px"
              :disabled="typeof opt.disabled=== 'function'? opt.disabled(row) : opt.disabled"
              v-show="!opt.hide"
              @click="()=> { optColumnFuntion(opt, row, column, rowIndex) }">{{ opt.title }}</a>
          </span>
        </div>
      </template>
    </vxe-grid>
    <addColumnModal
      ref="addColumnModal"
      isEmit
      @ok="handleAddTableColumn"></addColumnModal>
  </div>
</template>
<script lang="jsx">
import {ajaxFindDictItems} from '@/api/api'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import addColumnModal from './addColumnModal'
export default {
    mixins: [tableMixins],
    components: {
        addColumnModal
    },
    props: {
        currentRow: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: '',
            type: String
        }
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        }
    },
    data () {
        return {
            optColumnList: [
                {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.handleTableDel}
            ]
        }
    },
    watch: {
        currentRow (value) {
            this.initColumns(value)
        }
    },
    methods: {
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            // const fields = ['supplierAccount']
            const fields = ['supplierName']
            const cellValue = row['supplierName']
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow['supplierName'] === cellValue) {
                    return { rowspan: 0, colspan: 0 }
                } else {
                    let countRowspan = 1
                    while (nextRow && nextRow['supplierName'] === cellValue) {
                        nextRow = visibleData[++countRowspan + _rowIndex]
                    }
                    if (countRowspan > 1) {
                        console.log(row, _rowIndex, column, visibleData, countRowspan)
                        return { rowspan: countRowspan, colspan: 1 }
                    }
                }
            }
        },
        // 获取表格下拉字典
        queryDictData (column) {
            const that = this
            if (column && column.dictCode) {
                let postData = {
                    busAccount: that.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: column.dictCode
                }
                ajaxFindDictItems(postData).then(res => {
                    if(res.success) {
                        let options = res.result.map((dictItem) => {
                            return {
                                value: dictItem.value,
                                label: dictItem.text,
                                title: dictItem.title
                            }
                        })
                        if (column.editRender) {
                            column.editRender.options = options
                        }
                        // dictOptions初始化数据字典的字段会用到
                        column.dictOptions= options
                        that.$forceUpdate()
                    } else {
                        if (column.editRender) {
                            column.editRender.options = []
                        }
                        // dictOptions初始化数据字典的字段会用到
                        column.dictOptions= []
                        that.$forceUpdate()
                    }
                })
            } else {
                if (column.editRender) {
                    column.editRender.options = []
                }
                // dictOptions初始化数据字典的字段会用到
                column.dictOptions= []
                that.$forceUpdate()
            }
        },
        // 添加行
        handleTableAdd () {
            if (!this.currentRow) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFYBx_dbe46160`, '请先选择招标函'))
            this.currentRow.customizeFieldData.push({})
            this.$refs.customizeFieldData.loadData(this.currentRow.customizeFieldData)
        },
        // 删除行
        handleTableDel (vue, row, col, index) {
            this.currentRow.customizeFieldData.splice(index, 1)
            this.$refs.customizeFieldData.loadData(this.currentRow.customizeFieldData)
        },
        // 选择列弹窗
        handleSelectTableColumn () {
            if (!this.currentRow) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFYBx_dbe46160`, '请先选择招标函'))
            let url = 'tender/tenderCustomColumn/list'
            let columns = [
                { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ARL_13ecfda`, '列名称') },
                { field: 'fieldCategory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AzA_13e938c`, '列分类') },
                { field: 'fieldType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOAc_2b290e2e`, '字段类型') },
                { field: 'dictCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFJCAo_bc817d6a`, '数据字典编码') },
                { field: 'must_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lS_bf146`, '必填') },
                { field: 'inputOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMC_15630eb`, '填写方') }
            ]
            this.$refs.addColumnModal.open(url, {}, columns, 'multiple')
        },
        // 列处理
        columnSerialization (item) {
            // 1招标单位, 0投标单位
            let canEdit = item.inputOrg == '0'
            switch(item.fieldType) {
            case 'string':
                item.editRender = { enabled: canEdit, name: '$input' }
                break
            case 'dict':
                item.editRender = { enabled: canEdit, name: 'srmSelect', options: this.queryDictData(item)}
                break
            case 'date':
                item.editRender = { enabled: canEdit, name: 'mDatePicker' }
                break
            case 'number':
                item.editRender = { enabled: canEdit, name: '$input', props: {type: 'number'} }
                break
            }
            let itemColumn = {
                'title': item.name || item.title,
                'field': item.columnFieldName || item.field,
                'fieldType': item.fieldType,
                'fieldCategory': item.fieldCategory,
                'required': item.must == '1' || item.must == true ? true : false,
                'must': item.must == '1' || item.must == true ? true : false,
                'inputOrg': item.inputOrg,
                width: 160,
                slots: {
                    header: ({columnIndex, column}) => {
                        return [
                            <span >{item.name || item.title}<a-icon type="delete" onClick={() => this.handleDeleteColumn(columnIndex, column)}/></span>
                        ]
                    }
                },
                editRender: item.editRender
            }
            if (item.dictCode) {
                itemColumn.dictCode = item.dictCode
            }
            return itemColumn
        },
        // 添加列
        handleAddTableColumn (data) {
            data.map(item => {
                let column = this.columnSerialization(item)
                let index = this.currentRow.customizeFieldModel.length - 1
                this.currentRow.customizeFieldModel.splice(index, 0, column)
            })
            this.$refs.customizeFieldData.loadColumn(this.currentRow.customizeFieldModel)
        },
        handleDeleteColumn (i, col) {
            if (!this.isEdit) return
            this.currentRow.customizeFieldModel.splice(i, 1)
            this.$refs.customizeFieldData.loadColumn(this.currentRow.customizeFieldModel)
        },
        // 操作列方法
        optColumnFuntion (opt, row, col, index) {
            opt.click && opt.click(this, row, col, index)
        },
        handleDelete (vue, row) {
            this.$refs.customizeFieldData.remove(row)
        },
        // 格式化列
        initColumns (data) {
            if (!data.customizeFieldModel) {
                let custom = [
                    {
                        type: 'seq',
                        width: 50,
                        key: 'seq',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                    },
                    {
                        slots: { default: 'grid_opration' },
                        width: 50,
                        key: 'grid_opration',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作')
                    }
                ]
                this.$set(data, 'customizeFieldModel', custom)
            } else {
                let flag = false
                data.customizeFieldModel.map(item => {
                // 存在操作列
                    if (item.key == 'grid_opration') {
                        flag = true
                    }
                })
                // 不存在操作列的情况下
                if (!flag) {
                    let columnlsit = data.customizeFieldModel.map(item => {
                        return this.columnSerialization(item)
                    })
                    columnlsit.unshift(
                        {
                            type: 'seq',
                            width: 50,
                            key: 'seq',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                        }
                    )
                    columnlsit.push(
                        {
                            slots: { default: 'grid_opration' },
                            width: 50,
                            key: 'grid_opration',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作')
                        }
                    )
                    console.log(columnlsit)
                    this.$set(data, 'customizeFieldModel', columnlsit)
                }
            }
        }
    }
}
</script>
<style lang="less" scoped>

</style>


