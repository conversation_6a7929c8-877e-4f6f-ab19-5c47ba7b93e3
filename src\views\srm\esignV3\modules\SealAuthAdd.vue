<template>
  <div>
    <a-drawer
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_zRSujRlb_dafb8183`, '批量添加员工授权')"
      placement="right"
      :closable="true"
      :visible="visible"
      :width="860"
      @close="onClose"
    >
      <a-form-model
        :label-col="labelCol"
        ref="form"
        :wrapper-col="wrapperCol"
        :rules="rules"
        :model="formData">
        <a-row :getterr="24">
          <a-col :span="10">
            <a-form-model-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_field_RIWeKy_2e589443`, '指定印章角色')"
              prop="sealRole"
              required>
              <a-select
                style="width: 135px"
                v-model="formData.sealRole"
              >
                <template v-for="i in sealRoleCodeOption">
                  <a-select-option :value="i.value">
                    {{ i.label }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_field_iFLj_42f90848`, '选择成员')"
              prop="psnName"
              required>
              <a-input
                readOnly
                v-model="formData.psnName"
                @click="openSelectModal">
                <a-icon
                  slot="suffix"
                  type="close-circle"
                  @click="() => clearInputValue(item)"></a-icon>
              </a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :getterr="24">
          <a-col :span="10">
            <a-form-model-item
              :label="$srmI18n(`${this.$getLangAccount()}#i18n_field_lbbXKI_bdc443c2`, '授权生效时间')"
              prop="effectiveTime"
              required>
              <a-date-picker
                v-model="formData.effectiveTime"
                :show-time="true"
                valueFormat="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_field_lbKXKI_b1120e70`, '授权失效时间')"
              prop="expireTime"
              required>
              <a-date-picker
                v-model="formData.expireTime"
                :show-time="true"
                valueFormat="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-model-item>
          </a-col>
        </a-row>

      </a-form-model>
      <div
        :style="{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          zIndex: 1,
        }"
      >
        <a-button
          :style="{ marginRight: '8px' }"
          @click="onClose">
          {{$srmI18n(`${this.$getLangAccount()}#i18n_title_cancle`, '取消')}}
        </a-button>
        <a-button
          type="primary"
          @click="submit">
          {{$srmI18n(`${this.$getLangAccount()}#i18n_title_saveAndSumbit`, '提交')}}
        </a-button>
      </div>
    </a-drawer>
    <field-select-modal
      ref="fieldSelectModal"/>
  </div>
</template>
<script>
import {getAction, postAction} from '@/api/manage'
import {TimePicker} from 'ant-design-vue'
import fieldSelectModal from '@comp/template/fieldSelectModal'

export default {
    props: {
        roleType: {
            type: String,
            default: '0'
        },
        title: {
            type: String,
            default: ''
        },
        row: {
            type: Object,
            default: () => {
                return {id: ''}
            }
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    components: {
        fieldSelectModal,
        ATimePicker: TimePicker
    },
    data () {
        return {
            sealRoleCodeOption: [
                {value: 'SEAL_USER', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeKjj_d0dd247f`, '印章使用员')},
                {value: 'SEAL_EXAMINER', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeUzj_d1086cf0`, '印章审批员') }
            ],
            tenderBidTetterVoList: [],
            quoteColumnNameOptions: [],
            getTenderBidTetterLoading: false,
            formData: {psnName: '', psnId: ''},
            rules: {
                sealRole: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWeKy_6f237707`, '请选择印章角色')
                }],
                psnName: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWelbLj_22603caa`, '请选择印章授权成员')
                }],
                effectiveTime: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMlbbXKI_3cf44cc7`, '请填写授权生效时间')
                }],
                expireTime: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMlbKXKI_30421775`, '请填写授权失效时间')
                }]
            },
            labelCol: {
                span: 12
            },
            wrapperCol: {
                span: 12
            },
            colSapn: 8,
            sTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbRv_2ed19e80`, ':授权管理'),
            dataSource: [],
            dataSourceData: [],
            gridOptions: {
                border: false,
                size: 'mini',
                resizable: true,
                showOverflow: true,
                align: 'center',
                toolbarConfig: {
                    slots: {
                        // 自定义工具栏模板
                        buttons: 'toolbar_buttons'
                    }
                },
                editConfig: {
                    trigger: 'click',
                    mode: 'cell',
                    showStatus: true,
                    activeMethod: this.activeRowMethod
                },
                columns: [
                    {type: 'seq', width: 50},
                    {
                        field: 'psnName',
                        width: 150,
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'),
                        showOverflow: true
                    },
                    {
                        field: 'subAccount',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRWWWey_f8d6ad72`, '员工SRM账号'),
                        showOverflow: true
                    },
                    {
                        field: 'effectiveTime',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbbXKI_bdc443c2`, '授权生效时间'),
                        showOverflow: true
                    },
                    {
                        field: 'expireTime',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbKXKI_b1120e70`, '授权失效时间'),
                        showOverflow: true
                    }
                ],
                data: []
            },
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 20
            },
            validRules: {},
            roleTypeOpt: [
                {value: '2', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_WesRj_d0df4e04`, '印章保管员')},
                {value: '3', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_LjRvj_8e167f2b`, '成员管理员')}
            ]
        }
    },
    mounted () {
        //this.getData()
    },
    methods: {
        async submit () {
            const errMap = await this.$refs.form.validate(this.formData).catch(errMap => errMap)
            if(errMap){
                console.log('row:', this.row)
                const param = {
                    sealId: this.row.sealId,
                    roleType: this.roleType,
                    orgId: this.row.orgId,
                    orgName: this.row.orgName,
                    sealType: this.row.sealType,
                    sealName: this.row.sealName,
                    ...this.formData
                }
                postAction('/esignv3/esignV3SealsAuth/saveAuth', param).then(res => {
                    if (res.success) {
                        this.visible = false
                        window.open(res.message, '_blank')
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_savaSuccess`, '保存成功'))
                    } else {
                        this.$message.error(res.message)
                    }
                })
            }
        },
        fieldSelectOk (data) {
            this.formData.psnId = data.map(r => r.psnCode).join(',')
            this.formData.psnName = data.map(r => r.psnName).join(',')
            console.log('this.formData:', this.formData)
        },
        openSelectModal () {
            const orgId = this.row.orgId
            const sealId = this.row.sealId
            let param = {orgId, sealId}
            let dalColumn = [
                {
                    field: 'subAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'),
                    with: 150
                },
                {
                    field: 'psnName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'),
                    with: 150
                },
                {field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPsmLjDeyBK_b93e3b33`, 'E签宝个人用户账号标识'), with: 150},
                {field: 'role_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_role`, '角色'), with: 150}]
            this.$refs.fieldSelectModal.open(this.roleType == '0'? '/esignv3/purchaseEsignV3OrgPsn/notAdminList':
                '/esignv3/saleEsignV3OrgPsn/notAdminList', param, dalColumn, 'multiple')
        },
        onClose () {
            this.$emit('close', false)
        },
        activeRowMethod ({row, rowIndex}) {
            if (row.id) {
                return false
            }
            return true
        },
        formatRoleType (val) {
            console.log('val', val)
            if (!val) {
                return
            }
            let label = []
            this.roleTypeOpt.forEach((item) => {
                val.forEach((v) => {
                    if (item.value == v) {
                        label.push(item.label)
                    }
                })

            })
            return label.join(',')
        },
        // 员工搜索
        applyUserNameOnSearch (val) {
            this.dataSource = []
            this.dataSourceData = []
            let param = {name: val}
            this.dataSource = []
            getAction('/esignv3/purchaseEsignV3Personal/queryList', param).then((res) => {
                if (res && res.success) {
                    res.result.forEach((item) => {
                        if (item.psnName) {
                            this.dataSource.push(item.psnName + ',' + item.psnAccount)
                            this.dataSourceData.push(item)
                        }
                    })
                }
            })
        },
        // 员工选择
        applyUserNameOnSelect (row, psn) {
            console.log('id:', psn)
            let account = psn.split(',')[1]
            const item = this.dataSourceData.filter(item => {
                return item.psnAccount == account
            })
            console.log('item', item)
            if (item) {
                row.psnName = item[0].psnName
                row.psnAccount = item[0].psnAccount
                row.subAccount = item[0].subAccount
            }
        },
        getData () {
            let param = {orgId: this.row.id}
            getAction('/esignv3/purchaseEsignV3OrgPsn/list', param).then((res) => {
                if (res && res.success) {
                    this.gridOptions.data = res.result.records
                    this.tablePage.total = res.result.total
                } else {
                    this.$message.error(res.message)
                }
            })
        },
        handlePageChange ({currentPage, pageSize}) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            this.getData()
        },
        add () {

        },
        del (row) {
            if (row && row.id) {
                postAction('/contractLock/purchaseCLCompanyInfo/removeStaff', row).then((res) => {
                    if (res && res.success) {
                        this.$message.success(res.message)
                        this.$refs.SealAuthGridRef.remove(row)
                    } else {
                        this.$message.error(res.message)
                    }
                })
            } else {
                this.$refs.SealAuthGridRef.remove(row)
            }
        },
        async save (row) {
            // 插入一条数据并触发校验
            const errMap = await this.$refs.sealAuthGridRef.validate(row).catch(errMap => errMap)
            if (!errMap) {
                if (row.userId) {
                    row.id = row.userId
                }
                row.companyId = this.row.companyId
                postAction('/contractLock/purchaseCLCompanyInfo/addStaff', row).then((res) => {
                    if (res && res.success) {
                        this.$message.success(res.message)
                        this.getData()
                    } else {
                        if (res.message == '网络异常，请检查网络连接') {
                            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAQLAEsLHcAZdROlbWVWeRSMlbKyyVHcROlbKHcROQLtk_54c76816`, '发起流程企业还未进行契约锁功能授权，请先通过获取授权链接接口进行功能授权在进行功能流程操作'))
                        } else {
                            this.$message.error(res.message)
                        }
                    }
                })
            }
        }
    }
}
</script>