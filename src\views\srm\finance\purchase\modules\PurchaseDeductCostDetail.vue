<template>
  <div class="page-container">
    <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :remoteJsFilePath="remoteJsFilePath"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="detail"
        v-on="businessHandler"
      >
      </business-layout>
    <!-- 加载配置文件 -->
    <RelationGraphModal
      v-if="modalVisibleDocket && currentEditRow.documentId"
      :modalVisibleDocket="modalVisibleDocket"
      :id="currentEditRow.documentId"
      :rootId="currentEditRow.id"
      @closeModalDocket="closeModalDocket"
      @onNodeClick="clickNode"
    />
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
import RelationGraphModal from '@comp/RelationGraphModal'
export default {
    name: 'PurchaseDeductCostDetail',
    mixins: [businessUtilMixin],
    components: {
        flowViewModal,
        RelationGraphModal,
        BusinessLayout
    },
    data () {
        return {
            modalVisibleDocket: false,
            showRemote: false,
             businessRefName: 'businessRef',
                requestData: {
                    detail: {
                        url: '/finance/purchaseDeductCost/queryById',
                        args: (that) => {
                            return { id: that.currentEditRow.id }
                        }
                    }
                },
            pageHeaderButtons:[
                { 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                        },
                    click: this.cancelAudit,
                    id: 'cancelAudit',
                    show: this.showCancelConditionBtn
                },
                { 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tFKm_2766a28a`, '单据联查'),
                    show: this.showDocket,
                    click: this.viewDocket 
                },
                { 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    click: this.showFlow
                },
                {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                        key: 'goBack'
                }
            ],
            url: {
                detail: '/finance/purchaseDeductCost/queryById',
                public: '/finance/purchaseDeductCost/publish',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            },
            flowView: false,
            flowId: ''
        }
    },
    computed: {
        remoteJsFilePath() {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${elsAccount}/purchase_deductCost_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        // 如果是外部的参数，先请求获取模板js必须的参数
        if (this.$route.query && this.$route.query.open  && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
         handleBeforeRemoteConfigData(){
            return {
                    groups: [
                        {
                            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                            groupNameI18nKey: '',
                            groupCode: 'purchaseAttachmentList',
                            groupType: 'item',
                            sortOrder: '6',
                            extend: {
                                optColumnList: [
                                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent  },
                                    { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                ]
                            }
                        }
                    ],
                    formFields: [],
                    itemColumns: [
                        { 
                            groupCode: 'purchaseAttachmentList',
                            field: 'fileType_dictText',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                            fieldLabelI18nKey: '',
                            width: 200
                        },
                        { 
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                            fieldLabelI18nKey: '',
                            field: 'fileName',
                            width: 180
                            
                        },
                        { 
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                            fieldLabelI18nKey: '',
                            field: 'uploadTime',
                            width: 120
                        },
                        { 
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                            fieldLabelI18nKey: '',
                            field: 'uploadElsAccount_dictText',
                            width: 120
                        },
                        {
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                            fieldLabelI18nKey: '',
                            field: 'uploadSubAccount_dictText',
                            width: 120
                        },
                        {
                            groupCode: 'purchaseAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                            fieldLabelI18nKey: '',
                            field: 'grid_opration',
                            width: '100',
                            slots: { default: 'grid_opration' }
                        }
                    ]
                }
        },
        // 单据联查
        clickNode (){
            this.$store.dispatch('SetTabConfirm', false)
            this.modalVisibleDocket = false
        },
        closeModalDocket (){
            this.modalVisibleDocket = false
        },
        // 是否显示单据联查
        showDocket () {
            return this.currentEditRow.documentId
        },
        // 单据联查
        viewDocket () {
            this.modalVisibleDocket = true
        },
        showFlow (){
            let params = this.getAllData() || {}
            this.flowId = params.flowId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        preViewEvent (Vue,row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        goBack () {
            this.$emit('hide')
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            this.attachmentDownloadEvent(row)
        },
        showCancelConditionBtn () {
            let params =  this.getAllData() || {}
            let auditStatus = params.auditStatus
            if (auditStatus == '1') {
                return true
            } else {
                return false
            }
        },
        cancelAudit () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.auditPostData(that.url.cancelAudit)
                }
            })
        },
        auditPostData (invokeUrl) {
            this.confirmLoading = true
            let formData = this.getAllData()
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'deductCost'
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param, 'post')
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.$parent.cancelCallBack(formData)
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.$refs[this.businessRefName] && this.$refs[this.businessRefName].queryDetail()
                    this.confirmLoading = false
                })
        },
    }
}
</script>
