<template>
  <div class="calendar_overview">
    <a-button 
      class=""
      @click="$router.go(-1)"
      type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
    <FullCalendar
      ref="calendar"
      :options="calendarOptions" />
  </div>
</template>
<script>
import FullCalendar from '@fullcalendar/vue'
import dayGridPlugin from '@fullcalendar/daygrid'
import resourceTimelinePlugin  from '@fullcalendar/resource-timeline'
import interactionPlugin from '@fullcalendar/interaction'
import setDialog from './modules/setDialog'
import { getAction, postAction } from '@api/manage'
import { DEFAULT_LANG } from '@/store/mutation-types'

export default {
    components: {
        FullCalendar,
        setDialog
    },
    data () {
        return { 
            calendarOptions: {
                locale: this.$ls.get(DEFAULT_LANG)==='en' ? 'en-us' : 'zh-cn', // 切换语言，当前为中文
                plugins: [
                    resourceTimelinePlugin,
                    interactionPlugin,
                    dayGridPlugin
                ],
                timeZone: 'UTC',
                weekNumberCalculation: 'ISO',
                initialView: 'resourceTimelineYear',
                dayHeaderFormat: { weekday: 'long', month: 'numeric', day: 'numeric', omitCommas: true },
                resourceAreaWidth: '15%',
                scrollTime: '08:00',
                height: '95%', // 当前日历高度
                aspectRatio: 1.5,
                views: {
                    resourceTimelineYear: {
                        // slotLabelFormat: [
                        //     { month: 'long'}, 
                        //     { days: 'numeric'},
                        //     { weekday: 'long'} 
                        // ]
                    }

                },
                editable: true,
                resourceAreaHeaderContent: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                resources: [],
                events: [
                    // { title: 'event 1', date: '2021-04-30', color: '#1890ff', textColor: 'white', resourceId: 'a1' },
                    // { title: 'event 2', date: '2021-04-31' },
                    // { title: 'event 3', date: '2021-05-06', allDay: false  }
                ]
            },
            curDayInfo: '',
            yearStart: '2021-01-01',
            yearEnd: '2021-12-30'
            
        }
    },
    computed: {
        langAccount () {
            return this.$getLangAccount()
        }
    },
    methods: {
        init () {
            this.getFactory()
        },
        getCalendar () {
            let url = 'calendar/purchaseFactoryCalendar/getCalendar'
            return getAction(url, {elsAccount: this.$ls.get('Login_elsAccount')})
        },
        async getFactory () {
            const account = this.$getLangAccount()
            const {$srmI18n} = this

            let url = 'calendar/purchaseFactoryCalendar/getFactory'
            await getAction(url, {elsAccount: this.$ls.get('Login_elsAccount')}).then((res) => {
                if(res.success) {
                    // let hasSetFac = res.result.filter(rs => rs.id) // 改工厂已经设置休息日
                    this.calendarOptions.resources = res.result.map((rs, idx) => {
                        return {
                            id: rs.id + idx,
                            title: rs.orgName,
                            children: [
                                {
                                    // id: rs.id,
                                    id: rs.orgCode,
                                    title: $srmI18n(`${account}#i18n_title_restday`, '休息日'),
                                    eventColor: 'green'
                                }
                            ]
                        }
                    })
                    console.log(this.calendarOptions.resources)
                } else {
                    this.$message.warning(res.message)
                }
            })
            let { success, result, message } = await this.getCalendar()
            if (success) {
                // let day = result.find(rs => rs.id == this.curFac)
                console.log(result)
                result.forEach(s => {
                    console.log(s)
                    let arr = s.dayOffList.map(el => {
                        return  {
                            date: el,
                            title: $srmI18n(`${account}#i18n_title_restday`, '休息日'),
                            textColor: '#ffffff',
                            // display: 'background',
                            color: '#1890ff',
                            // resourceId: s.id,
                            resourceId: s.factory
                        }  
                    })
                    this.calendarOptions.events = [...this.calendarOptions.events, ...arr]
                })
                console.log(this.calendarOptions.events)
            } else {
                this.$message.warning(message)
            }
        }
    },
    mounted () {
        this.init()
    }
}
</script>
<style lang="less">
    .calendar_overview{
        padding: 20px;
        max-width: 1800px;
        background: white;
        height: 100%;
        .fc-direction-ltr{
            max-height: 600px;
            overflow-y: auto;
        }
        .fc-license-message{
            display: none !important;
        }
    }
</style>