<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      refresh
      :page-data="pageData"
      :currentEditRow="currentEditRow"
      :url="url"
    />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
  name: 'PurchaseDeliveryNoticeModal',
  components: {
    fieldSelectModal
  },
  mixins: [EditMixin],
  props: {
    currentEditRow: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      showRemote: false,
      pageData: {
        selectedType: '',
        form: {},
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eRcVH_2730e70f`, '通知行信息'),
            groupType: 'item',
            groupCode: 'purchaseDeliveryNoticeList',
            type: 'grid',
            custom: {
              ref: 'purchaseDeliveryNoticeList',
              columns: [],
              buttons: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.insertGridItem },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteGridItem },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem }
              ]
            }
          }
        ],
        formFields: [],
        publicBtn: [
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent, authorityCode: 'deliveryNoticeByOrder#purchaseDeliveryNotice:edit' },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publishEvent, showCondition: this.showPublishConditionBtn, authorityCode: 'order#purchaseDeliveryNoticeHead:publish' },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
        ]
      },
      url: {
        add: '/delivery/purchaseDeliveryNoticeOrderHead/add',
        edit: '/delivery/purchaseDeliveryNoticeOrderHead/edit',
        detail: '/delivery/purchaseDeliveryNoticeOrderHead/queryById',
        public: '/delivery/purchaseDeliveryNoticeOrderHead/publish'
      }
    }
  },
  computed: {
    fileSrc() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_purchaseDeliveryNoticeByOrder_${templateNumber}_${templateVersion}.js?t=` + time
    }
  },
  mounted() {
    this.initInfo()
  },
  methods: {
    init() {
      // queryDetail方法已经处理了id,可以直接调用
      let that = this
      if (this.currentEditRow) {
        this.$refs.editPage.queryDetail(this.currentEditRow.id, (data) => {
          console.log(that.pageData.groups)
          if (data?.purchaseDeliveryNoticeList?.length) {
            // 行信息有值就置灰表头
            // that.handleHeadform(true)
              let sourceRequestNumber = data.purchaseDeliveryNoticeList[0].sourceRequestNumber
              //若行信息中送货需求申请号为null，库存组织和库存地点可编辑
             if(sourceRequestNumber!==null){
                 that.handleHeaderFields({ pageData: that.pageData, flag: true })
             }else{
                 that.handleHeaderFields({ pageData: that.pageData, flag: false })
             }
          }
        })
      }
    },
    handleHeadform(flag) {
      // 所有表头字段置灰
      const formFields = this.pageData.formFields || ''
      formFields.forEach((n) => {
        n.disabled = flag
      })
    },
    handleLoadSuccess(res) {
      this.currentEditRow = res.res.result
      this.showRemote = true
    },
    initInfo() {
      if (this.currentEditRow && this.currentEditRow.id) {
        getAction(this.url.detail, { id: this.currentEditRow.id }).then((res) => {
          if (res && res.success) {
            this.currentEditRow = res.result
            this.showRemote = true
          }
        })
      } else {
        this.showRemote = true
      }
    },
    showPublishConditionBtn() {
      let params = this.$refs.editPage ? this.$refs.editPage.form : {}
      if (params.id) {
        return true
      } else {
        return false
      }
    },
    //新增行
    insertGridItem() {
      let editPageData = this.$refs.editPage ? this.$refs.editPage.form : {}
      const params = { toElsAccount: editPageData.toElsAccount }
      if (!editPageData.toElsAccount) {
        this.$message.warning('请选择供应商ELS账号')
        return
      }
      debugger
      //如果是公司内部订单需要选择供应商公司代码
      if (editPageData.elsAccount === editPageData.toElsAccount) {
        if (!editPageData.toCompany) {
          this.$message.warning('公司间内部单据,请选择供应商公司代码')
          return
        }
      }
      // if (editPageData.factory) {
      //   params['factory'] = editPageData.factory
      // }
      // if (editPageData.storageLocation) {
      //   params['storageLocation'] = editPageData.storageLocation
      // }
      if (editPageData.toCompany) {
        params['toCompany'] = editPageData.toCompany
      }
      let url = '/order/purchaseOrderItem/deliveyNoticeByOrderlist'
      let columns = [
        {
          field: 'remainNoticeQuantity',
          title: '可转送货通知单量',
          fieldLabelI18nKey: 'i18n__qsdSeRtR_acf36748',
          width: 150
        },
        {
          field: 'orderNumber',
          title: '订单号',
          fieldLabelI18nKey: 'i18n_field_orderNumber',
          width: 150
        },
        {
          field: 'itemNumber',
          title: '订单行号',
          fieldLabelI18nKey: 'i18n_field_itemNumber',
          width: 150
        },
        {
          field: 'materialNumber',
          title: '物料编码',
          fieldLabelI18nKey: 'i18n_field_materialNumber',
          width: 150
        },
        {
          field: 'materialDesc',
          title: '物料描述',
          fieldLabelI18nKey: 'i18n_field_materialDesc',
          width: 150
        },
        {
          field: 'materialName',
          title: '物料名称',
          fieldLabelI18nKey: 'i18n_field_materialName',
          width: 150
        },
        {
          field: 'materialSpec',
          title: '物料组',
          fieldLabelI18nKey: 'i18n_field_materialGroup',
          width: 150
        },
        {
          field: 'materialGroupName',
          title: '物料组名称',
          fieldLabelI18nKey: 'i18n_field_materialGroupName',
          width: 150
        },
        {
          field: 'cateCode',
          title: '物料分类',
          fieldLabelI18nKey: 'i18n_field_cateCode',
          width: 150
        },
        {
          field: 'cateName',
          title: '物料分类名称',
          fieldLabelI18nKey: 'i18n_title_materialClassification',
          width: 150
        },
        {
          field: 'purchaseUnit_dictText',
          title: '采购单位',
          fieldLabelI18nKey: '',
          width: 150
        },
        {
          field: 'materialSpec',
          title: '物料规格',
          fieldLabelI18nKey: 'i18n_title_materialSpec',
          width: 150
        },
        {
          field: 'factory_dictText',
          title: '工厂',
          fieldLabelI18nKey: 'i18n_field_factory',
          width: 150
        },
        {
          field: 'storageLocation_dictText',
          title: '库存地点',
          fieldLabelI18nKey: 'i18n_field_storageLocation',
          width: 150
        },
        {
          field: 'jit_dictText',
          title: '送货安排',
          fieldLabelI18nKey: 'i18n_dict_dSpA_43933cef',
          width: 150
        }
      ]
      this.$refs.fieldSelectModal.open(url, params, columns, 'multiple')
    },
    //删除复选框选定行
    deleteGridItem() {
      let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryNoticeList[0]
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据!'))
        return
      }
      //已锁定的行，不能删除
      for (let i = 0; i < checkboxRecords.length; i++) {
        if (checkboxRecords[i].locked === '1') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isEIdIxOQG_7c7195a4`, '选中行已锁定，不能删除!'))
          return
        }
      }
      //移除状态为新建的单
      itemGrid.removeCheckboxRow()
      let { fullData } = itemGrid.getTableData()
      if (fullData?.length == 0) {
        // this.handleHeadform(false)
        this.handleHeaderFields({ pageData: this.pageData, flag: false })
      }
    },
    //?
    fieldSelectOk(data) {
      let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryNoticeList[0]
      let { fullData } = itemGrid.getTableData()
      let materialList = fullData.map((item) => {
        return item.orderNumber + '_' + item.itemNumber
      })

      //过滤已有数据
      let insertData = data.filter((item) => {
        // 过滤时间
        item.createTime = null
        item.createBy = null
        item.updateTime = null
        item.updateBy = null
        return !materialList.includes(item.orderNumber + '_' + item.orderItemNumber)
      })
      console.log('insertData', insertData)
      insertData = insertData.map((item) => {
        item['jitFlag'] = item.jit
        item['orderItemNumber'] = item.itemNumber
        item['sourceId'] = item.headId
        item['sourceItemId'] = item.id
        item['sourceNumber'] = item.orderNumber
        item['sourceItemNumber'] = item.itemNumber
        item['id'] = null
        item.quantityUnit = item.quantityUnit
        item.conversionRate = item.conversionRate
        item.purchaseCycle = item.purchaseCycle
        if (!!item.requireQuantity) {
          item.secondaryQuantity = item.requireQuantity / (item.conversionRate || 1)
        } else {
          item.secondaryQuantity = 0
        }
        item.secondaryQuantity = item.secondaryQuantity.toFixed(6)
        //属性置空
        return item
      })
      itemGrid.insertAt(insertData, -1)
      // this.handleHeadform(true)
      if(insertData[0].factory!='' && insertData[0].storageLocation==''){
          this.handleHeaderFields({ pageData: this.pageData, flag: false })
      }else{
          this.handleHeaderFields({ pageData: this.pageData, flag: true })
      }
    },
    saveEvent() {
      let { purchaseDeliveryNoticeList = [] } = this.$refs.editPage.getPageData() || {}
      if (!purchaseDeliveryNoticeList.length) {
        let tip = [this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_empty_addLine`, '请添加行项目')]
        this.$message.error(tip.join(''))
        return
      }

      this.$refs.editPage.postData()
    },
    goBack() {
      this.$emit('hide')
    },
    selectCallBack(item) {
      this.pageData.form.dictCode = item[0].dictCode
      this.$refs.editPage.$forceUpdate()
    },
    publishEvent() {
      let { purchaseDeliveryNoticeList = [] } = this.$refs.editPage.getPageData() || {}
      if (!purchaseDeliveryNoticeList.length) {
        let tip = [this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_empty_addLine`, '请添加行项目')]
        this.$message.error(tip.join(''))
        return
      }
        if(this.$refs.editPage.getPageData().factory!==purchaseDeliveryNoticeList[0].factory){
            this.$message.warning('头部库存组织需与行信息库存组织一致')
            return
        }
      for (let i = 0; i < purchaseDeliveryNoticeList.length; i++) {
        if (purchaseDeliveryNoticeList[i].requireQuantity <= 0) {
          let tip = [this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cTVWRlTfUW_49c8866`, '行需求数量必须大于0')]
          this.$message.error(tip.join(''))
          return
        }
      }
      let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
      let that = this
        that.$refs.editPage.handValidate( null, null, function (){
            that.$confirm({
                title: '发布',
                content: '是否发布数据?',
                onOk: function () {
                    that.$refs.editPage.confirmLoading = true
                    postAction(that.url.public, params)
                        .then((res) => {
                            if (res.success) {
                                that.$message.success(res.message)
                                that.goBack()
                            } else {
                                that.$message.warning(res.message)
                            }
                        })
                        .finally(() => {
                            that.$refs.editPage.confirmLoading = false
                        })
                },
                onCancel: function () {
                    that.$refs.editPage.confirmLoading = false
                }
            })
        })
    },
    initDictData(dictCode) {
      //根据字典Code, 初始化字典数组
      let postData = {
        busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
        dictCode
      }
      return ajaxFindDictItems(postData)
    },
    getValueByCode(vlist, formFields) {
      return new Promise((resolve, reject) => {
        let obj = {}
        let nameArr = Object.keys(vlist)
        let request = formFields.filter((el) => nameArr.includes(el.fieldName) && el.dictCode).map((rs) => this.initDictData(rs.dictCode))
        Promise.all(request)
          .then(function (posts) {
            posts = posts.map((ts) => ts.result)
            posts.forEach((p, idx) => {
              let name = `${nameArr[idx]}_text`
              console.log(name)
              obj[name] = p.find((f) => f.value == vlist[nameArr[idx]]).text
            })
            resolve(obj)
          })
          .catch(function (reason) {
            reject(reason)
          })
      })
    },
    async queryMaxRequireQuantity() {
      const _this = this
      let editPageData = this.$refs.editPage ? this.$refs.editPage.form : {}
      let formFields = this.$refs.editPage ? this.$refs.editPage.pageData.formFields : []
      let toElsAccount = editPageData.toElsAccount
      let materialNumber = editPageData.materialNumber
      let factory = editPageData.factory
      let storageLocation = editPageData.storageLocation
      if (!toElsAccount) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierInfoTips`, '请选择供应商信息！'))
        return
      }
      if (!materialNumber) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectMaterialInfoTips`, '请选择物料信息！'))
        return
      }
      if (!factory) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectFactorylInfoTips`, '请选择工厂信息！'))
        return
      }
      if (!storageLocation) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectInventoryLocationlInfoTips`, '请选择库存地点信息！'))
        return
      }
      let psa = await postAction(this.url.queryMaxRequireQuantity, editPageData)
      const type = psa.success ? 'success' : 'error'
      let vlist = {
        factory,
        storageLocation
      }
      let values = await _this.getValueByCode(vlist, formFields)
      console.log(values)
      _this.$message[type]('供应商[' + toElsAccount + ']物料编码[' + materialNumber + ']' + '工厂[' + values.factory_text + ']' + '库存地点[' + values.storageLocation_text + ']' + '的最大需求量为[' + psa.result + ']')
    }
  }
}
</script>

<style lang="less" scoped></style>
