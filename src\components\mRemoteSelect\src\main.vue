<template>
  <div>
    <a-select
      style="width: 100%"
      show-search
      class="mSelectInputStyle"
      allowClear
      :value="value"
      :disabled="disabled"
      :placeholder="value || placeholder || ''"
      :filter-option="false" 
      :showArrow="false"
      :not-found-content="fetching ? undefined : null"
      :defaultActiveFirstOption="false"
      v-bind="$attrs"
      @focus="handleSearch"
      @search="handleSearch"
      @change="handleChange"
    >
      <a-spin
        v-if="fetching"
        slot="notFoundContent"
        size="small" />
      <a-select-option
        v-for="item in dataSource"
        :key="item.id">
        {{ formatTitle(item) }}
      </a-select-option>
      <template #clearIcon>
        <a-icon
          v-if="!disabled"
          slot="suffix"
          :type="!value ? 'search' : 'close-circle'"
          @click="clearInputValue"
        />
      </template>
    </a-select>
  </div>
</template>

<script>
/**
 * MRemoteSelect
 * @description 弹窗选择组件
 * @property {Object} config 当前字段配置
 * @property {Object} pageData 父级所有数据
 * @property {Object} form 父级表头数据
 * @property {Boolean} isRow 是否为表行弹窗类型
 * @property {Object} row 表行配置，当前行数据
 * @property {Object} column 表行配置，当前列数据
 * @property {Array} config.extend.modalConfigs 多套弹窗参数配置
 */
import {
    EXTEND,
    BEFORE_CHECK_CALLBACK,
    // AFTER_CLEAR_CALLBACK,
    MODAL_PARAMS,
    SELECT_MODEL,
    MODAL_URL,
    MODAL_COLUMNS,
    PRIVATE_MODAL_PARAMS_FUNC,
    PRIVATE_BEFORE_CHECK_CALLBACK
} from '@/utils/constant.js'

import { getObjType, bindfunctionMiddleware } from '@/utils/util.js'
import { debounce } from 'lodash'
import { httpRequest } from '@/api/manage'

export default {
    name: 'MRemoteSelect',
    inject: {
        tplRootRef: {
            default: () => {}
        }
    },
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: String,
            default: ''
        },
        config: {
            type: Object,
            default () {
                return {}
            }
        },
        pageData: {
            type: Object,
            default () {
                return {}
            }
        },
        form: {
            type: Object,
            default () {
                return {}
            }
        },
        isRow: {
            type: Boolean,
            default: false
        },
        row: {
            type: Object,
            default () {
                return {}
            }
        },
        column: {
            type: Object,
            default () {
                return {}
            }
        },
        placeholder: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            fetching: false,
            searchValue: this.value,
            dataSource: []
        }
    },
    computed: {
        requestMethod () {
            return this.config?.extend?.requestMethod || 'get'
        },
        // 当前配置索引值，当弹窗配置为数组时
        current () {
            return this.config && this.config[EXTEND] && this.config[EXTEND].current || 0
        },
        // 当前配置项
        curConfig () {
            let extend = (this.config && this.config[EXTEND]) || {}
            if (extend.modalConfigs && Array.isArray(extend.modalConfigs)) {
                return extend.modalConfigs[this.current]
            }
            return extend
        },
        modalParams () {
            let modalParams = this.curConfig[MODAL_PARAMS] || {}
            // 配置为一个方法时
            // 返回方法执行后的结果
            if (getObjType(modalParams) === 'function') {
                if (modalParams.name.indexOf(PRIVATE_MODAL_PARAMS_FUNC) !== -1) {
                    let params = {
                        _pageData: this.pageData,
                        _cacheAllData: this.tplRootRef.getAllData(),
                        _row: this.row,
                        _value: this.value,
                        _form: this.form
                    }
                    modalParams = bindfunctionMiddleware(this.tplRootRef, modalParams, params)
                } else {
                    modalParams = modalParams(this, this.form, this.row)
                }
            }
            return modalParams
        },
        selectModel () {
            let selectModel = this.curConfig[SELECT_MODEL] || 'single'
            if (getObjType(selectModel) === 'function') { // 动态配置selectModel参数
                selectModel = selectModel(this, this.form, this.row)
            }
            return selectModel
        },
        modalUrl () {
            let modalUrl = this.curConfig[MODAL_URL] || ''
            if (getObjType(modalUrl) === 'function') { // 动态配置modalUrl参数
                modalUrl = modalUrl(this, this.form, this.row)
            }
            return modalUrl
        },
        modalColumns () {
            let modalColumns = this.curConfig[MODAL_COLUMNS] || []
            if (getObjType(modalColumns) === 'function') { // 动态配置modalColumns参数
                modalColumns = modalColumns(this, this.form, this.row)
            }
            // 拼接时使用
            if (modalColumns.length > 2) {
                modalColumns = modalColumns.slice(0, 2)
            }
            return modalColumns
        }
    },
    methods: {
        handleChange (value) {
            if (!value) {
                this.handleSearch()
                return
            }
            let selectedData = this.dataSource.filter(n => n.id === value) || []
            this.$emit('ok', selectedData)
        },
        formatTitle (item) {
            let columns = this.modalColumns
            let leng = columns.length
            return columns.reduce((acc, obj, i) => {
                acc += (`${item[obj.field]}` || '')
                if (leng > 1 && i !== columns.length - 1) {
                    acc += '_'
                }
                return acc
            }, '')
        },
        handleJudge () {
            let flag = true
            if (!this.curConfig && !Object.keys(this.curConfig).length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_configurationParameter`, '请配置参数'))
                flag = false
            }
            return flag
        },
        handleSearch (val = '') {
            if (!this.handleJudge()) {
                return
            }
            let beforeCheckedCallBack = this.curConfig[BEFORE_CHECK_CALLBACK] || (() => Promise.resolve())
            return this.changeSelectModalValue(beforeCheckedCallBack).then(() => {
                this.loadData(val)
            })
        },
        loadData: debounce(function (keyWord) {
            this.fetching = true
            let params = Object.assign({}, this.modalParams, {
                pageNo: 1,
                pageSize: 20,
                keyWord
            })
            httpRequest(this.modalUrl, params, this.requestMethod).then((res) => {
                this.dataSource = res.result.records || res.result || []
            }).finally(() => {
                this.fetching = false
            })
        }, 500, { leading: true }),
        clearInputValue () {
            this.$emit('change', '')
            this.$emit('afterClearCallBack')
        },
        // 统一表头、表行回调参数
        changeSelectModalValue (cb) {
            if (cb.name === PRIVATE_BEFORE_CHECK_CALLBACK) {
                let params = {
                    _pageData: this.pageData,
                    _cacheAllData: this.tplRootRef.getAllData(),
                    _value: this.value,
                    _row: {},
                    _form: {}
                }
                params[this.isRow ? '_row' : '_form'] = this.row
                return bindfunctionMiddleware(this.tplRootRef, cb, params)
            } else {
                if (this.isRow) {
                    return cb && cb(this, this.row, this.column, this.form)
                } else {
                    return cb && cb(this, this.pageData, this.groupData, this.form)
                }
            }
        }
    }
}
</script>
<style lang="less" scoped>
:deep(.mSelectInputStyle .ant-select-selection .ant-select-selection__rendered .ant-select-search--inline .ant-select-search__field__wrap input){
    border: none;
    background-color: rgba(0,0,0,0);
}
</style>
