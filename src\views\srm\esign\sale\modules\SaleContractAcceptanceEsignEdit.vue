<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <a-modal
    v-drag
      v-model="previewModal"
      title="预览"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
    <a-modal
    v-drag
      v-model="visible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_PeIL_39fb9595`, '签章定位')"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :label-col="layout.labelCol"
        :wrapper-col="layout.wrapperCol">
        <a-form-model-item label="关键字">
          <a-input
            v-model="form.keyword" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { REPORT_ADDRESS } from '@/utils/const.js'

export default {
    name: 'SaleContractAcceptanceEsignEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            rowIndex: -1,
            sealAeraIndex: -1,
            rowSignerIndex: -1,
            selectType: 'esignFlow',
            previewModal: false,
            previewContent: '',
            visible: false,
            signerBtn: false,
            templateNumber: undefined,
            templateOpts: [],
            printRow: {},
            form: {
                keyword: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                    busType: 'contract',
                    busNumber: null,
                    businessScene: null,
                    relationId: null,
                    filesName: null,
                    filesId: null,
                    uploaded: null,
                    autoArchiving: null,
                    autoInitiate: null,
                    remark: null,
                    cutOffTime: null,
                    contractRemind: null,
                    noticeType: null,
                    effectiveTime: null
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'busType',
                                    dictCode: 'electronicSignatureBusType',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRCELSey_feb832ca`, '采购方ELS账号'),
                                    fieldName: 'elsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRCRL_47f9a0f6`, '采购方名称'),
                                    fieldName: 'purchaseName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessNumber`, '业务单号'),
                                    fieldName: 'busNumber',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_WhoWillStampFirst`, '哪方先盖章'),
                                    fieldName: 'firstSeal',
                                    dictCode: 'srmSignatoryType'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessAssociationID`, '业务关联id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                                    fieldName: 'filesName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'),
                                    fieldName: 'filesId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkisSuccessfullyUploaded`, '文件是否上传成功'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '1')
                                            setDisabledByProp('busNumber', flag)
                                            setDisabledByProp('toElsAccount', flag)
                                        }else{
                                            setDisabledByProp('busNumber', true)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    disabled: true,
                                    dictCode: 'srmSendStatus'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSignatoryMaintainsTheState`, '签署人维护状态'),
                                    fieldName: 'signerVindicateStatus',
                                    dictCode: 'srmSignerVindicateStatus',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCKQWXre_ad12ca8a`, '供方是否线上盖章'),
                                    fieldName: 'onlineSealed',
                                    dictCode: 'yn',
                                    disabled: true,
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '1')
                                            if(flag){
                                                setDisabledByProp('firstSeal', false)
                                            }else{
                                                setDisabledByProp('firstSeal', true)
                                            }
                                        }else{
                                            setDisabledByProp('firstSeal', true)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BqQfRcWCQGdlnH74`, '供方是否上传盖章文件'),
                                    fieldName: 'signFileUploaded',
                                    disabled: true,
                                    dictCode: 'yn',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '1')
                                            setDisabledByProp('firstSeal', flag)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_autoArchiving`, '是否自动归档'),
                                    fieldName: 'autoArchiving',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheProcessIsAutomaticallyStarted`, '流程是否自动开启'),
                                    fieldName: 'autoInitiate',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationDateSigning`, '签署有效截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractRemind`, '文件到期前多少时间提醒(小时)'),
                                    fieldName: 'contractRemind'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationdateDocument`, '文件有效截止时间'),
                                    fieldName: 'effectiveTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                busType: [{required: true, message: '业务类型不能为空'}],
                                busNumber: [{required: true, message: '业务单号不能为空', trigger: 'change'}],
                                businessScene: [{required: true, message: '文件主题不能为空'}],
                                firstSeal: [{required: true, message: '哪方先盖章不能为空'}]
                            }
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCPWL_36613b74`, '采方签署人'), groupCode: 'purchaseSignersList', type: 'grid', custom: {
                        ref: 'purchaseSignersList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 230, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'autoArchive', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`,
                                '是否自动落章'), width: 120, editRender: {name: '$select', options: [
                                {label: '否', value: '0'},
                                {label: '是', value: '1'}]}},
                            { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                            { field: 'signWord', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'), width: 120 },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'enterpriseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 180 },
                            { field: 'loadingCompany', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), visible: false},
                            { field: 'loadingCompany_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), width: 120, visible: false},
                            { field: 'companyCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'), width: 120, visible: false},
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureCompany`, '签署公司'), width: 200 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureAccount`, '签署用户E签宝账号'), width: 120 },
                            { field: 'idType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170, visible: false },
                            { field: 'idType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170 },
                            { field: 'idNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'), width: 170 },
                            { field: 'orgLegalIdNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateNumber`, '法人证件号'), width: 170 },
                            { field: 'orgLegalName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_corporateName`, '法人名称'), width: 120 },
                            { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_companySignatureAccount`, '公司E签宝账号'), width: 120 },
                            { field: 'filesId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'), width: 120 },
                            { field: 'filesName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'), width: 120 },
                            { field: 'silentAuthStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OClbzE_30db0125`, '静默授权状态'), visible: false},
                            { field: 'certificationStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), visible: false},
                            { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120},
                            // { field: 'autoSign', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isAutomaticSignature`, '是否自动签署'), width: 120, editRender: {name: '$select', options: [
                            //     {label: '否', value: '0'},
                            //     {label: '是', value: '1'}
                            // ]} },
                            { field: 'signType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), width: 120, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xW_a09e3`, '不限'), value: '0'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_tEPW_282d3374`, '单页签署'), value: '1'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_AGPW_482faf60`, '骑缝签署'), value: '2'}
                            ]} }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RCPWL_91e5ef48`, '供方签署人'), groupCode: 'saleSignersList', type: 'grid', custom: {
                        ref: 'saleSignersList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 230, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'autoArchive', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`,
                                '是否自动落章'), width: 120, editRender: {name: '$select', options: [
                                {label: '否', value: '0'},
                                {label: '是', value: '1'}]}},
                            { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                            { field: 'signWord', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'), width: 120 },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'enterpriseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 180 },
                            { field: 'loadingCompany', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), visible: false},
                            { field: 'loadingCompany_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), width: 120, visible: false},
                            { field: 'companyCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'), width: 120, visible: false},
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureCompany`, '签署公司'), width: 200 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureAccount`, '签署用户E签宝账号'), width: 120 },
                            { field: 'idType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170, visible: false },
                            { field: 'idType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170 },
                            { field: 'idNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'), width: 170 },
                            { field: 'orgLegalIdNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateNumber`, '法人证件号'), width: 170 },
                            { field: 'orgLegalName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_corporateName`, '法人名称'), width: 120 },
                            { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_companySignatureAccount`, '公司E签宝账号'), width: 120 },
                            { field: 'filesId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'), width: 120 },
                            { field: 'filesName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'), width: 120 },
                            { field: 'certificationStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), visible: false},
                            { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120},
                            { field: 'signType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), width: 120, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xW_a09e3`, '不限'), value: '0'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_tEPW_282d3374`, '单页签署'), value: '1'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_AGPW_482faf60`, '骑缝签署'), value: '2'}
                            ]} }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuPWRC_72eda045`, '添加签署公司'),  type: 'primary', click: this.addSaleSignEvent, showCondition: this.showAddPurchaseSignEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QGPWRC_a5504d04`, '删除签署公司'), click: this.deleteSaleSignEvent, showCondition: this.showAddPurchaseSignEvent}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWMU_b399f609`, '设置签署区域'), clickFn: this.getSignArea },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRWe_416ee4e0`, '设置印章'), clickFn: this.getSeal },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWL_ed04f016`, '设置签署人'), clickFn: this.addSaleSignerEvent}
                        ],
                        rules: {
                            sealIds: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WexOLV_4a4b6180`, '印章不能为空')}],
                            subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWWWeyWxOLV_18bfbcf9`, '[签署人SRM账号]不能为空')}],
                            accountId: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWPseyWxOLV_9b256723`, '[签署人E签宝账号]不能为空')}]
                            // ,
                            // signArea: [{required: true, message: '签署区域不能为空'}]
                        }
                    } },
                    { groupName: '签章文件', groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'saleAttachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                type: 'upload', showCondition: this.showUpload,
                                attr: this.attrHandle,
                                businessType: 'esign', callBack: this.uploadCallBack},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.previewEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFile}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: '下载', clickFn: this.downloadEvent }
                        ]
                    } }
                ],
                formFields: [],
                footerButtons: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_ESQIUB_224d09a`, '业务文件预览'), type: 'primary', click: this.preview },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_PWQIUB_ee68be07`, '签署文件预览'), type: 'primary', click: this.esignFileDown, showCondition: this.showEsignFileDown },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileUpload`, '文件上传'), type: 'primary', click: this.fileUpload, showCondition: this.showUploadBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hdnCSuPeL_9dea9bd1`, '发送采方添加签章人'), type: 'primary', click: this.sendEvent, showCondition: this.showSendBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                edit: '/esign/elsContractAcceptanceEsign/edit',
                detail: '/esign/elsContractAcceptanceEsign/queryById',
                uploadFile: '/esign/elsContractAcceptanceEsign/fileCreate',
                keyWordToAera: '/esign/elsContractAcceptanceEsign/keyWordToAera',
                viewEsignFile: '/esign/elsContractAcceptanceEsign/viewEsignFile',
                uploadLogUrl: '/attachment/purchaseAttachment/uploadLog',
                upload: '/attachment/purchaseAttachment/upload'
            }
        }
    },
    mounted () {
        this.init()
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.busNumber,
                actionRoutePath: '/srm/esign/ContractAcceptanceEsignList,/srm/esign/sale/SaleContractAcceptanceEsignList'
            }
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
                getAction(this.url.detail, {id: this.currentEditRow.id}).then(res => {
                    if (res.success) {
                        this.$parent.currentEditRow = res.result || {}
                    }
                })
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        uploadCallBack (result) {
            const saleAttachments = this.$refs.editPage.getPageData().saleAttachments
            let itemGrid = this.$refs.editPage.$refs.saleAttachments[0]
            if(saleAttachments.length>0){
                const ids = saleAttachments.map(n => (n.id)).join(',')
                const params = {
                    ids
                }
                getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                    if (res.success) {
                        itemGrid.remove()
                        itemGrid.insertAt(result, -1)
                    }
                })
            }else{
                itemGrid.insertAt(result, -1)
            }
        },
        deleteFile () {
            const fileGrid = this.$refs.editPage.$refs.saleAttachments[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        previewEvent () {
            const fileGrid = this.$refs.editPage.$refs.saleAttachments[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if (checkboxRecords.length > 1) {
                this.$message.warning('只能选择一条数据！')
                return
            }
            let preViewFile = checkboxRecords[0]
            this.$previewFile.open({params: preViewFile })
        },
        getSignArea (row, column, $rowIndex){
            if(row.signType==='0'){
                this.$message.warning('当【签署类型】为‘不限’时，不需要设置签署区域')
                return
            }
            this.selectType = 'keyWord'
            this.sealAeraIndex = $rowIndex
            this.visible = true
        },
        handleOk () {
            if(!this.form.keyword){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIJxOLV_594d82ac`, '关键字不能为空'))
                return
            }
            const form = this.$refs.editPage.getPageData()
            let param = form.saleSignersList[this.sealAeraIndex]
            param.signWord = this.form.keyword
            param.filesId = form.filesId
            param.signArea = ''
            this.confirmLoading = true
            let columns = [
                { field: 'pageNo', title: '页码', width: 200 },
                { field: 'posx', title: '横轴(X)', width: 180 },
                { field: 'posy', title: '纵轴(Y)', width: 180 }
            ]
            this.$refs.fieldSelectModal.open(this.url.keyWordToAera, param, columns, 'single')
            this.visible = false
        },
        getSeal (row, column, $rowIndex){
            this.selectType = 'addSigner'
            this.rowIndex = $rowIndex
            let url = '/esign/elsSeals/list'
            let columns = [
                { field: 'companyName', title: '机构名称', width: 200 },
                { field: 'sealId', title: '签章id', width: 180 },
                { field: 'alias', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'), width: 180 }
            ]
            let params = {orgId: row.orgId, elsAccount: row.elsAccount}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        showUpload (){
            return this.currentEditRow.onlineSealed==='0'
        },
        showUploadBtn (){
            if(this.currentEditRow.onlineSealed==='0' && this.currentEditRow.signFileUploaded =='0'){
                return true
            }
            if(!this.signerBtn){
                //已经上传
                if(this.currentEditRow.uploaded==='1'){
                    return false
                }
                //供应商线上盖章，并且还未上传盖章文件到电子签章，显示
                if(this.currentEditRow.onlineSealed==='1' && this.currentEditRow.uploaded !=='1'){
                    return true
                }
                //供应商线下盖章，并且已经上传了盖章文件
                if(this.currentEditRow.onlineSealed!=='1' && this.currentEditRow.signFileUploaded==='1'){
                    return true
                }
                return false
            }else{
                return false
            }
        },
        showEsignFileDown (){
            if(!this.signerBtn){
                if(this.currentEditRow.uploaded==='1'){
                    return true
                }else{
                    return false
                }
            }else{
                return true
            }
        },
        showSendBtn (){
            if(this.currentEditRow.sendStatus==='1'){
                return false
            }
            if(!this.currentEditRow.filesId){
                return false
            }
            if(this.currentEditRow.onlineSealed == '0' && this.currentEditRow.signFileUploaded !=='1' ){
                return false
            }
            return true
        },
        showSignSendBtn (){
            //线下盖章，并且还未发送
            if(this.currentEditRow.onlineSealed!=='1'
                && this.currentEditRow.sendStatus!=='1' && this.currentEditRow.uploaded == '1'){
                return true
            }
            return false
        },
        showAddPurchaseSignEvent (){
            if(this.currentEditRow.onlineSealed!=='1'){
                return false
            }
            if(!this.signerBtn){
                //线下，供方签署文件已上传
                if(this.currentEditRow.onlineSealed!=='1' && this.currentEditRow.signFileUploaded==='1' && this.currentEditRow.uploaded==='1'){
                    return true
                }
                //线上，签署文件已上传
                if(this.currentEditRow.onlineSealed==='1' && this.currentEditRow.uploaded==='1'){
                    return true
                }
                return false
            }else{
                return true
            }
        },
        showDeleteSaleSignEvent (){
            if(!this.signerBtn){
                //线下，供方签署文件已上传
                if(this.currentEditRow.onlineSealed!=='1' && this.currentEditRow.signFileUploaded==='1' && this.currentEditRow.uploaded==='1'){
                    return true
                }
                //线上，签署文件已上传
                if(this.currentEditRow.onlineSealed==='1' && this.currentEditRow.uploaded==='1'){
                    return true
                }
                return false
            }else{
                return true
            }
        },
        addSaleSignEvent () {
            this.selectType = 'saleEsign'
            const form = this.$refs.editPage.getPageData()
            let url = '/esign/elsContractAcceptanceEsign/getSignerlist'
            let columns = [
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                { field: 'enterpriseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 180 },
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称'), width: 180 },
                { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_companySignatureAccount`, '公司E签宝账号'), width: 120 },
                { field: 'certificationStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120, editRender: {name: '$select', options: [
                    {label: '未认证', value: '0'},
                    {label: '已认证', value: '1'}
                ], disabled: true} }
            ]
            let params = {id: form.relationId, signer: 'sale', busType: form.busType}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addSaleSignerEvent (row, column, $rowIndex){
            this.selectType = 'saleEsigner'
            this.rowSignerIndex = $rowIndex
            const form = this.$refs.editPage.getPageData()
            if(form.uploaded!=='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeQIsLXVShILreMU_88640b6a`, '签章文件还未上传，无法定位盖章区域'))
                return
            }
            let url = '/esign/elsSubaccountCertificationInfo/list'
            let columns = [
                { field: 'subAccount', title: '子账号', width: 100 },
                { field: 'accountId', title: 'e签宝账户号', width: 180 },
                { field: 'name', title: '姓名', width: 180 },
                { field: 'certificationStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120, editRender: {name: '$select', options: [
                    {label: '未认证', value: '0'},
                    {label: '已认证', value: '1'}
                ], disabled: true} },
                { field: 'silentAuthStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OClbzE_30db0125`, '静默授权状态'), width: 120, editRender: {name: '$select', options: [
                    {label: '未授权', value: '0'},
                    {label: '已授权', value: '1'}
                ], disabled: true} }
            ]
            let params = {orgCreateFlag: '1'}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        fieldSelectOk (data) {
            const form = this.$refs.editPage.getPageData()
            if(this.selectType == 'saleEsign'){
                if(data[0].certificationStatus!=='1'){
                    this.$message.warning('该签署公司需要先进行实名认证，可前往认证[电子签章->企业认证->找到对应的公司提交认证并根据返回的链接进行认证]')
                    return
                }
                let arr = data.map(({ id, ...others }) => ({ relationId: id, signType: '1', autoSign: '0', ...others, subAccount: '', accountId: ''}))
                let itemGrid = this.$refs.editPage.$refs.saleSignersList[0]
                itemGrid.remove()
                let { fullData } = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.relationId
                })
                let insertData = arr.filter(item => {
                    return !regulations.includes(item.relationId)
                })
                itemGrid.insertAt(insertData)
            }else if(this.selectType === 'addSigner'){
                let ids = data.map(n => n.sealId).join(',')
                // const form = this.$refs.editPage.getPageData()
                form.saleSignersList[this.rowIndex].sealIds = ids
            }else if(this.selectType === 'keyWord'){
                if(data && data.length>0){
                    const { pageNo = '', posx = '', posy = '' } = data[0] || {}
                    let result = `${pageNo}_${posx}_${posy}`
                    // const form = this.$refs.editPage.getPageData()
                    let param = form.saleSignersList[this.sealAeraIndex]
                    param.signArea = result
                }
            }else if(this.selectType === 'saleEsigner'){
                if(data[0].certificationStatus!=='1'){
                    this.$message.warning('该签署公司需要先进行实名认证，可前往认证[电子签章->个人认证->找到对应的公司提交认证并根据返回的链接进行认证]')
                    return
                }
                // const form = this.$refs.editPage.getPageData()
                form.saleSignersList[this.rowSignerIndex].subAccount = data[0].subAccount
                form.saleSignersList[this.rowSignerIndex].accountId = data[0].accountId
            }
        },
        deleteSaleSignEvent (){
            let itemGrid = this.$refs.editPage.$refs.purchaseSignersList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        preview () {
            const token = this.$ls.get('Access-Token')
            let url = ''
            if(this.currentEditRow.busType === 'reconciliationConfirmation') {
                url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:sale_reconciliation_confirmation.ureport.xml&token=' +
                    token+'&id='+this.currentEditRow.relationId
            } else if(this.currentEditRow.busType === 'contractAcceptance') {
                url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:sale_contract_acceptance.ureport.xml&token=' +
                    token+'&id='+this.currentEditRow.relationId
            }

            window.open(url, '_blank')
        },
        selectedPrintTemplate () {
            if(this.templateNumber) {
                const that = this
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    printId: template[0].printId,
                    printName: template[0].printName,
                    printType: template[0].printType,
                    param: template[0].param
                }
                that.demandVisible = false
                that.submitLoading = false
                let rowItem = this.printRow
                this.printRow = {}
                let urlParam = ''
                if (params.param) {
                    let json = JSON.parse(params.param)
                    console.log('json:', json)
                    Object.keys(json).forEach((key, i) => {
                        urlParam += '&'+key+'='+rowItem[json[key]]
                    })
                }
                if (params.printType=='ureport') {
                    const token = this.$ls.get('Access-Token')
                    //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
                    const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.printName+'&token=' + token+urlParam
                    window.open(url, '_blank')
                }
                if (params.printType=='jimu') {
                    const token = this.$ls.get('Access-Token')
                    const url = REPORT_ADDRESS + '/els/report/jmreport/view/'+params.printId+'?token=' + token+urlParam
                    //const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.title+'&token=' + token+'&id='+rowItem.id
                    window.open(url, '_blank')
                }
            }
        },
        queryPrintTemList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: 'order'}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        orderReview (id){
            this.queryPrintTemList(this.$ls.get('Login_elsAccount')).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        getAction('/order/purchaseOrderHead/queryById', {id: id}).then(resOrder => {
                            this.printRow = resOrder.result
                            let options = res.result.map(item => {
                                return {
                                    value: item.id,
                                    printId: item.printId,
                                    printName: item.printName,
                                    title: item.templateName,
                                    printType: item.printType,
                                    param: item.param
                                }
                            })
                            this.templateNumber = ''
                            this.templateOpts = options
                            // 只有单个模板直接新建
                            if (this.templateOpts && this.templateOpts.length===1) {
                                this.templateNumber = this.templateOpts[0].value
                                this.selectedPrintTemplate()
                            } else {
                                // 有多个模板先选择在新建
                                this.printVisible = true
                            }
                        })
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        prevEvent () {
            this.$refs.editPage.prevStep()
        },
        nextEvent () {
            this.$refs.editPage.nextStep()
        },

        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    const signer = params.saleSignersList
                    if(signer && signer.length>0){
                        if(signer[0].signType!=='0'&& !signer[0].signArea){
                            this.$message.warning('签署类型为(单页签署/骑缝签署)时需要设置签署区域')
                            return
                        }
                        // eslint-disable-next-line no-useless-escape
                        const reg = /[a-zA-z0-9_\.]+/
                        if(signer[0].signType!=='0' && !reg.test(signer[0].signArea)){
                            this.$message.warning('【签署区域】格式不规范，事例（页码_X_Y）：1_96.0_780.927')
                            return
                        }
                    }

                    if(params.onlineSealed==='0'){
                        //线下签署，上传文件
                        const files = params.saleAttachments
                        if(!files || files.length<1){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQIxOLV_d540bb93`, '签署文件不能为空'))
                            return
                        }
                        //前述状态“已签署”
                        params.signFileUploaded='1'
                    }
                    let url = this.url.edit
                    //采购方操作
                    params.modifyPerson='1'
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.init()
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        sendEvent (){
            const params = this.$refs.editPage.getPageData()
            const signer = params.saleSignersList
            if(signer && signer.length>0 && signer[0].signType!=='0'&& !signer[0].signArea){
                this.$message.warning('签署类型为(单页签署/骑缝签署)时需要设置签署区域')
                return
            }
            //线上盖章
            if(params.onlineSealed==='1'){
                if(params.uploaded!=='1'){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeQIsLXVShILreMU_88640b6a`, '签章文件还未上传，无法定位盖章区域'))
                    return
                }
            }
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const signer = params.saleSignersList
                    if(signer && signer.length>0){
                        if(signer[0].signType!=='0'&& !signer[0].signArea){
                            this.$message.warning('签署类型为(单页签署/骑缝签署)时需要设置签署区域')
                            return
                        }
                        // eslint-disable-next-line no-useless-escape
                        const reg = /[a-zA-z0-9_\.]+/
                        if(signer[0].signType!=='0' && !reg.test(signer[0].signArea)){
                            this.$message.warning('【签署区域】格式不规范，事例（页码_X_Y）：1_96.0_780.927')
                            return
                        }
                    }else {
                        if(params.onlineSealed==='1'){
                            this.$message.warning('请添加供方签署人')
                            return
                        }
                    }
                    //发送
                    params.sendStatus = '1'
                    //供方
                    params.modifyPerson='1'
                    //是否发送信息发布
                    params.operateType = 'publish'
                    let url = this.url.edit
                    postAction(url, params).then(res => {
                        if(res.success){
                            this.$message.info('发送成功')
                            this.goBack()
                        }else{
                            this.$message.warning(res.message)
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        fileUpload (){
            const params = this.$refs.editPage.getPageData()
            if(params.uploaded==='1'){
                this.$message.warning('文件已上传')
                return
            }
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    let url = this.url.uploadFile
                    params.reportUrl = REPORT_ADDRESS
                    params.token = this.$ls.get('Access-Token')
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.signerBtn = true
                            this.init()
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
            })

        },
        esignFileDown (){
            const params = this.$refs.editPage.getPageData()
            getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                if(res.success){
                    debugger
                    window.open(res.result.downloadUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>