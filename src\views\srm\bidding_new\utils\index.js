import {message} from 'ant-design-vue'
import { isArray, isObject } from 'lodash'
import { getAction } from '@views/srm/bidding_new/plugins/manage'

export const valiStringLength = (data = {}, rules = []) => {
    if (!isObject(data) || !Object.prototype.toString.call(data) === '[object Object]') {
        throw new Error('data is not Object')
    }
    if (!isArray(rules)) {
        throw new Error('rules is not Array')
    }
    let flag = false
    for (let i = 0; i < rules.length; i++) {
        let {field, label, maxLength = 100, type} = rules[i]
        if (typeof data[field] == 'string' && data[field].length > maxLength) flag = true
        if (type == 'number' && data[field] && !valitNumberLength(data[field], 8)) flag = true
        if (type == 'amount' && data[field] && !valitNumberLength(data[field], 12, 6)) flag = true
        if (flag) {
            message.error(`${label}长度不能超过${maxLength}`)
            throw new Error(`${label}长度不能超过${maxLength}`)
        }
    }
}
// 调用示例 valitNumberLength(999999999.99)     // 默认无限制，只要能转成数字就行
// 调用示例 valitNumberLength(9.99, 1) 		   // 限制整数位最多为允许1位
// 调用示例 valitNumberLength(9.9, 1, 1)       // 限制整数位最多1位，小数位最多允许1位
// 调用示例 valitNumberLength(9, 1, 0)         // 限制为整数，且整数最多允许1位
// 调用示例 valitNumberLength(0.9, 0, 1)       // 限制为小数，且小数最多允许1位
// 参数说明 
//	 number 输入的值可以是字符串或数字  
//   integerBit 整数部分允许位数(为0时限制为小数，大于0时则限制整数部分的位数，小于0或没传时则无限制)
//   decimalBit 小数部分允许位数(为0时限制为整数，大于0时则限制小数部分的位数，小于0或没传时则无限制)
export const valitNumberLength = (number, integerBit, decimalBit) =>{
    var exp
    if (integerBit > 0) {
        exp = '/^[-]{0,1}(([1-9]{1}[0-9]{0,' + (integerBit - 1) + '})|([0]{1}))'
    } else if (integerBit === 0) {
        exp = '/^[-]{0,1}([0]{1})'
    } else {
        exp = '/^[-]{0,1}(([1-9]{1}[0-9]*)|([0]{1}))'
    }
    if (decimalBit > 0) {
        exp += '((\\.{1}[0-9]{1,' + decimalBit + '}$)|$)/'
    } else if (decimalBit === 0) {
        exp += '$/'
    } else {
        exp += '((\\.{1}[0-9]+$)|$)/'
    }
    // eslint-disable-next-line no-eval
    eval('exp = ' + exp)
    return exp.test(number)
}
export const getAttachmentUrl = ({id, subpackageId}) => {
    return getAction('/tender/common/download/getDownLoadUrl', {id, subpackageId})
}
export const getCommonAttachmentUrl = ({id}) => {
    return getAction('/attachment/purchaseAttachment/getDownLoadUrl', {id})
}