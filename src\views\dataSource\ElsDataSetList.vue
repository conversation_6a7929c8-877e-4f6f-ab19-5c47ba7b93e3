<template>
  <div style="height: 100%">
    
    <list-layout
      ref="listPage"
      :extraListConfig="extraListConfig"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <ElsDataSetModalEdit
      v-if="showEditPage"
      ref="eightDisciplines"
      :dataSource="dataSource"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <!-- <purchase-eight-disciplines-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" /> -->
  </div>
</template>
<script lang='jsx'>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction } from '@/api/manage'
import { ListMixin } from '@comp/template/list/ListMixin'
import ElsDataSetModalEdit from './modules/ElsDataSetModalEdit'
// import purchaseEightDisciplinesHeadDetail from './modules/PurchaseEightDisciplinesHeadDetail'
import layIM from '@/utils/im/layIM.js'
import { postAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        ElsDataSetModalEdit
    },
    data () {
        return {
            showEditPage: false,
            dataSource: [],
            showDetailPage: false,
            // 自定义渲染按钮
            extraListConfig: {
                toolbarConfig: {
                    slots: { buttons: (params, h) => {
                        let oldSlots = ''
                        let buttons = []
                        if (params?.$grid) {
                            const $grid = params?.$grid
                            oldSlots = $grid?.$slots?.toolbar_buttons ? $grid.$slots.toolbar_buttons : ''
                            buttons = [...oldSlots]
                        }
                        const props = {
                        }
                        
                        const on = {
                            click: (e) => {
                                return this.handleMenuClick(e)
                            }
                        }
                        let ButtonJsx = [<a-dropdown >
                            <a-menu slot="overlay" {...{ props, on }}>
                                <a-menu-item key="sql">
                                        sql
                                </a-menu-item>
                                <a-menu-item key="http">
                                        http
                                </a-menu-item>
                            </a-menu>
                            <a-button>{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增') }  <a-icon type="down" /> </a-button>
                        </a-dropdown>]
                        //  原按钮压在最后
                        buttons.unshift([ButtonJsx])
                        return (buttons)
                    } }
                }
            },
            pageData: {
                businessType: 'eightDisciplines',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNWFtAoSWFtRL_293d939f`, '请输入数据集编码或数据集名称')
                    }
                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    // { buttonRender: { name: 'ToolbarButtonDownload', events: { click: this.handleMenuClick } } },
                    // {
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    //     icon: 'plus',
                    //     clickFn: this.handleAdd,
                    //     dropdowns: [{name: 'SQL', code: 'SQL'}, {name: 'HTTP', code: 'HTTP'}],
                    //     type: 'MASelect',
                    //     authorityCode: 'eightDisciplines#purchaseEightDisciplines:add'
                    // },
                    // {
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    //     icon: 'plus',
                    //     clickFn: this.handleAdd,
                    //     type: 'primary',
                    //     authorityCode: 'eightDisciplines#purchaseEightDisciplines:add'
                    // },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
                        icon: 'download',
                        folded: false,
                        clickFn: this.handleExportXls,
                        authorityCode: 'eightDisciplines#purchaseEightDisciplines:export'
                    },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF }
                ],
                optColumnList: [
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEditSingle, authorityCode: 'dataSource#elsReportChartDataSet:edit' },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDeleteSingle, authorityCode: 'dataSource#elsReportChartDataSet:edit' }
                ],
                optColumnWidth: 270
            },
            url: {
                list: '/report/dataSource/elsReportChartDataSet/list',
                delete: '/report/dataSource/elsReportChartDataSet/delete',
                columns: 'elsReportChartDataSet'
            }
        }
    },
    methods: {
        handleMenuClick (e) { // 新增的按钮事件
            // 新增清空
            this.currentEditRow = {
                setType: e.key
            }
            if (e.key == 'sql') {
                this.queryAllDataSource()
            } else {
                this.showEditPage = true
            }
        },
        queryAllDataSource () {
            const url = '/report/dataSource/elsReportChartDataSource/queryAllDataSource'
            this.loading = true
            this.$refs.listPage.loading = true
            getAction(url)
                .then((res) => {
                    if (res.success) {
                        this.showEditPage = true
                        this.dataSource = res.result.map(rs => {return{
                            textI18nKey: '',
                            title: rs.sourceName,
                            value: rs.sourceCode,
                            text: rs.sourceName
                        }})
                        // this.$message.success(res.message)
                    } else {
                        // this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.loading = true
                    this.$refs.listPage.loading = false
                })
        },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.eightDisciplinesNumber || id
            // 创建
            layIM.creatGruopChat({ id, type: 'PurchaseEightDisciplinesHead', url: this.url || '', recordNumber })
        },
        allowChat (row) {
            if (row.eightDisciplinesStatus != 'D0' && row.toElsAccount) {
                return false
            } else {
                return true
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WWrH_2745ce`, '8D改进'))
        },
        cancelAuditCallBack (row) {
            this.currentEditRow = row
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        handleEditSingle (row) {
            this.handleEdit(row)
        },
        handleDeleteSingle (row) {
            this.handleDelete(row)
        }
    }
}
</script>
