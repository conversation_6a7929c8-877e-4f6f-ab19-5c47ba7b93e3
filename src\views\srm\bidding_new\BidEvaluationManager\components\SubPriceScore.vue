<template>
  <!-- 价格评分 -->
  <div class="ReviewItems">
    <a-spin :spinning="confirmLoading">
      <div class="review-items-top">
        <a-row>
          <a-col :span="12">
            <span>{{ currentRow.title }}</span>
          </a-col>
          <a-col
            :span="12"
            style="text-align: right;">
            <a-button
              type="primary"
              v-if="currentRow['editStatus'] && priceRegulationInfo?.calType == 0"
              @click="calculation">{{ $srmI18n(`${$getLangAccount()}#i18n_field_td_116416`, '计算') }}</a-button>
            <a-button
              type="primary"
              @click="submitData"
              v-if="currentRow['editStatus']"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
            <a-button
              @click="goBack"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </a-col>
          <a-col :span="24">
            <template v-if="priceRegulationInfo.calType == 0 && priceRegulationInfo.pricePointsCalFormula =='operationMinPriceStrategy' ">
              <span >{{ priceRegulationInfo.pricePointsCalFormula_dictText }}</span>
            </template>
            <template v-else-if="priceRegulationInfo.calType == 0 && priceRegulationInfo.pricePointsCalFormula =='operationMinPricePositiveDeviationStrategy' ">
              <span>
                {{ priceRegulationInfo.pricePointsCalFormula_dictText }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_WsuBxUtruWWV_2ddce27d`, '：报价每高于基准价1%扣') }}{{ priceRegulationInfo.aboveScore }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_zWenVR_31baf384`, '分，最低扣至') }}{{ priceRegulationInfo.lowestScore }}分
              </span>
            </template>
            <template v-else-if="priceRegulationInfo.calType == 0 && priceRegulationInfo.pricePointsCalFormula =='operationStandardPriceStrategy' ">
              <span>
                {{ priceRegulationInfo.pricePointsCalFormula_dictText }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_WsuBxUtruWWV_2ddce27d`, '：报价每高于基准价1%扣') }}{{ priceRegulationInfo.aboveScore }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_zWBnUtruWWV_3d25724d`, '分，每低于基准价1%扣') }}{{ priceRegulationInfo.belowScore }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_zWenVR_31baf384`, '分，最低扣至') }}{{ priceRegulationInfo.lowestScore }}分
              </span>
            </template>
            <template v-else>
              <div>
                {{ priceRegulationInfo.calArtificialRulesDesc }}
              </div>
            </template>
            <span v-if="priceRegulationInfo.calType == 0">{{ $srmI18n(`${$getLangAccount()}#i18n_field_truMRLFdjjXeBsuj_67fc24a4`, '基准价取值规则：所有有效投标报价的') }}{{ priceRegulationInfo.baseCalRules_dictText }}为基准价</span>
          </a-col>
        </a-row>
      </div>
      <div class="review-items-grid">
        <!-- <div
          v-for="(item, key, index) in resultData.evaResultListMap"
          :key="key + index">
          <listTable
            :setGridHeight="gridHeight"
            ref="listTable"
            :fromSourceData="item[0].supplierMaterialList"
            :mergeRowMethod="mergeRowMethod"
            :showTablePage="false"
            :footerMethod="() => {
              let arr = footerMethod(item[0])
              return arr
            }"
            :pageStatus="item[0].invalid == '1' ? 'detail': 'edit'"
            :statictableColumns="statictableColumns"></listTable>
        </div> -->
        <div
          v-for="(item, index) in resultData.supplierList"
          :key="item.supplierAccount + index">
          <listTable
            :key="item.supplierAccount + index"
            :setGridHeight="gridHeight(item)"
            ref="listTable"
            :fromSourceData="item.invalid !== '1' ? resultData.evaResultListMap[item.supplierAccount][0].supplierMaterialList : [{supplierName: `${item.supplierName}（已否决）`}]"
            :footerMethod="() => {
              let arr = item.invalid !== '1' ? footerMethod(resultData.evaResultListMap[item.supplierAccount][0]) : []
              return arr
            }"
            :mergeRowMethod="mergeRowMethod"
            :showTablePage="false"
            :statictableColumns="statictableColumns"></listTable>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import { getAction, postAction } from '@/api/manage'
import { gridOptionsMixin } from '../public/gridOptionsMixin.js'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import { add, mul, div } from '@/utils/mathFloat.js'

export default {
    mixins: [gridOptionsMixin],
    props: {
        currentRow: {
            type: Object,
            default () {
                return {}
            }
        },
        resultDataProp: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    components: {
        listTable
    },
    computed: {
        headParams () {
            let {checkType, currentStep, processType } = this.currentRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return {xNodeId}
        },
        // gridHeight () {
        //     console.log(this.resultData)
        //     let height = '100'
        //     const clientHeight = document.documentElement.clientHeight
        //     height = clientHeight - 260
        //     // 自定义设置表行高
        //     let length = 0
        //     for(let key in this.resultData.evaResultListMap){
        //         length = this.resultData.evaResultListMap[key][0].supplierMaterialList.length
        //     }
        //     // let length = this.resultData.evaResultListMap[0][0].supplierMaterialList.length || 0
        //     if (length !== 0) {
        //         height = 36 * length + 72
        //     }
        //     console.log('height', height)
        //     return height + 'px'
        // },
        statictableColumns () {
            let list = [
                { 
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'), 
                    'field': 'supplierName',
                    slots: {
                        default: ({ row, column }) => {
                            return row.invalid == '1' ? [<span>{row[column.property]}  (已否决)</span>] : [<span>{row[column.property]}</span>] 
                        }
                    }
                },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), 'field': 'materialName'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBsu_2e62dc84`, '投标报价'), 'field': 'quote'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umci_25769c1a`, '价格修正'), 'field': 'priceCorrection', fieldType: 'number', props: {min: -9999}},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBu_2199294`, '评标价'), 'field': 'evaPrice'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tru_154776b`, '基准价'), 'field': 'basePrice'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_getScore`, '得分'), 'field': 'score'}
            ]
            if (this.priceRegulationInfo.calType == '1') {
                list = list.map(item => {
                    if (item.field == 'score') {
                        this.$set(item, 'fieldType', 'number')
                        this.$set(item, 'props', {min: 0, max: this.maxScore})
                        this.$set(item, 'bindFunction', this.handleGetWeightScore)
                    }
                    if (item.field == 'priceCorrection') {
                        this.$set(item, 'bindFunction', this.handleChangePriceCorrection)
                    }
                    return item
                }).filter(item => item.field !== 'basePrice')
            }
            return list
        }
    },
    data () {
        return {
            calculateStatus: false,
            confirmLoading: false,
            resultData: {},
            priceRegulationInfo: {},
            maxScore: '9999'
        }
    },
    created () {
        this.resultData = Object.assign({}, this.resultDataProp)
        this.maxScore = this.resultData.evaluationGroupVO.score
        this.priceRegulationInfo = this.resultData.evaluationGroupVO.tenderEvaluationTemplatePriceRegulationInfo
    },
    methods: {
        gridHeight (item) {
            console.log(this.resultData)
            console.log(item)
            if (item.bidEvaRegulationResultList.length == 0) {
                return '72px'
            }
            let height
            const clientHeight = document.documentElement.clientHeight
            height = clientHeight - 260
            // 自定义设置表行高
            let length = 0
            for(let key in this.resultData.evaResultListMap){
                length = this.resultData.evaResultListMap[key][0].supplierMaterialList.length
            }
            // let length = this.resultData.evaResultListMap[0][0].supplierMaterialList.length || 0
            if (length !== 0) {
                height = 36 * length + 72
            }
            console.log('height', height)
            return height + 'px'
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            // const fields = ['supplierAccount']
            const fields = ['supplierName']
            const cellValue = row['supplierAccount']
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow['supplierAccount'] === cellValue) {
                    return { rowspan: 0, colspan: 0 }
                } else {
                    let countRowspan = 1
                    while (nextRow && nextRow['supplierAccount'] === cellValue) {
                        nextRow = visibleData[++countRowspan + _rowIndex]
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 }
                    }
                }
            }
        },
        // 计算
        calculation (neewMessage = true) {
            const {evaResultListMap={}} = this.resultData
            const keys = evaResultListMap && Object.keys(evaResultListMap)
            const regulationCalList = keys.map(item => {
                return evaResultListMap[item][0]
            })
            let params = regulationCalList

            this.confirmLoading = true
            return postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/priceScoreCalculation', params, {headers: this.headParams}).then(res => {
                this.confirmLoading = false
                if (res.code == 200) {
                    const {result = []} = res
                    for(let item of result) {
                        let supplierMaterialList = item?.supplierMaterialList
                        if (item.invalid == '1') {
                            supplierMaterialList.map(item => {
                                item.priceCorrection = ''
                            })
                        }
                        this.$set(this.resultData.evaResultListMap[item.supplierAccount][0], 'supplierMaterialList', supplierMaterialList)
                        this.$set(this.resultData.evaResultListMap[item.supplierAccount][0], 'score', item.score)
                        this.$set(this.resultData.evaResultListMap[item.supplierAccount][0], 'weight', item.weight)
                        this.$set(this.resultData.evaResultListMap[item.supplierAccount][0], 'weightScore', item.weightScore)
                        this.$set(this.resultData.evaResultListMap[item.supplierAccount][0], 'invalid', item.invalid)
                    }
                    this.calculateStatus = true
                    neewMessage && this.$message.success(res.message)
                } else {
                    this.$message.error(res.message)
                }
                return res
            })
        },
        // 提交数据
        async submitData () {
            // 系统计算且是否已经计算完成
            if (this.priceRegulationInfo?.calType == 0){
                if (!this.calculateStatus) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWHctdW_eaf6fea9`, '请先进行计算！'))
                    return false
                }
                let {success} = await this.calculation(false)
                if (!success) return false
            }
            // 人工计算
            if (this.priceRegulationInfo?.calType == 1) {
                for(let item of Object.values(this.resultData.evaResultListMap)) {
                    if (item[0].score == 0) {
                        return this.$message.warning(`${item[0].supplierName}未填写得分`)
                    }
                }
            }
            this.resultData.evaResultListMap && (() => {
                const evaObj = this.resultData.evaResultListMap
                Object.keys(evaObj).forEach(key => {
                    evaObj[key].forEach(item => {
                        item.judgesTaskItemId = this.currentRow.id
                        item.judgesTaskHeadId = this.currentRow.judgesTaskHeadId
                        // for (let row of this.resultData.supplierList) {
                        //     if (row['supplierAccount'] == item['supplierAccount']) {
                        //         row['invalid'] = item.invalid
                        //         continue
                        //     }
                        // }
                    })
                })
            })()
            this.resultData.supplierList && this.resultData.supplierList.forEach(data => {
                data['judgesTaskItemId'] = this.currentRow.id || ''
            })
            this.confirmLoading = true
            postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/publishSupplierEvaGroupResult', this.resultData, {headers: this.headParams}).then(res => {
                this.confirmLoading = false
                if (res.code == 200) {
                    this.$message.success(res.message)
                    this.goBack()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        footerMethod (data) {
            let {score, weightScore, weight} = data
            let arr = [['', '汇总得分', score, '权重', `${weight}%`, '实际得分', weightScore]]
            if (this.priceRegulationInfo?.calType == 1) arr[0].shift()
            return arr
        },
        // 返回
        goBack () {
            this.$emit('goBack')
        },
        // 价格修正
        handleChangePriceCorrection (row, column, value) {
            let {quote} = row
            let total = add(quote, value)
            this.$set(row, 'evaPrice', total)
        },
        // 最终得分计算
        handleGetWeightScore (row, column, value) {
            let {supplierAccount} = row
            let {weight, supplierMaterialList} = this.resultData.evaResultListMap[supplierAccount][0]
            let supplierMaterialListLength = supplierMaterialList.length
            // 得分总和
            let total = supplierMaterialList.reduce((total, item) => {
                return add(total, item.score)
            }, 0)
            // 汇总得分 = 得分总分 / 分项长度 （平均值）
            let totalscore = div(total, supplierMaterialListLength)
            // 最终得分 = 汇总得分 * 权重%
            let totalWeight = div(mul(totalscore, weight), 100)
            this.$set(this.resultData.evaResultListMap[supplierAccount][0], 'score', totalscore)
            this.$set(this.resultData.evaResultListMap[supplierAccount][0], 'weightScore', totalWeight)
            this.$set(row, 'weightScore', totalWeight)
        }
    }
}
</script>

<style lang="less" scoped>
  .ReviewItems {
    height: 100%;
    background-color: #fff;
    padding: 15px 10px;
  }
  .review-items-top {
    line-height: 30px;

    .ant-col-12:nth-child(1) {
      font-size: 16px;
      font-weight: 600;
    }
    .ant-col-24 {
      background-color: #E6F7FF;
      padding: 5px 10px;
      margin-top: 5px;
      border-radius: 5px;
      color: #02A7F0;

      span {
        display: block;
        font-size: 12px;
        height: 20px;
        line-height: 20px;
      }
    }
  }
  .review-items-grid {
    margin-top: 15px;
  }
</style>
