<!-- 已废弃 -->
<template>
  <div v-if="ifshow">
    <content-header
      :btns="btns"
    />
    <!-- <list-table
      ref="purchaseTenderProjectAttachmentInfoList"
      :groupCode="groupCode"
      :statictableColumns="statictableColumns"
      :pageData="pageData"
      setGridHeight="500"
      :fromSourceData="purchaseTenderProjectAttachmentInfoList"
      :showTablePage="false"
    >
    </list-table> -->
    <RecordResponse
      ref="response"
      :formData="formData"></RecordResponse>
    <RecordFile
      ref="file"
      :formData="formData"></RecordFile>
  </div>
</template>
<script>
import RecordFile from './modules/RecordFile'
import RecordResponse from './modules/RecordResponse'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import ContentHeader from '../components/content-header'
  
export default {
    mixins: [baseMixins],
    components: {
        RecordFile,
        RecordResponse,
        ContentHeader
    },
    props: {
        // groupCode: {
        //     default: '',
        //     type: String
        // },
        // fromSourceData: {
        //     default: () => {},
        //     type: Object
        // },
        // pageStatus: {
        //     default: 'edit',
        //     type: String 
        // }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subPackageRow () {
            return this.currentSubPackage()
        },
        btns () {
            let btn = []
            // 刚进去或者保存过后为新建状态0，展示保存发布按钮
            // if (this.formData.status == '0' || !this.formData.status) {
            //     btn = [
            //         { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
            //         { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish }
            //     ]
            // }
            // //点击发布后已发布状态status == 1以及审批中状态aduitStatus == 1，
            // if(this.formData.status == '1' && this.formData.auditStatus == '1'){
            //     btn = [
            //         { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_withdraw`, '撤回'), type: 'primary', click: this.withdraw }
            //     ]
            // }
            // if(this.formData.audit == '1' && this.formData.status != '0' && this.formData.flowId != null){
            //     btn.push(
            //         { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow }
            //     )
            // }
            // if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') btn = []
            btn = [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.back }
            ]
            return btn
        }
        
          
    },
    
    data () {
        return {
            ifshow: false,
            formData: [],
            purchaseTenderProjectAttachmentInfoList: [],
            // externalToolBar: [
            //     {
            //         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
            //         key: 'upload',
            //         args: {
            //             property: 'label', // 可省略
            //             itemInfo: [], // 必传
            //             disabledItemNumber: true,
            //             action: '/attachment/purchaseAttachment/upload', // 必传
            //             businessType: 'biddingPlatform', // 必传,
            //             headId: '', // 必传
            //             modalVisible: false // 必传
            //         },
            //         attr: this.attrHandle,
            //         callBack: this.uploadCallBack
            //     }
                
            // ],
            pageData: {
                optColumnList: [
                    // {
                    //     key: 'download',
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                    //     clickFn: this.downloadEvent
                    // },
                    // {
                    //     key: 'preView',
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                    //     clickFn: this.preViewEvent
                    // }
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            }
        }
    },
    methods: {
        save () {
            let params = this.formData
            let url = this.params.id ? '/tender/openbid/tenderOpenBidRecordHead/edit' : '/tender/openbid/tenderOpenBidRecordHead/add'
            postAction(url, params).then(res=>{
                if(res.success){
                    this.$message.success(res.message)
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        publish (){
            let url = '/tender/openbid/tenderOpenBidRecordHead/publish'
            let params = this.formData
            postAction(url, params).then(res=>{
                if(res.success){
                    this.$message.success(res.message)
                    this.getData()
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        back (){

        },
        async getData () {
            let params = {
                // subpackageId: this.subPackageRow.subpackageId,
                // 先写死分包id，拿默认数据
                subpackageId: '1536961111769309186'
            }
            await getAction('/tender/openbid/tenderOpenBidRecordHead/queryBySubpackageId', params, {headers: {xNodeId: this.getNodeParams().nodeId}}).then(res=>{
                if(res.success) {
                    this.formData = res.result
                    // this.$refs.response.init(res.result)
                    // this.$refs.file.init(res.result)
                    this.ifshow = true
                    console.log(this.formData)
                }else{
                    this.$message.error(res.message)
                }
            })
        }
    },
    async created () {
        await this.getData()
        // if (['1', '2'].includes(this.fromSourceData.status) || this.pageStatus == 'detail') this.externalToolBar = []
    }
}
</script>
  <style lang="less" scoped>
  
  </style>