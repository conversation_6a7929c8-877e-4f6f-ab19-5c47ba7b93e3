<template>
  <div class="page detail-page">
    <div class="page-header">
      <a-page-header :ghost="false"
      >
        <template slot="extra">
          <a-button
            v-for="(item, index) in awardNameBtn[awardName]"
            :key="index"
            @click="item.click">{{ item.title }}</a-button>
          <taskBtn
            v-if="taskInfo.taskId"
            :currentEditRow="currentEditRow"
            :pageHeaderButtons="publicBtn"
            v-on="$listeners"/>
        </template>
        <template slot="tags">
          <a-tag
            color="blue" 
            style="font-size:16px">
            {{ subpackageTitle }}
          </a-tag>
        </template>
      </a-page-header>
      <a-spin :spinning="confirmLoading">
        <div
          class="container"
          :style="style">
          <div>
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBLVH_7e1ecb2e`, '中标人信息') }}</span>
            </titleTrtl>
          </div>
          <div
            :is="awardName"
            :pageStatus="'detail'"
            ref="editLayout"
            :resultData="resultData">
          </div>
        </div>
      </a-spin>
      <a-modal
        v-model="auditVisible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
        :okText="okText"
        @ok="handleOk">
        <a-textarea
          v-model="opinion"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-modal>
    </div>
  </div></template>
<script lang="jsx">
import ContentHeader from '@views/srm/bidding_new/BiddingHall/components/content-header'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import {getAction, postAction} from '@/api/manage'
import normalAward from './modules/normalAward'
import materialAward from './modules/materialAward'
import supplierAward from './modules/supplierAward'
import { mapGetters } from 'vuex'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
export default {
    name: 'DetermineTheWinner',
    components: {
        titleTrtl,
        ContentHeader,
        normalAward,
        supplierAward,
        materialAward,
        taskBtn
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        },
        resultData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            tableData: [],
            formData: {},
            showHeader: true,
            height: 0,
            active: 0,
            awardName: 'normalAward',
            awardNameBtn: {
                'normalAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pRdXlB_8d34dee3`, '按供应商授标'), type: 'primary', click: () => {this.changeAwardName('supplierAward')} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pSLRHlB_e8a46290`, '按物料明细授标'), type: 'primary', click: () => {this.changeAwardName('materialAward')} }
                ],
                'materialAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pRdXlB_8d34dee3`, '按供应商授标'), type: 'primary', click: () => {this.changeAwardName('supplierAward')} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_kBCK_2dd0e2bb`, '总览模式'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ],
                'supplierAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pSLRHlB_e8a46290`, '按物料明细授标'), type: 'primary', click: () => {this.changeAwardName('materialAward')} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_kBCK_2dd0e2bb`, '总览模式'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ]
            },
            url: {
                queryById: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningFinalCandidate',
                add: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/add',
                edit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/edit',
                submit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryInfo: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningConfirmInfoBySubpackage'
            }
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        publicBtn () {
            let btn =[
                ...this.awardNameBtn[this.awardName],
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    type: 'primary',
                    click: this.auditPass,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    type: '',
                    click: this.auditReject,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    type: '',
                    click: this.showFlow,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'rollBack',
                    click: this.goBackAudit,
                    showCondition: () => {
                        return true
                    }
                }]
            return btn
        },
        ...mapGetters([
            'taskInfo'
        ])
    },
    methods: {
        changeAwardName (name) {
            this.confirmLoading = true
            // 给个切换loading，假装一下加载
            setTimeout(() => {
                this.awardName = name
                this.confirmLoading = false
            }, 50)
        },
        /**
     * 审批方法
     */
        goBackAudit () {
            this.$emit('goBackAudit')
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass () {
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject () {
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk () {
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = 'purchaseProduct'
            param['businessId'] = this.currentEditRow.businessId
            param['taskId'] = this.currentEditRow.taskId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            this.confirmLoading = true
            postAction(this.currentUrl, param)
                .then(res => {
                    if (res.success) {
                        this.auditVisible = false
                        this.$message.success(res.message)
                        this.$parent.$parent.reloadAuditList()
                        this.goBackAudit()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    created () {
        this.subpackageTitle = this.currentEditRow.subject || ''
        this.formData = this.resultData
        console.log(this.formData)
    }
}
</script>
<style lang="less" scoped>
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
</style>




