<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <set-confirm-item-modal ref="setConfirmItemModal" />
  </div>
</template>

<script lang="jsx">
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import SetConfirmItemModal from './SetConfirmItemModal'
import { getAction } from '@/api/manage'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { ajaxFindDictItems } from '@/api/api'

export default {
    name: 'EbiddingBuyEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal,
        SetConfirmItemModal
    },
    data () {
        return {
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseEbiddingItemList',
                        columns: [],
                        buttons: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                        ref: 'purchaseEbiddingSupplierList',
                        columns: [
                            { type: 'checkbox', width: 40  },
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 150 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), field: 'supplierCode', width: 150 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 200 },
                            // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'), field: 'supplierStatus_dictText', width: 200 },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'),
                                field: 'needCoordination',
                                width: 120,
                                dictCode: 'srmSupplierCoordinationWay',
                                slots: {default: 'renderDictLabel'}
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addSupplierEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteSupplierEvent}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLd_1d59643`, '确认项'), groupCode: 'confirmItem', type: 'grid', custom: {
                        ref: 'purchaseEbiddingConfirmList',
                        columns: [
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { type: 'checkbox', width: 40 },
                            { field: 'confirmDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLPCMW_713efd4f`, '确认要点描述'), width: 220, editRender: {name: '$input'} },
                            { field: 'must', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120, cellRender: {name: '$switch', props: {openValue: '1', closeValue: '0'}} },
                            { field: 'writeType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMAc_2973057e`, '填写类型'), width: 120, dictCode: 'inspection_item_write_type', editRender: {name: '$select', options: []} },
                            { field: 'confirmItemList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_URid_469b0f42`, '预制选项'), width: 120,
                                slots: {
                                    default: ({row}) =>{
                                        if (row && ['0', '1'].includes(row.writeType)) {
                                            let label =this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notSet`, '未设置')
                                            if (row.confirmItemList && row.confirmItemList.length > 0) {
                                                label =this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isSet`, '已设置')
                                            }
                                            return [
                                                <a onClick={() => this.setConfrimItem(row)}>{label}</a>
                                            ]
                                        } else {
                                            return ''
                                        }
                                    }
                                }
                            },
                            { field: 'purchaseRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), editRender: {name: '$input'} }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addConfirm},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteConfirm}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120, dictCode: 'srmFileType', editRender: {name: '$select', options: []} },
                            { field: 'stageType',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'),
                                width: 120,
                                dictCode: 'srmEbiddingStageType',
                                editRender: {
                                    // enabled: true
                                    // name: '$select', options: []
                                }, 
                                slots: {
                                    default: ({row}) => {
                                        let dictcode = 'srmEbiddingStageType'
                                        return [
                                            row['stageType'] ? this.getDictLabel(row['stageType'], dictcode) : ''
                                        ]
                                    },
                                    edit: ({row}) => {
                                        const form = this.$refs.editPage.getPageData()
                                        // 0 邀请竞价
                                        let dictcode = form.ebiddingScope === '0' ? 'srmEbidding4StageType' : 'srmEbiddingStageType'
                                        return [
                                            <m-select configData={row} getPopupContainer={triggerNode => {
                                                return triggerNode.parentNode || document.body
                                            }}
                                            v-model={row.stageType}
                                            placeholder={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')}
                                            dict-code={dictcode} />
                                        ]
                                    }
                                }
                            },
                            { field: 'required', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120, cellRender: {name: '$switch', props: {openValue: '1', closeValue: '0'}} },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220, editRender: {name: '$input'} }
                        ],
                        rules: {fileType: [{required: true, message: '必填!'}], stageType: [{required: true, message: '必填!'}]},
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), type: 'primary', click: this.addFileDemand},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFileDemand}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120, dictCode: 'fileType', editRender: {name: '$select', options: []} },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                dictCode: 'srmFileType', type: 'upload', businessType: 'ebidding',
                                attr: this.attrHandle,
                                callBack: this.uploadCallBack},
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), 
                                click: this.deleteBatch
                            }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publishEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/ebidding/purchaseEbiddingHead/add',
                edit: '/ebidding/purchaseEbiddingHead/edit',
                detail: '/ebidding/purchaseEbiddingHead/queryById',
                public: '/ebidding/purchaseEbiddingHead/createNewRound',
                upload: '/attachment/purchaseAttachment/upload',
                submit: '/a1bpmn/audit/api/submit'
            },
            optionsMap: []
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_ebidding_${templateNumber}_${templateVersion}.js`
        }
    },
    created () {
        this.getDictData()
    },
    methods: {
        attrHandle () {
            let cn = ''
            if (this.currentEditRow.ebiddingNumber) {
                cn = this.currentEditRow.ebiddingNumber
            } else {
                const params = this.$refs.editPage.getPageData()
                cn = params.ebiddingNumber
            }
            return {
                sourceNumber: cn,
                actionRoutePath: '/srm/ebidding/EbiddingBuyHeadList,/srm/ebidding/sale/EbiddingSaleHeadList'
            }
        },
        getDictData () {
            let dictCodeArr = [
                {code: 'srmEbiddingStageType', dict: 'srmEbiddingStageType'}
            ]
            dictCodeArr.map(item => {
                let postData = {
                    busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: item.dict
                }
                ajaxFindDictItems(postData).then(res => {
                    if(res.success) {
                        let options = res.result.map(item2 => {
                            return {
                                value: item2.value,
                                label: item2.text,
                                title: item2.title
                            }
                        })
                        this.optionsMap[item.code] = options
                    }
                })
            })
        },
        // 通过value显示label
        getDictLabel (value, dict) {
            let currentValueArr = value.split(',') || []
            if (dict) {
                let dictItem = this.optionsMap[dict].filter((opt) => {
                    return currentValueArr.includes(opt.value)
                }).map(item => item.label)
                return dictItem.length ? dictItem.join('；'): currentValueArr[0]
            } else {
                return value
            }
        },
        confirmItemOk (itemList) {
            this.currentRow['confirmItemList'] = itemList
        },
        addConfirm () {
            let confirmGrid = this.$refs.editPage.$refs.purchaseEbiddingConfirmList[0]
            confirmGrid.insertAt({ must: '0' }, -1)
        },
        deleteConfirm () {
            let confirmGrid = this.$refs.editPage.$refs.purchaseEbiddingConfirmList[0]
            let checkboxRecords = confirmGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            confirmGrid.removeCheckboxRow()
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        setConfrimItem (row) {
            this.currentRow = row
            this.$refs.setConfirmItemModal.open(row)
        },
        fieldSelectOk (data) {
            let supplierGrid = this.$refs.editPage.$refs.purchaseEbiddingSupplierList[0]
            let { fullData } = supplierGrid.getTableData()
            let supplierList = fullData.map(item => {
                return item.toElsAccount
            })
            // 过滤已有数据
            let insertData = data.filter(item => {
                return !supplierList.includes(item.toElsAccount)
            })
            insertData = insertData.map(item => {
                return {
                    toElsAccount: item.toElsAccount,
                    supplierCode: item.supplierCode,
                    supplierName: item.supplierName,
                    supplierStatus_dictText: item.supplierStatus_dictText
                }
            })
            supplierGrid.insertAt(insertData, -1)
        },
        addSupplierEvent () {
            let url = '/supplier/supplierMaster/list'
            let columns = [
                {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150},
                {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 200},
                {field: 'supplierStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'), width: 200},
                {field: 'cateName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'), width: 200}
            ]
            // 获取供应商范围参数
            const form = this.$refs.editPage.getPageData()
            const { supplierScope = '' } = form
            this.$refs.fieldSelectModal.open(url, { supplierStatus: supplierScope, frozenFunctionValue: '2' }, columns, 'multiple')
        },
        deleteSupplierEvent () {
            let supplierGrid = this.$refs.editPage.$refs.purchaseEbiddingSupplierList[0]
            let checkboxRecords = supplierGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            supplierGrid.removeCheckboxRow()
        },
        addFileDemand () {
            let demandGrid = this.$refs.editPage.$refs.purchaseAttachmentDemandList[0]
            demandGrid.insertAt({ required: '0' }, -1)
        },
        deleteFileDemand () {
            let demandGrid = this.$refs.editPage.$refs.purchaseAttachmentDemandList[0]
            let checkboxRecords = demandGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            demandGrid.removeCheckboxRow()
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        // 批量删除
        deleteBatch () {
            let arr = []
            let delArr = []
            //大B
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if(checkboxRecords && checkboxRecords.length>0){
                checkboxRecords.forEach(row=> {
                    //当前登录人等账号于查询的账号 大B
                    if (user == row.uploadElsAccount) {
                        if( subAccount==row.uploadSubAccount){
                            delArr.push(row)
                        }else{
                            arr.push(row.fileName)
                        }
                    }else{
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if(arr && arr.length>0){
                    let str = arr.join(',')
                    this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') +str+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        publishEvent () {
            this.$refs.editPage.handleSend()
        }
    }
}
</script>