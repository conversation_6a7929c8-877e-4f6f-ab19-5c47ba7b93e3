/*
 * @Author: your name
 * @Date: 2021-03-29 14:02:20
 * @LastEditTime: 2021-07-15 11:51:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\store\index.js
 */
import Vue from 'vue'
import Vuex from 'vuex'

import app from './modules/app'
import user from './modules/user'
import permission from './modules/permission'
import purchaseEnquiry from './modules/purchaseEnquiry'
import getters from './getters'
import formDesigner from './modules/formDesigner'
import module_cart from './modules/module_cart'
import switchLanguage from './modules/language'
import ebiddingWSOnline from './modules/ebiddingWSOnline'
import message from './modules/message'

Vue.use(Vuex)

export default new Vuex.Store({
    modules: {
        app,
        user,
        permission,
        purchaseEnquiry,
        formDesigner,
        module_cart,
        switchLanguage,
        ebiddingWSOnline,
        message
    },
    state: {

    },
    mutations: {

    },
    actions: {

    },
    getters
})
