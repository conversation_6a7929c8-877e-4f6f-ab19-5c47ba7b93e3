<template>
  <a-modal
    centered
    :footer="null"
    :mask-closable="false"
    :title="title"
    :visible="visible"
    :width="1120"
    @cancel="visible = false">
    <div
      v-if="visible"
      id="his-chart"
      style="height: 330px; width: 1000px"/>
    <a-row :gutter="12">
      <a-col :span="8">
        <m-select
          style="width: 240px"
          v-model="form.priceType"
          :options="typeOptions"
          v-show="!showCost"
          @change="getData"/>
      </a-col>
      <a-col :span="8">
        <m-select
          style="width: 240px"
          title-map="ladder"
          value-map="ladderQuantity"
          v-model="form.ladderQuantity"
          v-show="showLadder"
          :options="ladderOptions"
          @change="getData"/>
      </a-col>
      <a-col :span="8">
        <m-select
          style="width: 240px"
          title-map="groupName"
          value-map="groupCode"
          v-model="form.costGroup"
          v-show="showCost"
          :options="costOptions"
          @change="getData"/>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script lang="jsx">
import {getAction} from '@api/manage'
import * as echarts from 'echarts'

export default {
    data (){
        return{
            costOptions: [],
            form: {
                priceType: 'price'
            },
            ladderOptions: [],
            options: {
                grid: {bottom: '5%', containLabel: true, left: '5%', top: '90'},
                legend: {data: [], top: '30', type: 'scroll'}, 
                series: [],
                toolbox: {
                    feature: {
                        magicType: {
                            title: {
                                bar: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_FSLdzP_96f476fa`, '切换为柱状图'),
                                line: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_FSLNWP_96e1d8f8`, '切换为折线图')
                            },
                            type: ['line', 'bar']
                        },
                        restore: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_Sj_11bec7`, '还原') },
                        saveAsImage: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_sMLPO_902181c8`, '保存为图片') }
                    }
                },
                tooltip: {trigger: 'item'},
                xAxis: {
                    boundaryGap: true,
                    data: [],
                    axisLabel: {
                        formatter: '{value} 次'
                    },
                    name: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_frequencyn`, '次数'),
                    type: 'category'
                },
                yAxis: {
                    name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_um_9f825`, '价格'),
                    type: 'value'
                }
            },
            showCost: false,
            showLadder: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trendChartOfQuotation`, '本次报价趋势图'),
            typeOptions: [
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'), value: 'price'},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'), value: 'netPrice'}
            ],
            visible: false
        }
    },
    methods: {
        getData () {
            getAction('/enquiry/purchaseEnquiryItemHis/queryQuoteHisTrend', this.form).then(res => {
                const { legendData = [], series = [], xAxisData = [], xaxisData = [] } = res.result || {}
                if (!series || series.length == 0) {
                    return this.$message.error('暂无数据')
                }
                this.$set(this.options.legend, 'data', legendData)
                series.map(item => {
                    item['label'] =  {
                        show: true,
                        position: 'top',
                        formatter: '{c}'
                    }
                })
                let x = xAxisData.length > 0 ? xAxisData : xaxisData
                this.$set(this.options, 'series', series)
                this.$set(this.options.xAxis, 'data', x)
                this.visible = true
                this.$nextTick(() => {
                    this.initChart()
                })
            })
        },
        initChart (){
            const chart = echarts.init(document.getElementById('his-chart'))
            chart.setOption(this.options)
        },
        open ({headId, row}){
            this.$set(this.form, 'headId', headId)
            this.$set(this.form, 'itemNumber', row.itemNumber)
            this.showLadder = false
            this.showCost = false
            if(row.quotePriceWay === '1'){
                this.ladderOptions = JSON.parse(row.ladderPriceJson)
                const ladderQuantity = this.ladderOptions ? this.ladderOptions[0].ladderQuantity : ''
                this.$set(this.form, 'ladderQuantity', ladderQuantity)
                this.showLadder = true
            }else if(row.quotePriceWay === '2'){
                this.costOptions = JSON.parse(row.costFormJson).groups
                console.log(this.costOptions)
                const costGroup = this.costOptions ? this.costOptions[0].groupCode : ''
                this.$set(this.form, 'costGroup', costGroup)
                this.showCost = true
            }
            this.getData()
        }
    },
    name: 'TrendChartOfRoundQuotations'
}
</script>
