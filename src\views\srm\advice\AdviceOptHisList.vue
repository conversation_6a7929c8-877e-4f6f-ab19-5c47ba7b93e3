<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url" />
    <!-- 查看 -->
    <ViewAdviceModal
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>
</template>
<script>
import ViewAdviceModal from './modules/ViewAdviceModal'
import { ListMixin } from '@comp/template/list/ListMixin'

export default {
    mixins: [ListMixin],
    components: {
        ViewAdviceModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input', 
                        label: '关键字',
                        fieldName: 'keyWord', 
                        placeholder: '请输入单号/标题'
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView}
                ],
                form: {
                    keyWord: ''
                }
            },
            showAbnormalPage: false,
            url: {
                list: '/advice/complaintAdviceOperateRecord/list',
                columns: 'complaintAdviceOperateRecord'
            }
        }
    },
    methods: {
        handleView (row){
            this.currentEditRow = row
            this.currentEditRow['id'] = row.adviceId
            this.showDetailPage = true
        }
    }
}
</script>