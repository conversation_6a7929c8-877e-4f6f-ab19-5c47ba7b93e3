<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 编辑界面 -->
    <ebidding-sale-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <ebidding-sale-detail 
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>
</template>

<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import EbiddingSaleEdit from './modules/EbiddingSaleEdit'
import EbiddingSaleDetail from './modules/EbiddingSaleDetail'
import layIM from '@/utils/im/layIM.js'
export default {
    mixins: [ListMixin],
    components: {
        'ebidding-sale-edit': EbiddingSaleEdit,
        'ebidding-sale-detail': EbiddingSaleDetail
    },
    data () {
        return {
            pageData: {
                form: {
                    ebiddingDesc: '',
                    ebiddingNumber: '',
                    ebiddingStatus: undefined
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号'),
                        fieldName: 'ebiddingNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterBidDocumentNo`, '请输入竞价单号')
                    },
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaDesc`, '单据描述'),
                        fieldName: 'ebiddingDesc',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFtFMW_6ebaa131`, '请选择单据描述')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidResponse`, '应标'), clickFn: this.handleEdit, allow: this.allowEdit},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_OufY_390dbcd7`, '竞价大厅'), clickFn: this.openLobby, allow: this.allowOpenLobby},
                    {authorityCode: 'ebidding#saleEbiddingHead:chat', type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat}
                ]
            },
            url: {
                list: '/ebidding/saleEbiddingHead/list',
                delete: '/ebidding/saleEbiddingHead/deleteBatch',
                columns: 'saleEbiddingHead' 
            },
            countTabsUrl: '/ebidding/saleEbiddingHead/count'
        }
    },
    mounted () {
        this.serachCountTabs(this.countTabsUrl)
    },
    methods: {
        openLobby (row){
            if (row.ebiddingMethod === '0' || !row.ebiddingMethod) {
                // this.$router.push({
                //     path: '/ebidding/saleLobbyNew',
                //     query: {
                //         id: row.id,
                //         ebiddingNumber: row.ebiddingNumber,
                //         currentItemNumber: row.currentItemNumber || '1',
                //         relationId: row.relationId
                //     }
                // })
                window.open(`${window.origin}/ebidding/saleLobbyNew?id=${row.id}&ebiddingNumber=${row.ebiddingNumber}&currentItemNumber=${row.currentItemNumber}&relationId=${row.relationId}&busAccount=${row.busAccount}`, '_blank')
            } else if (row.ebiddingMethod === '1') {
                // this.$router.push({
                //     path: '/ebidding/saleLobbyNewJap',
                //     query: {
                //         id: row.id,
                //         ebiddingNumber: row.ebiddingNumber,
                //         currentItemNumber: row.currentItemNumber || '1',
                //         relationId: row.relationId
                //     }
                // })
                window.open(`${window.origin}/ebidding/saleLobbyNewJap?id=${row.id}&ebiddingNumber=${row.ebiddingNumber}&currentItemNumber=${row.currentItemNumber}&relationId=${row.relationId}&busAccount=${row.busAccount}`, '_blank')
            } else if (row.ebiddingMethod === '2' || row.ebiddingWay === '3') { // 批量 或 一次性
                window.open(`${window.origin}/ebidding/saleLobbyNewDutch?id=${row.id}&ebiddingNumber=${row.ebiddingNumber}&currentItemNumber=${row.currentItemNumber}&relationId=${row.relationId}&busAccount=${row.busAccount}`, '_blank')
            }
        },
        //
        allowOpenLobby ({ebiddingStatus}){
                 
            return ebiddingStatus !== '3' && ebiddingStatus !== '4'
        },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.ebiddingNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SaleEbiddingBuy', url: this.url || '', recordNumber})
        },
        
        allowEdit ({ ebiddingStatus }) {
            //待应标状态
            return ebiddingStatus != '1'
        }
    }
}
</script>