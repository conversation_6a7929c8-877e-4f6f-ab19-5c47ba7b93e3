<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      modelLayout="seal"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <ElsSealsEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <ElsSealsDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <ElsSealsAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>

</template>
<script>
// 新列表模板
import { ListMixin } from './list/ListMixin'
// import { ListMixin } from '@comp/template/list/ListMixin'
import ElsSealsAdd from './modules/ElsSealsAdd'
import ElsSealsEdit from './modules/ElsSealsEdit'
import ElsSealsDetail from './modules/ElsSealsDetail'
// import { postAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        ElsSealsEdit,
        ElsSealsDetail,
        ElsSealsAdd
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary'}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCRLWWeqR_511a0e32`, '(公司名称/印章别名)')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, style: {'color': '#f5222d'}}
                ]
            },
            url: {
                add: '/esign/elsSeals/add',
                list: '/esign/elsSeals/list',
                delete: '/esign/elsSeals/delete',
                columns: 'ElsSealsList'
            }
        }
    },
    methods: {
        //已认证不能被编辑
        handleAdd () {
            this.showAddPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
        },
        allowEdit (row){
            if(row.sealId){
                return true
            }
            return false
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        allowDelete (row){
            if(row.sealId){
                return true
            }
            return false
        }
    }
}
</script>