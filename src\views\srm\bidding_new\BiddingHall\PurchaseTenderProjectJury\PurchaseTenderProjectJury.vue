<template>
  <div style="height: 100%">
    <a-spin :spinning="confirmLoading">
      <setp-lay-out
        v-if="show"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :pageFooterButtons="pageFooterButtons"
        :pageHeaderButtons="pageHeaderButtons"
        :sourceGroups="sourceGroups"
        :pageStatus="pageStatus"
        @nextStepHandle="nextStepHandle"
        @preStepHandle="preStepHandle"
        :fromSourceData="fromSourceData">
        <template #baseForm="{ slotProps }">
          <baseForm
            ref="baseForm"
            :pageStatus="pageStatus"
            :fromSourceData="fromSourceData"
          />
        </template>
        <template #juryMember="{ slotProps }">
          <juryMember
            ref="juryMember"
            :pageStatus="pageStatus"
            :fromSourceData="fromSourceData"
            @setLoading="setLoading"
            :root="root"
          />
        </template>
        <!-- <template #attachment="{ slotProps }">
          <attachment
            ref="attachment"
            :pageStatus="pageStatus"
            :fromSourceData="fromSourceData"
          />
        </template> -->
      </setp-lay-out>
    </a-spin>
  </div>
</template>

<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'

import setpLayOut from '@views/srm/bidding_new/BiddingHall/components/setpLayOut'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import baseForm from './components/baseForm'
import juryMember from './components/juryMember'
// import attachment from './components/attachment'
export default {
    name: 'PurchaseTenderProjectJury',
    components: {
        setpLayOut,
        baseForm,
        juryMember
        // attachment
    },
    mixins: [baseMixins],
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        },
        pageStatus () {
            // let { resultResponseStatus, preResponseStatus } = this.fromSourceData
            // let status = 'detail'
            // if ((resultResponseStatus != '1' && this.checkType == '1') || (preResponseStatus != '1' && this.checkType == '0')) {
            //     // 0 递交文件可以操作
            //     status = 'edit'
            // }
            let {headStatus} = this.fromSourceData
            let status = 'edit'
            if(headStatus == '1' ||  this.$ls.get('SET_TENDERCURRENTROW').applyRole == 0){
                status = 'detail'
            }
            return status
        },
        pageHeaderButtons () {
            const { status } = this.currentSubPackage()
            const notStatusList = [2310, 2320, 2330, 4110, 4120, 4130, 4310, 4320, 4330, 4510, 4520, 4530]
            const statusList = [2320, 4520, 4120, 4320]
            if (this.fromSourceData.headStatus == '1' && (statusList.includes(status) || !notStatusList.includes(status))) {
                return [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ULMAH_db95ee26`, '评委会变更'),
                        attrs: {
                            type: 'primary'
                        },
                        key: 'jury',
                        click: this.juryChange
                    }
                ]
            }
            return []
        }
    },
    data () {
        return {
            root: this,
            flowView: false,
            flowId: 0,
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            refresh: true,
            show: false,
            sourceGroups: [
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ULMVH_db956cb8`, '评委会信息'),
                    groupNameI18nKey: '',
                    groupCode: 'baseForm',
                    groupType: 'head',
                    show: true,
                    sortOrder: '1'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ULMLj_db979452`, '评委会成员'),
                    groupNameI18nKey: '',
                    groupCode: 'juryMember',
                    groupType: 'item',
                    show: true,
                    sortOrder: '2'
                }
                // {
                //     groupName: '附件',
                //     groupNameI18nKey: '',
                //     groupCode: 'attachment',
                //     groupType: 'item',
                //     show: true,
                //     sortOrder: '3'
                // }
            ],
            businessRefName: 'businessRef',
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.saveEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    // args: {
                    //     url: '/tender/tenderProjectAttachmentInfo/publish'
                    // },
                    attrs: {
                        type: 'primary'
                    },
                    click: this.handlePublish
                }
            ],
            currentGroupCode: {
                groupCode: 'baseForm'
            },
            currentEditRow: {},
            status: '',
            confirmLoading: false,
            fromSourceData: {},
            userInfo: {},
            urlParams: {},
            decodeFileKey: false,
            uKeyInfo: null,
            url: {
                detail: '/tender/jury/purchaseTenderProjectJuryHead/queryByCondition',
                add: '/tender/jury/purchaseTenderProjectJuryHead/add',
                edit: '/tender/jury/purchaseTenderProjectJuryHead/edit',
                publish: '/tender/jury/purchaseTenderProjectJuryHead/publish',
                juryChange: '/tender/jury/purchaseTenderProjectJuryHead/change'
            }
        }
    },
    methods: {
        // 评委会变更
        juryChange () {
            const params = {
                id: this.fromSourceData.id
            }
            // params.templateNumber = params.templateNumber || this.currentEditRowData.templateNumber || ''
            // params.templateVersion = params.templateVersion || this.currentEditRowData.templateVersion || ''
            // params.templateAccount = params.templateAccount || this.currentEditRowData.templateAccount || ''
            // params.templateName = params.templateName || this.currentEditRowData.templateName || ''
            this.confirmLoading = true
            postAction(this.url.juryChange, params)
                .then((res) => {
                    const resType = res.success ? 'success' : 'error'
                    this.$message[resType](res.message)
                    if (res.code == 200) {
                        // this.$emit('resetCurrentSubPackage') || ''
                        this.resetCurrentSubPackage()
                        this.queryDetail()
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        queryDetail (cb) {
            this.confirmLoading = true
            this.show = false
            getAction(this.url.detail, { subpackageId: this.subId})
                .then(async (res) => {
                    if (res.success) {
                        this.fromSourceData = res.result || {}
                        if (res.result) {
                            //适应前端组件的数据回显形式(将后端返回的字符串转化为前段需要的数组形式)
                            this.fromSourceData.juryConditionList.forEach((item)=>{
                                item.addressCode=item.addressCode ? item.addressCode.split(',') : []
                            })
                            res.result.purchaseTenderProjectJuryMemberList.map(item=>{
                                return item.subAccount = item.elsSubAccount
                            })
                            // 类型为0的就是代表  类型为1的就是专家；筛选出来后放到对应表
                            this.fromSourceData.tenderAdmin = res.result.purchaseTenderProjectJuryMemberList.filter((item) => item.memberType == '0')
                            this.fromSourceData.evaluationExperts = res.result.purchaseTenderProjectJuryMemberList.filter((item) => item.memberType == '1')
                            this.fromSourceData.confirmWay = this.fromSourceData.samplingWay == '0' ? '':this.fromSourceData.confirmWay
                            console.log('this.fromSourceData', this.fromSourceData)
                            
                        } else {
                            this.fromSourceData.status = '0'
                        }
                        if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') this.externalToolBar = {}
                    } else {
                        this.$message.warning(res.message)
                    }
                    // this.currentEditRowData = res.result && res.result.templateNumber && res.result.templateAccount ? {
                    //     templateNumber: res.result.templateNumber,
                    //     templateName: res.result.templateName,
                    //     templateVersion: res.result.templateVersion,
                    //     templateAccount: res.result.templateAccount
                    // } : await this.getBusinessTemplate('tenderJury')
                    // this.remoteJsFilePath = `${this.currentEditRowData['templateAccount']}/purchase_tenderJury_${this.currentEditRowData['templateNumber']}_${this.currentEditRowData['templateVersion']}`
                    if (cb) cb()
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.show = true
                })
        },
        setCurrentStep (i) {
            this.$refs[this.businessRefName].currentStep = i
        },
        // 获取数据和校验
        async getParamsName () {
            let params = {
            }
            return params
        },
        handlePublish () {
            // if(!this.fromSourceData.id){
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
            //     return
            // }
            let params = this.getParams()
            let forbidden = false
            if(params.juryMemberRecordList && params.juryMemberRecordList.length != 0){
                params.juryMemberRecordList.forEach(item=>{
                    // 抽取方式线上同时线上抽取的专家为确认是否参加，不执行发布操作
                    if(params.samplingWay == '1' && item.confirmStatus == '0'){
                        forbidden = true
                    }
                })
            }
            if(forbidden){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWRLVMsuKQsu_cc0ea69`, '请先确认抽取专家是否参加！'))
                
                return false
            }
            this.confirmLoading = true

            postAction(this.url.publish, params).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    // 刷新
                    this.queryDetail()
                    
                }else{
                    this.$message.error(res.message)
                }
            }).finally(()=>{
                this.confirmLoading = false
            })
        },
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef(this.currentGroupCode)
            itemGrid.insertAt([...data], -1)
        },
        // 校验
        getAllValidate (refNames) {
            const handlePromise = (list = []) =>
                list.map((promise) =>
                    promise.then(
                        (res) => {
                            console.log(res)
                            return{
                                status: 'success',
                                res
                            }
                        },
                        (err) => {
                            console.log(err)
                            return {
                                status: 'error',
                                err
                            }
                        }
                    )
                )
            let promiseValidateArr = refNames.map((ref) => this.$refs[ref].getValidatePromise()).filter((promise) => promise)
            return new Promise((resolve, reject) => {
                Promise.all(handlePromise(promiseValidateArr))
                    .then((result) => {
                        let currentStep = null
                        let flag = true
                        for (let i = 0; i < result.length; i++) {
                            if (result[i].status === 'error') {
                                currentStep = i
                                flag = false
                            }
                        }
                        if (flag) {
                            let resolveData = { validStatus: true, currentStep: currentStep, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiLR_48599c04`, '验证成功') }
                            resolve(resolveData)
                        } else {
                            let resolveData = { validStatus: false, currentStep: currentStep, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiKmWVGv_95d29236`, '验证失败，请处理') }
                            resolve(resolveData)
                        }
                    }, (err) => {
                        console.log(err)
                    })
                    .catch((err) => {
                        console.log(err)
                        reject(err)
                    })
            })
        },
        async nextStepHandle (data) {
            this.currentGroupCode = data.groupData
        },
        preStepHandle (data) {
            this.currentGroupCode = data.groupData
        },
        childrenInitData (data) {
            this.sourceGroups.map((ref) => {
                this.$refs[ref.groupCode].init(data)
            })
        },
        getParams () {
            let target = []
            let params = Object.assign({
                subpackageId: this.subId,
                tenderProjectId: this.tenderCurrentRow.id
            }, this.fromSourceData)
            console.log('fromSourceData', this.fromSourceData)
            console.log('params', params)
            let {tenderAdmin} = this.$refs.juryMember.externalAllData()
            params['tenderAdmin'] = tenderAdmin
            let {evaluationExpert} = this.$refs.juryMember.externalAllData()
            params['evaluationExperts'] = evaluationExpert
            params['purchaseTenderProjectJuryMemberList'] = tenderAdmin.length == 0 ? [] : tenderAdmin
            // 抽取方式为线下时，提交评标专家列表
            if(params.samplingWay == '0'){
                // params['purchaseTenderProjectJuryMemberList']=[...tenderAdmin, ...params.evaluationExperts]
                params['purchaseTenderProjectJuryMemberList'] = params.evaluationExperts? [...params['purchaseTenderProjectJuryMemberList'], ...params.evaluationExperts] :  params['purchaseTenderProjectJuryMemberList']
            }else if(params.samplingWay == '1' && params.juryMemberRecordList){
                // 抽取方式为线上时，提交抽取结果为确认参加的专家列表
                // params['purchaseTenderProjectJuryMemberList']=[...tenderAdmin, ...params.juryMemberRecordList]
                params.juryMemberRecordList.forEach(item=>{
                    // 已确认参加，则塞到待提交的参数中
                    if(item.confirmStatus == '1'){
                        target.push(item)
                    }
                })
                params['purchaseTenderProjectJuryMemberList'] = target? [...params['purchaseTenderProjectJuryMemberList'], ...target] : params['purchaseTenderProjectJuryMemberList']
                console.log(params.juryMemberRecordList)
            }
            // if(tenderAdmin && params.evaluationExperts){
            //     params['purchaseTenderProjectJuryMemberList']=[...tenderAdmin, ...params.evaluationExperts]
            // }
            

            console.log(params)
            console.log(params['juryConditionList'])
            // 将级联地址从前端的数组形式转化成后端所需的字符串形式
            if(params['juryConditionList']){
                params['juryConditionList'].forEach(item=>{
                    console.log('还是进来了')
                    if(item.addressCode && item.addressCode.constructor == Array){
                        item.addressCode=item.addressCode.join(',')
                        console.log(item.addressCode)
                    }
                })
            }
            params.juryType = this.currentNode().extend.checkType
            return params
        },
        saveEvent () {
            let refs = this.sourceGroups.map((ref) => ref.groupCode)
            this.getAllValidate(refs).then(res => {
                if (res.validStatus) {
                    let url = this.fromSourceData.id ? this.url.edit : this.url.add
                    let params = this.getParams()
                    console.log('params2', params)
                    
                    
                    this.confirmLoading = true
                    postAction(url, params).then((res) => {
                        if (res.success) {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            
                            // 刷新
                            this.queryDetail()
                            
                            
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                } else {
                    this.setCurrentStep(res.currentStep)
                }
            })
        },
        setLoading (flag) {
            this.confirmLoading = flag
        }
    },
    created () {
        this.queryDetail()
    }
}
</script>
