<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <Org-Info-Modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
  </div>
</template>

<script>
import OrgInfoModal from './modules/OrgInfoModal'
import {listPageMixin} from '@comp/template/listPageMixin'

export default {
    name: 'OrgInfoList',
    mixins: [listPageMixin],
    components: {
        OrgInfoModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input', 
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationName`, '组织名称'),
                        fieldName: 'orgName', 
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterOrganizationName`, '请输入组织名称')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/base/orgInfo/list',
                delete: '/base/orgInfo/delete',
                deleteBatch: '/base/orgInfo/deleteBatch',
                exportXlsUrl: '/base/orgInfo/exportXls',
                importExcelUrl: '/base/orgInfo/importExcel',
                columns: 'baseOrgInfoList'          
            }
        }
    },
    computed: {

    },
    created () {

    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationInfo`, '组织信息'))
        }
    }
}
</script>
