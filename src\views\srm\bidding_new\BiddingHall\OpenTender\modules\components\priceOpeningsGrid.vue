<template>
  <div>
    <vxe-grid
        :height="300"
        ref="table"
        v-bind="gridConfig"
        :scroll-y="{enabled: false}"
        :data="tableData"
        :columns="tableColumns"
        :span-method="mergeRowMethod"
        show-overflow="title" />
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
export default {
    mixins: [tableMixins],
    props: {
        priceOpeninData: {
            default: () => {
                return []
            },
            type: Array
        }
    },
    data () {
        return {
            tableColumns: [],
            tableData: []
        }
    },
    methods: {
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            const fields = ['supplierName']
            const cellValue = row[column.property]
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow[column.property] === cellValue) {
                    return { rowspan: 0, colspan: 0 }
                } else {
                    let countRowspan = 1
                    while (nextRow && nextRow[column.property] === cellValue) {
                        nextRow = visibleData[++countRowspan + _rowIndex]
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 }
                    }
                }
            }
        }
    },
    created () {
        // 列
        if (this.priceOpeninData.length > 0 && this.priceOpeninData[0].customizeFieldModel) {
            let columns = JSON.parse(this.priceOpeninData[0].customizeFieldModel)
            this.tableColumns = []
            columns.map(item => {
                let columnItem = {
                    'title': item.title,
                    'field': item.field
                }
                if (item.type == 'dictCode') {
                    columnItem['field'] = item.field + '_dictText'
                }
                this.tableColumns.push(columnItem)
            })
        }
        // 总项报价逻辑
        if(this.priceOpeninData[0].bidLetterFormatGroup.quoteType == '0'){
            this.tableData = this.priceOpeninData.map(({customizeFieldData, supplierName}) => {
                let row = customizeFieldData ? JSON.parse(customizeFieldData) : {supplierName}
                return row[0]
            })
        }else{
            //分项报价逻辑
            this.priceOpeninData.forEach(({customizeFieldData, supplierName})=>{
                let row = customizeFieldData ? JSON.parse(customizeFieldData) : {supplierName}
                this.tableData.push(...row)
            })
        }
    }
}
</script>

