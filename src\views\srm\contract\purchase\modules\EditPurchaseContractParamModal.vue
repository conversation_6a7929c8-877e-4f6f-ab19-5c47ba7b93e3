<template>
  <div class="els-page-comtainer">
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      @goBack="goBack"
    />
  </div>
</template>

<script>
import { duplicateCheck } from '@/api/api'
import { tileEditPageMixin } from '@comp/template/tileStyle/tileEditPageMixin'

export default {
    name: 'EditPurchaseContractParamModal',
    mixins: [tileEditPageMixin],
    data () {
        return {
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractParams`, '合同参数'),
                form: {
                    paramName: '',
                    paramType: '1',
                    paramContent: ''
                },
                panels: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            ref: 'baseForm',
                            colSpan: '24',
                            type: 'form',
                            form: {},
                            list: [
                                {
                                    type: 'select', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paramType`, '参数类型'),
                                    fieldName: 'paramType', 
                                    dictCode: 'srmParamType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectParamsTypeTips`, '请选择参数类型')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paramName`, '参数名称'),
                                    fieldName: 'paramName', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterParamsName`, '请输入参数名称')
                                },
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paramContent`, '参数内容'),
                                    fieldName: 'paramContent', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterParamContent`, '请输入参数内容')
                                }
                            ]}}
                ],
                validRules: {
                    paramName: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}],
                    paramType: [{max: 20, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow20`, '内容长度不能超过20个字符')}]
                }
            },
            url: {
                add: '/contract/purchaseContractParam/add',
                edit: '/contract/purchaseContractParam/edit',
                detail: '/contract/purchaseContractParam/queryById'
            }
        }
    },
    methods: {
        validateCode (rule, value, callback) {
            // 重复校验
            var params = {
                tableName: '',
                fieldName: '',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then(res => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        selectCallBack (item) {
            this.pageData.form.dictCode = item[0].dictCode
            this.$refs.editPage.$forceUpdate()
        }
    }
}
</script>

<style lang="less" scoped>
</style>