/*
 * @Author: fzb
 * @Date: 2022-03-01 14:20:56
 * @LastEditTime: 2022-03-10 15:13:56
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\components\designer\widget\mixins\chartsMixins.js
 */
import { renderWidgetData } from '@comp/chart/widget/utils/dataUtils'
export const chartsMixins = {
    props: {
        widget: {
            type: [Object],
            default: () => { }
        }
    },
    data () {
        return {
            render: null
        }
    },
    computed: {
        style () {
            return {
                width: this.widget.location.width + 'px',
                height: this.widget.location.height + 'px'
            }
        },
        option () {
            return this.widget.option.series[0].customRadius
        },
        data () {
            return this.widget.data
        },
        refresh () {
            return this.widget.service.timestamp
        }
    },
    watch: {
    // option: {
    //   handler(val) {
    //     this.widget.option.series[0].radius[0] = val[0] + "%";
    //     this.widget.option.series[0].radius[1] = val[1] + "%";
    //   },
    //   deep: true
    // },
        data: {
            handler () {
                this.refreshWidgetInfo()
            },
            deep: true
        },
        refresh: {
            handler () {
                this.refreshWidgetInfo()
            }
        }
    },
    mounted () {
        this.refreshWidgetInfo()
    },
    destroyed () {
        this.recoveryRenderObject()
    },
    methods: {
        refreshWidgetInfo () {
            this.recoveryRenderObject()
            this.render = renderWidgetData(this.widget, this.refreshWidgetData)
        },
        refreshWidgetData (data) {
            const option = this.widget.option
            option.series[0].data = data
        },
        recoveryRenderObject () {
            if (this.render && this.render.apiTimer) {
                clearInterval(this.render.apiTimer)
                this.render.apiTimer = null
            }
            if (this.render && this.render.sqlTimer) {
                clearInterval(this.render.sqlTimer)
                this.render.sqlTimer = null
            }
            if (this.render && this.render.websocket) {
                this.render.websocket.doDisconnect()
            }
        }
    }
}