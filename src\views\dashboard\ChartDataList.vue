<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <chartData-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="modalForm"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import ChartDataModal from './modules/ChartDataModal'
import {listPageMixin} from '@comp/template/listPageMixin'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    mixins: [listPageMixin],
    components: {
        ChartDataModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: srmI18n(`${getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseEnterCodeDescription`, '请输入编码或描述')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/report/dashboard/chartData/list',
                delete: '/report/dashboard/chartData/delete',
                deleteBatch: '/report/dashboard/chartData/deleteBatch',
                exportXlsUrl: '/report/dashboard/chartData/exportXls',
                importExcelUrl: 'dashboard/chartData/importExcel',
                columns: 'chartDataList'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(srmI18n(`${getLangAccount()}#i18n_title_chartData`, '图表数据'))
        }
    }
}
</script>