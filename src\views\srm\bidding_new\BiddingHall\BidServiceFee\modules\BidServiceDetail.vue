<template>
  <div class="page-container">
    <div
      class="edit-page"
      v-if="show">
      
      <a-spin :spinning="confirmLoading">
        
        <a-page-header
          v-if="taskInfo.taskId"
        >
          <template
            slot="extra">
            <taskBtn
              :currentEditRow="currentEditRow"
              :pageHeaderButtons="publicBtn"
              v-on="$listeners"/>
          </template>
        </a-page-header>
        <content-header
          v-else-if="showHeader"
          :btns="btns" />
        <titleCrtl>
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_baseform`, '基本信息') }}</span>
        </titleCrtl>
        <div>
          <Dataform
            ref="dataform"
            :formData="formData"
            :pageStatus="pageStatus"
            :validateRules="validateRules"
            :fields="fields"> </Dataform>
        </div>
        <titleCrtl>
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_BSuJptH_537c47e9`, '服务费缴纳记录') }}</span>
          <template slot="right">
            <a-button
              v-if="(formData.status != '2' && formData.auditStatus != '1' && formData.auditStatus != '2')"
              @click="addInviteItem"
              type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_title_new`, '新增') }}</a-button>
          </template>
        </titleCrtl>
        <list-table
          ref="purchaseTenderBidWinningServiceFeeItemList"
          :statictableColumns="statictableColumns"
          :pageData="pageData"
          :pageStatus="pageStatus"
          setGridHeight="500"
          :fromSourceData="formData.purchaseTenderBidWinningServiceFeeItemList"
          :showTablePage="false"> </list-table>
        <a-modal
          v-model="auditVisible"
          :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
          :okText="okText"
          @ok="handleOk">
          <a-textarea
            v-model="opinion"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
            :auto-size="{ minRows: 3, maxRows: 5 }"
          />
        </a-modal>
        <flowViewModal
          v-model="flowView"
          :flowId="String(flowId)" />
      </a-spin>
    </div>
  </div>
</template>
<script lang="jsx">
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import listTable from '../../components/listTable'
import ContentHeader from '../../components/content-header'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import Dataform from '../../components/Dataform'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { USER_INFO } from '@/store/mutation-types'
import flowViewModal from '@comp/flowView/flowView'
import { mapGetters } from 'vuex'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
 
export default {
    mixins: [tableMixins],
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        projectMemberPermission: {
            type: Boolean,
            default: true
        }
    },
    components: {
        titleCrtl,
        listTable,
        ContentHeader,
        Dataform,
        flowViewModal,
        taskBtn
    },
    data () {
        return {
            auditVisible: false,
            okText: '',
            opinion: '',
            publicBtn: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    type: 'primary',
                    click: this.auditPass,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    type: '',
                    click: this.auditReject,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    type: '',
                    click: this.showFlow,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'rollBack',
                    click: this.goBackAudit,
                    showCondition: () => {
                        return true
                    }
                }
            ],
            showHeader: true,
            alreadyCancel: false,
            flowId: 0,
            flowView: false,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            show: true,
            fromSourceData: [],
            validateRules: {},
            formData: {
                
            },
            confirmLoading: false,
            tableData: [],
            pageData: {
                optColumnList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        allow: this.delDisabled,
                        clickFn: this.handleDeleteFile
                    }
                ]
            },
            statictableColumns: [
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JpCK_3bbb12b5`, '缴纳方式'),
                    field: 'paymentType',
                    fieldType: 'select',
                    required: '1',
                    dictCode: 'tenderBidWinningServiceFeeItemPaymentType'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JpHfWjW_7a343c23`, '缴纳金额'),
                    field: 'paymentAmount',
                    fieldType: 'input',
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                    field: 'attachmentDTOList',
                    required: '0',
                    slots: {
                        default: ({ row, column }) => {
                            console.log(row, column)
                            if (row.attachmentDTOList) {
                                console.log('jinlaile')
                                let fileListDoms = row.attachmentDTOList.map((item) => {
                                    return (
                                        <div>
                                            <span style='color: blue;style="margin-right: 4px"'>{item.fileName}</span>
                                            <a style="margin: 0 4px" onClick={() => this.preViewEvent(item)}>
                                                {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览')}
                                            </a>
                                            <a onClick={() => this.deleteFilesEvent(row, item)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除')}</a>
                                        </div>
                                    )
                                })
                                return [
                                    // <span >{row.purchaseAttachmentDemandDTOList[0]}</span>
                                    // row['purchaseAttachmentDemandDTOList'].length> 0 ? <div ><span style='color: blue' onClick={() => this.preViewEvent(row)}>{row['purchaseAttachmentDemandDTOList'][0]['fileName']} </span><a-icon type="delete" onClick={() => row.purchaseAttachmentDemandDTOList = []}/> </div>: ''
                                    <div>
                                        <a-upload
                                            name={this.file}
                                            multiple={true}
                                            showUploadList={false}
                                            action={this.uploadUrl}
                                            headers={this.uploadHeader}
                                            accept={this.accept}
                                            data={{ headId: row.id || '', businessType: 'biddingPlatform', sourceNumber: this.formData.tenderProjectNumber || this.formData.id || '', actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开' }}
                                            beforeUpload={(e) => this.beforeUpload(e, row)}
                                            onChange={(e) => this.handleUploadChange(e, row)}
                                        >
                                            <a-button onClick={(e) => this.handleRow(e, row)}> { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件')}</a-button>
                                        </a-upload>
                                        {...fileListDoms}
                                    </div>
                                ]
                            } else {
                                return [
                                    <div>
                                        <a-upload
                                            name={this.file}
                                            multiple={true}
                                            showUploadList={false}
                                            action={this.uploadUrl}
                                            headers={this.uploadHeader}
                                            accept={this.accept}
                                            data={{ headId: row.id || '', businessType: 'biddingPlatform', sourceNumber: this.formData.tenderProjectNumber || this.formData.id || '', actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开' }}
                                            beforeUpload={(e) => this.beforeUpload(e, row)}
                                            onChange={(e) => this.handleUploadChange(e, row)}
                                        >
                                            <a-button onClick={(e) => this.handleRow(e, row)}> { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件')}</a-button>
                                        </a-upload>
                                    </div>
                                ]
                            }
                        }
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JpKI_3bbb50fd`, '缴纳时间'),
                    field: 'paymentTime',
                    fieldType: 'date',
                    props: {
                        valueFormat: 'YYYY-MM-DD HH:mm:ss',
                        showTime: true
                    },
                    
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operaition`, '操作'),
                    field: 'title',
                    slots: { default: 'grid_opration' }
                }
            ],
            fields: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectNumber`, '项目编号'),
                    fieldLabelI18nKey: '',
                    field: 'tenderProjectNumber',
                    required: '1',
                    disabled: true,
                    fieldType: 'input',
                    bindFunction: (value, item) => {
                        // this.fields[1].disabled = value == '0'
                        // this.$set(this.formData, 'stageQuoteOperator', '')
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                    fieldLabelI18nKey: '',
                    field: 'tenderProjectName',
                    fieldType: 'input',
                    disabled: true,
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称'),
                    fieldLabelI18nKey: '',
                    field: 'subpackageName',
                    fieldType: 'input',
                    disabled: true,
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tLRL_2715c81b`, '单位名称'),
                    fieldLabelI18nKey: '',
                    field: 'supplierName',
                    fieldType: 'input',
                    disabled: true,
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQKnD_c8206068`, '是否联合体'),
                    fieldLabelI18nKey: '',
                    field: 'combination',
                    fieldType: 'select',
                    dictCode: 'yn',
                    required: 0,
                    disabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KnDRL_37c77362`, '联合体名称'),
                    fieldLabelI18nKey: '',
                    field: 'combinationName',
                    fieldType: 'input',
                    required: '0',
                    disabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JpCK_3bbb12b5`, '缴纳方式'),
                    fieldLabelI18nKey: '',
                    field: 'paymentType',
                    fieldType: 'select',
                    dictCode: 'tenderBidWinningServiceFeeHeadPaymentType',
                    disabled: false,
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dJHfWjW_7f083ba2`, '应缴金额'),
                    fieldLabelI18nKey: '',
                    field: 'dueAmount',
                    disabled: false,
                    fieldType: 'number',
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQTPUz_4704a190`, '是否需要审批'),
                    fieldLabelI18nKey: '',
                    field: 'audit',
                    fieldType: 'select',
                    dictCode: 'yn',
                    disabled: false,
                    required: '1'
                }
            ],
            url: {
                queryById: '/tender/sale/purchaseTenderBidWinningServiceFeeHead/queryById',
                edit: '/tender/sale/purchaseTenderBidWinningServiceFeeHead/edit',
                submit: '/tender/sale/purchaseTenderBidWinningServiceFeeHead/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        ...mapGetters([
            'taskInfo'
        ]),
        pageContentHeight () {
            // let height = this.check ? document.body.clientHeight - 200 : document.body.clientHeight - 395
            let height = document.body.clientHeight - 395
            return height + 'px'
        },
        btns () {
            let flag = false
            let btn = [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.handleBack }
            ]
            if (this.formData.status == '0' || !this.formData.status) {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.handleSave },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'), type: 'primary', click: this.handleSubmit },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.handleBack }
                ]
            }
            if (this.formData.audit == '1' && this.formData.auditStatus == '1') {
                flag = true
                btn = [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                        type: 'primary',
                        click: this.cancelAudit
                        // show: this.cancelAuditShow
                    },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.handleBack }
                ]
            }
            console.log(this.formData.audit, this.formData.status, ['1', '2'].includes(this.formData.auditStatus), this.formData.flowId)
            if (this.formData.audit == '1' && this.formData.status != '0' && ['1', '2'].includes(this.formData.auditStatus) && this.formData.flowId) {
                if(flag){
                    btn.splice(1, 0, { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow })
                }else{
                    btn.unshift({ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow })
                }
            }
            console.log(this.projectMemberPermission)
            if (!this.projectMemberPermission) {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.handleBack }
                ]
            }
            return btn
            
            
        },
        pageStatus (){
            let status = this.formData.status == '2' || (this.formData.audit == '1' && this.formData.auditStatus == '1') ? 'detail' : 'edit'
            return status
        }
    },
    methods: {
        handleOk () {
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = 'purchaseProduct'
            param['businessId'] = this.currentEditRow.businessId
            param['taskId'] = this.currentEditRow.taskId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            this.confirmLoading = true
            postAction(this.currentUrl, param)
                .then(res => {
                    if (res.success) {
                        this.auditVisible = false
                        this.$message.success(res.message)
                        this.$parent.reloadAuditList()
                        this.goBackAudit()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow () {
            this.flowId = this.formData.flowId || this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass () {
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject () {
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        beforeUpload (e, row) {
            console.log('nilaolou')
            console.log('nilaolou', row)
            let {fullData} =this.$refs.purchaseTenderBidWinningServiceFeeItemList.getTableData()
            this.formData.purchaseTenderBidWinningServiceFeeItemList=fullData
            if (!row.id) {
                return new Promise((resolve, reject) => {
                    postAction(this.url.edit, this.formData).then(async (res) => {
                        if (res.success) {
                            await this.queryDetail()
                            resolve()
                        } else {
                            reject()
                        }
                    })
                })
            }
        },
        async cancelAudit () {
            let params = this.formData
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: async function () {
                    await that.postAuditData('/a1bpmn/audit/api/cancel', params)
                    await that.queryDetail()
                    
                    // that.$refs.businessRefName.loadData()    
                }
            })
        },
        postAuditData (invokeUrl, formData) {
            this.showHeader = false
            let param = {}
            param['rootProcessInstanceId'] = formData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param)
                .then((res) => {
                    if (res.success) {
                        this.alreadyCancel = true
                        this.queryDetail()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.showHeader = true
                    this.confirmLoading = false
                })
        },
        // showFlow () {
        //     this.flowId = this.formData.flowId
        //     this.flowView = true
        // },
        delDisabled (row){
            return (this.formData.status == '2') || (this.formData.auditStatus == '1') || (this.formData.auditStatus == '2')
        },  
        handleRow (e, row) {
            console.log('this.formData.id2', this.formData.id)
            this.row = row
            // if (!row.id) {
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
            //     e.stopPropagation()
            // }
            if(this.formData.status == '2' || this.formData.auditStatus == '1' || this.formData.auditStatus == '2'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_APzExqtk_128cab96`, '当前状态不可操作'))
                e.stopPropagation()
            }
        },
        handleUploadChange ({ file, fileList, event }, row) {
            console.log('123')
            console.log(row)
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if (this.formData.id) {
                this.$refs.purchaseTenderBidWinningServiceFeeItemList.loading = true
                if (file.status === 'done') {
                    if (file.response.success) {
                        let { fileName, filePath, fileSize, id } = file.response.result
                        let { headId } = row.id
                        let fileListData = {
                            uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                            uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName,
                            filePath,
                            fileSize,
                            id,
                            headId
                        }
                        // if (!this.formData.purchaseAttachmentList) this.$set(this.formData, 'purchaseAttachmentList', [])
                        console.log('row', row)
                        this.formData.purchaseTenderBidWinningServiceFeeItemList.forEach((item) => {
                            if (item.id == row.id) {
                                item.attachmentDTOList = (item.attachmentDTOList instanceof Array) ? item.attachmentDTOList : []
                                console.log('item.attachmentDTOList', item.attachmentDTOList)
                                item.attachmentDTOList.push(fileListData)
                                console.log(item)

                            }
                        })
                        this.$refs.purchaseTenderBidWinningServiceFeeItemList.loading = false

                        // attachmentDTOList.push(fileListData)

                        // 仅对表格数据进行操作，再在父组件获取子组件table表格数据进行保存发布
                        // this.$refs.purchaseTenderBidWinningServiceFeeItemList.insertAt(fileListData, -1)
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                    this.$refs.purchaseTenderBidWinningServiceFeeItemList.loading = false
                }
            }
        },
        addInviteItem (){
            this.formData.purchaseTenderBidWinningServiceFeeItemList.push({})
        },
        handleDeleteFile (deleteItem) {
            console.log('1', this.formData.purchaseTenderBidWinningServiceFeeItemList, deleteItem)
            // 仅对表格数据进行操作，再在父组件获取子组件table表格数据进行保存发布
            // this.$refs.purchaseTenderBidWinningServiceFeeItemList.fromSourceData.splice(index, 1)
            let list = this.formData.purchaseTenderBidWinningServiceFeeItemList
            let targetIndex
            
            list.forEach((item, index)=>{
                if(item.id == deleteItem.id) targetIndex = index
            })
            this.formData.purchaseTenderBidWinningServiceFeeItemList.splice(targetIndex, 1)
        },
        preViewEvent (item) {
            this.subpackageId = this.currentEditRow.subpackageId
            this.$previewFile.open({params: item })
        },
        deleteFilesEvent (row, item) {
            if(this.formData.status == '2' || this.formData.auditStatus == '1' || this.formData.auditStatus == '2'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_APzExqtk_128cab96`, '当前状态不可操作'))
                return 
            }
            // const fileGrid = this.$refs.purchaseTenderBidWinningServiceFeeItemList
            console.log(row, item)
            row.attachmentDTOList = row.attachmentDTOList.filter(file=>{
                return file.id != item.id
            })
            console.log('row', row)
        },
        async queryDetail () {
            let url = this.url.queryById
            // this.confirmLoading = true
            // this.show = false
            let query = await getAction(url, {id: this.currentEditRow.id})
            // this.confirmLoading = false
            console.log(query)

            if (query && query.success) {
                // querybyid接口返回的值
                this.formData = Object.assign({}, query.result)
                console.log('this.formData', this.formData)
            } else {
                this.$message.error(query.message)
            }
            // this.show = true
        },
        handleSave () {
            this.confirmLoading = true
            // 判断走的哪个接口（根据saleTenderEvaClarificationItemList是否有值：回复过（保存过））
            let url = this.url.edit
            let {fullData} =this.$refs.purchaseTenderBidWinningServiceFeeItemList.getTableData()
            // 缴纳记录列表时间转为时间戳
            // if(fullData.length != 0){
            //     fullData.forEach(item=>{
            //         if(item.paymentTime){
            //             item.paymentTime = new Date(item.paymentTime).getTime()
            //         }else{
            //             item.paymentTime = new Date().getTime()
            //         }
            //     })
            // }
            console.log('fullData', fullData)
            this.formData.purchaseTenderBidWinningServiceFeeItemList=fullData
            postAction(url, this.formData).then((res) => {
                let type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
            }).finally(() => {
                this.confirmLoading = false
                this.queryDetail()
            })
        },
        handleSubmit () {
            // valiStringLength(this.formData, [
            //     {field: 'remark', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjW_27a9ee3d`, '变更原因'), maxLength: 100},
            //     {field: 'content', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MBCc_28d55903`, '回复内容'), maxLength: 100}
            // ])
            let {fullData} =this.$refs.purchaseTenderBidWinningServiceFeeItemList.getTableData()
            
            // 校验数据
            this.$refs.dataform.$refs.form.validate().then((valid1) => {
                this.$refs.purchaseTenderBidWinningServiceFeeItemList.getValidate().then((valid2) => {
                    let flag = false
                    if(fullData.length != 0){
                        fullData.forEach(item=>{
                            item.attachmentDTOList = (item.attachmentDTOList instanceof Array) ? item.attachmentDTOList : []
                            // item.paymentTime = new Date(item.paymentTime).getTime()
                            
                            // 现金缴纳且附件为空时，不进行发布操作
                            if(item.paymentType == '0' && item.attachmentDTOList.length == '0') {
                                flag = true
                            }
                        })
                    }
                    if(flag){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JuCKLWHJpKTXVBI_adff0750`, '缴费方式为现金缴纳时需上传附件！'))
                        return 
                    }
                    this.confirmLoading = true
                    postAction(this.url.submit, this.formData).then((res) => {
                        let type = res.success ? 'success' : 'error'
                        if (res.success) {
                            this.$message[type](res.message)
                            this.queryDetail()

                            // this.$emit('hide')
                        } else {
                            this.$message[type](res.message)
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                })
            })
        },
        handleBack () {
            this.$emit('hide')
        }
        
        
    },
    mounted () {
        this.queryDetail()
    }
}
</script>
<style lang="less" scoped>

:deep(.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell) {
    max-height: none;
}
:deep(.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis > .vxe-cell) {
    max-height: none;
}
</style>
