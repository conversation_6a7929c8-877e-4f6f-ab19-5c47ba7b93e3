<template>
  <div class="Offline business-container">
    <business-layout
      :showHeader="false"
      pageStatus="edit"
      useLocalModelLayout
      modelLayout="unCollapse"
      :ref="businessRefName"
      :currentEditRow="vuex_currentEditRow"
      :requestData="requestData"
      :externalToolBar="externalToolBar"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      :handleAfterDealSource="handleAfterDealSource"
      v-on="businessHandler"
    />
  </div>
</template>

<script lang="jsx">
import { cloneDeep } from 'lodash'

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {
    ROWDEMO,
    GRID_OPTION_ROW,
    TOOLBAR_BUTTON_UPLOAD,
    ATTACHMENT_GROUP,
    ATTACHMENT_COLUMNS_WITHOUT_OPRATION
} from '@/utils/constant.js'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

import { add } from '@/utils/mathFloat.js'

export default {
    name: 'Offline',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            cachePageConfig: {},
            cacheAllData: {},
            requestData: {
                detail: {
                    url: '/bidding/purchaseBiddingHead/bidEvaOfOffLine',
                    args: () => ({id: this.vuex_currentEditRow.id})
                }
            },
            externalToolBar: {
                purchaseAttachmentBidEvaList: [
                    {
                        ...TOOLBAR_BUTTON_UPLOAD,
                        args: {
                            modalVisible: false, // 必传
                            businessType: 'bidding', // 必传
                            disabledItemNumber: true
                        },
                        dictCode: 'srmFileTypeBidEva'
                    }
                ]
            },
            pageHeaderButtons: [],
            url: {
                detail: '/bidding/purchaseBiddingHead/queryById'
            }
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        editEnable () {
            return this.vuex_currentEditRow.biddingStatus === '3'
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: '评标管理',
                        groupNameI18nKey: 'i18n_field_UBRv_411e9c88',
                        groupCode: 'biddingSupplierVOList',
                        groupType: 'item',
                        sortOrder: '1'
                    },
                    {
                        ...ATTACHMENT_GROUP,
                        groupName: '评标材料附件',
                        groupNameI18nKey: 'i18n_alert_UBnLBI_6a89b67e',
                        groupCode: 'purchaseAttachmentBidEvaList'
                    }
                ],
                formFields: [],
                itemColumns: [
                    ...ATTACHMENT_COLUMNS_WITHOUT_OPRATION({ groupCode: 'purchaseAttachmentBidEvaList' }),
                    {
                        ...GRID_OPTION_ROW,
                        groupCode: 'purchaseAttachmentBidEvaList',
                        width: 140,
                        slots: {
                            default: ({ row }) => {
                                return [
                                    (<span>
                                        <a-button type="link" style="padding: 0 4px;" onClick={ () => this.attachmentDownload(row) }>
                                            { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载') }
                                        </a-button>
                                        <a-button type="link" style="padding: 0 4px;" onClick={ () => this.attachmentPreview(row) }>
                                            { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览') }
                                        </a-button>
                                        <a-button type="link" style="padding: 0 4px;" onClick={ () => this.attachmentDelete(row, { groupCode: 'purchaseAttachmentBidEvaList' }) }>
                                            { this.$srmI18n(`${this.$getLangAccount(row)}#i18n_title_delete`, '删除') }
                                        </a-button>
                                    </span>)
                                ]
                            }
                        }
                    }
                ]
            }
        },
        countRowTotal (resultData) {
            console.log('resultData :>> ', resultData)
            const biddingSupplierVOList = resultData.biddingSupplierVOList || []
            biddingSupplierVOList.forEach(item => {
                const purchaseBiddingEvaResultList = item.purchaseBiddingEvaResultList || []
                let scoreArr = purchaseBiddingEvaResultList.map(n => n.score || 0)
                let total = scoreArr.reduce((acc, num) => {
                    acc = add(acc, num)
                    return acc
                }, 0)
                item.totalScore = total.toFixed(2)
            })
        },
        handleAfterDealSource (pageConfig, resultData) {
            this.cachePageConfig = cloneDeep(pageConfig)
            this.cacheAllData = cloneDeep(resultData)

            // 只有未审批状态时允许编辑
            let enabled = resultData.biddingStatus === '3'

            console.log('purchaseBiddingEvaResultList :>> ', purchaseBiddingEvaResultList)

            this.countRowTotal(resultData)

            const { biddingSupplierVOList = [] } = resultData || {}
            if (!biddingSupplierVOList.length) {
                return
            }
            const { purchaseBiddingEvaResultList = [] } = biddingSupplierVOList[0] || {}
            if (!purchaseBiddingEvaResultList.length) {
                return
            }
            console.log('purchaseBiddingEvaResultList :>> ', purchaseBiddingEvaResultList)
            let expandColumns = purchaseBiddingEvaResultList.map((item, idx) => {
                let column = cloneDeep(ROWDEMO)
                column = Object.assign({}, column, {
                    groupCode: 'biddingSupplierVOList',
                    title: item.regulationName,
                    field: `score_${idx}`,
                    helpText: item.regulationDetail || '',
                    width: 120,
                    required: '1',
                    editRender: {
                        enabled: enabled,
                        autofocus: '.custom-cell-number input'
                    },
                    slots: {
                        default: ({ row }) => {
                            return [
                                (<span>{ row.purchaseBiddingEvaResultList[idx].score }</span>)
                            ]
                        },
                        edit: (({row}) => {
                            const props = {
                                type: 'float',
                                min: '0',
                                max: row.purchaseBiddingEvaResultList[idx].fullMark || ''
                            }
                            const on = {
                                change: () => {
                                    let purchaseBiddingEvaResultList = row.purchaseBiddingEvaResultList || []
                                    let scoreArr = purchaseBiddingEvaResultList.map(n => n.score || 0)
                                    let total = scoreArr.reduce((acc, num) => {
                                        acc = add(acc, num)
                                        return acc
                                    }, 0)
                                    row.totalScore = total.toFixed(2)
                                }
                            }
                            return [
                                (<vxe-input  class='custom-cell-number' vModel={row.purchaseBiddingEvaResultList[idx].score} {...{ props, on }} />)
                            ]
                        })
                    }
                })
                return column
            })

            let preFix = [
                { ...ROWDEMO, title: '供应商名称', fieldLabelI18nKey: 'i18n_field_supplierName', field: 'supplierName', width: 150 },
                {
                    ...ROWDEMO,
                    title: '否决投标',
                    fieldLabelI18nKey: 'i18n_field_QKeB_277f7b1f',
                    width: 150,
                    editRender: {
                        enabled: enabled
                    },
                    slots: {
                        default: ({ row }) => {
                            let txt = row.bidEva === '1' ? `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是')}` : `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否')}`
                            return [
                                (<span>{ txt }</span>)
                            ]
                        },
                        edit: (({row}) => {
                            const props = {
                                dictCode: 'yn'
                            }
                            return [
                                (<m-select vModel={row.bidEva} {...{ props }} />)
                            ]
                        })
                    }
                },
                {
                    ...ROWDEMO,
                    title: '是否为评委推荐人',
                    fieldLabelI18nKey: 'i18n_field_KQLULYIL_88b87b1f',
                    width: 150,
                    fieldType: enabled ? 'select' : '',
                    editRender: {
                        enabled: enabled
                    },
                    slots: {
                        default: ({ row }) => {
                            let txt = row.recommend === '1' ? `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是')}` : `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否')}`
                            return [
                                (<span>{ txt }</span>)
                            ]
                        },
                        edit: (({row}) => {
                            const props = {
                                dictCode: 'yn'
                            }

                            return [
                                (<m-select vModel={row.recommend} {...{ props }} />)
                            ]
                        })
                    }
                }
            ]

            let suffix = [
                {
                    ...ROWDEMO,
                    title: '价格得分',
                    fieldLabelI18nKey: 'i18n_field_umjz_25786834',
                    field: 'priceScore',
                    required: '1',
                    editRender: {
                        enabled: enabled,
                        autofocus: '.custom-cell-number input'
                    },
                    width: 120,
                    slots: {
                        default: ({ row }) => {
                            return [
                                (<span>{ row.priceScore }</span>)
                            ]
                        },
                        edit: (({row}) => {
                            const props = {
                                type: 'float',
                                min: '0',
                                max: row.priceScore || ''
                            }
                            const on = {
                                change: () => {
                                    let purchaseBiddingEvaResultList = row.purchaseBiddingEvaResultList || []
                                    let scoreArr = purchaseBiddingEvaResultList.map(n => n.score || 0)
                                    let total = scoreArr.reduce((acc, num) => {
                                        acc = add(acc, num)
                                        return acc
                                    }, 0)
                                    row.totalScore = add(total, row.priceScore).toFixed(2)
                                }
                            }
                            return [
                                (<vxe-input  class='custom-cell-number' vModel={row.priceScore} {...{ props, on }} />)
                            ]
                        })
                    }
                },
                {   ...ROWDEMO,
                    title: '汇总得分',
                    fieldLabelI18nKey: 'i18n_field_Mksz_32ad7d23',
                    field: 'totalScore',
                    width: 120,
                    slots: {
                        default: ({ row }) => {
                            let purchaseBiddingEvaResultList = row.purchaseBiddingEvaResultList || []
                            let scoreArr = purchaseBiddingEvaResultList.map(n => n.score || 0)
                            let total = scoreArr.reduce((acc, num) => {
                                acc = add(acc, num)
                                return acc
                            }, 0)
                            row.totalScore = add(total, row.priceScore).toFixed(2)

                            return [
                                (<span>{row.totalScore}</span>)
                            ]
                        }
                    }
                },
                {
                    ...ROWDEMO,
                    title: '备注',
                    fieldLabelI18nKey: 'i18n_title_remark',
                    field: 'purchaseRemark',
                    width: 150,
                    editRender: {
                        enabled: enabled,
                        autofocus: '.custom-cell-input input'
                    },
                    slots: {
                        default: ({ row }) => {
                            return [
                                (<span>{ row.purchaseRemark }</span>)
                            ]
                        },
                        edit: (({row}) => {
                            return [
                                (<vxe-input class='custom-cell-input' vModel={row.purchaseRemark} />)
                            ]
                        })
                    }
                }
            ]
            pageConfig.groups[0].columns = [
                ...pageConfig.groups[0].columns,
                ...preFix,
                ...expandColumns,
                ...suffix
            ]
        }
    }
}
</script>


<style lang="less">
.Offline {
    .page-container {
        padding: 0;
        .edit-page {
            .page-content {
                margin: 0;
            }
        }
    }

}
</style>
