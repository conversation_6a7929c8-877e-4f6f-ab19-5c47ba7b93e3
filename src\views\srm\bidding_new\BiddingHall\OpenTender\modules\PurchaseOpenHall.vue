<template>
  <div>
    <a-spin :spinning="loading">
      <navStautsList
        :navStautsList="navStautsList"
        :formData="formData"/>
      <newsBox
        :openInfoRecordsList="openInfoRecordsList"
        :formData="formData"/>
      <floorCtrlBtn :floorBtns="floorBtns">
        <template slot="left">
          <div
            class="deadline" 
            v-if="deadline">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_ywutKW_7f0992b6`, '解密倒计时：') }}</span>
            <countdown 
              :time="deadline"
              @end="handleFinish"
            >
              <template slot-scope="props"> <span class="fontColor">{{ props.hours }} : {{ props.minutes }} : {{ props.seconds }}</span></template>
            </countdown>
          </div>
        </template>
      </floorCtrlBtn>
    </a-spin>
    <a-modal
      v-drag    
      v-model="showOpenBidKey"
      width="400px"
      @ok="handleOpenBidKey"
      :confirmLoading="loading"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_vBwo_2cc15842`, '开标密码')">
      <a-form-model
        :model="form"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_vBwo_2cc15842`, '开标密码')"
          required>
          <a-input-password
            v-model="password" />
        </a-form-model-item >
      </a-form-model>
    </a-modal>
    <a-modal
      v-drag    
      v-model="showPriceOpeningsGrid"
      width="800px"
      :title="$srmI18n(`${$getLangAccount()}#i18n_dict_vBIBB_6b38ab39`, '开标一览表')">
      <template slot="footer">
        <a-button
          :loading="gridLoading"
          @click="exportExcel">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_uGLWWWWW_4f2ec79b`, '导出Excel') }}</a-button>
        <a-button
          :loading="gridLoading"
          @click="exportPdf"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_uGLWWW_9dc90ad6`, '导出PDF') }}</a-button>
        <a-button
          @click="cancel">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
      </template>
      <priceOpeningsGrid
        v-if="showPriceOpeningsGrid"
        :priceOpeninData="priceOpeninData"/>
    </a-modal>

  </div>
</template>
<script>
import moment from 'moment'
import { getAction, postAction, downFile } from '@views/srm/bidding_new/plugins/manage'
import eventBus from '@/utils/eventBus.js'
import MyWebSocket from '../websocket/websocket.js'
import {USER_INFO, USER_COMPANYSET} from '@/store/mutation-types'
import layIM from '@/utils/im/layIM.js'
import priceOpeningsGrid from './components/priceOpeningsGrid'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
import countdown from '@/components/countdown/index.js'
import navStautsList from './components/navStautsList'
import newsBox from './components/newsBox'
import floorCtrlBtn from './components/floorCtrlBtn'
import { Base64 } from 'js-base64'

const HEART_BEAT_CONFIG = {
    time: 30 * 1000, // time：心跳时间间隔
    timeout: 3 * 1000, // timeout：心跳超时间隔
    reconnect: 10 * 1000 // reconnect：断线重连时
}
export default {
    mixins: [baseMixins],
    components: {
        priceOpeningsGrid,
        countdown,
        navStautsList,
        newsBox,
        floorCtrlBtn
    },
    data () {
        return {
            password: '',
            showOpenBidKey: false,
            form: {},
            labelCol: { span: 8 },
            wrapperCol: { span: 16 },
            formData: {},
            news: [],
            loading: false,
            gridLoading: false,
            showPriceOpeningsGrid: false,
            floorBtns: [
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KWRe_2987fa32`, '在线沟通'), click: this.handleChat},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ixvB_2b14afc7`, '宣布开标'), click: this.handleOpen, key: 'open'},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ywyW_40164c6f`, '解密结束'), click: this.handleDecrypted, key: 'decrypted', disabled: true},
                {title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_i18n_dict_vBIBB_6b38ab39`, '开标一览表')), click: this.showPriceOpenings, key: 'priceOpenings', disabled: true},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yW_fc2ec`, '结束'), click: this.handleClose, key: 'close', disabled: true}
            ],
            openInfoRecordsList: [],
            priceOpeninData: [],
            deadline: null,
            websocket: null,
            clearReconnectTimer: false,
            heartBeatConfig: HEART_BEAT_CONFIG,
            url: {
                getDataUrl: '/tender/purchaseTenderProjectSubpackageInfo/queryById',
                openBidUrl: '/tender/purchaseTenderProjectSubpackageInfo/openBid',
                getOpenSettingUrl: '/tender/purchaseTenderProjectOpenSettingHead/queryBySubpackageId',
                decryptedUrl: '/tender/purchaseTenderProjectSubpackageInfo/decrypted',
                endBidUrl: '/tender/purchaseTenderProjectSubpackageInfo/endBid'
            }
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'currentSubPackage', 
        'resetCurrentSubPackage',
        'stopLoading',
        'handleLoading'
    ],
    computed: {
        subpackage (){
            return this.currentSubPackage()
        },
        subId () {
            return this.subpackageId()
        },
        openTimeCode () {
            let code = ''
            switch (this.checkType) {
            case '0':
                code = 'preAnnounceOpenBidTime_DateMaps'
                break
            case '1': 
                switch (this.processType) { 
                case '0':
                    code = 'resultAnnounceOpenBidTime_DateMaps'
                    break
                case '1': 
                    switch (this.currentStep) { 
                    case '0':
                        code = 'announceOpenBidTime_DateMaps'
                        break
                    case '1': 
                        code = 'resultAnnounceOpenBidTime_DateMaps'
                        break
                    default :
                        code = 'announceOpenBidTime_DateMaps'
                    }
                    break
                default :
                    code = 'announceOpenBidTime_DateMaps'
                }
                break
            default :
                code = 'announceOpenBidTime_DateMaps'
            }
            return code
        },
        openBidStatus () {
            let openBidStatus = ''
            switch (this.checkType) {
            case '0':
                openBidStatus = 'preOpenBidStatus'
                break
            case '1': 
                switch (this.processType) { 
                case '0':
                    openBidStatus = 'resultOpenBidStatus'
                    break
                case '1': 
                    switch (this.currentStep) { 
                    case '0':
                        openBidStatus = 'openBidStatus' 
                        break
                    case '1': 
                        openBidStatus = 'resultOpenBidStatus'
                        break
                    default :
                        openBidStatus = 'openBidStatus'
                    }
                    break
                default :
                    openBidStatus = 'openBidStatus'
                }
                break
            default :
                openBidStatus = 'openBidStatus'
            }
            return openBidStatus
        },
        navStautsList () {
            let openBidStatus = `${this.openBidStatus}_dictText`
            let columns = [
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidOpenStatus`, '开标状态'), fieldName: openBidStatus, icon: 'openBidStatus'},
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QInJLW_db379d17`, '文件递交人数'), fieldName: 'fileSubmitNumber', icon: 'fileSubmitNumber'},
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PuLW_39615fe8`, '签到人数'), fieldName: 'signInNumber', icon: 'signInNumber'},
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ywLW_40107779`, '解密人数'), fieldName: 'decryptNumber', icon: 'decryptNumber'}
                // {label: '签名人数 ', fieldName: 'signatureNumber'}
            ]
            // 非加密情况,则不展示签到人数和解密人数
            if(this.subpackage.openBidEncrypt != '1'){
                columns.splice(2, 2)
            }
            return columns
        }
    },
    methods: {
        handleChat () {
            layIM.creatGruopChat({id: this.subId, type: 'PurchaseTenderOpenBid', url: this.url, recordNumber: this.subId})
        },
        postMessage (msg) {
            if (this.websocket) {
                this.websocket.sendMsg(
                    {
                        'checkType': this.checkType,
                        'processType': this.processType,
                        'currentStep': this.currentStep,
                        'subpackageId': this.subId,
                        'messageContent': msg
                    }
                )
            }
        },
        // 获取解密时间
        getDecryptionTime () {
            return getAction(this.url.getOpenSettingUrl, {subpackageId: this.subId})
        },
        // 设置解密时间
        async setDecryptionTime () {
            let StartTime = moment().valueOf()
            let {result: {decryptionTime}, timestamp} = await this.getDecryptionTime()
            // 响应时间
            let announceOpenBidTime = this.formData[this.openTimeCode] || ''
            let openBidTime = moment(announceOpenBidTime).add(decryptionTime, 'minutes').valueOf()
            // 倒计时时间= 宣布开标时间 + 解密时间 - 当前服务器时间 
            if (timestamp < openBidTime) {
                let ResponseTime = moment().valueOf() - StartTime
                this.deadline = openBidTime - timestamp - ResponseTime
            }
        },
        // 检查文件是否需要加密
        checkOpenBidEncrypt () {
            if (this.tenderCurrentRow.openBidEncrypt == 1) {
                this.setDecryptionTime()
            } else {
                this.ableBtnList(['priceOpenings', 'close'])
            }
        },
        // 解密结束
        handleDecrypted () {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLywyW_71200265`, '确认解密结束'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLywyW_a0f638fc`, '是否确认解密结束'),
                onOk: () => {
                    this.loading = true
                    postAction(this.url.decryptedUrl, {id: this.subId}).then((res) => {
                        let type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.hideBtnList('decrypted')
                            this.getData()
                        }
                    }).finally(() => {
                        this.loading = false
                    })
                }
            })
        },
        // 解密倒计时
        handleFinish () {
            this.ableBtnList(['priceOpenings', 'close'])
            setTimeout(() => {
                this.deadline = null
            }, 50)
            // this.handleDecrypted()
        },
        // 按钮可操作
        ableBtnList (codes, disabled = false) {
            if (!Array.isArray(codes)) codes = [codes]
            this.floorBtns.map(btn => {
                if (codes.includes(btn.key)) btn.disabled = disabled
            })
        },
        // 隐藏按钮
        hideBtnList (codes) {
            if (!Array.isArray(codes)) codes = [codes]
            this.floorBtns = this.floorBtns.filter(btn => {
                return !codes.includes(btn.key)
            })
        },
        async cb (){
            this.loading = true
            await postAction(this.url.openBidUrl, {id: this.subId, openBidPassword: Base64.encode(this.password)}).then(async (res) => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success) {
                    this.hideBtnList('open')
                    this.ableBtnList('decrypted')
                    // this.formData = res.result || {}
                    this.openInfoRecordsList = res.result.openInfoRecordsList || []
                    await this.getData()
                    this.setDecryptionTime()
                }else{
                    this.loading = false
                }
            })
            // .finally(() => {
            //     this.loading = false
            // })
        },
        handleOpenBidKey () {
            if(this.tenderCurrentRow.openBidPassword != Base64.encode(this.password)){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_woJOKmVsK_54cfc4f4`, '密码校验失败，请重试！'))
                return
            }
            var that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_vB_be907`, '开标'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLvBW_ebcb05eb`, '是否确认开标?'),
                onOk () {
                    that.cb && that.cb()
                    that.showOpenBidKey = false
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        // 开标
        handleOpen () {
            // 校验响应人数是否小于预设的开标人数，小于则不允许开标
            if(this.formData.fileSubmitNumber < this.tenderCurrentRow.bidOpeningLimit){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vBLWxVxiTvB_7a45b85f`, '开标人数不足，不允许开标！'))
                return
            }
            // 招标项目有设置开标密码，则走开标密码逻辑
            if(this.tenderCurrentRow.openBidPassword){
                this.showOpenBidKey = true
            }else{
                // 否则走原逻辑
                const callback = async () => {
                    this.loading = true
                    this.handleLoading()
                    postAction(this.url.openBidUrl, {id: this.subId}).then(async (res) => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success) {
                            this.hideBtnList('open')
                            this.ableBtnList('decrypted')
                            // this.formData = res.result || {}
                            this.openInfoRecordsList = res.result.openInfoRecordsList || []
                            await this.getData()
                            this.checkOpenBidEncrypt()
                        }else{
                            this.loading = false
                        }
                    }).finally(() => {
                        this.loading = false
                        this.stopLoading()
                    })
                }
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_vB_be907`, '开标'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLvBW_ebcb05eb`, '是否确认开标?'),
                    onOk () {
                        callback && callback()
                    },
                    onCancel () {
                        console.log('Cancel')
                    }
                })
            }

            
        },
        // 结束开标
        handleClose (){
            const callback = () => {
                this.loading = true
                postAction(this.url.endBidUrl, {id: this.subId}).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    this.getData()
                    this.closeWebscoket()
                }).finally(() => {
                    this.loading = false
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yW_fc2ec`, '结束'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLyWW_ec4268a6`, '是否确认结束?'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        cancel () {
            this.gridLoading = false
            this.showPriceOpeningsGrid = false
        },
        // 开标预览表
        showPriceOpenings () {
            this.loading = true
            postAction(`/tender/purchaseTenderProjectSubpackageInfo/priceOpeningsBySubpackageId?id=${this.subId}`).then(res => {
                if(res.success) {
                    this.showPriceOpeningsGrid = true
                    this.priceOpeninData = res.result || []
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.loading = false
            })
        },
        // 导出开标一览表为Excel
        exportExcel () {
            this.gridLoading = true
            const exportUrl=`/tender/purchaseTenderProjectSubpackageInfo/exportPriceOpenings?id=${this.subId}`
            downFile(exportUrl).then((data) => {
                if (data.type=='application/json') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), '开标一览表.xlsx')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '开标一览表.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            }).finally(() => {
                this.gridLoading = false
            })
        },
        // 导出开标预览表为PDF
        exportPdf () {
            this.gridLoading = true
            const exportUrl=`/tender/purchaseTenderProjectSubpackageInfo/exportPriceOpeningsPDF?id=${this.subId}`
            downFile(exportUrl).then((data) => {
                if (data.type=='application/json') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), '开标一览表.pdf')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '开标一览表.pdf')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            }).finally(() => {
                this.gridLoading = false
            })
        },
        getData () {
            let params = {
                id: this.subId
            }
            this.loading = true
            this.deadline = null
            getAction(this.url.getDataUrl, params).then(async (res) => {
                if (res.success) {
                    this.formData = res.result || {}
                    this.openInfoRecordsList = this.formData.openInfoRecords || []
                    // openBidStatus: 1:解密中，2:解密结束，3:签名中，4:签名结束，5:开标结束
                    if(this.formData[this.openBidStatus] == 1) {
                        this.hideBtnList('open')
                        this.ableBtnList('decrypted')
                        // 已经开标获取解密时间
                        this.setDecryptionTime()
                    } else if (this.formData[this.openBidStatus] == '2') {
                        this.hideBtnList(['open', 'decrypted'])
                        this.ableBtnList(['close', 'priceOpenings'])
                        this.deadline = null
                    } else if (this.formData[this.openBidStatus] == '5') {
                        this.hideBtnList(['open', 'close', 'decrypted'])
                        this.ableBtnList(['priceOpenings'])
                        this.deadline = null
                        this.closeWebscoket()
                    }
                    if (this.formData[this.openBidStatus] !== '5') {
                        this.setWebscoket()
                    }
                    // 当解密人数等于文件递交人数才可操作解密结束
                    if (!this.formData.fileSubmitNumber && this.formData.fileSubmitNumber == this.formData.decryptNumber) {
                        this.ableBtnList(['decrypted'])
                    }
                    // this.$emit('resetCurrentSubPackage')
                    this.resetCurrentSubPackage()
                    this.$forceUpdate()
                } else {
                    this.$message.error(res.message)
                    this.hideBtnList(['open', 'decrypted', 'close', 'priceOpenings'])
                }
            }).finally(() => {
                this.loading = false
            })
        },
        handleWebScoketMessage (e) {
            try {
                let data = e.data
                console.log('ws data', data)
                if (data == 'heartCheck') { // 心跳
                    this.websocket.webSocketState = true
                }  else {
                    let {type, result} =  JSON.parse(data)
                    console.log(type, result)
                    if (type == 'updateOpenBidStatus') {
                        // 更新开标状态
                        this.websocket.webSocketState = true
                        let openBidStatus = result[this.openBidStatus]
                        this.statusMap(openBidStatus)
                    } else if (type == 'updateOpenInfo'){
                        // 更新开标信息
                        this.formData = result || {}
                        this.websocket.webSocketState = true
                    } else {
                        this.getData()
                        this.websocket.webSocketState = true
                    }
                }
            } catch (err) {
                console.log('err', err)
            }
        },
        // 状态枚举对应方法
        statusMap (status) {
            // let map = {
            //     1: () => {
            //         this.ableBtnList('decrypt')
            //     },
            //     4: () => {
            //         this.ableBtnList('decrypt', true)
            //     },
            //     5: () => {
            //         this.getData()
            //     }
            // }
            // try {
            //     map[status]()
            // } catch (error) {
            //     this.getData()
            // }
            switch (status) {
            case '1':
                this.ableBtnList('decrypt')
                break
            case '4':
                this.ableBtnList('decrypt', true)
                break
            default:
                this.getData()
                break
            }
        },
        connectWebSocket () {
            this.websocket = new MyWebSocket(this.wsUrl)
            this.websocket.init(this.heartBeatConfig, true)
        },
        closeWebscoket () {
            if (this.websocket) {
                console.log('websocket关闭。。。。')
                this.websocket.webSocketState = false
                this.websocket.closeHandler()
                this.websocket.close()
                this.websocket = null
            }
        },
        handleSockeMessage () {
            if (this.spinning) return
            this.refresh()
        },
        getWebsocketUrl () {
            let { serivceUrl = '', elsAccount = '', subAccount=''} = this.$ls.get(USER_INFO) || {}
            let url = serivceUrl.replace('https://', 'wss://').replace('http://', 'ws://')
            this.wsUrl = `${url}/els/websocket/tender/online/${this.subId}/${elsAccount}_${subAccount}`
        },
        reconnectWebSocket () {
            if (!this.websocket && !this.clearReconnectTimer) { //第一次执行，初始化
                this.connectWebSocket()
            }
            if (this.websocket && this.websocket.reconnectTimer) { //防止多个websocket同时执行
                clearTimeout(this.websocket.reconnectTimer)
                this.websocket.reconnectTimer = null
                this.connectWebSocket()
            }
        },
        firstMessage () {
            let {realname = ''} = this.$ls.get(USER_INFO) || {}
            let {companyName='' } = this.$ls.get(USER_COMPANYSET) || {}
            this.postMessage(`开标人 ${companyName} ${realname} 进入开标大厅`)
        },
        handleOpenWebscoket () {
            this.firstMessage()
        },
        setWebscoket () {
            if (!this.websocket) {
                eventBus.onEvent('reconnect', this.reconnectWebSocket) // 
                eventBus.onEvent('handleMessage', this.handleWebScoketMessage) //接收消息
                eventBus.onEvent('handleOpenWebscoket', this.handleOpenWebscoket) // 长链接开启
                eventBus.emitEvent('reconnect')
            }
        },
        init () {
            this.getWebsocketUrl()
            this.getData()
        }
    },
    beforeDestroy () {
        this.clearReconnectTimer = true
        this.closeWebscoket()
    },
    created () {
        this.init()
    }
}
</script>

<style lang="less" scoped>
  .color-blue{
    color: blue;
  }
  .nav-item{
    display: inline-block;
    font-size: 20px;
    & + &{
      margin-left: 10px;
    }
  }
  .deadline{
    font-size:20px;
    margin-left: 10px;
    display: inline-block;
    :deep(.ant-statistic){
      display: inline-block;
    }
    :deep(.ant-statistic-content){
      font-size:20px;
    }
  }
  
  .text-align-l{
    text-align: left;
  }
  .margin-r-10{
    margin-right: 10px;
  }
  .fontColor {
    color: rgba(249, 60, 0, 0.8);
}
</style>

