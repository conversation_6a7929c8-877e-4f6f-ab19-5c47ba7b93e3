<template>
  <div
    ref="pieEchart"
    class="chart-box"
    id="ChartBox"
    :style="chartStyle"></div>
</template>

<script>
// import 'echarts/map/js/china.js'
import { getAction} from '@/api/manage'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import * as echarts from 'echarts'
import 'echarts/extension/bmap/bmap'
import '@/utils/bpMap/china'
import {PROVINCE_NAME_MAP_SIMPLE} from '@/store/area'
// import {
//     merge
// } from 'lodash'

export default {
    name: 'EchartMap',
    props: {
        options: {
            type: Object,
            default: ()=>{}
        },
        chartStyle: {
            type: Object,
            default: ()=>{
                return {width: '100%', 'height': '100%'}
            } 

        },
        data: {
            type: Object,
            default: ()=>{}
        }

    },
    data () {
        return {
            echart: null,
            geoCoordMap: PROVINCE_NAME_MAP_SIMPLE,
            echartOptions: {
                backgroundColor: '#404a59',
                tooltip: {
                    trigger: 'item'
                },
                geo: {
                    show: true,
                    map: 'china',
                    label: {
                        normal: {
                            show: true,
                            color: '#fff'
                        },
                        emphasis: {
                            show: true,
                            color: '#fff'
                        }
                    },
                    roam: 'scale',
                    itemStyle: {
                        normal: {
                            areaColor: '#031525',
                            borderColor: '#3B5077'
                        },
                        emphasis: {
                            areaColor: '#2B91B7'
                        }
                    }
                }, 
                series: [
                    {
                        name: '供应商数量',
                        type: 'scatter',
                        coordinateSystem: 'geo',
                        symbol: 'pin',
                        data: [],
                        symbolSize: function () {
                            return 20
                        },
                        encode: {
                            value: 2
                        },
                        label: {
                            formatter: (val) => {
                                return val.name
                            },
                            position: 'right',
                            show: false
                        },
                        emphasis: {
                            label: {
                                show: true
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#ddb926'
                            }
                        }
                    },
                    {
                        name: '供应商主要分部范围',
                        type: 'effectScatter',
          
                        coordinateSystem: 'geo',
          
                        rippleEffect: {
                            color: '#e43961',
                            brushType: 'stroke',
                            number: 2
                        },
                        data: [],
                        symbolSize: function () {
                            return 30
                        },
                        encode: {
                            value: 2
                        },
                        showEffectOn: 'render',
          
                        label: {
                            formatter: '{b}',
                            position: 'right',
                            show: true
                        },
                        itemStyle: {
                            normal: {
                                color: '#ddb926'
                            }
                        },
                        zlevel: 10
                    }

                ]
            }
        }
    },
    watch: {
        data: {
            handler (val) {
                if (val) {
                    this.setChartConfig(val)
                }
            },
            deep: true,
            immediate: true
        }
    },
  
    methods: {
        setChartDivReize (){
            // this.echart.resize()
            // document.getElementById('ChartBox').onresize=function (){
            //     console.log('123123')
            // }
        
            this.echart.resize()
  
        },
        setChartConfig (){
            let _this=this
            this.$nextTick(()=>{
                _this.echart = echarts.init(_this.$refs.pieEchart, null, {
                    width: 'auto',
                    height: 'auto'
                })
                _this.echartOptions={..._this.echartOptions, ..._this.options}
                _this.echartOptions.geo.roam='scale'
                // this.$nextTick(()=>{
                _this.echart.showLoading()
                setTimeout(()=>{
                    _this.echartOptions.series[0].data=_this.convertData(_this.data)

                    _this.echartOptions.series[1].data=_this.convertArryData(_this.data)
                    _this.echart.setOption(this.echartOptions)
                    _this.echart.resize()
                    _this.echart.hideLoading()
                    _this.setChartDivReize()
                }, 1000)

                
            })
     
        },
        convertArryData (data){
            let resKeysAry= Object.keys(data)
            let temArry=[]
            let res=[]
            for(let i of resKeysAry){
                temArry.push({name: i, value: data[i]})

            } 

            let sortDatas=temArry
                .sort(function (a, b) {
                    return b.value - a.value
                })
                .slice(0, 6)
            for(let i of sortDatas){
                let geoCoord = this.geoCoordMap[i.name]
                if (geoCoord&&i.value>0) {
                    res.push({

                        value: geoCoord.concat(i.value)
                    })
                }
            }
            return res
        },
        convertData (data) {
            let res = []
            let resKeysAry= Object.keys(data)
            for(let i of resKeysAry){
                let geoCoord = this.geoCoordMap[i]
                if (geoCoord) {
                    res.push({

                        value: geoCoord.concat(data[i])
                    })
                }
            
            }

            return res
        },
        clearChar (){
            this.echart.clear()
        }
    }
}
</script>
<style lang="less" scoped>
//   .chart-box{

//     height:489px;
//   }
//   .h291{
//     height:291px!important
//   }
</style>