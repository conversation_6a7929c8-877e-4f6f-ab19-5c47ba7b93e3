<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @loadSuccess="handleLoadSuccess"/>
    <!-- 行明细弹出选择框 -->
    <field-select-modal
      ref="fieldSelectModal"/>
    <!-- <a-modal
        v-drag
          centered
          :width="960"
          :maskClosable="false"
          :visible="flowView"
          @ok="closeFlowView"
          @cancel="closeFlowView">
          <iframe
            style="width:100%;height:560px"
            title=""
            :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
            frameborder="0"></iframe>
        </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'ViewPurcaseContractPromiseModal',
    components: {
        flowViewModal,
        fieldSelectModal
    },
    mixins: [DetailMixin],
    data () {
        return {
            showRemote: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZcVH_519d8b79`, '履约行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchasePromiseItemList',
                            columns: []
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                        type: 'primary',
                        click: this.cancelAudit,
                        id: 'cancelAudit',
                        showCondition: this.showcCncelConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                        type: '',
                        click: this.showFlow,
                        id: 'showFlow',
                        showCondition: this.showFlowConditionBtn
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                add: '/contract/purchaseContractPromise/add',
                edit: '/contract/purchaseContractPromise/edit',
                detail: '/contract/purchaseContractPromise/queryById',
                public: '/contract/purchaseContractPromise/publishEvent',
                upload: '/attachment/purchaseAttachment/upload',
                import: '/els/base/excelByConfig/importExcel',
                submitAudit: '/a1bpmn/audit/api/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_contractPromise_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    created () {
        // 如果是外部的参数，先请求获取模板js必须的参数
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.currentEditRow.sourceType = res.result.sourceType
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
        handleLoadSuccess (res) {
            this.currentEditRow.sourceType = res.res.result.sourceType
        },
        goBack () {
            this.$emit('hide')
        },
        // loadSuccess () {
        //     this.pageConfig = getPageConfig() // eslint-disable-line
        //     this.handlePageData(this.pageConfig)
        // },
        afterHandleData (data) {
            if (this.currentEditRow.sourceType != 'item') {
                data.groups.splice(1, 1)
            }
            this.init()
        },
        showcAuditConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus == '3' || auditStatus == '0') {
                return true
            } else {
                return false
            }
        },
        submitAudit () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterSubmittingApprovalItcannotIsurSubmitApproval?`, '提交审批后将不能修改，是否确认提交审批?'),
                onOk: function () {
                    that.auditPostData(that.url.submitAudit, 'audit')
                }
            })
        },
        auditPostData (invokeUrl, type) {
            this.$refs.detailPage.confirmLoading = true
            let formData = this.$refs.detailPage.form
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'contractPromise'
            param['auditSubject'] = '履约单号：' + formData.promiseNumber
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    if ( type == 'audit') {
                        this.$parent.submitCallBack(formData) 
                    } else if (res?.result?.auditStatus == '0') {
                        this.$parent.cancelCallBack(formData)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.init()
                this.$refs.detailPage.confirmLoading = false
            })
        },
        cancelAudit () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.auditPostData(that.url.cancelAudit, 'cancel')

                }
            })/*.finally(() => {
                that.init()
            })*/
        },
        showcCncelConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus != '1') {
                return false
            } else {
                return true
            }
        },
        showFlowConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            //  || auditStatus == '1'
            if (auditStatus == '2' || auditStatus == '3') {
                return false
            } else {
                return true
            }
        },
        showFlow () {
            this.flowId = this.$refs.detailPage.form.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        }
    }
}
</script>
