<!--
 * @Author: your name
 * @Date: 2022-04-02 18:11:46
 * @LastEditTime: 2022-04-02 18:19:30
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \srm-v5\src\views\srm\bidding_new\TenderProcessNode\modules\TenderProcessNodeDetail.vue
-->
<template>
  <div class="purchaseSupplierCapacityHead">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        @handleTabChange="handleTabChange"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'

export default {
    name: 'PurchaseSupplierCapacityHead',
    components: {
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            showNode: false,
            requestData: {
                detail: { url: '/tender/tenderProcessNode/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {

            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            nodeData: {},
            groupCode: '',
            periodTypeInfoArray: {},
            allNodeMap: {},
            url: {
                queryByGruop: '/tender/tenderProcessNode/queryNodeByGruop'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_tenderProcessNode_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
    }
}
</script>