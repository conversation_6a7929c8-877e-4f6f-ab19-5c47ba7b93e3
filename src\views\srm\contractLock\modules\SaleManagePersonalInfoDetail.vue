<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
export default {
    name: 'SubaccountCertificationEdit',
    mixins: [DetailMixin],
    data () {
        return {
            selectType: 'esignPersonCertification',
            pageData: {
                form: {
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_UzSQNJOHrA0MPEQA`, '个人认证信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    fieldName: 'subAccount'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterpriseName`, '企业名称'),
                                    fieldName: 'companyName'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_role`, '角色'),
                                    fieldName: 'roleStr_dictText'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDLiRL_29ec92a2`, '指定用户认证名称'),
                                    fieldName: 'applyUserName',
                                    required: '1',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDLiRL_29ec92a2`, '指定用户认证名称')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'),
                                    fieldName: 'applyContact',
                                    required: '1',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    fieldName: 'applyContactType',
                                    dictCode: 'contractLockContactType',
                                    required: '1',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KRLiCK_389fb71a`, '实名认证模式'),
                                    fieldName: 'mode',
                                    dictCode: 'contractLockMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KRLiCK_389fb71a`, '实名认证模式')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiqcrdW_254a9ced`, '认证可修改项'),
                                    fieldName: 'modifyFields',
                                    dictCode: 'contractLockModifyFields',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiqcrdW_254a9ced`, '认证可修改项')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                                    fieldName: 'paperType',
                                    dictCode: 'contractLockPaperType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PtLiCKqid_7031f7d2`, '降级认证方式可选项'),
                                    fieldName: 'otherModes',
                                    dictCode: 'contractLockOtherModes',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PtLiCKqid_7031f7d2`, '降级认证方式可选项')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDUziy_2a6f5b4a`, '指定用户身份证号'),
                                    fieldName: 'idCardNo',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDUziy_2a6f5b4a`, '指定用户身份证号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmy_2e1bfb0e`, '指定用户银行卡号'),
                                    fieldName: 'bankNo',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmy_2e1bfb0e`, '指定用户银行卡号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmUQlty_7742a9aa`, '指定用户银行卡预留手机号'),
                                    fieldName: 'bankMobile',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmUQlty_7742a9aa`, '指定用户银行卡预留手机号')
                                },
                                {
                                    
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_longLink`, '实名认证长链接'),
                                    extend: {
                                        linkConfig: {
                                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                            titleI18nKey: 'i18n_title_authenticationLink'
                                        },
                                        exLink: true
                                    },
                                    disabled: true,
                                    fieldName: 'certificationPageUrl'
                                }
                            ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/contractLock/saleClPersonalInfo/queryById'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>
