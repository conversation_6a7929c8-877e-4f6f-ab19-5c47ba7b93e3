<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="show"
        :ref="businessRefName"
        :remoteJsFilePath="remoteJsFilePath"
        :currentEditRow="currentEditRow"
        modelLayout="masterSlave"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :fromSourceData="fromSourceData"
        pageStatus="detail"
        v-on="businessHandler"
      >
        <template #noticeInfo="{ slotProps }">
          <j-editor
            ref="ueditor"
            v-model="noticeContent" />
        </template>
      </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit/>
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"/>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import JEditor from '@/components/els/JEditor'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { baseMixins } from '../../../plugins/baseMixins.js'
import flowViewModal from '@comp/flowView/flowView'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { BUTTON_PUBLISH } from '@/utils/constant.js'
import { merge } from 'lodash'
import { USER_INFO } from '@/store/mutation-types'

export default {
    name: 'ChangeTenderNoticeDetail',
    components: {
        BusinessLayout,
        JEditor,
        flowViewModal,
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    mixins: [businessUtilMixin, baseMixins],
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage', 'currentNode'],
    data () {
        return {
            flowView: false,
            flowId: 0,
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            show: false,
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.cancelAudit,
                    show: this.cancelButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.flowButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            currentGroupCode: {},
            noticeStatus: '',
            fromSourceData: {},
            noticeContent: '',
            url: {
                detail: '/tender/purchaseTenderNoticeHead/queryById',
                add: '/tender/purchaseTenderNoticeHead/add',
                edit: '/tender/purchaseTenderNoticeHead/edit',
                publish: '/tender/purchaseTenderNoticeHead/publish',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            },
            remoteJsFilePath: ''
        }
    },
    computed: {
        // remoteJsFilePath () {
        //     let templateNumber = 'TC2022051401'
        //     let templateVersion = 1
        //     let account = 100000
        //     return `${account}/purchase_purchaseTenderNotice_${templateNumber}_${templateVersion}`
        // }
        subId () {
            return this.subpackageId()
        },
        subpackage (){
            return this.currentSubPackage()
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            if(this.$ls.get(USER_INFO).elsAccount != this.tenderCurrentRow.purchaseExecutorAccount){
                pageConfig.groups=pageConfig.groups.filter(item=>{
                    return item.groupCode != 'purchaseTenderSupplierInvitation'
                })
            }
            console.log('pageConfig', pageConfig)
            // console.log('this.projectMemberPermission', this.projectMemberPermission)
            // if(!this.projectMemberPermission){
            //     pageConfig.groups = pageConfig.groups.filter(item=>{
            //         return item.groupCode != 'purchaseTenderSupplierInvitation'
            //     })
            //     console.log('pageConfig', pageConfig)
            //     console.log('进来了')

            // }

            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }

            let signUpFlag = (this.subpackage.signUp !== '1')
            let biddingFlag = (this.subpackage.bidding !== '1')
            // let none = (signUpFlag && biddingFlag)
            // 分包预审
            const checkType = this.subpackage.checkType
            // 隐藏数组内的字段
            let arr = []
            // 非报名情况, 投标邀请变更的预审环节
            if(signUpFlag || (this.noticeType && this.noticeType == '5' && checkType == '0')){
                arr=arr.concat('signUpBeginTime', 'signUpEndTime', 'signUpType')
            }
            //非购标情况
            if(biddingFlag){
                arr=arr.concat('biddingBeginTime', 'biddingEndTime', 'biddingType', 'offlineSaleAccount')
            }
            // 不报名不购标情况
            // if(none){
            //     arr=arr.concat('fileSubmitEndTime')
            // }
            let formFields = []
            pageConfig.groups[1].formFields.forEach(item => {
                if (arr.indexOf(item.fieldName) == -1) {
                    formFields.push(item)
                }
            })
            pageConfig.groups[1].formFields = formFields

            setDisabledByProp('signUpBeginTime', signUpFlag)
            setDisabledByProp('signUpEndTime', signUpFlag)
            setDisabledByProp('biddingBeginTime', biddingFlag)
            setDisabledByProp('biddingEndTime', biddingFlag)
            // setDisabledByProp('fileSubmitEndTime', none)
            // 无论什么情况，变更公告这里的报名方式和购标方式都不能操作，只能查看
            setDisabledByProp('biddingType', true)
            setDisabledByProp('signUpType', true)

            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }

                rule[prop] = [{
                    required: flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            //此处写根据分包信息作为判断展示位
            let signUpValidateFlag = (this.subpackage.signUp == '1')
            let biddingValidateFlag = (this.subpackage.bidding == '1')
            // let Or = (signUpValidateFlag || biddingValidateFlag)

            setValidateRuleByProp('signUpBeginTime', signUpValidateFlag)
            setValidateRuleByProp('signUpEndTime', signUpValidateFlag)
            setValidateRuleByProp('signUpType', signUpValidateFlag)
            setValidateRuleByProp('biddingBeginTime', biddingValidateFlag)
            setValidateRuleByProp('biddingEndTime', biddingValidateFlag)
            // setValidateRuleByProp('fileSubmitEndTime', Or)

        },
        //校验是否为项目成员 负责人或采购执行人
        async checkProjectMemberPermission (){
            let url ='/tender/project/purchaseTenderProjectMember/checkProjectMemberPermission'
            console.log(this.currentEditRow)
            await getAction(url, {projectId: this.subpackage.headId}).then(res=>{
                this.projectMemberPermission= res.result
            }).finally(()=>{

            })
        },
        //校验是否为项目成员 负责人或采购执行人
        // async checkProjectMemberPermission (){
        //     let url ='/tender/project/purchaseTenderProjectMember/checkProjectMemberPermission'
        //     console.log(this.currentEditRow)
        //     await getAction(url, {projectId: this.subpackage.headId}).then(res=>{
        //         this.projectMemberPermission= res.result
        //     }).finally(()=>{

        //     })
        // },
        init () {
            this.confirmLoading = true
            this.show = false
            return getAction(this.url.detail+'?id='+this.currentEditRow.id)
                .then(res => {
                    if (res.success) {
                        if (res.result) {
                            this.fromSourceData = res.result || {}
                            // this.noticeStatus = res.result.noticeStatus
                            this.noticeContent = res.result.noticeContent
                            // if (this.noticeStatus != '0') this.externalToolBar = {}
                        } else {
                            // this.noticeStatus = '0'
                        }
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.show = true
                })
        },
        cancelAudit ({ Vue, pageConfig, btn, groupCode }) {
            this.auditStatus = pageConfig.groups[0].formModel.auditStatus
            if(this.auditStatus != '1'){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APxOqXUz_5fba973e`, '当前不能撤销审批'))
                return
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit, pageConfig.groups[0].formModel)
                    // that.$refs.businessRefName.loadData()
                }
            })


        },
        cancelButtonShow ({ pageData }) {
            return pageData.auditStatus == '1'? true:false
        },
        flowButtonShow  ({ pageData }) {
            // 判断是否需要审批且有flowid，都具备才展示查看流程按钮
            if (pageData.audit != '1') {
                return false
            }

            if(pageData.noticeStatus != '0' && pageData.flowId){
                return true
            }
            return false
        },
        showFlow ({ Vue, pageConfig, btn, groupCode }) {
            this.flowId = pageConfig.groups[0].formModel.flowId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_APUziNxOmAQLW_25fa4a00`, '当前审批策略不能查看流程！'))
                return
            }
            this.flowView = true
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['rootProcessInstanceId'] = formData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.init()
                    this.$parent.showNoticeDetailPage = false
                    this.$parent.showNoticeEditPage = true
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
        // handleAfterDealSource (pageConfig, resultData) {
        //     console.log(pageConfig, resultData)
        // }
    },
    async created () {
        // this.noticeStatus = '0'
        await this.init()
        const currentEditRow = this.fromSourceData.templateNumber && this.fromSourceData.templateAccount ? {
            templateNumber: this.fromSourceData.templateNumber,
            templateName: this.fromSourceData.templateName,
            templateVersion: this.fromSourceData.templateVersion,
            templateAccount: this.fromSourceData.templateAccount
        } : await this.getBusinessTemplate('purchaseTenderNotice')
        if (!currentEditRow) {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
            return
        }
        this.currentEditRow = Object.assign(this.currentEditRow, currentEditRow)
        this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/purchase_purchaseTenderNotice_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
        // 查看当前登录人角色，供应商端的变更公告则不允许查看邀请名单
    }
    // async mounted (){
    //     await this.checkProjectMemberPermission()
    // }
}
</script>
