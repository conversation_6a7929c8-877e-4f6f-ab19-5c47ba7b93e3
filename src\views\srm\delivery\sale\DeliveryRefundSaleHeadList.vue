<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <View-Delivery-Refund-Sale-Modal
      ref="detailPage"
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetail"
    />
  </div>
</template>

<script>
import ViewDeliveryRefundSaleModal from './modules/ViewDeliveryRefundSaleModal'
import {listPageMixin} from '@comp/template/listPageMixin'

export default {
    name: 'DeliveryRefundSaleHeadList',
    mixins: [listPageMixin],
    components: {
        ViewDeliveryRefundSaleModal
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            pageData: {
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', folded: false, clickFn: this.handleExportXls},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: false, clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnDocNo`, '退货单号'),
                        fieldName: 'deliveryNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterReturnDocNo`, '请输入退货单号')
                    }, 
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                        fieldName: 'deliveryStatus',
                        dictCode: 'isrmDeliveryStatus',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')
                    }
                ],
                form: {
                    keyWord: ''
                },
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.showDetail}
                ]
            },
            url: {
                list: '/delivery/deliveryRefundSaleHead/list',
                delete: '/delivery/deliveryRefundSaleHead/delete',
                deleteBatch: '/delivery/deliveryRefundSaleHead/deleteBatch',
                exportXlsUrl: '/delivery/deliveryRefundSaleHead/exportXls',
                importExcelUrl: '/delivery/deliveryRefundSaleHead/importExcel',
                columns: 'isrmDeliveryRefundSaleHead'          
            }
        }
    },
    computed: {

    },
    created () {

    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnDoc`, '退货单'))
        },
        showDetail (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },
        hideDetail () {
            this.showDetailPage = false
        }
    }
}
</script>
