import EditLayout from './EditLayout.vue'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { handlePromise, inputNumberBlurMethod, getObjType } from '@/utils/util.js'
import { ajaxFindDictItems } from '@/api/api'
import { currency, formatFloat, formatCascader } from '@/filters'
import { BatchDownloadBtn } from '@comp/template/business/class/batchDownloadBtn'
import { ButtonComponent } from '@comp/template/business/class/ComponentFactory'
import { getAction, postAction } from '@/api/manage'
import { GRID_OPTION_ROW } from '@/utils/constant.js'
import { SALEATTACHMENTDOWNLOADAPI } from '@/utils/const' 
import LadderPrice from '@comp/LadderPrice'
export const EditMixin = {
    provide () {
        return {
            tplRootRef: this
        }
    },
    components: {
        EditLayout,
        remoteJs: {
            render (createElement) {
                var self = this
                return createElement('script', {
                    attrs: { type: 'text/javascript', src: this.src, async: true },
                    on: {
                        load: function (event) {
                            self.$emit('load', event)
                            self.$emit('success', event)
                        },
                        error: function (event) {
                            self.$message.warning('获取模板失败, 请检查:' + event)
                            self.$emit('error', event)
                        }
                    }
                })
            },
            props: {
                src: { type: String, required: true }
            }
        },
        LadderPrice
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    filters: {
        currency
    },
    computed: {
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow && this.currentEditRow.busAccount) {
                account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount || this.$ls.get(USER_ELS_ACCOUNT)
            }
            return account
        },
        // 判断是否为新建状态
        isAddStatus () {
            return !this.currentEditRow.id
        }
    },
    data () {
        return {
            pageConfig: {},
            editFormData: {},
            cascaderDictData: []
        }
    },
    mounted () {
        this.initBatchDownloadBtn()
    },
    methods: {
        currency,
        inputNumberBlurMethod,
        // 表格向下填充
        fillDownGridItem (info, otherParams) {
            new ButtonComponent().gridFillDown(info, otherParams)
        },
        // busAccount 国际化
        i18nBusAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow && this.currentEditRow.busAccount) {
                account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount || this.$ls.get(USER_ELS_ACCOUNT)
            }
            return account
        },
        init () {
            // queryDetail方法已经处理了id,可以直接调用
            if (this.currentEditRow) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id, (data) => {
                    this.editFormData = data
                })
            }
        },
        handleHeaderFields ({pageData, flag}) {
            let group = pageData.groups.find(rs => rs.groupType == 'head' && rs.groupCode == 'baseForm') || []
            let headerFields = group?.extend?.headerFields || []
            headerFields = headerFields.filter(rs => rs.type).map(rs => rs.fieldName)
            const formFields = pageData.formFields || []
            formFields.forEach(n => {
                if (headerFields.includes(n.fieldName)) {
                    n.disabled = flag
                }
            })
        },
        
        //批量下载 注入
        initBatchDownloadBtn () {
            this.pageData.groups.forEach((group) => {
                new BatchDownloadBtn().handelCreateBatchDownload(group)
            })
        },
        //配置加载成功执行
        loadSuccess () {
            this.pageConfig = getPageConfig() // eslint-disable-line
            if(!!this.afterLoadConfig) this.afterLoadConfig();
            this.handlePageData(this.pageConfig)
            if (this.isAddStatus) {
                this.init()
            }
        },
        //配置加载异常执行
        loadError (err) {
            console.error(err)
        },
        beforeHandleData (data) {
            console.log('beforeHandleData', data)
        },
        afterHandleData (data) {
            console.log('afterHandleData', data)
        },
        disableHandle (row) {
            // 根据业务下行的字段去改变selectModal 是否禁用，在自己业务下 重写此方法
        },
        // 提取表格编辑配置
        extractGridEditConfig (groups, currentGroup) {
            const that = this
            let editConfig = null
            groups.forEach((groupItem) => {
                // 表格编辑规则判断字段是配置项的值为gridEditConfig唯一，groupCode可任意取值，方便扩展gridEditConfig
                if (groupItem.groupType === 'gridEditConfig' && groupItem.groupCode === 'gridEditConfig') {
                    if (groupItem.extend) {
                        editConfig = {}
                        editConfig.trigger = groupItem.extend.editConfig.trigger || 'click'
                        editConfig.mode = groupItem.extend.editConfig.mode || 'cell'
                        editConfig.showStatus = groupItem.extend.editConfig.showStatus || true
                        if (groupItem.extend.editConfig.activeMethod) {
                            editConfig.activeMethod = (gridData) => {
                                return that.gridActiveMethod(gridData, groupItem.extend.editConfig.activeMethod, currentGroup)
                            }
                        }
                    }
                }
            })
            return editConfig
        },
        //处理当前pageData数据和配置数据
        handlePageData (data) {
            const that = this
            this.beforeHandleData(data)
            if (data?.groups?.length) {
                this.pageData.groups = data?.groups?.concat(this.pageData.groups)
            }
            // 区分表格行的编辑规则和表头分组
            let gridLineRule = this.pageData.groups.filter((groupItem) => {
                if (groupItem.groupType === 'gridEditConfig') {
                    return true
                }
            })
            this.pageData.groups = this.pageData.groups.filter((groupItem) => {
                if (groupItem.groupType !== 'gridEditConfig') {
                    return true
                }
            })
            this.pageData.formFields = data.formFields
            let fields = []
            this.pageData.groups.forEach((item) => {
                //行表列信息，目前固定为itemInfo
                if (item.groupType == 'item') {
                    let rules = {}
                    if (!item.custom) item.custom = {}
                    item.type = 'grid'
                    let columns = data.itemColumns.filter((column) => {
                        if (item.groupCode == 'itemInfo') {
                            return !column.groupCode
                        }

                        return column.groupCode == item.groupCode
                    })
                    columns.forEach((sub) => {
                        if (item.groupCode == 'itemInfo') {
                            sub.title = that.$srmI18n(`${that.i18nBusAccount()}#${sub.fieldLabelI18nKey}`, sub.title)
                        }
                        // 隐藏域处理
                        if (sub.fieldType == 'hiddenField' || sub.fold === '1') {
                            sub.visible = false
                        }
                        if (sub.required === '1' && sub.fieldType) {
                            let msg = that.$srmI18n(`${that.i18nBusAccount()}#${sub.fieldLabelI18nKey}`, sub.title)
                            let ruleRequired = that.$srmI18n(`${that.$getLangAccount()}#i18n_title_required`, '必填')
                            rules[sub.field] = [{ required: true, message: `${msg}${ruleRequired}!` }]
                        }
                        if (sub.regex) {
                            const prop = sub.field
                            rules[prop] = rules[prop] || []
                            let patternMsg = that.$srmI18n(`${that.i18nBusAccount()}#${sub.alertMsgI18nKey}`, sub.alertMsg)
                            rules[prop].push({
                                pattern: sub.regex,
                                message: patternMsg
                            })
                        }

                        if (sub.fixType) {
                            //add 新增编辑和详情冻结功能
                            sub.fixed = sub.fixType == 'unfrozen' ? '' : sub.fixType
                        } else {
                            if (sub.fixed == 'unfrozen') delete sub.fixed
                        }
                        if (sub.fieldValidator) {
                            const prop = sub.field
                            rules[prop] = rules[prop] || []
                            rules[prop].push({
                                validator: function ({ rule, cellValue, row, column }) {
                                    return that.initFieldValiRowRule(sub, rule, cellValue, sub.fieldValidator.validateMethod, row, column)
                                },
                                trigger: sub.fieldValidator.trigger || 'change'
                            })
                        }
                        // 隐藏有sub.slots，配置项与vxe-table的slots的配置参数一样
                        switch (sub.fieldType) {
                        case 'input':
                            sub.editRender = {
                                name: '$input',
                                // props: { clearable: true },
                                events: {
                                    change: (currentRow, currentValue) => {
                                        that.changeGridItem(currentRow, currentValue, sub.bindFunction)
                                    }
                                }
                            }
                            break
                        case 'textArea':
                            sub.editRender = {
                                name: 'srmTextArea',
                                events: {
                                    change: (currentRow, currentValue) => {
                                        that.changeGridItem(currentRow, currentValue, sub.bindFunction)
                                    }
                                }
                            }
                            break
                        case 'select':
                            sub.editRender = {
                                name: '$select',
                                options: [],
                                props: { 
                                    clearable: true,
                                    filterable: true,
                                    transfer: true // false不把容器插到body内
                                    // filterMethod: ({ searchValue, option, group }) => {
                                    //     const optionText = option.text || option.title || ''
                                    //     return vxeFilterOption(optionText, searchValue, group)
                                    // }
                                },
                                events: {
                                    change: (currentRow, currentValue) => {
                                        that.changeGridItem(currentRow, currentValue, sub.bindFunction)
                                    }
                                }
                            }
                            break
                        case 'multiple':
                            sub.editRender = {
                                name: '$select',
                                props: { multiple: true, clearable: true },
                                options: [],
                                events: {
                                    change: (currentRow, currentValue) => {
                                        that.changeGridItem(currentRow, currentValue, sub.bindFunction)
                                    }
                                }
                            }
                            break
                        case 'switch':
                            sub.cellRender = {
                                name: '$switch',
                                props: { openValue: '1', closeValue: '0' },
                                events: {
                                    change: (currentRow, currentValue) => {
                                        that.changeGridItem(currentRow, currentValue, sub.bindFunction)
                                    }
                                }
                            }
                            break
                        case 'date':
                            sub.editRender = {
                                name: 'mDatePicker',
                                props: {
                                    showTime: sub.dataFormat && sub.dataFormat.length > 10 ? true : false,
                                    valueFormat: sub.dataFormat && sub.dataFormat.length > 10 ? sub.dataFormat : null
                                },
                                events: {
                                    change: (currentRow, currentValue) => {
                                        that.changeGridItemOther(currentRow, currentValue, sub.bindFunction)
                                    }
                                }
                            }
                            break
                        case 'number':
                            // sub.editRender = { name: '$input', props: {type: 'number'}, events: {change: (currentRow, currentValue)=> {that.changeGridItem(currentRow, currentValue, sub.bindFunction)}}}
                            sub.editRender = { type: 'default' }
                            sub.slots = Object.assign({}, sub.slots, {
                                default: ({ row, rowIndex, column, columnIndex }, h) => {
                                    return [<span>{row[column.property]}</span>]
                                },
                                edit: ({ row, rowIndex, column, columnIndex }, h) => {
                                    const props = {
                                        type: 'number'
                                    }
                                    if (sub?.extend?.hasOwnProperty('min')) props.min = Number(sub.extend.min) == NaN ? -Infinity : Number(sub.extend.min)
                                    const on = {
                                        change: (currentValue) => {
                                            that.changeGridFloatItem(row, column, currentValue, sub.bindFunction)
                                        },
                                        blur: () => inputNumberBlurMethod({value: row[column.property], fieldLabel: column.title, type: 'column', fieldType: sub.fieldType, row, column })
                                    }
                                    return [<a-input-number autoFocus vModel={row[column.property]} {...{ props, on }} />]
                                }
                            })
                            break
                        case 'richEditorModel':
                            sub.slots = Object.assign({}, sub.slots, { default: 'rich_editor_col_render' })
                            break
                        case 'float': {
                            sub.editRender = { type: 'default' }
                            sub.slots = Object.assign({}, sub.slots, {
                                default: ({ row, column }, h) => {
                                    return [<span>{formatFloat(row[column.property], sub.dataFormat)}</span>]
                                },
                                edit: ({ row, column }, h) => {
                                    const on = {
                                        change: (currentValue) => {
                                            that.changeGridFloatItem(row, column, currentValue, sub.bindFunction)
                                        }
                                    }
                                    return [<a-input-number autoFocus vModel={row[column.property]} {...{ on }} />]
                                }
                            })
                            break
                        }
                        // 货币千分位
                        case 'currency':
                            sub.editRender = { type: 'default' }
                            sub.slots = Object.assign({}, sub.slots, {
                                default: ({ row, column }, h) => {
                                    let extend = sub.extend || {}
                                    let symbol = extend && extend.symol || ''
                                    let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
                                    return [<span>{currency(row[column.property], symbol, decimals)}</span>]
                                },
                                edit: ({ row, column }, h) => {
                                    const on = {
                                        change: (currentRow, currentValue) => {
                                            that.changeGridFloatItem(currentRow, currentValue, sub.bindFunction)
                                        }
                                    }
                                    return [<a-input-number vModel={row[column.property]} {...{ on }} />]
                                }
                            })
                            break
                        case 'remoteSelect': {
                            sub.editRender = {}
                            const remoteSelectSlotsFuntion = function ({ row, rowIndex, column, columnIndex }) {
                                const props = {
                                    config: sub,
                                    row: row,
                                    form: that.editFormData,
                                    isRow: true
                                }
                                
                                const on = {
                                    afterClearCallBack: () => {sub?.extend?.afterRowClearCallBack && sub.extend.afterRowClearCallBack(that, row, column, rowIndex, columnIndex, that.editFormData)},
                                    ok: (data) => {
                                        sub.bindFunction && sub.bindFunction(row, data, that, that.editFormData)
                                    },
                                    change: (value) => {
                                        row[column.property] = value
                                    }
                                }
                                return [
                                    <m-remote-select {...{ props, on }}   value={row[column.property]} />
                                ]
                            }
                            sub.slots = Object.assign({}, sub.slots, {
                                default: ({row, column}) => [
                                    <span>{row[column.property]}</span>
                                ],
                                edit: (info) => {
                                    return remoteSelectSlotsFuntion(info)
                                }
                            })
                            break
                        }
                        case 'selectModal': {
                            sub.editRender = {}
                            const selectModalSlotsFuntion = function ({ row, rowIndex, column, columnIndex }, status) {
                                const isDisable = that.disableHandle(row, rowIndex, column, columnIndex) || false
                                const afterRowClearCallBack = sub.extend && sub.extend.afterRowClearCallBack
                                const scopedSlots = {
                                    default: ({ openFunc }) => {
                                        const text = status === 'edit' ? [<a-input title={row[column.property]} disabled style="text-align: center;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;" vModel={row[column.property]} bordered={false} />] : [<span>{row[column.property]}</span>]
                                        const closeIcon = row[column.property] && status === 'edit'?
                                            [<a-icon
                                                type="close-circle" 
                                                style="position: absolute;display: inline-block;font-size: 14px;right: 27px; top: 50%;transform:translateY(-50%);z-index: 2; cursor:pointer"
                                                onClick={(event) => {
                                                    event.stopPropagation()
                                                    getObjType(afterRowClearCallBack) === 'function' && afterRowClearCallBack(that, row, column, rowIndex, columnIndex, that.editFormData)
                                                }}
                                            />]
                                            : []
                                        return (
                                            <div title={row[column.property]} style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                                                {text}
                                                {closeIcon}
                                                <a disabled={isDisable} style="position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 2;">
                                                    <a-icon
                                                        type="file-search"
                                                        onClick={(event) => {
                                                            event.stopPropagation()
                                                            openFunc && openFunc()
                                                        }}
                                                    />
                                                </a>
                                            </div>
                                        )
                                    }
                                }
                                const props = {
                                    config: sub,
                                    row: row,
                                    form: that.editFormData,
                                    isRow: true
                                }
                                
                                const on = {
                                    afterClearCallBack: (cb) => cb && cb(row, column, rowIndex, columnIndex, that.editFormData),
                                    ok: (data) => {
                                        sub.bindFunction && sub.bindFunction(row, data, that, that.editFormData)
                                    }
                                }

                                return [<m-select-modal scopedSlots={scopedSlots} {...{ props, on }} />]
                            }
                            sub.slots = Object.assign({}, sub.slots, {
                                default: (info) => {
                                    return selectModalSlotsFuntion(info, 'default')
                                },
                                edit: (info) => {
                                    return selectModalSlotsFuntion(info, 'edit')
                                }
                            })
                            break
                        }
                        case 'cascader':
                            that.queryCascaderDictData(sub)
                            sub.editRender = { enabled: true}
                            sub.slots = Object.assign({}, sub.slots, {
                                default: ({ row, rowIndex, column, columnIndex }, h) => {
                                    const currentVal = row[column.property]
                                    return  formatCascader(currentVal, that.cascaderDictData, 'value', 'title' )
                                },
                                edit: ({row, column}, h) => {
                                    const props = {
                                        // mode: col.dictCode,
                                        fieldNames: { label: 'title', value: 'value', children: 'children' },
                                        options: this.cascaderDictData
                                    }
                                    const on = {
                                        change: (currentValue, selectedOptions) => {
                                            that.changeCascaderValue(row, currentValue, selectedOptions, sub.bindFunction)
                                        }
                                    }
                                    return [
                                        <m-cascader
                                            change-on-select
                                            vModel = {row[column.property]}
                                            { ...{ props, on } }
                                        />
                                    ]
                                }
                            })
                            break
                        case 'customSelect':
                            sub.editRender = {
                                name: 'customSelect',
                                options: [],
                                events: {
                                    change: (currentRow, currentValue) => {
                                        that.changeGridItemCustom(currentRow, currentValue, sub.bindFunction)
                                    }
                                }
                            }
                            break
                        case 'computed':
                            sub.editRender = {
                                name: '$input', props: { type: 'number' },
                                events: {
                                    blur: (currentRow) => {
                                        that.blurGridItem(currentRow, sub, item)
                                    }
                                }
                            }
                            break
                        case 'ladderPrice': // 阶梯价格类型
                            sub.slots = this.ladderPriceModalSlots(sub)
                            break
                        }
                        // 没有slot，补充一个空对象
                        if (sub.editRender && !sub.slots) {
                            sub.slots = {}
                        }
                    })
                    if (!item.custom.ref) {
                        item.custom.ref = item.groupCode
                    }
                    item.custom.rules = rules
                    const showGridFooter = this.$refs?.editPage?.showGridFooter || false 
                    if (!item.custom.notShowTableSeq) {
                        item.custom.columns = [
                            { type: 'checkbox', width: showGridFooter ? 50 : 40, fixed: 'left' },
                            { type: 'seq', fixed: 'left', width: 60, title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_seq`, '序号') }
                        ].concat(columns)
                    } else {
                        item.custom.columns = [{ type: 'checkbox', width: showGridFooter ? 50 : 40, fixed: 'left' }].concat(columns)
                    }
                    // 增加表行默认按钮，默认增加添加和删除
                    if (!item.custom.buttons) {
                        item.custom.buttons = [
                            {
                                title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_add`, '添加'),
                                type: 'primary',
                                click: () => {
                                    that.addItemMixin(item.custom.ref)
                                }
                            },
                            {
                                title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_delete`, '删除'),
                                click: () => {
                                    that.deleteItemMixin(item.custom.ref)
                                }
                            }
                        ]
                        if(item.groupCode == 'purchaseMaterialBrandList' && item.groupName == '品牌信息') {
                            item.custom.buttons.push({
                                title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_import`, '导入'),
                                type: 'primary',
                                click: () => {
                                    if(that.purchaseMaterialBrandListImport)
                                    that.purchaseMaterialBrandListImport()
                                }
                            })
                        }
                    }
                    this._addFoldOptColumn(columns, item)
                }

                if (item.groupCode == 'purchaseDeliverySubList' || item.groupCode == 'saleDeliverySubList') {
                    let rules = {}
                    let columns = item.custom.columns.filter((column) => {
                        return !!column.fieldType;
                    })

                    columns.forEach((sub) => {
                        if (sub.required === '1' && sub.fieldType) {
                            let msg = sub.title;
                            let ruleRequired = that.$srmI18n(`${that.$getLangAccount()}#i18n_title_required`, '必填')
                            rules[sub.field] = [{ required: true, message: `${msg}${ruleRequired}!` }]
                        }
                        switch(sub.fieldType) {
                            case 'number':
                                sub.editRender = { type: 'default' }
                                sub.slots = Object.assign({}, sub.slots, {
                                    default: ({ row, rowIndex, column, columnIndex }, h) => {
                                        return [<span>{row[column.property]}</span>]
                                    },
                                    edit: ({ row, rowIndex, column, columnIndex }, h) => {
                                        const props = {
                                            type: 'number'
                                        }
                                        if (sub?.extend?.hasOwnProperty('min')) props.min = Number(sub.extend.min) == NaN ? -Infinity : Number(sub.extend.min)
                                        const on = {
                                            change: (currentValue) => {
                                                that.changeGridFloatItem(row, column, currentValue, sub.bindFunction)
                                            },
                                            blur: () => inputNumberBlurMethod({value: row[column.property], fieldLabel: column.title, type: 'column', fieldType: sub.fieldType, row, column })
                                        }
                                        return [<a-input-number autoFocus vModel={row[column.property]} {...{ props, on }} />]
                                    }
                                })
                            break;

                            case 'date':
                                sub.editRender = {
                                    name: 'mDatePicker',
                                    props: {
                                        showTime: sub.dataFormat && sub.dataFormat.length > 10 ? true : false,
                                        valueFormat: sub.dataFormat && sub.dataFormat.length > 10 ? sub.dataFormat : null
                                    },
                                    events: {
                                        change: (currentRow, currentValue) => {
                                            that.changeGridItemOther(currentRow, currentValue, sub.bindFunction)
                                        }
                                    }
                                }
                            break;

                            case 'input':
                                sub.editRender = {
                                    name: '$input',
                                    events: {
                                        change: (currentRow, currentValue) => {
                                            that.changeGridItem(currentRow, currentValue, sub.bindFunction)
                                        }
                                    }
                                }
                            break;
                        }
                    })
                    item.custom.rules = rules
                }

                //表单信息根据分组编码分类
                if (item.type != 'grid') {
                    fields = data.formFields.filter((item2) => {
                        return item.groupCode == item2.groupCode
                    })
                    // 过滤配置为图片类型的字段，并塞在数组最后面
                    let fieldTypeArr = ['hiddenField', 'image', 'textArea']
                    let sortArr = fields.filter((n) => fieldTypeArr.includes(n.fieldType))
                    let otherFields = fields.filter((n) => !fieldTypeArr.includes(n.fieldType))
                    let lastFields = []
                    let textAreaFields = []
                    sortArr.forEach((rs) => {
                        if (rs.fieldType == 'hiddenField') {
                            //隐藏域字段放最后
                            lastFields.push(rs)
                        } else if (rs.fieldType == 'image') {
                            lastFields.unshift(rs)
                        } else if (rs.fieldType == 'textArea') {
                            textAreaFields.push(rs)
                        }
                    })
                    lastFields = lastFields.concat(textAreaFields)
                    fields = otherFields.concat(lastFields)

                    item.custom = {
                        formFields: fields,
                        form: {},
                        validateRules: {}
                    }
                    fields.forEach((item3) => {
                        if (item3.fieldType === 'treeSelect') {
                            item3.multiple = item3.multiple || false
                        }
                        // 防止switch类型字段没有默认值
                        if (item3.fieldType === 'switch') {
                            item3.defaultValue = item3.defaultValue ? item3.defaultValue : '0'
                        }
                        item.custom.form[item3.fieldName] = item3.defaultValue
                        if (item3.required === '1') {
                            let msg = that.$srmI18n(`${that.i18nBusAccount()}#${item3.fieldLabelI18nKey}`, item3.fieldLabel)
                            let ruleRequired = that.$srmI18n(`${that.$getLangAccount()}#i18n_title_required`, '必填')
                            item.custom.validateRules[item3.fieldName] = [{ required: true, message: `${msg}${ruleRequired}!` }]
                        }
                        if (item3.regex) {
                            const prop = item3.fieldName
                            item.custom.validateRules[prop] = item.custom.validateRules[prop] || []
                            let patternMsg = that.$srmI18n(`${that.i18nBusAccount()}#${item3.alertMsgI18nKey}`, item3.alertMsg)
                            item.custom.validateRules[prop].push({
                                pattern: item3.regex,
                                message: patternMsg
                            })
                        }
                        if (item3.fieldValidator) {
                            if (Array.isArray(item.custom.validateRules[item3.fieldName])) {
                                item.custom.validateRules[item3.fieldName].push({
                                    validator: function (rule, value, callback) {
                                        return that.initFieldValidateRule(item3, rule, value, callback, item3.fieldValidator.validateMethod)
                                    },
                                    trigger: item3.fieldValidator.trigger || 'change'
                                })
                            } else {
                                item.custom.validateRules[item3.fieldName] = [
                                    {
                                        validator: (rule, value, callback) => {
                                            return that.initFieldValidateRule(item3, rule, value, callback, item3.fieldValidator.validateMethod)
                                        },
                                        trigger: item3.fieldValidator.trigger || 'change'
                                    }
                                ]
                            }
                        }
                    })
                } else {
                    console.log(item)
                    item.custom.editConfig = that.extractGridEditConfig(gridLineRule, item)
                    item.custom.columns.forEach((sub) => {
                        // 默认设置slot为空
                        // sub.slots = Object.assign({}, sub.slots)
                        // 隐藏域处理
                        if (sub.fieldType == 'hiddenField') {
                            sub.visible = false
                        }
                        // 兼容处理国际化
                        if (sub.fieldLabelI18nKey) {
                            sub.title = that.$srmI18n(`${that.i18nBusAccount()}#${sub.fieldLabelI18nKey}`, sub.title)
                        }
                        if (sub.slots) {
                            sub.slots.header = ({ column }) => {
                                const flag = !!sub.helpText
                                const dom = flag ? (
                                    <vxe-tooltip content={sub.helpText}>
                                        <span style="display: flex; alignItems: center;">
                                            <i class="vxe-icon--question"></i>
                                            <span style="marginLeft: 6px;">{column.title}</span>
                                        </span>
                                    </vxe-tooltip>
                                ) : (
                                    <span> {column.title}</span>
                                )
                                return [dom]
                            }
                        }
                    })
                }
            })
            this.pageData.groups.sort(function(a, b) {
                return a.sortOrder - b.sortOrder;
            })
            this.setGroupFieldNameDictionaryData()

            this.afterHandleData(this.pageData)
        },
        _addFoldOptColumn (columns, item) {
            // 增加表行默认操作列
            if (columns.find((rs) => rs.fold && rs.fold === '1')) {
                item.custom.optColumnList = item.custom.optColumnList || []
                if (!columns.find((rs) => rs.field === 'grid_opration')) {
                    let customOption = {
                        fixed: 'right'
                    }
                    customOption = { ...GRID_OPTION_ROW, ...customOption }
                    item.custom.columns = item.custom.columns.concat(customOption)
                }
                item.custom.optColumnList.push({
                    type: 'fold',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_HOJO_3026ba64`, '更多字段'),
                    clickFn: this.openFoldDrawer
                })
            }
        },
        // 设置 group 数据字典值
        setGroupFieldNameDictionaryData () {
            let dictCodeItems = []
            const groups = this.pageData.groups || []
            groups.forEach((group) => {
                let flag = group.type && group.type === 'grid'
                const columns = flag ? group.custom.columns : group.custom.formFields

                return (
                    columns
                        .filter((sub) => !!sub.dictCode)
                        // 过滤 select类型，组件内会自动获取下拉字典选项值
                        .filter((sub) => sub.fieldType !== 'switch') // 过滤 switch
                        .forEach((sub) => {
                            dictCodeItems.push({
                                item: sub,
                                dictCode: sub.dictCode
                            })
                        })
                )
            })

            const promises = dictCodeItems.map((n) => {
                let params = {
                    busAccount: this.busAccount,
                    dictCode: n.dictCode
                }
                return ajaxFindDictItems(params)
            })
            if (this.$refs?.editPage?.confirmLoading !== undefined) {
                this.$refs.editPage.confirmLoading = true
            }
            Promise.all(handlePromise(promises))
                .then((res) => {
                    const result = res
                        // .filter((n) => n.status === "success")
                        .map((n) => n.res.result)

                    this.asyncSetFieldOptions(dictCodeItems, result)
                })
                .finally(() => {
                    if (!this.isAddStatus) {
                        this.init()
                    } else if (this.$refs?.editPage?.confirmLoading !== undefined) {
                        this.$refs.editPage.confirmLoading = false
                    }
                })
        },
        // 获取级联下拉数据字典
        queryCascaderDictData (column) {
            const that = this
            if (column && column.dictCode) {
                let postData = {
                    busAccount: that.busAccount || that.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: column.dictCode
                }
                ajaxFindDictItems(postData).then(res => {
                    if(res.success) {
                        that.cascaderDictData = res.result
                    }
                })
            } else {
                this.cascaderDictData = []
            }
        },
        // 闭包处理接口异步返回字典数据
        asyncSetFieldOptions (dictCodeItems = [], result = []) {
            dictCodeItems.forEach((v, idx) => {
                if (!v.item.options) v.item.options = []
                let options = []
                if (result && result[idx]) {
                    options = result[idx].map((n) => ({
                        ...n,
                        label: n.title,
                        disabled: false
                    }))
                }
                v.item.options = options
                if (v.item.editRender) {
                    v.item.editRender.options = options
                }
            })
            this.$nextTick(() => {
                this.$forceUpdate()
            })
        },
        //超链接跳转方法
        getNewRouter (col, row, column, linkConfig) {
            if (col?.extend?.handleBefore && typeof col?.extend?.handleBefore === 'function') {
                let callbackObj = col?.extend?.handleBefore(row, column, linkConfig, this, { pageConfig: this.pageConfig }) || {}
                linkConfig = { ...linkConfig, ...callbackObj }
            }
            console.log('[col,row,column,linkConfig]', col, row, column, linkConfig)
            if (row[column.property] && linkConfig.actionPath && linkConfig.bindKey) {
                let query = {
                    [linkConfig.primaryKey]: row[linkConfig.bindKey],
                    ...linkConfig.otherQuery,
                    t: new Date().getTime()
                }
                this.$router.push({ path: linkConfig.actionPath.trim(), query: { ...query } })
            }
        },
        //超链接slots
        linkModalSlots (col) {
            console.log('[slots执行]')
            const that = this
            let linkConfig = {
                title: '认证链接',
                titleI18nKey: 'i18n_title_authenticationLink',
                primaryKey: 'id', //查询用的主键名
                bindKey: 'fbk1', //绑定的主键key
                actionPath: '', //目标路由
                otherQuery: { open: true } //其余参数
            }
            let exLink = false
            if (col.extend && col.extend.linkConfig) linkConfig = { ...linkConfig, ...col.extend.linkConfig }
            return Object.assign({}, col.slots, {
                default: ({ row, rowIndex, column, columnIndex }) => {
                    if (exLink) {
                        return [
                            <a href={row[column.property]} target="_blank">
                                <span>{that.$srmI18n(`${that.$getLangAccount()}#${linkConfig.titleI18nKey}`, linkConfig.title)}</span>
                            </a>
                        ]
                    } else {
                        return [
                            <a
                                onClick={() => {
                                    that.getNewRouter(col, row, column, linkConfig)
                                }}
                            >
                                {row[column.property]}
                            </a>
                        ]
                    }
                }
            })
        },

        /**
         *form表单字段填充自定义验证方法
         * @param {*} _this
         * @param {*} form
         * @param {*} item3
         * @param {*} rule
         * @param {*} value
         * @param {*} callback
         * @param {*} validMethod
         * {
                validateMethod: function (taht, form, currentField, rule, value, callback) {
                    let startTime = new Date(form.checkStartDate).getTime()
                    let endTime = new Date(form.checkEndDate).getTime()
                    if (startTime> endTime) {
                        callback(new Error('开始时间不能大于结束时间'))
                    } else {
                        callback()
                    }
                },
                trigger: 'change'
            }
         */
        initFieldValidateRule (item, rule, value, callback, validMethod) {
            if (!validMethod) {
                return callback()
            } else {
                return validMethod(this, this.$refs.editPage.form, item, rule, value, callback)
            }
        },
        /**
         * 特殊功能存放处
         */
        // 表格计算属性失去焦点时-成本报价
        blurGridItem (data, column, group) {
            const that = this
            let url = '/formula/compute'
            let postData = {
                templateNumber: this.currentEditRow.templateNumber,
                templateVersion: this.currentEditRow.templateVersion,
                busAccount: this.currentEditRow.templateAccount || this.currentEditRow.busAccount || this.busAccount,
                field: column.field,
                row: data.row,
                ref: group.groupCode
            }
            postAction(url, postData).then((res) => {
                if (res.success) {
                    if (res.result) {
                        for (let key in res.result.row) {
                            data.row[key] = res.result.row[key]
                        }
                        // if (that.$refs[group.groupCode + 'grid']) {
                        //     let grid = that.$refs[group.groupCode + 'grid'][0].$children[0]
                        //     let countData = {}
                        //     let tableData = grid.getTableData().tableData
                        //     countData[group.groupCode] = tableData
                        //     that.countTotalValue(countData, group)
                        // }
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        // 单表格汇总-成本报价
        countTotalValue (data, group) {
            let total = null
            data[group.groupCode].forEach((data) => {
                if (data[group.total.culumnName]) {
                    if (data[group.total.culumnName]) {
                        total += Number(data[group.total.culumnName])
                    }
                }
            })
            group.total.totalValue = total
        },
        // 表行字段自定义验证方法
        /**
         *
         *
         * @param {*} _this
         * @param {*} item
         * @param {*} rule
         * @param {*} value
         * @param {*} row
         * @param {*} column
         * @param {*} validMethod
         *  {
                validateMethod: function (that, item, rule, value, row, column) {
                    // 编辑态下，that可以获取当前页面所有值
                    if (row.effectiveDate && row.expiryDate) {
                        let effectiveDate = new Date(row.effectiveDate).getTime()
                        let expiryDate = new Date(row.expiryDate).getTime()
                        if (effectiveDate > expiryDate) {
                            return  new Error('生效日期不能大于失效日期')
                        }
                    }
                },
                trigger: 'change' // blur,change,manual
            }
         */
        initFieldValiRowRule (item, rule, value, validMethod, row, column) {
            if (validMethod) {
                return validMethod(this, item, rule, value, row, column)
            }
        },
        goBack () {
            this.$emit('hide')
        },
        downloadSaleEvent (row){
            this.$refs.editPage.handleDownload(row, SALEATTACHMENTDOWNLOADAPI)
        },
        openFoldDrawer (row, column) {
            this.$refs.editPage.$refs?.GridFoldDrawer.openFoldDrawer(row, column)
        },
        prevEvent () {
            this.$refs.editPage.prevStep()
        },
        nextEvent () {
            this.$refs.editPage.nextStep()
        },
        downloadEvent (row) {
            this.$refs.editPage.handleDownload(row)
        },
        preViewEvent (row) {
            this.$previewFile.open({ params: row })
        },
        deleteFilesEvent ({ id = '', refName = '' }) {
            getAction('/attachment/purchaseAttachment/delete', { id: row.id }).then((res) => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    try {
                        const fileGrid = this.getItemGridRef(refName)
                        fileGrid && fileGrid.remove(row)
                    } catch (err) {
                        console.log('err :>> ', err)
                    }
                }
            })
        },
        getItemGridRef (ref) {
            return this.$refs.editPage.$refs[ref][0]
        },
        // 默认表行增加
        addItemMixin (refName) {
            if (refName) {
                let itemGrid = this.$refs.editPage.$refs[refName][0]
                let itemData = {}
                this.pageConfig.itemColumns.forEach((item) => {
                    if (item.groupCode == refName && item.defaultValue) {
                        // 特殊多选默认值
                        if (item.fieldType === 'multiple' && typeof item.defaultValue === 'string') {
                            itemData[item.field] = item.defaultValue.split(',')
                            console.log(itemData[item.field])
                        } else {
                            itemData[item.field] = item.defaultValue
                        }
                    }
                })
                itemGrid.insert([itemData])
            }
        },
        // 默认表行删除
        deleteItemMixin (refName) {
            if (refName) {
                let itemGrid = this.$refs.editPage.$refs[refName][0]
                let checkboxRecords = itemGrid.getCheckboxRecords()
                if (!checkboxRecords.length) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                    return
                }
                itemGrid.removeCheckboxRow()
            }
        },
        // selectModal change 事件
        changeSelectModal (row, data, cb) {
            cb && cb(row, data, this, this.editFormData)
        },
        // 表行编辑change事件
        changeGridItem (currentRow, currentValue, cb) {
            cb && cb(currentRow.row, currentRow.column, currentValue.value, this)
        },
        // 表行编辑  Cascader
        changeCascaderValue (currentRow, currentValue, selectedOptions, cb) {
            cb && cb(currentRow, selectedOptions, currentValue, this)
        },
        // 表行编辑float小数change事件
        changeGridFloatItem (currentRow, currentColumn, currentValue, cb) {
            cb && cb(currentRow, currentColumn, currentValue, this)
        },
        // 表行自定义编辑change事件
        changeGridItemCustom (currentRow, currentValue, cb) {
            cb && cb(currentRow.row, currentRow.column, currentValue, this)
        },
        // 表行编辑change事件 其他类型 date
        changeGridItemOther (currentRow, currentValue, cb) {
            cb && cb(currentRow, null, currentValue, this)
        },
        // 表格编辑控制，切记不要写成循环
        gridActiveMethod (gridData, cb, currentGroup) {
            let that = this
            let { row, _rowIndex, column, _columnIndex } = gridData
            return cb(that, row, _rowIndex, column, _columnIndex, currentGroup)
        },
        // 阶梯价格slots
        ladderPriceModalSlots (col) {
            const that = this
            return Object.assign({}, col.slots, {
                default: ({ row, rowIndex, column, columnIndex }) => {
                    const scopedSlots = {
                        default: ({ openFunc }) => {
                            const tpl = (
                                <div style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                                    <a-tooltip placement="topLeft">
                                        <template slot="title">
                                            <span>{row[column.property]}</span>
                                        </template>
                                        {row[column.property]}
                                    </a-tooltip>
                                    <a style="position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 10;">
                                        <a-icon
                                            type="stock"
                                            onClick={() => {
                                                openFunc && openFunc(row[column.property])
                                            }}
                                        />
                                    </a>
                                </div>
                            )
                            return tpl 
                        }
                    }
                    const props = {
                        config: col,
                        row: row,
                        pageData: that.pageData,
                        form: that.editFormData,
                        isRow: true
                    }
                    const on = {
                        afterClearCallBack: (cb) => {
                            if (cb && typeof cb === 'function') {
                                cb(that, row, col, rowIndex, columnIndex)
                            }
                        },
                        ok: (data) => {
                            if (col.bindFunction && typeof col.bindFunction === 'function') {

                                col.bindFunction && col.bindFunction(row, data, that)
                            }
                        }
                    }
                    const $modalBox = <ladder-price scopedSlots={scopedSlots} {...{ props, on }} />
                    return [$modalBox]
                }
            })
        }
    }
}
