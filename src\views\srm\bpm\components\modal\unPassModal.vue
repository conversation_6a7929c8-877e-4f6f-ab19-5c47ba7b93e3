<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_priorityLevel`, '优先级')"
          prop="priority">
          <a-radio-group
            name="radioGroup"
            defaultValue="50"
            v-model="form.priority">
            <a-radio
              v-for="(item, index) in priorityMap"
              :key="index"
              :value="item.value">{{
                item.title
              }}</a-radio>
          </a-radio-group>
        </a-form-model-item>

        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_taskName`, '任务标题')"
          prop="taskTitle">
          <a-input
            v-model="form.taskTitle"
            clearable />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见')"
          prop="opinion">
          <a-textarea
            show-word-limit
            v-model="form.opinion"
            allowClear
            type="textarea"
            autoSize />
        </a-form-model-item>
        <a-form-model-item
          i18n_field_BdL_183905d
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_BdL_183905d`, '抄送人')"
          prop="usersInfo">
          <a-tag
            v-for="(users, userIndex) in form.usersInfo"
            :key="users.id"
            size="large"
            color="blue"
            closable
            @close="delUsers(userIndex)"
          >{{ users.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectUsers()"></a-button>
          </div>
        </a-form-model-item>
        <a-form-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_accessory`, '附件')"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 14 }">
          <div class="dropbox">
            <a-upload
              name="file"
              :multiple="true"
              :action="uploadUrl"
              :headers="uploadHeader"
              :accept="accept"
              :data="{headId: headId, businessType: businessType}"
              @change="handleUploadChange"
            >
              <a-button> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
            </a-upload>
          </div>
        </a-form-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_HjU_1706bfd`, '常用语')"
          v-if="approval && approval.length > 0">
          <a-tag
            v-for="(item, index) in approval"
            color="primary"
            :key="index"
            type="border"
            @click.native="selectTag(item)"
          >
            {{ item.content }}
          </a-tag>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { completeTask } from '../../api/analy'
// import { listVariable } from '../../api/variable.js'
export default {
    mixins: [modalMixins],
    data () {
        return {
            bpmVarList: [],
            approval: [],
            relatedId: '',
            relatedType: '',
            nodeIndex: null,
            monitor: false,
            userShow: false,
            existData: null,
            labelCol: { span: 6 },
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            single: false,
            fileList: [],
            selectTagType: '',
            rules: {
                taskTitle: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMLSBD_9062f13c`, '请填写任务标题'), trigger: 'change' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                opinion: [
                    // { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMqdWII_13f544fb`, '请填写备注/意见'), trigger: 'change' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ]
                // usersInfo: [{ required: true, message: '请选择处理人' } ]
            }
        }
    },
    computed: {
        headId () {
            if (this.currentEditRow?.taskId) {
                return this.currentEditRow?.taskId
            }
            return ''
        },
        businessType () {
            if (this.taskInfo.businessType) {
                return this.taskInfo.businessType
            }
            return ''
        }
    },
    methods: {
        delUsers (userIndex) {
            this.form.usersInfo.splice(userIndex, 1)
        },
        selectUsers () {
            this.selectTagType = 'users'
            this.showUserSelectModal({ selectModel: 'multiple' })
        },
        fieldSelectOk (data) {
            if (this.selectTagType == 'users') {
                this.$set(this.form, 'usersInfo', data)
            }
        },
        selectTag (data) {
            this.form.opinion = data.content
        },
        async agreeHandler () {
            this.cheackValidate().then(() => {
                this.form.chooseNode = ''
                this.form.chooseNodeUser = ''
                this.completeTask()
            })
        },
        completeTask () {
            // new Promise((resolve, reject) => {
            const params = Object.assign({}, this.form, this.task)
            params['monitor'] = this.monitor
            delete params['usersIds']
            delete params['userNames']
            params.opinion = params.opinion ? params.opinion : '无'
            let attachmentDetailedList = this.fileList.map(item => {
                let {fileName, filePath, id} = item.response.result
                return {
                    name: fileName,
                    url: filePath,
                    id
                }
            })
            params.attachmentDetailedList = attachmentDetailedList.length > 0 ? attachmentDetailedList : []
            this.loading = true
            delete params['chooseNode']
            delete params['chooseNodeUser']
            completeTask(params).then(response => {
                this.loading = false
                if (response.code == 200) {
                    this.$emit('success')
                    this.$message.success(response.message)
                } else {
                    if(!!response.message && response.message.indexOf("\n") >= 0) {
                      const h = this.$createElement;
                      let strList = response.message.split("\n");
                      strList = strList.map((str, strIndex) => {
                          return h(strIndex === 0? 'span' : 'div',null,str);
                      })
                      this.$message.error(h('span', null, strList))
                      return;
                    }
                    this.$message.error(response.message)
                }
            }).finally(() => {
                this.loading = false
                this.$emit('closeLoading')
            })
            // })
        },
        handleConfirm () {
            this.agreeHandler()
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    this.fileList = fileList
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        }
    },
    beforeCreate () {
        this.form = this.$form.createForm(this, { name: 'customUploadForm' })
    },
    created () {
        this.$set(this.form, 'opinion', '拒绝')
        this.getDictData('taskPriority', 'priorityMap')
    }
}
</script>
