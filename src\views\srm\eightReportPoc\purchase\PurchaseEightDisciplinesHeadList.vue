<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage "
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <purchase-eight-disciplines-head-edit
      v-if="showEditPage"
      ref="eightDisciplines"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <purchase-eight-disciplines-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction } from '@/api/manage'
import { ListMixin } from '@comp/template/list/ListMixin'
import purchaseEightDisciplinesHeadEdit from './modules/PurchaseEightDisciplinesHeadEdit'
import purchaseEightDisciplinesHeadDetail from './modules/PurchaseEightDisciplinesHeadDetail'
import layIM from '@/utils/im/layIM.js'
import { REPORT_ADDRESS } from '@/utils/const.js'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        purchaseEightDisciplinesHeadEdit,
        purchaseEightDisciplinesHeadDetail
    },
    data () {
        return {
            showEditPage: false,
            printRow: {},
            templateNumber: undefined,
            templateOpts: [],
            pageData: {
                businessType: 'eightDisciplines',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNWWty_874ca137`, '请输入8D单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                        fieldName: 'eightDisciplinesStatus',
                        dictCode: 'srm8DStatus'
                    }
                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    {
                        allow: ()=> {
                            return this.btnInvalidAuth('purchaseEightDisciplines:add')
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    // {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), icon: 'delete', clickFn: this.handleDelete},
                    {
                        allow: ()=> {
                            return this.btnInvalidAuth('purchaseEightDisciplines:export')
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), 
                        icon: 'download', 
                        folded: false, 
                        clickFn: this.handleExportXls
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEditSingle, allow: this.allowEdit},
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDeleteSingle, allow: this.allowDel},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印'),  clickFn: this.serachTemplate},
                    { type: 'close', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_Rl_a72da`, '关闭'), clickFn: this.handleCloseSingle, allow: this.allowClose},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 270
            }, 
            url: {
                list: '/eightReport/purchaseEightDisciplinesPoc/list',
                add: '/eightReport/purchaseEightDisciplinesPoc/add',
                delete: '/eightReport/purchaseEightDisciplinesPoc/delete',
                close: '/eightReport/purchaseEightDisciplinesPoc/close',
                deleteBatch: '/eightReport/purchaseEightDisciplinesPoc/deleteBatch',
                importExcelUrl: '/eightReport/purchaseEightDisciplinesPoc/importExcel',
                columns: 'PurchaseEightDisciplinesHeadPoc',
                exportXlsUrl: '/eightReport/purchaseEightDisciplinesPoc/exportXls'
            }
        }
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.eightDisciplinesNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'PurchaseEightDisciplinesHead', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if(row.eightDisciplinesStatus != 'D0' && row.toElsAccount) {
                return false
            }else {
                return true
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WWrH_2745ce`, '8D改进'))
        },
        serachTemplate (row) {
            const token = this.$ls.get('Access-Token')
            //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
            const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:8DHF_PrintPdf.ureport.xml&token=' + token
            window.open(url, '_blank')
        },
        openModal () {
            this.queryPrintTemList(this.$ls.get('Login_elsAccount')).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.id,
                                printId: item.printId,
                                printName: item.printName,
                                title: item.templateName,
                                printType: item.printType,
                                param: item.param
                            }
                        })
                        this.templateNumber = ''
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length===1) {
                            this.templateNumber = this.templateOpts[0].value
                            this.selectedPrintTemplate()
                        } else {
                            // 有多个模板先选择在新建
                            this.printVisible = true
                        }
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        queryPrintTemList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: this.pageData.businessType}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        selectedPrintTemplate () {
            if(this.templateNumber) {
                const that = this
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    printId: template[0].printId,
                    printName: template[0].printName,
                    printType: template[0].printType,
                    param: template[0].param
                }
                that.demandVisible = false
                that.submitLoading = false
                let rowItem = this.printRow
                this.printRow = {}
                let urlParam = ''
                if (params.param) {
                    let json = JSON.parse(params.param)
                    Object.keys(json).forEach((key, i) => {
                        urlParam += '&'+key+'='+rowItem[json[key]]
                    })
                }
                console.log(urlParam)
                if (params.printType=='ureport') {
                    const token = this.$ls.get('Access-Token')
                    //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
                    const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.printName+'&token=' + token+urlParam
                    window.open(url, '_blank')
                }
                if (params.printType=='jimu') {
                    const token = this.$ls.get('Access-Token')
                    const url = REPORT_ADDRESS + '/els/report/jmreport/view/'+params.printId+'?token=' + token+urlParam
                    //const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.title+'&token=' + token+'&id='+rowItem.id
                    window.open(url, '_blank')
                }
            }
        },
        cancelAuditCallBack (row){
            this.currentEditRow = row
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        allowDel (row){
            if(this.btnInvalidAuth('purchaseEightDisciplines:delete')){
                return true
            }
            if (row.eightDisciplinesStatus != 'D0'){
                return true
            }
            return false
        },
        allowEdit (row){
            if(this.btnInvalidAuth('purchaseEightDisciplines:edit')){
                return true
            }
            if (row.eightDisciplinesStatus == 'D9' || row.auditStatus=='1'|| row.auditStatus=='2'){
                return true
            }
            if (row.fbk3 == '9'){
                return true
            }
            return false
        },
        handleEditSingle (row) {
            this.handleEdit(row)
        },
        handleDeleteSingle (row) {
            this.handleDelete(row)
        },
        handleCloseSingle (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmClose`, '确认关闭'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_closeSelecteData`, '是否关闭选中数据?'),
                onOk: function () {
                    that.loading = true
                    getAction(that.url.close, {id: row.id}).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData() // 刷新页面
                        }else {
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.loading = false
                    })
                }
            })
        }
    }
}
</script>