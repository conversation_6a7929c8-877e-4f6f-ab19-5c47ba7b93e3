<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url"
      :reloadData="handleReloadData"
    >
      <template #itemInfoTab="{ pageData, resultData }">
        <div class="lineWrap">
          <vxe-grid :toolbar="{slots: {buttons: 'toolbar_buttons'}}"
            border
            resizable
            align="center"
            show-overflow
            highlight-hover-row
            :minHeight="334"
            :maxHeight="gridHeight"
            height="100%"
            row-id="id"
            size="small"
            ref="purchaseEnquiryItemList"
            :loading="purchaseEnquiryItemLoading"
            :data="purchaseEnquiryItemData"
            @page-change="purchaseEnquiryItemHandlePageChange"
            :checkbox-config="checkboxConfig"
            :pager-config="purchaseEnquiryItemTablePage"
            :columns="getPurchaseEnquiryItemTableColumns(pageData)"
            :row-class-name="rowClassName"
          >
            <template #renderDictLabel="{ row, column }">
              <span>
                {{ getDictLabel(row, column) }}
              </span>
            </template>
              <template v-slot:toolbar_buttons>
                  <div style="margin-top:-6px;text-align:right;">
                    <a-button style="margin-left:8px" type="primary" icon="download" @click="itemExportExcel"> 导出 </a-button>
                  </div>
              </template>
          </vxe-grid>
        </div>
      </template>
    </detail-layout>
    <field-select-modal ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
    <a-modal
      v-drag
      v-model="updateQuoteEndTimeVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_updateQuotationDeadline`, '报价时间延期')"
      @ok="updateQuoteEndTimeOk"
    >
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_quotationDeadline`, '报价截止时间') }}：
      <a-date-picker
        :show-time="true"
        :disabled-date="disabledDate"
        :valueFormat="'YYYY-MM-DD HH:mm:ss'"
        v-model="quoteEndTime"
      />
    </a-modal>
    <a-modal
      v-drag
      v-model="openBidVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_bidOpeningAdvance`, '提前开标')"
      :confirmLoading="openBidLoading"
      @ok="openBidOk"
      >{{ $srmI18n(`${$getLangAccount()}#i18n_field_openBidPassword`, '开标密码') }}：
      <a-input-password
        autocomplete="new-password"
        v-model="openBidWord"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterOpeningAdvance`, '请输入开标密码')"
      />
    </a-modal>
    <purchase-edit-cost
      ref="costform"
      :current-edit-row="costEditRow"
    />
    <view-ladder-price-modal ref="ladderPage"></view-ladder-price-modal>
    <!-- <a-modal
    v-drag    
          centered
          :width="960"
          :maskClosable="false"
          :visible="flowView"
          @ok="closeFlowView"
          @cancel="closeFlowView">
          <iframe
            style="width:100%;height:560px"
            title=""
            :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
            frameborder="0"></iframe>
        </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"
    />
    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="templateVisible"
      @ok="selectedDeliveryTemplate"
    >
      <template slot="footer">
        <a-button
          key="back"
          @click="handleTemplateCancel"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedDeliveryTemplate"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      />
    </a-modal>
    <a-modal
      v-drag
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果')"
      :visible="riskShow"
      @ok="confirm"
      @cancel="confirm"
    >
      <div
        v-for="(data, index) in riskList"
        :key="data"
      >
        <p
          :key="index"
          v-if="data.type === '0'"
        >
          {{ index + 1 }}.{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_KCbVeMKdeyR_3727867`, '在股权穿透存在相同结果') }}：{{ data.result }}
        </p>
        <p
          :key="index"
          v-if="data.type === '1'"
        >
          {{ index + 1 }}.{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_KeslILMKdeyR_85d14c24`, '在最终受益人存在相同结果') }}：{{ data.result }}
        </p>
        <p
          :key="index"
          v-if="data.type === '3'"
        >
          {{ index + 1 }}.{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_KKVLMKdeyR_46ebba36`, '在实控人存在相同结果') }}：{{ data.result }}
        </p>
        <p
          :key="index"
          v-if="data.type === '4'"
        >
          {{ index + 1 }}.{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_KsuWWMKdeyR_17c9510e`, '在报价ip存在相同结果') }}：{{ data.result }}
        </p>
        <p v-if="data.type === '5' || data.type === '6' || data.type === '7' || data.type === '8'">{{ index + 1 }}、{{ data.sourceName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }}{{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_MKW_162724a`, '存在：') }}{{ data.result }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_RH_a59e8`, '关系') }}</p>
      </div>
    </a-modal>
  </div>
</template>
<script lang="jsx">
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'
import {postAction, getAction, httpAction, postDownFile} from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { formatDate } from '@/utils/util.js'
import ViewLadderPriceModal from '../../modules/ViewLadderPriceModal'
import PurchaseEditCost from './PurchaseEditCost'
import flowViewModal from '@comp/flowView/flowView'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import moment from 'moment'
import { BatchDownloadBtn } from '@comp/template/business/class/batchDownloadBtn'
export default {
  name: 'PurchaseEnquiryBargain',
  mixins: [DetailMixin],
  components: {
    flowViewModal,
    fieldSelectModal,
    ViewLadderPriceModal,
    PurchaseEditCost
  },
  data() {
    return {
      checkboxConfig: {
          highlight: true,
          reserve: true,
          trigger: 'cell'
      },
      openBidLoading: false,
      purchaseEnquiryItemLoading: false,
      purchaseEnquiryItemData: [],
      purchaseEnquiryItemTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 500,
        align: 'left',
        pageSizes: [20, 50, 100, 200, 500],
        layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
        perfect: true
      },
      purchaseEnquiryItemTableColumns: [],
      businessType: '',
      submitLoading: false,
      templateVisible: false,
      nextOpt: true,
      currentRow: {},
      templateNumber: undefined,
      templateOpts: [],
      stageTypeData: [],
      riskList: [],
      riskShow: false,
      flowView: false,
      flowId: '',
      currentBasePath: this.$variateConfig['domainURL'],
      updateQuoteEndTimeVisible: false,
      openBidVisible: false,
      quoteEndTime: '',
      openBidWord: '',
      costEditRow: {},
      pageData: {
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseEnquiryItemList',
              columns: []
            }
          },
          {
              groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBII_2ed15c51`, '授标意见'),
              groupCode: 'awardOpinion',
              type: 'grid',
              custom: {
                ref: 'purchaseAwardOpinionList',
                columns: [
                  { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                  { field: 'awardOpinion', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBII_2ed15c51`, '授标意见'), showOverflow: false, showFooterOverflow: false, align: 'left'},
                  { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                  { field: 'createTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'), width: 150 },
                  { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'), width: 150 }
                ],
                showOptColumn: false
              }
            },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'),
            groupCode: 'fileDemandInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseAttachmentDemandList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120 },
                { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), width: 120 },
                { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220 }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupCode: 'fileInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseAttachmentList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                { field: 'sourceNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号'), width: 130 },
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120 },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 140, align: 'center', slots: { default: 'grid_opration' } }
              ],
              showOptColumn: true,
              optColumnList: [
                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadFile },
                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent, showCondition: this.deleteFileConditionBtn }
              ],
              buttons: [
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'),
                  msgType: 'batchDownload',
                  key: 'batchDownload',
                  type: 'check',
                  beforeCheckedCallBack: (rowList) => {
                    this.batchDownload2(rowList)
                  }
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJitH_4a08df61`, '围标探测记录'),
            groupCode: 'probeResultList',
            type: 'grid',
            custom: {
              ref: 'probeResultList',
              columns: [
                {
                  type: 'seq',
                  width: 60,
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                  field: 'supplierName',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                  width: 120
                },
                {
                  field: 'probeResult',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果'),
                  width: 500
                },
                {
                  field: 'createTime',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                  width: 150
                },
                {
                  field: 'createBy',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'),
                  width: 120
                }
              ],
              showOptColumn: false
            }
          }
        ],
        publicBtn: [
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow, showCondition: this.showFlowConditionBtn },
          // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_bLne_36b82195`, '生成合同'), type: 'primary', click: this.generateContract, showCondition: this.generateContractlConditionBtn },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquiryHall`, '询价大厅'), type: 'primary', click: this.openLobby, showCondition: this.showLobby },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_LBJi_2919d4bc`, '围标探测'),
            type: 'primary',
            click: this.checkRisk,
            showCondition: this.checkRiskCondition,
            icon: 'question-circle-o',
            helpText: `1、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yVLIxDjWVOSmhyR_c7532315`, '接口为异步调用，请静候查询结果')}<br> 2、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lnICdjWRWefRdXWRxOfUWWu_f2d40ee9`, '受第三方应用限制，最大供应商数量不能大于20家')}`,
            authorityCode: ''
          },
          // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquiryHall`, '询价大厅'), type: 'primary', click: this.openLobby, showCondition: this.showLobby },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_updateDeadline`, '报价时间延期'), type: 'primary', click: this.updateEndTimeBtn, showCondition: this.showUpdateEndTimeBtn, authorityCode: 'enquiry#purchaseEnquiryHead:updateQuoteEntTime' },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidOpeningAdvance`, '提前开标'), type: 'primary', click: this.openBidBtn, showCondition: this.showOpeningBtn, authorityCode: 'enquiry#purchaseEnquiryHead:openBid' },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkOpeningAdvancePassword`, '查看开标密码'), type: 'primary', click: this.viewPassword, showCondition: this.showBtn, authorityCode: 'enquiry#purchaseEnquiryHead:viewPassword' },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_releaseNewSupplier`, '发布新供应商'), type: 'primary', click: this.publishNewSupplier, showCondition: this.showNewSupplierBtn, authorityCode: 'enquiry#purchaseEnquiryHead:publishNewSupplier' },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
        ]
      },
      url: {
        detail: '/enquiry/purchaseEnquiryHead/queryById',
        updateEndTime: '/enquiry/purchaseEnquiryHead/updateQuoteEntTime',
        generateContract: '/enquiry/purchaseEnquiryHead/generateContract',
        openBid: '/enquiry/purchaseEnquiryHead/openBid',
        viewPassword: '/enquiry/purchaseEnquiryHead/viewPassword',
        generatePriceRecord: '/enquiry/purchaseEnquiryHead/generatePriceRecord',
        publishNewSupplier: '/enquiry/purchaseEnquiryHead/publishNewSupplier'
      },
      ladderSlots: {
        default: ({ row, column }) => {
          const detailTpl = (
            <div style='position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;'>
              <a-tooltip
                placement='topLeft'
                overlayClassName='tip-overlay-class'
              >
                <template slot='title'>
                  <div>
                    <vxe-table
                      auto-resize
                      border
                      row-id='id'
                      size='mini'
                      data={this.initRowLadderJson(row[column.property])}
                    >
                      <vxe-table-column
                        type='seq'
                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号')}
                        width='80'
                      ></vxe-table-column>
                      <vxe-table-column
                        field='ladder'
                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')}
                        width='140'
                      ></vxe-table-column>
                      <vxe-table-column
                        field='price'
                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价')}
                        width='140'
                      ></vxe-table-column>
                      <vxe-table-column
                        field='netPrice'
                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价')}
                        width='140'
                      ></vxe-table-column>
                    </vxe-table>
                  </div>
                </template>
                {this.defaultRowLadderJson(row[column.property])}
              </a-tooltip>
            </div>
          )
          if (row && row.quotePriceWay == 1) {
            return detailTpl
            // let label = row['ladderPriceJson'] ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看') : ''
            // return [
            //     <a onClick={() => this.setLadder(row)}>{label}</a>
            // ]
          } else {
            return ''
          }
        }
      },
      costSlots: {
        default: ({ row }) => {
          if (row && row.quotePriceWay == 2) {
            let costJson = row.costFormJson ? JSON.parse(row.costFormJson) : {}
            let label = costJson['templateName'] ? costJson['templateName'] : ''
            return [<a onClick={() => this.openCost(row)}>{label}</a>]
          } else {
            return ''
          }
        }
      },
      timestamp: null // 服务器时间戳
    }
  },
  computed: {
    fileSrc() {
      this.getStageTypeData()
      let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
      let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
      let templateVersion = this.currentEditRow.templateVersion
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_enquiry_${templateNumber}_${templateVersion}.js?t=` + time
    },
    showPriceBtn() {
      let { quoteEndTime, enquiryStatus } = this.currentEditRow
      if (enquiryStatus === '0' || enquiryStatus === '10') {
        return false
      }

      return quoteEndTime <= formatDate(this.timestamp || new Date().getTime(), 'yyyy-MM-dd hh:mm:ss')
    },
    gridHeight() {
      // 计算当前页面最大高度
      const clientHeight = document.documentElement.clientHeight
      let contextHeight = clientHeight - 246
      let oneThird = contextHeight
      let MIN = 334
      if (contextHeight < MIN) contextHeight = MIN
      if (oneThird < MIN) oneThird = MIN

      let height = contextHeight
      return height
    }
  },
  watch: {
    '$store.state.purchaseEnquiry.refleshInterFaceData': {
      immediate: true,
      handler: function (val) {
        let that = this
        // 存在时才刷新数据
        if (val) {
          that.init()
        }
      }
    },
  },
  mounted() {
    this.getPurchaseEnquiryItemList()
  },
  methods: {
    // 請求接口後格式化列表數據
    async formatTableData(data) {
        console.log('請求接口後格式化列表數據', data)
        this.setColumnData();
        return new Promise((resolve, reject) => {
            data = data.map((item) => {
                if (item.price === 0 || Number(item.price) > 0) {
                    item.price = Number(item.price).toFixed(6)
                }
                if (item.netPrice === 0 || Number(item.netPrice) > 0) {
                    item.netPrice = Number(item.netPrice).toFixed(6)
                }
                if (item.quotaTaxAmount === 0 || Number(item.quotaTaxAmount) > 0) {
                    item.quotaTaxAmount = Number(item.quotaTaxAmount).toFixed(2)
                }
                if (item.quotaNetAmount === 0 || Number(item.quotaNetAmount) > 0) {
                    item.quotaNetAmount = Number(item.quotaNetAmount).toFixed(2)
                }
                return item
            })
            resolve(data)
        })
    },

    // 设置列显示
    setColumnData() {
        let st = setTimeout(() => {
            const form = this.$refs.detailPage.form
            // 0 含税价   1 不含税价
            let itemGrid = this.$refs.purchaseEnquiryItemList
            itemGrid.resetColumn(true);
            let columnsList = itemGrid.getColumns();
            columnsList = columnsList.map((column) => {
                if (column.field == 'taxCode' || column.field == 'taxRate') {
                    column.visible = form.quoteType == 1 ? false : true
                }
                return column
            })
            itemGrid.loadColumn(columnsList);
        }, 100)
    },
    itemExportExcel() {
      postDownFile("/enquiry/purchaseEnquiryHead/exportItemExcel/" + this.currentEditRow.id, null, null).then((data) => {
          // responseType为blob的请求，统一获取错误信息
          if (data.type === 'application/json') {
              const fileReader = new FileReader()
              fileReader.onloadend = () => {
                  const jsonData = JSON.parse(fileReader.result)
                  this.$message.error(jsonData.message)
              }
              fileReader.readAsText(data)
              return
          }
          if (!data) {
              this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
              return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
              window.navigator.msSaveBlob(new Blob([data]), '导出询价行.xls')
          } else {
              let url = window.URL.createObjectURL(new Blob([data]))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', '导出询价行.xls')
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) //下载完成移除元素
              window.URL.revokeObjectURL(url) //释放掉blob对象
          }
      })
    },
    batchDownload2(rowList) {
      const fileGrid = this.$refs.detailPage.$refs.purchaseAttachmentList[0]
      let checkboxRecords = fileGrid.getCheckboxRecords()
      if (checkboxRecords.length <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '请选择要下载的附件'))
        return
      }
      let elsAccount = this.$ls.get('Login_elsAccount')
      // 判断
      let flag = false
      let fromData = this.currentEditRow
      for (let i = 0; i < checkboxRecords.length; i++) {
        let row = checkboxRecords[i]
        if (fromData.enquiryStatus === '1' && row.uploadElsAccount != elsAccount) {
          this.$message.warning('所选行中存在开标前不允许查看的文件！')
          flag = true
          return
        }
      }
      if (flag) {
        return
      }
      new BatchDownloadBtn().batchDownloadZip(checkboxRecords)
    },
    showBtn() {
      let { quoteEndTime, enquiryStatus } = this.currentEditRow
      //新建和作废状态隐藏
      if (enquiryStatus === '0' || enquiryStatus === '10') {
        return false
      }
      return quoteEndTime > formatDate(this.timestamp || new Date().getTime(), 'yyyy-MM-dd hh:mm:ss') && this.$hasOptAuth('enquiry#purchaseEnquiryHead:viewPassword')
    },
    deleteFileConditionBtn(row) {
      // let user = this.$ls.get(USER_ELS_ACCOUNT)
      // let subAccount = this.$ls.get('Login_subAccount')
      // // 议价中，且当前登录用户账号、子账号为当前附件上传人，且结果审批 审批拒绝状态，可删除
      // return user === row.uploadElsAccount && row.createBy === subAccount && this.currentEditRow.resultAuditStatus === '3'
      return this.checkCanEditFile(row)
    },
    deleteFilesEvent(row) {
      let user = this.$ls.get(USER_ELS_ACCOUNT)
      let subAccount = this.$ls.get('Login_subAccount')
      const fileGrid = this.$refs.detailPage.$refs.purchaseAttachmentList[0]
      //如果删除的数据有和登录人账号不一致的
      // if (user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount) {
      //   this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
      //   return
      // }

      if(!this.checkCanEditFile(row)) {
        this.$message.warning('不允许删除')
        return
      }

      getAction('/enquiry/purchaseEnquiryHead/custom/detail/file/delete', { id: row.id }).then((res) => {
        const type = res.success ? 'success' : 'error'
        this.$message[type](res.message)
        if (res.success) fileGrid.remove(row)
      })

      // getAction('/attachment/purchaseAttachment/delete', { id: row.id }).then((res) => {
      //   const type = res.success ? 'success' : 'error'
      //   this.$message[type](res.message)
      //   if (res.success) fileGrid.remove(row)
      // })
    },
    // 通过value显示label
    getDictLabel(row, column) {
      // 如果没有配置数据字典则走返回的field key值
      return row[column.property + '_dictText'] || row[column.property]
    },
    rowClassName ({ row, rowIndex }) {
      let classNameRow = 'row-class-default'
      if (row.itemStatus) {
        if (row.itemStatus == '4') {
          classNameRow = 'row-class-blue' // 接受
        } else if(row.itemStatus == '5') {
          classNameRow = 'row-class-red' // 拒绝
        }
      }
      return classNameRow
    },
    getPurchaseEnquiryItemTableColumns(pageData) {
      let itemInfoGroup = pageData.groups.filter((v) => v.groupCode === 'itemInfo')
      if (itemInfoGroup && itemInfoGroup.length) {
        let columnList = itemInfoGroup[0].custom.columns
        columnList.forEach(item => {
          if(item.field == 'materialNumber') {
            item.sortable = true
          }
        })
        return columnList
      } else {
        return []
      }
    },
    purchaseEnquiryItemHandlePageChange({ currentPage, pageSize }) {
      this.purchaseEnquiryItemTablePage.currentPage = currentPage
      this.purchaseEnquiryItemTablePage.pageSize = pageSize
      this.getPurchaseEnquiryItemList()
    },
    getPurchaseEnquiryItemList() {
      let url = '/enquiry/purchaseEnquiryHead/itemList'
      const params = {
        headId: this.currentEditRow.id,
        pageSize: this.purchaseEnquiryItemTablePage.pageSize,
        pageNo: this.purchaseEnquiryItemTablePage.currentPage,
        column: 'id',
        order: 'asc'
      }
      getAction(url, params).then(async (rs) => {
        if (rs.success) {
          let purchaseEnquiryItemData = rs.result?.records || [];
          purchaseEnquiryItemData = await this.formatTableData(purchaseEnquiryItemData);
          this.purchaseEnquiryItemData = purchaseEnquiryItemData;
          this.purchaseEnquiryItemTablePage.total = rs.result.total;
        }
      })
    },
    // 阶梯报价json数据组装
    initRowLadderJson(jsonData) {
      let arr = []
      if (jsonData) {
        arr = JSON.parse(jsonData)
      }
      return arr
    },
    // 阶梯报价默认显示
    defaultRowLadderJson(jsonData) {
      let arrString = ''
      if (jsonData) {
        let arr = JSON.parse(jsonData)
        if (Array.isArray(arr)) {
          arr.forEach((item, index) => {
            let ladder = item.ladder
            let price = item.price || ''
            let str = `${ladder},${price}`
            let separator = index === arr.length - 1 ? '' : ';'
            arrString += str + separator
          })
        }
      }
      return arrString
    },
    // 禁用小于当前日期的
    disabledDate(current) {
      return current && current < moment().startOf('day')
    },
    afterHandleData(pageData) {
      if (this.currentEditRow?.detectionRequire != '1') {
        pageData.groups = pageData.groups.filter((v) => v.groupCode !== 'probeResultList')
      }
    },
    confirm() {
      this.riskShow = false
    },
    // 围标探测
    checkRisk() {
      this.$refs.detailPage.confirmLoading = true
      getAction('enquiry/purchaseEnquiryHead/queryRisk', { id: this.currentEditRow.id })
        .then((res) => {
          if (res && res.success) {
            if (res.result && res.result.length) {
              this.riskList = res.result
              this.riskShow = true
            } else {
              this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IiRCISReWF_3b256afc`, '检测公司间无共同数据'))
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.detailPage.confirmLoading = false
        })
    },
    async getStageTypeData() {
      let postData = {
        busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
        dictCode: 'srmEnquiryStageType'
      }
      const res = await ajaxFindDictItems(postData)
      if (res.success) {
        this.stageTypeData = res.result
      }
    },
    handleReloadData(res) {
      this.timestamp = res.timestamp
      res.result.ebiddingAmount = res.result.ebiddingAmount ? res.result.ebiddingAmount.toFixed(2) : ''
      res.result.savingAmount = res.result.savingAmount ? res.result.savingAmount.toFixed(2) : ''
      res.result.savingRate = res.result.savingRate ? res.result.savingRate.toFixed(2) : ''
      if (this.stageTypeData && this.stageTypeData.length > 0) {
        res.result.purchaseAttachmentDemandList = res.result.purchaseAttachmentDemandList.map((i) => {
          i.stageType_dictText = this.stageTypeData.find((item) => item.value === i.stageType).text
          return i
        })
      }
      return res
    },
    downloadFile(row) {
      // let elsAccount = this.$ls.get('Login_elsAccount')
      // // 判断
      // let fromData = this.currentEditRow
      // if (fromData.enquiryStatus != '7' && row.uploadElsAccount != elsAccount) {
      //   this.$message.warning('开标前不允许查看文件！')
      //   return
      // }
      if(this.checkFileAuth(row) == false) {
          return
      }
      this.$refs.detailPage.handleDownload(row)
    },
    preViewEvent(row) {
      // let elsAccount = this.$ls.get('Login_elsAccount')
      // // 判断
      // let fromData = this.currentEditRow
      // if (fromData.enquiryStatus != '7' && row.uploadElsAccount != elsAccount) {
      //   this.$message.warning('开标前不允许查看文件！')
      //   return
      // }
      if(this.checkFileAuth(row) == false) {
          return
      }
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    checkFileAuth (row){
        if(this.currentEditRow.enquiryStatus != '1' && // 报价中
            this.currentEditRow.enquiryStatus != '7' && // 议价中
            this.currentEditRow.enquiryStatus != '9' && // 已定价
            row.uploadElsAccount != this.$ls.get('Login_elsAccount')) {
            this.$message.warning('不允许查看文件')
            return false
        }
        return true
    },
    showFlowConditionBtn() {
      console.log(this.purchaseEnquiryItemData)
      // const itemGrid = this.$refs.detailPage?.$refs?.purchaseEnquiryItemList[0] || {}
      // let itemList = itemGrid?.getTableData?.().fullData || []
      // console.log(itemList, '[itemGriditemGriditemGrid]')
      // let show = false
      // itemList.forEach(item => {
      //     let auditStatus = item.auditStatus
      //     if (auditStatus == '1' || auditStatus == '2' || auditStatus == '3') {
      //         show = true
      //     }
      // })
      let show = false
      this.purchaseEnquiryItemData.forEach((item) => {
        let auditStatus = item.auditStatus
        if (auditStatus == '1' || auditStatus == '2' || auditStatus == '3') {
          show = true
        }
      })
      return show
    },
    beforeHandleData(data) {
        console.log(data.itemColumns,'打印啦')
      data.itemColumns.forEach((item) => {
        if (item.field == 'ladderPriceJson') {
          item.slots = this.ladderSlots
        }
        if (item.field == 'costFormJson') {
          item.slots = this.costSlots
        }
        if(item.field!=''){
            item.sortable = true
        }
      })
    },
    generateContract() {
      this.nextOpt = true
      this.serachTemplate('contract')
    },
    serachTemplate(businessType) {
      let params = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      if (this.nextOpt) {
        this.pageData.businessType = businessType
        this.openModal(params.elsAccount)
      }
    },
    queryTemplateList(elsAccount) {
      let params = {
        pageSize: 100,
        elsAccount: elsAccount,
        templateStatus: '1',
        businessType: this.pageData.businessType,
        pageNo: 1
      }
      return getAction('/template/templateHead/getListByType', params)
    },
    openModal(elsAccount) {
      this.queryTemplateList(elsAccount).then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            let options = res.result.map((item) => {
              return {
                value: item.templateNumber,
                title: item.templateName,
                version: item.templateVersion,
                account: item.elsAccount
              }
            })
            this.templateOpts = options
            // 只有单个模板直接新建
            if (this.templateOpts && this.templateOpts.length === 1) {
              this.templateNumber = this.templateOpts[0].value
              this.selectedDeliveryTemplate()
            } else {
              // 有多个模板先选择在新建
              this.templateVisible = true
            }
          } else {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleAfterDealSource(result, pageData) {
      const enquiryStatus = ['1'] // “报价中”、“已报价”或“重报价”
      const group = pageData.groups.find((rs) => rs.groupCode == 'fileInfo')
      if (group && enquiryStatus.includes(result.enquiryStatus)) {
        if (result.seePrice == '0') {
          group.custom.optColumnList.forEach(
            (rs) =>
              (rs.allow = function () {
                return true
              })
          )
        } else {
          group.custom.optColumnList.forEach(
            (rs) =>
              (rs.allow = function () {
                return false
              })
          )
        }
      }
    },
    generateContractlConditionBtn() {
      let params = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      let enquiryStatus = params.enquiryStatus
      let generateContract = params.generateContract
      return enquiryStatus == '9' && generateContract == '0'
    },
    handleTemplateCancel() {
      this.templateVisible = false
    },
    selectedDeliveryTemplate() {
      if (this.templateNumber) {
        const that = this
        let param = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
        this.submitLoading = true
        let template = this.templateOpts.filter((item) => {
          return item.value == that.templateNumber
        })
        let params = {
          templateNumber: this.templateNumber,
          templateName: template[0].title,
          templateVersion: template[0].version,
          templateAccount: template[0].account,
          id: param.id
        }
        that.templateVisible = false
        that.submitLoading = false
        if (this.url.generateContract == '') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
          return
        }
        that.postUpdateData(this.url.generateContract, params)
      }
    },
    postUpdateData(url, row) {
      this.$refs.detailPage.confirmLoading = true
      httpAction(url, row, 'post')
        .then((res) => {
          if (res.success) {
            if (res.result && res.result?.length == 1) {
              // 直接跳转到范式合同管理列表页面
              const query = {
                source: 'demand-pool',
                result: res.result
              }
              this.$router.push({ path: '/srm/contract/purchase/PurchaseContractHeadList', query })
            } else {
              this.$info({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                content: res.message,
                onOk: () => {
                  // 更新vuex 当前行数据
                  this.init()
                }
              })
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.detailPage.confirmLoading = false
        })
    },
    showOpeningBtn() {
      // 129353【开标】按钮应在单据状态为“报价中”时才可操作
      if (this.currentEditRow.enquiryStatus === '1') {
        //提前开标
        let openBidBefore = (this.$refs.detailPage && this.$refs.detailPage.form.openBidBefore) || this.currentEditRow.openBidBefore
        return openBidBefore == '1' && this.$hasOptAuth('enquiry#purchaseEnquiryHead:openBid') ? true : false
      } else {
        return false
      }
    },
    priceRecordDisabled() {
      let { priceCreateWay } = this.currentEditRow
      return priceCreateWay === '1' || priceCreateWay === '3'
    },
    showUpdateEndTimeBtn() {
      let { allowDelay, enquiryStatus } = this.currentEditRow
      //允许延期 && (报价中 || 未报价)
      return allowDelay === '1' && (enquiryStatus === '1' || enquiryStatus === '3') && this.$hasOptAuth('enquiry#purchaseEnquiryHead:updateQuoteEntTime')
    },
    showNewSupplierBtn() {
      let { enquiryStatus, publishNewSupplier } = this.currentEditRow
      // 报价中、议价中状态显示
      if ((enquiryStatus === '1' || enquiryStatus === '7') && publishNewSupplier === '1' && this.$hasOptAuth('enquiry#purchaseEnquiryHead:publishNewSupplier')) {
        return true
      }
      return false
    },
    showLobby() {
      let params = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      const { enquiryStatus } = params
      return enquiryStatus !== '0'
    },
    showFlow() {
      const itemGrid = this.$refs.purchaseEnquiryItemList
      const checkRow = itemGrid.getCheckboxRecords() || []
      if (checkRow.length !== 1) {
        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectARowToView`, '选择一行进行查看'))
        return
      }
      const item = checkRow[0]
      if (!item.flowId) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程！'))
        return
      }
      this.flowId = item.flowId
      this.flowView = true
    },
    closeFlowView() {
      this.flowView = false
    },
    openLobby() {
      // this.$store.dispatch('SetTabConfirm', false)
      // this.$router.push({
      //     path: '/enquiry/purchaseHall',
      //     query: {
      //         id: this.currentEditRow.id
      //     }
      // })
      window.open(`${window.origin}/enquiry/purchaseHall?id=${this.currentEditRow.id}`, '_blank')
    },
    //修改报价截止时间
    updateEndTimeBtn() {
      let form = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      if (form['allowDelay'] == '0') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentNonExtendable`, '当前不可延期'))
        return
      }
      this.updateQuoteEndTimeVisible = true
      this.quoteEndTime = ''
    },
    updateQuoteEndTimeOk() {
      if (this.quoteEndTime == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationDeadlineCannotBeBlank`, '报价截止时间不能为空'))
        return
      }
      let params = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      params['quoteEndTime'] = this.quoteEndTime
      this.$refs.detailPage.confirmLoading = true
      let url = this.url.updateEndTime
      postAction(url, params)
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.updateQuoteEndTimeVisible = false
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.detailPage.confirmLoading = false
          this.init()
        })
    },
    openBidBtn() {
      let form = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      if (form['openBidBefore'] != '1') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidOpenAdvanceNotAllowed`, '不可提前开标'))
        return
      }
      this.openBidVisible = true
      this.openBidWord = ''
    },
    openBidOk() {
      if (this.openBidWord == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidOpeningPasswordNotBlank`, '开标密码不能为空'))
        return
      }
      this.openBidLoading = true
      let params = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      params = JSON.parse(JSON.stringify(params))
      params['openBidWord'] = this.openBidWord
      this.$refs.detailPage.confirmLoading = true
      let url = this.url.openBid
      postAction(url, params)
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.openBidVisible = false
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.openBidLoading = false
          this.$refs.detailPage.confirmLoading = false
          this.init()
        })
    },
    viewPassword() {
      this.$refs.detailPage.confirmLoading = true
      let url = this.url.viewPassword
      let headId = (this.$refs.detailPage && this.$refs.detailPage.form && this.$refs.detailPage.form.id) || ''
      getAction(url, { headId: headId })
        .then((res) => {
          if (res.success) {
            const content = res.message
            this.$confirm({
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_openBidPassword`, '开标密码'),
              content: content
            })
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.detailPage.confirmLoading = false
        })
    },
    publishNewSupplier() {
      let url = '/supplier/supplierMaster/list'
      let columns = [
        { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150 },
        { field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150 },
        { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 200 },
        { field: 'supplierStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'), width: 200 },
        { field: 'supplierClassify', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'), width: 200 },
        {
          field: 'needCoordination_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'),
          width: 200
        }
      ]
      // 获取供应商范围参数
      const form = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      // 供应商冻结功能检查
      if (form.purchaseEnquiryItemList && form.purchaseEnquiryItemList.length > 0) {
        // 设置供应商信息和询价行信息
        const array = new Array()
        const cateCodeArray = new Array()
        form.purchaseEnquiryItemList.forEach((a) => {
          array.push(a.factory + ':' + a.cateCode)
          const cateCode = a.cateCode == null || a.cateCode == '' ? 'all' : a.cateCode
          if (cateCodeArray.indexOf(cateCode) == -1) cateCodeArray.push(cateCode)
        })
        form.purchaseOrgItemList = array.join(',')
        form.accessCategoryList = cateCodeArray.join(',')
      } else {
        form.purchaseOrgItemList = ''
        form.accessCategoryList = ''
      }

      const { supplierScope = '', purchaseOrgItemList = '', accessCategoryFilter = '0', accessCategoryList = '' } = form
      this.$refs.fieldSelectModal.open(
        url,
        {
          supplierStatus: supplierScope,
          frozenFunctionValue: '2',
          purchaseOrgItemList: purchaseOrgItemList,
          accessCategoryFilter: form.enquiryScope == '2' ? '1' : accessCategoryFilter,
          purchaseOrganization: form.purchaseOrg,
          accessCategoryList: accessCategoryList,
          pageFlag: 1
        },
        columns,
        'multiple'
      )
    },
    fieldSelectOk(data) {
      let params = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      params['enquirySupplierListList'] = data
      this.$refs.detailPage.confirmLoading = true
      let url = this.url.publishNewSupplier
      postAction(url, params)
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.init()
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.detailPage.confirmLoading = false
        })
    },
    generatePriceRecord() {
      const itemGrid = this.$refs.detailPage.$refs.purchaseEnquiryItemList[0]
      const checkRow = itemGrid.getCheckboxRecords() || []
      if (!checkRow.length) {
        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectAtLeastOneMaterialOperation`, '供应商品类'))
        return
      }
      const map = new Map()
      const distinctRow = checkRow.filter((row) => !map.has(row.itemNumber) && map.set(row.itemNumber, 1))
      const materialString = distinctRow.map((row) => row.materialDesc).join('，')
      const _this = this
      const callback = () => {
        let param = (_this.$refs.detailPage && _this.$refs.detailPage.form) || {}
        param['purchaseEnquiryItemList'] = distinctRow
        this.$refs.detailPage.confirmLoading = true
        postAction(_this.url.generatePriceRecord, param)
          .then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.getItemData()
            } else {
              this.$message.warning(res.message)
            }
          })
          .finally(() => {
            this.$refs.detailPage.confirmLoading = false
          })
      }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, '是否确认将物料')) + ':' + materialString + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_generateInformationRecord`, '生成信息记录'),
        onOk() {
          callback && callback()
        }
      })
    },
    setLadder(row) {
      this.$refs.ladderPage.open(row)
    },
    openCost(row) {
      let costJson = row.costFormJson ? JSON.parse(row.costFormJson) : {}
      this.costEditRow = costJson
      let data = costJson['data'] || {}
      this.$refs.costform.open(data, 'detail')
    },
    // 检查是否允许上传/删除文件
    // 登录用户是上传人自己，且结果审批状态不是审批中/审批通过 就可以删
    checkCanEditFile(row) {
      let user = this.$ls.get(USER_ELS_ACCOUNT)
      let subAccount = this.$ls.get('Login_subAccount')
      return (user === row.uploadElsAccount && row.createBy === subAccount) &&
             (this.currentEditRow.resultAuditStatus != 1 && this.currentEditRow.resultAuditStatus != 2)
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container{
  :deep(.row-class-default) {
    font-weight: bold;
  }

  :deep(.row-class-red) {
    font-weight: bold;
    //background-color: #facaca;
  }

  :deep(.row-class-blue) {
    font-weight: bold;
    background-color: #e8fbd3;
  }
} 
</style>
