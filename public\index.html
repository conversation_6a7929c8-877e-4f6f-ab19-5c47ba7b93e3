<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <meta name="format-detection" content="telephone=no">
  <title></title>
  <link rel="icon" href="<%= BASE_URL %>logo.png">
  <link rel="stylesheet" href="<%= BASE_URL %>im/layim-v3.9.6/dist/css/layui.css" />
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }
    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      background: #49a9ee;
    }
    #loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 120px;
      height: 120px;
      margin: -75px 0 0 -75px;
      /* COLOR 1 */
      border-top-color: #FFF;
      z-index: 1001;
    }
    /* JavaScript Turned Off */
    .no-js #loader-wrapper {
      display: none;
    }
    .no-js h1 {
      color: #222222;
    }
    #loader-wrapper .load_title {
      font-family: 'Open Sans',sans-serif;
      color: #FFF;
      font-size: 14px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }
    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 14px;
      color: #FFF;
      opacity: 0.5;
    }
    /* 滚动条优化 start */
    ::-webkit-scrollbar{
      width:16px;
      height:16px;
    }
    ::-webkit-scrollbar-track{
      background: #f6f6f6;
      border-radius:2px;
    }
    ::-webkit-scrollbar-thumb{
      background: #cdcdcd;
      border-radius:2px;
    }
    ::-webkit-scrollbar-thumb:hover{
      background: #747474;
    }
    ::-webkit-scrollbar-corner {
      background: #f6f6f6;
    }
    .sk-chase {
      width: 80px;
      height: 80px;
      position: absolute;
      left: 50%;
      top: 45%;
      margin-left: -40px;
      animation: sk-chase 2.5s infinite linear both; 
    }

    .sk-chase-dot {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0; 
      animation: sk-chase-dot 2.0s infinite ease-in-out both; 
    }

    .sk-chase-dot:before {
      content: '';
      display: block;
      width: 25%;
      height: 25%;
      background-color: #fff;
      border-radius: 100%;
      animation: sk-chase-dot-before 2.0s infinite ease-in-out both; 
    }

    .sk-chase-dot:nth-child(1) { animation-delay: -1.1s; }
    .sk-chase-dot:nth-child(2) { animation-delay: -1.0s; }
    .sk-chase-dot:nth-child(3) { animation-delay: -0.9s; }
    .sk-chase-dot:nth-child(4) { animation-delay: -0.8s; }
    .sk-chase-dot:nth-child(5) { animation-delay: -0.7s; }
    .sk-chase-dot:nth-child(6) { animation-delay: -0.6s; }
    .sk-chase-dot:nth-child(1):before { animation-delay: -1.1s; }
    .sk-chase-dot:nth-child(2):before { animation-delay: -1.0s; }
    .sk-chase-dot:nth-child(3):before { animation-delay: -0.9s; }
    .sk-chase-dot:nth-child(4):before { animation-delay: -0.8s; }
    .sk-chase-dot:nth-child(5):before { animation-delay: -0.7s; }
    .sk-chase-dot:nth-child(6):before { animation-delay: -0.6s; }

    @keyframes sk-chase {
      100% { transform: rotate(360deg); } 
    }

    @keyframes sk-chase-dot {
      80%, 100% { transform: rotate(360deg); } 
    }

    @keyframes sk-chase-dot-before {
      50% {
        transform: scale(0.4); 
      } 100%, 0% {
        transform: scale(1.0); 
      } 
    }
    /* 滚动条优化 end */
    .welcome-srm-wrap {
      display: flex;
      height: 100%;
      width: 100%;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      background-color: #3a6bff;
    }
    .welcome-srm-wrap .welcome-loading {
      width: 400px;
      height: 333px;
    }
  </style>
</head>

<body>
<!-- built files will be auto injected -->
<div id="app">
  <!-- <div id="loader-wrapper">
    <div class="sk-chase">
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
      <div class="sk-chase-dot"></div>
    </div>
    <div class="load_title">正在加载 SRM,请耐心等待</div>
  </div> -->
  <div class="welcome-srm-wrap">
    <img class="welcome-loading" src="<%= BASE_URL %>app-load.gif" />
  </div>
</div>
</body>

<script src="<%= BASE_URL %>js/shimBeforWebpack.js"></script>
<!--<script type="text/javascript" defer async src="https://api.map.baidu.com/api?v=3.0&ak=Lu6T5eF0VoagvqXSKp96Mz8UvuzKuUUG"></script>-->

</html>