export const noticeDetailTemplateMixin = {
    components: {
        remoteJs: {
            render(createElement) {
                var self = this
                return createElement('script', {
                    attrs: { type: 'text/javascript', src: this.src, async: true },
                    on: {
                        load: function (event) {
                            self.$emit('load', event)
                        },
                        error: function (event) {
                            self.$message.warning('获取模板失败, 请检查:' + event)
                            self.$emit('error', event)
                        }
                    }
                })
            },
            props: {
                src: { type: String, required: true }
            }
        }
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            pageConfig: {},
            pageData: {
                groups: []
            }
        }
    },
    methods: {
        //自处理配置加载成功执行
        loadSuccess() {
            this.pageConfig = getPageConfig() // eslint-disable-line
            this.handlePageData(this.pageConfig)
        },
        //自处理配置加载异常执行
        loadError(err) {
            console.log(err)
        },
        // 自处理处理当前pageData数据和配置数据
        handlePageData(data) {
            let fields = []
            this.beforeHandleData(data)
            this.pageData.groups = data.groups.concat(this.pageData.groups)
            this.pageData.formFields = data.formFields
            this.pageData.groups = this.pageData.groups.filter((groupItem) => {
                if (groupItem.groupType !== 'gridEditConfig') {
                    return true
                }
            })
            this.pageData.groups.forEach(item => {
                // 行表列信息，目前固定为itemInfo
                if (item.groupCode === 'itemInfo') {
                    if (!item.custom.notShowTableSeq) {
                        item.custom.columns = [{ type: 'checkbox', width: 40 }, { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') }].concat(data.itemColumns)
                    } else {
                        item.custom.columns = [{ type: 'checkbox', width: 40 }].concat(data.itemColumns)
                    }
                }
                if (item.type && item.type === 'grid') {
                    // 表格列字典值
                    item.custom.columns.forEach(sub => {
                        if (sub.dictCode) {
                            sub.field += '_dictText'
                        }
                        if (!sub.slots) {
                            // 恢复列操作
                            sub.slots = {
                                header: ({ column }) => {
                                    const flag = !!(sub.helpText)
                                    const dom = flag
                                        ? (<vxe-tooltip content={sub.helpText}>
                                            <span style="display: flex; alignItems: center;">
                                                <i class="vxe-icon--question"></i>
                                                <span style="marginLeft: 6px;">{this.$srmI18n(`${this.$getLangAccount()}#${sub.fieldLabelI18nKey}`, sub.title)}</span>
                                            </span>
                                        </vxe-tooltip>)
                                        : (<span>{this.$srmI18n(`${this.$getLangAccount()}#${sub.fieldLabelI18nKey}`, sub.title)}</span>)
                                    return [
                                        dom
                                    ]
                                }
                            }
                        }
                    })
                } else {
                    // 表单信息根据分组编码分类
                    fields = data.formFields.filter(item2 => {
                        return item.groupCode === item2.groupCode
                    })
                    item.custom = {
                        formFields: fields
                    }
                }
            })
            this.afterHandleData(this.pageData)
        },
        // 外部可以重写此方法处理handlePageData前的逻辑
        beforeHandleData(data) {
            console.log('beforeHandleData', data)
        },
        // 外部可以重写此方法处理handlePageData后的逻辑
        afterHandleData(data) {
            console.log('afterHandleData', data)
        }
    }
}