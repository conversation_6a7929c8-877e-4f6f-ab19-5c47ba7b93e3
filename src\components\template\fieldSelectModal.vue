<template>
  <div>
    <a-modal
      v-drag
      centered
      :title="modalTitle"
      :width="960"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      :footer="operations ? undefined : null"
      @ok="selectedOk"
      @cancel="close"
    >
      <a-input-search
        :placeholder="placeholder"
        style="margin-bottom: 8px"
        v-model="form.keyWord"
        @search="onSearch"
        enterButton
      />
      <vxe-grid
        border
        resizable
        show-overflow="title"
        highlight-hover-row
        max-height="350"
        row-id="id"
        size="small"
        :loading="loading"
        ref="selectGrid"
        :seq-config="{ startIndex: (tablePage.currentPage - 1) * tablePage.pageSize }"
        :data="tableData"
        :pager-config="getPagerConfig()"
        :radio-config="selectModel === 'single' ? checkedConfig : undefined"
        :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
        :tree-config="isTree ? treeConfig : null"
        :columns="columns"
        @cell-dblclick="selectedOk"
        @page-change="handlePageChange"
      >
        <!-- 阶梯价格 -->
        <template #ladder_price_json_render="{ row, column }">
          <a-tooltip
            placement="top"
            v-if="row[column.property]"
            overlayClassName="tip-overlay-class"
          >
            <template slot="title">
              <vxe-table
                auto-resize
                border
                size="mini"
                :data="initRowLadderJson(row[column.property])"
              >
                <vxe-table-column
                  type="seq"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_alert_seq`, '序号')"
                  width="80"
                ></vxe-table-column>
                <vxe-table-column
                  field="ladder"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')"
                  width="140"
                ></vxe-table-column>
                <vxe-table-column
                  field="price"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_price`, '含税价')"
                  width="140"
                ></vxe-table-column>
                <vxe-table-column
                  field="netPrice"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_netPrice`, '不含税价')"
                  width="140"
                ></vxe-table-column>
              </vxe-table>
            </template>
            <div class="json-box">
              <a href="javascript: void(0)">{{ defaultRowLadderJson(row[column.property]) }}</a>
            </div>
          </a-tooltip>
        </template>
      </vxe-grid>
    </a-modal>
  </div>
</template>

<script>
import { httpRequest, getAction } from '@/api/manage'
export default {
  name: 'FieldSelectModal',
  props: {
    // 自定义类型配置 包括页码pager-config，还有返回数据结构配置
    customType: {
      type: Object,
      default() {
        return {}
      }
    },
    isEmit: {
      type: Boolean,
      default: false
    },
    requestMethod: {
      type: String,
      default: 'get'
    },
    placeholder: {
      type: String,
      default() {
        return this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
      }
    },
    isTree: {
      type: Boolean,
      default: false
    },
    treeConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    pageConfigData: {
      type: Object,
      default() {
        return {}
      }
    },
    modalTitle: {
      type: String,
      default() {
        return this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SelectData`, '选择数据')
      }
    },
    handleListData: {
      type: Function,
      default: null
    },
      afterReqHandle: {
          type: Function,
          default: null
      },
    operations: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      confirmLoading: false,
      columns: [],
      url: '',
      selectModel: 'single',
      checkedConfig: { highlight: true, reserve: true, trigger: 'row' },
      queryParams: {},
      tableData: [],
      form: {
        keyWord: ''
      },
      needPager: true,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
        align: 'left',
        pageSizes: [10, 20, 50, 100, 200, 500],
        layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
        perfect: true
      }
    }
  },
  methods: {
    // 阶梯报价json数据组装
    initRowLadderJson(jsonData) {
      let arr = []
      if (jsonData) {
        arr = JSON.parse(jsonData)
      }
      return arr
    },
    // 阶梯报价默认显示
    defaultRowLadderJson(jsonData) {
      let arrString = ''
      if (jsonData) {
        let arr = JSON.parse(jsonData)
        arr.forEach((item) => {
          let ladderQuantity = item.ladderQuantity
          let price = item.price
          let netPrice = item.netPrice
          let str = `${ladderQuantity} ${price} ${netPrice} `
          arrString += str + ','
        })
      }
      return arrString
    },
    getPagerConfig() {
      let config = {}
      if (this.customType?.pagerConfig) {
        // 自定义类型配置
        config = Object.assign({}, this.tablePage, this.customType.pagerConfig)
      } else {
        // 原来逻辑
        config = this.isTree || !this.needPager ? null : this.tablePage
      }
      return config
    },
    loadData(params, extend) {
      this.loading = true
      httpRequest(this.url, params, this.requestMethod).then((res) => {
        this.loading = false
        if (!res.success) {
          this.$message.error(res.message)
          return
        }
        if (this.handleListData) {
          // 单独处理list数据
          return this.handleListData(this, res)
        }
        let responseResult = ''
        if (this.customType?.resultKey) {
          // 自定义返回结构
          try {
            responseResult = eval(this.customType.resultKey)
          } catch (err) {
            console.log(err)
            this.$message.warning('配置resultKey有误！')
          }
        } else {
          // 兼容旧结构
          responseResult = this.isTree ? res?.result : res?.result?.records
        }
        if(['/base/dict/findDictItems', '/material/purchaseMaterialHead/listBrandInfoByParams'].includes(this.url)) {
          responseResult = res?.result || [];
        }
        if (responseResult?.length) {
          this.tableData = responseResult
          this.tablePage.total = res.result.total
          if (extend && extend.action === 'init') {
            // 初始化清空选项 包括跨页勾选的
            if (this.selectModel === 'single') {
              this.$refs.selectGrid && this.$refs.selectGrid.clearRadioReserve()
            } else {
              this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxReserve()
            }
          }
        } else {
          this.tableData = []
          this.tablePage.total = 0
          this.tablePage.currentPage = 1
        }

          if (this.afterReqHandle) {
              return this.afterReqHandle(this, this.tableData)
          }
      })
    },
    open(url, params, columns, selectModel, checkedConfig, isInit = true) {
      console.log(456)
      // 打开清空keyWord
      this.form.keyWord = ''
      this.tablePage.currentPage = 1
      let tableColumns = columns ? [...columns] : []
      this.queryParams = { pageSize: this.tablePage.pageSize, pageNo: this.tablePage.currentPage }
      checkedConfig ? (this.checkedConfig = { ...this.checkedConfig, ...checkedConfig }) : ''
      this.url = url
      if (selectModel) {
        this.selectModel = selectModel
      }
      tableColumns.unshift({ type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 })
      if (this.selectModel === 'single') {
        tableColumns.unshift({ type: 'radio', width: 40 })
      } else if (this.selectModel === 'multiple') {
        tableColumns.unshift({ type: 'checkbox', width: 40 })
      }
      tableColumns.forEach((col) => {
        // 国际化处理
        if (col.fieldLabelI18nKey) {
          col.title = this.$srmI18n(`${this.$getLangAccount()}#${col.fieldLabelI18nKey}`, col.title)
        }
      })
      this.columns = tableColumns
      if (params) {
        this.queryParams = Object.assign({}, this.queryParams, params)
      }
      this.loadData(this.queryParams, { action: isInit ? 'init' : null })
      this.visible = true
    },
    close() {
      this.visible = false
      this.$emit('close')
    },
    selectedOk() {
      let selectedData = this.$refs.selectGrid.getCheckboxRecords() // 当前页已选择数据
      if (this.selectModel === 'single') {
        selectedData = this.$refs.selectGrid.getRadioRecord() ? [this.$refs.selectGrid.getRadioRecord()] : []
      } else {
        const otherPageCheckData = this.$refs.selectGrid.getCheckboxReserveRecords() || [] // 不包含当前页已选中数据
        selectedData = [...otherPageCheckData, ...selectedData]
      }
      if (selectedData.length) {
        this.visible = false
        if (this.pageConfigData?.itemColumns) {
          // 表行
          selectedData.forEach((item) => {
            this.pageConfigData.itemColumns.forEach((el) => {
              if (el.defaultValue && (item[el.field] == '' || item[el.field] == null)) {
                // 模板有默认值且当前表单返回没有值
                item[el.field] = el.defaultValue
              }
            })
          })
        }
        if (this.isEmit) {
          this.$emit('ok', selectedData)
        } else {
          this.$parent.fieldSelectOk(selectedData)
        }
      } else {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
      }
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      let params = Object.assign({}, this.queryParams, { pageSize: pageSize, pageNo: currentPage })
      this.loadData(params)
    },
    onSearch(keyWord) {
      this.form.keyWord = keyWord
      this.queryParams = Object.assign({}, this.queryParams, { keyWord: this.form.keyWord })
      let params = Object.assign({}, this.queryParams, { pageNo: 1 })
      this.loadData(params)
    }
  }
}
</script>
