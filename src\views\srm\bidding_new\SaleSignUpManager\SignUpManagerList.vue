<template>
  <div style="height: 100%">
    <list-layout
      :tabsList="tabsList"
      ref="listPage"
      v-show="!showEditPage && !showDetailPage && !showAddPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />

    <!-- 编辑 -->
    <sign-up-manager-edit
      v-if="showEditPage"
      :queryData="queryData"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <sign-up-manager-Add
      v-if="showAddPage"
      ref="addPage"
      :queryData="queryData"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <sign-up-manager-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow" />
  </div>
</template>

<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SignUpManagerAdd from './modules/SignUpManagerAdd'
import SignUpManagerEdit from './modules/SignUpManagerEdit'
import SignUpManagerDetail from './modules/SignUpManagerDetail'
import { postAction, getAction } from '@/api/manage'

export default {
    name: 'SignUpManagerList',
    mixins: [ListMixin],
    components: {
        SignUpManagerEdit,
        SignUpManagerAdd,
        SignUpManagerDetail
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            showAddPage: false,
            pageData: {
                businessType: 'SupplierTenderProjectSignUp',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNzsRLWdIRLSdIAy_921b8cfe`, '请输入分包名称、项目名称或项目编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns }
                ],
                optColumnList: [
                    // { title: '查看公告', clickFn: this.viewAnnouncements},
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'tender#supplierTenderProjectSignUp:add', clickFn: this.handleView, allow: this.showView },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'tender#supplierTenderProjectSignUp:edit', clickFn: this.handleEdit, allow: this.showEdit },
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supBiddingHall`, '投标大厅'), authorityCode: 'tender#supplierTenderProjectSignUp:toTenderHall', clickFn: this.handleTender, allow: this.showTender}
                ]
            },
            url: {
                list: '/tender/sale/supplierTenderProjectSignUp/list',
                columns: 'saleTenderProjectSignUpList',
                getNoticeDataUrl: '/tender/sale/supplierTenderProjectSignUp/queryBySubpackageId'
            },
            queryData: null
        }
    },
    created () {
        const queryData = this.$route.query
        this.queryData = queryData
    },
    mounted () {
        // this.serachTabs('srmReconciliationStatus', 'reconciliationStatus')
        this.serachCountTabs('/tender/sale/supplierTenderProjectSignUp/counts')
    },
    watch: {
        // showEditPage (val) {
        //     if (val) {
        //         this.$store.dispatch('SetTabConfirm', true)
        //     }
        // },
        '$route.query': {
            immediate: true,
            handler (value) {
                let { subpackageId } = value
                if (subpackageId && !this.queryData) {
                    this.queryData = value
                    this.getNoticeData(subpackageId)
                }
            }
        }
    },
    methods: {
        handleTender (row) {
            row['applyRole'] = '2'
            this.$ls.set('SET_TENDERCURRENTROW', row)
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/tenderHall',
                query: {
                    _t
                }
            })
            window.open(routeUrl.href, '_blank')
        },
        showView (row) {
            // const {status=''} = row
            return false
        },
        showEdit (row) {
            const { status = '' } = row
            if (status == '0' || status == '3') {
                // 新建和拒绝
                return false
            } else {
                return true
            }
        },
        showTender (row) {
            return row.status != '2'
        },
        handleAdd () {
            this.showAddPage = true
            this.currentEditRow = {}
        },
        hideEditPage () {
            this.showAddPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            this.queryData = {}
        },
        viewAnnouncements () {},
        getNoticeData (subpackageId) {
            console.log(subpackageId)
            getAction(this.url.getNoticeDataUrl, { subpackageId }).then((res) => {
                if (res.success) {
                    if (res.result.id) {
                        if (res.result.status == '1' || res.result.status == '2') {
                            this.handleView(res.result)
                        } else {
                            this.handleEdit(res.result)
                        }
                    } else {
                        this.handleAdd(subpackageId)
                    }
                }
            })
        }
    }
}
</script>
