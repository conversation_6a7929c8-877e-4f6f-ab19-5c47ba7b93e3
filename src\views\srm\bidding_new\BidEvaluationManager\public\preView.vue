<template>
  <a-modal
    v-drag    
    v-model="visible"
    :width="width"
    :dialog-style="{ top: '20px' }"
    @cancel="close"
    :footer="null"
    class="preview-frame"
  >
    <template slot="title">
      <a-row style="line-height: 32px;">
        <a-col :span="3">
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_QIUB_2f624453`, '文件预览') }}</span>
        </a-col>
        <a-col :span="6">
          <label>{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBtL_9dc04230`, '投标单位：') }}</label>
          <a-select 
            @change="queryFiel"
            v-model="tenderer" 
            style="width: calc(100% - 80px);">
            <a-select-option 
              :value="supplier.supplierAccount" 
              v-for="supplier in supplierList" 
              :key="supplier.supplierAccount">{{ supplier.supplierName }}</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <label>{{ $srmI18n(`${$getLangAccount()}#i18n_field_QI_187aeab`, '文件：') }}</label>
          <a-select 
            @change="changeFile"
            v-model="fileType" 
            style="width: calc(100% - 50px);">
            <a-select-option 
              v-for="(file, index) in fileList" 
              :value="index"
              :key="file.id">{{ file.fileName }}</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="3">
          <label>{{ $srmI18n(`${$getLangAccount()}#i18n_field_QI_187aeab`, '文件：') }}</label>
          <a-button
            @click="downloadEvent"
            :loading="iconLoading">{{ $srmI18n(`${$getLangAccount()}#i18n_title_downloadAttachment`, '附件下载') }}</a-button>
        </a-col>
      </a-row>
    </template>
    <video
      v-if="videoSrc"
      class="video"
      ref="videoRef"
      :src="videoSrc"
      controls="controls">
      do not support video
    </video>
    <iframe
      v-else
      :src="iframeUrl" 
      id="previewDialogFrame"
      style="width:100%;height:560px;"
      frameborder="0"></iframe>
  </a-modal>
</template>
<script>
import {getAction, postAction} from '@/api/manage'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
import { Base64 } from 'js-base64'
export default {
    props: {
        currentRow: {
            type: Object,
            default () {
                return {}
            }
        },
        supplierList: {
            type: Array,
            default () {
                return []
            }
        }
    },
    data () {
        return {
            iconLoading: false,
            width: '90%',
            visible: false,
            url: '/attachment/purchaseAttachment/getSignature',
            iframeUrl: '',
            params: {},
            videoSrc: '',
            tenderer: '',
            fileType: '',
            fileList: []
        }
    },
    computed: {
        headParams () {
            let {checkType, currentStep, processType } = this.currentRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return {xNodeId}
        }
    },
    methods: {
        queryFiel (value='') {
            this.visible = true
            let {checkType, currentStep, processType, subpackageId} = this.currentRow
            const tenderer = this.supplierList.length > 0 ? this.supplierList[0]['supplierAccount'] : ''
            this.tenderer = value || tenderer
            const returnTender = () => {
                if (this.tenderer.indexOf('_') != -1) {
                    return this.tenderer.split('_')[0]
                }
                return this.tenderer
            }
            const params = {
                supplierAccount: returnTender(),
                checkType, 
                currentStep, 
                processType, 
                subpackageId
            }
            getAction('/tender/sale/supplierTenderDocumentSubmitInfo/queryAttachmentBySubpackageId', params, {headers: this.headParams}).then(res => {
                if (res.code == 200 && res.result) {
                    this.fileList = res.result
                    this.getUrl(res.result[0], '')
                    this.setFileType()
                }
            })
        },
        changeFile (value) {
            this.getUrl(this.fileList[value], '')
        },
        close () {
            console.log(this.videoSrc)
            if(this.videoSrc)  this.$refs.videoRef.pause()
        },
        // open (data) {
        //     this.getUrl(data, 'https://v5sit-micro.51qqt.com/opt/upFiles/files/307002/********/测试_1655280106433.docx')
        // },
        getUrl (params, path) {
            this.iframeUrl = ''
            this.videoSrc = ''
            if (path) {
                let ext = path.slice(path.lastIndexOf('.')+1).toLowerCase()
                if('xls'=== ext||'xlsx' === ext){
                    this.iframeUrl =process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(path))}&reDownload=true`
                } else if (ext ==='mp4' || ext === 'flv' || ext==='wmv' || ext==='ogg') {
                    this.videoSrc = path
                } else{
                    this.iframeUrl =process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(path))}&reDownload=true`
                }
                this.visible = true
                return
            }
            let newUrl
            let {subpackageId} = this.currentRow
            params.subpackageId = subpackageId || ''
            if(params.subpackageId){
                newUrl = '/tender/common/download/getSignature'
                getAction(newUrl, params).then(res => {
                    if(res.success) {
                        this.visible = true
                        var url = res.message//要预览文件的访问地址
                        var ext = params.fileName ? params.fileName.slice(params.fileName.lastIndexOf('.')+1).toLowerCase() : ''
                        if('xls'==ext||'xlsx'==ext){
                            this.iframeUrl =process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}&reDownload=true`
                        } else if (ext ==='mp4' || ext === 'flv' || ext==='wmv' || ext==='ogg') {
                            this.videoSrc = url
                        } else{
                            this.iframeUrl =process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}&reDownload=true`
                        }
                    }
                }).finally(() => {
              
                })
            }else{
                newUrl = '/attachment/purchaseAttachment/getSignature'
                postAction(newUrl, params).then(res => {
                    if(res.success) {
                        this.visible = true
                        var url = res.message//要预览文件的访问地址
                        // let str = 'abc'
                        // str = Base64.encode(str)
                        // console.log(str)
                        // str = Base64.decode(str)
                        // console.log(str)
                        var ext = params.fileName ? params.fileName.slice(params.fileName.lastIndexOf('.')+1).toLowerCase() : ''
                        if('xls'==ext||'xlsx'==ext){
                            this.iframeUrl =process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}&reDownload=true`
                        } else if (ext ==='mp4' || ext === 'flv' || ext==='wmv' || ext==='ogg') {
                            this.videoSrc = url
                        } else{
                            this.iframeUrl =process.env.VUE_APP_PROXY_APPLICATION_URL + `preview/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}&reDownload=true`
                        }
                    }
                }).finally(() => {
                })
            }
        },
        async downloadEvent () {
            let row = this.fileList[this.fileType]
            console.log(row)
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.currentRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.iconLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                this.iconLoading = false
            })
        },
        setFileType () {
            this.fileType = this.fileList.length > 0 ? 0 : ''
        }
    }
}
</script>
<style lang="scss" scoped>
.video {
    width: 100%;
}
</style>