/**
 * 
 * @param {String} sign
 * @param {Number} dataFormatLen 格式化小数
 */
export const formatFloat = (value, dataFormat, setLength) => {
    // 默认4位
    const defaultLen = 4
    const dataFormatLen = dataFormat && dataFormat.split('.')[1] && dataFormat.split('.')[1].length || setLength || defaultLen
    let rs = ''
    if (value) {
        value = String(value)
        if (value.indexOf('.') > -1) {
            rs = value.substring(0, value.indexOf('.') + (dataFormatLen + 1))
            rs = Number(rs).toFixed(dataFormatLen)
        } else {
            rs = value + '.'.padEnd((dataFormatLen + 1), '0')
        }
    }
    if(value == 0){
        rs = '0'+'.'.padEnd((dataFormatLen + 1), '0')
    }
    return rs
}
