<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="templateVisible"
      @ok="selectedDeliveryTemplate">
      <template slot="footer">
        <a-button
          key="back"
          @click="handleTemplateCancel">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedDeliveryTemplate">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"/>
    </a-modal>
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {httpAction} from '@/api/manage'
import {postAction, getAction} from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import flowViewModal from '@comp/flowView/flowView'
import { SET_CACHE_VUEX_CURRENT_EDIT_ROW, SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapMutations } from 'vuex'
import { cloneDeep } from 'lodash'
export default {
    mixins: [DetailMixin],
    components: {
        flowViewModal,
        fieldSelectModal
    },
    data () {
        return {
            businessType: '',
            submitLoading: false,
            templateVisible: false,
            nextOpt: true,
            currentRow: {},
            templateNumber: undefined,
            templateOpts: [],
            flowView: false,
            flowId: '',
            currentBasePath: this.$variateConfig['domainURL'],
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseBiddingItemList',
                        columns: [],
                        buttons: [{title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplementaryMaterial`, '补充物料'), click: this.replenishMaterialNumber, authorityCode: 'bidding#purchaseBiddingHead:replenishMaterialNumber'}]
                    } },
                    { 
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), 
                        groupCode: 'supplierInfo', 
                        type: 'grid', 
                        needDynamicsHeight: true,
                        custom: {
                        ref: 'biddingSupplierList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'), field: 'supplierCode', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 300 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'), field: 'replyStatus_dictText', width: 100},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'), field: 'replyTime', width: 140},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'), field: 'contacts', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'), field: 'phone', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mail`, '邮件'), field: 'email', width: 150},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewBidsPermission`, '查看标书权限'), field: 'bidCheck_dictText', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidAuthority`, '投标权限'), field: 'bidQuote_dictText', width: 120},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_wjAc_30ae977b`, '来源类型'), field: 'sourceType_dictText', width: 120}
                        ]
                    } },
                    { 
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_participants`, '参与人员'), 
                        groupCode: 'specialistInfo', 
                        type: 'grid', 
                        needDynamicsHeight: true,
                        custom: {
                        ref: 'purchaseBiddingSpecialistList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), width: 120},
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), width: 120 },
                            { field: 'memberType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberType`, '成员类型'), width: 220 },
                            { field: 'memberRole_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberRole`, '成员角色'), width: 220 },
                            { field: 'specialistClasses_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistType`, '专家类型'), width: 220 },
                            { field: 'mobileTelephone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话'), width: 220 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'), width: 220 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 220 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 140 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent, authorityCode: 'bidding#purchaseBiddingHead:download' },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent, authorityCode: 'bidding#purchaseBiddingHead:preview' }
                        ]
                    } }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack },
                    //{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_bLne_36b82195`, '生成合同'), type: 'primary', click: this.generateContract, showCondition: this.generateContractlConditionBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow, showCondition: this.showFlowConditionBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'), type: 'primary', click: this.auditCancel, showCondition: this.auditCancelConditionBtn},
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingHall`, '招标大厅'),
                        attrs: {
                            type: 'primary'
                        },
                        click: this.toTender,
                        showCondition: this.allowHall
                    }
                ]
            },
            url: {
                detail: '/bidding/purchaseBiddingHead/queryById',
                generateContract: '/bidding/purchaseBiddingHead/generateContract',
                cancel: '/a1bpmn/audit/api/cancel',
                replenish: '/bidding/purchaseBiddingHead/replenishMaterialNumber'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount ? this.currentEditRow.templateAccount :  this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${account}/purchase_bidding_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    mounted() {
        console.log(123, this.allowEdit)
        if(this.allowEdit) {
            let publicBtn = cloneDeep(this.pageData.publicBtn)
            publicBtn.splice(0, 0, { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), click: this.toEdit });
            this.$set(this.pageData, "publicBtn", publicBtn);
        }
    },
    methods: {
        formatPageData(item) {
            console.log('請求接口後格式化页面數據', item)
            if (item.securityCost === 0 || Math.abs(item.securityCost) > 0) {
                item.securityCost = Number(item.securityCost).toFixed(2)
            }
            return item;
        },
        formatTableData(data) {
            console.log('請求接口後格式化列表數據', data)
            data = data.map((item) => {
                if (item.price === 0 || Math.abs(item.price) > 0) {
                    item.price = Number(item.price).toFixed(6)
                }
                if (item.netPrice === 0 || Math.abs(item.netPrice) > 0) {
                    item.netPrice = Number(item.netPrice).toFixed(6)
                }
                if (item.targetPrice === 0 || Math.abs(item.targetPrice) > 0) {
                    item.targetPrice = Number(item.targetPrice).toFixed(6)
                }

                
                if (item.quotaTaxAmount === 0 || Math.abs(item.quotaTaxAmount) > 0) {
                    item.quotaTaxAmount = Number(item.quotaTaxAmount).toFixed(2)
                }
                if (item.quotaNetAmount === 0 || Math.abs(item.quotaNetAmount) > 0) {
                    item.quotaNetAmount = Number(item.quotaNetAmount).toFixed(2)
                }
                
                return item
            })
            return data
        },
        // 缓存当前行数据
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW,
            setCacheVuexCurrentEditRow: SET_CACHE_VUEX_CURRENT_EDIT_ROW
        }),
        preViewEvent (row){
            let fromData = this.currentEditRow
            if(fromData.biddingStatus === '1' || fromData.biddingStatus === '0') {
                this.$message.warning('开标前不允许查看投标文件！')
                return
            }
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        generateContract () {
            this.nextOpt = true
            this.serachTemplate('contract')
        },
        serachTemplate (businessType) {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            if (this.nextOpt){
                this.pageData.businessType = businessType
                this.openModal(params.elsAccount)
            }
        },
        queryTemplateList (elsAccount) {
            let params = {
                pageSize: 100,
                elsAccount: elsAccount,
                templateStatus: '1',
                businessType: this.pageData.businessType,
                pageNo: 1
            }
            return getAction('/template/templateHead/getListByType', params)
        },
        openModal (elsAccount) {
            this.queryTemplateList(elsAccount).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.templateNumber,
                                title: item.templateName,
                                version: item.templateVersion,
                                account: item.elsAccount
                            }
                        })
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length===1) {
                            this.templateNumber = this.templateOpts[0].value
                            this.selectedDeliveryTemplate()
                        } else {
                            // 有多个模板先选择在新建
                            this.templateVisible = true
                        }
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        generateContractlConditionBtn (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let biddingStatus = params.biddingStatus
            let generateContract = params.generateContract
            return biddingStatus == '5' && generateContract=='0'
        },
        handleTemplateCancel () {
            this.templateVisible = false
        },
        selectedDeliveryTemplate () {
            if(this.templateNumber) {
                const that = this
                let param = this.$refs.detailPage && this.$refs.detailPage.form || {}
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version,
                    templateAccount: template[0].account,
                    id: param.id
                }
                that.templateVisible = false
                that.submitLoading = false
                if (this.url.generateContract==''){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
                    return
                }
                that.postUpdateData(this.url.generateContract, params)
            }
        },
        postUpdateData (url, row){
            this.$refs.detailPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$info({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                        content: res.message,
                        onOk: () =>{
                            // 更新vuex 当前行数据
                            this.init()
                        }
                    })
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        },
        auditCancelConditionBtn (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let auditStatus = params.auditStatus
            if(auditStatus == '1'){
                return true
            }else{
                return false
            }
        },
        showFlowConditionBtn (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let auditStatus = params.auditStatus
            if(auditStatus =='1' || auditStatus =='2' || auditStatus =='3'){
                return true
            }else{
                return false
            }
        },
        showFlow (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            this.flowId = params.flowId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        downloadEvent (row) {
            this.downloadFile(row)
        },
        handleDownload ({ id, fileName }) {
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        downloadFile (row){
            let fromData = this.currentEditRow
            if(fromData.biddingStatus === '1' || fromData.biddingStatus === '0'){
                this.$message.warning('开标前不允许下载投标文件！')
                return
            }
            this.handleDownload(row)
        },
        auditCancel (){
            let form = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let param = {}
            param['businessType'] = 'publishBidding'
            param['businessId'] = form.id
            param['rootProcessInstanceId'] = form.flowId
            this.confirmLoading = true
            httpAction(this.url.cancel, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    if (res?.result?.auditStatus == '0') {
                        this.$parent.cancelAuditCallBack(form)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        replenishMaterialNumber (){
            let records = this.$refs.detailPage.$refs.purchaseBiddingItemList[0].getCheckboxRecords() || []
            if(records.length !== 1){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectARowDataOperate`, '请选择一条行数据进行操作'))
                return
            }
            let row = JSON.parse(JSON.stringify(records[0]))
            if(row.materialNumber){
                this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_material`, '物料')}：` + row.materialDesc + `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_codeAlreadyExistsNoNeedSupplement`, '已存在编码，无需补充！')}`)
                return
            }
            let columns = [
                {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150},
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150},
                {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200},
                {field: 'brand', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialBrand`, '物料品牌'), width: 200},
                {field: 'purchaseCycle', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), width: 200},
                {field: 'purchaseType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseType`, '采购类型'), width: 200},
                {field: 'checkWay_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkWay`, '检验方式'), width: 200},
                {field: 'purchaseUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingUnit`, '采购单位'), width: 200},
                {field: 'materialModel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialModel`, '物料型号'), width: 200},
                {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'), width: 200}
            ]
            this.$refs.fieldSelectModal.open('/material/purchaseMaterialHead/list', {}, columns, 'single')
        },
        fieldSelectOk (data) {
            let records = this.$refs.detailPage.$refs.purchaseBiddingItemList[0].getCheckboxRecords()
            var row = JSON.parse(JSON.stringify(records[0]))
            row.materialNumber = data[0].materialNumber
            const _this = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, '是否确认将物料')}：` + row.materialDesc + `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_encodingUpdatedTo`, '的编码更新为')}：` + data[0].materialNumber,
                onOk () {
                    _this.$refs.detailPage.showLoading()
                    row.materialDesc = data[0].materialDesc
                    row.materialGroup = data[0].materialGroup
                    row.materialGroupName = data[0].materialGroupName
                    row.materialModel = data[0].materialModel
                    row.materialSpec = data[0].materialSpec
                    row.materialName = data[0].materialName
                    row.materialId = data[0].id
                    row.materialModel = data[0].materialModel
                    postAction(_this.url.replenish, row).then((res) => {
                        if (res.success) {
                            _this.$message.success(res.message)
                            _this.init()
                        } else {
                            _this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        _this.$refs.detailPage.hideLoading()
                    })
                }
            })
        },
        allowHall () {
            let row = Object.assign({}, this.currentEditRow)
            return row.biddingStatus !== '0'
        },
        toTender () {
            this.setVuexCurrentEditRow({})
            this.setCacheVuexCurrentEditRow({ row: this.currentEditRow })
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/hall',
                query: {
                    _t
                }
            })
            window.open(routeUrl.href, '_blank')
        }
    }
}
</script>
