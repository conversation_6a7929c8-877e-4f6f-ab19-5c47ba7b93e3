<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/im/layim-v3.9.6/dist/css/layui.css" />
    <title>Document</title>
    <style>
      .select-member-box{
        padding: 20px;
      }
      .select-member-box .wrap {
        display: none;
      }
      .select-member-box .wrap .top {
        margin: 8px 0;
      }
      .select-member-box .wrap .top > div{
        display: inline-block;
      }
      .select-member-box .wrap .top > div.title{
        color: #666;
      }
      .select-member-box .wrap .top > div.number{
        float: right;
        color: #666;
      }
      .select-member-box .wrap .bot{
        margin-top: 20px;
      }
      .select-member-box .box-loading{
        text-align: center;
      }
      .select-member-box .box-loading >i {
        font-size: 50px;
      }

      input.group-name-text:placeholder-shown {
        border-color: #4E85FF;
      }

      input.group-name-text::placeholder {
        color: rgba(0, 0, 0, 0.25);
      }
      
      input.group-name-text {
        border: 1px solid #d9d9d9;
        padding: 4px 12px;
        border-radius: 4px;
      }

      .layui-btn.member-submit,
      .layui-btn.member-close {
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
      }

      .layui-btn.member-submit {
        background: #1890FF;
      }

      .text-right {
        text-align: right;
      }

      .select-member-box .layui-transfer-active .layui-btn {
        background: #4E85FF;
        border-color: #4E85FF;
      }

      .select-member-box .layui-form-checked i {
        background: #4E85FF;
        border-color: #4E85FF !important;
      }

      .select-member-box .layui-form-checkbox[lay-skin=primary]:hover i {
        border-color: #4E85FF;
      }

      .layui-btn .layui-icon {
        color: #fff;
      }
    </style>
</head>
<body>
    <div class="select-member-box">
      <!-- <div class='box-loading'>
        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
      </div> -->
      <div class='wrap'>
        <div class="group">
          <!-- <label class="layui-form-label" id="smgroup">群名字：</label> -->
          <div class="group-name">
            <input id="groupName" type="text" name="text" class="layui-input group-name-text" placeholder="请输入群名字">
          </div>
        </div>

        <div class="top">
          <div class="title" id="smtitle">选人创建</div>
          <div class="number" id="smnumber">已选人数</div>
        </div>
        <div id="select-member"></div>
        
        <div class="bot">
          <div class="item">
            <div class="text-right">
              <button type="submit" class="layui-btn member-submit" id="smsubmit">确认</button>
              <button type="reset" class="layui-btn member-close layui-btn-primary" id="smclose">取消</button>
            </div>
          </div>
        </div>
      </div>
    </div>
</body>
<script src="/im/layim-v3.9.6/dist/layui.js"></script>
<script>
    var token = localStorage.getItem('t_token');
    const mglobalSrmI18n = window.parent.globalSrmI18n ? window.parent.globalSrmI18n : function(){}
    const mglobalGetLangAccount = window.parent.globalGetLangAccount ? window.parent.globalGetLangAccount : function(){}
    document.getElementById("smtitle").innerHTML= mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_iLcI_42ab5fd0", "选人创建")
    var i18nTxt = mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_IiLWWW_62a43c73", "已选人数")
    document.getElementById("smnumber").innerHTML= i18nTxt + '<span style="margin-left: 4px;">0</span>'
    // document.getElementById("smgroup").innerHTML= mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_aRJW_3b4d414c", "群名字：")
    var groupName = document.getElementById('groupName')
    var i18n = mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_alert_VWNaRL_e53dab1e", "请输入群名称")
    groupName.setAttribute('placeholder', i18n)
    document.getElementById("smsubmit").innerHTML= mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_RL_f20f6", "确认")
    document.getElementById("smclose").innerHTML= mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_title_cancle", "取消")
    layui.use(['layim', 'transfer', 'layer', 'util'], function(layim){
      var $ = layui.$
      ,transfer = layui.transfer
      ,layer = layui.layer
      ,util = layui.util;
      var olayui = parent.layui;
      var cache = olayui.layim.cache();
      var selectMemberObj = cache.srmParams.selectMemberObj;
      console.log('selectMemberObj :>> ', selectMemberObj);
      var transferId = selectMemberObj.id+'_'+cache.mine.id;
      // layim.config({
      //   brief: true
      // })
      //初始化基本数据
      var members = 0
      var selected = 0
      var api = '/user/getRecordPerson?type=' + selectMemberObj.type + '&id=' + selectMemberObj.id;
      var BASE_TYPE = 'get'
      // 手动创建群聊
      if(cache.base.directCreate){
        api = '/user/listAllFriends'
        BASE_TYPE = 'post'
      }
      var BASE_URL = localStorage.getItem('IM_BASE_URL') || '//v5sit.51qqt.com/els/im'
      if (location.hostname.includes('localhost')) {
        BASE_URL = 'https://v5sit-micro.51qqt.com/els/im'
      }
      $.ajax({
        headers: {
          'X-Access-Token': token
        },
        url: BASE_URL + api,
        type: BASE_TYPE,
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (res) {
          // $('.select-member-box .box-loading').hide().siblings('.wrap').show()
          $('.select-member-box .wrap').show()
          console.log('ajax success', res);
          if (res.code == 200 ) {
            if ((res.result.imUserList?.length > 0 && res.result.groupChat) || cache.base.directCreate) {
              let imUserList = cache.base.directCreate ? res.result : res.result.imUserList
              //初始右侧数据
              transfer.render({
                elem: '#select-member'
                ,showSearch: true      //开启搜索框
                ,id: transferId 
                ,parseData: function(res){
                  let tsearch = document.getElementsByClassName('layui-transfer-search')
                  for(let i = 0 ; i < tsearch.length ; i++){
                    console.log(tsearch[i],tsearch[i])
                    tsearch[i].children[1].placeholder = mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_alert_RImdd_c1adfd18", "关键词搜索")
                  }
                  return {
                    "value": res.id //数据值
                    ,"title": res.username //数据标题
                    // ,"disabled": res.disabled  //是否禁用
                    // ,"checked": res.checked //是否选中
                  }
                }
                ,data: imUserList
                ,title: [mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_yj_b29ee", "好友"), mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_sUN_1445ef9", "参与者")]  //自定义标题
                ,width: 242 //定义宽度
                ,height: 338 //定义高度
                ,onchange: function(obj, index){
                  selected = obj
                  // 获取后边的数据
                  members = $("#select-member .layui-transfer div[data-index=1]").find(".layui-transfer-data li").length;
                  $('#smnumber span').text(members);
                }
                ,text:{
                    none: mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_PSWF_30240c1c", "暂无数据"),
                    searchNone: mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_alert_SzEWF_c39d4a12", "无匹配数据")},
              })
            } else {
              layer.msg(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_rtFBjGRRdX_ac9a3bf7", "该单据没有设置供应商"));
            }
          } else {
            layer.msg(res.message);
          }
        },
        error: function () {
          console.log('ajax error');
        }
      });
      // 取消弹窗
      $('.select-member-box .bot .member-close').click(function(){
          cache && olayui.layer.close(cache.srmParams.winIndex)
      })
      var oname = '聊天室'
      // 创建群聊天室
      $('.select-member-box .bot .member-submit').click(function(){
        if(cache.base.directCreate && !groupName.value){
          olayui.layer.msg(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_lOcIaLTPSMaRL_7b7a03d6", "手动创建群聊需要填写群名称"));
          return
        }
        oname = $('.group-name-text').val() ? $('.group-name-text').val() : ''
        console.log(members)
        var getData = transfer.getData(transferId); // 获取右侧数据
        var postData = getData.map(res => res.value)
        postData = [...new Set(postData)].join(',')
        if (members == 0) {
          olayui.layer.msg(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_BiLjxOIaL_544ef740", "没选成员不能建群聊"));
          return
        }
        var p = {
          id: transferId, // 列表订单id
          token:token,
          chatGroupName: oname, // 名称
          members: postData, // 群组成员
          remark: selectMemberObj.url.list || '', // 记录订单详情路径
          recordNumber: selectMemberObj.recordNumber,
          recordType: selectMemberObj.type
        }  
        // 创建群聊
        chatSubmit(p)
        // 监听查看成员
        olayui.layim.on('members', function(data){
          console.log(data);
        });
      })
      function chatSubmit(params) {
        let submitUrl = ''
        let paramData = {}
        if(cache.base.directCreate){
          submitUrl = '/user/createChatGroup'
          paramData = {
            chatGroupName: groupName.value,
            members: params.members
          }
        }else{
          submitUrl = '/user/createChatGroupById'
          paramData = params
        }
        $.ajax({
          headers: {
            'X-Access-Token': token
          },
          url: BASE_URL + submitUrl,
          contentType: "application/json",
          dataType: 'json',
          type: 'POST',
          data: JSON.stringify(paramData),
          async: false,
          success: function (res) {
            olayui.layer.close(cache.srmParams.winIndex)
            if (res.code == 200) {
              console.log('ajax success', res);
              let groupId = cache.base.directCreate ? res.result.id : transferId
              let info = {
                type: 'group'
                ,avatar: "/im/layim-v3.9.6/dist/css/modules/layim/skin/group.jpg"  
                ,groupname: cache.base.directCreate ? res.result.groupname : res.message
                ,id: groupId
              }
              olayui.layim.addList(info)
              olayui.layim.chat({
                name: cache.base.directCreate ? res.result.groupname : res.message
                ,type: 'group' //群组类型
                ,brief: '1' //群组类型
                ,avatar: '/im/layim-v3.9.6/dist/css/modules/layim/skin/group.jpg'
                ,id: groupId  //定义唯一的id方便你处理信息
                ,members  //成员数，不好获取的话，可以设置为0
                ,master: res.result?.master
              });
            }else{
              olayui.layer.msg(res.message);
            }
            
          },
          error: function () {
            console.log('ajax error');
          }
        });
      }
    });
</script>
</html>