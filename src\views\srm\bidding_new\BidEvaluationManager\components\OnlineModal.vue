<template>
  <div>
    <a-modal
      :forceRender="true"
      v-drag    
      :visible="evaModalVisible"
      width="1000px"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_UBBm_41201cd7`, '评标表格')"
      :okText="okText"
      @ok="handleOk"
      @cancel="handleCancel"
      centered
      :confirmLoading="confirmLoading">

      <a-form-model
        ref="baseForm"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-row>
          <a-col :span="8">
            <a-select
              option-filter-prop="children"
              show-search
              :filter-option="filterOption"
              :default-value="''"
              @change="changEvent"
              v-model="templateLibraryName" 
            >
              <a-select-option 
                v-for="item in supplierList"
                :key="item.id"
                :value="item.id">{{ item.templateTitle }}</a-select-option>
            </a-select>
          </a-col>
          <a-button
            style="margin:0 5px"
            type="primary"
            @click="handleCreate">{{ $srmI18n(`${$getLangAccount()}#i18n_field_bL_e90d1`, '生成') }}</a-button>
        </a-row>
      </a-form-model>
      <j-editor
        v-if="evaModalVisible"
        :key="timeKey"
        ref="ueditor"
        v-model="templateContent" 
      />
    </a-modal>
  </div>
</template>
    
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import JEditor from '@/components/els/JEditor'
    
export default {
    name: 'BiddingFile',
    components: {
        titleTrtl,
        JEditor
    },
    computed: {
    },
    props: {
        row: {
            type: Object,
            default () {
                return {}
            }
        },
        root: {
            type: Object,
            default () {
                return {}
            }
        },
        supplierList: {
            type: Array,
            default () {
                return []
            }
        }
    },
    data () {
        return {
            currentRow: {},
            templateLibraryName: '',
            noticeContent: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
            evaModalVisible: false,
            confirmLoading: false,
            labelCol: { span: 9 },
            wrapperCol: { span: 14 },
            templateContent: ''
        }
    },
    methods: {
        filterOption (input, option) {
            return (
                option.componentOptions.children[0].text.indexOf(input) >= 0
            )
        },
        async handleOk (){
            let id = ''
            this.confirmLoading = true
            this.currentRow.content = this.templateContent
            this.currentRow.templateLibraryName = this.templateLibraryName
            // 先执行一次保存，拿到行id再拿id去生成
            await postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/saveEvaAttachmentInfo', this.currentRow).then(res=>{
                if(!res.success){
                    this.$message.error(res.message)
                    this.confirmLoading = false
                }
                
            })
            await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryAttachmentInfo', {evaInfoId: this.root.currentEditRow.evaInfoId, fileType: this.row.fileType}).then(res=>{
                if(res.success){
                    id = res.result.id
                    // this.row = res.result
                    // this.row.attachmentId = res.result.id
                }else{
                    this.$message.error(res.message)
                    this.confirmLoading = false
                }
            })
            // businessType:0（评标材料）1（中标通知书）2（落标通知书）
            await getAction('/tender/common/download/downloadPDF', {id, businessType: 0}).then(res=>{
                if(res.success){
                    this.$message.success(res.message)
                    this.evaModalVisible = false
                    // this.root.evaModalVisible = false
                    this.$emit('update:evaModalVisible', false)
                    this.root.getSupplierEvaRanking()
                }else{
                    this.$message.error(res.message)
                }
            }).finally(()=>{
                this.confirmLoading = false
            })
            
        }, 
        async handleCancel (){
            this.timeKey = ''
            this.currentRow.content = this.templateContent
            this.currentRow.templateLibraryName = this.templateLibraryName
            await postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/saveEvaAttachmentInfo', this.currentRow).then(res=>{
                console.log('保存结果：', res)
            })
            await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryAttachmentInfo', {evaInfoId: this.root.currentEditRow.evaInfoId, fileType: this.currentRow.fileType}).then(res=>{
                // this.row = res.result
                // this.row.attachmentId = res.result.id
            })
            this.evaModalVisible = false
            // this.root.evaModalVisible = false
            this.$emit('update:evaModalVisible', false)
            this.root.getSupplierEvaRanking()
        },
        // 下拉框切换不同模板时触发事件
        changEvent (id) {
            let target = this.supplierList.filter(item=>{
                return item.id == id
            })
            this.timeKey = new Date().getTime()
            this.templateContent = target[0].templateContent
            this.currentRow.templateLibraryId = target[0].id
            this.templateLibraryName = target[0].templateTitle
        },
        async handleCreate (){
            this.confirmLoading = true
            // 先执行一次保存，拿到行id再拿id去生成
            // await postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/saveEvaAttachmentInfo', this.row).then(res=>{
            //     // id = res.result.id
            // })
            // await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryAttachmentInfo', {evaInfoId: this.root.currentEditRow.evaInfoId, fileType: this.row.fileType}).then(res=>{
            //     id = res.result.id
            // })
            
            // 生成按钮接口
            await postAction('/tender/template/purchaseTenderTemplateLibrary/generateTemplate', {templateLibraryId: this.currentRow.templateLibraryId, businessId: this.root.currentEditRow.subpackageId, paramMap: {winningBidSupplierName: this.row.supplierName}}).then(res=>{
                if(res.success){
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bLLR_a0818fe1`, '生成成功！'))
                    this.templateContent = res.result.content
                }else{
                    this.$message.error(res.message)
                }
            })
            this.confirmLoading = false
        }
    },
    created () {
        
    },
    mounted (){
        const promise = new Promise((resolve, reject)=>{
            this.timeKey = new Date().getTime()
            if(this.timeKey){
                resolve()
            }else{
                reject()
            }
        })
        promise.then(res=>{
            this.templateContent = this.row.content
            this.templateLibraryName = this.row.templateLibraryName
            this.currentRow = JSON.parse(JSON.stringify(this.row))
            this.evaModalVisible = true
        })
    }
}
</script>
    