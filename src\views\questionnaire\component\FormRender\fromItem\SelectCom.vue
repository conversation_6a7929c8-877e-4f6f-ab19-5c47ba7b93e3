<template>
  <a-form-item
    :label="index +'.' + field.label"
    :has-feedback="field.validateOption.icon"
    :validate-status="field.validateOption.status"
    :help="field.validateOption.message"
    :required="!!field.rules && field.rules.length > 0"
  >
    <a-select
      :placeholder="field.placeholder"
      v-decorator="[field.id, {
        initialValue: field.initialValue
      }]"
      :options="options" />
  </a-form-item>
</template>

<script>
export default {
    name: 'SelectCom',
    props: {
        field: {
            type: Object,
            require: true
        }
    },
    inject: ['index'],
    data () {
        return {
            options: []
        }
    },
    created () {
        this.options = this.field.options
    }
}
</script>

<style scoped>

</style>
