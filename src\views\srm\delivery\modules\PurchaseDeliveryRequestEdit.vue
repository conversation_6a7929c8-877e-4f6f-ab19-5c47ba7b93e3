<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      refresh
      :page-data="pageData"
      :currentEditRow="currentEditRow"
      :url="url"
      :extraEditConfig="extraEditConfig"
    />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />

    <field-select-modal ref="fieldSelectModal" />

    <ItemImportExcel
      ref="itemImportExcel"
      @importCallBack="importCallBack"
      @importCallBack2="importCallBack2"
    />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction, getAction } from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
  name: 'PurchaseDeliveryNoticeModal',
  mixins: [EditMixin],
  components: {
    fieldSelectModal,
    ItemImportExcel
  },
  props: {
    currentEditRow: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pageData: {
        selectedType: '',
        form: {},
        groups: [
          {
            groupName: '送货申请行信息',
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseDeliveryRequestItemList',
              columns: [],
              buttons: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), type: 'primary', click: this.addItem },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteItemEvent },
                {
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入'),
                  params: this.importParams,
                  click: this.importExcel
                },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                  key: 'fillDown',
                  type: 'tool-fill',
                  beforeCheckedCallBack: this.fillDownGridItem
                },
              ]
            }
          }
        ],
        formFields: [],
        publicBtn: [
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DJ_c64d4`, '提交'), type: 'primary', click: this.publishEvent },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
        ]
      },
      url: {
        add: '/delivery/purchaseDeliveryRequestHead/add',
        edit: '/delivery/purchaseDeliveryRequestHead/edit',
        detail: '/delivery/purchaseDeliveryRequestHead/queryById',
        public: '/delivery/purchaseDeliveryRequestHead/submit'
      },
      extraEditConfig: {
        checkboxConfig: {
          highlight: true,
          trigger: 'default',
          checkMethod: ({ row }) => {
            return !(row.itemStatus == 2 || row.itemStatus == 3 || row.itemStatus == 5)
          }
        }
      }
    }
  },
  computed: {
    fileSrc() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_deliveryRequest_${templateNumber}_${templateVersion}.js?t=` + time
    }
  },
  methods: {
    beforeHandleData(data){
        data.itemColumns.map(item=>{
            if(item.field == 'materialNumber'){
                item.sortable=true
            }
        })
    },
    //配置加载成功执行
    loadSuccess() {
      this.pageConfig = getPageConfig() // eslint-disable-line
      this.setFiledsByData(this.currentEditRow)
    },
    // 根据数据相关信息控制表单输入框
    // editPageVue 有值则表示此为新增保存回调
    setFiledsByData(data, editPageVue) {
      // 头状态=部分退回/已退回，头字段全部灰显不允许变更
      if (data.requestStatus == 2 || data.requestStatus == 3) {
        this.pageConfig.formFields.forEach((filed) => {
          filed.disabled = true
        })
        console.log(this.pageConfig.formFields)
      }
      if (editPageVue) {
        this.pageData.groups = this.pageData.groups.filter((group) => {
          return ['baseForm'].indexOf(group.groupCode) < 0
        })
        this.handlePageData(this.pageConfig)
      } else {
        this.handlePageData(this.pageConfig)
        this.init()
      }
    },
    importParams() {
      const form = this.$refs.editPage.getPageData()
      return { id: form.id, handlerName: 'purchaseDeliveryRequestImportRpcServiceImpl', roleCode: 'purchase' }
    },
    importExcel() {
      const form = this.$refs.editPage.getPageData()
      let params = { id: form.id, templateAccount: form.templateAccount, templateNumber: form.templateNumber, templateVersion: form.templateVersion, handlerName: 'purchaseDeliveryRequestImportRpcServiceImpl', roleCode: 'purchase', excelCode: 'purchaseDeliveryRequestImportRpcServiceImpl', excelTemplateType: 'excel' }

      if (!params.id) {
        return this.$message.warning('请先保存单据再进行该操作')
      }
      // let params = { excelCode: 'purchaseDeliveryRequestImportRpcServiceImpl' }
      this.$refs.itemImportExcel.open(params, '送货申请行信息', 'purchaseDeliveryRequestItemList', '/base/excelHeader/downloadTemplate')
    },
    importCallBack(result) {
      console.log('importCallBack', result)
      if (result.file.status === 'done') {
        let response = result.file.response
        if (response.success) {
          let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryRequestItemList[0]
          let insertData = response.result.dataList;
          this.pageConfig.itemColumns.forEach((item) => {
            if (item.defaultValue) {
              insertData.forEach((insert) => {
                if (!insert[item.field]) {
                  insert[item.field] = item.defaultValue
                }
              })
            }
          })
          itemGrid.insertAt(insertData, -1)
        } else {
          this.$message.warning(response.message)
        }
      }
    },
    importCallBack2(result) {
      console.log('importCallBack2', result)
      if (result.file.status === 'done') {
        let response = result.file.response
        if (response.success) {
          let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryRequestItemList[0]
          let insertData = response.result.dataList
          this.pageConfig.itemColumns.forEach((item) => {
            if (item.defaultValue) {
              insertData.forEach((insert) => {
                if (!insert[item.field]) {
                  insert[item.field] = item.defaultValue
                }
              })
            }
          })
          itemGrid.insertAt(insertData, -1)
        } else {
          this.$message.warning(response.message)
        }
      }
    },
    addItem() {
      this.selectType = 'material'
      let url = '/material/purchaseMaterialHead/queryPage'
      let columns = [
        {
          field: 'canDeliverQuantity',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_canDeliverQuantity`, '可送货数量'),
          width: 100
        },
        {
          field: 'cateCode',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
          width: 150
        },
        {
          field: 'cateName',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
          width: 150
        },
        { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编号'), width: 150 },
        { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
        { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200 },
        { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'), width: 200 }
      ]
      let form = this.$refs.editPage.getPageData()
      console.log(form)
      let params = {
        blocDel: '0',
        freeze: '0',
        company: form.company, // 结算组织
        purchaseOrg: form.purchaseOrg, // 采购组织
        factory: form.factory // 库存组织
      }
      let errMsg = ''
      if (!params.company) errMsg = '结算组织'
      else if (!params.purchaseOrg) errMsg = '采购组织'
      else if (!params.factory) errMsg = '库存组织'

      if (!!errMsg) return this.$message.warning(`请先选择${errMsg}`)
      this.$refs.fieldSelectModal.open(url, params, columns, 'multiple')
    },
    deleteItemEvent() {
      let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryRequestItemList[0]
      let checkboxRecords = itemGrid.getCheckboxRecords()
      if (!checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
        return
      }
      itemGrid.removeCheckboxRow()
    },
    fieldSelectOk(data) {
      console.log(data)
      if (this.selectType == 'material') {
        let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryRequestItemList[0]
        let { fullData } = itemGrid.getTableData()
        let nextItemNumber = fullData[0] ? Number(fullData[fullData.length - 1].itemNumber) + 1 : 1
        let materialList = fullData.map((item) => {
          return item.materialNumber
        })
        //过滤已有数据
        let insertData = data.filter((item) => {
          // return !materialList.includes(item.materialNumber)
          // 任务【QQTP-659】，要求不再过滤相同数据
          return true
        })
        // 设置行号、设置采购组织等
        const form = this.$refs.editPage.getPageData()
        console.log(form)
        insertData.forEach((item) => {
          item.itemNumber = nextItemNumber++
          item.itemStatus = '0'
          item.purchaseOrg = form.purchaseOrg || null // 采购组织
          item.purchaseOrgName = form.purchaseOrg_dictText ? String(form.purchaseOrg_dictText).split('_')[1] : null || null // 采购组织名称
          item.storageLocation = form.storageLocation // 库存地点
          item.storageLocationName = form.storageLocationName || null // 库存地点名称
          item.conversionRate = item.conversionRate // 换算率
          item.secondaryUnit = item.repertoryUnit // 辅单位
          item.purchaseCycle = item.purchaseCycle //采购周期
          delete item.id // 刪除id
        })
        this.pageConfig.itemColumns.forEach((item) => {
          if (item.defaultValue) {
            insertData.forEach((insert) => {
              if (!insert[item.field]) {
                insert[item.field] = item.defaultValue
              }
            })
          }
        })
        console.log(insertData)
        itemGrid.insertAt(insertData, -1)
      }
    },
    saveEvent() {
      this.$refs.editPage.postData(null, this.setFiledsByData, function (params) {
        params.purchaseDeliveryRequestItemList.forEach((item, index) => {
          item.itemNumber = index++
        })
      })
    },
    goBack() {
      this.$emit('hide')
    },
    selectCallBack(item) {
      this.pageData.form.dictCode = item[0].dictCode
      this.$refs.editPage.$forceUpdate()
    },
    publishEvent() {
      this.$refs.editPage.handleSend()
    },
    initDictData(dictCode) {
      //根据字典Code, 初始化字典数组
      let postData = {
        busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
        dictCode
      }
      return ajaxFindDictItems(postData)
    }
  }
}
</script>

<style lang="less" scoped></style>
