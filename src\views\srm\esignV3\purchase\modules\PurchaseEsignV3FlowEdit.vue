<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <a-modal
      v-drag
      v-model="previewModal"
      title="预览"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
    <a-modal
      v-drag
      v-model="visible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_PeIL_39fb9595`, '签章定位')"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :layout="layout">
        <a-form-model-item label="关键字">
          <a-input
            v-model="form.keyword" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { REPORT_ADDRESS } from '@/utils/const.js'

export default {
    name: 'PurchaseEsignV3FlowEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            onlineDisabled: false,
            rowIndex: -1,
            sealAeraIndex: -1,
            rowSignerIndex: -1,
            selectType: 'esignFlow',
            previewModal: false,
            previewContent: '',
            visible: false,
            signerBtn: false,
            templateNumber: undefined,
            templateOpts: [],
            printRow: {},
            form: {
                keyword: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                    busType: 'contract',
                    firstSeal: null,
                    busNumber: null,
                    businessScene: null,
                    relationId: null,
                    filesName: null,
                    filesId: null,
                    uploaded: null,
                    autoArchiving: null,
                    autoInitiate: null,
                    remark: null,
                    cutOffTime: null,
                    contractRemind: null,
                    noticeType: null,
                    effectiveTime: null,
                    signFileUploaded: null
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'busType',
                                    dictCode: 'electronicSignatureBusType',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessNumber`, '业务单号'),
                                    fieldName: 'busNumber',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_WhoWillStampFirst`, '哪方先盖章'),
                                    fieldName: 'firstSeal',
                                    dictCode: 'srmSignatoryType'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessAssociationID`, '业务关联id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                                    fieldName: 'filesName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'),
                                    fieldName: 'filesId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkisSuccessfullyUploaded`, '文件是否上传成功'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    disabled: true,
                                    dictCode: 'srmSendStatus'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSignatoryMaintainsTheState`, '签署人维护状态'),
                                    fieldName: 'signerVindicateStatus',
                                    dictCode: 'srmSignerVindicateStatus',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_zyH3twLIV67xJOvs`, '供方是否线上盖章'),
                                    fieldName: 'onlineSealed',
                                    dictCode: 'yn',
                                    required: '1',
                                    bindFunction: function (parentRef, pageData, groupData, value,v1,v2,v3,v4) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(v3.signFileUploaded == '1'){
                                            setDisabledByProp('onlineSealed', true)
                                            return
                                        }
                                        if(value == '0'){
                                            pageData.form.firstSeal = 'purchase'
                                            setDisabledByProp('firstSeal', false)
                                        }else {
                                            setDisabledByProp('firstSeal', true)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BqQfRcWCQGdlnH74`, '供方是否上传盖章文件'),
                                    fieldName: 'signFileUploaded',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_autoArchiving`, '是否自动归档'),
                                    fieldName: 'autoArchiving',
                                    dictCode: 'yn',
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheProcessIsAutomaticallyStarted`, '流程是否自动开启'),
                                    fieldName: 'autoInitiate',
                                    dictCode: 'yn',
                                    required: '1'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWyRKI_ed45f8ea`, '签署截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                busType: [{required: true, message: '业务类型不能为空'}],
                                busNumber: [{required: true, message: '业务单号不能为空', trigger: 'change'}],
                                businessScene: [{required: true, message: '文件主题不能为空'}],
                                firstSeal: [{required: true, message: '哪方先盖章不能为空'}],
                                noticeType: [{required: true, message: '提醒方式不能为空'}],
                                autoInitiate: [{required: true, message: '流程是否自动开启不能为空'}],
                                autoArchiving: [{required: true, message: '是否自动归档不能为空'}]
                            }
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCPWL_36613b74`, '采方签署人'), groupCode: 'purchaseSignersList', type: 'grid', custom: {
                        ref: 'purchaseSignersList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 250, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'signerType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型'), width: 120, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: '0'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'), value: '1'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPerson`, '法定代表人'), value: '2'}
                            ]} },
                            { field: 'autoArchive', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`,
                                '是否自动落章'), width: 120, editRender: {name: '$select', options: [
                                {label: '否', value: '0'},
                                {label: '是', value: '1'}]}},
                            { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                            { field: 'signWord', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'), width: 120 },
                            {
                                field: 'signFieldStyle',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeMVK_55b1010`, '签章区样式'),
                                width: 120,
                                editRender: {
                                    name: '$select', options: [
                                        {label: '单页签署', value: '1'},
                                        {label: '骑缝签署', value: '2'}
                                    ]
                                }
                            },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 180 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDEPsey_25baae92`, '签署用户E签宝账号'), width: 120 },
                            { field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDcR_f5c3f89d`, '签署用户姓名'), width: 120 }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuPWRC_72eda045`, '添加签署公司'),  type: 'primary', click: this.addPurchaseSignEvent, showCondition: this.showAddPurchaseSignEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QGPWRC_a5504d04`, '删除签署公司'), click: this.deleteSaleSignEvent, showCondition: this.showDeleteSaleSignEvent}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWMU_b399f609`, '设置签署区域'), clickFn: this.getSignArea },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRWe_416ee4e0`, '设置印章'), clickFn: this.getSeal },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWL_ed04f016`, '设置签署人'), clickFn: this.addPurchaseSignerEvent}
                        ],
                        rules: {
                            subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWWWeyWxOLV_18bfbcf9`, '[签署人SRM账号]不能为空')}],
                            psnAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWPseyWxOLV_9b256723`, '[签署人E签宝账号]不能为空')}],
                            signerType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_[PWCAc]xOLV_d16c9bf3`, '[签署方类型]不能为空')}]
                            // ,
                            // signArea: [{required: true, message: '签署区域不能为空'}]
                        }
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RCPWL_91e5ef48`, '供方签署人'), groupCode: 'saleSignersList', type: 'grid', custom: {
                        ref: 'saleSignersList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 230, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'autoArchive', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`,
                                '是否自动落章'), width: 120, editRender: {name: '$select', options: [
                                {label: '否', value: '0'},
                                {label: '是', value: '1'}]}},
                            { field: 'signerType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型'), width: 120, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: '0'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'), value: '1'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPerson`, '法定代表人'), value: '2'}
                            ]} },
                            { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                            { field: 'signWord', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'), width: 120 },
                            {
                                field: 'signFieldStyle',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeMVK_55b1010`, '签章区样式'),
                                width: 120,
                                editRender: {
                                    name: '$select', options: [
                                        {label: '单页签署', value: '1'},
                                        {label: '骑缝签署', value: '2'}
                                    ]
                                }
                            },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 180 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDEPsey_25baae92`, '签署用户E签宝账号'), width: 120 },
                            { field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDcR_f5c3f89d`, '签署用户姓名'), width: 120 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeQI_39fcc8f1`, '签章文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'saleAttachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                formFields: [],
                footerButtons: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_ESQIUB_224d09a`, '业务文件预览'), type: 'primary', click: this.preview },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_PWQIUB_ee68be07`, '签署文件预览'), type: 'primary', click: this.esignFileDown, showCondition: this.showEsignFileDown },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileUpload`, '文件上传'), type: 'primary', click: this.fileUpload, showCondition: this.showUploadBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_hdRCSuPeL_2b11aaa5`, '发送供方添加签章人'), type: 'primary', click: this.sendEvent, showCondition: this.showSendBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_hdRCre_19e38f18`, '发送供方盖章'), type: 'primary', click: this.sendEvent, showCondition: this.showSignSendBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                edit: '/esignv3/elsEsignV3Flow/edit',
                detail: '/esignv3/elsEsignV3Flow/queryById',
                uploadFile: '/esignv3/elsEsignV3Flow/fileCreate',
                keyWordToAera: '/esignv3/elsEsignV3Flow/keyWordToAera',
                viewEsignFile: '/esignv3/elsEsignV3Flow/viewEsignFile'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        getSignArea (row, column, $rowIndex){
            this.selectType = 'keyWord'
            this.sealAeraIndex = $rowIndex
            this.visible = true
        },
        handleOk () {
            if(!this.form.keyword){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIJxOLV_594d82ac`, '关键字不能为空'))
                return
            }
            const form = this.$refs.editPage.getPageData()
            form.purchaseSignersList[this.sealAeraIndex].signWord = this.form.keyword
            let param = {
                signWord: this.form.keyword,
                filesId: form.filesId
            }
            this.confirmLoading = true
            let columns = [
                { field: 'page', title: '页码', width: 200 },
                { field: 'positionX', title: '横轴(X)', width: 180 },
                { field: 'positionY', title: '纵轴(Y)', width: 180 }
            ]
            this.$refs.fieldSelectModal.tablePage.pageSize = 100
            this.$refs.fieldSelectModal.open(this.url.keyWordToAera, param, columns, 'single')

            this.visible = false
        },
        getSeal (row, column, $rowIndex){
            this.selectType = 'addSigner'
            this.rowIndex = $rowIndex
            console.log('row:', row)
            if(!row.signerType){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFPWCAcW_752c471d`, '请先选择签署方类型！'))
                return
            }
            if(!row.psnId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWGRPWL_eab9ad1c`, '请先设置签署人！'))
                return
            }
            let url = '/esignv3/purchaseEsignV3Seals/getSignSeal'
            let columns = [
                { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 200 },
                { field: 'sealId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeWW_39f03bbd`, '签章id'), width: 180 },
                { field: 'sealName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'), width: 180 },
                { field: 'sealBizType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeESAc_49a8b9e7`, '印章业务类型'), width: 100 },
                { field: 'orgAuth_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIHAElb_744f8381`, '是否已跨企业授权'), width: 100 }

            ]
            let params = {orgId: row.orgId, sealType: row.signerType, psnId: row.psnId}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        showUploadBtn (){
            const form = this.$refs.editPage.getPageData()
            const saleAttachments = form.saleAttachments? form.saleAttachments[0]: {}
            console.log('saleAttachments:',saleAttachments)
            if(saleAttachments){
                if(this.currentEditRow.onlineSealed!=='1' && saleAttachments.uploaded !='1'){
                    return true
                }
            }
            if(!this.signerBtn){
                //已经上传
                if(this.currentEditRow.uploaded==='1'){
                    return false
                }
                //供应商线上盖章，并且还未上传盖章文件到电子签章，显示
                if(this.currentEditRow.onlineSealed==='1' && this.currentEditRow.uploaded !=='1'){
                    return true
                }
                if(this.currentEditRow.onlineSealed!=='1' && this.currentEditRow.uploaded !=='1'){
                    return true
                }
                //供应商线下盖章，并且已经上传了盖章文件
                if(this.currentEditRow.onlineSealed!=='1' && this.currentEditRow.signFileUploaded==='1'){
                    return true
                }
                return false
            }else{
                return false
            }
        },
        showEsignFileDown (){
            if(!this.signerBtn){
                if(this.currentEditRow.uploaded==='1'){
                    return true
                }else{
                    return false
                }
            }else{
                return true
            }
        },
        showSendBtn (){
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            console.log('params:',params)
            if(params.uploaded !== '1'){
                return false
            }
            if(params.sendStatus==='1'){
                return false
            }
            if(params.onlineSealed==='0'){
                return false
            }
            return true
        },
        showSignSendBtn (){
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if(params.uploaded !== '1'){
                return false
            }
            if(params.sendStatus==='1'){
                return false
            }
            if(params.sendStatus==='1'){
                return false
            }
            if(params.onlineSealed==='1'){
                return false
            }
            return true
        },
        showAddPurchaseSignEvent (){
            return true
        },
        showDeleteSaleSignEvent (){
            if(!this.signerBtn){
                //线下，供方签署文件已上传
                if(this.currentEditRow.onlineSealed!=='1' && this.currentEditRow.signFileUploaded==='1' && this.currentEditRow.uploaded==='1'){
                    return true
                }
                //线上，签署文件已上传
                if(this.currentEditRow.onlineSealed==='1' && this.currentEditRow.uploaded==='1'){
                    return true
                }
                return false
            }else{
                return true
            }
        },
        addPurchaseSignEvent () {
            this.selectType = 'purchaseEsign'
            const form = this.$refs.editPage.getPageData()
            if(form.uploaded!=='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeQIsLXVShILreMU_88640b6a`, '签章文件还未上传，无法定位盖章区域'))
                return
            }
            let url = '/esignv3/purchaseEsignV3Org/list'
            let columns = [
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 180 },
                { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_yVy933rH`, '机构ID'), width: 120 },
                { field: 'legalRepName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLcR_b362e8a`, '法定代表人姓名'), width: 120 }
            ]
            let params = {realnameStatus: 1}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addPurchaseSignerEvent (row, column, $rowIndex){
            this.selectType = 'purchaseEsigner'
            this.rowSignerIndex = $rowIndex
            const form = this.$refs.editPage.getPageData()
            if(form.uploaded!=='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeQIsLXVShILreMU_88640b6a`, '签章文件还未上传，无法定位盖章区域'))
                return
            }
            let url = '/esignv3/purchaseEsignV3OrgPsn/list'
            let columns = [
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 100 },
                { field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPseDy_a4573ac2`, 'e签宝账号'), width: 180 },
                { field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'), width: 180 },
                { field: 'psnId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPseyID_49891550`, 'E签宝账号ID'), width: 180 }
            ]
            let params = {orgId: row.orgId}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        fieldSelectOk (data) {
            const form = this.$refs.editPage.getPageData()
            if(this.selectType == 'purchaseEsign'){
                let arr = data.map(({ id, ...others }) => ({ relationId: id,  autoSign: '0', ...others, subAccount: '', psnAccount: '', psnId: '', psnName: ''}))
                let itemGrid = this.$refs.editPage.$refs.purchaseSignersList[0]
                itemGrid.remove()
                let { fullData } = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.relationId
                })
                let insertData = arr.filter(item => {
                    return !regulations.includes(item.relationId)
                })
                itemGrid.insertAt(insertData)
            }else if(this.selectType === 'addSigner'){
                let ids = data.map(n => n.sealId).join(',')
                form.purchaseSignersList[this.rowIndex].sealIds = ids
                form.purchaseSignersList[this.rowIndex].orgAuth = data[0].orgAuth
            }else if(this.selectType === 'keyWord'){
                if(data && data.length>0){
                    const { page = '', positionX = '', positionY = '' } = data[0] || {}
                    let result = `${page}_${positionX}_${positionY}`
                    // const form = this.$refs.editPage.getPageData()
                    let param = form.purchaseSignersList[this.sealAeraIndex]
                    param.signArea = result
                }
            }else if(this.selectType === 'purchaseEsigner'){
                form.purchaseSignersList[this.rowSignerIndex].subAccount = data[0].subAccount
                form.purchaseSignersList[this.rowSignerIndex].psnId = data[0].psnCode
                form.purchaseSignersList[this.rowSignerIndex].psnAccount = data[0].psnAccount
                form.purchaseSignersList[this.rowSignerIndex].psnName = data[0].psnName
                form.purchaseSignersList[this.rowSignerIndex].sealIds= ''
            }
        },
        deleteSaleSignEvent (){
            let itemGrid = this.$refs.editPage.$refs.purchaseSignersList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        preview () {
            let params= this.$refs.editPage.getPageData()
            if(params.busType == 'order'){
                this.orderReview(params.relationId)
            } else if(params.busType == 'reconciliationConfirmation') {
                this.confirmationReview(params.relationId)
            } else {
                getAction('/contract/purchaseContractHead/getPreviewData', {id: params.relationId}).then((res) => {
                    if (res.success) {
                        this.previewModal = true
                        this.previewContent = res.result
                    }
                })
            }
        },
        selectedPrintTemplate () {
            if(this.templateNumber) {
                const that = this
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    printId: template[0].printId,
                    printName: template[0].printName,
                    printType: template[0].printType,
                    param: template[0].param
                }
                that.demandVisible = false
                that.submitLoading = false
                let rowItem = this.printRow
                this.printRow = {}
                let urlParam = ''
                if (params.param) {
                    let json = JSON.parse(params.param)
                    console.log('json:', json)
                    Object.keys(json).forEach((key, i) => {
                        urlParam += '&'+key+'='+rowItem[json[key]]
                    })
                }
                if (params.printType=='ureport') {
                    const token = this.$ls.get('Access-Token')
                    //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
                    const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.printName+'&token=' + token+urlParam
                    window.open(url, '_blank')
                }
                if (params.printType=='jimu') {
                    const token = this.$ls.get('Access-Token')
                    const url = REPORT_ADDRESS + '/els/report/jmreport/view/'+params.printId+'?token=' + token+urlParam
                    //const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.title+'&token=' + token+'&id='+rowItem.id
                    window.open(url, '_blank')
                }
            }
        },
        queryPrintTemList (elsAccount, businessType) {
            let params = {elsAccount: elsAccount, businessType: businessType}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        orderReview (id){
            this.queryPrintTemList(this.$ls.get('Login_elsAccount'), 'order').then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        getAction('/order/purchaseOrderHead/queryById', {id: id}).then(resOrder => {
                            this.printRow = resOrder.result
                            let options = res.result.map(item => {
                                return {
                                    value: item.id,
                                    printId: item.printId,
                                    printName: item.printName,
                                    title: item.templateName,
                                    printType: item.printType,
                                    param: item.param
                                }
                            })
                            this.templateNumber = ''
                            this.templateOpts = options
                            // 只有单个模板直接新建
                            if (this.templateOpts && this.templateOpts.length===1) {
                                this.templateNumber = this.templateOpts[0].value
                                this.selectedPrintTemplate()
                            } else {
                                // 有多个模板先选择在新建
                                this.printVisible = true
                            }
                        })
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        confirmationReview (id){
            this.queryPrintTemList(this.$ls.get('Login_elsAccount'), 'purchaseReconciliationConfirmation').then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        getAction('/reconciliation/purchaseReconciliationConfirmation/queryById', {id: id}).then(resOrder => {
                            this.printRow = resOrder.result
                            let options = res.result.map(item => {
                                return {
                                    value: item.id,
                                    printId: item.printId,
                                    printName: item.printName,
                                    title: item.templateName,
                                    printType: item.printType,
                                    param: item.param
                                }
                            })
                            this.templateNumber = ''
                            this.templateOpts = options
                            // 只有单个模板直接新建
                            if (this.templateOpts && this.templateOpts.length===1) {
                                this.templateNumber = this.templateOpts[0].value
                                this.selectedPrintTemplate()
                            } else {
                                // 有多个模板先选择在新建
                                this.printVisible = true
                            }
                        })
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        prevEvent () {
            this.$refs.editPage.prevStep()
        },
        nextEvent () {
            this.$refs.editPage.nextStep()
        },

        saveEvent () {
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    const signers = params.purchaseSignersList
                    if (signers && signers.length > 0) {
                        for(let signer of signers){
                            //是自动落章
                            if(signer.autoArchive == '1'){
                                if(signer.signerType  !== '1'){
                                    this.$refs.editPage.confirmLoading = false
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOWeRRuPWCAcLtR_ccdecfa0`, '自动落章只支持【签署方类型】为【机构】'))
                                    return
                                }
                                if(!signer.sealIds){
                                    this.$refs.editPage.confirmLoading = false
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOWelTGRWe_75cb2b37`, '自动落章必须设置印章'))
                                    return
                                }
                                if(!signer.signArea){
                                    this.$refs.editPage.confirmLoading = false
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOWelTGRPWLR_dfb08273`, '自动落章必须设置【签署位置】'))
                                    return
                                }
                                if(signer.orgAuth !=='1'){
                                    this.$refs.editPage.confirmLoading = false
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOWejWelTKIHAElbje_f35c8219`, '自动落章的印章必须是【已跨企业授权】的章'))
                                    return
                                }
                            }
                            if(signer.signArea){
                                if(!signer.signFieldStyle){
                                    this.$refs.editPage.confirmLoading = false
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrPWLRlTGRPeMVK_2df4eb41`, '设置了【签署位置】，必须设置【签章区样式】'))
                                    return
                                }
                                if(signer.signFieldStyle !== '1'){
                                    this.$refs.editPage.confirmLoading = false
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrPWLRPeMVKlTLtEPe_d4b642fd`, '设置了【签署位置】，签章区样式必须为【单页签章】'))
                                    return
                                }
                            }
                            if (signer.sealIds || signer.signArea) {
                                if (!signer.sealIds) {
                                    this.$refs.editPage.confirmLoading = false
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrPWMUlTGRWe_a65494a5`, '设置了签署区域必须设置印章'))
                                    return
                                }
                                if (!signer.signArea) {
                                    this.$refs.editPage.confirmLoading = false
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrWelTGRPWMU_913de1e5`, '设置了印章必须设置签署区域'))
                                    return
                                }
                                if (!signer.signFieldStyle) {
                                    this.$refs.editPage.confirmLoading = false
                                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrWeSPWMUlTiFPeMVK_42827fb1`, '设置了印章或签署区域必须选择签章区样式'))
                                    return
                                }
                            }
                        }
                    }
                    let url = this.url.edit
                    //采购方操作
                    params.modifyPerson='0'
                    postAction(url, params).then(res => {
                        this.$refs.editPage.confirmLoading = false
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.init()
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        sendEvent (){
            const params = this.$refs.editPage.getPageData()
            //线上盖章
            if(params.onlineSealed==='1'){
                if(params.uploaded!=='1'){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeQIsLXVShILreMU_88640b6a`, '签章文件还未上传，无法定位盖章区域'))
                    return
                }
            }
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    const signer = params.purchaseSignersList
                    if (signer && signer.length > 0) {
                        for(let row of signer){
                            if(row.autoArchive == '1' && (!row.sealIds||!row.signArea) ){
                                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOWelTGRWenPWMU_10c9304e`, '自动落章必须设置印章和签署区域'))
                                return
                            }
                        }
                    }
                    //发送
                    params.sendStatus = '1'
                    //采方
                    params.modifyPerson='0'
                    //是否发送信息发布
                    params.operateType = 'publish'
                    postAction('/esignv3/elsEsignV3Flow/send', params).then(res => {
                        if(res.success){
                            this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hdLR_284294df`, '发送成功'))
                            this.goBack()
                        }else{
                            this.$message.warning(res.message)
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        fileUpload (){
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    let url = this.url.uploadFile
                    params.reportUrl = REPORT_ADDRESS
                    params.token = this.$ls.get('Access-Token')
                    postAction(url, params).then(res => {
                        this.$refs.editPage.confirmLoading = false
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.signerBtn = true
                            this.init()
                        }
                    })
                }
            }).catch(err => {
                this.$refs.editPage.confirmLoading = false
                console.log(err)
            })

        },
        esignFileDown (){
            const params = this.$refs.editPage.getPageData()
            getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                if(res.success){
                    window.open(res.result.fileDownloadUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>