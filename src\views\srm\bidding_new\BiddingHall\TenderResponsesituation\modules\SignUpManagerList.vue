<template>
  <div>
    <listTable 
      ref="listTable"
      :fromSourceData="fromSourceData"
      :statictableColumns="tableColumns"
      :showTablePage="false"
      :pageData="pageData"
    />
  </div>
</template>
<script>
import listTable from '../../components/listTable'
export default {
    props: {
        fromSourceData: {
            default: () => {
                return []
            },
            type: Array
        }
    },
    components: {
        listTable
    },
    inject: ['tenderCurrentRow'],
    data () {
        return {
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit },
                    {type: 'preview', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_UJ_b8064`, '审查'), clickFn: this.handleView, allow: this.allowAuditView}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sRzE_2e212813`, '报名状态'),
                    'field': 'status_dictText',
                    'width': 120
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sRKI_2e1fd2c6`, '报名时间'),
                    'field': 'createTime',
                    'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataSource`, '数据来源'),
                    'field': 'sourceType_dictText',
                    'width': 120
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                    'field': 'contacts'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
                    'field': 'contactsPhone'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ]
        }
    },
    computed: {
        applyRoleCanEdit () {
            return this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'
        }
    },
    methods: {
        handleView (row) {
            this.$emit('handleSighUpViewPage', row)
        },
        allowView (row) {
            // 0给编辑 1不给编辑
            return row.status !== '0'
            // true不可编辑
            // return true
        },
        allowEdit (row){
            if (this.applyRoleCanEdit) {
                return !(row.status == '0' && row.sourceType == '1')
            } else {
                return true
            }
        },
        allowAuditView (row){
            return !(row.status == '1')
        },
        handleEdit (row){
            this.$emit('handleSighUpEditPage', row)
        }
    },
    mounted (){
        console.log('fromSourceData', this.fromSourceData)
    }
}
</script>

