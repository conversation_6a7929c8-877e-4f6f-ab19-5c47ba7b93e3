<template>
  <div class="opening-authority">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      @content-header-save="handleSave"
    />

    <div
      class="container"
      :style="style">
    
      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_title_participatingSuppliers`, '参与供应商') }}</div>
          <div class="table">
            <vxe-grid
              ref="biddingSupplierList"
              v-bind="defaultGridOption"
              :columns="supplierColumns"
              :data="biddingSupplierList"
              @cell-click="handleCellClick">
              <template slot="empty">
                <a-empty />
              </template>
              <template #switch_render="{row, column}">
                <vxe-switch
                  close-value="0"
                  open-value="1"
                  v-model="row[column.property]"></vxe-switch>
              </template>
            </vxe-grid>
          </div>
        </div>

        <div class="itemBox">
          <div class="title dark"> {{ curRow.supplierName ? `${curRow.supplierName} ${i18nAttachment}` : i18nAttachment }}</div>
          <div class="table">
            <vxe-grid
              ref="attachment"
              v-bind="defaultGridOption"
              :columns="attachmentColumns"
              :data="attachmentList">
              <template slot="empty">
                <a-empty />
              </template>
              <template #grid_opration="{ row, column }">
                <a
                  v-for="(item, i) in optColumnList"
                  :key="i"
                  :title="item.title"
                  style="margin:0 4px"
                  @click="item.clickFn(row, column)">{{ item.title }}</a>
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>

    </div>
  </div>
</template>

<script>
import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import { getAction, postAction } from '@/api/manage'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        'content-header': ContentHeader
    },
    data () {
        return {
            i18nAttachment: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Attachment`, '附件文件'),
            confirmLoading: false,
            form: {},
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确定'), type: 'primary', event: 'save', authorityCode: 'bidding#purchaseBiddingHead:openPermission'}
            ],
            showHeader: true,
            height: 0,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                columns: [],
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'dblclick', mode: 'cell' }
            },
            supplierColumns: [
                { type: 'radio', width: 40 },
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'), field: 'supplierCode', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 300 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'), field: 'replyStatus_dictText', width: 100},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'), field: 'replyTime', width: 140},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'), field: 'contacts', width: 120},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'), field: 'phone', width: 120},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mail`, '邮件'), field: 'email', width: 150},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sourceType`, '来源类型'), field: 'sourceType_dictText', width: 120},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewBidsPermission`, '查看标书权限'), field: 'bidCheck', width: 120, fixed: 'right', slots: { default: 'switch_render' } },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidAuthority`, '投标权限'), field: 'bidQuote', width: 120, fixed: 'right', slots: { default: 'switch_render' }}
            ],
            optColumnList: [
                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.handleDownload },
                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
            ],
            biddingSupplierList: [],
            curRow: {},
            attachmentColumns: [
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 320 },
                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 140 },
                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
            ],
            attachmentList: []
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleCellClick ({ row, column }) {
            const unSelected = [ 'bidCheck', 'bidQuote' ]
            if (unSelected.includes(column.property)) {
                return
            }
            this.supSetRadioRow(row)
        },
        preViewEvent (row){
            debugger
            let fromData = this.form
            if(row.fileType == '2' && (fromData.biddingStatus === '1' || fromData.biddingStatus === '0')){
                let txt = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vBPxiTmAeBQI_f5bb35a5`, '开标前不允许查看投标文件')
                this.$message.warning(txt)
                return
            }
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        // 文件下载
        handleDownload ({ id, fileName, fileType }) {
            debugger
            let biddingStatus = this.form.biddingStatus
            if (fileType === '2' && (biddingStatus === '0' || biddingStatus === '1')) {
                let txt = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vBPxiTIKeBQI_310dc6b1`, '开标前不允许下载投标文件')
                this.$message.warn(txt)
                return
            }

            const params = {
                id
            }
            let downloadUrl = '/attachment/purchaseAttachment/download'
            getAction(downloadUrl, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        getData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingHead/queryById'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { biddingSupplierList = [], ...others } = res.result || {}
                    this.form = { ...others }
                    this.biddingSupplierList = biddingSupplierList
                    if (!this.biddingSupplierList.length) {
                        return
                    }
                    this.$nextTick(() => {
                        this.supSetRadioRow(this.biddingSupplierList[0])
                    })
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleSave () {
            const callback = () => {
                const params = {
                    ...this.form,
                    biddingSupplierList: this.biddingSupplierList
                }
                const url = '/bidding/purchaseBiddingHead/openPermission'
                this.confirmLoading = true
                postAction(url, params)
                    .then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.updateVuexCurrentEditRow()
                        }
                    })
                    .finally(() => {
                        this.confirmLoading = false
                    })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_permission`, '权限'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSupplierPermission`, '是否确认供应商权限规则?'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        supSetRadioRow (row) {
            if (row.id === this.curRow.id) {
                return
            }
            this.curRow = row
            const biddingSupplierListRef = this.$refs.biddingSupplierList
            if (!biddingSupplierListRef) {
                return
            }
            biddingSupplierListRef.setRadioRow(row).then(() => {
                this.getAttachmentListData(row)
            })
        },
        getAttachmentListData ({ toElsAccount }) {
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = {
                id: id,
                supplierAccount: toElsAccount
            }


            const url = '/bidding/purchaseBiddingHead/queryAttachmentBySupplierAccount'
            this.confirmLoading = true
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                   
                    let _attachmentList  = []
                    res.result.forEach(item => {
                        _attachmentList = _attachmentList.concat(item.purchaseAttachmentDTOList)
                    })
                    console.log('数据为:', _attachmentList)
                    this.attachmentList = _attachmentList
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    created () {
        this.height = document.documentElement.clientHeight
        this.getData()
    }
}
</script>

<style lang="less" scoped>
.opening-authority {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
}
</style>
