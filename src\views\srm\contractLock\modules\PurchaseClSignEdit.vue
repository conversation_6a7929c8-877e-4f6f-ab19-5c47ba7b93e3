<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'EsignFlowAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        const that = this
        return {
            confirmLoading: false,
            createFlag: true,
            rowIndex: -1,
            rowSignerIndex: -1,
            selectType: 'esignFlow',
            labelCol: { span: 4 },
            wrapperCol: { span: 14 },
            visible: false,
            peopleNumber: 1,
            form: {
                number: 1
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                    busType: 'single',
                    toElsAccount: '',
                    supplierName: '',
                    businessScene: null,
                    filesName: null,
                    filesId: null,
                    uploaded: null,
                    autoArchiving: '1',
                    autoInitiate: null,
                    remark: null,
                    cutOffTime: null,
                    contractRemind: null,
                    noticeType: null,
                    effectiveTime: null,
                    firstSeal: 'purchase',
                    uploadFlag: true,
                    companyId: null
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [

                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAy_24c6a9a8`, '业务编号'),
                                    fieldName: 'busNumber',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'subject'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QIKQXVAZd_18c96370`, '文件是否上传契约锁'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '1')
                                            setDisabledByProp('toElsAccount', flag)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjXKI_ef65ec11`, '签署有效时间'),
                                    fieldName: 'expireTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWsRKI_fa482f8c`, '签署终止时间'),
                                    fieldName: 'endTime'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRCTPMVQI_6b397fa7`, '是否供方需要回传文件'),
                                    fieldName: 'needCheck',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.$set(Vue.form, 'toElsAccount', data[0].toElsAccount)
                                        Vue.$set(Vue.form, 'supplierName', data[0].supplierName)
                                    },
                                    extend: {
                                        modalColumns: [
                                            {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), fieldLabelI18nKey: 'i18n_field_toCompanyCode', with: 150},
                                            {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), fieldLabelI18nKey: 'i18n_massProdHeade95_supplierName', with: 150}
                                        ],
                                        modalUrl: '/supplier/supplierMaster/list',
                                        modalParams: {}
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_gwarjw3QgSxzQUiQ`, '签署完成是否自动发送'),
                                    fieldName: 'autoSend',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isOpen`, '是否已开启'),
                                    fieldName: 'initiate',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isFile`, '是否已归档'),
                                    fieldName: 'archiving',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                                    fieldName: 'fileType',
                                    dictCode: 'contractLockFileType'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                subject: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_QIdDxOLV_a427525c`, '文件主题不能为空')}],
                                autoArchiving: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOLAxOLV_15ceb36`, '是否自动归档不能为空')}]
                            }
                        }

                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'), groupCode: 'purchaseSigners', type: 'grid', custom: {
                        ref: 'purchaseSigners',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'tenantTypeArr', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`,
                                this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型')),
                            width: 120, editRender: {
                                name: '$select',
                                props: { multiple: true, clearable: true },
                                options: [
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RC_a300c`, '公司'), value: 'COMPANY'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: 'PERSONAL'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPersonName`, '法人'), value: 'LP'}
                                ],
                                events: {
                                    change: (currentRow, currentValue) => {
                                        const data = currentRow.data[currentRow.$rowIndex]
                                        if(data.personal && currentValue.value !== ['PERSONAL']){
                                            that.$message.warning('签署个人不能选择其他类型')
                                            currentValue.value = ['PERSONAL']
                                            const params = this.$refs.editPage.getPageData()
                                            params.purchaseSigners.forEach(row=>{
                                                if(row.personal && row.signatoryContact == data.signatoryContact && row.signatoryContactType == data.signatoryContactType){
                                                    row.tenantTypeArr = currentValue.value
                                                }
                                            })
                                        }
                                    }
                                }
                            }
                            },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 330, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'signatoryName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWLcR_75c3ca0`, '签署人姓名'), width: 120 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuPWRC_72eda045`, '添加签署公司'),  type: 'primary', click: this.addPurchaseSignEvent, showCondition: this.showAddPurchaseSignEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SuPWmL_72ed3609`, '添加签署个人'),  type: 'primary', click: this.addPersonalEvent, showCondition: this.showAddPurchaseSignEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteSaleSignEvent, showCondition: this.showDeleteSaleSignEvent}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRWe_416ee4e0`, '设置印章'), clickFn: this.getSeal, allow: this.allowSeal },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWL_ed04f016`, '设置签署人'), clickFn: this.addPurchaseSignerEvent, allow: this.allowSign}
                        ],
                        rules: {
                            signatoryName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WPWLcRWxOLV_bdc3bfe8`, '[签署人姓名]不能为空')}],
                            tenantTypeArr: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WPWCAcWxOLV_d16c9bf3`, '[签署方类型]不能为空')}]
                        }
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'uploaded', title: '是否上传契约锁', width: 180, visible: false },
                            { field: 'uploaded_dictText', title: '是否上传契约锁', width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 320, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'contractLock', attr: this.attrHandle, callBack: this.uploadCallBack, showCondition: this.showFileDelBtn}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent, showCondition: this.showFileDelBtn }
                        ]
                    } }
                ],
                formFields: [],
                footerButtons: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAnetx_f7de10e0`, '发起草稿'), type: 'primary', click: this.createDraft, showCondition: this.showSignSendBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileUpload`, '文件上传'), type: 'primary', click: this.fileUpload, showCondition: this.showUploadBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_hAIxPW_f576ec9f`, '发起一步签署'), type: 'primary', click: this.launchOneStepEsignEvent, showCondition: this.showFlowBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/contractLock/elsClContract/add',
                edit: '/contractLock/elsClContract/edit',
                detail: '/contractLock/elsClContract/queryById',
                createDraft: '/contractLock/elsClContract/createDraft',
                upload: '/attachment/purchaseAttachment/upload',
                uploadLogUrl: '/attachment/purchaseAttachment/uploadLog',
                download: '/attachment/purchaseAttachment/download',
                launchOneStepEsign: '/esign/purchaseEsign/launchOneStepEsign',
                getSignature: '/attachment/purchaseAttachment/getSignature'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.pageData.form.needCheck = '0'
        this.pageData.form.autoSend = '0'
        this.init()
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.busNumber,
                actionRoutePath: '/srm/contractLock/PurchaseCLSignList'
            }
        },
        showFileDelBtn (){
            const form = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if(form.uploaded == '1'){
                return false
            }
            return true
        },
        showSignSendBtn (){
            const form = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (form.signerVindicateStatus == '3' && form.contractStatus  !== 'DRAFT' && form.launch !== '1'  ) {
                return true
            }
            return false
        },
        showUploadBtn (){
            const form = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (form.contractStatus  == 'DRAFT' && form.uploaded != '1'  ) {
                return true
            }
            return false
        },
        showFlowBtn (){
            const form = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (form.uploaded == '1' && form.launch != '1'  ) {
                return true
            }
            return false
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        allowSign (row){
            if(row.personal){
                return true
            }
            return false
        },
        allowSeal (row){
            let arr = row.tenantTypeArr || []
            if(arr.includes('COMPANY')){
                return false
            }
            return true
        },
        initEdit ( ){
            if(this.currentEditRow && this.currentEditRow.id) {
                getAction(this.url.detail, {id: this.currentEditRow.id}).then((res) => {
                    if (res.success) {
                        this.currentEditRow = res.result
                    }
                })
            }
        },
        createDraft (){
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            const that = this
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    console.log('params:', params)
                    if(!params.purchaseSigners || params.purchaseSigners.length == 0){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSuPWRCnGRPWL_6c367d06`, '请添加签署公司和设置签署人'))
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                    const  companyId = params.companyId || this.pageData.form.companyId
                    if(params.purchaseSigners[0].personal && !companyId){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AnImPWLLSAEjmLKWViFPWdD_4b0f671b`, '当第一个签署人为无企业的个人时，请选择签署主体'))
                        this.selectType = 'subject'
                        this.$refs.editPage.confirmLoading = false
                        let url = '/contractLock/purchaseCLCompanyInfo/getSignList'
                        let columns = [
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'), width: 180 },
                            { field: 'companyId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCAZdWW_99b0ddbb`, '公司id'), width: 120 },
                            { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态')}
                        ]
                        let params = {}
                        this.$refs.fieldSelectModal.open(url, params, columns, 'single')
                        return
                    }

                    getAction(this.url.createDraft, {id: params.id, companyId: companyId}).then(res=>{
                        if(res.success){
                            this.$message.success('合同创建成功')
                            this.$refs.editPage.confirmLoading = false
                            this.init()
                        }else {
                            this.$refs.editPage.confirmLoading = false
                            if(res.message == '网络异常，请检查网络连接'){
                                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAQLAEsLHcAZdROlbWVWKAELiHcneROjlb_421c7ed2`, '发起流程企业还未进行契约锁功能授权，请先在企业认证进行合同功能的授权'))
                            }else {
                                this.$message.error(res.message)
                            }
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        showAddPurchaseSignEvent (){
            return true
        },
        showDeleteSaleSignEvent (){
            return true
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.pageData.form = {}
                this.$refs.editPage.queryDetail('')
            }
        },
        deleteBatch (){
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/esign/purchaseEsignAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        deleteSaleSignEvent (){
            this.createFlag = false
            let itemGrid = this.$refs.editPage.$refs.purchaseSigners[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        deleteFilesEvent (row) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            getAction('/contractLock/signAttachment/delete', {id: row.id}).then(res => {
                if(res.success){
                    getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) fileGrid.remove(row)
                    })
                }else {
                    this.$message.warning(res.message)
                }
            })
            this.uploadFlag = false
        },
        getSeal (row, column, $rowIndex){
            this.createFlag = false
            this.selectType = 'addSigner'
            this.rowIndex = $rowIndex
            let url = '/contractLock/purchaseClSeals/list'
            let columns = [
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 200 },
                { field: 'sealId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeWW_39f03bbd`, '签章id'), width: 180 },
                { field: 'sealName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'), width: 180 }
            ]
            let params = {companyId: row.companyId, operateStatus: 'ENABLE', sealType: 'ENTERPRISE'}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addPurchaseSignEvent () {
            this.createFlag = false
            this.selectType = 'purchaseSign'
            const form = this.$refs.editPage.getPageData()
            let url = '/contractLock/purchaseCLCompanyInfo/getSignList'
            let columns = [
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称'), width: 180 },
                { field: 'companyId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCAZdWW_99b0ddbb`, '公司id'), width: 120 },
                { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态')}
            ]
            let params = {}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addPersonalEvent () {
            this.createFlag = false
            this.selectType = 'personalSign'
            const form = this.$refs.editPage.getPageData()
            let url = '/contractLock/saleClPersonalInfo/noCompanyList'
            let columns = [
                { field: 'applyUserName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'), width: 120 },
                { field: 'applyContactType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型')},
                { field: 'applyContact', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系方式')}
            ]
            let params = {}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addPurchaseSignerEvent (row, column, $rowIndex){
            this.createFlag = false
            this.selectType = 'purchaseSigner'
            this.rowSignerIndex = $rowIndex
            const form = this.$refs.editPage.getPageData()
            if(!row.tenantTypeArr){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFPWCAcW_752c471d`, '请先选择签署方类型！'))
                return
            }
            let url = '/contractLock/purchaseClPersonalInfo/list'
            let columns = [
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 100 },
                { field: 'applyUserName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), width: 100 },
                { field: 'applyContactType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'), width: 100 },
                { field: 'applyContact', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'), width: 100 },
                { field: 'roleStr_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_role`, '角色'), width: 100, disabled: true },
                { field: 'realName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQKRLi_2b438163`, '是否实名认证'), width: 100, editRender: {name: '$select', options: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LLi_194b947`, '未认证'), value: '0'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ILi_1721e0f`, '已认证'), value: '1'}
                ], disabled: true} }
            ]
            let params = {companyId: row.companyId, realName: '1'}
            let flag = false
            let change = []
            form.purchaseSigners.forEach(x=>{
                if(change.includes(x.companyId) || change.length == 0){
                    change.push(x.companyId)
                }else {
                    flag = true
                }
            })
            if(flag){
                let f = false
                for(let i=0;i<form.purchaseSigners.length;i++){
                    if(i == $rowIndex){
                        continue
                    }
                    let x =  form.purchaseSigners[i]
                    if(x.tenantTypeArr.includes('COMPANY') && x.roleStr !== 'ADMIN'){
                        if(x.signatoryName){
                            this.$message.warning('当不同公司签署时，签署方类型为公司的签署人必须为企业管理员，['+x.companyName
                                +']公司的['+x.signatoryName+']不是管理员角色!')
                            f = true
                            return
                        }
                    }
                }
                if(f){
                    return
                }
            }
            if(row.tenantTypeArr.includes('COMPANY')  && flag){
                params['roleStr'] = 'ADMIN'
            }
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        fieldSelectOk (data) {
            this.$refs.editPage.confirmLoading = false
            let form = this.$refs.editPage.getPageData()
            if(this.selectType == 'purchaseSign'){
                let itemGrid = this.$refs.editPage.$refs.purchaseSigners[0]
                let insertData = []
                let row ={}
                row['companyId'] = data[0].companyId
                row['companyName'] = data[0].companyName
                row['elsAccount'] = data[0].elsAccount
                form.companyId = data[0].companyId
                form.companyName = data[0].companyName
                form.elsAccount = data[0].elsAccount
                insertData.push(row)
                itemGrid.insertAt(insertData)
            }else if(this.selectType === 'personalSign'){
                let itemGrid = this.$refs.editPage.$refs.purchaseSigners[0]
                let insertData = []
                let row ={}
                row['elsAccount'] = data[0].elsAccount
                row['signatoryName'] = data[0].applyUserName
                row['signatoryContact'] = data[0].applyContact
                row['signatoryContactType'] = data[0].applyContactType
                row['roleStr'] = data[0].roleStr
                row['accountId'] = data[0].accountId
                row['tenantTypeArr'] = ['PERSONAL']
                row['roleType'] = '0'
                row['personal'] = true
                row.accountId = data[0].accountId
                insertData.push(row)
                itemGrid.insertAt(insertData)
            }
            else if(this.selectType === 'addSigner'){
                let ids = data.map(n => n.sealId).join(',')
                form.purchaseSigners[this.rowIndex].sealIds = ids
            }else if(this.selectType === 'keyWord'){
                if(data && data.length>0){
                    const { pageNo = '', posx = '', posy = '' } = data[0] || {}
                    let result = `${pageNo}_${posx}_${posy}`
                    let param = form.purchaseSigners[this.sealAeraIndex]
                    param.signArea = result
                }
            }else if(this.selectType === 'purchaseSigner'){
                let flag = false
                form.purchaseSigners.forEach(x=>{
                    if(x.companyId == data[0].companyId && x.signatoryContact == data[0].applyContact
                    && x.signatoryContactType == data[0].applyContactType){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IjdeRCdePWL_ed4f45b`, '已有相同公司相同签署人！'))
                        flag = true
                        return
                    }
                })
                if(flag){
                    return
                }
                form.purchaseSigners[this.rowSignerIndex].subAccount = data[0].subAccount
                form.purchaseSigners[this.rowSignerIndex].accountId = data[0].accountId
                form.purchaseSigners[this.rowSignerIndex].signatoryName = data[0].applyUserName
                form.purchaseSigners[this.rowSignerIndex].signatoryContact = data[0].applyContact
                form.purchaseSigners[this.rowSignerIndex].signatoryContactType = data[0].applyContactType
                form.purchaseSigners[this.rowSignerIndex].roleType = '0'
                form.purchaseSigners[this.rowSignerIndex].roleStr = data[0].roleStr
            }else if(this.selectType === 'subject'){
                this.pageData.form.companyId = data[0].companyId
                this.createDraft()
            }
        },
        uploadCallBack (result) {
            const params = this.$refs.editPage.getPageData()
            params.purchaseAttachmentList = []
            let itemGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            itemGrid.insertAt(result, -1)
            this.uploadFlag = false
        },
        deletePurchaseSignEvent (){
            let itemGrid = this.$refs.editPage.$refs.purchaseEsignSignersList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        saveEvent () {
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            const that = this
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                }
                if (flag) {
                    if(params.needCheck == '1' || params.autoSend == '1'){
                        if(!params.toElsAccount){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQTPMVQISPWMLKQJOhdLKKWRdXxOLV_459c36c8`, '是否需要回传文件或签署完成是否自动发送为是时，供应商不能为空'))
                            this.$refs.editPage.confirmLoading = false
                            return
                        }
                    }
                    params.signers = []
                    if(typeof params.id == 'undefined'){
                        postAction(this.url.add, params).then(res => {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            if(res.success){
                                this.$refs.editPage.confirmLoading = false
                                this.currentEditRow = res.result
                                this.uploadFlag = true
                                this.init()
                            }
                        })
                    }else {
                        postAction(this.url.edit, params).then(res => {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            if(res.success){
                                this.$refs.editPage.confirmLoading = false
                                this.uploadFlag = true
                                this.init()
                            }
                        })
                    }
                }
            }).catch(err => {
                console.log(err)
            })
        },
        launchOneStepEsignEvent (){
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            if(!params.purchaseSigners || params.purchaseSigners.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSuPWRCnGRPWL_6c367d06`, '请添加签署公司和设置签署人'))
                this.$refs.editPage.confirmLoading = false
                return
            }
            getAction('/contractLock/elsClContract/initiate', {id: params.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                this.$refs.editPage.confirmLoading = false
                if(res.success){
                    this.goBack()
                }
            })
        },
        fileUpload (){
            const params = this.$refs.editPage.getPageData()
            if(params.uploaded==='1'){
                this.$message.warning('文件已上传')
                return
            }
            if(!params.purchaseAttachmentList || params.purchaseAttachmentList.length<1){
                this.$message.warning('上传文件不能为空')
                return
            }
            if(!this.uploadFlag){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }
            this.$refs.editPage.confirmLoading = true
            postAction('/contractLock/elsClContract/uploadFileToContractLock', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                this.$refs.editPage.confirmLoading = false
                if(res.success){
                    this.init()
                }
            })

        },
        esignFileDown (){
            const params = this.$refs.editPage.getPageData()
            getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                if(res.success){
                    let str = res.result.downloadUrl
                    const fileName = str.substr(str.lastIndexOf('/')+1, str.length)
                    getAction(str, {}, {
                        responseType: 'blob'
                    }).then(res => {
                        let url = window.URL.createObjectURL(new Blob([res]))
                        let link = document.createElement('a')
                        link.style.display = 'none'
                        link.href = url
                        link.setAttribute('download', fileName)
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link) //下载完成移除元素
                        window.URL.revokeObjectURL(url) //释放掉blob对象
                    })

                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>