<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction } from '@/api/manage'
import REGEXP from '@/utils/regexp'
console.log('REGEXP', REGEXP)
export default {
    name: 'SaleEsignV3OrgEdit',
    mixins: [EditMixin],
    data () {
        return {
            selectType: 'esignPersonCertification',
            pageData: {
                form: {
                    subAccount: '',
                    psnAccount: '',
                    realname: '',
                    subAccountId: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRLiVH_1a6ae095`, '机构认证信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商账号'),
                                    fieldName: 'elsAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.form.elsAccount = data[0].toElsAccount,
                                        Vue.form.orgName = data[0].supplierName,
                                        Vue.form.subAccount = null,
                                        Vue.form.psnName = null,
                                        Vue.form.psnId = null
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), fieldLabelI18nKey: 'i18n_alert_ey_116b91', with: 150},
                                            {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), fieldLabelI18nKey: 'i18n_alert_RL_aa783', with: 150}
                                        ], modalUrl: 'supplier/supplierMaster/list', modalParams: {}
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQuKVRtRAB_8bd82932`, '是否加载组织机构列表'),
                                    fieldName: 'loadingOrg',
                                    dictCode: 'yn',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '0')
                                            setDisabledByProp('orgCode', flag)
                                            setDisabledByProp('orgName', !flag)
                                        }else{
                                            setDisabledByProp('orgCode', true)
                                            setDisabledByProp('orgName', true)
                                        }
                                    },
                                    required: '1',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQuKVRtRAB_8bd82932`, '是否加载组织机构列表')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRoo_307b3288`, '机构代码'),
                                    fieldName: 'orgCode',
                                    dictCode: 'purchase_organization_info,org_name,org_code,org_category_code="companyCode"',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                                    fieldName: 'orgName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                                    required: '1',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIy_76cf8d5`, '组织机构证件号'),
                                    fieldName: 'orgIdCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIy_76cf8d5`, '组织机构证件号'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIAc_e6376152`, '组织机构证件类型'),
                                    fieldName: 'orgIdCardType',
                                    dictCode: 'orgIdCardType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIAc_e6376152`, '组织机构证件类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLcR_b362e8a`, '法定代表人姓名'),
                                    fieldName: 'legalRepName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLcR_b362e8a`, '法定代表人姓名')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalIdNumber`, '法定代表人证件号'),
                                    fieldName: 'legalRepIdCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalIdNumber`, '法定代表人证件号')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLiIAc_2d136275`, '法定代表人证件类型'),
                                    fieldName: 'legalRepIdCardType',
                                    dictCode: 'psnIdCardType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLiIAc_2d136275`, '法定代表人证件类型')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIELsCLiFjtRLiCK_b76a6b88`, '指定页面中默认选择的机构认证方式'),
                                    fieldName: 'orgDefaultAuthMode',
                                    dictCode: 'orgDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIELsCLiFjtRLiCK_b76a6b88`, '指定页面中默认选择的机构认证方式')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjtRLiCK_1f7b29a2`, '设置页面中可选择的机构认证方式'),
                                    fieldName: 'orgAvailableAuthModes',
                                    dictCode: 'orgDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjtRLiCK_1f7b29a2`, '设置页面中可选择的机构认证方式')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjVH_fed36f58`, '设置页面中可编辑的信息'),
                                    fieldName: 'orgEditableFields',
                                    dictCode: 'orgEditableFields',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjVH_fed36f58`, '设置页面中可编辑的信息')
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    fieldName: 'subAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.form.subAccount = data[0].subAccount
                                        Vue.form.psnName = data[0].realname
                                        Vue.form.psnAccount = data[0].phone || data[0].email
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), fieldLabelI18nKey: 'i18n_alert_ey_116b91', with: 150},
                                            {field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), fieldLabelI18nKey: 'i18n_field_qcItemName', with: 150},
                                            {field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'), with: 150},
                                            {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), with: 150}
                                        ], modalUrl: '/account/elsSubAccount/getByElsAccount', modalParams: function (Vue, form) {
                                            return {
                                                participants: 'sale', toElsAccount: form.elsAccount, status: 1
                                            }
                                        },
                                        beforeCheckedCallBack: function (Vue, pageData, groupData, form) {
                                            debugger
                                            return new Promise((resolve, reject) => {
                                                let elsAccount = form.elsAccount || ''
                                                return elsAccount !== '' ? resolve('success') : reject(Vue.$srmI18n(`${Vue.$getLangAccount()}#i18n_title_selectSupplierFirst`, '请先选择供应商！'))
                                            })
                                        }
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLeyBK_a92cd95b`, '经办人账号标识'),
                                    fieldName: 'psnAccount',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLeyBK_a92cd95b`, '经办人账号标识'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLcR_21b77cc5`, '经办人姓名'),
                                    fieldName: 'psnName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLcR_21b77cc5`, '经办人姓名'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIy_15f34077`, '经办人证件号'),
                                    fieldName: 'psnIdCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIy_15f34077`, '经办人证件号')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIAc_a87a0df0`, '经办人证件类型'),
                                    fieldName: 'psnIdCardType',
                                    dictCode: 'psnIdCardType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIAc_a87a0df0`, '经办人证件类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLWEmy_ad7bbab7`, '经办人银行卡号'),
                                    fieldName: 'bankCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLWEmy_ad7bbab7`, '经办人银行卡号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLlty_155a8cbd`, '经办人手机号'),
                                    fieldName: 'psnMobile',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLlty_155a8cbd`, '经办人手机号')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '设置页面中默认选择的实名认证方式'),
                                    fieldName: 'psnDefaultAuthmode',
                                    dictCode: 'psnDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '设置页面中默认选择的实名认证方式')
                                },
                                {
                                    fieldType: 'multiple',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjmLLiCK_fdb374e8`, '设置页面中可选择的个人认证方式'),
                                    fieldName: 'psnAvailableAuthmodes',
                                    dictCode: 'psnDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjmLLiCK_fdb374e8`, '设置页面中可选择的个人认证方式')
                                },
                                {
                                    fieldType: 'multiple',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjmLVHJO_bd7b6fe6`, '设置页面中可编辑的个人信息字段'),
                                    fieldName: 'psnEditableFields',
                                    dictCode: 'psnEditableFields',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjmLVHJO_bd7b6fe6`, '设置页面中可编辑的个人信息字段')
                                },
                                {
                                    fieldType: 'multiple',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsbWvL_3d54cd4e`, '设置页面中权限范围'),
                                    fieldName: 'authorizedScopes',
                                    dictCode: 'orgAuthorizedScopes',
                                    required: '1',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsbWvL_3d54cd4e`, '设置页面中权限范围')
                                }
                            ],
                            validateRules: {
                                elsAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#111`, '供应商不能为空')}],
                                psnName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cRxOLV_d345baa`, '经办人姓名不能为空')}],
                                subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LieyxOLV_542509fe`, '认证账号不能为空')}],
                                psnAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLeyBKxOLV_4f83b7eb`, '经办人账号标识不能为空')}],
                                authorizedScopes: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bWvLxOLV_b65bb98e`, '权限范围不能为空')}],
                                orgName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRRLxOLV_539dca1d`, '机构名称不能为空')}],
                                orgIdCardType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIAc_e6376152`, '组织机构证件类型')}],
                                orgIdCardNum: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIy_76cf8d5`, '组织机构证件号')}]
                            }
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), type: 'primary', click: this.submitCertificationEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                edit: '/esignv3/saleEsignV3Org/edit',
                detail: '/esignv3/saleEsignV3Org/queryById',
                auth: '/esignv3/saleEsignV3Org/submitCertification',
                modifyAuthInfo: '/esignv3/saleEsignV3Org/modifyAuthInfo'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        modifyInfoBtn (){
            if(this.currentEditRow.accountId){
                return true
            }
            return false
        },
        saveEvent () {
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    let url = this.url.edit
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        this.goBack()
                    }).finally(() => {
                        this.$refs.editPage.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        submitCertificationEvent () {
            const _this = this
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    let url = this.url.auth
                    postAction(url, params).then(res => {
                        if(res.success){
                            this.goBack()
                            window.open(res.result.authUrl)
                        }else{
                            this.$message.error(res.message)
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>