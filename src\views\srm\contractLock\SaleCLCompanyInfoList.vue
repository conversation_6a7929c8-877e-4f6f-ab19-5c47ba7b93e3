<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <SaleManageClCompanyInfoEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleManageClCompanyInfoDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <SaleManageClCompanyInfoAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleManageClCompanyInfoAdd from './modules/SaleManageClCompanyInfoAdd'
import SaleManageClCompanyInfoDetail from './modules/SaleManageClCompanyInfoDetail'
import SaleManageClCompanyInfoEdit from './modules/SaleManageClCompanyInfoEdit'
import { postAction } from '@/api/manage'
import { getAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        SaleManageClCompanyInfoAdd,
        SaleManageClCompanyInfoDetail,
        SaleManageClCompanyInfoEdit
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                businessType: 'contractLock',
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractLock#saleCompanyInfo:detail'}
                ]
            },
            url: {
                list: '/contractLock/saleCLCompanyInfo/saleList',
                delete: '/contractLock/saleCLCompanyInfo/delete',
                deleteBatch: '/contractLock/saleCLCompanyInfo/deleteBatch',
                exportXlsUrl: 'contractLock/saleCLCompanyInfo/exportXls',
                importExcelUrl: 'contractLock/saleCLCompanyInfo/importExcel',
                columns: 'SaleCLCompanyInfo',
                auth: '/contractLock/saleCLCompanyInfo/submitCertification',
                refresh: '/contractLock/saleCLCompanyInfo/getCertificationInfo',
                getAuthPage: '/contractLock/saleCLCompanyInfo/getAuthPage'
            }
        }
    },
    methods: {
        //已认证不能被编辑
        allowEdit (row){
            if(row.certificationStatus == '2'){
                return true
            }
            return false
        },
        allowCertification (row){
            if(row.certificationStatus == '2'){
                return true
            }
            return false
        },
        allowRefreshStatus (row) {
            if(row.certificationStatus == '2'){
                return true
            }
            return false
        },
        //已经创建e签宝的账号的不能删除
        allowDelete (row){
            if(row.companyId){
                return true
            }
            return false
        },
        handleAdd () {
            this.showAddPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
        },
        handleEmployeeManagement (row) {
            console.log(row)
        },
        handleCertification (row) {
            let url = this.url.auth
            postAction(url, row).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                window.open(res.result.certificationPageUrl)
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        handleRefreshStatus (row) {
            getAction(this.url.refresh, {id: row.id}).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                }else {
                    this.$message.warning(res.message)
                }
                // 刷新列表
                this.$refs.listPage.loadData()
            })
        },
        getAuth (row) {
            getAction(this.url.getAuthPage, {id: row.id}).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    window.open(res.result.authPageUrl)
                }else {
                    this.$message.warning(res.message)
                }
                // 刷新列表
                this.$refs.listPage.loadData()
            })
        }
    }
}
</script>