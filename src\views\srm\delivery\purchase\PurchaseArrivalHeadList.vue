<template>
  <div style="height:100%">
    <list-layout
      v-show="!showLogistics && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"/>
    <Purcase-Delivery-Head-Modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
    <ViewPurchaseDeliveryModal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <logistics-timeline
      :show="logisticsVisible"
      :logisticsData="logisticsMsg"
      @logisticsHandleOk="logisticsHandleOk"
      @logisticsHandleCancel="handleCancel"
    ></logistics-timeline>
    <a-modal
      v-drag
      :maskClosable="false"
      :width="900"
      :height="660"
      v-model="transportVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_iWLE_438b95d1`, '运输规划')"
      :okText="$srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确认')"
      @cancel="transportVisible=false"
      @ok="transportVisible=false">
      <a-row
        type="flex"
        v-if="transportVisible">
        <a-col>
          <div class="map-container">
            <div
              v-if="transportVisible"
              id="transportMapContainer"
              style="height: 460px; width: 850px;"></div>
            <div
              v-if="transportVisible"
              class="extend-box">
              <a-button
                v-if="!searchBoxVisible"
                @click="searchBoxVisible=true">
                <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_open`, '展开') }}</span>
              </a-button>
            </div>
            <div
              v-if="transportVisible && searchBoxVisible"
              class="opt-box-wrap">
              <div class="search-box">
                <a-form-model
                  ref="transportFormRef"
                  :model="transportForm"
                  :rules="transportformRules"
                  :layout="formLayout">
                  <!-- <a-form-model-item :label="`车牌号:${transportForm.carNumber}`"  prop="carNumber">
                                                                                                        </a-form-model-item> -->
                  <a-form-model-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_alert_vKKIUyWKI_2190508f`, '开始时间与结束时间')"
                    prop="rangePicker">
                    <a-range-picker
                      show-time
                      format="YYYY-MM-DD HH:mm:ss"
                      v-model="transportForm.rangePicker"
                      @change="onChangeRangePicker">
                      <a-icon
                        slot="suffixIcon"
                        type="smile"/>
                    </a-range-picker>
                    <!-- <a-date-picker v-model="transportForm.beginTime">
                                                                                                                        <template slot="dateRender" slot-scope="current, today">
                                                                                                                            <div class="ant-calendar-date" :style="getCurrentStyle(current, today)">
                                                                                                                            {{ current.date() }}
                                                                                                                            </div>
                                                                                                                        </template>
                                                                                                                    </a-date-picker> -->
                  </a-form-model-item>
                  <!-- <a-form-model-item label="结束时间"  prop="endTime">
                                                                                                            <a-date-picker v-model="transportForm.endTime">
                                                                                                                <template slot="dateRender" slot-scope="current, today">
                                                                                                                    <div class="ant-calendar-date" :style="getCurrentStyle(current, today)">
                                                                                                                    {{ current.date() }}
                                                                                                                    </div>
                                                                                                                </template>
                                                                                                            </a-date-picker>
                                                                                                        </a-form-model-item> -->
                  <a-form-model-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_dict_qAyOy_1a017627`, '车牌号颜色')"
                    prop="licensePlateNumberColor">
                    <a-select
                      :placeholder="$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')"
                      v-model="transportForm.licensePlateNumberColor"
                      allow-clear>
                      <a-select-option
                        v-for="(f,fIndex) in plateNumberColors"
                        :key=" 'field'+fIndex"
                        :value="f.value"
                        :data-idx="fIndex"
                      >
                        {{ f.title }}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                  <a-form-model-item>
                    <div class="search-btn-wrap">
                      <a-button
                        type="primary"
                        @click="searchTransportationNode">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}
                      </a-button>
                      <a-button @click="searchBoxVisible=false">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_putAway`, '收起') }}
                      </a-button>
                    </div>
                  </a-form-model-item>
                </a-form-model>
                <a-card
                  class="search-result"
                  size="small"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_results`, '结果')">
                  <p>{{
                    $srmI18n(`${$getLangAccount()}#i18n_field_UtufKI_1902b3e9`, '预计到达时间')
                  }}：{{ estimateArriveTime(transportForm.estimateArriveTime) }}</p>
                  <p>{{
                    $srmI18n(`${$getLangAccount()}#i18n_field_bUiF_26b1fe1d`, '剩余运距')
                  }}：{{ transportForm.emainDistance }}</p>
                  <p>{{
                    $srmI18n(`${$getLangAccount()}#i18n_field_qRcKvL_d9db77c9`, '车辆行驶里程')
                  }}：{{ transportForm.runDistance || 0 }} km</p>
                  <!-- <p>地址：{{ transportForm.adr}}</p> -->
                  <p>{{
                    $srmI18n(`${$getLangAccount()}#i18n_field_APKQKW_6183c708`, '当前是否在线')
                  }}：{{
                    transportForm.offlineState ? $srmI18n(`${$getLangAccount()}#i18n_field_vW_f3c64`, '离线') : $srmI18n(`${$getLangAccount()}#i18n_field_KW_b0c97`, '在线')
                  }}</p>
                  <p>{{
                    $srmI18n(`${$getLangAccount()}#i18n_field_vWKH_393ea1ad`, '离线时长')
                  }}：{{ transportForm.offlineTime }}</p>
                </a-card>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script>
import ViewPurchaseDeliveryModal from './modules/ViewPurchaseDeliveryModal'
import LogisticsTimeline from '@comp/LogisticsTimeline/LogisticsTimeline'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, httpAction, postAction} from '@/api/manage'
import layIM from '@/utils/im/layIM.js'
import map from '@/utils/map'
import dateTransform from '@/utils/dateTransform'

export default {
    mixins: [ListMixin],
    components: {
        ViewPurchaseDeliveryModal,
        LogisticsTimeline
    },
    data () {
        return {
            transportVisible: false,
            searchBoxVisible: false,
            transportForm: {
                carNumber: '',
                licensePlateNumberColor: '',
                offlineState: null,
                offlineTime: null,
                runDistance: null,
                emainDistance: null,
                estimateArriveTime: null,
                adr: null,
                rangePicker: null,
                beginTime: null,
                endTime: null
            },
            transportformRules: {
                rangePicker: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择'),
                    trigger: 'blur'
                }],
                licensePlateNumberColor: [{
                    required: true,
                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择'),
                    trigger: 'blur'
                }]
            },
            plateNumberColors: [
                {value: '1', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_By_109935`, '蓝色')},
                {value: '2', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_Zy_13bc2e`, '黄色')},
                {value: '3', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ZIy_263e117`, '黄绿色')}
            ],
            formLayout: 'vertical',
            transportMap: null,
            currentEditRow: {},
            logisticsMsg: {},
            nextOpt: true,
            logisticsVisible: false,
            showLogistics: false,
            pageData: {
                businessType: 'purchaseDelivery',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDescOrOrderCode`, '请输入单据描述或发货单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliveryWay`, '配送方式'),
                        fieldName: 'deliveryWay',
                        dictCode: 'srmDeliveryWay',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')
                    }
                ],
                form: {
                    keyWord: '',
                    deliveryWay: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_uSRL_277d402d`, '到货确认'),
                        authorityCode: 'delivery#purchaseDeliveryHead:arrival',
                        icon: '',
                        clickFn: this.arrival,
                        primary: true
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'order#purchaseDeliveryHead:queryById'
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iWLE_438b95d1`, '运输规划'),
                        clickFn: this.handleTransport,
                        allow: this.showTransportCondition,
                        authorityCode: 'logistics#trace:planningService'
                    },
                    {
                        type: '',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsInfo`, '物流信息'),
                        allow: this.showLogisticsCondition,
                        clickFn: this.logisticsMessage,
                        authorityCode: 'logistics#trace:express'
                    },
                    {
                        type: 'chat',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'),
                        clickFn: this.handleChat
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }

                ],
                optColumnWidth: 150
            },
            url: {
                list: '/delivery/purchaseDeliveryHead/listArrival',
                arrival: '/delivery/purchaseDeliveryHead/arrival',
                express: '/api/trace/express',
                columns: 'purchaseDeliveryHeadList'
            }
        }
    },
    methods: {
        // 格式化预计到达时间
        estimateArriveTime (arriveTime) {
            let estimateArrive = ''
            if (arriveTime) {
                estimateArrive = dateTransform.dateTrans(new Date(arriveTime).getTime(), 'yyyy-mm-dd hh:mm:ss')
            }
            return estimateArrive
        },
        onChangeRangePicker (date, dateString) {
            this.transportForm.rangePicker = dateString
            this.transportForm.beginTime = dateString[0]
            this.transportForm.endTime = dateString[1]
        },
        getCurrentStyle (current, today) {
            const style = {}
            if (current.date() === 1) {
                style.border = '1px solid #1890ff'
                style.borderRadius = '50%'
            }
            return style
        },
        // 获取运输-search
        searchTransportationNode () {
            this.transportMap?.destroy()
            this.planningService(this.transportForm)
        },
        // 运输规划
        planningService (row) {
            row.beginTime = row.beginTime || dateTransform.dateTrans(new Date(row.deliveryTime).getTime(), 'yyyy-mm-dd hh:mm:ss')
            row.endTime = row.endTime || dateTransform.dateTrans(new Date(row.deliveryTime).getTime() + 24 * 60 * 60 * 1000 * 2, 'yyyy-mm-dd hh:mm:ss')
            map.init().then(async (mapInstance) => {
                let shoppingAddress = ''
                let deliveryAddress = ''
                let geocoder = new mapInstance.service.Geocoder()
                if (row.fbk1) {
                    let shoppingAddressObj = await geocoder.getLocation({address: row.fbk1 || ''})
                    shoppingAddress = shoppingAddressObj.result.location.lng + ',' + shoppingAddressObj.result.location.lat

                }
                if (row.deliveryAddress) {
                    let deliveryAddressObj = await geocoder.getLocation({address: row.deliveryAddress || ''})
                    deliveryAddress = deliveryAddressObj.result.location.lng + ',' + deliveryAddressObj.result.location.lat
                }
                let params = {
                    licensePlateNumber: row.carNumber,  //'陕YH0008',
                    licensePlateNumberColor: row.licensePlateNumberColor || 2, //'2',
                    beginTime: row.beginTime, //'2022-03-05 15:56:46',
                    endTime: row.endTime,  //'2022-03-06 15:56:46'
                    originCoordinates: shoppingAddress,
                    destinationCoordinates: deliveryAddress
                }

                this.transportForm.carNumber = row.carNumber
                this.transportForm.licensePlateNumberColor = row.licensePlateNumberColor
                this.transportForm.fbk1 = row.fbk1,
                this.transportForm.deliveryAddress = row.deliveryAddress
                postAction('/api/trace/planningService', params).then((res) => {
                    if (res && res.success) {
                        let result = res.result
                        //this.transportForm.carNumber = result.carNumber
                        //this.transportForm.licensePlateNumberColor = result.licensePlateNumberColor
                        this.transportForm.offlineState = result.offlineState
                        this.transportForm.offlineTime = result.offlineTime
                        this.transportForm.runDistance = result.runDistance
                        this.transportForm.emainDistance = result.emainDistance
                        this.transportForm.estimateArriveTime = result.estimateArriveTime
                        this.transportForm.adr = result.adr
                        // this.transportForm.beginTime = result.beginTime
                        // this.transportForm.endTime = result.endTime
                        let trackArray = result.runRoute
                        let estimateRoute = result.estimateRoute || []
                        let endLatLng = result.estimateRoute && result.estimateRoute.length ? estimateRoute[estimateRoute.length - 1] : trackArray[trackArray.length - 1]
                        this.initMap(mapInstance, trackArray[0], endLatLng, trackArray, estimateRoute)
                    } else {
                        this.$message.warning(res.message)
                        this.initMap(mapInstance)
                    }
                })
            })
        },
        // 初始化地图，传参必传
        initMap (TMap, beginLatLng, endLatLng, trackArray, estimateRoute) {
            let center = new TMap.LatLng(39.984120, 116.307484)
            // 定义map变量，调用 TMap.Map() 构造函数创建地图
            this.transportMap = new TMap.Map(document.getElementById('transportMapContainer'), {
                center: center, //设置地图中心点坐标
                zoom: 13   //设置地图缩放级别
            })
            // let geocoder = new TMap.service.Geocoder()
            // geocoder.getLocation({ address: '北京市朝阳区'}).then((result)=> {
            //     console.log('坐标和地址转换，北京市朝阳区起始地址：'+ result.result.location)
            // })

            if (beginLatLng && endLatLng && trackArray && trackArray.length) {
                let markerArray = [
                    {
                        position: new TMap.LatLng(beginLatLng.lat / 600000, beginLatLng.lon / 600000),
                        styleId: 'start',
                        id: 'beginMarker'
                    },
                    {
                        position: new TMap.LatLng(endLatLng.lat / 600000, endLatLng.lon / 600000),
                        styleId: 'end',
                        id: 'endMarker'
                    }
                ]
                let polylinePaths = []
                if (trackArray && trackArray.length) {
                    trackArray.forEach((item) => {
                        let latlng = new TMap.LatLng(item.lat / 600000, item.lon / 600000)
                        polylinePaths.push(latlng)
                    })
                }

                let estimateRoutePaths = []
                if (estimateRoute && estimateRoute.length) {
                    estimateRoute.forEach((item) => {
                        let latlng = new TMap.LatLng(item.lat / 600000, item.lon / 600000)
                        estimateRoutePaths.push(latlng)
                    })
                }
                let polylineArray = [
                    {
                        id: 'pl_1',
                        styleId: 'style_red',
                        paths: polylinePaths
                    },
                    {
                        id: 'pl_2',
                        styleId: 'style_green',
                        paths: estimateRoutePaths
                    }
                ]
                let marker = new TMap.MultiMarker({
                    map: this.transportMap,
                    styles: {
                        start: new TMap.MarkerStyle({
                            width: 25,
                            height: 35,
                            anchor: {x: 16, y: 32},
                            src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/start.png'
                        }),
                        end: new TMap.MarkerStyle({
                            width: 25,
                            height: 35,
                            anchor: {x: 16, y: 32},
                            src: 'https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/end.png'
                        })
                    },
                    geometries: markerArray
                })
                let polylineLayer = new TMap.MultiPolyline({
                    map: this.transportMap,
                    styles: {
                        'style_red': new TMap.PolylineStyle({
                            'color': '#CC0000', //线填充色
                            'width': 6, //折线宽度
                            'borderWidth': 5, //边线宽度
                            'borderColor': '#CCC', //边线颜色
                            'lineCap': 'round' //线端头方式
                        }),
                        'style_green': new TMap.PolylineStyle({
                            'color': '#009688', //线填充色
                            'width': 6, //折线宽度
                            'borderWidth': 5, //边线宽度
                            'borderColor': '#CCC', //边线颜色
                            'lineCap': 'round' //线端头方式
                        })
                    },
                    geometries: polylineArray
                })
                this.transportMap.setCenter(new TMap.LatLng(beginLatLng.lat / 600000, beginLatLng.lon / 600000))
                // this.transportMap.easeTo({zoom:17,rotation:90},{duration: 2000})
            }

        },
        handleTransport (row) {
            this.transportVisible = true
            //this.transportForm.carNumber = row.carNumber
            //this.transportForm.licensePlateNumberColor = row.licensePlateNumberColor
            this.planningService(row)
        },
        showTransportCondition (row) {
            if (row.carNumber && row.licensePlateNumberColor) {
                return false
            } else {
                return true
            }
        },
        handleChat (row) {
            let {id} = row
            let recordNumber = row.deliveryNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'PurchaseDeliveryHead', url: this.url || '', recordNumber})
        },
        hideDetailPage () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            } else {
                this.showDetailPage = false
            }
        },
        logisticsMessage (row) {
            if (row.trackingNumber == null) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsDocNoIseEmpty`, '物流单号为空'))
                return
            } else {
                if (row.trackingNumber.startsWith('SF') && row.receivePhone == '') {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '顺丰单号查询需要输入发货/收货人手机号'))
                    return
                }
                this.$refs.listPage.confirmLoading = true
                getAction(this.url.express, {
                    'expressNumber': row.trackingNumber,
                    'phone': row.receivePhone
                }, 'get').then((res) => {
                    if (res.success) {
                        this.logisticsMsg = res.result
                        this.logisticsVisible = true
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.$refs.listPage.confirmLoading = false
                })
            }
        },
        showLogisticsCondition (row) {
            if (this.btnInvalidAuth('express:viewArrival')) {
                return false
            }
            if (row.trackingNumber) {
                return false
            }
            return true
        },
        logisticsHandleOk () {
            this.logisticsVisible = false
        },
        handleCancel () {
            this.logisticsVisible = false
        },
        arrival () {
            let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            if (selectedRows.length <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectLineToConfirm`, '请选择需要确认的行'))
                return
            } else {
                let that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmArrival`, '确认到货'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationWillConfirmArrivalConfirmArrival`, '此操作将确认到货，是否确认到货?'),
                    onOk: function () {
                        that.postUpdateData(that.url.arrival, selectedRows)
                    }
                })

            }
        },
        postUpdateData (url, row) {
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        }
    }
}
</script>
<style lang="scss" scoped>
:deep(.ant-calendar-picker ) {
    width: 368px;
}

.map-container {
    position: relative;

.extend-box {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 1000;
}

.opt-box-wrap {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 400px;
    bottom: 8px;
    background-color: rgba(255, 255, 255, 0.6);
    z-index: 1000;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.5s;

.search-btn-wrap {
    text-align: center;
}

.search-box {
    position: relative;
    width: 100%;
    height: 100%;

.search-btn-wrap {

.ant-btn {
    margin-right: 10px;
}

}

.search-result {

p {
    margin: 0px;
}

}
}
}
}
</style>