<template>
  <div class="business-container">
    <business-layout
      :ref="businessRefName"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :remoteJsFilePath="remoteJsFilePath"
      :pageHeaderButtons="pageHeaderButtons"
      :externalToolBar="externalToolBar"
      :gridCustomConfig="gridCustomConfig"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      :handleAfterDealSource="handleAfterDealSource"
      modelLayout="tab"
      pageStatus="detail"
      v-on="businessHandler"
    >
      <template #customRender="{slotProps}">
        <div
          :style="{color:primaryColor,cursor:'pointer'}"
          @click="linkToDetail(slotProps.group)">
          {{ `${$srmI18n(`${$getLangAccount()}#i18n_field_mARH_31060cbe`, '查看明细')}` }}
        </div>
      </template>
    </business-layout>

    <customSelectModal
      ref="customSelectModal"
      :isEdit="false"
      :footer="{footer: null}"
    />
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import { mixin } from '@/utils/mixin.js'
import {
    BUTTON_BACK,
    BUTTON_AUDIT_CANCEL,
    BUTTON_ChECKPROCESS
} from '@/utils/constant.js'
export default {
    mixins: [businessUtilMixin, mixin],
    components: {
        BusinessLayout,
        customSelectModal: () =>import('../components/frozenFunctionModal.vue')
    },
    data () {
        return {
            gridCustomConfig: {
                rowClassName: function ({row}){
                    if (row.updateType && row.updateType===3){
                        return 'row-delete-type'
                    }
                    if (row.updateType && row.updateType===2){
                        return 'row-add-type'
                    }
                },
                cellClassName ({ row, rowIndex, column, columnIndex }) {
                    if (row[column.property+'_new']) {
                        return 'row-add-type'
                    }
                }
            },
            externalToolBar: {
                supplierBankInfoList: [
                    {...new BatchDownloadBtn({pageCode: 'supplierBankInfoList'}).btnConfig}
                ],
                supplierInfoChangeAttachmentList: [
                    {...new BatchDownloadBtn({pageCode: 'supplierInfoChangeAttachmentList'}).btnConfig}
                ]
            },
            pageHeaderButtons: [
                // {
                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                //     key: 'release',
                //     attrs: {
                //         type: 'primary'
                //     },
                //     click: this.handleRelease,
                //     show: this.showReleaseBtn
                // },
                BUTTON_BACK
            ],
            requestData: {
                detail: {
                    url: '/supplier/supplierInfoChangeHead/queryById', args: (that) => {return { id: that.currentEditRow.id }}
                }
            },
            infoChangVisible: false,
            url: {
                detail: '/supplier/supplierInfoChangeHead/queryById',
                release: '/supplier/supplierInfoChangeHead/releaseByIds',
                cancelAudit: '/elsUflo/audit/cancel',
                submit: '/supplier/supplierInfoChangeHead/submit',
                submitAudit: '/a1bpmn/audit/api/submit',
                download: '/attachment/purchaseAttachment/download'
            }

        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_supplierMasterData_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        // 确定操作
        handleConfirm () {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                content: '是否确认该单据?',
                onOk: function () {
                    getAction(that.url.confirm, {id: formatData.infoChangId}).then((res) => {
                        if (res && res.success) {
                            that.$message.success('确认成功！')
                            that.businessHide()
                        } else {
                            that.$message.warning('确认失败！')
                        }
                    })
                }
            })
        },
        handleAfterDealSource (pageConfig) {
            const allData = this.getAllData()
            const { enterpriseForm= {}, expandForm = {} } = allData
            let formatData = { ...enterpriseForm, ...expandForm }

            pageConfig.groups.forEach(v=>{
                if(v.groupCode !== 'enterpriseForm' && v.groupCode !== 'expandForm' && v.groupCode !== 'supplierInfoChangeAttachmentList' && v.groupCode !== 'supplierCertificatedInfoList'){
                    const column = {
                        groupCode: 'supplierContactsInfoList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_crAc_25e0e6bb`, '修改类型'),
                        field: 'updateType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        width: '150'
                    }
                    v.columns.splice(3, 0, column)
                }

                // 添加变更标识
                if (formatData[v.groupCode+'_hasStar']) {
                    v.hasStar=true
                }
            })

            // 银行页签 file 下载
            let index0 = pageConfig.groups.findIndex(item => item.groupCode == 'supplierBankInfoList')
            if (pageConfig.groups[index0].extend && pageConfig.groups[index0].extend.editConfig) {
                pageConfig.groups[index0].extend.optColumnList = [{key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.bankFileDownloadEvent}]
            }
        },
        // 发布按钮显隐
        showReleaseBtn (){
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let params = {
                ...enterpriseForm,
                ...expandForm
            }
            let status = params.infoChangStatus
            let publishStatus = params.infoChangPublishStatus

            // 单据状态为“新建” 和 发布状态为“未发布”，则显示按钮
            if (status==='0' && publishStatus && publishStatus==='0') {
                return true
            }
            return false
        },
        // 发布事件
        handleRelease () {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let params = {
                ...enterpriseForm,
                ...expandForm
            }
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                onOk: function () {
                    getAction(that.url.release, {id: params.infoChangId}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            // 刷新列表
                            that.businessHide()
                        }
                    })
                }
            })
        },

        // 提交
        submitEvent () {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm,
                ...allData,
                id: enterpriseForm.id
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmToSubmit`, '确认提交'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_i18n_title_confirmToSubmitTips`, '是否确认提交?'),
                onOk: function () {
                    that.$refs[that.businessRefName].confirmLoading = true
                    // 需要审批
                    if (formatData.infoChangNeedAudit && formatData.infoChangNeedAudit==='1' && formatData.infoChangAuditStatus==='0') {
                        let param = {}
                        param['businessId'] = formatData.infoChangId
                        param['rootProcessInstanceId'] = formatData.infoChangFlowId
                        param['businessType'] = 'supplierInfoChangAudit'
                        param['auditSubject'] = formatData.toElsAccount + ' -- 供应商信息变更审批'
                        param['params'] = JSON.stringify(formatData)
                        postAction(that.url.submitAudit, param).then(res => {
                            that.$refs[that.businessRefName].confirmLoading = false
                            if (res.success) {
                                that.businessHide()
                                that.$message.success(res.message)
                            } else {
                                that.$message.warning(res.message)
                            }
                        }).catch(()=>{
                            that.$refs[that.businessRefName].confirmLoading = false
                        })
                    } else {
                        // 无需审批
                        postAction(that.url.submit, formatData).then(res => {
                            that.$refs[that.businessRefName].confirmLoading = false
                            if(res.success) {
                                if (!that.currentEditRow.id) {
                                    that.currentEditRow.id = res.result.id
                                }
                                that.businessHide()
                                that.$message.success(res.message)
                            }else {
                                that.$message.warning(res.message)
                            }
                        }).catch(()=>{
                            that.$refs[that.businessRefName].confirmLoading = false
                        })
                    }
                }
            })
        },
        showcSubmitBtn () {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            console.log(formatData, 'formatData')
            let status = formatData.infoChangStatus
            let auditStatus = formatData.infoChangAuditStatus
            // “新建+未审批” 或者 "新建+无需审批"
            if((status==='0' && auditStatus && auditStatus==='0') || (status==='0' && auditStatus && auditStatus==='4')){
                return true
            }else{
                return false
            }
        },
        showcCncelConditionBtn (){
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            let status = formatData.infoChangStatus
            let auditStatus = formatData.infoChangAuditStatus
            // 新建+审批中
            if(status==='0' && auditStatus==='1'){
                return true
            }else{
                return false
            }
        },
        showConfirmBtn (){
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            let status = formatData.infoChangStatus
            let publishStatus = formatData.infoChangPublishStatus

            // 单据状态为“待确认” 和 发布状态为“已发布”，则显示按钮
            if (status==='3' && publishStatus && publishStatus==='1') {
                return true
            }
            return false
        },
        handleBeforeRemoteConfigData (data) {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEVHAHBI_2f93ce75`, '企业信息变更附件'),
                        groupCode: 'supplierInfoChangeAttachmentList',
                        groupType: 'item',
                        sortOrder: data.groups.length+'',
                        extend: {
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'supplierBankInfoList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '100',
                        slots: {default: 'grid_opration'}
                    },

                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }

                ]
            }
        },
        bankFileDownloadEvent (Vue, row) {
            this.downloadFile (row)
        },
        downloadFile (row){
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }

            let fileNameStr = row.fileName
            if (row.fileName.includes('➔')) {
                if (fileNameStr.split('➔').length > 1 && (fileNameStr.split('➔')[1]!=='' && fileNameStr.split('➔')[1]!==null)) {
                    fileNameStr = fileNameStr.split('➔')[1].trim()
                } else {
                    fileNameStr = fileNameStr.split('➔')[0].trim()
                }
            }

            const [id, fileName] = fileNameStr.split('-')
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        downloadEvent (Vue, row) {
            const params = {id: row.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        linkToDetail (data) {
            console.log(data)
            let {supplierMasterFrozenHistoryList, frozenFunction} = data.formModel
            let params = {
                supplierMasterFrozenHistoryList,
                frozenFunction: []
            }
            try {
                params = {
                    supplierMasterFrozenHistoryList,
                    frozenFunction: frozenFunction ? JSON.parse(frozenFunction) : ''
                }

            } catch (e) {
                console.log(e)
            }
            this.$refs.customSelectModal.open(params)
        },
        changeInfoView (row){
            this.currentChangEditRow = row
            this.infoChangVisible = true
        }
    }
}
</script>
<style lang="scss" scoped>
    .business-container{
        :deep(.row-delete-type ){
            color: rgba(0, 0, 0, 0.25);
            background-color: #f5f5f5;
        }
        :deep(.row-add-type ){
            background-color: #fff3e0;
            color:rgb(24, 144, 255);
        }
    }
</style>