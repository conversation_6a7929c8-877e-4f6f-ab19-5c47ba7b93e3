<!--
 * @Author: your name
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2021-11-19 11:23:07
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\sale\modules\SaleEditCost.vue
-->
<template>
  <a-modal
    v-drag    
    v-if="showVisible"
    :visible="showVisible"
    :maskClosable="false"
    :width="800"
    :height="160"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_costTemplate`, '成本模板')"
    @cancel="showVisible = false"
    @ok="setCostOk"
  >
    <div
      class="page-container"
      style="height: 460px;overflow-y: scroll">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :fromSourceData="fromSourceData"
        :pageStatus="containerStatus"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        @handleImportSuccess="importCallBack"
        v-on="businessHandler"
      >
      </business-layout>
    </div>
  </a-modal>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { downloadCostTemplate } from '@/utils/util.js'

const IMPORT_URL = '/els/cost/excel/importExcel'
const EXPORT_URL = '/cost/excel/exportExcel'

export default {
    name: 'SaleEditCost',
    components: {
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        const that = this
        return {
            containerStatus: 'edit',
            showVisible: false,
            fromSourceData: {},
            requestData: {
                export: {
                    url: EXPORT_URL,
                    args: ({ groupCode }) => {
                        let account = that.currentEditRow.templateAccount || that.currentEditRow.busAccount
                        let id = that.currentEditRow.templateNumber + '_' + that.currentEditRow.templateVersion + '_' + account
                        let data = that.currentEditRow.itemId + '_' + groupCode
                        return {
                            groupCode: groupCode,
                            roleCode: 'sale',
                            handlerName: 'saleCostExcelRpcServiceImpl',
                            id: id,
                            data: data
                        }
                    }
                },
                import: {
                    url: IMPORT_URL,
                    args: ({ groupCode }) => {
                        let account = that.currentEditRow.templateAccount || that.currentEditRow.busAccount
                        let id = that.currentEditRow.templateNumber + '_' + that.currentEditRow.templateVersion + '_' + account
                        return { groupCode: groupCode, roleCode: 'sale', handlerName: 'saleCostExcelRpcServiceImpl', id: id,
                            excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_costTemplate`, '成本模板')}
                    }
                }
            },
            externalToolBar: {
                all: [
            //         {
            //             title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt`, '导入'),
            //             key: 'gridImportExcel',
            //             attrs: { type: 'primary' },
            //             args: {
            //                 url: IMPORT_URL,
            //                 roelCode: 'sale',
            //                 handlerName: 'saleCostExcelRpcServiceImpl',
            //                 type: 'enquiry-sale-cost',
            //                 busAccount: that.currentEditRow.busAccount,
            //                 account: that.currentEditRow.templateAccount || that.currentEditRow.busAccount,
            //                 templateNumber: that.currentEditRow.templateNumber,
            //                 templateVersion: that.currentEditRow.templateVersion
            //             },
            //             beforeClick: ({ btn }) => {
            //                 btn.args.type = 'enquiry-sale-cost'
            //                 btn.args.busAccount = that.currentEditRow.busAccount
            //                 btn.args.account = that.currentEditRow.templateAccount || that.currentEditRow.busAccount
            //                 btn.args.templateNumber = that.currentEditRow.templateNumber
            //                 btn.args.templateVersion = that.currentEditRow.templateVersion

            //                 return new Promise(resolve => {
            //                     resolve(true)
            //                 })
            //             },
            //             callBack: this.uploadCallBackCurrent
            //         },
            //         {
            //             title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
            //             key: 'gridExport',
            //             attrs: {
            //                 type: 'primary'
            //             },
            //             args: {
            //                 url: EXPORT_URL,
            //                 roelCode: 'sale',
            //                 handlerName: 'saleCostExcelRpcServiceImpl',
            //                 id: '',
            //                 data: '',
            //                 busAccount: '',
            //                 account: '',
            //                 templateNumber: '',
            //                 templateVersion: ''
            //             },
            //             beforeClick: ({ Vue, btn }) => {
            //                 let account = that.currentEditRow.templateAccount || that.currentEditRow.busAccount
            //                 let id = that.currentEditRow.templateNumber + '_' + that.currentEditRow.templateVersion + '_' + account
            //                 let data = that.currentEditRow.itemId + '_' + Vue.groupCode
            //                 btn.args.id = id
            //                 btn.args.data = data
            //                 btn.args.itemId = that.currentEditRow.itemId
            //                 btn.args.busAccount = that.currentEditRow.busAccount
            //                 btn.args.account = account
            //                 btn.args.templateNumber = that.currentEditRow.templateNumber
            //                 btn.args.templateVersion = that.currentEditRow.templateVersion

            //                 return new Promise(resolve => {
            //                     resolve(true)
            //                 })
            //             },
            //             click: data => {
            //                 this.businessGridExportExcelCurrent(data)
            //             }
            //         },
                     {
                         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                         key: 'gridAdd',
                         attrs: {
                             type: 'primary'
                         },
                         click: this.businessGridAdd
                     },
                     {
                         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                         key: 'gridDelete',
                         attrs: {
                             type: 'primary'
                         },
                         click: this.businessGridDelete
                     }
                 ]
             }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_costForm_${templateNumber}_${templateVersion}`
        },
        externalToolBar () {
            if (this.containerStatus !== 'edit') return {}
            return {
                all: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt`, '导入'),
                        key: 'gridImportExcel',
                        attrs: { type: 'primary' },
                        args: {
                            url: IMPORT_URL,
                            roelCode: 'sale',
                            handlerName: 'saleCostExcelRpcServiceImpl',
                            type: 'enquiry-sale-cost',
                            busAccount: this.currentEditRow.busAccount,
                            account: this.currentEditRow.templateAccount || this.currentEditRow.busAccount,
                            templateNumber: this.currentEditRow.templateNumber,
                            templateVersion: this.currentEditRow.templateVersion,
                            excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_costTemplate`, '成本模板')
                        },
                        beforeClick: ({ btn }) => {
                            btn.args.type = 'enquiry-sale-cost'
                            btn.args.busAccount = this.currentEditRow.busAccount
                            btn.args.account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
                            btn.args.templateNumber = this.currentEditRow.templateNumber
                            btn.args.templateVersion = this.currentEditRow.templateVersion

                            return new Promise(resolve => {
                                resolve(true)
                            })
                        },
                        callBack: (info, groupCode, pageConfig) => this.uploadCallBackCurrent(info, groupCode, pageConfig)
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
                        key: 'gridExport',
                        attrs: {
                            type: 'primary'
                        },
                        args: {
                            url: EXPORT_URL,
                            roelCode: 'sale',
                            handlerName: 'saleCostExcelRpcServiceImpl',
                            id: '',
                            data: '',
                            busAccount: '',
                            account: '',
                            templateNumber: '',
                            templateVersion: ''
                        },
                        beforeClick: ({ Vue, btn }) => {
                            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
                            let id = this.currentEditRow.templateNumber + '_' + this.currentEditRow.templateVersion + '_' + account
                            let data = this.currentEditRow.itemId + '_' + Vue.groupCode
                            btn.args.id = id
                            btn.args.data = data
                            btn.args.itemId = this.currentEditRow.itemId
                            btn.args.busAccount = this.currentEditRow.busAccount
                            btn.args.account = account
                            btn.args.templateNumber = this.currentEditRow.templateNumber
                            btn.args.templateVersion = this.currentEditRow.templateVersion
                            return new Promise(resolve => {
                                resolve(true)
                            })
                        },
                        click: data => {
                            this.businessGridExportExcelCurrent(data)
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        attrs: {
                            type: 'primary'
                        },
                        click: this.businessGridAdd
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        attrs: {
                            type: 'primary'
                        },
                        click: this.businessGridDelete
                    }
                ]
            }
        }
    },
    methods: {
        open (data, containerStatus) {
            this.containerStatus = containerStatus || 'edit'
            this.showVisible = true
            this.fromSourceData = data
        },
        uploadCallBackCurrent (result, groupCode, pageConfig) {
            const code = result && result.fileList[0] && result.fileList[0].response && result.fileList[0].response.code
            if (code === 200) {
                const refs = Object.keys(result.fileList[0].response.result)
                refs.forEach(i => {
                    const fileGrid = this.getItemGridRef(i)
                    fileGrid.loadData(result.fileList[0].response.result[i])
                })
                // 分别计算成本 小计赋值
                for (let index = 0; index < pageConfig.groups.length; index++) {
                    const group = pageConfig.groups[index]
                    let sumField = group.total.culumnName
                    if (sumField) {
                        let totalValue = 0
                        result.fileList[0].response.result[group.groupCode].forEach(item => {
                            totalValue += Number(item[sumField])
                        })
                        group.total.totalValue = totalValue
                    }
                }
            }
        },
        businessGridExportExcelCurrent ({ btn }) {
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let id = this.currentEditRow.templateNumber + '_' + this.currentEditRow.templateVersion + '_' + account
            const { allData = {} } = this.getBusinessExtendData(this.businessRefName)
            const {  groupCode = '', handlerName = '', roelCode = '', url = ''} = btn.args || {}
            let data = this.currentEditRow.itemId + '_' + groupCode
            const options = {
                url,
                groupCode,
                handlerName,
                roelCode,
                id: id || allData.id,
                itemId: this.currentEditRow.itemId,
                busAccount: this.currentEditRow.busAccount,
                account: this.currentEditRow.templateAccount,
                templateNumber: this.currentEditRow.templateNumber,
                templateVersion: this.currentEditRow.templateVersion,
                data: data // 成本报价this.currentEditRow.itemId + '_' + groupCode
            }
            return downloadCostTemplate(options)
        },
        importCallBack ({ response, group, ref }) {
            let result = response.result.dataList
            ref[0].loadData(result)
            let sumField = group.total.culumnName
            let totalValue = 0
            result.forEach(item => {
                totalValue += Number(item[sumField])
            })
            group.total.totalValue = totalValue
        },
        setCostOk () {
            let extendAllData = this.$refs[`${this.businessRefName}`].extendAllData()
            // this.$emit('costCallBack', extendAllData)
            this.$parent.$refs.editPage.$parent.costCallBack(extendAllData)
            this.showVisible = false
        }
    }
}
</script>
