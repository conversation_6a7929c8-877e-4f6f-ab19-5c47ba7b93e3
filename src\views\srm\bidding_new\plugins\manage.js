import { axios } from '@/utils/request'
import { isObject, isArray, merge } from 'lodash'
// 提供给招投标大厅使用
// 需要在请求头带上'xNodeId',请求体带上checkType，processType

const getNodeParams = () => {
    let currentNode = sessionStorage.getItem('tender_currentNode') ? JSON.parse(sessionStorage.getItem('tender_currentNode')) : {}
    let { nodeId = '', extend = {} } = currentNode
    return {
        nodeId,
        ...extend
    }
}

//post
export function postAction (url, parameter = {}, config) {
    let { nodeId, checkType, processType, currentStep } = getNodeParams()
    if (isArray(parameter)) {
        parameter.map(item => {
            if (isObject(item)) {
                item = Object.assign(item, {
                    checkType,
                    processType
                })
            }
        })
    } else {
        parameter = Object.assign(parameter, {
            checkType,
            processType,
            currentStep
        })
    }
    let params = {
        url: url,
        method: 'POST',
        data: parameter,
        headers: {
            xNodeId: nodeId
        }
    }
    if (config) {
        Object.assign(params, config)
    }
    return axios(params)
}

//post method= {post | put}
export function httpAction (url, parameter, method) {
    return axios({
        url: url,
        method: method,
        data: parameter
    })
}

//put
export function putAction (url, parameter) {
    let { nodeId, checkType, processType, currentStep } = getNodeParams()
    return axios({
        url: url,
        method: 'post',
        data: parameter,
        headers: {
            xNodeId: nodeId
        }
    })
}

//get
export function getAction (url, parameter, config) {
    let { nodeId, checkType, processType, currentStep } = getNodeParams()
    console.log(checkType, processType, currentStep)
    if (!isArray(parameter) && parameter != null) {
        parameter = Object.assign(
            {
                checkType,
                processType,
                currentStep
            },
            parameter
        )
    } else if (parameter ?? '' != '') {
        parameter = Object.assign(
            {},
            {
                checkType,
                processType,
                currentStep
            }
        )
    }
    let params = {
        url: url,
        method: 'get',
        params: parameter,
        headers: {
            xNodeId: nodeId
        }
    }
    if (config) {
        Object.assign(params, config)
    }
    return axios(params)
}

//deleteAction
export function deleteAction (url, parameter) {
    let { nodeId, checkType, processType, currentStep } = getNodeParams()
    return axios({
        url: url,
        method: 'get',
        params: parameter,
        headers: {
            xNodeId: nodeId
        }
    })
}

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export function downFile (url, parameter) {
    let { nodeId } = getNodeParams()
    return axios({
        url: url,
        params: parameter,
        headers: {
            xNodeId: nodeId
        },
        method: 'get',
        responseType: 'blob'
    })
}

// 自定义method post or other
export function httpRequest (url, parameter, method) {
    let params = {
        url,
        method,
        params: parameter
    }
    return axios(params)
}
