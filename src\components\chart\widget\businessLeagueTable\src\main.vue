
<template>
  <dv-scroll-ranking-board
    :config="{
      data: data,
      unit: widget.option.unit,
      waitTime: widget.option.waitTime,
      rowNum: widget.option.rowNum,
      valueFormatter: widget.option.valueFormatter,
    }"
    :style="style"
  />
</template>
<script>
import { chartsMixins } from "@comp/chart/widget/mixins/chartsMixins";
export default {
  name: "BusinessLeagueTable",
  mixins: [chartsMixins],
  props: {
    widget: {
      type: [Object],
      default: () => { },
    },
  },
  computed: {
    option () {
      return {
        unit: this.widget.option.unit,
        waitTime: this.widget.option.waitTime,
      };
    },
  },
  watch: {
    option: {
      handler (val) {
        this.widget.option.unit = val.unit;
        this.widget.option.waitTime = val.waitTime;
      },
      deep: true,
    },
  },
  methods: {
    refreshWidgetData (data) {
      this.widget.data = data;
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .row-item {
  height: 80px !important;
  margin: 0 10px;
}
</style>