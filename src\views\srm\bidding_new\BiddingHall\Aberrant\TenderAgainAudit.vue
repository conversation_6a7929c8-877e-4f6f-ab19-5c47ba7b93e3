<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-header">
          <a-row>
            <a-col
              class="desc-col"
              :span="6">
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_details`, '详情') }}</span>
              <span
                v-if="subpackageTitle"
                style="position:absolute;margin-left:10px;"><a-tag
                  color="blue"
                  style="font-size:15px">{{ subpackageTitle }}</a-tag></span>
            </a-col>
            <a-col
              class="btn-col"
              :span="18">
              <taskBtn
                v-if="taskInfo.taskId"
                :currentEditRow="currentEditRow"
                :pageHeaderButtons="pageHeaderButtons"
                v-on="$listeners" />
            </a-col>
          </a-row>
        </div>
        <div class="page-content">
          <div class="baseInfo">
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_baseInfo`, '基本信息') }}</span>
            </titleTrtl>
            <a-form-model
              ref="baseForm"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
              :rules="rules"
              :model="formData">
              <a-row>
                <a-col :span="8">
                  <a-form-model-item
                    :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_KQVIYBdI_1e8f8c02`, '是否新建招标项目')}`"
                    prop="againProject">
                    <m-select
                      class="width120"
                      v-model="formData['againProject']"
                      :disabled="!isEdit"
                      :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                      dict-code="yn" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_KQoNVzs_5f0de999`, '是否带入新分包')}`"
                    prop="againSupplier">
                    <m-select
                      class="width120"
                      v-model="formData['againSupplier']"
                      :disabled="!isEdit"
                      @change="changeAgainSupplier"
                      :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                      dict-code="yn" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_KQUz_2fba950f`, '是否审批')}`"
                    prop="audit">
                    <m-select
                      class="width120"
                      v-model="formData['audit']"
                      :disabled="!isEdit"
                      :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                      dict-code="yn" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item 
                    :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_VVYBdRBI_8f4ea1bc`, '重新招标相关附件')}`"
                  >
                    <div
                      v-for="(fileItem, index) in formData.purchaseAttachmentDTOList"
                      :key="fileItem.id">
                      <span style="color: blue; cursor: pointer; margin-right: 8px">{{ fileItem.fileName }}</span>
                      <span>
                        <a
                          style="margin: 0 8px"
                          @click="preViewEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                        <a @click="downloadEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
                      </span>
                    </div>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row class="textAreaClass">
                <a-form-model-item
                  :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_VVYBtH_94014b34`, '重新招标记录')}`"
                  prop="againTenderRecord">
                  <a-textarea
                    v-model="formData['againTenderRecord']"
                    :disabled="!isEdit"
                    :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_field_VVYBtH_94014b34`, '重新招标记录')}`"
                    :auto-size="{ minRows: 3, maxRows: 5 }" />
                </a-form-model-item>
              </a-row>
            </a-form-model>
          </div>
          <div class="subPackInfo">
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_zsVH_268adaad`, '分包信息') }}</span>
            </titleTrtl>
            <listTable
              ref="listTable"
              v-if="showTable"
              :fromSourceData="purchaseTenderAgainSubpackageItemList"
              :showTablePage="false"
              :checkedConfig="checkedConfig"
              :statictableColumns="statictableColumns"> </listTable>
          </div>
        </div>
      </a-spin>
    </div>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      :isTree="true"
      @ok="fieldSelectOk" />
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow" />
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import titleTrtl from '../components/title-crtl'
import listTable from '../components/listTable'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
import flowViewModal from '@comp/flowView/flowView'
import { mapGetters } from 'vuex'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    props: {
        currentEditRow: {
            default: () => {},
            type: Object
        }
    },
    components: {
        flowViewModal,
        titleTrtl,
        listTable,
        taskBtn,
        fieldSelectModal
    },
    data () {
        return {
            subpackageTitle: '',
            labelCol: { span: 9 },
            wrapperCol: { span: 14 },
            confirmLoading: false,
            formData: {},
            rules: {},
            url: {
                queryById: '/tender/abnormal/purchaseTenderAgainSubpackageHead/queryById'
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    type: '',
                    click: this.showFlow,
                    showCondition: () => {
                        return true
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'rollBack',
                    click: this.goBackAudit,
                    showCondition: () => {
                        return true
                    }
                }
            ],
            flowView: false,
            flowId: '',
            purchaseTenderAgainSubpackageItemList: [],
            checkedConfig: {
                checkMethod: ({ row }) => {
                    // 当前分包不能勾选
                    if (row.id == this.subId) {
                        return false
                    }
                    return true
                }
            },
            showTable: true,
            statictableColumns: [
                {
                    type: 'checkbox',
                    width: 50
                },
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    width: 120,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsAy_269085a0`, '分包编号'),
                    field: 'subpackageNumber'
                },
                {
                    width: 120,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称'),
                    field: 'subpackageName',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '招标流程模型名称'),
                    width: 160,
                    field: 'tenderProcessModelName'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_biddingType`, '招标类型'),
                    field: 'tenderType_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJCK_2b39a7da`, '审查方式'),
                    field: 'checkType_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_KQsR_2fbb601f`, '是否报名'),
                    field: 'signUp_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQlB_2fb9d2b0`, '是否售标'),
                    field: 'tenderType_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_UBCK_411be079`, '评标方式'),
                    field: 'evaluationType_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_vBCK_2cc273bd`, '开标方式'),
                    field: 'bidOpenType_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_YBLT_2e859370`, '招标程序'),
                    field: 'processType_dictText'
                }
            ]
        }
    },
    computed: {
        ...mapGetters(['taskInfo']),
        isEdit () {
            return false
        }
    },
    methods: {
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程'))
                return
            }
            this.flowView = true
        },
        /**
         * 审批方法
         */
        goBackAudit () {
            this.$parent.hideController()
        },
        queryDetail () {
            this.confirmLoading = true
            let params = {
                id: this.currentEditRow.id
            }
            getAction(this.url.queryById, params)
                .then((res) => {
                    if (res.success) {
                        this.formData = res.result || {}
                        this.purchaseTenderAgainSubpackageItemList = this.formData.purchaseTenderAgainSubpackageItemList || []
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        async downloadEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            this.$previewFile.open({ params: row })
        }
    },
    mounted () {
        this.subpackageTitle = this.currentEditRow.subject || ''

        this.queryDetail()
    }
}
</script>
<style lang="less" scoped>
.deliveryTime-title,
.clarification-title {
    padding: 8px;
    background: #f2f3f5;
}
:deep(.edit-page .textAreaClass .ant-form-item-control-wrapper){
    width: 88%;
}
:deep(.edit-page .textAreaClass .ant-form-item-label){
    width: 12%;
}
</style>
