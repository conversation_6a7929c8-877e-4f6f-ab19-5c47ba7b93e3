<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
    />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"
    />
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'
import { httpAction } from '@/api/manage'
import { postAction, getAction } from '@/api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import flowViewModal from '@comp/flowView/flowView'

export default {
  mixins: [DetailMixin],
  components: {
    flowViewModal,
    fieldSelectModal
  },
  data() {
    return {
      flowView: false,
      flowId: '',
      currentBasePath: this.$variateConfig['domianURL'],
      pageData: {
        groups: [
          {
            groupName: '送货申请行信息',
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseDeliveryRequestItemList',
              columns: [],
              buttons: []
            }
          }
        ],
        publicBtn: [{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }]
      },
      url: {
        add: '/delivery/purchaseDeliveryRequestHead/add',
        edit: '/delivery/purchaseDeliveryRequestHead/edit',
        detail: '/delivery/purchaseDeliveryRequestHead/queryById',
        public: '/delivery/purchaseDeliveryRequestHead/publish'
      }
    }
  },
  computed: {
    fileSrc() {
      let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
      let templateVersion = this.currentEditRow.templateVersion
      let account = this.currentEditRow.templateAccount ? this.currentEditRow.templateAccount : this.currentEditRow.busAccount
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${account}/purchase_deliveryRequest_${templateNumber}_${templateVersion}.js?t=` + time
    }
  },
  methods: {
    beforeHandleData(data){
      data.itemColumns.map(item=>{
          if(item.field == 'materialNumber'){
              item.sortable=true
          }
      })
    },
    preViewEvent(row) {
      let fromData = this.currentEditRow
      if (row.fileType == '2' && (fromData.biddingStatus === '1' || fromData.biddingStatus === '0')) {
        this.$message.warning('开标前不允许查看投标文件！')
        return
      }
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    auditCancelConditionBtn() {
      let params = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      let auditStatus = params.auditStatus
      if (auditStatus == '1') {
        return true
      } else {
        return false
      }
    },
    showFlowConditionBtn() {
      let params = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      let auditStatus = params.auditStatus
      if (auditStatus == '1' || auditStatus == '2' || auditStatus == '3') {
        return true
      } else {
        return false
      }
    },
    showFlow() {
      let params = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      this.flowId = params.flowId
      if (!this.flowId) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
        return
      }
      this.flowView = true
    },
    closeFlowView() {
      this.flowView = false
    },
    downloadEvent(row) {
      this.downloadFile(row)
    },
    handleDownload({ id, fileName }) {
      const params = {
        id
      }
      getAction('/attachment/purchaseAttachment/download', params, {
        responseType: 'blob'
      }).then((res) => {
        console.log(res)
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    },
    downloadFile(row) {
      let fromData = this.currentEditRow
      if (row.fileType == '2' && (fromData.biddingStatus === '1' || fromData.biddingStatus === '0')) {
        this.$message.warning('开标前不允许下载投标文件！')
        return
      }
      this.handleDownload(row)
    },
    auditCancel() {
      let form = (this.$refs.detailPage && this.$refs.detailPage.form) || {}
      let param = {}
      param['businessType'] = 'publishBidding'
      param['businessId'] = form.id
      param['rootProcessInstanceId'] = form.flowId
      this.confirmLoading = true
      httpAction(this.url.cancel, param, 'post')
        .then((res) => {
          if (res.success) {
            this.auditVisible = false
            this.$message.success(res.message)
            this.$parent.cancelAuditCallBack(form)
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    replenishMaterialNumber() {
      let records = this.$refs.detailPage.$refs.purchaseBiddingItemList[0].getCheckboxRecords() || []
      if (records.length !== 1) {
        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectARowDataOperate`, '请选择一条行数据进行操作'))
        return
      }
      let row = records[0]
      if (row.materialNumber) {
        this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_material`, '物料')}：` + row.materialDesc + `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_codeAlreadyExistsNoNeedSupplement`, '已存在编码，无需补充！')}`)
        return
      }
      let columns = [
        { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150 },
        { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150 },
        { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200 },
        { field: 'brand', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialBrand`, '物料品牌'), width: 200 },
        { field: 'purchaseCycle', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_procurementCycle`, '采购周期'), width: 200 },
        { field: 'purchaseType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseType`, '采购类型'), width: 200 },
        { field: 'checkWay_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkWay`, '检验方式'), width: 200 },
        { field: 'purchaseUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingUnit`, '采购单位'), width: 200 },
        { field: 'materialModel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialModel`, '物料型号'), width: 200 },
        { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'), width: 200 }
      ]
      this.$refs.fieldSelectModal.open('/material/purchaseMaterialHead/list', {}, columns, 'single')
    },
    fieldSelectOk(data) {
      let records = this.$refs.detailPage.$refs.purchaseBiddingItemList[0].getCheckboxRecords()
      var row = records[0]
      row.materialNumber = data[0].materialNumber
      const _this = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tips`, '提示'),
        content: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, '是否确认将物料')}：` + row.materialDesc + `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_encodingUpdatedTo`, '的编码更新为')}：` + data[0].materialNumber,
        onOk() {
          _this.$refs.detailPage.showLoading()
          row.materialDesc = data[0].materialDesc
          row.materialGroup = data[0].materialGroup
          row.materialGroupName = data[0].materialGroupName
          row.materialModel = data[0].materialModel
          row.materialSpec = data[0].materialSpec
          postAction(_this.url.replenish, row)
            .then((res) => {
              if (res.success) {
                _this.$message.success(res.message)
                _this.init()
              } else {
                _this.$message.warning(res.message)
              }
            })
            .finally(() => {
              _this.$refs.detailPage.hideLoading()
            })
        }
      })
    }
  }
}
</script>
