<template>
  <div>
    <div class="title_ctrl clearfix margin-b-10">
      <span class="fl">{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBQD_2e695bfc`, '投标问题') }}</span>
      <div
        v-if="targetFlag"
        class="fr">
        <a-button
          type="primary"
          size="small"
          @click="handleAddQuestion">{{ $srmI18n(`${$getLangAccount()}#i18n_field_DGQD_2ea5c614`, '提出问题') }}</a-button>
      </div>
    </div>
    <div>
      <listTable
        ref="listTable"
        :showTablePage="true"
        :pageData="pageData"
        :defaultParams="defaultParams"
        :url="url"
        :statictableColumns="tableColumns"
      />
    </div>
  </div>
</template>
<script>
import listTable from '../../components/listTable'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins.js'

export default {
    components: {
        listTable
    },
    inject: ['subpackageId', 'currentSubPackage'],
    mixins: [ baseMixins],
    computed: {
        subId (){
            return this.subpackageId()
        },
        subpackage (){
            return this.currentSubPackage()
        }
    },
    data () {
        return {
            targetFlag: false,
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QfBD_4603a317`, '问题标题'),
                    'field': 'title'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DGL_180ebd0`, '提出人'),
                    'field': 'questionerName',
                    'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DGKI_2e9ff468`, '提出时间'),
                    'field': 'createTime',
                    'width': 160
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText',
                    'width': 80
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit},
                    {type: 'reply', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reply`, '回复'), clickFn: this.handleReply, allow: this.allowReply},
                    {type: 'close', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_close`, '关闭'), clickFn: this.handleClose, allow: this.allowClose}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            defaultParams: {
                subpackageId: this.subId
            },
            url: {
                list: '/tender/saleTenderMentoringHead/list',
                close: '/tender/saleTenderMentoringHead/close',
                publish: '/tender/saleTenderMentoringHead/publish'

            }
        }
    },
    methods: {
        handleAddQuestion (){
            this.$emit('handQuestionsEditPage', {})
        },
      
        handleView (row) {
            this.$emit('handleView', row)
        },
        handleEdit (row) {
            this.$emit('handleEdit', row)
        },
        allowEdit (row){
            return row.status != '0'
        },
        handleReply (row) {
            this.$emit('handleReply', row)
        },
        handleClose (row){
            var that=this
            this.confirmLoading = true
            postAction(that.url.close+'?id='+row.id).then(res => {
                if (res.success) {
                    console.log('res', res)
                    that.$message.success(res.message)
                    that.$refs.listTable.loadData()
                    // this.$emit('hide')
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        allowReply (row){
            return row.status != '3'&&row.status !='0' ? false : true
        },
        allowClose (row){
            return row.status != '3'&&row.status !='0' ? false : true
        },
        allowPublish (row){
            return row.status != '1' ? false : true
        },
        handlePublish (row){
            var that=this
            this.confirmLoading = true
            postAction(this.url.publish+'?id='+row.id).then(res => {
                if (res.success) {
                    that.$message.success(res.message)
                    that.$refs.listTable.loadData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
            
            
        }
    },
    created () {
        let flag=this.subpackage.status < 4010 && this.subpackage.status > 3010
        let preFlag=this.subpackage.status < 2210 && this.subpackage.status > 2010
        this.targetFlag = this.currentNode().extend.checkType == '0' ? preFlag : flag
        console.log(flag, preFlag, this.targetFlag)
        this.defaultParams= { subpackageId: this.subId } 
        console.log('defaultParams', this.defaultParams)
    }
}
</script>
<style lang="less" scoped>
.fl{
  float: left;
}
.fr{
  float: right;
}
.clearfix{
  clear:both;
}
.title_ctrl {
  padding: 8px;
  background: #F2F3F5;
}
.margin-b-10{
  margin-bottom: 5px;
}
</style>


