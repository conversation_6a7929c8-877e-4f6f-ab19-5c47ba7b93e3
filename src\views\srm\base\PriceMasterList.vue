<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <price-master-modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
  </div>
</template>

<script>
import PriceMasterModal from './modules/PriceMasterModal'
import {listPageMixin} from '@comp/template/listPageMixin'

export default {
    name: 'PriceMasterList',
    mixins: [listPageMixin],
    components: {
        PriceMasterModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input', 
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                        fieldName: 'supplierName', 
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterSupplierName`, '请输入供应商名称')
                    },
                    {
                        type: 'input', 
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                        fieldName: 'materialDesc', 
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterMaterialDesc`, '请输入物料描述')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/base/priceMaster/list',
                delete: '/base/priceMaster/delete',
                deleteBatch: '/base/priceMaster/deleteBatch',
                exportXlsUrl: '/base/priceMaster/exportXls',
                importExcelUrl: '/base/priceMaster/importExcel',
                columns: 'basePriceMasterList'          
            }
        }
    },
    computed: {

    },
    created () {

    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceMainDate`, '价格主数据'))
        }
    }
}
</script>
