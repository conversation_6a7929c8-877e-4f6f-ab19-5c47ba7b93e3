<template>
  <a-form-item
    :label="attr.label"
    :label-col="{ span: attr.layout }"
    :wrapper-col="{ span: attr.layout === 24 ? 24 : 24 - attr.layout }"
    :required="attr.rules.length > 0"
  >
    <a-checkbox-group :default-value="attr.initialValue">
      <template v-for="(el, idx) of attr.options">
        <a-checkbox
          :style="radioStyle"
          :value="el.value"
          :key="idx">
          {{ String.fromCharCode((65 + idx)) }}.{{ el.label }}
        </a-checkbox>
      </template>
    </a-checkbox-group>
  </a-form-item>
</template>

<script>
import { mapState } from 'vuex'
export default {
    name: 'CheckboxControl',
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    data () {
        return {
            radioStyle: {
                display: 'block',
                height: '30px',
                lineHeight: '30px',
                marginLeft: 0
            }
        }
    },
    computed: {
        attr () {
            return this.data.attr
        },
        ...mapState({
            formData: state => state.formDesigner.formData
        })
    }
}
</script>

<style lang="less" scoped>

</style>
