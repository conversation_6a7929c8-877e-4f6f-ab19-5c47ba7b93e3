import { srmI18n, getLangAccount } from '@/utils/util'
export const tabs = [
    srmI18n(`${getLangAccount()}#i18n_title_operaRecord`, '操作记录'),
    srmI18n(`${getLangAccount()}#i18n_title_auditRecord`, '审批记录'),
    srmI18n(`${getLangAccount()}#i18n_field_nXtH_31a0ab6d`, '核销记录')
]

export const operaUrl = '/log/queryOptHisList'
export const auditUrl = '/elsUflo/audit/auditHislist'
export const recordUrl = '/finance/purchaseWriteOffRecord/queryWriteOffList'

export const operaPagerConfig = {
    total: 0,
    currentPage: 1,
    pageSize: 500,
    align: 'right',
    pageSizes: [20, 50, 100, 200, 500],
    layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
    perfect: true
}

export const auditPagerConfig = {
    total: 0,
    currentPage: 1,
    pageSize: 500,
    align: 'right',
    pageSizes: [20, 50, 100, 200, 500],
    layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
    perfect: true
}

export const recordPagerConfig = {
    total: 0,
    currentPage: 1,
    pageSize: 500,
    align: 'right',
    pageSizes: [20, 50, 100, 200, 500],
    layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
    perfect: true
}

export const operaColumns = [
    {
        'type': 'seq',
        'width': 50,
        'title': srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号')
    },
    {
        'title': srmI18n(`${getLangAccount()}#i18n_title_operatorPerson`, '操作人'),
        'field': 'username',
        'width': 200
    },
    {
        'title': srmI18n(`${getLangAccount()}#i18n_title_operationTime`, '操作时间'),
        'field': 'createTime',
        'width': 150
    },
    {
        'title': srmI18n(`${getLangAccount()}#i18n_title_action`, '动作'),
        'field': 'operateName',
        'width': 200
    },
    {
        'title': srmI18n(`${getLangAccount()}#i18n_title_elapsedTimeMS`, '耗时（毫秒）'),
        'field': 'costTime'
    }
]

export const auditColumns = [
    {
        'type': 'seq',
        'width': 50,
        'title': srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号')
    },
    { field: 'subject', title: srmI18n(`${getLangAccount()}#i18n_title_processTheTheme`, '流程主题'), width: 220 },
    { field: 'nodeName', title: srmI18n(`${getLangAccount()}#i18n_title_node`, '节点'), width: 100 },
    { field: 'state_dictText', title: srmI18n(`${getLangAccount()}#i18n_baseForm01bf_ruleStatus`, '状态'), width: 100 },
    { field: 'owner', title: srmI18n(`${getLangAccount()}#i18n_field_userDeal`, '处理人'), width: 120 },
    { field: 'opinion', title: srmI18n(`${getLangAccount()}#i18n_field_opinion`, '处理意见'), width: 120 },
    { field: 'createDate', title: srmI18n(`${getLangAccount()}#i18n_field_createTime`, '创建时间'), width: 150 },
    { field: 'endDate', title: srmI18n(`${getLangAccount()}#i18n_field_completeTime`, '完成时间'), width: 150 }
]

export const recordColumns = [
    {
        'type': 'seq',
        'width': 50,
        'title': srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号')
    },
    { field: 'elsAccount', title: srmI18n(`${getLangAccount()}#i18n_title_purchaseElsAccount`, '采购ELS账号'), width: 220 },
    { field: 'toElsAccount', title: srmI18n(`${getLangAccount()}#i18n_title_supplierElsNumber`, '供应商ELS账号'), width: 100 },
    { field: 'thisWriteOffAmount', title: srmI18n(`${getLangAccount()}#i18n_field_vmnXHf_2582c9a9`, '本次核销金额'), width: 100 },
    { field: 'paymentApplyNumber', title: srmI18n(`${getLangAccount()}#i18n_field_BVty_25742128`, '付款单号'), width: 120 },
    { field: 'reconciliationNumber', title: srmI18n(`${getLangAccount()}#i18n_title_reconciliationNo`, '对账单号'), width: 120 },
    { field: 'beginDate', title: srmI18n(`${getLangAccount()}#i18n_field_soaBeginDate`, '对账开始日期'), width: 150 },
    { field: 'endDate', title: srmI18n(`${getLangAccount()}#i18n_field_soaEndDate`, '对账结束日期'), width: 150 },
    { field: 'writeOffTime', title: srmI18n(`${getLangAccount()}#i18n_field_BVnXKI_a37024ec`, '付款核销时间'), width: 150 },
    { field: 'createBy', title: srmI18n(`${getLangAccount()}#i18n_field_createBy`, '创建人'), width: 150 },
    { field: 'createTime', title: srmI18n(`${getLangAccount()}#i18n_title_createTime`, '创建时间'), width: 150 },
    { field: 'updateBy', title: srmI18n(`${getLangAccount()}#i18n_PurchaseMassProdHeadList_updateBy`, '修改人'), width: 150 },
    { field: 'updateTime', title: srmI18n(`${getLangAccount()}#i18n_field_updateTime`, '修改时间'), width: 150 }
]