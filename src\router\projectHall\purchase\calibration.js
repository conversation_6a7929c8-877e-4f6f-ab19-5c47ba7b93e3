import { RouteView } from '@/components/layouts'

const calibrationRouter = {
    path: '/projectHall/calibration',
    name: 'project_calibration',
    meta: {
        title: '定标',
        titleI18nKey: 'i18n_title_calibration',
        icon: 'icon-111-01',
        keepAlive: false
    },
    component: RouteView,
    children: [
        {
            path: '/projectHall/calibration/instruct',
            name: 'project_instruct',
            meta: {
                title: '定标请示',
                titleI18nKey: 'i18n_menu_IBVK_2b3bb490',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'instruct' */ '@/views/srm/bidding_project/hall/purchase/calibration/Instruct.vue')
        },
        {
            path: '/projectHall/calibration/instructHistory',
            name: 'project_instructHistory',
            meta: {
                title: '定标记录查看',
                titleI18nKey: 'i18n_menu_IBtHmA_4aac6158',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'instructHistory' */ '@/views/srm/bidding_project/hall/purchase/calibration/InstructHistory.vue')
        },
        {
            path: '/projectHall/calibration/resultNotice',
            name: 'project_resultNotice',
            meta: {
                title: '中标通知书',
                titleI18nKey: 'i18n_title_bidWinNotice',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'resultNotice' */ '@/views/srm/bidding_project/hall/purchase/calibration/ResultNotice.vue')
        }
    ]
}

export default calibrationRouter