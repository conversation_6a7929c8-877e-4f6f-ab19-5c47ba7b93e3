<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
    <!-- <field-select-modal 
      ref="fieldSelectModal" /> -->
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
// import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction } from '@/api/manage'
import REGEXP from '@/utils/regexp'
export default {
    name: 'SubaccountCertificationAdd',
    mixins: [EditMixin],
    // components: {
    //     fieldSelectModal
    // },
    data () {
        return {
            selectType: 'esignPersonCertification',
            pageData: {
                form: {
                    subAccount: '',
                    applyUserName: '',
                    applyContact: '',
                    applyContactType: '',
                    mode: '',
                    modifyFields: '',
                    subAccountId: '',
                    paperType: '',
                    otherModes: '',
                    idCardNo: '',
                    bankMobile: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_UzSQNJOHrA0MPEQA`, '个人认证信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    fieldName: 'subAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.form.subAccount = data[0].subAccount
                                        Vue.form.applyUserName = data[0].realname
                                        Vue.form.applyContact = data[0].phone
                                        if(Vue.form.applyContactType === 'EMAIL') {
                                            Vue.form.applyContact = data[0].email
                                        }
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150},
                                            {field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150},
                                            {field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_phone`, '手机号'), with: 150},
                                            {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'), with: 150}
                                        ], modalUrl: '/account/elsSubAccount/list', modalParams: {status: 1}
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDLiRL_29ec92a2`, '指定用户认证名称'),
                                    fieldName: 'applyUserName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDLiRL_29ec92a2`, '指定用户认证名称'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'),
                                    fieldName: 'applyContact',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    fieldName: 'applyContactType',
                                    dictCode: 'contractLockContactType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KRLiCK_389fb71a`, '实名认证模式'),
                                    fieldName: 'mode',
                                    dictCode: 'contractLockMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KRLiCK_389fb71a`, '实名认证模式')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiqcrdW_254a9ced`, '认证可修改项'),
                                    fieldName: 'modifyFields',
                                    dictCode: 'contractLockModifyFields',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiqcrdW_254a9ced`, '认证可修改项')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                                    fieldName: 'paperType',
                                    dictCode: 'contractLockPaperType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PtLiCKqid_7031f7d2`, '降级认证方式可选项'),
                                    fieldName: 'otherModes',
                                    dictCode: 'contractLockOtherModes',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PtLiCKqid_7031f7d2`, '降级认证方式可选项')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDUziy_2a6f5b4a`, '指定用户身份证号'),
                                    fieldName: 'idCardNo',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDUziy_2a6f5b4a`, '指定用户身份证号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmy_2e1bfb0e`, '指定用户银行卡号'),
                                    fieldName: 'bankNo',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmy_2e1bfb0e`, '指定用户银行卡号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmUQlty_7742a9aa`, '指定用户银行卡预留手机号'),
                                    fieldName: 'bankMobile',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDWEmUQlty_7742a9aa`, '指定用户银行卡预留手机号')
                                }
                            ],
                            validateRules: {
                                subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LieyxOLV_542509fe`, '认证账号不能为空')}],
                                applyUserName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIjDLiRLxOLV_8ba775b2`, '指定用户认证名称不能为空')}],
                                applyContactType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAcxOLV_394ba69d`, '联系方式类型不能为空')}],
                                applyContact: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKxOLV_4de576d`, '联系方式不能为空')}]
                            }
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), type: 'primary', click: this.submitCertificationEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/contractLock/purchaseClPersonalInfo/add',
                auth: '/contractLock/purchaseClPersonalInfo/submitCertification'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
        },
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.add
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        this.goBack()
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        submitCertificationEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.auth
                    postAction(url, params).then(res => {
                        this.goBack()
                        window.open(res.result.certificationPageUrl)
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>