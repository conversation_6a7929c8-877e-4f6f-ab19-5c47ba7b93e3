<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showNewRoundPage"
      ref="listPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <purchase-bidding-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPageNew"/>
    <!-- 详情界面 -->
    <purchase-bidding-detail
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      :allowEdit="allowViewToEdit"
      @toEdit="handleEditFromViewPage"
      @hide="hideEditPage" />
    <!-- 轮次界面 -->
    <!-- <Purchase-Ebidding-New-Round 
      v-if="showNewRoundPage" 
      ref="newRoundPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideNewRoundPage" /> -->
    <!-- 变更弹窗 -->
    <a-modal
      v-drag    
      v-model="regretVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_tip`, '提示')"
      @ok="handleRegret">
      <div>
        <a-radio-group
          v-model="regretValue"> 
          <a-radio
            class="radio-wrap"
            :value="0">{{ $srmI18n(`${$getLangAccount()}#i18n_title_noMoreFulfillment`, '悔标，不再寻源') }}</a-radio> 
          <a-radio
            class="radio-wrap"
            :value="1">{{ $srmI18n(`${$getLangAccount()}#i18n_title_replaceSourcing`, '悔标，重新寻源') }}</a-radio> 
          <a-radio
            class="radio-wrap"
            :value="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_priceComparison`, '重新比价/定标') }}</a-radio>
        </a-radio-group>
      </div>
    </a-modal>
  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import PurchaseBiddingEdit from './modules/PurchaseBiddingEdit'
import layIM from '@/utils/im/layIM.js'
import PurchaseBiddingDetail from './modules/PurchaseBiddingDetail'
import {handleDemandPool} from '@/utils/util'
//import PurchaseEbiddingNewRound from './modules/PurchaseEbiddingNewRound'
import {getAction, postAction} from '@/api/manage'

import { SET_CACHE_VUEX_CURRENT_EDIT_ROW, SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapMutations } from 'vuex'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseBiddingEdit,
        PurchaseBiddingDetail
        //PurchaseEbiddingNewRound
    },
    data () {
        return {
            regretValue: 0,
            regretVisible: false,
            currentRow: {},
            showNewRoundPage: false,
            pageData: {
                businessType: 'bidding',
                form: {
                    ebiddingDesc: '',
                    ebiddingNumber: '',
                    ebiddingStatus: undefined
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'bidding#purchaseBiddingHead:add'},
                    //{label: '报价历史', icon: 'snippets', clickFn: this.showHistoryList},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tenderNumber`, '招标单号'),
                        fieldName: 'biddingNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTenderNumberTips`, '请输入招标单号')
                    }
                    // {
                    //     type: 'select',
                    //     label: '单据状态',
                    //     fieldName: 'ebiddingStatus',
                    //     dictCode: 'srmEbiddingStatus',
                    //     placeholder: '请选择单据状态'
                    // }
                ],
                optColumnWidth: 320,
                optColumnList: [
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat, authorityCode: 'bidding#purchaseBiddingHead:creatGruopChat'},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'bidding#purchaseBiddingHead:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'bidding#purchaseBiddingHead:edit'},
                    // {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_newRound`, '新轮次'), clickFn: this.createNew, allow: this.allowCreateNew, authorityCode: 'bidding#purchaseBiddingHead:newRound'},
                    {type: 'abandon', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uB_bdd48`, '废标'), clickFn: this.handleAbandon, allow: this.allowAbandon, authorityCode: 'bidding#purchaseBiddingHead:abandon'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowEdit, authorityCode: 'bidding#purchaseBiddingHead:delete'},
                    {type: 'regret', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_change`, '变更'), clickFn: this.showRegret, allow: this.allowRegret, authorityCode: 'bidding#purchaseBiddingHead:regret'},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_JdYBfY_2d6065db`, '自主招标大厅'), clickFn: this.toTender, allow: this.allowTender, authorityCode: 'bidding#purchaseBiddingHead:customerTender' },
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord},
                    {type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopy, authorityCode: 'bidding#purchaseBiddingHead:copyData'}
                ]
            },
            tabsList: [],
            url: {
                add: '/bidding/purchaseBiddingHead/add',
                list: '/bidding/purchaseBiddingHead/list',
                delete: '/bidding/purchaseBiddingHead/delete',
                regret: '/bidding/purchaseBiddingHead/regret',
                columns: 'PurchaseBiddingHead',
                excelCode: 'bidding',
                copyData: '/bidding/purchaseBiddingHead/copyData'
            },
            countTabsUrl: '/bidding/purchaseBiddingHead/counts'
        }
    },
    computed: {
        allowViewToEdit() {
            if(!!this.viewRow) return !this.allowEdit(this.viewRow);
            return false;
        }
    },
    mounted () {
        // this.serachTabs('srmBiddingStatus', 'biddingStatus')
        this.serachCountTabs(this.countTabsUrl)
    },
    created () {
        this.getUrlParam()
        if (this.$route.query.id) {
            this.pageData.form.id = this.$route.query.id
            this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
        } 
        handleDemandPool(this)
    },
    // activated (){
    //     this.init()
    // },
    beforeRouteEnter (to, from, next) {
        if (Object.keys(to.query).length) {
            next(vm => vm.init())
        } else {
            next()
        }
    },
    methods: {
        // 缓存当前行数据
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW,
            setCacheVuexCurrentEditRow: SET_CACHE_VUEX_CURRENT_EDIT_ROW
        }),
        handleAbandon (row){
            let param = {id: row.id}
            getAction('bidding/purchaseBiddingHead/abandoned', param).then(res=>{
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.$refs.listPage.loadData()
                }
            })
        },
        // 返回按钮
        hideEditPageNew () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
            if (query.source == 'demand-pool') {
                this.$router.replace({path: this.$route.path})
            }
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        init () {
            if (this.$route.query.id && this.$route.path.includes('PurchaseBiddingHeadList')) {
                this.pageData.form.id = this.$route.query.id
                this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
                this.$refs.listPage.initColumns()
                this.$refs.listPage.loadData()
                this.$refs.listPage.columnDrop()
            } else {
                this.pageData.form.id = ''
                this.countTabsUrl = '/bidding/purchaseBiddingHead/counts'
            }
            handleDemandPool(this)
        },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.templateNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'PurchaseBidding', url: this.url || '', recordNumber})
        }, 
        allowChat (row){
            return row.biddingStatus === '0'
        },
        getUrlParam (){
            let templateNumber = this.$route.query.templateNumber
            let templateVersion = this.$route.query.templateVersion
            let busAccount = this.$route.query.busAccount
            let id = this.$route.query.id
            if(templateNumber && templateVersion && id && busAccount){
                let row = {}
                row['templateNumber']=templateNumber
                row['templateVersion']=templateVersion
                row['id']=id
                row['busAccount']=busAccount
                this.currentEditRow = row
                this.showDetailPage = true
            }
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (row){
            this.currentEditRow = row
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        toTender (row) {
            this.setVuexCurrentEditRow({})
            this.setCacheVuexCurrentEditRow({ row })
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/hall',
                query: {
                    _t
                }
            })
            window.open(routeUrl.href, '_blank')
        },
        allowTender (row) {
            return row.biddingStatus == '0'
        },
        allowEdit (row) {
            //审批中禁用
            if(row.auditStatus == '1'){
                return true
            }
            //新建不禁用
            return row.biddingStatus != '0'
        },
        allowAbandon (row) {
            return (row.biddingStatus == 5|| row.biddingStatus == 6|| row.biddingStatus == 7)
        },
        allowCreateNew (row){
            //竞价结束状态5
            return row.biddingStatus != '5'
        },
        allowRegret (row){
            let reusltAudit = row.reusltAudit
            if(reusltAudit == '1'){
                //已授标状态6 && 审批通过2
                return row.biddingStatus != '6' && row.reusltAuditStatus != '2'
            }else {
                //已授标状态6
                return row.biddingStatus != '6'
            }
        },
        createNew (row){
            this.currentEditRow = row
            // this.showNewRoundPage = true
        },
        hideNewRoundPage (){
            this.showNewRoundPage = false
        },
        showHistoryList (){
            this.$store.dispatch('SetTabConfirm', false)
            this.$router.push({
                path: '/ebidding/purchaseEbiddingHisList'
            })
        },
        showRegret (row){
            // this.regretVisible = true
            // this.currentRow = row
        },
        handleRegret (){
            let headId = this.currentRow.id
            postAction(this.url.regret, {id: headId, regretFlag: this.regretValue}).then(res => {
                if(res.success) {
                    this.regretVisible = false
                    this.$message.success(res.message)
                    this.$refs.listPage.loadData()
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        handleCopy (row){
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(row)
                }
            })
        },
        copyData (row){
            let headId = row.id
            this.$refs.listPage.loading = true
            postAction(this.url.copyData, {id: headId}).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    this.$refs.listPage.loadData()
                }else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.loading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
.radio-wrap {
    display: block;
    height: 30px;
}
</style>
