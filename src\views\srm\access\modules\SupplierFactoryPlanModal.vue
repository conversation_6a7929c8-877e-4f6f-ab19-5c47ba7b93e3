<template>
  <div class="els-page-container">
    <a-spin :spinning="confirmLoading">
      <a-page-header
        :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
        :title="pageData.title"
      >
        <template slot="extra">
          <a-button
            v-for="(item, key) in pageData.publicBtn"
            :type="item.type || 'default'"
            @click="item.clickFn"
            :key="'public_btn_' + key">{{ item.title }}</a-button>
        </template>
      </a-page-header>
      <div
        class="table-page-search-wrapper"
        style="padding:8px">
        <a-form-model
          ref="headerForm"
          :model="formField"
          :rules="pageData.validateRules"
          class="ant-advanced-search-form"
          style="padding-top:0"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          layout="inline">
          <a-row :gutter="24">
            <a-col 
              v-for="(item, i) in pageData.form"
              :key="'field_'+i"
              :span="pageData.colSpan || 8">
              <a-form-model-item
                :prop="item.fieldName"
                v-if="item.type==='input'"
                :label="item.label">
                <a-input
                  :disabled="item.disabled"
                  v-model="formField[item.fieldName]"
                  :placeholder="item.placeholder" />
              </a-form-model-item>
              <a-form-model-item
                :prop="item.fieldName"
                v-if="item.type==='number'"
                :label="item.label">
                <a-input-number
                  style="width:100%"
                  :disabled="item.disabled"
                  v-model="formField[item.fieldName]"
                  :placeholder="item.placeholder" />
              </a-form-model-item>
              <a-form-model-item
                :prop="item.fieldName"
                v-if="item.type==='selectModal'"
                :label="item.label">
                <a-input
                  readOnly
                  :disabled="item.disabled"
                  v-model="formField[item.fieldName]"
                  :placeholder="item.placeholder"
                  @click="e => openSelectModal(e, item)">
                  <a-icon
                    slot="suffix"
                    type="close-circle"
                    @click="() => clearInputValue(item)"></a-icon>
                </a-input>
              </a-form-model-item>
              <a-form-model-item
                v-if="item.type==='numberGroup'"
                :label="item.label">
                <a-input-number
                  v-for="(field,fieldIndex) in item.extendFields.fieldList"
                  :style="{width: (1/item.extendFields.fieldLIst.length*100) + '%'}"
                  :key="'number_group_'+fieldIndex"
                  :disabled="item.disabled"
                  v-model="formField[field]" />
              </a-form-model-item>
              <a-form-model-item
                :prop="item.fieldName"
                v-if="item.type==='select'"
                :label="item.label">
                <m-select
                  :mode="item.mode"
                  :disabled="item.disabled"
                  v-model="formField[item.fieldName]"
                  :placeholder="item.placeholder"
                  :show-opt-value="item.extendFields.showOptValue"
                  :options="item.extendFields.options"
                  :source-url="item.extendFields.sourceUrl"
                  :source-map="item.extendFields.sourceMap"
                  :dict-code="item.extendFields.dictCode"
                  :noEmptyOpt="item.extendFields.noEmptyOpt"
                  @change="item.changeEvent" />
              </a-form-model-item>
              <a-form-model-item
                :prop="item.fieldName"
                :label="item.label"
                v-else-if="item.type==='dateRange'">
                <a-range-picker
                  :show-time="item.extendFields.showTime"
                  @change="(dates) => changeDateRangePicker(dates, item)"
                  :disabled="item.disabled || false"
                  :value="formField[item.fieldName]"
                  :valueFormat="item.extendFields.valueFormat || 'YYYY-MM-DD'" />
              </a-form-model-item>
              <a-form-model-item
                :prop="item.fieldName"
                :label="item.label"
                v-else-if="item.type==='datepicker'">
                <a-date-picker
                  :disabled="item.disabled || false"
                  :show-time="item.extendFields.showTime"
                  :valueFormat="item.extendFields.valueFormat || 'YYYY-MM-DD'"
                  v-model="formField[item.fieldName]" />
              </a-form-model-item>
              <a-form-model-item
                :prop="item.fieldName"
                :label="item.label"
                v-else-if="item.type==='addressCascader'">
                <a-cascader
                  :options="areas"
                  :placeholder="item.placeholder"
                  :value="formField[item.fieldName]"
                  @change="(val) => onChangeArea(val, item)"
                />
              </a-form-model-item>
              <a-form-model-item
                v-else-if="item.type==='switch'"
                :label="item.label"
                :prop="item.fieldName"
              >
                <m-switch
                  :disabled="item.disabled"
                  :close-value="item.extendFields.closeValue"
                  :open-value="item.extendFields.openValue"
                  :checked-children="item.extendFields.checkedChildren"
                  :un-checked-children="item.extendFields.unCheckedChildren"
                  v-model="formField[item.fieldName]"
                />
              </a-form-model-item>
              <a-form-model-item
                class="textarea-form-item"
                :prop="item.fieldName"
                :label="item.label"
                v-else-if="item.type==='textarea'">
                <a-textarea 
                  :disabled="item.disabled || false"
                  v-model="formField[item.fieldName]"
                  :placeholder="item.placeholder" />
              </a-form-model-item>
              <a-form-model-item
                :prop="item.fieldName"
                :label="item.label"
                v-else-if="item.type==='timePicker'">
                <a-time-picker
                  style="width:100%"
                  valueFormat="HH:mm:ss"
                  :disabled="item.disabled || false"
                  v-model="formField[item.fieldName]" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <a-tabs
          @change="changeTabs"
          type="card">
          <a-tab-pane
            key="tab_1"
            forceRender
            :tab="$srmI18n(`${$getLangAccount()}#i18n_title_auditArrangement`, '审核安排')">
            <j-editor
              id="myEditor"
              ref="editor"
              v-if="showEditor"
              v-model="formField.fbk1"></j-editor>
          </a-tab-pane>
          <a-tab-pane
            v-if="tableColumns.length"
            key="tab_2"
            forceRender
            :tab="$srmI18n(`${$getLangAccount()}#i18n_title_rowIitem`, '行项目')">
            <vxe-grid
              border
              ref="templateGrid"
              show-overflow
              highlight-hover-row
              :columns="tableColumns"
              :edit-rules="tableRules"
              :toolbar="{slots: {buttons: 'toolbar_buttons'}}"
              height="420"
              size="small"
              align="center"
              :edit-config="{trigger: 'click', mode: 'cell'}">
              <template slot="empty">
                <a-empty />
              </template>
              <template v-slot:cell_edit_modal="{ row, column }">
                <a
                  @click="openCellEditModal(row, column)"
                  v-html="row[column.property] || $srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')"></a>
              </template>
              <template v-slot:toolbar_buttons>
                <div style="margin-top:-16px;text-align:right">
                  <a-button
                    v-for="(btn, key) in toolsBarBtn"
                    :key="'btn_'+key"
                    style="margin-left:8px"
                    v-show="btn.showCondition ? btn.showCondition() : true"
                    @click="btn.clickFn"
                    :type="btn.type || ''">
                    {{ btn.title }}
                  </a-button>
                </div>
              </template>
            </vxe-grid>
          </a-tab-pane>
          <a-tab-pane
            v-if="pageData.showUploadList"
            key="tab_3"
            forceRender
            :tab="$srmI18n(`${$getLangAccount()}#i18n_title_attachment`, '附件')">>
            <upload-list ref="uploadList"></upload-list>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-spin>
    <field-select-modal
      ref="fieldSelectModal">
    </field-select-modal>
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"></remote-js> 
  </div>
</template>
<script>
import { Empty } from 'ant-design-vue'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import uploadList from '@comp/uploadList/uploadList'
import JEditor from '@comp/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import { getAction, postAction } from '@api/manage'
export default {
    name: 'MasterSlaveTemplate',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        AEmpty: Empty,
        uploadList,
        flowViewModal,
        fieldSelectModal,
        JEditor,
        remoteJs: {
            render (createElement) {
                var self = this
                return createElement('script', {
                    attrs: { type: 'text/javascript', src: this.src },
                    on: {
                        load: function (event) {
                            self.$emit('load', event)
                        },
                        error: function (event) {
                            self.$emit('error', event)
                        }
                    }
                })
            },
            props: {
                src: { type: String, required: true }
            }
        }
    },
    
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_masterTemplate`, '主从模板'),
            confirmLoading: false,
            fixPageHeader: false,
            pageData: {},
            showEditor: false,
            formField: {
                auditStatus: '0',
                formStatus: '0',
                formType: this.$route.name.split('-')[0],
                fbk1: '<table><tbody><tr style="background:#F7F7F7;height:32px;"><th rowspan="1" colspan="6" style="word-break: break-all;"><strong>供应商验厂计划</strong><br/></th></tr><tr><td valign="top" rowspan="1" colspan="4" style="word-break: break-all;"><span style="font-size: 14px;">审核对象：<br/></span></td><td valign="top" rowspan="1" colspan="2" style="word-break: break-all;"><span style="font-size: 14px;">产品类型：<br/></span></td></tr><tr><td valign="top" rowspan="1" colspan="4" style="word-break: break-all;"><span style="font-size: 14px;">审核类型：</span></td><td valign="top" rowspan="1" colspan="2" style="word-break: break-all;"><span style="font-size: 14px;">审核日期：<br/></span></td></tr><tr><td valign="top" rowspan="1" colspan="4" style="word-break: break-all;"><span style="font-size: 14px;">审核人员：<br/></span></td><td valign="top" rowspan="1" colspan="2" style="word-break: break-all;"><span style="font-size: 14px;">审核团队联系方式：</span></td></tr><tr><td valign="top" rowspan="1" colspan="6" align="center" style="word-break: break-all;"><span style="font-size: 14px;"><strong>审核安排</strong><br/></span></td></tr><tr><td width="195" valign="top" align="center" style="word-break: break-all;"><span style="font-size: 14px;">日期</span></td><td width="195" valign="top" align="center" style="word-break: break-all;"><span style="font-size: 14px;">时间</span></td><td width="195" valign="top" align="center" style="word-break: break-all;"><span style="font-size: 14px;">项目安排</span></td><td width="195" valign="top" align="center" style="word-break: break-all;"><span style="font-size: 14px;">地点</span></td><td width="195" valign="top" align="center" style="word-break: break-all;"><span style="font-size: 14px;">需要配合的事项</span></td><td valign="top" colspan="1" rowspan="1" width="195" align="center" style="word-break: break-all;"><span style="font-size: 14px;">备注</span></td></tr><tr><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td></tr><tr><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1" style="word-break: break-all;"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td></tr><tr><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1" style="word-break: break-all;"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td></tr><tr><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td><td valign="top" align="center" colspan="1" rowspan="1"><br/></td></tr></tbody></table>'
            },
            labelCol: {
                xs: { span: 24 },
                sm: { span: 6 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 18 }
            },
            url: {
                add: '/config/purBussFormHead/add',
                edit: '/config/purBussFormHead/edit',
                detail: '/config/purBussFormHead/queryById',
                submitAudit: '/a1bpmn/audit/api/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            },
            currentBasePath: this.$variateConfig['domainURL'],
            flowId: 0,
            flowView: false,
            currentSelectModal: {},
            voucherId: ''
        }
    },
    computed: {
        fileSrc () {
            return `${this.$variateConfig['domainURL']}/static/${this.$route.name}.js?_t=${new Date().getTime()}`
        },
        tableColumns () {
            if(this.pageData.grid) {
                return this.pageData.grid.columns
            }else {
                return []
            }
        },
        toolsBarBtn () {
            if(this.pageData.grid) {
                return this.pageData.grid.buttons
            }else {
                return []
            }
        },
        tableRules () {
            if(this.pageData.grid) {
                return this.pageData.grid.validRules
            }else {
                return null
            }
        }
    },
    methods: {
        init () {
            if(this.currentEditRow) {
                this.queryDetail(this.currentEditRow.id)
            }
        },
        loadSuccess () {
            this.pageData = getPageConfig(this) // eslint-disable-line
            this.init()
            this.showEditor = true
        },
        loadError (e) {
            console.log(e)
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_configurationException`, '配置加载异常'))
        },
        addRows () {
            this.$refs.templateGrid.insert([{}])
        },
        // 删除选中行
        deleteRows () {
            let grid = this.$refs.templateGrid
            let selectedData = grid.getCheckboxRecords()
            if(!selectedData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDeleteRowTips`, '请选择删除行'))
            }
            grid.removeCheckboxRow()
        },
        goBack () {
            this.$parent.hideEditPage()
        },
        queryDetail (id) {
            this.voucherId = id
            getAction(this.url.detail, {id: id}).then(res => {
                if(res.success) {
                    this.formField = res.result
                    if(this.pageData.showUploadList) {
                        this.$refs.uploadList.getFileList(id, this.$route.name)
                    }
                    if(res.result.extendFields) {
                        Object.assign(this.formField, JSON.parse(res.result.extendFields))
                    }
                    let list = res.result.configDetailList
                    if(list && list.length) {
                        list.forEach(item => {
                            if(item.extendFields) {
                                Object.assign(item, JSON.parse(item.extendFields))
                            }
                        })
                        this.$refs.templateGrid.loadData(list)
                    }
                }
            })
        },
        saveEvent () {
            let that = this
            that.$refs.headerForm.validate(valid => {
                if(valid) {
                    if(that.$refs.templateGrid) {
                        that.$refs.templateGrid.validate(error => {
                            if(!error) {
                                that.postData()
                            }
                        })
                    }else {
                        that.postData()
                    }
                }
            })
        },
        postData () {
            let that = this
            let params = {...this.formField}
            params.extendFields = ''
            params.formType = this.$route.name.split('-')[0]
            if(this.$refs.templateGrid) {
                let tableData = this.$refs.templateGrid.getTableData().fullData
                tableData.forEach(item => {
                    let extendFields = {}
                    that.tableColumns.forEach(col => {
                        if(col.ext == '1') {
                            extendFields[col.field] = item[col.field]
                        }
                    })
                    item.extendFields = JSON.stringify(extendFields)
                })
                params.configDetailList = tableData
            }
            let extendFields = {}
            this.pageData.form.forEach(item => {
                if(item.ext == '1') {
                    extendFields[item.fieldName] = this.formField[item.fieldName]
                }
            })           
            params.extendFields = JSON.stringify(extendFields)
            let url = this.url.add
            if(this.currentEditRow && this.currentEditRow.id) {
                url = this.url.edit
            }
            this.confirmLoading = true
            postAction(url, params).then(res => {
                this.confirmLoading = false
                if(res.success) {
                    this.$parent.modalFormOk()
                    if(!this.voucherId) {
                        this.queryDetail(res.result.id)
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        onChangeArea (val, item) {
            this.formField[item.extendFields.state] = val[0]
            this.formField[item.extendFields.city] = val[1]
            this.formField[item.extendFields.area] = val[2]
            this.formField[item.fieldName] = [val[0], val[1], val[2]]
            this.$forceUpdate()
        },
        changeDateRangePicker (dates, item) {
            this.formField[item.extendFields.start] = dates[0]
            this.formField[item.extendFields.end] = dates[1]
            this.formField[item.fieldName] = [dates[0], dates[1]]
            this.$forceUpdate()
        },
        //打开选择弹窗
        openSelectModal (e, item) {
            this.currentSelectModal = item
            let params = item.extendFields.params
            for(let key in params) {
                if(typeof(params[key]) == 'string' && params[key].match(/\${(.+?)}/)) {
                    let fieldName = params[key].match(/\${(.+?)}/)[1]
                    params[key] = this.formField[fieldName]
                }
            }
            if(item.extendFields.sourceUrl) {
                this.$refs.fieldSelectModal.open(item.extendFields.sourceUrl, params, item.extendFields.columns, item.extendFields.selectModel)
            }
        },
        fieldSelectOk (data) {
            this.currentSelectModal.bindFunction(data)
        },
        //清除弹窗选择输入框
        clearInputValue (item) {
            this.formField[item.fieldName] = ''
            this.$forceUpdate()
        },
        changeTabs (key) {
            if(key != 'tab_1') {
                this.showEditor = false
            }else {
                this.showEditor = true
            }
        },
        showFlow (){
            this.flowId = this.formField.flowCode
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        submitAudit (){
            if(!this.formField.id){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saveDocumentBeforeTips`, '请先保存单据在提交审批！'))
                return false
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApproval`, '确认提交审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApprovalTips`, '是否确认提交审批?'),
                onOk: function () {
                    that.postAuditData(that.url.submitAudit)
                }
            })
        },
        cancelAudit (){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit)
                }
            })
        },
        postAuditData (invokeUrl){
            this.confirmLoading = true
            let param = {}
            param['businessId'] = this.formField.id
            param['rootProcessInstanceId'] = this.formField.flowCode
            param['businessType'] = this.formField.formType
            param['auditSubject'] = this.pageData.bussTypeName+this.$srmI18n(`${this.$getLangAccount()}#i18n_title_oddNum`, '单号')+'：'+this.formField.formNumber
            param['params'] = JSON.stringify(this.formField)
            postAction(invokeUrl, param).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$emit('ok')
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }

    }
}
</script>
<style lang="less">
  .vxe-toolbar.size--small {
        height: 32px
  }
</style>