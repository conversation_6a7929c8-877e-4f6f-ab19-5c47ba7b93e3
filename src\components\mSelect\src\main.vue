<template>
  <div>
    <a-select
      style="width:100%"     
      show-search
      class="mSelstInputStyle"
      option-filter-prop="children"
      :filter-option="filterOption"
      :mode="mode"
      :placeholder="placeholder"
      :value="realValue"
      :disabled="disabled"
      :allowClear="$attrs.configData?.extend?.afterClearCallBack?false:realValue?true:false"
      v-bind="$attrs"
      @select="selectEvent"
      @change="changeEvent">
      <a-icon
        v-if="$attrs.configData?.extend?.afterClearCallBack?true:false"
        slot="suffixIcon"
        :style="{fontSize: '14px',color:'rgba(0, 0, 0, 0.65)' }"
        type="close-circle"
        @click="clearInputValue"
      ></a-icon>
      <template
        v-if="showMaxTagPlaceholder"
        slot="maxTagPlaceholder">
        <span class="maxTagPlaceholder">+{{ length }}</span>
      </template>
      <a-select-option
        v-if="!noEmptyOpt"
        value="">
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择') }}
      </a-select-option>
      <a-select-option
        v-for="(option, key) in realOptions"
        :key="'option_' + key"
        :disabled="disabledValueList.includes(option[valueMap])"
        :title="$srmI18n(`${$getLangAccount()}#${option.textI18nKey}`, option[titleMap] )"
        :value="option[valueMap]">
        {{ showOptValue ? option[valueMap]+'_'+$srmI18n(`${$getLangAccount()}#${option.textI18nKey}`, option[titleMap] ) : $srmI18n(`${$getLangAccount()}#${option.textI18nKey}`, option[titleMap] ) }}
      </a-select-option>
    </a-select>
  </div>
</template>
<script>
import { getAction } from '@api/manage'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import {ajaxFindDictItems} from '@/api/api'
export default {
    name: 'MSelect',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        disabled: {
            type: Boolean,
            default: false
        },
        mode: {
            type: String,
            default: 'default'
        },
        placeholder: {
            type: String,
            default: ''
        },
        value: {
            type: String || Number,
            default: null
        },
        options: {
            type: Array,
            default: () => []
        },
        sourceUrl: {
            type: String,
            defalt: ''
        },
        sourceMap: {
            type: Object,
            default: null
        },
        dictCode: String,
        showOptValue: {
            type: Boolean,
            default: false
        },
        noEmptyOpt: {
            type: Boolean,
            default: true
        },
        titleMap: {
            type: String,
            default: 'title'
        },
        valueMap: {
            type: String,
            default: 'value'
        },
        currentEditRow: {
            type: Object,
            default: ()=> {
                return null
            }
        },
        // change方法返回选项的title
        returnTitle: {
            type: Boolean,
            default: false
        },
        // 禁用选项list
        disabledValueList: {
            type: Array,
            default: ()=> {
                return []
            }
        },
        // 过滤不需要的选项
        filterSelectList: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    data () {
        return {
            realOptions: this.options
        }
    },
    computed: {
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow && this.currentEditRow.busAccount) {
                account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount
            }
            return account
        },
        realValue () {
            if(this.mode === 'multiple' || this.mode === 'tags') {
                return this.value ? this.value.split(',') : []
            }
            return this.value
        },
        showMaxTagPlaceholder () {
            let { maxTagCount } = this.$attrs || {}
            if (!maxTagCount) return false
            let min = maxTagCount + 1
            return ['multiple', 'tags'].includes(this.mode)
                && Array.isArray(this.realValue)
                && this.realValue.length >= min
        },
        length () {
            if (!this.showMaxTagPlaceholder) return ''
            let { maxTagCount } = this.$attrs || {}
            maxTagCount = maxTagCount || 1
            return this.realValue.length - maxTagCount
        }
    },
    watch: {
        options (newVal) {
            if(newVal && newVal.length>0){
                this.realOptions = newVal
            }
        },
        sourceUrl: {
            immediate: true,
            handler () {
                this.querySourceData()
            }
        },
        dictCode: {
            immediate: true,
            handler () {
                this.initDictData()
            }
        }
    },
    methods: {
        filterOption (input, option) {
            return (
                option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
            )
        },
        changeEvent (val, opt) {
            let realValue = '',
                oldVal = this.realValue, Rtitle = ''
            if(this.mode === 'multiple' || this.mode === 'tags') {
                realValue = val.join(',')
            }else {
                realValue = val || ''
            }
            if (this.returnTitle) {
                this.realOptions.map(({value, title}) => {
                    if (value == realValue) Rtitle = title
                })
            }
            // 当前选中对象
            let filterDataArr = this.realOptions.filter(({value}) => {
                return value == realValue
            })
            let selectedData = (filterDataArr && filterDataArr.length)?filterDataArr[0]: {}
            // 当前form的fields
            let fields = this.$attrs.fieldList? this.$attrs.fieldList: []
            // 当前关联的对象{fieldName:fieldName}
            let relationData = this.$attrs.relationData? this.$attrs.relationData: {}
            // 当前formModel
            let formModel = this.$attrs.formModel? this.$attrs.formModel: {}
            // configData 当前form表单数据，相当于主模板的bindFunction 挂着引用主件上 如：<m-select ...  :configData='panel.content.list'></m-select>
            this.$emit('change', realValue, opt, oldVal, this.$attrs.configData, Rtitle)
            this.$emit('relation', {realValue, selectedData, fields, relationData, formModel})
        },
        selectEvent (val, opt){
            let extend = (this.$attrs.configData && this.$attrs.configData.extend) || {}
            let afterSelectCallBack = extend.afterSelectCallBack || ((f) => f)
            if(extend.afterSelectCallBack){
                this.$emit('afterSelectCallBack', afterSelectCallBack)
            }
        },
        loadSuccess () {
            this.$emit('load', this.realOptions)
        },
        querySourceData () {
            if(this.sourceUrl) {
                getAction(this.sourceUrl, this.sourceMap).then(res => {
                    if(res.success) {
                        this.realOptions = res.result
                        this.loadSuccess()
                    }
                })
            }
        },
        initDictData () {
            if(this.dictCode) {
                //根据字典Code, 初始化字典数组
                let postData = {
                    busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: this.dictCode
                }             
                ajaxFindDictItems(postData).then((res) => {
                    if (res.success) {
                        if (this.filterSelectList.length > 0) { // 是否需要过滤操作
                            const filterOption = res.result.filter(option => {
                                return !this.filterSelectList.includes(option.value)&&!this.filterSelectList.includes(option.title)
                            })
                            this.realOptions = filterOption
                        } else {
                            this.realOptions = res.result
                        }
                        this.loadSuccess()
                    }
                })
            }
        },
        clearInputValue (event) {
            event.stopPropagation()
            let extend = (this.$attrs.configData && this.$attrs.configData.extend) || {}
            let afterClearCallBack = extend.afterClearCallBack || ((f) => f)
            this.$emit('afterClearCallBack', afterClearCallBack)
        }
    }
}
</script>
<style scoped lang="less" scoped>
:deep(.mSelstInputStyle .ant-select-selection .ant-select-selection__rendered .ant-select-search--inline .ant-select-search__field__wrap input){
    border: none;
    background-color: rgba(0,0,0,0);
}
</style>
