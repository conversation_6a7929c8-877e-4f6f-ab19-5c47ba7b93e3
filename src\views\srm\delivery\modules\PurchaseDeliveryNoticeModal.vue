<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      refresh
      :page-data="pageData"
      :currentEditRow="currentEditRow"
      :url="url" />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <field-select-modal
      ref="fieldSelectModal" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {postAction, getAction} from '@/api/manage'
import {ajaxFindDictItems} from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
    name: 'PurchaseDeliveryNoticeModal',
    components: {
        fieldSelectModal
    },
    mixins: [EditMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            showRemote: false,
            factory: {},
            storageLocation: {},
            pageData: {
                selectedType: '',
                form: {
                },
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eRcVH_2730e70f`, '通知行信息'), groupType: 'item', groupCode: 'purchaseDeliveryNoticeList', type: 'grid', custom: {
                        ref: 'purchaseDeliveryNoticeList',
                        columns: [],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.insertGridItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteGridItem},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_queryMaxDemand`, '查询最大需求量'), type: 'primary', click: this.queryMaxRequireQuantity, authorityCode: 'order#purchaseDeliveryNotice:queryMaxRequireQuantity' },
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem}
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [

                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent, authorityCode: 'order#purchaseDeliveryNotice:edit'  },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publishEvent, showCondition: this.showPublishConditionBtn, authorityCode: 'order#purchaseDeliveryNotice:publish'},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/delivery/purchaseDeliveryNoticeHead/add',
                edit: '/delivery/purchaseDeliveryNoticeHead/edit',
                detail: '/delivery/purchaseDeliveryNoticeHead/queryById',
                public: '/delivery/purchaseDeliveryNoticeHead/publish',
                queryMaxRequireQuantity: '/delivery/purchaseDeliveryNotice/queryMaxRequireQuantity'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_deliveryNotice_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    created () {
        this.getDictByCode('purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"', 'factory')
        this.getDictByCode('purchase_organization_info#org_name#org_code#org_category_code="location" && status="1"', 'storageLocation')
    },
    mounted () {
        this.initInfo()
    },
    methods: {
        init () {
            // queryDetail方法已经处理了id,可以直接调用
            let that = this
            if (this.currentEditRow) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id, (data) => {
                    if (data?.purchaseDeliveryNoticeList?.length) { // 行信息有值就置灰表头
                        that.handleHeaderFields({pageData: that.pageData, flag: true})
                    }
                })
            }
        },
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        initInfo () {
            if (this.currentEditRow && this.currentEditRow.id) {
                getAction(this.url.detail, {id: this.currentEditRow.id}).then((res) => {
                    if (res && res.success) {
                        let result = res.result || {}
                        // 更新父级行数据缓存 + 所有数据传参
                        this.$parent.currentEditRow = result
                        this.showRemote = true
                    }
                })
            } else {
                this.showRemote = true
            }
        },
        showPublishConditionBtn (){
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if((params.id)) {
                return true
            }else{
                return false
            }
        },
        //新增行
        insertGridItem () {
            let pageData = this.$refs.editPage.getPageData() || {}
            if (!pageData.toElsAccount) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierInfoTips`, '请选择供应商信息！'))
                return
            }
            //公司内部单据需要选择供应商公司编码
            if(pageData.elsAccount===pageData.toElsAccount){
                if(!pageData.toCompany){
                    this.$message.warning('公司内部单据，请选择供应商公司代码!')
                    return
                }
            }
            const param = {
                toElsAccount: pageData.toElsAccount
            }
            if (pageData.factory) {
                param['factory'] = pageData.factory
            }
            if (pageData.storageLocation) {
                param['storageLocation'] = pageData.storageLocation
            }
            if (pageData.toCompany) {
                param['toCompany'] = pageData.toCompany
            }
            let url = '/order/purchaseOrderItem/queryOrderMaterialList'
            let columns = [
                {
                    field: 'materialNumber',
                    title: '物料编码',
                    fieldLabelI18nKey: 'i18n_field_materialNumber',
                    width: 150
                },
                {
                    field: 'materialDesc',
                    title: '物料描述',
                    fieldLabelI18nKey: 'i18n_field_materialDesc',
                    width: 150
                },
                {
                    field: 'materialName',
                    title: '物料名称',
                    fieldLabelI18nKey: 'i18n_field_materialName',
                    width: 150
                },
                {
                    field: 'materialSpec',
                    title: '物料规格',
                    fieldLabelI18nKey: 'i18n_field_materialSpec',
                    width: 150
                },
                {
                    field: 'purchaseUnit_dictText',
                    title: '采购单位',
                    fieldLabelI18nKey: 'i18n_field_purchaseUnit',
                    width: 150
                },
                {
                    field: 'factory_dictText',
                    title: '工厂',
                    fieldLabelI18nKey: 'i18n_massProdHead4e3_factoryCode',
                    width: 150
                },
                {
                    field: 'storageLocation_dictText',
                    title: '库存地点',
                    fieldLabelI18nKey: 'i18n_field_storageLocation',
                    width: 150
                },
                {
                    field: 'jit_dictText',
                    title: '送货安排',
                    fieldLabelI18nKey: 'i18n_field_dSpA_43933cef',
                    width: 150
                }
            ]
            this.$refs.fieldSelectModal.open(url, param, columns, 'multiple')
        },
        //删除复选框选定行
        deleteGridItem () {
            let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryNoticeList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据!'))
                return
            }
            //已锁定的行，不能删除
            for (let i = 0; i < checkboxRecords.length; i++) {
                if (checkboxRecords[i].locked === '1') {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isEIdIxOQG_7c7195a4`, '选中行已锁定，不能删除!'))
                    return
                }
            }
            //移除状态为新建的单
            itemGrid.removeCheckboxRow()
            let {fullData} = itemGrid.getTableData()
            if (fullData?.length == 0) {
                // this.handleHeadform(false)
                this.handleHeaderFields({pageData: this.pageData, flag: false})
            }
        },
        //?
        fieldSelectOk (data) {
            let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryNoticeList[0]
            let {fullData} = itemGrid.getTableData()
            let materialList = fullData.map(item => {
                return item.materialNumber
            })

            //过滤已有数据
            let insertData = data.filter(item => {
                // 过滤时间
                item.createTime = null
                item.createBy = null
                item.updateTime = null
                item.updateBy = null
                return !materialList.includes(item.materialNumber)
            })
            insertData = insertData.map(item => {
                item['jitFlag'] = item.jit
                item['id'] = null
                item['materialId'] = item.materialId
                //属性置空
                return item
            })
            itemGrid.insertAt(insertData, -1)
            this.handleHeaderFields({pageData: this.pageData, flag: true})
        },
        saveEvent () {
            let { purchaseDeliveryNoticeList = [] } = this.$refs.editPage.getPageData() || {}
            if (!purchaseDeliveryNoticeList.length) {
                let tip = [this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_empty_addLine`, '请添加行项目')]
                this.$message.error(tip.join(''))
                return
            }
            this.$refs.editPage.postData()
        },
        goBack () {
            this.$emit('hide')
        },
        selectCallBack (item) {
            this.pageData.form.dictCode = item[0].dictCode
            this.$refs.editPage.$forceUpdate()
        },
        publishEvent () {
            let { purchaseDeliveryNoticeList = [] } = this.$refs.editPage.getPageData() || {}
            if (!purchaseDeliveryNoticeList.length) {
                let tip = [this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_empty_addLine`, '请添加行项目')]
                this.$message.error(tip.join(''))
                return
            }
            for (let i = 0; i < purchaseDeliveryNoticeList.length; i++) {
                if (purchaseDeliveryNoticeList[i].requireQuantity <= 0) {
                    let tip = [this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cTVWRlTfUW_49c8866`, '行需求数量必须大于0')]
                    this.$message.error(tip.join(''))
                    return
                }
            }
            this.$refs.editPage.handleSend()
        },
        getDictByCode (dictCode, map) {
            let params = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: dictCode
            }
            ajaxFindDictItems(params).then(res => {
                if (!res.success) {
                    return
                }
                let result = res.result || []
                let dictMap = result.reduce((acc, obj) => {
                    acc[obj.value] = obj.title
                    return acc
                }, {})
                this[map] = dictMap
            })
        },
        queryMaxRequireQuantity () {
            let pageData = this.$refs.editPage.getPageData() || {}

            let itemGrid = this.$refs.editPage.$refs.purchaseDeliveryNoticeList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (checkboxRecords.length == 0 || checkboxRecords.length > 1) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectPieceOfData`, '请选择一条数据!'))
                return
            }
            
            let materialNumber = checkboxRecords[0].materialNumber
            let factory = checkboxRecords[0].factory
            if (pageData.factory) {
                pageData.factory = factory
            }
            let storageLocation = checkboxRecords[0].storageLocation
            if (pageData.storageLocation) {
                pageData.storageLocation = storageLocation
            }

            if (!pageData.toElsAccount) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierInfoTips`, '请选择供应商信息！'))
                return
            }
            if (!materialNumber) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectMaterialInfoTips`, '请选择物料信息！'))
                return
            }
            if (!factory) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectFactorylInfoTips`, '请选择工厂信息！'))
                return
            }
            if (!storageLocation) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectInventoryLocationlInfoTips`, '请选择库存地点信息！'))
                return
            }
            const param = {
                noticeNumber: pageData.noticeNumber,
                toElsAccount: pageData.toElsAccount,
                materialNumber: materialNumber,
                factory: factory,
                storageLocation: storageLocation
            }
            postAction(this.url.queryMaxRequireQuantity, param).then(res => {
                if (!res.success) {
                    this.$message.error(res.message)
                    return
                }

                let factory_dictText = this.factory[factory] || ''
                let storageLocation_dictText = this.storageLocation[storageLocation] || ''
                
                let tip = [
                    this.$srmI18n(`${this.$getLangAccount()}#i18n__RdX_1369e0d`, '供应商'),
                    ': ',
                    pageData.toElsAccount,
                    ', ',
                    this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead9ab6_materialCode`, '物料编码'),
                    ': ',
                    materialNumber,
                    ', ',
                    this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'),
                    ': ',
                    factory_dictText,
                    ', ',
                    this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageLocation`, '库存地点'),
                    ': ',
                    storageLocation_dictText,
                    this.$srmI18n(`${this.$getLangAccount()}#i18n__jefTVRL_57cc4d38`, '的最大需求量为'),
                    ': ',
                    res.result
                ]
                this.$message.success(tip.join(''))
            })
        }
    }
}
</script>
