<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkTemplateSelectOk"
        ref="TemplateFieldSelectModal" />
      <field-select-modal
        :isEmit="true"
        @ok="checkDocumentSelectOk"
        ref="orderFieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getLodop } from '@/utils/LodopFuncs'
import { getAction, postAction } from '@/api/manage'

export default {
    name: 'PurchaseBarcodePrintHeadEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            printTemplateCode: '',
            printTemplateName: '',
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            showConst: ['LINE', 'RECT', 'STYLE', 'STYLEA', 'ELLIPSE', 'SHAPE'],
            hideItems: [],
            requestData: {
                detail: {
                    url: '/base/barcode/purchaseBarcodePrintHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                itemList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.templateGridAddPopup
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ptfW_2e49e1e9`, '按单打印'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.templateGridOrder
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete'
                }]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/base/barcode/purchaseBarcodePrintHead/edit'
                    },
                    key: 'save',
                    showMessage: true,
                    handleBefore: this.handleSubmitBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/purchaseBarcodePrintHead/edit',
                templateResolve: '/base/barcode/purchaseBarcodePrintHead/templateResolve',
                queryItemByHeadId: '/base/barcode/purchaseBarcodeTemplateHead/queryItemByHeadId',
                queryPageFieldValueList: '/base/barcode/elsBarcodeAttribute/queryPageFieldValueList',
                getPageFieldString: '/base/barcode/elsBarcodeAttribute/getPageFieldString'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_barcodePrint_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            debugger
            let formModel = pageConfig.groups[0].formModel
            resultData['roleType'] = 'purchase'
            this.printTemplateCode = formModel.formModel
            this.printTemplateName = formModel.printTemplateName
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        // 处理保存回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handleSubmitBefore (args) {
            this.hideItems.forEach(row =>{
                args.allData.itemList.push(row)
            })
            return new Promise(resolve => {
                // 在这里处理逻辑
                args.allData.source = '打印任务'
                args.allData.printTemplateCode = this.printTemplateCode
                args.allData.printTemplateName = this.printTemplateName
                return resolve(args)
            })
        },
        // 处理发布回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!args.allData.itemList || !args.allData.itemList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cGtCcxOLVW_b5b62ece`, '行设计内容不能为空！'))
                    reject(args)
                }else {
                    resolve(args)
                }
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        handleShowFn () {
            let rs = true
            if (this.currentEditRow && this.currentEditRow.id) {
                rs = true
            } else {
                rs = false
            }
            return rs
        },

        templateDesign () {
            let allData = this.getBusinessExtendData(this.businessRefName).allData
            // let itemGrid = this.getItemGridRef('templateList')

            let oldTemplate = ''
            if (allData.templateList.length >0){
                oldTemplate += 'LODOP.PRINT_INIT("'+(!allData.templateName ? '条码套打设计' : allData.templateName)+'");'
                allData.templateList.forEach( a =>{
                    oldTemplate += a.designContent+';'
                })

            }
            let that = this
            let LODOP = getLodop()//调用getLodop获取LODOP对象

            if(oldTemplate!=''){
                eval(oldTemplate)
            }else{
                LODOP.PRINT_INIT( !allData.templateName ? '条码套打设计' : allData.templateName)
            }

            if (LODOP.CVERSION) CLODOP.On_Return=function (TaskID, Value){ that.templateResolve(Value, that)}

            if (LODOP.CLodopIsLocal){
                window.location.href = 'CLodop.protocol:setup'
            }
            LODOP.PRINT_DESIGN()
        },
        templateResolve (value, that){
            let data = {
                designContent: value
            }
            postAction(this.url.templateResolve, data).then((res) => {
                if (res.success) {
                    let resultArr = new Array()
                    res.result.forEach( a =>{
                        resultArr.push(a)
                    })
                    // 插入行数据
                    let itemGrid = that.getItemGridRef('templateList')
                    itemGrid.remove()
                    itemGrid.insertAt(res.result, -1)
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        templateGridAddPopup ({ Vue, pageConfig, btn, groupCode }) {
            this.curGroupCode = groupCode
            this.fieldSelectType = 'material'
            let url = '/base/barcode/purchaseBarcodeTemplateHead/list'
            let columns = [
                {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fWIrRL_9225b67e`, '打印模板名称')},
                {field: 'number', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fWIrAy_922ac69c`, '打印模板编号'), width: 150},
                {field: 'status_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tFzE_2764f484`, '单据状态'), width: 200}
            ]
            this.$refs.TemplateFieldSelectModal.open(url, {status: 'enabled'}, columns, 'single')
        },
        templateGridOrder ({ Vue, pageConfig, btn, groupCode }) {
            this.getItemGridRef(groupCode)
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseCheckNeedSnedData`,
                    '请勾选需要按单打印的数据'))
                return
            }
            if (checkboxRecords.length!==1) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseCheckNeedSnedData`,
                    '请勿勾选多条按单打印的数据'))
                return
            }
            let row = checkboxRecords[0]
            let url = this.url.queryPageFieldValueList
            let columns = [
                {field: 'fieldValue', title: this.$srmI18n(`${this.$getLangAccount()}#`, '单据编号')}
            ]
            this.$refs.orderFieldSelectModal.open(url, {businessType: row.businessType, businessField: 'DOCUMENT_NUMBER'},
                columns, 'single')

        },
        checkTemplateSelectOk (data) {
            let that = this
            getAction(this.url.queryItemByHeadId, {id: data[0].id} ).then((res) => {
                if (res.success) {
                    this.printTemplateName = data[0].name
                    this.printTemplateCode = data[0].number
                    console.log('this.printTemplateName:',this.printTemplateName)

                    let rowItem = []
                    this.hideItems = []
                    res.result.forEach(row =>{
                        if(this.showConst.findIndex(item => item === row['elementType']) == -1){
                            rowItem.push(row)
                        }else {
                            this.hideItems.push(row)
                        }
                    })
                    // 插入行数据
                    let itemGrid = that.getItemGridRef('itemList')
                    itemGrid.remove()
                    itemGrid.insertAt(rowItem, -1)
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        checkDocumentSelectOk (data) {
            let itemGrid = this.getItemGridRef('itemList')
            let row = itemGrid.getCheckboxRecords()[0]
            let newData = []
            let keyWord = data[0].fieldValue
            let url = this.url.getPageFieldString
            getAction(url, {businessType: row.businessType, businessField: row.businessField, keyWord,
                documentQueryType: 'DOCUMENT_QUERY'
            } ).then((res) => {
                if (res.success) {
                    itemGrid.getTableData().fullData.forEach(a =>{
                        if(a.id == row.id){
                            a.datasource = res.result
                        }
                        newData.push(a)
                    })
                    itemGrid.loadData(newData)
                } else {
                    this.$message.warning(res.message)
                }
            })

        }
    }
}
</script>