<template>
  <div class="page-container">
    <a-spin :spinning="confirmLoading">
      <!-- 头部页面 -->
      <div class="page-header">
        <!-- 页面标题 -->
        <div class="breadcrumb">
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_OusBfW_31b54479`, '竞价报表打印') }}</span>
        </div>
        <!-- 头部按钮 -->
        <div class="btnGroups">
          <a-button
            type="primary"
            @click="goPrintpage"
          >
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_Print`, '打印') }}
          </a-button>

          <a-button
            type="default"
            @click="back"
          >
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}
          </a-button>
        </div>
      </div>


      <div
        class="page-main"
        id="printPage"
      >
        <div class="printPage-title">{{ $srmI18n(`${$getLangAccount()}#i18n_field_OutHB_e96988ea`, '竞价记录表') }}</div>
        <!-- 表单 -->
        <div class="form">
          <a-descriptions
            size="small"
            :column="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }"
            bordered>
            <a-descriptions-item 
              v-for="item in formData.custom.formFields"
              :key="item.fieldName"
              :label="item.fieldLabel">
              {{ form[item.fieldName] }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 表格 -->
        <div
          class="grid"
          v-for="grid in pageData.groups"
          :key="grid.groupCode">
          <vxe-grid
            :ref="grid.custom.ref"
            v-bind="defaultGridOption"
            :scroll-y="{enabled: false}"
            :columns="grid.custom.columns">
            <template #grid_opration="{ row, column }">
              <a
                v-for="(item, i) in grid.custom.optColumnList"
                :key="'opt_'+ row.id + '_' + i"
                :title="item.title"
                style="margin:0 4px"
                :disabled="item.allow ? item.allow(row) : false"
                v-show="item.showCondition ? item.showCondition(row) : true"
                @click="item.clickFn(row, column)">{{ item.title }}</a>
            </template>
          </vxe-grid>
        </div>

      </div>
    </a-spin>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
    name: 'EbiddingPrint',
    props: {
        currentEditRow: {
            type: Object,
            default: () =>{
                return {}
            }
        }
    },
    data (){
        return {
            confirmLoading: false,
            showBa: false,
            form: {},
            // 表格默认配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: false,
                showHeaderOverflow: false,
                columnKey: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                columns: [],
                data: []
            },
            // 表单
            formData: {
                groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_basicData`, '基础信息'),
                groupCode: 'baseForm',
                custom: {
                    formFields: [
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号'),
                            fieldName: 'ebiddingNumber'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sender`, '发布人'),
                            fieldName: 'publishUser'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitTime`, '发布日期'),
                            fieldName: 'publishTime'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司代码'),
                            fieldName: 'company_dictText'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRVR_44661349`, '采购组织'),
                            fieldName: 'purchaseOrg_dictText'
                        },
                        {
                            fieldType: 'password',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXWR_8e14292c`, '供应商数量'),
                            fieldName: 'supplierCounts'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingType`, '竞价类型'),
                            fieldName: 'ebiddingType_dictText'
                        },
                        {
                            fieldType: 'switch',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suCK_2e0c7ce8`, '报价方式'),
                            fieldName: 'ebiddingWay_dictText'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_budgetAmount`, '预算金额'),
                            fieldName: 'projectBudget'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_economyAmount`, '节支金额'),
                            fieldName: 'savingAmount'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBL_182891b`, '授标人'),
                            fieldName: 'pricingPeople_dictText'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBII_2ed15c51`, '授标意见'),
                            fieldName: 'pricingInstructions'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OuyRUzzE_deb710c5`, '竞价结果审批状态'),
                            fieldName: 'resultAuditStatus_dictText'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OuyRUzL_830e02c0`, '竞价结果审批人'),
                            fieldName: 'resultOpinionPeople'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OuyRII_3e0982d4`, '竞价结果意见'),
                            fieldName: 'resultOpinion'
                        }
                    ]
                }

            },
            // 表格
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                        ref: 'purchaseEbiddingSupplierList',
                        columns: [
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 170 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 300 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), field: 'needCoordination_dictText', width: 160 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseEbiddingItemList',
                        columns: [
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SLzAAo_fa81e5f0`, '物料分类编码'), field: 'cateCode', width: 160 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationName`, '物料分类名称'), field: 'cateName', width: 110 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialNumber`, '物料编码'), field: 'materialNumber', width: 175 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialDesc`, '物料描述'), field: 'materialDesc', width: 135 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TVWR_46474721`, '需求数量'), field: 'requireQuantity', width: 75 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fIWWW_f03b2eb3`, '税率(%)'), field: 'taxRate', width: 65 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currency`, '币别'), field: 'currency_dictText', width: 62 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'), field: 'supplierName', width: 140 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCSRH_a3092b47`, '公司或工厂'), field: 'factory_dictText', width: 190 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'), field: 'quoteRank', width: 50 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lVsuWLftuW_667d4185`, '首轮报价（未税单价)'), field: 'firstQuotePrice', width: 142 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CVsuWLftuW_f3b2eb90`, '末轮报价（未税单价)'), field: 'netPrice', width: 142 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quota`, '配额'), field: 'quota', width: 62 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteCount`, '报价次数'), field: 'quoteCount', width: 75 }
                        ]
                    } }
                    // { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVBI_24b50608`, '上传附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                    //     ref: 'purchaseAttachmentExtendList',
                    //     columns: [
                    //         { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                    //         { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 150 },
                    //         { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 150 },
                    //         { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                    //         { field: 'enterpriseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCRL_713698e6`, '上传方名称'), width: 200 },
                    //         { field: 'uploadElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCey_7137a94a`, '上传方帐号'), width: 160 },
                    //         { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 200 },
                    //         { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                    //     ],
                    //     showOptColumn: true,
                    //     optColumnList: [
                    //         { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.handleDownload },
                    //         { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                    //     ]
                    // } }
                ]
            },
            url: {
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    created (){
        this.getData()
    },
    methods: {
        back (){
            this.$emit('hide')
        },
        goPrintpage () {
            let url = this.$router.resolve({
                path: '/print/ebiddingPrint',
                query: { id: this.currentEditRow.id }
            })
            window.open(url.href, '_blank')
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        getData (){
            this.confirmLoading = true
            getAction('/ebidding/purchaseEbiddingHead/printEbidding', {id: this.currentEditRow.id}).then(res=>{
                if(res.success) {
                    this.form = res.result
                    this.pageData.groups.forEach(group=>{
                        if(this.form.ebiddingWay == '0') {
                            if(group.custom.ref == 'purchaseEbiddingItemList'){
                                let column = { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CVsuWLftuW_f3b2eb90`, '末轮报价（未税单价)'), field: 'netTotalAmount', width: 142 }
                                group.custom.columns.splice(12, 1, column)
                            }
                        }
                        if(group.type == 'grid') {
                            let ref = group.custom.ref
                            this.$refs[ref][0].loadData(res.result[ref])
                        }

                    })  
                }
            }).finally(()=>{
                this.confirmLoading = false
            })
        },
        // 文件下载
        handleDownload ({ id, fileName }) {
            const params = {
                id
            }
            let downloadUrl = this.url.download 
            getAction(downloadUrl, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }
    }
}
</script>

<style lang="less" scoped>
@primary-color: #1890ff;
.page-container {
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 14px;
        background: #fff;
        border-bottom: 1px solid #e8eaec;
        .btnGroups {
            text-align: right;
            .ant-btn {
                & +.ant-btn {
                    margin-left: 10px;
                }
            }
        }
    }
    .page-main {
        position: relative;
    }
}


  :deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
    background-color: #f8feff;
  }
  :deep(.ant-descriptions-item-content) {
    width: 16.66%;
    max-width: 16.66%;
    background-color: #fff;
  }
  :deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title),
  :deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description) {
    color: @primary-color;
  }
  :deep(.rich-editor-display-box) p {
      margin-bottom: 0px;
  }

  :deep(.vTable-table .vxe-table .vxe-table--header .vxe-header--row  .vxe-header--column  .vxe-cell--title) {
	    white-space:pre-wrap;
	    word-wrap: break-word;
  }

</style>

