<template>
  <div class="purchaseSupplierCapacityHead">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="flag"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :handleAfterDealSource="handleAfterDealSource"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="tab"
        pageStatus="detail"

        v-on="businessHandler"
      >
      </business-layout>

      <!-- <field-select-modal
        ref="fieldSelectModal"
        @ok="fieldSelectOk"
        isEmit
      /> -->
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"/>
      <a-modal
        v-model="visible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_checkOpeningAdvancePassword`, '查看开标密码')"
        :footer="null">
        <p>{{ openBidPassword }}</p>
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import flowViewModal from '@comp/flowView/flowView'
import { getAction, postAction } from '@/api/manage'

export default {
    name: 'PurchaseTenderProjectHead',
    components: {
        BusinessLayout,
        fieldSelectModal,
        flowViewModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            openBidPassword: '',
            visible: false,
            businessRefName: 'businessRefName',
            flag: true,
            flowView: false,
            flowId: 0,
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                detail: { url: '/tender/purchaseTenderProjectHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                // projectItemList: [
                //     {
                //         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplementaryMaterial`, '补充物料'),
                //         click: this.addBiddingItem, authorityCode: 'tender#purchaseTenderProjectHead:replenish'
                //     }

                // ]
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.cancelAudit,
                    show: this.cancelButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.auditButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkOpeningAdvancePassword`, '查看开标密码'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.viewPassword,
                    show: this.passwordButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingHall`, '招标大厅'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.toTender,
                    show: this.allowHall
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                save: '/tender/purchaseTenderProjectHead/edit',
                publish: '/tender/purchaseTenderProjectHead/publish',
                reject: '/tender/purchaseTenderProjectHead/reject',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                viewPassword: '/tender/purchaseTenderProjectHead/viewPassword'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            console.log(templateNumber, templateVersion)
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_biddingPlatform_${templateNumber}_${templateVersion}`
        }

    },
    methods: {
        // 查看开标密码，展示开标密码弹窗
        async viewPassword (){
            let url = '/tender/purchaseTenderProjectHead/viewPassword'
            let id = this.currentEditRow.id
            await getAction(url, {id}).then((res) => {
                if(res.success) {
                    this.openBidPassword = res.message
                    this.visible = true
                }else{
                    this.$message.error(res.message)
                }
            }).finally(() => {

            })
        },
        // 查看开标密码按钮控制
        passwordButtonShow (){
            return ((this.currentEditRow.openBidPassword ?? '') != ''&& this.currentEditRow.auditStatus != '1')
        },
        // 补充物料
        // addBiddingItem () {
        //     this.selectType = 'material'
        //     console.log(this.$refs.businessRefName.$refs.projectItemListgrid[0])
        //     const form = this.$refs.businessRefName.$refs.projectItemListgrid[0].gridData
        //     const { mustMaterialNumber = '1' } = form
        //     if(mustMaterialNumber == '1'){
        //         let url = '/material/purchaseMaterialHead/list'
        //         let columns = [
        //             {
        //                 field: 'cateCode',
        //                 title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
        //                 width: 150
        //             },
        //             {
        //                 field: 'cateName',
        //                 title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
        //                 width: 150
        //             },
        //             {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150},
        //             {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150},
        //             {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200},
        //             {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200}
        //         ]
        //         this.$refs.fieldSelectModal.open(url, {blocDel: '0', freeze: '0'}, columns, 'multiple')
        //     }else{
        //         console.log('进入了else')
        //         let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
        //         let itemData = {}
        //         this.pageConfig.itemColumns.forEach(item => {
        //             if(item.defaultValue) {
        //                 itemData[item.field] = item.defaultValue
        //             }
        //         })
        //         itemGrid.insertAt([itemData], -1)
        //     }
        // },
        // fieldSelectOk (data) {
        //     let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
        //     let {tableData} = itemGrid.$refs.projectItemList.getTableData()
        //     let materialList = tableData.map(item => {
        //         return item.materialNumber
        //     })
        //     //过滤已有数据
        //     let insertData = data.filter(item => {
        //         if(materialList.includes(item.materialNumber)){
        //             this.$message.warning('请勿重复补充物料！')
        //             return false
        //         }
        //         return true
        //         // return !materialList.includes(item.materialNumber)
        //     })
        //     itemGrid.pageConfig.groups[2].columns.forEach(item => {
        //         if(item.defaultValue) {
        //             insertData.forEach(insert => {
        //                 if(!insert[item.field]){
        //                     insert[item.field] = item.defaultValue
        //                 }
        //             })
        //         }
        //     })
        //     insertData.forEach(insert => {
        //         insert['materialId'] = insert['id']
        //         delete insert.id
        //     })
        //     // let param = {
        //     //     'purOrgCode': this.$refs.businessRefName.$refs.projectItemListgrid[0].gridData.purchaseOrg,
        //     //     'materialDataVos': data
        //     // }
        //     // postAction(this.url.materialBidding, param).then(res => {
        //     //     if(res.success){
        //     //         itemGrid.insertAt(insertData, -1)
        //     //     } else {
        //     //         this.$confirm({
        //     //             title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLSu_38d85f7b`, '确认添加'),
        //     //             content: res.message,
        //     //             onOk: function () {
        //     //                 itemGrid.insertAt(insertData, -1)
        //     //             }
        //     //         })
        //     //     }
        //     // })
        //     console.log(insertData)
        //     // 当补充的物料在表行中不存在时，则保存不重复的物料
        //     if(insertData.length >0){
        //         itemGrid.$refs.projectItemList.insertAt(insertData, -1)
        //         let url = '/tender/purchaseTenderProjectHead/replenish'
        //         console.log(this.$refs.businessRefName)
        //         this.$refs.businessRefName.resultData.projectItemList.push(...insertData)
        //         let params = this.$refs.businessRefName.resultData
        //         this.confirmLoading = true
        //         postAction(url, params).then(res => {
        //             if(res.success) {
        //                 this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_savaSuccess`, '保存成功'))
        //                 this.confirmLoading = false
        //             }else{
        //                 this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VsK_21f9ddf`, '请重试'))
        //                 this.confirmLoading = false
        //                 return false
        //             }
        //         })
        //     }
        // },
        cancelAudit ({ Vue, pageConfig, btn, groupCode }) {
            this.auditStatus = pageConfig.groups[0].formModel.auditStatus
            // if(this.auditStatus != '1'){
            //     this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APxOqXUz_5fba973e`, '当前不能撤销审批'))
            //     return
            // }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit, pageConfig.groups[0].formModel)
                    // that.$refs.businessRefName.loadData()
                }
            })


        },
        auditButtonShow  ({ pageData }) {
            const {flowId, audit, status} = pageData
            console.log('pagedata', pageData)
            return (flowId != null && audit == '1' && status != '0')

            // return flowId ? true : false
        },
        cancelButtonShow  ({ pageData }) {
            const {auditStatus, audit} = pageData
            console.log('pagedata', pageData)
            return (auditStatus == '1' && audit == '1')

            // return flowId ? true : false
        },
        showFlow ({ Vue, pageConfig, btn, groupCode }) {
            this.flowId = pageConfig.groups[0].formModel.flowId
            // if(!this.flowId){
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_APUziNxOmAQLW_25fa4a00`, '当前审批策略不能查看流程！'))
            //     return
            // }
            this.flowView = true
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'barcodeInfoAudit'
            param['auditSubject'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_TotUzWtyW_1dc23b59`, '条码单审批，单号：') + formData.trialNumber
            param['params'] = JSON.stringify(formData)
            this.confirmLoading=true
            this.flag=false
            postAction(invokeUrl, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    if (res?.result?.auditStatus == '0') {
                        this.$parent.submitCallBack(formData)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
                this.flag=true
            })
        },
        handleAfterDealSource (pageConfig, resultData) {
            pageConfig.groups.forEach(group =>{
                if(group.groupCode == 'baseForm'){
                    group.formModel.openBidPassword = (group.formModel.openBidPassword ?? '') != '' ? '******' : ''
                }
            })
        },
        allowHall (){
            let row = Object.assign({}, this.currentEditRow)
            console.log(row.tenderStatus)
            return row.tenderStatus == '1'
        },
        toTender () {
            // 默认角色为0，只能查看
            let row = Object.assign({}, this.currentEditRow)
            row['applyRole'] = '0'
            getAction('/tender/purchaseTenderProjectHead/getExecutorAuthority', {id: row.id}).then(res=>{
                row['applyRole'] = res.result > 1 ? '0' : res.result
                this.$ls.set('SET_TENDERCURRENTROW', row)
                let _t = +new Date()
                const routeUrl = this.$router.resolve({
                    path: '/biddingHall',
                    query: {
                        _t
                    }
                })
                window.open(routeUrl.href, '_blank')
            })
        }
    }
    // mounted(){
    //     this.auditStatus
    // }
}
</script>