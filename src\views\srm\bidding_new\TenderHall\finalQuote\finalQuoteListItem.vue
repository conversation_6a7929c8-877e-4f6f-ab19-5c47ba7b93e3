<template>
  <div class="page-container">
    <a-spin 
      :spinning="confirmLoading">
      <titleCrtl>
        <span class="margin-r-10">{{ $srmI18n(`${$getLangAccount()}#i18n_field_essu_30ba66ba`, '最终报价') }}</span>
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_suyRKIW_9441ecf2`, '报价截止时间：') }}{{ '' }} {{ currentEditRow.quotedPriceEndTime }}</span>
        <template slot="right">
          <a-button
            v-if="isEdit"
            type="primary"
            :loading="loading"
            class="margin-r-10"
            @click="addQuotedPrice">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
          <a-button
            v-else
            type="primary"
            :loading="loading"
            class="margin-r-10"
            @click="revokeQuotedPrice">{{ $srmI18n(`${$getLangAccount()}#i18n_title_withdraw`, '撤回') }}</a-button>
          <a-button
            :loading="loading"
            @click="back">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
        </template>
      </titleCrtl>
      <tenderBidLetterVoList
        ref="tenderBidLetterVoList"
        :subpackage="subpackage"
        v-if="showPrice"
        :fromSourceData="fromSourceData"
        :pageStatus="pageStatus"></tenderBidLetterVoList>
    </a-spin>
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import tenderBidLetterVoList from '../DocumentSubmitOfCa/components/tenderBidLetterVoList.vue'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
export default {
    mixins: [baseMixins],
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            pageType: 'edit',
            formData: {},
            gridData: [],
            showPrice: false,
            loading: false,
            saleTenderPriceOpeningsObj: {},
            confirmLoading: false,
            url: {
                queryQuotedPriceInfo: '/tender/sale/tenderEvaQuotedPriceHead/queryQuotedPriceInfo',
                addQuotedPriceInfo: '/tender/sale/tenderEvaQuotedPriceHead/addQuotedPriceInfo',
                revokeQuotedPriceInfo: '/tender/sale/tenderEvaQuotedPriceHead/revokeQuotedPriceInfo'
            }
        }
    },
    components: {
        tenderBidLetterVoList,
        titleCrtl
    },
    computed: {
        subId () {
            return this.subpackageId()
        },
        isEdit () {
            return this.formData.status == '0'
        },
        subpackage (){
            return this.currentSubPackage()
        },
        pageStatus () {
            return this.formData.status == '0' ? 'edit' : 'detail'
        }
    },
    methods: {
        getData () {
            this.confirmLoading = true
            let params = {
                quotedPriceItemId: this.currentEditRow.quotedPriceItemId
            }
            getAction(this.url.queryQuotedPriceInfo, params).then(res => {
                if (res.success) {
                    let {saleTenderBidLetterVos, ...other} = res.result || {}
                    this.formData = other
                    let tenderBidLetterVoList = JSON.parse(JSON.stringify(saleTenderBidLetterVos)) || []
                    let processType = '0'
                    tenderBidLetterVoList.map(item => {
                        if (item.processType == '1') processType = '1'
                        if (item.priceOpeningsList) {
                            item.priceOpeningsList[0]['customizeFieldData'] = JSON.parse(item.priceOpeningsList[0]['customizeFieldData']) || []
                            item.priceOpeningsList[0]['customizeFieldModel'] = JSON.parse(item.priceOpeningsList[0]['customizeFieldModel']) || []
                            item.priceOpeningsList[0]['customizeFieldModel'].must = item.priceOpeningsList[0]['customizeFieldModel'].must ? '1' : '0'
                        }
                    })
                    this.fromSourceData = {tenderBidLetterVoList, processType}
                    this.showPrice = true
                    if (this.$refs.tenderBidLetterVoList) this.$refs.tenderBidLetterVoList.init({tenderBidLetterVoList: tenderBidLetterVoList})
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        addQuotedPrice () {
            let tenderBidLetterVoList = this.$refs.tenderBidLetterVoList.getParamsData()
            let priceOpeningsList = []
            let saleQuoteMaterialDataVOList = []
            let flag = false
            for (let i = 0; i < tenderBidLetterVoList.length; i++) {
                // 投标函其他类型不需要校验
                if (tenderBidLetterVoList[i].formatType != '9') {
                    // 投标列输入quoteColumnSource 0 手动输入不需要校验物料行，但需要校验投标函报价
                    console.log(tenderBidLetterVoList[i])
                    if (tenderBidLetterVoList[i].quoteColumnSource !== '0') {
                        // 物料报价列遍历
                        for (let item of tenderBidLetterVoList[i].saleQuoteMaterialDataList) {
                            // 物料行遍历
                            for (let material of item.materialDataList) {
                                if (!material.price) {
                                    flag = true
                                    this.$message.warning(`投标函[${tenderBidLetterVoList[i].name}]报价列[${item.title}]物料号[${material.materialName}]未填写报价`)
                                    break
                                }
                            }
                            if (flag) break
                        }
                    } else {
                        // quoteColumnSource 0 手动输入，需要校验投标函的报价列的数据
                        tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldModel.some(item => {
                            if (item.must && !tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldData[0][item.field]) {
                                this.$message.warning(`投标函[${tenderBidLetterVoList[i].name}]报价列[${item.title}]未填写报价`)
                                flag = true
                                return true
                            }
                        })
                    }
                    if (flag) return false
                }
                tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldData = JSON.stringify(tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldData)
                // 数据格式处理
                tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldModel.forEach(item=>{
                    item.must = item.must ? '1' : '0'
                })
                tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldModel = JSON.stringify(tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldModel)
            }
            priceOpeningsList = tenderBidLetterVoList.map(item => {
                saleQuoteMaterialDataVOList.push(...item.saleQuoteMaterialDataList)
                return item.priceOpeningsList[0]
            })
            if (flag) return 
            let params = Object.assign(this.formData, {priceOpeningsList, saleQuoteMaterialDataVOList})
            let target = this.$refs.tenderBidLetterVoList.$refs.Information.$refs.materialList.$refs.listTable
            console.log(target)
            target.getValidate().then(res=>{
                this.confirmLoading = true
                postAction(this.url.addQuotedPriceInfo, params).then(res => {
                    let type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        this.showPrice = false
                        this.getData()
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            }).catch(error=>{
                console.log('err', error)
                this.$refs.tenderBidLetterVoList.$refs.Information.isTetterListTable = false
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VPSLcsdjlSdSMMi_d83b6e21`, '请将物料行中所有必填项填写完整！'))
            })
            
        },
        revokeQuotedPrice () {
            let params = {
                quotedPriceItemId: this.formData.id
            }
            this.confirmLoading = true
            getAction(this.url.revokeQuotedPriceInfo, params).then(res => {
                let type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.showPrice = false
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        back () {
            this.$emit('hidden')
        }
    },
    created () {
        this.getData()
    }
}
</script>
<style lang="less" scoped>
.margin-r-10{
    margin-right: 10px;
}
</style>

