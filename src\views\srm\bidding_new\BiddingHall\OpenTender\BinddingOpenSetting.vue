<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="show"
        :ref="businessRefName"
        :currentEditRow="currentEditRowData"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="masterSlave"
        :fromSourceData="fromSourceData"
        :pageStatus="pageStatus"
        v-on="businessHandler"> </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { baseMixins } from '../../plugins/baseMixins.js'

export default {
    name: 'TenderOpenSetting',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'resetCurrentSubPackage'],
    computed: {
        // remoteJsFilePath () {
        //     // let templateNumber = this.currentEditRow.templateNumber
        //     // let templateVersion = this.currentEditRow.templateVersion
        //     // let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
        //     let templateNumber = 'TC2022042801'
        //     let templateVersion = 1
        //     let account = 100000
        //     return `${account}/purchase_tenderOpenSetting_${templateNumber}_${templateVersion}`
        // },
        pageStatus () {
            let status = this.fromSourceData.status == '1' ? 'detail' : 'edit'
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') status = 'detail'
            return status
        },
        subId () {
            return this.subpackageId()
        }
    },
    mixins: [businessUtilMixin, baseMixins],
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            show: false,
            fromSourceData: {},
            externalToolBar: {
                attendeesList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            },
            pageFooterButtons: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/tender/purchaseTenderProjectOpenSettingHead/publish'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    click: this.publish,
                    key: 'publish'
                }
            ],
            currentGroupCode: {},
            noticeStatus: '',
            url: {
                detail: '/tender/purchaseTenderProjectOpenSettingHead/queryBySubpackageId',
                add: '/tender/purchaseTenderProjectOpenSettingHead/add',
                edit: '/tender/purchaseTenderProjectOpenSettingHead/edit',
                publish: '/tender/purchaseTenderProjectOpenSettingHead/publish'
            },
            remoteJsFilePath: '',
            currentEditRowData: {}
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData){
            console.log('pageConfig', pageConfig)
            
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }
            let secretFlag = ((this.tenderCurrentRow.openBidPassword ?? '') !== '')
            if(secretFlag){
                pageConfig.groups[0].formFields.forEach(item=>{
                    if(item.fieldName == 'autoOpenBid'){
                        item.helpText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dIGRjvBwoxiTJOvB_6d1b6d43`, '项目设置的开标密码，不允许自动开标！')
                        // item.alertMsg = 'sos!~!!!'
                    }
                })
            }
            setDisabledByProp('autoOpenBid', secretFlag)
            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }

                rule[prop] = [
                    {
                        required: flag,
                        message: `${fieldLabel}必填`
                    }
                ]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            //此处写根据分包信息作为判断展示位
            let signUpValidateFlag = !secretFlag
            setValidateRuleByProp('autoOpenBid', signUpValidateFlag)

            // 是否隐藏
            let setVisibleByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.hide = flag
                            break
                        }
                    }
                }
            }
            // 根据招标项目头信息 “开标是否需要加密” 设置“解密时长（分钟）” 显示并必填（是 1）
            const openEncryptFlag = this.tenderCurrentRow.openBidEncrypt === '1'
            setValidateRuleByProp('decryptionTime', openEncryptFlag)
            setVisibleByProp('decryptionTime', !openEncryptFlag)
        },
        init () {
            this.confirmLoading = true
            this.show = false
            this.getData()
        },
        getData () {
            return getAction(this.url.detail, { subpackageId: this.subId, checkType: this.checkType })
                .then((res) => {
                    if (res.success) {
                        this.fromSourceData = res.result
                        if (this.fromSourceData.status == '1') this.externalToolBar = {}
                        console.log('this.tenderCurrentRow', this.tenderCurrentRow)
                        if ((this.tenderCurrentRow.openBidPassword ?? '') !== '') {
                            console.log('coming in!!!')
                            this.fromSourceData.autoOpenBid = '0'
                        }
                        console.log('this.fromSourceData.autoOpenBid', this.fromSourceData.autoOpenBid)
                        this.show = true
                        // this.$emit('resetCurrentSubPackage')
                        this.resetCurrentSubPackage()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        businessGridAdd () {
            let url = '/account/elsSubAccount/list'
            if (this.fromSourceData.biddingUnitElsAccount) {
                url += `?els_account=${this.fromSourceData.biddingUnitElsAccount}`
            }
            if (this.fromSourceData.agencyElsAccount) {
                url += `&els_account=${this.fromSourceData.agencyElsAccount}`
            }
            let columns = [
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), with: 150 },
                { field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), with: 150 }
            ]
            this.$refs.fieldSelectModal.open(url, {}, columns, 'multiple')
        },
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef('attendeesList')
            let userList = data.map((item) => {
                return {
                    ...item,
                    attendeesElsAccount: item.elsAccount,
                    attendeesElsSubAccount: item.subAccount,
                    attendeesElsRealname: item.realname,
                    subpackageId: this.subId,
                    // ID置空
                    id: ''
                }
            })
            itemGrid.insertAt(userList, -1)
        },
        async getParamsData (args, cb) {
            this.stepValidate(args).then(() => {
                const allData = this.getAllData()
                // if (allData.decryptionTime < 5) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ywKHenWzs_10287ef4`, '解密时长最低5分钟'))
                let params = {
                    ...allData,
                    subpackageId: this.subId,
                    checkType: this.checkType
                }
                cb(params)
            })
        },
        saveEvent (args) {
            let cb = (params) => {
                const allData = this.getAllData()
                let url = allData.id ? this.url.edit : this.url.add
                params = {
                    ...params,
                    ...this.currentEditRowData
                }
                this.confirmLoading = true
                params = {
                    ...params,
                    ...this.currentEditRowData
                }
                postAction(url, params)
                    .then((res) => {
                        let type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.init()
                        }
                    })
                    .finally(() => {
                        this.confirmLoading = false
                    })
            }
            this.getParamsData(args, cb)
        },
        publish (args) {
            let that = this
            let cb = (params) => {
                that.$confirm({
                    title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_submit`, '提交'),
                    content: that.$srmI18n(`${that.$getLangAccount()}#i18n_field_submitStatus`, '是否提交'),
                    onOk: () => {
                        params = {
                            ...params,
                            ...this.currentEditRowData
                        }
                        that.confirmLoading = true
                        params = {
                            ...params,
                            ...this.currentEditRowData
                        }
                        postAction(that.url.publish, params)
                            .then((res) => {
                                let type = res.success ? 'success' : 'error'
                                that.$message[type](res.message)
                                if (type == 'success') {
                                    that.$emit('resetCurrentSubPackage') || ''
                                    that.init()
                                }
                            })
                            .finally(() => {
                                that.confirmLoading = false
                            })
                    }
                })
            }
            this.getParamsData(args, cb)
        }
    },
    async mounted () {
        this.currentEditRowData = await this.getBusinessTemplate('tenderOpenSetting')
        this.remoteJsFilePath = `${this.currentEditRowData['templateAccount']}/purchase_tenderOpenSetting_${this.currentEditRowData['templateNumber']}_${this.currentEditRowData['templateVersion']}`
        this.init()
    }
}
</script>
