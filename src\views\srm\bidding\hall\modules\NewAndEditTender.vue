<template>
  <div class="change-tenders">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      @content-header-goback="handleReturn"
      @content-header-save="handleSave"
      @content-header-approval="handleApproval"
    />
    <div
      class="container"
      :style="style">
      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_title_changeheader`, '变更头') }}</div>
          <div class="table">
            <a-descriptions
              bordered
              size="small">
              <a-descriptions-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_changeOrderNo`, '变更单号')"
                :span="4">
                {{ tenderForm.changeNumber }}
              </a-descriptions-item>
              <a-descriptions-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_baseForm65d_auditStatus`, '审批状态')"
                :span="4">
                <a-select
                  style="width:100%"
                  :disabled="true"
                  v-model="tenderForm.auditStatus"
                >
                  <a-select-option value="0">
                    {{ $srmI18n(`${$getLangAccount()}#i18n_title_notApproved`, '未审批') }}
                  </a-select-option>
                  <a-select-option value="1">
                    {{ $srmI18n(`${$getLangAccount()}#i18n_title_approveding`, '审批中') }}
                  </a-select-option>
                  <a-select-option value="2">
                    {{ $srmI18n(`${$getLangAccount()}#i18n_title_approved`, '审批通过') }}
                  </a-select-option>
                  <a-select-option value="3">
                    {{ $srmI18n(`${$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝') }}
                  </a-select-option>
                </a-select>
              </a-descriptions-item>
              <a-descriptions-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_changeReason`, '变更原因')"
                :span="4">
                <a-input
                  type="textarea"
                  v-model="tenderForm.changeReason"
                />
              </a-descriptions-item>
              <a-descriptions-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_remark`, '备注')"
                :span="4">
                <a-input
                  type="textarea"
                  v-model="tenderForm.remark"
                />
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_title_changeDetail`, '变更明细') }}</div>
          <div class="table">
            <vxe-grid
              ref="subTender"
              v-bind="defaultGridOption"
              :columns="subTenderColumns"
              :data="subTenderList">
              <template slot="empty">
                <a-empty />
              </template>
              <template #custom_render="{ row, column }">
                <a-input
                  v-if="row.fieldType!=='datetime'"
                  v-model="row[column.property]"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')">
                </a-input>
                <a-date-picker
                  v-else
                  style="width:100%"
                  :show-time="true"
                  :valueFormat="'YYYY-MM-DD HH:mm:ss'"
                  v-model="row[column.property]"
                ></a-date-picker>
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import { getAction, postAction } from '@/api/manage'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        'content-header': ContentHeader
    },
    props: {
        // id必传
        id: {
            type: String,
            require: true,
            default: ()=> {
                return null
            }
        },
        // relationId 必填-招标id
        relationId: {
            type: String,
            require: true,
            default: ()=> {
                return null
            }
        },
        // tenderType 必传，值为new or edit
        tenderType: {
            type: String,
            require: true,
            default: ()=> {
                return null
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            formLayout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 14 }
            },
            tenderForm: {
                id: null,
                changeNumber: null,
                auditStatus: null,
                changeReason: null,
                elsAccount: null,
                remark: null,
                purchaseBiddingChangeItemList: []
            },
            tenderFormRules: {},
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', event: 'save' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'), type: 'primary', event: 'approval' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: '', event: 'goback' }
            ],
            showHeader: true,
            height: 0,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                columns: [],
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'dblclick', mode: 'cell' }
            },
            subTenderColumns: [
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                { field: 'fieldCode_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fieldName`, '字段名') },
                { field: 'fieldOriginalValue', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_originalValue`, '原始值') },
                { field: 'fieldValue', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeValue`, '变更值'), slots: {default: 'custom_render'}}
            ],
            subTenderList: []
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleReturn () {
            this.$emit('go-back')
        },
        handleSave () {
            this.addOrSaveChangeTender()
            this.$emit('save-tender')
        },
        handleApproval () {
            let list = this.$refs.subTender.getTableData().fullData
            this.tenderForm.purchaseBiddingChangeItemList = list
            this.$emit('approval-tender', this.tenderForm)
        },
        getDetailData () {
            this.confirmLoading = true
            // 新建-招标id查询，编辑-通过id查询
            let url = this.tenderType==='new'? '/bidding/purchaseBiddingChange/queryByBiddingId': '/bidding/purchaseBiddingChange/queryById'
            let params = { id: this.tenderType==='new'?this.relationId: this.id }
            getAction(url, params).then(res => {
                if (!res.success) {
                    this.$message.error(res.message)
                    return
                }
                if (this.tenderType==='new') {
                    if (res.result && res.result.length) {
                        res.result.forEach((data)=> {
                            let item = {
                                fieldCode_dictText: data.fieldCode_dictText,
                                fieldCode: data.fieldCode,
                                fieldType: data.fieldType,
                                fieldOriginalValue: data.fieldOriginalValue,
                                fieldValue: data.fieldValue
                            }
                            this.subTenderList.push(item)
                        })
                    }
                } else {
                    if (res.result) {
                        this.tenderForm.id = res.result.id
                        this.tenderForm.changeNumber = res.result.changeNumber
                        this.tenderForm.auditStatus = res.result.auditStatus
                        this.tenderForm.changeReason = res.result.changeReason
                        this.tenderForm.remark = res.result.remark
                        this.tenderForm.elsAccount = res.result.elsAccount
                        if (res.result.purchaseBiddingChangeItemList && res.result.purchaseBiddingChangeItemList.length) {
                            res.result.purchaseBiddingChangeItemList.forEach((data)=> {
                                let item = {
                                    fieldCode_dictText: data.fieldCode_dictText,
                                    fieldCode: data.fieldCode,
                                    fieldType: data.fieldType,
                                    fieldOriginalValue: data.fieldOriginalValue,
                                    fieldValue: data.fieldValue
                                }
                                this.subTenderList.push(item)
                            })
                        }
                    }
                }
                
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 保存
        addOrSaveChangeTender () {
            const that = this
            let list = that.$refs.subTender.getTableData().fullData
            let url = that.tenderForm.id? 'bidding/purchaseBiddingChange/edit':'/bidding/purchaseBiddingChange/add'
            let postData = {
                id: that.tenderForm.id,
                relationId: that.relationId,
                changeReason: that.tenderForm.changeReason,
                remark: that.tenderForm.remark,
                elsAccount: that.tenderForm.elsAccount,
                purchaseBiddingChangeItemList: list
            }
            postAction(url, postData).then((res)=> {
                if (!res.success) {
                    this.$message.error(res.message)
                    return
                }
                if(!that.tenderForm.id){
                    that.tenderForm.id = res.result.id
                    that.tenderForm.changeNumber = res.result.changeNumber
                }
                this.$message.success(res.message)
            })
        }
    },
    created () {
        this.height = document.documentElement.clientHeight
        this.tenderForm.id = this.id
        this.getDetailData()
    }
}
</script>

<style lang="less" scoped>
.change-tenders {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
    :deep(.ant-descriptions-bordered .ant-descriptions-item-label){
        background: #F5F6F7;
    }
}
</style>
