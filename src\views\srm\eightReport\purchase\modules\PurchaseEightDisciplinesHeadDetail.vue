<!--
 * @Author: fzb
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2021-08-05 16:11:40
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\purchase\modules\PurchaseEditCost.vue
-->
<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <business-layout
        ref="businessRef"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>

      <!-- <a-modal
    v-drag    
        centered
        :width="960"
        :maskClosable="false"
        :visible="flowView"
        @ok="closeFlowView"
        @cancel="closeFlowView">
        <iframe
          style="width:100%;height:560px"
          title=""
          :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
          frameborder="0"></iframe>
      </a-modal> -->
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"/>
    </a-spin>
  </div>
</template>
<script lang='jsx'>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {ajaxFindDictItems} from '@/api/api'
import moment from 'moment'

export default {
    name: 'PurchaseEightDisciplinesHeadEdit',
    components: {
        flowViewModal,
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            requestData: {
                detail: { url: '/eightReport/purchaseEightDisciplines/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                purchaseEightDisciplinesTeamList: {add: true, delete: true}
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.cancelAudit,
                    show: this.showCancelAudit
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.showFlowAudit
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                save: '/eightReport/purchaseEightDisciplines/edit',
                publish: '/eightReport/purchaseEightDisciplines/publis',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            },
            materialNumberOptions: {}
        }
    },
    created () {
        this.queryDictData()
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_eightDisciplines_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        showCancelAuditCondition ({ pageData }) {
            if(pageData.auditStatus=='1'){
                return true
            }
            return false
        },
        showAuditFlowCondition ({ pageData }){
            if(pageData.auditStatus=='0'){
                return false
            }
            return true
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: '关联Tab',
                        fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        // fieldType: 'select',
                        defaultValue: '',
                        // dictCode: 'SRMEightAttachmentRelationTab',
                        slots: { default: ({row}) => {
                            return <span>{this.materialNumberOptions[row.materialNumber]}</span>
                        } },
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: {
                            default: ({row}) => {
                                return [
                                    <span>{moment(row.uploadTime).format('YYYY-MM-DD HH:mm:ss')}</span>
                                ]
                            }
                        }
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        field: 'uploadSubAccount',
                        fieldLabelI18nKey: '',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120' 
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData){
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
            }
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        showFlow ({ Vue, pageConfig, btn, groupCode }){
            this.flowId = pageConfig.groups[0].formModel.flowId
            this.flowView = true
        },
        cancelAudit ({ Vue, pageConfig, btn, groupCode }) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit, pageConfig.groups[0].formModel)
                }
            })
        },
        closeFlowView (){
            this.flowView = false
        },
        showCancelAudit ({ pageConfig }){
        //审批中的状态才显示
            if(pageConfig.groups[0].formModel.auditStatus=='1'){
                return true
            }
            return false
        },
        showFlowAudit ({ pageConfig }){
        //审批中的状态才显示
            if(pageConfig.groups[0].formModel.auditStatus=='1'){
                return true
            }
            return false
        },
        postAuditData (invokeUrl, formData){
            const that = this
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'eightDisciplines'
            param['auditSubject'] = '8D单号：'+formData.eightDisciplinesNumber
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    this.$parent.cancelAuditCallBack(formData)
                }else {
                    this.$message.warning(res.message)
                }
            })

        },
        queryDictData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'SRMEightAttachmentRelationTab'
            }
            ajaxFindDictItems(postData).then(res => {
                if(res.success) {
                    res.result.forEach(element => {
                        this.materialNumberOptions[element.value] = element.text
                    })
                }
            })
        }
    }
}
</script>