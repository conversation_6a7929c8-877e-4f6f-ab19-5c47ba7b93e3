<template>
  <div class="SaleFadadaSealPsn business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="detail"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
// import { postAction } from '@/api/manage'
import {  BUTTON_BACK} from '@/utils/constant.js'

export default {
    name: 'DetailSaleFadadaSealPsnModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            requestData: {
                detail: { url: '/electronsign/fadada/saleFadadaSealPsn/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            url: {
                detail: '/electronsign/fadada/saleFadadaSealPsn/queryById'
            }
        }
    },
    methods: {        
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEsNo`, '采购ELS号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'busAccount'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRRL_4460e249`, '采购名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'companyName'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpName'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hfftRID_5f2da41a`, '法大大机构ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'openCorpId',
                        disabled: true    
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeRL_27c5a0f3`, '印章名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealName'  
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeAc_27ca63e0`, '印章类型'),
                        fieldLabelI18nKey: '',
                        dictCode: 'fadadaCategoryType',
                        fieldName: 'categoryType',
                        disabled: true     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sealId`, '印章ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealId',
                        disabled: true     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberName'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRID_27b7ae68`, '员工ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberId',
                        disabled: true   
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbKI_2ecf20b9`, '授权时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'grantTime'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbvKKI_b38ae204`, '授权开始时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'grantStartTime',
                        required: '1'       
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbyWKI_c23545e5`, '授权结束时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'grantEndTime',
                        required: '1'     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbzE_2ed07606`, '授权状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'grantStatus_dictText',
                        required: '1'     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbKyjXKH10zs_67247343`, '授权链接(有效时长10分钟)'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealGrantUrl',
                        extend: {
                            linkConfig: {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeLjlbjELKy_3f7c9ca5`, '印章成员授权的页面链接'),
                                titleI18nKey: ''
                            },
                            exLink: true
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HVKI_30576cfa`, '更新时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'updateTime'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'image',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'),
                        fieldLabelI18nKey: '',
                        fieldName: 'picFileUrl'
                    }
                ]
            }
        }
    }
}
</script>
