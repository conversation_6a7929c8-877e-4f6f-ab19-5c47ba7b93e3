<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url"
      @loadSuccess="handleLoadSuccess"/>
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {getAction} from '@/api/manage'

export default {
    name: 'ViewPurcaseContractPromiseModal',
    mixins: [DetailMixin],
    data () {
        return {
            showRemote: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            confirmLoading: false,
            auditVisible: false,
            opinion: '',
            flowId: 0,
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZcVH_519d8b79`, '履约行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchasePromiseItemList',
                            columns: []
                        }
                    }
                    /* { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'purchaseRequest', callBack: this.uploadCallBack}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }*/

                ],
                formFields: [],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IZRL_2bea9b97`, '履约确认'),
                        type: 'primary',
                        click: this.confirm,
                        showCondition: this.showOptConditionBtn,
                        authorityCode: 'contractPromise#saleContractPromise:confirmed'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IZYM_2bed417f`, '履约退回'),
                        type: 'primary',
                        click: this.refund,
                        showCondition: this.showOptConditionBtn,
                        authorityCode: 'contractPromise#saleContractPromise:refund'
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                add: '/contract/saleContractPromise/add',
                edit: '/contract/saleContractPromise/edit',
                detail: '/contract/saleContractPromise/queryById',
                publishEvent: '/contract/saleContractPromise/publishEvent',
                upload: '/attachment/saleAttachment/upload'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_contractPromise_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.currentEditRow.sourceType = res.result.sourceType
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
        goBack () {
            this.$emit('hide')
        },
        loadSuccess () {
            this.pageConfig = getPageConfig() // eslint-disable-line
            this.handlePageData(this.pageConfig)
        },
        afterHandleData (data) {
            if (this.currentEditRow.sourceType != 'item') {
                data.groups.splice(1, 1)
            }
            this.init()
        },
        showOptConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            let promiseStatus = params.promiseStatus
            if (promiseStatus == '2') {
                return true
            } else {
                return false
            }
        },
        init () {
            if (this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }
        },
        refund () {
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractReturn`, '合同退回')
        },
        confirm () {
            let that = this
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractConfirmation`, '合同确认')
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_thisOperationWillConfirmContractConfirm`, '此操作将确认合同，是否确认?'),
                onOk: function () {
                    that.getData(that.url.confirm, null)
                }
            })
        },
        getData (url, param) {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (!param) {
                param = {}
            }
            param['id'] = params.id
            getAction(url, param).then(res => {
                if (res.success) {
                    this.form = res.result
                    this.auditVisible = false
                    this.opinion = ''
                    this.$message.success(res.message)
                    this.goBack
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.auditVisible = false
                this.init()
                this.$parent.modalFormOk()
            })
        }
    }
}
</script>
