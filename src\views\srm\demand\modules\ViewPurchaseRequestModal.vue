<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :url="url"
      :pageData="pageData"
    />
    <!-- 查看流程 -->
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"
    />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
    <RelationGraphModal
      v-if="modalVisibleDocket && currentEditRow.documentId"
      :modalVisibleDocket="modalVisibleDocket"
      :id="currentEditRow.documentId"
      :rootId="currentEditRow.id"
      @closeModalDocket="closeModalDocket"
      @onNodeClick="clickNode"
    >
    </RelationGraphModal>
  </div>
</template>

<script>
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'
import { httpAction, postAction, getAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
import RelationGraphModal from '@comp/RelationGraphModal'
import { cloneDeep } from 'lodash'
export default {
  name: 'ViewPurchaseRequestModal',
  mixins: [DetailMixin],
  components: {
    flowViewModal,
    RelationGraphModal
  },
  data() {
    return {
      modalVisibleDocket: false,
      cancelAdjustment: false,
      confirmLoading: false,
      flowId: 0,
      currentBasePath: this.$variateConfig['domainURL'],
      flowView: false,
      auditVisible: false,
      opinion: '',
      okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
      pageData: {
        form: {},
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionInfo`, '采购申请行信息'),
            groupType: 'item',
            groupCode: 'itemInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseRequestItemList',
              columns: []
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupCode: 'fileInfo',
            type: 'grid',
            custom: {
              ref: 'purchaseAttachmentList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'materialName', width: 120, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称') },
                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
              ],
              showOptColumn: true,
              optColumnList: [
                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
              ]
            }
          },
          {
            groupName: '退回历史记录',
            groupCode: 'purchaseRequestItemReturnHistoryList',
            type: 'grid',
            custom: {
              ref: 'purchaseRequestItemReturnHistoryList',
              columns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { field: 'requestNumber', title: '采购申请号', width: 120 },
                { field: 'itemNumber', title: '采购申请行号', width: 120 },
                { field: 'materialNumber', title: '物料编码', width: 120 },
                { field: 'materialName', title: '物料名称', width: 120 },
                { field: 'originalQuantity', title: '退回前数量', width: 120 },
                { field: 'partReturnQuantity', title: '退回数量', width: 120 },
                { field: 'backRemark', title: '退回意见', width: 120 },
                { field: 'createTime', title: '创建时间', width: 120 },
                { field: 'createBy', title: '创建用户', width: 120 }
              ]
            }
          }
        ],
        formFields: [],
        publicBtn: [
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'), type: 'primary', click: this.publishEvent, showCondition: this.showPublicConditionBtn },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'), type: 'primary', click: this.cancelAudit, id: 'cancelAudit', showCondition: this.showcCncelConditionBtn },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tFKm_2766a28a`, '单据联查'), showCondition: this.showDocket, click: this.viewDocket },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow, id: 'showFlow' },
          { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
        ]
      },
      url: {
        detail: '/demand/purchaseRequestHead/queryById',
        submitAudit: '/a1bpmn/audit/api/submit',
        public: '/demand/purchaseRequestHead/toDemandPool',
        cancelAudit: '/a1bpmn/audit/api/cancel'
      }
    }
  },
  computed: {
    fileSrc() {
      let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
      let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
      let templateVersion = this.currentEditRow.templateVersion
      let time = new Date().getTime()
      return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_purchaseRequest_${templateNumber}_${templateVersion}.js?t=` + time
    }
  },
  mounted() {
    let { query } = this.$route
    console.log(query)
    // 单据联查时，url上带有参数，通过此项判断是否为单据联查跳转
    if (this.allowEdit && !query.id) {
      let publicBtn = cloneDeep(this.pageData.publicBtn)
      publicBtn.splice(4, 0, { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), click: this.toEdit })
      this.$set(this.pageData, 'publicBtn', publicBtn)
    }
  },
  methods: {
    clickNode() {
      this.$store.dispatch('SetTabConfirm', false)
      this.modalVisibleDocket = false
    },
    closeModalDocket() {
      this.modalVisibleDocket = false
    },
    // 是否显示单据联查
    showDocket() {
      return this.currentEditRow.documentId
    },
    // 单据联查
    viewDocket() {
      this.modalVisibleDocket = true
    },

    /*showConditionBtn (id){
            let obj = this.currentEditRow
            if(obj == null){
                return false
            }
            let auditStatus = obj.auditStatus
            if(id == 'showFlow'){
                if(!auditStatus || auditStatus == '0'){
                    return false
                }
            }else if(id == 'cancelAudit'){
                if(auditStatus != '1'){
                    return false
                }
            }
            return true
        },*/
    preViewEvent(row) {
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    showcCncelConditionBtn() {
      let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
      let auditStatus = params.auditStatus
      let audit = params.audit
      let requestStatus = params.requestStatus
      if (requestStatus != '1' && audit == '1' && auditStatus == '1') {
        return true
      } else {
        return false
      }
    },
    publishEvent() {
      let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
      if (params.audit == '1' && params.auditStatus != '2') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentDocumentNeedsSubmittedApprovalSubmittedDemandPoolAfterApproval`, '当前单据需要提交审批,且审批通过后提交需求池！'))
        return
      }
      let that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitRequirements`, '提交需求'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitPurchaseRequisitionLineDemaIsureSubmit`, '将采购申请行提交需求池，是否确认提交?'),
        onOk: function () {
          postAction(that.url.public, params).then((res) => {
            if (res.success) {
              that.form = res.result
              that.$message.success(res.message)
              that.goBack()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
    showPublicConditionBtn() {
      let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
      let auditStatus = params.auditStatus
      let requestStatus = params.requestStatus
      let audit = params.audit
      if (requestStatus != 0 && requestStatus != 1 && requestStatus != 3 && ((audit == '1' && auditStatus == '2') || audit == '0')) {
        return true
      } else {
        return false
      }
    },
    showFlowConditionBtn() {
      let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
      let auditStatus = params.auditStatus
      let audit = params.audit
      if ((auditStatus == '0' && auditStatus != '') || audit == '0') {
        return false
      } else {
        return true
      }
    },
    showcAuditConditionBtn() {
      let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
      let auditStatus = params.auditStatus
      let requestStatus = params.requestStatus
      let audit = params.audit
      if (requestStatus != '1' && (auditStatus == '0' || auditStatus == '3') && audit == '1') {
        return true
      } else {
        return false
      }
    },
    submitAudit() {
      let formData = this.$refs.detailPage ? this.$refs.detailPage.form : {}
      if (formData.requestNumber == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
        return
      }
      let that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterSubmittingApprovalItcannotIsurSubmitApproval?`, '提交审批后将不能修改，是否确认提交审批?'),
        onOk: function () {
          that.postData(that.url.submitAudit)
        }
      })
    },
    cancelAudit() {
      let formData = this.$refs.detailPage ? this.$refs.detailPage.form : {}
      let auditStatus = formData.auditStatus
      if (auditStatus != '1') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvacCannotrevokedCurrenStatus`, '当前状态不能撤销审批！'))
        return
      }
      let that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
        onOk: function () {
          that.cancelAdjustment = true
          that.postData('/a1bpmn/audit/api/cancel')
        }
      })
    },
    postData(invokeUrl) {
      this.$refs.detailPage.confirmLoading = true
      let formData = this.$refs.detailPage ? this.$refs.detailPage.form : {}
      let param = {}
      param['businessId'] = formData.id
      param['rootProcessInstanceId'] = formData.flowId
      param['businessType'] = 'purchaseRequest'
      param['auditSubject'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请单号') + '：' + formData.requestNumber
      param['params'] = JSON.stringify(formData)
      let pro = new Promise((resolve, reject) => {
        httpAction(invokeUrl, param, 'post')
          .then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              if (formData.requestStatus == 6 && this.cancelAdjustment) {
                resolve()
                this.this.cancelAdjustment = false
              } else {
                this.$parent.cancelAuditCallBack(formData, res.result)
                this.init()
              }
            } else {
              this.$message.warning(res.message)
            }
          })
          .finally(() => {
            this.$refs.detailPage.confirmLoading = false
          })
      })
      pro.then(() => {
        getAction(`/budget/budgetManage/refundOfOccupiedAmount?purchaseRequestHeadId=${formData.id}`).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.$parent.cancelAuditCallBack(formData)
            this.init()
          } else {
            this.$message.warning(res.message)
          }
        })
      })
    },
    showFlow() {
      this.flowId = this.$refs.detailPage.form.flowId
      if (!this.flowId) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processCannotViewed`, '当前不能查看流程！'))
        return
      }
      this.flowView = true
    },
    closeFlowView() {
      this.flowView = false
    }
  }
}
</script>
