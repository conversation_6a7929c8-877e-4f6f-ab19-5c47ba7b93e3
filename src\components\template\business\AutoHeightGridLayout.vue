<template>
  <div>
    <slot></slot>
    <vxe-grid
      :height="gridHeight"
      :ref="group.groupCode"
      v-bind="currentGridConfig"
      :edit-config="group.editConfig"
      :edit-rules="group.editRules"
      :columns="group.columns"
      :data="gridData"
      :show-footer="showGridFooter"
      :footer-method="
        ({ columns, data }) => {
          return footerMethod({ group, columns, data })
        }
      "
      @checkbox-change="checkboxChangeDetail"
      @cell-click="cellClickEvent"
    >
      <!-- 继承原有的所有插槽 -->
      <template #code_editor_col_render="{ row, column }">
        <code-editor-model
          :value="row[column.property]"
          @handleSureClick="
            (content) => {
              row[column.property] = content
            }
          "
        ></code-editor-model>
      </template>
      
      <template #rich_editor_col_render="{ row, column }">
        <renderHtmlModal :content="row[column.property]" />
      </template>
      
      <template #renderDictLabel="{ row, column }">
        <span>{{ getDictLabel(row, column) }}</span>
      </template>
      
      <template #renderCurrency="{ row, column }">
        <span>{{ currencyFormat(row[column.property], column, group.columns) }}</span>
      </template>
      
      <template #toolbar_buttons>
        <business-button
          :buttons="group.externalToolBar"
          :groupCode="group.groupCode"
          :pageConfig="pageConfig"
          :currentEditRow="currentEditRow"
          isToolbarButtons
          v-bind="$attrs"
          v-on="$listeners"
        />
      </template>
      
      <template #grid_opration="{ row, column }">
        <div v-if="group.extend && group.extend.optColumnList">
          <span
            v-for="(opt, optIndex) in group.extend.optColumnList"
            :key="'opt_' + row.id + '_' + optIndex"
          >
            <a
              :title="opt.title"
              style="margin: 0 4px"
              :disabled="opt.disabled"
              v-show="optionHandle({ opt, row, column })"
              @click="() => { optColumnFuntion(opt, row, column, group.columns) }"
            >{{ opt.title }}</a>
          </span>
        </div>
      </template>
      
      <template #bottom>
        <div class="summary-message" v-if="group.total.totalValue">
          <span class="summary-message-content">
            {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }} 
            {{ $srmI18n(`${$getLangAccount()}#${group.i18n_title_generalSummary}`, '总汇总') }}：
            <span class="total-num">{{ group.total.totalValue }}</span>
          </span>
        </div>
      </template>

      <template #empty>
        <m-empty
          :displayModel="displayModel"
          :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
        />
      </template>
    </vxe-grid>
  </div>
</template>

<script>
import BusinessButton from './components/BusinessButton'
import RichEditorModel from '@comp/richEditorModel/RichEditorModel'
import { getObjType, isPromise } from '@/utils/util'
import { currency } from '@/filters'

export default {
  name: 'AutoHeightGridLayout',
  inject: ['tplRootRef'],
  components: {
    RichEditorModel,
    BusinessButton
  },
  props: {
    // 传入归属方busAccount
    busAccount: {
      required: true,
      type: String,
      default: null
    },
    group: {
      type: Object,
      required: true,
      default() {
        return {}
      }
    },
    loadData: {
      type: Array,
      default() {
        return []
      }
    },
    pageConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    currentEditRow: {
      type: Object,
      default() {
        return {}
      }
    },
    gridCustomConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    gridConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    displayModel: {
      type: String,
      default: 'tab'
    },
    gridFooterMethod: {
      type: Function,
      default: null
    },
    showGridFooter: {
      type: Boolean,
      default: false
    },
    // 新增：自动高度配置
    autoHeightConfig: {
      type: Object,
      default() {
        return {
          enabled: false,
          rowHeight: 36,
          headerHeight: 50,
          maxHeight: 600,
          minHeight: 150,
          paddingHeight: 20
        }
      }
    }
  },
  data() {
    return {
      gridData: this.loadData || []
    }
  },
  computed: {
    currentGridConfig() {
      return this.gridConfig
    },
    
    // 计算表格高度
    gridHeight() {
      // 检查是否启用自动高度
      const autoConfig = this.group.extend?.autoHeightByData || this.autoHeightConfig.enabled
      
      if (autoConfig) {
        return this.calculateAutoHeight()
      }
      
      // 原有逻辑：动态高度判断
      if (this.isDynamics()) {
        return '100%'
      }
      
      return '334px'
    }
  },
  watch: {
    loadData: {
      immediate: true,
      handler: function (val) {
        this.gridData = val
        this.loadGridData()
      }
    }
  },
  methods: {
    // 计算自动高度
    calculateAutoHeight() {
      const dataLength = this.gridData.length
      const config = {
        rowHeight: 36,
        headerHeight: 50,
        maxHeight: 600,
        minHeight: 150,
        paddingHeight: 20,
        ...this.autoHeightConfig,
        ...(this.group.extend || {})
      }
      
      if (dataLength === 0) {
        return config.minHeight + 'px'
      }
      
      // 计算高度：数据行高度 + 表头高度 + 内边距
      let calculatedHeight = dataLength * config.rowHeight + config.headerHeight + config.paddingHeight
      
      // 应用最大最小高度限制
      if (calculatedHeight > config.maxHeight) {
        calculatedHeight = config.maxHeight
      }
      if (calculatedHeight < config.minHeight) {
        calculatedHeight = config.minHeight
      }
      
      console.log('自动高度计算:', {
        dataLength,
        calculatedHeight,
        config
      })
      
      return calculatedHeight + 'px'
    },
    
    // 原有的动态判断逻辑
    isDynamics() {
      if(
        this.group.groupCode === 'purchaseBiddingHeadList' || 
        this.group.groupCode === 'purchaseBiddingItemList' || 
        this.group.groupCode === 'recAcceptReturnList' || 
        this.group.groupCode === 'recAdditionalChargesList' || 
        this.group.groupCode === 'recChargeList' || 
        this.group.groupCode === 'prePaymentWriteOffList' || 
        this.group.groupCode === 'invoiceList' || 
        this.group.groupCode === 'supplierContactsInfoList' || 
        this.group.groupCode === 'supplierAddressInfoList'
      ) {
        return true;
      }
      return false;
    },
    
    // 以下方法继承自原 DetailGridLayout
    cellClickEvent(info) {
      const { row, rowIndex, column, columnIndex } = info
      this.$emit('cell-click', { row, rowIndex, column, columnIndex })
    },
    
    footerMethod({ group, columns, data }) {
      let footerData = []
      if (columns) {
        footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '合计'
            }
            return null
          })
        ]
      }
      if (this.gridFooterMethod && getObjType(this.gridFooterMethod) === 'function') {
        return this.gridFooterMethod({ group, columns, data })
      } else {
        return footerData
      }
    },
    
    optionHandle(data) {
      let { opt, row } = data
      let hide = opt.hide
      let response = false
      if (hide && typeof hide === 'function') {
        response = hide(row)
        if (isPromise(response)) {
          response.then(() => { response = true }, () => { response = false })
        }
      } else if (typeof hide !== 'undefined') {
        response = !!hide
      } else {
        response = false
      }
      return !response
    },
    
    optColumnFuntion(opt, row, col, columns) {
      opt.click && opt.click(this, row, col, this.tplRootRef, { columns })
    },
    
    loadGridData() {
      let that = this
      this.$nextTick(() => {
        if (that.$refs[that.group.groupCode]) {
          that.$refs[that.group.groupCode].loadData(that.gridData)
        }
      })
    },
    
    getDictLabel(row, column) {
      return row[column.property + '_dictText'] || row[column.property]
    },
    
    currencyFormat(value, info, columns = []) {
      if (!value) {
        return ''
      }
      let extend = {}
      for (let item of columns) {
        if (info.property === item.field) {
          extend = item.extend || {}
          break
        }
      }
      
      let symbol = (extend && extend.symbol) || ''
      let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
      return currency(value, symbol, decimals)
    },
    
    checkboxChangeDetail({ checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
      this.tplRootRef.checkboxChange && this.tplRootRef.checkboxChange({ checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event })
    }
  }
}
</script>

<style lang="less" scoped>
.summary-message {
  height: 14px;
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.summary-message-content {
  flex-grow: 1;
  font-weight: bolder;

  .total-num {
    font-size: 16px;
    color: red;
  }
}

:deep(.vxe-body--column.required-col) {
  background-color: #f6ebe8;
  background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
}

:deep(.vxe-header--column.required-col) {
  background-color: #f6ebe8;
  background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
}
</style>
