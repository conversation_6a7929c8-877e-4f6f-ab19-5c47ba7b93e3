<template>
  <div>
    <div class="display-box">
      <a-tooltip placement="top"> 
        <template
          slot="title"
          v-if="code && showTooltip">
          <span v-html="code"></span>
        </template>
        <div
          class="dispaly-content ant-input ant-input-disabled"
          v-html="code"></div>
      </a-tooltip>
      <a-icon
        v-if="!disabled"
        class="opt-btn"
        type="edit"
        :style="{cursor: 'pointer', color: defaultColor }"
        @click="editorModalFlag= true" />
    </div>
    <a-modal
    v-drag    
      forceRender
      :visible="editorModalFlag"
      :maskClosable="false"
      :title="$srmI18n(`${this.$getLangAccount()}#i18n_title_richEditor`, '富文本编辑器')"
      :width="800"
      @ok="handleSureClick"
      @cancel="editorModalFlag=false">
      <j-editor
        v-if="editorModalFlag"
        v-model="code">
      </j-editor>
    </a-modal>
  </div>
</template>

<script>
import { Icon } from 'ant-design-vue'
import JEditor from '@/components/els/JEditor'
import { DEFAULT_COLOR } from '@/store/mutation-types'
export default {
    name: 'RichEditorModel',
    components: {
        JEditor,
        AIcon: Icon
    },
    props: {
        // value 必传，并且是字符串
        value: {
            type: String,
            default: ''
        },
        // codemirro的配置参数
        options: {
            type: Object,
            default: null
        },
        showTooltip: {
            type: Boolean,
            default: true
        },
        // 
        disabled: {
            type: Boolean,
            default: null
        }
    },
    data () {
        return {
            code: '',
            editorModalFlag: false
        }
    },
    computed: {
        // 获取项目的主题色
        defaultColor () {
            return this.$ls.get(DEFAULT_COLOR)
        }
    },
    watch: {
        value: {
            immediate: true,
            handler (val) {
                this.code = val? val: ''
            }
        }
    },
    methods: {
        changeCodeMirror (val) {
            this.code = val
        },
        handleSureClick () {
            this.editorModalFlag = false
            // 有传入的回写值
            this.$emit('handleSureClick', this.code)
        }
    }
}
</script>
<style lang="less" scoped>
.display-box {
  position: relative;
  .dispaly-content {
      padding-right: 24px;
      display: inline-block;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      :deep(p , div){
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      :deep(p){
        width: 100%;
      }
  }
  .opt-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 14px;
    height: 14px;
    display: inline-block;
    background-color: rgba(0,0,0,0);
  }
}
</style>