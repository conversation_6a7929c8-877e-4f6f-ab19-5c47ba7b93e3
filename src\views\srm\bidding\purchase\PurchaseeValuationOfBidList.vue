<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage"
      ref="listPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"
    />
  </div>
</template>

<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import layIM from '@/utils/im/layIM.js'
import {postAction} from '@/api/manage'

import { SET_VUEX_CACHE_ROW, SET_VUEX_CURRENT_EDIT_ROW, SET_CACHE_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    mixins: [ListMixin],
    components: {},
    data () {
        return {
            regretValue: 0,
            currentRow: {},
            pageData: {
                businessType: 'bidding',
                form: {
                    ebiddingNumber: ''
                },
                button: [
                    //{label: '报价历史', icon: 'snippets', clickFn: this.showHistoryList},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tenderNumber`, '招标单号'),
                        fieldName: 'biddingNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTenderNumberTips`, '请输入招标单号')
                    }
                ],
                optColumnWidth: 320,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vtUB_391c24cb`, '立即评标'), clickFn: this.toEvaluation, allow: this.allowEvaluation, authorityCode: 'bidding#valuationOfBid:evaluation'},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBmA_411c3bc9`, '评标查看'), clickFn: this.toTender, authorityCode: 'bidding#valuationOfBid:queryById'}
                ]
            },
            tabsList: [],
            url: {
                list: '/bidding/purchaseBiddingHead/queryeValuationOfBidList',
                columns: 'PurchaseValuationOfBid',
                excelCode: 'ebidding'
            },
            countTabsUrl: '/bidding/purchaseBiddingHead/queryEvaBidTabsCount'
        }
    },
    mounted () {
        this.serachCountTabs(this.countTabsUrl)
        // this.serachTabs('srmBiddingStatus', 'biddingStatus')
    },
    watch: {
        '$route.query': {
            handler () {
                this.init()
            }
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        })
    },
    methods: {
        // 缓存当前行数据
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW,
            setVuexCacheRow: SET_VUEX_CACHE_ROW,
            setCacheVuexCurrentEditRow: SET_CACHE_VUEX_CURRENT_EDIT_ROW
        }),
        toTender (row) {
            let _t = +new Date()
            let query = {_t}
            let name = ''
            this.setVuexCurrentEditRow({})
            if (row.projectId) {
                name = 'project_announcement'
                this.setVuexCacheRow({ row: { id: row.projectId } })
                query['id'] = row.id
            } else {
                name = 'announcement'
                this.setCacheVuexCurrentEditRow({ row })
            }
            const routeUrl = this.$router.resolve({
                name,
                query
            })
            window.open(routeUrl.href, '_blank')
        },
        // 跳转至评标页面，区分线下评标、或线上评标
        toEvaluation (row) {
            let name = ''
            let _t = +new Date()
            let query = {_t}
            this.setVuexCurrentEditRow({})
            if (row.projectId) {
                name = row.bidEvaluationWay === '0' ? 'project_offlineEvaluation' : 'project_bidResultList'
                this.setVuexCacheRow({ row: { id: row.projectId } })
                query['id'] = row.id
            } else {
                name = row.bidEvaluationWay === '0' ? 'offlineEvaluation' : 'bidResultList'
                this.setCacheVuexCurrentEditRow({ row })
            }
            const routeUrl = this.$router.resolve({
                name,
                query
            })
            window.open(routeUrl.href, '_blank')
        },
        allowEvaluation (row) {
            let biddingStatus = row.biddingStatus
            if (biddingStatus == '3') {
                return false
            }
            return true
        },
        init () {
            if (this.$route.query.id && this.$route.path.includes('PurchaseeValuationOfBidList')){
                this.pageData.form.id = this.$route.query.id
                this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
                this.$refs.listPage.initColumns()
                this.$refs.listPage.loadData()
                this.$refs.listPage.columnDrop()
            } else {
                this.pageData.form.id = ''
                this.countTabsUrl = '/bidding/purchaseBiddingHead/queryEvaBidTabsCount'
            }
        },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.templateNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'PurchaseeValuationOfBid', url: this.url || '', recordNumber})
        },
        allowChat (row){
            return row.biddingStatus === '0'
        },
        getUrlParam (){
            let templateNumber = this.$route.query.templateNumber
            let templateVersion = this.$route.query.templateVersion
            let busAccount = this.$route.query.busAccount
            let id = this.$route.query.id
            if(templateNumber && templateVersion && id && busAccount){
                let row = {}
                row['templateNumber']=templateNumber
                row['templateVersion']=templateVersion
                row['id']=id
                row['busAccount']=busAccount
                this.currentEditRow = row
                this.showDetailPage = true
            }
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (row){
            this.currentEditRow = row
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        allowTender (row) {
            return row.biddingStatus == '0'
        },
        allowEdit (row) {
            //审批中禁用
            if(row.auditStatus == '1'){
                return true
            }
            //新建不禁用
            return row.biddingStatus != '0'
        },
        allowCreateNew (row){
            //竞价结束状态5
            return row.ebiddingStatus != '5'
        },
        allowRegret (row){
            let reusltAudit = row.reusltAudit
            if(reusltAudit == '1'){
                //已授标状态6 && 审批通过2
                return row.ebiddingStatus != '6' && row.reusltAuditStatus != '2'
            }else {
                //已授标状态6
                return row.ebiddingStatus != '6'
            }
        },
        createNew (row){
            this.currentEditRow = row
            this.showNewRoundPage = true
        },
        hideNewRoundPage (){
            this.showNewRoundPage = false
        },
        showHistoryList (){
            this.$store.dispatch('SetTabConfirm', false)
            this.$router.push({
                path: '/ebidding/purchaseEbiddingHisList'
            })
        },
        showRegret (row){
            this.regretVisible = true
            this.currentRow = row
        },
        handleRegret (){
            let headId = this.currentRow.id
            postAction(this.url.regret, {id: headId, regretFlag: this.regretValue}).then(res => {
                if(res.success) {
                    this.regretVisible = false
                    this.$message.success(res.message)
                    this.$refs.listPage.loadData()
                }else {
                    this.$message.warning(res.message)
                }
            })
        }
    },
    created () {
        this.getUrlParam()
        if (this.$route.query.id) {
            this.pageData.form.id = this.$route.query.id
            this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
        } 
    }
}
</script>
<style lang="less" scoped>
.radio-wrap {
    display: block;
    height: 30px;
}
</style>