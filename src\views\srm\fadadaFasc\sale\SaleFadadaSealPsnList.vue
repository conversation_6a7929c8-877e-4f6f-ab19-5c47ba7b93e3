<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <EditSaleFadadaSealPsn-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideEditPage" />
    <ViewSaleFadadaSealPsn-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import EditSaleFadadaSealPsnModal from './modules/EditSaleFadadaSealPsnModal'
import ViewSaleFadadaSealPsnModal from './modules/ViewSaleFadadaSealPsnModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {httpAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EditSaleFadadaSealPsnModal,
        ViewSaleFadadaSealPsnModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'fadada',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeRLjRcR_bbc12983`, '印章名称/员工姓名'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeRLjRcR_bbc12983`, '印章名称/员工姓名')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'fadada#saleFadadaSealPsn:add', icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'fadada#saleFadadaSealPsn:view', clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'fadada#saleFadadaSealPsn:edit', clickFn: this.handleEdit, allow: this.showEditCondition},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'fadada#saleFadadaSealPsn:delete', clickFn: this.handleDelete, allow: this.showDelCondition},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 300
            },
            url: {
                list: '/electronsign/fadada/saleFadadaSealPsn/list',
                add: '/electronsign/fadada/saleFadadaSealPsn/add',
                delete: '/electronsign/fadada/saleFadadaSealPsn/delete',
                deleteBatch: '/electronsign/fadada/saleFadadaSealPsn/deleteBatch',
                cancel: '/electronsign/fadada/saleFadadaSealPsn/cancel',
                columns: 'saleFadadaSealPsnList'
            }
        }
    },
    methods: {
        handleChidCallback (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleAdd () {          
            this.currentEditRow = {}
            this.showDetailPage = false
            this.showEditPage = true
        },
        handleView (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
        },
        // showCancelCondition (row){
        //     return false
        // },
        showEditCondition (row) {
            if(row.grantStatus==='effective'){
                return true
            }
            return false
        },
        showDelCondition (row) {
            if(row.grantStatus==='effective'||row.grantStatus==='to_be_effective'){
                return true
            }
            return false
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (){
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        cancel (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        }
    }
}
</script>