<template>
  <div class="SupplierInformationList">
    <titleTrtl class="margin-b-10">
      <div v-if="msgReady">
        <div class="display_inline_block marging_right_20">{{ $srmI18n(`${$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息') }}</div>
        <div class="display_inline_block marging_right_10">
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sULW_2741ed82`, '参与人数') }}：</span>
          <span>{{ formData.participantsTotal }}</span>
        </div>

        <div
          class="display_inline_block marging_right_10"
          v-if="this.subpackage.checkType == '0'">
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_UUdBLW_79a45646`, '预审应标人数') }}：</span>
          <span>{{ formData.preBiddersNumber }}</span>
        </div>
        <div
          class="display_inline_block marging_right_10"
          v-if="this.subpackage.checkType == '0'">
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_UULdBLW_2ff541d6`, '预审未应标人数') }}：</span>
          <span>{{ formData.preNotBiddersNumber }}</span>
        </div>

        <div class="display_inline_block marging_right_10">
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_dBLW_2c8e9929`, '应标人数') }}：</span>
          <span v-if="this.subpackage.processType == '1'">{{ formData.biddersNumber }}</span>
          <span v-else>{{ formData.resultBiddersNumber }}</span>
        </div>
        <div class="display_inline_block marging_right_10">
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_LdBLW_da535b53`, '未应标人数') }}：</span>
          <span v-if="this.subpackage.processType == '1'">{{ formData.notBiddersNumber }}</span>
          <span v-else>{{ formData.resultNotBiddersNumber }}</span>
        </div>
      </div>
      <template slot="right">
        <a-tooltip placement="bottom">
          <template
            slot="title"
            v-if="riskHelpText">
            <span v-html="riskHelpText"></span>
          </template>
          <a-button
            @click="checkRisk"
            type="primary"
            icon="question-circle-o"
            class="marging_right_10">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_LBJi_2919d4bc`, '围标探测') }}</a-button>
        </a-tooltip>
        <a-button @click="refresh">{{ $srmI18n(`${$getLangAccount()}#i18n_field_refresh`, '刷新') }}</a-button>
      </template>
    </titleTrtl>
    <div>
      <listTable
        ref="listTable"
        :pageData="pageData"
        :fromSourceData="formData.tenderProjectSupplier"
        :statictableColumns="tableColumns"
        :showTablePage="false"
      />
    </div>
    <a-modal
      v-drag    
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果')"
      v-model="riskShow"
      @ok="confirm"
      @cancel="confirm"
    >
      <div
        v-for="(data, index) in riskList"
        :key="data">
        <p v-if="data.type==='0'">{{ index + 1 }}、{{ data.sourceName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KCbVeMKdeyR_3727867`, '在股权穿透存在相同结果') }}：{{ data.result
        }}</p>
        <p v-if="data.type==='1'">{{ index + 1 }}、{{ data.sourceName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KeslILMKdeyR_85d14c24`, '在最终受益人存在相同结果') }}：{{ data.result
        }}</p>
        <p v-if="data.type==='2'">{{ index + 1 }}、{{ data.sourceName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KKVLMKdeyR_46ebba36`, '在实控人存在相同结果') }}：{{ data.result
        }}</p>
        <p
          :key="index"
          v-if="data.type==='4'">{{ index + 1 }}、{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_KsuWWMKdeyR_17c9510e`, '在报价ip存在相同结果') }}：{{ data.result
          }}</p>
        <p 
          v-if="data.type === '5' || data.type === '6' || data.type === '7' || data.type === '8'">{{ index + 1 }}、{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_MKW_162724a`, '存在：') }}{{ data.result }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_RH_a59e8`, '关系') 
        }}</p>
      </div>
    </a-modal>
  </div>
</template>
<script>
import titleTrtl from '../../components/title-crtl'
import listTable from '../../components/listTable'
import { mapState, mapMutations } from 'vuex'
import { getAction } from '@/api/manage'

export default {
    inject: ['currentSubPackage'],
    props: {
        formData: {
            default: () => {
                return {}
            },
            type: Object
        },
        propOfCheckType: {
            default: () => {
                return ''
            },
            type: [String, Number]
        }
    },
    components: {
        titleTrtl,
        listTable
    },
    computed: {
        subpackage (){
            return this.currentSubPackage()
        },
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        })
    },
    data () {
        return {
            riskShow: false,
            riskList: [],
            msgReady: false,
            riskHelpText: `1、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yVLIxDjWVOSmhyR_c7532315`, '接口为异步调用，请静候查询结果')}<br> 2、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lnICdjWRWefRdXWRxOfUWWu_f2d40ee9`, '受第三方应用限制，最大供应商数量不能大于20家')}`,
            tableColumns: [
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'risk', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_suppliersRisk`, '供应商风险'), clickFn: this.handleRisk}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            fromSourceData: []
            // url: {
            //     detail: '/tender/purchaseTenderClarificationInfo/queryById'
            // }
        }
    },
    methods: {
        confirm () {
            this.riskShow = false
        },
        refresh () {
            this.$emit('refresh')
        },
        checkRisk () {
            const url = '/tender/purchase/supplierTenderProjectMasterInfo/queryRisk?subpackageId=' + this.subpackage.id
            this.$refs.listTable.loading = true
            getAction(url)
                .then(res => {
                    if (res && res.success) {
                        if (res.result && res.result.length) {
                            this.riskList = res.result
                            this.riskShow = true
                        } else {
                            this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IiRCISReWF_3b256afc`, '检测公司间无共同数据'))
                        }
                    }else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.$refs.listTable.loading = false
                    // this.init()
                })
        },
        // 供应商风险页面跳转
        handleRisk (row) {
            // let {elsAccount} = row.elsAccount
            console.log('rowrowrow', row)
            sessionStorage.setItem('cache_elsAccout', row.supplierAccount)
            let params = { linkFilter: true, backToHistory: true}
            this.$router.push({ name: 'SupplierVenture', query: params })
            // this.$router.push({ name: 'SupplierVenture'})
        }
    },
    created (){
        console.log('this.subpackage', this.subpackage)
        // 后审一步法,processType == '0' resultResponseTime
        // 后审两步法第一步,processType == '1' responseTime
        let responseTime = this.subpackage.processType == '0' ? 'resultResponseTime' :  'responseTime'
        let responseStatus = this.subpackage.processType == '0' ? 'resultResponseStatus_dictText' :  'responseStatus_dictText'
        if(this.subpackage.checkType == '0'){
            this.tableColumns=[
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUddzE_751366cf`, '预审响应状态'),
                    'field': 'preResponseStatus_dictText'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUddKI_75121182`, '预审响应时间'),
                    'field': 'preResponseTime'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'),
                    'field': responseStatus
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'),
                    'field': responseTime
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                    'field': 'contacts'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
                    'field': 'contactsPhone'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ]
        }else{
            this.tableColumns=[{
                'type': 'seq',
                'width': 50,
                'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
            },
            {
                'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                'field': 'supplierName'
            },
            {
                'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'),
                'field': responseStatus
            },
            {
                'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'),
                'field': responseTime
            },
            {
                'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                'field': 'contacts' 
            },
            {
                'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
                'field': 'contactsPhone'
            },
            {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                width: 180,
                fixed: 'right',
                slots: { default: 'grid_opration' }
            }
            ]
        }
        this.msgReady=true
    }
}
</script>
<style lang="less" scoped>
.SupplierInformationList{
    margin-top: 20px;
}
.margin-b-10{
    margin-bottom: 5px;
}
.marging_right_20{
    margin-right: 20px
}
.marging_right_10{
    margin-right: 10px
}
.display_inline_block{
    display: inline-block;
}
</style>


