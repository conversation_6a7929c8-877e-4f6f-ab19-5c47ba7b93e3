<template>
  <div>
    <div
      class="page-container"
      v-show="!showDetail">
      <a-spin 
        :spinning="confirmLoading">
        <div>
          <titleCrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_suVH_2e09d920`, '报价信息') }}</span>
            <template slot="right" >
              <a-button
                type="primary"
                style="margin-right: 10px"
                @click="getData">{{ $srmI18n(`${$getLangAccount()}#i18n_title_refresh`, '刷新') }}</a-button>
            </template>
          </titleCrtl>
          <listTable 
            ref="listTable"
            setGridHeight="auto"
            :fromSourceData="fromSourceData"
            :pageData="pageData"
            :showTablePage="false"
            :statictableColumns="statictableColumns"
          />
        </div>
      </a-spin>
    </div>
    <multipleQuotes
      v-if="showDetail"
      :currentEditRow="currentEditRow"
      @hidden="hidden"></multipleQuotes>
  </div>
</template>
<script lang="jsx">
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
import multipleQuotes from './multipleQuotes'
export default {
    mixins: [baseMixins],
    data () {
        return {
            pageType: 'edit',
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, allow: this.allowView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_su_c40f2`, '报价'), clickFn: this.handleView, allow: this.allowEdit}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            statictableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PVyO_43cfa5f6`, '邀请阶段'),
                    'field': 'processType',
                    slots: {
                        default: ({row}) => {
                            let str = '评审'
                            if (row.processType == '1') {
                                if (row.currentStep == '0') {
                                    str = '第一步评审'
                                } else {
                                    str = '第二步评审'
                                }
                            }
                            return [<span>{str}</span>]
                        }
                    }
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suVm_2e1195e5`, '报价轮次'),
                    'field': 'stage'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suyRKI_db7df728`, '报价截止时间'),
                    'field': 'quotedPriceEndTime'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suzE_2e0e107d`, '报价状态'),
                    'field': 'status_dictText'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suKI_2e0cbb30`, '报价时间'),
                    'field': 'quotedPriceTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 200,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            tableHeight: '',
            showDetail: false,
            fromSourceData: [],
            confirmLoading: false,
            currentEditRow: {},
            timestamp: null,
            url: {
                list: '/tender/sale/tenderEvaQuotedPriceHead/list',
                quotedPriceItemId: '/tender/sale/tenderEvaQuotedPriceHead/quotedPriceItemId'
            }
        }
    },
    components: {
        titleCrtl,
        listTable,
        multipleQuotes
    },
    computed: {
        subId () {
            return this.subpackageId()
        }
    },
    methods: {
        allowEdit (row) {
            if (new Date(row.quotedPriceEndTime).getTime() > this.timestamp) {
                return row.status != 0
            } else {
                return true
            }
        },
        allowView (row) {
            return row.status == 0
        },
        getData () {
            this.confirmLoading = true
            getAction(this.url.list, { subpackageId: this.subId, quotedType: '0'}).then(res => {
                if (res.success) {
                    this.fromSourceData = res.result || []
                    this.timestamp = res.timestamp
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleView (row) {
            this.currentEditRow = row
            this.showDetail = true
        },
        hidden () {
            this.currentEditRow = null
            this.showDetail = false
        }
    },
    created () {
        this.getData()
    }
}
</script>
