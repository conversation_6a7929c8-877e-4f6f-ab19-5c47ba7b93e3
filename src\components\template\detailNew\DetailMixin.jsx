import DetailLayout from './DetailLayout.vue'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import { GRID_OPTION_ROW } from '@/utils/constant.js'
import {formatFloat, currency} from '@/filters'
import LadderPrice from '@comp/LadderPrice'
import rebateLadderPrice from '@comp/rebateLadderPrice'

export const DetailMixin = {
    provide () {
        return {
            tplRootRef: this
        }
    },
    components: {
        DetailLayout,
        LadderPrice,
        rebateLadderPrice,
        remoteJs: {
            render (createElement) {
                var self = this
                return createElement('script', {
                    attrs: {type: 'text/javascript', src: this.src, async: true},
                    on: {
                        load: function (event) {
                            self.$emit('load', event)
                        },
                        error: function (event) {
                            self.$emit('error', event)
                        }
                    }
                })
            },
            props: {
                src: {type: String, required: true}
            }
        }
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
            }
        },
        allowEdit: {
            type: Object,
            default: false,
        },
    },
    data () {
        return {
            pageConfig: {}
        }
    },
    created () {
        // 自定义组装数据时通过调用传入的方法组装
        if (!this.fileSrc) {
            this.handleFromSourceData()
        }
    },
    mounted () {
        this.initBatchDownloadBtn()
        if (this.isAudit) { // 是否审批页面
            this.init()
        }
    },
    computed: {
        isAudit () {
            return this.$refs.detailPage?.isAudit
        }
    },
    methods: {
        init () {
            if (this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id, this.handleAfterDealSource)
            }
        },
        handleAfterDealSource (result, pageData) {
            console.log('[handleAfterDealSource]', result, pageData)
        },
        //批量下载 注入
        initBatchDownloadBtn () {
            this.pageData.groups.forEach(group => {
                new BatchDownloadBtn().handelCreateBatchDownload(group)
            })
        },
        //配置加载成功执行
        loadSuccess () {
            this.pageConfig = getPageConfig() // eslint-disable-line
            this.handlePageData(this.pageConfig)
            this.init()
        },
        //配置加载异常执行
        loadError (err) {
            console.log(err)
        },
        // 提取表格编辑配置
        // extractGridEditConfig (groups) {
        //     const that = this
        //     let editConfig = null
        //     groups.forEach((groupItem)=> {
        //         // 表格编辑规则判断字段是配置项的值为gridEditConfig唯一，groupCode可任意取值，方便扩展gridEditConfig
        //         if (groupItem.groupType==='gridEditConfig' && groupItem.groupCode==='gridEditConfig') {
        //             if (groupItem.extend) {
        //                 editConfig= {}
        //                 editConfig.trigger= groupItem.extend.editConfig.trigger || 'click'
        //                 editConfig.mode= groupItem.extend.editConfig.mode || 'cell'
        //                 editConfig.showStatus= groupItem.extend.editConfig.showStatus || true
        //                 if (groupItem.extend.editConfig.activeMethod) {
        //                     editConfig.activeMethod= (gridData)=> { return that.gridActiveMethod(gridData, groupItem.extend.editConfig.activeMethod) }
        //                 }
        //             }
        //         }
        //     })
        //     return editConfig
        // },
        // 处理当前pageData数据和配置数据
        handlePageData (data) {
            this.beforeHandleData(data)
            this.pageData.groups = data.groups.concat(this.pageData.groups)
            // 区分表格行的编辑规则和表头分组
            // let gridLineRule = this.pageData.groups.filter((groupItem)=> {
            //     if (groupItem.groupType==='gridEditConfig') {
            //         return true
            //     }
            // })
            this.pageData.groups = this.pageData.groups.filter(groupItem => groupItem.groupType !== 'gridEditConfig')
            this.pageData.formFields = data.formFields
            let fields = []
            this.pageData.groups.forEach(item => {
                //行表列信息，目前固定为itemInfo
                if (item.groupType === 'item') {
                    item.type = 'grid'
                    if (!item.custom) {
                        item.custom = {}
                    }
                    let columns = data.itemColumns.filter(column => {
                        if (item.groupCode === 'itemInfo') {
                            return !column.groupCode
                        }
                        return column.groupCode == item.groupCode
                    })
                    columns.forEach(sub => {
                        // 隐藏域处理
                        if (sub.fieldType == 'hiddenField' || sub.fold === '1') {
                            sub.visible = false
                        }
                    })
                    if (!item.custom.ref) {
                        item.custom.ref = item.groupCode
                    }
                    if (!item.custom.notShowTableSeq) {
                        item.custom.columns = [{type: 'checkbox', width: 40}, {type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')}].concat(columns)
                    } else {
                        item.custom.columns = [{type: 'checkbox', width: 40}].concat(columns)
                    }

                    this._addFoldOptColumn(columns, item)
                }
                //表单信息根据分组编码分类
                if (item.type !== 'grid') {
                    fields = data.formFields.filter(item2 => item.groupCode === item2.groupCode)
                    // 过滤配置为图片类型的字段，并塞在数组最后面
                    let imageFields = fields.filter(n => n.fieldType === 'image')
                    let textAreaFields = fields.filter(n => n.fieldType === 'textArea')
                    let otherFields = fields.filter(n => n.fieldType !== 'image'&& n.fieldType !== 'textArea')
                    fields = otherFields.concat(imageFields, textAreaFields)

                    item.custom = {
                        formFields: fields,
                        form: {},
                        validateRules: {}
                    }
                } else {
                    // item.custom.editConfig= this.extractGridEditConfig(gridLineRule)
                    item.custom.columns.forEach(sub => {
                        // 默认设置slot为空
                        // sub.slots = Object.assign({}, sub.slots)
                        // 隐藏域处理
                        if (sub.fieldType == 'hiddenField') {
                            sub.visible = false
                        }
                        // 富文本编辑器
                        if (sub.fieldType == 'richEditorModel') {
                            sub.slots= Object.assign({}, sub.slots, { default: 'rich_editor_col_render'})
                        }
                        // 货币千分位
                        if (sub.fieldType == 'currency') {
                            sub.slots = Object.assign({}, sub.slots, {
                                default: ({row, column}, h) => {
                                    let extend = sub.extend || {}
                                    let symbol = extend && extend.symol || ''
                                    let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
                                    return [
                                        <span>{currency(row[column.property], symbol, decimals)}</span>
                                    ]
                                }
                            })
                        }
                        // 阶梯报价
                        if (sub.fieldType == 'ladderPrice') {
                            sub.slots = this.ladderPriceModalSlots(sub)
                        }
                        if (sub.fieldLabelI18nKey) {
                            sub.title = this.$srmI18n(`${this.$getLangAccount()}#${sub.fieldLabelI18nKey}`, sub.title)
                        }
                        if (sub.dictCode) {
                            sub.field =  sub.field.includes('_dictText') ? sub.field: sub.field += '_dictText'
                        }

                        if(sub.fixType) {//add 新增编辑和详情冻结功能
                            sub.fixed = sub.fixType=='unfrozen'?'':sub.fixType
                        }else{
                            if(sub.fixed=='unfrozen') delete   sub.fixed
                        }
                        if(sub.extend && sub.extend.linkConfig ){
                            sub.slots = this.linkModalSlots(sub)
                        }
                        // delete sub.slots
                        if (sub.slots) {
                            sub.slots.header = ({column}) => {
                                const flag = !!(sub.helpText)
                                const dom = flag
                                    ? (<vxe-tooltip content={sub.helpText}>
                                        <span style="display: flex; alignItems: center;">
                                            <i class="vxe-icon--question"></i>
                                            <span style="marginLeft: 6px;">{column.title}</span>
                                        </span>
                                    </vxe-tooltip>)
                                    : (<span>{column.title}</span>)
                                return [
                                    dom
                                ]
                            }
                        }
                        // 查看业务模板没有 字段类型 暂判断 后需查看业务模板需优化
                        if (sub.dataFormat) {
                            const floatReg = /^#\.\d{1,4}$/
                            if (floatReg.test(sub.dataFormat)) { // 小数类型
                                const digits = sub.dataFormat.match(/^#\.(\d{1,4})/)
                                if (digits[1]) {
                                    sub.slots = Object.assign({}, sub.slots, {
                                        default: ({row, rowIndex, column, columnIndex}, h) => {
                                            return [
                                                (<span>{Number(row[column.property]).toFixed(digits[1].length)}</span>)
                                            ]
                                        }
                                    })
                                }
                            }
                        }
                    })
                    
                }
                
            })
            this.pageData.groups.sort(function(a, b) {
                return a.sortOrder - b.sortOrder;
            })
            this.afterHandleData(this.pageData)
            // groups - 组装完成 收集依赖
            let arr = [...this.pageData.groups]
            this.$set(this.pageData, 'groups', [])
            this.$nextTick(()=>{
                this.$set(this.pageData, 'groups', arr)
            })
        },
        openFoldDrawer (row, column) {
            this.$refs.detailPage.$refs?.GridFoldDrawer.openFoldDrawer(row, column)
        },
        // 选择弹窗的 slots (fieldType:selectModal)
        ladderPriceModalSlots (col) {
            const that = this
            // 阶梯报价json数据组装
            const initRowLadderJson = function (jsonData) {
                let arr = []
                if (jsonData) {
                    arr = JSON.parse(jsonData)
                }
                return arr
            }
            // 阶梯报价默认显示
            const defaultRowLadderJson = function (jsonData) {
                console.log(jsonData)
                let arrString = ''
                if (jsonData) {
                    let arr = JSON.parse(jsonData)
                    if (Array.isArray(arr)) {
                        arr.forEach((item, index)=> {
                            let ladder = item.ladder
                            let price = item.price || ''
                            let fix = item.fix || ''
                            let rate = item.rate || ''
                            let union = item.union || ''
                            let str = `${ladder},${price||fix||rate||union||''}`
                            let separator = index===arr.length-1? '': ';'
                            arrString +=str+ separator
                        })
                    }
                }
                return arrString
            }
            return Object.assign({}, col.slots, {
                default: ({ row, rowIndex, column, columnIndex }) => {
                    const scopedSlots = {
                        default: ({ openFunc }) => {
                            debugger
                            if((props?.config?.groupCode == 'rebateRuleItems' && props?.config?.field == 'rebateLadder') ||
                                (props?.config?.groupCode == 'rebateRuleSupplements' && props?.config?.field == 'rebateLadder') ||
                                (props?.config?.groupCode == 'rebateCalculationSheetRuleSupplements' && props?.config?.field == 'rebateLadder') ||
                                (props?.config?.groupCode == 'rebateCalculationSheetRuleDetails' && props?.config?.field == 'rebateLadder')){
                                const detailTpl = (
                                    <div style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                                        <a-tooltip placement="topLeft" overlayClassName="tip-overlay-class">
                                            <template slot="title">
                                                <div>
                                                    <vxe-table auto-resize border row-id="id" size="mini" data={initRowLadderJson(row[column.property])}>
                                                        <vxe-table-column type="seq" title={that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_seq`, '序号')} width="80"></vxe-table-column>
                                                        <vxe-table-column field="ladder" title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')} width="140"></vxe-table-column>
                                                        <vxe-table-column field="fix" title={that.$srmI18n(`${that.$getLangAccount()}#`, '固定值')} width="140"></vxe-table-column>
                                                        <vxe-table-column field="rate" title={that.$srmI18n(`${that.$getLangAccount()}#`, '比例值%')} width="140"></vxe-table-column>
                                                        <vxe-table-column field="union" title={that.$srmI18n(`${that.$getLangAccount()}#`, '每单位返')} width="140"></vxe-table-column>
                                                    </vxe-table>
                                                </div>
                                            </template>
                                            {defaultRowLadderJson(row[column.property])}
                                        </a-tooltip>
                                    </div>)
                                return detailTpl
                            }else{
                                const detailTpl = (
                                    <div style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                                        <a-tooltip placement="topLeft" overlayClassName="tip-overlay-class">
                                            <template slot="title">
                                                <div>
                                                    <vxe-table auto-resize border row-id="id" size="mini" data={initRowLadderJson(row[column.property])}>
                                                        <vxe-table-column type="seq" title={that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_seq`, '序号')} width="80"></vxe-table-column>
                                                        <vxe-table-column field="ladder" title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')} width="140"></vxe-table-column>
                                                        <vxe-table-column field="price" title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_price`, '含税价')} width="140"></vxe-table-column>
                                                        <vxe-table-column field="netPrice" title={that.$srmI18n(`${that.$getLangAccount()}#i18n_title_netPrice`, '不含税价')} width="140"></vxe-table-column>
                                                    </vxe-table>
                                                </div>
                                            </template>
                                            {defaultRowLadderJson(row[column.property])}
                                        </a-tooltip>
                                    </div>)
                                return detailTpl
                            }
                        }
                    }
                    const props = {
                        config: col,
                        row: row,
                        pageData: that.pageConfig,
                        form: that.editFormData,
                        isRow: true
                    }
                    if((props?.config?.groupCode == 'rebateRuleItems' && props?.config?.field == 'rebateLadder' ) ||
                        (props?.config?.groupCode == 'rebateRuleSupplements' && props?.config?.field == 'rebateLadder' ) ||
                        (props?.config?.groupCode == 'rebateCalculationSheetRuleSupplements' && props?.config?.field == 'rebateLadder' ) ||
                        (props?.config?.groupCode == 'rebateCalculationSheetRuleDetails' && props?.config?.field == 'rebateLadder')){
                        props['pageStatus'] =that.pageStatus
                        const $modalBoxRebate = <rebateLadderPrice scopedSlots={scopedSlots} {...{ props }}/>
                        return [$modalBoxRebate]
                    }else{
                        const $modalBox = <LadderPrice scopedSlots={scopedSlots} {...{ props }} />
                        return [$modalBox] 
                    }
                }
            })
        },
        _addFoldOptColumn (columns, item) {
            // 增加表行默认操作列
            if (columns.find(rs => rs.fold && rs.fold === '1')) {
                item.custom.optColumnList = item.custom.optColumnList || []
                if (!columns.find(rs => rs.field === 'grid_opration')) {
                    let customOption = {
                        fixed: 'right'
                    }
                    customOption = {...GRID_OPTION_ROW, ...customOption}
                    item.custom.columns = item.custom.columns.concat(customOption)
                }
                item.custom.optColumnList.push({
                    type: 'fold',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_HOJO_3026ba64`, '更多字段'),
                    clickFn: this.openFoldDrawer
                })
            }
        },
        // 处理无业务模板
        handleFromSourceData () {
            if(this.pageData?.groups.length){
                this.pageData.groups.forEach(item => {
                    if (item.type == 'grid') {
                        item.custom.columns.forEach(sub => {
                            if(sub.extend && sub.extend.linkConfig ){
                                sub.slots = this.linkModalSlots(sub)
                            }
                        })
                    }
                })
            }
        },
        //超链接跳转方法
        getNewRouter (col, row, column, linkConfig){
            if (col?.extend?.handleBefore && typeof col?.extend?.handleBefore === 'function' ){
                let callbackObj = col?.extend?.handleBefore(row, column, linkConfig, this, { pageConfig: this.pageConfig }) || {}
                linkConfig = { ...linkConfig, ...callbackObj }
            }
            if(row[column.property] && linkConfig.actionPath && linkConfig.bindKey){
                let query = {
                    [linkConfig.primaryKey]: row[linkConfig.bindKey],
                    ...linkConfig.otherQuery,
                    t: new Date().getTime()
                }
                this.$router.push({ path: linkConfig.actionPath.trim(), query: { ...query } })
            }
        },
        //超链接slots
        linkModalSlots (col) {
            const that = this
            let linkConfig = {
                title: '认证链接',
                titleI18nKey: 'i18n_title_authenticationLink',
                primaryKey: 'id', //查询用的主键名
                bindKey: 'fbk1', //绑定的主键key
                actionPath: '', //目标路由
                otherQuery: { open: true } //其余参数
            }
            let exLink = false
            if (col.extend && col.extend.linkConfig) linkConfig = { ...linkConfig, ...col.extend.linkConfig }
            return Object.assign({}, col.slots, {
                default: ({ row, rowIndex, column, columnIndex }) => {
                    if(exLink){
                        return [
                            <a
                                href= {row[column.property]}
                                target="_blank"
                            >
                                <span>{ that.$srmI18n(`${that.$getLangAccount()}#${linkConfig.titleI18nKey}`, linkConfig.title) }</span>
                            </a>
                        ]
                    }else{
                        return [
                            <a
                                onClick={() => {
                                    that.getNewRouter(col, row, column, linkConfig)
                                }}
                            >
                                {row[column.property]}
                            </a>
                        ]
                    }
                }
            })
        },
        //批量添加 批量下载 按钮
        // handelCreateBatchDownload (item) {
        //     let batchDownloadConfig = {
        //         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'),
        //         msgType: 'batchDownload',
        //         key: 'batchDownload',
        //         type: 'check',
        //         beforeCheckedCallBack: (data, that) => {
        //             that.batchDownload(data)
        //         }
        //     }
        //     if (!item.custom.buttons) {
        //         item.custom.buttons = [batchDownloadConfig]
        //     } else {
        //         if (item.custom.buttons.findIndex(itx => itx.msgType === batchDownloadConfig.msgType) === -1) {
        //             item.custom.buttons.push(batchDownloadConfig)
        //         }
        //     }
        // },
        goBack () {
            this.$emit('hide')
        },
        toEdit() {
            this.$emit('toEdit')
        },
        prevEvent () {
            this.$refs.detailPage.prevStep()
        },
        nextEvent () {
            this.$refs.detailPage.nextStep()
        },
        downloadEvent (row) {
            this.$refs.detailPage.handleDownload(row)
        },
        beforeHandleData (data) {
            console.log('beforeHandleData', data)
        },
        afterHandleData (data) {
            console.log('afterHandleData', data)
        },
        showAuditBtn () {
            return this.$refs.detailPage && this.$refs.detailPage.form && this.$refs.detailPage.form.auditStatus === '1'
        }
    }
}
