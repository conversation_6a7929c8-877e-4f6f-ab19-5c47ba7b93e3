<template>
  <a-modal
    v-drag
    centered
    :title="$srmI18n(`${$getLangAccount()}#i18n_field_uNWF_2b0e8aa7`, '导入数据')"
    :width="720"
    :visible="visible"
    :maskClosable="false"
    @ok="ok"
    :footer="null"
    @cancel="close"
  >
    <a-steps
      v-if="!isLoading"
      v-model="current"
      @change="onChange"
    >
      <a-step
        disabled
        key="step-1"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_improt`, '导入')"
      >
        <a-icon
          slot="icon"
          type="import"
        />
      </a-step>
      <a-step
        disabled
        key="step-2"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_ML_b7804`, '完成')"
      >
        <a-icon
          slot="icon"
          type="smile-o"
        />
      </a-step>
    </a-steps>
    <a-spin :spinning="confirmLoading">
      <div
        style="margin-top: 12px"
        class="steps-content"
      >
        <div
          v-if="current === 0"
          v-show="!isLoading"
          class="step-2"
        >
          <div class="import-desc">
            <h3>{{ $srmI18n(`${$getLangAccount()}#i18n_field_uNlR_2b133743`, '导入说明') }}：</h3>
            <ul>
              <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_QIfXxOBR_f2502a49`, '文件大小不能超过') }} 10M；</li>
              <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_HRuWWWWWWWWWWWWWWmKQI_121e8f6`, '仅支持 *.xls、*.xlsx 格式文件') }}；</li>
              <li>
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_qICtNv_c65982cb`, '可以点击这里') }}，<a
                  href="#"
                  @click="downloadFile"
                  >{{ $srmI18n(`${$getLangAccount()}#i18n_title_downloadTemplate`, '下载模板') }}!</a
                >
              </li>
            </ul>
          </div>
          <a-upload-dragger
            name="file"
            :headers="uploadHeader"
            :accept="accept"
            :data="params"
            :action="importUrl"
            @change="imortChangeEvent"
            :beforeUpload="beforeUpload"
          >
            <p class="ant-upload-drag-icon">
              <a-icon type="inbox" />
            </p>
            <p class="ant-upload-text">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_CtSdQIenuNmMUXV_d2c66d10`, '点击或把文件拖拽到这个区域上传') }}
            </p>
          </a-upload-dragger>
        </div>
        <div
          v-if="isLoading"
          class="progress"
        >
          <a-progress
            type="circle"
            :stroke-color="{
              '0%': '#108ee9',
              '100%': '#87d068'
            }"
            :percent="percent"
          />
        </div>
        <div
          v-if="current === lastStepIndex && !isLoading"
          class="step-3"
        >
          <a-result
            status="success"
            :title="$srmI18n(`${$getLangAccount()}#i18n_field_uNLR_2b0e1138`, '导入成功')"
          ></a-result>
          <div
            class="import-error"
            v-if="errorId"
          >
            <div class="statistics">
              总计{{ successCount + failCount }}条，导入成功<span class="success">{{ successCount }}</span
              >条，导入失败<span class="fail">{{ failCount }}</span
              >条。
            </div>
            <div
              class="errDownload"
              @click="downloadTaskErrorFiles(errorId)"
            >
              {{ $srmI18n(`${$getLangAccount()}#i18n_alert_uNIrWFxzIHWqCtIKmA_4f928296`, '导入模板数据部分异常，可点击下载查看') }}
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import { Steps, Result } from 'ant-design-vue'
import { downFile } from '@api/manage'
export default {
  name: 'ItemImportExcel',
  props: {
    excelCode: String
  },
  components: {
    ASteps: Steps,
    AStep: Steps.Step,
    AResult: Result
  },
  data() {
    return {
      visible: false,
      current: 0,
      confirmLoading: false,
      uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
      accept: '.xls, .xlsx',
      params: {},
      errorId: '',
      dataKey: '',
      url: '',
      local_excelCode: '',
      isConfigUrl: '1',
      fileName: '模板文件',
      isLoading: false,
      percent: 0,
      itemCode: '',
      successCount: 0,
      failCount: 0
    }
  },
  computed: {
    importUrl() {
      // return '/els/base/excelByConfig/importExcel'
        return '/els/baseExtend/excelByConfig/importExtendExcel'
    },
    lastStepIndex() {
      let current = 1
      return current
    }
  },
  methods: {
    open(params, fileName, itemCode, url) {
      this.current = 0
      this.params = params
      this.url = url
      this.errorId = ''
      this.fileName = fileName
      this.itemCode = itemCode
      this.local_excelCode = params.excelCode || ''
      this.visible = true
      if (!url) {
        this.url = '/base/excelByConfig/downloadTemplate'
        this.isConfigUrl = '0'
      }
    },
    ok() {
      this.visible = false
    },
    close() {
      this.visible = false
    },
    onChange(current) {
      this.current = current
    },
    //下载模板
    downloadFile() {
      //'/base/excelByConfig/downloadTemplate'
      let params = { ...this.params }
      if (this.url === '/base/excelHeader/downloadTemplate') {
        params = {
          excelCode: params.excelCode
        }
      }
      downFile(this.url, params)
        .then((data) => {
          if (!data) {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
            return
          }
          if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(new Blob([data]), this.fileName + '.xlsx')
          } else {
            let url = window.URL.createObjectURL(new Blob([data]))
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = url
            link.setAttribute('download', this.fileName + '.xlsx')
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(url) //释放掉blob对象
          }
        })
        .finally(() => {
          this.gridLoading = false
        })
    },
    //导入
    imortChangeEvent(info) {
      // 导入时loading
      if (info.event && info.file.status === 'uploading') {
        this.isLoading = true
        try {
          this.percent = Math.floor((info.event.loaded / info.event.total) * 100)
        } catch (error) {
          throw new Error(error)
        }
      }
      if (info.file.status === 'done') {
        let res = info.file.response
        if (res.success) {
          if (res.result) {
            this.errorId = res.result.errorId
            this.successCount = res.result.successCount
            this.failCount = res.result.failCount
            if (res.result.failCount == 0 || null == res.result.failCount) {
              this.errorId = ''
            }
            if (res.result.successCount > 0) {
              this.$emit('importCallBack', info, this.fileName)
            } else if (res.result.dataList?.length > 0) {
              this.$emit('importCallBack2', info)
            }
            this.current += 1
          } else {
            this.$message.warning(res.message)
          }
        } else {
          this.$message.warning(res.message)
        }
        this.isLoading = false
      }
      if (info.file.status === 'error') {
        this.isLoading = false
      }
    },
    beforeUpload(file) {
      if (file.size > 10 * 1024 * 1024) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BRefQIWR_929fb07e`, '超过最大文件限制!'))
        return false
      }
    },
    // 下载部分异常文件
    downloadTaskErrorFiles(errorId) {
      let id = errorId
      downFile(`/base/fileTask/downTaskFile/${id}`).then((data) => {
        if (!data) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LYuIdQI_12f6af66`, '未找到对应文件'))
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), this.fileName + '_error.xlsx')
        } else {
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', this.fileName + '_error.xlsx')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.progress {
  text-align: center;
}
.import-error {
  color: #f5222d;
  padding-bottom: 6px;
  margin-top: 10px;
  .errDownload {
    cursor: pointer;
  }
}
.statistics {
  color: #000;
  .success {
    color: #54c41e;
  }
  .fail {
    color: #d8051d;
  }
}
</style>
