<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkSupplierSelectOk"
        ref="SupplierFieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getLodop } from '@/utils/LodopFuncs'
import { getAction, postAction } from '@/api/manage'

export default {
    name: 'PurchaseBarcodeTemplateHeadEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            showConst: ['LINE', 'RECT', 'STYLE', 'STYLEA', 'ELLIPSE', 'SHAPE'],
            hideItems: [],
            requestData: {
                detail: {
                    url: '/base/barcode/purchaseBarcodeTemplateHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                templateList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IrGt_31d451a1`, '模板设计'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.templateDesign
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete'
                }],
                supplierList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.supplierGridAddPopup
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete'
                }]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/base/barcode/purchaseBarcodeTemplateHead/edit'
                    },
                    key: 'save',
                    showMessage: true,
                    handleBefore: this.handleSubmitBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                    args: {
                        url: '/base/barcode/purchaseBarcodeTemplateHead/publish'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish',
                    handleBefore: this.handlePublishBefore,
                    handleAfter: this.handlePublishAfter,
                    show: this.handleShowFn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/purchaseBarcodeTemplateHead/edit',
                templateResolve: '/base/barcode/purchaseBarcodeTemplateHead/templateResolve'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_barcodeTemplate_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hxId_2786675a`, '发布对象'),
                        groupNameI18nKey: 'i18n_field_hxId_2786675a',
                        groupCode: 'supplierList',
                        groupType: 'item',
                        sortOrder: '3'
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'supplierList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'),
                        fieldLabelI18nKey: 'i18n_title_supplierELSAccount',
                        field: 'toElsAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'supplierList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXWWWAo_86dc641`, '供应商ERP编码'),
                        fieldLabelI18nKey: 'i18n_field_RdXWWWAo_86dc641',
                        field: 'supplierCode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'supplierList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                        fieldLabelI18nKey: 'i18n_field_RdXRL_8e11f650',
                        field: 'supplierName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    }
                ]
            }
        },
        handleSubmitBefore (args){
            this.hideItems.forEach(row =>{
                args.allData.templateList.push(row)
            })
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        // 处理发布回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!args.allData.templateList || !args.allData.templateList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cGtCcxOLVW_b5b62ece`, '行设计内容不能为空！'))
                    reject(args)
                }else {
                    this.hideItems.forEach(row =>{
                        args.allData.templateList.push(row)
                    })
                    resolve(args)
                }
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        handleShowFn () {
            let rs = true
            if (this.currentEditRow && this.currentEditRow.id) {
                rs = true
            } else {
                rs = false
            }
            return rs
        },
        templateDesign () {
            let allData = this.getBusinessExtendData(this.businessRefName).allData
            // let itemGrid = this.getItemGridRef('templateList')

            let oldTemplate = ''
            if (allData.templateList.length >0){
                oldTemplate += 'LODOP.PRINT_INIT("'+(!allData.templateName ? '条码套打设计' : allData.templateName)+'");'
                allData.hideTemplateList.forEach(a =>{
                    allData.templateList.push(a)
                })
                allData.templateList.forEach( a =>{
                    oldTemplate += a.designContent+';'
                })
            }
            let that = this
            let LODOP = getLodop()//调用getLodop获取LODOP对象

            if(oldTemplate!=''){
                eval(oldTemplate)
            }else{
                LODOP.PRINT_INIT( !allData.templateName ? '条码套打设计' : allData.templateName)
            }

            if (LODOP.CVERSION) CLODOP.On_Return=function (TaskID, Value){ that.templateResolve(Value, that)}

            if (LODOP.CLodopIsLocal){
                window.location.href = 'CLodop.protocol:setup'
            }
            LODOP.PRINT_DESIGN()
        },
        templateResolve (value, that){
            let data = {
                designContent: value,
                id: that.currentEditRow.id
            }
            this.hideItems = []
            postAction(this.url.templateResolve, data).then((res) => {
                if (res.success) {
                    let resultArr = new Array()
                    res.result.forEach( a =>{
                        resultArr.push(a)
                    })
                    // 插入行数据
                    let itemGrid = that.getItemGridRef('templateList')
                    itemGrid.remove()

                    let templateList = res.result
                    let rowItem = []
                    templateList.forEach((row)=>{
                        if(this.showConst.findIndex(item => item === row['elementType']) == -1){
                            rowItem.push(row)
                        }else {
                            this.hideItems.push(row)
                        }
                    })
                    itemGrid.insertAt(rowItem, -1)
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        supplierGridAddPopup ({ Vue, pageConfig, btn, groupCode }) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            this.curGroupCode = groupCode
            this.fieldSelectType = 'material'
            let url = '/supplier/supplierMaster/list'
            let columns = [
                {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), width: 150},
                {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 150},
                {field: 'supplierStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'), width: 150},
                {field: 'cateName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'), width: 150}
            ]
            this.$refs.SupplierFieldSelectModal.open(url, {}, columns, 'multiple')
        },
        checkSupplierSelectOk (data) {
            let itemGrid = this.getItemGridRef('supplierList')
            data.forEach(item => {
                item['id'] = null
            })
            itemGrid.insertAt(data, -1)
        }
    }
}
</script>