<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <purchase-bidding-project-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPageNew" />
    <!-- 详情界面 -->
    <purchase-bidding-project-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :allowEdit="allowViewToEdit"
      @toEdit="handleEditFromViewPage"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import PurchaseBiddingProjectEdit from './modules/PurchaseBiddingProjectEdit'
import layIM from '@/utils/im/layIM.js'
import PurchaseBiddingProjectDetail from './modules/PurchaseBiddingProjectDetail'
import { getAction, postAction } from '@/api/manage'
import { handleDemandPool } from '@/utils/util'

import { SET_VUEX_CACHE_ROW, SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'
import { cloneDeep } from 'lodash'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseBiddingProjectEdit,
        PurchaseBiddingProjectDetail
    },
    data () {
        return {
            regretValue: 0,
            regretVisible: false,
            currentRow: {},
            showNewRoundPage: false,
            pageData: {
                businessType: 'biddingProject',
                form: {
                    projectNumber: ''
                },
                button: [
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'bidding#purchaseBiddingProjectHead:add' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF }
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectNumber`, '项目编号'),
                        fieldName: 'projectNumber'

                    }
                ],
                optColumnWidth: 320,
                optColumnList: [
                    { type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat, authorityCode: 'bidding#purchaseBiddingProjectHead:creatGruopChat' },
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'bidding#purchaseBiddingProjectHead:queryById' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'bidding#purchaseBiddingProjectHead:edit' },
                    { type: 'abandon', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uB_bdd48`, '废标'), clickFn: this.handleAbandon, allow: this.allowAbandon, authorityCode: 'bidding#purchaseBiddingProjectHead:abandon' },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingHall`, '招标大厅'), clickFn: this.toTender, allow: this.allowBiddingHall, authorityCode: 'bidding#purchaseBiddingProjectHead:toHall' },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'bidding#purchaseBiddingProjectHead:delete' },
                    { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord, authorityCode: 'bidding#purchaseBiddingProjectHead:record' },
                    { type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopy, authorityCode: 'bidding#purchaseBiddingProjectHead:copyData' }
                ]
            },
            tabsList: [],
            url: {
                add: '/bidding/purchaseBiddingProjectHead/add',
                list: '/bidding/purchaseBiddingProjectHead/subpackage/list',
                delete: '/bidding/purchaseBiddingProjectHead/delete',
                regret: '/bidding/purchaseBiddingHead/regret',
                columns: 'PurchaseBiddingProjectHead',
                excelCode: 'ebidding',
                copyData: '/bidding/purchaseBiddingProjectHead/copyData',
                queryById: '/bidding/purchaseBiddingProjectHead/queryById'
            },
            countTabsUrl: '/bidding/purchaseBiddingProjectHead/subpackage/counts'
        }
    },
    mounted () {
        // this.serachTabs('srmBiddingStatus', 'biddingStatus')
        this.serachCountTabs(this.countTabsUrl)
    },
    created () {
        this.getUrlParam()
        if (this.$route.query.id) {
            this.pageData.form.id = this.$route.query.id
            this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
        }
        handleDemandPool(this)
    },
    watch: {
        '$route.query': {
            handler (newVal) {
                if (newVal && Object.keys(newVal).length) {
                    this.init()
                }
            }
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        
        allowViewToEdit() {
            console.log("viewRow", cloneDeep(this.viewRow))
            if(!!this.viewRow) return !this.allowEdit(this.viewRow);
            return false;
        }
    },
    methods: {
        handleDelete (row) {
            let target = JSON.parse(JSON.stringify(row))
            target.id = target.projectId
            this.$refs.listPage.deleteRows(target)
        },
        handleEdit (row) {
            let target = JSON.parse(JSON.stringify(row))
            target.id = target.projectId
            this.currentEditRow = target
            this.showEditPage = true
        },
        handleView (row) {
            let viewRow = cloneDeep(row);
            const viewAuth = this.getViewAuth()
            if (!viewAuth) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BjbWWVKHRvjlb_beee0887`, '没有权限，请联系管理员授权'))
                return
            }
            let target = JSON.parse(JSON.stringify(row))
            target.id = target.projectId
            this.viewRow = viewRow;
            this.currentEditRow = target
            this.showDetailPage = true
            console.log(222, this.viewRow)
        },
        handleAbandon (row) {
            let param = { id: row.id }
            getAction('bidding/purchaseBiddingHead/abandoned', param).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.$refs.listPage.loadData()
                }
            })
        },
        // 缓存当前行数据
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW,
            setVuexCacheRow: SET_VUEX_CACHE_ROW
        }),
        // 返回按钮
        hideEditPageNew () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
            if (query.source == 'demand-pool') {
                this.$router.replace({ path: this.$route.path })
            }
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        init () {
            if (this.$route.query.id && this.$route.path.includes('PurchaseBiddingProjectHeadList')) {
                this.pageData.form.id = this.$route.query.id
                this.countTabsUrl = this.countTabsUrl + '?id=' + this.$route.query.id
                this.$refs.listPage.initColumns()
                this.$refs.listPage.loadData()
                this.$refs.listPage.columnDrop()
            }
            handleDemandPool(this)
        },
        handleChat (row) {
            let { projectId } = row
            let recordNumber = row.templateNumber || projectId
            // 创建群聊
            layIM.creatGruopChat({ id: projectId, type: 'PurchaseBiddingProject', url: this.url || '', recordNumber })
        },
        allowChat (row) {
            return row.projectStatus === '0'
        },
        getUrlParam () {
            let templateNumber = this.$route.query.templateNumber
            let templateVersion = this.$route.query.templateVersion
            let busAccount = this.$route.query.busAccount
            let id = this.$route.query.id
            if (templateNumber && templateVersion && id && busAccount) {
                let row = {}
                row['templateNumber'] = templateNumber
                row['templateVersion'] = templateVersion
                row['id'] = id
                row['busAccount'] = busAccount
                this.currentEditRow = row
                this.showDetailPage = true
            }
        },
        submitCallBack (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (row) {
            this.currentEditRow = row
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        getCurrentProjectRow (row) {
            return getAction(this.url.queryById, { id: row.projectId })
        },
        async toTender (row) {
            // row.id = row.projectId
            let { result: currentProjectRow } = await this.getCurrentProjectRow(row)
            currentProjectRow.subpackageId = row.id
            this.setVuexCurrentEditRow({})
            this.setVuexCacheRow({ row: currentProjectRow })
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/projectHall',
                query: {
                    _t
                }
            })
            window.open(routeUrl.href, '_blank')
        },
        allowEdit (row) {
            //只有新建状态允许编辑
            if (row.projectStatus == '0' || !row.projectStatus) {
                if (row.auditStatus == '1') return true
                return false
            } else {
                return true
            }
            //新建不禁用
            // return row.projectStatus != '0'
        },
        allowBiddingHall (row) {
            //审批中禁用0:未审批；1：审批中；2："审批通过"；3：审批拒绝；4：无需审批；
            if ((row.auditStatus == '2' || row.auditStatus == '4') && row.projectStatus != '0') {
                return false
            } else {
                return true
            }
        },
        allowDelete (row) {
            return row.projectStatus != '0'
        },
        allowCreateNew (row) {
            //竞价结束状态5
            return row.ebiddingStatus != '5'
        },
        allowRegret (row) {
            let reusltAudit = row.reusltAudit
            if (reusltAudit == '1') {
                //已授标状态6 && 审批通过2
                return row.ebiddingStatus != '6' && row.reusltAuditStatus != '2'
            } else {
                //已授标状态6
                return row.ebiddingStatus != '6'
            }
        },
        allowAbandon (row) {
            // 新建已定标流标废标可编辑
            return [5, 6, 7].includes(row.biddingProjectStatus) || row.projectStatus == '0'
        },
        createNew (row) {
            this.currentEditRow = row
            this.showNewRoundPage = true
        },
        showRegret (row) {
            this.regretVisible = true
            this.currentRow = row
        },
        handleRegret () {
            let headId = this.currentRow.id
            postAction(this.url.regret, { id: headId, regretFlag: this.regretValue }).then(res => {
                if (res.success) {
                    this.regretVisible = false
                    this.$message.success(res.message)
                    this.$refs.listPage.loadData()
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        handleCopy (row) {
            let target = JSON.parse(JSON.stringify(row))
            target.id = target.projectId
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(target)
                }
            })
        },
        copyData (row) {
            let headId = row.id
            this.$refs.listPage.loading = true
            postAction(this.url.copyData, { id: headId }).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$refs.listPage.loadData()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.loading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
.radio-wrap {
    display: block;
    height: 30px;
}
</style>