<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showDetailPage && ! showEditPage"
      :pageData="pageData"
      :url="url" />
    <!-- 编辑界面 -->
    <EnterpriseEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <EnterpriseDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import EnterpriseDetail from './modules/EnterpriseDetail'
import EnterpriseEdit from './modules/EnterpriseEdit'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction} from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        EnterpriseDetail,
        EnterpriseEdit
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    // {
                    //     type: 'input',
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                    //     fieldName: 'keyWord',
                    //     placeholder: '请输入关键字(采购方)'
                    // }
                ],
                form: {
                    keyWord: '',
                    name: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'enterpriseList#enterpriseList:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, authorityCode: 'enterpriseList#enterpriseList:edit'}
                ]
            },
            url: {
                list: '/supplier/supplierMaster/supplierlist',
                isHaveNewAndWaitConfirmOrNewData: '/supplier/supplierInfoChangeHead/isHaveNewAndWaitConfirmOrNewData',
                isHaveNewData: '/supplier/supplierInfoChangeHead/isHaveNewData',
                columns: 'EnterpriseMasterList',
                download: '/attachment/purchaseAttachment/download'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierMasterData`, '供应商主数据'))
        },
        handleView (row) {
            // 打开详情页
            this.showDetailPage = false
            this.$nextTick(() => {

                getAction(this.url.isHaveNewData, {id: row.id}).then((rest) => {
                    if (rest.success) {
                        // 确定按钮 loading 开启
                        this.currentEditRow = rest.result
                        this.showDetailPage = true
                    } else {
                        // 确定按钮 loading 开启
                        this.currentEditRow = row
                        this.showDetailPage = true
                    }
                })
            })
        },
        handleEdit (row) {
            // 通过供应商主数据id查找是否存在新建和待确认的单据
            getAction(this.url.isHaveNewAndWaitConfirmOrNewData, {id: row.id, status: '3'}).then((res) => {
                if (res.success) {
                    let status = res.result.status
                    const data = res.result.data
                    if (status !== '-1') {
                        const that = this
                        let content = ''

                        // 新建
                        if (status === '0') {
                            content = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_XmAHLhxWtyLW_cfabf825`, '上次变更未发布，单号为：') +
                                data.changeNumber + this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WLLfvrAHtWVRLKQtTW_97f93ca6`, '，为您打开该变更单，请确认是否继续？')
                        }

                        // 驳回
                        if (status === '5') {
                            content = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_XmAHhxSqrMtyL_d10bee61`, '上次变更发布后被驳回，单号为：') +
                                data.changeNumber + this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WLLfvrAHtWVRLKQtTW_97f93ca6`, '，为您打开该变更单，请确认是否继续？')
                        }

                        this.$confirm({
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isSure`, '是否确认'),
                            content: content,
                            onOk: function () {
                                // 确定按钮 loading 开启
                                that.currentEditRow = data
                                that.showEditPage = true
                            }
                        })
                    } else {
                        // 确定按钮 loading 开启
                        this.currentEditRow = row
                        this.showEditPage = true
                    }
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_rnRXMKLMLjAHtFW_b17a9e8b`, '该采购商存在未完成的变更单据！'))
                }
            }).finally(() => {

            })
        }
    }
}
</script>