<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-header">
          <a-row>
            <a-col
              class="desc-col"
              :span="6">
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_YBsR_2e863326`, '招标终止') }}</span>
            </a-col>
            <a-col
              class="btn-col"
              :span="18">
              <a-button
                @click="cancelAudit"
                style="margin-right: 10px"
                v-if="cancelAuditShow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批') }}</a-button>
              <a-button
                @click="showFlow"
                v-if="viewButtonShow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_viewProcess`, '查看流程') }}</a-button>
            </a-col>
          </a-row>
        </div>
        <div class="page-content">
          <a-form-model
            ref="baseForm"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            :rules="rules"
            :model="formData">
            <a-row>
              <a-col :span="8">
                <a-form-model-item
                  :label="$srmI18n(`${$getLangAccount()}#i18n_field_sRjW_3b3f3e3b`, '终止原因')"
                  prop="terminationReason">
                  <m-select
                    v-model="formData['terminationReason']"
                    :disabled="!isEdit || formData.status == '2' || formData.auditStatus == '1'"
                    :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                    dict-code="tenderTerminationReason" />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item
                  :label="$srmI18n(`${$getLangAccount()}#i18n_field_KQUz_2fba950f`, '是否审批')"
                  prop="audit">
                  <m-select
                    v-model="formData['audit']"
                    :disabled="!isEdit || formData.status == '2' || formData.auditStatus == '1'"
                    :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                    dict-code="yn" />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_sRdRBI_78783087`, '终止相关附件')">
                  <a-upload
                    name="file"
                    :multiple="true"
                    :showUploadList="false"
                    :action="uploadUrl"
                    :headers="uploadHeader"
                    :accept="accept"
                    :disabled="!isEdit"
                    v-if="isEdit && formData.status != '2' && formData.auditStatus != '1'"
                    :data="{ headId: formData.id, businessType: 'biddingPlatform', 'sourceNumber': tenderCurrentRow.tenderProjectNumber || tenderCurrentRow.id || '', 'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开' }"
                    @change="handleUploadChange">
                    <a-button
                      type="primary"
                      @click="beforeUpload"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                  </a-upload>
                  <div
                    v-for="(fileItem, index) in formData.purchaseAttachmentDTOList"
                    :key="fileItem.id">
                    <span style="color: blue; cursor: pointer; margin-right: 8px">{{ fileItem.fileName }}</span>
                    <div>
                      <a-icon
                        type="delete"
                        v-if="isEdit && formData.status != '2' && formData.auditStatus != '1'"
                        @click="handleDeleteFile(index)" />
                      <span>
                        <a
                          style="margin: 0 8px"
                          @click="preViewEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                        <a @click="downloadEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
                      </span>
                    </div>
                  </div>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row class="textAreaClass">
              <a-form-model-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_sRjWtH_6879e520`, '终止原因记录')"
                prop="terminationReasonDetail">
                <a-textarea
                  v-model="formData['terminationReasonDetail']"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_sRjWtH_6879e520`, '终止原因记录')"
                  :disabled="!isEdit || formData.status == '2' || formData.auditStatus == '1'"
                  :auto-size="{ minRows: 3, maxRows: 5 }" />
              </a-form-model-item>
            </a-row>
          </a-form-model>
        </div>
        <div class="page-footer">
          <a-button
            @click="handleSave"
            type="primary"
            v-if="isEdit && formData.status != '2' && formData.auditStatus != '1'">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
          <a-button
            @click="handlePublish"
            type="primary"
            v-if="isEdit && formData.status != '2' && formData.auditStatus != '1'">{{ $srmI18n(`${$getLangAccount()}#i18n_title_publish`, '发布') }}</a-button>
        </div>
      </a-spin>
    </div>
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow" />
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { USER_INFO } from '@/store/mutation-types'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import ContentHeader from '../components/content-header'
import flowViewModal from '@comp/flowView/flowView'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    mixins: [baseMixins],
    components: {
        ContentHeader,
        flowViewModal
    },
    data () {
        return {
            labelCol: { span: 9 },
            wrapperCol: { span: 15 },
            confirmLoading: false,
            formData: {},
            rules: {},
            flowView: false,
            flowId: '',
            currentEditRow: this.tenderCurrentRow,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            url: {
                queryDetailById: '/tender/terminate/subpackageTerminateHead/queryById',
                queryDetailBySubpackageId: '/tender/terminate/subpackageTerminateHead/selectBySubpackageId',
                add: '/tender/terminate/subpackageTerminateHead/add',
                edit: '/tender/terminate/subpackageTerminateHead/edit',
                publish: '/tender/terminate/subpackageTerminateHead/publish'
            }
        }
    },
    inject: ['subpackageId', 'tenderCurrentRow', 'resetCurrentSubPackage'],
    computed: {
        subId () {
            return this.subpackageId()
        },
        isEdit () {
            let status = this.formData.status == '0' || !this.formData.status
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') status = 'detail'
            console.log('status', status, (status != 'detail'))
            return (status != 'detail')
        },
        cancelAuditShow () {
            let flag = this.formData.status == '1' ? true : false
            console.log(flag)
            // 判断是否为审批中，审批中才展示撤销审批按钮
            return this.formData.status == '1' ? true : false
        },
        viewButtonShow () {
            console.log('this.formData.status', this.formData.status)
            // 判断是否需要审批且有flowid，都具备才展示查看流程按钮
            // 判断是否需要审批且有flowid，都具备才展示查看流程按钮
            if (this.formData.status == '1') {
                console.log('this.formData.flowId', this.formData.flowId)

                if (this.formData.flowId) {
                    return true
                } else {
                    return false
                }
            }
            return false
        }
    },
    methods: {
        cancelAudit () {
            this.auditStatus = this.formData.auditStatus
            let params = this.formData
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: () => {
                    this.postAuditData('/a1bpmn/audit/api/cancel', params)
                    // that.$refs.businessRefName.loadData()
                }
            })
        },
        postAuditData (invokeUrl, fromSourceData) {
            let param = {}
            param['rootProcessInstanceId'] = fromSourceData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param)
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.queryDetail()
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        showFlow () {
            this.flowId = this.formData.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_APUziNxOmAQLW_25fa4a00`, '当前审批策略不能查看流程！'))
                return
            }
            this.flowView = true
        },
        async downloadEvent (row) {
            row.subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.subId
            this.$previewFile.open({ params: row })
        },
        save (url) {
            let params = Object.assign(
                {
                    projectId: this.tenderCurrentRow.id,
                    subpackageId: this.subId
                },
                this.formData
            )
            valiStringLength(params, [
                {field: 'terminationReasonDetail', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sRjWtH_6879e520`, '终止原因记录'), maxLength: 100}
            ])
            this.confirmLoading = true
            postAction(url, params)
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.queryDetail()
                        // this.$emit('resetCurrentSubPackage')
                        this.resetCurrentSubPackage()
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleSave () {
            let url = this.formData.id ? this.url.edit : this.url.add
            this.save(url)
        },
        handlePublish () {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布?'),
                onOk: () => {
                    let url = this.url.publish
                    this.save(url)
                }
            })
        },
        queryDetail () {
            this.confirmLoading = true
            let params = {
                subpackageId: this.subId
            }
            getAction(this.url.queryDetailBySubpackageId, params)
                .then((res) => {
                    console.log(res.result)
                    this.formData = res.result || {}
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        beforeUpload (e) {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
                e.stopPropagation()
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if (this.formData.id) {
                if (file.status === 'done') {
                    if (file.response.success) {
                        let { fileName, filePath, fileSize, id, headId } = file.response.result
                        let fileListData = {
                            uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                            uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName,
                            filePath,
                            fileSize,
                            id,
                            headId
                        }
                        if (!this.formData.purchaseAttachmentDTOList) this.$set(this.formData, 'purchaseAttachmentDTOList', [])
                        this.formData.purchaseAttachmentDTOList.push(fileListData)
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                    this.formData.purchaseAttachmentDTOList = fileList.map((res) => {
                        let { fileName, filePath, fileSize, id, headId } = res.response.result
                        return {
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName,
                            filePath,
                            fileSize,
                            id,
                            headId
                        }
                    })
                }
            }
        },
        handleDeleteFile (index) {
            this.formData.purchaseAttachmentDTOList.splice(index, 1)
        }
    },
    mounted () {
        this.queryDetail()
        this.resetCurrentSubPackage()
    }
}
</script>
<style lang="less" scoped>
.deliveryTime-title,
.clarification-title {
    padding: 8px;
    background: #f2f3f5;
}

:deep(.edit-page .textAreaClass .ant-form-item-control-wrapper){
    width: 87.5%;
}
:deep(.edit-page .textAreaClass .ant-form-item-label){
    width: 12.5%;
}
</style>
