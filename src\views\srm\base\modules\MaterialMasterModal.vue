<template>
  <div class="els-page-comtainer">
    
    <a-page-header
      :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
      :title="title"
    >
      <template slot="extra">
        <a-button
          @click="handleOk"
          type="primary"
          key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <a-button
          @click="goBack"
          key="3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-page-header>
    <div class="table-page-search-wrapper">
      <a-spin :spinning="confirmLoading">
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            key="1"
            :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
          >
            <a-form
              class="ant-advanced-search-form"
              layout="inline"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
              :form="form">
              <a-row :gutter="24">
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialCode`, '物料编码')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterMaterialCode`, '请输入物料编码')"
                      v-decorator="['materialCode', validatorRules.materialCode]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialDesc`, '物料描述')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterMaterialDesc`, '请输入物料描述')"
                      v-decorator="['materialDesc', validatorRules.materialDesc]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialSpec`, '物料规格')">
                    <a-input
                      :placeholder=" $srmI18n(`${$getLangAccount()}#i18n_title_enterMaterialSpec`, '请输入物料规格')"
                      v-decorator="['materialSpec', validatorRules.materialSpec]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialGroup`, '物料组')">
                    <j-dict-select-tag
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectMaterialGroupTips`, '请选择物料组')"
                      v-decorator="['materialGroupCode', validatorRules.materialGroupCode]"
                      :trigger-change="true"
                      dict-code="isrm_base_material_group,material_group_name,material_group_code,is_deleted=0"
                      @change="changeMaterialGroupCode"
                    /> 
                  </a-form-item>
                </a-col>
              
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialType`, '物料类型')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterMaterialType`, '请输入物料类型')"
                      v-decorator="['materialType', validatorRules.materialType]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterProcurementCycle`, '请输入采购周期')"
                      v-decorator="['purchaseCycle', validatorRules.purchaseCycle]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_PurchaseMassProdHeadList_factoryCode`, '工厂编码')">
                    <j-dict-select-tag
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectFactoryCodeTips`, '请选择工厂编码')"
                      v-decorator="['factoryCode', validatorRules.factoryCode]"
                      :trigger-change="true"
                      dict-code="isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code='factory'"
                      @change="changeFactoryCode"
                    /> 
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_PurchaseMassProdHeadList_factoryCode_dictText`, '工厂名称')">
                    <a-input
                      disabled
                      placeholder=""
                      v-decorator="['factoryName', validatorRules.factoryName]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_procurementSectionCode`, '采购组编码')">
                    <j-dict-select-tag
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectProcurementSectionCode`, '请选择采购组编码')"
                      v-decorator="['purchaseGroupCode', validatorRules.purchaseGroupCode]"
                      :trigger-change="true"
                      dict-code="isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code='purchaseGroup'"
                      @change="changePurchaseGroupCode"
                    /> 
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_PurchaseMassProdHeadList_purchaseGroupCode_dictText`, '采购组名称')">
                    <a-input
                      disabled
                      placeholder=""
                      v-decorator="['purchaseGroupName', validatorRules.purchaseGroupName]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_procurementOrganizationCode`, '采购组织编码')">
                    <j-dict-select-tag
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectProcurementOrganizationCode`, '请选择采购组织编码')"
                      v-decorator="['purchaseOrgCode', validatorRules.purchaseOrgCode]"
                      :trigger-change="true"
                      dict-code="isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code='purchaseOrg'"
                      @change="changePurchaseOrgCode"
                    /> 
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_massProdHead92a_purchaseOrgCode_dictText`, '采购组织名称')">
                    <a-input
                      disabled
                      placeholder=""
                      v-decorator="['purchaseOrgName', validatorRules.purchaseOrgName]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_companyCode`, '公司编码')">
                    <j-dict-select-tag
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectCompanyCodeTips`, '请选择公司编码')"
                      v-decorator="['companyCode', validatorRules.companyCode]"
                      :trigger-change="true"
                      dict-code="isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code='company'"
                      @change="changeCompanyCode"
                    /> 
                    
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称')">
                    <a-input
                      disabled
                      placeholder=""
                      v-decorator="['companyName', validatorRules.companyName]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_basicUnit`, '基本单位')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterBasicUnit`, '请输入基本单位')"
                      v-decorator="['baseUnit', validatorRules.baseUnit]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_orderUnit`, '订单单位')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterOrderUnit`, '请输入订单单位')"
                      v-decorator="['orderUnit', validatorRules.orderUnit]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_storageCondition`, '存储条件')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterStorageCondition`, '请输入存储条件')"
                      v-decorator="['storageCondition', validatorRules.storageCondition]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_checkWay`, '检验方式')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterCheckWay`, '请输入检验方式')"
                      v-decorator="['checkWay', validatorRules.checkWay]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_checkType`, '检验类型')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterCheckType`, '请输入检验类型')"
                      v-decorator="['checkType', validatorRules.checkType]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_productLine`, '产品线')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterProductLine`, '请输入产品线')"
                      v-decorator="['productLine', validatorRules.productLine]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_qualityTest`, '是否质检')">
                    <a-switch
                      :checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')"
                      :un-checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')"
                      v-model="qualityTestFlag"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_isFreeze`, '是否冻结')">
                    <a-switch
                      :checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')"
                      :un-checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')"
                      v-model="freezeFlag"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_isLocked`, '是否锁定')">
                    <a-switch
                      :checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')"
                      :un-checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')"
                      v-model="lockedFlag"
                    />                  
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_isBatchManagement`, '是否启用批次管理')">
                    <a-switch
                      :checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')"
                      :un-checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')"
                      v-model="batchFlag"
                    />
                    
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_isAssetItem`, '是否资产物料')">
                    <a-switch
                      :checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')"
                      :un-checked-children="$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')"
                      v-model="assetMaterialFlag"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </div>
  </div>
  <!-- </a-modal> -->
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { duplicateCheck } from '@/api/api'

export default {
    name: 'MaterialMasterModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            activeKey: ['1'],
            fixPageHeader: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialMasterData`, '物料主数据'),
            visible: false,
            materialGroupName: '',
            qualityTestFlag: false,
            freezeFlag: false,
            lockedFlag: false,
            batchFlag: false,
            assetMaterialFlag: false,
            model: {},
            labelCol: {
                xs: { span: 24 },
                sm: { span: 6 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 18 }
            },
            confirmLoading: false,
            form: this.$form.createForm(this),
            validatorRules: {
                materialCode: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterMaterialCode`, '请输入物料编码!') }, {max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}, { validator: this.validateCode } ], validateTrigger: 'blur'},
                materialDesc: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterMaterialDesc`, '请输入物料描述!') }, {max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符' )}]},
                baseUnit: {rules: [{max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符' )}]},
                orderUnit: {rules: [{max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符' )}]},
                storageCondition: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                checkWay: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                checkType: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                productLine: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]}
            },
            url: {
                add: '/base/materialMaster/add',
                edit: '/base/materialMaster/edit'
            }
        }
    },
    mounted () {
        this.init()
        window.addEventListener('scroll', this.handleScroll)
    },
    methods: {
        init () {
            if(this.currentEditRow) {
                this.edit(this.currentEditRow)
            }else {
                this.add()
            }
        },
        add () {
            this.edit({})
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addMaterialMasterData`, '新增物料主数据')
        },
        edit (record) {
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editMaterialMasterData`, '编辑物料主数据')
            this.form.resetFields()
            this.model = Object.assign({}, record)
            this.materialGroupName = this.model.materialGroupName
            this.qualityTestFlag = record.qualityTest?true:false
            this.freezeFlag = record.freeze?true:false
            this.lockedFlag = record.locked?true:false
            this.batchFlag = record.batch?true:false
            this.assetMaterialFlag = record.assetMaterial?true:false
            this.visible = true
            this.$nextTick(() => {
                this.form.setFieldsValue(pick(this.model, 'materialCode', 'materialDesc', 'materialSpec', 'materialGroupCode', 'materialGroupName', 'materialType', 'purchaseCycle', 'factoryCode', 'factoryName', 'purchaseGroupCode', 'purchaseGroupName', 'purchaseOrgCode', 'purchaseOrgName', 'companyCode', 'companyName', 'baseUnit', 'orderUnit', 'storageCondition', 'checkWay', 'checkType', 'productLine', 'qualityTest', 'freeze', 'lock', 'batch', 'assetMaterial'))
                //时间格式化
            })

        },
        changeMaterialGroupCode (e, option){  
            this.materialGroupName= option.title  
        },

        getOrgName (value, url, setOrgName){
            let that = this
            let keyObj = {}
            if(value){
                let params = {}
                getAction(url, params).then(res => {
                    if(res.success&&res.result) {
                        keyObj[setOrgName] = res.result.orgName                    
                        that.form.setFieldsValue(keyObj) 
                    }
                })
            }else{
                keyObj[setOrgName] = ''   
                that.form.setFieldsValue(keyObj) 
            }    
        },

        changeFactoryCode (value){
            let url = '/base/orgInfo/getOrgName/factory/'+value
            this.getOrgName(value, url, 'factoryName')
        },

        changePurchaseGroupCode (value){
            let url = '/base/orgInfo/getOrgName/purchaseGroup/'+value
            this.getOrgName(value, url, 'purchaseGroupName')
        },
        changePurchaseOrgCode (value){
            let url = '/base/orgInfo/getOrgName/purchaseOrg/'+value
            this.getOrgName(value, url, 'purchaseOrgName')
        },

        changeCompanyCode (value){
            let url = '/base/orgInfo/getOrgName/company/'+value
            this.getOrgName(value, url, 'companyName')
        },
        validateCode (rule, value, callback) {
            // 重复校验
            var params = {
                tableName: 'isrm_base_material_master',
                fieldName: 'material_code',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then((res) => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    that.confirmLoading = true
                    let httpurl = ''
                    let method = ''
                    if(!this.model.id){
                        httpurl+=this.url.add
                        method = 'post'
                    }else{
                        httpurl+=this.url.edit
                        method = 'post'
                    }
                    let formData = Object.assign(this.model, values)
                    //时间格式化         
                    formData.materialGroupName = this.materialGroupName
                    formData.qualityTest = this.qualityTestFlag?1:0
                    formData.freeze = this.freezeFlag ?1:0
                    formData.locked = this.lockedFlag ?1:0
                    formData.batch = this.batchFlag?1:0
                    formData.assetMaterial  = this.assetMaterialFlag ?1:0

                    httpAction(httpurl, formData, method).then((res)=>{
                        if(res.success){
                            that.$message.success(res.message)
                            that.$emit('ok')
                            that.goBack()
                        }else{
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        handleScroll () {
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 50) {
                this.fixPageHeader = true
            }else {
                this.fixPageHeader = false
            }
        }
    }
}
</script>

<style lang="less" scoped>

</style>