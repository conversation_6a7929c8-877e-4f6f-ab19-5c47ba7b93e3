<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
export default {
    name: 'BiddingEvaluationRegulationDetail',
    mixins: [DetailMixin],
    data () {
        return {
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regulationOptions`, '条例选项'), groupCode: 'biddingEvaluationRegulationSelectionList', type: 'grid', custom: {
                        ref: 'biddingEvaluationRegulationSelectionList',
                        columns: [
                            { 
                                type: 'checkbox', width: 40 
                            },
                            { 
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsNumExample`, '选项编号（如：A、B、C等）'),
                                field: 'selectName',
                                width: 250
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsValue`, '选项值'),
                                field: 'selectValue',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsScore`, '选项分值'),
                                field: 'score',
                                width: 150
                            }
                        ]
                    }},
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'attachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            // { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120, dictCode: 'fileType', editRender: {name: '$select', options: []} },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.previewEvent}
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/bidding/biddingEvaluationRegulation/queryById',
                upload: '/attachment/purchaseAttachment/upload'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount
            if(!elsAccount || elsAccount===''){
                elsAccount = this.$ls.get('Login_elsAccount')
            }
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_biddingRegulation_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        previewEvent () {
            const fileGrid = this.$refs.detailPage.$refs.attachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if (checkboxRecords.length > 1) {
                this.$message.warning('只能选择一条数据！')
                return
            }
            let preViewFile = checkboxRecords[0]
            this.$previewFile.open({params: preViewFile })
        }
    }
}
</script>