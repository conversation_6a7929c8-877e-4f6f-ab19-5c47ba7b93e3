<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction } from '@/api/manage'
// import REGEXP from '@/utils/regexp'
export default {
    name: 'SubaccountCertificationEdit',
    mixins: [EditMixin],
    data () {
        return {
            selectType: 'esignEnterpriseCertification',
            credentialsNo: '',
            credentialsType: '',
            pageData: {
                form: {
                    loadingCompany: '',
                    companyCode: '',
                    companyName: '',
                    subAccount: '',
                    accountId: '',
                    idNumber: '',
                    idType: '',
                    orgLegalIdNumber: '',
                    orgLegalName: '',
                    authType: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_64dEMQeAfsqvfsQ4`, '企业认证信息维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_loadingCompany`, '是否加载公司列表'),
                                    fieldName: 'loadingCompany',
                                    dictCode: 'yn',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_loadingCompany`, '是否加载公司列表'),
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub['disabled'] = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '0')
                                            setDisabledByProp('companyCode', flag)
                                            setDisabledByProp('companyName', !flag)
                                        }else{
                                            setDisabledByProp('companyCode', true)
                                            setDisabledByProp('companyName', true)
                                        }
                                    },
                                    required: '1'

                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码'),
                                    fieldName: 'companyCode',
                                    dictCode: 'purchase_organization_info,org_name,org_code,org_category_code="companyCode"',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码'),
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称'),
                                    fieldName: 'companyName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称'),
                                    disabled: true
                                },
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_userAccount`, '用户账号'),
                                    fieldName: 'subAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.form.subAccount = data[0].subAccount,
                                        Vue.form.name = data[0].realname,
                                        Vue.form.accountId = data[0].accountId
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150},
                                            {field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_xat0lPoJ`, 'e签宝账户'), with: 150},
                                            {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150}
                                        ], modalUrl: '/esign/elsSubaccountCertificationInfo/list', modalParams: {orgCreateFlag: '1'}
                                    },
                                    required: '1'
                                },
                                {
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_fekrL2LC7bftWONd`, '用户e签宝id'),
                                    fieldType: 'input',
                                    fieldName: 'accountId',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_61w5j37BPo3g0ZZ5`, '机构证件类型'),
                                    fieldName: 'idType',
                                    dictCode: 'srmCompanyEsignIdType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_61w5j37BPo3g0ZZ5`, '机构证件类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    fieldName: 'idNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalIdNumber`, '法定代表人证件号'),
                                    fieldName: 'orgLegalIdNumber'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalName`, '法定代表人名称'),
                                    fieldName: 'orgLegalName'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型'),
                                    fieldName: 'authType',
                                    dictCode: 'srmCompanyCertificationType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_XQzTTQLipsGMe3Mt`, 'e签宝个人机构账户'),
                                    fieldName: 'orgId',
                                    disabled: true,
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            setDisabledByProp('subAccount', true)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cbN3tX16RHkSMJqd`, '是否认证完成'),
                                    fieldName: 'certificationStatus',
                                    disabled: true,
                                    dictCode: 'yn',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cbN3tX16RHkSMJqd`, '是否认证完成')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_shortLink`, '实名认证短链接'),
                                    fieldName: 'shortLink',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_longLink`, '实名认证长链接'),
                                    disabled: true,
                                    fieldName: 'longLink'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LAkPCzGnQeU0UR7F`, '认证发起时间'),
                                    disabled: true,
                                    fieldName: 'certificationStartTime'
                                }
                            ],
                            validateRules: {
                                loadingCompany: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BFQiKPrmHbGTqCS8`, '是否加载公司列表不能为空')}],
                                subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Rj3r30Q2x80Ltran`, '用户账号不能为空')}],
                                accountId: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_5BfpnlBpz9oPzzAx`, '用户账号对应的e签宝账号不能为空')}],
                                idType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_f6XyqCu3syIh31zT`, '证件类型不能为空')}],
                                idNumber: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_wfOnIW5LtvkPwjKg`, '证件号不能为空')}]
                            }
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_YIpUJ3gPutMJ2ZCU`, '修改认证信息'), type: 'primary', click: this.modifyCertificationEvent, showCondition: this.modifyInfoBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), type: 'primary', click: this.submitCertificationEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                edit: '/esign/elsEnterpriseCertificationInfo/edit',
                detail: '/esign/elsEnterpriseCertificationInfo/queryById',
                auth: '/esign/elsEnterpriseCertificationInfo/submitCertification',
                modifyAuthInfo: '/esign/elsEnterpriseCertificationInfo/modifyAuthInfo'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
                if(this.currentEditRow.accountId){
                    this.credentialsNo = this.currentEditRow.idNumber
                    this.credentialsType = this.currentEditRow.idType
                }
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        modifyInfoBtn (){
            if(this.currentEditRow.orgId){
                return true
            }
            return false
        },
        prevEvent () {
            this.$refs.editPage.prevStep()
        },
        nextEvent () {
            this.$refs.editPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    if(params.loadingCompany==='0' && !params.companyName){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCRLxOLV_3f6f291f`, '公司名称不能为空'))
                        return
                    }
                    if(params.loadingCompany==='1' && !params.companyCode){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCooxOLV_5449119a`, '公司代码不能为空'))
                        return
                    }
                    let url = this.url.edit
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        this.goBack()
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        submitCertificationEvent () {
            const _this = this
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    if(params.loadingCompany==='0' && !params.companyName){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCRLxOLV_3f6f291f`, '公司名称不能为空'))
                        return
                    }
                    if(params.loadingCompany==='1' && !params.companyCode){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCooxOLV_5449119a`, '公司代码不能为空'))
                        return
                    }
                    let url = this.url.auth
                    let longLink = params.longLink
                    if (longLink && longLink.length>0) {
                        const that = this
                        this.$confirm({
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationSystems`, '确认认证'),
                            content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_rVHIDJRLiWKmDJMAbujWKQtTDJW_96191780`, '该信息已提交过认证，再次提交会产生费用，是否继续提交？'),
                            onOk: function () {
                                postAction(url, params).then(res => {
                                    const type = res.success ? 'success' : 'error'
                                    that.$message[type](res.message)
                                    if(type){
                                        _this.$parent.submitCallBack(params)
                                    }
                                }).finally(() => {
                                    that.confirmLoading = false
                                })
                            }
                        })
                    } else {
                        postAction(url, params).then(res => {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            if(type){
                                _this.$parent.submitCallBack(params)
                            }
                        }).finally(() => {
                            this.confirmLoading = false
                        })
                    }
                }
            }).catch(err => {
                console.log(err)
            })
        },
        modifyCertificationEvent (){
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    if(this.credentialsNo !== params.idNumber || this.credentialsType !== params.idType){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IDJLixOcriIyniIAcWNTcrWtkQGKVVDJLi_f21199ce`, '已提交认证不能修改证件号和证件类型，如需修改先操作删除再重新提交认证'))
                        return
                    }
                    let url = this.url.modifyAuthInfo
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.init()
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>