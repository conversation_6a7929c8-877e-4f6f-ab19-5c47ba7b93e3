<template>
  <div style="height:100%">
    <list-layout
      v-show="!showDetailPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 详情界面 -->
    <SaleEcnDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import SaleEcnDetail from './modules/SaleEcnDetail'
import {ListMixin} from '@comp/template/list/ListMixin'
import { getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        SaleEcnDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'ecn',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeOrderNo`, '(变更单号)')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'ecn#saleEcnHead:view'}
                ]
            },
            url: {
                list: '/ecn/saleEcn/list',
                view: '/ecn/saleEcn/view',
                columns: 'SaleECNList'
            }
        }
    },
    methods: {
        handleView (row){
            this.currentEditRow = row
            if(row.viewStatus==='0'){
                //触发查询按钮时，将“查看状态”置为“已阅读”
                row.viewStatus = '1'
                getAction(this.url.view, {id: row.id}).then(res => {
                    if(res.success){
                        this.showDetailPage = true
                        this.$store.dispatch('SetTabConfirm', true)
                    }
                })
            }else{
                this.showDetailPage = true
                this.$store.dispatch('SetTabConfirm', true)
            }
        }
    }
}
</script>