<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <purchase-barcode-template-head-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <Purchase-barcode-template-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>

import PurchaseBarcodeTemplateHeadEdit from './modules/PurchaseBarcodeTemplateHeadEdit'
import PurchaseBarcodeTemplateHeadDetail from './modules/PurchaseBarcodeTemplateHeadDetail'
import { ListMixin } from '@comp/template/list/ListMixin'
import { getAction, postAction } from '@/api/manage'
import { getLodop } from '@/utils/LodopFuncs'

var LODOP //声明为全局变量

export default {
    mixins: [ListMixin],
    components: {
        PurchaseBarcodeTemplateHeadEdit,
        PurchaseBarcodeTemplateHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeTemplate',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fWIrAy_922ac69c`, '打印模板编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    { 
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus', 
                        clickFn: this.handleAdd, 
                        type: 'primary',
                        authorityCode: 'barcode#template:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'barcode#template:detail'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.showEdit,
                        authorityCode: 'barcode#template:edit'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'),
                        clickFn: this.templateCopy,
                        authorityCode: 'barcode#template:copy'
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.showDelete,
                        authorityCode: 'barcode#template:delete'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        clickFn: this.preview,
                        authorityCode: 'barcode#template:preview'
                    },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enable`, '启用'), clickFn: this.handleEnabled, allow: this.showEnabled, authorityCode: 'barcode#template:enable' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disable`, '禁用'), clickFn: this.handleDisabled, allow: this.showDisabled, authorityCode: 'barcode#template:disabled' },
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/base/barcode/purchaseBarcodeTemplateHead/list',
                add: '/base/barcode/purchaseBarcodeTemplateHead/add',
                delete: '/base/barcode/purchaseBarcodeTemplateHead/delete',
                deleteBatch: '/base/barcode/purchaseBarcodeTemplateHead/deleteBatch',
                enabled: '/base/barcode/purchaseBarcodeTemplateHead/enabled',
                disabled: '/base/barcode/purchaseBarcodeTemplateHead/disabled',
                templateCopy: '/base/barcode/purchaseBarcodeTemplateHead/templateCopy',
                templatePreview: '/base/barcode/purchaseBarcodeTemplateHead/templatePreview',
                columns: 'purchaseBarcodeTemplateHeadList'
            }
        }
    },
    methods: {
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        showEdit (row) {
            return row.status == 'new' || row.status == 'disabled' ? false : true
        },
        showDelete (row) {
            return row.status == 'new' ? false : true
        },
        showEnabled (row) {
            return row.status == 'disabled' ? false : true
        },
        showDisabled (row) {
            return row.status == 'enabled' ? false : true
        },
        preview (row) {
            let that = this
            getAction(this.url.templatePreview+'?id='+row.id, {}).then((res) => {
                if (res.success) {debugger
                    LODOP = getLodop()//调用getLodop获取LODOP对象
                    eval(res.message)
                    LODOP.SET_PREVIEW_WINDOW(0, 3, 0, 600, 400, '')
                    LODOP.PREVIEW()
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        handleEnabled (row) {
            let that = this
            const params = { id: row.id }
            postAction(this.url.enabled, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_AjLR_28088728`, '启用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        handleDisabled (row) {
            let that = this
            const params = { id: row.id }
            postAction(this.url.disabled, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_HjLR_38ff8896`, '禁用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        templateCopy (row) {
            let that = this
            const params = { id: row.id }
            postAction(this.url.templateCopy, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        }
    }
}
</script>