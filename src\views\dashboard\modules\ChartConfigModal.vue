<template>
  <div class="els-page-comtainer">
    
    <a-page-header
      :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
      :title="title"
    >
      <template slot="extra">
        <a-button
          @click="handleViews"
          type="primary"
          key="1">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a-button>
        <a-button
          @click="handleOk"
          type="primary"
          key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <a-button
          @click="goBack"
          key="3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-page-header>
    <div class="table-page-search-wrapper">
      <a-spin :spinning="confirmLoading">
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            key="1"
            :header="$srmI18n(`${$getLangAccount()}#i18n_title_basicInfo`, '基本信息')"
          >
            <a-form :form="form">
              
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_chartCoding`, '图表编码')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_chartCodingMsg`, '请输入图表编码')"
                  v-decorator="['chartCode', validatorRules.chartCode]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_chartName`, '图表名称')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_chartNameMsg`, '请输入图表名称')"
                  v-decorator="['chartName', validatorRules.chartName]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_chartLabel`, '图表标签')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_chartLabelMsg`, '请输入图表标签')"
                  v-decorator="['chartLabel', validatorRules.chartLabel]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_PBWFj_96ba8e8`, '图表数据源')">
                <j-dict-select-tag
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_alert_ViFPBWFj_acfdb511`, '请选择图表数据源')"
                  v-decorator="['dataSourceType', validatorRules.dataSourceType]"
                  :trigger-change="true"
                  dict-code="srmChartsDataSourceType"
                  @change="changeDataId"
                />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                class="data-ids-par"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_chartData`, '图表数据')">
                <!-- <j-dict-select-tag
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_chartDataMsg`, '请选择图表数据')"
                  v-decorator="['dataId', validatorRules.dataId]"
                  :trigger-change="true"
                  dict-code="els_chart_data,data_desc,id,is_deleted=0"
                /> -->
                <a-select
                  style="width: calc(100% - 34px);"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_chartDataMsg`, '请选择图表数据')"
                  v-decorator="['dataId', validatorRules.dataId]"
                  @change="chartDataIdChange"
                >
                  <a-select-option
                    v-for="(item, key) in picDictOptions"
                    :key="key"
                    :value="item.value"
                  >
                    <span
                      style="display: inline-block;width: 100%"
                      :title=" item.text || item.label "
                    >
                      {{ item.text || item.label }}
                    </span>
                  </a-select-option>
        
                </a-select>
                <a-button 
                  @click="dataViews=true"
                  class="data-ids"
                  type="link"
                  icon="eye" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_chartType`, '图表类型')">
                <j-dict-select-tag
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_chartTypeMsg`, '请选择图表类型')"
                  v-decorator="['chartType', validatorRules.chartType]"
                  :trigger-change="true"
                  dict-code="srmChartType"
                  @change="changeChartType"
                />
              </a-form-item>
              <a-form-item
                v-show="chartType == 'echart'"
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_chartSubtype`, '图表子类型')">
                <j-dict-select-tag
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_chartSubtypeMsg`, '请选择图表子类型')"
                  v-decorator="['chartSubType', validatorRules.chartSubType]"
                  :trigger-change="true"
                  dict-code="srmChartSubType"
                  @change="changeSubType"
                />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_wide`, '宽')">
                <a-input-number
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_wideMsg`, '请输入宽')"
                  v-decorator="['chartWidth', validatorRules.chartWidth]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_high`, '高')">
                <a-input-number
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_highMsg`, '请输入高')"
                  v-decorator="['chartHeight', validatorRules.chartHeight]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_routerJump`, '路由跳转')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_routerJumpMsg`, '请输入路由跳转')"
                  v-decorator="['routeUrl', validatorRules.routeUrl]" />
              </a-form-item>
              
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_smallIcon`, '小图标')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_smallIconMsg`, '请输入小图标')"
                  v-decorator="['chartIcon', validatorRules.chartIcon]" >
                  <a-icon
                    slot="addonAfter"
                    type="setting"
                    @click="selectIcons"
                  />
                </a-input>
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_bg`, '背景色')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_bgMsg`, '请输入背景色')"
                  v-decorator="['backgroundColor', validatorRules.backgroundColor]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_renovate`, '自动刷新间隔(s)')">
                <a-input-number
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_noneRenovate`, '0为不刷新')"
                  v-decorator="['refreshInterval', validatorRules.refreshInterval]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                v-if="false"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_chartExtend`, '图表扩展')">
                <a-input
                  type="textarea"
                  rows="10"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_chartExtendMsg`, '请输入图表扩展')"
                  v-decorator="['chartExt', validatorRules.chartExt]" />
                <!-- <j-code-editor
                  language="javascript"
                  v-model="editorValue"
                  :fullScreen="true"
                  :lineNumbers="false"
                  style="min-height: 100px"/> -->
              </a-form-item>
            </a-form>
          </a-collapse-panel>
        </a-collapse>
        <!-- 选择图标 -->
        <icons
          @choose="handleIconChoose"
          @close="handleIconCancel"
          :icon-choose-visible="iconChooseVisible"
        />
      </a-spin>
    </div>

    <viewPreview
      ref="viewPreviewDom"

      :modalData="modalData"
      :widget="widget"
      :options="options"
      :resultData="resultData"
    />
    <a-modal
      v-drag
      destroyOnClose
      :visible="dataViews"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_srmCodeEditor`, 'SRM代码编辑器')"
      :width="800"
      @ok="dataViews=false"
      @cancel="dataViews=false">
      <codemirror
        v-if="dataViews"
        v-model="resultJson"
        :options="editorOption2"
      >
      </codemirror>
    </a-modal>
  </div>
</template>

<script>
import { postAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { codemirror }  from 'vue-codemirror'
import 'codemirror/lib/codemirror.css'
import { duplicateReportCheck } from '@/api/api'
import Icons from './icon/Icons'
import JCodeEditor from '@comp/els/JCodeEditor'
import { CHAR_CONFIG } from './chartConfig.js'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import EchartMap from '@/components/EchartMap/index'
import { initWidgetInfo } from '@/components/chart/widget/utils/widgetUtils'
import EchartPreview from '@views/dashboard/modules/echartPreview'
import ViewPreview from '@views/dashboard/modules/ViewPreview'
import {isObjectPlus} from '@/utils/util'
export default {
    name: 'ChartConfigModal',
    components: {
        Icons,
        JCodeEditor,
        codemirror,
        EchartMap,
        ViewPreview,
        EchartPreview
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            widget: {},
            gridConfig: {
                border: true,
                stripe: true,
                resizable: true,
                autoResize: true,
                keepSource: true,
                height: 'auto',
                showOverflow: true,
                showHeaderOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center'
            },

            editorValue: 'xxx',
            chartType: '',
            dataSourceType: 'chartData',
            activeKey: ['1'],
            fixPageHeader: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_chartConfigure`, '图表配置'),
            visible: false,
            displayFlag: false,
            model: {},
            iconChooseVisible: false,
            labelCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            wrapperCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            confirmLoading: false,
            form: this.$form.createForm(this),
            url: {
                add: '/report/dashboard/chartConfig/add',
                edit: '/report/dashboard/chartConfig/edit',
                data: '/report/dashboard/chartData/getData/',
                dataSource: '/report/dataSource/elsReportChartDataSet/getDataById'
            },
            picDictOptions: [],
            visibleViews: false,
            modalData: {},
            resultData: null,
            resultJson: null,
            dataViews: false,
            options: {}
        }
    },
    watch: {
        // 'model.dataId': {
        //     handler (val) {
        //         if (val) {
        //             debugger
        //             this.changeChartData(val)
        //         }
        //     },
        //     deep: true,
        //     immediate: true
        // }
    },
    computed: {
        langAccount () {
            return this.$getLangAccount()
        },
        editorOption2 () {
            let opt =  {
                // mode: 'text/javascript',
                mode: 'javascript',
                indentUnit: 4,
                lineNumbers: true,
                styleActiveLine: true,
                autofocus: true,
                foldGutter: true,
                lineWrapping: true,
                gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
                // 代码提示功能
                hintOptions: {
                    // 避免由于提示列表只有一个提示信息时，自动填充
                    completeSingle: false
                },
                readOnly: true,
                keyMap: 'sublime'

            }
            // let editorOpt = Object.assign({}, opt, this.options)
            return opt
        },
        validatorRules (){
            let account = this.langAccount
            const {$srmI18n} = this
            return {
                dataId: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_chartDataMsg`, '请选择图表数据!') }]},
                dataSourceType: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_alert_ViFPBWFj_acfdb511`, '请选择图表数据源!') }]},
                chartCode: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_chartCodingMsg`, '请输入图表编码!') }, {max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符')}, { validator: this.validateCode }]},
                chartName: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_chartNameMsg`, '请输入图表名称!') }, {max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符')}]},
                chartLabel: {rules: [{max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符')}]},
                chartType: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_chartTypeMsg`, '请选择图表类型!') }]},
                routeUrl: {rules: [{max: 100, message: $srmI18n(`${account}#i18n_title_overflow100`, '内容长度不能超过100个字符')}]},
                chartIcon: {rules: [{max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符')}]},
                backgroundColor: {rules: [{max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符')}]},
                chartWidth: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_wideMsg`, '请输入宽!') }]},
                chartHeight: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_highMsg`, '请输入高!') }]},
                // seq: {rules: [{ required: true, message: '请输入顺序!' }]},
                refreshInterval: {'initialValue': '0'}
            }
        }
    },
    mounted () {
        this.init()
        window.addEventListener('scroll', this.handleScroll)
    },
    methods: {
        chartDataIdChange (e){
            this.changeChartData(e)
        },
        //获取图表数据
        getPicDictOptions (){
            let params = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'els_chart_data,data_desc,id,is_deleted=0'
            }
            if (this.dataSourceType =='sourceData') {
                params.dictCode = 'els_report_chart_data_set,set_name,id,is_deleted=0'
            }
            postAction('/report/base/dict/findDictItems', params).then(res=>{
                if(res.success){
                    this.picDictOptions=res.result
                }
            })
        },
        init () {
            if(this.currentEditRow) {
                this.edit(this.currentEditRow)
            }else {
                this.add()
            }
        },
        add () {
            this.edit({})
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addChartConfigure`, '新增图表配置')
        },
        edit (record) {
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editChartConfigure`, '编辑图表配置')
            this.form.resetFields()
            this.displayFlag = record.display?true:false
            this.model = Object.assign({}, record)
            this.visible = true
            this.dataSourceType=this.model.dataSourceType
            this.$nextTick(() => {
                this.form.setFieldsValue(pick(this.model, 'dataId', 'dataSourceType', 'chartCode', 'chartName', 'chartLabel', 'chartType', 'chartWidth', 'chartHeight', 'routeUrl', 'chartExt', 'chartIcon', 'backgroundColor', 'seq', 'display', 'refreshInterval', 'chartSubType'))
                this.getPicDictOptions()
                //时间格式化
                // 初始化数据集接口
                const  dataId = this.form.getFieldValue('dataId')
                if (dataId) {
                    this.changeChartData(dataId)
                }
            })
            this.chartType = record.chartType
        },
        validateCode (rule, value, callback) {
        // 重复校验
            var params = {
                tableName: 'els_chart_config',
                fieldName: 'chart_code',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateReportCheck(params).then((res) => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    that.confirmLoading = true
                    let httpurl = ''
                    if(!this.model.id){
                        httpurl+=this.url.add
                    }else{
                        httpurl+=this.url.edit
                    }
                    let formData = Object.assign(this.model, values)
                    //时间格式化
                    this.model.display = this.displayFlag?1:0
                    console.log(formData)
                    postAction(httpurl, formData).then((res)=>{
                        if(res.success){
                            that.$message.success(res.message)
                            that.$emit('ok')
                        }else{
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                        that.goBack()
                    })
                }
            })
        },
        viewbar (formData, chartExt){
            
            const valueArr = this.resultData || []
            chartExt = formData.chartExt.replace(/\${result_name}/g, JSON.stringify(formData.chartName))
                .replace(/\${result_value}/g, JSON.stringify(valueArr))

            return {resData: formData, resExt: chartExt}
        },
        viewbars (formData, chartExt){
            const valueArr = this.resultData || []
            chartExt = formData.chartExt.replace(/\${result_name}/g, JSON.stringify(formData.chartName))
                .replace(/\${result_value}/g, JSON.stringify(valueArr))

            return {resData: formData, resExt: chartExt}
        },
        viewlineBar (formData, chartExt){
            let valueArr = this.resultData || []
            valueArr = {
                barData: [56, 83, 49, 76, 82, 96, 26, 35, 37, 37, 56, 86],
                lineData: [56, 83, 49, 76, 82, 96, 26, 35, 37, 37, 56, 86]
            }
            chartExt = formData.chartExt.replace(/\${result_name}/g, JSON.stringify(formData.chartName))
            let extObj = JSON.parse(chartExt)
            extObj.series.forEach(item => {
                item.data = valueArr[item.data]
            })
            chartExt = JSON.stringify(extObj)
                        
            return {resData: formData, resExt: chartExt}
        },
        viewscatter (formData, chartExt){
            let valueArr = this.resultData || []
            chartExt = formData.chartExt.replace(/\${result_name}/g, JSON.stringify(formData.chartName))
                .replace(/\${result_value}/g, JSON.stringify(valueArr))
            return {resData: formData, resExt: chartExt}

        },
        viewlist (formData, chartExt){
            chartExt = formData.chartExt
            formData.resultData = this.resultData || []
            return {resData: formData, resExt: chartExt}
        },
        viewnum (formData, chartExt){
            formData.resultData = this.resultData || []
            return {resData: formData, resExt: chartExt}
        },
        viewmap (formData, chartExt){
            chartExt = formData.chartExt
            formData.resultData =isObjectPlus( this.resultData)?this.resultData:{}
            return {resData: formData, resExt: chartExt}
        },
        viewpie (formData, chartExt){
            return {resData: formData, resExt: chartExt}
        },
        viewline (formData, chartExt){
            const valueArr = this.resultData || []
            chartExt = formData.chartExt.replace(/\${result_name}/g, JSON.stringify(formData.chartName))
                .replace(/\${result_value}/g, JSON.stringify(valueArr))

            return {resData: formData, resExt: chartExt}
        },
        handleViews () {
            const that = this
            const chartType = this.form.getFieldValue('chartType')
            if (chartType == 'echart') {
                const chartSubType = this.form.getFieldValue('chartSubType')
                if (!chartSubType) {
                    this.$message.warning('请选择图表子类型！')
                    return
                }
            }
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    let formData = Object.assign(that.model, values)
                    let chartExt = '{}'
                    this.modalData = formData
                    if (formData.chartType == 'echart') {
                        // this.modalData.chartType = 'echart'
                        console.log(this.model.id)
                        const chartSubType = this.form.getFieldValue('chartSubType')
                        const dataId = this.form.getFieldValue('dataId')
                        // 图表数据 数据集是否有值
                        if (this.resultData) {
                            this.initChartType(chartSubType)
                            this.$refs.viewPreviewDom.open(this.resultData)
                        } else {
                            if (this.dataSourceType == 'sourceData' && dataId) {
                                this.changeChartData(dataId, function () {
                                    that.initChartType(chartSubType)
                                    that.$refs.viewPreviewDom.open(this.resultData)
                                })
                            }
                        }
                    } else if (formData.chartType == 'list') {
                        // this.modalData.chartType = formData.chartType
                        const dataId = this.form.getFieldValue('dataId')
                        if (this.resultData) {
                            this.$refs.viewPreviewDom.open(this.resultData)
                        } else if  (this.dataSourceType == 'sourceData' && this.model.dataId) {
                            this.changeChartData(dataId, function () {
                                that.$refs.viewPreviewDom.open(this.resultData)
                            })
                        }
                    } else if (formData.chartType == 'num') {
                        // this.modalData.chartType = formData.chartType
                        const dataId = this.form.getFieldValue('dataId')
                        let resultData = JSON.parse(JSON.stringify(this.resultData))
                        this.resultData = this.resultData.slice(0, 1)
                        if (resultData && resultData.length) {
                            this.$refs.viewPreviewDom.open(this.resultData)
                        } else if  (this.dataSourceType == 'sourceData' && this.model.dataId) {
                            this.changeChartData(dataId, function () {
                                let newData = JSON.parse(JSON.stringify(this.resultData))
                                if (newData && newData.length) {
                                    that.$refs.viewPreviewDom.open(this.resultData)
                                }
                            })
                        }
                    }  else { // 保留原逻辑
                        this.options = {}
                        this.$refs.viewPreviewDom.open(this.resultData)
                    }
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        selectIcons (){
            this.iconChooseVisible = true
        },
        handleIconCancel () {
            this.iconChooseVisible = false
        },
        handleIconChoose (value) {
            this.form.setFieldsValue({'chartIcon': value})
            this.iconChooseVisible = false
        },
        handleScroll () {
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 50) {
                this.fixPageHeader = true
            }else {
                this.fixPageHeader = false
            }
        },
        formatJSON (json, options) {
            var reg = null,
                formatted = '',
                pad = 0,
                PADDING = '    '
            options = options || {}
            options.newlineAfterColonIfBeforeBraceOrBracket = (options.newlineAfterColonIfBeforeBraceOrBracket === true) ? true : false
            options.spaceAfterColon = (options.spaceAfterColon === false) ? false : true
            if (typeof json !== 'string') {
                json = JSON.stringify(json)
            } else {
                json = JSON.parse(json)
                json = JSON.stringify(json)
            }
            reg = /(\[\{\}])/g
            json = json.replace(reg, '\r\n$1\r\n')
            reg = /(\[\[\]])/g
            json = json.replace(reg, '\r\n$1\r\n')
            reg = /(,)/g
            json = json.replace(reg, '$1\r\n')
            reg = /(\r\n\r\n)/g
            json = json.replace(reg, '\r\n')
            reg = /\r\n,/g
            json = json.replace(reg, ',')
            if (!options.newlineAfterColonIfBeforeBraceOrBracket) {
                reg = /:\r\n\{/g
                json = json.replace(reg, ':{')
                reg = /:\r\n\[/g
                json = json.replace(reg, ':[')
            }
            if (options.spaceAfterColon) {
                reg = /:/g
                json = json.replace(reg, ':')
            }
            (json.split('\r\n')).forEach(function (node) {
                var i = 0,
                    indent = 0,
                    padding = ''

                if (node.match(/\{$/) || node.match(/\[$/)) {
                    indent = 1
                } else if (node.match(/\}/) || node.match(/\]/)) {
                    if (pad !== 0) {
                        pad -= 1
                    }
                } else {
                    indent = 0
                }

                for (i = 0; i < pad; i++) {
                    padding += PADDING
                }

                formatted += padding + node + '\r\n'
                pad += indent
            }
            )
            return formatted
        },
        setCharExt (e=''){
            let chartType=e?e:this.chartType
            let config = CHAR_CONFIG[ chartType ]
            config = config && this.formatJSON(config)
            this.form.setFieldsValue({
                chartExt: config
            })
        },
        changeChartType (e) {
            this.chartType = e
            debugger
            if (e == 'echart') { 
                this.resultData = this.handlePercenByData(this.resultData)
            } else { // 类型为表格、统计数字、地图
                this.form.setFieldsValue('chartSubType', '')
            }
        },
        //图表数据源切换的时候需要做的清空操作
        dataIdChangeSetReset (){
            this.model.dataId=''
            this.model.chartType=''
            this.resultJson=''
            this.model.chartExt=''
            this.form.setFieldsValue(pick(this.model, 'dataId', 'chartType', 'chartExt'))
        },
        changeDataId (e) {
            this.dataIdChangeSetReset()
            this.dataSourceType = e
             

            let params = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'els_chart_data,data_desc,id,is_deleted=0'
            }
            if (e =='sourceData') {
                params.dictCode = 'els_report_chart_data_set,set_name,id,is_deleted=0'
            }
            postAction('/report/base/dict/findDictItems', params).then(res=>{
                if(res.success){
                    this.picDictOptions=res.result
                }
            })
        },
        changeSubType (e) {
            let config = CHAR_CONFIG[e]
            config = config && this.formatJSON(config)
            this.form.setFieldsValue({
                chartExt: config
            })
            // this.setCharExt(e)
            // 这里需要兼容旧的逻辑
            this.initChartType(e)
        },
        initChartType (e) {
            // form.getFieldValue('fileBelong')
            this.widget = initWidgetInfo(e)
            if (!this.widget) {
                return 
            }
            let widgetData = this.resultData
            this.widget.data = widgetData
        },
        handlePercenByData (data) {
            data.forEach(rs => {
                if (rs.value && typeof(rs.value) == 'string') {
                    rs.value = parseInt(rs.value)
                }
            })
            return data
        },
        changeChartData (e, cb) {
            if (this.dataSourceType == 'sourceData') {
                const that = this
                getAction(this.url.dataSource, {id: e}).then(res => {
                    if (res.code == 200) {
                        const chartType = this.form.getFieldValue('chartType')
                        that.resultData = e == '1592049481968508930' ?  res.result.data[0].result : res.result.data
                        if (e == '1592049481968508930') { // 地图数据，暂写死
                            that.resultData = res.result.data[0].result
                        } else if (chartType == 'echart') {
                            that.resultData = that.handlePercenByData(res.result.data)
                        } else {
                            that.resultData = res.result.data
                        }
                        that.resultJson = JSON.stringify(res, null, '\t')
                        cb && cb(res.result)
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }else {
                getAction(this.url.data + e, {}).then(res => {
                    if (res.code == 200) {
                        this.resultData = res.result.resultData
                        this.resultJson = JSON.stringify(res, null, '\t')
                        cb && cb(res.result)
                    }
                })
            }
        },
        numIcon () {
            return function (item) {
                let rs = 'pay-circle'
                if (item.chartType == 'num') {
                    switch (item.chartCode) {
                    case 'fund': {
                        rs = 'pay-circle'
                        break
                    }
                    case 'growthRateForOrder': {
                        rs = 'fund'
                        break
                    }
                    case 'supplierQuantity': {
                        rs = 'shop'
                        break
                    }
                    default:
                        break
                    }
                }
                return rs
            }
        }
    }
}
</script>

<style lang="less" scoped>
.num-panel-item-new {
  display: flex;
  flex-wrap: wrap;
      height: 500px;
    overflow: auto;
  .box{
      width: 218px;
    padding: 10px;
    height: 100px;
    margin-right: 20px;
    box-shadow: 0px 0px 6px #949494;
    margin-bottom: 20px;
    }
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      border-radius: 30px;
      text-align: center;
      padding: 9px 16px;
      color: white;
      background: #ed2f2f;
      line-height: 100%;
      height: 15px;
      font-weight: 600;
      -webkit-box-sizing: content-box;
      box-sizing: content-box;
    }
  }
  .count {
    padding: 10px 0;
    font-size: 18px;
    color: #494949;
    font-weight: 500;
  }
}
  :deep(.data-ids-par .ant-select-selection ){
    border-right: none;
    border-radius: 4px 0px 0px 4px
  }
  :deep(.data-ids-par .ant-select-arrow){
    display: none;
  }
  .data-ids{
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    color: rgba(0, 0, 0, 0.65);
    
    border-radius: 0px 4px 4px 0px;
    padding-top: 2px;
    margin-left: -1px;
  }
</style>