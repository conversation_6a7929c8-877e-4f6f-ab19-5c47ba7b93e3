<template>
  <div>
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      :extraDetailConfig="extraDetailConfig"
      :current-edit-row="currentEditRow"
      @goBack="goBack" />
  </div>
</template>

<script lang="jsx">
import { tileEditPageMixin } from '@comp/template/tileStyle/tileEditPageMixin'
import { getAction, postAction } from '@/api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
export default {
    mixins: [tileEditPageMixin],
    name: 'UploadFileModal',
    components: {
    },
    data () {
        const batchDownloadBtn = new BatchDownloadBtn({pageCode: 'saleAttachmentList'})
        batchDownloadBtn.baseConfig.url = '/attachment/saleAttachment/downloadZip'
        return {
            editRowModal: false,
            editItemRow: {},
            currentItemContent: '',
            pageData: {
                title: '',
                form: {
                },
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'),
                        content: {
                            type: 'table',
                            ref: 'saleAttachmentDemandList',
                            height: 200,
                            columns: [
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                                { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                                { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                                { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), width: 120 },
                                { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220 }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentList`, '附件列表'),
                        content: {
                            type: 'table',
                            ref: 'saleAttachmentList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                                { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 320 },
                                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 140 },
                                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                                { field: 'sendStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_comfigSend`, '是否发送'), width: 120 },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: {
                                        default: ({row}) => {
                                            let resultArray = [
                                                <a title="下载" style="margin-left:8px" onClick={() => this.downloadFile(row)}>下载</a>,
                                                <a title="预览" style="margin-left:8px" onClick={() => this.preViewEvent(row)}>预览</a>
                                            ]
                                            if(row.sendStatus == '0'){
                                                resultArray.push(<a title="删除" style="margin-left:8px" onClick={() => this.deleteRow(row)}>删除</a>)
                                            }
                                            return resultArray
                                        }
                                    }
                                }
                            ],
                            toolbarButton: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), 
                                    dictCode: 'srmFileType', 
                                    type: 'upload', 
                                    businessType: 'bidding', 
                                    callBack: this.uploadCallBack, 
                                    attr: this.attrHandle},
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), clickFn: this.deleteBatch },
                                {...batchDownloadBtn.btnConfig}
                            ]
                        }
                        
                    }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', clickFn: this.publishEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack }
                ]
            },
            url: {
                detail: '/bidding/saleBiddingHead/queryById',
                upload: '/attachment/saleAttachment/upload',
                delete: '/attachment/saleAttachment/delete',
                publish: '/attachment/saleAttachment/send',
                download: '/attachment/saleAttachment/download'
            },
            extraDetailConfig: {
                checkboxConfig: {
                    highlight: true,
                    trigger: 'row',
                    checkMethod: ({ row }) => {
                        let fromData = this.pageData.form
                        let supplier = fromData.biddingSupplierList[0]
                        let elsAccount = this.$ls.get('Login_elsAccount')
                        if(row.uploadElsAccount != elsAccount){
                            if(supplier.bidCheck != '1'){
                                return false
                            }
                        }
                        return true
                    }
                }
            }
        }
    },
    methods: {
        attrHandle () {
            // project 为多标包发布的招标项目，其他的为单标包发布的项目
            const path = this.currentEditRow.createType == 'project' ? '/srm/bidding/purchase/PurchaseBiddingProjectHeadList' : '/srm/bidding/purchase/PurchaseBiddingHeadList'
            return {
                sourceNumber: this.currentEditRow.biddingNumber,
                actionRoutePath: `${path},/srm/bidding/sale/SaleBiddingHeadList`
            }
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        downloadFile (row){
            let fromData = this.pageData.form
            let supplier = fromData.biddingSupplierList[0]
            let elsAccount = this.$ls.get('Login_elsAccount')
            if(row.uploadElsAccount != elsAccount){
                if(supplier.bidCheck != '1'){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidNotDownload`, '暂无标书下载权限！'))
                    return 
                }
            }
            this.downloadEvent(row)
        },
        publishEvent () {
            let params = {elsAccount: this.currentEditRow.elsAccount, headId: this.currentEditRow.id}
            let key = this.currentEditRow.relationId
            let toSend = {}
            toSend[key]=this.currentEditRow.toElsAccount
            params['toSend'] = toSend
            postAction(this.url.publish, params).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    this.goBack()
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteRow (row) {
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            //如果删除的数据有和登录人账号不一致的
            if(user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
                return
            }
            let params = {id: row.id}
            let that = this
            getAction(this.url.delete, params).then(res => {
                console.log(res)
                that.init()
            })
            
        },
        // 批量删除
        deleteBatch () {
            let arr = []
            let delArr = []
            //大B
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if(checkboxRecords && checkboxRecords.length>0){
                checkboxRecords.forEach(row=> {
                    //当前登录人等账号于查询的账号 大B
                    if (user == row.uploadElsAccount) {
                        if( subAccount==row.uploadSubAccount){
                            delArr.push(row)
                        }else{
                            arr.push(row.fileName)
                        }
                    }else{
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if(arr && arr.length>0){
                    let str = arr.join(',')
                    this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') +str+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        }
    }
}
</script>