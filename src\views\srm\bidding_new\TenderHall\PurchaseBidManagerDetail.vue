<template>
  <div class="PurchaseBidManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import EditFormLayout from '@comp/template/business/EditFormLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    name: 'PurchaseBidManagerEdit',
    components: {
        BusinessLayout,
        EditGridLayout,
        EditFormLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        queryData: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            console.log('editrow', this.currentEditRow)
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_SupplierTenderProjectPurchaseBid_${templateNumber}_${templateVersion}`
            // return '100000/sale_SupplierTenderProjectPurchaseBid_TC2022042103_1'
        }
    },
    data () {
        return {
            businessRefName: 'businessRef',
            pageStatus: 'detail',
            confirmLoading: false,
            requestData: {
                detail: {
                    url: '/tender/sale/supplierTenderProjectPurchaseBid/queryById', 
                    args: (that) => {
                        return { 
                            id: that.currentEditRow.id || ''
                        }
                    },
                    config: (that) => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {
                                xNodeId: `${that.currentEditRow.checkType || ''}_0`
                            }
                        }
                    }
                }
            },
            url: {
              
            },
            userInfo: {},
            projectObj: {}
        }
    },
    created () {
        if (JSON.stringify(this.queryData) != '{}') {
            this.userInfo = this.$ls.get(USER_INFO)
        }
    },
    mounted () {
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log(pageConfig, resultData)

            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
                if (key == 'supplierName') {
                    formModel[key] = resultData[key] || this.$ls.get(USER_COMPANYSET).companyName
                }
            }

            pageConfig.groups.forEach(group => {
                if (group.groupCode == 'tenderProjectPurchaseBidOrderVOList' && resultData.status == '2') {
                    group['extend'] = {
                        optColumnList: [
                            { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                            { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                        ]
                    }
                    group.columns.push({
                        groupCode: 'tenderProjectPurchaseBidOrderVOList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        align: 'center',
                        field: 'grid_opration',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    })
                }
            })

            // 编辑赋值
            const saleTenderInvoiceInfoList = resultData['saleTenderInvoiceInfoList']
            if (saleTenderInvoiceInfoList && saleTenderInvoiceInfoList.length > 0) {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'invoiceInfo') {
                        group['formModel'] = Object.assign({}, group['formModel'], saleTenderInvoiceInfoList[0])
                    }
                    if (group.groupCode == 'payType') {
                        group['formModel'] = Object.assign({}, group['formModel'], saleTenderInvoiceInfoList[0])
                    }
                })
            }
        },
        preViewEvent (Vue, row){
            row.subpackageId = this.currentEditRow.subpackageId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    }
}
</script>
