<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <ElsBarcodeShortCodeEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <ElsBarcodeShortCodeDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import ElsBarcodeShortCodeEdit from './modules/ElsBarcodeShortCodeEdit.vue'
import ElsBarcodeShortCodeDetail from './modules/ElsBarcodeShortCodeDetail.vue'

export default {
    mixins: [ListMixin],
    components: {
        ElsBarcodeShortCodeEdit,
        ElsBarcodeShortCodeDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barCodeShortCode',
                form: {
                    mouldGroupDesc: ''
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'shortCodeNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNIoAy_c11dc9cb`, '请输入简码编号')
                    }
                ],
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'barcode#shortCode:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'barcode#shortCode:detail' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, authorityCode: 'barcode#shortCode:edit' },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, authorityCode: 'barcode#shortCode:delete' },
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/base/barcode/elsBarCodeShortCode/add',
                list: '/base/barcode/elsBarCodeShortCode/list',
                delete: '/base/barcode/elsBarCodeShortCode/delete',
                columns: 'ElsBarCodeShortCodeList'
            }
        }
    },
    methods: {
        handleResultRecord (row) {
            this.currentEditRow = row
            this.showResultPage = true
            this.showEditPage = false
            this.showDetailPage = false
        },
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        }
    }
}
</script>