<template>
  <div class="PurchaseBarcodeInfoHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="masterSlave"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getLodop } from '@/utils/LodopFuncs'
import { postAction } from '@/api/manage'
import {
    BUTTON_SAVE,
    BUTTON_PUBLISH,
    BUTTON_SUBMIT,
    BUTTON_BACK
} from '@/utils/constant.js'

export default {
    name: 'ElsBarcodeTemplateHeadEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            requestData: {
                detail: {
                    url: '/base/barcode/elsBarcodeTemplateHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                elsBarcodeTemplateItemList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IrGt_31d451a1`, '模板设计'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.templateDesign
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete'
                }]
            },
            pageHeaderButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/base/barcode/elsBarcodeTemplateHead/edit'
                    }
                },
                BUTTON_BACK
            ],
            url: {
                submit: '/base/barcode/elsBarcodeTemplateHead/edit',
                templateResolve: '/base/barcode/elsBarcodeTemplateHead/templateResolve'
            }
        }
    },
    // computed: {
    //     remoteJsFilePath () {
    //         let templateNumber = this.currentEditRow.templateNumber
    //         let templateVersion = this.currentEditRow.templateVersion
    //         let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
    //         return `${account}/purchase_barcodeInfo_${templateNumber}_${templateVersion}`
    //     }
    // },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#`, '模板设计'),
                        groupNameI18nKey: '',
                        groupCode: 'elsBarcodeTemplateItemList',
                        groupType: 'item',
                        sortOrder: '5'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_templateName`, '模板名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'templateName',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'number',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_printNumber`, '打印份数'),
                        fieldLabelI18nKey: '',
                        fieldName: 'printNumber',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'switch',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_record_dictText`, '是否记录'),
                        fieldLabelI18nKey: '',
                        fieldName: 'record',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '1',
                        placeholder: ''
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'elsBarcodeTemplateItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cGtCc_c247d803`, '行设计内容'),
                        fieldLabelI18nKey: '',
                        field: 'designContent',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'elsBarcodeTemplateItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cGtAc_c24d079f`, '行设计类型'),
                        fieldLabelI18nKey: '',
                        field: 'designType',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'elsBarcodeTemplateItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fieldType`, '字段类型'),
                        fieldLabelI18nKey: '',
                        field: 'filedType',
                        fieldType: 'select',
                        dictCode: 'barcodeFiledType',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'elsBarcodeTemplateItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm132_busDocType`, '业务单据类型'),
                        fieldLabelI18nKey: '',
                        field: 'businessType',
                        fieldType: 'select',
                        dictCode: 'barcodeBusinessType',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'elsBarcodeTemplateItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_EStFJO_fa24b7fe`, '业务单据字段'),
                        fieldLabelI18nKey: '',
                        field: 'businessField',
                        fieldType: 'selectModal',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        bindFunction: function (obj, data, Vue) {
                            obj.businessField = data[0].filed
                        },
                        extend: {
                            modalColumns: [
                                {field: 'filed', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fieldValue`, '字段值'), with: 150},
                                {field: 'filedName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_columnName`, '字段名'), with: 150},
                                {field: 'filedType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fieldType`, '字段类型'), with: 150}
                            ],
                            modalUrl: '/base/barcode/elsBarcodeAttribute/queryPageFieldList',
                            modalParams: function (Vue, form, obj){
                                return {businessType: obj.businessType}
                            },
                            beforeCheckedCallBack: function (Vue, pageData, groupData, form) {
                                return new Promise((resolve, reject) => {
                                    let businessType = pageData.businessType || ''
                                    return businessType != '' ? resolve('success') : reject('须先选择业务单据类型')
                                })
                            },
                            afterClearCallBack: function (form, pageData) {
                                pageData.datasource = ''
                            }
                        }
                    },
                    {
                        groupCode: 'elsBarcodeTemplateItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFj_1894252`, '数据源'),
                        fieldLabelI18nKey: '',
                        field: 'datasource',
                        fieldType: 'selectModal',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        bindFunction: function (obj, array, Vue, Vue2) {
                            debugger
                            let newArray = new Array()
                            array.forEach( a =>{
                                newArray.push(a.fieldValue)
                            })
                            if (obj.datasource!=null && obj.datasource.length>0){
                                obj.datasource = obj.datasource + ',' + newArray.join(',')
                            }else {
                                obj.datasource = newArray.join(',')
                            }
                        },
                        extend: {
                            modalColumns: [{
                                field: 'fieldValue',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataValue`, '数据值')
                            }],
                            modalUrl: '/base/barcode/elsBarcodeAttribute/queryPageFieldValueList',
                            selectModel: 'multiple',
                            modalParams: function (Vue, form, obj){
                                return {businessField: obj.businessField, businessType: obj.businessType}
                            },
                            beforeCheckedCallBack: function (Vue, pageData, groupData, form) {
                                return new Promise((resolve, reject) => {
                                    let businessType = pageData.businessType || ''
                                    let businessField = pageData.businessField || ''
                                    return businessType != '' && businessField != '' ? resolve('success') : reject('须先选择业务单据类型和业务单据字段')
                                })
                            },
                            afterClearCallBack: function (form, pageData) {
                                pageData.sampleItemNumber = ''
                            }
                        }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        // 处理发布回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!args.allData.elsBarcodeTemplateItemList || !args.allData.elsBarcodeTemplateItemList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cGtCcxOLVW_b5b62ece`, '行设计内容不能为空！'))
                    reject(args)
                }else {
                    resolve(args)
                }
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        // goBack () {
        //     this.$parent.hideEditPage()
        // },

        templateDesign () {debugger
            let allData = this.getBusinessExtendData(this.businessRefName).allData
            // let itemGrid = this.getItemGridRef('elsBarcodeTemplateItemList')

            let oldTemplate = ''
            if (allData.elsBarcodeTemplateItemList.length >0){
                oldTemplate += 'LODOP.PRINT_INIT("'+(!allData.templateName ? '条码套打设计' : allData.templateName)+'");'
                allData.elsBarcodeTemplateItemList.forEach( a =>{
                    oldTemplate += a.designContent+';'
                })

            }
            let that = this
            let LODOP = getLodop()//调用getLodop获取LODOP对象

            if(oldTemplate!=''){
                eval(oldTemplate)
            }else{
                LODOP.PRINT_INIT( !allData.templateName ? '条码套打设计' : allData.templateName)
            }

            if (LODOP.CVERSION) CLODOP.On_Return=function (TaskID, Value){ that.templateResolve(Value, that)}

            if (LODOP.CLodopIsLocal){
                window.location.href = 'CLodop.protocol:setup'
            }
            LODOP.PRINT_DESIGN()
        },
        templateResolve (value, that){

            let data = {
                designContent: value
            }
            postAction(this.url.templateResolve, data).then((res) => {
                if (res.success) {debugger
                    let resultArr = new Array()
                    res.result.forEach( a =>{
                        resultArr.push(a)
                    })
                    // 插入行数据
                    let itemGrid = that.getItemGridRef('elsBarcodeTemplateItemList')
                    itemGrid.remove()
                    itemGrid.insertAt(res.result, -1)
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        selectField () {
            let item = {
                selectModel: 'single',
                sourceUrl: '/base/barcode/elsBarcodeAttribute/queryPageFieldList',
                params: {
                    businessType: this.$refs.editPage.pageData.form.businessType
                },
                columns: [
                    {field: 'filed', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fieldValue`, '字段值'), with: 150},
                    {field: 'filedName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_columnName`, '字段名'), with: 150},
                    {field: 'filedType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fieldType`, '字段类型'), with: 150}
                ]
            }
            this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        },
        selectFiledCallBack (data) {
            this.pageData.form.businessField = data[0].filed
            this.pageData.form.businessFieldName = data[0].filedName
            this.$refs.editPage.$forceUpdate()
        }
    }
}
</script>