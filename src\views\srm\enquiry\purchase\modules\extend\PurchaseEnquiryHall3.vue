<template>
  <div class="purchase-enquiry-hall">
    <a-spin :spinning="spinning">
      <div class="container">
        <a-row
          class="page-header-button"
          justify="end"
          type="flex">
          <enquiry-hall-button :buttons="pageHeaderButton"/>
        </a-row>
        <a-row class="page-header-title">
          <a-col
            class="title__col"
            v-for="title in pageHeaderTitle"
            :key="title.value"
            :span="8">
            <span class="title__col-label">{{ `${title.label}:` }}</span>
            <span
              class="title__col-value"
              :class="title.value == 'quoteEndTime' ? 'red': ''">{{ form[title.value] }}</span>
          </a-col>
        </a-row>
        <div class="page-content">
          <a-row class="content-header-button">
            <enquiry-hall-button :buttons="contentHeaderButton"/>
          </a-row>
          <a-tabs
            v-model="currentTab"
            @change="handleTabChange">
            <a-tab-pane
              v-for="tab in tabs"
              :disabled="tab.disabled"
              :key="tab.key"
              :tab="tab.title">
            </a-tab-pane>
          </a-tabs>
          <vxe-grid
            v-bind="gridConfig"
            :cell-style="({row, column}) => cellStyle(row, column, currentTab)"
            :columns="tableColumns"
            :data="tableData"
            :edit-config="editConfig"
            :height="vxeGridHeight"
            :merge-cells="mergeCells"
            :ref="currentTab"
            :show-overflow="showOverflow">
            <template #grid_operation="{row}">
              <a-button
                type="link"
                :disabled="reQuoteDisabled"
                @click="handleReQuote({reQuoteWay: 'user', row})">
                重报价
              </a-button>
            </template>
          </vxe-grid>
        </div>
      </div>
      <const-form ref="constForm"/>
      <enquiry-column-setting
        ref="enquiryColumnSetting"
        @ok="columnSettingOk"/>
      <enquiry-price-notice
        ref="enquiryPriceNotice"
        @success="getData"/>
      <enquiry-price-submit
        ref="enquiryPriceSubmit"
        @success="getData"/>
      <enquiry-regret
        ref="enquiryRegret"
        @success="getData"/>
      <enquiry-re-quote
        ref="enquiryReQuote"
        @success="getData"/>
      <enquiry-supplier-setting
        ref="enquirySupplierSetting"
        @ok="supplierSettingOk"/>
      <field-select-modal
        is-emit
        ref="fieldSelectModal"
        @ok="fieldSelectOk"/>
      <trend-chart-of-historical-quotations ref="trendChartOfHistoricalQuotations"/>
      <trend-chart-of-round-quotations ref="trendChartOfRoundQuotations"/>
      <submit-priced ref="submitPriced"/>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import {REPORT_ADDRESS} from '@/utils/const'
import {nominalEdgePullWhiteBlack} from '@/utils/util'
import {ajaxGetAllColumns, ajaxFindDictItems} from '@api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import {downFile, getAction, postAction} from '@api/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import ConstForm from './components/ConstForm'
import EnquiryColumnSetting from './components/EnquiryColumnSetting'
import EnquiryHallButton from './components/EnquiryHallButton'
import EnquiryMaterialInfo from './components/EnquiryMaterialInfo'
import EnquiryPriceNotice from './components/EnquiryPriceNotice'
import EnquiryPriceSubmit from './components/EnquiryPriceSubmit'
import EnquiryRegret from './components/EnquiryRegret'
import EnquiryReQuote from './components/EnquiryReQuote'
import EnquirySupplierSetting from './components/EnquirySupplierSetting'
import TrendChartOfHistoricalQuotations from './components/TrendChartOfHistoricalQuotations'
import TrendChartOfRoundQuotations from './components/TrendChartOfRoundQuotations'
import SubmitPriced from '../../../component/SubmitPriced'
import { currency } from '@/filters'
import {add} from '@/utils/mathFloat.js'
import {
    CHECKBOX_COLUMN,
    COMPARE_COLUMN,
    COMPARE_HEAD_COLUMN,
    COST_COLUMN,
    CURRENT_NET_PRICE,
    CURRENT_PRICE,
    LADDER_COLUMN,
    MATERIAL_COLUMN,
    // MATERIAL_COLUMNS,
    OPERATION_COLUMN,
    PACKAGE_COLUMNS,
    PAGE_HEADER_TITLE,
    QUOTA,
    STATUS,
    QUOTA_QUANTITY,
    QUOTA_SCALE,
    SEQ_COLUMN,
    BARGAIN_REMARK
} from './constant'

export default {
    components: {
        ConstForm,
        EnquiryColumnSetting,
        EnquiryHallButton,
        EnquiryMaterialInfo,
        EnquiryPriceNotice,
        EnquiryPriceSubmit,
        EnquiryRegret,
        EnquiryReQuote,
        EnquirySupplierSetting,
        fieldSelectModal,
        TrendChartOfHistoricalQuotations,
        TrendChartOfRoundQuotations,
        SubmitPriced
    },
    computed: {
        reQuoteDisabled (){
            
            if (this.currentTab != 'Material') {
                let ListMap = {
                    'NormalCompare': this.normalList,
                    'LadderCompare': this.ladderList,
                    'CostCompare': this.costList,
                    'PackageCompare': this.supplierList
                }
                let total =  ListMap[this.currentTab].reduce((acc, item) => {
                    acc = add(acc, item.quotaScale)
                    return acc
                }, 0)
                console.log(total)
                if (total >= 100) return true
            }
            if (this.$hasOptAuth('enquiry#purchaseEnquiryHead:reQuote')) {
                return false
            } else {
                return true
            }
            
            // return !this.$hasOptAuth('enquiry#purchaseEnquiryHead:reQuote')
        },
        vxeGridHeight (){
            return document.documentElement.clientHeight - 240
        }
    },
    created () {
        nominalEdgePullWhiteBlack()
    },
    data (){
        return{
            contentHeaderButton: [
                {click: this.handleSupplement, disabled: this.allowSupplement, title: '补充物料编码', type: 'primary'},
                {
                    click: key => this.handleSet(key),
                    key: 'dropdown',
                    menus: [
                        {key: 'enquiryHallCompareItems', title: '对比项设置'},
                        {key: 'materialInfoFields', title: '物料信息设置'},
                        {key: 'supplier', title: '供应商设置'}
                    ],
                    title: '比价设置',
                    type: 'primary'
                },
                {
                    click: key => this.handleTrendChart(key),
                    disabled: this.allowTrendChart,
                    key: 'dropdown',
                    menus: [
                        {key: 'roundQuotations', title: '轮次报价趋势图'},
                        {key: 'historicalQuotations', title: '历史报价趋势图'}
                    ],
                    title: '报价趋势图',
                    type: 'primary'
                },
                {
                    click: (key) => this.handleAward({range: 'material', type: key}),
                    key: 'dropdown',
                    menus: [
                        {key: 'minPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_enxftu_48cb3e13`, '最低含税价')},
                        {key: 'minNetPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_enLftu_516dd6b4`, '最低未税单价')},
                        {key: 'minPackagePrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_enfsu_d0fd2317`, '最低打包价')}
                    ],
                    show: this.showAward,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IIlB_25b1692d`, '一键授标'),
                    type: 'primary'
                },
                // {
                //     click: (key) => this.handleAward({range: 'group', type: key}),
                //     key: 'dropdown',
                //     menus: [
                //         {key: 'minPrice', title: '最低含税价'},
                //         {key: 'minNetPrice', title: '最低未税价'}
                //     ],
                //     show: this.showAward,
                //     title: '分组授标',
                //     type: 'primary'
                // },
                // {
                //     click: () => this.handleAward({range: 'supplier'}),
                //     show: this.showAward,
                //     title: '供应商授标',
                //     type: 'primary'
                // },
                // {click: this.handleManualAward, title: '确认授标', type: 'primary'},
                {
                    authorityCode: 'enquiry#purchaseEnquiryHead:reQuote',
                    click: (key) => this.handleReQuote({reQuoteWay: key}),
                    key: 'dropdown',
                    menus: [
                        {key: 'all', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_2f691299`, '整单重报')},
                        {key: 'material', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_iSLss_cb3a023c`, '整物料重报')},
                        {key: 'supplier', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_iRdXss_ad823c51`, '整供应商重报')},
                        {key: 'allAccept', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_allAccept`, '全部物料接受')},
                        {key: 'allReject', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_allReject`, '全部物料拒绝')},
                        {key: 'partReject', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_itss_partReject`, '余行全部拒绝')}
                    ],
                    show: this.showReQuote,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_restatement1`, '批量操作'),
                    type: 'primary'
                },
                {click: this.handleExport, title: '导出', type: 'primary'}
                // {click: this.handleReport, title: '报表', type: 'primary'}
            ],
            costList: [],
            currentNetPrice: CURRENT_NET_PRICE,
            currentPrice: CURRENT_PRICE,
            currentTab: 'Material',
            editConfig: {mode: 'cell', trigger: 'click'},
            enquiryHallCompareItems: [],
            fieldSelectType: '',
            form: {},
            gridConfig: {
                border: true,
                resizable: true,
                size: 'mini',
                stripe: true,
                showHeaderOverflow: true
            },
            ladderList: [],
            materialInfoFields: [
                {hidden: '0', columnCode: 'materialNumber', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码')},
                {hidden: '0', columnCode: 'materialName', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称')},
                {hidden: '0', columnCode: 'materialDesc', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述')},
                {hidden: '0', columnCode: 'materialSpec', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格')},
                {hidden: '0', columnCode: 'requireQuantity', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量')},
                {hidden: '0', columnCode: 'purchaseUnit_dictText', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位')},
                {hidden: '0', columnCode: 'futurePrice', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IBtu_37a0e2db`, '目标单价')},
                {hidden: '0', columnCode: 'factory_dictText', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂')},
                {hidden: '0', columnCode: 'storageLocation_dictText', columnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageLocation`, '库存地点')}
            ],
            materials: [],
            mergeCells: [],
            normalList: [],
            pageHeaderButton: [
                {click: this.handleNoticeSet, title: '定价通知', type: 'primary'},
                {
                    authorityCode: 'enquiry#purchaseEnquiryHead:generatePriceRecord',
                    click: this.handleGenerate,
                    disabled: this.allowGenerate,
                    title: '生成价格记录',
                    type: 'primary'
                },
                {
                    click: (key) => this.handlePriceSubmit(key),
                    key: 'dropdown',
                    menus: [
                        {key: 'whole', title: '整单提交'},
                        {disabled: this.allowSubmitByRow, key: 'row', title: '按行提交'}
                    ],
                    show: this.showSubmit,
                    title: '提交定价',
                    type: 'primary'
                },
                {
                    click: this.submitEvaluationPrice,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_DJnu_2e936d93`, '提交核价'),
                    type: 'primary'
                },
                {
                    authorityCode: 'enquiry#purchaseEnquiryHead:regret',
                    click: (key) => this.handleRegret(key),
                    key: 'dropdown',
                    menus: [
                        {key: 'whole', title: '整单悔标'},
                        {disabled: this.allowRegretByRow, key: 'material', title: '按物料悔标'}
                    ],
                    show: this.showRegret,
                    title: '悔标',
                    type: 'danger'
                },
                {click: this.getData, title: '刷新', type: 'primary'}
            ],
            pageHeaderTitle: PAGE_HEADER_TITLE,
            showOverflow: false,
            sourceId: '',
            spinning: false,
            supplierList: [],
            suppliers: [],
            tableColumns: [],
            tableData: [],
            tabs: [
                {disabled: false, key: 'Material', title: '物料行'},
                {cellStyleFn: this.cellStyleNormal, disabled: false, key: 'NormalCompare', title: '常规比价'},
                {cellStyleFn: this.cellStyleLadder, disabled: false, key: 'LadderCompare', title: '阶梯比价'},
                {cellStyleFn: this.cellStyleCost, disabled: false, key: 'CostCompare', title: '成本比价'},
                {cellStyleFn: this.cellStylePackage, disabled: false, key: 'PackageCompare', title: '打包比价'}
            ],
            pageConfig: {},
            // old
            url: { detail: '/enquiry/purchaseEnquiryHead/getData' },
            quotaWayDictText: null,
            packageOptFrist: null,
            packagesList: [],
            compareItems: [],
            materialColumns: [
                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 200  },
                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150 },
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 250 },
                { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 250 },
                // { field: 'factory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'), width: 250 },
                // { field: 'storageLocation_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageLocation`, '库存地点'), width: 250 },
                { field: 'quantityUnit_dictText', title: '主单位', width: 75 },
                { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 250 },
                { field: 'requireQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireQuantity`, '需求数量'), width: 120 },
                { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireDate`, '需求日期'), width: 250 }
            ]
        }
    },
    methods: {
        /**
         * 生成价格记录按钮状态控制
         * currentTab PackageCompare-打包比价
         * priceCreateWay 2-手动生成
         * @return {boolean}
         */
        allowGenerate (){
            const {priceCreateWay} = this.form
            return this.currentTab === 'PackageCompare' || priceCreateWay !== '2'
        },
        /**
         * 按行悔标按钮状态控制
         * currentTab PackageCompare-打包比价
         * @return {boolean}
         */
        allowRegretByRow (){
            return this.currentTab === 'PackageCompare'
        },
        /**
         * 按行提交定价按钮状态控制
         * currentTab PackageCompare-打包比价
         * @return {boolean}
         */
        allowSubmitByRow (){
            return this.currentTab === 'PackageCompare'
        },
        /**
         * 补充物流编码按钮状态控制
         * currentTab Material-物料行
         * @return {boolean}
         */
        allowSupplement (){
            return this.currentTab !== 'Material'
        },
        /**
         * 趋势图按钮状态控制
         * currentTab Material-打包比价
         * @return {boolean}
         */
        allowTrendChart (){
            return this.currentTab === 'PackageCompare'
        },
        cellStyle (row, column, key){
            let tab = this.tabs.find(item => item.key == key)
            return tab.cellStyleFn && tab.cellStyleFn({row, column})
        },
        /**
         * 设置成本报价单元格样式
         * @param row
         * @param column
         * @return {{color: string}}
         */
        cellStyleCost ({row, column}){
            if (column.property && column.property.indexOf('supplier_') !== -1) {
                const {columnCode, itemNumber, materialNumber} = row
                const toElsAccount = column.property.split('_')[1]
                const item = this.costList.find(item => item.itemNumber === itemNumber && item.materialNumber === materialNumber && item.toElsAccount === toElsAccount)
                if(item){
                    if(columnCode === 'price' && item.minPrice){
                        return {color: 'green'}
                    }
                    if(columnCode === 'price' && item.maxPrice){
                        return {color: 'red'}
                    }
                    if(columnCode === 'netPrice' && item.minNetPrice){
                        return {color: 'green'}
                    }
                    if(columnCode === 'netPrice' && item.maxNetPrice){
                        return {color: 'red'}
                    }
                }
            }
        },
        /**
         * 设置阶梯报价单元格样式
         * @param row
         * @param column
         * @return {{color: string}}
         */
        cellStyleLadder ({row, column}){
            if (column.property && column.property.indexOf('supplier_') !== -1) {
                const {columnCode, itemNumber, materialNumber} = row
                const toElsAccount = column.property.split('_')[1]
                const item = this.ladderList.find(item => item.itemNumber === itemNumber && item.materialNumber === materialNumber && item.toElsAccount === toElsAccount)
                if(item){
                    if(columnCode === 'price' && item.minPrice){
                        return {color: 'green'}
                    }
                    if(columnCode === 'price' && item.maxPrice){
                        return {color: 'red'}
                    }
                    if(columnCode === 'netPrice' && item.minNetPrice){
                        return {color: 'green'}
                    }
                    if(columnCode === 'netPrice' && item.maxNetPrice){
                        return {color: 'red'}
                    }
                }
            }
        },
        /**
         * 设置常规报价单元格样式
         * @param row
         * @param column
         * @return {{color: string}}
         */
        cellStyleNormal ({row, column}){
            if (column.property && column.property.indexOf('supplier_') !== -1) {
                const {columnCode, itemNumber, materialNumber} = row
                const toElsAccount = column.property.split('_')[1]
                const item = this.normalList.find(item => item.itemNumber === itemNumber && item.materialNumber === materialNumber && item.toElsAccount === toElsAccount)
                if(item){
                    if (columnCode === 'price' && item.minPrice) {
                        return {color: 'green'}
                    }
                    if (columnCode === 'price' && item.maxPrice) {
                        return {color: 'red'}
                    }
                    if (columnCode === 'netPrice' && item.minNetPrice) {
                        return {color: 'green'}
                    }
                    if (columnCode === 'netPrice' && item.maxNetPrice) {
                        return {color: 'red'}
                    }
                }
            }
        },
        /**
         * 设置打包比价单元格样式
         * @param row
         * @param column
         * @return {{color: string}}
         */
        cellStylePackage ({row, column}){
            if(column.property === 'netAmount' && row.maxNetAmount){
                return {color: 'red'}
            }
            if(column.property === 'taxAmount' && row.maxTaxAmount){
                return {color: 'red'}
            }
            if(column.property === 'netAmount' && row.minNetAmount){
                return {color: 'green'}
            }
            if(column.property === 'taxAmount' && row.minTaxAmount){
                return {color: 'green'}
            }
        },
        /**
         * 分组编码修改事件
         * @param row
         * @param column
         * @param value
         */
        changeGroupNo (row, column, value){
            const material = this.materials.find(material => material.itemNumber === row.itemNumber && material.materialNumber === row.materialNumber)
            this.$set(material, column.property, value)
        },
        /**
         * 拆分数量修改事件
         * @param row 修改行
         * @param toElsAccount 供应商号
         * @param value 修改值
         * @param listName 修改的列表名称
         */
        changeQuotaQuantity (row, toElsAccount, value, listName){
            const {columnCode, itemNumber, materialNumber} = row
            const currentItem = this[listName].find(item => item.toElsAccount === toElsAccount && item.itemNumber === itemNumber && item.materialNumber === materialNumber)
            this.$set(currentItem, columnCode, value)
        },
        /**
         * 拆分比例修改事件
         * @param row 修改行
         * @param toElsAccount 供应商号
         * @param value 修改值
         * @param listName 修改的列表名称
         */
        changeQuotaScale (row, toElsAccount, value, listName){
            const {columnCode, itemNumber, materialNumber} = row
            const currentItem = this[listName].find(item => item.toElsAccount === toElsAccount && item.itemNumber === itemNumber && item.materialNumber === materialNumber)
            this.$set(currentItem, columnCode, value)
        },
        columnSettingOk ({columnCode, data}) {
            const fn = this[`${columnCode}SettingOk`]
            fn && fn(data)
        },
        /**
         * 对比项显隐设置确认回调
         */
        enquiryHallCompareItemsSettingOk (data = []){
            if (data.length > 0) this.enquiryHallCompareItems = data
            if (this.enquiryHallCompareItems.length > 0) { 
                const fn = this[`set${this.currentTab}Grid`]
                fn && fn()
                return
            }
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEnquiryCompare'
            }
            ajaxFindDictItems(postData).then(res => {
                if (res.success) {
                    this.enquiryHallCompareItems = res.result.map(i => {
                        return { columnName: i.text, columnCode: i.value, hidden: '0'}
                    })
                    const price = this.enquiryHallCompareItems.find(compareItem => compareItem.columnCode === 'price')
                    this.$set(this.currentPrice, 'hidden', price.hidden)
                    const netPrice = this.enquiryHallCompareItems.find(compareItem => compareItem.columnCode === 'netPrice')
                    this.$set(this.currentNetPrice, 'hidden', netPrice.hidden)
                    const fn = this[`set${this.currentTab}Grid`]
                    fn && fn()
                }
            })
        },
        fieldSelectOk (data){
            if(this.fieldSelectType === 'material'){
                // 补充物料编码选择确认回调
                const checkboxRecords = this.$refs[this.currentTab].getCheckboxRecords()
                const {materialDesc, materialGroup, materialGroupName, materialModel, materialNumber, materialSpec, id} = data[0]
                const row = {
                    ...checkboxRecords[0],
                    materialId: id,
                    materialDesc,
                    materialGroup,
                    materialGroupName,
                    materialModel,
                    materialNumber,
                    materialSpec
                }
                this.spinning = true
                postAction('/enquiry/purchaseEnquiryHead/replenishMaterialNumber', row).then(res => {
                    if(res.success){
                        this.$notification.success({description: res.message, message: '成功'})
                        this.getData()
                    }else{
                        this.$notification.warning({description: res.message, message: '警告'})
                    }
                }).finally(() => {
                    this.spinning = false
                })
            }
            if(this.fieldSelectType === 'supplier'){
                // 供应商授标选择确认回调
                this.spinning = true
                const supplierList = data.map(supplier => ({...supplier, headId: this.sourceId}))
                postAction('/enquiry/purchaseEnquiryHead/supplierAward', supplierList).then(res => {
                    if(res.success){
                        this.$notification.success({description: res.message, message: '成功'})
                        this.getData()
                    }else{
                        this.$notification.warning({description: res.message, message: '警告'})
                    }
                }).finally(() => {
                    this.spinning = false
                })
            }
            if (['allAccept', 'allReject'].includes(this.fieldSelectType)) {
                let param = {}
                param['id'] = this.sourceId
                param['packageOpt'] = false
                param['purchaseEnquiryItemList'] = data
                this.spinning = true
                postAction(`/enquiry/purchaseEnquiryHead/${this.fieldSelectType}`, param).then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.getData()
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.spinning = false
                })
            }
        },
        /**
         * 获取供应商动态列信息
         * @param suppliers 参与比价的供应商
         * @param listName 修改的列表名称
         * @return {*}
         */
        getSupplierColumns (suppliers, listName){
            return suppliers.map(supplier => ({
                align: 'center',
                field: `supplier_${supplier.toElsAccount}`,
                editRender: {enabled: true},
                slots: {
                    edit: ({row, column}) => {
                        if(['quotaQuantity', 'quotaScale'].includes(row.columnCode)){
                            let show = (() => {
                                if ('quotaQuantity' == row.columnCode) {
                                    return this.form.quotaWay === '1'
                                } else {
                                    return this.form.quotaWay === '0'
                                }
                            })()
                            const props = {disabled: row[`${column.property}_accept`], max: row.columnCode == 'quotaQuantity' ? row.requireQuantity : 100, min: 0, type: 'number'}
                            const on = {change: ({value}) => {this.changeQuotaQuantity(row, supplier.toElsAccount, value, listName)}}
                            return show ? [<vxe-input vModel={row[column.property]} {...{on, props}}/>] : [<span>{row[column.property]}</span>]
                        }else if(row.columnCode === 'bargainRemark'){
                            const props = {disabled: row[`${column.property}_accept`]}
                            const on = {change: ({value}) => {this.changeQuotaScale(row, supplier.toElsAccount, value, listName)}}
                            return [<vxe-input vModel={row[column.property]} {...{on, props}}/>]
                        }
                        else{
                            return [<span>{row[column.property]}</span>]
                        }
                    },
                    default: ({row, column}) => {
                        if(row.columnCode === 'acceptQuota'){
                            return [
                                <div>
                                    <vxe-button type="text" status="primary" {...{on: {click: () => {this.handleContract(row, supplier, false, 'accept', listName)}}, props: {disabled: row[`${column.property}_accept`]}}}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accept`, '接受')}</vxe-button>
                                    <vxe-button type="text" status="primary" {...{on: {click: () => {this.handleContract(row, supplier, false, 'reject', listName)}}, props: {disabled: row[`${column.property}_reject`]}}}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝')}</vxe-button>
                                    <vxe-button type="text" status="primary" {...{on: {click: () => {this.handleContract(row, supplier, false, 'revoke', listName)}}, props: {disabled: row[`${column.property}_revoke`]}}}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销')}</vxe-button>
                                </div>
                            ]
                        } else {
                            return [<span>{row[column.property]}</span>]
                        }
                    }
                },
                title: supplier.supplierName,
                width: 180
            }))
        },
        /**
         * 一键授标
         * @param range 授标范围
         * @param type 授标规则
         */
        handleAward ({range, type}){
            if(range === 'group'){
                // 分组授标
                postAction('/enquiry/purchaseEnquiryHead/saveGroup', {id: this.sourceId, purchaseEnquiryItemList: this.materials}).then(res => {
                    if(res.success){
                        this.spinning = true
                        getAction('/enquiry/purchaseEnquiryHead/fastAward', {headId: this.sourceId, range, type}).then(res => {
                            if(res.success){
                                this.$notification.success({description: res.message, message: '成功'})
                                this.getData()
                            }else{
                                this.$notification.warning({description: res.message, message: '警告'})
                            }
                        }).finally(() => {
                            this.spinning = false
                        })
                    }
                })
            }else if(range === 'supplier'){
                // 供应商授标
                this.fieldSelectType = 'supplier'
                const modalColumns = [
                    {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150},
                    {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150},
                    {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200},
                    {field: 'needCoordination_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), width: 200}
                ]
                const modalUrl = '/enquiry/purchaseEnquiryHead/reQuoteSupplierList'
                this.$refs.fieldSelectModal.open(modalUrl, {headId: this.sourceId}, modalColumns, 'single')
            }else{
                const callback = () => {
                    this.spinning = true
                    getAction('/enquiry/purchaseEnquiryHead/oneAward', {'headId': this.sourceId, 'optType': type}).then((res) => {
                        if (res.success) {
                            this.$message.success(res.message)
                            this.getData()
                        } else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.spinning = false
                    })
                }
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLlB_3928214c`, '是否确认授标'),
                    onOk () {
                        callback && callback()
                    }
                })
            }
        },
        handleCostForm (row){
            console.log('row', row.supplier_priceType)
            let {templateNumber, templateVersion, templateAccount, templateName} = row.costFormJson ? JSON.parse(row.costFormJson) : {}
            const currentEditRow = {
                busAccount: this.form.busAccount,
                templateAccount,
                templateName,
                templateNumber,
                templateVersion,
                role: 'purchase'
            }
            const suppliers = this.suppliers.filter(supplier => supplier.hidden === '0')
            this.$refs.constForm.open({currentEditRow, suppliers, row})
        },
        handleExport (){
            this.spinning = true
            downFile('/enquiry/purchaseEnquiryHead/exportBargain', {headId: this.sourceId}).then((data) => {
                if (!data) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败') )
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表')+'.xlsx')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表')+'.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            }).finally(() => {
                this.spinning = false
            })
        },
        handleGenerate (){
            const checkboxRecords = this.$refs[this.currentTab].getCheckboxRecords()
            if(!checkboxRecords.length){
                this.$notification.warning({description: '请选择需要生成价格记录的物料', message: '警告'})
                return
            }
            if(checkboxRecords.some(checkboxRecord => checkboxRecord.itemStatus === '11')){
                this.$notification.warning({description: '已悔标物料不可生成价格记录', message: '警告'})
                return
            }
            const that = this
            this.$confirm({
                title: '提示',
                content: '此操作将生成价格记录, 是否继续',
                onOk: () => {
                    const params = {...that.form, purchaseEnquiryItemList: checkboxRecords}
                    that.spinning = true
                    postAction('/enquiry/purchaseEnquiryHead/generatePriceRecord', params).then(res => {
                        if(res.success){
                            that.$notification.success({description: res.message, message: '成功'})
                            that.getData()
                        }else{
                            that.$notification.warning({description: res.message, message: '警告'})
                        }
                    }).finally(() => {
                        that.spinning = false
                    })
                }
            })
        },
        handleManualAward (){
            const that = this
            this.$confirm({
                title: '提示',
                content: '此操作将手动授标, 是否继续',
                onOk: () => {
                    const params = {
                        id: this.sourceId,
                        purchaseEnquiryItemList: [...this.costList, ...this.ladderList, ...this.normalList]
                    }
                    that.spinning = true
                    postAction('/enquiry/purchaseEnquiryHead/manualAward', params).then(res => {
                        if(res.success){
                            that.$notification.success({description: res.message, message: '成功'})
                            that.getData()
                        }else{
                            that.$notification.warning({description: res.message, message: '警告'})
                        }
                    }).finally(() => {
                        that.spinning = false
                    })
                }
            })
        },
        handleNoticeSet (){
            this.$refs.enquiryPriceNotice.open({headId: this.sourceId, pricingNotice: this.form.pricingNotice})
        },
        handlePriceSubmit (key){
            let purchaseEnquiryItemList = []
            if(key === 'whole'){
                purchaseEnquiryItemList = this.materials
            }
            if(key === 'row'){
                const checkboxRecords = this.$refs[this.currentTab].getCheckboxRecords()
                if(!checkboxRecords.length){
                    this.$notification.warning({description: '请选择需要提交定价的物料', message: '警告'})
                    return
                }
                purchaseEnquiryItemList = checkboxRecords
            }
            // this.$refs.enquiryPriceSubmit.open({headId: this.sourceId, purchaseEnquiryItemList})
            this.$refs.submitPriced.awardOpinion = null
            this.$refs.submitPriced.open(this.sourceId, purchaseEnquiryItemList, key)
        },
        handleRegret (key){
            if(key === 'whole'){
                this.$refs.enquiryRegret.openWhole({headId: this.sourceId})
            }
            if(key === 'material'){
                const checkboxRecords = this.$refs[this.currentTab].getCheckboxRecords()
                if(!checkboxRecords.length){
                    this.$notification.warning({description: '请选择需要提交定价的物料', message: '警告'})
                    return
                }
                this.$refs.enquiryRegret.openMaterial({headId: this.sourceId, purchaseEnquiryItemList: checkboxRecords})
            }
        },
        handleReport (){
            const token = this.$ls.get('Access-Token')
            const url = `${REPORT_ADDRESS}/els/report/jmreport/view/824096099903758336?token=${token}&id=${this.sourceId}`
            window.open(url, '_blank')
        },
        handleReQuote ({reQuoteWay, row}){
            if(reQuoteWay === 'all'){
                this.$refs.enquiryReQuote.openAll({headId: this.sourceId})
            }
            if(reQuoteWay === 'supplier'){
                this.$refs.enquiryReQuote.openSupplier({headId: this.sourceId})
            }
            if(reQuoteWay === 'material'){
                this.$refs.enquiryReQuote.openMaterial({headId: this.sourceId})
            }
            if(reQuoteWay === 'user'){
                const list = JSON.parse(JSON.stringify([...this.normalList, ...this.ladderList, ...this.costList]))
                const enquiryItemList = list.filter(item => item.materialNumber === row.materialNumber && item.itemNumber === row.itemNumber && ['2', '6', '3', '8'].includes(item.itemStatus))
                this.$refs.enquiryReQuote.openUser({headId: this.sourceId, enquiryItemList})
            }
            if (['allAccept', 'allReject', 'partReject'].includes(reQuoteWay)) this.patchOperate(reQuoteWay)
        },
        patchOperate (type) {
            let callback = () => {
            // 弹窗选择供应商
                if(['allAccept', 'allReject'].includes(type)) {
                    this.fieldSelectType = type
                    const modalColumns = [
                        {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 140},
                        {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 140},
                        {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200},
                        {field: 'needCoordination_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), width: 140}
                    ]
                    if (type == 'allAccept') modalColumns.push(
                        {field: 'quotaScale', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比'), width: 130, 
                            slots: {
                                default: ({row, column}) => {
                                    const props = { max: 100, min: 0, type: 'number'}
                                    return [<vxe-input vModel={row[column.property]} {...{props}}/>]                                
                                }
                            }
                        }
                    )
                    const modalUrl = '/enquiry/purchaseEnquiryHead/reQuoteSupplierList'
                    this.$refs.fieldSelectModal.open(modalUrl, {headId: this.sourceId}, modalColumns, 'multiple')
                } else{
                    let param = {}
                    param['id'] = this.sourceId
                    param['packageOpt'] = true
                    param['purchaseEnquiryItemList'] = this.supplierList
                    this.spinning = true
                    postAction('/enquiry/purchaseEnquiryHead/partReject', param).then((res) => {
                        if (res.success) {
                            this.$message.success(res.message)
                            this.getData()
                        } else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.spinning = false
                    })
                }
            }
            let titleMap = {
                allAccept: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_KQRLdRRdXbxSLyl_c6e429a7`, '是否确认相关供应商全部物料接受'),
                allReject: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_KQRLdRRdXbxSLFK_c6e43b20`, '是否确认相关供应商全部物料拒绝'),
                partReject: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_KQRLAvSLcbxFK_6bdd50ed`, '是否确认其它物料行全部拒绝')
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: titleMap[type],
                onOk () {
                    callback && callback()
                }
            })
        },
        handleSet (columnCode){
            if(columnCode === 'supplier'){
                this.$refs.enquirySupplierSetting.open({tableData: this.suppliers})
            }else{
                this.$refs.enquiryColumnSetting.open({columnCode, tableData: this[columnCode]})
            }
        },
        handleSupplement (){
            this.fieldSelectType = 'material'
            const checkboxRecords = this.$refs[this.currentTab].getCheckboxRecords()
            if(checkboxRecords.length !== 1){
                this.$notification.warning({description: '请选择一条数据', message: '警告'})
                return
            }
            if(checkboxRecords[0].materialNumber){
                this.$notification.warning({description: '所选数据已存在物料编码, 无需补充', message: '警告'})
                return
            }
            const modalColumns = [
                {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150},
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150},
                {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200},
                {field: 'brand', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialBrand`, '物料品牌'), width: 200},
                {field: 'purchaseCycle', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), width: 200},
                {field: 'purchaseType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseType`, '采购类型'), width: 200},
                {field: 'checkWay_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkWay`, '检验方式'), width: 200},
                {field: 'purchaseUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingUnit`, '采购单位'), width: 200},
                {field: 'materialModel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialModel`, '物料型号'), width: 200},
                {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料规格'), width: 200}
            ]
            const modalUrl = '/material/purchaseMaterialHead/list'
            this.$refs.fieldSelectModal.open(modalUrl, {}, modalColumns, 'single')
        },
        handleTabChange (activeKey){
            if (activeKey == 'Material') {
                const fn = this[`set${activeKey}Grid`]
                fn && fn()
            } else {
                this.getData()
            }
        },
        handleTrendChart (type){
            const checkboxRecords = this.$refs[this.currentTab].getCheckboxRecords()
            if(!checkboxRecords.length){
                this.$notification.warning({description: '请勾选行数据', message: '警告'})
                return
            }
            if(type === 'roundQuotations'){
                this.$refs.trendChartOfRoundQuotations.open({headId: this.sourceId, row: checkboxRecords[0]})
            }
            if(type === 'historicalQuotations'){
                const {itemNumber, materialNumber} = checkboxRecords[0]
                const list = JSON.parse(JSON.stringify([...this.normalList, ...this.ladderList, ...this.costList]))
                const rows = list.filter(item => item.itemNumber = itemNumber && item.materialNumber === materialNumber)
                this.$refs.trendChartOfHistoricalQuotations.open({materialNumber, rows})
            }
        },
        materialInfoFieldsSettingOk (data){
            this.materialInfoFields = data
        },
        async getDetailData () {
            this.spinning = true
            const res = await getAction(this.url.detail, { id: this.sourceId })
            if (res && res.success) {
                this.quotaWayDictText = res.result.quotaWay_dictText // 拆分方式
                this.packageOptFrist = res.result.packageOptFrist
            }
            this.spinning = false
        },
        getData () {
            const params = {
                headId: this.sourceId
            }
            const apiQueryMaterialList = getAction('/enquiry/purchaseEnquiryHead/queryMaterialList', params)
            const querySupplier = getAction('/enquiry/purchaseEnquiryHead/querySupplier', params)
            const queryCompare = getAction('/enquiry/purchaseEnquiryItemHis/queryCompareForLast', params)

            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))
            const promiseList = [
                apiQueryMaterialList,
                querySupplier,
                queryCompare
            ]
            this.spinning = true
            console.time('接口')
            Promise.all(handlePromise(promiseList)).then(res => {
                console.timeEnd('接口')
                this.spinning = false
                try{
                    console.time('遍历数据')
                    const [infoRes, supplierRes, compareRes] = res || []
                    if (infoRes && infoRes.status === 'success') {
                        this.fixInfoData(infoRes.res)
                    }
                    if(supplierRes && supplierRes.status === 'success'){
                        this.suppliers = supplierRes.res.result.map(supplier => ({...supplier, hidden: '0'}))
                    }
                    if(compareRes && compareRes.status === 'success'){
                        const {costList = [], ladderList = [], normalList = [], supplierList = []} = compareRes.res.result
                        this.costList = costList
                        this.ladderList = ladderList
                        this.normalList = normalList
                        this.supplierList = supplierList
                        this.tabs.forEach(tab => {
                            if(tab.key === 'NormalCompare'){
                                this.$set(tab, 'disabled', !this.normalList.length)
                            }
                            if(tab.key === 'CostCompare'){
                                this.$set(tab, 'disabled', !this.costList.length)
                            }
                            if(tab.key === 'LadderCompare'){
                                this.$set(tab, 'disabled', !this.ladderList.length)
                            }
                            if(tab.key === 'PackageCompare'){
                                this.$set(tab, 'disabled', !this.supplierList.length)
                            }
                        })
                    }
                    this.enquiryHallCompareItemsSettingOk()
                    console.timeEnd('遍历数据')
                }
                catch{
                    this.spinning = false
                }
            })
        },
        fixInfoData ({ result }) {
            const { purchaseEnquiryItemList = [], ...others } = result || {}
            this.purchaseEnquiryItemList = purchaseEnquiryItemList
            if (purchaseEnquiryItemList.length) {
                this.itemNumber = purchaseEnquiryItemList[0].itemNumber || '1'
            }
            this.materials = purchaseEnquiryItemList.map((material, index) => ({...material, groupNo: material.groupNo || `${index + 1}`}))
            this.form = {
                ...others
            }
        },
        setCostCompareGrid () {
            const costMaterials = this.materials.filter(material => material.quotePriceWay === '2')
            const filters = costMaterials.map(material => ({label: material.materialName, value: material.materialNumber}))
            const suppliers = this.suppliers.filter(supplier => supplier.hidden === '0')
            const supplierColumns = this.getSupplierColumns(suppliers, 'costList')
            this.tableColumns = [
                CHECKBOX_COLUMN,
                {
                    ...MATERIAL_COLUMN,
                    filters,
                    slots: {
                        default: ({row}) => {
                            return [<enquiry-material-info columns={this.materialInfoFields} row={row}/>]
                        }
                    }
                },
                {
                    ...COMPARE_HEAD_COLUMN,
                    children: [
                        COMPARE_COLUMN, 
                        {
                            ...COST_COLUMN,
                            slots: {
                                default: ({row, column}) => {
                                    return [<a onClick={() => this.handleCostForm(row, column)}>{row[column.property]}</a>]
                                }
                            }
                        }
                    ]
                },
                ...supplierColumns,
                OPERATION_COLUMN
            ]
            let current = 0
            const compareColCells = []
            const compareRowCells = []
            const materialCompareMap = {}
            const costData = []
            const enquiryHallCompareItems = [...this.enquiryHallCompareItems, this.currentPrice, this.currentNetPrice, QUOTA, STATUS, QUOTA_QUANTITY, QUOTA_SCALE]
            // 定义对比项 过滤隐藏项
            const compareItems = enquiryHallCompareItems.filter(item => item.hidden === '0')
            // 定义除去 含税价和非含税价的对比项
            const newCompareItems = compareItems.filter(item => !['netPrice', 'price'].includes(item.columnCode))
            // 成本物料遍历
            costMaterials.forEach(material => {
                const {itemNumber, materialNumber} = material
                let groupsLsit = []
                this.costList.filter(item => {
                    return item.itemNumber === itemNumber && item.materialNumber === materialNumber
                }).map(item => {
                    if(item.costFormJson) {
                        let {groups = []} = JSON.parse(item.costFormJson)
                        if (groups.length > 0) {
                            groupsLsit = groups
                        }
                    }
                })
                // 定义对比项的数组
                const costCompareItems = []
                compareItems.forEach(compareItem => {
                    if(['netPrice', 'price'].includes(compareItem.columnCode)){
                        if(groupsLsit.length > 0){
                            // 成本项的集合
                            const length = groupsLsit.length
                            if(compareItem.columnCode === 'price'){
                                compareRowCells.push({col: 2, colspan: 1, row: current, rowspan: length})
                            }
                            if(compareItem.columnCode === 'netPrice') {
                                if(compareItems.some(item => item.columnCode === 'price')){
                                    compareRowCells.push({col: 2, colspan: 1, row: current + length, rowspan: length})
                                }else{
                                    compareRowCells.push({col: 2, colspan: 1, row: current, rowspan: length})
                                }
                            }
                            groupsLsit.forEach(group => {
                                costCompareItems.push({...compareItem, costLabel: group.groupName, costValue: group.groupCode, isCost: true, netPrice: group.netPrice, price: group.price})
                            })
                        }
                    }
                })
                costCompareItems.push(...newCompareItems)
                materialCompareMap[`${materialNumber}_${itemNumber}`] = costCompareItems
                costCompareItems.forEach((compareItem, index) => {
                    current += 1
                    const realMaterial = {
                        ...JSON.parse(JSON.stringify(material)),
                        ...compareItem,
                        id: `${materialNumber}${itemNumber}${index}`
                    }
                    suppliers.forEach(supplier => {
                        const currentSupplierItem = this.costList.find(item => item.materialNumber === materialNumber && item.itemNumber === itemNumber && item.toElsAccount === supplier.toElsAccount)
                        if(currentSupplierItem) {
                            const key = `supplier_${currentSupplierItem.toElsAccount}`
                            if (compareItem.isCost) {
                                let JSONData = currentSupplierItem.costFormJson ? JSON.parse(currentSupplierItem.costFormJson) : {}
                                const {groups = []} = JSONData
                                let d = groups.filter(group => group.groupCode == compareItem.costValue)[0]
                                if (compareItem.columnCode === 'price') {
                                    realMaterial[key] =d?.price || ''
                                    realMaterial['supplier_priceType'] = compareItem.columnCode
                                } else if (compareItem.columnCode === 'netPrice') {
                                    realMaterial[key] = d?.netPrice == 0 ?  '' : d?.netPrice || ''
                                    realMaterial['supplier_priceType'] = compareItem.columnCode
                                }
                                realMaterial[`supplier_${currentSupplierItem.toElsAccount}_costFormJson`] = JSONData
                            } else {
                                if(compareItem.columnCode === 'currentPrice'){
                                    realMaterial[key] = currentSupplierItem.price
                                }else if(compareItem.columnCode === 'currentNetPrice'){
                                    realMaterial[key] = currentSupplierItem.netPrice
                                }else{
                                    realMaterial[key] = currentSupplierItem[compareItem.columnCode]
                                }
                                compareColCells.push({col: 2, colspan: 2, row: current - 1, rowspan: 1})
                            }
                            realMaterial[`supplier_${supplier.toElsAccount}_accept`] = (() => {
                                if (this.form.packageOptFrist === '1') return true
                                let f = !(['2', '8'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                                return f
                            })()
                            realMaterial[`supplier_${supplier.toElsAccount}_reject`] = (() => {
                                if (this.form.packageOptFrist === '1') return true
                                let f = !(['2', '8', '6', '3'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                                return f
                            })()
                            realMaterial[`supplier_${supplier.toElsAccount}_revoke`] = (() => {
                                if (this.form.packageOptFrist === '1') return true
                                if(currentSupplierItem.evaluationStatus== '4') return true
                                // 提交定价后不能撤销
                                if (currentSupplierItem.pricedFlag == '1') return true
                                // 审批中 不能撤销
                                if (currentSupplierItem.auditStatus == '1') return true
                                if (currentSupplierItem.itemStatus === '11') return true // 行悔标 不可操作
                                if (currentSupplierItem.itemStatus == '4' || currentSupplierItem.itemStatus == '5') return false // 行接受或拒绝 可操作
                                return !(['4', '5'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                            })()
                        }
                    })
                    costData.push(realMaterial)
                })
            })
            this.tableData = costData
            this.showOverflow = false
            const array = [0]
            const lengthArr = []
            costMaterials.forEach((material, index) => {
                const {itemNumber, materialNumber} = material
                const mLength = materialCompareMap[`${materialNumber}_${itemNumber}`].length
                lengthArr.push(mLength)
                array.push(array[index] + mLength)
            })
            const mergeCheckCells = lengthArr.map((item, index) => ({col: 0, colspan: 1, row: array[index], rowspan: item}))
            const mergeCells = lengthArr.map((item, index) => ({col: 1, colspan: 1, row: array[index], rowspan: item}))
            const mergeOperationCells = lengthArr.map((item, index) => ({col: suppliers.length + 4, colspan: 1, row: array[index], rowspan: item}))
            this.mergeCells = [...mergeCheckCells, ...mergeCells, ...compareRowCells, ...compareColCells, ...mergeOperationCells]
        },
        setLadderCompareGrid (){
            const ladderMaterials = this.materials.filter(material => material.quotePriceWay === '1')
            const filters = ladderMaterials.map(material => ({label: material.materialName, value: material.materialNumber}))
            const suppliers = this.suppliers.filter(supplier => supplier.hidden === '0')
            const supplierColumns = this.getSupplierColumns(suppliers, 'ladderList')
            this.tableColumns = [
                CHECKBOX_COLUMN,
                {
                    ...MATERIAL_COLUMN,
                    filters,
                    slots: {
                        default: ({row}) => {
                            return [<enquiry-material-info columns={this.materialInfoFields} row={row}/>]
                        }
                    }
                },
                {
                    ...COMPARE_HEAD_COLUMN,
                    children: [COMPARE_COLUMN, LADDER_COLUMN]
                },
                ...supplierColumns,
                OPERATION_COLUMN
            ]
            let current = 0
            const compareColCells = []
            const compareRowCells = []
            const materialCompareMap = {}
            const ladderData = []
            const enquiryHallCompareItems = [...this.enquiryHallCompareItems, this.currentPrice, this.currentNetPrice, QUOTA, STATUS, QUOTA_QUANTITY, QUOTA_SCALE]
            const compareItems = enquiryHallCompareItems.filter(item => item.hidden === '0')
            const newCompareItems = compareItems.filter(item => !['netPrice', 'price'].includes(item.columnCode))
            ladderMaterials.forEach(material => {
                const {itemNumber, materialNumber} = material
                const currentMaterial = this.ladderList.find(item => item.itemNumber === itemNumber && item.materialNumber === materialNumber)
                const ladderCompareItems = []
                compareItems.forEach(compareItem => {
                    if(['netPrice', 'price'].includes(compareItem.columnCode)){
                        if(currentMaterial && currentMaterial.ladderPriceJson){
                            const length = currentMaterial.ladderPriceJson.length
                            if(compareItem.columnCode === 'price'){
                                compareRowCells.push({col: 2, colspan: 1, row: current, rowspan: length})
                            }
                            if(compareItem.columnCode === 'netPrice') {
                                if(compareItems.some(item => item.columnCode === 'price')){
                                    compareRowCells.push({col: 2, colspan: 1, row: current + length, rowspan: length})
                                }else{
                                    compareRowCells.push({col: 2, colspan: 1, row: current, rowspan: length})
                                }
                            }
                            currentMaterial.ladderPriceJson.forEach(item => {
                                ladderCompareItems.push({...compareItem, ladderLabel: item.ladder, isLadder: true})
                            })
                        }
                    }
                })
                ladderCompareItems.push(...newCompareItems)
                materialCompareMap[`${materialNumber}_${itemNumber}`] = ladderCompareItems
                ladderCompareItems.forEach((compareItem, index) => {
                    current += 1
                    const realMaterial = {
                        ...JSON.parse(JSON.stringify(material)),
                        ...compareItem,
                        id: `${materialNumber}${itemNumber}${index}`
                    }
                    suppliers.forEach(supplier => {
                        const currentSupplierItem = this.ladderList.find(item => item.materialNumber === materialNumber && item.itemNumber === itemNumber && item.toElsAccount === supplier.toElsAccount)
                        if(currentSupplierItem) {
                            const key = `supplier_${currentSupplierItem.toElsAccount}`
                            if (compareItem.isLadder) {
                                if (compareItem.columnCode === 'price') {
                                    realMaterial[key] = currentSupplierItem[compareItem.ladderLabel].price
                                } else if (compareItem.columnCode === 'netPrice') {
                                    realMaterial[key] = currentSupplierItem[compareItem.ladderLabel].netPrice
                                }
                            } else {
                                if(compareItem.columnCode === 'currentPrice'){
                                    realMaterial[key] = currentSupplierItem.price
                                }else if(compareItem.columnCode === 'currentNetPrice'){
                                    realMaterial[key] = currentSupplierItem.netPrice
                                }else{
                                    realMaterial[key] = currentSupplierItem[compareItem.columnCode]
                                }
                                compareColCells.push({col: 2, colspan: 2, row: current - 1, rowspan: 1})
                            }
                            realMaterial[`supplier_${supplier.toElsAccount}_accept`] = (() => {
                                if (this.form.packageOptFrist === '1') return true
                                let f = !(['2', '8'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                                return f
                            })()
                            realMaterial[`supplier_${supplier.toElsAccount}_reject`] = (() => {
                                if (this.form.packageOptFrist === '1') return true
                                let f = !(['2', '8', '6', '3'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                                return f
                            })()
                            realMaterial[`supplier_${supplier.toElsAccount}_revoke`] = (() => {
                                if (this.form.packageOptFrist === '1') return true
                                if(currentSupplierItem.evaluationStatus== '4') return true
                                // 提交定价后不能撤销
                                if (currentSupplierItem.pricedFlag == '1') return true
                                // 审批中 不能撤销
                                if (currentSupplierItem.auditStatus == '1') return true
                                if (currentSupplierItem.itemStatus === '11') return true // 行悔标 不可操作
                                if (currentSupplierItem.itemStatus == '4' || currentSupplierItem.itemStatus == '5') return false // 行接受或拒绝 可操作
                                return !(['4', '5'].includes(currentSupplierItem.itemStatus) || (currentSupplierItem.itemStatus === '11' && currentSupplierItem.regretFlag === '2'))
                            })()
                        }
                    })
                    ladderData.push(realMaterial)
                })
            })
            this.tableData = ladderData
            this.showOverflow = false
            const array = [0]
            const lengthArr = []
            ladderMaterials.forEach((material, index) => {
                const {itemNumber, materialNumber} = material
                const mLength = materialCompareMap[`${materialNumber}_${itemNumber}`].length
                lengthArr.push(mLength)
                array.push(array[index] + mLength)
            })
            const mergeCheckCells = lengthArr.map((item, index) => ({col: 0, colspan: 1, row: array[index], rowspan: item}))
            const mergeCells = lengthArr.map((item, index) => ({col: 1, colspan: 1, row: array[index], rowspan: item}))
            const mergeOperationCells = lengthArr.map((item, index) => ({col: suppliers.length + 4, colspan: 1, row: array[index], rowspan: item}))
            this.mergeCells = [...mergeCheckCells, ...mergeCells, ...compareRowCells, ...compareColCells, ...mergeOperationCells]
        },
        setMaterialGrid (){
            this.tableColumns = [
                CHECKBOX_COLUMN, 
                SEQ_COLUMN,
                ...this.materialColumns
            ]
            this.tableData = this.materials
            this.showOverflow = true
            this.mergeCells = []
        },
        setNormalCompareGrid (){
            // 过滤常规报价物料
            const normalMaterials = this.materials.filter(material => material.quotePriceWay === '0')
            // 物料过滤项
            const filters = normalMaterials.map(material => ({label: material.materialName, value: material.materialNumber}))
            // 过滤隐藏的供应商
            const suppliers = this.suppliers.filter(supplier => supplier.hidden === '0')
            // 根据过滤的供应商显示多次个供应商列
            const supplierColumns = this.getSupplierColumns(suppliers, 'normalList')
            // 列设置
            this.tableColumns = [
                CHECKBOX_COLUMN,
                {
                    ...MATERIAL_COLUMN,
                    filters,
                    slots: {
                        default: ({row}) => {
                            return [<enquiry-material-info columns={this.materialInfoFields} row={row}/>]
                        }
                    }
                },
                COMPARE_COLUMN,
                ...supplierColumns,
                OPERATION_COLUMN
            ]
            // 定义列表数据
            const normalData = []
            // 对比项 + 状态列 + 操作列
            const enquiryHallCompareItems = [...this.enquiryHallCompareItems, QUOTA, STATUS, QUOTA_QUANTITY, QUOTA_SCALE, BARGAIN_REMARK]
            // 过滤隐藏对比项
            const compareItems = enquiryHallCompareItems.filter(item => item.hidden === '0')
            // 常规报价物料遍历
            normalMaterials.forEach(material => {
                const {itemNumber, materialNumber} = material
                // 对比项遍历
                compareItems.forEach((compareItem, index) => {
                    const realMaterial = {
                        ...JSON.parse(JSON.stringify(material)),
                        ...compareItem,
                        id: `${materialNumber}${itemNumber}${index}`
                    }
                    // 过滤完的供应商
                    suppliers.forEach(supplier => {
                        // 根据物料编码，物料行，供应商编码 获取到当前物料数据
                        const currentItem = this.normalList.find(item => item.itemNumber === itemNumber && item.materialNumber === materialNumber && item.toElsAccount === supplier.toElsAccount)
                        if(currentItem){
                            realMaterial[`supplier_${supplier.toElsAccount}`] = currentItem[compareItem.columnCode]
                            // realMaterial[`supplier_${supplier.toElsAccount}_disabled`] = currentItem.itemStatus !== '2'
                            realMaterial[`supplier_${supplier.toElsAccount}_accept`] = (() => {
                                if (this.form.packageOptFrist === '1') return true
                                let f = !(['2', '8'].includes(currentItem.itemStatus) || (currentItem.itemStatus === '11' && currentItem.regretFlag === '2'))
                                return f
                            })()
                            realMaterial[`supplier_${supplier.toElsAccount}_reject`] = (() => {
                                if (this.form.packageOptFrist === '1') return true
                                let f = !(['2', '8', '6', '3'].includes(currentItem.itemStatus) || (currentItem.itemStatus === '11' && currentItem.regretFlag === '2'))
                                return f
                            })()
                            realMaterial[`supplier_${supplier.toElsAccount}_revoke`] = (() => {
                                if (this.form.packageOptFrist === '1') return true
                                if(currentItem.evaluationStatus== '4') return true
                                // 提交定价后不能撤销
                                if (currentItem.pricedFlag == '1') return true
                                // 审批中 不能撤销
                                if (currentItem.auditStatus == '1') return true
                                if (currentItem.itemStatus === '11') return true // 行悔标 不可操作
                                if (currentItem.itemStatus == '4' || currentItem.itemStatus == '5') return false // 行接受或拒绝 可操作
                                return !(['4', '5'].includes(currentItem.itemStatus) || (currentItem.itemStatus === '11' && currentItem.regretFlag === '2'))
                            })()
                        }
                    })
                    normalData.push(realMaterial)
                })
            })
            this.tableData = normalData
            this.showOverflow = false
            const length = compareItems.length
            const rows = normalData.map((item, index) => index).filter(arr => arr % length === 0)
            const mergeCheckCells = rows.map(row => ({col: 0, colspan: 1, row, rowspan: length}))
            const mergeCells = rows.map(row => ({col: 1, colspan: 1, row, rowspan: length}))
            const mergeOperationCells = rows.map(row => ({col: suppliers.length + 3, colspan: 1, row, rowspan: length}))
            this.mergeCells = [...mergeCheckCells, ...mergeCells, ...mergeOperationCells]
        },
        setPackageCompareGrid (){
            this.tableColumns = [
                // RADIO_COLUMN,
                SEQ_COLUMN,
                ...[
                    { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center', fixed: 'left' },
                    { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'), width: 70 },
                    { sortable: true, field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vendorName`, '供应商'), width: 150},
                    {
                        sortable: true,
                        field: 'payTermsCode_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paymentCondition`, '付款条件'),
                        width: 100
                    },
                    {
                        sortable: true,
                        field: 'tradeCondition_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tradeCondition`, '国贸条件'),
                        width: 100
                    },
                    {
                        sortable: true,
                        field: 'supplierStatus_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierStatus`, '供应商状态'),
                        width: 120
                    },
                    { sortable: true, field: 'quotaScale', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比'), width: 130, 
                        editRender: {enabled: true},
                        
                        slots: {
                            default: ({row, column}) => {
                                return [<span>{row[column.property]}</span>]
                            },
                            edit: ({row, column}) => {
                                let acceptDisabled = (() => {
                                    if (this.form.packageOptFrist === '0') return true
                                    let f = !(['2', '8'].includes(row.itemStatus) || (row.itemStatus === '11' && row.regretFlag === '2'))
                                    return f
                                })()
                                const props = {disabled: acceptDisabled, max: 100, min: 0, type: 'number'}
                                return [ <vxe-input vModel={row[column.property]} {...{ props}}/>]
                            }
                        }
                    },
                    {
                        sortable: true,
                        field: 'taxAmount',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'),
                        width: 150,
                        slots: {
                            default ({row, column}) {
                                return [
                                    <span>{currency(row[column.property], '', 6)}</span>
                                ]
                            }
                        }
                    },
                    {
                        sortable: true,
                        field: 'netAmount',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未含税金额'),
                        width: 150,
                        slots: {
                            default ({row, column}) {
                                return [
                                    <span>{currency(row[column.property], '', 6)}</span>
                                ]
                            }
                        }
                    }
                ],
                {
                    ...OPERATION_COLUMN,
                    width: 150,
                    slots: {
                        default: ({row}) => {
                            
                            let acceptDisabled= (() => {
                                if (this.form.packageOptFrist === '0') return true
                                let f = !(['2', '8'].includes(row.itemStatus) || (row.itemStatus === '11' && row.regretFlag === '2'))
                                return f
                            })()
                            let revokeDisabled = (() => {
                                if (this.form.packageOptFrist === '0') return true
                                if(row.evaluationStatus== '4') return true
                                // 提交定价后不能撤销
                                if (row.pricedFlag == '1') return true
                                // 审批中 不能撤销
                                if (row.auditStatus == '1') return true
                                if (row.itemStatus === '11') return true // 行悔标 不可操作
                                if (row.itemStatus == '4' || row.itemStatus == '5') return false // 行接受或拒绝 可操作
                                return !(['4', '5'].includes(row.itemStatus) || (row.itemStatus === '11' && row.regretFlag === '2'))
                            })()
                            let rejectDisabled = (() => {
                                if (this.form.packageOptFrist === '0') return true
                                let f = !(['2', '8', '6', '3'].includes(row.itemStatus) || (row.itemStatus === '11' && row.regretFlag === '2'))
                                return f
                            })()
                            return [
                                <div>
                                    <vxe-button type="text" status="primary" {...{on: {click: () => {this.handleContract(row, {}, true, 'accept')}}, props: {disabled: acceptDisabled}}}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accept`, '接受')}</vxe-button>
                                    <vxe-button type="text" status="primary" {...{on: {click: () => {this.handleContract(row, {}, true, 'reject')}}, props: {disabled: rejectDisabled}}}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝')}</vxe-button>
                                    <vxe-button type="text" status="primary" {...{on: {click: () => {this.handleContract(row, {}, true, 'revoke')}}, props: {disabled: revokeDisabled}}}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销')}</vxe-button>
                                </div>
                            ]
                        }
                    }
                }
            ]
            let toElsAccounts = this.suppliers.filter(supplier => supplier.hidden === '0').map(sup => sup.toElsAccount) || []
            this.tableData = this.supplierList.filter(supplier => toElsAccounts.includes(supplier.toElsAccount))
            this.showOverflow = true
            this.mergeCells = []
        },
        showAward (){
            return this.form.enquiryStatus !== '10'
        },
        showRegret (){
            return this.form.enquiryStatus !== '10'
        },
        showReQuote (){
            return this.form.enquiryStatus !== '10'
        },
        showSubmit (){
            return this.form.enquiryStatus !== '10'
        },
        supplierSettingOk (suppliers){
            this.suppliers = suppliers
            const fn = this[`set${this.currentTab}Grid`]
            fn && fn()
        },
        handleContract (row, sup, packageOpt, handleType, listName) {
            const contentText = {
                'accept': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sureToAcceptSelectedRowData`, '是否确认接受选择的行数据'),
                'reject': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sureToAcceptSelectedNotRowData`, '是否确认拒绝选择的行数据'),
                'revoke': this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLqXiFjcWF_d4057e2f`, '是否确认撤销选择的行数据')
            }
            const callback = () => {
                const {id} = sup
                const listNameitem = listName && this[listName].filter(item => id == item.supplierId)[0]
                let param = {}
                param['id'] = row.headId
                param['packageOpt'] = packageOpt
                param['purchaseEnquiryItemList'] = id ? [
                    {   
                        ...row,
                        ...listNameitem,
                        quotaScale: Number(listNameitem.quotaScale),
                        quotaQuantity: Number(listNameitem.quotaQuantity)
                    }
                ] : [row]
                this.spinning = true
                postAction(`/enquiry/purchaseEnquiryHead/${handleType}`, param).then(async (res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.getData()
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.spinning = false
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: contentText[handleType],
                onOk () {
                    callback && callback()
                }
            })
        },
        submitEvaluationPrice (){
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_DJnu_2e936d93`, '提交核价'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLDJnuW_94074dd6`, '确认提交核价?'),
                onOk: () => {
                    this.spinning = true
                    postAction('/enquiry/purchaseEnquiryHead/submitEvaluationPrice', {id: this.sourceId}).then((res) => {
                        if (res.success) {
                            this.$message.success(res.message)
                            this.getData()
                        } else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.spinning = false
                    })
                }
            })
        }
    },
    name: 'PurchaseEnquiryHall',
    watch: {
        '$route': {
            handler ({path, query}) {
                if (path === '/enquiry/purchaseHall3') {
                    this.sourceId = this.$route.query.id || ''
                    // this.getDetailData()
                    this.getData()
                }
            },
            immediate: true
        }
    }
}
</script>

<style lang="less" scoped>
.purchase-enquiry-hall {
    background-color: #eaeaea;
    height: 100vh;
    .page-header-button {
        align-items: center;
        background-color: #ffffff;
        height: 44px;
        padding-right: 40px;
    }
    .page-header-title {
        background-color: #ffffff;
        margin: 8px 8px 0 8px;
        .title__col {
            align-items: center;
            font-size: 18px;
            font-weight: 700;
            height: 44px;
            line-height: 44px;
            padding-left: 20px;
            .title__col-value {
                color: #1890ff;
                margin-left: 20px;
            }
            .red {
                color: red;
            }
        }
    }
    .page-content {
        background-color: #ffffff;
        height: calc(100vh - 112px);
        margin: 8px;
        padding: 8px;
        .content-header-button {
            margin: 8px 0;
        }
    }
    
}
</style>