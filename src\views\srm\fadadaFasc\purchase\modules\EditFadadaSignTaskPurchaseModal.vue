<template>
  <div class="FadadaSignTaskPurchase business-container">
    <business-layout
      :ref="businessRefName"
      :headerTitle="title"
      pageStatus="edit"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :externalToolBar="externalToolBar"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

    <a-modal
      v-drag
      v-model="previewModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <field-select-modal
      isEmit
      ref="fieldSelectModal"
      @ok="fieldSelectOk" />
    
    <a-modal
      v-drag
      v-model="fieldModel"
      @ok="handleOk"
      :title="$srmI18n(`${$getLangAccount()}#i18n_alert_VI_c602f`, '控件')"
      :width="1000">
      <vxe-toolbar
        ref="xToolbar"
        :loading="loading">
        <template #buttons>
          <vxe-button
            status="primary"
            :content="$srmI18n(`${$getLangAccount()}#i18n_alert_JKVa_2517feb0`, '临时新增')"
            @click="insertEvent"></vxe-button>
          <vxe-button
            :content="$srmI18n(`${$getLangAccount()}#i18n_btn_kBILlR_e2ef9604`, '坐标定位说明')"
            @click="locatingTool"></vxe-button>
        </template>
      </vxe-toolbar>

      <vxe-table
        border
        resizable
        show-overflow
        keep-source
        ref="xTable"
        height="320"
        :loading="loading"
        :data="tableData"
        :edit-rules="validRules"
        :edit-config="{trigger: 'click', mode: 'row', showUpdateStatus: true, showInsertStatus: true}">
        <vxe-column
          type="checkbox"
          width="60"></vxe-column>          
        <vxe-column
          field="fieldType"
          :title="$srmI18n(`${$getLangAccount()}#i18n_field_VIAc_2e847adf`, '控件类型')"
          :edit-render="{}">
          <template #default="{ row }">
            <span>{{ formatFieldType(row.fieldType) }}</span>
          </template>
          <template #edit="{ row }">
            <a-select
              @change="(val)=>{
                return fieldTypeModeChange.call(this,val,row)
              }"
              v-model="row.fieldType"
              style="width: 120px">
              <a-select-option
                v-for="(el, idx) of fieldTypeList"
                :value="el.value"
                :key="idx">
                {{ el.label }}
              </a-select-option>
            </a-select>
          </template>
        </vxe-column>
        <vxe-column
          field="positionMode"
          :title="$srmI18n(`${$getLangAccount()}#i18n_field_ILCK_2adaaf41`, '定位模式')"
          :edit-render="{}">
          <template #default="{ row }">
            <span>{{ formatPositionMode(row.positionMode) }}</span>
          </template>
          <template #edit="{ row }">
            <a-select
              @change="(val)=>{
                return positionModeChange.call(this,val,row)
              }"
              v-model="row.positionMode"
              style="width: 120px">
              <a-select-option
                v-for="(el, idx) of positionModeList"
                :value="el.value"
                :key="idx">
                {{ el.label }}
              </a-select-option>
            </a-select>
          </template>
        </vxe-column>
        <vxe-column
          field="positionPageNo"
          :title="$srmI18n(`${$getLangAccount()}#i18n_field_Eo_12ee2c`, '页码')"
          :edit-render="{}">
          <template #edit="{ row }">
            <vxe-input
              :disabled="row.fieldType=='corp_seal_cross_page'"
              v-model="row.positionPageNo"
              type="int"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          field="positionKeyword"
          :title="$srmI18n(`${$getLangAccount()}#i18n_title_keyword`, '关键字')"
          :edit-render="{}">
          <template #edit="{ row }">
            <vxe-input
              :disabled="row.fieldType=='corp_seal_cross_page'"
              v-model="row.positionKeyword"
              type="text"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          field="positionX"
          :title="$srmI18n(`${$getLangAccount()}#i18n_field_XkBpx_e7490788`, 'X坐标(px)')"
          :edit-render="{}">
          <template #edit="{ row }">
            <vxe-input
              :disabled="row.fieldType=='corp_seal_cross_page'"
              v-model="row.positionX"
              type="int"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          field="positionY"
          :title="$srmI18n(`${$getLangAccount()}#i18n_field_YkBpx_1c2f42c9`, 'Y坐标(px)')"
          :edit-render="{}">
          <template #edit="{ row }">
            <vxe-input
              v-model="row.positionY"
              type="int"></vxe-input>
          </template>
        </vxe-column>
        <vxe-column
          :title="$srmI18n(`${$getLangAccount()}#i18n_title_operation`, '操作')"
          width="120">
          <template #default="{ row }">
            <vxe-button
              status="warning"
              :content="$srmI18n(`${$getLangAccount()}#i18n_btn_JKQG_2515de06`, '临时删除')"
              @click="removeRowEvent(row)"></vxe-button>
          </template>
        </vxe-column>
      </vxe-table>
    </a-modal>
  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction, postAction } from '@/api/manage'
import { TOOLBAR_BUTTON_ADD, TOOLBAR_BUTTON_UPLOAD, BUTTON_SAVE, BUTTON_BACK} from '@/utils/constant.js'

export default {
    name: 'EditFadadaSignTaskPurchaseModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {

        function positionPageNoValid ({row}){
            // console.log("testets",test)
            if(row.fieldType!='corp_seal_cross_page'&&!row.positionPageNo){
                return   new Error('请输入页码')
            }
      
        }
        
        function positionKeywordNoValid ({row}){
            
            if(row.fieldType!='corp_seal_cross_page'&&row.positionMode=='keyword'&&!row.positionKeyword){
                return   new Error('请输入关键字')
            }

        }

        function positionXValid ({row}){
            console.log(row)
            if(row.fieldType!='corp_seal_cross_page'&&row.positionMode=='pixel'&&!row.positionX){
                return   new Error('请输入X坐标')
            }

        }

        function positionYValid ({row}){
            
            if(row.positionMode=='pixel'&&!row.positionY){
                return   new Error('请输入Y坐标')
            }

        }

        return {
            title: '编辑签署任务',
            businessRefName: 'businessRef',
            previewModal: false,
            fieldModel: false,
            previewContent: '',
            currentFieldRow: null,
            requestData: {
                detail: { url: '/electronsign/fadada/fadadaSignTaskPurchase/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                fadadaTaskActorPurchaseList: [
                    TOOLBAR_BUTTON_ADD,
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                        key: 'gridDelete',
                        authorityCode: 'fadada#fadadaSignTaskPurchase:delete'
                    }
                ],
                fadadaSignAttachmenPurchaseList: [
                    {
                        ...TOOLBAR_BUTTON_UPLOAD,
                        args: {
                            attachmentExtensionDictCode: 'fadadaAttachmentExtension',
                            businessType: 'fadada' // 必传,
                        }
                    }
                ]
            },
            pageHeaderButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/electronsign/fadada/fadadaSignTaskPurchase/edit'
                    },
                    authorityCode: 'fadada#fadadaSignTaskPurchase:edit',
                    handleBefore: this.handleSaveBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_QIXVhff_e9e44570`, '文件上传法大大'),
                    attrs: {
                        type: 'primary'
                    },
                    args: {
                        url: '/electronsign/fadada/fadadaSignTaskPurchase/edit'
                    },
                    authorityCode: 'fadada#fadadaSignTaskPurchase:uploadFile',
                    show: this.uploadFadadCondiction,
                    click: this.uploadFileToFadada
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'),
                    attrs: {
                        type: 'primary'
                    },
                    args: {
                        url: '/electronsign/fadada/fadadaSignTaskPurchase/send'
                    },
                    authorityCode: 'fadada#fadadaSignTaskPurchase:send',
                    show: this.showSend,
                    click: this.send
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_initSignature`, '发起签署'),
                    attrs: {
                        type: 'primary'
                    },
                    args: {
                        url: '/electronsign/fadada/fadadaSignTaskPurchase/signHandle'
                    },
                    authorityCode: 'fadada#fadadaSignTaskPurchase:signHandle',
                    show: this.showsignHandle,
                    click: this.signHandle
                },
                BUTTON_BACK
            ],
            url: {
                save: '/electronsign/fadada/fadadaSignTaskPurchase/edit',                
                download: '/attachment/purchaseAttachment/download',
                downloadback: '/attachment/saleAttachment/download',
                uploadFileToFadada: '/electronsign/fadada/fadadaSignTaskPurchase/uploadFile',
                sign: '/electronsign/fadada/fadadaSignTaskPurchase/signHandle',
                send: '/electronsign/fadada/fadadaSignTaskPurchase/send',
                back: '/electronsign/fadada/fadadaSignTaskPurchase/sendback',
                detail: '/electronsign/fadada/fadadaSignTaskPurchase/queryById'
            },
            loading: false,
            tableData: [],
            validRules: {
                fieldType: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VIAclS_9f5d3065`, '控件类型必填') }
                ],
                positionMode: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ILCKlS_def7d447`, '定位模式必填') }
                ],
                positionPageNo: [
                    { validator: positionPageNoValid }
                ],
                positionKeyword: [
                    { validator: positionKeywordNoValid }
                ],
                positionX: [
                    { validator: positionXValid }
                ],
                positionY: [
                    { validator: positionYValid }
                ]
            },
            fieldTypeList: [
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mLPR_24beda1f`, '个人签名'), value: 'person_sign' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEWe_25198a69`, '企业印章'), value: 'corp_seal' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEAGe_7f23c3db`, '企业骑缝章'), value: 'corp_seal_cross_page' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BAV_18b5f99`, '日期戳'), value: 'date_sign' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qdM_15bb699`, '备注区'), value: 'remark_sign' }
            ],
            positionModeList: [
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_kBR_154ac65`, '坐标值'), value: 'pixel' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'), value: 'keyword' }
            ]
        }
    },
    methods: {
        fieldTypeModeChange (val, row){
          
            if(val=='corp_seal_cross_page'){
                row.positionMode='pixel'
                row.positionPageNo=''
                row.positionKeyword=''
                row.positionX=''
                row.positionY=''
            }

        },
        positionModeChange (val, row){
            if(row.fieldType=='corp_seal_cross_page'){
                this.$message.warning('企业骑缝章，不能选择关键字')
                row.positionMode='pixel'
            }
        },

        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        showsignHandle ({pageData, pageConfig}) {
            //供方未提交签署人，发起签署按钮隐藏
            if (pageData.saleSignerSubmintStatus==='1' && pageData.signFileUploaded==='1' ) {
                return true
            } else {
                return false
            }
        },
        uploadFadadCondiction ({pageData, pageConfig}) {
            //文件已上传法大大后，上传按钮隐藏
            if (pageData.signFileUploaded==='1' ) {
                return false
            } else {
                return true
            }
        },
        showSend ({pageData, pageConfig}) {
            //双方线上签署，签署文件未上传法大大，发送按钮隐藏
            if (pageData.signFileUploaded!='1' ) {
                return false
            }
            //订单已发送情况下，发送按钮隐藏
            if (pageData.sendStatus==='1' ) {
                return false
            } else {
                return true
            }

        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dDVH_24c39726`, '主体信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCPWL_36613b74`, '采方签署人'),
                        groupNameI18nKey: '',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        groupType: 'item',
                        sortOrder: '2'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RCPWL_91e5ef48`, '供方签署人'),
                        groupNameI18nKey: '',
                        groupCode: 'fadadaTaskActorSaleList',
                        groupType: 'item',
                        sortOrder: '3'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PWQIBI_dc74fdbe`, '签署文件/附件'),
                        groupNameI18nKey: '',
                        groupCode: 'fadadaSignAttachmenPurchaseList',
                        groupType: 'item',
                        sortOrder: '4',
                        extend: {
                            editConfig: {
                                activeMethod: function (that, row, _rowIndex, column) {
                                    debugger
                                    if(row.uploaded==='1' && column.property === 'signType'){
                                        return false
                                    }
                                    return true
                                }
                            }
                        }
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXELSy_298fa878`, '供应商ELS号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'toElsAccount',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toElsDesc`, '供应商名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'saleName',
                        required: '1',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_standardType`, '单据类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'busType',
                        dictCode: 'electronicSignatureBusType',
                        required: '0',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_levelNumber`, '单据编号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'busNumber',
                        required: '0',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWdD_3a0b6871`, '签署主题'),
                        fieldLabelI18nKey: '',
                        fieldName: 'signTaskSubject',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQAAc_eec498e0`, '签署文档类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'signDocType',
                        dictCode: 'fadadaSignDocType',
                        defaultValue: 'contract',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LSRAKI_95a080fc`, '任务过期时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'expiresTime',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LPHO_26cf9a06`, '免签场景'),
                        fieldLabelI18nKey: '',
                        fieldName: 'businessId',
                        dictCode: 'fadadaBusinessId',
                        defaultValue: '',
                        disabled: false
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pICWre_61090d0f`, '哪一方先盖章'),
                        fieldLabelI18nKey: '',
                        fieldName: 'firstSeal',
                        dictCode: 'srmSignatoryType',
                        defaultValue: 'sale',
                        required: '1',
                        bindFunction: function (parentRef, pageData, groupData) {
                            let setDisabledByProp = (prop, flag) => {
                                for (let group of groupData.groups) {
                                    if (group.type) continue
                                    let formFields = group.formFields
                                    for (let sub of formFields) {
                                        if (sub.fieldName === prop) {
                                            sub.disabled = flag
                                            break
                                        }
                                    }
                                }
                            }
                      
                
                            let flag = (parentRef.currentEditRow.sendStatus === '1')
                            console.log(flag)
                            setDisabledByProp('firstSeal', flag)
                                
                            
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCWXre_ab9e81b3`, '供方线上盖章'),
                        fieldLabelI18nKey: '',
                        fieldName: 'onlineSealed',
                        dictCode: 'yn',
                        defaultValue: '0',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'hiddenField',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQLKQjT_6dffff5b`, '签署流程是否有序'),
                        fieldLabelI18nKey: '',
                        fieldName: 'signInOrder',
                        dictCode: 'fadadaJudge',
                        defaultValue: 'true',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIhdRC_5de99b49`, '是否已发送供方'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sendStatus',
                        dictCode: 'yn',
                        defaultValue: '0',
                        disabled: true,
                        bindFunction: function (parentRef, pageData, groupData, obj, value) {
                            let setDisabledByProp = (prop, flag) => {
                                for (let group of groupData.groups) {
                                    if (group.type) continue
                                    let formFields = group.formFields
                                    for (let sub of formFields) {
                                        if (sub.fieldName === prop) {
                                            sub.disabled = flag
                                            break
                                        }
                                    }
                                }
                            }
                            if(value){
                                let flag = (value === '1')
                                setDisabledByProp('signTaskSubject', flag)
                                setDisabledByProp('expiresTime', flag)
                                setDisabledByProp('firstSeal', flag||parentRef.currentEditRow.sendStatus=='1')
                            }
                        }
                    }
                ],
                itemColumns: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAL_14c13b4`, '发起人'),
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'fbk1',
                        fieldType: 'switch',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '0',
                        width: '120',
                        dictCode: 'yn',
                        bindFunction: function bindFunction (row, column, value, Vue) {
                            if (value === '1') {
                                let itemGrid = Vue.getItemGridRef('fadadaTaskActorPurchaseList')
                                let data = itemGrid.getTableData().fullData

                                let isRt = '0'
                                let grd_id = '0'
                                data.forEach(v =>{
                                    if (v.fbk1 == '1') {
                                        if (isRt==='0' && row._X_ID !== v._X_ID) {
                                            isRt = '1'
                                            grd_id = v._X_ID
                                        }
                                    }
                                })

                                if (isRt==='1') {
                                    Vue.$message.warning(Vue.$srmI18n(`${Vue.$getLangAccount()}#i18n_alert_RiTjImhAL_a011a122`, '只允许有一个发起人'))
                                    row.fbk1 = '0'
                                }
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'corpName',
                        fieldType: 'selectModal',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        // required: '1',
                        bindFunction: function bindFunction (row, data, _self) {
                            row.corpName = data[0].corpName
                            row.clientCorpId = data[0].clientCorpId
                            row.openCorpId = data[0].openCorpId
                        },
                        extend: {
                            modalColumns: [{
                                field: 'corpName',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                                fieldLabelI18nKey: '',
                                with: 150
                            }, {
                                field: 'orgCode',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRoo_307b3288`, '机构代码'),
                                fieldLabelI18nKey: '',
                                with: 150
                            }],
                            modalUrl: '/electronsign/fadada/purchaseFadadaOrg/listOrg',
                            modalParams: {},
                            // 表行弹窗清除按钮回调
                            afterRowClearCallBack: function (Vue, row){
                                row.useName = ''
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'),
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'useName',
                        fieldType: 'selectModal',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1',
                        bindFunction: function bindFunction (row, data, _self) {
                            row.useName = data[0].memberName
                            row.clientUserId = data[0].clientUserId
                            row.openUserId = data[0].openUserId
                            row.memberId = data[0].memberId
                            row.mobile = data[0].memberMobile
                            row.email = data[0].memberEmail
                        },
                        extend: {
                            modalColumns: [{
                                field: 'memberName',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memeberName`, '成员姓名'),
                                fieldLabelI18nKey: '',
                                with: 150
                            }, {
                                field: 'orgName',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'),
                                fieldLabelI18nKey: '',
                                with: 150
                            }],
                            modalUrl: '/electronsign/fadada/purchaseFadadaOrgPsn/listEnableMember', 
                            modalParams (Vue, form, row) {
                                return { openCorpId: row.openCorpId }
                            },
                            beforeCheckedCallBack (Vue, row) {
                                return new Promise((resolve, reject) => {
                                    if(row.actorType==='person'){
                                        resolve('success')
                                    }else{
                                        !row.openCorpId ? reject('先选择机构') : resolve('success')
                                    }
                                })
                            },
                            // 表行弹窗清除按钮回调
                            afterRowClearCallBack: function (Vue, row){
                                // row.useName = ''
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCdDAc_e00692ed`, '签署方主体类型'),
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'actorType',
                        fieldType: 'select',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'corp',
                        width: '120',
                        dictCode: 'fadadaActorType',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeR_1486c9d`, '印章名'),
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'sealName',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'selectModal',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1',
                        disabled: false,
                        bindFunction: function bindFunction (row, data, _self) {
                            row.sealName = data[0].sealName
                            row.sealId = data[0].sealId
                        },
                        extend: {
                            modalColumns: [{
                                field: 'sealName',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeR_1486c9d`, '印章名'),
                                fieldLabelI18nKey: '',
                                with: 150
                            }, {
                                field: 'categoryType_dictText',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeAc_27ca63e0`, '印章类型'),
                                fieldLabelI18nKey: '',
                                with: 150
                            }],
                            modalUrl: '/electronsign/fadada/purchaseFadadaSealPsn/list', 
                            modalParams (Vue, form, row) {
                                return { openCorpId: row.openCorpId, memberId: row.memberId, flag: '1'}
                            },
                            beforeCheckedCallBack (Vue, row) {
                                debugger
                                return new Promise((resolve, reject) => {
                                    if(row.actorType==='person'){
                                        reject('个人签署不能设置印章')
                                    }else{
                                        !row.openUserId ? reject('先选择签署人') : resolve('success')
                                    }
                                })
                            },
                            // 表行弹窗清除按钮回调
                            afterRowClearCallBack: function (Vue, row){
                                // row.useName = ''
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_sUCbW_c152e4da`, '参与方权限'),
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'permissions',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'select',
                        defaultValue: 'sign',
                        width: '120',
                        dictCode: 'fadadaPermissions',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQTPhffUEdf_6327e6d7`, '是否需要法大大平台送达'),
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'sendNotification',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'true',
                        width: '120',
                        dictCode: 'fadadaJudge',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dfCK_439e2d13`, '送达方式'),
                        fieldType: 'select',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'notifyWay',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'mobile',
                        width: '120',
                        dictCode: 'fadadaAccountType',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话号码'),
                        fieldType: 'input',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'mobile',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'),
                        fieldType: 'input',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'email',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, '签章类型',
                    //     fieldType: 'select',
                    //     groupCode: 'fadadaTaskActorPurchaseList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'fieldType',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: 'corp_seal',
                    //     width: '120',
                    //     dictCode: 'fadadaFieldType',
                    //     required: '1'
                    // },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`, '是否自动落章'),
                        fieldType: 'select',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'requestVerifyFree',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'false',
                        width: '120',
                        dictCode: 'fadadaJudge',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'),
                        fieldType: 'selectModal',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1',
                        bindFunction: function bindFunction (row, data, _self) {
                            row.fileName = data[0].fileName
                            row.fieldDocId = data[0].fileId
                        },
                        extend: {
                            modalColumns: [{
                                field: 'fileName',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称'),
                                fieldLabelI18nKey: '',
                                with: 150
                            }],
                            modalUrl: '/electronsign/fadada/fadadaSignTaskPurchase/listSignFile', 
                            modalParams (Vue, form, row) {
                                let allData = Vue.tplRootRef.getAllData()
                                return { headId: allData.id, firstSeal: allData.firstSeal }
                            },
                            beforeCheckedCallBack (Vue, row, column, form) {
                                let allData = Vue.tplRootRef.getAllData()
                                let attachment 
                                if(allData.firstSeal==='sale' && allData.onlineSealed!='1'){
                                    attachment = allData.fadadaSignAttachmenPurchaseBackList
                                }else{
                                    attachment = allData.fadadaSignAttachmenPurchaseList
                                }
                                return new Promise((resolve, reject) => {
                                    attachment.length > 0 ? resolve('success') : reject('暂无签署文档')
                                })
                            },
                            // 表行弹窗清除按钮回调
                            afterRowClearCallBack: function (Vue, row){
                                // row.useName = ''
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件ID'),
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'fieldDocId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, '定位模式',
                    //     fieldType: 'select',
                    //     groupCode: 'fadadaTaskActorPurchaseList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionMode',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: 'keyword',
                    //     width: '120',
                    //     dictCode: 'fadadaPositionMode',
                    //     required: '1'
                    // },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, '定位页码',
                    //     fieldType: 'number',
                    //     groupCode: 'fadadaTaskActorPurchaseList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionPageNo',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '1',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '1'
                    // },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, '关键字',
                    //     fieldType: 'input',
                    //     groupCode: 'fadadaTaskActorPurchaseList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionKeyword',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: ''
                    // },
                    {
                        groupCode: 'fadadaTaskActorPurchaseList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: '',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '50',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                // resultArray.push(<a
                                //     title={'X/Y轴定位工具'}
                                //     onClick={() => this.locatingTool(row)}> {'X/Y轴定位工具'}</a>)
                                
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VI_c602f`, '控件')}
                                    onClick={() => this.fieldButton(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VI_c602f`, '控件')}</a>)
                                return resultArray
                            }
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VIId_2e80bff7`, '控件对象'),
                        fieldType: 'input',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'fieldObject',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, 'Y轴值(px)',
                    //     fieldType: 'number',
                    //     groupCode: 'fadadaTaskActorPurchaseList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionY',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '0'
                    // },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, 'X轴值(px)',
                    //     fieldType: 'number',
                    //     groupCode: 'fadadaTaskActorPurchaseList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionX',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '0'
                    // },
                    {
                        title: '企业id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'clientCorpId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '法大大企业id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'openCorpId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '用户id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'clientUserId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '法大大用户id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'openUserId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '成员ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'memberId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '印章ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'sealId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'corpName',
                        // fieldType: 'selectModal',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'useName',
                        // fieldType: 'selectModal',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCdDAc_e00692ed`, '签署方主体类型'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'actorType_dictText',
                        // fieldType: 'select',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'corp',
                        width: '120',
                        dictCode: 'fadadaActorType',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeR_1486c9d`, '印章名'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'sealName',
                        align: 'center',
                        headerAlign: 'center',
                        // fieldType: 'selectModal',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_sUCbW_c152e4da`, '参与方权限'),
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'permissions_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        // fieldType: 'select',
                        defaultValue: 'sign',
                        width: '120',
                        dictCode: 'fadadaPermissions',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQTPhffUEdf_6327e6d7`, '是否需要法大大平台送达'),
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'sendNotification',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'true',
                        width: '120',
                        dictCode: 'fadadaJudge',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dfCK_439e2d13`, '送达方式'),
                        // fieldType: 'select',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'notifyWay_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'mobile',
                        width: '120',
                        dictCode: 'fadadaAccountType',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话号码'),
                        // fieldType: 'input',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'mobile',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'),
                        // fieldType: 'input',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'email',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeAc_39ff9912`, '签章类型'),
                    //     // fieldType: 'select',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'fieldType_dictText',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: 'corp_seal',
                    //     width: '120',
                    //     dictCode: 'fadadaFieldType',
                    //     required: '1'
                    // },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`, '是否自动落章'),
                        // fieldType: 'select',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'requestVerifyFree_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'false',
                        width: '120',
                        dictCode: 'fadadaJudge',
                        required: '1'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'),
                        // fieldType: 'selectModal',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120'
                    },
                    {
                        title: '文件ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'fieldDocId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ILCK_2adaaf41`, '定位模式'),
                    //     // fieldType: 'select',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionMode_dictText',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: 'keyword',
                    //     width: '120',
                    //     dictCode: 'fadadaPositionMode',
                    //     required: '1'
                    // },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suVHuG_d303869e`, '定位页码'),
                    //     // fieldType: 'number',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionPageNo',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '1',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '1'
                    // },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                    //     // fieldType: 'input',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionKeyword',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: ''
                    // },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YkBpx_1c2f42c9`, 'Y轴值(px)'),
                    //     fieldType: 'number',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionY',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '0'
                    // },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XkBpx_e7490788`, 'X轴值(px)'),
                    //     fieldType: 'number',
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'positionX',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '0'
                    // },
                    // {
                    //     groupCode: 'fadadaTaskActorSaleList',
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    //     fieldLabelI18nKey: 'i18n_title_operation',
                    //     field: '',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '50',
                    //     align: 'left',
                    //     slots: {
                    //         default: ({row}) => {
                    //             let resultArray = []
                    //             // resultArray.push(<a
                    //             //     title={'X/Y轴定位工具'}
                    //             //     onClick={() => this.locatingTool(row)}> {'X/Y轴定位工具'}</a>)
                                
                    //             resultArray.push(<a
                    //                 title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VI_c602f`, '控件')}
                    //                 onClick={() => this.fieldButton(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VI_c602f`, '控件')}</a>)
                    //             return resultArray
                    //         }
                    //     }
                    // },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VIId_2e80bff7`, '控件对象'),
                        // fieldType: 'input',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'fieldObject',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '1'
                    },
                    {
                        title: '企业id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'clientCorpId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '法大大企业id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'openCorpId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '用户id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'clientUserId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '法大大用户id',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'openUserId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '成员ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'memberId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: '印章ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaTaskActorSaleList',
                        fieldLabelI18nKey: '',
                        field: 'sealId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    // {
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, '文件类型',
                    //     // fieldType: 'input',
                    //     groupCode: 'fadadaSignAttachmenPurchaseList',
                    //     fieldLabelI18nKey: '',
                    //     field: 'fileType',
                    //     align: 'center',
                    //     headerAlign: 'center',
                    //     defaultValue: '',
                    //     width: '120',
                    //     dictCode: '',
                    //     required: '0',
                    //     disabled: true
                    // },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称'),
                        // fieldType: 'input',
                        groupCode: 'fadadaSignAttachmenPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIXVhff_f0ce8644`, '是否已上传法大大'),
                        // fieldType: 'select',
                        groupCode: 'fadadaSignAttachmenPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'uploaded_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '0',
                        width: '120',
                        dictCode: 'yn',
                        required: '0'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'),
                        fieldType: 'select',
                        groupCode: 'fadadaSignAttachmenPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'signType',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: 'doc',
                        width: '120',
                        dictCode: 'fadadaFileType',
                        required: '1',
                        extend: {
                            beforeCheckedCallBack (Vue, row) {
                                return new Promise((resolve, reject) => {
                                    !row.uploaded==='1' ? reject('文件已上传法大大，不能修改类型') : resolve('success')
                                })
                            }
                        }
                    },
                    {
                        title: '文件表文件ID',
                        fieldType: 'hiddenField',
                        groupCode: 'fadadaSignAttachmenPurchaseList',
                        fieldLabelI18nKey: '',
                        field: 'relationId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120',
                        dictCode: '',
                        required: '0'
                    },
                    {
                        groupCode: 'fadadaSignAttachmenPurchaseList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: 'sourceType_dictText',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '130',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载')}
                                    onClick={() => this.downloadEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载')}</a>)
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览')}
                                    style="margin-left:8px"
                                    onClick={() => this.preViewEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览')}</a>)
                                if(row.uploaded!='1'){
                                    resultArray.push(<a
                                        title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除')}
                                        style="margin-left:8px"
                                        onClick={() => this.preDeleteEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除')}</a>)
                                }
                                
                                return resultArray
                            }
                        }
                    }
                ]
            }
        },
        downloadEvent (row){
            this.download(row, this.url.download)
        },
        downloadBackEvent (row){
            this.download(row, this.url.download)
        },
        download (row, url) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownloadi18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction(url, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        //新增行
        addBiddingItem ({ pageConfig, groupCode }) {
            this.selectType = 'material'
            const form = this.getAllData()
            const { mustMaterialNumber = '1' } = form
            if (mustMaterialNumber == '1') {
                let url = '/material/purchaseMaterialHead/list'
                let columns = [
                    {
                        field: 'cateCode',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
                        width: 150
                    },
                    {
                        field: 'cateName',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
                        width: 150
                    },
                    { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编号'), width: 150 },
                    { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                    { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200 },
                    { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200 }
                ]
                this.$refs.fieldSelectModal.open(url, { blocDel: '0', freeze: '0' }, columns, 'multiple')
            } else {
                console.log('pageConfig :>> ', pageConfig)
                this.businessGridAdd({ pageConfig, groupCode })
            }
        },
        // fieldSelectOk (data) {
        // let pageConfig = this.$refs[this.businessRefName].pageConfig || {}
        // 获取页面所有配置默认值
        // let result = getGroupDefaultData(pageConfig, true)
        // let defaultObj = result['purchaseBiddingItemList'] || {}
        // let itemGrid = this.getItemGridRef('purchaseBiddingItemList')
        // },
        uploadFileToFadada (){
            // 获取页面所有数据
            const allData = this.getAllData() || {}
            if(!allData.id || allData.id==''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WsM_13b2663`, '先保存'))
                return
            }
            //供方线上签署并提交签署人之后，不能再次上传签署文档
            // const saleActors = allData.fadadaTaskActorSaleList
            if(allData.onlineSealed==='1' && allData.saleSignerSubmintStatus==='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCAFXVjPWQAIDJrPWLxOKmXVQA_9c5a12f4`, '供方根据上传的签署文档以提交了签署人，不能再次上传文档'))
                return
            }
            let files = allData.fadadaSignAttachmenPurchaseList
            // let itemGrid = this.getItemGridRef('fadadaSignAttachmenPurchaseList')
            if(allData.onlineSealed!='1' && allData.firstSeal==='sale'){
                // itemGrid = this.getItemGridRef('fadadaSignAttachmenPurchaseBackList')
                files = allData.fadadaSignAttachmenPurchaseBackList
            }

            if(!files || files.length<1){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_XVQIxOLV_117a08b5`, '上传文件不能为空'))
                return
            }
            if(allData.signFileUploaded==='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PWQIIXVRIPVVXVGBrDjQI_9979dc7b`, '签署文件已上传，确定要重新上传并覆盖掉原文件？'))
                return
            }
            files.forEach(element => {
                if(!element.signType || element.signType==''){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_XVQIjPWAcxOLV_5b216033`, '上传文件的签署类型不能为空'))
                    return
                }
            })
            this.$refs[this.businessRefName].confirmLoading = true
            postAction(this.url.uploadFileToFadada, allData).then(res => {
                this.$refs[this.businessRefName].confirmLoading = false
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.refreshPageData()
                    // itemGrid.remove()
                    // itemGrid.insertAt(res.result, -1)
                }
            })
        },
        locatingTool (){
            // window.open('https://esign.jcfintech.net.cn/#/admin/coordTools', '_blank')
            // window.open('https://dev.fadada.com/api-doc/CFZDONY73P/NB2LFBHBUESC5CJB/5-1', '_blank')
            window.open('https://uat-dev.fadada.com/demo/sign/position', '_blank')
        },
        signHandle (args){
            const allData = this.getAllData() || {}
            if(!allData.id || allData.id==''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WsM_13b2663`, '先保存'))
                return
            }
            const actors = allData.fadadaTaskActorPurchaseList
            if(!actors || actors.length<1){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_nCPWLLV_230a21f4`, '采方签署人为空'))
                return
            }
            let initiator = false
            actors.forEach(element => {
                if(element.fbk1==='1'){
                    initiator = true
                }
            })
            if(!initiator){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_iFImPWhAL_3270996`, '选择一个签署发起人'))
                return
            }
            const saleActors = allData.fadadaTaskActorSaleList
            if(!saleActors || saleActors.length<1){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCPWLLV_b03130c8`, '供方签署人为空'))
                return
            }
            // 默认保存方法不会校验当前业务模板必填字段
            // 此处手动触发校验方法
            this.stepValidate(args).then(() => {
                postAction(this.url.sign, allData).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.$emit('handleChidCallback')
                    }
                })
            })
        },
        send (args){
            const allData = this.getAllData() || {}
            if(!allData.id || allData.id==''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WsM_13b2663`, '先保存'))
                return
            }
            if(allData.sendStatus==='1' && allData.saleFileUploaded==='1' && allData.sendBack!='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_rtIhdGFRCIMVPWQI_cebe54c1`, '该单已发送，并且供方已回传签署文件'))
                return
            }
            const files = allData.fadadaSignAttachmenPurchaseList
            if(!files || files.length<1){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQIxOLV_d540bb93`, '签署文件不能为空'))
                return
            }
            let existDoc = false
            let uploadFadadaFlag = false
            files.forEach(element => {
                if(element.signType==='doc'){
                    existDoc = true
                }
                if(element.uploaded==='1'){
                    uploadFadadaFlag = true
                }
            })
            if(!existDoc){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PWQIsRjBI_c07d1e5b`, '签署文件中只有附件'))
                return
            }
            if(!uploadFadadaFlag){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPPWQIXVhff_17b2213e`, '先将签署文件上传法大大'))
                return
            }
            // 默认保存方法不会校验当前业务模板必填字段
            // 此处手动触发校验方法
            this.stepValidate(args).then(() => {
                this.$refs[this.businessRefName].confirmLoading = true
                postAction(this.url.send, allData).then(res => {
                    this.$refs[this.businessRefName].confirmLoading = false
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.$emit('handleChidCallback')
                    }
                })
            })
        },
        sendback (){
            const allData = this.getAllData() || {}
            if(!allData.id || allData.id==''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WsM_13b2663`, '先保存'))
                return
            }
            if(allData.sendStatus!='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PWLSGLhdSYMtk_8cc8c0df`, '签署任务并未发送，无退回操作'))
                return
            }
            if(allData.saleFileUploaded!='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCPWQIMMVSYMtk_a71142e0`, '供方签署文件回回传，无退回操作'))
                return
            }
            if(!allData.sendBackReson || allData.sendBackReson==''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_YMjWxOLV_7ffdab0f`, '退回原因不能为空'))
                return
            }
            getAction(this.url.back, {id: allData.id, sendBackReson: allData.sendBackReson}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.$emit('handleChidCallback')
                }
            })
        },
        preDeleteEvent (row){
            if(row.sendStatus ==='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_QIIhdxOQG_e05136ad`, '文件已发送，不能删除'))
                return
            }
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    let itemGrid = this.getItemGridRef('fadadaSignAttachmenPurchaseList')
                    itemGrid.remove(row)
                }
            })            
        }, fieldButton (row){
            this.currentFieldRow = row
            if(row.fieldObject){
                this.tableData = JSON.parse(row.fieldObject)
            }else{
                this.tableData = []
            }
            this.fieldModel = true
            console.log(row)
        },
        formatFieldType (value) {
            if (value === 'person_sign') {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_mLPe_24bf00f2`, '个人签章')
            }
            if (value === 'corp_seal') {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEWe_25198a69`, '企业印章')
            }
            if (value === 'corp_seal_cross_page') {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEAGe_7f23c3db`, '企业骑缝章')
            }
            if (value === 'date_sign') {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BAV_18b5f99`, '日期戳')
            }
            if (value === 'remark_sign') {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qdM_15bb699`, '备注区')
            }
            return value
        },
        formatPositionMode (value){            
            if (value === 'pixel') {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_field_kBR_154ac65`, '坐标值')
            }
            if (value === 'keyword') {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字')
            }
            return value
        },
        async insertEvent () {
            const $table = this.$refs.xTable
            const newRecord = {}
            const { row: newRow } = await $table.insert(newRecord)
            await $table.setActiveRow(newRow)
        },
        async removeSelectEvent () {
            const $table = this.$refs.xTable
            await $table.removeCheckboxRow()
        },
        async removeRowEvent (row) {
            const $table = this.$refs.xTable
            await $table.remove(row)
        },
        async handleOk (){
            // const $table = this.$refs.xTable
            // let fieldObject = $table.afterFullData
            // if(!fieldObject || fieldObject.length<1){
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VILV_2e7f03ef`, '控件为空'))
            //     return
            // }
            // let message = ''
            // let flag = false
            // fieldObject.forEach(element => {
            //     if(!element.fieldType){
            //         message = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_[VIAc]xOLV_f0633c73`, '[控件类型]不能为空')
            //         flag = true
            //     }
            //     if(!element.positionMode){
            //         message = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_[ILCK]xOLV_9d087551`, '[定位模式]不能为空')
            //         flag = true
            //     }
            //     if(!element.positionPageNo){
            //         message = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_[Eo]xOLV_bf398e86`, '[页码]不能为空')
            //         flag = true
            //     }
            //     if(element.positionMode==='pixel'&& (!element.positionX || !element.positionY)){
            //         message = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_kBILK[XYkB]xOLV_b68e7ac3`, '坐标定位时[X/Y坐标]不能为空')
            //         flag = true
            //     }
            //     if(element.positionMode==='keyword'&& (!element.positionKeyword)){
            //         message = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RIJILK[RIJ]xOLV_1a8dfcc5`, '关键字定位时[关键字]不能为空')
            //         flag = true
            //     }
            //     element.fieldId = this.createCode()
            //     element.fieldName = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VI_c602f`, '控件') + element.fieldId
            // })
            // if(flag){
            //     this.$message.warning(message)
            //     return
            // }
            // this.currentFieldRow.fieldObject = JSON.stringify(fieldObject)
            // this.loading = false
            // this.fieldModel = false

            const $table = this.$refs.xTable
            const errMap = await $table.validate(true).catch(errMap => errMap)

            if (!errMap) {
                let fieldObject = $table.afterFullData
                fieldObject.forEach(element => {
                    element.fieldId = this.createCode()
                    element.fieldName = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VI_c602f`, '控件')  + element.fieldId
                })
                this.currentFieldRow.fieldObject = JSON.stringify(fieldObject)
                this.loading = false
                this.fieldModel = false
            } 
        },
        createCode () {
            let code = ''//声明一个空的字符串值用于后面赋值
            const codeLength = 8 //验证码的长度，可以根据个人需求改变
            const random = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
                'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
            ] //随机数的取值范围
            for (let i = 0; i < codeLength; i++) {
                //循环遍历codeLength，值是几，就循环几次
                let index = Math.floor(Math.random() * 36) //Math.floor方法用于返回小于或等于一个给定数字的最大整数；Math.random方法返回 0（含）和 1（不含）之间的随机数
                code += random[index] //根据索引取得随机数加到code上
            }
            return code //把code值返回
        }
    }
}
</script>
