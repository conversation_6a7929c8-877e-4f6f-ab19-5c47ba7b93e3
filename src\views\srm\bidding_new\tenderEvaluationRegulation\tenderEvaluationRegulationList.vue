<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage "
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <tender-evaluation-regulation-edit
      v-if="showEditPage"
      ref="supplierCapacity"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <tender-evaluation-regulation-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
 
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import tenderEvaluationRegulationEdit from './modules/tenderEvaluationRegulationEdit'
import tenderEvaluationRegulationDetail from './modules/tenderEvaluationRegulationDetail'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        tenderEvaluationRegulationEdit,
        tenderEvaluationRegulationDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'tenderEvaluationRegulation',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNTvRLWMW_6ca5af7a`, '请输入条例名称/描述')
                    }
                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'tender#tenderEvaluationRegulation:add',
                        key: 'add',
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'tender#tenderEvaluationRegulation:queryById', clickFn: this.handleView},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'tender#tenderEvaluationRegulation:edit', clickFn: this.handleEditSingle, allow: this.allowEdit, key: 'edit'},
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'tender#tenderEvaluationRegulation:delete', clickFn: this.handleDeleteSingle, allow: this.allowDel, key: 'delete'}
                ],
                optColumnWidth: 270
            }, 
            url: {
                // 列表数据展示
                list: '/tender/tenderEvaluationRegulation/list',
                columns: 'tenderEvaluationRegulation',
                // 新增
                add: '/tender/tenderEvaluationRegulation/add/',
                // 编辑
                edit: '/tender/tenderEvaluationRegulation/edit/',
                // 删除
                delete: '/tender/tenderEvaluationRegulation/delete/'
            }
        }
    },
    methods: {
        handleView (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },
        allowDel (row){
            if (row.status != '0'){
                return true
            }
            return false
        },
        allowEdit (row){
            if (row.status != '0'){
                return true
            }
            return false
        },
        handleEditSingle (row) {
            console.log(row)
            this.handleEdit(row)
        },
        handleDeleteSingle (row) {
            this.handleDelete(row)
        }
    }
}
</script>