import { srmI18n, getLangAccount } from '../util'
import router from '../../router'
import layIM from './layIM.js'
import { menuInit } from './rightMenu'
import { popModify, friendGroupAdd, friendGroupRemoveClear, moveToGroup } from './firendGroup'
let fListInfo = {} // 好友列表信息

// 右键菜单配置项
const config = {
    rightMenu: []
}
const ctxMenu = (im) => {
    const layim = im.LAYIM
    config.rightMenu = menuInit()
    const $ = window.layui.jquery
    // 阻止浏览器默认右键点击事件
    document.oncontextmenu = function () {
        return false
    }
    // 单击聊天主界面事件
    $('body').on('click', '.layui-layim', function () {
        emptyTips()
    })
    // 右击聊天主界面事件
    $('body').on('mousedown', '.layui-layim', function () {
        emptyTips()
    })
    /* 监听鼠标滚轮事件 */
    $('body').on('mousewheel DOMMouseScroll', '.layim-tab-content', function () {
        emptyTips()
    })
    /* 绑定好友右击事件 */
    $('body').on('mousedown', '.layim-list-friend li ul li', function (e) {
        // 清空所有右击弹框
        emptyTips()
        if (3 != e.which) {
            return
        }
        // 不再派发事件
        e.stopPropagation()

        var othis = $(this)
        if (othis.hasClass('layim-null')) return

        var liH = othis.outerHeight()
        var layimMain = im.LAYIM.getLayimMain()
        var imW = layimMain.width()

        let friend_id = othis[0].className.replace(/^layim-friend/g, '').split(' ')
        friend_id = friend_id && friend_id[0]
        // 获取当前li的原接口信息
        fListInfo = othis.data('info') || {}
        fListInfo = decodeURIComponent(fListInfo)
        fListInfo = JSON.parse(fListInfo)


        var uid = Date.now().toString(36)
        var space_text = ''
        let html = ''

        html += '<ul id="contextmenu_' + uid + '" data-avatar="1" data-name="' + friend_id + '" data-groudId="' + fListInfo.groupId + '" data-id="' + friend_id + '" data-index="' + othis.index() + '" data-mold="1">'
        let { mine, friend } = layim.cache()
        const friendGroupArr = friend.filter(f => f.id != fListInfo.groupId).map(fs => ({
            groupname: fs.groupname,
            id: fs.id
        }))
        config.rightMenu.forEach(el => {
            if (el.id == 'deleteFriend' && friend_id == mine.id) { // 删除好友，排除自己
                html += ''
            } else if (el.id == 'moveToGroup') { // 移动好友
                html += `<li data-id="${el.id}" class="move-to-group-wrap" data-type="${el.handleFnName}" ><span class='text'>${space_text}${el.title}</span>`
                html += '<select name="city" class="move-to-group-otp" lay-verify="" lay-search>'
                html += `<option value="${srmI18n(`${getLangAccount()}#i18n_field_iF_11d400`, '选择')}">${srmI18n(`${getLangAccount()}#i18n_title_pleaseChoose`, '请选择')}</option>`
                friendGroupArr.forEach(fr => {
                    html += `<option value="${fr.id}">${fr.groupname}</option>`
                })
                html += '</select>'
            } else {
                html += `<li data-id="${el.id}" data-type="${el.handleFnName}"><span class='text'>${space_text}${el.title}</span></li>`
            }
        })
        html += '</ul>'

        window.layer.tips(html, othis, {
            tips: 1,
            time: 0,
            shift: 5,
            fix: true,
            skin: 'layui-box layui-layim-qqtmenu',
            success: function (layero) {
                var stopmp = function (e) {
                    window.stope(e)
                }
                layero.off('mousedowm', stopmp).on('mousedowm', stopmp)

                // 弹窗css居中
                var layeroW = layero.width()
                var layeroH = layero.height()

                var left = layero.css('left')
                var top = layero.css('top')
                left = ~~left.replace('px', '')
                top = ~~top.replace('px', '')

                left += (imW - layeroW) / 2
                top += layeroH + liH / 2

                layero.css({ 'left': left + 'px', 'top': top + 'px' })


                // 移动好友分组选中事件
                $('.move-to-group-wrap').on('change', '.move-to-group-otp', function (ev) {
                    let vaule = ev.target.value
                    console.log(vaule)
                    console.log(fListInfo)
                    // let id = $(this).parent().data('id')
                    let index = $(this).parent().parent().data('index')
                    let groudId = $(this).parent().parent().data('groudid') || ''
                    console.log(index)
                    let obj = {
                        curFirendId: fListInfo.id,
                        curFirendIndex: index,
                        curGroupId: groudId, // 当前组id
                        toGroupId: vaule, // 选择的组id
                        type: 'friend',
                        curlistInfo: fListInfo
                    }
                    moveToGroup(obj, layim)
                    emptyTips()
                })
            }
        })
    })
    /* 绑定分组右击事件 */
    $('body').on('mousedown', '.layim-list-friend li h5', function (e) {
        // 清空所有右击弹框
        emptyTips()
        if (3 != e.which) {
            return
        }
        // 不再派发事件
        e.stopPropagation()

        var othis = $(this)
        if (othis.hasClass('layim-null')) return

        var h5H = othis.outerHeight()
        var layimMain = im.LAYIM.getLayimMain()
        var imW = layimMain.width()

        let groupId = othis.data('groupid')
        let groupName = othis.find('span').text()
        let uid = Date.now().toString(36)

        var html = [
            '<ul id="contextmenu_' + uid + '" data-id="' + groupId + '" data-index="' + othis.parent().index() + '" data-groupname="' + groupName + '">',
            '<li data-type="groupInsert">' + srmI18n(`${getLangAccount()}#i18n_field_SuzV_333f2443`, '添加分组') + '</li>',
            '<li data-type="groupRename">' + srmI18n(`${getLangAccount()}#i18n_field_sRR_22de1bd`, '重命名') + '</li>',
            '<li data-type="groupRemove" data-mold="1">' + srmI18n(`${getLangAccount()}#i18n_field_QGzV_2793f4c2`, '删除分组') + '</li></ul>'
        ].join('')

        window.layer.tips(html, othis, {
            tips: 1,
            time: 0,
            shift: 5,
            fix: true,
            skin: 'layui-box layui-layim-qqtmenu',
            success: function (layero) {
                var stopmp = function (e) { window.stope(e) }
                layero.off('mousedowm', stopmp).on('mousedowm', stopmp)

                // 弹窗css居中
                var layeroW = layero.width()
                var layeroH = layero.height()

                var left = layero.css('left')
                var top = layero.css('top')
                left = ~~left.replace('px', '')
                top = ~~top.replace('px', '')

                left += (imW - layeroW) / 2
                top += layeroH + h5H / 2

                layero.css({ 'left': left + 'px', 'top': top + 'px' })
            }
        })
    })
    /* 绑定查找点击事件 */
    $('body').on('click', '.layim-tool-find', function (e) {
        // 清空所有右击弹框
        emptyTips()
        // 不再派发事件
        e.stopPropagation()

        var othis = $(this)
        if (othis.hasClass('layim-null')) return
        const{srmParams} = layim.cache()
        window.layer.close(srmParams.winIndex)

        let uid = Date.now().toString(36)

        var html = [
            '<ul id="contextmenu_' + uid + '">',
            '<li data-type="addFriends">' + srmI18n(`${getLangAccount()}#i18n_field_Suyj_333fe0b3`, '添加好友') + '</li>',
            '<li data-type="groupBuild">' + srmI18n(`${getLangAccount()}#i18n_field_hAaL_283ccf6c`, '发起群聊') + '</li></ul>'
        ].join('')

        window.layer.tips(html, othis, {
            tips: 1,
            time: 0,
            shift: 5,
            fix: true,
            skin: 'layui-box layui-layim-qqtmenu',
            success: function (layero) {
                var stopmp = function (e) { window.stope(e) }
                layero.off('mousedowm', stopmp).on('mousedowm', stopmp)

                var left = layero.css('left')
                var top = layero.css('top')
                left = ~~left.replace('px', '')
                top = ~~top.replace('px', '') + 7

                layero.css({ 'left': left + 'px', 'top': top + 'px' })
            }
        })
    })
    /* 绑定群组成员鼠标移入移出事件 */
    var membersTips = {}
    $('body').on('mouseover', '#layui-layim-chat .member-list .memberName', function (e) {
        // 清空所有右击弹框
        emptyTips()
        // 不再派发事件
        e.stopPropagation()

        var othis = $(this)
        const nameValue = e.currentTarget.innerHTML || ''
        var html = '<div style="color:rgba(69, 79, 89, .8)">'+nameValue+'</div>'
        membersTips = window.layer.tips(html, othis, {
            tips: 1,
            time: 0,
            shift: 5,
            fix: true,
            skin: 'layui-box layui-layim-qqtmenu',
            success: function (layero) {
                var left = layero.css('left')
                var top = layero.css('top') + 7
                left = ~~left.replace('px', '')
                top = ~~top.replace('px', '')

                layero.css({ 'left': left + 'px', 'top': top + 'px' })
            }
        })
    }).on('mouseout', '#layui-layim-chat .member-list .memberName', function (e) {
        layer.close(membersTips)
    })
    // 清空所有右击弹框
    var emptyTips = function () {
        // 移除所有好友选中的样式
        $('.layim-list-friend li ul li').removeAttr('style', '')
        // 移除所有群组选中的样式
        $('.layim-list-group li').removeAttr('style', '')
        // 关闭右键菜单
        window.layer.closeAll('tips')
    }


    // 获取窗口的文档显示区的高度
    var currentHeight = getViewSizeWithScrollbar()
    function getViewSizeWithScrollbar () {
        var clientHeight = 0
        if (window.innerWidth) {
            clientHeight = window.innerHeight
        } else if (document.documentElement.offsetWidth == document.documentElement.clientWidth) {
            clientHeight = document.documentElement.offsetHeight
        } else {
            clientHeight = document.documentElement.clientHeight
        }
        clientHeight = clientHeight - 180
        return clientHeight
    }

    let jumpFn = (ctx) => {
        let id = ctx.data('id')
        let data = config.rightMenu.find(rs => rs.id == id)
        if (!data) {
            return false
        }
        // 这里可以做相关路由操作
        router.push({ path: data.path + `?toElsAccount=${fListInfo.elsAccount}` })
    }
    //节流函数
    function throttle (fn, delay){
        var lastTime = 0
        return function (){
            var nowTime =  Date.now()
            if(nowTime- lastTime > delay){
                fn()
                lastTime = nowTime
            }
        }
    }
    // 绑定右击菜单中选项的点击事件
    var active = {
        addFriends: function (){
            let { base } = layim.cache()
            window.layer.close(active.addFriends.index)
            return (active.addFriends.index = layer.open({
                type: 2,
                title: globalSrmI18n(globalGetLangAccount() + '#i18n_field_Suyj_333fe0b3', '添加好友'),
                shade: false,
                maxmin: true,
                area: ['798px', '530px'],
                skin: 'layui-box layui-layer-border layui-find',
                resize: false,
                content: base.find
            }))
        },
        groupBuild: throttle(function (){
            let cache = layim.cache()
            cache.base.directCreate = true
            layIM.creatGruopChat({
                url: ''
            })
            console.log('函数被触发了')
        }, 2000),
        menuChat: function () {
            console.log(layim)
            /*发送即时消息*/
            var mineId = $(this).parent().data('id')
            var moldId = $(this).parent().data('mold')
            var name = $(this).parent().data('name')
            var avatar = $(this).parent().data('avatar')
            layim.chat({
                type: moldId == 1 ? 'friend' : 'group',
                name,
                avatar,
                id: mineId,
                status: srmI18n(`${getLangAccount()}#i18n_field_yjAPvWzE_45d1737`, '好友当前离线状态')
            })
        },
        menuHistory: function () {
            /*消息记录*/
            // cache必须从初始化配置里面拿，插件有做动态处理
            let { base } = layim.cache()
            if (!base.chatLog) {
                return window.layer.msg(srmI18n(`${getLangAccount()}#i18n_field_LvAHOLStH_7f39c0c3`, '未开启更多聊天记录'))
            }

            window.layer.close(active.menuHistory.index)
            return (active.menuHistory.index = window.layer.open({
                type: 2,
                maxmin: true,
                title: srmI18n(`${getLangAccount()}#i18n_field_U_4e0e`, '与') + fListInfo.username + srmI18n(`${getLangAccount()}#i18n_field_WjLStH_f865c868`, '的聊天记录'),
                area: ['450px', '100%'],
                shade: false,
                offset: 'rb',
                skin: 'layui-box',
                anim: 2,
                id: 'layui-layim-chatlog',
                // content: cache.base.chatLog + '?id=' + thatChat.data.id + '&type=' + thatChat.data.type
                content: base.chatLog + '?id=' + fListInfo.id + '&type=' + 'friend' // type 暂时固定
            }))
        },
        menuBindEvent: function () {
            // 集中处理
            jumpFn($(this))
        },
        // 删除好友
        menuDeleteFriend: function () {
            console.log(11)
            $.ajax({
                headers: {
                    'X-Access-Token': im.token
                },
                url: im.BASE_URL + '/user/deleteFriend?friendId=' + fListInfo.id,
                type: 'get',
                success: function (res) {
                    console.log('ajax success', res)
                    if (res.code === 200) {
                        console.log('success', res)
                        im.LAYIM.removeList({
                            type: 'friend', //或者group
                            id: fListInfo.id //好友或者群组ID
                        })
                        window.layer.msg(srmI18n(`${getLangAccount()}#i18n_alert_deleteSuccess`, '删除成功'))
                    } else {
                        console.log('error', res.message)
                        window.layer.msg(res.message)
                    }
                },
                error: function () {
                    window.layer.msg('ajax error')
                }
            })
        },
        groupRename: function () {
            let curName = $(this).parent().data('groupname')
            let id = $(this).parent().data('id')
            let index = $(this).parent().data('index')
            console.log(curName)
            let obj = {
                curName,
                id,
                index
            }
            popModify(obj, layim)
        },
        groupInsert: function () {
            friendGroupAdd(layim)
        },
        groupRemove: function () {
            let id = $(this).parent().data('id')
            let index = $(this).parent().data('index')
            let obj = {
                index,
                id
            }
            friendGroupRemoveClear(obj, layim)
        },
        moveToGroup: function () {
            console.log(fListInfo)
            // let id = $(this).parent().data('id')
            // let index = $(this).parent().data('index')
            // let obj = {
            //     index,
            //     curFirendId: id,
            //     curGroupId: id,
            //     type: 'friend'
            // }
            // moveToGroup(obj, layim)
        }
    }
    $('body').on('click', '.layui-layer-tips li', function () {
        console.log(1111)
        var type = $(this).data('type')
        active[type] ? active[type].call(this) : ''
        if (type != 'moveToGroup') { // 排除好友移动
            // 清空所有右击弹框
            emptyTips()
        }
    })
}
export { ctxMenu }