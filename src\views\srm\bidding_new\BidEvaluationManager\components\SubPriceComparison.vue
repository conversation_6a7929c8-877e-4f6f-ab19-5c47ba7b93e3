<template>
  <!-- 价格评分 -->
  <div class="ReviewItems">
    <a-spin :spinning="confirmLoading">
      <div class="review-items-top">
        <a-row>
          <a-col :span="12">
            <span>{{ currentRow.title }}</span>
          </a-col>
          <a-col
            :span="12"
            style="text-align: right;">
            <a-button
              type="primary"
              v-if="currentRow['editStatus']"
              @click="calculation">{{ $srmI18n(`${$getLangAccount()}#i18n_field_td_116416`, '计算') }}</a-button>
            <a-button
              type="primary"
              @click="submitData"
              v-if="currentRow['editStatus']"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
            <a-button
              @click="goBack"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </a-col>
          <a-col :span="24">
            <a-col :span="24">
              <span>{{ '价格分排名规则:' }}{{ priceRegulationInfo.rankingRules_dictText }}</span>
            </a-col>
          </a-col>
        </a-row>
      </div>
      <div class="review-items-grid">
        <div
          v-for="(item, index) in resultData.supplierList"
          :key="item.supplierAccount + index">
          <listTable
            :setGridHeight="'auto'"
            ref="listTable"
            :fromSourceData="item.invalid !== '1' ? resultData.evaResultListMap[item.supplierAccount][0].supplierMaterialList : [{supplierName: `${item.supplierName}（已否决）`}]"
            :show-header="index == 0"
            :mergeRowMethod="mergeRowMethod"
            :showTablePage="false"
            :statictableColumns="statictableColumns"></listTable>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import { getAction, postAction } from '@/api/manage'
import { gridOptionsMixin } from '../public/gridOptionsMixin.js'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'

export default {
    mixins: [gridOptionsMixin],
    props: {
        currentRow: {
            type: Object,
            default () {
                return {}
            }
        },
        resultDataProp: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    components: {
        listTable
    },
    computed: {
        headParams () {
            let {checkType, currentStep, processType } = this.currentRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return {xNodeId}
        },
        gridHeight () {
            let height = '100'
            const clientHeight = document.documentElement.clientHeight
            height = clientHeight - 400
            return height + 'px'
        }
    },
    data () {
        return {
            calculateStatus: false,
            confirmLoading: false,
            resultData: {},
            priceRegulationInfo: {},
            statictableColumns: [
                { 
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'), 
                    'field': 'supplierName',
                    slots: {
                        default: ({ row, column }) => {
                            return row.invalid == '1' ? [<span>{row[column.property]}  (已否决)</span>] : [<span>{row[column.property]}</span>] 
                        }
                    }
                },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), 'field': 'materialName'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBsu_2e62dc84`, '投标报价'), 'field': 'quote'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umci_25769c1a`, '价格修正'), 'field': 'priceCorrection', fieldType: 'number', props: {min: -9999}},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBu_2199294`, '评标价'), 'field': 'evaPrice'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_esAR_30ba8883`, '最终排名'), 'field': 'orderBy'}
            ]
        }
    },
    created () {
        this.resultData = Object.assign({}, this.resultDataProp)
        this.priceRegulationInfo = this.resultData.evaluationGroupVO.tenderEvaluationTemplatePriceRegulationInfo
    },
    methods: {
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            // const fields = ['supplierAccount']
            const fields = ['supplierName']
            const cellValue = row['supplierAccount']
            if (cellValue && fields.includes(column.property)) {
                const prevRow = visibleData[_rowIndex - 1]
                let nextRow = visibleData[_rowIndex + 1]
                if (prevRow && prevRow['supplierAccount'] === cellValue) {
                    return { rowspan: 0, colspan: 0 }
                } else {
                    let countRowspan = 1
                    while (nextRow && nextRow['supplierAccount'] === cellValue) {
                        nextRow = visibleData[++countRowspan + _rowIndex]
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 }
                    }
                }
            }
        },
        // 计算
        calculation (neewMessage = true) {
            let resultData = JSON.parse(JSON.stringify(this.resultData))
            let params = []
            for(let key in resultData.evaResultListMap) {
                let item = resultData.evaResultListMap[key][0]
                params.push(item)
            }
            this.confirmLoading = true
            return postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/priceScoreCalculation', params, {headers: this.headParams}).then(res => {
                this.confirmLoading = false
                if (res.code == 200) {
                    const {result = []} = res
                    for(let item of result) {
                        for(let evaResultList in this.resultData.evaResultListMap) {
                            let row = this.resultData.evaResultListMap[evaResultList][0]
                            if (row.supplierAccount == item.supplierAccount) {
                                this.$set(this.resultData.evaResultListMap[evaResultList], '0', item)
                            }
                        }
                    }
                    this.calculateStatus = true
                    neewMessage && this.$message.success(res.message)
                } else {
                    this.$message.error(res.message)
                }
                return res
            })
        },
        // 提交数据
        async submitData () {
            if (!this.calculateStatus) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWHctdW_eaf6fea9`, '请先进行计算！'))
                return false
            }
            let {success} = await this.calculation(false)
            if (!success) return false
            let params = JSON.parse(JSON.stringify(this.resultData))
            params.evaResultListMap && (() => {
                for (let key in params.evaResultListMap) {
                    let item = params.evaResultListMap[key][0]
                    item.judgesTaskItemId = this.currentRow.id
                    item.judgesTaskHeadId = this.currentRow.judgesTaskHeadId
                    
                }
            })()
            params.supplierList && params.supplierList.forEach(data => {
                data['judgesTaskItemId'] = this.currentRow.id || ''
            })

            this.confirmLoading = true
            postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/publishSupplierEvaGroupResult', params, {headers: this.headParams}).then(res => {
                this.confirmLoading = false
                if (res.code == 200) {
                    this.$message.success(res.message)
                    this.goBack()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 返回
        goBack () {
            this.$emit('goBack')
        }
    }
}
</script>

<style lang="less" scoped>
  .ReviewItems {
    height: 100%;
    background-color: #fff;
    padding: 15px 10px;
  }
  .review-items-top {
    line-height: 30px;

    .ant-col-12:nth-child(1) {
      font-size: 16px;
      font-weight: 600;
    }
    .ant-col-24 {
      background-color: #E6F7FF;
      padding: 5px 10px;
      margin-top: 5px;
      border-radius: 5px;
      color: #02A7F0;

      span {
        display: block;
        font-size: 12px;
        height: 20px;
        line-height: 20px;
      }
    }
  }
  .review-items-grid {
    margin-top: 15px;
  }
  :deep(.vxe-table--body){
    height: 100%;

  }
</style>
