<template>
  <a-modal
    v-drag    
    v-model="madalVisible"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderSetting`, '阶梯设置')"
    @ok="setLadderOk">
    <a-spin :spinning="confirmLoading">
      <div class="compare-page-container">
        <div class="page-content-right">
          <div style="margin-bottom: 12px">
            <vxe-grid
              border
              ref="headerGrid"
              row-id="ladderQuantity"
              show-overflow
              size="small"
              height="350"
              :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderInfo`, '阶梯信息')"
              :columns="tableHeaderColumn"
              :data="tableHeaderData"
              :radio-config="{highlight: true, reserve: true, trigger: 'row'}"
              :toolbar="{ slots: { buttons: 'toolbar_buttons' }}"
            >
              <template #toolbar_buttons>
                <div style="margin-top:-8px;text-align:right">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_ladderCount`, '阶梯数量') }}：
                  <a-input-number
                    v-model="ladderQuantity"
                    :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterladderMostOne`, '请输入大于1的阶梯数量')"
                    style="width:200px"/>
                  <a-button
                    @click="addLadderQuantity"
                    type="primary"
                    style="margin-left:8px"
                    slot="tabBarExtraContent">  {{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '添加') }}
                  </a-button>
                  <a-button
                    @click="deleteLadder"
                    type=""
                    style="margin-left:8px"
                    slot="tabBarExtraContent">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}
                  </a-button>
                </div>
              </template>
            </vxe-grid>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import {List} from 'ant-design-vue'
export default {
    name: 'SetLadderModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            headObj: {},
            ladderQuantity: '',
            confirmLoading: false,
            listData: [],
            madalVisible: false,
            tableHeaderData: [],
            tableHeaderColumn: [
                { type: 'radio', width: 40, align: 'center'},
                { field: 'ladder', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级'), width: 150},
                { field: 'ladderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderCount`, '阶梯数量'), width: 200}
            ]
        }
    },
    mounted () {
    },
    methods: {
        open (row) {
            let that = this
            this.madalVisible = true
            let ladderJson = row.ladderPriceJson
            if(ladderJson){
                let itemList = JSON.parse(ladderJson)
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData(itemList)
                })
            }else{
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData([])
                })
            }
        },
        goBack () {
            this.$emit('hide')
        },
        addLadderQuantity (){
            if(!this.ladderQuantity){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterladderCount`, '请输入阶梯数量！'))
                return
            }
            if(this.ladderQuantity <= 1){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderMostOne`, '梯数量必须大于1！'))
                return
            }
            let grid = this.$refs.headerGrid
            let itemList = grid.getTableData().fullData
            if(itemList == 0){
                var item = {'ladderQuantity': 1, 'ladder': 1+' < x < '+this.ladderQuantity}
                grid.insert(item)
            }
            itemList = grid.getTableData().fullData
            if(itemList.length >= 1){
                let currentLastIndex = itemList.length-1
                let item = itemList[currentLastIndex]
                if(parseInt(item.ladderQuantity) >= parseInt(this.ladderQuantity) ){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderMostLastLadderCount`, '阶梯数量必须大于上一阶梯数量'))
                    return
                }
                item['ladder']=item.ladderQuantity+' <= x < '+this.ladderQuantity
            }
            let item2 = {'ladderQuantity': this.ladderQuantity, 'ladder': this.ladderQuantity+' <= x '}
            grid.insertAt(item2, -1)
            this.ladderQuantity = ''
        },
        deleteLadder (){
            let currentItem = this.$refs.headerGrid.getRadioRecord()
            if(!currentItem){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDataDelete`, '请选择需要删除的数据！'))
                return
            }
            let itemList = this.$refs.headerGrid.getTableData().fullData
            if(currentItem.ladderQuantity != itemList[itemList.length - 1].ladderQuantity){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseDeleteFollowLadderLineFirst`, '请先删除后面的阶梯行！'))
            }else{
                this.$refs.headerGrid.removeRadioRow()
            }
        },
        setLadderOk (){
            let itemList = this.$refs.headerGrid.getTableData().fullData
            if(itemList.length == 0 ){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseAddLadderInfo`, '请添加阶梯信息！'))
                return
            }
            // this.$emit('setLadderCallBack',itemList)
            this.$parent.$refs.editPage.$parent.setLadderCallBack(itemList)
            this.madalVisible = false
        }
    }
}
</script>
<style lang="less" scoped>
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 1000px;
            background-color: #fff
        }
    }
</style>