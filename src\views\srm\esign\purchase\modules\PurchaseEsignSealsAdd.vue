<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction } from '@/api/manage'
export default {
    name: 'ElsSealsAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'seals',
            pageData: {
                form: {
                    relationId: '',
                    orgName: '',
                    alias: '',
                    height: '',
                    width: '',
                    transparentFlag: '',
                    filePath: '',
                    sealId: '',
                    subAccount: '',
                    orgId: '',
                    accountId: '',
                    sealType: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WeLD_27cabba0`, '印章维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_transferType`, '类型'),
                                    fieldName: 'sealType',
                                    dictCode: 'srmEsignSealsType',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value !== '0')
                                            setDisabledByProp('subAccount', flag)
                                            setDisabledByProp('companyName', !flag)
                                        }else{
                                            setDisabledByProp('subAccount', true)
                                            setDisabledByProp('companyName', true)
                                        }
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationOrg`, '选择机构'),
                                    fieldName: 'orgName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationOrg`, '选择机构'),
                                    bindFunction: function (Vue, data){
                                        Vue.form.orgName = data[0].companyName,
                                        Vue.form.subAccount = data[0].subAccount,
                                        Vue.form.orgId = data[0].orgId,
                                        Vue.form.accountId = data[0].accountId,
                                        Vue.form.relationId = data[0].id
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), with: 150},
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '绑定子账号'), with: 150},
                                            {field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationID`, '机构id'), with: 150},
                                            {field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_subAccountID`, '子账号id'), with: 150}
                                        ],
                                        modalUrl: '/esign/elsEnterpriseCertificationInfo/list',
                                        modalParams: {},
                                        beforeCheckedCallBack: function (Vue, pageData, groupData, form) {
                                            return new Promise((resolve, reject) => {
                                                let sealType = form.sealType || ''
                                                return sealType !== '' ? resolve('success') : reject('先选择类型')
                                            })
                                        }
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'),
                                    fieldName: 'subAccount',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'),
                                    bindFunction: function (Vue, data){
                                        Vue.form.subAccount = data[0].subAccount,
                                        Vue.form.accountId = data[0].accountId,
                                        Vue.form.relationId = data[0].id
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), with: 150},
                                            {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150},
                                            {field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_xat0lPoJ`, 'e签宝账号'), with: 150}
                                        ],
                                        modalUrl: '/esign/elsSubaccountCertificationInfo/list',
                                        modalParams: {orgCreateFlag: '1'},
                                        beforeCheckedCallBack: function (Vue, pageData, groupData, form) {
                                            return new Promise((resolve, reject) => {
                                                let sealType = form.sealType || ''
                                                return sealType !== '' ? resolve('success') : reject('先选择类型')
                                            })
                                        }
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_sMURgjgo`, '印章别名'),
                                    fieldName: 'alias',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_OQj0KZ8V`, '关联机构id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_yVy933rH`, '机构id'),
                                    fieldName: 'orgId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_HS1PSf1z`, '子账号id'),
                                    fieldName: 'accountId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'number',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeVzpx_371db172`, '印章宽度(px)'),
                                    fieldName: 'width',
                                    placeholder: '个人默认95px, 机构默认159px'
                                },
                                {
                                    fieldType: 'number',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Wexzpx_e67fab37`, '印章高度(px)'),
                                    fieldName: 'height',
                                    placeholder: '个人默认95px, 机构默认159px'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_transparentFlag`, '是否对图片进行透明化处理'),
                                    fieldName: 'transparentFlag',
                                    defaultValue: '1',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sealId`, '印章id'),
                                    fieldName: 'sealId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'image',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_qBGuFJsS`, '图片路径'),
                                    fieldName: 'filePath',
                                    extend: {multiple: false, limit: 1, businessType: 'esign',actionRoutePath: '/srm/esign/purchase/PurchaseEsignSealsList'}
                                }
                            ],
                            validateRules: {
                                alias: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeqRxOLV_4fade7f4`, '签章别名不能为空')}],
                                filePath: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WexOLV_4a4b6180`, '印章不能为空')}],
                                sealType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WWWWWWWWWWWWWWWWWW_38a90bb`, '类型不能为空')}],
                                orgName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LitRW_f0e17419`, '机构不能为空')}]
                            }
                        }

                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/esign/purchaseEsignSeals/add'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {},
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.add
                    const _this = this
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            _this.$parent.addCallBack(res.result)
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            })
        }
    }
}
</script>