<!--
 * @Author: your name
 * @Date: 2022-03-02 10:23:10
 * @LastEditTime: 2022-04-22 14:40:25
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /srm-frontend_210820/src/views/srm/barcode/modules/PurchaseBarcodeLevelHeadEdit.vue
-->
<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="masterSlave"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'

export default {
    name: 'PurchaseBarcodeLevelHeadEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            requestData: {
                detail: {
                    url: '/base/barcode/purchaseBarcodeLevelHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                barcodeLevelItemList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.addItemBarcode
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete'
                }]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/base/barcode/purchaseBarcodeLevelHead/edit'
                    },
                    key: 'save',
                    showMessage: true,
                    handleBefore: this.handleSaveBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                    args: {
                        url: '/base/barcode/purchaseBarcodeLevelHead/publish'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish',
                    handleBefore: this.handlePublishBefore,
                    handleAfter: this.handlePublishAfter,
                    show: this.handleShowFn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/purchaseBarcodeLevelHead/edit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_barcodeLevel_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        handleSaveBefore (args) {
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!args.allData.barcode) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ToxOLVW_fc0b7831`, '条码不能为空！'))
                    reject(args)
                }else {
                    resolve(args)
                }
            })
        },
        // 处理发布回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!args.allData.barcodeLevelItemList || !args.allData.barcodeLevelItemList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_JtToxOLV_7dd590e7`, '子级条码不能为空！'))
                    reject(args)
                }else {
                    resolve(args)
                }
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        handleShowFn () {
            let rs = true
            if (this.currentEditRow && this.currentEditRow.id) {
                rs = true
            } else {
                rs = false
            }
            return rs
        },
        goBack () {
            this.$parent.hideEditPage()
        },
        addItemBarcode ({ Vue, pageConfig, btn, groupCode }) {debugger
            let itemGrid = this.getItemGridRef(groupCode)
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)
    
            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {
                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            // row.printNumber = this.getBusinessExtendData(this.businessRefName).allData.printNumber
            itemGrid.insertAt([row], -1)
        }

    }
}
</script>