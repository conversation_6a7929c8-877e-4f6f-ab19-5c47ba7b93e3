<template>
  <div
    style="height:100%"
    class="SaleCLSealsList">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      modelLayout="seal"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleEsignV3SealsEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleEsignV3SealsDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <SaleEsignV3SealsAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <SealAuth
      v-if="showAuthPage"
      :title="sealName"
      :row="sealRow"
      role-type="1"
      :visible="showAuthPage"
      @close="showAuthPage=false">
    </SealAuth>
  </div>

</template>
<script>
// 新列表模板
import { ListMixin } from '@comp/template/list/ListMixin'
import {getAction, postAction} from '@/api/manage'
import SaleEsignV3SealsAdd from './modules/SaleEsignV3SealsAdd'
import SaleEsignV3SealsEdit from './modules/SaleEsignV3SealsEdit'
import SaleEsignV3SealsDetail from './modules/SaleEsignV3SealsDetail'
import SealAuth from '../modules/SealAuth'
export default {
    mixins: [ListMixin],
    components: {
        SaleEsignV3SealsEdit,
        SaleEsignV3SealsDetail,
        SaleEsignV3SealsAdd,
        SealAuth
    },
    data () {
        return {
            sealRow: null,
            showAuthPage: false,
            showAddPage: false,
            showNewRoundPage: false,
            sealName: null,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'esignv3#saleEsignV3Seals:add'}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCRLWeqR_511a0e32`, '公司名称/印章别名')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esignv3#saleEsignV3Seals:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esignv3#saleEsignV3Seals:add'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_lb_c74bb`, '授权'), clickFn: this.auth, allow: this.allowAuth,  authorityCode: 'esignv3#saleEsignV3Seals:auth'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteItem, allow: this.allowDelete, authorityCode: 'esignv3#saleEsignV3Seals:delete' }
                ],
                showGridLayoutBtn: false
            },
            url: {
                add: '/esignv3/saleEsignV3Seals/add',
                edit: '/esignv3/saleEsignV3Seals/edit',
                list: '/esignv3/saleEsignV3Seals/list',
                delete: '/esignv3/saleEsignV3Seals/delete'
            }
        }
    },
    methods: {
        allowAuth (row){
            return row.sealStatus !== '1'
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        // 新增
        handleAdd () {
            this.showAddPage = false
            this.$nextTick(() => {
                this.showDetailPage = false
                this.showEditPage = false
                this.showAddPage = true
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        // 查看
        handleView (row) {
            this.showDetailPage = false
            this.$nextTick(() => {
                this.currentEditRow = row
                this.showDetailPage = true
                this.showEditPage = false
                this.showAddPage = false
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        // 编辑
        handleEdit (row){
            this.showEditPage = false
            this.$nextTick(() => {
                this.currentEditRow = row
                this.showDetailPage = false
                this.showEditPage = true
                this.showAddPage = false
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        // 启用
        auth (row) {
            this.sealName = row.sealName
            this.sealRow = row
            this.showAuthPage = true
        },
        // 启用
        enableItem (row) {
            row.operateStatus = 'ENABLE'
            postAction(this.url.edit, row).then((res)=>{
                // 接口调用完查询
                if(res && res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_AjLR_28088728`, '启用成功'))
                    this.searchEvent()
                } else{
                    this.$message.warning(res.message)
                }
            })

        },
        // 禁用
        disabledItem (row) {
            row.operateStatus = 'DISABLE'
            postAction(this.url.edit, row).then((res)=>{
                // 接口调用完查询
                if(res && res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_HjLR_38ff8896`, '禁用成功'))
                    this.searchEvent()
                } else{
                    this.$message.warning(res.message)
                }
            })
        },
        pushItem (row) {
            getAction('/contractLock/saleClSeals/push', {id: row.id}).then((res)=>{
                // 接口调用完查询
                if(res && res.success){
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_YdLR_2f75e1a8`, '推送成功'))
                    this.searchEvent()
                }else{
                    this.$message.warning(res.message)
                }
            })
            this.searchEvent()
        },
        // 删除
        deleteItem (row) {
            getAction(this.url.delete, {id: row.id}).then((res)=>{
                // 接口调用完查询
                if(res && res.success){
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_deleteSuccess`, '删除成功'))
                    this.searchEvent()
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        allowEdit (row){
            return row.sealStatus == '1' || row.sealStatus == '2'
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        allowDelete (row){
            if(row.sealId || row.uploaded == '1'){
                return true
            }
            return false
        }
    }
}
</script>

<style lang="less" scoped>
.SaleCLSealsList {
    .page-container {
        :deep(.grid-box){
            overflow: auto;
            overflow-x: hidden;
            overflow-y: auto;
        }
    }
}
</style>
