<template>
  <div class="ViewInvoiceOCRModal">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="isView"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :requestData="requestData"
        :current-edit-row="currentEditRow"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :collapseHeadCode="['baseForm','busRule','personFrom']"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        v-on="businessHandler"
      >
      </business-layout>

    </a-spin>
  </div>
</template>

<script>

import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, httpAction, postAction} from '@/api/manage'
import {axios} from '@/utils/request'

export default {
    name: 'ViewInvoiceOCRModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            isView: false,
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            templateVisible: false,
            refresh: true,
            submitLoading: false,
            nextOpt: true,
            currentRow: {},
            templateNumber: undefined,
            templateOpts: [],
            businessType: 'order',
            showHelpTip: false,
            editRowModal: false,
            previewModal: false,
            orderAllow: true,
            notShowTableSeq: true,
            editItemRow: {},
            currentItemContent: '',
            previewContent: '',
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            requestData: {

                detail: { url: '/reconciliation/purchaseInvoiceHookInformation/queryInvoiceHookList', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.preview
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ]
        }
    },

    computed: {

    },
    mounted () {
        const  that = this
        getAction('/reconciliation/invoiceOcrData/invoiceQueryById', {id: this.currentEditRow.id}).then(res=>{
            if(res.success){
                if (res.result) {
                    that.isView = true
                } else {
                    this.$message.error('查询失败')
                }
            }
        })
    },
    methods: {
        confirmEdit () {},
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: '勾稽发票信息',
                        groupNameI18nKey: 'i18n_field_RthPVH_814c8a24',
                        groupCode: 'invoiceHookInformationList',
                        groupType: 'item',
                        sortOrder: '2'
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '对账单号',
                        fieldLabelI18nKey: 'i18n_field_reconciliationNumber',
                        field: 'reconciliationNumber',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '对账单行',
                        fieldLabelI18nKey: 'i18n_title_statementLineNo',
                        field: 'itemNumber',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '勾稽单据类别',
                        fieldLabelI18nKey: 'i18n_field_RttFAq_80c70508',
                        field: 'checkTheDocumentCategory_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '勾稽单据编号',
                        fieldLabelI18nKey: 'i18n_field_RttFAy_80c75799',
                        field: 'checkTheDocumentNumber',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '单据未税金额',
                        fieldLabelI18nKey: 'i18n_field_tFLfHf_dd2e3649',
                        field: 'documentNetAmount',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '单据含税金额',
                        fieldLabelI18nKey: 'i18n_field_tFxfHf_d48b9da8',
                        field: 'documentTaxAmount',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '单据税额',
                        fieldLabelI18nKey: 'i18n_field_tFff_276610c8',
                        field: 'documentTax',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '发票未税金额',
                        fieldLabelI18nKey: 'i18n_field_hPLfHf_e677ac47',
                        field: 'notIncludeTaxAmount',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '发票含税金额',
                        fieldLabelI18nKey: 'i18n_field_includeTaxAmount',
                        field: 'includeTaxAmount',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '发票税额',
                        fieldLabelI18nKey: 'i18n_title_invoiceAmountTax',
                        field: 'taxAmount',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    },
                    {
                        groupCode: 'invoiceHookInformationList',
                        title: '是否红冲',
                        fieldLabelI18nKey: 'i18n_field_KQSV_2fbec167',
                        field: 'redDashed_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        dataFormat: '',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        mobile: 1,
                        helpText: ''
                    }
                ]
            }
        },

        handleAfterDealSource (pageConfig, resultData){

        },
        preview () {
            if (localStorage.getItem('preViewFile')) {
                let preViewFile = JSON.parse(localStorage.getItem('preViewFile'))
                if(preViewFile.id && preViewFile.fileName){
                    this.$previewFile.open({params: preViewFile})
                }else{
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_hPBjPO_e8a4f3c8`, '发票没有图片'))
                }
            }else{
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_hPBjPO_e8a4f3c8`, '发票没有图片'))
            }
                
        },
        openModal () {

        },
        handleTempCancel () {
            this.templateVisible = false
        },
        postUpdateData (url, row) {
            this.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.init()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    },
    beforeDestroy (){
        localStorage.removeItem('preViewFile')
    }
}
</script>