<template>
  <div class="cardLayout">
    <better-scroll
      ref="scroll"
      class="scrollWrapper"
      :pullup="true"
      :data="tableData">
      <div class="scrollContent">
        <div class="masonry">
          <a-row :gutter="8">
            <a-col
              v-for="item in tableData"
              :key="item.id"
              :span="12">
              <card-item
                :item="item"
                :title="title"
                :titleProp="titleProp"
                :dataIndex="dataIndex"
                :columns="columns"
                :buttons="buttons"
                v-on="$listeners"
              />
            </a-col>
          </a-row>
        </div>
        <!-- <load-more-txt :status="loadMoreStatus" /> -->
      </div>
    </better-scroll>
  </div>
</template>

<script>
import BetterScroll from '@/components/BetterScroll'
// import LoadMoreTxt from '@/components/loadMoreTxt'
import eventBus from '@/utils/eventBus.js'
import CardItem from './cardItem'
export default {
    name: 'BetterScrollLayout',
    components: {
        'better-scroll': BetterScroll,
        'card-item': CardItem
        // 'load-more-txt': LoadMoreTxt
    },
    props: {
        title: {
            type: String,
            default: 'title'
        },
        titleProp: {
            type: String,
            default: ''
        },
        dataIndex: {
            type: String,
            default: 'dataIndex'
        },
        columns: {
            type: Array,
            default () {
                return []
            }
        },
        tableData: {
            type: Array,
            default () {
                return []
            }
        },
        buttons: {
            type: Array,
            default () {
                return []
            }
        },
        pagerConfig: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        loadMoreStatus () {
            if (this.loading) {
                return  'loading'
            } else {
                let { pages, currentPage } = this.pagerConfig || {}
                return pages > currentPage ? 'loadmore' : 'nomore'
            }
        }
    },
    methods: {
        handleScrollTo (args) {
            this.$refs.scroll && this.$refs.scroll.scrollTo(...args)
        },
        handleScrollToEnd () {
            if (this.loadMoreStatus === 'nomore' || this.loading) {
                return
            }
            console.log('scrollToEnd :>> ')
            this.$emit('scrollToEnd')
        }
    },
    beforeDestroy () {
        eventBus.offEvent('scrollTo', this.handleScrollTo)
    },
    created () {
        eventBus.onEvent('scrollTo', this.handleScrollTo)
    }
}
</script>

<style lang="less" scoped>
.scrollWrapper {
	overflow: hidden;
	position: relative;
	height: calc(100vh - 222px);
}
</style>
