<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <content-header
        v-if="showHeader"
        :btns="btns"
      />
      <div
        class="container"
        :style="style">
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidWinNotice`, '中标通知书') }}</span>
          </titleTrtl>
          <vxe-grid
            v-bind="gridConfig"
            :showOverflow="false"
            :height="250"
            ref="table"
            :data="tableData"
            :columns="tableColumns"
            show-overflow="title" >
            <template #grid_opration="{ row }">
              <div v-if="formData.status !== '2' && tenderCurrentRow.applyRole == '1'">
                <a-button
                  v-if="showHeader && pageStatus == 'edit'"
                  type="link"
                  style="margin:0 4px"
                  @click="handleOnlineEdit(row)">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_field_KWAt_298a3612`, '在线编辑') }}
                </a-button>
                <a-upload
                  name="file"
                  :showUploadList="false"
                  :action="uploadUrl"
                  :headers="uploadHeader"
                  :accept="accept"
                  :data="{headId: row.id, businessType: 'biddingPlatform', 'sourceNumber': tenderCurrentRow.tenderProjectNumber || tenderCurrentRow.id || '', 'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'}"
                  :beforeUpload="beforeUpload"
                  @change="(file) => handleUploadChange(file, row)"
                >
                  <a-button
                    v-if="showHeader && pageStatus == 'edit'"
                    type="link"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                </a-upload>
              </div>
              <div v-else>
                <a
                  style="margin:0 4px"
                  @click="preViewEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                <a @click="downloadEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
              </div>
            </template>
          </vxe-grid>
        </div>
      </div>
    </a-spin>
    <a-modal
      :forceRender="true"
      :visible="evaModalVisible"
      width="1000px"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_UBBm_41201cd7`, '评标表格')"
      :okText="okText"
      @ok="handleOk"
      @cancel="handleCancel"
      centered
      :confirmLoading="confirmLoading">

      <a-form-model
        v-if="evaModalVisible"
        ref="baseForm"
        :label-col="labelCol"
        :wrapper-col="wrapperCol" >
        <a-row>
          <a-col :span="8">
            <a-select
              option-filter-prop="children"
              show-search
              :filter-option="filterOption"
              :default-value="''"
              @change="changEvent"
              v-model="templateTitle"
            >
              <a-select-option
                v-for="item in supplierList"
                :key="item.id"
                :value="item.id">{{ item.templateTitle }}</a-select-option>
            </a-select>
          </a-col>
          <a-button
            style="margin:0 5px"
            type="primary"
            @click="handleCreate">{{ $srmI18n(`${$getLangAccount()}#i18n_field_bL_e90d1`, '生成') }}</a-button>
        </a-row>
      </a-form-model>
      <j-editor
        v-if="evaModalVisible"
        ref="ueditor"
        v-model="templateContent"
      />
    </a-modal>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '../components/content-header'
import titleTrtl from '../components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { USER_INFO } from '@/store/mutation-types'
import JEditor from '@/components/els/JEditor'
import { valitNumberLength } from '@views/srm/bidding_new/utils/index'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    name: 'BidWinningNotice',
    components: {
        titleTrtl,
        ContentHeader,
        JEditor
    },
    mixins: [tableMixins],
    data () {
        return {
            timeKey: '',
            templateContent: '',
            templateTitle: '',
            templateLibraryId: '',
            supplierList: [

            ],
            labelCol: { span: 9 },
            wrapperCol: { span: 14 },
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
            evaModalVisible: false,
            confirmLoading: false,
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    'field': 'scopeSort',
                    width: '80'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHf_252229e6`, '中标金额'),
                    field: 'winnerAmount',
                    editRender: {
                        // enabled: !this.formData.status || this.formData.status == '0',
                        autofocus: '.custom-cell-number input'
                    },
                    slots: {
                        default: ({ row, column  }, h) => {
                            return [<span>{row[column.property]}</span>]
                        },
                        edit: ({ row, column }, h) => {
                            const props = {
                                type: 'number',
                                min: 0
                            }
                            return [<a-input-number class='custom-cell-number'  vModel={row[column.property]} {...{ props }} />]
                        }
                    },
                    width: '100'
                },
                // {
                //     'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                //     'field': 'quote'
                // },
                // {
                //     'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHf_252229e6`, '中标金额'),
                //     'field': 'evaPrice'
                // },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidWinNotice`, '中标通知书'),
                    'field': 'purchaseAttachmentDemandDTOList',
                    slots: {
                        default: ({ row }) => {
                            let fileList = ''
                            if (row['purchaseAttachmentDemandDTOList'].length> 0) {
                                fileList = row['purchaseAttachmentDemandDTOList'].map((item, index) => {
                                    return [
                                        <div ><span style='color: blue; cursor:pointer;' onClick={() => this.preViewEvent(row)}>{item['fileName']} </span>
                                            {this.formData.status !== '2' ? <a-icon type="delete" onClick={() => this.handleDeleteAttach(row, index)}/> : ''}
                                        </div>
                                    ]
                                })
                            }
                            return [
                                fileList
                            ]
                        }
                    }
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    'field': 'evaPrice3',
                    slots: {default: 'grid_opration' }
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            // btns: [
            //     { title: '保存', type: 'primary', click: this.save },
            //     { title: '发布', type: 'primary', click: this.submit }
            // ],
            url: {
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryById: '/tender/calibration/purchaseTenderBidWinningAffirmInform/queryBySubpackageId',
                add: '/tender/calibration/purchaseTenderBidWinningAffirmInform/add',
                edit: '/tender/calibration/purchaseTenderBidWinningAffirmInform/edit',
                submit: '/tender/calibration/purchaseTenderBidWinningAffirmInform/publish',
                newValue: '/tender/calibration/purchaseTenderBidWinningAffirmInform/createPriceInfo'
            }
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId', 
        'resetCurrentSubPackage'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        btns () {
            let btn = []
            if (this.formData.status == '0' || !this.formData.status) {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.submit }
                ]
            }else if(this.formData.quoteType == '1'){
                // 分项才有这个按钮
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bLumdWF_80867b03`, '生成价格主数据'), type: 'primary', click: this.newValue }
                ]
            }
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') btn = []
            return btn
        },
        pageStatus () {
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                return 'detail'
            } else {
                if (this.formData.status == '0' || !this.formData.status) {
                    return 'edit'
                } else {
                    return 'detail'
                }
            }
        }
    },
    methods: {
        filterOption (input, option) {
            return (
                option.componentOptions.children[0].text.indexOf(input) >= 0
            )
        },
        async handleCreate (){
            this.confirmLoading = true
            // 先执行一次保存，拿到行id再拿id去生成
            // await postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/saveEvaAttachmentInfo', this.row).then(res=>{
            //     // id = res.result.id
            // })
            // await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryAttachmentInfo', {evaInfoId: this.root.currentEditRow.evaInfoId, fileType: this.row.fileType}).then(res=>{
            //     id = res.result.id
            // })
            
            // 生成按钮接口
            await postAction('/tender/template/purchaseTenderTemplateLibrary/generateTemplate', {templateLibraryId: this.templateLibraryId, businessId: this.subId, paramMap: {winningBidSupplierName: this.row.supplierName, winningBidAmount: this.row.winnerAmount}}).then(res=>{
                if(res.success){
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bLLR_a0818fe1`, '生成成功！'))
                    this.templateContent = res.result.content
                }else{
                    this.$message.error(res.message)
                }
            })
            this.confirmLoading = false
        },
        // 下拉框切换不同模板时触发事件
        changEvent (id) {
            console.log('jinlaile')
            let target = this.supplierList.filter(item=>{
                return item.id == id
            })
            console.log(target)
            this.timeKey = new Date().getTime()
            this.templateContent = target[0].templateContent
            this.templateTitle = target[0].templateTitle
            this.templateLibraryId = target[0].id
        },
        async handleOk (){
            let id = ''
            this.confirmLoading = true
            this.row.noticeContent = this.templateContent
            this.row.templateTitle = this.templateTitle
            this.row.templateLibraryId = this.templateLibraryId
            // 先执行一次保存，拿到行id再拿id去生成
            await postAction('/tender/calibration/purchaseTenderBidWinningAffirmInform/editBidWinningAffirmItem', this.row).then(res=>{
                console.log('保存结果：', res)
            })
            await getAction('/tender/calibration/purchaseTenderBidWinningAffirmInform/queryByBidWinningAffirmItemId?bidWinningAffirmItemId='+this.row.id).then(res=>{
                this.row = res.result
                id = this.row.attachmentId = res.result.id
                console.log('保存结果：', res)
            })
            // businessType:0（评标材料）1（中标通知书）2（落标通知书）
            await getAction('/tender/common/download/downloadPDF', {id, businessType: 1}).then(res=>{
                if(res.success){
                    this.$message.success(res.message)
                    this.templateContent = ''
                    this.evaModalVisible = false
                    this.getData()
                }else{
                    this.$message.error(res.message)
                }
            }).finally(()=>{
                this.confirmLoading = false
            })
            
        }, 
        async handleCancel (){
            this.timeKey = ''
            this.row.noticeContent = this.templateContent
            this.row.templateTitle = this.templateTitle
            this.row.templateLibraryId = this.templateLibraryId
            console.log(this.templateContent, this.row)
            await postAction('/tender/calibration/purchaseTenderBidWinningAffirmInform/editBidWinningAffirmItem', this.row).then(res=>{
                console.log('保存结果：', res)
            })
            console.log('2@@', this.currentEditRow, this.formData)
            await getAction('/tender/calibration/purchaseTenderBidWinningAffirmInform/queryByBidWinningAffirmItemId?bidWinningAffirmItemId='+this.row.id).then(res=>{
                this.row = res.result
                this.row.attachmentId = res.result.id
                console.log('保存结果：', res)
            })
            this.templateContent = ''
            this.evaModalVisible = false
            this.getData()
        },
        // 在线编辑按钮功能（展示弹窗）
        async handleOnlineEdit (row){
            this.row = {...row}
            this.templateContent = row.noticeContent || ''
            this.templateTitle = row.templateTitle || ''
            this.templateLibraryId = row.templateLibraryId || ''
            console.log(row)
            let url = '/tender/template/purchaseTenderTemplateLibrary/queryTemplateLibraryForEva'
            // 获取评标模板下拉列表数据
            await getAction(url, {businessType: 'tender', templateType: 'bidWinningNoInform'}).then(res=>{
                if(res.result && res.result.length != 0){
                    this.supplierList = res.result || []
                }
            })
            this.evaModalVisible = true
        },
        async save () {
            let params = Object.assign({}, this.formData)
            params['subpackageId'] = this.subId
            params['tenderProjectId'] = this.tenderCurrentRow.id
            params['bidWinningAffirmItemVoList'] = this.tableData
            for( let item of this.tableData) {
                if (!valitNumberLength(item.winnerAmount, 8, 6)){
                    return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHfHzxOBR_1ffa065f`, '中标金额长度不能超过') + '8')
                }
            }
            // 拼接数据
            let url = params.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            postAction(url, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        newValue () {
            this.confirmLoading = true
            postAction(this.url.newValue, {subpackageId: this.subId}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        async submit () {
            for( let item of this.tableData) {
                if (!valitNumberLength(item.winnerAmount, 8)){
                    return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHfHzxOBR_1ffa065f`, '中标金额长度不能超过') + '8')
                }
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                onOk: () => {
                    this.confirmLoading = true
                    let params = Object.assign({}, this.formData)
                    params['subpackageId'] = this.subId
                    params['tenderProjectId'] = this.tenderCurrentRow.id
                    params['bidWinningAffirmItemVoList'] = this.tableData
                    postAction(this.url.submit, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.getData()
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            })
        },
        async getData () {
            let params = {
                subpackageId: this.subId
            }
            try {
                this.confirmLoading = true
                let res2 = await getAction(this.url.queryById, params)
                if (res2.code == 200 && res2.result) {
                    let {bidWinningAffirmItemVoList = [], ...others} = res2.result || {}
                    this.formData = others
                    bidWinningAffirmItemVoList && bidWinningAffirmItemVoList.forEach(item => {
                        item.saleQuoteColumnVOS.forEach(vos => {
                            item[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                            item.affirm = item.affirm == '1' ? true : false
                        })
                    })
                    this.tableData = []
                    this.tableData = bidWinningAffirmItemVoList
                }
                // this.tableColumns = []
                const res = await getAction(this.url.queryPrice, params)
                if (res.code == 200 && res.result) {
                    // 分项报价且线上评标的时候，去掉排名
                    if ((res2.result.quoteType == '1' && res2.result.evaluationType =='1') || (res2.result.quoteType == null)){
                        this.tableColumns = this.tableColumns.filter(item=>{
                            return item['field'] != 'scopeSort'
                        })
                    } else {
                        // 否则，其他情况就附带上排名以及各个投标函的投标报价列信息
                        const resultData = res.result
                        let columns = []
                        resultData.forEach(data => {
                            let obj = {
                                title: data.title,
                                children: []
                            }
                            let columnChildren = []
                            data.quoteColumnList.forEach(column => {
                                column['field'] = `${column['field']}_${data['id']}`
                                columnChildren.push(column)
                            })
                            obj.children = columnChildren
                            columns.push(obj)
                        })
                        // 如果是线下的情况，则替换投标报价字段field
                        if(res2.result.evaluationType == '2'){
                            columns.forEach(item=>{
                                item.children.forEach(item2=>{
                                    item2.field = 'evaPrice'
                                })
                            })
                        }
                        this.tableColumns = this.tableColumns.filter(column => {
                            if (!column.hasOwnProperty('children')) {
                                return column
                            }
                        })
                        this.tableColumns.splice(3, 0, ...columns)

                    }
                    console.log('tableColumns', this.tableColumns)
                }


            } catch (error) {
                console.log(error)
            }
            this.confirmLoading = false
            this.$emit('resetCurrentSubPackage')
            // this.confirmLoading = true
            // getAction(this.url.queryById, params).then(res => {
            //     if(res.success) {
            //         console.log(res.result)
            //         let {bidWinningAffirmItemVoList = [], ...others} = res.result || {}
            //         this.tableData = bidWinningAffirmItemVoList || []
            //         this.formData = others
            //     }
            // }).finally(() => {
            //     this.confirmLoading = false
            //     this.$emit('resetCurrentSubPackage')
            // })
        },
        beforeUpload () {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return false
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }, row) {
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if(this.formData.id){
                this.confirmLoading = true
                if (file.status === 'done') {
                    this.confirmLoading = false
                    if (file.response.success) {
                        let {fileName, filePath, fileSize, id, headId} = fileList[fileList.length-1].response.result
                        row.purchaseAttachmentDemandDTOList =  [{
                            uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                            uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName, filePath, fileSize, id, headId
                        }]
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                    //上传完后直接报错
                    this.save()
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                }
            }
        },
        handleDeleteAttach (row, index) {
            row.purchaseAttachmentDemandDTOList.splice(index, 1)
        },
        preViewEvent (row) {
            row.purchaseAttachmentDemandDTOList[0].subpackageId = this.subId
            this.$previewFile.open({params: row.purchaseAttachmentDemandDTOList[0] })
        },
        async downloadEvent (row) {
            console.log(row)
            row.purchaseAttachmentDemandDTOList[0].subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row.purchaseAttachmentDemandDTOList[0])
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.purchaseAttachmentDemandDTOList[0].fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
        }
    },
    mounted () {
        this.init()
        console.log('tenderCurrentRow', this.tenderCurrentRow)
    }
}
</script>
<style lang="less" scoped>
.container{

    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
.margin-t-20{
    margin-top: 20px;
}
.label{
    text-align:right;
    padding-right: 10px;
}
</style>




