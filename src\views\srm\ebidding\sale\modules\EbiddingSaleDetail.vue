<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage" 
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      :reloadData="handleReloadData" />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />


    <a-modal
      v-drag
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_more`, '更多')"
      :visible="priceModelShow"
      @ok="priceModelShow=false"
      @cancel="priceModelShow=false"
    >
    
      <!-- 打包 -->
      <vxe-grid
        v-if="$refs.detailPage&&$refs.detailPage.form.ebiddingWay === '0'"
        ref="quotaGrid"
        :height="437+resizeHeight"
        show-overflow
        v-bind="quotaPackGridOptions"
      >
        <!-- v-on="quotaGridEvents" -->
        <template slot="empty">
          <a-empty />
        </template>
       
          
        <template #totalAmount_default="{ row, rowIndex }">
          <div>
            
            <span>{{ row.totalAmount }}</span>
          </div>
        </template>
        <template #bidNumber_default="{ row }">
          <div>
            
            <span>{{ row.bidNumber }}</span>
          </div>
        </template>
        <template #enterTaxCode_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.taxCode }}</span>
          </div>
        </template>
        <template #enterTaxRate_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.taxRate }}</span>
          </div>
        </template>
        <template #netTotalAmount_default="{ row, rowIndex }">
          <div>
            
            <span >{{ row.netTotalAmount }}</span>
          </div>
        </template>
      </vxe-grid>
      <!-- 逐条 -->
      <vxe-grid
        v-else
        ref="quotaGrid"
        height="437"
        show-overflow
        v-bind="quotaGridDeOptions"
      >
        <!-- v-on="quotaGridEvents" -->
        <template slot="empty">
          <a-empty />
        </template>
       
        <template #price_header="{ column }">
          <i class="vxe-icon--edit-outline"></i>
          <span>{{ column.title }}</span>
         
        </template>
          
        <template #price_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.price }}</span>
          </div>
        </template>
        <template #enterTaxCode_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.taxCode }}</span>
          </div>
        </template>
        <template #enterTaxRate_default="{ row, rowIndex }">
          <div>
       
            <span>{{ row.taxRate }}</span>
          </div>
        </template>
        <template #netPrice_default="{ row, rowIndex }">
          <div>
           
            <span >{{ row.netPrice }}</span>
          </div>
        </template>
        <template #bidNumber_default="{ row }">
          <div>
          
            <span >{{ row.bidNumber }}</span>
          </div>
        </template>
       
       
      </vxe-grid>

    </a-modal>  



  </div>
</template>
<script lang="jsx">
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import { getAction, postAction } from '@/api/manage'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import {quotaGridDeOptions, quotaPackGridDeOptions} from '../../gridConfig/sale/indexDutch'
export default {
    mixins: [DetailMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            priceBtnShow: true,
            priceBtnShowSubmit: false,
            stageTypeData: [],
            showRemote: false,
            quotaGridDeOptions,
            quotaPackGridDeOptions,
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'saleEbiddingItemList',
                        columns: [],
                        // buttons: []
                        buttons: [
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_umxb_257d5888`, '价格补全'), type: 'primary', click: this.priceBtn, showCondition: this.priceShow },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_umDJ_2578e7b9`, '价格提交'), type: 'primary', click: this.priceBtnSubmit, showCondition: this.priceShowSubmit }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLd_1d59643`, '确认项'), groupCode: 'confirmItem', type: 'grid', custom: {
                        ref: 'saleEbiddingConfirmList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'confirmDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLPCMW_713efd4f`, '确认要点描述'), width: 220 },
                            { field: 'must_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120 },
                            { field: 'writeType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMAc_2973057e`, '填写类型'), width: 120 },
                            { field: 'content', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_URid_469b0f42`, '预制选项'), width: 220,
                                slots: {
                                    default: ({row}) =>{
                                        if (row && ['0', '1'].includes(row.writeType) && row.confirmItemList) {
                                            if (row.writeType === '0') {
                                                return [
                                                    (<a-select
                                                        vModel={row.content}
                                                        allowClear={true}
                                                        placeholder='请选择'
                                                    >
                                                        {
                                                            row.confirmItemList.map((item)=> {
                                                                return (
                                                                    <a-select-option value= {item.optionsCode}>
                                                                        { item.optionsName }
                                                                    </a-select-option>
                                                                )
                                                            })
                                                        }
                                                    </a-select>)
                                                ]
                                            }
                                            if (row.writeType === '1') {
                                                if (row.content && typeof row.content === 'string') row.content = row.content.split(',')
                                                else if (!row.content) row.content = []
                                                return [
                                                    (<a-select
                                                        mode='multiple'
                                                        vModel={row.content}
                                                        allowClear={true}
                                                        placeholder='请选择'
                                                    >
                                                        {
                                                            row.confirmItemList.map((item)=> {
                                                                return (
                                                                    <a-select-option value= {item.optionsCode}>
                                                                        { item.optionsName }
                                                                    </a-select-option>
                                                                )
                                                            })
                                                        }
                                                    </a-select>)
                                                ]
                                            }
                                        } else {
                                            return [(<a-input v-model={ row.content } />)]
                                        }
                                    }
                                }
                            },
                            { field: 'supplierRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注') },
                            { field: 'purchaseRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRemarks`, '采购备注')}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'fileSize', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bigSmall`, '大小'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rowIitem`, '行项目'), width: 120 },
                            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), type: 'primary', click: this.sendFiles, showCondition: this.showUpload },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), dictCode: 'srmFileType', type: 'upload', businessType: 'ebidding', callBack: this.uploadCallBack, showCondition: this.showUpload }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent, showCondition: this.showUploadDel }
                        ]
                    } }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_OufY_390dbcd7`, '竞价大厅'), type: 'primary', click: this.openLobby, showCondition: this.showOpenLobby },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/ebidding/saleEbiddingHead/queryById',
                accect: '/ebidding/saleEbiddingHead/acceptResponse',
                reject: '/ebidding/saleEbiddingHead/rejectResponse',
                upload: '/attachment/saleAttachment/upload',
                download: '/attachment/saleAttachment/download',
                publish: '/attachment/saleAttachment/send',
                priceSubmission: '/ebidding/saleEbiddingHead/priceSubmission'
            },
            priceModelShow: false,
            priceData: []
        }
    },
    computed: {
        fileSrc () {
            this.getStageTypeData()
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            return `${this.$variateConfig['configFiles']}/${account}/sale_ebidding_${templateNumber}_${templateVersion}.js`
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.currentEditRow.ebiddingStatus = res.result.ebiddingStatus
                    this.showRemote = true
                } else {
                    this.$message.error(res.message)
                }
            })
        }else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
        // 价格提交
        priceBtnSubmit (){
            const _this = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                onOk () {
                    const params = _this.$refs.detailPage.getPageData()
                    postAction(_this.url.priceSubmission, params).then(res => {
                        if (res.success) {
                            _this.$message.success(res.message)
                            _this.goBack()
                        } else {
                            _this.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        priceShow () {
            return this.priceBtnShow && this.currentEditRow.ebiddingWay == 0 && this.currentEditRow.ebiddingStatus == 9
        },
        priceShowSubmit () {
            return this.priceBtnShowSubmit
        },
        // 价格补全
        priceBtn () {
            let form = this.$refs.detailPage && this.$refs.detailPage.form || {}
            const { quoteType } = form
            this.priceBtnShow=false
            this.priceBtnShowSubmit=true
            let data=this.pageData.groups
            for(let i of data){
                if(i.groupCode=='itemInfo'){
                    for(let formField of i.custom.columns){
                        if(quoteType === '0' && formField.field == 'price'){
                            formField.slots.default=({row}) =>{
                                return [
                                    (<a-input v-model={ row.price } />)
                                ]
                            }
                        }
                        if(quoteType === '1' && formField.field == 'netPrice'){
                            formField.slots.default=({row}) =>{
                                return [
                                    (<a-input v-model={ row.netPrice } />)
                                ]
                            }
                        }
                    }
                }
            }
        },
        showOpenLobby () {
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            const { ebiddingStatus } = params
            return ebiddingStatus == '3' || ebiddingStatus == '4'
        },
        async getStageTypeData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEbiddingStageType'
            }
            const res = await ajaxFindDictItems(postData)
            if (res.success) {
                this.stageTypeData = res.result
            }
        },
        beforeHandleData (pageData){
            let _this=this
            for(let clm of pageData.itemColumns){
                if(clm.field=='price'){
                    clm.slots ={
                        default: ({row}) => {  
                            let handleClick= function (row){
                                console.log(row)
                                _this.priceModelShow=true

                                if(_this.$refs.detailPage&&_this.$refs.detailPage.form.ebiddingWay=='0'){
                                    _this.quotaPackGridDeOptions.data=row.purchaseEbiddingItemHis
                                }else{
                                    _this.quotaGridDeOptions.data=row.purchaseEbiddingItemHis
                                }
                            }
                            let isMore=row.purchaseEbiddingItemHis&&row.purchaseEbiddingItemHis.length>0?<span onClick={handleClick.bind(_this, row)} style="color:#1890ff;float:right">更多</span>:''
                            let  El = (<div><span>{row.price}</span>{isMore}</div>)
                            return El
                        }
                    }
                }
                if(clm.field=='netPrice'){
                    clm.slots = {
                        default: ({row}) => {
                            let  El = (<div><span>{row.netPrice}</span></div>)
                            return El
                        }
                    }
                }
            }
            
        },
        handleAfterDealSource (result) {

            let {saleEbiddingItemList, purchaseEbiddingItemHis}=result
            for(let i of saleEbiddingItemList){
                if(!i.purchaseEbiddingItemHis){
                    i.purchaseEbiddingItemHis=[]
                    console.log(11111)
                }

                for(let his of purchaseEbiddingItemHis){
                    
                    if(i.id==his.itemId) i.purchaseEbiddingItemHis.push(his)

                }
            }

            console.log(result.saleEbiddingItemList)
            
        },
        handleReloadData (res) {
            res.result.ebiddingAmount = res.result.ebiddingAmount ? res.result.ebiddingAmount.toFixed(2) : ''
            res.result.savingAmount = res.result.savingAmount ? res.result.savingAmount.toFixed(2) : ''
            res.result.savingRate = res.result.savingRate ? res.result.savingRate.toFixed(2) : ''
            if (this.stageTypeData && this.stageTypeData.length > 0) {
                res.result.saleAttachmentDemandList = res.result.saleAttachmentDemandList.map(i => {
                    i.stageType_dictText = this.stageTypeData.find(item => item.value === i.stageType).text
                    return i
                })
            }
            return res
        },
        showUpload () {
            return this.currentEditRow.ebiddingStatus === '9' && this.currentEditRow.awardFile === '1'
        },
        showUploadDel (row) {
            return this.currentEditRow.ebiddingStatus === '9' && this.currentEditRow.awardFile === '1' && row.sendStatus === '0'
        },
        sendFiles () {
            let params = { elsAccount: this.currentEditRow.elsAccount, headId: this.currentEditRow.id }
            let key = this.currentEditRow.relationId
            let toSend = {}
            toSend[key] = this.currentEditRow.toElsAccount
            params['toSend'] = toSend
            postAction(this.url.publish, params).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    this.goBack()
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.detailPage.$refs.saleAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            let fileGrid = this.$refs.detailPage.$refs.saleAttachmentList[0]
            getAction('/attachment/saleAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        downloadEvent (row) {
            this.$refs.detailPage.handleDownload(row)
        },
        openLobby () {
            const { ebiddingMethod, id, ebiddingNumber, currentItemNumber, relationId, busAccount, ebiddingWay } = this.currentEditRow
            // // 当竞价状态不是待竞价和竞价中时，不允许进入竞价大厅
            // if(ebiddingStatus !== '3' && ebiddingStatus !== '4') return this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_APzEL_666e77f5`, '当前状态为')}${ebiddingStatus_dictText}，${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiTHNOufY_b1ac6742`, '未允许进入竞价大厅')}`)
            // this.$store.dispatch('SetTabConfirm', false)
            // // 竞价方式 - ebiddingMethod 0 || null 英式，1 日式，2 荷式
            // if (this.currentEditRow.ebiddingMethod === '0' || !this.currentEditRow.ebiddingMethod) {
            //     this.$router.push({
            //         path: '/ebidding/saleLobbyNew',
            //         query: {
            //             id: this.currentEditRow.id,
            //             ebiddingNumber: this.currentEditRow.ebiddingNumber,
            //             currentItemNumber: this.currentEditRow.currentItemNumber || '1',
            //             relationId: this.currentEditRow.relationId
            //         }
            //     })
            // } else {
            //     this.$router.push({
            //         path: '/ebidding/saleLobbyNewJap',
            //         query: {
            //             id: this.currentEditRow.id,
            //             ebiddingNumber: this.currentEditRow.ebiddingNumber,
            //             currentItemNumber: this.currentEditRow.currentItemNumber || '1',
            //             relationId: this.currentEditRow.relationId
            //         }
            //     })
            // }
            if (ebiddingMethod === '0' || !ebiddingMethod) {
                window.open(`${window.origin}/ebidding/saleLobbyNew?id=${id}&ebiddingNumber=${ebiddingNumber}&currentItemNumber=${currentItemNumber}&relationId=${relationId}&busAccount=${busAccount}`, '_blank')
            } else if (ebiddingMethod === '1') {
                window.open(`${window.origin}/ebidding/saleLobbyNewJap?id=${id}&ebiddingNumber=${ebiddingNumber}&currentItemNumber=${currentItemNumber}&relationId=${relationId}&busAccount=${busAccount}`, '_blank')
            } else if (ebiddingMethod === '2' || ebiddingWay === '3') { // 批量 或 一次性
                window.open(`${window.origin}/ebidding/saleLobbyNewDutch?id=${id}&ebiddingNumber=${ebiddingNumber}&currentItemNumber=${currentItemNumber}&relationId=${relationId}&busAccount=${busAccount}`, '_blank')
            }
        }
    }
}
</script>