<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
export default {
    name: 'EnterpriseCertificationDetail',
    mixins: [DetailMixin],
    data () {
        return {
            selectType: 'esignEnterpriseCertification',
            pageData: {
                form: {
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_64dEMQeAfsqvfsQ4`, '企业认证信息维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_loadingCompany`, '是否加载公司列表'),
                                    fieldName: 'loadingCompany',
                                    dictCode: 'yn',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'),
                                    fieldName: 'companyCode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称'),
                                    fieldName: 'companyName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称')
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_fekrL2LC7bftWONd`, '用户账号'),
                                    fieldName: 'subAccount',
                                    required: '1'
                                },
                                {
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_fekrL2LC7bftWONd`, '用户e签宝id'),
                                    fieldType: 'input',
                                    fieldName: 'accountId',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_61w5j37BPo3g0ZZ5`, '机构证件类型'),
                                    fieldName: 'idType',
                                    dictCode: 'srmCompanyEsignIdType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_61w5j37BPo3g0ZZ5`, '机构证件类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    fieldName: 'idNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalIdNumber`, '法定代表人证件号'),
                                    fieldName: 'orgLegalIdNumber'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalName`, '法定代表人名称'),
                                    fieldName: 'orgLegalName'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型'),
                                    fieldName: 'authType',
                                    dictCode: 'srmCompanyCertificationType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_XQzTTQLipsGMe3Mt`, 'e签宝个人机构账户'),
                                    fieldName: 'orgId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cbN3tX16RHkSMJqd`, '是否认证完成'),
                                    fieldName: 'certificationStatus',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_shortLink`, '实名认证短链接'),
                                    fieldName: 'shortLink',
                                    extend: {
                                        linkConfig: {
                                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                            titleI18nKey: 'i18n_title_authenticationLink'
                                        },
                                        exLink: true
                                    },                                      
                                    disabled: true
                                },
                                {
                                    
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_longLink`, '实名认证长链接'),
                                    extend: {
                                        linkConfig: {
                                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                            titleI18nKey: 'i18n_title_authenticationLink'
                                        },
                                        exLink: true
                                    },                                    
                                    disabled: true,
                                    fieldName: 'longLink'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LAkPCzGnQeU0UR7F`, '认证发起时间'),
                                    fieldName: 'certificationStartTime'
                                }
                            ],
                            validateRules: {
                                loadingCompany: [{required: true, message: '是否加载公司列表不能为空'}],
                                subAccount: [{required: true, message: '用户账号不能为空'}],
                                accountId: [{required: true, message: '用户账号对应的e签宝账号不能为空'}],
                                idType: [{required: true, message: '证件类型不能为空'}],
                                idNumber: [{required: true, message: '证件号不能为空'}]
                            }
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/esign/elsEnterpriseCertificationInfo/queryById'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>
