<template>
  <div class="PurchaseBarcodeInfoHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        ref="businessRef"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="unCollapse"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>

      <!-- <a-modal
    v-drag    
        centered
        :width="960"
        :maskClosable="false"
        :visible="flowView"
        @ok="closeFlowView"
        @cancel="closeFlowView">
        <iframe
          style="width:100%;height:560px"
          title=""
          :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
          frameborder="0"></iframe>
      </a-modal> -->
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"/>
      <a-modal
        v-drag    
        v-model="auditVisible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
        :okText="okText"
        @ok="handleOk">
        <a-textarea
          v-model="opinion"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction, postAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
export default {
    name: 'PurchaseBarcodeInfoHeadAudit',
    components: {
        BusinessLayout,
        flowViewModal,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            remoteJsFilePath: '',
            currentBasePath: this.$variateConfig['domainURL'],
            flowId: '',
            auditVisible: false,
            opinion: '',
            showRemote: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            currentRow: {},
            currentUrl: '',
            flowView: false,
            cancelAuditShow: false,
            confirmLoading: false,
            requestData: {
                detail: {
                    url: '/base/barcode/purchaseBarcodeInfoHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditPass,
                    show: this.showAuditBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditReject,
                    show: this.showAuditBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'back',
                    click: this.goBackAudit
                }
            ],
            url: {
                submit: '/base/barcode/purchaseBarcodeInfoHead/edit',
                detail: '/base/barcode/purchaseBarcodeInfoHead/queryById',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    created () {
        let that = this
        getAction(this.url.detail, {id: this.currentEditRow.businessId}).then(res => {
            if(res.success) {
                that.form = res.result
                let account = res.result.templateAccount || res.result.busAccount
                this.remoteJsFilePath = `${account}/purchase_aduit_barcodeInfo_${res.result.templateNumber}_${res.result.templateVersion}`
            }else {
                that.$message.warning(res.message)
            }
        })
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_LFToRH_cc18358d`, '规则条码明细'),
                        groupNameI18nKey: '',
                        groupCode: 'sysBarcodeList',
                        groupType: 'item',
                        sortOrder: '5'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_JIIToRH_2e146811`, '自定义条码明细'),
                        groupNameI18nKey: '',
                        groupCode: 'customBarcodeList',
                        groupType: 'item',
                        sortOrder: '10'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_hxIdRH_5f96d352`, '发布对象明细'),
                        groupNameI18nKey: '',
                        groupCode: 'barcodeSupplierListList',
                        groupType: 'item',
                        sortOrder: '15'
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_To_cfcc0`, '条码'),
                        fieldLabelI18nKey: '',
                        field: 'barcode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_jVH_143f48d`, '原信息'),
                        fieldLabelI18nKey: '',
                        field: 'originalBarcode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormd2b_printNumber`, '允许打印份数'),
                        fieldLabelI18nKey: '',
                        field: 'printNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'customBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_To_cfcc0`, '条码'),
                        fieldLabelI18nKey: '',
                        field: 'barcode',
                        fieldType: 'input',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'customBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormd2b_printNumber`, '允许打印份数'),
                        fieldLabelI18nKey: '',
                        field: 'printNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'barcodeSupplierListList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                        fieldLabelI18nKey: '',
                        field: 'toElsAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'barcodeSupplierListList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
                        fieldLabelI18nKey: '',
                        field: 'supplierCode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'barcodeSupplierListList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                        fieldLabelI18nKey: '',
                        field: 'supplierName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            this.currentEditRow.rootProcessInstanceId = pageConfig.groups[0].formModel.flowId
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        goBack () {
            this.$parent.hideEditPage()
        },
        showFlow ({ Vue, pageConfig, btn, groupCode }) {
            this.flowId = pageConfig.groups[0].formModel.flowId
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            postAction(this.currentUrl, param).then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBackAudit()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        goBackAudit () {
            this.$parent.hideController()
        }
    }
}
</script>