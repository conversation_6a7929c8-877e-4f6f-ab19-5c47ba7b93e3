<!--
 * @Author: fzb
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2022-02-07 14:43:31
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\purchase\modules\PurchaseEditCost.vue
-->
<template>
  <div>
    
        <div v-if="isView">
            <a-spin :spinning="confirmLoading">
            <business-layout
                ref="businessRef"
                
                :currentEditRow="currentEditRow"
                :remoteJsFilePath="remoteJsFilePath"
                :requestData="requestData"
                :externalToolBar="externalToolBar"
                :pageHeaderButtons="pageHeaderButtons"
                modelLayout="unCollapse"
                pageStatus="detail"
                :handleAfterDealSource="handleAfterDealSource"
                :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
                v-on="businessHandler"
            >
            </business-layout>
            
            </a-spin>
        </div>
      

      <!-- <a-modal
    v-drag    
        centered
        :width="960"
        :maskClosable="false"
        :visible="flowView"
        @ok="closeFlowView"
        @cancel="closeFlowView">
        <iframe
          style="width:100%;height:560px"
          title=""
          :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
          frameborder="0"></iframe>
      </a-modal> -->
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"/>
      <a-modal
    v-drag    
        v-model="auditVisible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
        :okText="okText"
        @ok="handleOk">
        <a-textarea
          v-model="opinion"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-modal>

  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'PurchaseEightDisciplinesHeadEdit',
    components: {
        flowViewModal,
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            opinion: '',
            currentUrl: '',
            auditVisible: false,
            isView: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            requestData: {
                detail: { url: '/eightReport/purchaseEightDisciplines/queryById', args: (that) => { return {id: this.currentEditRow.businessId}}}
            },
            externalToolBar: {
                purchaseEightDisciplinesTeamList: {add: true, delete: true}
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditPass,
                    show: this.showAuditBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditReject,
                    show: this.showAuditBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                save: '/eightReport/purchaseEightDisciplines/edit',
                publish: '/eightReport/purchaseEightDisciplines/publis',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_aduit_eightDisciplines_${templateNumber}_${templateVersion}`
        }
    },
    mounted () {
        const  that = this
        getAction('/eightReport/purchaseEightDisciplines/queryById', {id: this.currentEditRow.businessId}).then(res=>{
            if(res.success){
                if (res.result) {
                    that.currentEditRow.templateNumber=res.result.templateNumber
                    that.currentEditRow.templateVersion=res.result.templateVersion
                    that.currentEditRow.templateAccount=res.result.templateAccount
                    that.isView = true
                } else {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mhKm_3152c771`, '查询失败'))
                }
            }
        })
      
        
    },
    methods: {
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10'
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: '关联Tab',
                        fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'select',
                        defaultValue: '',
                        dictCode: 'SRMEightAttachmentRelationTab',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' },
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载') },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除') }
                        ]
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData){
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
            }
        },
        showFlow ({ Vue, pageConfig, btn, groupCode }){
            this.flowId = pageConfig.groups[0].formModel.flowId
            this.flowView = true
        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        closeFlowView (){
            this.flowView = false
        },
        goBackAudit () {
            this.$parent.hideController()
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            postAction(this.currentUrl, param).then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBackAudit()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
            })
        }
    }
}
</script>