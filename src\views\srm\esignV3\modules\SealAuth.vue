<template>
  <div>
    <SealAuthAdd
      v-if="addVisible"
      :visible="addVisible"
      :row="row"
      :role-type="roleType"
      @close="addVisible=false"
    />
    <a-drawer
      :title="title+sTitle"
      placement="right"
      :closable="false"
      :visible="visible"
      :width="860"
      @close="onClose"
    >
      <vxe-grid
        ref="sealAuthGridRef"
        :edit-rules="validRules"
        :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
        v-bind="gridOptions">
        <template #toolbar_buttons>
          <a-button
            size="small"
            type="primary"
            @click="add">{{ $srmI18n(`${$getLangAccount()}#i18n_field_zRVajR_3fad0d71`, '批量新增员工') }}
          </a-button>
          <a-tooltip>
            <template slot="title">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_lTlbvAAedjSnOkJOWetk_e2f5e292`, '必须授权给企企通应用后才能做自动落章操作') }}
            </template>
            <a-button
              size="small"
              type="danger"
              @click="orgAuth">{{ $srmI18n(`${$getLangAccount()}#i18n_field_HAElb_f4d2c39c`, '跨企业授权') }}
            </a-button>
          </a-tooltip>

        </template>
        <template #grid_opration="{ row }">
          <a-button
            style="margin-right: 10px;"
            v-if="!row.id"
            size="small"
            type="danger"
            @click="del(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}
          </a-button>
          <a-button
            v-if="!row.id"
            size="small"
            type="primary"
            @click="save(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}
          </a-button>
          <a-button
            v-if="row.id && !row.role.includes('1') "
            size="small"
            type="danger"
            @click="del(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}
          </a-button>
        </template>
        <template #psnName_edit="{ row }">
          <a-auto-complete
            allowClear
            :notFoundContent="$srmI18n(`${$getLangAccount()}#i18n_field_VWumLLiSumL_e99989a1`,
            '请先到个人认证添加个人')"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_ddjRcRGiF_bf295669`, '搜索员工姓名并选择')"
            v-model="row.psnName"
            @select="applyUserNameOnSelect(row,row.psnName)"
            @search="applyUserNameOnSearch"
          >
            <template slot="dataSource">
              <a-select-option
                v-for="item in dataSource"
                :key="item"
                :title="item">
                <span>{{ item.split(",")[0] }}</span>
                <span
                  class="ml-16 fs-14"
                  style="color: #A0A2AA;">{{ item.split(",")[1] }}</span>
              </a-select-option>
            </template>
          </a-auto-complete>

        </template>
        <template #role_default="{ row }">
          <span>{{ row.role_dictText || formatRoleType(row.role) }}</span>
        </template>
        <template #role_edit="{ row }">
          <a-select
            mode="multiple"
            v-model="row.role">
            <a-select-option
              v-for="item in roleTypeOpt"
              :key="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </template>
        <template #pager>
          <vxe-pager
            :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
            :current-page.sync="tablePage.currentPage"
            :page-size.sync="tablePage.pageSize"
            :total="tablePage.total"
            @page-change="handlePageChange">
          </vxe-pager>
        </template>
      </vxe-grid>
    </a-drawer>
    <a-modal
      v-drag
      v-model="orgVisible"
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbbXKI_bdc443c2`, '授权生效时间')"
      @ok="handleOk">
      <a-form-model
        ref="form"
        :rules="rules"
        :model="form">
        <a-form-model-item :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIlb_c7a146f6`, '是否已授权')">
          <span>{{ row.orgAuth == '1' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否') }}</span>
        </a-form-model-item>
        <a-form-model-item
          :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbbXKI_bdc443c2`, '授权生效时间')"
          prop="effectiveTime"
        >
          <a-date-picker
            :show-time="true"
            style="width:100%"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            v-model="form.effectiveTime"
          />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_lbKXKI_b1120e70`, '授权失效时间')"
          prop="expireTime">
          <a-date-picker
            :show-time="true"
            style="width:100%"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            v-model="form.expireTime"
          />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import {getAction, postAction} from '@/api/manage'
import {isEmail, isMobile} from '@/utils/validate.js'
import {UserOutlined} from '@ant-design/icons'
import SealAuthAdd from './SealAuthAdd'

export default {
    props: {
        title: {
            type: String,
            default: ''
        },
        roleType: {
            type: String,
            default: '0'
        },
        row: {
            type: Object,
            default: () => {
                return {id: ''}
            }
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    components: {
        SealAuthAdd
    },
    data () {
        return {
            rules: {
                effectiveTime: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFbXKI_7e1def3e`, '请选择生效时间')}],
                expireTime: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFKXKI_716bb9ec`, '请选择失效时间')}]
            },
            form: {expireTime: '', effectiveTime: ''},
            orgVisible: false,
            addVisible: false,
            sTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbRv_2ed19e80`, ':授权管理'),
            dataSource: [],
            dataSourceData: [],
            gridOptions: {
                border: false,
                size: 'mini',
                resizable: true,
                showOverflow: true,
                align: 'center',
                toolbarConfig: {
                    slots: {
                        // 自定义工具栏模板
                        buttons: 'toolbar_buttons'
                    }
                },
                editConfig: {
                    trigger: 'click',
                    mode: 'cell',
                    showStatus: true,
                    activeMethod: this.activeRowMethod
                },
                columns: [
                    {type: 'seq', width: 50},
                    {
                        field: 'psnName',
                        width: 150,
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'),
                        showOverflow: true
                    },
                    {
                        field: 'subAccount',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRWWWey_f8d6ad72`, '员工SRM账号'),
                        showOverflow: true
                    },
                    {
                        field: 'sealRole_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_role`, '角色'),
                        showOverflow: true
                    },
                    {
                        field: 'sealStatus_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbzE_2ed07606`, '授权状态'),
                        showOverflow: true
                    },
                    {
                        field: 'effectiveTime',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbbXKI_bdc443c2`, '授权生效时间'),
                        showOverflow: true
                    },
                    {
                        field: 'expireTime',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbKXKI_b1120e70`, '授权失效时间'),
                        showOverflow: true
                    }
                ],
                data: []
            },
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 20
            },
            validRules: {},
            roleTypeOpt: [
                {value: '2', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_WesRj_d0df4e04`, '印章保管员')},
                {value: '3', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_LjRvj_8e167f2b`, '成员管理员')}
            ]
        }
    },
    mounted () {
        this.getData()
    },
    methods: {
        async handleOk () {
            const errMap = await this.$refs.form.validate(this.form).catch(errMap => errMap)
            if (errMap) {
                let param = {sealId: this.row.sealId, effectiveTimeDate: this.form.effectiveTime, expireTimeDate: this.form.expireTime}
                postAction(this.roleType == '0'? '/esignv3/purchaseEsignV3Seals/orgAuth':
                    '/esignv3/saleEsignV3Seals/orgAuth'
                , param).then(res => {
                    if(res.success){
                        window.open(res.result.authorizationSignUrl)
                    }else {
                        this.$message.error(res.message)
                    }
                })
            }
        },
        onClose () {
            this.$emit('close', false)
        },
        activeRowMethod ({row, rowIndex}) {
            if (row.id) {
                return false
            }
            return true
        },
        formatRoleType (val) {
            console.log('val', val)
            if (!val) {
                return
            }
            let label = []
            this.roleTypeOpt.forEach((item) => {
                val.forEach((v) => {
                    if (item.value == v) {
                        label.push(item.label)
                    }
                })

            })
            return label.join(',')
        },
        // 员工搜索
        applyUserNameOnSearch (val) {
            this.dataSource = []
            this.dataSourceData = []
            let param = {name: val}
            this.dataSource = []

            getAction(this.roleType == '0' ? '/esignv3/purchaseEsignV3Personal/queryList' : '/esignv3/saleEsignV3Personal/queryList', param).then((res) => {
                if (res && res.success) {
                    res.result.forEach((item) => {
                        if (item.psnName) {
                            this.dataSource.push(item.psnName + ',' + item.psnAccount)
                            this.dataSourceData.push(item)
                        }
                    })
                }
            })
        },
        // 员工选择
        applyUserNameOnSelect (row, psn) {
            console.log('id:', psn)
            let account = psn.split(',')[1]
            const item = this.dataSourceData.filter(item => {
                return item.psnAccount == account
            })
            console.log('item', item)
            if (item) {
                row.psnName = item[0].psnName
                row.psnAccount = item[0].psnAccount
                row.subAccount = item[0].subAccount
            }
            // let param= {psnName: val, realnameStatus: '1'}
            // getAction('/esignv3/purchaseEsignV3Personal/list', param).then((res)=> {
            //     if (res && res.success) {
            //         if (res.result.records && res.result.records.length) {
            //             row.psnName = res.result.records[0].psnName
            //             row.subAccount = res.result.records[0].subAccount
            //             row.subAccount = res.result.records[0].subAccount
            //         }
            //     }
            // })
        },
        getData () {
            this.form.expireTime = this.row.expireTime
            this.form.effectiveTime = this.row.effectiveTime
            let param = {sealId: this.row.sealId, orgId: this.row.orgId, roleType: this.roleType}
            getAction('/esignv3/esignV3SealsAuth/list', param).then((res) => {
                if (res && res.success) {
                    this.gridOptions.data = res.result.records
                    this.tablePage.total = res.result.total
                } else {
                    this.$message.error(res.message)
                }
            })
        },
        handlePageChange ({currentPage, pageSize}) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            this.getData()
        },
        add () {
            this.addVisible = true
        },
        orgAuth () {
            this.orgVisible = true
        },
        del (row) {
            if (row && row.id) {
                postAction('/contractLock/purchaseCLCompanyInfo/removeStaff', row).then((res) => {
                    if (res && res.success) {
                        this.$message.success(res.message)
                        this.$refs.SealAuthGridRef.remove(row)
                    } else {
                        this.$message.error(res.message)
                    }
                })
            } else {
                this.$refs.SealAuthGridRef.remove(row)
            }
        },
        async save (row) {
            // 插入一条数据并触发校验
            const errMap = await this.$refs.sealAuthGridRef.validate(row).catch(errMap => errMap)
            if (!errMap) {
                if (row.userId) {
                    row.id = row.userId
                }
                row.companyId = this.row.companyId
                postAction('/contractLock/purchaseCLCompanyInfo/addStaff', row).then((res) => {
                    if (res && res.success) {
                        this.$message.success(res.message)
                        this.getData()
                    } else {
                        if (res.message == '网络异常，请检查网络连接') {
                            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAQLAEsLHcAZdROlbWVWeRSMlbKyyVHcROlbKHcROQLtk_54c76816`, '发起流程企业还未进行契约锁功能授权，请先通过获取授权链接接口进行功能授权在进行功能流程操作'))
                        } else {
                            this.$message.error(res.message)
                        }
                    }
                })
            }
        }
    }
}
</script>