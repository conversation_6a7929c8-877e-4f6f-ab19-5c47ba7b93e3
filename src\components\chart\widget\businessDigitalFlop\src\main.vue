
<template>
  <div id="digital-flop" :style="style">
    <div class="digital-flop-item" v-for="item in data" :key="item.title">
      <div class="digital-flop-title">{{ item.title }}</div>
      <div class="digital-flop">
        <dv-digital-flop
          :config="item.number"
          style="width: 100px; height: 50px"
        />
        <div class="unit">{{ widget.option.unit }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { chartsMixins } from "@comp/chart/widget/mixins/chartsMixins";
export default {
  name: "BusinessDigitalFlop",
  mixins: [chartsMixins],
  props: {
    widget: {
      type: [Object],
      default: () => { },
    },
  },
  computed: {
    option () {
      return {
        unit: this.widget.option.unit,
      };
    },
  },
  watch: {
    option: {
      handler (val) {
        this.widget.option.unit = val.unit;
      },
      deep: true,
    },
  },
  methods: {
    refreshWidgetData (data) {
      this.widget.data = data;
    },
  },
  mounted () {
  }
};
</script>
<style lang="less" scoped>
#digital-flop {
  position: relative;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(6, 30, 93, 0.5);

  .dv-decoration-10 {
    position: absolute;
    width: 95%;
    left: 2.5%;
    height: 5px;
    bottom: 0px;
  }

  .digital-flop-item {
    width: 11%;
    height: 80%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-left: 3px solid rgb(6, 30, 93);
    border-right: 3px solid rgb(6, 30, 93);
  }

  .digital-flop-title {
    font-size: 20px;
    margin-bottom: 20px;
    color: #fff;
  }

  .digital-flop {
    display: flex;
  }

  .unit {
    margin-left: 10px;
    display: flex;
    align-items: flex-end;
    box-sizing: border-box;
    padding-bottom: 13px;
    color: #fff;
  }
}
</style>