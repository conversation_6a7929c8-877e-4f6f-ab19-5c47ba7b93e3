<template>
  <div class="bid-information-detail">
    <a-spin :spinning="confirmLoading">
      <div class="title">
        <span>{{ dataObj.noticeTitle }}</span>
      </div>
      <div class="time">
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_publishTime`, '发布时间') }}：{{ dataObj.updateTime }}</span>
      </div>
      <div class="content">
        <p v-html="dataObj.noticeContent"></p>
      </div>
      <div class="footer-grid">
        <vxe-grid
          v-bind="gridOptions"
          :columns="columns"
          :data="gridData">
          <template #operation_default="{ row }">
            <template v-if="dataObj.noticeType == '0' || dataObj.noticeType == '5'">
              <a-button
                @click="invitationConfirm(row)"
                type="primary"
                size="small"
                ghost> {{ $srmI18n(`${$getLangAccount()}#i18n_field_PVRL_43cc2aad`, '邀请确认') }} </a-button>
            </template>
            <template v-else>
              <a-button
                @click="joinNow(row, 'signUp')"
                v-if="accessToken && row.signUp && row.signUp == '1'"
                type="primary"
                size="small"
                ghost>
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_vtsR_39171610`, '立即报名') }}
              </a-button>
              <a-button
                @click="joinNow(row, 'bidding')"
                v-else-if="accessToken && row.bidding && row.bidding == '1'"
                type="primary"
                size="small"
                ghost>
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_vtRB_391c5082`, '立即购标') }}
              </a-button>
              <a-button
                @click="joinNow(row, '')"
                v-else-if="accessToken"
                type="primary"
                size="small"
                ghost>
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_vtsu_39154726`, '立即参加') }}
              </a-button>
              <router-link
                v-else
                class="row space"
                :to="{ name: 'login', query: { redirect: `/BiddingInformationDetail?id=${params.id}&businessId=${params.businessId}&businessType=${params.businessType}&noticeTitle=${params.noticeTitle}&elsAccount=${params.elsAccount}&templateVersion=${params.templateVersion}&templateNumber=${params.templateNumber}`} }">
                <a-button
                  type="primary"
                >{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBEHVWEHKtk_3b5dd35`, '还没登录？请先登录再操作') }}</a-button>
              </router-link>
            </template>
          </template>
        </vxe-grid>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { ACCESS_TOKEN } from '@/store/mutation-types'

export default {
    data () {
        return {
            accessToken: this.$ls.get(ACCESS_TOKEN),
            confirmLoading: false,
            gridOptions: {
                border: true,
                showHeaderOverflow: true,
                showOverflow: true,
                align: 'center',
                headerAlign: 'center',
                height: '200',
                size: 'mini',
                toolbarConfig: {
                    enabled: false
                }
            },
            columns: [
                { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                { field: 'tenderProjectName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead140_projectName`, '项目名称') },
                { field: 'subpackageName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称') },
                { field: 'signUpEndTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_applyEndTime`, '报名截止时间') },
                { field: 'biddingEndTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QISMyRKI_288391c4`, '文件获取截止时间') },
                { field: 'fileSubmitEndTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKI_5b008708`, '递交截止时间') },
                { field: 'operation', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), slots: { default: 'operation_default' } }
            ],
            gridData: [],
            params: {},
            dataObj: {
                noticeTitle: '',
                updateTime: '',
                noticeContent: '',
                noticeType: ''
            }
        }
    },
    created () {
        const queryData = this.$route.query
        this.params = queryData
        console.log(this.params)
    },
    mounted () {
        this.getInfo()
    },
    methods: {
        async getInfo () {
            // 获取招标信息详情
            if (!this.params && !this.params.id) {
                return false
            }
            const url = `/inquiry/public/noToken/queryById/${this.params.businessId}/${this.params.businessType}`
            this.confirmLoading = true
            const res = await getAction(url)
            if (res.code == 200) {
                const { result } = res
                result &&
                    ((result) => {
                        console.log(result)
                        const { noticeTitle, updateTime, noticeContent, noticeType } = result
                        this.dataObj = {
                            noticeTitle: noticeTitle,
                            updateTime: updateTime,
                            noticeContent: noticeContent,
                            noticeType: noticeType
                        }
                        if (result.purchaseTenderNoticeInfoVOS) {
                            // 从表头拿取tenderProjectNumber
                            this.gridData = result.purchaseTenderNoticeInfoVOS.map((item) => {
                                item.tenderProjectNumber = result.tenderProjectNumber
                                return item
                            })
                        }
                    })(result)
            } else {
                this.$message.error(res.message)
            }
            this.confirmLoading = false
        },
        async joinNow (row, type) {
            // 立即参加按钮函数
            console.log(row)
            let url = ''
            let { subpackageId, consortiumBidding } = row
            let { businessId } = this.params
            let { noticeType } = this.dataObj
            this.$ls.set('SET_TENDERCURRENTROW', {
                ...row,
                businessId: businessId,
                noticeType: noticeType
            })
            switch (type) {
            case 'signUp': // 报名
                url = `/bidder/SignUpManagerList?subpackageId=${subpackageId}&businessId=${businessId}&noticeType=${noticeType}&consortiumBidding=${consortiumBidding}`
                break
            case 'bidding': // 购标
                url = `/bidder/PurchaseBidManagerList?subpackageId=${subpackageId}&businessId=${businessId}&noticeType=${noticeType}&consortiumBidding=${consortiumBidding}`
                break
            default:
                // 直接参加项目用的接口：/sale/supplierTenderProjectMasterInfo/join
                row['noticeId'] = businessId
                await postAction('/tender/sale/supplierTenderProjectMasterInfo/join', row).then((res) => {
                    if (res.code == 200) {
                        url = '/bidder/BiddingManagerList'
                    } else {
                        this.$message.error(res.message)
                    }
                })
                break
            }
            console.log(url)
            url &&
                this.$router.push({
                    path: url
                })
        },
        receiptStatusMapMethods (receiptStatus, row = {}) {
            let mapMethods = {
                '1': () => {
                    // 邀请回执单状态：未确认
                    const url = `/bidder/SaleInvitationReceiptList?receiptStatus=${receiptStatus}`
                    url &&
                    this.$router.push({
                        path: url
                    })  
                },
                '2': () => {
                // 邀请回执单状态：已确认
                    row['applyRole'] = '2'
                    this.$ls.set('SET_TENDERCURRENTROW', row)
                    let _t = +new Date()
                    const routeUrl = this.$router.resolve({
                        path: '/tenderHall',
                        query: {
                            _t
                        }
                    })
                    window.open(routeUrl.href, '_blank')
                },
                '3': () => {
                    // 邀请回执单状态：拒绝
                    const url = '/bidder/SaleInvitationReceiptList'
                    url &&
                    this.$router.push({
                        path: url
                    })
                }
            }
            mapMethods[receiptStatus]()
        },
        async invitationConfirm (row) {
            // 预审阶段
            if (row.checkType == '0') {
                this.receiptStatusMapMethods('2', row)
                return
            }
            // 邀请确认
            const params = {
                subpackageId: row.subpackageId || ''
            }
            const resultData = await getAction('/tender/sale/tenderInvitationSupplierReceipt/queryBySubpackageId', params) || {}
            if (resultData.code != 200) {
                this.$message.error(result.message)
                return
            }
            const { result } = resultData
            const { receiptStatus } = result
            this.$ls.set('SET_TENDERCURRENTROW', {...result, tenderProjectNumber: row.tenderProjectNumber})
            this.receiptStatusMapMethods(receiptStatus, row)
        }
    }
}
</script>

<style lang="less" scoped>
.bid-information-detail {
    height: 100%;
    background-color: #fff;
    padding: 0 10px;

    .title {
        text-align: center;
        height: 60px;
        line-height: 60px;
        font-size: 18px;
        font-weight: 600;
    }
    .time {
        text-align: center;
        height: 30px;
        line-height: 30px;
        background-color: #eee;
        color: #666;
    }
    .content {
        margin-top: 10px;
        height: calc(100vh - 300px);
        overflow-y: auto;
    }
    .footer-grid {
        margin-top: 10px;
        position: fixed;
        bottom: 0;
        width: calc(100% - 20px);
    }
}
</style>
