<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <material-master-modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
  </div>
</template>

<script>
import MaterialMasterModal from './modules/MaterialMasterModal'
import {listPageMixin} from '@comp/template/listPageMixin'

export default {
    name: 'MaterialMasterList',
    mixins: [listPageMixin],
    components: {
        MaterialMasterModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input', 
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                        fieldName: 'materialDesc', 
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterMaterialDesc`, '请输入物料描述')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/base/materialMaster/list',
                delete: '/base/materialMaster/delete',
                deleteBatch: '/base/materialMaster/deleteBatch',
                exportXlsUrl: '/base/materialMaster/exportXls',
                importExcelUrl: '/base/materialMaster/importExcel',
                columns: 'baseMaterialMasterList'          
            }
        }
    },
    computed: {

    },
    created () {

    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialInfo`, '物料信息'))
        }
    }
}
</script>
