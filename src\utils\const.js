/*
 * @Author: your name
 * @Date: 2021-06-15 09:58:52
 * @LastEditTime: 2022-11-17 15:52:16
 * @LastEditors: wangxin <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\utils\const.js
 */
import { ROWSELECTMODALCONST } from './selectModalConst'

// report的地址
const reportAddress = () => {
    let reportHost = location.origin
    if (/localhost/g.test(reportHost)) {
        reportHost = '//localhost:8085'
    }
    return reportHost
}
const REPORT_ADDRESS = reportAddress()
const REPORT_JMREPORT_ADDRESS_SUFFIX = '/els/report/jmreport/list'
// 路由白名单
const ROUTER_WHITE_LIST = ['/user/login', '/user/register', '/user/register-result', '/user/alteration', '/user/noticeList', '/user/noticeDetail', '/user/noticeDetailTemplate', '/user/service', '/user/suggestion', '/designForm', '/designForm/home', '/user/supplierRegister', '/oauth2/authorize', '/thirdPartyEmail', '/user/forgetPassword', '/registrationGuidePage', '/user/qcLogin', '/questionnaire/observe', '/questionnaire/result', '/user/QcCodeBinding', '/BiddingInformationDetail', '/BiddingCandidateDetail', '/BiddingWinBulletinDetail', '/user/oaLogin']
// 下载地址
const PURCHASEATTACHMENTDOWNLOADAPI = '/attachment/purchaseAttachment/download'
const SALEATTACHMENTDOWNLOADAPI = '/attachment/saleAttachment/download'

// 待办审批地址
const GOING_LIST_PATH = '/srm/bpm/bpmnTodoList'
const DONE_LIST_PATH = '/srm/bpm/bpmnDoneList'
const APPROVAL_PATH_LIST = ['/srm/bpm/bpmnTodoList', '/srm/bpm/bpmnDoneList', '/srm/bpm/commuReceiverList', '/srm/bpm/toMeList', '/srm/bpm/commuDoneReceiverList', '/srm/bpm/startUpList', '/srm/bpm/task/adminTaskList']

// 新闻与活动
const ELS_TENANT_PORTAL_NEW_LIST = 'elsTenantPortalNewsList'
// 向下填充回调
const FILLDOWN_CALLBACK = 'filldownCallback'
// 上传附件扩展名 数据字典
const ATTACHMENT_EXTENSION_DICT_CODE = 'attachmentExtension'
// 数字类型最大长度
const NUMBER_MAX_LENGTH = 9
export {
    REPORT_ADDRESS,
    REPORT_JMREPORT_ADDRESS_SUFFIX,
    ROUTER_WHITE_LIST,
    PURCHASEATTACHMENTDOWNLOADAPI,
    SALEATTACHMENTDOWNLOADAPI,
    ROWSELECTMODALCONST,
    FILLDOWN_CALLBACK,
    ELS_TENANT_PORTAL_NEW_LIST,
    ATTACHMENT_EXTENSION_DICT_CODE,
    NUMBER_MAX_LENGTH,
    GOING_LIST_PATH,
    DONE_LIST_PATH,
    APPROVAL_PATH_LIST
}