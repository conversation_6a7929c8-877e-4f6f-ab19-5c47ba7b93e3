<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage" 
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
export default {
    name: 'SaleClarificationInfoDetail',
    mixins: [DetailMixin],
    data () {
        return {
            selectType: 'clarification',
            pageData: {
                form: {
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_basicData`, '基础信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_clarificationNo`, '澄清编号'),
                                    fieldName: 'clarificationNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_clarificationNo`, '澄清编号')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'businessType',
                                    dictCode: 'srmClarificationBusinessType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型')
                                },
                                {
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderNo`, '业务单号'),
                                    fieldName: 'businessNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderNo`, '业务单号'),
                                    
                                    extend: {
                                        linkConfig: {
                                            primaryKey: 'businessNumber',
                                            actionPath: '',
                                            bindKey: 'businessNumber',
                                            otherQuery: {linkFilter: true}
                                        },
                                        handleBefore: function (form, linkConfig, that) {
                                            if(form.businessType=='1'){
                                                linkConfig.primaryKey='ebiddingNumber', linkConfig.actionPath='/srm/ebidding/sale/EbiddingSaleHeadList'
                                            }
                                            if(form.businessType=='2'){
                                                linkConfig.primaryKey='enquiryNumber', linkConfig.actionPath='/srm/enquiry/sale/SaleEnquiryList'
                                            }
                                            if(form.businessType=='3'){
                                                linkConfig.primaryKey='biddingNumber', linkConfig.actionPath='/srm/bidding/sale/SaleBiddingHeadList'}
                                        }
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessName`, '业务描述'),
                                    fieldName: 'businessName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessName`, '业务描述')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessId`, '业务单据id'),
                                    fieldName: 'relationId',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessId`, '业务单据id')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_matterTitel`, '事项标题'),
                                    fieldName: 'matterTitel'
                                },
                                {
                                    fieldType: 'richEditorModel', 
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_clarificationMatter`, '澄清事项'),
                                    fieldName: 'clarificationMatter',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_clarificationMatter`, '澄清事项')
                                },
                                // {
                                //     fieldType: 'select',
                                //     fieldLabel: '阅读状态',
                                //     fieldName: 'viewStatus',
                                //     dictCode: 'srmHandleFlag'
                                // },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')
                                }
                            ]
                        }
                        
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'attachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/bidding/saleClarificationInfo/queryById',
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }
        }
    }
}
</script>
