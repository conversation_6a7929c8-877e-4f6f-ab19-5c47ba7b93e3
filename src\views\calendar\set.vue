<template>
  <div class="calendar_set">
    <!-- <div class="factory">
      <span class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_title_currentFactory`, '当前工厂') }}</span>
      <a-select 
        v-model="curFac"
        style="width: 300px;margin-right: 10px;"
        @change="handleChange">
        <a-select-option
          v-for="el of factoryArr"
          :key="el.orgCode"
          :value="el.orgCode">
          {{ el.orgName }}
        </a-select-option>
      </a-select>

      <a-button 
        class="master_plan"
        @click="goOverview"
        type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_title_annualOverview`, '年总览图') }}</a-button>
    </div> -->
    <FullCalendar
      ref="calendar"
      :options="calendarOptions" />
    <div class="btnCont">
      <div class="btns">
        <a-button 
          class=""
          @click="setting"
          type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_zRGR_2ef82466`, '批量设置') }}</a-button>
      </div>
      <div class="btns">
        <a-button 
          class=""
          @click="settingCallOn"
          type="primary">批量设置预约拜访日</a-button>
      </div>
    </div>
    <!-- 批量设置 -->
    <setDialog
      :factoryArr="factoryArr"
      @addCalendar="addCalendar"
      ref="setDialog" />

    <!-- 批量设置预约拜访日 -->
    <setCallOnDialog
      :factoryArr="factoryArr"
      @addCalendarCallon="addCalendarCallon"
      ref="setCallOnDialog" />
    <!-- 可以设置当天工作日活休息日 -->
    <a-modal
      title="设置"
      :visible="dayVisible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="() => { this.dayVisible = false}"
    >
      <a-form-model
        :model="form"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }">
        <a-form-model-item label="当前日期：">
          <span style="fontWeight:bold;">{{ currentDayLabel }}</span>
        </a-form-model-item>
        <a-form-model-item label="设置为：">
          <a-radio-group v-model="form.holiday">
            <a-radio value="1">
              {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_restday`, '休息日') }}
            </a-radio>
            <a-radio value="0">
              {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RkB_16a7aae`, '工作日') }}
            </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="预约拜访日">
          <a-radio-group v-model="form.visit">
            <a-radio value="1">
              是
            </a-radio>
            <a-radio value="0">
              否
            </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-item label="开放预约时间点">
          <a-select
            :maxTagCount="5"
            mode="multiple"
            v-model="form.timeSelect"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')"
          >
            <a-select-option
              v-for="el of time"
              :value="el.value"
              :key="el.value">
              {{ el.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import '@fullcalendar/core/vdom' // 解决启动vite日历组件报错问题
import FullCalendar from '@fullcalendar/vue'
import dayGridPlugin from '@fullcalendar/daygrid'
import interactionPlugin from '@fullcalendar/interaction'
import setDialog from './modules/setDialog'
import setCallOnDialog from './modules/setCallOnDialog'
import dateTrans from '@/utils/dateTransform'
import { getAction, postAction } from '@api/manage'
import { DEFAULT_LANG } from '@/store/mutation-types'
import {deepFlatten} from '@/utils/util.js'
// 
export default {
    components: {
        FullCalendar,
        setDialog,
        setCallOnDialog
    },
    data () {
        return {
            form: {
                holiday: '0',
                visit: '1',
                timeSelect: []
            },
            dayVisible: false,
            confirmLoading: false,
            time: [],
            currentDayLabel: '',
            calendarOptions: {
                plugins: [ dayGridPlugin, interactionPlugin ],
                initialView: 'dayGridMonth',
                selectable: false, // 是否拖动选择
                dateClick: this.handleDateClick,
                select: this.handleDateSelect,
                events: [],
                // eventSources: [
                //     {
                //         events: [],
                //         color: 'black',     // an option!
                //         textColor: 'yellow' // an option!
                //     },
                //     {
                //         events: [],
                //         color: 'blue',     // an option!
                //         textColor: 'red' // an option!
                //     }
                // ],
                // eventSources: [
                //     {
                //         events: [
                //             // {
                //             //     title: 'event1',
                //             //     start: '2023-04-17'
                //             // },
                //             // {
                //             //     title: 'event2',
                //             //     start: '2023-04-07',
                //             //     end: '2023-04-27'
                //             // },
                //             // {
                //             //     title: 'event3',
                //             //     start: '2023-04-20T12:30:00'
                //             // }
                //         ],
                //         color: 'black',     // an option!
                //         textColor: 'yellow' // an option!
                //     },
                //     {
                //         events: [
                //             // {
                //             //     title: 'event4',
                //             //     start: '2023-04-07'
                //             // },
                //             // {
                //             //     title: 'event5',
                //             //     start: '2023-04-15T12:30:00'
                //             // }
                //         ],
                //         color: 'red',     // an option!
                //         textColor: 'blue' // an option!
                //     }
                //     // any other event sources...
                // ],
                weekends: true,
                firstDay: 1, // 设置一周中显示的第一天是哪天，周日是0，周一是1，类推
                locale: this.$ls.get(DEFAULT_LANG)==='en' ? 'en-us' : 'zh-cn', // 切换语言，当前为中文
                // locale: 'zh-cn', // 切换语言，当前为中文
                eventColor: '#3BB2E3', // 全部日历日程背景色
                // themeSystem: 'bootstrap', // 主题色(本地测试未能生效)
                //initialDate: moment().format('YYYY-MM-DD'), // 自定义设置背景颜色时一定要初始化日期时间
                timeGridEventMinHeight: '20', // 设置事件的最小高度
                height: '80%', // 当前日历高度
                aspectRatio: 1.3, // 设置日历单元格宽度与高度的比例。
                // displayEventTime: false, // 是否显示时间
                // allDaySlot: false, // 周，日视图时，all-day 不显示
                eventLimit: true // 设置月日程，与all-day slot的最大显示数量，超过的通过弹窗显示
                // headerToolbar: { // 日历头部按钮位置
                //     left: '',
                //     center: 'prevYear,prev title next,nextYear',
                //     right: 'today dayGridMonth,timeGridWeek,timeGridDay'
                // },
                // buttonText: {
                //     today: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_HS_9e39f`, '今天'),
                //     month: '月',
                //     week: '周',
                //     day: '日'
                // }
            },
            curDayInfo: '',
            yearStart: '2021-01-01',
            yearEnd: '2021-12-30',
            factoryArr: [],
            curFac: ''
        }
    },
    methods: {
        selectOption (){
            postAction('/base/dict/findDictItems', {busAccount: `${this.$getLangAccount()}`, dictCode: 'calanderTime' }).then(res => {
                if(res.success == true){
                    this.time = res.result.map(ress=>{
                        return{
                            value: ress.value,
                            name: ress.value
                        }
                    })
                }
            })
        },
        handleOk () {
            this.confirmLoading = true
            const url = '/calendar/purchaseFactoryCalendar/settingCalendar'
            const params = {
                currentDate: this.currentDayLabel,
                holiday: this.form.holiday,
                visit: this.form.visit,
                times: this.form.timeSelect.join(',')
            }
            postAction(url, params).then(rs => {
                if (rs.success) {
                    this.dayVisible = false
                    // this.getFactory()
                    this.getCalendar()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // handleChange () {
        //     // 更新工厂后获取对应的
        //     this.getFactory()
        // },
        goOverview () {
            this.$router.push({path: '/calendar/overview'})
        },
        getCalendar () {
            let  that = this
            let url = 'calendar/purchaseFactoryCalendar/getCalendar'
            getAction(url).then(res=>{
                let result = res.result[0]
                if(res.code == 200) {
                    if (result) {
                        // let holiday = result.map(item => item.holidayList || [])
                        let holiday = deepFlatten(result.holidayList)
                        let hd = holiday.map( rs => ({
                            title: '休息日',
                            date: rs,
                            textColor: '#ffffff',
                            eventColor: '#3788d8',
                            color: '#3788d8'
                        }))
                        let visit = deepFlatten(result.visitList)
                        let hd2 = visit.map( rs => ({
                            title: '拜访日',
                            date: rs,
                            textColor: '#ffffff',
                            eventColor: '#9a1010',
                            color: '#109a17'
                        }))
                        debugger
                        this.calendarOptions.events = [...hd, ...hd2]
                        console.log(this.calendarOptions.events)
                        this.calendarId = result.id // 默认去第一个
                    } else {
                        this.calendarOptions.events = []
                    }
                }
            })
            return getAction(url, {elsAccount: this.$ls.get('Login_elsAccount'), factory: this.curFac})
        },
        addCalendarCallon (data){
            const account = this.$getLangAccount()
            const {$srmI18n} = this
            console.log(data)
            let holiday = this.holiday(data)
            console.log(holiday)
            let url = 'calendar/purchaseFactoryCalendar/addCalendar'
            let params = holiday.map(res=>{
                return{
                    dayOff: res,
                    times: data.timeSelect.map(res=> res).join(','),
                    dayType: 'visit'
                }
            })
            console.log(params)
            let dataSelects = {
                items: params,
                config: {
                    years: data.yearSelect.map(res=> res).join(','),
                    weeks: data.weekSelect.map(res=> res).join(','),
                    days: data.daySelect.map(res=> res).join(','),
                    times: data.timeSelect.map(res=> res).join(','),
                    configType: 'visit' 
                }
            }
            postAction(url, dataSelects).then((res) => {
                if(res.success) {
                    let hd = holiday.map( rs => ({ title: $srmI18n(`${account}#i18n_title_restday`, '拜访日'), date: rs, textColor: '#ffffff',
                        eventColor: '#9a1010',
                        color: '#109a17'}))
                    let holidayData = this.calendarOptions.events.filter(rx=> rx.title != '拜访日')
                    // 更新界面
                    this.calendarOptions.events = [...hd, ...holidayData]
                    this.getCalendar()
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        addCalendar (data) {
            const account = this.$getLangAccount()
            const {$srmI18n} = this
            let holiday = this.holiday(data)
            let url = 'calendar/purchaseFactoryCalendar/addCalendar'
            let params = holiday.map(res=>{
                return{
                    dayOff: res,
                    dayType: 'holiday'
                }
            })
            let dataSelects = {
                items: params,
                config: {
                    years: data.yearSelect.map(res=> res).join(','),
                    weeks: data.weekSelect.map(res=> res).join(','),
                    days: data.daySelect.map(res=> res).join(','),
                    configType: 'holiday' 
                }
            }
            let that = this
            postAction(url, dataSelects).then((res) => {
                if(res.success) {
                    let hd = holiday.map( rs => ({ title: $srmI18n(`${account}#i18n_title_restday`, '休息日'), date: rs, textColor: '#ffffff',
                        eventColor: '#3788d8',
                        color: '#3788d8'}))
                    console.log(that.calendarOptions.events)
                    let holidayData = that.calendarOptions.events.filter(rx=> rx.title != '休息日')
                    console.log(holidayData)
                    // 更新界面
                    that.calendarOptions.events = [...hd, ...holidayData]
                    this.getCalendar()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        setting () {
            let that = this
            getAction('calendar/purchaseFactoryCalendar/getCalendarConfig', {configType: 'holiday'}).then(res=>{
                let formSelectSeting = {
                    yearSelect: res?.result?.years ? res.result.years.split(',').map(res=>parseInt(res)) : [],
                    weekSelect: res?.result?.weeks ? res.result.weeks.split(',').map(res=>parseInt(res)) : [],
                    daySelect: res?.result?.days?res.result.days.split(',').map(res=>parseInt(res)): []
                } 
                that.$refs.setDialog.open(formSelectSeting)
            })
        },
        settingCallOn () {
            getAction('calendar/purchaseFactoryCalendar/getCalendarConfig', {configType: 'visit'}).then(res=>{
                let formSelectSeting = {
                    yearSelect: res?.result?.years ? res.result.years.split(',').map(res=>parseInt(res)) : [],
                    weekSelect: res?.result?.weeks ? res.result.weeks.split(',').map(res=>parseInt(res)) : [],
                    daySelect: res?.result?.days ? res.result.days.split(',').map(res=>parseInt(res)): [],
                    timeSelect: res?.result?.times ? res.result.times.split(','): []
                } 
                this.$refs.setCallOnDialog.open(formSelectSeting)
                // that.$refs.setDialog.open(formSelectSeting)
            })
           
        },
        holiday (data) {
            let ws = []
            let ds = []
            let years = data.yearSelect.sort((a, b) => a-b)
            console.log(years)
            let yearStart = `${years[0]}-01-01`
            let yearEnd = `${years.length > 1 ? years[years.length-1]: years}-12-31`
            console.log(yearStart)
            console.log(yearEnd)
            if (data.daySelect.length > 0) { // 日
                ws = dateTrans.getDayOrweek(yearStart, yearEnd, data.daySelect, 'day')
            }
            if (data.weekSelect.length > 0) { // 周
                ds = dateTrans.getDayOrweek(yearStart, yearEnd, data.weekSelect, 'week')
            }
            let hd = [...new Set([...ws, ...ds])]
            console.log(hd)
            return hd
        },
        handleDateSelect (info) {
            
            this.dayVisible= true
            this.currentDayLabel = info.startStr + '至' + info.endStr + '设为：'
        },
        handleDateClick (info) {
            this.currentDayLabel = info.dateStr
            this.curDay = info.dateStr
            getAction('calendar/purchaseFactoryCalendar/getSettingConfig', {currentDate: this.currentDayLabel}).then(res=>{
                this.form= {
                    holiday: res.result.holiday,
                    visit: res.result.visit,
                    timeSelect: res.result.times != null? res.result.times.split(','):[]
                },
                this.dayVisible= true
            })
        },
        toggleWeekends () {
            this.calendarOptions.weekends = !this.calendarOptions.weekends // toggle the boolean!
        }
    },
    mounted () {
        this.getCalendar()
        this.selectOption()
        console.log(this.$refs.calendar)
        console.log(this.$refs.toggleWeekends)
        console.log(this.$refs.calendar.getApi())
        // 前端获取周六日 具体日期
        // console.log(dateTrans.getWeek('2021-01-01', '2021-12-30', [6, 0]))
    }
}
</script>
<style lang="less" scoped>
    .calendar_set{
        padding: 20px;
        background: white;
        height: 100%;
        .factory{
            .label{
                margin-right: 10px;
            }
        }
        .btnCont{
            display: flex;
        }
        .btns{
            color: #109a17;
            margin-top: 20px;
            margin-right: 10px;
        }
    }
</style>