<template>
  <div class="els-page-comtainer">
    
    <a-page-header
      :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
      :title="title"
    >
      <template slot="extra">
        <a-button
          @click="handleOk"
          type="primary"
          key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <a-button
          @click="goBack"
          key="3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-page-header>
    <div class="table-page-search-wrapper">
      <a-spin :spinning="confirmLoading">
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            key="collpapse_1"
            :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
          >
           
            <a-form 
              class="ant-advanced-search-form"
              layout="inline"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
              :form="form">
              <a-row :gutter="24">
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_organizationTypeCode`, '组织类型编码')">
                    <j-dict-select-tag
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectOrganizationTypeCode`, '请选择组织类型编码')"
                      v-decorator="['orgTypeCode', validatorRules.orgTypeCode]"
                      :trigger-change="true"
                      dict-code="isrmOrgTypeCode"
                    /> 
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_organizationName`, '组织名称')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterOrganizationName`, '请输入组织名称')"
                      v-decorator="['orgName', validatorRules.orgName]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_organizationCode`, '组织编码')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterOrganizationCode`, '请输入组织编码')"
                      v-decorator="['orgCode', validatorRules.orgCode]" />
                  </a-form-item>
                </a-col>
             
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_address`, '地址')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterAddress`, '请输入地址')"
                      v-decorator="['address', validatorRules.address]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_chargePerson`, '负责人')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterChargePerson`, '请输入负责人')"
                      v-decorator="['principal', validatorRules.principal]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_sourceType`, '来源类型')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterSourceType`, '请输入来源类型')"
                      v-decorator="['sourceType', validatorRules.sourceType]" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')">
                    <a-input
                      v-model="form.remark"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterRemark`, '请输入备注')" 
                      v-decorator="['remark', validatorRules.remark]"/>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-collapse-panel>
          <a-collapse-panel
            key="collpapse_2"
            :header="$srmI18n(`${$getLangAccount()}#i18n_title_parentOrganization`, '上级组织')"
          >
            <vxe-grid
              border
              auto-resize 
              resizable
              column-key
              highlight-hover-row
              show-overflow
              ref="listGrid"
              height="300"
              size="small"
              :toolbar="tableToolbar"
              :checkbox-config="{highlight: true}"
              :columns="columns"
              :data="tableData">
              <template v-slot:toolbar_buttons>
                <div style="text-align:right">
                  <a-button
                    type="primary"
                    @click="selectParentOrg"
                    style="margin-right:8px">{{ $srmI18n(`${$getLangAccount()}#i18n_title_improt`, '导入') }}</a-button>
                  <a-button
                    @click="deleteEvent">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
                </div>
              </template>
            </vxe-grid>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </div>
    <select-modal
      ref="pickList"
      :url="url.queryParentOrg"
      :columns="selectColumns"
      :title="selectTitle"
      @ok="selectOk"/>
  </div>
  <!-- </a-modal> -->
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { duplicateCheck } from '@/api/api'
import selectModal from '@comp/selectModal/selectModal'
export default {
    components: {
        selectModal
    },
    name: 'OrgInfoModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            activeKey: ['collpapse_1', 'collpapse_2'],
            fixPageHeader: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationInfo`, '组织信息'),
            visible: false,
            model: {},
            labelCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            wrapperCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            confirmLoading: false,
            form: this.$form.createForm(this),
            validatorRules: {
                orgTypeCode: {rules: [{ required: true, message: '请输入组织类型编码!' }, {max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                orgCode: {rules: [{ required: true, message: '请输入组织编码!' }, {max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}, { validator: this.validateCode }]},
                orgName: {rules: [{ required: true, message: '请输入组织名称!' }, {max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                address: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                principal: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                sourceType: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                remark: {rules: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符' )}]}
              
            },
            url: {
                add: '/base/orgInfo/add',
                edit: '/base/orgInfo/edit',
                queryParentOrg: '/base/orgInfo/list',
                initParentOrg: '/base/baseOrgRelationship/queryByOrgId'
            },
            columns: [
                { type: 'checkbox', width: 40, align: 'center'},
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center'},
                { field: 'orgTypeName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationTypeName`, '组织类型名称'), align: 'center'},
                { field: 'orgCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationCode`, '组织编码'), align: 'center'},
                { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationName`, '组织名称'), align: 'center'}
            ],
            tableData: [],
            tableLoading: false,
            tableToolbar: {
                slots: {
                    buttons: 'toolbar_buttons'
                }
            },
            selectColumns: [
                { type: 'checkbox', width: 40, align: 'center'},
                { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60, align: 'center'},
                { field: 'orgTypeName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationTypeName`, '组织类型名称'), align: 'center'},
                { field: 'orgCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationCode`, '组织编码'), align: 'center'},
                { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationName`, '组织名称'), align: 'center'}           
            ],
            selectTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectParentOrganization`, '选择上级组织')
        }
    },
    mounted () {
        this.init()
        window.addEventListener('scroll', this.handleScroll)
    },
    methods: {
        init () {
            if(this.currentEditRow) {
                this.edit(this.currentEditRow)
            }else {
                this.add()
            }
        },
        add () {
            this.edit({})
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addOrganizationInfo`, '新增组织信息')
            this.tableData=[]
        },
        edit (record) {
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editOrganizationInfo`, '编辑组织信息')
            this.tableData=[]
            this.form.resetFields()
            this.model = Object.assign({}, record)
            this.initQuery(record)
            this.visible = true
            this.$nextTick(() => {
                this.form.setFieldsValue(pick(this.model, 'orgTypeCode', 'orgTypeName', 'orgCode', 'orgName', 'address', 'principal', 'sourceType', 'remark', 'fbk1', 'fbk2', 'fbk3', 'fbk4', 'fbk5', 'fbk6', 'fbk7', 'fbk8', 'fbk9', 'fbk10', 'fbk11', 'fbk12', 'fbk13', 'fbk14', 'fbk15', 'fbk16', 'fbk17', 'fbk18', 'fbk19', 'fbk20', 'extendField'))
                //时间格式化
            })

        },
        validateCode (rule, value, callback) {
        // 重复校验
            var params = {
                tableName: 'isrm_base_org_info',
                fieldName: 'org_code',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then((res) => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    that.confirmLoading = true
                    let httpurl = ''
                    let method = ''
                    if(!this.model.id){
                        httpurl+=this.url.add
                        method = 'post'
                    }else{
                        httpurl+=this.url.edit
                        method = 'post'
                    }
                    let formData = Object.assign(this.model, values)
                    let tableData = this.$refs.listGrid.getTableData().fullData
                    formData.lstBaseOrgRelationshipVO = tableData
                    httpAction(httpurl, formData, method).then((res)=>{
                        if(res.success){
                            that.$message.success(res.message)
                            that.$emit('ok')
                            that.goBack()
                        }else{
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        selectParentOrg (){   
            let id = this.model.id
            if(id){
                let params = {removeId: id}
                this.$refs.pickList.open(params)
            }else{
                this.$refs.pickList.open()
            }                   
        },
        selectOk (data){
            const listGrid = this.$refs.listGrid
            let tableData = listGrid.getTableData().fullData
            let idList = tableData.map(item => {
                return item.parentOrgId
            })
            let filterList = data.filter(item => {
                return !idList.includes(item.id)
            })
        
            let addTableData = []
            filterList.forEach((item, i) => {
                let lineNum = tableData.length + (i + 1)
                
                addTableData.push({
                    parentOrgId: item.id,
                    orgTypeCode: item.orgTypeCode,
                    orgTypeName: item.orgTypeName,
                    orgCode: item.orgCode,
                    orgName: item.orgName,
                    lineNumber: lineNum
                })
            })
            this.$refs.listGrid.insert(addTableData)

        },

        initQuery (item){
            const that = this
            if(item.id){
                this.tableLoading = true
                let params = {'orgId': item.id}
                getAction(this.url.initParentOrg, params).then(res => {
                    if(res.success&&res.result) {                      
                        that.tableData = res.result
                        that.tableLoading = false
                    }
                })
            
            }
        },

        deleteEvent (){
            let selectedData = this.$refs.listGrid.getCheckboxRecords()
            if(!selectedData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDeleteRowTips`, '请选择删除行'))
            }
            this.$refs.listGrid.removeCheckboxRow()
        },

        handleScroll () {
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 50) {
                this.fixPageHeader = true
            }else {
                this.fixPageHeader = false
            }
        }
    }
}
</script>

<style lang="less" scoped>

</style>