<template>
  <a-modal
    centered
    :mask-closable="false"
    :title="title"
    :visible="visible"
    :width="1120"
    @cancel="visible = false">
    <a-alert
      style="margin-bottom: 10px;"
      type="info"
      :description="$srmI18n(`${$getLangAccount()}#`, '根据数据字典srmEnquiryCompare动态配置更改')"
      :message="$srmI18n(`${$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示')"/>
    <div
      class="grid-box"
      style="overflow: hidden; position:relative">
      <vxe-grid
        auto-resize
        border
        height="300"
        ref="columnGrid"
        row-id="id"
        size="small"
        :columns="tableColumn"
        :data="tableData"
        :edit-config="{trigger: 'click', mode: 'cell'}">
        <template #empty>
          <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
        </template>
      </vxe-grid>
    </div>
    <template slot="footer">
      <a-button
        key="reset"
        @click="reset">
        {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reset`, '重置') }}
      </a-button>
      <a-button
        key="back"
        @click="visible = false">
        {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cancle`, '取消') }}
      </a-button>
      <a-button
        key="submit"
        type="primary"
        :loading="loading"
        @click="selectedOk">
        {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认') }}
      </a-button>
    </template>    
  </a-modal>
</template>
<script lang="jsx">
import {postAction} from '@api/manage'

export default {
    data () {
        return {
            columnCode: '',
            tableColumn: [
                {fixed: 'left', type: 'checkbox', width: 40},
                {
                    align: 'center',
                    field: 'columnCode',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title__columnCoding`, '列编码'),
                    width: 220
                },
                {
                    align: 'center',
                    field: 'columnName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title__columnName`, '列名称'),
                    width: 220
                },
                {
                    align: 'center',
                    editRender: {
                        name: 'mSwitch',
                        props: {closeValue: '0', openValue: '1'},
                        type: 'visible'
                    },
                    field: 'hidden',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hidden`, '是否隐藏')
                }
            ],
            tableData: [],
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n__GRIld_ec8c876e`, '对比项设置'),
            visible: false,
            loading: false
        }
    },
    methods: {
        open ({columnCode, tableData}){
            this.columnCode = columnCode
            this.tableData = JSON.parse(JSON.stringify(tableData))
            this.visible = true
        },
        reset (){
            this.selectedOk('0')
        },
        selectedOk (hidden){
            const {fullData} = this.$refs.columnGrid.getTableData()
            const columns = fullData.map(column => ({
                hidden: hidden == '0' ? hidden : column.hidden,
                columnName: column.columnName,
                columnCode: column.columnCode
            }))
            this.visible = false
            this.$emit('ok', {columnCode: this.columnCode, data: columns})
        }
    },
    name: 'ColumnSetting'
}
</script>
