<template>
  <a-modal
    centered
    v-drag
    :mask-closable="false"
    :title="title"
    :visible="visible"
    :width="1120"
    :confirmLoading="loading"
    @cancel="visible = false"
    @ok="handleOk">
    <a-textarea
      placeholder="请输入授标意见"
      style="width: 100%"
      v-model="form.awardOpinion"
      :auto-size="{maxRows: 5, minRows: 3}"/>
    <div style="margin-top: 4px">
      <vxe-grid
        ref="attachmentList"
        v-bind="gridConfig"
        :columns="columns"
        :data="tableData">
        <template #empty>
          <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
        </template>
        <template #grid_operation="{ row }">
          <a @click="handleDelete(row)">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}
          </a>
          <a @click="handleDownload(row)">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}
          </a>
        </template>
        <template #toolbar_buttons>
          <custom-upload
            dict-code="srmFileType"
            :accept="accept"
            :action="uploadUrl"
            :itemNumbeValueProp="'itemNumber'"
            :data="uploadData"
            :headers="tokenHeader"
            itemNumberSelectIndex
            :itemInfo="form.purchaseEnquiryItemList"
            :title="$srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件')"
            :visible.sync="modalVisible"
            @change="(info) => handleUploadChange(info)"/>
        </template>
      </vxe-grid>
    </div>
  </a-modal>
</template>

<script>
import CustomUpload from '@comp/template/CustomUpload'
import {getAction, postAction} from '@api/manage'
export default {
    components: {CustomUpload},
    computed: {
        uploadData () {
            return { businessType: 'enquiry', headId: this.form.headId }
        }
    },
    data (){
        return{
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            columns: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),
                    type: 'seq', 
                    width: 50 
                },
                {
                    field: 'fileType_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), 
                    width: 150 
                },
                {
                    field: 'fileName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), 
                    width: 150 
                },
                {
                    field: 'uploadTime',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), 
                    width: 150 
                },
                { 
                    field: 'uploadElsAccount_dictText', 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), 
                    width: 120 
                },
                {
                    field: 'itemNumber',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                    width: 120
                },
                { 
                    field: 'grid_operation',
                    slots: {default: 'grid_operation'},
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), 
                    width: 120 
                }
            ],
            form: {},
            gridConfig: {
                align: 'center',
                autoResize: true,
                border: true,
                columnKey: true,
                headerAlign: 'center',
                highlightHoverRow: true,
                resizable: true,
                showOverflow: true,
                size: 'mini',
                toolbarConfig: {
                    perfect: true,
                    slots: {buttons: 'toolbar_buttons'}
                }
            },
            modalVisible: false,
            tableData: [],
            tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_lBII_2ed15c51`, '授标意见'),
            uploadUrl: '/attachment/purchaseAttachment/upload',
            visible: false,
            loading: false
        }
    },
    methods: {
        handleDelete (row){
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                if (res.success) {
                    const itemGrid = this.$refs.attachmentList
                    itemGrid.remove(row)
                }else{
                    this.$notification.warning({description: res.message, message: '警告'})
                }
            })
        },
        handleDownload ({fileName, id}){
            getAction('/attachment/purchaseAttachment/download', {id}, {responseType: 'blob'}).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        handleOk (){
            this.visible = false
            this.loading = true
            postAction('/enquiry/purchaseEnquiryHead/submitPriced', this.form).then(res => {
                if(res.success){
                    this.$notification.success({description: res.message, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tkLRW_b20577c3`, '操作成功')})
                    this.$emit('success')
                }else{
                    if(!!res.message && res.message.indexOf("\n") >= 0) {
                        const h = this.$createElement;
                        let strList = res.message.split("\n");
                        strList = strList.map((str, strIndex) => {
                            return h(strIndex === 0? 'span' : 'div', strIndex === 0? null : { style: 'margin-left: 24px' } ,str);
                        })
                        this.$message.warning(h('span', { style: 'text-align: left' }, strList))
                        return;
                    }
                    this.$notification.warning({description: res.message, message: '警告'})
                }
            }).finally(() => {
                this.loading = false
            })
        },
        handleUploadChange (info){
            const itemGrid = this.$refs.attachmentList
            itemGrid.insertAt(info, -1)
        },
        open ({headId, purchaseEnquiryItemList}){
            this.$set(this.form, 'awardOpinion', null)
            this.$set(this.form, 'headId', headId)
            this.$set(this.form, 'purchaseEnquiryItemList', purchaseEnquiryItemList)
            this.visible = true
        }
    },
    name: 'EnquiryPriceSubmit'
}
</script>
