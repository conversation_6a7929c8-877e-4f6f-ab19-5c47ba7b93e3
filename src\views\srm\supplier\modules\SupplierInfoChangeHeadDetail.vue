<template>
  <div class="business-container">
    <business-layout
      v-if="isView"
      :ref="businessRefName"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :remoteJsFilePath="remoteJsFilePath"
      :pageHeaderButtons="pageHeaderButtons"
      :externalToolBar="externalToolBar"
      :gridCustomConfig="gridCustomConfig"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      :handleAfterDealSource="handleAfterDealSource"
      modelLayout="tab"
      pageStatus="detail"
      v-on="businessHandler"
    >
      <template #customRender="{slotProps}">
        <div
          :style="{color:primaryColor,cursor:'pointer'}"
          @click="linkToDetail(slotProps.group)">
          {{ `${$srmI18n(`${$getLangAccount()}#i18n_field_mARH_31060cbe`, '查看明细')}` }}
        </div>
      </template>
    </business-layout>
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <!-- 驳回弹出框 -->
    <a-modal
      v-drag    
      v-model="rejectVisible"
      :okText="okText"
      @ok="handleOkToReject">
      <a-form-model
        ref="rejectForm"
        :rules="rejectRules"
        :model="rejectForm">
        <a-form-model-item
          :label="this.$srmI18n(`${$getLangAccount()}#i18n_field_rMII_4787ce9d`, '驳回意见')"
          rop="rejectReason" >
          <a-textarea
            v-model="rejectForm.rejectReason"
            :placeholder="this.$srmI18n(`${$getLangAccount()}#i18n_alert_VWNrMII_ceaf9086`, '请输入驳回意见')"
            :auto-size="{ minRows: 3, maxRows: 5 }"
          />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <customSelectModal
      ref="customSelectModal"
      :isEdit="false"
      :footer="{footer: null}"
    />

    <qualificationFileModal
      ref="qualificationFileModal"/>

    <auditChangeTypeCodeModal
      ref="auditChangeTypeCodeModal"
      @ok="(auditChangeTypeCode) => handleOkAuditCode(auditChangeTypeCode)"
    />

  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import flowViewModal from '@comp/flowView/flowView'
import {getAction, postAction} from '@/api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import { mixin } from '@/utils/mixin.js'
import {
    BUTTON_BACK,
    BUTTON_AUDIT_CANCEL,
    BUTTON_ChECKPROCESS,
    SUPPLIER_INFO_CHANG_AUDIT
} from '@/utils/constant.js'
export default {
    mixins: [businessUtilMixin, mixin],
    components: {
        flowViewModal,
        BusinessLayout,
        customSelectModal: () =>import('../components/frozenFunctionModal.vue'),
        qualificationFileModal: () =>import('../components/QualificationFileModal.vue'),
        auditChangeTypeCodeModal: () =>import('../components/AuditChangeTypeCodeModal.vue'),
    },
    data () {
        return {
            isView: false,
            gridCustomConfig: {
                rowClassName: function ({row}){
                    if (row.updateType && row.updateType===3){
                        return 'row-delete-type'
                    }
                    if (row.updateType && row.updateType===2){
                        return 'row-add-type'
                    }
                },
                cellClassName ({ row, rowIndex, column, columnIndex }) {
                    if (row[column.property+'_new']) {
                        return 'row-add-type'
                    }
                }
            },
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_define`, '确定'),
            rejectForm: {rejectReason: ''},
            rejectRules: {
                rejectReason: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWNrMII_ceaf9086`, '请输入驳回意见'), trigger: 'blur' } ]
            },
            rejectVisible: false,
            flowView: false,
            flowId: 0,
            externalToolBar: {
                supplierBankInfoList: [
                    {...new BatchDownloadBtn({pageCode: 'supplierBankInfoList'}).btnConfig}
                ],
                supplierCertificatedInfoList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'),
                        key: 'gridDownload',
                        click: this.certificatedDownloadEvent
                    }
                ],
                supplierInfoChangeAttachmentList: [
                    {...new BatchDownloadBtn({pageCode: 'supplierInfoChangeAttachmentList'}).btnConfig}
                ]
            },
            pageHeaderButtons: [
                // {
                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DJ_c64d4`, '提交'),
                //     key: 'submit',
                //     attrs: {
                //         type: 'primary'
                //     },
                //     click: this.submitEvent,
                //     show: this.showcSubmitBtn
                // },
                {
                    ...BUTTON_AUDIT_CANCEL,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    click: this.cancelAudit,
                    show: this.showcCncelConditionBtn
                },
                {
                    ...BUTTON_ChECKPROCESS,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    click: this.showFlow,
                    show: this.showFlowConditionBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_iFUzQDJ_5f9433dd`, '选择审批流提交'),
                    key: 'submit',
                    attrs: {
                        type: 'primary'
                    },
                    authorityCode: 'supplier_info_change#supplierInfoChange:submitChange',
                    click: this.submitChangeEvent,
                    show: this.showConfirmChangeBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                    key: 'confirm',
                    attrs: {
                        type: 'primary'
                    },
                    click: this.handleConfirm,
                    show: this.showConfirmBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'),
                    key: 'confirm',
                    attrs: {
                        type: 'primary'
                    },
                    click: this.handleReject,
                    show: this.showConfirmBtn
                },
                {
                    ...BUTTON_BACK,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回')
                }
            ],
            requestData: {
                detail: {
                    url: '/supplier/supplierInfoChangeHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            infoChangVisible: false,
            url: {
                detail: '/supplier/supplierInfoChangeHead/queryById',
                confirm: '/supplier/supplierInfoChangeHead/confirm',
                rejected: '/supplier/supplierInfoChangeHead/rejected',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                submit: '/supplier/supplierInfoChangeHead/submit',
                submitAudit: '/a1bpmn/audit/api/submit',
                download: '/attachment/purchaseAttachment/download'
            }

        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_supplierMasterData_${templateNumber}_${templateVersion}`
        }
    },
    created () {
        // 如果是外部的参数，先请求获取模板js必须的参数
        if (this.$route.query && this.$route.query.open  && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                }
                this.isView = true
            })
        } else {
            this.isView = true
        }
    },
    methods: {
        // 选择审批流提交【弹出框】
        submitChangeEvent() {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {},
                supplierAddressInfoList= [],
                supplierBankInfoList = [],
                supplierContactsInfoList = [],
                supplierOrgInfoList = []
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm,
                ...allData,
                id: enterpriseForm.id
            }
            delete formatData.enterpriseForm
            delete formatData.expandForm

            if (!(formatData.needAudit && formatData.needAudit==='1')) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VPrtFGRLTPUz_92bd7a03`, '请将该单据设置为【需要审批】'))
                return
            }

            this.$refs.auditChangeTypeCodeModal.open(formatData.elsAccount)
        },
        // 选择审批流提交-弹出框【确定】
        async handleOkAuditCode (setFormAuditCode) {
            let that = this
            // 获取参数
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {},
                supplierAddressInfoList= [],
                supplierBankInfoList = [],
                supplierContactsInfoList = [],
                supplierOrgInfoList = []
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm,
                ...allData,
                id: enterpriseForm.id,
                auditChangeTypeCode: setFormAuditCode.auditChangeTypeCode
            }
            delete formatData.enterpriseForm
            delete formatData.expandForm

            this.$refs[this.businessRefName].confirmLoading = true
            this.$refs.auditChangeTypeCodeModal.submitLoadingAuditCode = true
            const supplierInfoChange = SUPPLIER_INFO_CHANG_AUDIT.get(setFormAuditCode.auditChangeTypeCode)
            let param = {}
            param['businessId'] = formatData.supplierInfoChangeId
            param['rootProcessInstanceId'] = formatData.infoChangFlowId
            param['businessType'] = supplierInfoChange.businessType
            param['auditSubject'] = `${supplierInfoChange.auditSubject}${this.currentEditRow.changeNumber}`
            // param['businessType'] = 'supplierInfoChangAudit'
            // param['auditSubject'] = await that.$FlowUtil.convertFlowTitle(that.currentEditRow,
            //     formatData.toElsAccount + ' -- 供应商信息变更审批')
            param['auditOpinion'] = setFormAuditCode.auditOpinion
            param['params'] = JSON.stringify(formatData)
            postAction(this.url.submitAudit, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.businessHide()
                    this.$refs[this.businessRefName].confirmLoading = false
                    this.$refs.auditChangeTypeCodeModal.submitLoadingAuditCode = false
                    this.$parent.showEditPage = false
                } else {
                    this.$message.warning(res.message)
                    this.$refs.auditChangeTypeCodeModal.submitLoadingAuditCode = false
                    this.$refs[this.businessRefName].confirmLoading = false
                }
            })
        },
        // 确定操作
        handleConfirm () {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLrtF_a1bd760e`, '是否确认该单据?'),
                onOk: function () {
                    getAction(that.url.confirm, {id: formatData.infoChangId}).then((res) => {
                        if (res && res.success) {
                            that.$message.success(that.$srmI18n(`${that.$getLangAccount()}#i18n__RLLRW_e207d19c`, '确认成功！'))
                            that.businessHide()
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        handleAfterDealSource (pageConfig) {
            const allData = this.getAllData()
            const { enterpriseForm= {}, expandForm = {} } = allData
            let formatData = { ...enterpriseForm, ...expandForm }

            pageConfig.groups.forEach(v=>{
                if(v.groupCode !== 'enterpriseForm' && v.groupCode !== 'expandForm' && v.groupCode !== 'supplierInfoChangeAttachmentList' && v.groupCode !== 'supplierCertificatedInfoList'){
                    const column = {
                        groupCode: 'supplierContactsInfoList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_crAc_25e0e6bb`, '修改类型'),
                        field: 'updateType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        width: '150'
                    }
                    v.columns.splice(3, 0, column)
                }

                // 🎯 启用联系人信息页签的自动高度功能
                if (v.groupCode === 'supplierContactsInfoList') {
                    // 确保 extend 对象存在
                    if (!v.extend) {
                        v.extend = {}
                    }

                    // 计算当前可用高度 - 针对tab布局优化
                    const clientHeight = document.documentElement.clientHeight
                    const availableHeight = clientHeight - 250  // 减少固定高度预留，更充分利用空间

                    // 获取当前数据条数
                    const dataLength = allData.supplierContactsInfoList?.length || 0

                    // 启用根据数据条数自动设置高度
                    v.extend.autoHeightByData = true
                    v.extend.rowHeight = 36                    // 每行高度
                    v.extend.headerHeight = 50                 // 表头高度
                    v.extend.paddingHeight = 10                // 减少内边距，节省空间

                    // 智能最大高度策略
                    if (dataLength > 10) {
                        // 数据较多时，使用更大的最大高度
                        v.extend.maxHeight = Math.max(700, availableHeight)
                    } else if (dataLength > 5) {
                        // 中等数据量，适中高度
                        v.extend.maxHeight = Math.max(600, availableHeight * 0.8)
                    } else {
                        // 数据较少时，避免过高
                        v.extend.maxHeight = Math.max(400, availableHeight * 0.6)
                    }

                    v.extend.minHeight = 150                   // 最小高度限制

                    console.log(`[联系人信息页签] 已启用智能自动高度功能`, {
                        dataLength,
                        clientHeight,
                        availableHeight,
                        strategy: dataLength > 10 ? '大数据量' : dataLength > 5 ? '中等数据量' : '小数据量',
                        maxHeight: v.extend.maxHeight,
                        config: v.extend
                    })
                }

                // 添加变更标识
                if (formatData[v.groupCode+'_hasStar']) {
                    v.hasStar=true
                }
            })

            // 银行页签 file 下载
            let index0 = pageConfig.groups.findIndex(item => item.groupCode == 'supplierBankInfoList')
            if (pageConfig.groups[index0].extend && pageConfig.groups[index0].extend.editConfig) {
                pageConfig.groups[index0].extend.optColumnList = [{key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.bankFileDownloadEvent}]
            }

            // 资质认证页签 file 列表
            let index1 = pageConfig.groups.findIndex(item => item.groupCode == 'supplierCertificatedInfoList')
            if (pageConfig.groups[index1].extend) {
                pageConfig.groups[index1].extend.optColumnList = [{key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Attachment`, '附件'), click: this.qualificationFileList}]
            }
        },
        // 驳回确定操作
        handleOkToReject () {
            let rejectReason = this.rejectForm.rejectReason
            if(!rejectReason || rejectReason == ''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWNrMII_ceaf9086`, '请输入驳回意见'))
                return
            }
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            postAction(this.url.rejected, {id: formatData.infoChangId, rejectReason: rejectReason}).then((res) => {
                if (res && res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_rMLR_a9701ba7`, '驳回成功！'))
                    this.businessHide()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        // 驳回弹出框操作
        handleReject () {
            this.rejectForm.rejectReason = ''
            this.rejectVisible = true
        },
        // 查看流程
        showFlow (){
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            this.flowId = formatData.infoChangFlowId
            this.flowView = true
        },
        // 撤销审批
        cancelAudit (){
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm,
                ...allData,
                id: enterpriseForm.id
            }
            let that = this
            let url = this.url.cancelAudit
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: async function () {
                    that.$refs[that.businessRefName].confirmLoading = true
                    let param = {}
                    const supplierInfoChange = SUPPLIER_INFO_CHANG_AUDIT.get(that.currentEditRow?.auditChangeTypeCode)
                    param['businessId'] = formatData.infoChangId
                    param['rootProcessInstanceId'] = formatData.infoChangFlowId
                    param['businessType'] = supplierInfoChange?.businessType
                    param['auditSubject'] = `${supplierInfoChange.auditSubject}${that.currentEditRow?.changeNumber}`
                    // param['businessType'] = 'supplierInfoChangAudit'
                    // param['auditSubject'] = await that.$FlowUtil.convertFlowTitle(that.currentEditRow,
                    //     formatData.toElsAccount + ' -- 供应商信息变更审批')
                    param['params'] = JSON.stringify(formatData)
                    postAction(url, param).then((res) => {
                        that.$refs[that.businessRefName].confirmLoading = false
                        if (res.success) {
                            that.$message.success(res.message)
                            that.businessHide()
                        } else {
                            that.$message.warning(res.message)
                        }
                    }).catch(()=>{
                        that.$refs[that.businessRefName].confirmLoading = false
                    })
                }
            })
        },
        // 提交
        submitEvent () {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm,
                ...allData,
                id: enterpriseForm.id
            }
            let that = this
            const supplierInfoChange = SUPPLIER_INFO_CHANG_AUDIT.get(that.currentEditRow?.auditChangeTypeCode)
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmToSubmit`, '确认提交'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_i18n_title_confirmToSubmitTips`, '是否确认提交?'),
                onOk: async function () {
                    that.$refs[that.businessRefName].confirmLoading = true
                    // 需要审批
                    if (formatData.infoChangNeedAudit && formatData.infoChangNeedAudit==='1' && formatData.infoChangAuditStatus==='0') {
                        let param = {}
                        param['businessId'] = formatData.infoChangId
                        param['rootProcessInstanceId'] = formatData.infoChangFlowId
                        param['businessType'] = supplierInfoChange?.businessType
                        param['auditSubject'] = `${supplierInfoChange.auditSubject}${that.currentEditRow?.changeNumber}`
                        // param['businessType'] = 'supplierInfoChangAudit'
                        // param['auditSubject'] = await that.$FlowUtil.convertFlowTitle(that.currentEditRow,
                        //     formatData.toElsAccount + ' -- 供应商信息变更审批')
                        param['params'] = JSON.stringify(formatData)
                        postAction(that.url.submitAudit, param).then(res => {
                            that.$refs[that.businessRefName].confirmLoading = false
                            if (res.success) {
                                that.businessHide()
                                that.$message.success(res.message)
                            } else {
                                that.$message.warning(res.message)
                            }
                        }).catch(()=>{
                            that.$refs[that.businessRefName].confirmLoading = false
                        })
                    } else {
                        // 无需审批
                        postAction(that.url.submit, formatData).then(res => {
                            that.$refs[that.businessRefName].confirmLoading = false
                            if(res.success) {
                                if (!that.currentEditRow.id) {
                                    that.currentEditRow.id = res.result.id
                                }
                                that.businessHide()
                                that.$message.success(res.message)
                            }else {
                                that.$message.warning(res.message)
                            }
                        }).catch(()=>{
                            that.$refs[that.businessRefName].confirmLoading = false
                        })
                    }
                }
            })
        },
        showcSubmitBtn () {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            console.log(formatData, 'formatData')
            let status = formatData.infoChangStatus
            let auditStatus = formatData.infoChangAuditStatus
            // “新建+未审批” 或者 "新建+无需审批"
            if((status==='0' && auditStatus && auditStatus==='0') || (status==='0' && auditStatus && auditStatus==='4')){
                return true
            }else{
                return false
            }
        },
        showcCncelConditionBtn (){
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            let status = formatData.infoChangStatus
            let auditStatus = formatData.infoChangAuditStatus
            // 新建+审批中 || 待确认+审批中
            if((status==='0' && auditStatus==='1') || (status==='3' && auditStatus==='1')){
                return true
            }else{
                return false
            }
        },
        showFlowConditionBtn (){
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            let auditStatus = formatData.infoChangAuditStatus
            let status = formatData.infoChangStatus
            // “新建+审批中 或者 审批拒绝”，“生效+审批通过”、“待确认+审批中”、“已确认+审批通过”、“已驳回+审批拒绝”
            if((status==='0' && auditStatus && (auditStatus==='1' || auditStatus==='3'))
                || (status==='1' && auditStatus && auditStatus==='2')
                || (status==='3' && auditStatus && auditStatus==='1')
                || (status==='4' && auditStatus && auditStatus==='2')
                || (status==='5' && auditStatus && auditStatus==='3')){
                return true
            }else {
                return false
            }
        },
        showConfirmChangeBtn (){
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            let status = formatData.infoChangStatus
            let publishStatus = formatData.infoChangPublishStatus
            let auditStatus = formatData.infoChangAuditStatus
            let needAudit = formatData.infoChangNeedAudit

            // 单据状态为“待确认” 和 发布状态为“已发布”，则显示按钮
            if ((auditStatus==='' || auditStatus==='0' || auditStatus==='4')
                && (status==='3' && publishStatus && publishStatus==='1')) {
                return true
            }
            return false
        },
        showConfirmBtn (){
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {}
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm
            }
            let status = formatData.infoChangStatus
            let publishStatus = formatData.infoChangPublishStatus
            let auditStatus = formatData.infoChangAuditStatus

            // 单据状态为“待确认” 和 发布状态为“已发布”，则显示按钮
            if ((auditStatus==='' || auditStatus==='0' || auditStatus==='4')
                && (status==='3' && publishStatus && publishStatus==='1')) {
                return true
            }
            return false
        },
        handleBeforeRemoteConfigData (data) {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXVHAHBI_4e631e49`, '供应商信息变更附件'),
                        groupCode: 'supplierInfoChangeAttachmentList',
                        groupType: 'item',
                        sortOrder: data.groups.length+'',
                        extend: {
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'supplierBankInfoList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '100',
                        slots: {default: 'grid_opration'}
                    },

                    {
                        groupCode: 'supplierCertificatedInfoList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '100',
                        slots: {default: 'grid_opration'}
                    },

                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }

                ]
            }
        },
        bankFileDownloadEvent (Vue, row) {
            this.downloadFile (row)
        },
        downloadFile (row){
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }

            let fileNameStr = row.fileName
            if (row.fileName.includes('➔')) {
                if (fileNameStr.split('➔').length > 1 && (fileNameStr.split('➔')[1]!=='' && fileNameStr.split('➔')[1]!==null)) {
                    fileNameStr = fileNameStr.split('➔')[1].trim()
                } else {
                    fileNameStr = fileNameStr.split('➔')[0].trim()
                }
            }

            const [id, fileName] = fileNameStr.split('-')
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        downloadEvent (Vue, row) {
            const params = {id: row.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        // 资质认证批量下载功能
        certificatedDownloadEvent ({groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            let ids = []
            for(let item of checkboxRecords){
                ids.push(item.id)
            }
            let str = ids.join(',')
            getAction('/supplier/purchaseCertificatedInfo/queryFileListByIds', {ids: str}).then(res => {
                if (res.success) {
                    let list = res.result || []
                    if (list && list.length===0) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BjqIKjBI_9a59ec9d`, '没有可下载的附件'))
                    } else {
                        new BatchDownloadBtn().batchDownloadZip(list)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        // 资质认证-附件列表弹出框事件
        qualificationFileList (Vue, row) {
            this.$refs.qualificationFileModal.open(row)
        },
        // 基本信息冻结功能弹出框
        linkToDetail (data) {
            console.log(data)
            let {supplierMasterFrozenHistoryList, frozenFunction} = data.formModel
            let params = {
                supplierMasterFrozenHistoryList,
                frozenFunction: []
            }
            try {
                params = {
                    supplierMasterFrozenHistoryList,
                    frozenFunction: frozenFunction ? JSON.parse(frozenFunction) : ''
                }

            } catch (e) {
                console.log(e)
            }
            this.$refs.customSelectModal.open(params)
        },
        changeInfoView (row){
            this.currentChangEditRow = row
            this.infoChangVisible = true
        }
    }
}
</script>
<style lang="scss" scoped>
.business-container{
     :deep(.row-delete-type){
        color: rgba(0, 0, 0, 0.25);
        background-color: #f5f5f5;
    }
    :deep(.row-add-type){
        background-color: #fff3e0;
        color:rgb(24, 144, 255);
    }
}
</style>