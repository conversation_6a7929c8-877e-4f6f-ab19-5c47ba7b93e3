<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showEditPromisePage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />

    <detail
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <edit
      v-if="showEditPromisePage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <a-modal
      v-drag    
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="templateVisible"
      @ok="selectedTemplate">
      <template slot="footer">
        <a-button
          key="back"
          @click="handleTemplateCancel">

          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedDeliveryTemplate">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"/>
    </a-modal>
    <record-modal
      v-model="recordShowVisible"
      :currentEditRow="currentEditRow"
    />
  </div>
</template>
<script>
import SaleContractHeadModal from './modules/SaleContractHeadModalNew'
import SaleContractPromiseModal from './modules/SaleContractPromiseModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {httpAction} from '@/api/manage'
import RecordModal from '@comp/recordModal'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        'detail': SaleContractHeadModal,
        'edit': SaleContractPromiseModal,
        'record-modal': RecordModal
    },
    data () {
        return {
            showEditPage: false,
            showEditPromisePage: false,
            recordShowVisible: false,
            templateVisible: false,
            businessType: '',
            toElsAccount: '',
            submitLoading: false,
            nextOpt: true,
            templateNumber: undefined,
            templateOpts: [],
            createRow: {},
            pageData: {
                businessType: 'contract',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractStatus`, '合同状态'),
                        fieldName: 'contractStatus',
                        dictCode: 'srmContractStatus',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: true, clickFn: this.settingColumns}
                ],
                form: {
                    keyWord: '',
                    contractStatus: ''
                },
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), authorityCode: 'contract#saleContractHead:view', clickFn: this.handleView},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/contract/saleContractHead/list',
                createPromise: '/contract/saleContractPromise/createPromise',
                exportXlsUrl: 'contract/saleContractHead/exportXls',
                columns: 'saleContractHeadList'
            }
        }
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.contractNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SaleContractHead', url: this.url || '', recordNumber})
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_salesContractHeader`, '销售合同头'))
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showEditPromisePage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
        },
        createPromise (row){
            this.nextOpt = true
            this.createRow = row
            this.serachTemplate('contractPromise')
        },
        showPromiseCondition (row) {
            if (row.promiseType=='promiseSale') {
                return false
            }else {
                return true
            }
        },
        serachTemplate (businessType) {
            this.currentEditRow = {}
            let selectedRows = this.createRow
            this.pageData.toElsAccount = this.createRow.toElsAccount
            if (this.nextOpt){
                this.pageData.businessType = businessType
                this.pageData.toElsAccount = selectedRows.toElsAccount
                this.openModal(selectedRows.toElsAccount)
            }
        },
        openModal (elsAccount) {
            this.$refs.listPage.queryTemplateList(elsAccount).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.templateNumber,
                                title: item.templateName,
                                version: item.templateVersion,
                                account: item.elsAccount
                            }
                        })
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length===1) {
                            this.templateNumber = this.templateOpts[0].value
                            this.selectedDeliveryTemplate()
                        } else {
                            // 有多个模板先选择在新建
                            this.templateVisible = true
                        }
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        handleTemplateCancel () {
            this.templateVisible = false
        },
        selectedDeliveryTemplate () {
            if(this.templateNumber) {
                const that = this
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version,
                    templateAccount: template[0].account,
                    busAccount: '',
                    headId: this.createRow.id
                }
                that.templateVisible = false
                that.submitLoading = false
                if (this.url.createPromise==''){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
                    return
                }
                params.saleOrderDeliveryPlanList =that.$refs.listPage.$refs.listGrid.getCheckboxRecords()
                params.busAccount = that.$refs.listPage.$refs.listGrid.getCheckboxRecords()[0].toElsAccount
                that.postUpdateData(this.url.createPromise, params)
            }
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.currentEditRow = res.result
                    this.showEditPromisePage = true
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        }
    }
}
</script>