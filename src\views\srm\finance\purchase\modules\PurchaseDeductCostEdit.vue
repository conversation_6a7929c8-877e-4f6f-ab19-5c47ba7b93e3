<template>
  <div class="page-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"></business-layout>
    </a-spin>
    <field-select-modal
      ref="fieldSelectModal" />
    <select-Data-Modal
      ref="selectDataModal"
      @ok="selectDataOk"/>
    <!-- 加载配置文件 -->
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import selectDataModal from './selectDataModal'
import { getAction } from '@/api/manage'
import {BUTTON_PUBLISH, BUTTON_SAVE, BUTTON_SUBMIT} from '@/utils/constant.js'
export default {
    name: 'PurchaseDeductCostEdit',
    mixins: [ businessUtilMixin ],
    components: {
        fieldSelectModal,
        selectDataModal,
        BusinessLayout
    },
    data () {
        return {
            businessRefName: 'businessRef',
            requestData: {
                detail: {
                    url: '/finance/purchaseDeductCost/queryById',
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                purchaseAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'deductCost', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                        click: this.deleteBatch
                    }
                ],
                deductCostItemList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary'},
                    click: this.handleAdd
                }, 
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iFVVwj_6960ba46`, '选择扣款来源'),
                    attrs: { type: 'primary'},
                    click: this.selectSourceEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete',
                    click: this.businessGridDelete
                }
                ]
            },
            pageFooterButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/finance/purchaseDeductCost/edit'
                    }
                },
                {
                    ...BUTTON_PUBLISH,
                    args: {
                        url: '/finance/purchaseDeductCost/publish'
                    },
                    showCondition: this.showPublishCondition,
                    authorityCode: 'deductCost#purchaseDeductCost:publish'
                },
                {
                    ...BUTTON_SUBMIT,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_examineApprove`, '审批'), 
                    args: {
                        url: '/a1bpmn/audit/api/submit'
                    },
                    handleBefore: this.handleBeforeFooterSubmit,
                    showCondition: this.showSubmitAuditCondition
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                add: '/finance/purchaseDeductCost/add',
                edit: '/finance/purchaseDeductCost/edit',
                detail: '/finance/purchaseDeductCost/queryById',
                public: '/finance/purchaseDeductCost/publish',
                upload: '/attachment/purchaseAttachment/upload',
                submitAudit: '/a1bpmn/audit/api/submit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${elsAccount}/purchase_deductCost_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        selectDataOk () {
            const { pageConfig = {} } = this.getBusinessExtendData(this.businessRefName)
            this.handleHeaderFields({pageData: pageConfig, flag: true})
        },
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.deductNumber,
                actionRoutePath: '/srm/finance/purchase/PurchaseDeductCostList,/srm/finance/sale/SaleDeductCostList'
            }
        },
        handleBeforeFooterSubmit (args) {
            const { allData = {} } = args || {}
            return new Promise((resolve, reject) => {
                let formatData = {
                    businessId: allData.id,
                    businessType: 'deductCost',
                    auditSubject: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deductionNumber`, '扣款单号')  + `${allData.deductNumber}`,
                    params: JSON.stringify(allData)
                }
                resolve({
                    ...args,
                    allData: formatData
                })
            })
        },
        // 处理最后的数据
        handleAfterDealSource (pageConfig, resultData) {
            if (resultData?.deductCostItemList?.length) { // 行信息有值就置灰表头
                this.handleHeaderFields({pageData: pageConfig, flag: true})
            }
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '3',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFilesEvent}
                            ]
                        }
                    }
                ],
                formFields: [],
                itemColumns: [
                    { 
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        width: 200
                    },
                    { 
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        width: 180
                    },
                    { 
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        width: 120
                    },
                    { 
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        width: 120
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        width: '100',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        goBack () {
            this.$emit('hide')
        },
        saveEvent () {
            this.$refs.editPage.postData()
        },
        publishEvent () {
            this.$refs.editPage.handleSend()
        },
        uploadCallBack (result) {
            let fileGrid =this.getItemGridRef('purchaseAttachmentList')
            fileGrid.insertAt(result, -1)
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            this.attachmentDownloadEvent(row)
        },
        businessGridAddPopup (row) {

        },
        selectSourceEvent (row) {
            let pageData=this.getAllData() || {}
            if (!pageData.toElsAccount) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFRdX_f2ffa076`, '请选择供应商！'))
                return
            }
            if (!pageData.company) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectCompanyTips`, '请选择公司！'))
                return
            }
            let params = {}
            params.toElsAccount = pageData.toElsAccount
            params.saleElsAccount = pageData.toElsAccount
            params.company = pageData.company
            params.companyCode = pageData.company
            this.$refs.selectDataModal.open(params)
        },
        //新增行
        insertGridItem () {
            let itemGrid = this.$refs.editPage.$refs.deductCostItemList[0]
            itemGrid.insert({})
        },
        deleteFilesEvent (Vue, row) {
            const fileGrid =this.getItemGridRef('purchaseAttachmentList')
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        showSubmitAuditCondition () {
            let params = this.getAllData() || {}
            let requiredAudit = params.requiredAudit
            if(requiredAudit == '1'){
                return true
            }else{
                // 不可操作
                return false
            }
        },
        showPublishCondition () {
            let params = this.getAllData() || {}
            let requiredAudit = params.requiredAudit||'0'
            if ((!params.id)) {
                return false
            }
            if(requiredAudit == '0'){
                return true
            }else{
                // 不可操作
                return false
            }
        },
        // 表格通用删除
        businessGridDelete ({ groupCode = '' }) {
            if (!groupCode) {
                return
            }
            let itemGrid = this.getItemGridRef(groupCode)
            const { pageConfig = {} } = this.getBusinessExtendData(this.businessRefName)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            itemGrid.removeCheckboxRow()
            let {fullData} = itemGrid.getTableData()
            if (fullData?.length == 0) {
                this.handleHeaderFields({pageData: pageConfig, flag: false})
            }
        },
        // 批量删除
        deleteBatch () {
            const fileGrid = this.getItemGridRef('purchaseAttachmentList')
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        }
    }
}
</script>
