<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <content-header
        v-if="showHeader"
        :btns="btns"
      />
      <div
        class="container"
        :style="style">
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_WBeRW_78efa911`, '落标通知书') }}</span>
          </titleTrtl>
          <vxe-grid
            v-bind="gridConfig"
            :height="250"
            ref="table"
            :data="tableData"
            :columns="tableColumns"
            show-overflow="title" >
            <template #grid_opration="{ row, column }">
              <!-- <a-upload
                name="file"
                :showUploadList="false"
                :action="uploadUrl"
                :headers="uploadHeader"
                :accept="accept"
                :data="{headId: row.id, businessType: 'biddingPlatform'}"
                :beforeUpload="beforeUpload"
                @change="(file) => handleUploadChange(file, row)"
              >
                <a-button
                  v-if="showHeader && pageStatus == 'edit'"
                  type="link"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
              </a-upload> -->
              <div>
                <a
                  style="margin:0 4px"
                  @click="preViewEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                <a @click="downloadEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
              </div>
            </template>
          </vxe-grid>
        </div>
      </div>
    </a-spin>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '../components/content-header'
import titleTrtl from '../components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    name: 'BidRejectionNotice',
    components: {
        titleTrtl,
        ContentHeader
    },
    mixins: [tableMixins],
    data () {
        return {
            confirmLoading: false,
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WBeRW_78efa911`, '落标通知书'),
                    'field': 'purchaseAttachmentDTOList',
                    slots: {
                        default: ({ row, column }) => {
                            return [
                                // <span >{row.purchaseAttachmentDTOList[0]}</span>
                                (row['purchaseAttachmentDTOList'] != null) ? <div ><span style='color: blue' onClick={() => this.preViewEvent(row)}>{row['purchaseAttachmentDTOList'][0]['fileName']} </span> </div>: ''
                            ]
                        }
                    }
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    'field': 'evaPrice3',
                    slots: {default: 'grid_opration' }
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            btns: [
                // { title: '保存', type: 'primary', click: this.save },
                // { title: '发布', type: 'primary', click: this.submit }
            ],
            url: {
                queryById: '/tender/sale/saleTenderProjectBidWinningNoAffirmItem/queryBySubpackageId',
                add: '/tender/calibration/purchaseTenderBidWinningAffirmNoInfom/add',
                edit: '/tender/calibration/purchaseTenderBidWinningAffirmNoInfom/edit',
                submit: '/tender/calibration/purchaseTenderBidWinningAffirmNoInfom/submit'
            }
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'resetCurrentSubPackage'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        pageStatus () {
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                return 'detail'
            } else {
                if (this.formData.status == '0' || !this.formData.status) {
                    return 'edit'
                } else {
                    return 'detail'
                }
            }
        }
    },
    methods: {
        async getData () {
            this.height = document.documentElement.clientHeight
            let params = {
                subpackageId: this.subId
            }
            this.confirmLoading = true
            await getAction(this.url.queryById, params).then(res => {
                if(res.success) {
                    this.tableData = (res.result ?? '') != '' ? [res.result] : []
                }
            }).finally(() => {
                this.confirmLoading = false
                // this.$emit('resetCurrentSubPackage')
                this.resetCurrentSubPackage()
            })
        },
        preViewEvent (row){
            row.purchaseAttachmentDTOList[0].subpackageId = this.subId
            this.$previewFile.open({params: row.purchaseAttachmentDTOList[0] })
        },
        async downloadEvent (row) {
            row.purchaseAttachmentDTOList[0].subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row.purchaseAttachmentDTOList[0])
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.purchaseAttachmentDTOList[0].fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
        }
    },
    async mounted () {
        await this.getData()
    }
}
</script>
<style lang="less" scoped>
.container{

    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
.margin-t-20{
  margin-top: 20px;
}
.label{
  text-align:right;
  padding-right: 10px;
}
</style>




