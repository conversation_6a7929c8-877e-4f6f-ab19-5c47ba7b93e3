<template>
  <div>
    <!-- 单据联查 -->
    <a-modal
    v-drag    
      centered
      :width="'90%'"
      :height="'90%'"
      :maskClosable="false"
      :visible="modalVisibleDocket"
      :mask="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_alert_tFKm_2766a28a`, '单据联查')"
      @ok="closeModalDocket"
      @cancel="closeModalDocket">
      <div>
        <div
          style="width: calc(100%);height:calc(60vh);">                   
          <SeeksRelationGraph
            ref="seeksRelationGraph"
            :options="graphOptions"
            :on-node-expand="onNodeExpand"
            :on-node-click="onNodeClick">
          </SeeksRelationGraph>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import SeeksRelationGraph from 'relation-graph'
import {getAction, postAction} from '@/api/manage'
export default {
    name: 'RelationGraphModal',
    components: {SeeksRelationGraph},
    props: {
        modalVisibleDocket: {
            type: Boolean,
            default: false
        },
        id: {
            require: true,
            type: String,
            default: ''
        },
        rootId: {
            require: true,
            type: String,
            default: ''
        }
    },
    data (){
        return {
            graphOptions: {
                // disableDragNode:true,//是否禁用图谱中节点的拖动
                defaultFocusRootNode: false, //默认为根节点添加一个被选中的样式
                // defaultNodeHeight:'100',
                debug: false,
                //'backgrounImage': 'http://ai-mark.cn/images/ai-mark-desc.png',
                'backgrounImageNoRepeat': true,
                'layouts': [
                    {
                        'label': 'tree',
                        'layoutName': 'tree',
                        'layoutClassName': 'seeks-layout-center',
                        'defaultNodeShape': 0,
                        'defaultLineShape': 1,
                        'from': 'left',
                        // 通过这4个属性来调整 tree-层级距离&节点距离
                        'min_per_width': '200',
                        'max_per_width': '200',
                        'min_per_height': '20',
                        'max_per_height': '60',
                        'levelDistance': '' // 如果此选项有值，则优先级高于上面那4个选项
                        // 'distance_coefficient':0.3
                    }
                ],
                'defaultLineMarker': {
                    'markerWidth': 12,
                    'markerHeight': 12,
                    'refX': 6,
                    'refY': 6,
                    'data': 'M2,2 L10,6 L2,10 L6,6 L2,2'
                },
                'defaultExpandHolderPosition': 'right',
                'defaultNodeShape': 1,
                'defaultNodeWidth': '130', //节点宽度
                'defaultLineShape': 4,
                'defaultJunctionPoint': 'lr',
                'defaultNodeBorderWidth': 0,
                'defaultLineColor': 'rgba(30 144 255)',
                'defaultNodeColor': 'rgba(30 144 255)'
            }
        }
    },
    mounted (){
        setTimeout(() => {
            this.showSeeksGraph()
        }, 500)
    },
    methods: {
        onNodeClick (nodeObject) {
            let { id } = nodeObject
            let {apiPath, url , businesType } = nodeObject.data
            // 点击订单的单据联查的送货通知单"businesType": "deliveryNotice"，
            // 会先调/delivery/purchaseDeliveryNotice/queryById接口 ==后端传过来的接口
            // 根据返回结果jitFlag若等于1，跳转的url改为订单送货通知列表：/delivery/purchaseDeliveryNoticeByOrder/list，否则跳转的url改为JIT送货通知单列表：/delivery/purchaseDeliveryNotice/list
            if(businesType == 'deliveryNotice') {
              getAction(apiPath, {id: id}).then(res => {
                    if (res.success) {
                        if(res.result.jitFlag == '1') {
                            this.$router.push({ path: '/srm/delivery/PurchaseDeliveryNoticeByOrderList', query: {
                                id: res.result.headId,
                                open: true,
                                templateAccount: res.result.templateAccount,
                                templateName: res.result.templateName,
                                templateNumber: res.result.templateNumber,
                                templateVersion: res.result.templateVersion
                            }
                            })
                        }
                        else {
                            this.$router.push({ path: url, query: {
                                id: res.result.headId,
                                open: true,
                                templateAccount: res.result.templateAccount,
                                templateName: res.result.templateName,
                                templateNumber: res.result.templateNumber,
                                templateVersion: res.result.templateVersion
                            }
                            })
                        }
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }
            else if(apiPath){
                getAction(apiPath, {id: id}).then(res => {
                    if (res.success) {
                        this.$router.push({ path: url, query: {
                            id: id,
                            open: true,
                            templateAccount: res.result.templateAccount,
                            templateName: res.result.templateName,
                            templateNumber: res.result.templateNumber,
                            templateVersion: res.result.templateVersion
                        }
                        })
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }else{
                this.$router.push({ path: url})
            }
            this.$emit('onNodeClick', nodeObject)
            // if(nodeObject.text){
            //     const textArry = nodeObject.text.split('\n')
            //     const modalType = textArry[0]
            //     const id = nodeObject.id
            //     let url = ''
            //     let apiPath = ''
            //     // switch(modalType){
            //     // case '采购申请':
            //     //     url = '/srm/demand/PurchaseRequestHeadList'
            //     //     apiPath = '/demand/purchaseRequestHead/queryById'
            //     //     break
            //     // case '询价单':
            //     //     url = '/srm/enquiry/purchase/PurchaseEnquiryList'
            //     //     apiPath = '/enquiry/purchaseEnquiryHead/queryById'
            //     //     break
            //     // case '竞价单':
            //     //     url = '/srm/ebidding/EbiddingBuyHeadList'
            //     //     apiPath = '/ebidding/purchaseEbiddingHead/queryById'
            //     //     break
            //     // case '招标单':
            //     //     url = '/srm/bidding/purchase/PurchaseBiddingHeadList'
            //     //     apiPath = '/bidding/purchaseBiddingHead/queryById'
            //     //     break
            //     // case '采购合同':
            //     //     url = '/srm/contract/purchase/PurchaseContractHeadList'
            //     //     apiPath = '/contract/purchaseContractHead/queryById'
            //     //     break
            //     // case '采购订单':
            //     //     url = '/srm/order/purchase/PurchaseOrderHeadList'
            //     //     apiPath = '/order/purchaseOrderHead/queryById'
            //     //     break
            //     // case '送货通知单':
            //     // // url = '/srm/delivery/PurchaseDeliveryNoticeList'
            //     // // apiPath = '/delivery/purchaseDeliveryNotice/queryById'
            //     //     url = '/srm/delivery/PurchaseDeliveryNoticeByOrderList'
            //     //     apiPath = ''
            //     //     break
            //     // case '送货通知单(基于订单)':
            //     //     url = '/srm/delivery/PurchaseDeliveryNoticeByOrderList'
            //     //     // apiPath = '/delivery/purchaseDeliveryNotice/queryById'
            //     //     apiPath = ''
            //     //     break
            //     // case '到货单':
            //     //     url = '/srm/delivery/purchase/PurchaseArrivalHeadList'
            //     //     apiPath = '/delivery/purchaseDeliveryHead/queryById'
            //     //     break
            //     // case '收货单':
            //     // // url = '/srm/delivery/purchase/PurchaseDeliveryHeadList'
            //     //     url = '/srm/delivery/purchase/PurchaseArrivalHeadList'//到货单
            //     //     apiPath = '/delivery/purchaseDeliveryHead/queryById'
            //     //     break
            //     // case '退货通知单':
            //     //     url = '/srm/delivery/purchase/PurchaseRefundsDeliveryHeadList'
            //     //     apiPath = '/delivery/purchaseRefundsDeliveryHead/queryById'
            //     //     break
            //     // case '凭证单':
            //     //     url = '/srm/delivery/voucher/VoucherHeadList'
            //     //     apiPath = '/delivery/purchaseVoucherHead/queryById'
            //     //     break
            //     // case '采购对账':
            //     //     url = '/srm/reconciliation/purchase/PurchaseReconciliationList'
            //     //     apiPath = '/reconciliation/purchaseReconciliation/queryById'
            //     //     break
            //     // case '采购附加费':
            //     //     url = '/srm/finance/purchase/PurchaseAddCostList'
            //     //     apiPath = '/finance/purchaseAddCost/queryById'
            //     //     break
            //     // case '采购扣款单':
            //     //     url = '/srm/finance/purchase/PurchaseDeductCostList'
            //     //     apiPath = '/finance/purchaseDeductCost/queryById'
            //     //     break
            //     // case '采购发票':
            //     //     url = '/srm/finance/purchase/PurchaseInvoiceList'
            //     //     // apiPath = '/finance/purchase/PurchaseInvoiceHead/queryById'
            //     //     apiPath = ''
            //     //     break
            //     // case '付款申请单':
            //     //     url = '/srm/finance/purchase/PurchasePaymentApplyList'
            //     //     apiPath = '/finance/purchasePaymentApplyHead/queryById'
            //     //     break
            //     // default:
            //     //     url = ''
            //     //     break
            //     // }
            //     if(apiPath){
            //         getAction(apiPath, {id: id}).then(res => {
            //             if (res.success) {
            //                 this.$router.push({ path: url, query: {
            //                     id: id,
            //                     templateAccount: res.result.templateAccount,
            //                     templateName: res.result.templateName,
            //                     templateNumber: res.result.templateNumber,
            //                     templateVersion: res.result.templateVersion,
            //                     modalType: modalType,
            //                     fbk15: res.result.fbk15,
            //                     createAccount: res.result.createAccount}})
            //             }else {
            //                 this.$message.warning(res.message)
            //             }
            //         })
            //     }else{
            //         this.$router.push({ path: url})
            //     }
            // }
        // this.$router.push({ path: '/srm/contract/purchase/PurchaseContractHeadList' ,query:{}})
        },
        closeModalDocket (){
            this.$emit('closeModalDocket')
        },
        // 单据联查
        viewDocket (){
            this.showSeeksGraph()
        },
        showSeeksGraph () {
            let __graph_json_data = {}          
            // 使用要点：通过节点属性expandHolderPosition: 'right' 和 expanded: false 可以让节点在没有子节点的情况下展示一个"展开"按钮
            //         通过onNodeExpand事件监听节点，在被展开的时候有选择的去从后台获取数据，如果已经从后台加载过数据，则让当前图谱根据当前的节点重新布局
            // let __graph_json_data = {
            //   'rootId': '1498127695500435458',
            //   'nodes': [
            //     {'id': '1501444113966948353', 'text': '采购订单\n1501444113966948353'},
            //     {'id': '1498127695500435458', 'text': '竞价单\nCN202203040012'},
            //     // {'id': '1499655334061031425', 'text': '采购合同\nCN202203040012'},
            //     {'id': '1499655334061031425', 'text': '采购合同\nCN202203040012'},
            //     {'id': '1501464170380005378','text': '询价单\nCN202203040012'},
            //     {'id': '1501425060072300546', 'text': '招标单\nCN202203040012'},
            //     {'id': '1499314114533146625', 'text': '采购对账\nCN202203040012'},
            //     {'id': '1499218503880294402','text': '采购附加费\nCN202203040012'},
            //     {'id': '1498490428452388866', 'text': '采购扣款单\nCN202203040012'},
            //     {'id': '1498534432099418114', 'text': '付款申请单\nCN202203040012'},
            //     {'id': '1501107599959650305', 'text': '到货单\nCN202203040012'},
            //     {'id': '1499647267890151425','text': '收货单\nCN202203040012'},
            //     {'id': '1501804875371704321', 'text': '退货通知单\nCN202203040012'},
            //     {'id': '1501756869888897025', 'text': '凭证单\nCN202203040012'},
            //     {'id': '1501757667708432386', 'text': '采购发票\nCN202203040012'},
            //     {'id': 'b5', 'text': 'b5'},
            //     {'id': 'b6', 'text': 'b6'}],
            //   'links': [
            //     {'from': '1501444113966948353', 'to': '1498127695500435458'},
            //     {'from': '1498127695500435458', 'to': '1499655334061031425'},
            //     {'from': '1498127695500435458', 'to': '1501107599959650305'},
            //     {'from': '1499655334061031425', 'to': '1501464170380005378'},
            //     {'from': '1499655334061031425','to': '1501425060072300546'},
            //     {'from': '1499655334061031425', 'to': '1499314114533146625'},
            //     {'from': '1499655334061031425', 'to': '1499218503880294402'},
            //     {'from': '1499655334061031425', 'to': '1498490428452388866'},
            //     {'from': '1499655334061031425','to': '1498534432099418114'},
            //     {'from': '1501107599959650305', 'to': '1499647267890151425'},
            //     {'from': '1501107599959650305', 'to': '1501804875371704321'},
            //     {'from': '1501804875371704321', 'to': '1501756869888897025'},
            //     {'from': '1501756869888897025', 'to': '1501757667708432386'},
            //     {'from': '1501757667708432386', 'to': 'b5'},
            //     {'from': 'b5', 'to': 'b6'}]
            // }
            // let __graph_json_data = {
            //     'rootId': '7c25c41c-b1e4-4470-8578-e50064f218ea',
            //     'nodes': [
            //         {
            //             'id': '1501802779714879490',
            //             'text': '1501802779714879490\n采购申请'
            //         },
            //         {
            //             'id': '1501803054378876929',
            //             'text': '1501803054378876929\n采购订单'
            //         }, {
            //             'id': '1501805635721293825',
            //             'text': '1501805635721293825\n送货通知单'
            //         }, {
            //             'id': '1501819060493029377',
            //             'text': '1501819060493029377\n发货单'
            //         },

            //         {
            //             'id': '1501826918861258754',
            //             'text': '1501826918861258754\n收货单'
            //         }
            //     ],
            //     'links': [ {
            //         'from': '1501803054378876929',
            //         'to': '1501805635721293825'
            //     }, {
            //         'from': '1501805635721293825',
            //         'to': '1501819060493029377'
            //     }, {
            //         'from': '1501805635721293825',
            //         'to': '1501826918861258754'
            //     }, {
            //         'from': '1501802779714879490',
            //         'to': '1501803054378876929'
            //     } ]
            // }
            // this.id = 'f36f63fd-d1b6-4f52-ab67-83e30ab5386f'
            // this.rootId = '1502119749125603330'
            // this.$refs.seeksRelationGraph.setJsonData(__graph_json_data, (seeksRGGraph) => {
            //     // 这些写上当图谱初始化完成后需要执行的代码
            //     // this.$message.success('初始化完成！')
            //     this.$refs.seeksRelationGraph.refresh()
            //     this.$refs.seeksRelationGraph.resetViewSize()
            // })

            if(this.id && this.rootId){
                postAction('/integratedDocument/getDetail', {id: this.id, rootId: this.rootId}).then(res => {                  
                    if (res.success) {
                        if (res.result) {
                            res.result.nodes.forEach(item=> {
                                item.data= {
                                    url: item.url,
                                    apiPath: item.apiPath,
                                    businesType: item.businesType
                                }
                            })
                            // __graph_json_data = {"nodes":[{"id":"1502125901699809282","text":"采购申请\n1502125901699809282"},{"id":"1502126123922423810","text":"询价单\n1502126123922423810"},{"id":"1502126201395412993","text":"招标单\n1502126201395412993"}],"rootId":"1502126123922423810","links":[{"from":"1502125901699809282","to":"1502126123922423810"},{"from":"1502125901699809282","to":"1502126139093221378"},{"from":"1502125901699809282","to":"1502126201395412993"}]}
                            // __graph_json_data = {"nodes":[{"id":"1502626081992540161","text":"采购申请\nPR202203120059"},{"id":"1502628029047177217","text":"凭证单\nVN202203120027"},{"id":"1502628065864777729","text":"凭证单\nVN202203120028"},{"id":"1502627397196251137","text":"采购订单\nPO202203120029"},{"id":"1502628880440557570","text":"采购发票\nSI202203120002"},{"id":"1502628400515710978","text":"采购对账\nPR202203120009"},{"id":"1502628400515710978","text":"采购对账\nPR202203120009"},{"id":"1502629198872117250","text":"付款申请单\nPA202203120020","styleClass":"my-node-style"},{"id":"1502627700872249346","text":"送货通知单\nTZ202203120021"},{"id":"1502627814093291522","text":"收货单\nDN202203120030"},{"id":"1502627915939381249","text":"收货单\nDN202203120031"}],"rootId":"1502626081992540161","links":[{"from":"1502626081992540161","to":"1502627397196251137"},{"from":"1502627397196251137","to":"1502627700872249346"},{"from":"1502627700872249346","to":"1502627814093291522"},{"from":"1502627700872249346","to":"1502627915939381249"},{"from":"1502627915939381249","to":"1502628029047177217"},{"from":"1502627814093291522","to":"1502628065864777729"},{"from":"1502628029047177217","to":"1502628400515710978"},{"from":"1502628065864777729","to":"1502628400515710978"},{"from":"1502628400515710978","to":"1502628880440557570"},{"from":"1502629198872117250","to":"1502628880440557570"}]}
                            __graph_json_data = res.result
                            console.log('__graph_json_data', JSON.stringify(__graph_json_data))
                            this.$refs.seeksRelationGraph.setJsonData(__graph_json_data, (seeksRGGraph) => {
                                // 这些写上当图谱初始化完成后需要执行的代码
                                // this.$message.success('初始化完成！')
                                this.$refs.seeksRelationGraph.refresh()
                                this.$refs.seeksRelationGraph.resetViewSize()
                            })
                        }
                    }else{
                        this.$message.error(res.message)
                    }
                })
            }
          
        },
        onNodeExpand (node, e) {
        // 当有一些节点被显示或隐藏起来时，会让图谱看着很难看，需要布局器重新为节点分配位置，所以这里需要调用refresh方法来重新布局
            console.log('onNodeExpand:', node)
            // node.Fx = -2000
            this.$refs.seeksRelationGraph.refresh()
            this.$refs.seeksRelationGraph.resetViewSize()
        // debugger    resetViewSize
        }
    }

}
</script>

<style lang="scss">
  .my-node-style{
    background-color: rgb(30, 144, 255); 
    color: rgb(255, 255, 255); 
    border: 0px solid rgb(144, 238, 144); 
    width: 130px;
    box-shadow: rgb(247, 145, 105) 0px 0px 30px 5px;
    // background-color: 'rgba(30 144 255)' !important;/** 通过自定义样式设置节点颜色需要加 !important **/
  }
  .my-node-style:hover{
    background-color: #ff0000 !important;
  }
  @keyframes myFlash {
    from {
      opacity: 1.0;
    }
    50% {
      opacity: 0.4;
    }
    to {
      opacity: 1.0;
    }
  }
  @-webkit-keyframes myFlash {
    from {
      opacity: 1.0;
    }
    50% {
      opacity: 0.4;
    }
    to {
      opacity: 1.0;
    }
  }
  .my-node-style{
    animation: myFlash 1000ms infinite;
    -webkit-animation: myFlash 1000ms infinite;
  }
</style>
