<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
    <a-modal
    v-drag    
      v-model="previewModal"
      title="预览"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction } from '@/api/manage'
import {REPORT_ADDRESS} from '@/utils/const'
export default {
    name: 'EsignFlowDetail',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            previewModal: false,
            previewContent: '',
            visible: false,
            templateNumber: undefined,
            templateOpts: [],
            printRow: {},
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'busType',
                                    dictCode: 'electronicSignatureBusType',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName'
                                },
                                {

                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderNo`, '业务单号'),
                                    fieldName: 'busNumber',
                                    extend: {
                                        linkConfig: {
                                            primaryKey: 'busNumber', //主键名
                                            actionPath: '/srm/order/purchase/PurchaseOrderHeadList', //目标路由
                                            bindKey: 'busNumber', //绑定的主键key
                                            otherQuery: { linkFilter: true } //其余参数
                                        },
                                        exLink: false, // 是否是外链
                                        // 表行handle
                                        handleBefore: function (row, column, linkConfig, that) {
                                        },
                                        // 表头handle
                                        handleBefore: function (form, linkConfig, that) {
                                            // linkConfig.actionPath = 'srm/order/purchase/PurchaseOrderHeadList'
                                            if (form.busType === 'order') {
                                                linkConfig.actionPath = '/srm/order/purchase/PurchaseOrderHeadList'
                                                linkConfig.primaryKey = 'orderNumber'
                                            } else if (form.busType === 'contract') {
                                                if (form.contractType && form.contractType==='3') {
                                                    linkConfig.actionPath = '/srm/contract/purchase/PurchaseContractHeadListSimple'
                                                    linkConfig.primaryKey = 'contractNumber'
                                                } else {
                                                    linkConfig.actionPath = '/srm/contract/purchase/PurchaseContractHeadList'
                                                    linkConfig.primaryKey = 'contractNumber'
                                                }
                                            } else if (form.busType === 'reconciliation') {
                                                linkConfig.actionPath = '/srm/reconciliation/purchase/PurchaseReconciliationList'
                                                linkConfig.primaryKey = 'reconciliationNumber'
                                            }
                                        }
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_WhoWillStampFirst`, '哪方先盖章'),
                                    fieldName: 'firstSeal',
                                    dictCode: 'srmSignatoryType',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessAssociationID`, '业务关联id'),
                                    fieldName: 'relationId'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                                    fieldName: 'filesName'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'),
                                    fieldName: 'filesId'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkisSuccessfullyUploaded`, '文件是否上传成功'),
                                    fieldName: 'uploaded',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    dictCode: 'srmSendStatus'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSignatoryMaintainsTheState`, '签署人维护状态'),
                                    fieldName: 'signerVindicateStatus',
                                    dictCode: 'srmSignerVindicateStatus'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_zyH3twLIV67xJOvs`, '供方是否线上盖章'),
                                    fieldName: 'onlineSealed',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BqQfRcWCQGdlnH74`, '供方是否上传盖章文件'),
                                    fieldName: 'signFileUploaded',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_autoArchiving`, '是否自动归档'),
                                    fieldName: 'autoArchiving',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheProcessIsAutomaticallyStarted`, '流程是否自动开启'),
                                    fieldName: 'autoInitiate',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationDateSigning`, '签署有效截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'number',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractRemind`, '文件到期前多少时间提醒(小时)'),
                                    fieldName: 'contractRemind'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationdateDocument`, '文件有效截止时间'),
                                    fieldName: 'effectiveTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注')
                                }
                            ]
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCPWL_36613b74`, '采方签署人'), groupCode: 'purchaseSignersList', type: 'grid', custom: {
                        ref: 'purchaseSignersList',
                        columns: [
                            { field: 'autoArchive_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`, '是否自动落章'), width: 120 },
                            { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                            { field: 'signWord', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'), width: 120 },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'enterpriseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 180 },
                            { field: 'loadingCompany_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), width: 120, visible: false},
                            { field: 'companyCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'), width: 120, visible: false},
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureCompany`, '签署公司'), width: 200 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureAccount`, '签署用户E签宝账号'), width: 120 },
                            { field: 'idType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170 },
                            { field: 'idNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'), width: 170 },
                            { field: 'orgLegalIdNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateNumber`, '法人证件号'), width: 170 },
                            { field: 'orgLegalName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_corporateName`, '法人名称'), width: 120 },
                            { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_companySignatureAccount`, '公司E签宝账号'), width: 120 },
                            { field: 'filesId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'), width: 120 },
                            { field: 'filesName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'), width: 120 },
                            { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationStatus`, '认证状态'), width: 120},
                            // { field: 'autoSign_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isAutomaticSignature`, '是否自动签署'), width: 120 },
                            { field: 'signType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), width: 120 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RCPWL_91e5ef48`, '供方签署人'), groupCode: 'saleSignersList', type: 'grid', custom: {
                        ref: 'saleSignersList',
                        columns: [
                            { field: 'autoArchive_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`, '是否自动落章'), width: 120 },
                            { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                            { field: 'signWord', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'), width: 120 },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'loadingCompany_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), width: 120, visible: false},
                            { field: 'companyCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'), width: 120, visible: false },
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureCompany`, '签署公司'), width: 200 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'idType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170 },
                            { field: 'idNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'), width: 170 },
                            { field: 'orgLegalIdNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateNumber`, '法人证件号'), width: 170 },
                            { field: 'orgLegalName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_corporateName`, '法人名称'), width: 120 },
                            { field: 'filesName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'), width: 120 },
                            { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationStatus`, '认证状态'), width: 120},
                            // { field: 'autoSign_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isAutomaticSignature`, '是否自动签署'), width: 120},
                            { field: 'signType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), width: 120 }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_ESQIUB_224d09a`, '业务文件预览'), type: 'primary', click: this.preview },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_PWQIUB_ee68be07`, '签署文件预览'), type: 'primary', click: this.esignFileDown, showCondition: this.showEsignFileDown },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/esign/elsEsign/queryById',
                viewEsignFile: '/esign/elsEsign/viewEsignFile'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        },
        preview () {
            let params= this.$refs.detailPage.getPageData()
            if (!params.relationId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WiFne_bc5dc24c`, '先选择合同'))
                return
            }
            if(params.busType == 'order'){
                this.orderReview(params.relationId)
            } else if(params.busType == 'reconciliationConfirmation') {
                this.confirmationReview(params.relationId)
            } else {
                getAction('/contract/purchaseContractHead/getPreviewData', {id: params.relationId}).then((res) => {
                    if (res.success) {
                        this.previewModal = true
                        this.previewContent = res.result
                    }
                })
            }
        },
        queryPrintTemList (elsAccount, businessType) {
            let params = {elsAccount: elsAccount, businessType: businessType}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        orderReview (id){
            this.queryPrintTemList(this.$ls.get('Login_elsAccount'), "order").then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        getAction('/order/purchaseOrderHead/queryById', {id: id}).then(resOrder => {
                            this.printRow = resOrder.result
                            let options = res.result.map(item => {
                                return {
                                    value: item.id,
                                    printId: item.printId,
                                    printName: item.printName,
                                    title: item.templateName,
                                    printType: item.printType,
                                    param: item.param
                                }
                            })
                            this.templateNumber = ''
                            this.templateOpts = options
                            // 只有单个模板直接新建
                            if (this.templateOpts && this.templateOpts.length===1) {
                                this.templateNumber = this.templateOpts[0].value
                                this.selectedPrintTemplate()
                            } else {
                            // 有多个模板先选择在新建
                                this.printVisible = true
                            }
                        })
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        confirmationReview (id){
            this.queryPrintTemList(this.$ls.get('Login_elsAccount'), "purchaseReconciliationConfirmation").then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        getAction('/reconciliation/purchaseReconciliationConfirmation/queryById', {id: id}).then(resOrder => {
                            this.printRow = resOrder.result
                            let options = res.result.map(item => {
                                return {
                                    value: item.id,
                                    printId: item.printId,
                                    printName: item.printName,
                                    title: item.templateName,
                                    printType: item.printType,
                                    param: item.param
                                }
                            })
                            this.templateNumber = ''
                            this.templateOpts = options
                            // 只有单个模板直接新建
                            if (this.templateOpts && this.templateOpts.length===1) {
                                this.templateNumber = this.templateOpts[0].value
                                this.selectedPrintTemplate()
                            } else {
                                // 有多个模板先选择在新建
                                this.printVisible = true
                            }
                        })
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        selectedPrintTemplate () {
            if(this.templateNumber) {
                const that = this
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    printId: template[0].printId,
                    printName: template[0].printName,
                    printType: template[0].printType,
                    param: template[0].param
                }
                that.demandVisible = false
                that.submitLoading = false
                let rowItem = this.printRow
                this.printRow = {}
                let urlParam = ''
                if (params.param) {
                    let json = JSON.parse(params.param)
                    console.log('json:', json)
                    Object.keys(json).forEach((key, i) => {
                        urlParam += '&'+key+'='+rowItem[json[key]]
                    })
                }
                if (params.printType=='ureport') {
                    const token = this.$ls.get('Access-Token')
                    //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
                    const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.printName+'&token=' + token+urlParam
                    window.open(url, '_blank')
                }
                if (params.printType=='jimu') {
                    const token = this.$ls.get('Access-Token')
                    const url = REPORT_ADDRESS + '/els/report/jmreport/view/'+params.printId+'?token=' + token+urlParam
                    //const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.title+'&token=' + token+'&id='+rowItem.id
                    window.open(url, '_blank')
                }
            }
        },
        showEsignFileDown (){
            if(this.currentEditRow.uploaded==='1'){
                return true
            }else{
                return false
            }
        },
        esignFileDown (){
            const params = this.$refs.detailPage.getPageData()
            getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                if(res.success){
                    window.open(res.result.downloadUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>
