<template>
  <div class="steps-content-warp step2">

    <div class="formWrap">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        v-bind="formItemLayout">
        <a-row>
          <a-col :span="12">
            <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_announcementLetter`, '是否发布中标通知书')">
              <m-switch v-model="form.bidWinNotification" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item
          :wrapper-col="{ span: 24 }"
          prop="bidWinNotificationContent">
          <div class="editor">
            <j-editor
              ref="editor"
              v-model="form.bidWinNotificationContent" />
          </div>
        </a-form-model-item>
        <a-row>
          <a-col :span="12">
            <a-form-model-item
              :label-col="{ span: 0 }">
              <custom-upload
                :disabledItemNumber="true"
                :visible.sync="upload.modalVisible"
                :title="upload.title"
                :action="upload.action"
                :accept="upload.accept"
                :headers="upload.tokenHeader"
                :data="extraData"
                @change="handleUploadChange"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>

      <div class="attachment">
        <ul>
          <template v-for="file in form.attachment__$$__2">
            <li
              :key="file.id"
              class="file">
              <a
                href="javascript:void(0)"
                @click="handleDownload(file)"
              >
                {{ file.fileName }}
              </a>
              <a-icon
                @click="handleDel(file)"
                type="delete"
                class="icon del"
              />
            </li>
          </template>
        </ul>
      </div>
    </div>
    
  </div>
</template>

<script>
import JEditor from '@/components/els/JEditor'
import CustomUpload from '@comp/template/CustomUpload'
import { getAction } from '@/api/manage'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        JEditor,
        CustomUpload
    },
    props: {
        parentData: {
            type: Object,
            default () {
                return {}
            }
        },
        templateContent: {
            type: String,
            default: ''
        },
        current: {
            type: Number,
            default: 0
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh'
    ],
    data () {
        return {
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 14 }
            },
            form: {
                bidWinNotification: '0', // 是否发布中标通知书，0：否、1：是
                bidWinNotificationContent: '' // 中标通知书内容
            },
            formItemLayout: {
                labelCol: { span: 10 },
                wrapperCol: { span: 14 }
            },
            rules: {
                bidWinNotificationContent: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterAnnouncementLetter`, '请输入中标通知书内容'), trigger: 'change' }
                ]
            },
            upload: {
                modalVisible: false,
                action: '/attachment/purchaseAttachment/upload',
                tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
                accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf'
            }
        }
    },
    computed: {
        ...mapState({
            vuex_cacheRow: state => state.app.vuex_cacheRow,
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        extraData () {
            return {
                businessType: 'winBidNotification',
                headId: this.vuex_currentEditRow.id || '',
                // businessType: 'bidding',
                // headId: this.vuex_cacheRow.id || '', // 之前传的是包id，现在需要项目列的id
                sourceNumber: this.vuex_currentEditRow.biddingNumber,
                actionRoutePath: '/srm/bidding/purchase/PurchaseBiddingProjectHeadList,/srm/bidding/sale/SaleBiddingHeadList'
            }
        }
    },
    watch: {
        parentData: {
            deep: true,
            immediate: true,
            handler (obj) {
                if (!obj || Object.keys(obj).length === 0) return
                const { bidWinNotification, bidWinNotificationContent, attachment__$$__2 } = JSON.parse(JSON.stringify(obj)) || {}
                this.form = Object.assign({}, this.form, {
                    bidWinNotification: bidWinNotification || '0',
                    bidWinNotificationContent: bidWinNotificationContent || '',
                    attachment__$$__2: attachment__$$__2 || []
                })
            }
        },
        templateContent (str) {
            if (!str) return
            const props = [ 'bidWinContent', 'bidWinNotificationContent', 'bidFailNotificationContent' ]
            const property = props[this.current]
            if (!this.form.hasOwnProperty(property)) return
            this.form[property] = str
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        updateParent () {
            let parentData = JSON.parse(JSON.stringify(this.parentData))
            parentData = Object.assign({}, parentData, this.form)
            this.$emit('update:parentData', parentData)
        },
        handleUploadChange (info) {
            this.updateParent()
            this.$emit('updateFile', {
                _property: 'attachment__$$__2',
                info
            })
        },
        handleDel (file) {
            this.$emit('deleteFile', {
                _property: 'attachment__$$__2',
                ...file
            })
        },
        // 文件下载
        handleDownload ({ id, fileName }) {
            const params = {
                id
            }
            let downloadUrl = '/attachment/purchaseAttachment/download'
            getAction(downloadUrl, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }
    },
    // 使用 beforeRouteUpdate 导航守卫监听路由变化
    // 对路由销毁重建
    beforeRouteUpdate (to, from, next) {
        this.routerRefresh() //路由销毁重建方法
        next()
    }
}
</script>
