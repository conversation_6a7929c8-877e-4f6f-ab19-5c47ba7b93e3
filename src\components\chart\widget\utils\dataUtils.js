/*
 * @Author: fzb
 * @Date: 2022-03-01 14:49:11
 * @LastEditTime: 2022-03-18 15:11:00
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\components\designer\widget\utils\dataUtils.js
 */
import axios from 'axios'
// import WebSocketClient from '@comp/designer/ws/WebSocket'
import {
    STATIC_SERVICE_TYPE,
    HTTP_STATIC_SERVICE_TYPE,
    API_SERVICE_TYPE,
    SQL_SERVICE_TYPE,
    WEBSOCKET_SERVICE_TYPE,
    CHARTS_BAR,
    CHARTS_PIE,
    CHARTS_RADAR,
    CHARTS_GAUGE,
    CHARTS_LINE_CHART,
    CHARTS_SPLASHES,
    CHARTS_K_LINE,
    BUSINESS_BY_TABLE,
    BUSINESS_LEAGUE_TABLE,
    BUSINESS_DIGITAL_FLOP
} from '@comp/chart/widget/const'
// import {
//     reportControlCabinLoadTableData
// } from '@api'
// 核心数据格式转换
const structureData = {
    // chartsBar数据
    [CHARTS_BAR]: (records) => {
        let data = { categories: [], series: [] },
            arrTemp = [],
            arrTempCategories = [],
            arrTempName = [],
            serie = []
        if (records && records.length) {
            records.forEach((record) => {
                arrTempCategories.push(record.name)
                if (record.type) {arrTemp.push(record.type)}
                arrTempName = [...new Set(arrTemp)]
            })
            if (arrTempName.length) { // 没有type的时候
                let obj = {}
                arrTempName.map((item) => {
                    obj[item] = []
                })
                records.forEach((record) => {
                    for (var key in obj) {
                        if (key === record.type) {
                            data.categories = [...new Set(arrTempCategories)]
                            obj[key].push(record.value)
                        }
                    }
                })
                for (var key in obj) {
                    serie.push({ name: key, data: obj[key] })
                }
            } else {
                serie.push({ data: records.map(rs => rs.value) })
            }
        
            data.series = serie
        }   
        return data
    },
    // chartsPie数据
    [CHARTS_PIE]: (records) => {
        let data = []
        if (records && records.length) {
            records.forEach((record) => {
                let serie = { name: '', value: '' }
                serie.name = record.type ? record.type + '_' + record.name : record.name
                serie.value = record.value
                data.push(serie)
            })
        }
        return data
    },
    // charsRadar数据
    [CHARTS_RADAR]: (records) => {
        let data = { indicator: [], series: [] }
        if (records && records.length) {
            records.forEach((record) => {
                let obj = {
                    name: record.name,
                    max: record.value
                }
                data.indicator.push(obj)
                let serie = { name: '', value: [] }
                serie.name = record.name
                serie.data.push(record.value)
                data.series.push(serie)
            })
        }
        return data
    },
    // 仪表盘
    [CHARTS_GAUGE]: (records) => {
        let data = {
            min: 0,
            max: 100,
            name: 'name',
            value: 0,
            unit: '%'
        }
        if (records && records.length) {
            let val
            if (typeof (records[0].value) == 'string') {
                val = parseFloat(((records[0].value).split('%'))[0])
            }
            data = {
                min: records[0].min || 0,
                max: records[0].max || 100,
                name: records[0].name,
                value: val || records[0].value,
                unit: records[0].unit || '%'
            }
        }
        return data
    },
    // 折线图数据
    [CHARTS_LINE_CHART]: (records) => {   
        // let data = { xAxisData: [], seriesData: [] }
        // if (records && records.length) {
        //     records.forEach((record) => {       
        //         let childSeriesData = records.map(rs => rs.value)
        //         data.xAxisData.push(record.name)
        //         data.seriesData.push({
        //             name: record.type,
        //             data: childSeriesData
        //         })
        //     })
        //     data.xAxisData = [...new Set(data.xAxisData)]
        // }   

        // let data = { categories: [], series: [] },
        let data ={ xAxisData: [], seriesData: [] },
            arrTemp = [],
            arrTempCategories = [],
            arrTempName = [],
            serie = []
        if (records && records.length) {
            records.forEach((record) => {
                arrTempCategories.push(record.name)
                if (record.type) {arrTemp.push(record.type)}
                arrTempName = [...new Set(arrTemp)]
            })
            if (arrTempName.length) { // 没有type的时候
                let obj = {}
                arrTempName.map((item) => {
                    obj[item] = []
                })
                records.forEach((record) => {
                    for (var key in obj) {
                        if (key === record.type) {
                            data.xAxisData = [...new Set(arrTempCategories)]
                            obj[key].push(record.value)
                        }
                    }
                })
                for (var key in obj) {
                    serie.push({ name: key, data: obj[key] })
                }
            } else {
                serie.push({ data: records.map(rs => rs.value) })
            }
        
            data.seriesData = serie
        }   
        return data
    },
    // 散点图数据
    [CHARTS_SPLASHES]: (records) => {
        return records
    },
    // K线图数据
    [CHARTS_K_LINE]: (records) => {
        return records
    },
    // 轮播表数据
    [BUSINESS_BY_TABLE]: (records) => {
        let header = []
        let data = []
        if (records && records.length) {
            // 组装头部  
            for (let key in records[0]) {
                header.push(key)
            }
            records.forEach((record) => {
                let arr = []
                for (let item in record) {
                    arr.push(record[item])
                }
                data.push(arr)
            })
        }
        return {
            header: header,
            data: data
        }
    },
    // 排名轮播表数据
    [BUSINESS_LEAGUE_TABLE]: (records) => {
        return records
    },
    // 数字文本数据
    [BUSINESS_DIGITAL_FLOP]: (records) => {
        return records
    }
}
// api
function renderByOpenApi (service, renderFunc) {
    axios({ method: service.api.method, url: service.api.url }).then(
        (response) => {
            renderFunc(response.data)
        }
    )
}

function renderByOpenSql (widgetEntity, service, renderFunc) {
    let sqlRequest = {
        sql: service.sql.sql,
        dbSource: service.sql.dbSource,
        tableName: service.sql.tableName,
        pageNo: service.sql.pageNo,
        pageSize: service.sql.pageSize
    }
    // reportControlCabinLoadTableData(sqlRequest).then((response) => {
    //     if (response && response.success) {
    //         if (widgetEntity === CHARTS_BAR) {
    //             renderFunc(structureData[CHARTS_BAR](response.result.records))
    //         }
    //         if (widgetEntity === CHARTS_PIE) {
    //             renderFunc(structureData[CHARTS_PIE](response.result.records))
    //         }
    //         if (widgetEntity === CHARTS_RADAR) {
    //             renderFunc(structureData[CHARTS_RADAR](response.result.records))
    //         }
    //         if (widgetEntity === CHARTS_GAUGE) {
    //             renderFunc(structureData[CHARTS_GAUGE](response.result.records))
    //         }
    //         if (widgetEntity === CHARTS_LINE_CHART) {
    //             renderFunc(structureData[CHARTS_LINE_CHART](response.result.records))
    //         }
    //         if (widgetEntity === CHARTS_SPLASHES) {
    //             renderFunc(structureData[CHARTS_SPLASHES](response.result.records))
    //         }
    //         if (widgetEntity === CHARTS_K_LINE) {
    //             renderFunc(structureData[CHARTS_K_LINE](response.result.records))
    //         }
    //         if (widgetEntity === BUSINESS_BY_TABLE) {
    //             renderFunc(structureData[BUSINESS_BY_TABLE](response.result.records))
    //         }
    //         if (widgetEntity === BUSINESS_LEAGUE_TABLE) {
    //             renderFunc(structureData[BUSINESS_LEAGUE_TABLE](response.result.records))
    //         }
    //         if (widgetEntity === BUSINESS_DIGITAL_FLOP) {
    //             renderFunc(structureData[BUSINESS_DIGITAL_FLOP](response.result.records))
    //         }
    //     } else {
    //         console.error(response.message || '接口异常')
    //     }
    // }
    // )
}

function renderByHttpResult (widgetEntity, resultData, renderFunc) {
    debugger
    if (widgetEntity === CHARTS_BAR) {
        renderFunc(structureData[CHARTS_BAR](resultData))
    }
    if (widgetEntity === CHARTS_PIE) {
        renderFunc(structureData[CHARTS_PIE](resultData))
    }
    if (widgetEntity === CHARTS_RADAR) {
        renderFunc(structureData[CHARTS_RADAR](resultData))
    }
    if (widgetEntity === CHARTS_GAUGE) {
        renderFunc(structureData[CHARTS_GAUGE](resultData))
    }
    if (widgetEntity === CHARTS_LINE_CHART) {
        renderFunc(structureData[CHARTS_LINE_CHART](resultData))
    }
    if (widgetEntity === CHARTS_SPLASHES) {
        renderFunc(structureData[CHARTS_SPLASHES](resultData))
    }
    if (widgetEntity === CHARTS_K_LINE) {
        renderFunc(structureData[CHARTS_K_LINE](resultData))
    }
    if (widgetEntity === BUSINESS_BY_TABLE) {
        renderFunc(structureData[BUSINESS_BY_TABLE](resultData))
    }
    if (widgetEntity === BUSINESS_LEAGUE_TABLE) {
        renderFunc(structureData[BUSINESS_LEAGUE_TABLE](resultData))
    }
    if (widgetEntity === BUSINESS_DIGITAL_FLOP) {
        renderFunc(structureData[BUSINESS_DIGITAL_FLOP](resultData))
    }
}

export function renderWidgetData (widget, renderFunc) {
    let render = {}
    const service = widget.service
    if (service.type === STATIC_SERVICE_TYPE) {
        renderFunc(widget.data)
    } else if (service.type === API_SERVICE_TYPE) {
        renderByOpenApi(widget.entity, service, renderFunc)
    // let apiTimer = setInterval(() => {
    //   renderByOpenApi(widget.entity, service, renderFunc)
    // }, service.api.refresh)
    // render.apiTimer = apiTimer
    } else if (service.type === SQL_SERVICE_TYPE) {
        renderByOpenSql(widget.entity, service, renderFunc)
    // let sqlTimer = setInterval(() => {
    //   renderByOpenSql(widget.entity, service, renderFunc)
    // }, service.sql.refresh)
    // render.apiTimer = sqlTimer
    } else if (service.type === WEBSOCKET_SERVICE_TYPE) {
        // let websocket = new WebSocketClient(widget.id, widget.service.websocket.url, (resp) => {
        //     renderFunc(JSON.parse(resp.data))
        // })
        // websocket.init()
        // render.websocket = websocket
    } else if (service.type === HTTP_STATIC_SERVICE_TYPE) { // 在外传入接口返回来的数据
        renderByHttpResult(widget.entity, widget.data, renderFunc)
    }
    return render
}