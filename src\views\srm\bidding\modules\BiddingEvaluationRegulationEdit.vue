<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :currentEditRow="currentEditRow"
      :url="url" />
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'ElsEnterpriseInfoEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regulationOptions`, '条例选项'), groupCode: 'biddingEvaluationRegulationSelectionList', type: 'grid', custom: {
                        ref: 'biddingEvaluationRegulationSelectionList',
                        columns: [
                            {
                                type: 'checkbox', width: 40
                            },
                            {
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsNumExample`, '选项编号（如：A、B、C等）'),
                                field: 'selectName',
                                width: 250,
                                editRender: {name: 'AInput'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsValue`, '选项值'),
                                field: 'selectValue',
                                width: 150,
                                editRender: {name: 'AInput'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionsScore`, '选项分值'),
                                field: 'score',
                                width: 150,
                                editRender: {name: '$input', props: {type: 'number'}}
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addStandardItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteItemEvent}
                        ],
                        rules: {
                            selectName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionNameCannotBeEmpty`, '选项名称不能为空')}],
                            selectValue: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionDescCannotBeEmpty`, '选项描述不能为空')}],
                            score: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_optionScoreCannotBeEmpty`, '选项分值不能为空')}, {
                                pattern: /^$|^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
                                message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterGreaterZero`, '请输入大于等于0的整数')
                            }]
                        }
                    }},
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'attachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            // { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120, dictCode: 'fileType', editRender: {name: '$select', options: []} },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload',
                                businessType: 'biddingRegulation',
                                attr: this.attrHandle,
                                callBack: this.uploadCallBack},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/bidding/biddingEvaluationRegulation/add',
                edit: '/bidding/biddingEvaluationRegulation/edit',
                detail: '/bidding/biddingEvaluationRegulation/queryById',
                upload: '/attachment/purchaseAttachment/upload'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount
            if(!elsAccount || elsAccount==''){
                elsAccount = this.$ls.get('Login_elsAccount')
            }
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_biddingRegulation_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.regulationNumber,
                actionRoutePath: '/srm/bidding/BiddingEvaluationRegulation'
            }
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        addStandardItem () {
            const inputType = this.$refs.editPage.getPageData().inputType
            if(!inputType){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WiFDIAc_94ed51c3`, '先选择【题目类型】'))
                return
            }
            if(inputType==='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noMaintenanceRegulationTips`, '【手输型条例】不需要维护条例选项'))
                return
            }
            let itemGrid = this.$refs.editPage.$refs.biddingEvaluationRegulationSelectionList[0]
            let itemData = {}
            itemGrid.insert([itemData])
        },
        deleteItemEvent () {
            let itemGrid = this.$refs.editPage.$refs.biddingEvaluationRegulationSelectionList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.attachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.attachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        deleteBatch () {
            const fileGrid = this.$refs.editPage.$refs.attachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    if(params.inputType!=='1'){
                        const items = params.biddingEvaluationRegulationSelectionList
                        if(!items || items.length<1){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needToAddRegulations`, '（单选/多选）条例需添加条例选项'))
                            return
                        }
                    }else{
                        if(!params.inputValue){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_manualInputAnswer`, '（手输）条例需输入【手输型参考答案】'))
                            return
                        }
                        if(!params.fullMark){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterFullScore`, '（手输）条例需输入【满分】'))
                            return
                        }
                    }
                    let url = this.currentEditRow.id? this.url.edit: this.url.add
                    this.$refs.editPage.confirmLoading = true
                    postAction(url, params).then(res => {
                        if(res.success) {
                            this.currentEditRow = res.result
                            this.$set(this.$refs.editPage, 'form', res.result)
                            this.$message.success(res.message)
                            // this.goBack()
                        }else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.$refs.editPage.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        publishEvent () {
            this.$refs.editPage.handleSend()
        }

    }
}
</script>