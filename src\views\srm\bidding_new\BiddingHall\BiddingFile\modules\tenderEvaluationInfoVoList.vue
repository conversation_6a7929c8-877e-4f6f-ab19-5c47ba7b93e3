<template>
  <div class="tenderEvaluationInfoVoList">
    <a-spin :spinning="confirmLoading">
      <!-- <div v-if="isEdit" class="topBtn"> -->
      <div v-if="isEdit">
        <a-button
          @click="handleSelectMode"
          type="primary"
          style="margin-right: 10px">{{ $srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '选择模板') }}</a-button>
        <a-button
          @click="handleAddNode"
          type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_VaSy_2f94aa21`, '新增环节 ') }}</a-button>
      </div>
      <template-node-box
        :class="[isEdit ? 'nodeBox' : '',]"
        :style="{height: templateNodeBoxHeight }"
        ref="nodeBox"
        :pageStatus="pageStatus"
        :isBiddingFileModule="true"
        :nodeListData="tenderEvaluationInfoVoList"
      />
    </a-spin>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>
<script>
import TemplateNodeBox from '../../../TenderEvaluationTemplate/modules/components/TemplateNodeBox'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
export default {
    name: 'TenderEvaluationInfoVoList',
    props: {
        fromSourceData: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: '',
            type: String
        }
    },
    components: {
        TemplateNodeBox,
        fieldSelectModal
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        templateNodeBoxHeight () {
            let contentHeight = '600'
            const clientHeight = document.documentElement.clientHeight
            contentHeight = clientHeight - 265
            return contentHeight + 'px'
        }
    },
    data () {
        return {
            labelCol: {
                span: 9
            },
            wrapperCol: {
                span: 15
            },
            confirmLoading: false,
            tenderEvaluationInfoVoList: []
        }
    },
    methods: {
        fieldSelectOk (data) {
            this.confirmLoading = true
            getAction('tender/tenderEvaluationTemplateHead/queryById', { id: data[0].id })
                .then(res => {
                    if (res.success) {
                        // 选择模板清空ID
                        let list = res.result ? res.result.tenderEvaluationTemplateItemVoList.map(item => {
                            item.id = ''
                            if(item.tenderEvaluationTemplatePriceRegulationInfo) item.tenderEvaluationTemplatePriceRegulationInfo.id = ''
                            item.tenderEvaluationTemplateRegulationInfoList && item.tenderEvaluationTemplateRegulationInfoList.map(row => {
                                row.id = ''
                            })
                            return item
                        }) : []
                        this.tenderEvaluationInfoVoList = list
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleSelectMode () {
            let url = '/tender/tenderEvaluationTemplateHead/list'
            let columns = [
                { field: 'evaluationNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBIrAy_6bd5cce2`, '评标模板编号') },
                { field: 'evaluationName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBIrRL_6bd0bcc4`, '评标模板名称') },
                { field: 'evaluationDescribe', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_UBIrMWW_e85abff`, '评标模板描述') }
            ]
            this.$refs.fieldSelectModal.open(url, {status: 1}, columns)
        },
        handleAddNode () {
            this.$refs.nodeBox.handleAddNode()
        },
        getValidatePromise () {
            return new Promise(async (resolve, reject) => {
                let flag = await this.$refs.nodeBox.getValidatePromise()
                if (!flag) {
                    reject({
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMMi_ddcaceed`, '请填写完整'),
                        validateStatus: 'error',
                        currentStep: 3
                    })
                }
                resolve(flag)
            })
        },
        async externalAllData () {
            let tenderEvaluationInfoVoList = await this.$refs.nodeBox.getAllData()
            return tenderEvaluationInfoVoList
        },
        init (data) {
            this.tenderEvaluationInfoVoList = data.tenderEvaluationInfoVoList
        }
    },
    mounted () {
        this.tenderEvaluationInfoVoList = this.fromSourceData.tenderEvaluationInfoVoList
    }
}
</script>
<style lang="less" scoped>
.tenderEvaluationInfoVoList {
    min-height: 400px;
    .topBtn{
        position: fixed;
        z-index: 100;
    }
    .nodeBox{
        margin-top: 5px;
        overflow: auto
    }
}
</style>
