<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      table-code="saleDeliveryItem"
      v-show="!showEditPage && !showDetailPage && !showRequestToOrderPage"
      :pageData="pageData"
      :url="url"
      :current-edit-row="currentEditRow"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      @handleView="handleView"/>
    <a-modal
    v-drag    
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="templateVisible"
      @ok="selectedTemplate">
      <template slot="footer">
        <a-button
          key="back"
          @click="handleTemplateCancel">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedDeliveryTemplate">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"/>
    </a-modal>
  </div>
</template>
<script>

import {ListMixin} from '@comp/template/list/ListMixin'
import {httpAction, postAction, getAction} from '@/api/manage'


export default {
    mixins: [ListMixin],
    components: {
    },
    data () {
        return {
            showRequestToOrderPage: false,
            showEditPage: false,
            businessType: '',
            toElsAccount: '',
            submitLoading: false,
            templateVisible: false,
            nextOpt: true,
            currentEditRow: {},
            currentRow: {},
            templateNumber: undefined,
            templateOpts: [],
            sourceData: [],
            responseData: {}, //上报接口数据
            pageData: {
                businessType: 'delivery',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterOrderNo`, '请输入订单号')
                    }
                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), allow: () => {
                            return this.btnInvalidAuth('delivery#saleDeliveryItem:export')
                        }, icon: 'download', folded: false, clickFn: this.handleExportXls
                    },

                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        value: 'define',
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                showOptColumn: false
            },
            url: {
                list: '/delivery/saleDeliveryItem/list',
                columns: 'saleDeliveryItem',
                exportXlsUrl: '/delivery/saleDeliveryItem/exportXls'
            }
        }
    },
    methods: {
        showProcessCheck (row){
            if(!row.deliveryProgressTemplateId){
                return true
            }
            return false
        },
        hideOrderProgressModel () {
            this.showEditPage = false
            this.showRequestToOrderPage = false
            this.searchEvent(false)
        },

        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dStRH_2eb66187`, '发货单明细'))
        }


    },
    mounted () {
        this.serachCountTabs('/delivery/saleDeliveryItem/counts')
    }
}
</script>