<template>
  <div style="height:100%">
    <list-layout
      v-show=" !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <ViewDeliveryOrderItem-Modal
      ref="detailPage"
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetail"/>
  </div>
</template>

<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import ViewDeliveryOrderItemModal from './modules/ViewDeliveryOrderItemModal'
import {httpAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        ViewDeliveryOrderItemModal
    },
    data () {
        return {
            showDetailPage: false,
            pageData: {
                businessType: 'purchaseDelivery',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterSupplierOrOrder`, '请输入供应商ELS账号或订单号')
                    }
                    // ,
                    // {
                    //     type: 'select',
                    //     label: '行状态',
                    //     fieldName: 'itemStatus',
                    //     dictCode: 'srmOrderItemStatus',
                    //     placeholder: '请选择'
                    // }
                ],
                form: {
                    keyWord: '',
                    itemStatus: ''
                },
                isOrder:{
                    column: undefined
                },
                button: [
                    //{label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receive`, '收货'), title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receive`, '收货'), clickFn: this.handleEditList},
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receive`, '收货'),
                        clickFn: this.handleEdit,
                        allow: this.showEditCondition,
                        authorityCode: 'delivery#purchaseDeliveryOrderItem:itemReceive'
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ],
                optColumnWidth: 150
            },
            tabsList: [],
            url: {
                list: '/order/purchaseOrderItem/listDelivery',
                receiveOrder: '/order/purchaseOrderItem/receiveOrder',
                columns: 'PurchaseOrderDeliveryItem'
            }
        }
    },
    mounted () {
        // this.serachTabs('srmOrderItemStatus', 'itemStatus')
        this.serachCountTabs('/order/purchaseOrderItem/countsDelivery')
    },
    methods: {
        showEditCondition (row) {
            if (this.btnInvalidAuth('delivery#purchaseDeliveryOrderItem:itemReceive')) {
                return true
            }
            if (row.notDeliveryQuantity > 0) {
                return false
            } else {
                return true
            }
        },
        handleEditList () {
            let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            if (selectedRows.length <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectLineToReceive`, '请选择需要收货的行'))
                return
            } else {
                let that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receive`, '收货'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherSelectLineReceipt`, '确认是否选中行收货?'),
                    onOk: function () {
                        that.postUpdateData(that.url.receiveOrder, selectedRows)
                    }
                })
            }
        },
        hideDetail () {
            this.$store.dispatch('SetTabConfirm', false)
            this.showDetailPage = false
        },
        handleEdit (row) {
            this.currentEditRow = row
            row['editType'] = 'edit'
            this.showDetailPage = true
        },
        handleView (row) {
            this.currentEditRow = row
            row['editType'] = 'detail'
            this.showDetailPage = true
        },
        postUpdateData (url, params) {
            this.$refs.listPage.confirmLoading = true
            httpAction(url, params, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        hideDetailPage () {
            this.showDetailPage = false
        }
    }
}
</script>
