import { srmI18n, getLangAccount } from '@/utils/util'

const types = {
    403: {
        img: 'https://gw.alipayobjects.com/zos/rmsportal/wZcnGqRDyhPOEYFcZDnb.svg',
        title: '403',
        desc: srmI18n(`${getLangAccount()}#i18n_title_noAccessDesc`, '抱歉，你无权访问该页面')
    },
    404: {
        img: 'https://gw.alipayobjects.com/zos/rmsportal/KpnpchXsobRgLElEozzI.svg',
        title: '404',
        desc: srmI18n(`${getLangAccount()}#i18n_alert_sPWLBjCQbWWVKHRvjHclb_d7b42815`, '抱歉，您没有访问权限，请联系管理员进行授权')
    },
    500: {
        img: 'https://gw.alipayobjects.com/zos/rmsportal/RVRUAYdCGeYNBWoKiIwB.svg',
        title: '500',
        desc: srmI18n(`${getLangAccount()}#i18n_title_serverErrorDesc`, '抱歉，服务器出错了')
    }
}

export default types