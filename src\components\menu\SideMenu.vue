<template>
  <a-layout-sider
    :class="['sider', isDesktop() ? null : 'shadow', theme, fixSiderbar ? 'ant-fixed-sidemenu' : null ]"
    width="200px"
    :collapsible="collapsible"
    v-model="collapsed"
    :trigger="null"
  >
<!--    <logo :title="companySet.companyShortName || logoTitle" />-->
    <logo title="SRM" />
    <div
      class="searchWrap"
      :class="{change:systemTheme === 'dark'}"
      v-if="searchShow">
      <a-icon
        type="search"
        class="searchIcon"
        :class="{change:systemTheme === 'dark'}" />
      <input
        v-model="search"
        :class="{change:systemTheme === 'dark'}"
        ref="search"
        readonly
        onfocus="this.removeAttribute('readonly');" 
        onblur="this.setAttribute('readonly',true);"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNmhRIJ_56bae616`, '请输入查询关键字')" />
      <a-icon
        v-if="search" 
        type="close-circle" 
        class="closeIcon"
        :class="{change:systemTheme === 'dark'}"
        @click="searchClose" />
    </div>
    <s-menu
      :collapsed="collapsed"
      :menu="navMenus"
      :theme="theme"
      @select="onSelect"
      :mode="mode"
      :searchOpenKeys="searchOpenKeys"
      :style="smenuStyle"
    />
  </a-layout-sider>
</template>

<script>
import ALayoutSider from 'ant-design-vue/es/layout/Sider'
import Logo from '../tools/Logo'
import SMenu from './index'
import { mixin, mixinDevice } from '@/utils/mixin.js'
import { USER_COMPANYSET } from '@/store/mutation-types'
import { mapGetters, mapState } from 'vuex'
export default {
    name: 'SideMenu',
    components: { ALayoutSider, Logo, SMenu },
    mixins: [mixin, mixinDevice],
    props: {
        searchShow: {
            type: Boolean,
            default: true
        },
        mode: {
            type: String,
            required: false,
            default: 'inline'
        },
        theme: {
            type: String,
            required: false,
            default: 'dark'
        },
        collapsible: {
            type: Boolean,
            required: false,
            default: false
        },
        collapsed: {
            type: Boolean,
            required: false,
            default: false
        },
        menus: {
            type: Array,
            required: true
        }
    },
    computed: {
        ...mapGetters({
            companySet: 'getCompanySet'
        }),
        ...mapState({
            systemTheme: state => state.app.theme
        }),
        logoTitle () {
            let comSet = this.$ls.get(USER_COMPANYSET) || {}
            return comSet.companyShortName || 'SRM'
        },
        smenuStyle () {
            let style = { 'padding': '0' }
            if (this.fixSiderbar) {
                style['height'] = 'calc(100% - 119px)'
                style['overflow'] = 'auto'
                style['overflow-x'] = 'hidden'
            }
            return style
        },
        navMenus () {
            if(this.search){
                const menus = JSON.parse(JSON.stringify(this.menus))
                const list = this.arrayRecursionFilter(menus, this.search)
                if(list && list.length>0){
                    const _list = JSON.parse(JSON.stringify(list))
                    let keys = this.arrayPathFilter(_list, [])
                    this.searchOpenKeys = Array.from(new Set(keys))
                    // console.log(this.searchOpenKeys, 'this.searchOpenKeys')
                }
                return list
            }
            this.searchOpenKeys = []
            return this.menus
        }
    },
    data () {
        return {
            search: '',
            searchOpenKeys: []
        }
    },
    methods: {
        searchClose () {
            this.search = ''
            this.$refs.search && this.$refs.search.focus()
        },
        // 递归过滤
        arrayRecursionFilter (arr, search) {
            const list = arr.filter(item=>{
                if(item.children && item.children.length>0){
                    item.children = this.arrayRecursionFilter(item.children || [], search)
                }
                return item.meta.title.includes(search) || (item.children && item.children.length)
            })            
            return list
        },
        arrayPathFilter (list, arr) {
            list.forEach(item=>{
                if(item.children && item.children.length>0){
                    arr.push(item.path)
                    item.children = this.arrayPathFilter(item.children, arr)
                }
            })
            // console.log(arr, 'arr')
            return arr
        },
        onSelect (obj) {
            this.$emit('menuSelect', obj)
        }
    }
}
</script>
<style lang="scss" scoped>


  .sider {
    $scrollBarSize: 4px;

    ul.ant-menu {

      /* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
      &::-webkit-scrollbar {
        width: $scrollBarSize;
        height: $scrollBarSize;
        background-color: transparent;
        // display: none;
      }

      & .-o-scrollbar {
        display: none;
      }

      /* 兼容IE */
      -ms-overflow-style: none;
      -ms-scroll-chaining: chained;
      -ms-content-zooming: zoom;
      -ms-scroll-rails: none;
      -ms-content-zoom-limit-min: 100%;
      -ms-content-zoom-limit-max: 500%;
      -ms-scroll-snap-type: proximity;
      -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);

      /* 定义滚动条轨道 */
      &::-webkit-scrollbar-track {
        background-color: transparent;
      }

      /* 定义滑块 */
      &::-webkit-scrollbar-thumb {
        border-radius: $scrollBarSize;
        background-color: #eee;
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);

        &:hover {
          background-color: #dddddd;
        }

        &:active {
          background-color: #bbbbbb;
        }
      }
    }

    /** 暗色系滚动条样式 */
    &.dark ul.ant-menu {
      &::-webkit-scrollbar-thumb {
        background-color: #666666;

        &:hover {
          background-color: #808080;
        }

        &:active {
          background-color: #999999;
        }
      }
    }
    // 菜单高度修改
    :deep(.ant-menu-inline > .ant-menu-item){
        height: 28px;
        line-height: 28px;
    }
    :deep(.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title){
        height: 28px;
        line-height: 28px;
    }
    :deep(.ant-menu-sub.ant-menu-inline > .ant-menu-item, .ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title){
        height: 28px;
        line-height: 28px;
    }
  }
  :deep(.ant-menu-vertical > .ant-menu-item , .ant-menu-inline > .ant-menu-item){
      height: 28px;
      line-height: 28px;
  }
  :deep(.ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title){
      height: 28px;
      line-height: 28px;
  }
  .searchWrap{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 14px 0 10px 0;
      position: relative;
      width: 179px;
      height: 36px;
      line-height: 36px;
      background: #f2f3f5;
      border-radius: 8px;
      margin-left: 14px;
      .searchIcon{
          width: 20px;
          margin: 0 2px 0 4px;
          color: #B8B8BB;
      }
      .closeIcon{
          width: 20px;
          margin-right: 3px;
          color: rgba(0, 0, 0, 0.25); 
      }
      input{
        flex: 1;
        min-width: 0;
        height: 36px;
        background: #F2F3F5;
        border-radius: 8px;
        outline: none;
        border: none;
        color: #B8B8BB;
      }
      input::-webkit-input-placeholder{
          color: #BBBBBB;
      }
      .change{
         background: #1C314F;
         color: #999999;
      }
      .change::-webkit-input-placeholder{
          color: #999999;
      }
  }
  .change{
     background: #1C314F; 
  }
</style>


<style lang="scss">
  $aside-bg-color:rgb(33, 56, 89);
  $aside-font-color:rgb(179, 192, 231);

  .ant-menu-vertical.ant-menu-sub .ant-menu-item{
    height: 28px;
    line-height: 28px;
  }

  .ant-menu-dark, .ant-menu-dark .ant-menu.ant-menu-vertical.ant-menu-sub.ant-menu-submenu-content {
    background: $aside-bg-color;
    color: $aside-font-color;
    >li {
      >a, >div {
        font-size: 13px;
      }
      >a {
        color:  $aside-font-color;
      }
    }
  }

  .ant-menu.ant-menu-vertical.ant-menu-sub.ant-menu-submenu-content {
    >li {
      >a, >div {
        font-size: 13px;
      }
    }
  }

  .ant-menu-submenu {
    .ant-menu-vertical {
      .ant-menu-submenu {
        .ant-menu-submenu-title, >a {
          height: 28px;
          line-height: 28px;
        }
      }
    }
  }
  
  .ant-menu.ant-menu-root {
    & > .ant-menu-item:first-child {
      background-color: transparent;

      & > a, & > a:hover {
        color: rgba(0, 0, 0, 0.65);
      }

      &.ant-menu-item-selected {
        & > a, & > a:hover {
          color: #1890ff;
        }
      }
    }

    &.ant-menu-dark > .ant-menu-item:first-child {
      & > a, & > a:hover {
        color: rgba(255, 255, 255, 0.65);
      }

      &.ant-menu-item-selected {
        & > a, & > a:hover {
          color: rgba(255, 255, 255, 1);
        }
      }
    }
  }
</style>
