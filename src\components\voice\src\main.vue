<template>
  <audio
    v-if="playing"
    autoplay="autoplay"
    type="audio/mpeg"
    @ended="ended"
    ref="audioRef"
    controls>
  </audio>
</template>
<script>
import {  DEFAULT_LANG } from '@/store/mutation-types'
import {postAction} from '@/api/manage'
export default {
    name: 'Voice',
    props: {

    },

    data () {
        return {
            playing: false,
            baseUrl: '/base/translate/textToMp3File',
            sourceUrl: ''
        }
    },

    mounted () {
        console.log(this.$ls.get(DEFAULT_LANG))
    },
    computed: {

    },

    methods: {
        init (opt) {
            this.playing = true
            if (opt.baseUrl) {
                this.baseUrl = opt.baseUrl
            }
            if (this.sourceUrl) { //没播完切换
                window.URL.revokeObjectURL(this.sourceUrl) //释放掉blob对象 
            }
            const params = {
                lan: this.$ls.get(DEFAULT_LANG) || 'zh',
                text: opt.text,
                spd: 3,
                source: 'web'
            }
            console.log(params)
            postAction(this.baseUrl, params,
                {
                    responseType: 'arraybuffer'
                }
            ).then(res => {
                var blob =  new Blob([res], {type: 'audio/mpeg'})
                this.sourceUrl =  window.URL.createObjectURL(blob)
                this.$refs.audioRef.src = this.sourceUrl
            }).finally(() => {
            })
        },
        ended () {
            this.playing = false
            window.URL.revokeObjectURL(this.sourceUrl) //释放掉blob对象 
        }
    }
}
</script>

<style lang="scss" scoped>
 
</style>