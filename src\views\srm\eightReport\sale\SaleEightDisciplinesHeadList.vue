<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage "
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <sale-eight-disciplines-head-edit
      v-if="showEditPage"
      ref="eightDisciplines"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <sale-eight-disciplines-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import saleEightDisciplinesHeadEdit from './modules/SaleEightDisciplinesHeadEdit'
import saleEightDisciplinesHeadDetail from './modules/SaleEightDisciplinesHeadDetail'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        saleEightDisciplinesHeadEdit,
        saleEightDisciplinesHeadDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'eightDisciplines',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNWWty_874ca137`, '请输入8D单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                        fieldName: 'eightDisciplinesStatus',
                        dictCode: 'srm8DStatus'
                    }
                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'eightReport#SaleEightReportHead:query' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEditSingle, allow: this.allowEdit, authorityCode: 'eightReport#SaleEightReportHead:edit'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, authorityCode: 'eightReport#SaleEightReportHead:chat'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord, authorityCode: 'eightReport#SaleEightReportHead:record'}
                ],
                optColumnWidth: 270
            }, 
            url: {
                list: '/eightReport/saleEightDisciplines/list',
                add: '/eightReport/saleEightDisciplines/add',
                delete: '/eightReport/saleEightDisciplines/delete',
                deleteBatch: '/eightReport/saleEightDisciplines/deleteBatch',
                exportXlsUrl: '/eightReport/saleEightDisciplines/exportXls',
                importExcelUrl: '/eightReport/saleEightDisciplines/importExcel',
                columns: 'SaleEightDisciplinesHead',
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.eightDisciplinesNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SaleEightDisciplinesHead', url: this.url || '', recordNumber})
        },
        allowEdit (row){
            // 8D状态=='已结案' 或 审批状态=='审批中'时不可编辑
            if (row.eightDisciplinesStatus == 'D9' || row.auditStatus == '1' || row.eightDisciplinesStatus == 'D10'){
                return true
            }
            return false
        },
        handleEditSingle (row) {
            this.handleEdit(row)
        },
        handleDeleteSingle (row) {
            this.handleDelete(row)
        }
    }
}
</script>