<template>
  <div class="edit-page">
    <a-spin
      v-if="!reviewStatus && !scoringStatus && !reviewScoringStatus && !priceComparisonStatus && !priceScoreStatus && !showQuotes"
      :spinning="confirmLoading">
      <div class="page-header">
        <a-steps
          :current="currentStep2"
          @change="changeStep"
          size="small">
          <template v-for="step in stepList">
            <a-step
              :key="step.id"
              :title="step.name" />
          </template>
        </a-steps>
      </div>
      <div class="page-content">
        <div class="page-content-title">
          <titleCrtl>
            <span>{{ titleFn }}</span>

            <template slot="right">
              <a-button
                @click="handleEvaluationClarify"
                v-if="titleFn == pingbus"
                style="float: right">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBLV_411d1f04`, '评标澄清') }}</a-button>
            </template>
          </titleCrtl>
        </div>
        <div class="page-content-grid">
          <template v-for="(grid, index) in gridList">
            <vxe-grid
              :loading="loading"
              :ref="grid.gridCode"
              :key="grid.gridCode"
              v-show="index == currentStep2"
              v-bind="gridConfig"
              height="300"
              :data="grid.gridData"
              :columns="grid.columns">
              <template
                #expert_default="{ row }"
                v-if="index == 0">
                <span>{{ row.expert }}</span>
                <span
                  v-if="row.judgesGroupLeader == '1'"
                  style="color: blue; margin-left: 5px">{{ $srmI18n(`${$getLangAccount()}#i18n_field_[Ve]_2175647`, '[组长]') }}</span>
              </template>
              <template #data_default="{ row, column }">
                <span v-if="row.invalid == '1'">/</span>
                <span v-else>{{ row[column['property']] }}</span>
              </template>
              <template #checkbox_default="{ row }">
                <vxe-checkbox
                  v-if="row.invalid != '1'"
                  v-model="row.candidate"
                  checked-value="1"
                  unchecked-value="0"
                  disabled></vxe-checkbox>
              </template>
              <template
                #file_default="{ row }"
                v-if="index == 2">
                <span v-if="row.fileType == '1'">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBBm_41201cd7`, '评标表格') }}</span>
                <span v-if="row.fileType == '2'">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBsx_411b7648`, '评标报告') }}</span>
              </template>
              <template
                #cz_default="{ row }"
                v-if="index == 2">
                <a-button
                  v-if="row.fileName"
                  type="link"
                  @click="downloadEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a-button>
                <a-button
                  v-if="row.fileName"
                  type="link"
                  @click="preViewEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a-button>
              </template>
            </vxe-grid>
          </template>
        </div>
      </div>
      <div class="page-footer">
        <a-button
          :type="customPageFooterPreBtn.type"
          v-if="currentStep2"
          @click="customPageFooterPreBtn.click">{{ customPageFooterPreBtn.title }}</a-button>
        <a-button
          :type="customPageFooterNextBtn.type"
          v-if="currentStep2 < stepList.length - 1"
          @click="customPageFooterNextBtn.click">{{ customPageFooterNextBtn.title }}</a-button>
        <a-button
          v-if="evaStatus == '0'"
          type="primary"
          @click="biddingEnd">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBsR_411ef59d`, '评标终止') }}</a-button>
        <a-button
          v-if="canMultipleQuotes"
          @click="handleQuotes"
          type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_field_hAOVsu_fb06e64c`, '发起多轮报价') }}</a-button>
        <a-button
          v-if="[2320, 4520].includes(subpackage.status)"
          type="primary"
          @click="biddingAnew">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VVUB_43d60d86`, '重新评标') }}</a-button>
      </div>
    </a-spin>
    <!-- 评审项 -->
    <ReviewItems
      v-if="reviewStatus"
      :currentRow="currentRow" />
    <!-- 评分项 -->
    <ScoringItem
      v-if="scoringStatus"
      :currentRow="currentRow" />
    <PriceComparison
      v-if="priceComparisonStatus"
      :currentRow="currentRow"
    />
    <!-- 评审评分项 -->
    <ReviewScoringItem
      v-if="reviewScoringStatus"
      :currentRow="currentRow" />
    <!-- 价格评分项 -->
    <PriceScore
      v-if="priceScoreStatus"
      :currentRow="currentRow" />
    <multipleQuotes
      v-if="showQuotes"
      :pageStatus="isEvaFinish ? 'detail' : 'edit'"
      :show="showQuotes"
      :currentEditRow="currentEditRow" />
    <LeaderOpinionModal
      ref="LeaderOpinionModal"
      typeNum="leader"
      pageStatus="detail"/>
  </div>
</template>

<script lang="jsx">
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
// import { gridOptionsMixin } from '../public/gridOptionsMixin.js'
import ReviewItems from '../components/ReviewItems.vue'
import ScoringItem from '../components/ScoringItem.vue'
import ReviewScoringItem from '../components/ReviewScoringItem.vue'
import PriceScore from '../components/PriceScore.vue'
import LeaderOpinionModal from '../components/LeaderOpinionModal.vue'
import { USER_INFO } from '@/store/mutation-types'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import multipleQuotes from '../multipleQuotes/multipleQuotes.vue'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import PriceComparison from '../components/PriceComparison.vue'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    mixins: [tableMixins, baseMixins],
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage', 'resetCurrentSubPackage'],
    computed: {
        titleFn () {
            let title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理')
            switch (this.currentStep2) {
            case 1:
                title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUAR_40ed07d8`, '评审排名')
                break
            case 2:
                title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRnL_753bc075`, '评标结果材料')
                break
            default:
                title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理')
                break
            }
            return title
        },
        canMultipleQuotes () {
            if (this.formData && this.formData.stageQuote == '1') {
                // 多轮报价发起人:0-全部，1-采购方，2-评标组长
                if (this.formData.stageQuoteOperator == '1') {
                    let { elsAccount } = this.$ls.get(USER_INFO)
                    if (this.formData.purchaseExecutorAccount == elsAccount) return true
                    return false
                } else if (this.formData.stageQuoteOperator == '2') {
                    if (this.leaderStatus) return true
                    return false
                } else {
                    return true
                }
            } else {
                return false
            }
        },
        subpackage () {
            return this.currentSubPackage()
        }
    },
    components: {
        ReviewItems,
        ScoringItem,
        ReviewScoringItem,
        PriceScore,
        multipleQuotes,
        titleCrtl,
        LeaderOpinionModal,
        PriceComparison
    },
    data () {
        return {
            pingbus: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理'),
            priceComparisonStatus: false,
            reviewStatus: false, // 评审项组件控制状态
            scoringStatus: false, // 评分项组件控制状态
            reviewScoringStatus: false, // 评审评分项组件控制状态
            priceScoreStatus: false, // 价格评分项
            confirmLoading: false,
            loading: false,
            showQuotes: false, // 发起多轮报价
            currentStep2: 0,
            customPageFooterPreBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_prevStep`, '上一步'), type: 'primary', belong: 'preStep', click: this.prevStep },
            customPageFooterNextBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nextStep`, '下一步'), type: 'primary', belong: 'nextStep', click: this.nextStep },
            stepList: [
                { id: 1, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理') },
                { id: 2, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUAR_40ed07d8`, '评审排名') },
                { id: 3, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRnL_753bc075`, '评标结果材料') }
            ],
            gridList: [
                {
                    gridCode: 'BidEvaluation',
                    columns: [{ field: 'expert', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_evaluationExpert`, '评标专家'), slots: { default: 'expert_default' } }],
                    gridData: []
                },
                {
                    gridCode: 'ReviewRanking',
                    columns: [],
                    gridData: []
                },
                {
                    gridCode: 'BidEvaluationResults',
                    columns: [
                        { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 50 },
                        { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nLAc_30836479`, '材料类型'), slots: { default: 'file_default' } },
                        { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称') },
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), slots: { default: 'cz_default' } }
                    ],
                    gridData: [
                        { fileType: '1', fileName: '', attachmentId: '' },
                        { fileType: '2', fileName: '', attachmentId: '' }
                    ]
                }
            ],
            columns1: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), type: 'seq', width: 50 },
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBtL_2e61022a`, '投标单位') }
            ],
            columns2: [
                { field: 'weightScore', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_totalScore`, '总分'), slots: { default: 'data_default' } },
                { field: 'order', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'), slots: { default: 'data_default' } },
                { field: 'candidate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQYI_2fbbaebf`, '是否推荐'), width: 100, slots: { default: 'checkbox_default' } }
            ],
            currentRow: {},
            isEvaFinish: false, // 是否全部完成评审
            currentEditRow: {},
            rankingColumns: [],
            evaStatus: '0',
            formData: {}
        }
    },
    async mounted () {
        this.currentEditRow = this.$ls.get('SET_TENDERCURRENTROW') || {}
        this.currentEditRow['subpackageId'] = this.subpackageId()
        console.log(this.currentEditRow)
        await this.getBidEvaluationData()
        await this.getIsEvaFinish()
        console.log(this.resetCurrentSubPackage)
    },
    methods: {
        // 跳转到评标澄请列表页
        handleEvaluationClarify () {
            let params = { linkFilter: true, subpackageId: this.currentEditRow.subpackageId, evaInfoId: this.currentEditRow.evaInfoId, ifToList: true, checkType: this.currentEditRow.checkType, processType: this.currentEditRow.processType, currentStep: this.currentEditRow.currentStep }
            const routeUrl = this.$router.resolve({
                path: '/BidEvaluationClarify/BidEvaluationClarifyList',
                query: {
                    ...params
                }
            })
            console.log(this.$ls.get('SET_TENDERCURRENTROW'))
            console.log(params)
            window.open(routeUrl.href, '_blank')
        },
        biddingEnd () {
            // 评标终止
            const that = this
            const params = {
                subpackageId: this.subpackageId(),
                evaInfoId: this.currentEditRow.evaInfoId || ''
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DKW_1865cf0`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CtSvmdIUByWxqtTtk_80e3de97`, '点击后，本次项目评标结束，不可继续操作！'),
                onOk () {
                    return new Promise((resolve, reject) => {
                        postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/termination', params)
                            .then(async (res) => {
                                const tp = res.success ? 'success' : 'error'
                                that.$message[tp](res.message)
                                if (res.code == 200 && tp == 'success') {
                                    await that.getBidEvaluationData()
                                    await that.getIsEvaFinish()
                                    that.resetCurrentSubPackage()
                                }
                            })
                            .finally(() => {
                                resolve(false)
                            })
                    })
                }
            })
        },
        biddingAnew () {
            // 重新评标
            const that = this
            const params = {
                subpackageId: this.subpackageId(),
                evaInfoId: this.currentEditRow.evaInfoId || ''
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DKW_1865cf0`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CtSAPzsVVUBVVzELSSULqHcUUtk_938b5c0`, '点击后，当前分包重新评标，重新分配任务后，评委可进行评审操作！'),
                onOk () {
                    return new Promise((resolve, reject) => {
                        postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/reEvaluate', params)
                            .then(async (res) => {
                                const tp = res.success ? 'success' : 'error'
                                that.$message[tp](res.message)
                                if (res.success) {
                                    that.resetCurrentSubPackage()
                                    await that.getBidEvaluationData()
                                    await that.getIsEvaFinish()
                                }
                            })
                            .finally(() => {
                                resolve(false)
                            })
                    })
                }
            })
        },
        checkBoxChange (row, $event) {
            const { checked } = $event
            const gridObj = this.$refs.ReviewRanking
            if (gridObj && gridObj.length > 0) {
                gridObj[0].setCheckboxRow(row, checked)
            }
        },
        // 获取是否完成评标
        getIsEvaFinish () {
            const params = {
                evaInfoId: this.currentEditRow.evaInfoId || ''
            }
            getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/isEvaFinish', params).then((res) => {
                if (res.code == 200) {
                    let { result = false } = res
                    if (result) {
                        this.getSupplierEvaRanking()
                    }
                    this.isEvaFinish = result
                }
            })
        },
        // 获取评标管理数据
        async getBidEvaluationData () {
            const params = {
                subpackageId: this.currentEditRow.subpackageId || '',
                requestType: 1
            }
            this.confirmLoading = true
            // 获取评标专家组数据
            const res = await getAction('/tender/evaluation/purchaseTenderProjectEvaSettingHead/queryEvaGroupBySubpackageId', params)
            if (res.code != 200) {
                this.confirmLoading = false
                return
            }
            const resultList = res.result
            this.rankingColumns = resultList
            let columns = []
            resultList.forEach((rl) => {
                if (rl) {
                    let titleTip = '',
                        type = rl.type || '0'
                    switch (type) {
                    case '0':
                        titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WUUdW_4326c815`, '(评审项)')
                        break
                    case '1':
                        titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n__WUzdW_4302b93a`, '(评分项)')
                        break
                    case '2':
                        titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WUUUzdW_10142117`, '(评审评分项)')
                        break
                    case '3':
                        titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WumUUdW_143bbba`, '(价格评审项)')
                        break
                    }
                    const column = {
                        field: rl.id,
                        checkType: rl.checkType,
                        currentStep: rl.currentStep,
                        title: `${rl.name}${titleTip}`,
                        slots: {
                            header: () => {
                                return [
                                    <span>{`${rl.name}${titleTip}`}<a-icon type="edit" theme="twoTone" onClick={() => this.showLeaderOpinion(rl)}/></span>
                                ]
                            },
                            // 使用 JSX 渲染
                            default: ({ row, column }) => {
                                if (row[column.property]) {
                                    let span = null
                                    if (row['summaryStatus']) {
                                        // 是否是显示汇总的操作按钮判断
                                        if (rl.type != '3'  && rl.type != '4') {
                                            // 价格评审项没有汇总操作
                                            span =
                                                row[column.property] == 0 ? (
                                                    <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LMk_190c17e`, '未汇总')}</span>
                                                ) : (
                                                    <span style="color: blue; cursor: pointer;" onClick={() => this.pbclick({ row: row, type: rl.type, id: rl.id, status: false, sum: true, summaryCalType: rl.summaryCalType, title: rl.name })}>
                                                        {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IMk_16e2646`, '已汇总')}
                                                    </span>
                                                )
                                        } else {
                                            span = <span></span>
                                        }
                                    } else {
                                        span =
                                            row[column.property] == 0 ? (
                                                <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LUU_1948d07`, '未评审')}</span>
                                            ) : (
                                                <span style="color: blue; cursor: pointer;" onClick={() => this.pbclick({ row: row, type: rl.type, id: rl.id, status: false, title: rl.name })}>
                                                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUML_40ec1d21`, '评审完成')}
                                                </span>
                                            )
                                    }
                                    return [span]
                                }
                                return []
                            }
                        }
                    }
                    columns.push(column)
                }
            })
            console.log(columns)
            // 由于返回值中processType为空，所以不作为条件过滤
            let { checkType, currentStep, processType } = this.getNodeParams()
            if(checkType == '1' && currentStep == '1'){
                columns=columns.filter(item=>{
                    if(item.checkType == '1' && item.currentStep == '1'){
                        return true
                    }
                })
            }
            this.gridList.forEach((grid) => {
                if (grid.gridCode == 'BidEvaluation') {
                    grid.columns = [grid.columns[0]] // 清空原有数据，只留第一条
                    grid.columns = [...grid.columns, ...columns] // 合并最终需要的数据
                }
            })

            // 查询专家内容
            const res2 = await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryBySubpackageId', params)
            if (res2.code != 200) {
                this.confirmLoading = false
                return
            }
            const resultList2 = res2.result || {}
            this.formData = resultList2
            this.currentEditRow['evaInfoId'] = resultList2.evaInfoId || ''
            this.evaStatus = resultList2.evaStatus || ''
            const expertTaskList = resultList2.expertTaskList || {}
            let gridData = []
            const { subAccount } = this.$ls.get(USER_INFO)
            let leaderStatus = false
            // 连续替换，记录是否全部评标完成
            let record = {}
            Object.keys(expertTaskList).forEach((key) => {
                let objArray = key && key.split('_')
                let obj = {
                    expert: objArray[0],
                    elsAccount: objArray[1],
                    elsSubAccount: objArray[2],
                    btnStatus: subAccount == objArray[2] ? true : false,
                    summaryStatus: false
                }
                expertTaskList[key].forEach((item, index) => {
                    if (index == 0) {
                        obj['judgesGroupLeader'] = item['judgesGroupLeader']
                        // 标识当前账号是否是组长
                        if (item['judgesGroupLeader'] == '1' && subAccount == objArray[2]) {
                            leaderStatus = true
                        }
                    }
                    obj[item.evaGroupId] = item.evaGroupStatus
                    record[item.evaGroupId] = record[item.evaGroupId] == 0 ? '0' : item.evaGroupStatus // 记录最后一次状态
                    record[`${item.evaGroupId}_ID`] = item.id
                    obj[`${item.evaGroupId}_ID`] = item.id
                })

                if (obj['judgesGroupLeader'] == '1') {
                    gridData.unshift(obj)
                } else {
                    gridData.push(obj)
                }
            })
            gridData.push(Object.assign({}, record, { expert: '汇总', summaryStatus: true }))
            gridData.forEach((grid, index, array) => {
                grid['leaderStatus'] = leaderStatus
            })
            console.log('gridData', gridData)
            this.gridList.forEach((grid) => {
                if (grid.gridCode == 'BidEvaluation') {
                    grid.gridData = gridData // 合并最终需要的数据
                }
            })
            this.confirmLoading = false
        },
        // 获取评审排名和评标结果材料数据
        async getSupplierEvaRanking () {
            const params = {
                evaInfoId: this.currentEditRow.evaInfoId || ''
            }
            await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/getSupplierEvaRanking', params).then((res) => {
                if (res.code == 200) {
                    let { result = {} } = res
                    let supplierEvaRanking = result && result.supplierEvaRanking
                    const evaBidAttachmentInfoList = result.evaBidAttachmentInfoList
                    let oldData = {}
                    let col = []
                    supplierEvaRanking &&
                        supplierEvaRanking.forEach((item, index) => {
                            let resultItem = item.result
                            if (resultItem) {
                                const resultData = Object.keys(resultItem).map((rl) => {
                                    return resultItem[rl]
                                })
                                let dataObj = {}
                                resultData.forEach((data) => {
                                    const evaGroupResult = data['evaGroupResult'] == '1' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_passed`, '通过') : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xeR_13701ba`, '不通过')
                                    dataObj[data['evaGroupId']] = data['evaGroupType'] == '0' ? evaGroupResult : data['weightScore']
                                })
                                item = Object.assign(item, dataObj)
                                if (index === 0) {
                                    this.rankingColumns.forEach((rank) => {
                                        // 判断评审不通过时的操作
                                        oldData[rank['id']] = rank['type'] == '0' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xeR_13701ba`, '不通过') : '/'
                                    })
                                }
                            } else {
                                item = Object.assign(item, oldData)
                            }
                        })
                    this.rankingColumns.forEach((rank) => {
                        // 延用评标管理的表头顺序
                        col.push({
                            field: rank['id'],
                            title: rank['name'],
                            width: 100
                        })
                    })
                    col = [...this.columns1, ...col, ...this.columns2]
                    this.gridList.forEach((grid) => {
                        if (grid.gridCode == 'ReviewRanking') {
                            grid.gridData = supplierEvaRanking // 合并最终需要的数据
                        }
                    })
                    this.gridList.forEach((grid) => {
                        if (grid.gridCode == 'ReviewRanking') {
                            grid.columns = col // 合并最终需要的数据
                        }
                    })
                    if (evaBidAttachmentInfoList) {
                        this.gridList.forEach((grid) => {
                            if (grid.gridCode == 'BidEvaluationResults') {
                                grid.gridData = evaBidAttachmentInfoList.length > 0 ? evaBidAttachmentInfoList : grid.gridData // 合并最终需要的数据
                            }
                        })
                    }
                } else {
                    this.$message.error(res.message)
                    this.confirmLoading = false
                }
            })

            let gridObj = this.$refs.ReviewRanking[0]
            this.gridList[1].gridData &&
                this.gridList[1].gridData.forEach((grid) => {
                    if (grid.candidate == '1') {
                        gridObj.setCheckboxRow(grid, true) // 设置默认勾选项
                    }
                })
        },
        // 预览文件
        preViewEvent (row) {
            const subpackageId = row.subpackageId
            let preViewFile = {
                id: row.id,
                sourceType: row.sourceType,
                filePath: row.filePath,
                subpackageId
            }
            if(row.attachmentId){
                preViewFile.id = row.attachmentId
            }
            this.$previewFile.open({ params: preViewFile })
        },
        async downloadEvent (row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.attachmentId ? row.attachmentId : row.id
            const fileName = row.fileName
            const subpackageId = row.subpackageId
            let {message: url} = await getAttachmentUrl({id, subpackageId})
            this.confirmLoading=true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                this.confirmLoading=false
            })
        },
        // 点击评标触发该函数
        pbclick (rowData) {
            let { row, type, id, status, sum = false, summaryCalType = null, title } = rowData
            this.currentRow = {
                editStatus: status,
                summary: sum,
                summaryCalType: summaryCalType,
                evaGroupId: id,
                title: title,
                elsAccount: row.elsAccount,
                elsSubAccount: row.elsSubAccount,
                expert: row.expert,
                id: row[`${id}_ID`] || ''
            }
            let { checkType, currentStep, processType } = this.getNodeParams()
            let { subpackageId, evaInfoId } = this.currentEditRow
            this.currentRow = Object.assign(this.currentRow, { checkType, currentStep, processType, subpackageId, evaInfoId })

            switch (type) {
            case '0':
                this.reviewStatus = true
                break
            case '1':
                this.scoringStatus = true
                break
            case '2':
                this.reviewScoringStatus = true
                break
            case '3':
                this.priceScoreStatus = true
                break
            case '4':
                this.priceComparisonStatus = true
                break

            }
        },
        changeStep (step) {
            if (!this.isEvaFinish) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MKLMLjUULSSMkLS_142afc07`, '存在未完成的评审任务或汇总任务'))
                return false
            }
            this.currentStep2 = step
        },
        prevStep () {
            this.currentStep2 > 0 && this.currentStep2--
        },
        nextStep () {
            if (!this.isEvaFinish) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MKLMLjUULSSMkLS_142afc07`, '存在未完成的评审任务或汇总任务'))
                return false
            }
            if (this.currentStep2 < this.stepList.length - 1) {
                this.currentStep2++
            }
        },
        // 发起多轮报价
        handleQuotes () {
            this.showQuotes = true
        },
        // 评审意见
        showLeaderOpinion (item) {
            this.confirmLoading = true
            getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryLeaderOpinion', {evaGroupId: item.id, evaInfoId: this.formData.evaInfoId}).then(res => {
                if (res.success) {
                    let data = res.result
                    this.$refs.LeaderOpinionModal.open({data, title: item.name, leaderStatus: this.leaderStatus})
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    }
}
</script>

<style lang="less" scoped>
//编辑界面
.edit-page {
    height: 100%;
    .ant-spin-nested-loading {
        height: 100%;
        .ant-spin-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
    }
    .page-header {
        padding: 6px 14px;
        margin-bottom: 6px;
        background-color: #fff;
        .desc-col {
            text-align: left;
            line-height: 32px;
        }
        .btn-col {
            text-align: right;
        }
    }
    .page-content {
        flex: 1;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: auto;
        padding-left: 6px;
        padding-top: 6px;
        padding-right: 6px;
        background-color: #fff;
        margin-left: 6px;
        margin-right: 6px;
        .edit-form-box {
            // position: absolute;
        }
        .ant-advanced-rule-form {
            padding: 12px;
            .ant-form-item {
                display: flex;
                margin-bottom: 0;
                height: 55px;
                line-height: 55px;
                .ant-form-item-label {
                    text-overflow: ellipsis;
                }
            }
            .ant-form-item-control-wrapper {
                flex: 1;
            }
        }
        .edit-grid-box {
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            left: 0;
            overflow: auto;
            .vxe-toolbar {
                padding: 0 12px;
                .vxe-toolbar.size--mini {
                    height: 42px;
                }
                .tools-btn {
                    margin-left: 6px;
                }
            }
            .ant-input {
                height: 28px;
            }
        }
        .page-content-title {
            background-color: #eee;
            padding: 10px;
        }
        .page-content-grid {
            margin-top: 10px;
        }
    }
    .page-footer {
        border-top: 1px solid #e8eaec;
        background-color: #fff;
        margin-top: -1px;
        margin-left: 6px;
        margin-right: 6px;
        padding: 6px;
        text-align: center;
        .ant-btn:not(:first-child) {
            margin-left: 6px;
        }
    }
}
</style>
