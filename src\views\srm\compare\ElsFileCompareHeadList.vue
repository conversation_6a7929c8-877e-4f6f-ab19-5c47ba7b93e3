<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <EditElsFileCompareHead-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <ViewElsFileCompareHead-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>
</template>
<script>
import EditElsFileCompareHeadModal from './modules/EditElsFileCompareHeadModal'
import ViewElsFileCompareHeadModal from './modules/ViewElsFileCompareHeadModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {postAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EditElsFileCompareHeadModal,
        ViewElsFileCompareHeadModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'fileCompare',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '关键字')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'compare#elsFileCompareHead:add',
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        authorityCode: 'compare#elsFileCompareHead:view',
                        clickFn: this.handleView
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        authorityCode: 'compare#elsFileCompareHead:edit',
                        clickFn: this.handleEdit,
                        allow: this.showCondition
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        authorityCode: 'compare#elsFileCompareHead:delete',
                        clickFn: this.handleDelete,
                        allow: this.showCondition
                    },
                    {
                        type: 'close-circle',
                        title: this.$srmI18n(`${this.$getLangAccount()}#`, '对比'),
                        authorityCode: 'compare#elsFileCompareHead:edit',
                        clickFn: this.fileCompare,
                        allow: this.showCancelCondition
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ],
                optColumnWidth: 300
            },
            url: {
                list: '/compare/elsFileCompareHead/list',
                add: '/compare/elsFileCompareHead/add',
                delete: '/compare/elsFileCompareHead/delete',
                fileCompare: '/compare/elsFileCompareHead/fileCompare',
                exportXlsUrl: 'compare/elsFileCompareHead/exportXls',
                columns: 'elsFileCompareHeadList'
            }
        }
    },
    mounted () {
        this.pageData.form.keyWord = this.$route.query.sourceId
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls('文件对比')
        },
        showCondition (row) {
            return row.compareStatus == '1'
        },
        fileCompareCallBack (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        fileCompare (row) {
            postAction(this.url.fileCompare, row).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.currentEditRow = res.result
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>