<template>
  <detail-page
    ref="detailPage"
    :current-edit-row="currentEditRow"
    :pageData="pageData"></detail-page>
</template>

<script>
import detailPage from '@comp/template/detailPage'
export default {
    name: 'ViewDeliveryNoticeSaleModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        detailPage
    },
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryNotice`, '送货通知单'),
            confirmLoading: false,
            pageData: {
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            form: {
                                noticeNumber: '',
                                supplierId: '',
                                supplierCode: '',
                                supplierName: '',
                                companyCode: '',
                                companyName: '',
                                purchaseGroupCode: '',
                                purchaseGroupName: '',
                                purchaseOrgCode: '',
                                purchaseOrgName: '',
                                noticeType: '0',
                                noticeStatus: '0',
                                auditStatus: '0',
                                noticeDesc: '',
                                receiveAddress: '',
                                receiveContact: '',
                                receivePhone: '',
                                purchasePrincipal: '',
                                supplierPrincipal: '',
                                purchaseRemark: '',
                                supplierRemark: ''
                            },
                            list: [
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notificationNumber`, '通知单号'),
                                    fieldName: 'noticeNumber'
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                                    fieldName: 'noticeStatus_dictText'
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm65d_auditStatus`, '审批状态'),
                                    fieldName: 'auditStatus_dictText'
                                },
                                {
                                    type: 'selectModal',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                                    fieldName: 'supplierName'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaDesc`, '单据描述'),
                                    fieldName: 'noticeDesc'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司'),
                                    fieldName: 'companyCode'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeadc98_purchaseOrgCode`, '采购组织'),
                                    fieldName: 'purchaseOrgCode'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseGroupCode`, '采购组'),
                                    fieldName: 'purchaseGroupCode'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_recommdRemarks`, '需方备注'),
                                    fieldName: 'purchaseRemark'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRemarks`, '供方备注'),
                                    fieldName: 'supplierRemark'
                                }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                        content: {
                            type: 'table',
                            ref: 'deliveryNoticeSaleItemList',
                            columns: [
                                { 
                                    type: 'seq', width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                                    field: 'orderNumber',
                                    width: 130
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                    field: 'orderItemNumber',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialCode',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                    field: 'materialDesc',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
                                    field: 'materialSpec',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                                    field: 'materialGroupCode',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderDemandDate`, '订单需求日期'),
                                    field: 'orderRequireDate',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'),
                                    field: 'quantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityUnit`, '数量单位'),
                                    field: 'quantityUnit',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
                                    field: 'taxCode',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'),
                                    field: 'taxRate',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '净价'),
                                    field: 'netPrice',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'),
                                    field: 'price',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_demandDate`, '需求日期'),
                                    field: 'requireDate',
                                    width: 120,
                                    editRender: {name: 'mDatePicker'}
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量'),
                                    field: 'requireQuantity',
                                    width: 120,
                                    editRender: {name: 'AInputNumber'}
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_recommdRemarks`, '需方备注'),
                                    field: 'purchaseRemark',
                                    width: 220,
                                    editRender: {name: 'AInput'}
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_replyDate`, '回复交期'),
                                    field: 'replyDate',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_replyQuantity`, '回复数量'),
                                    field: 'replyQuantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remainQuantity`, '剩余数量'),
                                    field: 'remainQuantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRemarks`, '供方备注'),
                                    field: 'supplierRemark',
                                    width: 220
                                }
                            ]
                        }
                    }
                ],
                url: {
                    detail: '/delivery/deliveryNoticeSaleHead/queryDetailById'
                },
                publicBtn: [
                    {type: 'rollBack', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack}
                ]
            }
            
        }
    },
    mounted () {
       
    },
    created (){
      
    },
    methods: {
        init (row) {
            let params = {id: row.id}
            this.$refs.detailPage.getPageDetail(params)
        },
        goBack () {
            this.$emit('hide')
        }
    }
}
</script>
