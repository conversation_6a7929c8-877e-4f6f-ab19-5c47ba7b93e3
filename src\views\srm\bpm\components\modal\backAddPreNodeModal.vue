<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见')"
          prop="opinion">
          <a-textarea
            show-word-limit
            v-model="form.opinion"
            allowClear
            type="textarea"
            autoSize />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import {  withdrawalAddPreNode } from '../../api/analy.js'
export default {
    mixins: [modalMixins],
    data () {
        return {
            rules: {
                opinion: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMIIqd_9967a918`, '请填写意见备注'), trigger: 'change' },
                    { max: 600, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IIjHzxOBRWWW_a19cfb2b`, '意见的长度不能超过600'), trigger: 'change' }
                ]
            }
        }
    },
    methods: {
        // fieldSelectOk (data) {
        //     this.loading = true
        //     let params = {
        //         taskId: this.taskId,
        //         opinion: this.form.opinion
        //     }
        //     withdrawalAddPreNode(params).then(res => {
        //         if (res.code == 200) {
        //             this.$message.success(res.message)
        //         }
        //         this.loading = false
        //     })
        // }
        handleConfirm () {
            this.cheackValidate().then(() => {
                let {opinion} = this.form
                let params = {
                    opinion,
                    taskId: this.taskId
                }
                this.loading = true
                withdrawalAddPreNode(params).then(res => {
                    if (res.code == 200) {
                        this.$message.success(res.message)
                        this.$router.push('/srm/bpm/bpmnTodoList')
                    }else{
                        this.$message.error(res.message)
                    }
                    this.loading = false
                }).finally(() => {
                    this.loading = false
                    this.$emit('closeLoading')
                })
            })
        }
    },
    created () {
        
    }
}
</script>
  