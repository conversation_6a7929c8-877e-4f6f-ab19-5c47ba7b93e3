/*
 * @Author: your name
 * @Date: 2021-08-20 09:55:42
 * @LastEditTime: 2022-05-25 15:14:05
 * @LastEditors: wangxin <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend\src\utils\selectModalConst.js
 */
export const ROWSELECTMODALCONST = {
    groupCode: 'baseForm',
    sortOrder: '20',
    fieldType: 'selectModal',
    fieldLabel: '税码',
    fieldName: 'taxCode',
    dictCode: '',
    defaultValue: '',
    dataFormat: '',
    helpText: '',
    alertMsg: '',
    required: '0',
    bindFunction: function (row, data) {
        console.log('row', row)
        console.log('data', data)
    },
    extend: {
        modalColumns: [{
            field: 'taxCode',
            title: '税码',
            with: 150
        },
        {
            field: 'taxRate',
            title: '税率（%）',
            with: 150
        },
        {
            field: 'taxName',
            title: '税码名称',
            with: 150
        },
        {
            field: 'remark',
            title: '备注',
            with: 150
        }
        ],
        modalUrl: '/base/tax/list',
        modalParams: {}
        // beforeCheckedCallBack: function (parentRef, pageData, groupData, form) {
        //     return new Promise((resolve, rejec) => {
        //         let orderStatus = form.orderStatus || ''
        //         console.log('orderStatus', orderStatus)
        //         return orderStatus === '0' ? resolve('success') : rejec('只允许操作新建状态单据')
        //     })
        // }
    },
    placeholder: '',
    slots: {
        default: ({ row, rowIndex, column, columnIndex }, h) => {
            const scopedSlots = {
                default: ({ openFunc }) => {
                    return (
                        <div style="position: relative;min-height: 30px;padding-right: 20px;">
                            {row[column.property]}
                            <a style="position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 10;">
                                <a-icon type="file-search" onClick={() => { openFunc && openFunc() }} />
                            </a>
                        </div>
                    )
                }
            }

            const props = {
                config: column._own || {},
                row: row,
                isRow: true
            }
            const on = {
                afterClearCallBack: (cb) => cb && cb(row, column._own, rowIndex, columnIndex),
                ok: (data) => { column._own.bindFunction && column._own.bindFunction(row, data) }
            }

            return [
                (<m-select-modal scopedSlots={scopedSlots} {...{ props, on }} />)
            ]
        }
    }
}