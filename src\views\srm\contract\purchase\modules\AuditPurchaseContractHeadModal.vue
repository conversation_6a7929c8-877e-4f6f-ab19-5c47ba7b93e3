<template>
  <div>
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :url="url"
      isAudit
      :pageData="pageData"
      @loadSuccess="handleLoadSuccess"
    />
    <a-modal
      v-drag
      v-model="previewModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <!-- <a-modal
                v-drag
                  centered
                  :width="960"
                  :maskClosable="false"
                  :visible="flowView"
                  @ok="closeFlowView"
                  @cancel="closeFlowView">
                  <iframe
                    style="width:100%;height:560px"
                    title=""
                    :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
                    frameborder="0"></iframe>
                </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
      v-drag
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <view-item-diff-modal ref="viewDiffModal"/>
    <His-Contract-Item-Modal ref="hisContractItemModal"/>
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {getAction, httpAction} from '@/api/manage'
import ViewItemDiffModal from './ViewItemDiffModal'
import HisContractItemModal from './HisContractItemModal'
import JEditor from '@comp/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'AuditPurchaseContractHeadModal',
    mixins: [DetailMixin],
    components: {
        flowViewModal,
        ViewItemDiffModal,
        HisContractItemModal,
        JEditor
    },
    data () {
        return {
            flowId: '',
            auditVisible: false,
            opinion: '',
            showRemote: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            currentRow: {},
            currentUrl: '',
            flowView: false,
            cancelAuditShow: false,
            currentBasePath: this.$variateConfig['domainURL'],
            previewContent: '',
            confirmLoading: false,
            previewModal: false,
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLineInfo`, '采购合同行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseContractItemList',
                            columns: []
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLibrary`, '采购合同条款库'),
                        groupCode: 'itemContentInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseContractContentItemList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'id',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectNumber`, '项目编号'),
                                    width: 150
                                },
                                {
                                    field: 'itemName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                                    width: 120
                                },
                                {
                                    field: 'itemType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                                    width: 120,
                                    dictCode: 'srmItemType'
                                },
                                {
                                    field: 'itemVersion',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_itemVersion`, '项目版本'),
                                    width: 120
                                },
                                {
                                    field: 'changeFlag',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeIdentification`, '变更标识'),
                                    width: 120,
                                    editRender: {
                                        name: '$switch',
                                        type: 'visible',
                                        props: {closeValue: '0', openValue: '1', disabled: true}
                                    }
                                },
                                {
                                    field: 'sourceType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'),
                                    width: 120,
                                    dictCode: 'srmContractContentSourceType'
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachmentList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 120
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    clickFn: this.download
                                },
                                {
                                    type: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preViewEvent
                                }
                            ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                        type: 'primary',
                        click: this.auditPass
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                        type: '',
                        click: this.auditReject
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                        type: '',
                        click: this.showFlow
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        type: '',
                        key: 'alias',
                        click: this.preview
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                        type: 'rollBack',
                        click: this.goBackAudit
                    }
                ]
            },
            url: {
                detail: '/contract/purchaseContractHead/queryById'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentRow.templateNumber
            let templateVersion = this.currentRow.templateVersion
            let elsAccount = this.currentRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            if (this.currentRow.contractType === '3') {
                return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_contractSimple_${templateNumber}_${templateVersion}.js?t=` + time
            }
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_contract_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },

    watch: {
        currentRow (obj) {
            if (obj.contractType && obj.contractType === '3') {
                this.pageData.groups.splice(0, 1)
            }
        }
    },
    methods: {
        preview () {
            let contentGrid = this.$refs.detailPage.$refs.purchaseContractContentItemList[0]
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.$refs.detailPage.confirmLoading = true
            getAction('/contract/purchaseContractHead/getPreviewData', {id: this.currentEditRow.id}).then((res) => {
                if (res.success) {
                    this.previewModal = true
                    this.previewContent = res.result
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        },
        handleLoadSuccess (res) {
            this.currentRow = res.res.result
            this.showRemote = true
            this.flowId = this.currentRow.flowId
            this.currentEditRow.rootProcessInstanceId = this.currentRow.flowId
        },
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass () {
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject () {
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk () {
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBackAudit()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {

            })
        },
        download (row) {
            this.$refs.detailPage.handleDownload(row)
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        }
    }
}
</script>
