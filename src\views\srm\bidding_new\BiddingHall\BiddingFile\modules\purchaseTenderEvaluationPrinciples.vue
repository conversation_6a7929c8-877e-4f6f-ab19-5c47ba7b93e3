<template>
  <div class="purchaseTenderEvaluationPrinciples">
    <a-form-model
      ref="form"
      :model="formData"
      :rules="validateRules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol">
      <a-row :getterr="12">
        <a-col
          :span="8"
          v-for="(item, index) in fields"
          :key="item.field + index">
          <template v-if="item.fieldType == 'input'">
            <a-form-model-item
              :label="item.title"
              :prop="item.field">
              <a-input
                v-model="formData[item.field]"
                :disabled="!isEdit"/>
            </a-form-model-item>
          </template>
          <template v-else-if="item.fieldType == 'select'">
            <a-form-model-item
              :label="item.title"
              :prop="item.field">
              <m-select
                v-model="formData[item.field]"
                :disabled="!isEdit"
                :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                :dict-code="item.dictCode"
              />
            </a-form-model-item>
          </template>
          <template v-else-if="item.fieldType == 'number'">
            <a-form-model-item
              :label="item.title"
              :prop="item.field">
              <a-input-number
                style="width:100%"
                :max="9999"
                :min="0"
                :disabled="!isEdit"
                v-model="formData[item.field]" />
            </a-form-model-item>
          </template>
        </a-col>
      </a-row>
    </a-form-model>
    <div class="radio-group">
      <titleTrtl>
        <div>{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_dict_MkztdLF_c7f8fe1d`, '汇总分计算规则')) }}</div>
      </titleTrtl>
      <div>
        <a-radio-group
          v-model="formData.summaryCalRules"
          :disabled="!isEdit"
          @change="onChangeSummaryCalRules">
          <a-radio
            :style="radioStyle"
            value="0">
            {{ $srmI18n(`${$getLangAccount()}#i18n_field_AFMkztdCKHcMkKWMdjULfzTR_11a1ac13`, '根据汇总分计算方式进行汇总时，取所有评委打分均值') }}
          </a-radio>
          <a-radio
            :style="radioStyle"
            value="1">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_AFMkztdCKHcMkKWMD_1781c032`, '根据汇总分计算方式进行汇总时，去掉') }}</span>
            <a-input-number
              :disabled="!isEdit"
              class="mar-5"
              :max="9999"
              :min="0"
              v-model="formData['highestScoreNumber']" />
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_mexzW_7e9b94c8`, '个最高分，') }}</span>
            <a-input-number
              :disabled="!isEdit"
              class="mar-5"
              :max="9999"
              :min="0"
              v-model="formData['lowestScoreNumber']" />
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_menzMTR_1b16385d`, '个最低分取均值') }}</span>
          </a-radio>
          <a-radio
            :style="radioStyle"
            :disabled="!isEdit"
            class="margin-top-6"
            value="2">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_AFMkztdCKHcMkKWtmULUzOvdjULUzTRW_2078c80a`, '根据汇总分计算方式进行汇总时，单个评委评分偏离所有评委评分均值±') }}</span>
            <a-input-number
              :disabled="!isEdit"
              class="mar-5"
              :max="9999"
              :min="0"
              v-model="formData['offBase']" />
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_WjMGWbUjUzTRLMkz_685334ff`, '%的去除，剩余的评分均值为汇总分') }}</span>
          </a-radio>
        </a-radio-group>
      </div>
    </div>
    <div
      class="margin-top-30 radio-group"
      v-if="showShortlistedRules">
      <titleTrtl>
        <div>{{ $srmI18n(`${$getLangAccount()}#i18n_field_NLLF_26576344`, '入围规则') }}:</div>
      </titleTrtl>
      <div>
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_jzARP_69d22b43`, '得分排名前') }}</span>
        <a-input-number
          class="mar-5"
          :disabled="!isEdit"
          :min="0"
          :max="9999"
          v-model="formData['scoreRanking']" />
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_R_540d`, '名') }}</span>
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_Mksz_32ad7d23`, '汇总得分') }}</span>
        <a-input-number
          class="mar-5"
          :disabled="!isEdit"
          :max="9999"
          :defaultValue="1"
          :min="1"
          v-model="formData['summaryScore']" />
      </div>
      <div>
        <a-radio-group
          :disabled="!isEdit"
          v-model="formData.shortlistedRules">
          <a-radio
            :style="radioStyle"
            value="0">
            {{ $srmI18n(`${$getLangAccount()}#i18n_field_BnPVjeBtLbxNL_9ef1f1ba`, '符合要求的投标单位全部入围') }}
          </a-radio>
          <a-radio
            :style="radioStyle"
            value="1">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_jzARP_69d22b43`, '得分排名前') }}</span>
            <a-tag
              color="cyan"
              class="mar-5"
            >{{ formData['scoreRanking'] }}</a-tag>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_RjeBtLNL_a96248b0`, '名的投标单位入围') }}</span>
          </a-radio>
          <a-radio
            :style="radioStyle"
            class="margin-top-6"
            value="2">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_Mksz_32ad7d23`, '汇总得分') }}</span>
            <a-tag
              color="cyan"
              class="mar-5"
            >{{ formData['summaryScore'] }}</a-tag>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_ztIXjeBtLNL_f5f973f4`, '分及以上的投标单位入围') }}</span>
          </a-radio>
          <a-radio
            :style="radioStyle"
            class="margin-top-6"
            value="3">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_jzARP_69d22b43`, '得分排名前') }}</span>
            <a-tag
              color="cyan"
              class="mar-5"
            >{{ formData['scoreRanking'] }}</a-tag>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_RjeBtLFMksz_40d36f16`, '名的投标单位且汇总得分') }}</span> 
            <a-tag
              color="cyan"
            >{{ formData['summaryScore'] }}</a-tag>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_ztIXjeBtLNL_f5f973f4`, '分及以上的投标单位入围') }}</span>
          </a-radio>
          <a-radio
            :style="radioStyle"
            class="margin-top-6"
            value="4">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_jzARP_69d22b43`, '得分排名前') }} </span>
            <a-tag
              color="cyan"
              class="mar-5"
            >{{ formData['scoreRanking'] }}</a-tag>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_RjeBtLSMksz_5ac5b218`, '名的投标单位或汇总得分') }}</span>
            <a-tag
              color="cyan"
              class="mar-5"
            >{{ formData['summaryScore'] }}</a-tag>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_ztIXjeBtLNL_f5f973f4`, '分及以上的投标单位入围') }}</span>
          </a-radio>
        </a-radio-group>
      </div>
    </div>
  </div>
</template>
<script>
import titleTrtl from '../../components/title-crtl'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import { valitNumberLength } from '@views/srm/bidding_new/utils/index'

export default {
    mixins: [baseMixins],
    name: 'PurchaseTenderEvaluationPrinciples',
    props: {
        purchaseTenderEvaluationPrinciples: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        fromSourceData: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        pageStatus: {
            default: '',
            type: String
        }
    },
    components: {
        titleTrtl
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        showShortlistedRules () {
            console.log(this.processType, this.checkTypeValue, this.$ls.get('changeBidFile'), this.currentSubPackage().processType)
            // 预审和二步法时候显示入围规则
            if ((this.processType == '1' || this.checkTypeValue == '0') || (this.$ls.get('changeBidFile') && this.currentSubPackage().processType == '1')|| (this.$ls.get('changeBidFile') && this.checkTypeValue == '0')) {
                return true
            }
            return false
        }
    },
    data () {
        let checkNumber = (rule, value, callback) => {
            if (value) {
                if (!valitNumberLength(value, 8)){
                    callback(new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '8'))
                }
            }
            callback()
        }
        return {
            checkTypeValue: '',
            labelCol: { span: 9 },
            wrapperCol: { span: 15 },
            formData: {
                reviewOrder: '',
                summaryCalType: '',
                reviewSummaryRules: '',
                summaryScorePrecision: '',
                summaryCalRules: '',
                highestScoreNumber: '',
                lowestScoreNumber: '',
                offBase: '',
                shortlistedRules: '',
                scoreRanking: '',
                summaryScore: ''
            },
            radioStyle: {
                display: 'block',
                height: '30px',
                lineHeight: '30px'
            },
            fields: [
                // {
                //     title: '评标办法',
                //     fieldLabelI18nKey: '',
                //     field: 'evaluationType',
                //     defaultValue: '',
                //     fieldType: 'select',
                //     dictCode: 'tenderEvaluationMethod'
                // },
                // {
                //     title: '推荐候选家数',
                //     fieldLabelI18nKey: '',
                //     field: 'candidatesNumber',
                //     defaultValue: '',
                //     fieldType: 'number'
                // },
                {
                    title: '评审顺序',
                    fieldLabelI18nKey: '',
                    field: 'reviewOrder',
                    defaultValue: '',
                    fieldType: 'select',
                    dictCode: 'tenderReviewOrder'
                },
                {
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUMkLF_bdfe6a86`, '评审汇总规则')),
                    fieldLabelI18nKey: '',
                    field: 'reviewSummaryRules',
                    defaultValue: '',
                    fieldType: 'select',
                    dictCode: 'tenderReviewSummaryRules'
                },  
                {
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MktdCK_51f059e0`, '汇总计算方式')),
                    fieldLabelI18nKey: '',
                    field: 'summaryCalType',
                    defaultValue: '',
                    fieldType: 'select',
                    dictCode: 'tenderSummaryCalType'
                },
                
                {
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MkzROz_3720c5f2`, '汇总分值精度')),
                    fieldLabelI18nKey: '',
                    field: 'summaryScorePrecision',
                    defaultValue: '',
                    fieldType: 'select',
                    dictCode: 'tenderSummaryScorePrecision'
                }
            ],
            validateRules: {
                evaluationType: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNUBrh_c8415fe3`, '请输入评标办法'), trigger: 'blur' }],
                candidatesNumber: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNYISiuW_cbb22bdb`, '请输入推荐候选家数'), trigger: 'blur' },
                    { validator: checkNumber, trigger: 'change' }
                ],
                reviewOrder: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNUUkT_c81b3c5b`, '请输入评审顺序'), trigger: 'blur' }],
                reviewSummaryRules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNUUMkLF_1a3d562f`, '请输入评审汇总规则'), trigger: 'blur' }],
                summaryCalType: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNMktdCK_ae2f4589`, '请输入汇总计算方式'), trigger: 'blur' }],
                summaryScorePrecision: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNMkzROz_935fb19b`, '请输入汇总分值精度'), trigger: 'blur' }]
            }
        }
    },
    methods: {
        onChangeSummaryCalRules () {
            this.$set(this.formData, 'highestScoreNumber', '')
            this.$set(this.formData, 'lowestScoreNumber', '')
            this.$set(this.formData, 'offBase', '')
        },
        getValidatePromise () {
            return this.$refs.form.validate()
        },
        externalAllData () {
            let params = Object.assign({}, this.formData)
            return params
        },
        init (param) {
            this.formData = param?.purchaseTenderEvaluationPrinciples || {}
        }
    },
    mounted () {
        this.formData = this.purchaseTenderEvaluationPrinciples || {}
        console.log('@3', this.fromSourceData)
        this.checkTypeValue = this.fromSourceData?.checkType || this.checkType
    }
}
</script>
<style lang="less" scoped>
.margin-top-6{
  margin-top: 6px;
}
.margin-top-30{
  margin-top: 30px;
}
.radio-group{
  padding-left: 10px;
}
.mar-5{
  margin: 0 5px
}
</style>


