<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form
        layout="inline"
        @keyup.enter.native="searchQuery"
      >
        <a-row :gutter="24">
          <a-col
            :md="6"
            :sm="8"
          >
            <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateCODE`, '模板CODE')">
              <a-input
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputTemplateCode`, '请输入模板CODE')"
                v-model="queryParam.templateCode"
              />
            </a-form-item>
          </a-col>
          <a-col
            :md="6"
            :sm="8"
          >
            <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateContent`, '模板内容')">
              <a-input
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputTemplateContent`, '请输入模板内容')"
                v-model="queryParam.templateContent"
              />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col
              :md="6"
              :sm="8"
            >
              <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateTitle`, '模板标题')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputTemplateTitle`, '请输入模板标题')"
                  v-model="queryParam.templateName"
                />
              </a-form-item>
            </a-col>
            <a-col
              :md="6"
              :sm="8"
            >
              <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateType`, '模板类型')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_inputTemplateType`, '请输入模板类型')"
                  v-model="queryParam.templateType"
                />
              </a-form-item>
            </a-col>
          </template>
          <a-col
            :md="6"
            :sm="8"
          >
            <span
              style="float: left;overflow: hidden;"
              class="table-page-search-submitButtons"
            >
              <a-button
                type="primary"
                @click="searchQuery"
                icon="search"
              >{{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}</a-button>
              <a-button
                type="primary"
                @click="searchReset"
                icon="reload"
                style="margin-left: 8px"
              >{{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}</a-button>
              <a
                @click="handleToggleSearch"
                style="margin-left: 8px"
              >
                {{ toggleSearchStatus ? $srmI18n(`${$getLangAccount()}#i18n_title_putAway`, '收起') : $srmI18n(`${$getLangAccount()}#i18n_title_open`, '展开') }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div
      class="table-operator"
      style="text-align:right">
      <a-button
        @click="handleAdd"
        type="primary"
        icon="plus"
      >
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}
      </a-button>
      <a-button
        type="primary"
        icon="download"
        @click="handleExportXls($srmI18n(`${$getLangAccount()}#i18n_title_messageTemplate`, '消息模板'))"
      >
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_Export`, '导出') }}
      </a-button>
      <a-upload
        name="file"
        :show-upload-list="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        @change="handleImportExcel"
      >
        <a-button
          type="primary"
          icon="import"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_Improt`, '导入') }}
        </a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item
            key="1"
            @click="batchDel"
          >
            <a-icon type="delete" />
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_batchOperation`, '批量操作') }}
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div
        class="ant-alert ant-alert-info"
        style="margin-bottom: 16px;"
      >
        <em class="anticon anticon-info-circle ant-alert-icon" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_selected`, '已选择') }} <a style="font-weight: 600">{{
          selectedRowKeys.length }}</a>{{ $srmI18n(`${$getLangAccount()}#i18n_title_term`, '项') }}
        <a
          style="margin-left: 24px"
          @click="onClearSelected"
        >{{ $srmI18n(`${$getLangAccount()}#i18n_title_clearALL`, '清空') }}</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        row-key="id"
        :columns="columns"
        :data-source="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :row-selection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange"
      >
        <!-- 字符串超长截取省略号显示-->
        <span
          slot="templateContent"
          slot-scope="text"
        >
          <j-ellipsis
            :value="text"
            :length="25"
          />
        </span>


        <span
          slot="action"
          slot-scope="text, record"
        >
          <a @click="handleEdit(record)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑') }}</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">{{ $srmI18n(`${$getLangAccount()}#i18n_title_more`, '更多') }} <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a-popconfirm
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_deleteTips`, '确定删除吗?')"
                  @confirm="() => handleDelete(record.id)"
                >
                  <a>{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a>
                </a-popconfirm>
              </a-menu-item>
              <a-menu-item>
                <a @click="handleTest(record)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_sendTest`, '发送测试') }}</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <sysMessageTemplate-modal
      ref="modalForm"
      @ok="modalFormOk"
    />

    <sysMessageTest-modal ref="testModal" />
  </a-card>
</template>

<script>
import SysMessageTemplateModal from './modules/SysMessageTemplateModal'
import SysMessageTestModal from './modules/SysMessageTestModal'
import {elsListMixin} from '@/mixins/elsListMixin'
import JEllipsis from '@/components/els/JEllipsis'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    name: 'SysMessageTemplateList',
    mixins: [elsListMixin],
    components: {
        JEllipsis,
        SysMessageTemplateModal,
        SysMessageTestModal
    },
    data () {
        return {
            description: srmI18n(`${getLangAccount()}#i18n_title_msgTemplateManagementPage`, '消息模板管理页面'),
            // 表头
            columns: [
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号'),
                    dataIndex: '',
                    key: 'rowIndex',
                    width: 60,
                    align: 'center',
                    customRender: function (t, r, index) {
                        return parseInt(index) + 1
                    }
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_templateCODE`, '模板CODE'),
                    align: 'center',
                    dataIndex: 'templateCode'
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_templateTitle`, '模板标题'),
                    align: 'center',
                    dataIndex: 'templateName'
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_templateContent`, '模板内容'),
                    align: 'center',
                    dataIndex: 'templateContent',
                    scopedSlots: {customRender: 'templateContent'}
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_templateType`, '模板类型'),
                    align: 'center',
                    dataIndex: 'templateType',
                    customRender: function (text) {
                        if(text=='1') {
                            return srmI18n(`${getLangAccount()}#i18n_title_message`, '短信')
                        }
                        if(text=='2') {
                            return srmI18n(`${getLangAccount()}#i18n_title_mail`, '邮件')
                        }
                        if(text=='3') {
                            return srmI18n(`${getLangAccount()}#i18n_title_weChat`, '微信')
                        }
                        if(text=='4') {
                            return srmI18n(`${getLangAccount()}#i18n_title_internalMsg`, '站内消息')
                        }
                    }
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
                    dataIndex: 'action',
                    align: 'center',
                    scopedSlots: {customRender: 'action'}
                }
            ],
            url: {
                list: '/message/sysMessageTemplate/list',
                delete: '/message/sysMessageTemplate/delete',
                deleteBatch: '/message/sysMessageTemplate/deleteBatch',
                exportXlsUrl: 'message/sysMessageTemplate/exportXls',
                importExcelUrl: 'message/sysMessageTemplate/importExcel'
            }
        }
    },
    computed: {
        importExcelUrl: function () {
            return `${this.$variateConfig['domainURL']}/${this.url.importExcelUrl}`
        }
    },
    methods: {
        handleTest (record){
            this.$refs.testModal.open(record)
            this.$refs.testModal.title = srmI18n(`${getLangAccount()}#i18n_title_sendTest`, '发送测试')
        }

    }
}
</script>
<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 3px
  }

  .ant-card-body .table-operator {
    margin-bottom: 18px;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .anty-row-operator button {
    margin: 0 5px
  }

  .ant-btn-danger {
    background-color: #ffffff
  }

  .ant-modal-cust-warp {
    height: 100%
  }

  .ant-modal-cust-warp .ant-modal-body {
    height: calc(100% - 110px) !important;
    overflow-y: auto
  }

  .ant-modal-cust-warp .ant-modal-content {
    height: 90% !important;
    overflow-y: hidden
  }
</style>