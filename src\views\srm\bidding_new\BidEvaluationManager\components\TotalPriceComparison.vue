<template>
  <!-- 价格评分 -->
  <div class="ReviewItems">
    <a-spin :spinning="confirmLoading">
      <div class="review-items-top">
        <a-row>
          <a-col :span="12">
            <span>{{ currentRow.title }}</span>
          </a-col>
          <a-col
            :span="12"
            style="text-align: right;">
            <a-button
              type="primary"
              v-if="currentRow['editStatus']"
              @click="calculation">{{ $srmI18n(`${$getLangAccount()}#i18n_field_td_116416`, '计算') }}</a-button>
            <a-button
              type="primary"
              @click="submitData"
              v-if="currentRow['editStatus']"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
            <a-button
              @click="goBack"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </a-col>
          <a-col :span="24">
            <span>排名规则: {{ priceRegulationInfo.rankingRules_dictText }}</span>
          </a-col>
        </a-row>
      </div>
      <div class="review-items-grid">
        <div class="grid-ul">
          <ul>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位') }}</li>
            <li
              :key="review.id"
              v-for="review in reviewList">{{ review.name }}</li>
          </ul>
          <ul
            class="ul"
            v-for="supplier in resultData.supplierList"
            :key="supplier.supplierAccount">
            <li v-if="supplierInvalidMap[supplier.supplierAccount] == '1'">{{ supplier.supplierName }}({{ $srmI18n(`${$getLangAccount()}#i18n_alert_IuB_16c86ba`, '已废标') }})</li>
            <li v-else>{{ supplier.supplierName }}</li>
            <template v-if="resultData.evaResultListMap">
              <template v-if="resultData.evaResultListMap[supplier.supplierAccount]">
                <template v-for="review in resultData.evaResultListMap[supplier.supplierAccount]">
                  <li
                    v-for="(field, i) in reviewList"
                    :key="review.regulationId + field.id + i">
                    <a-input
                      :addon-after="$srmI18n(`${$getLangAccount()}#i18n_field_j_5143`, '元')"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNumciR_e72db299`, '请输入价格修正值')"
                      type="number"
                      :disabled="!currentRow['editStatus'] || review.invalid == '1'"
                      style="width: 50%;margin-top: 2px;"
                      v-if="field.code == 'priceCorrection'"
                      v-model="review.priceCorrection" />
                    <span v-else>{{ review[field.code] }}<span v-if="field.code == 'weight'">%</span></span>
                  </li>
                </template>
              </template>
              <template v-else>
                <li
                  v-for="(item, i) in reviewList"
                  :key="item + i"><span>/</span></li>
              </template>
            </template>
          </ul>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { gridOptionsMixin } from '../public/gridOptionsMixin.js'

export default {
    mixins: [gridOptionsMixin],
    props: {
        currentRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        headParams () {
            let {checkType, currentStep, processType } = this.currentRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return {xNodeId}
        }
    },
    data () {
        return {
            calculateStatus: false,
            confirmLoading: false,
            resultData: {},
            supplierInvalidMap: {},
            priceRegulationInfo: {},
            reviewList: [
                {id: 1, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBsu_2e62dc84`, '投标报价'), code: 'quote'},
                {id: 2, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umci_25769c1a`, '价格修正'), code: 'priceCorrection'},
                {id: 3, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBu_2199294`, '评标价'), code: 'evaPrice'},
                // {id: 4, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tru_154776b`, '基准价'), code: 'basePrice'},
                {id: 5, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_esAR_30ba8883`, '最终排名'), code: 'orderBy'}
            ]
        }
    },
    async created () {
        await this.getData()
    },
    methods: {
        setSupplierInvalidMap (item = null) {
            this.resultData.supplierList.map(supplier => {
                if (item) {
                    if (supplier.supplierAccount == item.supplierAccount) this.$set(this.supplierInvalidMap, supplier.supplierAccount, item.invalid)
                } else {
                    this.$set(this.supplierInvalidMap, supplier.supplierAccount, supplier.invalid)
                }
            })
        },
        // 获取评审项的数据 ?evaGroupId=1518091956243259394
        async getData () {
            const {evaGroupId, id} = this.currentRow
            const params = {
                evaGroupId: evaGroupId,
                judgesTaskItemId: id
            }
            this.confirmLoading = true
            await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/querySupplierEvaGroupResultByGroupId', params, {headers: this.headParams}).then(res => {
                if (res.code == 200) {
                    const {result = {} } = res
                    this.resultData = result
                    this.priceRegulationInfo = result.evaluationGroupVO.tenderEvaluationTemplatePriceRegulationInfo
                    this.setSupplierInvalidMap()
                }else{
                    this.$message.error(res.message)
                }
                this.confirmLoading = false
            })
        },
        // 计算
        calculation (needMessage = true) {
            const {evaResultListMap={}} = this.resultData
            const keys = evaResultListMap && Object.keys(evaResultListMap)
            const regulationCalList = keys.map(item => {
                return evaResultListMap[item][0]
            })
            let params = regulationCalList
            this.confirmLoading = true
            return postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/priceScoreCalculation', params, {headers: this.headParams}).then(res => {
                this.confirmLoading = false
                if (res.code == 200) {
                    const {result = []} = res
                    result.forEach(item => {
                        this.resultData.evaResultListMap[item.supplierAccount] = [item]
                        this.setSupplierInvalidMap(item)
                    })
                    // this.resultData.supplierList = result || []
                    this.calculateStatus = true
                    needMessage && this.$message.success(res.message)
                } else {
                    this.$message.error(res.message)
                }
                return res
            })
        },
        // 提交数据
        async submitData () {
            if (!this.calculateStatus) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWHctdW_eaf6fea9`, '请先进行计算！'))
                return false
            }
            let {success} = await this.calculation(false)
            if (!success) return false
            this.resultData.evaResultListMap && (() => {
                const evaObj = this.resultData.evaResultListMap
                Object.keys(evaObj).forEach(key => {
                    evaObj[key].forEach(item => {
                        item.judgesTaskItemId = this.currentRow.id
                        item.judgesTaskHeadId = this.currentRow.judgesTaskHeadId
                    })
                })
            })()
            this.resultData.supplierList && this.resultData.supplierList.forEach(data => {
                data['judgesTaskItemId'] = this.currentRow.id || ''
            })

            this.confirmLoading = true
            postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/publishSupplierEvaGroupResult', this.resultData, {headers: this.headParams}).then(res => {
                this.confirmLoading = false
                if (res.code == 200) {
                    this.$message.success(res.message)
                    this.goBack()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 返回
        goBack () {
            this.$emit('goBack')
        }
    }
}
</script>

<style lang="less" scoped>
  .ReviewItems {
    height: 100%;
    background-color: #fff;
    padding: 15px 10px;
  }
  .review-items-top {
    line-height: 30px;

    .ant-col-12:nth-child(1) {
      font-size: 16px;
      font-weight: 600;
    }
    .ant-col-24 {
      background-color: #E6F7FF;
      padding: 5px 10px;
      margin-top: 5px;
      border-radius: 5px;
      color: #02A7F0;

      span {
        display: block;
        font-size: 12px;
        height: 20px;
        line-height: 20px;
      }
    }
  }
  .review-items-grid {
    margin-top: 15px;

    .grid-ul {
      display: flex;

      ul:nth-child(1) {
        width: 90px;
      }
      ul {
        text-align: center;

        li:nth-child(1) {
          background-color: #e4e4e4;
        }
        li {
          border: 1px solid #f0eeee;
          height: 40px;
          line-height: 40px;
        }
      }
      .ul {
        flex-grow: 1;
      }

    }
  }
</style>
