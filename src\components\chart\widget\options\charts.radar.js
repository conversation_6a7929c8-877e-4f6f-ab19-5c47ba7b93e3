import {textColor} from '@comp/chart/widget/utils/theme.js'
// 雷达图
export default {
    backgroundColor: 'rgba(255, 255, 255, 0)',
    title: {
        show: true,
        text: '',
        top: 10,
        left: 'center',
        textStyle: {
            color: textColor,
            fontWeight: 'normal',
            fontSize: 18
        },
        subtext: '',
        subtextStyle: {
            color: textColor,
            fontWeight: 'normal',
            fontSize: 16
        }
    },
    legend: {
        show: true,
        left: 'left',
        orient: 'horizontal',
        textStyle: {
            color: textColor,
            fontSize: 10
        }
    },
    tooltip: {
        show: true,
        textStyle: {
            color: '#000000',
            fontWeight: 'normal',
            fontSize: 14
        }
    },
    color: ['#83bff6', '#23B7E5', '#61a0a8', '#d48265', '#91c7ae'],
    radar: {
        radius: ['0%', '60%'],
        customRadius: [0, 60], // 自定义属性 - 外内圈半径
        customOpacity: 0.5,    // 自定义属性 - 区域透明度
        indicator: []
    },
    series: [
        {
            type: 'radar',
            data: []
        }
    ]
}