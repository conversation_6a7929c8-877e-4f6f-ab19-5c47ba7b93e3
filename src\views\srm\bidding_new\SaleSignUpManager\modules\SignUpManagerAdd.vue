<template>
  <div class="SignUpManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="layoutShow"
        :ref="businessRefName"
        :currentEditRow="currentEditData"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :fromSourceData="fromSourceData"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    name: 'SignUpManagerEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    data () {
        return {
            pageStatus: 'edit',
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            // externalToolBar: {},
            pageFooterButtons: [

                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectSignUp/add'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    click: this.handleSave
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectSignUp/submit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish2',
                    // showMessage: true
                    click: this.handleSubmit
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            attachmentListData: {},
            url: {
                queryById: '/tender/sale/supplierTenderProjectSignUp/queryById',
                queryProjectInfo: '/tender/purchaseTenderProjectHead/queryProjectInfo',
                submit: '/tender/sale/supplierTenderProjectSignUp/submit',
                add: '/tender/sale/supplierTenderProjectSignUp/add',
                edit: '/tender/sale/supplierTenderProjectSignUp/edit'
            },
            userInfo: {},
            projectObj: {},
            currentEditData: {},
            fromSourceData: {},
            layoutShow: false,
            remoteJsFilePath: ''
        }
    },
    props: {
        queryData: {
            type: Object,
            default () {
                return {}
            }
        },
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        externalToolBar () {
            return {
                attachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            disabledItemNumber: true,
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'SupplierTenderProjectSignUp', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            }
        }
    },
    mounted () {
        this.getBusinessTemplate()
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditData.tenderProjectNumber || this.currentEditData.id || '',
                actionRoutePath: '采购商与供应商的路径逗号隔开,/bidder/SignUpManagerList'
            }
        },
        // 获取业务模板信息
        getBusinessTemplate () {
            let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'SupplierTenderProjectSignUp'}
            this.confirmLoading = true
            getAction('/template/templateHead/getListByType', params).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                templateNumber: item.templateNumber,
                                templateName: item.templateName,
                                templateVersion: item.templateVersion,
                                templateAccount: item.elsAccount
                            }
                        })
                        this.currentEditData = options[0]
                        this.remoteJsFilePath = `${this.currentEditData['templateAccount']}/sale_SupplierTenderProjectSignUp_${this.currentEditData['templateNumber']}_${this.currentEditData['templateVersion']}`
                        this.getData()
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleSubmitBefore (args) {
            return new Promise((resolve) => {
                let allData = args.allData
                allData = Object.assign(allData, this.currentEditData)
                let params = {
                    allData: allData
                }
                args = Object.assign({}, args, params)
                resolve(args)
            })
        },
        uploadCallBack (result, ref) {
            result.forEach(res => {
                res['fileType'] = null
                res['fileType_dictText'] = null
            })
            let fileGrid = this.getItemGridRef(ref)
            fileGrid.insertAt(result, -1)
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')

                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        async handleAfterDealSource (pageConfig, resultData) {
            let {consortiumBidding} = this.queryData
            console.log(11, consortiumBidding)
            if(consortiumBidding == '0'){
                pageConfig.groups[0].formModel.combination='0'
            }
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }
            let flag = (consortiumBidding == '0')
            setDisabledByProp('combination', flag)
            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {

                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }


                rule[prop] = [{
                    required: !flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            let validateFlag = (consortiumBidding == '0')
            setValidateRuleByProp('combination', validateFlag)

            pageConfig.groups[1]['extend'] = {
                optColumnList: [
                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                    { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                ]
            }

            let that = this
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            // 上传 附件需要 headId
            that.externalToolBar['attachmentList'][0].args.headId = resultData.id || ''
            that.externalToolBar['attachmentList'][0].args.itemInfo = itemInfo
        },
        checkItemSelectOk (data) {
            console.log(data)
        },
        preViewEvent (Vue, row){
            row.subpackageId = this.queryData.subpackageId || this.currentEditRow.subpackageId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.queryData.subpackageId || this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleSave () {
            const {templateAccount='', templateName='', templateNumber='', templateVersion=1, id=''} = this.currentEditData
            let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, {templateAccount, templateName, templateNumber, templateVersion, id})
            // let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, this.currentEditData)
            params['consortiumBidding'] = this.queryData.consortiumBidding || ''
            let url = params.id ? this.url.edit : this.url.add
            params['noticeId'] =  this.queryData.businessId || ''
            this.confirmLoading = true
            postAction(url, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.currentEditData.id = res.result.id
                    this.externalToolBar['attachmentList'][0].args.headId = res.result.id || ''
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleSubmit () {
            const pageConfig = this.$refs[this.businessRefName].extendAllData()
            // 校验数据
            this.stepValidate(pageConfig).then(() => {
                // let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, this.currentEditData)
                const {templateAccount='', templateName='', templateNumber='', templateVersion=1, id=''} = this.currentEditData
                let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, {templateAccount, templateName, templateNumber, templateVersion, id})
                params['consortiumBidding'] = this.queryData.consortiumBidding || ''

                params['noticeId'] =  this.queryData.businessId || ''
                this.confirmLoading = true

                postAction('/tender/sale/supplierTenderProjectSignUp/submit', params).then(res => {
                    let type = res.success ? 'success': 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        this.$parent.showAddPage = false
                        this.$store.dispatch('SetTabConfirm', false)
                        this.$parent.searchEvent(false)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
                // postAction(url, params).then(res => {
                //     let type = res.success ? 'success': 'error'
                //     this.$message[type](res.message)
                //     if (res.success) {
                //         params['id'] = res.result.id
                //         postAction('/tender/sale/supplierTenderProjectSignUp/submit', params).then(res => {
                //             let type = res.success ? 'success': 'error'
                //             this.$message[type](res.message)
                //             if (res.success) {
                //                 this.$parent.showAddPage = false
                //                 this.$store.dispatch('SetTabConfirm', false)
                //                 this.$parent.searchEvent(false)
                //             }
                //         })
                //     }
                // }).finally(() => {
                //     this.confirmLoading = false
                // })
            }, error => {
                console.log('最后有一个没有填', error)
            })

        },
        getData () {
            let url = ''
            let params = {}
            let cb = null
            if (this.currentEditData.id) {
                url = this.url.queryById
                params = {id: this.currentEditData.id}
                cb = (data = {}) => {
                    this.fromSourceData = data
                    this.currentEditData = Object.assign(data, this.currentEditData)
                }
            } else {
                url = this.url.queryProjectInfo
                params = {subpackageId: this.queryData.subpackageId}
                cb = (data = {}) => {
                    this.fromSourceData = data
                    this.currentEditData = Object.assign(data, this.currentEditData)
                    let {elsAccount} = this.$ls.get(USER_INFO)
                    this.fromSourceData['supplierName'] = this.$ls.get(USER_COMPANYSET).companyName
                    this.fromSourceData['supplierAccount'] = elsAccount
                }
            }

            this.confirmLoading = true
            getAction(url, params).then(res => {
                if (res.success) {
                    if (res.result) {
                        cb(res.result)
                    }
                }
            }).finally(() => {
                this.layoutShow = true
                this.confirmLoading = false
            })
        }
    }
}
</script>

<style lang="less" scoped>
.registration-title{
    background-color: #eee;
    padding: 5px 10px;
    margin-bottom: 10px;
}
:deep(.page-container .edit-page .page-content){
    flex: auto;
}
</style>


