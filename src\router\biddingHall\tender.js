const TenderRouter = [
    {
        path: '/tenderHall/tender/SignUpManagerEdit',
        name: 'SignUpManagerEdit',
        meta: {
            title: '报名管理',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/SignUpManagerEdit')
    },
    {
        path: '/tenderHall/tender/PrePurchaseBidManagerEdit',
        name: 'PrePurchaseBidManagerEdit',
        meta: {
            title: '资格预审购标管理',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/PurchaseBidManagerEdit')
    },
    {
        path: '/tenderHall/tender/PurchaseBidManagerEdit',
        name: 'PurchaseBidManagerEdit',
        meta: {
            title: '购标管理',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/PurchaseBidManagerEdit')
    },
    {
        path: '/tenderHall/tender/PreDocumentSubmissionEdit',
        name: 'PreDocumentSubmissionEdit',
        meta: {
            title: '资格预审文件递交',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/DocumentSubmissionEdit')
    },
    {
        path: '/tenderHall/tender/DocumentSubmissionEdit',
        name: 'DocumentSubmissionEdit',
        meta: {
            title: '文件递交',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/DocumentSubmissionEdit')
    },
    {
        path: '/tenderHall/tender/PerDocumentSubmissionEdit',
        name: 'PertrialDocumentSubmissionEdit',
        meta: {
            title: '预审文件递交',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/DocumentSubmissionEdit')
    },
    {
        path: '/tenderHall/tender/PreSupplierClarifyAndQuestions',
        name: 'PreSupplierClarifyAndQuestions',
        meta: {
            title: '供应商澄清答疑',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/BiddingHall/SupplierClarifyAndQuestions/SupplierClarifyAndQuestions')
    },
    {
        path: '/tenderHall/tender/SupplierClarifyAndQuestions',
        name: 'SupplierClarifyAndQuestions',
        meta: {
            title: '供应商澄清答疑',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/BiddingHall/SupplierClarifyAndQuestions/SupplierClarifyAndQuestions')
    },
    {
        path: '/tenderHall/tender/FirstSaleOpen',
        name: 'FirstSaleOpen',
        meta: {
            title: '第一步开标',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/BiddingHall/OpenTender/SaleOpen')
    },
    {
        path: '/tenderHall/tender/SecondSaleOpen',
        name: 'SecondSaleOpen',
        meta: {
            title: '第二步开标',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/BiddingHall/OpenTender/SaleOpen')
    },
    {
        path: '/tenderHall/tender/SaleOpen',
        name: 'SaleOpen',
        meta: {
            title: '开标',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/BiddingHall/OpenTender/SaleOpen')
    },
    {
        path: '/tenderHall/tender/TenderBidWinningNotice',
        name: 'TenderBidWinningNotice',
        meta: {
            title: '中标通知书',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/BiddingHall/BidWinning/TenderBidWinningNotice')
    },
    {
        path: '/tenderHall/tender/BidRejectionNotice',
        name: 'TenderBidRejectionNotice',
        meta: {
            title: '落标通知书',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/BiddingHall/BidWinning/TenderBidRejectionNotice')
    },
    {
        path: '/tenderHall/tender/PreBiddingFile',
        name: 'TenderPreBiddingFile',
        meta: {
            title: '资格预审文件',
            keepAlive: false
        },
        component: () => import(/* webpackChunkName: 'TenderPreBiddingFile' */ '@/views/srm/bidding_new/BiddingHall/BiddingFile/BiddingFile')
    },
    {
        path: '/tenderHall/tender/BiddingFile',
        name: 'TenderBiddingFile',
        meta: {
            title: '招标文件管理',
            keepAlive: false
        },
        component: () => import(/* webpackChunkName: 'TenderBiddingFile' */ '@/views/srm/bidding_new/BiddingHall/BiddingFile/BiddingFile')
    },
    {
        path: '/tenderHall/tender/TenderPreBiddingNotice',
        name: 'TenderPreBiddingNotice',
        meta: {
            title: '资格预审招标公告',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/TenderBiddingNotice')
    },
    {
        path: '/tenderHall/tender/TenderBiddingNotice',
        name: 'TenderBiddingNotice',
        meta: {
            title: '招标公告',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/TenderBiddingNotice')
    },
    {
        path: '/tenderHall/tender/TenderPreChangeNoticeList',
        name: 'TenderPreChangeNoticeList',
        meta: {
            title: '资格预审变更公告',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/TenderChangeNoticeList')
    },
    {
        path: '/tenderHall/tender/TenderChangeNoticeList',
        name: 'TenderChangeNoticeList',
        meta: {
            title: '变更公告',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/TenderChangeNoticeList')
    },
    {
        path: '/tenderHall/tender/MarginPaymentHome',
        name: 'MarginPaymentHome',
        meta: {
            title: '保证金管理',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/MarginManagement/MarginPaymentHome')
    },
    {
        path: '/tenderHall/tender/InvitationBid',
        name: 'TenderInvitationBid',
        meta: {
            title: '投标邀请',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/InvitationBid')
    },
    {
        path: '/tenderHall/tender/multipleQuotesList',
        name: 'multipleQuotesList',
        meta: {
            title: '多轮报价',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/multipleQuotes/multipleQuotesList')
    },
    {
        path: '/tenderHall/tender/finalQuoteList',
        name: 'finalQuoteList',
        meta: {
            title: '最终报价',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/TenderHall/finalQuote/finalQuoteList')
    },
    {
        path: '/tenderHall/tender/PreSaleBidEvaluationClarifyList',
        name: 'PreSaleBidEvaluationClarifyList',
        meta: {
            title: '预审评标澄清',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/SaleBidEvaluationClarify/SaleBidEvaluationClarifyList')
    },
    {
        path: '/tenderHall/tender/SaleBidEvaluationClarifyList',
        name: 'SaleBidEvaluationClarifyList',
        meta: {
            title: '评标澄清',
            keepAlive: false
        },
        component: () => import('@/views/srm/bidding_new/SaleBidEvaluationClarify/SaleBidEvaluationClarifyList')
    }
    
]

export default TenderRouter