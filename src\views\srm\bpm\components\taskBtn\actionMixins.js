import { invalidTask, completeActivitySignOnly, completeActivityOrNo, completeActivity, revokeStartActivity, withdrawalTask } from '../../api/analy'

export default {
    methods: {

        showConfirm (p) {
            let { title, content, fn } = p
            this.$confirm({
                title: title,
                content: content,
                onOk () {
                    fn()
                },
                onCancel () { }
            })
        },
        agreeAction () {
            this.setTask('agree')
            this.moadlName = 'agreeModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见'),
                visible: true
            }
        },
        passAction () {
            this.setTask('pass')
            this.moadlName = 'agreeModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见'),
                visible: true
            }
        },
        unPassAction () {
            this.setTask('unPass')
            this.moadlName = 'unPassModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MPFK_25d12cef`, '会签拒绝'),
                visible: true
            }
        },
        agreeAdHocSubProcessAction () {
            this.setTask('agreeAdHocSubProcess')
            this.moadlName = 'agreeModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见'),
                visible: true
            }
        },
        informedAction () {
            this.setTask('informed')
            this.moadlName = 'informedModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VZ_a2ae5`, '传阅'),
                visible: true
            }
        },
        transferTaskAction () {
            this.setTask('transferTask')
            this.moadlName = 'transferTaskModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办'),
                visible: true
            }
        },
        approvalHistoryAciton () {
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvalComments`, '审批意见'),
                visible: true,
                width: 1200,
                handleOk: () => {
                    this.modalForm.visible = false
                    this.task.action = ''
                }
            }
        },
        addNodeAcion () {
            this.setTask('addNode')
            this.moadlName = 'addNodeModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SuP_146056c`, '后加签'),
                width: 1200,
                visible: true
            }
        },
        addSignNodeAcion () {
            this.setTask('addSignNode')
            this.moadlName = 'addSignNodeModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JKMPyC_3561e25d`, '临时会签节点'),
                width: 1200,
                visible: true
            }
        },
        addSignAction () {
            this.setTask('addSign')
            this.moadlName = 'addSign'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PuP_13f6feb`, '前加签'),
                width: 1200,
                visible: true
            }
        },
        transactionUrgeAction () {
            this.setTask('transactionUrge')
            this.moadlName = 'transactionUrgeModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zKzr_353e55f7`, '特事特办'),
                visible: true
            }
        },
        backToNodeAction () {
            this.setTask('backToNode')
            this.moadlName = 'backToNodeModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_drawBack`, '退回'),
                visible: true
            }
        },
        fallBackAcion () {
            this.setTask('fallBack')
            this.moadlName = 'fallBackModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JKyCSuvK_7b93f7aa`, '临时节点添加历史'),
                width: 1200,
                visible: true
            }
        },
        getNextNodeFaWen () {
            this.setTask('flw_fawen_add')
            this.moadlName = 'flw_fawen_addModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见'),
                visible: true
            }
        },
        urgAcion () {
            this.setTask('urg')
            this.moadlName = 'urgAcionModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HrKd_25ec1e40`, '催办事项'),
                visible: true
            }
        },
        invalidTaskAction () {
            let p = {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_ku_9fac3`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                fn: () => {
                    invalidTask(this.taskInfo.taskId).then((response) => {
                        if (response.code == 200) {
                            this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                            setTimeout(() => {
                                this.back()
                            }, 300)
                        }
                    })
                }
            }
            this.showConfirm(p)
        },
        flowImageAction () {
            let processInstanceModelId = this.changeFlowId ? this.changeFlowId : ''
            let url = `/els/bpmns/view/tab.html?fireHoverAction=true&table=false&instId=${this.processInstanceId}&messageId=${this.tokenHeader}&processInstanceModelId=${processInstanceModelId}`
            this.flowImage.src = url
            this.moadlName = 'flowImageModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLP_1a93f54`, '流程图'),
                width: 1200,
                visible: true,
                handleOk: () => {
                    this.modalForm.visible = false
                    this.task.action = ''
                }
            }
        },
        headMapAction () {
            let t = new Date().getTime()
            let url = `/els/bpmns/head-map/index.html?processefinitionId=${this.processDefinitionId}&t=${t}&messageId=${this.tokenHeader}`
            this.flowImage.src = url
            this.moadlName = 'flowImageModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xvP_1b24170`, '热力图'),
                width: 1200,
                visible: true,
                handleOk: () => {
                    this.modalForm.visible = false
                    this.task.action = ''
                }
            }
        },
        completeActivityAction () {
            completeActivity(this.taskInfo.taskId).then((response) => {
                if (response.code == 0) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                    setTimeout(() => {
                        this.back()
                    }, 300)
                }
            })
        },
        flw_fawen_target_sign_onlyAction () {
            completeActivitySignOnly(this.taskInfo.taskId).then((response) => {
                if (response.code == 0) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                    setTimeout(() => {
                        this.back()
                    }, 300)
                }
            })
        },
        flw_fawen_target_onlyAction () {
            completeActivityOrNo(this.taskInfo.taskId).then((response) => {
                if (response.code == 0) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                    setTimeout(() => {
                        this.back()
                    }, 300)
                }
            })
        },
        revokeAcion () {
            let processInstanceId = this.processInstanceId
            let p = {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_withdraw`, '撤回'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n__KQqMtk_2f6334c0`, '是否撤回操作'),
                fn: () => {
                    let params = {
                        taskTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qMtk_2f124469`, '撤回操作'),
                        option: 'option',
                        operate: 'fetchBack',
                        processInstanceId
                    }
                    revokeStartActivity(params).then((response) => {
                        if (response.code == 200) {
                            this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                            setTimeout(() => {
                                this.back()
                            }, 300)
                        }
                    })
                }
            }
            this.showConfirm(p)
        },
        // 审批后下个节点未审批可进行撤回
        withdrawalTaskAcion () {
            debugger
            let processInstanceId = this.processInstanceId
            let p = {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_qMUz_2f113b32`, '撤回审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQqMUzVH_df8f79f7`, '是否撤回审批信息'),
                fn: () => {
                    let params = {
                        taskTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qMUztk_afbbbae1`, '撤回审批操作'),
                        option: 'option',
                        operate: 'fetchBack',
                        processInstanceId,
                        rootProcessInstanceId: processInstanceId
                    }
                    withdrawalTask(params).then((response) => {
                        if (response.code == 200) {
                            this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                            setTimeout(() => {
                                this.back()
                            }, 300)
                        } else {
                            this.$message.error(response.message)
                        }
                    })
                }
            }
            this.showConfirm(p)
        },
        // 前加签撤回
        withdrawalAddPreNodeAction () {
            this.setTask('backAddPre')
            this.moadlName = 'backAddPreNodeModal'
            this.modalForm = {
                ...this.modalFormDemo,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qMPuP_b2f1b6f1`, '撤回前加签'),
                visible: true
            }
        },
        setTask (type) {
            this.task.action = type
            this.task.actionName = type
        }
    }
}