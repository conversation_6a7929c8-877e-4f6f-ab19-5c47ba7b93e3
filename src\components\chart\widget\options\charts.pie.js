import {textColor} from '@comp/chart/widget/utils/theme.js'
//饼图
export default {
    backgroundColor: 'rgba(255, 255, 255, 0)',
    title: {
        show: true,
        text: '',
        top: 10,
        left: 'center',
        textStyle: {
            color: textColor,
            fontWeight: 'normal',
            fontSize: 18
        },
        subtext: '',
        subtextStyle: {
            color: textColor,
            fontWeight: 'normal',
            fontSize: 16
        }
    },
    legend: {
        show: true,
        left: 'left',
        orient: 'horizontal',
        textStyle: {
            color: '#000000',
            fontSize: 10
        }
    },
    tooltip: {
        show: true,
        textStyle: {
            color: '#000000',
            fontWeight: 'normal',
            fontSize: 14
        }
    },
    color: ['#83bff6', '#23B7E5', '#61a0a8', '#188df0', '#564AA3'],
    series: [
        {
            type: 'pie',
            radius: ['0%', '60%'],
            customRadius: [0, 60],
            label: {
                show: true,
                color: textColor,
                fontSize: 12,
                fontWeight: 'normal'
            },
            labelLine: {
                show: true
            },
            data: []
        }
    ]
}