<template>
  <div class="SupplierInfo">
    <div class="page-container">
      <detail-layout
        ref="detailPage"
        useLocalModelLayout
        modelLayout="unCollapse"
        :page-data="pageData"
        :current-edit-row="vuex_currentEditRow"
        :url="url"
      />
    </div>

    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />

  </div>
</template>

<script>
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'
import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    mixins: [
        DetailMixin
    ],
    data () {
        return {
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_participatingSuppliers`, '参与供应商'),
                        groupCode: 'supplierInfo',
                        type: 'grid',
                        custom: {
                            ref: 'biddingSupplierList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'),
                                    field: 'toElsAccount',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'),
                                    field: 'supplierCode',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                                    field: 'supplierName',
                                    width: 300
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'),
                                    field: 'replyStatus_dictText',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'),
                                    field: 'replyTime',
                                    width: 140
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                                    field: 'contacts',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'),
                                    field: 'phone',
                                    width: 120
                                },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mail`, '邮件'), field: 'email', width: 150 },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewBidsPermission`, '查看标书权限'),
                                    field: 'bidCheck_dictText',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidAuthority`, '投标权限'),
                                    field: 'bidQuote_dictText',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sourceType`, '来源类型'),
                                    field: 'sourceType_dictText',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    showOverflow: true,
                                    slots: { default: 'grid_opration' }
                                }
                            ],
                            optColumnList: [
                                {
                                    type: 'risk',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_suppliersRisk`, '供应商风险'),
                                    clickFn: this.handleRisk
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJitH_4a08df61`, '围标探测记录'),
                        groupCode: 'probeResultList',
                        type: 'grid',
                        custom: {
                            ref: 'probeResultList',
                            columns: [
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'supplierName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                                    width: 120
                                },
                                {
                                    field: 'probeResult',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果'),
                                    width: 500
                                },
                                {
                                    field: 'createTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                                    width: 150
                                },
                                {
                                    field: 'createBy',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'),
                                    width: 120
                                }
                            ],
                            showOptColumn: false
                        }
                    }
                ]
            },
            url: {
                detail: '/bidding/purchaseBiddingHead/queryById',
                submit: '/a1bpmn/audit/api/submit'
            }
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        fileSrc () {
            const {
                templateNumber = '',
                templateVersion = '',
                templateAccount = '',
                busAccount = ''
            } = this.vuex_currentEditRow || {}

            const configFiles = this.$variateConfig['configFiles']
            const time = +new Date()
            const url = `${configFiles}/${templateAccount || busAccount}/purchase_bidding_${templateNumber}_${templateVersion}.js?t=`+time
            return url
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleAfterDealSource (data) {
            if (data?.showProbeList == '0') {
                this.pageData.groups = this.pageData.groups.filter(v=>v.groupCode !== 'probeResultList')
            }
        },
        //查看风险
        handleRisk ({ toElsAccount }) {
            sessionStorage.setItem('cache_elsAccout', toElsAccount)
            this.$router.push({
                path: '/srm/base/SupplierVenture'
            })
        },
        beforeHandleData (data) {
            console.log('beforeHandleData', data)
            data.groups = []
        }
    }
}
</script>
