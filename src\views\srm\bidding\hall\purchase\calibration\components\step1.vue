<template>
  <div class="steps-content-warp step1">

    <div class="formWrap">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        v-bind="formItemLayout">
        <a-row>
          <a-col :span="12">
            <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_issueWinningAnnouncement`, '是否发布中标公告')">
              <m-switch v-model="form.bidWinNotice" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_announcementScope`, '中标公告范围')"
              ref="srmNoticeScope"
              prop="bidWinScope"
            >
              <m-select
                v-model="form.bidWinScope"
                dict-code="srmNoticeScope"
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectType`, '请选择类型')" 
                @change="(val) => {
                  $refs.srmNoticeScope.onFieldChange();
                  if (val == 4) {
                    showNoticeSupplier = true;
                  } else {
                    showNoticeSupplier = false;
                  }
                }"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_announcementStopTime`, '中标公告截止时间')"
              ref="bidWinEndTime"
              prop="bidWinEndTime"
            >
              <a-date-picker
                style="width:100%"
                format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                :show-time="{ defaultValue: moment('00:00:00', 'HH:mm:ss') }"
                v-model="form.bidWinEndTime"
              />
            </a-form-model-item>
          </a-col>
          <a-col
            :span="12"
            v-if="showNoticeSupplier">
            <a-form-model-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号')"
              ref="noticeSupplier"
              prop="noticeSupplier"
            >
              <m-select
                v-model="form.noticeSupplier"
                mode="multiple"
                :options="supplierOptions"
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectType`, '请选择类型')" 
                @change="() => {
                  $refs.srmNoticeScope.onFieldChange()
                }"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item
          :wrapper-col="{ span: 24 }"
          prop="bidWinContent">
          <div class="editor">
            <j-editor
              ref="editor"
              v-model="form.bidWinContent" />
          </div>
        </a-form-model-item>
        <a-row>
          <a-col :span="12">
            <a-form-model-item
              :label-col="{ span: 0 }">
              <custom-upload
                :disabledItemNumber="true"
                :visible.sync="upload.modalVisible"
                :title="upload.title"
                :action="upload.action"
                :accept="upload.accept"
                :headers="upload.tokenHeader"
                :data="extraData"
                @change="handleUploadChange"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>

      <div class="attachment">
        <ul>
          <template v-for="file in form.attachment__$$__1">
            <li
              :key="file.id"
              class="file">
              <a
                href="javascript:void(0)"
                @click="handleDownload(file)"
              >
                {{ file.fileName }}
              </a>
              <a-icon
                @click="handleDel(file)"
                type="delete"
                class="icon del"
              />
            </li>
          </template>
        </ul>
      </div>
    </div>
    
  </div>
</template>

<script>
import JEditor from '@/components/els/JEditor'
import CustomUpload from '@comp/template/CustomUpload'
import { getAction } from '@/api/manage'

import moment from 'moment'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        JEditor,
        CustomUpload
    },
    props: {
        parentData: {
            type: Object,
            default () {
                return {}
            }
        },
        templateContent: {
            type: String,
            default: ''
        },
        current: {
            type: Number,
            default: 0
        }
    },
    created () {
        this.getSupplierList()
    },
    data () {
        return {
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 14 }
            },
            supplierOptions: [],
            showNoticeSupplier: false,
            form: {
                bidWinNotice: '0', // 是否发布中标公告，0：否、1：是
                bidWinScope: '', // 中标公告范围
                bidWinEndTime: '', // 中标公告截止时间
                bidWinContent: '', // 中标公告内容
                noticeSupplier: '' // 供应商ELS账号
            },
            formItemLayout: {
                labelCol: { span: 10 },
                wrapperCol: { span: 14 }
            },
            rules: {
                bidWinScope: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectAnnouncementScopeTips`, '请选择中标公告范围'), trigger: 'change' }
                ],
                bidWinEndTime: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectAnnouncementStopTimeTips`, '请选择中标公告截止时间'), trigger: 'change' }
                ],
                bidWinContent: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterAnnouncementContentTips`, '请输入中标公告内容'), trigger: 'change' }
                ]
            },
            upload: {
                modalVisible: false,
                action: '/attachment/purchaseAttachment/upload',
                tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
                accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf'
                // extraData: {
                //     businessType: 'winBidNotice',
                //     headId: this.vuex_currentEditRow.id
                // }
            }
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        extraData () {
            return {
                businessType: 'winBidNotice',
                headId: this.vuex_currentEditRow.id || ''
            }
        }
    },
    watch: {
        parentData: {
            deep: true,
            immediate: true,
            handler (obj) {
                if (!obj || Object.keys(obj).length === 0) return
                const { bidWinNotice, bidWinScope, bidWinEndTime, bidWinContent, noticeSupplier, attachment__$$__1 } = JSON.parse(JSON.stringify(obj)) || {}
                this.form = Object.assign({}, this.form, {
                    bidWinNotice: bidWinNotice || '0',
                    bidWinScope: bidWinScope || '',
                    bidWinEndTime: bidWinEndTime ? moment(bidWinEndTime) : null,
                    bidWinContent: bidWinContent || '',
                    noticeSupplier: noticeSupplier || '',
                    attachment__$$__1: attachment__$$__1 || []
                })
                this.showNoticeSupplier = bidWinScope && bidWinScope == '4' ? true : false
            }
        },
        templateContent (str) {
            if (!str) return
            const props = [ 'bidWinContent', 'bidWinNotificationContent', 'bidFailNotificationContent' ]
            const property = props[this.current]
            if (!this.form.hasOwnProperty(property)) return
            this.form[property] = str
        }
    },
    methods: {
        moment,
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        getSupplierList () {
            let url = '/bidding/purchaseBiddingHead/querySupplierList'
            let params = {
                id: this.vuex_currentEditRow.id
            }
            getAction(url, params).then(res => {
                this.supplierOptions = res.result && res.result.map(rs => ({text: rs.supplierName, value: rs.toElsAccount, title: rs.supplierName})) || []
            })
        },
        disabledDate (current) {
            // Can not select days before today and today
            return current && current < moment().endOf('day')
        },
        updateParent () {
            let parentData = JSON.parse(JSON.stringify(this.parentData))
            parentData = Object.assign({}, parentData, this.form, {
                bidWinEndTime: this.form.bidWinEndTime && this.form.bidWinEndTime.format('YYYY-MM-DD HH:mm:ss')
            })
            this.$emit('update:parentData', parentData)
        },
        handleUploadChange (info) {
            this.updateParent()
            this.$emit('updateFile', {
                _property: 'attachment__$$__1',
                info
            })
        },
        handleDel (file) {
            this.$emit('deleteFile', {
                _property: 'attachment__$$__1',
                ...file
            })
        },
        // 文件下载
        handleDownload ({ id, fileName }) {
            const params = {
                id
            }
            let downloadUrl = '/attachment/purchaseAttachment/download'
            getAction(downloadUrl, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }  
    }
}
</script>
