import { RouteView } from '@/components/layouts'

const BidEvaluationRouter = {
    path: '/applyHall/bidEvaluation',
    name: 'project_supBidEvaluation',
    meta: {
        title: '自主招标',
        titleI18nKey: 'i18n_dict_JdYB_3c4049dd',
        icon: 'icon-111-03',
        keepAlive: false
    },
    component: RouteView,
    children: [
        {
            path: '/applyHall/bidEvaluation/applyAnnouncement',
            name: 'project_applyAnnouncement',
            meta: {
                title: '项目公告',
                titleI18nKey: 'i18n_menu_dIRx_47180cb3',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'applyAnnouncement' */ '@/views/srm/tender/status/Announcement.vue')
        }
    ]
}

export default BidEvaluationRouter