<template>
  <div class="Instruct">
    <div
      class="container"
      :current-edit-row="currentEditRow"
      :style="style">
      <div class="btnGroups">
        <div class="btns">

          <template v-if="!taskInfo.taskId">
            <a-button
              type="primary"
              @click="handleSubmit">
              {{ $srmI18n(`${$getLangAccount()}#i18n_title_approved`, '审批通过') }}
            </a-button>
            <a-button
              type="primary"
              @click="handleReject">
              {{ $srmI18n(`${$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝') }}
            </a-button>
            <a-button @click="showFlow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_viewProcess`, '查看流程') }}</a-button>
            <a-button @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </template>
          <template v-else>
            <taskBtn
              :currentEditRow="currentEditRow"
              :pageHeaderButtons="[{'type': 'back', 'click': goBack}]"/>
          </template>
        </div>
      </div>
      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="table">
            <vxe-grid
              ref="biddingSupplierList"
              v-bind="defaultGridOption"
              :columns="columns">
              <template slot="empty">
                <a-empty />
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <!-- 审批意见 -->
    <a-modal
      v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import { getAction, httpAction } from '@/api/manage'

import taskBtn from '@/views/srm/bpm/components/taskBtn'
import flowViewModal from '@comp/flowView/flowView'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations, mapGetters } from 'vuex'

const ALIAS = '__$$__'

export default {
    name: 'InstructAudit',
    mixins: [DetailMixin],
    components: {
        flowViewModal,
        'content-header': ContentHeader,
        taskBtn
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IByRUz_44f0f90e`, '定标结果审批'),
            confirmLoading: false,
            flowId: 0,
            showRemote: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            auditVisible: false,
            opinion: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            purchaseBiddingItemList: [],
            form: {},
            btns: [
                // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatPriceRecord`, '生成价格记录'), type: 'primary', event: 'record' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'), type: 'primary', event: 'submit', showCondition: this.showAuditBtn },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'), type: '', event: 'cancelAudit', showCondition: this.showAuditBtn },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', event: 'showFlow' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', event: 'save' }
            ],
            showHeader: true,
            height: 0,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'dblclick', mode: 'cell' }
            },
            columns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'), field: 'supplierCode', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 200 }
            ]
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        // 判断是否为拆分方式
        isQuotaWay () {
            const { quotaWay = '0' } = this.currentEditRow || {}
            return quotaWay !== '1'
        },
        ...mapGetters([
            'taskInfo'
        ])
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        showFlow (){
            this.flowId = this.currentEditRow.rootProcessInstanceId
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        cancelAudit (){
            let form = this.currentEditRow
            let param = {}
            param['businessType'] = 'resultBidding'
            param['businessId'] = form.id
            param['rootProcessInstanceId'] = form.resultFlowId
            this.confirmLoading = true
            httpAction('/a1bpmn/audit/api/cancel', param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.getTenderData()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        getData () {
            const url = '/bidding/purchaseBiddingHead/queryConfirmBidByIdIgnore'
            const params = { id: this.currentEditRow.businessId }
            this.confirmLoading = true
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    let { biddingSupplierList = [] } = res.result || {}
                    if (biddingSupplierList.length) {
                        this.purchaseBiddingItemList = biddingSupplierList[0].purchaseBiddingItemList || []
                        this.setColumns()
                    }
                    this.$refs.biddingSupplierList.loadData(biddingSupplierList)
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        setColumns () {
            let priceFormatter = (row, index) => {
                return row.purchaseBiddingItemList[index].price
            }
            let itemStatusFormatter = (row, index) => {
                return row.purchaseBiddingItemList[index].itemStatus === '3' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_wonTheBid`, '已中标') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notWonTheBid`, '未中标')
            }
            let quoteFormatter = (row, index) => {
                return row.purchaseBiddingItemList[index].quota
            }
            let itemStatusChangeEvent = (row, column, index) => {
                let val = row[column.property]
                row.purchaseBiddingItemList[index].itemStatus = val
            }

            let quoteChangeEvent = (row, column, index) => {
                let val = row[column.property]
                row.purchaseBiddingItemList[index].quota = val
            }

            const itemColumns = this.purchaseBiddingItemList.map((item, i) => {
                let obj = {
                    title: item.materialDesc,
                    children: [
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_offer`, '报价'),
                            field: `price${ALIAS}${i}`,
                            formatter: ({row}) => priceFormatter(row, i),
                            width: 120
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractAward`, '授标'),
                            field: `itemStatus${ALIAS}${i}`,
                            formatter: ({row}) => itemStatusFormatter(row, i),
                            width: 120,
                            editRender: {
                                name: '$select',
                                options: [
                                    { value: '3', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_wonTheBid`, '已中标') },
                                    { value: '2', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notWonTheBid`, '未中标') }
                                ],
                                events: {
                                    change: ({row, column}) => itemStatusChangeEvent(row, column, i)
                                }
                            }
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitRatio`, '拆分比例(%)'),
                            field: `quota${ALIAS}${i}`,
                            visible: this.isQuotaWay,
                            formatter: ({row}) => quoteFormatter(row, i),
                            width: 120,
                            editRender: {
                                name: '$input',
                                props: { type: 'number' },
                                events: {
                                    change: ({row, column}) => quoteChangeEvent(row, column, i)
                                }
                            }
                        }
                    ]
                }
                return obj
            })
            this.columns = this.columns.concat(itemColumns)
        },
        handleReject (e){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        // 提交审批
        handleSubmit (e) {
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        goBack () {
            this.$parent.hideController()
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
        }
    },
    created () {
        this.init()
    }
}
</script>
<style lang="less" scoped>
.Instruct {
    .btnGroups{
        margin: 5px 5px;
        *zoom: 1;
        .btns{
            float: right;
        }
        &::after,&::before{
            content: "";
            display: table;
        }
        &::after{
            clear: both;
        }
    }
  .ant-btn {
    margin: 5px;
  }
}
</style>

