<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <field-select-modal 
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      v-if="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction } from '@/api/manage'
export default {
    name: 'ElsEnterpriseInfoEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            fileSrc: '',
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contactInfo`, '联系人信息'), groupCode: 'contactsInfo', type: 'grid', custom: {
                        ref: 'supplierContactsInfoList',
                        columns: [
                            { 
                                type: 'checkbox', width: 40 
                            },
                            { 
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                                field: 'itemNumber',
                                disable: true,
                                width: 50
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseElsAccount`, '采购ELS账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                required: '0',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_function`, '职能'),
                                field: 'functionName',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_position`, '职位'),
                                required: '1',
                                field: 'position',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                                field: 'name',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话号码'),
                                field: 'telphone',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'),
                                required: '1',
                                field: 'email',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: 'QQ',
                                field: 'qqNo',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_weChat`, '微信'),
                                field: 'wxNo',
                                width: 150,
                                editRender: {name: '$input'}
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addContactsItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteContactsEvent}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addressInfo`, '地址信息'), groupCode: 'addressInfo', type: 'grid', custom: {
                        ref: 'supplierAddressInfoList',
                        columns: [
                            { 
                                type: 'checkbox', width: 40 
                            },
                            { 
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                                field: 'itemNumber',
                                disable: true,
                                width: 50
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseElsAccount`, '采购ELS账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_country`, '国家'),
                                field: 'country',
                                width: 150,
                                required: '1',
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_province`, '省份'),
                                field: 'province',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_city`, '城市'),
                                field: 'city',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_address`, '详细地址'),
                                field: 'address',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话'),
                                field: 'telphone',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zipCode`, '邮编'),
                                field: 'fax',
                                width: 150,
                                editRender: {name: '$input'}
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addAddressItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteAddressEvent}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankInfo`, '银行信息'), groupCode: 'bankInfo', type: 'grid', custom: {
                        ref: 'supplierBankInfoList',
                        columns: [
                            { 
                                type: 'checkbox', width: 40 
                            },
                            { 
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                                field: 'itemNumber',
                                disable: true,
                                width: 50
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseElsAccount`, '采购ELS账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCountry`, '银行国家'),
                                field: 'bankCountry',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankProvince`, '银行省份'),
                                field: 'bankProvince',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCity`, '银行城市'),
                                field: 'bankCity',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankBranchName`, '开户行全称'),
                                field: 'bankBranchName',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankAccount`, '银行账号'),
                                field: 'bankAccount',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankAccountName`, '银行账号名称'),
                                field: 'bankAccountName',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cooperationBankType`, '合作银行类型'),
                                field: 'cooperationBankType',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCode`, '银行代码'),
                                field: 'bankCode',
                                required: '1',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCodeIBAN`, 'IBAN（国际银行帐户号码）'),
                                field: 'iban',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_swiftCode`, 'SWIFT CODE（银行国际代码）'),
                                field: 'swiftCode',
                                width: 150,
                                editRender: {name: '$input'}
                            }
                            // ,
                            // {
                            //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedName`, '开户资料扫描件名称'),
                            //     field: 'fileName',
                            //     width: 150,
                            //     editRender: {name: '$input'}
                            // },
                            // {
                            //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedPath`, '开户资料扫描件地址'),
                            //     field: 'filePath',
                            //     width: 150,
                            //     editRender: {name: '$input'}
                            // }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addBankItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteBankEvent}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationInformation`, '认证资料信息'), groupCode: 'certificatedInfo', type: 'grid', custom: {
                        ref: 'supplierCertificatedInfoList',
                        columns: [
                            { 
                                type: 'checkbox', width: 40 
                            },
                            { 
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                                field: 'itemNumber',
                                disable: true,
                                width: 50
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseElsAccount`, '采购ELS账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                required: '0',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationType`, '认证类型'),
                                field: 'certificationType',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationOrg`, '认证机构'),
                                field: 'certificationOrg',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationSta`, '认证标准'),
                                field: 'certificationSta',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationName`, '认证名称'),
                                field: 'certificationName',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationNo`, '认证编号'),
                                field: 'certificationNo',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationDate`, '认证日期'),
                                field: 'certificationDate',
                                width: 150,
                                editRender: {name: 'mDatePicker'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_effectiveDate`, '生效日期'),
                                field: 'effectiveDate',
                                width: 150,
                                editRender: {name: 'mDatePicker'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_unableDate`, '失效日期'),
                                field: 'expiryDate',
                                width: 150,
                                editRender: {name: 'mDatePicker'}
                            }
                            // ,
                            // {
                            //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedName`, '开户资料扫描件名称'),
                            //     field: 'fileName',
                            //     width: 150,
                            //     editRender: {name: '$input'}
                            // },
                            // {
                            //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedPath`, '开户资料扫描件地址'),
                            //     field: 'filePath',
                            //     width: 150,
                            //     editRender: {name: '$input'}
                            // }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addRows`, '添加行'), type: 'primary', click: this.addCertificatedItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deletRows`, '删除行'), click: this.deleteCertificatedEvent}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationInfo`, '组织信息'), groupCode: 'orgInfo', type: 'grid', custom: {
                        ref: 'supplierOrgInfoList',
                        columns: [
                            { 
                                type: 'checkbox', width: 40 
                            },
                            { 
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                                field: 'elsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserAccount`, '采购商账号'),
                                field: 'toElsAccount',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationCode`, '组织类别编码'),
                                field: 'orgCategoryId',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationDesc`, '组织类别描述'),
                                field: 'orgCategoryDesc',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orgCode`, '组织编码'),
                                field: 'orgCode',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orgDesc`, '组织描述'),
                                field: 'orgDesc',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addressCode`, '地址编码'),
                                field: 'addrId',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addressDesc`, '地址描述'),
                                field: 'addrDesc',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_payConditionCode`, '付款条件代码'),
                                field: 'payConditionCode',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paymentWay`, '付款方式'),
                                field: 'paymentMethod',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                                field: 'frozenFlag',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_frozenFunction`, '冻结功能'),
                                field: 'frozenFunction',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ledgerSubject`, '总账中的统驭科目'),
                                field: 'ledgerSubject',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_paymentList`, '付款方式清单'),
                                field: 'paymentList',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseGroupCode`, '采购组'),
                                field: 'procurementSection',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_purchaseGroupCode_dictText`, '采购组名称'),
                                field: 'procurementSectionName',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dutyPersonName`, '负责人'),
                                field: 'principal',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataSource`, '数据来源'),
                                field: 'dataSource',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orgFullDesc`, '组织全称'),
                                field: 'orgFullDesc',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orgEnDesc`, '组织英文名称'),
                                field: 'orgEnDesc',
                                width: 150,
                                editRender: {name: '$input'}
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receiveClerk`, '收货员'),
                                field: 'receiveClerk',
                                width: 150,
                                editRender: {name: '$input'}
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addOrgItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteOrgEvent}
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/supplier/supplierMaster/addEnterpriseInfoVO',
                edit: '/supplier/supplierMaster/updateEnterpriseInfoVO',
                detail: '/supplier/supplierMaster/queryByEnterpriseId',
                public: '/ebidding/purchaseEbiddingHead/publish',
                upload: '/attachment/purchaseAttachment/upload'
            }
        }
    },
    created () {
        this.getFileSrc()
    },
    methods: {
        addContactsItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierContactsInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach(item => {
                if(item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            itemGrid.insert([itemData])
        },
        deleteContactsEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierContactsInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        addAddressItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierAddressInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach(item => {
                if(item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            itemGrid.insert([itemData])
        },
        deleteAddressEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierAddressInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        addBankItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierBankInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach(item => {
                if(item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            itemGrid.insert([itemData])
        },
        deleteBankEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierBankInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        addCertificatedItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierCertificatedInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach(item => {
                if(item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            itemGrid.insert([itemData])
        },
        deleteCertificatedEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierCertificatedInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        fieldSelectOk (data) {
            let supplierGrid = this.$refs.editPage.$refs.supplierOrgInfoList[0]
            let { fullData } = supplierGrid.getTableData()
            let supplierList = fullData.map(item => {
                return item.elsAccount
            })
            // 过滤已有数据
            let insertData = data.filter(item => {
                return !supplierList.includes(item.elsAccount)
            })
            insertData = insertData.map(item => {
                return {
                    toElsAccount: item.elsAccount
                }
            })
            supplierGrid.insertAt(insertData, -1)
        },
        addOrgItem () {
            let url = '/supplier/supplierMaster/getPurchaseAccount'
            let columns = [
                {field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserAccount`, '采购商账号'), width: 150},
                {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseName`, '采购商名称'), width: 200}
            ]
            // 获取供应商范围参数
            let form = this.$refs.editPage.getPageData()
            this.$refs.fieldSelectModal.open(url, { toElsAccount: form.elsAccount}, columns, 'multiple')
        },
        deleteOrgEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierOrgInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        saveEvent () {
            this.$refs.editPage.postData()
        },
        publishEvent () {
            this.$refs.editPage.handleSend()
        },
        getFileSrc () {
            let time = new Date().getTime()
            const param = {businessType: 'enterprise', elsAccount: '100000'}
            console.log(param)
            getAction('/template/templateHead/noToken/listByBusiness', param).then(res => {
                const type = res.success ? 'success' : 'error'
                console.log(res)
                if(type==='success' && res.result && res.result.records){
                    let templateNumber = res.result.records[0].templateNumber
                    let templateVersion = res.result.records[0].templateVersion
                    this.fileSrc = `${this.$variateConfig['configFiles']}/100000/purchase_enterprise_${templateNumber}_${templateVersion}.js?t=`+time
                }
            })
        }
    }
}
</script>