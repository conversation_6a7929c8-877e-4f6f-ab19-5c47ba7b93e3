<template>
  <div>
    <listTable
      ref="listTable"
      :fromSourceData="fromSourceData"
      :statictableColumns="tableColumns"
      :showTablePage="false"
      :pageData="pageData"
    />
  </div>
</template>
<script>
import listTable from '../../components/listTable'

export default {
    inject: ['currentSubPackage'],
    props: {
        fromSourceData: {
            default: () => {
                return []
            },
            type: Array
        },
        type: {
            default: () => {
                return ''
            },
            type: String
        }
    },
    components: {
        listTable
    },
    computed: {
        subpackage (){
            return this.currentSubPackage()
        }
    },
    data () {
        return {
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView
                    }
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            tableColumns: []
        }
    },
    methods: {
        handleView (row) {
            this.$emit('handleResponsesViewPage', row)
        }

    },
    created () {
        if (this.type == 'prejudication') {
            this.tableColumns = [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUddzE_751366cf`, '预审响应状态'),
                    'field': 'preResponseStatus_dictText',
                    'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUddKI_75121182`, '预审响应时间'),
                    'field': 'preResponseTime',
                    'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataSource`, '数据来源'),
                    'field': 'sourceType_dictText',
                    'width': 120
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                    'field': 'contacts'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
                    'field': 'contactsPhone'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ]
        } else {
            // 后审一步法,processType == '0' resultResponseTime
            // 后审两步法第一步,processType == '1' responseTime
            let responseTime = this.subpackage.processType == '0' ? 'resultResponseTime' :  'responseTime'
            let responseStatus = this.subpackage.processType == '0' ? 'resultResponseStatus_dictText' :  'responseStatus_dictText'

            this.tableColumns = [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseStatus`, '响应状态'),
                    'field': responseStatus,
                    'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_responseTime`, '响应时间'),
                    'field': responseTime,
                    'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataSource`, '数据来源'),
                    'field': 'sourceType_dictText',
                    'width': 120
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                    'field': 'contacts'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
                    'field': 'contactsPhone'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ]
        }
    }
}
</script>

