<template>
  <div class="BiddingDetail">
    <div class="page-container">
      <detail-layout
        ref="detailPage"
        :currentEditRow="currentEditRow"
        useLocalModelLayout
        modelLayout="unCollapse"
        :showHeader="false"
        :page-data="pageData"
        :current-edit-row="vuex_currentEditRow"
        :url="url"
      />
    </div>

    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
  </div>
</template>
<script>
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

import {
    SEQ_COLUMN,
    CHECKBOX_COLUMN,
    ATTACHMENT_COLUMNS
} from '@/utils/constant.js'

const fileInfoAchmentList = ATTACHMENT_COLUMNS('fileInfo')

export default {
    name: 'BiddingDetail',
    mixins: [DetailMixin],
    data () {
        return {
            btns: [],
            showHeader: false,
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suVH_2e09d920`, '报价信息'),
                        groupCode: 'purchaseBiddingItem',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseBiddingItemList',
                            columns: [
                                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), field: 'supplierName', width: 150 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), field: 'materialNumber', width: 250 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), field: 'materialName', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), field: 'materialDesc', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量'), field: 'requireQuantity', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), field: 'purchaseUnit_dictText', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PVJSBA_bf78d67e`, '要求交货日期'), field: 'requireDate', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), field: 'currency_dictText', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'), field: 'taxCode', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), field: 'taxRate', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'), field: 'netPrice', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'), field: 'price', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netAmount`, '未税金额'), field: 'netAmount', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), field: 'taxAmount', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_effectiveDate`, '生效时间'), field: 'effectiveDate', width: 120 },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_expiryDate`, '失效时间'), field: 'expiryDate', width: 120 }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachmentList',
                            columns: [
                                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                CHECKBOX_COLUMN,
                                ...fileInfoAchmentList
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                            ]
                        }
                    }
                ]
            },
            url: {
                detail: '/bidding/purchaseBiddingHead/queryById'
            }
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        fileSrc () {
            const {
                templateNumber = '',
                templateVersion = '',
                templateAccount = '',
                busAccount = ''
            } = this.vuex_currentEditRow || {}

            const configFiles = this.$variateConfig['configFiles']
            const time = +new Date()
            const url = `${configFiles}/${templateAccount || busAccount}/purchase_bidding_${templateNumber}_${templateVersion}.js?t=`+time
            return url
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        init () {
            if (this.vuex_currentEditRow.projectId) {
                this.$refs.detailPage.queryDetail(this.vuex_currentEditRow.id)
            }
        },
        beforeHandleData (pageData) {
            pageData.groups = []
            console.log('beforeHandleData pageData', pageData)
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        }
    }
}
</script>

<style lang="less" scoped>
.BiddingDetail {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
}
</style>
