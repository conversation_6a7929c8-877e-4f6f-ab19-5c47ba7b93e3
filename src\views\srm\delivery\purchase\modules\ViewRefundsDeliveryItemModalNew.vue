<template>
  <div class="els-page-comtainer">
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      @goBack="goBack"
      @cell-click="cellClickEvent"
    />
    <logistics-timeline
      :show="logisticsVisible"
      :logisticsData="logisticsMsg"
      @logisticsHandleOk="logisticsHandleOk"
      @logisticsHandleCancel="handleCancel"
    ></logistics-timeline>
  </div>
</template>
<script lang="jsx">
import {tileEditPageMixin} from '@comp/template/tileStyle/tileEditPageMixin'
import LogisticsTimeline from '@comp/LogisticsTimeline/LogisticsTimeline'
import {getAction, postAction} from '@/api/manage'
import { sub } from '@/utils/mathFloat'
import {SelectModal} from '@comp/template/business/class/selectModal'

export default {
    name: 'ViewRefundsDeliveryItemModalNew',
    mixins: [tileEditPageMixin],
    components: {
        LogisticsTimeline
    },
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnSalesMemo`, '退货通知单'),
            confirmLoading: false,
            logisticsVisible: false,
            logisticsMsg: {},
            refundCondition: true,
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnSalesMemoDetail`, '退货通知单详情'),
                form: {
                    refundsDeliveryNumber: '',
                    toElsAccount: '',
                    supplierCode: '',
                    supplierName: '',
                    refundsDeliveryDesc: '',
                    refundsDeliveryWay: '',
                    refundsDeliveryStatus: '',
                    purchaseOrg: '',
                    company: '',
                    refundsDeliveryTime: '',
                    planReturnDate: '',
                    logisticsCompany: '',
                    trackingNumber: '',
                    refundsAddress: '',
                    refundsPrincipal: '',
                    refundsPrincipalPhone: '',
                    refundsPrincipalMail: '',
                    purchaseRemark: '',
                    supplierRemark: '',
                    batchNumber: '',
                    fbk20:"",
                    refundsReason: ''
                },
                panels: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnSalesDetail`, '退货详情信息'),
                        content: {
                            ref: 'baseForm',
                            colSpan: '8',
                            type: 'form',
                            list: [
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnDocNo`, '退货单号'),
                                    fieldName: 'refundsDeliveryNumber',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                                    fieldName: 'toElsAccount',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'),
                                    fieldName: 'supplierCode',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '供应商名字'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnDocDesc`, '退货单描述'),
                                    fieldName: 'refundsDeliveryDesc',
                                    disabled: true
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnMethod`, '退货方式'),
                                    fieldName: 'refundsDeliveryWay',
                                    dictCode: 'srmRefundsDeliveryWay',
                                    disabled: true
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnDocStatus`, '退货单状态'),
                                    fieldName: 'refundsDeliveryStatus',
                                    dictCode: 'srmRefundsDeliveryStatus',
                                    disabled: true
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeadc98_purchaseOrgCode`, '采购组织'),
                                    fieldName: 'purchaseOrg',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="purchaseOrganization" && status="1"',
                                    disabled: true
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司'),
                                    fieldName: 'company',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="companyCode" && status="1"',
                                    disabled: true
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryTime`, '退货日期'),
                                    fieldName: 'refundsDeliveryTime',
                                    disabled: true
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_planReturnDate`, '计划退货日期'),
                                    fieldName: 'planReturnDate',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: '车牌号',
                                    fieldName: 'fbk20',
                                    disabled: false
                                },

                                /*{
                                  type: 'input',
                                  label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsCompany`, '物流公司'),
                                  fieldName: 'logisticsCompany',
                                  disabled: true
                              },
                              {
                                  type: 'input',
                                  label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trackingNumber`, '物流单号'),
                                  fieldName: 'trackingNumber',
                                  disabled: true
                              },
                              {
                                  type: 'number',
                                  label: '退货地址',
                                  fieldName: 'refundsAddress',
                                  disabled: true
                              },
                              {
                                  type: 'input',
                                  label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipal`, '退货负责人'),
                                  fieldName: 'refundsPrincipal',
                                  disabled: true
                              },
                              {
                                  type: 'input',
                                  label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipalPhone`, '退货负责人电话'),
                                  fieldName: 'refundsPrincipalPhone',
                                  disabled: true
                              },
                              {
                                  type: 'input',
                                  label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipalMail`, '退货负责人邮箱'),
                                  fieldName: 'refundsPrincipalMail',
                                  disabled: true
                              },*/
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_recommdRemarks`, '需方备注'),
                                    fieldName: 'purchaseRemark',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierRemarks`, '供方备注'),
                                    fieldName: 'supplierRemark',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#`, '供应商公司代码'),
                                    fieldName: 'toCompany_dictText',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: '退货原因',
                                    fieldName: 'refundsReason',
                                    disabled: false,
                                    required: true
                                },
                            ]}},
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnLineInfo`, '退货行信息'),
                        content: {
                            type: 'table',
                            ref: 'purchaseRefundsDeliveryItemList',
                            columns: [
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lineStatus`, '行状态'),
                                    field: 'itemStatus_dictText',
                                    width: '150',
                                    dictCode: 'srmRefundsItemStatus'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialNumber',
                                    width: '150'
                                },
                                {
                                    field: 'materialName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
                                    width: 150
                                },
                                // {
                                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                //     field: 'materialDesc',
                                //     width: '150'
                                // },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_batchNumber`, '批次号'),
                                    field: 'batchNumber',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_refundsQuantity`, '退货数量'),
                                    field: 'refundsQuantity',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'),
                                    field: 'quantity',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overTolerance`, '超量容差率'),
                                    field: 'overTolerance',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'),
                                    field: 'price',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '净价'),
                                    field: 'netPrice',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
                                    field: 'taxCode',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'),
                                    field: 'taxRate',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnAmount`, '退货金额'),
                                    field: 'taxAmount',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantity`, '收货数量'),
                                    field: 'receiveQuantity',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsQuantityAlready`, '已出库数量'),
                                    field: 'refundsQuantityAlready',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsQuantityNow`, '本次出库数量'),
                                    field: 'refundsQuantityNow',
                                    width: '150',
                                    editRender: {name: 'AInputNumber', events: {blur: this.refundsQuantityNowValid}}
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsDate`, '本次出库日期'),
                                    field: 'refundsDate',
                                    width: '150',
                                    editRender: {name: '$input', props: { type: 'date', clearable: true }}
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsCompany`, '物流公司'),
                                    field: 'logisticsCompany',
                                    editRender: {name: 'AInput'},
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trackingNumber`, '物流单号'),
                                    field: 'trackingNumber',
                                    editRender: {name: 'AInput'},
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnMethod`, '退货方式'),
                                    field: 'refundsDeliveryWay_dictText',
                                    width: '150',
                                    dictCode: 'srmRefundsDeliveryWay'
                                },
                                // {
                                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipal`, '退货负责人'),
                                //     field: 'refundsPrincipal',
                                //     width: '150'
                                // },
                                {
                                    ...new SelectModal({
                                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipal`, '退货负责人'),
                                        field: 'refundsPrincipal',
                                        required: '1',
                                        width: 150,
                                        bindFunction: function (row, data) {
                                            row.principal = data[0].subAccount + '_' +data[0].realname
                                        },
                                        extend: { modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), with: 150},
                                            {field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), with: 150}], 
                                        modalUrl: '/account/elsSubAccount/list', modalParams: {}}
                                    })
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipalPhone`, '退货负责人电话'),
                                    field: 'refundsPrincipalPhone',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipalMail`, '退货负责人邮箱'),
                                    field: 'refundsPrincipalMail',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                                    field: 'orderNumber',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                    field: 'orderItemNumber',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_factoryCode`, '库存组织'),
                                    field: 'factory_dictText',
                                    width: '150',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocation`, '库存地点'),
                                    field: 'storageLocation_dictText',
                                    width: '150',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="location" && status="1"'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receiptVoucherNo`, '收货凭证单号'),
                                    field: 'voucherNumber',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voucherItemNumber`, '收货凭证行号'),
                                    field: 'voucherItemNumber',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'),
                                    field: 'sourceType_dictText',
                                    width: '150',
                                    dictCode: 'srmRefundsSourceType'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号'),
                                    field: 'sourceNumber',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceLineNumber`, '来源单行号'),
                                    field: 'sourceItemNumber',
                                    width: '150'
                                }
                            ],
                            toolbarButton: [
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                                    key: 'fillDown',
                                    type: 'tool-fill',
                                    beforeCheckedCallBack: this.fillDownGridItem
                                }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnRecord`, '退货记录'),
                        content: {
                            type: 'table',
                            ref: 'refundsDeliveryItemRecordList',
                            columns: [
                                {
                                    type: 'seq',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),
                                    width: 50
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                                    field: 'orderNumber',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                    field: 'orderItemNumber',
                                    width: '150'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialNumber',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料名称'),
                                    field: 'materialName',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
                                    field: 'materialSpec',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                                    field: 'materialGroup',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'),
                                    field: 'quantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
                                    field: 'taxCode',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'),
                                    field: 'taxRate',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '净价'),
                                    field: 'netPrice',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'),
                                    field: 'price',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_outboundQuantity`, '出库数量'),
                                    field: 'refundsQuantityNow',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_refundsDate`, '出库日期'),
                                    field: 'refundsDate',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnMethod`, '退货方式'),
                                    field: 'refundsDeliveryWay_dictText',
                                    width: '150',
                                    dictCode: 'srmRefundsDeliveryWay'
                                },
                                // {
                                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipal`, '退货负责人'),
                                //     field: 'refundsPrincipal',
                                //     width: '150'
                                // },
                                // {
                                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipalPhone`, '退货负责人电话'),
                                //     field: 'refundsPrincipalPhone',
                                //     width: '150'
                                // },
                                // {
                                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refundsPrincipalMail`, '退货负责人邮箱'),
                                //     field: 'refundsPrincipalMail',
                                //     width: '150'
                                // },
                                // {
                                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsCompany`, '物流公司'),
                                //     field: 'logisticsCompany',
                                //     width: '150'
                                // },
                                // {
                                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trackingNumber`, '物流单号'),
                                //     field: 'trackingNumber',
                                //     width: '150'
                                // },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 100,
                                    align: 'left',
                                    slots: {
                                        default: ({row}) => {
                                            let resultArray = []
                                            resultArray.push(<a title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')} onClick={() => this.viewTrackingDetail(row)}>物流详情</a>)
                                            return resultArray
                                        }
                                    }
                                }
                            ],
                            toolbarButton: [
                            ]
                        }
                    }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_GGIt_26b7f58c`, '出库'), type: 'primary', clickFn: this.refundsEvent, showCondition: this.showRefundCondition},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack }
                ]
            },
            url: {
                detail: '/delivery/purchaseRefundsDeliveryHead/queryById',
                express: '/api/trace/express',
                createRefundMsg: '/delivery/purchaseRefundsDeliveryHead/createRefundMsg'
            }
        }
    },
    mounted () {
    },
    created (){
    },
    methods: {
        // 行点击
        cellClickEvent({ row, rowIndex, column, columnIndex }) {
            let itemGrid = this.$refs.editPage.$refs.purchaseRefundsDeliveryItemList[0]
            if (!itemGrid.isEditByRow(row)) return

            itemGrid.clearCheckboxRow()
            itemGrid.setCheckboxRow([row], true)
        },
        requestAfter (data) {
            data.purchaseRefundsDeliveryItemList.map(item=>{ 
                let items = sub(Number(item.refundsQuantity), Number(item.refundsQuantityAlready))
                item.refundsQuantityNow = item.refundsQuantityNow || items || 0
            })
            this.refundCondition = data.canReturnNow
        },
        showRefundCondition (){
            return this.refundCondition
        },
        viewTrackingDetail (row) {
            if (row.trackingNumber==null){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsDocNoIseEmpty`, '物流单号为空'))
            }else {
                this.$refs.editPage.confirmLoading = true
                getAction(this.url.express, {'expressNumber': row.trackingNumber}, 'get').then((res) => {
                    if (res.success) {
                        this.logisticsMsg = res.result
                        this.logisticsVisible = true
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.$refs.editPage.confirmLoading = false
                })
            }
        },
        logisticsHandleOk () {
            this.logisticsVisible= false
        },
        handleCancel () {
            this.logisticsVisible= false
        },
        refundsQuantityNowValid (data) {
            if (data.row.refundsQuantityAlready) {
                if (parseFloat(data.row.refundsQuantityAlready) + parseFloat(data.row.refundsQuantityNow) > parseFloat(data.row.refundsQuantity)) {
                    data.row.refundsQuantityNow = parseFloat(data.row.refundsQuantity) - parseFloat(data.row.refundsQuantityAlready)
                    this.$message.warning('此行本次最大可出库数量为:' + data.row.refundsQuantityNow)
                }
            } else {
                if (parseFloat(data.row.refundsQuantityNow) > parseFloat(data.row.refundsQuantity)) {
                    data.row.refundsQuantityNow = data.row.refundsQuantity
                    this.$message.warning('此行本次最大可出库数量为:' + data.row.refundsQuantityNow)
                }
            }
        },
        refundsEvent () {
            let params = this.$refs.editPage.getParamsData()
            let items = params.purchaseRefundsDeliveryItemList
            let netOpt = true
            let allNum =0

            if (params.fbk20 == null || params.fbk20 == '') {
                this.$message.warning('请输入车牌号')
                return
            }
            if (params.refundsReason == null || params.refundsReason == '') {
                this.$message.warning('请输入退货原因')
                return
            }
            let date= new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            let currentTime = year + '-'+month+'-'+day
            items.map((item, i) => {
                item.carNumber = params.fbk20
                allNum= allNum + item.refundsQuantityNow
                if(!item.refundsDate){
                    item.refundsDate = currentTime
                }
                if (item.refundsQuantityNow == null ||(item.refundsQuantityNow && parseFloat(item.refundsQuantityNow) < 0)) {
                    netOpt = false
                    this.$message.warning('退货行信息第' + (i + 1) + '行本次出库数量为空或小于0')
                }
                // else if (item.refundsQuantityNow && !item.refundsDate) {
                //     netOpt = false
                //     this.$message.warning('退货行第' + (i + 1) + '行请输入出库日期')
                // }
                //已出库数量 >= 退货数量  && 本次出库  > 退货数量 - 已出库数量
                else if (item.refundsQuantityAlready > item.refundsQuantity || item.refundsQuantityNow > item.refundsQuantity - item.refundsQuantityAlready){
                    netOpt = false
                }
                // 赋值退货原因
                item.refundsReason = params.refundsReason
            })
            if(!allNum){
                this.$message.warning('至少输入一条退货行信息的本次出库数量')
                netOpt = false
            }
            if (netOpt) {
                let that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refunds`, '确认出库'),
                    content: '是否确认本次出库数量?',
                    onOk: function () {
                        that.$refs.editPage.confirmLoading = true
                        postAction(that.url.createRefundMsg, items).then(res => {
                            if (res.success) {
                                that.$message.success(res.message)
                                that.$refs.editPage.goBack()
                                that.$parent.searchEvent()
                            } else {
                                that.$message.warning(res.message)
                            }
                        }).finally(o => {
                            that.$refs.editPage.confirmLoading = false
                        })
                    }
                })
            } else {
                this.$message.warning('请检查数量')
            }
        }
    }
}
</script>
<style lang="less" scoped>
</style>