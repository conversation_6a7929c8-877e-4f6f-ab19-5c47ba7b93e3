<template>
  <div>
    <a-modal
    v-drag    
      centered
      :title="title"
      :width="960"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="close"
    >
      <a-input-search
        :placeholder="$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')"
        style="margin-bottom:8px"
        @search="onSearch"
        enterButton
      />
      <vxe-grid
        border
        resizable
        max-height="350"
        row-id="id"
        size="mini"
        :loading="loading"
        ref="selectGrid"
        :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
        :data="tableData"
        :pager-config="tablePage"
        :radio-config="selectModel === 'single' ? checkedConfig : undefined"
        :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
        :columns="columns"
        @page-change="handlePageChange"
      ></vxe-grid>
    </a-modal>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import { srmI18n, getLangAccount } from '@/utils/util'
export default {
    props: {
        title: {
            type: String,
            default: '选择数据'
        },
        url: {
            type: String,
            default: '/baseinfo/item/list'
        },
        columns: {
            type: Array,
            default: () => []
        },
        selectModel: {
            type: String,
            default: 'multiple'
        }
    },
    data () {
        return {
            visible: false,
            loading: false,
            confirmLoading: false,
            urlList: {
                item: '/baseinfo/item/list'
            },
            checkedConfig: { highlight: true, reserve: true, trigger: 'row' },
            tableData: [],
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                align: 'left',
                pageSizes: [10, 20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            propParams: {}
        }
    },
    methods: {
        loadData (params) {
            this.loading = true
            getAction(this.url, params).then((res) => {
                if (res.success) {
                    let list = res.result.records || []
                    this.tableData = list
                    this.tablePage.total = res.result.total
                }
                this.loading = false
            })
        },
        open (params) {
            let queryParams = { pageSize: this.tablePage.pageSize, pageNo: this.tablePage.currentPage }
            if (params) {
                queryParams = Object.assign({}, queryParams, params)
            }
            this.propParams = { ...queryParams }
            this.loadData(queryParams)
            this.visible = true
            this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxRow()
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            let selectedData = this.$refs.selectGrid.getCheckboxRecords()
            if (this.selectModel == 'single') {
                selectedData = this.$refs.selectGrid.getRadioRecord() ? [this.$refs.selectGrid.getRadioRecord()] : []
            }
            if (selectedData.length) {
                this.visible = false
                this.$emit('ok', selectedData)
            } else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
            }
        },
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            this.loadData({ pageSize: pageSize, pageNo: currentPage })
        },
        onSearch (keyWord) {
            this.loadData(Object.assign({}, this.propParams, { keyWord: keyWord }))
        }
    }
}
</script>