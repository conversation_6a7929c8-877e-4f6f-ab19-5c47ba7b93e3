<!--
 * @Author: fzb
 * @Date: 2022-02-25 15:41:53
 * @LastEditTime: 2022-03-02 17:49:42
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\views\Preview.vue
-->
<template>
  <div
    class="preview"
    :style="screenStyle">
    <!-- :is="widget.entity" -->
    <component
      :is="widget.entity"
      class="echart-box"
      :widget="widget"
    ></component>
    <!-- :style="{
        position: 'absolute',
        width: widget.location.width + 'px',
        height: widget.location.height + 'px',
        top: widget.location.top + 'px',
        left: widget.location.left + 'px',
      }" -->
  </div>
</template>
<script>
import {
    ChartsBar,
    ChartsGauge,
    ChartsPie,
    ChartsRadar,
    ChartsLineChart,
    ChartsSplashes,
    ChartsKLine,
    BusinessByTable,
    BusinessLeagueTable,
    BusinessDigitalFlop 
} from '@comp/chart/widget/widgets' 
// import { reportControlCabinQueryById } from '@api/index'
export default {
    props: {
        widget: {
            type: [Object],
            default: () => { }
        }
    },
    components: {
        ChartsBar,
        ChartsGauge,
        ChartsPie,
        ChartsRadar,
        ChartsLineChart,
        ChartsSplashes,
        ChartsKLine,
        BusinessByTable,
        BusinessLeagueTable,
        BusinessDigitalFlop    
    },
    data () {
        return {
            screen: {},
            widgets: []
        }
    },
    computed: {
        screenStyle () {
            return this.getScreenStyle(this.screen)
        }
    },
    mounted () {
    // const screenId = this.$route.query.id;
    // reportControlCabinQueryById(screenId).then((res) => {
    //   if (res && res.success) {
    //     this.widgets = JSON.parse(res.result.widgets)
    //     delete res.result.widgets
    //     this.screen = res.result
    //   } else {
    //     this.$message.error(res.message)
    //   }
    // })
    },
    methods: {
        getScreenStyle (screen) {
            let style = {
                width: screen.width + 'px',
                height: screen.height + 'px'
            }
            if (screen.image) {
                style.backgroundImage = 'url(' + screen.image + ')'
                style.backgroundSize = '100% 100%'
            }
            if (screen.color) {
                style.backgroundColor = screen.color
            }
            return style
        }
    }

}
</script>
<style lang="less" scope>
body {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: hsla(0, 0%, 100%, 0.2);
    border-radius: 5px;
    background-color: #606060;
  }
  .echart-box{
    width: 100%;
    height: 100%;
  }
  .preview{
    width: 100%;
    height: 100%;
  }
}
</style>