<template>
  <div style="height:100%">
    <list-layout
      v-show="!showDetailPage && !showEditPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 详情界面 -->
    <SaleEsignDetail 
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
    <!-- 详情界面 -->
    <SaleEsignEdit 
      v-if="showEditPage" 
      ref="editPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>
</template>
<script>
import SaleEsignDetail from './modules/SaleEsignDetail'
import SaleEsignEdit from './modules/SaleEsignEdit'
import {ListMixin} from '@comp/template/list/ListMixin'
export default {
    mixins: [ListMixin],
    components: {
        SaleEsignDetail,
        SaleEsignEdit
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'esign',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: '流水号'
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                form: {
                    keyWord: ''
                },
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esign#saleEsign:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MVPWQA_d0ab0d72`, '回传签署文档'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esign#saleEsign:editDoc'}
                ]
            },
            url: {
                list: '/esign/saleEsign/list',
                columns: 'SaleEsignList'
            }
        }
    },
    methods: {
        allowEdit (row){
            if(row.passBack==='1' && row.reject==!'1'){
                return true
            }
            return false
        }
    }
}
</script>