<template>
  <div
    class="record-chat"
    :style="{ height: chatHeight ,overflow: !disabled? 'hidden':'auto'}" >
    <div
      class="ask_list"
      ref="askList"
      :class="{ 'active_list': !disabled }"
      v-show="messageRecordList.length>0">
      <ul ref="askListContent" >
        <li
          class="animated fadeIn"
          v-for="(item, index) in messageRecordList"
          :key="index">
          <div
            class="list-l"
            :class="item.contentType == '0' ? 'ask_user' : ''">
            <div
              class="ask-baseinfo"
              v-show="item.contentType != '0'">
              <span>{{ item.createBy }}</span> {{ item.updateTime }}
            </div>
            <div
              class="ask-baseinfo ask-baseinfo-r"
              v-show="item.contentType == '0'">
              <span>{{ item.updateTime }}</span> {{ item.createBy }}
            </div>
            <div class="content">
              <div class="text">
                <div
                  class="content-text"
                  v-html="dealContext(item.content)"></div>
              </div>
              <div
                class="reSend"
                v-if="item.contentType == '0' && item.sendStatus == '2'"
                @click="reSendNews(item)">
                <a-icon type="exclamation" />
              </div>
            </div>
          </div>
        </li>
      </ul>
      
    </div>
    <div
      class="empty-box"
      v-show="messageRecordList.length <=0">
      <p>{{ $srmI18n(`${$getLangAccount()}#i18n_alert_PSRetH_bb4e66de`, '暂无沟通记录') }}</p>
    </div>
    <div
      class="ask-form"
      v-if="!disabled">
      <j-editor
        v-if="showSendMessageBox"
        :id="getJEditorID()"
        ref="orderUeditor"
        v-model="editorContent"
        v-bind="{ coustomInit, plugins }" />
      <div class="submit">
        <a-button
          v-if="!showSendMessageBox"
          type="primary"
          @click="showSendMessageBox=true">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_MBXH_28d8c216`, '回复消息') }}</a-button>
        <a-button
          v-if="showSendMessageBox"
          @click="showSendMessageBox=false">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
        <a-button
          v-if="showSendMessageBox"
          @click="submit"
          type="primary" >{{ $srmI18n(`${$getLangAccount()}#i18n_btn_hdXH_28440637`, '发送消息') }}</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import JEditor from '@/components/els/JEditor'
import { mapGetters } from 'vuex'
export default {
    name: 'Chat',
    props: {
        messageRecordList: {
            type: Array,
            default: () => {
                return []
            }
        },
        disabled: {
            type: Boolean,
            default: false
        },
        chatHeight: {
            type: String,
            default: () => {
                return '100%'
            }
        }
    },
    components: {
        JEditor
    },
    data () {
        return {
            showSendMessageBox: false,
            serviceIconSrc: '',
            editorContent: '',
            plugins: 'lists image media table wordcount fullscreen code powerpaste',
            coustomInit: {
                height: 250
            }
        }
    },
    mounted () {
        this.enterKeyup()
    },
    created () {
    },
    methods: {
        dealContext (context) {
            if (context && context.length) {
                // 处理从srm传过来的图片
                let data = context.match(/^img\[(.+?)\]$/)
                if (data && data.length) {
                    let src = data[1]
                    context = `<img src=${src} alt="not image" />`
                }
            }
            return context
        },
        enterKeyupDestroyed () {
            document.removeEventListener('keyup', this.enterKey)
        },
        enterKeyup () {
            document.addEventListener('keyup', this.enterKey)
        },
        //添加回车监听发送消息
        enterKey (event){
            let _key = event.keyCode
            if (_key === 13 && this.showSendMessageBox) {
                this.submit()
            }
        },
        getJEditorID () {
            return `editor-box-${Math.floor(Math.random() * 10 + 1)}`
        },
        ...mapGetters(['avatar']),
        submit () {
            this.$emit('sendNews', this.editorContent)
            this.editorContent = ''

        },
        reSendNews (item) {
            if (this.disabled) {
                return
            }
            this.$emit('reSendNews', item)
        },
        getAvatar () {
            return `${this.avatar() ? this.avatar() : this.serviceIconSrc}`
        },
        //滚动至聊天记录底部
        scrollToMessageEnd (){
            this.$nextTick(()=>{
                let askListHeight=this.$refs.askListContent.offsetHeight
                this.$refs.askList.scrollTo(0, askListHeight)
            })
        }
    },
    destroyed () {
    // 销毁enter事件
        this.enterKeyupDestroyed()
    }
}
</script>
<style lang="less" scoped>
.record-chat {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 4px 0 0 4px;
  color: #979da8;
  font-size: 14px;
  border: 1px solid #ebebeb;
  .empty-box{
    display: flex;
    align-items: center;
    flex: 1 1;
    text-align: center;
    p{
      margin: 0;
      flex: 1;
    }
  }
  ul{
    padding: 0;
    li {
      list-style: none;
    }
  }
  

  .ask_list {
    flex: 1 1;
    padding: 10px 24px 0 24px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .active_list {
	  overflow: auto;
  }

  .icon_user {
    width: 40px;
    height: 40px;
    position: absolute;
    left: 0;
    overflow: hidden;
    border-radius: 50%;
  }

  .content {
    position: relative;
    display: inline-block;
    &::after {
      top: 50%;
      left: -23px;
      transform: translateY(-50%);
      position: absolute;
      content: '';
      border-width: 12px;
      border-style: solid;
      border-color: transparent #eebf8e transparent transparent;
    }
  }

  :deep(.content-text){
    p {
      margin-bottom: 0px;
    }

    img {
      max-width: 100%;
    }
  }

  .list-l {
    box-sizing: border-box;
    padding: 0 12px 24px;
    position: relative;
  }

  .ask_user {
    text-align: right;

    .icon_user {
      right: 0;
      left: auto;
    }

    .text {
      background-color: #aece8b;
    }


    .content {
      &::after {
        left: unset;
        right: -23px;
        border-color: transparent transparent transparent #aece8b;
      }
    }
  }

  .ask-baseinfo {
    color: #d0cfcf;
    margin-bottom: 10px;
    font-size: 13px;
    span {
      margin-right: 15px;
      color: #666666;
    }
  }
  .ask-baseinfo-r {
    margin-bottom: 10px;
    color: #666666;
    font-size: 13px;
    span {
      margin-right: 15px;
      color: #d0cfcf;
    }
  }
  .reSend {
    position: absolute;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    left: -50px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 25px;
    color: #fa5252;

    &:hover {
      cursor: pointer;
    }
  }
  .text {
    word-break: break-all;
    background-color: #eebf8e;
    color: #ffffff;
    border-radius: 8px;
    padding: 12px 20px;
    text-align: left;
  }

  .ask-form {
    border-top: 1px solid #e1e4ea;
    padding: 0 0 12px;
    font-size: 14px;
    line-height: 20px;
    color: #5a626e;
  }

  .submit {
    margin-top: 10px;
    text-align: right;
    .ant-btn {
      margin-right: 10px;
    }
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.animated.fadeIn,
.animated.fadeOut {
  animation-duration: 0.35s;
}

.fadeIn {
  animation-name: fadeIn;
}

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}
</style>
