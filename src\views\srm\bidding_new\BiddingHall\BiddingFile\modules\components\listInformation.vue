<template>
  <div>
    <titleTrtl class="margin-b-10">
      <span>{{ rowTitel }}{{ currentRow ? '' : $srmI18n(`${$getLangAccount()}#i18n_field_WumvBIBBVHWVWRiYBxW_e5696734`, '：价格开标一览表信息(请先勾选招标函)') }}</span>
      <template
        v-if="currentRow"
        slot="right"
      >
        <a-button
          v-if="showMaintenanceMaterial"
          type="primary"
          size="small"
          @click="maintenanceMaterial">{{ $srmI18n(`${$getLangAccount()}#i18n_field_LDSLc_2b9121cc`, '维护物料行') }}</a-button>
        <a-button
          v-if="currentRow?.formatType == '9' && isEdit"
          type="primary"
          size="small"
          @click="handleTableAdd">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Suc_1a764e7`, '添加行') }}</a-button>
        <a-button
          v-if="isEdit"
          type="primary"
          size="small"
          style="margin-left: 10px"
          @click="handleSelectTableColumn">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VaA_188c6e9`, '新增列') }}</a-button>
      </template>
    </titleTrtl>
    <div v-if="currentRow">
      <vxe-grid
        :height="height"
        v-bind="gridConfig"
        ref="customizeFieldDataTable"
        key="customizeFieldDataTable"
        :data="currentRow.customizeFieldData"
        :edit-rules="editRules"
        :merge-cells="mergeCells"
        :columns="currentRow.customizeFieldModel"
        show-overflow="title">
        <template #grid_opration="{ row, column, rowIndex }">
          <div v-if="optColumnList && isEdit">
            <span
              v-for="(opt, optIndex) in optColumnList"
              :key="'opt_' + row.id + '_' + optIndex">
              <a
                :title="opt.title"
                style="margin: 0 4px"
                :disabled="typeof opt.disabled === 'function' ? opt.disabled(row) : opt.disabled"
                v-show="!opt.hide"
                @click="
                  () => {
                    optColumnFuntion(opt, row, column, rowIndex)
                  }
                "
              >{{ opt.title }}</a
              >
            </span>
          </div>
        </template>
      </vxe-grid>
    </div>
    <addColumnModal
      ref="addColumnModal"
      isEmit
      @ok="selectedOk"></addColumnModal>
  </div>
</template>
<script lang="jsx">
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import addColumnModal from './addColumnModal'
import titleTrtl from '@/views/srm/bidding_new/BiddingHall/components/title-crtl'
import { valitNumberLength } from '@views/srm/bidding_new/utils/index'
let checkNumber = (rule, value, callback) => {
    if (value) {
        if (!valitNumberLength(value, 8)){
            callback(new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '8'))
        }
    }
    callback()
}
export default {
    mixins: [tableMixins, baseMixins],
    components: {
        addColumnModal,
        titleTrtl
    },
    props: {
        currentRow: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: '',
            type: String
        },
        formData: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        rowTitel () {
            if (this.currentRow) return this.currentRow.name
            return '某投标函'
        },
        mergeCells () {
            if (!['0', '1', '2'].includes(this.currentRow.formatType)) return[]
            let rowspan = this.currentRow.customizeFieldData.length
            return [
                // row: 行，col: 列，
                { row: 0, col: 1, rowspan, colspan: 1 }
            ]
        },
        showMaintenanceMaterial () {
            if ( this.currentRow?.formatType != '9' && this.checkType =='1') {
                if (this.formData.quoteType == '1' && this.currentRow?.quoteBidLetter == '0'){
                    return false
                }
                return true
            } 
            return false
        }
    },
    data () {
        return {
            optColumnList: [{ type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.handleTableDel }],
            height: 300,
            businessType: 'column',
            // mergeCells: [
            //     { row: 0, col: 1, rowspan: 4, colspan: 1 },
            //     { row: 0, col: 0, rowspan: 4, colspan: 1 }
            // ],
            editRules: {}
        }
    },
    methods: {
        selectedOk (data) {
            let businessTypeMap = {
                column: this.handleAddTableColumn
            }
            businessTypeMap[this.businessType](data)
        },
        // 获取表格下拉字典
        queryDictData (column) {
            const that = this
            if (column && column.dictCode) {
                let postData = {
                    busAccount: that.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: column.dictCode
                }
                ajaxFindDictItems(postData).then((res) => {
                    if (res.success) {
                        let options = res.result.map((dictItem) => {
                            return {
                                value: dictItem.value,
                                label: dictItem.text,
                                title: dictItem.title
                            }
                        })
                        if (column.editRender) {
                            column.editRender.options = options
                        }
                        // dictOptions初始化数据字典的字段会用到
                        column.dictOptions = options
                        that.$forceUpdate()
                    } else {
                        if (column.editRender) {
                            column.editRender.options = []
                        }
                        // dictOptions初始化数据字典的字段会用到
                        column.dictOptions = []
                        that.$forceUpdate()
                    }
                })
            } else {
                if (column.editRender) {
                    column.editRender.options = []
                }
                // dictOptions初始化数据字典的字段会用到
                column.dictOptions = []
                that.$forceUpdate()
            }
        },
        // 添加行
        handleTableAdd () {
            if (!this.currentRow) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFYBx_dbe46160`, '请先选择招标函'))
            this.$emit('handleTableAdd')
        },
        // 删除行
        handleTableDel (vue, row, col, index) {
            this.$emit('handleTableDel', index)
        },
        // 选择列弹窗
        handleSelectTableColumn () {
            if (!this.currentRow) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFYBx_dbe46160`, '请先选择招标函'))
            let url = 'tender/tenderCustomColumn/list'
            let columns = [
                { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ARL_13ecfda`, '列名称') },
                { field: 'fieldCategory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AzA_13e938c`, '列分类') },
                { field: 'fieldType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOAc_2b290e2e`, '字段类型') },
                { field: 'dictCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFJCAo_bc817d6a`, '数据字典编码') },
                { field: 'must_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lS_bf146`, '必填') },
                { field: 'inputOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMC_15630eb`, '填写方') }
            ]
            // 获取已经选择物料列
            let fields = this.currentRow.customizeFieldModel.map((item) => item.field)
            let checkedConfig = {
                checkMethod: ({ row }) => {
                    // 不能重复选择
                    if (fields.includes(row.columnFieldName)) return false
                    // 根据头部的投标函类型：开标一览表(后审和非其他类型时候可以选择报价列) 且 报价类型为总项报价0，分项报价会默认添加报价列，不需要手动添加报价列
                    if (this.checkType == '1' && this.currentRow.formatType != '9' && this.formData.quoteType == '0') {
                        return true
                    } else {
                        if (row.fieldCategory == '1') {
                            return false
                        } else {
                            return true
                        }
                    }
                }
            }
            this.businessType = 'column'
            this.$refs.addColumnModal.open(url, {}, columns, 'multiple', checkedConfig)
        },
        // 列处理
        columnSerialization (item) {
            // 1招标单位, 0投标单位
            let canEdit = item.inputOrg == '1'
            let field = item.columnFieldName || item.field
            this.editRules[field] = []
            // 默认能删除列
            switch (item.fieldType) {
            case 'string':
                item.editRender = { enabled: canEdit, name: '$input' }
                this.editRules[field].push({ max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')})
                break
            case 'dict':
                item.editRender = { enabled: canEdit, name: 'srmSelect', options: this.queryDictData(item) }
                break
            case 'date':
                item.editRender = { enabled: canEdit, name: 'mDatePicker' }
                break
            case 'number':
                item.editRender = { enabled: canEdit, name: '$input', props: { type: 'number' } }
                this.editRules[field].push({ validator: checkNumber, trigger: 'change' })
                break
            }
            // 默认操作列头部
            let slots = {
                header: ({ columnIndex, column }) => {
                    return this.isEdit && !item.canDeleteColumn
                        ? [
                            <span>
                                {item.name || item.title}
                                <a-icon type="delete" onClick={this.handleDeleteColumn.bind(this, columnIndex, column)} />
                            </span>
                        ]
                        : [<span>{item.name || item.title}</span>]
                }
            }
            let itemColumn = {
                title: item.name || item.title,
                field: field,
                fieldType: item.fieldType,
                fieldCategory: item.fieldCategory,
                required: item.must == '1' || item.must == true ? true: false,
                must: item.must == '1' || item.must == true ? true: false,
                inputOrg: item.inputOrg,
                width: 160,
                slots: item.slots || slots,
                editRender: item.editRender,
                canDeleteColumn: item.canDeleteColumn
            }
            if (item.must == '1' || item.must == true) {
                this.editRules[field].push({ required: true, message: `请输入${itemColumn.title}` })
            }
            if (item.dictCode) {
                itemColumn.dictCode = item.dictCode
            }
            return itemColumn
        },
        // 添加列
        handleAddTableColumn (data) {
            let columns = []
            data.map((item) => {
                let column = this.columnSerialization(item)
                columns.push(column)
            })
            this.$emit('handleAddTableColumn', columns)
        },
        handleDeleteColumn (i, col) {
            if (!this.isEdit) return
            // 删除行数据字典
            this.$emit('handleDeleteColumn', {i, col})
        },
        // 操作列方法
        optColumnFuntion (opt, row, col, index) {
            opt.click && opt.click(this, row, col, index)
        },
        // 格式化列
        initColumns (data) {
            if (!data) return
            // 存在列时候需要拼接列自定义
            let flag = false
            data.customizeFieldModel.map((item) => {
                // 存在操作列
                if (item.key == 'seq') {
                    flag = true
                }
            })
            let columnlsit = []
            // 不存在操作列的情况下
            if (!flag) {
                columnlsit = data.customizeFieldModel.map((item) => {
                    return this.columnSerialization(item)
                })
                columnlsit.unshift({
                    type: 'seq',
                    width: 50,
                    key: 'seq',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                })
                // 其他操作类型
                if (data.formatType == '9') {
                    columnlsit.push({
                        slots: { default: 'grid_opration' },
                        width: 50,
                        key: 'grid_opration',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作')
                    })
                }
            } else {
                columnlsit = data.customizeFieldModel.map(item => {
                    if (!['seq', 'grid_opration'].includes(item.key)) return this.columnSerialization(item)
                    return item
                })
            }
            this.$set(data, 'customizeFieldModel', columnlsit)
        },
        maintenanceMaterial () {
            this.$emit('maintenanceMaterial')
        },
        
        reloadData () {
            this.$refs.customizeFieldDataTable.reloadData(this.currentRow.customizeFieldData)
        }
    },
    created () {
        this.height = this.checkType == '1' ? document.documentElement.clientHeight - 390 : document.documentElement.clientHeight - 290
    }
}
</script>
<style lang="less" scoped>
    .margin-l-10{
        margin-left: 10px;
    }
    :deep(.fl){
        float: left;
        max-width: 320px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
