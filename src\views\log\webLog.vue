<template>
  <div class="els-page-container">
    <div>
      <!-- table区域-begin -->
      <div class="els-table-box">
        <div class="els-alert-info">
          <!-- 操作按钮区域 -->
          <div class="table-operator">
            <a-button
              @click="openSocket"
              type="primary"
              icon="plus"
            >
              
              {{ $srmI18n(`${$getLangAccount()}#i18n_title_openLog`, '开启日志') }}
            </a-button>
            <a-button
              icon="closeSocket"
              @click="closeSocket"
            >
              {{ $srmI18n(`${$getLangAccount()}#i18n_title_outputlog`, '关闭日志') }}
            </a-button>
          </div>
        </div>
        <div
          ref="logContainer"
          style="height: 700px; overflow-y: scroll; background: #000000; color: #aaa; padding: 10px;">
          <div
            ref="logContainerDiv"
            v-html="res"
            id="logContent"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SockJS from 'sockjs-client'
import  {Stomp} from '@stomp/stompjs'
export default {
    name: 'ConsolePage',
    data () {
        return {
            stompClient: null,
            res: ''
        }
    },
    methods: {
        openSocket () {
            if (this.stompClient == null) {
                this.res = '<div style=\'color: #18d035;font-size: 14px\'>通道连接成功,静默等待....</div>'
                // this.$refs['logContainerDiv'].append();
                // 建立连接对象
                let socket = new SockJS(this.$variateConfig['domainURL'] + '/websocket?token=kl')
                // 获取STOMP子协议的客户端对象
                this.stompClient = Stomp.over(socket)
                this.stompClient.connect({token: 'kl'}, () => {
                    this.stompClient.subscribe('/topic/pullLogger', (event) =>  {
                        let content = JSON.parse(event.body)
                        let leverhtml = ''
                        let className = '<span style=\'color: #229379\'>'+content.className + '</span>'
                        switch (content.level) {
                        case 'INFO':
                            leverhtml = '<span style=\'color: #90ad2b\'>' +content.level + '</span>'
                            break
                        case 'DEBUG':
                            leverhtml = '<span style=\'color: #A8C023\'>' +content.level + '</span>'
                            break
                        case 'WARN':
                            leverhtml = '<span style=\'color: #fffa1c\'>' +content.level + '</span>'
                            break
                        case 'ERROR':
                            leverhtml = '<span style=\'color: #e3270e\'>' +content.level + '</span>'
                            break
                        }
                        this.res+= '<div style=\'color: #18d035;font-size: 14px\'>' + content.timestamp + ' ' +leverhtml + ' --- [' + content.threadName + '] ' + className + ' ：' + content.body + '</div>'
                        // this.$refs['logContainerDiv'].append(content.timestamp + " " + leverhtml + " --- [" + content.threadName + "] " + className + " ：" + content.body + "<br/>");
                        if (content.exception != '') {
                            this.res+= '<div>' + content.exception + '</div>'
                            // this.$refs['logContainerDiv'].append();
                        }
                        if (content.cause != '') {
                            this.res+= '<div>' + content.cause + '</div>'
                            // this.$refs['logContainerDiv'].append(content.cause);
                        }
                        // this.$refs['logContainer'].scrollTo(this.$refs['logContainerDiv'].height() - this.$refs['logContainer'].height());
                    },
                    {
                        token: 'kltoen'
                    })
                })
            }
        },
        closeSocket () {
            if (this.stompClient != null) {
                this.stompClient.disconnect()
                this.stompClient = null
            }
        }
    },
    mounted () {
        //console.log(this.$refs.logContainerDiv)
    }
}
</script>
<style scoped>
  @import '@assets/less/common.less'
</style>