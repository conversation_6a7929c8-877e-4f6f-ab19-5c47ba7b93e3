<template>
  <div class="els-page-container">
    
    <a-page-header
      :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
      :title="title"
    >
      <template slot="extra">
        <a-button
          @click="handleOk"
          type="primary"
          key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <a-button
          @click="goBack"
          key="3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-page-header>
    <div class="table-page-search-wrapper">
      <a-spin :spinning="confirmLoading">
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            key="1"
            :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
          >
            <a-form :form="form">
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_enterpriseAbbreviation`, '企业简称')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterEnterpriseAbbreviation`, '请输入企业简称')"
                  v-decorator="['shortName', validatorRules.shortName]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_enterpriseFullName`, '企业全称')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterEnterpriseFullName`, '请输入企业全称')"
                  v-decorator="['fullName', validatorRules.fullName]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_enterpriseDesc`, '企业描述')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterEnterpriseDesc`, '请输入企业描述')"
                  v-decorator="['description', validatorRules.description]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_enterpriseType`, '企业类型')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterCompanyOrgType`, '请输入企业类型')"
                  v-decorator="['enterpriseType', validatorRules.enterpriseType]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_field_industry`, '行业')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterIndustryTips`, '请输入行业')"
                  v-decorator="['industry', validatorRules.industry]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_country`, '国家')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterCountry`, '请输入国家')"
                  v-decorator="['country', validatorRules.country]" />
              </a-form-item>

              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_location`, '所在地区')">
                <a-cascader
                  :options="areas"
                  @change="onChange"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectLocation`, '请选择地区')"
                  :value="defaultAreas"
                />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_address`, '地址')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterAddress`, '请输入地址')"
                  v-decorator="['address', validatorRules.address]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_telphone`, '电话')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterTelphone`, '请输入电话')"
                  v-decorator="['phone', validatorRules.phone]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_emailAddress`, '邮箱地址')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseWriteEmailAddress`, '请输入邮箱地址')"
                  v-decorator="['email', validatorRules.email]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_companyWebsite`, '公司网站')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterCompanyWebsite`, '请输入公司网站')"
                  v-decorator="['website', validatorRules.website]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_enterpriseTaxNum`, '企业税号')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterEnterpriseTaxNum`, '请输入企业税号')"
                  v-decorator="['idNumOfTax', validatorRules.idNumOfTax]" />
              </a-form-item>
              
            </a-form>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </div>
  </div>
  <!-- </a-modal> -->
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import pick from 'lodash.pick'
import { duplicateCheck } from '@/api/api'
import { areas } from '@/store/area'

export default {
    name: 'EnterpriseInfoSaleModal',
    data () {
        return {
            activeKey: ['1'],
            fixPageHeader: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterpriseInfo`, '企业基本信息' ),
            visible: false,
            defaultAreas: [],
            areas: areas,
            model: {},
            labelCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            wrapperCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            confirmLoading: false,
            form: this.$form.createForm(this),
            validatorRules: {
                shortName: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                fullName: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterEnterpriseFullName`, '请输入企业全称!' )}, {max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符' )}]},
                description: {rules: [{max: 1000, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow1000`, '内容长度不能超过1000个字符' )}]},
                enterpriseType: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                industry: {rules: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符' )}]},
                country: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                province: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                city: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                county: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                address: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                phone: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                email: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                website: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                idNumOfTax: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]}
            },
            url: {
                add: '/base/enterpriseInfo/add',
                edit: '/base/enterpriseInfo/edit'
            }
        }
    },
    mounted () {
        window.addEventListener('scroll', this.handleScroll)
        this.init()
    },
    methods: {
        add () {
            this.edit({})
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addEnterpriseInfo`, '新增企业基本信息' )
        },
        init (){
            getAction('/base/enterpriseInfo/queryCurrent', {}).then(res => {
                this.edit(res.result)
            })
        },
        edit (record) {
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editEnterpriseInfo`, '编辑企业基本信息' )
            this.form.resetFields()
            this.model = Object.assign({}, record)
            this.visible = true
            this.$nextTick(() => {
                this.form.setFieldsValue(pick(this.model, 'shortName', 'fullName', 'description', 'enterpriseType', 'industry', 'country', 'province', 'city', 'county', 'address', 'phone', 'email', 'website', 'idNumOfTax', 'fbk1', 'fbk2', 'fbk3', 'fbk4', 'fbk5', 'fbk6', 'fbk7', 'fbk8', 'fbk9', 'fbk10', 'fbk11', 'fbk12', 'fbk13', 'fbk14', 'fbk15', 'fbk16', 'fbk17', 'fbk18', 'fbk19', 'fbk20', 'extendField'))
                //时间格式化
            })
            this.defaultAreas = []
            if (record.province) {
                this.defaultAreas = [record.province, record.city, record.county]
            }
        },
        onChange (value) {
            this.defaultAreas = value
            if (value) {
                this.model.province = value[0]
                if (value[1]) {
                    this.model.city = value[1]
                }
                if (value[1]) {
                    this.model.county = value[2]
                }
            } else {
                this.model.province = ''
                this.model.city = ''
                this.model.county = ''
            }
        },
        validateCode (rule, value, callback) {
        // 重复校验
            var params = {
                tableName: '',
                fieldName: '',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then((res) => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    that.confirmLoading = true
                    let httpurl = ''
                    let method = ''
                    if(!this.model.id){
                        httpurl+=this.url.add
                        method = 'post'
                    }else{
                        httpurl+=this.url.edit
                        method = 'post'
                    }
                    let formData = Object.assign(this.model, values)
                    //时间格式化
            
                    console.log(formData)
                    httpAction(httpurl, formData, method).then((res)=>{
                        if(res.success){
                            that.$message.success(res.message)
                            that.$emit('ok')
                            that.goBack()
                        }else{
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        handleScroll () {
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 50) {
                this.fixPageHeader = true
            }else {
                this.fixPageHeader = false
            }
        }
    }
}
</script>
