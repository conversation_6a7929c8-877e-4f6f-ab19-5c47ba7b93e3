import MyWebSocket from '@/utils/websocket.js'

const HEART_BEAT_CONFIG = {
    time: 3 * 1000, // time：心跳时间间隔
    timeout: 3 * 1000, // timeout：心跳超时间隔
    reconnect: 10 * 1000 // reconnect：断线重连时
}
function initOnlineWS (commit, state, wsOnlineUrl, id) {
    if (state.timeOutObj) clearInterval(state.timeOutObj)
    commit('SET_TIMEOUT_OBJ', null)
    commit('SET_PAGE_REFRESH_TIME', null)
    let websocketOnline = new MyWebSocket(wsOnlineUrl)
    websocketOnline.init(state.heartBeatConfig, true)
    websocketOnline.onmessage = (e) => {
        try {
            let data = e.data
            console.log('Online e 1', e)
            if (data) {
                console.log('Online data 1', data)
                if (data === 'heartCheck' || data === 'ONLINE_HEART') { // 心跳
                    websocketOnline.webSocketState = true
                } if (data === id) {
                    websocketOnline.webSocketState = true
                    commit('SET_ONLINE_ID', { time: new Date().getTime(), id })
                } else {
                    websocketOnline.webSocketState = true
                    commit('SET_ONLINE_SUPPLIERS', data)
                }
            }
        } catch (err) {
            console.log('err', err)
        }
    }
    websocketOnline.onerror = (e) => {
        console.log('Online onerror 1', e)
        initOnlineWS(commit, state, wsOnlineUrl, id)
    }
    // websocketOnline.onopen = () => {
    //     console.log('ONLINE_HEART')
    //     websocketOnline.send('ONLINE_HEART')
    // }
    // websocketOnline.onclose = function (e) {
    //     console.log('websocket 断开: ' + e.code + ' ' + e.reason + ' ' + e.wasClean)
    //     console.log(e)
    // }
    return websocketOnline
}

// 强制大厅 30秒刷新
function timeOutRefresh (commit, state) {
    let fn = () => {
        commit('SET_PAGE_REFRESH_TIME', { id: state.onlineId.id, time: new Date().getTime() })
    }
    const timeOutObj = setInterval(fn, 30 * 1000)
    commit('SET_TIMEOUT_OBJ', timeOutObj)
}

const permission = {
    state: {
        heartBeatConfig: HEART_BEAT_CONFIG,
        onlineId: {}, // 报价id - 监控报价的单据
        onlineSuppliers: '', // WS 在线用户
        wsOnlineUrl: '', // WS 地址
        websocketOnline: null,  // WS 实例
        timeOutObj: null,
        pageRefreshTime: null
    },
    getters: {
        // 获取在线账号
        getOnlineSuppliers (state) {
            return state.onlineSuppliers
        },
        // 获取在线ID
        getOnlineID (state) {
            return state.onlineId
        },
        // 获取刷新时间
        getPageRefreshTime (state) {
            return state.pageRefreshTime
        }
    },
    mutations: {
        // 设置id
        SET_ONLINE_ID: (state, data) => {
            state.onlineId = data
        },
        // 设置在线账号
        SET_ONLINE_SUPPLIERS: (state, data) => {
            state.onlineSuppliers = data
        },
        // 设置WS 地址
        SET_ONLINE_URLS: (state, data) => {
            state.wsOnlineUrl = data
        },
        // 设置WS 实例
        SET_ONLINE_WS: (state, data) => {
            state.websocketOnline = data
        },
        // 设置TIMEOUT
        SET_TIMEOUT_OBJ: (state, data) => {
            state.timeOutObj = data
        },
        // 设置TIMEOUT
        SET_PAGE_REFRESH_TIME: (state, data) => {
            state.pageRefreshTime = data
        }
    },
    actions: {
        // 设置WS 地址、实例
        SetOnlineWS ({ commit, state }, data) {
            const { wsOnlineUrl, id } = data
            console.log('Online wsOnlineUrl', wsOnlineUrl, id)
            commit('SET_ONLINE_URLS', wsOnlineUrl)
            commit('SET_ONLINE_ID', { time: new Date().getTime(), id })
            if (!state.websocketOnline) { //第一次执行，初始化
                const websocketOnline = initOnlineWS(commit, state, wsOnlineUrl, id)
                commit('SET_ONLINE_WS', websocketOnline)
                timeOutRefresh(commit, state)
            }
        },
        // 关闭WS
        CloseOnlineWS ({ commit, state }) {
            commit('SET_ONLINE_ID', null)
            if (state.websocketOnline) {
                state.websocketOnline.close()
                commit('SET_ONLINE_WS', null)
            }
            if (state.timeOutObj) clearInterval(state.timeOutObj)
            commit('SET_TIMEOUT_OBJ', null)
            commit('SET_PAGE_REFRESH_TIME', null)
        }
    }
}

export default permission