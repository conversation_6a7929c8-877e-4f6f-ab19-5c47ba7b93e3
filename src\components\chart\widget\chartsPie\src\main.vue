<!--
 * @Author: fzb
 * @Date: 2022-02-21 16:28:52
 * @LastEditTime: 2022-03-01 14:35:13
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\components\designer\widget\chartsBar\src\main.vue
-->
<template>
  <vue-echarts
    :style="style"
    :option="widget.option"
    :update-options="{ notMerge: true }"
    autoresize
  />
</template>
<script>
import { chartsMixins } from '../../mixins/chartsMixins'
export default {
  name: 'ChartsPie',
  mixins:[chartsMixins],
  computed: {
    option() {
      return this.widget.option.series[0].customRadius;
    }
  },
  watch: {
    option: {
      handler(val) {
        this.widget.option.series[0].radius[0] = val[0] + "%";
        this.widget.option.series[0].radius[1] = val[1] + "%";
      },
    }
  }
}
</script>