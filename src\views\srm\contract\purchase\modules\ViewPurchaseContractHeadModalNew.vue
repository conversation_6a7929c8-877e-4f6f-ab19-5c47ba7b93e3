<template>
  <div class="PurchaseEightDisciplinesHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="isView"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :current-edit-row="currentEditRow"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :collapseHeadCode="['baseForm', 'busRule', 'personFrom']"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleAfterRemoteConfigData="handleAfterRemoteConfigData"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
      <a-modal
        v-drag
        :width="1000"
        :height="500"
        v-model="fileCompareVisible"
        title="对比结果"
        @ok="fileCompareVisible = false"
      >
        <vxe-grid
          :height="300"
          v-bind="recordGridOptions"
        >
        </vxe-grid>
      </a-modal>
      <a-modal
        v-drag
        centered
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
        :width="360"
        v-model="templateVisible"
        @ok="selectedTemplateAfter"
      >
        <template slot="footer">
          <a-button
            key="back"
            @click="handleTempCancel"
          >
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
          </a-button>
          <a-button
            key="submit"
            type="primary"
            :loading="submitLoading"
            @click="selectedTemplateAfter"
          >
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
          </a-button>
        </template>
        <m-select
          v-model="templateNumber"
          :options="templateOpts"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"
        />
      </a-modal>
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"
      />
      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk"
      />
      <a-modal
        v-drag
        forceRender
        :visible="editRowModal"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')"
        :width="800"
        @ok="confirmEdit"
        @cancel="closeEditModal"
      >
        <j-editor
          v-if="editRowModal"
          v-model="currentItemContent"
        ></j-editor>
      </a-modal>
      <a-modal
        v-drag
        v-model="previewModal"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
        :footer="null"
        :width="1000"
      >
        <div
          style="width: 210mm; margin: 0 auto; padding: 2.54mm 3.18mm; border: 1px solid #ccc; overflow: auto"
          v-html="previewContent"
        ></div>
      </a-modal>
      <view-item-diff-modal ref="viewDiffModal" />
      <His-Contract-Item-Modal ref="hisContractItemModal" />
    </a-spin>
  </div>
</template>

<script lang="jsx">
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import ViewItemDiffModal from './ViewItemDiffModal'
import HisContractItemModal from './HisContractItemModal'
import { getAction, httpAction, postAction } from '@/api/manage'
import JEditor from '@comp/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import { axios } from '@/utils/request'
import { cloneDeep } from 'lodash'
import {
    bindDefaultValue,
    getLangAccount,
    srmI18n,
    composePromise,
    getObjType,
    createPromise,
    isDef,
    downloadTemplate,
    handValidate
} from '@/utils/util.js'

export default {
  name: 'PurchaseContractHeadModal',
  mixins: [businessUtilMixin],
  components: {
    flowViewModal,
    BusinessLayout,
    fieldSelectModal,
    ViewItemDiffModal,
    HisContractItemModal,
    JEditor
  },
  props: {
    currentEditRow: {
      required: true,
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      recordGridOptions: {
        columns: [],
        data: []
      },
      fileCompareVisible: false,
      isView: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 15 },
      templateVisible: false,
      refresh: true,
      submitLoading: false,
      nextOpt: true,
      currentRow: {},
      fileSelectRow: {},
      templateNumber: undefined,
      templateOpts: [],
      businessType: 'order',
      showHelpTip: false,
      editRowModal: false,
      previewModal: false,
      orderAllow: true,
      notShowTableSeq: true,
      editItemRow: {},
      currentItemContent: '',
      previewContent: '',
      confirmLoading: false,
      currentBasePath: this.$variateConfig['domainURL'],
      flowView: false,
      flowId: 0,
      requestData: {
        detail: {
          url: '/contract/purchaseContractHead/queryById',
          args: (that) => {
            return { id: that.currentEditRow.id }
          }
        }
      },
      externalToolBar: {
        purchaseContractItemList: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreate`, '创建订单'),
            key: 'gridCreate',
            click: this.createOrder,
            show: false, // this.showCreateOrderBtn, 业务要求隐藏 创建订单 按钮
            authorityCode: 'contract#purchaseContractHead:createOrderByItems',
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cIIZt_b19ad3f5`, '创建履约单'),
            key: 'gridCreate',
            click: this.createPromise,
            show: this.showCreatePromiseItemBtn,
            authorityCode: 'contract#purchaseContractHead:createPromiseByItems',
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bLumdWF_80867b03`, '生成价格主数据'),
            key: 'gridCreate',
            click: this.createPrice,
            show: this.showCreatePriceBtn,
            authorityCode: 'contract#purchaseContractHead:createPrice',
            attrs: {
              type: 'primary'
            }
          },
          {
            title: '作废',
            key: 'gridCreate',
            click: this.batchCancel,
            show: () => true,
            authorityCode: 'contract#purchaseContractHead:batchCancel',
            attrs: {
              type: 'primary'
            }
          }
        ],
        purchaseContractContentItemList: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            click: this.addContentItemRow,
            show: false,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            show: false,
            click: this.deleteContentGridItem
          }
        ],
        purchaseContractPromiseList: [],
        contractItemCustom1List: [],
        contractItemCustom2List: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            show: false,
            click: this.businessGridAdd,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            show: false,
            click: this.businessGridDelete
          }
        ],
        contractItemCustom3List: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            show: false,
            click: this.businessGridAdd,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            show: false,
            click: this.businessGridDelete
          }
        ],
        contractItemCustom4List: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            show: false,
            click: this.businessGridAdd,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            show: false,
            click: this.businessGridDelete
          }
        ],
        contractItemCustom5List: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            key: 'gridAdd',
            show: false,
            click: this.businessGridAdd,
            attrs: {
              type: 'primary'
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            key: 'gridDelete',
            show: false,
            click: this.businessGridDelete
          }
        ],
        purchaseAttachmentList: [
          // {
          //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
          //     key: 'upload',
          //     args: {
          //         property: 'label', // 可省略
          //         itemInfo: [], // 必传
          //         action: '/attachment/purchaseAttachment/upload', // 必传
          //         businessType: 'contract', // 必传,
          //         itemNumberKey: 'materialNumber',
          //         itemNumbeValueProp: 'value',
          //         itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
          //         headId: '', // 必传
          //         modalVisible: false // 必传
          //     },
          //     callBack: this.uploadCallBack
          // },
          // {
          //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
          //     key: 'gridDelete',
          //     click: this.businessGridDelete
          // }
        ]
      },
      pageHeaderButtons: [
        /*{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                    attrs: {
                        type: 'primary'
                    },
                    authorityCode: 'contract#purchaseContractHead:getPreviewData',
                    click: this.preview
                },*/
        // {
        //   key: 'submit',
        //   attrs: {
        //       type: 'primary'
        //   },
        //   args: {
        //     url: '/contract/purchaseContractHead/submitOA'
        //   },
        //   title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '提交OA审批'),
        //   click: this.handleCustomSubmit,
        //   show: this.handleShowCustomSubmit,
        //   handleBefore: this.handleSubmitBefore //this.handleOaSubmitBefore
        // },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
          attrs: {
            type: 'primary'
          },
          authorityCode: 'contract#purchaseContractHead:getPreviewData',
          click: this.previewPdf
        },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
          attrs: {
            type: 'primary'
          },
          click: this.showFlow,
          show: this.showFlowConditionBtn
        },
        // {
        //   title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
        //   args: {
        //     url: '/a1bpmn/audit/api/cancel'
        //   },
        //   attrs: {
        //     type: 'primary'
        //   },
        //   click: this.cancelAudit,
        //   show: this.showCncelConditionBtn
        // },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
          attrs: {
            type: 'primary'
          },
          authorityCode: 'contract#purchaseContractHead:download',
          click: this.downloadFile
        },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IKWord_12f184fc`, '下载Word'),
          attrs: {
            type: 'primary'
          },
          authorityCode: 'contract#purchaseContractHead:download',
          click: this.downloadWordFile
        },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
          key: 'goBack'
        }
      ],
      url: {
        detail: '/contract/purchaseContractHead/queryById',
        createOrderByItems: '/contract/purchaseContractHead/createOrderByItems',
        createPromiseByItems: '/contract/purchaseContractHead/createPromiseByItems',
        createPriceByItems: '/contract/purchaseContractHead/createPriceByItems',
        cancelAudit: '/a1bpmn/audit/api/cancel',
        downloadUrl: '/attachment/purchaseAttachment/download', // 附件下载地址
        download: '/contract/purchaseContractHead/download',
        downloadDoc: '/contract/purchaseContractHead/downloadDoc',
        isExistFrozen: 'supplier/supplierMaster/isExistFrozenStateData'
      }
    }
  },

  computed: {
    remoteJsFilePath() {
      let templateNumber = this.currentEditRow.templateNumber
      let templateVersion = this.currentEditRow.templateVersion
      let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
      return `${account}/purchase_contract_${templateNumber}_${templateVersion}`
    }
  },
  mounted() {
    const that = this
    getAction('/contract/purchaseContractHead/queryById', { id: this.currentEditRow.id }).then((res) => {
      if (res.success) {
        if (res.result) {
          that.currentEditRow.templateNumber = res.result.templateNumber
          that.currentEditRow.templateVersion = res.result.templateVersion
          that.currentEditRow.templateAccount = res.result.templateAccount
          that.isView = true
        } else {
          this.$message.error('查询失败')
        }
      }
    })

    console.log(123, this.allowEdit)
    if (this.allowEdit) {
      let pageHeaderButtons = cloneDeep(this.pageHeaderButtons)
      pageHeaderButtons.splice(5, 0, { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), click: this.toEdit })
      this.pageHeaderButtons = pageHeaderButtons
    }
  },
  methods: {
    formatPageData(item) {
      console.log('請求接口後格式化页面數據', item)
      if (item.totalTaxAmount === 0 || Math.abs(item.totalTaxAmount) > 0) {
        item.totalTaxAmount = Number(item.totalTaxAmount).toFixed(2)
      }
      if (item.totalNetAmount === 0 || Math.abs(item.totalNetAmount) > 0) {
        item.totalNetAmount = Number(item.totalNetAmount).toFixed(2)
      }

      return item
    },
    // 請求接口後格式化列表數據
    formatTableData(data) {
      let purchaseContractItemList = data.purchaseContractItemList || []
      console.log('請求接口後格式化列表數據', purchaseContractItemList)
      purchaseContractItemList = purchaseContractItemList.map((item) => {
        if (item.price === 0 || Number(item.price) > 0) {
          item.price = Number(item.price).toFixed(6)
        }
        if (item.taxAmount === 0 || Number(item.taxAmount) > 0) {
          item.taxAmount = Number(item.taxAmount).toFixed(2)
        }
        if (item.netAmount === 0 || Number(item.netAmount) > 0) {
          item.netAmount = Number(item.netAmount).toFixed(2)
        }
        if (item.totalTaxAmount === 0 || Number(item.totalTaxAmount) > 0) {
          item.totalTaxAmount = Number(item.totalTaxAmount).toFixed(2)
        }
        if (item.totalNetAmount === 0 || Number(item.totalNetAmount) > 0) {
          item.totalNetAmount = Number(item.totalNetAmount).toFixed(2)
        }
        return item
      })
      data.purchaseContractItemList = purchaseContractItemList
      return data
    },
    confirmEdit() {},
    handleAfterRemoteConfigData(configData) {
      configData.itemColumns.forEach((column) => {
        if (column.field == 'sourceNumber') {
          column['extend'] = {
            linkConfig: {
              primaryKey: 'sourceNumber',
              actionPath: '',
              bindKey: 'sourceNumber',
              otherQuery: { linkFilter: true }
            },
            handleBefore: function (that, form, linkConfig) {
              let { sourceType } = form._row
              switch (sourceType) {
                case 'material': // 物料
                  linkConfig.actionPath = '/srm/material/PurchaseMaterialHeadList'
                  linkConfig.primaryKey = 'materialNumber'
                  break
                case 'purchaseRequest': // 采购申请
                  linkConfig.actionPath = '/srm/demand/PurchaseRequestHeadList'
                  linkConfig.primaryKey = 'requestNumber'
                  break
                case 'order': // 采购订单
                  linkConfig.actionPath = '/srm/order/purchase/PurchaseOrderHeadList'
                  linkConfig.primaryKey = 'orderNumber'
                  break
                case 'bidding': // bidding
                  linkConfig.actionPath = ''
                  linkConfig.primaryKey = ''
                  break
                case 'ebidding': // 在线竞价
                  linkConfig.actionPath = '/srm/ebidding/EbiddingBuyHeadList'
                  linkConfig.primaryKey = 'ebiddingNumber'
                  break
                case 'enquiry': // 询报价
                  linkConfig.actionPath = '/srm/enquiry/purchase/PurchaseEnquiryList'
                  linkConfig.primaryKey = 'enquiryNumber'
                  break
                default:
                  linkConfig.actionPath = ''
                  linkConfig.primaryKey = ''
                  that.$message.warning('未设置该来源类型跳转')
              }
            }
          }
        }
      })
    },
    handleBeforeRemoteConfigData() {
      return {
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
            groupNameI18nKey: '',
            groupCode: 'purchaseAttachmentList',
            groupType: 'item',
            sortOrder: '10',
            extend: {
              optColumnList: [
                {
                  key: 'download',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                  click: this.downloadEvent
                },
                {
                  key: 'preView',
                  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                  click: this.preViewEvent
                },
                {
                  key: 'preView',
                  title: this.$srmI18n(`${this.$getLangAccount()}#`, '比对'),
                  click: this.fileCompare,
                  authorityCode: 'compare#elsFileCompareHead:edit'
                },
                {
                  key: 'preView',
                  title: this.$srmI18n(`${this.$getLangAccount()}#`, '比对结果'),
                  click: this.fileCompareResult,
                  authorityCode: 'compare#elsFileCompareHead:list'
                }
              ]
            }
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLibrary`, '合同条款库'),
            groupNameI18nKey: '',
            groupCode: 'purchaseContractContentItemList',
            groupType: 'item',
            sortOrder: '4',
            show: true,
            extend: {
              optColumnList: []
            }
          },
          {
            groupName: '关联订单',
            groupNameI18nKey: 'i18n_field_RKIt_26f94cf4',
            groupCode: 'orderItemList',
            groupType: 'item',
            sortOrder: '5',
            show: false, // 业务要求隐藏
            extend: {
              optColumnList: []
            }
          }
        ],
        itemColumns: [
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
            groupCode: 'orderItemList',
            field: 'orderNumber',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true,
            extend: {
              linkConfig: {
                primaryKey: 'orderNumber',
                actionPath: '',
                bindKey: 'orderNumber',
                otherQuery: { linkFilter: true }
              },
              handleBefore: function (that, form, linkConfig) {
                linkConfig.actionPath = '/srm/order/purchase/PurchaseOrderHeadList'
                linkConfig.primaryKey = 'orderNumber'
              }
            }
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
            groupCode: 'orderItemList',
            field: 'itemNumber',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseType`, '采购类型'),
            field: 'purchaseType_dictText',
            groupCode: 'orderItemList',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ItczE_d79d4484`, '订单行状态'),
            field: 'itemStatus_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'),
            groupCode: 'orderItemList',
            field: 'factory_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageLocation`, '库存地点'),
            groupCode: 'orderItemList',
            field: 'storageLocation_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
            groupCode: 'orderItemList',
            field: 'materialNumber',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialDesc`, '物料描述'),
            groupCode: 'orderItemList',
            field: 'materialDesc',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),
            groupCode: 'orderItemList',
            field: 'materialSpec',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroup`, '物料组'),
            groupCode: 'orderItemList',
            field: 'materialGroupCode',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroupName`, '物料组名称'),
            groupCode: 'orderItemList',
            field: 'materialGroupName',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'),
            groupCode: 'orderItemList',
            field: 'purchaseCycle',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_requiredDeliveryDate`, '采购周期'),
            groupCode: 'orderItemList',
            field: 'requireDate',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_unitQuantity`, '数量单位'),
            groupCode: 'orderItemList',
            field: 'quantityUnit',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_order_quantity`, '数量单位'),
            groupCode: 'orderItemList',
            field: 'quantity',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税价'),
            groupCode: 'orderItemList',
            field: 'price',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价'),
            groupCode: 'orderItemList',
            field: 'netPrice',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xfku_2818dc9f`, '含税总价'),
            groupCode: 'orderItemList',
            field: 'taxAmount',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xxfku_73f95c2c`, '含税总价'),
            groupCode: 'orderItemList',
            field: 'netAmount',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_wjneEy_ad073b7a`, '来源合同行号'),
            groupCode: 'orderItemList',
            field: 'sourceItemNumber',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            dictCode: '',
            required: '0',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectNumber`, '项目编号'),
            fieldLabelI18nKey: 'i18n_title_projectNumber',
            field: 'itemId',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            dictCode: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
            fieldLabelI18nKey: 'i18n_title_projectName',
            field: 'itemName',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
            fieldLabelI18nKey: 'i18n_title_itemType',
            field: 'itemType_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_itemVersion`, '项目版本'),
            fieldLabelI18nKey: 'i18n_title_projectVersion',
            field: 'itemVersion',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeIdentification`, '变更标识'),
            fieldLabelI18nKey: 'i18n_title_changeIdentification',
            field: 'changeFlag',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            cellRender: {
              name: '$switch',
              type: 'visible',
              props: { closeValue: '0', openValue: '1', disabled: true }
            },
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sourceType`, '来源类型'),
            fieldLabelI18nKey: 'i18n_title_sourceType',
            field: 'sourceType_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            disabled: true
          },
          {
            groupCode: 'purchaseContractContentItemList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
            fieldLabelI18nKey: 'i18n_title_operation',
            field: 'sourceType_dictText',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            align: 'left',
            slots: {
              default: ({ row }) => {
                let resultArray = []
                resultArray.push(
                  <a
                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                    onClick={() => this.viewDetail(row)}
                  >
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                  </a>
                )
                if (row.changeFlag == '1') {
                  resultArray.push(
                    <a
                      title='比对'
                      style='margin-left:8px'
                      onClick={() => this.viewDiff(row)}
                    >
                      比对
                    </a>
                  )
                }
                return resultArray
              }
            }
          },
          {
            groupCode: 'purchaseAttachmentList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
            fieldLabelI18nKey: '',
            field: 'fileName',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150'
          },
          {
            groupCode: 'purchaseAttachmentList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
            fieldLabelI18nKey: '',
            field: 'uploadTime',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150'
          },
          {
            groupCode: 'purchaseAttachmentList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
            fieldLabelI18nKey: '',
            field: 'uploadElsAccount_dictText',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150'
          },
          {
            groupCode: 'purchaseAttachmentList',
            fieldLabelI18nKey: '',
            field: 'uploadSubAccount_dictText',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: 120
          },
          {
            groupCode: 'purchaseAttachmentList',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
            fieldLabelI18nKey: '',
            field: 'grid_opration',
            align: 'center',
            headerAlign: 'center',
            defaultValue: '',
            width: '150',
            slots: { default: 'grid_opration' }
          }
        ]
      }
    },
    handleAfterDealSource(pageConfig, resultData) {
      let formModel = pageConfig.groups[0].formModel
      for (let key in resultData) {
        formModel[key] = resultData[key]
      }

      // this.externalToolBar['purchaseAttachmentList'][0].args.headId = resultData.id || ''
      // let itemInfo = pageConfig.groups
      //     .map(n => ({ label: n.groupName, value: n.groupCode }))
      // this.externalToolBar['purchaseAttachmentList'][0].args.itemInfo = itemInfo
      if (resultData.showCustom1 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', false)
      }
      if (resultData.showCustom2 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', false)
      }
      if (resultData.showCustom3 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', false)
      }
      if (resultData.showCustom4 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', false)
      }
      if (resultData.showCustom5 == '0') {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', true)
      } else {
        this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', false)
      }
      if (resultData.showItem == '1') {
        this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', false)
      } else {
        this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', true)
      }
      if ((resultData.contractStatus == '3' || resultData.contractStatus == '8' || resultData.contractStatus == '6') && (resultData.promiseType == 'promiseSale' || resultData.promiseType == 'promisePurchase')) {
        this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', false)
      } else {
        this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', true)
      }

      // 业务要求 关联订单隐藏
      // if (resultData.promiseType == 'order' && (resultData.contractStatus == '3' || resultData.contractStatus == '8' || resultData.contractStatus == '6')) {
      //     this.hideSingleGroup(this.businessRefName, 'orderItemList', false)
      // } else {
      //     this.hideSingleGroup(this.businessRefName, 'orderItemList', true)
      // }
    },
    showCreateOrderBtn({ pageData, pageConfig }) {
      //①	如果合同启用电子签章，则“合同状态”必须为“已归档”，才可点击。
      //②	如果合同不启用电子签章，则“合同状态”必须为“已归档”或者“供应商已确认
      let signFlag = pageData.sign == '1' && pageData.contractStatus == '6'
      let notSignFlag = pageData.sign !== '1' && (pageData.contractStatus == '6' || pageData.contractStatus == '3')
      if ((signFlag || notSignFlag) && pageData.promiseType == 'order') {
        return true
      } else {
        return false
      }
    },
    showCreatePromiseBtn({ pageData, pageConfig }) {
      //①	如果合同启用电子签章，则“合同状态”必须为“已归档”，才可点击。
      //②	如果合同不启用电子签章，则“合同状态”必须为“已归档”或者“供应商已确认
      let signFlag = pageData.sign == '1' && pageData.contractStatus == '6'
      let notSignFlag = pageData.sign !== '1' && (pageData.contractStatus == '6' || pageData.contractStatus == '3')
      if ((signFlag || notSignFlag) && pageData.promiseType == 'promisePurchase') {
        return true
      } else {
        return false
      }
    },
    showCreatePromiseItemBtn({ pageData, pageConfig }) {
      let signFlag = pageData.sign == '1' && pageData.contractStatus == '6'
      let notSignFlag = pageData.sign !== '1' && (pageData.contractStatus == '6' || pageData.contractStatus == '3')
      if ((signFlag || notSignFlag) && pageData.promiseType == 'promisePurchase') {
        return true
      } else {
        return false
      }
    },
    showCreatePriceBtn({ pageData, pageConfig }) {
      if (['3', '6'].includes(pageData.contractStatus) && pageData.auditStatus === '2') {
        return false
      } else {
        return true
      }
    },
    batchCancel() {
      const selectedRows = this.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
      const datas = selectedRows.filter((r) => Boolean(r.id)).map((r) => r.id)
      if (!datas.length) {
        this.$message.warning('请选择需要作废的数据')
        return
      }
        let that = this
        that.$confirm({
           title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_void`, '作废'),
           content: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
           onOk: function () {
               that.confirmLoading = true
               postAction('/contract/purchaseContractHead/batchCancelContractItem', datas)
                   .then((res) => {
                       if (res.success) {
                           that.$message.success(res.message)
                           for (const data of selectedRows) {
                               if (!data.id) {
                                   continue
                               }
                               data.contractItemStatus = '1'
                               data.contractItemStatus_dictText = '是'
                           }
                       } else {
                           that.$message.warning(res.message)
                       }
                   })
                   .finally(() => {
                       that.confirmLoading = false
                   })
           },
          onCancel:function (){
              that.confirmLoading = false
          }
      })
    },
    showCncelConditionBtn({ pageData }) {
      console.log(pageData)
      if (pageData.auditStatus == '1') {
        return true
      } else {
        return false
      }
    },
    fileCompareResult(Vue, row) {
      this.confirmLoading = true
      getAction('/compare/elsFileCompareHead/getResultById', { id: row.id })
        .then((res) => {
          if (res.success) {
            if (res.result.elsFileCompareResultList && res.result.elsFileCompareResultList.length == 1) {
              let filePath = res.result.elsFileCompareResultList[0].filePath
              this.$previewFile.open({ params: {}, path: filePath })
            } else {
              this.recordGridOptions.columns = [
                {
                  title: '比对时间',
                  fieldLabelI18nKey: 'i18n_field_lIKI_326a4423',
                  field: 'createTime',
                  helpText: ''
                },
                {
                  title: '文件类型',
                  fieldLabelI18nKey: 'i18n_field_fileType',
                  field: 'fbk2_dictText',
                  helpText: ''
                },
                {
                  title: '文件名称',
                  fieldLabelI18nKey: 'i18n_title_fileName',
                  field: 'fileName',
                  helpText: ''
                },
                {
                  title: '文件路径',
                  fieldLabelI18nKey: 'i18n_field_filePath',
                  field: 'filePath',
                  helpText: ''
                },
                {
                  title: '操作',
                  fieldLabelI18nKey: 'i18n_title_operation',
                  field: 'sourceType_dictText',
                  headerAlign: 'center',
                  slots: {
                    default: ({ row }) => {
                      let resultArray = []
                      resultArray.push(
                        <a
                          title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '预览')}
                          onClick={() => this.resultView(row)}
                        >
                          {' '}
                          {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '预览')}
                        </a>
                      )
                      resultArray.push(
                        <a
                          title={this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}
                          style='margin-left:8px'
                          onClick={() => this.resultDownload(row)}
                        >
                          {' '}
                          {this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}
                        </a>
                      )
                      return resultArray
                    }
                  }
                }
              ]
              this.recordGridOptions.data = res.result.elsFileCompareResultList
              this.fileCompareVisible = true
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
      /*this.$router.push({
                path: '/srm/compare/ElsFileCompareHeadList',
                query: {sourceId: row.id}
            })*/
    },
    resultView(row) {
      this.$previewFile.open({ params: {}, path: row.filePath })
    },
    resultDownload(row) {
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = row.filePath
      link.setAttribute('download', row.fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) //下载完成移除元素
      window.URL.revokeObjectURL(row.filePath) //释放掉blob对象
    },
    fileCompare(Vue, row) {
      this.fileSelectRow = row
      let item = {
        selectModel: 'single',
        sourceUrl: '/attachment/purchaseAttachment/listFileCompare',
        params: {
          businessType: 'contract'
        },
        columns: [
          {
            field: 'businessType_dictText',
            fieldLabelI18nKey: 'i18n_field_businessType',
            title: '业务类型',
            with: 150
          },
          {
            field: 'fileType_dictText',
            fieldLabelI18nKey: 'i18n_field_fileType',
            title: '文件类型',
            with: 150
          },
          {
            field: 'uploadElsAccount',
            fieldLabelI18nKey: 'i18n_field_uploadElsAccount',
            title: '文件上传方账号',
            with: 150
          },
          {
            field: 'uploadSubAccount',
            fieldLabelI18nKey: 'i18n_title_fileUploadPartySubAccount',
            title: '文件上传方子账号',
            with: 150
          },
          {
            field: 'uploadTime',
            fieldLabelI18nKey: 'i18n_title_uploadTime',
            title: '上传时间',
            with: 150
          },
          {
            field: 'fileName',
            fieldLabelI18nKey: 'i18n_title_fileName',
            title: '文件名称',
            with: 150
          },
          {
            field: 'filePath',
            fieldLabelI18nKey: 'i18n_field_filePath',
            title: '文件路径',
            with: 150
          },
          {
            field: 'fileSize',
            fieldLabelI18nKey: 'i18n_field_fileSize',
            title: '文件大小',
            with: 150
          }
        ]
      }
      this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
    },
    compareFileTypes(file1, file2) {
      const getFileType = (filename) => {
        const fileExtension = filename.split('.').pop()
        return fileExtension
      }
      const type1 = getFileType(file1)
      const type2 = getFileType(file2)
      if ((type1 == 'doc' && type2 == 'docx') || (type1 == 'docx' && type2 == 'doc')) return true
      if ((type1 == 'xlsx' && type2 == 'xls') || (type1 == 'xls' && type2 == 'xlsx')) return true
      return type1 === type2
    },
    fieldSelectOk(data) {
      let flag = this.compareFileTypes(this.fileSelectRow.fileName, data[0].fileName)
      if (!flag) return this.$message.warning('不同类型文件不做对比')
      this.confirmLoading = true
      let fileCompareRow = {
        fileASourceId: this.fileSelectRow.id,
        fileABusinessType: this.fileSelectRow.businessType,
        fileAType: this.fileSelectRow.fileType,
        fileAUploadElsAccount: this.fileSelectRow.uploadElsAccount,
        fileAUploadSubAccount: this.fileSelectRow.uploadSubAccount,
        fileAUploadTime: this.fileSelectRow.uploadTime,
        fileAName: this.fileSelectRow.fileName,
        fileAPath: this.fileSelectRow.filePath,
        fileASize: this.fileSelectRow.fileSize,
        fileBSourceId: data[0].id,
        fileBBusinessType: data[0].businessType,
        fileBType: data[0].fileType,
        fileBUploadElsAccount: data[0].uploadElsAccount,
        fileBUploadSubAccount: data[0].uploadSubAccount,
        fileBUploadTime: data[0].uploadTime,
        fileBName: data[0].fileName,
        fileBPath: data[0].filePath,
        fileBSize: data[0].fileSize
      }
      postAction('/compare/elsFileCompareHead/fileCompare', fileCompareRow)
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    createOrder() {
      this.nextOpt = true
      this.serachTemplate('order')
    },
    createPromise() {
      this.nextOpt = true
      this.serachTemplate('contractPromise')
    },
    createPrice() {
      this.nextOpt = true
      this.serachTemplate('price')
    },
    viewDiff(row) {
      this.$refs.viewDiffModal.open(row)
    },
    viewDetail(row) {
      this.$refs.hisContractItemModal.open(row)
    },
    preview() {
      let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
      if (!contentGrid.getTableData().tableData.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
        return
      }
      this.confirmLoading = true
      getAction('/contract/purchaseContractHead/getPreviewData', { id: this.currentEditRow.id })
        .then((res) => {
          if (res.success) {
            this.previewModal = true
            this.previewContent = res.result
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    previewPdf() {
      let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
      if (!contentGrid.getTableData().tableData.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
        return
      }
      this.confirmLoading = true
      axios({
        url: '/contract/purchaseContractHead/download',
        responseType: 'blob',
        params: { id: this.currentEditRow.id }
      })
        .then((res) => {
          if (res) {
            debugger
            let url = window.URL.createObjectURL(new Blob([res], { type: 'application/pdf' }))
            window.open(url)
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },

    preViewEvent(Vue, row) {
      let preViewFile = row
      this.$previewFile.open({ params: preViewFile })
    },
    cancelAudit() {
      let that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
        onOk: function () {
          that.auditPostData(that.url.cancelAudit)
        }
      }) /*.finally(() => {
                that.init()
            })*/
    },
    serachTemplate(businessType) {
      this.currentRow = {}
      if (businessType == 'order') {
        let selectedRows = this.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
        if (selectedRows.length <= 0) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
          return
        } else {
          selectedRows.forEach((item, i) => {
            if (item.sourceType == 'order') {
              this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行来源为订单行')
              this.nextOpt = false
              return
            }
          })
          if (this.nextOpt) {
            this.businessType = businessType
            this.openModal()
          }
        }
      } else if (businessType == 'price') {
        let selectedRows = this.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
        if (selectedRows.length <= 0) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
          return
        } else {
          // selectedRows.forEach((item, i) => {
          //     if (item.sourceType != 'material') {
          //         this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行来源不是物料主数据')
          //         this.nextOpt = false
          //         return
          //     }
          // })
          if (this.nextOpt) {
            this.businessType = businessType
            this.openModal()
          }
        }
      } else {
        let parm = this.getAllData()
        let selectedItemRows = []
        if (parm.showCustom1 == '1') {
          selectedItemRows = this.getItemGridRef('contractItemCustom1List').getCheckboxRecords()
        }
        if (parm.showItem == '1' && selectedItemRows.length == 0) {
          selectedItemRows = this.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
        }

        if (selectedItemRows.length <= 0) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
          return
        } else {
          /*selectedRows.forEach((item, i) => {
                        if (item.sourceType == 'order') {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行来源为订单行')
                            this.nextOpt = false
                            return
                        }
                    })*/
          if (this.nextOpt) {
            this.businessType = businessType
            this.openModal()
          }
        }
      }
    },
    queryTemplateList(elsAccount) {
      let params = { elsAccount: elsAccount, businessType: this.businessType }
      return getAction('/template/templateHead/getListByTypeDefault', params)
    },
    openModal() {
      // 每次打开重置选中值
      this.templateNumber = ''
      this.queryTemplateList(this.$ls.get('Login_elsAccount')).then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            let options = res.result.map((item) => {
              return {
                value: item.templateNumber,
                title: item.templateName,
                version: item.templateVersion,
                account: item.elsAccount
              }
            })
            this.templateOpts = options
            // 只有单个模板直接新建
            if (this.templateOpts && this.templateOpts.length === 1) {
              this.templateNumber = this.templateOpts[0].value
              this.selectedTemplateAfter()
            } else {
              // 有多个模板先选择在新建
              this.templateVisible = true
            }
          } else {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleTempCancel() {
      this.templateVisible = false
    },
    selectedTemplateAfter() {
      if (this.templateNumber) {
        const that = this
        let parm = that.getAllData()
        this.submitLoading = true
        let template = this.templateOpts.filter((item) => {
          return item.value == that.templateNumber
        })
        let params = {
          templateNumber: this.templateNumber,
          templateName: template[0].title,
          templateVersion: template[0].version,
          templateAccount: template[0].account,
          purchaseContractItemList: [],
          contractItemCustom1List: []
        }
        if (this.businessType == 'order') {
          this.url.temp = this.url.createOrderByItems
        }
        if (this.businessType == 'contractPromise') {
          this.url.temp = this.url.createPromiseByItems
        }
        if (this.businessType == 'price') {
          this.url.temp = this.url.createPriceByItems
          params['id'] = this.currentEditRow.id
        }
        if (parm.showItem == '1') {
          params.purchaseContractItemList = that.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
        }
        if (parm.showCustom1 == '1') {
          params.contractItemCustom1List = that.getItemGridRef('contractItemCustom1List').getCheckboxRecords()
        }
        that.templateVisible = false
        that.submitLoading = false
        if (this.url.temp == '') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
          return
        }
        that.postUpdateData(this.url.temp, params)
      }
    },
    postUpdateData(url, row) {
      this.confirmLoading = true
      httpAction(url, row, 'post')
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.init()
          } else {
            let resMsg = res.message
            if (!!resMsg && resMsg.indexOf('\n') >= 0) {
              const h = this.$createElement
              let strList = resMsg.split('\n')
              strList = strList.map((str, strIndex) => {
                return h(strIndex === 0 ? 'span' : 'div', null, str)
              })
              resMsg = h('span', null, strList)
            }
            this.$message.warning(resMsg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    paramIntegrate() {
      let pageData = this.getAllData() || {}
      let { busRule = {}, personFrom = {} } = pageData || {}

      // pageData = { ...pageData, ...busRule, ...personFrom}
      delete pageData.busRule
      delete pageData.personFrom

      pageData = Object.assign({}, pageData, busRule, personFrom)
      return pageData
    },
    auditPostData(invokeUrl) {
      this.confirmLoading = true
      let formData = this.paramIntegrate()
      let param = {}
      param['businessId'] = formData.id
      param['rootProcessInstanceId'] = formData.flowId
      param['businessType'] = 'contract'
      param['auditSubject'] = '合同编号：' + formData.contractNumber + ' ' + formData.contractName || ''
      // param['auditSubject'] = '合同编号：'+formData.contractNumber
      param['params'] = JSON.stringify(formData)
      postAction(invokeUrl, param, 'post')
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            if (res?.result?.auditStatus == '0') {
              this.$parent.submitCallBack(formData)
            }
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    downloadFile() {
      let params = { id: this.paramIntegrate().id }
      this.confirmLoading = true
      axios({
        url: this.url.download,
        responseType: 'blob',
        params: params
      }).then((res) => {
        this.confirmLoading = false
        let fieldName = this.currentEditRow.contractNumber + '_' + this.currentEditRow.contractVersion + '.pdf'
        //console.log(res)
        const blob = new Blob([res])
        const blobUrl = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = blobUrl
        a.download = fieldName
        a.click()
      })
    },
    downloadWordFile() {
      let params = { id: this.paramIntegrate().id }
      this.confirmLoading = true
      axios({
        url: this.url.downloadDoc,
        responseType: 'blob',
        params: params
      }).then((res) => {
        this.confirmLoading = false
        let fieldName = this.currentEditRow.contractNumber + '_' + this.currentEditRow.contractVersion + '.doc'
        //console.log(res)
        const blob = new Blob([res])
        const blobUrl = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = blobUrl
        a.download = fieldName
        a.click()
      })
    },
    closeEditModal() {
      this.editRowModal = false
    },
    showFlow() {
      let params = this.paramIntegrate()
      this.flowId = params.flowId
      if (!this.flowId) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
        return
      }
      this.flowView = true
    },
    closeFlowView() {
      this.flowView = false
    },
    handleCancel() {
      this.visible = false
    },
    downloadEvent(Vue, row) {
      if (!row.fileName) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
        return
      }
      const id = row.id
      const fileName = row.fileName
      const params = {
        id
      }
      getAction(this.url.downloadUrl, params, {
        responseType: 'blob'
      }).then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    },

    handleShowCustomSubmit() {
      let rs = true
      if (this.currentEditRow.contractStatus == '3' && (this.currentEditRow.auditStatus == '2' || this.currentEditRow.auditStatus == '4') && this.currentEditRow.oaAuditStatus == 0) {
        rs = true
      } else {
        rs = false
      }
      console.log('rs', rs)
      return rs
    },

    handleSubmitBefore(args) {
      let thisData = this.getItemGridRef('purchaseContractContentItemList').getTableData().fullData
      let param = this.paramIntegrate()
      if (param.contractLevels == 'subsidiary' && param.masterContractNumber == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_neDtLNneKWdneylSW_94ea76ac`, '合同层级为从合同时,主合同号必填!'))
        return new Promise((resolve, reject) => {
          reject(false)
        })
      }
      if (!(param && param.id != '')) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WsMWKDJUz_81a98950`, '先保存,再提交审批'))
        return new Promise((resolve, reject) => {
          reject(false)
        })
      }

      if (!thisData || thisData.length === 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTermLibraryInformationNeedsConfigured`, '需配置合同条款库信息!'))
        return new Promise((resolve, reject) => {
          reject(false)
        })
      }

      return new Promise((resolve) => {
        const frozen = {
          toElsAccount: args.allData.toElsAccount,
          frozenFunction: '3',
          orgType: '0',
          orgCode: args.allData.purchaseOrg
        }
        // 检测供应商对否被冻结
        if (frozen.toElsAccount && frozen.toElsAccount.length > 0 && frozen.orgCode && frozen.orgCode.length > 0) {
          postAction(this.url.isExistFrozen, frozen).then((rest) => {
            if (rest.success) {
              let { allData = {} } = args || {}
              let { busRule = {}, personFrom = {} } = allData || {}

              // pageData = { ...pageData, ...busRule, ...personFrom}
              delete allData.busRule
              delete allData.personFrom

              allData = Object.assign({}, allData, busRule, personFrom)
              let params = {
                businessId: allData.id,
                businessType: 'contract',
                auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractNoCode`, '合同编号')}：${allData.contractNumber} ${allData.contractName || ''}`,
                params: JSON.stringify(allData)
              }
              args = Object.assign({}, args, {
                allData: params
              })
              resolve(args)
            } else {
              this.$message.warning(rest.message)
              this.confirmLoading = false
              return
            }
          })
        } else {
          let { allData = {} } = args || {}
          let { busRule = {}, personFrom = {} } = allData || {}

          // pageData = { ...pageData, ...busRule, ...personFrom}
          delete allData.busRule
          delete allData.personFrom

          allData = Object.assign({}, allData, busRule, personFrom)
          let params = {
            businessId: allData.id,
            businessType: 'contract',
            auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractNoCode`, '合同编号')}：${allData.contractNumber} ${allData.contractName || ''}`,
            params: JSON.stringify(allData)
          }
          args = Object.assign({}, args, {
            allData: params
          })
          resolve(args)
        }
      })
    },

    async handleCustomSubmit(args) {
      const { showItem, contractLevels, masterContractNumber } = this.getAllData()
      if (contractLevels == 'subsidiary' && masterContractNumber == '') {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_neDtLNneKWdneylSW_94ea76ac`, '合同层级为从合同时,主合同号必填!'))
        return new Promise((resolve, reject) => {
          reject(false)
        })
      }
      if (showItem == '1') {
        let itemGrid = this.getItemGridRef('purchaseContractItemList')
        let { fullData } = itemGrid.getTableData()
        if (!fullData.length) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRRSuIccVH_cca12f4`, '请至少添加一行行信息'))
          return false
        }
      }
      // 获取页面所有数据
      // const allData = this.getAllData() || {}
      // 这里可以添加自定义校验逻辑
      function add(a, b) {
        let c, d, e
        try {
          c = a.toString().split('.')[1].length
        } catch (f) {
          c = 0
        }
        try {
          d = b.toString().split('.')[1].length
        } catch (f) {
          d = 0
        }
        e = Math.pow(10, Math.max(c, d))
        return (mul(a, e) + mul(b, e)) / e
      }

      function mul(a, b) {
        let c = 0
        let d = a.toString()
        let e = b.toString()
        try {
          c += d.split('.')[1].length
        } catch (f) {}
        try {
          c += e.split('.')[1].length
        } catch (f) {}
        return (Number(d.replace('.', '')) * Number(e.replace('.', ''))) / Math.pow(10, c)
      }

      if (this.getAllData().showCustom1 === '1') {
        let contractItemCustom1List = this.getItemGridRef('contractItemCustom1List').getTableData().fullData
        let totalAmount = 0.0
        let totalPerformanceRatio = 0.0
        for (let i = 0; i < contractItemCustom1List.length; i++) {
          if (contractItemCustom1List[i].performanceRatio === '0.000%') {
            let num = i + 1
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '第' + num + '行履行比例需大于0%'))
            return
          }
          totalAmount = add(totalAmount, contractItemCustom1List[i].amount)
          totalPerformanceRatio = add(totalPerformanceRatio, (contractItemCustom1List[i].performanceRatio + '').replace('%', ''))
        }

        if (totalPerformanceRatio < 100 || totalPerformanceRatio > 100) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IclknxEU100xqDJ_6cd18c87`, '履行比总和不等于100%，不可提交！'))
          return
        }
        if (this.getAllData().targetQuantity && (totalAmount < this.getAllData().targetQuantity || totalAmount < this.getAllData().targetQuantity)) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '履行总金额不等于合同含税总金额 ' + this.getAllData().targetQuantity + '，不可提交！'))
          return
        }
      }
      this.composeBusinessSubmit(args)
    },

    composeBusinessSubmit (args) {
      const allData = this.getAllData()

      let steps = [
          this.stepHideMessage, // 隐藏按钮配置的 showMessage
          this.stepShowMessage, // 恢复按钮配置的 showMessage
          this.stepJudgeIsExistFrozenData,
          this.stepConfirmSubmit, // 二次确认
          this.stepBusinessSubmit // 提交审批操作
      ]
      const handleCompose = composePromise(...steps)
      this.confirmLoading = true
      handleCompose({ ...args, allData, _isAdd: false })
          .then(res => {
              console.log('all submit success', res)
              this.businessHide()
          }, err => {
              console.log('all submit error', err)
          })
          .finally(() => {
              this.confirmLoading = false
              if (this.refresh) {
                  this.refreshPageData()
              }
          })
    },
  }
}
</script>
