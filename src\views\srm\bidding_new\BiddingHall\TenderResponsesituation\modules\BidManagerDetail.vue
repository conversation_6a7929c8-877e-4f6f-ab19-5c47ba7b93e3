<template>
  <div class="PurchaseBidManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :queryData="queryData"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import EditFormLayout from '@comp/template/business/EditFormLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { USER_COMPANYSET } from '@/store/mutation-types'
export default {
    name: 'PurchaseBidManagerEdit',
    components: {
        BusinessLayout,
        EditGridLayout,
        EditFormLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        queryData: {
            type: Object,
            default () {
                return {}
            }
        },
        propOfCheckType: {
            default: () => {
                return ''
            },
            type: [String, Number]
        }
    },
    computed: {
        remoteJsFilePath () {
            console.log('editrow', this.currentEditRow)
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_SupplierTenderProjectPurchaseBid_${templateNumber}_${templateVersion}`
            // return '100000/sale_SupplierTenderProjectPurchaseBid_TC2022042103_1'
        }
    },
    data () {
        return {
            businessRefName: 'businessRef',
            pageStatus: 'detail',
            confirmLoading: false,
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            requestData: {
                detail: {
                    url: '/tender/purchase/supplierTenderProjectPurchaseBid/queryById', 
                    args: (that) => {
                        return { 
                            id: that.currentEditRow.id
                        }
                    },
                    config: () => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {xNodeId: `${this.propOfCheckType || ''}_0`}
                        }
                    }
                }
            },
            url: {
              
            },
            userCompanyset: {},
            projectObj: {}
        }
    },
    created () {
        // 判断是否为审批中，动态添加头部的确认审批按钮
        if( this.currentEditRow.status == '1'){
            this.pageHeaderButtons=[
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                    key: 'confirm',
                    attrs: {
                        type: 'primary'
                    },
                    click: this.confirm
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ]
        }else{
            this.pageHeaderButtons=[{
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                key: 'goBack'
            }
            ]
        }

        if (JSON.stringify(this.queryData) != '{}') {
            this.userCompanyset = this.$ls.get(USER_COMPANYSET)
        }
    },
    mounted () {
    },
    methods: {
        confirm (){
            var that = this
            let url = '/tender/purchase/supplierTenderProjectPurchaseBid/approved'
            this.confirmLoading = true
            getAction(url, {id: this.currentEditRow.id}, {headers: {xNodeId: `${this.propOfCheckType}_0_0`}}).then((res) => {
                console.log('resres', res)
                if(res.success){
                    that.$message.success(res.message)
                    this.$emit('hide')
                }else{
                    that.$message.warning(res.message)
                    this.confirmLoading = false

                }
            })
        },
        handleSubmitBefore (args) {
            console.log('args', args)
            return new Promise((resolve) => {
                let allData = args.allData
                allData['saleTenderInvoiceInfoList'] = [Object.assign(allData['invoiceInfo'], allData['payType'])]
                let params = {
                    allData: allData
                }
                args = Object.assign({}, args, params)
                resolve(args)
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log(pageConfig, resultData)

            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
                if (key == 'supplierName') {
                    formModel[key] = resultData[key] || this.userCompanyset.companyName
                }
            }

            // 编辑赋值
            const saleTenderInvoiceInfoList = resultData['saleTenderInvoiceInfoList']
            if (saleTenderInvoiceInfoList.length > 0) {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'invoiceInfo') {
                        group['formModel'] = Object.assign({}, group['formModel'], saleTenderInvoiceInfoList[0])
                    }
                    if (group.groupCode == 'payType') {
                        group['formModel'] = Object.assign({}, group['formModel'], saleTenderInvoiceInfoList[0])
                    }
                })
            }
        }
    }
}
</script>
