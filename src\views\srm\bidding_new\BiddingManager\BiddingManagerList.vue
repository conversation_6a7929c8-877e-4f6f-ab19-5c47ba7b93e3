<template>
  <div
    class="BiddingManagerList"
    style="height: 100%">
    <list-layout
      ref="listPage"
      :tabsList="tabsList"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import layIM from '@/utils/im/layIM.js'
import { getAction } from '@/api/manage'

export default {
    name: 'BiddingManagerList',
    mixins: [ListMixin],
    data () {
        return {
            tabsList: [],
            pageData: {
                businessType: 'BiddingManager',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNdIAy_ce4555df`, '请输入项目编号')
                    }
                ],
                form: {
                    keyWord: '',
                    ebiddingDesc: '',
                    ebiddingNumber: '',
                    status: undefined
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), authorityCode: 'tender#supplierTenderProjectMasterInfo:creatGruopChat', clickFn: this.handleChat},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supBiddingHall`, '投标大厅'), authorityCode: 'tender#supplierTenderProjectMasterInfo:queryById', clickFn: this.handleTender}
                ]
            },
            url: {
                list: '/tender/sale/supplierTenderProjectMasterInfo/list',
                columns: 'saleTenderProjectSupplierList'
            },
            countTabsUrl: '/tender/sale/supplierTenderProjectMasterInfo/counts'
        }
    },
    methods: {
        // 重写路径变动监听事件
        handleRouterChange (query) {
            if (query.linkFilter || query.open) {
                this.$nextTick(() => {
                    const listPageObj = this.$refs.listPage
                    let isOrder = listPageObj.pageData.isOrder || listPageObj.isOrder
                    //不为id，则说明点了页面的排序
                    if(listPageObj.isOrder.column != 'id'){
                        isOrder = listPageObj.isOrder
                    }
                    if(query.id){
                        query.id.indexOf('_') && (() => {query.id = query.id.split('_')[0]})()
                    }
                    let params = Object.assign(
                        {
                            pageSize: listPageObj.tablePage.pageSize,
                            pageNo: listPageObj.tablePage.currentPage,
                            filter: listPageObj.filter,
                            dataSource: listPageObj.dataSource
                        }, isOrder, query)
                    listPageObj.loading = true
                    getAction(this.url.list, params).then((res) => {
                        if(res.success) {
                            let list = [...res.result.records]
                            let row = list[0]
                            listPageObj.tableData = list
                            listPageObj.tablePage.total = res.result.total
                            this.handleTender(row, query)
                        }
                    }).finally(() => {
                        listPageObj.loading = false
                    })
                })
            }
        },
        handleChat (row) {
            let { subpackageId } = row
            const id = subpackageId
            let recordNumber = subpackageId || id
            // 创建聊天
            layIM.creatGruopChat({id, type: 'SaleTenderProject', url: this.url || '', recordNumber})
        },
        handleTender (row, query) {
            row['applyRole'] = '2'
            this.$ls.set('SET_TENDERCURRENTROW', row)
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/tenderHall',
                query: {
                    _t
                }
            })
            // if(query.open){
            //     window.open(routeUrl.href, '_self')
            // }else{
            //     window.open(routeUrl.href, '_blank')
            // }
            window.open(routeUrl.href, '_blank')
            //如果是query.open跳转的，跳转后我的项目页面置为false，避免刷新状态栏又跳到大厅内
            if(query.open){
                query.open = false
            }
        }
    },
    
    mounted () {
        // this.serachTabs('srmReconciliationStatus', 'reconciliationStatus')
        this.serachCountTabs(this.countTabsUrl)
    },
    activated () {
        // this.serachTabs('srmReconciliationStatus', 'reconciliationStatus')
        this.serachCountTabs(this.countTabsUrl)
        this.$refs.listPage.loadData()
    }
}
</script>
