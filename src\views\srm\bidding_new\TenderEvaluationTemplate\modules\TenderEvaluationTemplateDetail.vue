<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-content">
          <div class="baseInfo">
            <a-form-model
              ref="baseForm"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
              :rules="rules"
              :model="formData">
              <a-row >
                <a-col :span="8">
                  <a-form-model-item
                    :label="$srmI18n(`${this.$getLangAccount()}#i18n_field_templateName`, '模板名称')"
                    prop="evaluationName">
                    <span>{{ formData.evaluationName }}</span>
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    :label="$srmI18n(`${this.$getLangAccount()}#i18n_field_templateDesc`, '模板描述')"
                    prop="evaluationDescribe">
                    <span>{{ formData.evaluationDescribe }}</span>
                  </a-form-model-item>
                </a-col>
                <!-- <a-col :span="8">
                  <a-form-model-item
                    :label="$srmI18n(`${this.$getLangAccount()}#i18n_dict_UBrh_41199dfa`, '评标办法')"
                    prop="evaluationMethod"
                    required>
                    <span>{{ formData.evaluationMethod_dictText }}</span>
                  </a-form-model-item>
                </a-col> -->
              </a-row>
            </a-form-model>
          </div>
          <template-node-box
            ref="nodeBox"
            pageStatus="detail"
            :nodeListData="nodeListData"/>
        </div>
        <div class="page-footer">
          <a-button @click="() => {this.$emit('hide')}">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script>
import {getAction, postAction, httpAction} from '@/api/manage'
import TemplateNodeBox from './components/TemplateNodeBox'

export default {
    name: 'PurchaseSupplierCapacityHead',
    components: {
        TemplateNodeBox
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: { span: 12 },
            wrapperCol: { span: 12 },
            rejectVisible: false,
            confirmLoading: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                detail: { url: '/tender/tenderEvaluationTemplateHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            url: {
            },
            formData: {},
            nodeListData: [],
            rules: {
                evaluationName: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNjXRL_b784ed0b`, '请输入有效名称'), trigger: 'blur' }
                ],
                evaluationMethod: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNjXCh_b78703c4`, '请输入有效方法'), trigger: 'blur' }
                ]
            }
        }
    },
    methods: {
        // 通过接口获取数据
        async queryDetail () {
            if (this.requestData.detail) {
                this.confirmLoading = true
                let url = this.requestData.detail.url
                if (url) {
                    let method = 'get'
                    let args = this.requestData.detail.args(this)
                    // 有id 才能请求,新建时是没有id的，不用请求查询接口
                    if (args && args.id) {
                        if (this.requestData.detail.method) {
                            method= this.requestData.detail.method.toLowerCase()
                        }
                        let query = method==='get'?await getAction(url, args): await postAction(url, args)
                        this.confirmLoading = false
                        if (query && query.success) {
                            console.log(query.result)
                            this.formData = Object.assign({}, query.result)
                            this.nodeListData = this.formData.tenderEvaluationTemplateItemVoList
                            console.log('this.nodeListData', this.nodeListData)
                        } else {
                            this.$message.error(query.message)
                        }
                    } else {
                        this.confirmLoading = false
                        this.$nextTick(()=> {
                            this.dealSource(this.currentEditRow)
                            this.formData = {}
                            this.nodeListData = []
                        })
                    }
                }
            }
        },
        dealSource (data) {
            console.log(data)
        }
    },
    mounted () {
        this.queryDetail()
    }
}
</script>
