<template>
  <div class="els-page-comtainer">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="masterSlave"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>

  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {BUTTON_PUBLISH} from '@/utils/constant.js'

export default {
    name: 'PurchaseTenderVariableLibraryEdit',
    components: {
        fieldSelectModal,
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            requestData: {
                detail: { url: '/tender/template/purchaseTenderVariableLibrary/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IrARG_7e52a3e`, '模板变量库'),
                validRules: {
                    paramName: [{max: 1000, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CcHzxOBR1000mJB_132b682d`, '内容长度不能超过1000个字符')}],
                    paramValue: [{max: **********, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CcHzxOBR**********mJB_2958b05f`, '内容长度不能超过**********个字符')}],
                    paramContent: [{max: **********, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CcHzxOBR**********mJB_2958b05f`, '内容长度不能超过**********个字符')}],
                    businessType: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CcHzxOBR100mJB_c7f645d5`, '内容长度不能超过100个字符')}],
                    beanName: [{max: 1000, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CcHzxOBR1000mJB_132b682d`, '内容长度不能超过1000个字符')}],
                    groupType: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CcHzxOBR100mJB_c7f645d5`, '内容长度不能超过100个字符')}],
                    remark: [{max: 1000, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CcHzxOBR1000mJB_132b682d`, '内容长度不能超过1000个字符')}],
                    createById: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CcHzxOBR50mJB_b53d7131`, '内容长度不能超过50个字符')}],
                    updateById: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CcHzxOBR50mJB_b53d7131`, '内容长度不能超过50个字符')}]
                }
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/template/purchaseTenderVariableLibrary/edit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save',
                    showMessage: true,
                    handleBefore: this.handleSaveBefore,
                    handleAfter: this.handleSaveAfter
                },
                {
                    ...BUTTON_PUBLISH,
                    args: {
                        url: '/tender/template/purchaseTenderVariableLibrary/publish'
                    },
                    show: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                add: '/tender/template/purchaseTenderVariableLibrary/add',
                edit: '/tender/template/purchaseTenderVariableLibrary/edit',
                detail: '/tender/template/purchaseTenderVariableLibrary/queryById'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_tenderVariableLibrary_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        
        validateCode (rule, value, callback) {
            // 重复校验
            var params = {
                tableName: '',
                fieldName: '',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then(res => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        selectCallBack (item) {
            this.pageData.form.dictCode = item[0].dictCode
            this.$refs.editPage.$forceUpdate()
        }
    }
}
</script>

<style lang="less" scoped>
</style>