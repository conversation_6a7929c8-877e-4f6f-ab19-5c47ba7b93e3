<template>
  <div>
    <template v-for="(btn, index) in localButtons">
      <template v-if="btn.key === 'dropdown'">
        <a-dropdown
          style="margin-right: 8px"
          v-if="btn._show"
          :disabled="btn.disabled ? btn.disabled() : false"
          :key="index">
          <a-button :type="btn.type">
            {{ btn.title }}
            <a-icon type="down" />
          </a-button>
          <a-menu
            slot="overlay"
            @click="({key}) => handleMenuClick(key, btn)">
            <a-menu-item
              v-for="menu in btn.menus"
              :disabled="menu.disabled ? menu.disabled() : false"
              :key="menu.key">
              {{ menu.title }}
            </a-menu-item>
          </a-menu>
        </a-dropdown>
      </template>
      <template v-else>
        <a-button
          style="margin-right: 8px"
          v-debounce="{ method: 'handleClick', event: 'click', args: btn }"
          v-if="btn._show"
          :disabled="btn.disabled ? btn.disabled() : false"
          :key="index"
          :type="btn.type">
          {{ btn.title }}
        </a-button>
      </template>
    </template>
  </div>
</template>

<script>
import {isPromise} from '@/utils/util'

export default {
    data (){
        return{
            localButtons: []
        }
    },
    methods: {
        getAuthCodeButtons (buttons){
            const authButtons = []
            if (buttons && buttons.length) {
                buttons.forEach(button => {
                    if (button && button.authorityCode) {
                        if (this.$hasOptAuth(button.authorityCode)) {
                            authButtons.push(button)
                        }
                    } else {
                        authButtons.push(button)
                    }
                })
            }
            return authButtons
        },
        handleButtonShow (button){
            if (!button._show) {
                button._show = undefined
            }
            const show = button.show
            if (show && typeof show === 'function') {
                let response = show()
                if (isPromise(response)) {
                    response.then(() => {button._show = true}, () => {button._show = false})
                } else {
                    button._show = !!response
                }
            } else if (typeof button.show !== 'undefined') {
                button._show = !!button.show
            } else {
                button._show = true
            }
        },
        handleClick (button){
            if (button.click && typeof button.click === 'function') {
                const clickFn = button.click
                clickFn && clickFn()
            }
        },
        handleMenuClick (key, button){
            if (button.click && typeof button.click === 'function') {
                const clickFn = button.click
                clickFn && clickFn(key)
            }
        }
    },
    name: 'EnquiryHallButton',
    props: {
        buttons: {
            default: () => [],
            type: Array
        }
    },
    watch: {
        buttons: {
            handler (buttons) {
                if (!Array.isArray(buttons) || !buttons.length) {
                    return
                }
                const localButtons = buttons.map(button => {
                    this.handleButtonShow(button)
                    return { _show: true, ...button }
                })
                this.localButtons =  this.getAuthCodeButtons(localButtons)
            },
            immediate: true
        }
    }
}
</script>

<style scoped>

</style>