<!--
 * @Author: fzb
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2022-08-19 16:44:24
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\purchase\modules\PurchaseEditCost.vue
-->
<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :url="url"
        modelLayout="masterSlave"
        pageStatus="edit"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
    <a-modal
      v-drag    
      v-model="rejectVisible"
      :title="rejectModelTitle"
      :okText="okText"
      @ok="handleOk">
      <a-form-model
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :model="rejectForm">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_rMyC_478bc282`, '驳回节点')">
          <a-select
            style="width: 100%"
            v-model="rejectForm.node">
            <a-select-option
              v-for="(item, i) in filterNodeList"
              :key="i"
              :value="item.val"
            >
              {{ item.key }}
            </a-select-option>
          </a-select>
        </a-form-model-item >
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_rMvj_478a05f6`, '驳回理由')"
        >
          <a-textarea
            v-model="rejectForm.reject"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNrMvj_ceb1c7df`, '请输入驳回理由')"
            :auto-size="{ minRows: 3, maxRows: 5 }"
          />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import { USER_ELS_ACCOUNT, DEFAULT_LANG } from '@/store/mutation-types'
import moment from 'moment'

export default {
    name: 'SaleEightDisciplinesHeadEdit',
    components: {
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    created () {
        // 监听syncRow 事件，D5纠正措施字段改变，D6跟着改变
        this.$root.$on('syncRow', msg => {
            const {groupCode, property, content, id } = msg
            if (groupCode === 'eightDisciplinesFiveList') {
                let syncItemGrid = this.getItemGridRef('eightDisciplinesSixList')
                const tableData = syncItemGrid && syncItemGrid.getTableData().fullData
                tableData.forEach(syncRow => {
                    if(syncRow._X_ID){
                        if (Number(id.split('_')[1]) + 1  === Number(syncRow._X_ID.split('_')[1])) {
                            syncRow[property] = content
                        }
                    }
                    
                })
                syncItemGrid.loadData(tableData)
            }
        })
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNrMvj_ceb1c7df`, '请输入驳回理由'),
            rejectModelTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIyC_2e6c072a`, '指定节点'),
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'),
            rejectVisible: false,
            refresh: true,
            nodeList: [
                {key: 'D0:问题提出', val: 'D0'},
                {key: 'D1:小组成立', val: 'D1'},
                {key: 'D2:问题界定', val: 'D2'},
                {key: 'D3:围堵措施', val: 'D3'},
                {key: 'D4:原因分析', val: 'D4'},
                {key: 'D5:纠正措施', val: 'D5'},
                {key: 'D6:效果验证', val: 'D6'},
                {key: 'D7:预防再发生', val: 'D7'},
                {key: 'D8:结案评价', val: 'D8'}
            ],
            filterNodeList: [],
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                export: { url: '/base/excelByConfig/downloadTemplate', args: () => {} },
                import: { url: '/base/excelByConfig/importExcel', args: () => {}  },
                detail: { url: '/eightReport/saleEightDisciplines/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                eightDisciplinesThreeList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete'
                    }
                ],
                eightDisciplinesFiveList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete'
                    }
                ],
                eightDisciplinesSixList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete'
                    }
                ],
                saleAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: 'eightDisciplines', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                        key: 'gridDelete',
                        click: this.businessGridDeleteAttachment
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/eightReport/saleEightDisciplines/edit'
                    },
                    key: 'save',
                    showMessage: true,
                    authorityCode: 'eightReport#SaleEightReportHead:edit'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/eightReport/saleEightDisciplines/publish'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish',
                    authorityCode: 'eightReport#SaleEightReportHead:publish'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'),
                    args: {
                        url: '/eightReport/saleEightDisciplines/reject'
                    },
                    attrs: {
                        type: 'danger'
                    },
                    key: 'reject',
                    click: this.reject,
                    authorityCode: 'eightReport#SaleEightReportHead:reject'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                reject: '/eightReport/saleEightDisciplines/reject',
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_eightDisciplines_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        attrHandle (){
            return {
                'sourceNumber': this.currentEditRow.eightDisciplinesNumber,
                'actionRoutePath': '/srm/eightReport/sale/SaleEightDisciplinesHeadList'
            }
        },
        preViewEvent (Vue, row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleAfterDealSource (pageConfig, resultData){
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
            }
            this.externalToolBar['saleAttachmentList'][0].args.headId = resultData.id || ''
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            this.externalToolBar['saleAttachmentList'][0].args.itemInfo = itemInfo
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFilesEvent}
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'saleAttachmentList',
                        title: '关联Tab',
                        fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'select',
                        defaultValue: '',
                        dictCode: 'SRMEightAttachmentRelationTab',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: {
                            default: ({row}) => {
                                return [
                                    <span>{moment(row.uploadTime).format('YYYY-MM-DD HH:mm:ss')}</span>
                                ]
                            }
                        }
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        uploadCallBack (result, ref) {
            let fileGrid = this.getItemGridRef(ref)
            fileGrid.insertAt(result, -1)
        },
        businessGridAdd ({ Vue, pageConfig, btn, groupCode }) {
            console.log('groupCode', groupCode)
            let itemGrid = this.getItemGridRef(groupCode)
            let syncItemGrid = ''
            if (groupCode === 'eightDisciplinesFiveList') {
                syncItemGrid = this.getItemGridRef('eightDisciplinesSixList')
            }
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)
            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {
                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            itemGrid.insertAt([row], -1)
            syncItemGrid && syncItemGrid.insertAt([row], -1)
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction('/attachment/saleAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        reject (){
            let eightReportStatus = this.getAllData().eightDisciplinesStatus
            if(eightReportStatus == 'D0') return
            let index = this.nodeList.findIndex(item=>item.val == eightReportStatus)
            this.rejectForm.node = this.nodeList[index-1].val
            this.filterNodeList = this.nodeList.filter((item, idx)=> idx < index)
            this.rejectVisible = true
        },
        handleOk () {
            const that = this
            let pageAllData = this.getAllData()
            //驳回理由
            pageAllData.rejectReason = this.rejectForm.reject
            //退回到的节点
            pageAllData.fbk1 = this.rejectForm.node
            postAction(this.url.reject, pageAllData).then(res => {
                if(res.success) {
                    that.$message.success(res.message)
                }else {
                    that.$message.warning(res.message)
                }
                that.stepBusinessRefresh()
            })
            this.rejectVisible = false
        },
        businessGridDeleteAttachment ({ pageConfig, groupCode }) {
            debugger
            let itemGrid = this.getItemGridRef(groupCode)
            let arr = []
            let delArr = []
            //大B
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')

            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            if(checkboxRecords && checkboxRecords.length>0){
                checkboxRecords.forEach(row=> {
                    //当前登录人等账号于查询的账号 大B
                    if (user == row.uploadElsAccount) {
                        if( subAccount==row.uploadSubAccount){
                            delArr.push(row)
                        }else{
                            arr.push(row.fileName)
                        }
                    }else{
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if(arr && arr.length>0){
                    let str = arr.join(',')
                    this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') +str+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }

            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                // if (res.success) itemGrid.removeCheckboxRow()
                itemGrid.removeCheckboxRow().then(()=>{
                    const tableData = itemGrid && itemGrid.getTableData().fullData
                    if(tableData && tableData.length>0){
                        checkboxRecords.forEach((row)=> {
                            if (user == row.uploadElsAccount) {
                                delArr.push(row)
                            }else {
                                arr.push(row.id)
                            }
                        })
                    }
                })
            })
        },
        deleteFilesEvent (Vue, row){
            const fileGrid = Vue.$refs.saleAttachmentList
            //大B
            let user = this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            //如果删除的数据有和登录人账号不一致的
            if(user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
                return
            }
            const id = row.id
            const params = {
                id
            }
            getAction('/attachment/saleAttachment/delete', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        }
    }
}
</script>