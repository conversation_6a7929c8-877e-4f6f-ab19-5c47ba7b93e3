<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-header">
          <a-row>
            <a-col
              class="desc-col"
              :span="6">
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_VVYB_43d1194f`, '重新招标') }}</span>
            </a-col>
            <a-col
              class="btn-col"
              :span="18">
              <a-button
                @click="cancelAudit"
                style="margin-right: 10px"
                v-if="cancelAuditShow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批') }}</a-button>
              <a-button
                @click="showFlow"
                v-if="viewButtonShow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_viewProcess`, '查看流程') }}</a-button>
            </a-col>
          </a-row>
        </div>
        <div class="page-content">
          <div class="baseInfo">
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息') }}</span>
            </titleTrtl>
            <a-form-model
              ref="baseForm"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
              :rules="rules"
              :model="formData">
              <a-row>
                <a-col :span="8">
                  <a-form-model-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_KQVIYBdI_1e8f8c02`, '是否新建招标项目')"
                    prop="againProject">
                    <m-select
                      class="width120"
                      v-model="formData['againProject']"
                      :disabled="!isEdit"
                      :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                      dict-code="yn" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item prop="againSupplier">
                    <span slot="label">
                      {{ $srmI18n(`${$getLangAccount()}#i18n_field_RdXKQoN_5929a1a3`, '供应商是否带入') }}
                      <a-tooltip :title="tooltip">
                        <a-icon type="question-circle-o" />
                      </a-tooltip>
                    </span>
                    <m-select
                      class="width120"
                      v-model="formData['againSupplier']"
                      :disabled="!isEdit"
                      @change="changeAgainSupplier"
                      :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                      dict-code="yn" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_KQUz_2fba950f`, '是否审批')"
                    prop="audit">
                    <m-select
                      class="width120"
                      v-model="formData['audit']"
                      :disabled="!isEdit"
                      :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                      dict-code="yn" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_VVYBdRBI_8f4ea1bc`, '重新招标相关附件')"
                    prop="file">
                    <a-upload
                      v-if="isEdit"
                      name="file"
                      :multiple="true"
                      :showUploadList="false"
                      :action="uploadUrl"
                      :headers="uploadHeader"
                      :accept="accept"
                      :data="{ headId: formData.id, businessType: 'biddingPlatform', 'sourceNumber': tenderCurrentRow.tenderProjectNumber || tenderCurrentRow.id || '', 'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开' }"
                      @change="handleUploadChange">
                      <a-button
                        type="primary"
                        @click="beforeUpload"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                    </a-upload>
                    <div
                      v-for="(fileItem, index) in formData.purchaseAttachmentDTOList"
                      :key="fileItem.id">
                      <span style="color: blue; cursor: pointer; margin-right: 8px">{{ fileItem.fileName }}</span>
                      <a-icon
                        v-if="isEdit"
                        type="delete"
                        @click="handleDeleteFile(index)" />
                      <span>
                        <a
                          style="margin: 0 8px"
                          @click="preViewEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                        <a @click="downloadEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
                      </span>
                    </div>
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row class="textAreaClass">
                <a-form-model-item
                  :label="$srmI18n(`${$getLangAccount()}#i18n_field_VVYBtH_94014b34`, '重新招标记录')"
                  prop="againTenderRecord">
                  <a-textarea
                    v-model="formData['againTenderRecord']"
                    :disabled="!isEdit"
                    :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VVYBtH_94014b34`, '重新招标记录')"
                    :auto-size="{ minRows: 3, maxRows: 5 }" />
                </a-form-model-item>
              </a-row>
            </a-form-model>
          </div>
          <div class="subPackInfo">
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_zsVH_268adaad`, '分包信息') }}</span>
              <template
                slot="right"
                v-if="isEdit">
                <a-button
                  type="primary"
                  style="margin-right: 10px"
                  @click="handleTableAdd">{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}</a-button>
                <a-button @click="handleTableDel">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delect`, '删除') }}</a-button>
              </template>
            </titleTrtl>
            <listTable
              ref="listTable"
              v-if="showTable"
              :fromSourceData="purchaseTenderAgainSubpackageItemList"
              :showTablePage="false"
              :checkedConfig="checkedConfig"
              :statictableColumns="statictableColumns"> </listTable>
          </div>
        </div>
        <div class="page-footer">
          <a-button
            @click="handleSave"
            type="primary"
            v-if="isEdit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
          <a-button
            @click="handlePublish"
            type="primary"
            v-if="isEdit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_publish`, '发布') }}</a-button>
        </div>
      </a-spin>
    </div>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      :isTree="true"
      @ok="fieldSelectOk" />
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow" />
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import titleTrtl from '../components/title-crtl'
import listTable from '../components/listTable'
import { USER_INFO } from '@/store/mutation-types'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import ContentHeader from '../components/content-header'
import flowViewModal from '@comp/flowView/flowView'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    mixins: [baseMixins],
    components: {
        titleTrtl,
        listTable,
        fieldSelectModal,
        ContentHeader,
        flowViewModal
    },
    data () {
        return {
            labelCol: { span: 9 },
            wrapperCol: { span: 14 },
            confirmLoading: false,
            formData: {},
            flowView: false,
            flowId: '',
            currentEditRow: this.tenderCurrentRow,
            rules: {
                againProject: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNKQVIYBdI_66c2316b`, '请输入是否新建招标项目') }],
                againSupplier: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNRdXKQoN_84c82b1a`, '请输入供应商是否带入') }],
                audit: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNKQUz_b6e256f8`, '请输入是否审批') }]
            },
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            url: {
                queryDetailBySubpackageId: '/tender/abnormal/purchaseTenderAgainSubpackageHead/queryAgainSubpackageBySubpackageId',
                add: '/tender/abnormal/purchaseTenderAgainSubpackageHead/add',
                edit: '/tender/abnormal/purchaseTenderAgainSubpackageHead/edit',
                publish: '/tender/abnormal/purchaseTenderAgainSubpackageHead/publish'
            },
            purchaseTenderAgainSubpackageItemList: [],
            checkedConfig: {
                checkMethod: ({ row }) => {
                    // 当前分包不能勾选
                    if (row.subpackageId == this.subId) {
                        return false
                    }
                    return true
                }
            },
            showTable: true,
            tooltip: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iFKKAPzsjsRtHRxtHTPVauVzsIxiTcrzsjYBQLCciFQKAPzsjsRtHRxtHxTPVauVzsIiTcrzsjYBQLCc_977a780c`, '选择是时：当前分包的报名记录/购买记录需要新增到新分包下，不允许修改分包的招标流程模型;选择否时：当前分包的报名记录/购买记录不需要新增到新分包下，允许修改分包的招标流程模型'),
            statictableColumns: [
                {
                    type: 'checkbox',
                    width: 50
                },
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    width: 120,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsAy_269085a0`, '分包编号'),
                    field: 'subpackageNumber'
                },
                {
                    width: 120,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称'),
                    field: 'subpackageName',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YBQLCcRL_63b39603`, '分包名称'),
                    width: 160,
                    field: 'tenderProcessModelName'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_biddingType`, '招标类型'),
                    field: 'tenderType_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJCK_2b39a7da`, '审查方式'),
                    field: 'checkType_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_KQsR_2fbb601f`, '是否报名'),
                    field: 'signUp_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQlB_2fb9d2b0`, '是否售标'),
                    field: 'bidding_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_UBCK_411be079`, '评标方式'),
                    field: 'evaluationType_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_vBCK_2cc273bd`, '开标方式'),
                    field: 'bidOpenType_dictText'
                },
                {
                    width: 100,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_YBLT_2e859370`, '招标程序'),
                    field: 'processType_dictText'
                }
            ]
        }
    },
    computed: {
        subId () {
            return this.subpackageId()
        },
        isEdit () {
            let status = this.formData.status == '0' || !this.formData.status ? 'edit' : 'detail'
            // if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') status = 'detail'
            console.log('@==', status)
            return status == 'edit'
        },
        subPackageRow () {
            return this.currentSubPackage()
        },
        cancelAuditShow () {
            // 判断是否为审批中，审批中才展示撤销审批按钮
            return this.formData.status == '1' ? true : false
        },
        viewButtonShow () {
            // 判断是否需要审批且有flowid，都具备才展示查看流程按钮
            if (this.formData.status == '1') {
                if (this.formData.flowId) {
                    return true
                } else {
                    return false
                }       
            }
            return false
        }
    },
    methods: {
        cancelAudit () {
            this.auditStatus = this.formData.auditStatus
            let params = this.formData
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: () => {
                    this.postAuditData('/a1bpmn/audit/api/cancel', params)
                    
                    // that.$refs.businessRefName.loadData()
                }
            })
        },
        postAuditData (invokeUrl, fromSourceData) {
            let param = {}
            param['rootProcessInstanceId'] = fromSourceData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param)
                .then(async (res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        await this.queryDetail()
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        showFlow () {
            this.flowId = this.formData.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_APUziNxOmAQLW_25fa4a00`, '当前审批策略不能查看流程！'))
                return
            }
            this.flowView = true
        },
        async downloadEvent (row) {
            row.subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.subId
            this.$previewFile.open({ params: row })
        },
        handleTableAdd () {
            let url = '/tender/purchaseTenderProjectHead/discardSubpckage'
            let columns = [
                { field: 'subpackageNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsAy_269085a0`, '分包编号') },
                { field: 'subpackageName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称') },
                { field: 'tenderType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_biddingType`, '招标类型') },
                { field: 'checkType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJCK_2b39a7da`, '审查方式') },
                { field: 'bidOpenType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_vBCK_2cc273bd`, '开标方式') },
                { field: 'processType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_YBLT_2e859370`, '招标程序') }
            ]
            let checkedConfig = {
                checkMethod: ({ row }) => {
                    // 当前分包不能勾选
                    if (row.id == this.subId) {
                        return false
                    }
                    return true
                }
            }
            this.$refs.fieldSelectModal.open(url, { projectId: this.tenderCurrentRow.id }, columns, 'multiple', checkedConfig)
        },
        fieldSelectOk (data) {
            for(let item of data) {
                item.subpackageId = item.id
                delete item.id
            }
            this.$refs.listTable.insertAt(data, -1)
        },
        handleTableDel () {
            this.$refs.listTable.removeCheckboxRow()
        },
        handleSave (v, callBack = null) {
            let url = this.formData.id ? this.url.edit : this.url.add
            let purchaseTenderAgainSubpackageItemList = this.$refs['listTable'].getTableData().fullData
            let params = Object.assign(
                this.formData,
                {
                    purchaseTenderAgainSubpackageItemList,
                    subpackageId: this.subId,
                    projectId: this.tenderCurrentRow.id
                }
            )
            this.confirmLoading = true
            postAction(url, params)
                .then((res) => {
                    if (res.success) {
                        if (!this.formData.id) {
                            this.$set(this.formData, 'id', res.result.id)
                        }
                        if (callBack) {
                            console.log(callBack)
                            this.formData = res.result || {}
                            this.purchaseTenderAgainSubpackageItemList = this.formData.purchaseTenderAgainSubpackageItemList || []
                            callBack()
                        } else {
                            this.$message.success(res.message)
                            this.queryDetail()
                        }
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleSubmit () {
            let that = this
            this.$refs.baseForm.validate((valid) => {
                if (valid) {
                    that.confirmLoading = true
                    let purchaseTenderAgainSubpackageItemList = this.$refs['listTable'].getTableData().fullData
                    let params = Object.assign(
                        this.formData,
                        {
                            purchaseTenderAgainSubpackageItemList,
                            subpackageId: this.subId,
                            projectId: this.tenderCurrentRow.id
                        }
                    )
                    this.confirmLoading = true
                    postAction(this.url.publish, params)
                        .then((res) => {
                            if (res.success) {
                                // this.$emit('resetCurrentSubPackage') || ''
                                this.resetCurrentSubPackage()
                                this.$message.success(res.message)
                                this.queryDetail()
                                if (params.againProject == '0') {
                                    this.$emit('getSubpackage')
                                }
                            } else {
                                this.$message.error(res.message)
                            }
                        })
                        .finally(() => {
                            this.confirmLoading = false
                        })
                }
            })
        },
        handlePublish () {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布?'),
                onOk: () => {
                    this.handleSave(null, () => {
                        this.handleSubmit()
                    })
                }
            })
        },
        queryDetail () {
            this.confirmLoading = true
            let params = {
                subpackageId: this.subId
            }
            getAction(this.url.queryDetailBySubpackageId, params)
                .then((res) => {
                    if (res.success) {
                        this.formData = res.result || {}
                        if (this.formData.id) {
                            this.purchaseTenderAgainSubpackageItemList = this.formData.purchaseTenderAgainSubpackageItemList || []
                        } else {
                            let row = Object.assign({}, this.subPackageRow)
                            row.subpackageId = this.subId
                            this.purchaseTenderAgainSubpackageItemList = [row]
                        }
                        if (this.formData.againSupplier == '0') {
                            this.changeAgainSupplier(this.formData.againSupplier)
                        }
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        beforeUpload (e) {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
                e.stopPropagation()
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if (this.formData.id) {
                if (file.status === 'done') {
                    if (file.response.success) {
                        let { fileName, filePath, fileSize, id, headId } = file.response.result
                        let fileListData = {
                            uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                            uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName,
                            filePath,
                            fileSize,
                            id,
                            headId
                        }
                        if (!this.formData.purchaseAttachmentDTOList) this.$set(this.formData, 'purchaseAttachmentDTOList', [])
                        this.formData.purchaseAttachmentDTOList.push(fileListData)
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                    this.formData.purchaseAttachmentDTOList = fileList.map((res) => {
                        let { fileName, filePath, fileSize, id, headId } = res.response.result
                        return {
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName,
                            filePath,
                            fileSize,
                            id,
                            headId
                        }
                    })
                }
            }
        },
        handleDeleteFile (index) {
            this.formData.purchaseAttachmentDTOList.splice(index, 1)
        },
        initPurchaseTenderAgainSubpackageItemList () {
            this.confirmLoading = true
            let params = {
                subpackageId: this.subId
            }
            getAction(this.url.queryDetailBySubpackageId, params)
                .then((res) => {
                    if (res.success) {
                        if (res.result.id) {
                            this.purchaseTenderAgainSubpackageItemList = res.result.purchaseTenderAgainSubpackageItemList || []
                        } else {
                            this.purchaseTenderAgainSubpackageItemList = [this.subPackageRow]
                        }
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        changeAgainSupplier (val) {
            this.showTable = false
            let arr = this.statictableColumns.map((item) => {
                if (item.field == 'tenderProcessModelName') {
                    if (val == '1') {
                        item = {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YBQLCcRL_63b39603`, '招标流程模型名称'),
                            width: 160,
                            field: 'tenderProcessModelName'
                        }
                    } else {
                        item = {
                            fieldType: 'selectModal',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YBQLCcRL_63b39603`, '招标流程模型名称'),
                            width: 160,
                            field: 'tenderProcessModelName',
                            bindFunction: (row, data, that, parentRef, rowIndex) => {
                                console.log(row, data, that, rowIndex,  '===')
                                let { id, subpackageNumber, subpackageName } = row
                                let { tenderProcessName, id: tenderProcessModelId, ...other } = data[0]
                                let obj = Object.assign(other, {
                                    id,
                                    subpackageNumber,
                                    subpackageName,
                                    tenderProcessModelName: tenderProcessName,
                                    tenderProcessModelId
                                })
                                console.log('obj', obj)
                                this.$set(this.purchaseTenderAgainSubpackageItemList, rowIndex, obj)
                            },
                            
                            extend: {
                                modalUrl: '/tender/tenderProcessModelHead/list',
                                selectModel: 'single',
                                modalColumns: [
                                    { field: 'tenderProcessNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLAo_3383fe15`, '流程编码') },
                                    { field: 'tenderProcessName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_processName`, '流程名称') },
                                    { field: 'processType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_YBLT_2e859370`, '招标程序') },
                                    { field: 'tenderType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_biddingType`, '招标类型') },
                                    { field: 'checkType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJCK_2b39a7da`, '审查方式') },
                                    { field: 'bidOpenType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_vBCK_2cc273bd`, '开标方式') }
                                ],
                                checkedConfig: {
                                    checkMethod: ({ row }) => {
                                        let ids = this.$refs.listTable.getTableData().fullData.map((item) => item.id) || []
                                        // 当前分包不能勾选
                                        if (ids.includes(row.id)) {
                                            return false
                                        }
                                        return true
                                    }
                                },
                                modalParams: {
                                    status: '1'
                                }
                            }
                        }
                    }
                }
                return item
            })
            if (val == '1') {
                this.initPurchaseTenderAgainSubpackageItemList()
            }
            setTimeout(() => {
                this.statictableColumns = arr
                this.showTable = true
            }, 50)
        }
    },
    mounted () {
        this.queryDetail()
        this.resetCurrentSubPackage()
    }
}
</script>
<style lang="less" scoped>
.deliveryTime-title,
.clarification-title {
    padding: 8px;
    background: #f2f3f5;
}
:deep(.edit-page .textAreaClass .ant-form-item-control-wrapper ){
    width: 88%;
}
:deep(.edit-page .textAreaClass .ant-form-item-label){
    width: 12%;
}
</style>
