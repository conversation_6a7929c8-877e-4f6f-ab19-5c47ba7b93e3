<template>
  <div class="els-page-comtainer">
    
    <a-page-header
      :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
      :title="title"
    >
      <template slot="extra">
        <a-button
          @click="handleOk"
          type="primary"
          key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <a-button
          @click="goBack"
          key="3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-page-header>
    <div class="table-page-search-wrapper">
      <a-spin :spinning="confirmLoading">
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            key="1"
            :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
          >
            <a-form 
              class="ant-advanced-search-form"
              layout="inline"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
              :form="form">
              <a-row :gutter="24">
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称')">
                    <a-input
                      readOnly
                      @click="selectSupplier"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectSupplierName`, '请点击选择供应商')"
                      v-decorator="['supplierName', validatorRules.supplierName]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码')">
                    <a-input
                      disabled
                      v-decorator="['supplierCode']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialCode`, '物料编码')">
                    <a-input
                      readOnly
                      @click="selectMaterialMaster"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_clickSelectMaterialCode`, '请点击选择物料编码')"
                      v-decorator="['materialCode', validatorRules.materialCode]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialDesc`, '物料描述')">
                    <a-input
                      disabled
                      v-decorator="['materialDesc']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_minPackaging`, '最小包装量')">
                    <a-input-number 
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterMinPackaging`, '请输入最小包装量')"
                      :min="0"
                      :max="*********"
                      :precision="4"
                      style="width:100%"
                      v-decorator="['minPackQuantity', validatorRules.minPackQuantity]" />
                  </a-form-item>
                </a-col> 
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialSpec`, '物料规格')">
                    <a-input
                      disabled
                      v-decorator="['materialSpec']" />
                  </a-form-item>
                </a-col>
                              
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_minOrderQuantity`, '最小订单量')">
                    <a-input-number
                      :min="0"
                      :max="*********"
                      :precision="4"
                      style="width:100%"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterMinOrderQuantity`, '请输入最小订单量')"
                      v-decorator="['minOrderQuantity', validatorRules.minOrderQuantity]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterProcurementCycle`, '请输入采购周期')"
                      v-decorator="['purchaseCycle', validatorRules.purchaseCycle]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialGroup`, '物料组')">
                    <a-input
                      disabled
                      @click="selectMaterialGroup"
                      placeholder=""
                      v-decorator="['materialGroupCode', validatorRules.materialGroupCode]"/>
                   
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialGroupName`, '物料组名称')">
                    <a-input
                      disabled
                      v-decorator="['materialGroupName']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_PurchaseMassProdHeadList_factoryCode`, '工厂编码')">
                    <j-dict-select-tag
                      placeholder=""
                      v-decorator="['factoryCode', validatorRules.factoryCode]"
                      disabled
                      :trigger-change="true"
                      dict-code="isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code='factory'"
                      @change="changeFactoryCode"
                    /> 
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_PurchaseMassProdHeadList_factoryCode_dictText`, '工厂名称')">
                    <a-input
                      disabled
                      v-decorator="['factoryName']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_procurementSectionCode`, '采购组编码')">
                    <j-dict-select-tag
                      placeholder=""
                      v-decorator="['purchaseGroupCode', validatorRules.purchaseGroupCode]"
                      :trigger-change="true"
                      disabled
                      dict-code="isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code='purchaseGroup'"
                      @change="changePurchaseGroupCode"
                    /> 
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_PurchaseMassProdHeadList_purchaseGroupCode_dictText`, '采购组名称')">
                    <a-input
                      disabled
                      v-decorator="['purchaseGroupName']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_procurementOrganizationCode`, '采购组织编码')">
                    <j-dict-select-tag
                      placeholder=""
                      v-decorator="['purchaseOrgCode', validatorRules.purchaseOrgCode]"
                      :trigger-change="true"
                      disabled
                      dict-code="isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code='purchaseOrg'"
                      @change="changePurchaseOrgCode"
                    /> 
                    
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_massProdHead92a_purchaseOrgCode_dictText`, '采购组织名称')">
                    <a-input
                      disabled
                      v-decorator="['purchaseOrgName']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_companyCode`, '公司编码')">
                    <j-dict-select-tag
                      placeholder=""
                      disabled
                      v-decorator="['companyCode', validatorRules.companyCode]"
                      :trigger-change="true"
                      dict-code="isrm_base_org_info,org_code,org_code,is_deleted=0 and org_type_code='company'"
                      @change="changeCompanyCode"
                    /> 
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称')">
                    <a-input
                      disabled
                      v-decorator="['companyName']" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_quotationMethod`, '报价方式')">
                    <j-dict-select-tag
                      style="width:250px"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectQuotationMethod`, '请选择报价方式')"
                      :trigger-change="true"
                      v-decorator="['quotePriceWay', validatorRules.quotePriceWay]"
                      dict-code="isrmQuotePriceWay"
                      @change="changeQuotePriceWay"
                    /> 
                    <a-button
                      @click="ladderInfo"
                      v-show="showLadderInfo"
                      type="primary"
                      style="margin-left:12px"
                    >{{ $srmI18n(`${$getLangAccount()}#i18n_title_ladderInfo`, '阶梯信息') }}</a-button>
                  </a-form-item>

                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_currency`, '币别')">
                    <j-dict-select-tag
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectCurrencyTips`, '请选择币别')"
                      :trigger-change="true"
                      v-decorator="['currency', validatorRules.currency]"
                      dict-code="isrmCurrency"
                    /> 
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_enterTaxCode`, '税码')">
                    <j-dict-select-tag
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectEnterTaxCodeTips`, '请选择税码')"
                      :trigger-change="true"
                      v-decorator="['taxCode', validatorRules.taxCode]"
                      dict-code="isrmTaxCode"
                      @change="changeTaxCode"
                    /> 
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_taxRate`, '税率')">
                    <a-input-number
                      style="width:100%"
                      disabled
                      v-decorator="['taxRate', validatorRules.taxRate]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_netPrice`, '净价')">
                    <a-input-number
                      :min="0"
                      :max="*********"
                      :precision="4"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterNetPrice`, '请输入净价')"
                      style="width:100%"
                      @change="changeNetPrice"
                      v-decorator="['netPrice', validatorRules.netPrice]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_price`, '含税价')">
                    <a-input-number
                      :min="0"
                      :max="*********"
                      :precision="4"
                      style="width:100%"
                      @change="changePrice"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterPrice`, '请输入含税价')"
                      v-decorator="['price', validatorRules.price]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_priceBase`, '价格基数')">
                    <a-input-number
                      style="width:100%"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterPriceBase`, '请输入价格基数')"
                      v-decorator="['priceBase', validatorRules.priceBase]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_priceUnit`, '价格单位')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterPriceUnit`, '请输入价格单位')"
                      v-decorator="['priceUnit', validatorRules.priceUnit]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_basicUnit`, '基本单位')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterBasicUnit`, '请输入基本单位')"
                      v-decorator="['baseUnit', validatorRules.baseUnit]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_orderUnit`, '订单单位')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterOrderUnit`, '请输入订单单位')"
                      v-decorator="['orderUnit', validatorRules.orderUnit]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_quota`, '配额')">
                    <a-input-number
                      :min="0"
                      :max="100"
                      :precision="4"
                      style="width:100%"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterQuota`, '请输入配额')"
                      v-decorator="['quota', validatorRules.quota]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_startDistributionNum`, '启配数量')">
                    <a-input
                      :min="0"
                      :max="*********99"
                      :precision="4"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterStartDistributionNum`, '请输入启配数量')"
                      v-decorator="['startQuotaQuantity', validatorRules.startQuotaQuantity]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_effectiveDate`, '生效日期')">
                    <a-date-picker v-decorator="[ 'effectiveDate', validatorRules.effectiveDate ]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_unableDate`, '失效日期')">
                    <a-date-picker v-decorator="[ 'expiryDate', validatorRules.expiryDate ]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_field_quotePriceDate`, '报价日期')">
                    <a-date-picker v-decorator="[ 'quotePriceDate', {}]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_sourceType`, '来源类型')">
                    <j-dict-select-tag
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectSourceType`, '请选择来源类型')"
                      :trigger-change="true"
                      v-decorator="['sourceType', validatorRules.sourceType]"
                      dict-code="isrmPriceMasterSourceType"
                    />  
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterSourceOrderNumber`, '请输入来源单号')"
                      v-decorator="['sourceNumber', validatorRules.sourceNumber]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_title_sourceLineNumber`, '来源单行号')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterSourceLineNumber`, '请输入来源单行号')"
                      v-decorator="['sourceItemNumber', validatorRules.sourceItemNumber]" />
                  </a-form-item>
                </a-col>
                <a-col :span="12" >
                  <a-form-item
                    :label="$srmI18n(`${$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')">
                    <a-input
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterRemark`, '请输入备注')" 
                      v-decorator="['remark', validatorRules.remark]" />
                  </a-form-item>
                </a-col>

              
              </a-row></a-form>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </div>
    <select-modal
      ref="supplierList"
      :url="url.querySupplierList"
      :columns="selectSupplierColumns"
      :title="selectSupplierTitle"
      @ok="selectSupplierOk"/>
    <select-modal
      ref="MaterialMasterList"
      :url="url.queryMaterialMasterList"
      :columns="selectMaterialMasterColumns"
      :title="selectMaterialMasterTitle"
      @ok="selectMaterialMasterOk"/>
    <select-modal
      ref="MaterialGroupList"
      :url="url.queryMaterialGroupList"
      :columns="selectMaterialGroupColumns"
      :title="selectMaterialGroupTitle"
      @ok="selectMaterialGroupOk"/>
    <View-Ladder-Price-Modal
      ref="ladderPage"
    />
  </div>
  <!-- </a-modal> -->
</template>

<script>
import moment from 'moment'
import pick from 'lodash.pick'
import { duplicateCheck } from '@/api/api'
import { httpAction, getAction } from '@/api/manage'
import selectModal from '@comp/selectModal/selectModal'
import ViewLadderPriceModal from '@views/srm/enquiry/modules/ViewLadderPriceModal'
export default {
    components: {
        selectModal,
        ViewLadderPriceModal
    },
    name: 'PriceMasterModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            activeKey: ['1'],
            fixPageHeader: false,
            showLadderInfo: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceMainDate`, '价格主数据'),
            visible: false,
            model: {
                quotePriceWay: '0'
            },
            labelCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            wrapperCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            confirmLoading: false,
            form: this.$form.createForm(this),
            validatorRules: {
                supplierName: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierNameCantBeEmpty`, '供应商名称不能为空!') }, {max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                price: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterPrice`, '请输入含税价!') }]},                
                factoryCode: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                factoryName: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                materialCode: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectMaterialTips`, '请选择物料!') }, {max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                materialDesc: {rules: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符' )}]},
                materialSpec: {rules: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符' )}]},
                materialGroupCode: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                materialGroupName: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                purchaseCycle: {rules: [{max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符' )}]},
                purchaseGroupCode: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                purchaseGroupName: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                purchaseOrgCode: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                purchaseOrgName: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                companyCode: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                companyName: {rules: [{max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                currency: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currencyCantBeEmpty`, '币别不能为空!') }, {max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符' )}]},
                taxCode: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectEnterTaxCodeTips`, '请选择税码!') }, {max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符' )}]},
                taxRate: {rules: [{max: 1000}]},
                priceUnit: {rules: [{max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符' )}]},
                baseUnit: {rules: [{max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符' )}]},
                orderUnit: {rules: [{max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符' )}]},
                sourceType: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                sourceNumber: {rules: [{max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                sourceItemNumber: {rules: [{max: 10, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow10`, '内容长度不能超过10个字符' )}]},
                effectiveDate: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_effectiveDateCantBeEmpty`, '价格生效日期不能为空!') }]},
                expiryDate: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expiryDateCantBeEmpty`, '价格失效日期不能为空!') }]},
                remark: {rules: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符' )}]}
            },
            url: {
                add: '/base/priceMaster/add',
                edit: '/base/priceMaster/edit',
                querySupplierList: '/supplier/supplierMaster/list?supplierStatus=2',
                queryMaterialMasterList: '/base/materialMaster/list',
                queryMaterialGroupList: '/base/materialGroup/list',
                queryTaxCodeList: '/base/priceMaster/queryTaxCode?dictCode=isrmTaxCode'
            },
            selectSupplierColumns: [
                { type: 'checkbox', width: 40, align: 'center'},
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),  align: 'center'},
                { field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'), align: 'center'},
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),  align: 'center'},
                { field: 'supplierStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'), align: 'center'}           
            ],
            selectSupplierTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierNameTitle`, '选择供应商'),
            selectMaterialMasterColumns: [
                { type: 'checkbox', width: 40, align: 'center'},
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),  align: 'center'},
                { field: 'materialCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), align: 'center'},
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), align: 'center'},
                { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),  align: 'center'}           
            ],
            selectMaterialMasterTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectMaterialTitle`, '选择物料'),
            selectMaterialGroupColumns: [
                { type: 'checkbox', width: 40, align: 'center'},
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),  align: 'center'},
                { field: 'materialGroupCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroupCode`, '物料组编码'),  align: 'center'},
                { field: 'materialGroupName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroupName`, '物料组名称'),  align: 'center'},
                { field: 'materialGroupLevel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroupLevel`, '分组级别'), align: 'center'}           
            ],
            selectMaterialGroupTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectMaterialGroupTitle`, '选择物料组'),
            selectTaxCodeColumns: [
                { type: 'checkbox', width: 40, align: 'center'},
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),  align: 'center'},
                { field: 'itemValue', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'),  align: 'center'},
                { field: 'itemText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_describe`, '描述'), align: 'center'},
                { field: 'description', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), align: 'center'}           
            ],
            selectTaxCodeTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectEnterTaxCodeTitle`, '选择税码')
        }
    },
    mounted () {
        this.init()
        window.addEventListener('scroll', this.handleScroll)
    },
    methods: {
        ladderInfo (){
            this.$refs.ladderPage.open(this.currentEditRow)
        },
        init () {
            if(this.currentEditRow) {
                this.edit(this.currentEditRow)
            }else {
                this.add()
            }
        },
        add () {
            this.edit({quotePriceWay: '0'})
            this.title =  this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addPriceMainDate`, '新增价格主数据')
        },
        edit (record) {
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editPriceMainDate`, '编辑价格主数据')
            this.form.resetFields()
            this.model = Object.assign({}, record)
            this.visible = true
            this.$nextTick(() => {
                this.form.setFieldsValue(pick(this.model, 'supplierId', 'supplierCode', 'supplierName', 'factoryCode', 'factoryName', 'materialCode', 'materialDesc', 'materialSpec', 'materialGroupCode', 'materialGroupName', 'minPackQuantity', 'minOrderQuantity', 'purchaseCycle', 'purchaseGroupCode', 'purchaseGroupName', 'purchaseOrgCode', 'purchaseOrgName', 'companyCode', 'companyName', 'quotePriceWay', 'currency', 'taxCode', 'taxRate', 'netPrice', 'price', 'priceBase', 'priceUnit', 'baseUnit', 'orderUnit', 'quota', 'startQuotaQuantity', 'effectiveDate', 'expiryDate', 'quotePriceDate', 'sourceType', 'sourceNumber', 'sourceItemNumber', 'remark', 'fbk1', 'fbk2', 'fbk3', 'fbk4', 'fbk5', 'fbk6', 'fbk7', 'fbk8', 'fbk9', 'fbk10', 'fbk11', 'fbk12', 'fbk13', 'fbk14', 'fbk15', 'fbk16', 'fbk17', 'fbk18', 'fbk19', 'fbk20', 'extendField'))
                //时间格式化
                this.form.setFieldsValue({effectiveDate: this.model.effectiveDate?moment(this.model.effectiveDate):null})
                this.form.setFieldsValue({expiryDate: this.model.expiryDate?moment(this.model.expiryDate):null})
                this.form.setFieldsValue({quotePriceDate: this.model.quotePriceDate?moment(this.model.quotePriceDate):null})
            })
            if(record.quotePriceWay == 1){
                this.showLadderInfo = true
            }
        },
        validateCode (rule, value, callback) {
        // 重复校验
            var params = {
                tableName: '',
                fieldName: '',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then((res) => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        selectSupplier (){
            this.$refs.supplierList.open()
        },

        selectSupplierOk (data){
            let selectResult =  {'supplierId': data[0].id, 'supplierCode': data[0].supplierCode, 'supplierName': data[0].supplierName}
            this.model = Object.assign(this.model, selectResult)
            this.form.setFieldsValue(pick(selectResult, 'supplierId', 'supplierCode', 'supplierName' ))
        },

        selectMaterialMaster (){
            this.$refs.MaterialMasterList.open()
        },
        selectMaterialMasterOk (data){
            let selectResult =  {'materialCode': data[0].materialCode,
                'materialDesc': data[0].materialDesc, 
                'materialSpec': data[0].materialSpec,
                'materialGroupCode': data[0].materialGroupCode, 
                'materialGroupName': data[0].materialGroupName,
                'factoryCode': data[0].factoryCode,
                'factoryName': data[0].factoryName,
                'purchaseGroupCode': data[0].purchaseGroupCode,
                'purchaseGroupName': data[0].purchaseGroupName,
                'purchaseOrgCode': data[0].purchaseOrgCode,
                'purchaseOrgName': data[0].purchaseOrgName,
                'companyCode': data[0].companyCode,
                'companyName': data[0].companyName
            }
            this.model = Object.assign(this.model, selectResult)
            this.form.setFieldsValue(pick(selectResult, 'materialCode', 'materialDesc', 'materialSpec',
                'materialGroupCode', 'materialGroupName', 'factoryCode', 
                'factoryName', 'purchaseGroupCode', 'purchaseGroupName', 'purchaseOrgCode', 'purchaseOrgName', 'companyCode', 'companyName'))
        },
        selectMaterialGroup (){
            this.$refs.MaterialGroupList.open()
        },
        selectMaterialGroupOk (data){
            let selectResult =  {'materialGroupCode': data[0].materialGroupCode, 'materialGroupName': data[0].materialGroupName}
            this.model = Object.assign(this.model, selectResult)
            this.form.setFieldsValue(pick(selectResult, 'materialGroupCode', 'materialGroupName' ))
        },
        
        changeTaxCode (value){
            if(value){
                let reslut = value.split('_')
                let selectResult =  {'taxRate': reslut[1]}
                this.model = Object.assign(this.model, selectResult)
                this.form.setFieldsValue(pick(selectResult, 'taxRate' )) 
            }else{
                let selectResult =  {'taxRate': ''}
                this.model = Object.assign(this.model, selectResult)
                this.form.setFieldsValue(pick(selectResult, 'taxRate' )) 
            }
            let taxTateValue = this.form.getFieldValue('taxRate')
            let netPriceValue = this.form.getFieldValue('netPrice')
            let priceValue = this.form.getFieldValue('price')

            if(netPriceValue&&taxTateValue){
                let price = (parseFloat(taxTateValue)+1)*netPriceValue
                this.form.setFieldsValue({price: price}) 
            }else if(priceValue&&taxTateValue){
                let netPrice = priceValue/(parseFloat(taxTateValue)+1)
                this.form.setFieldsValue({netPrice: netPrice}) 
            }
        },
        changeNetPrice (value){
            let taxTateValue = this.form.getFieldValue('taxRate')
            if(taxTateValue&&value){
                let price = (parseFloat(taxTateValue)+1)*value
                this.form.setFieldsValue({price: price}) 
            }
        },
        changePrice (value){
            let taxTateValue = this.form.getFieldValue('taxRate')
            if(taxTateValue&&value){
                let netPrice = value/(parseFloat(taxTateValue)+1)
                this.form.setFieldsValue({netPrice: netPrice})  
            }
        },
        getOrgName (value, url, setOrgName){
            let that = this
            let keyObj = {}
            if(value){
                let params = {}
                getAction(url, params).then(res => {
                    if(res.success&&res.result) {
                        keyObj[setOrgName] = res.result.orgName                    
                        that.form.setFieldsValue(keyObj) 
                    }
                })
            }else{
                keyObj[setOrgName] = ''   
                that.form.setFieldsValue(keyObj) 
            }    
        },

        changeFactoryCode (value){
            let url = '/base/orgInfo/getOrgName/factory/'+value
            this.getOrgName(value, url, 'factoryName')
        },
        changePurchaseGroupCode (value){
            let url = '/base/orgInfo/getOrgName/purchaseGroup/'+value
            this.getOrgName(value, url, 'purchaseGroupName')
        },
        changePurchaseOrgCode (value){
            let url = '/base/orgInfo/getOrgName/purchaseOrg/'+value
            this.getOrgName(value, url, 'purchaseOrgName')
        },

        changeCompanyCode (value){
            let url = '/base/orgInfo/getOrgName/company/'+value
            this.getOrgName(value, url, 'companyName')
        },
        changeQuotePriceWay (value){
            if(value == 1){
                this.showLadderInfo = true
            }else{
                this.showLadderInfo = false
            }
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    that.confirmLoading = true
                    let httpurl = ''
                    let method = ''
                    if(!this.model.id){
                        httpurl+=this.url.add
                        method = 'post'
                    }else{
                        httpurl+=this.url.edit
                        method = 'post'
                    }
                    let formData = Object.assign(this.model, values)
                    //时间格式化
            
                    console.log(formData)
                    httpAction(httpurl, formData, method).then((res)=>{
                        if(res.success){
                            that.$message.success(res.message)
                            that.$emit('ok')
                            that.goBack()
                        }else{
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        handleScroll () {
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 50) {
                this.fixPageHeader = true
            }else {
                this.fixPageHeader = false
            }
        }
    }
}
</script>

<style lang="less" scoped>

</style>