<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见')"
          prop="opinion">
          <a-textarea
            show-word-limit
            v-model="form.opinion"
            allowClear
            type="textarea"
            autoSize />
        </a-form-model-item>
        <a-form-model-item
          required
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_srL_224b448`, '转办人')"
          prop="usersInfo">
          <a-tag
            v-if="singleUserData != null && singleUserData.userNo != null"
            size="large"
            color="blue"
            closable
            @close="delSingleUsers"
          >{{ singleUserData.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectSingleShowUsers"></a-button>
          </div>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { transferTask } from '../../api/analy.js'
export default {
    mixins: [modalMixins],
    data () {
        return {
            rules: {
                opinion: [
                    // { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMqdWII_13f544fb`, '请填写备注/意见'), trigger: 'change' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                usersInfo: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFsrL_f3edb6b1`, '请选择转办人') } ]
            },
            singleUserData: {}
        }
    },
    methods: {
        selectSingleShowUsers () {
            this.showUserSelectModal({ selectModel: 'single' })
        },
        fieldSelectOk (data) {
            this.$set(this.form, 'usersInfo', data[0])
            this.singleUserData = data[0]
        },
        delSingleUsers () {
            this.singleUserData = {}
        },
        handleConfirm () {
            this.cheackValidate().then(() => {
                this.loading = true
                transferTask(this.form).then(res => {
                    this.loading = false
                    if (res.code == 0) {
                        this.$message.success(res.message)
                        this.$emit('success')
                    } else {
                        this.$message.error(res.msg)
                    }
                }).finally(() => {
                    this.loading = false
                    this.$emit('closeLoading')
                    this.clearValidate()
                })
            })
        }
    },
    created () {}
}
</script>
