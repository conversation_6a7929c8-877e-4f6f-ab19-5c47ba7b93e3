<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <content-header
        v-if="showHeader"
        :btns="btns"
      />
      <div
        class="container"
        :style="style">
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidWinNotice`, '中标通知书') }}</span>
          </titleTrtl>
          <vxe-grid
            v-bind="gridConfig"
            :height="250"
            ref="table"
            :data="tableData"
            :columns="tableColumns"
            show-overflow="title" >
            <template #grid_opration="{ row, column }">
              
              <div>
                <a
                  style="margin:0 4px"
                  @click="preViewEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                <a @click="downloadEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
              </div>
            </template>
          </vxe-grid>
        </div>
      </div>
    </a-spin>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '../components/content-header'
import titleTrtl from '../components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    name: 'TenderBidWinningNotice',
    components: {
        titleTrtl,
        ContentHeader
    },
    mixins: [tableMixins],
    data () {
        return {
            confirmLoading: false,
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    'field': 'scopeSort',
                    width: '80'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHf_252229e6`, '中标金额'),
                    field: 'winnerAmount',
                    width: '100'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidWinNotice`, '中标通知书'),
                    'field': 'purchaseAttachmentDTOList',
                    slots: {
                        default: ({ row, column }) => {
                            return [
                                // <span >{row.purchaseAttachmentDemandDTOList[0]}</span>
                                row['purchaseAttachmentDTOList'].length> 0 ? <div ><span style='color: blue' onClick={() => this.preViewEvent(row)}>{row['purchaseAttachmentDTOList'][0]['fileName']} </span></div>: ''
                            ]
                        }
                    }
                }, {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    'field': 'evaPrice3',
                    slots: {default: 'grid_opration' }
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            btns: [
            ],
            url: {
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryById: '/tender/sale/saleTenderProjectBidWinningAffirmItem/queryBySubpackageId'
            }
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'resetCurrentSubPackage'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        pageStatus () {
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                return 'detail'
            } else {
                if (this.formData.status == '0' || !this.formData.status) {
                    return 'edit'
                } else {
                    return 'detail'
                }
            }
        }
    },
    methods: {
        async getData () {
            let params = {
                subpackageId: this.subId
            }
            this.confirmLoading = true
            try {
                let res2 = await getAction(this.url.queryById, params)
                if (res2.code == 200 && res2.result) {
                    this.formData = res2.result || {}
                    this.formData.saleQuoteColumnVO && this.formData.saleQuoteColumnVO.forEach(vos => {
                        this.formData[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                        this.formData.affirm = this.formData.affirm == '1' ? true : false
                    })
                    this.tableData = [this.formData]
                }
                const res = await getAction(this.url.queryPrice, params)
                if (res.code == 200 && res.result) {
                    // 分项报价且线上评标的时候，去掉排名
                    if ((res2.result.quoteType == '1' && res2.result.evaluationType =='1') || (res2.result.quoteType == null)){
                        this.tableColumns = this.tableColumns.filter(item=>{
                            return item['field'] != 'scopeSort'
                        })
                    } else {
                        const resultData = res.result
                        let columns = []
                        resultData.forEach(data => {
                            let obj = {
                                title: data.title,
                                children: []
                            }
                            let columnChildren = []
                            data.quoteColumnList.forEach(column => {
                                column['field'] = `${column['field']}_${data['id']}`
                                columnChildren.push(column)
                            })
                            obj.children = columnChildren
                            columns.push(obj)
                        })
                        // 如果是线下的情况，则替换投标报价字段field
                        if(res2.result.evaluationType == '2'){
                            columns.forEach(item=>{
                                item.children.forEach(item2=>{
                                    item2.field = 'evaPrice'
                                })
                            })
                        }
                        this.tableColumns = this.tableColumns.filter(column => {
                            if (!column.hasOwnProperty('children')) {
                                return column
                            }
                        })
                        this.tableColumns.splice(3, 0, ...columns)
                    }
                    
                }
                console.log('tableColumns', this.tableColumns)
            } catch (error) {
                console.log(error)
                this.confirmLoading = false
            }
            this.confirmLoading = false
            this.resetCurrentSubPackage()
        },
        preViewEvent (row){
            row.purchaseAttachmentDTOList[0].subpackageId = this.subId
            this.$previewFile.open({params: row.purchaseAttachmentDTOList[0] })
        },
        async downloadEvent (row) {
            console.log(row, ' === ')
            row.purchaseAttachmentDTOList[0].subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row.purchaseAttachmentDTOList[0])
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.purchaseAttachmentDTOList[0].fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
        }
    },
    mounted () {
        this.init()
    }
}
</script>
<style lang="less" scoped>
.container{

    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
.margin-t-20{
  margin-top: 20px;
}
.label{
  text-align:right;
  padding-right: 10px;
}
</style>




