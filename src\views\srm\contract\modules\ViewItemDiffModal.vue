<template>
  <a-modal
    v-drag    
    v-model="diffModal"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_differenceComparison`, '差异对比')"
    :footer="null"
    :width="1300">
    <div class="code-diff">
      <code-diff
        :old-string="oldStr"
        :new-string="newStr"
        outputFormat="side-by-side" />
    </div>
  </a-modal>
</template>
<script>
import {CodeDiff} from 'v-code-diff'
export default {
    name: 'ViewItemDiff',
    components: {CodeDiff},
    data () {
        return {
            diffModal: false,
            oldStr: '',
            newStr: ''
        }
    },
    methods: {
        open (row) {
            this.diffModal = true
            let targetRow = JSON.parse(JSON.stringify(row))
            let oldStr = targetRow.originalContent.replace(/<(.*?)>/g, '').replace(/&nbsp;/g, ' ')
            let newStr = targetRow.itemContent.replace(/<(.*?)>/g, '').replace(/&nbsp;/g, ' ')
            this.oldStr = oldStr
            this.newStr = newStr
        }
    }
}
</script>