<template>
  <div>
    <a-modal
    v-drag    
      centered
      v-model="showNode"
      :width="800"
      @ok="confirmAddColumn"
      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_newColumnCustomization`, '新增列自定义')}`">
      <div>
        <a-spin :spinning="confirmLoading">
          <a-form-model
            ref="form"
            :model="formData"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            :rules="rules">
            <a-row :getterr="12">
              <a-col :span="12">
                <a-form-model-item
                  :label="`${$srmI18n(`${$getLangAccount()}#i18n_dict_AR_a44d6`, '列名')}`"
                  prop="name"
                  required>
                  <a-input v-model="formData.name"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  :label="`${$srmI18n(`${$getLangAccount()}#i18n_dict_JIIAzA_e01fd393`, '自定义列分类')}`"
                  prop="fieldCategory"
                  required>
                  <m-select
                    v-model="formData.fieldCategory"
                    @change="handleChangeNodeFieldCategory"
                    :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                    dict-code="customerFieldCategory" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  :label="`${$srmI18n(`${$getLangAccount()}#i18n_title_fieldType`, '字段类型')}`"
                  prop="fieldType"
                  required>
                  <m-select
                    v-model="formData.fieldType"
                    :disabled="['0', '1'].includes(formData.fieldCategory)"
                    :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                    dict-code="columnDataType" />
                </a-form-model-item>
              </a-col>
              <a-col
                :span="12"
                v-if="formData.fieldType == 'dict'">
                <a-form-model-item
                  :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_WFJCAo_bc817d6a`, '数据字典编码')}`"
                  prop="dictCode"
                  required>
                  <a @click="() => showDictModal()">{{ formData.dictCode || $srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择') }}</a>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  :label="$srmI18n(`${$getLangAccount()}#i18n_field_KQlS_2fbb0b5d`, '是否必填')"
                  prop="must"
                  required>
                  <m-select
                    v-model="formData.must"
                    :disabled="['0', '1'].includes(formData.fieldCategory)"
                    :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                    dict-code="yn" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  :label="$srmI18n(`${$getLangAccount()}#i18n_field_WNtL_427fd64a`, '输入单位')"
                  prop="inputOrg"
                  required>
                  <m-select
                    v-model="formData.inputOrg"
                    :disabled="['0', '1'].includes(formData.fieldCategory)"
                    :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                    dict-code="customerFieldInputOrg" />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </a-spin>
      </div>
    </a-modal>
    <add-dict-code-modal
      ref="dictCodeModal"
      @ok="fieldSelectOk" />
  </div>
</template>
<script>
import AddDictCodeModal from '@/views/sys/config/modules/AddDictCodeModal'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'

export default {
    props: {
        currentNodeItme: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    components: {
        AddDictCodeModal
    },
    data () {
        return {
            showNode: false,
            labelCol: { span: 12 },
            wrapperCol: { span: 12 },
            confirmLoading: false,
            formData: {
                name: '',
                fieldCategory: '',
                fieldType: '',
                dictCode: '',
                must: '',
                inputOrg: ''
            },
            rules: {
                name: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNAR_f6db6cff`, '请输入列名') }],
                fieldCategory: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFAzA_f30795f5`, '请选择列分类') }],
                fieldType: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNJOAc_b250d017`, '请输入字段类型') }],
                dictCode: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFJCAo_8b30f661`, '请选择数据字典编码') }],
                inputOrg: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNtL_f6db8ec1`, '请输入单位') }],
                must: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFKQlS_77125614`, '请选择是否必填') }]
            },
            url: {
                add: '/tender/tenderCustomColumn/add',
                edit: '/tender/tenderCustomColumn/edit'
            }
        }
    },
    methods: {
        open (row = null) {
            this.formData = {
                must: '0',
                inputOrg: '1'
            }
            if (row) {
                this.formData = Object.assign({}, row)
                this.formData.must = this.formData.must + ''
            }
            this.showNode = true
        },
        confirmAddColumn () {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    let params = Object.assign({}, this.formData)
                    valiStringLength(params, [
                        {field: 'name', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_AR_a44d6`, '列名')}
                    ])
                    this.confirmLoading = true
                    let url = params.id ? this.url.edit : this.url.add
                    postAction(url, params)
                        .then((res) => {
                            let type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            if (res.success) {
                                this.$emit('confirmAddColumn')
                                this.showNode = false
                            }
                        })
                        .finally(() => {
                            this.confirmLoading = false
                        })
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },
        showDictModal () {
            this.$refs.dictCodeModal.open()
        },
        fieldSelectOk (data) {
            this.$set(this.formData, 'dictCode', data.dictCode)
        },
        handleChangeNodeFieldCategory (v) {
            if (v == '0') {
                this.$set(this.formData, 'fieldType', 'string')
                this.$set(this.formData, 'must', '1')
                this.$set(this.formData, 'inputOrg', '1')
            } else if (v == '1') {
                this.$set(this.formData, 'fieldType', 'number')
                this.$set(this.formData, 'must', '1')
                this.$set(this.formData, 'inputOrg', '0')
            }
        }
    }
}
</script>
