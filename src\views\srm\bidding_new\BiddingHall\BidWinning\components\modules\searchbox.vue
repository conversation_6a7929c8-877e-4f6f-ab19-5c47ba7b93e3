<template>
  <div>
    <div>
      <a-input-search
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '输入关键字')"
        allow-clear
        v-model="key"
        @change="onSearch"
        @search="onSearch" />
    </div>
    <div>
      <ul
        class="list"
        :style="{height: `${height - 45}px`}">
        <li
          class="item"
          :class="selectCodes.includes(item[Format.field]) ? 'activeBox': 'normalBox'"
          @click="handleInitData(item, i)"
          v-for="(item, i) in columnsdata"
          :key="i">{{ item[Format.title] }}</li>
      </ul>
    </div>
  </div>
</template>
<script lang="jsx">

export default {
    props: {
        height: {
            default: '200',
            type: [Number, String]
        },
        searchData: {
            default: () => {
                return []
            },
            type: Array
        },
        Format: {
            default: () => {
                return {
                    field: '',
                    title: ''
                }
            },
            type: Object
        }
    },
    data () {
        return {
            key: '',
            totalData: this.searchData,
            columnsdata: [],
            selectCodes: []
        }
    },
    methods: {
        onSearch () {
            let {title} = this.Format
            this.columnsdata = this.totalData.filter(item => {
                if (!this.key) return true
                return item[title].includes(this.key)
            })
        },
        handleInitData (item, i) {
            let {field} = this.Format
            if (this.selectCodes.includes(item[field])) {
                let key = this.selectCodes.indexOf(item[field])
                this.selectCodes.splice(key, 1)
            } else {
                this.selectCodes.push(item[field])
            }
            this.$emit('searchChange', this.selectCodes)
        }
    },
    created () {
        this.onSearch()
    }
}
</script>
<style lang="less" scoped>
ul, li{
    margin: 0;
    padding: 0;
}
li{
    list-style-type: none;
}
.list{
    overflow: auto;
}
.item{
    padding: 5px 10px;
    cursor: pointer;
    text-align: center;
    margin-top: 10px;
}
.normalBox{
    border: 1px solid #ccc;
}
.activeBox {
    border: 1px solid #1890ff;
}
</style>