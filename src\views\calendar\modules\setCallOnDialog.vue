<template>
  <div>
    <a-modal
      v-drag    
      v-model="visible"
      title="批量设置预约拜访日"
      @ok="handleOk"
    >
      <a-form
        :form="form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 12 }">
        <!-- <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_selectFactory`, '选择工厂')">
          <a-select
            :maxTagCount="5"
            mode="multiple"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')"
            v-model="form.factory"
          >
            <a-select-option
              v-for="el of factoryArr"
              :key="el.orgCode"
              :title="el.id"
              :value="el.orgCode">
              {{ el.orgName }}
            </a-select-option>
          </a-select>
        </a-form-item> -->
        <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_year`, '年')">
          <a-select
            :maxTagCount="5"
            mode="multiple"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selecteFfectiveTime`, '请选择生效时间')"
            v-model="form.yearSelect"
          >
            <a-select-option
              v-for="el of yearArr"
              :value="el.value"
              :key="el.value">
              {{ el.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_week`, '周')">
          <a-select
            :maxTagCount="5"
            mode="multiple"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')"
            v-model="form.weekSelect"
          >
            <a-select-option
              v-for="el of weekArr"
              :value="el.value"
              :key="el.value">
              {{ el.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_day`, '日')">
          <a-select
            :maxTagCount="5"
            mode="multiple"
            v-model="form.daySelect"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')"
          >
            <a-select-option
              v-for="el of dayArr"
              :value="el.value"
              :key="el.value">
              {{ el.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="开放预约时间点">
          <a-select
            :maxTagCount="5"
            mode="multiple"
            v-model="form.timeSelect"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')"
          >
            <a-select-option
              v-for="el of time"
              :value="el.value"
              :key="el.value">
              {{ el.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { postAction } from '@api/manage'
const createData = type => {
    const weekMark = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    let arr = []
    let len = 7
    switch (type) {
    case 'week':
        len = 7
        break
    case 'day':
        len = 31
        break
    case 'year':
        len = 50 // 设置50年
        break
    }
    let newYear = new Date().getFullYear()
    for (let i = 0; i < len; i++) {
        let obj = {
            value: type == 'week' ? i : type == 'day' ? i + 1 : newYear + i,
            name: type == 'week' ? weekMark[i] : type == 'day' ? `${i + 1}号` : `${newYear + i}年`
        }
        arr.push(obj)
    }
    return arr
}
export default {
    props: ['factoryArr'],
    data () {
        return {
            visible: false,
            weekArr: createData('week'),
            yearArr: createData('year'),
            dayArr: createData('day'),
            time: [],
            form: {
                weekSelect: [],
                daySelect: [],
                yearSelect: [],
                timeSelect: []
            }
        }
    },

    mounted () {
        this.selectOption()
    },
    methods: {
        selectOption (){
            postAction('/base/dict/findDictItems', {busAccount: `${this.$getLangAccount()}`, dictCode: 'calanderTime' }).then(res => {
                if(res.success == true){
                    this.time = res.result.map(ress=>{
                        return{
                            value: ress.value,
                            name: ress.value
                        }
                    })
                }
            })
        },
        open (data) {
            this.form  =  Object.assign({}, this.form, {...data})
            this.visible = true
        },
        handleSelectChange () {
            console.log(1)
        },
        handleOk () {
            const account = this.$getLangAccount()
            if (!this.form.yearSelect.length) {
                this.$message.error(this.$srmI18n(`${account}#i18n_title_selectYear`, '至少选择一个年范围'))
                return
            }
            if (this.form.weekSelect.length > 0 || this.form.daySelect.length > 0) {
                console.log(this.form)
                let data = Object.assign({ ...this.form })
                console.log(data)
                this.$emit('addCalendarCallon', { ...data })
                this.visible = false
            } else {
                this.$message.error(this.$srmI18n(`${account}#i18n_title_selectDay`, '周或者日至少选择一个~'))
            }
        }
    }
}
</script>

<style></style>
