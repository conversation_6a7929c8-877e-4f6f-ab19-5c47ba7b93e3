<template>
    <div class="els-page-comtainer">
        <tile-edit-page
            ref="editPage"
            :pageData="pageData"
            :url="url"
            @goBack="goBack"
        />
    </div>
</template>
<script>
import {tileEditPageMixin} from '@comp/template/tileStyle/tileEditPageMixin'
import {httpAction} from '@/api/manage'

export default {
    name: 'DevopsEventHandleModal',
    mixins: [tileEditPageMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_handle`, '处理'),
            confirmLoading: false,
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderReceiptDetail`, '处理详情'),
                form: {
                    id: '',
                    handleStatus: '',
                    handleNotes: '',
                },
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentReceiptInfo`, '处理信息'),
                        content: {
                            ref: 'baseForm',
                            colSpan: '8',
                            type: 'form',
                            list: [
                                {
                                    type: 'input',
                                    label: '标题',
                                    fieldName: 'title',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: '描述',
                                    fieldName: 'notes',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: '位置',
                                    fieldName: 'location',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: '业务信息',
                                    fieldName: 'bizNotes',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: '异常',
                                    fieldName: 'exNotes',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: '处理人id',
                                    fieldName: 'handleUserId',
                                    disabled: true
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_handleStatus`, '处理状态'),
                                    dictCode: 'devopsEventHandleStatus',
                                    fieldName: 'handleStatus',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectAServiceType`, '请选择处理状态'),
                                    disabled: false
                                },
                                {
                                    type: 'textarea',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_handleNotes`, '处理备注'),
                                    fieldName: 'handleNotes',
                                    disabled: false
                                }
                            ]
                        }
                    }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receive`, '处理'), type: 'primary', clickFn: this.mySaveEvent},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack}
                ]
            },
            url: {
                detail: '/devops/eventHandle/queryById',
                edit: '/devops/eventHandle/handle'
            },
        }
    },
    created() {
    },
    methods: {
        goBack() {
            this.$emit('hide')
        },
        mySaveEvent() {
            this.postUpdateData(this.url.edit, this.$refs.editPage.getParamsData())
        },
        postUpdateData(url, params) {
            let that = this
            this.$refs.editPage.confirmLoading = true
            httpAction(url, params, 'post').then((res) => {
                if (res.success) {
                    that.$message.success(res.message)
                    that.$parent.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
                that.$refs.editPage.confirmLoading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
</style>