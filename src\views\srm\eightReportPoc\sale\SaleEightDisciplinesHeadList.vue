<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage "
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <sale-eight-disciplines-head-edit
      v-if="showEditPage"
      ref="eightDisciplines"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <sale-eight-disciplines-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import saleEightDisciplinesHeadEdit from './modules/SaleEightDisciplinesHeadEdit'
import saleEightDisciplinesHeadDetail from './modules/SaleEightDisciplinesHeadDetail'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        saleEightDisciplinesHeadEdit,
        saleEightDisciplinesHeadDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'eightDisciplines',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNWWty_874ca137`, '请输入8D单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                        fieldName: 'eightDisciplinesStatus',
                        dictCode: 'srm8DStatus'
                    }
                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    //{label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    // {label: '删除', icon: 'delete', clickFn: this.handleDelete},
                    //{label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', folded: false, clickFn: this.handleExportXls},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'eightReportPoc#SaleEightReportPocHead:query' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEditSingle, allow: this.allowEdi, authorityCode: 'eightReportPoc#SaleEightReportPocHead:edit' },
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, authorityCode: 'eightReportPoc#SaleEightReportPocHead:chat'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord, authorityCode: 'eightReportPoc#SaleEightReportPocHead:record'}
                ],
                optColumnWidth: 270
            }, 
            url: {
                list: '/eightReport/saleEightDisciplinesPoc/list',
                add: '/eightReport/saleEightDisciplinesPoc/add',
                delete: '/eightReport/saleEightDisciplinesPoc/delete',
                deleteBatch: '/eightReport/saleEightDisciplinesPoc/deleteBatch',
                exportXlsUrl: '/eightReport/saleEightDisciplinesPoc/exportXls',
                importExcelUrl: '/eightReport/saleEightDisciplinesPoc/importExcel',
                columns: 'SaleEightDisciplinesHeadPoc'
            }
        }
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.eightDisciplinesNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SaleEightDisciplinesHead', url: this.url || '', recordNumber})
        },
        allowEdit (row){
            //采购审核状态==采购确认，采购审核，SQE验证，ICQ验证，时不能编辑
            if(row.fbk3=='1'||row.fbk3=='4'||row.fbk3=='6'||row.fbk3=='7'||row.fbk3=='9'){
                return true
            }
            return false
        },
        handleEditSingle (row) {
            this.handleEdit(row)
        },
        handleDeleteSingle (row) {
            this.handleDelete(row)
        }
    }
}
</script>