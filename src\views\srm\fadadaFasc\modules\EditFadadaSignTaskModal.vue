<template>
  <div class="FadadaSignTask business-container">
    <business-layout
        :ref="businessRefName"
        pageStatus="edit"
        modelLayout="masterSlave"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        v-on="businessHandler" />

    <field-select-modal
        isEmit
        ref="fieldSelectModal"
        @ok="fieldSelectOk" />

    <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow" />
  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import flowViewModal from '@comp/flowView/flowView'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { downFile, getAction, postAction } from '@/api/manage'
import { TOOLBAR_BUTTON_ADD, TOOLBAR_BUTTON_DELETE, TOOLBAR_BUTTON_UPLOAD, BUTTON_SAVE, BUTTON_PUBLISH, BUTTON_SUBMIT, BUTTON_BACK, GRID_OPTION_ROW, ATTACHED_DEMAND_LIST_GROUP, ATTACHMENT_DEMAND_COLUMNS, ATTACHMENT_GROUP, ATTACHMENT_COLUMNS_WITHOUT_OPRATION } from '@/utils/constant.js'
import { getGroupDefaultData, createPromise } from '@/utils/util'

export default {
    name: 'editFadadaSignTaskModal',
    mixins: [businessUtilMixin],
    components: {
      flowViewModal,
      BusinessLayout,
      fieldSelectModal
    },
    props: {
      currentEditRow: {
        required: true,
        type: Object,
        default () {
          return {}
        }
      }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
              detail: { url: '/electronsign.fadada/fadadaSignTask/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                fadadaTaskActorList: [
                  TOOLBAR_BUTTON_ADD,
                  {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                    key: 'gridDelete',
                    authorityCode: 'electronsign.fadada#fadadaSignTask:delete'
                  }
                ],
                purchaseAttachmentList: [
                  {
                    ...TOOLBAR_BUTTON_UPLOAD,
                    args: {
                      modalVisible: false, // 必传
                      businessType: 'electronsign.fadada', // 必传
                      itemNumberKey: 'materialNumber'
                    }
                  }
                  // TOOLBAR_BUTTON_DELETE,
                ]
            },
            pageHeaderButtons: [
                {
                  ...BUTTON_SAVE,
                  args: {
                    url: '/electronsign.fadada/fadadaSignTask/edit'
                  },
                  authorityCode: 'electronsign.fadada#fadadaSignTask:edit',
                  handleBefore: this.handleSaveBefore
                },
                {
                  ...BUTTON_PUBLISH,
                  args: {
                    url: '/electronsign.fadada/fadadaSignTask/publish'
                  },
                  click: this.handleCustomPublish,
                  authorityCode: 'electronsign.fadada#fadadaSignTask:publish'
                },
                {
                  ...BUTTON_SUBMIT,
                  args: {
                    url: '/a1bpmn/audit/api/submit'
                  },
                  handleBefore: this.handleBeforeSubmit,
                  click: this.handleCustomSubmit,
                  authorityCode: 'electronsign.fadada#fadadaSignTask:submit'
                },
                BUTTON_BACK
            ],
            url: {
                save: "/electronsign.fadada/fadadaSignTask/edit",
                audit: '/a1bpmn/audit/api/submit',
                detail: '/electronsign.fadada/fadadaSignTask/queryById'
            }
        }
    },
    computed: {
      remoteJsFilePath () {
        let templateNumber = this.currentEditRow.templateNumber
        let templateVersion = this.currentEditRow.templateVersion
        let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
        return `${account}/purchase_electronsign.fadada_${templateNumber}_${templateVersion}`
      }
    },
    methods: {
        preViewEvent (row) {
          let preViewFile = row
          this.$previewFile.open({ params: preViewFile })
        },
        handleBeforeRemoteConfigData (){
            return {
              groups: [
                  //{
                  //  groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                  //  groupNameI18nKey: '',
                  //  groupCode: 'purchaseAttachmentList',
                  //  groupType: 'item',
                  //  sortOrder: '10',
                  //  extend: {
                  //    optColumnList: [
                  //      { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                  //      {
                  //        key: 'preView',
                  //        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                  //        click: this.preViewEvent
                  //      },
                  //      {
                  //        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                  //        key: 'gridDelete',
                  //        click: this.deleteFiles
                  //      }
                  //    ]
                  //  }
                  //},
              ],
              itemColumns: [
                //{
                //  title: '订单号',
                //  groupCode: 'orderItemList',
                //  fieldLabelI18nKey: 'i18n_field_orderNumber',
                //  field: 'orderNumber',
                //  align: 'center',
                //  headerAlign: 'center',
                //  defaultValue: '',
                //  width: '150',
                //  dictCode: '',
                //  required: '0',
                //  disabled: true
                //}
              ]
            }
        },
        handleAfterDealSource (pageConfig, resultData){

        },
        handleSaveAfter (obj) {
          return new Promise(resolve => {
            return resolve(obj)
          })
        },
        handleSaveBefore (args) {
            //let {Vue, pageConfig, btn, allData} = args
            //let obj = allData && JSON.parse(JSON.stringify(allData))
            //const localBusRule = this.filterObj('busRule', obj.busRule)
            //const localPersonFrom = this.filterObj('personFrom', obj.personFrom)
            //obj = Object.assign({}, obj, localBusRule, localPersonFrom)
            //let params = {
            //  Vue, pageConfig, btn, allData: obj
            //}
            //delete obj.busRule
            //delete obj.personFrom
            //return new Promise((resolve) => {
            //  return resolve(params)
            //})
        },
      // 自定义发布
      handleCustomPublish (args) {
        //// 分包信息自定义校验
        //let flag = this.checkHeadItem()
        //if (!flag) {
        //  return
        //}
        //// 此处手动触发校验方法
        //this.composeBusinessPublish(args)
      },
      //审批前校验
      handleBeforeSubmit (args) {
        const { allData = {} } = args || {}
        // 格式化提交审批数据逻辑
        // 注: 需要返回一个 promise 对象
        let fn = (resolve, reject) => {
          let formatData = {
            businessId: allData.id || '',
            businessType: 'electronsign.fadada',
            auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YBthxUzAy_eee84974`, '招标单发布审批编号')}：${allData.projectNumber || ''}`,
            params: JSON.stringify(allData)
          }

          resolve({
            ...args,
            allData: formatData
          })
        }

        return createPromise(fn)
      },
      // 自定义提交审批
      handleCustomSubmit (args) {
        // 分包信息自定义校验
        //let flag = this.checkHeadItem()
        //if (!flag) {
        //  return
        //}

        // 获取页面所有数据
        //const allData = this.getAllData() || {}
        //let { supplierTaxRate = '', biddingType = '', participateQuantity = '', purchaseBiddingItemList = [], biddingSupplierList = [] } = allData
        //// 校验供应商税率
        //if (supplierTaxRate === '1') {
        //  let flag = purchaseBiddingItemList.some((n) => !n.taxCode)
        //  if (flag) {
        //    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierTaxRateTips`, '供应商税率不能为空'))
        //    return
        //  }
        //}
        //// 默认保存方法不会校验当前业务模板必填字段
        //// 此处手动触发校验方法
        //this.composeBusinessSubmit(args)
      },
        //新增行
      addBiddingItem ({ pageConfig, groupCode }) {
        this.selectType = 'material'
        const form = this.getAllData()
        const { mustMaterialNumber = '1' } = form
        if (mustMaterialNumber == '1') {
          let url = '/material/purchaseMaterialHead/list'
          let columns = [
            {
              field: 'cateCode',
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
              width: 150
            },
            {
              field: 'cateName',
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
              width: 150
            },
            { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编号'), width: 150 },
            { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200 },
            { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200 }
          ]
          this.$refs.fieldSelectModal.open(url, { blocDel: '0', freeze: '0' }, columns, 'multiple')
        } else {
          console.log('pageConfig :>> ', pageConfig)
          this.businessGridAdd({ pageConfig, groupCode })
        }
      },
      fieldSelectOk (data) {
        let pageConfig = this.$refs[this.businessRefName].pageConfig || {}
        // 获取页面所有配置默认值
        let result = getGroupDefaultData(pageConfig, true)
        let defaultObj = result['purchaseBiddingItemList'] || {}
        let itemGrid = this.getItemGridRef('purchaseBiddingItemList')
      }
    }
}
</script>
