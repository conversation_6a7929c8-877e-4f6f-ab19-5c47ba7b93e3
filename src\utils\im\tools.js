const iframeUrl = (params, path) => {
    let rs = ''
    let BASE_URL = `${window.location.origin}/kefu` + path
    if (location.hostname.includes('localhost')) {
        BASE_URL = 'http://localhost:8086' + path
    }
    if (!params.baseUrl) {
        let url = `${window.location.origin}/els/im`
        params.baseUrl = url
    }
    if (!params.wsUrl) {
        let protocol = window.location.protocol
        let wsUrl = `${protocol == 'http:' ? `ws://${window.location.host}/els/imChat` : `wss://${window.location.host}/els/imChat`}`
        params.wsUrl = wsUrl
    }
    if (params.id && params.token) {
        rs = BASE_URL + `?token=${params.token}&id=${params.id}&baseUrl=${params.baseUrl || ''}&wsUrl=${params.wsUrl || ''}&customerName=${params.customerName}&customerAvatar=${params.customerAvatar}&customerID=${params.customerID}`
    }
    return rs
}
const imUrl = () => {
    let BASE_URL = `${window.location.origin}/els/im`
    localStorage.setItem('IM_BASE_URL', BASE_URL)
    let protocol = window.location.protocol
    let wsUrl = `${protocol == 'http:' ? `ws://${window.location.host}/els/imChat` : `wss://${window.location.host}/els/imChat`}`
    if (location.hostname.includes('localhost')) {
        BASE_URL = 'http://localhost:11888/els/im'
        wsUrl = 'ws://localhost:9326/els/imChat'
    }
    // let BASE_URL = 'https://v5sit-micro.51qqt.com/els/im'
    // let wsUrl = 'wss://v5sit-micro.51qqt.com/els/imChat'
    return {
        BASE_URL,
        wsUrl
    }
}
export { iframeUrl, imUrl}