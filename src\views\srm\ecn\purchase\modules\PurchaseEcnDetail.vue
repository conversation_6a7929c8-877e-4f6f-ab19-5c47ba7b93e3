<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url"
      @loadSuccess="handleLoadSuccess" />
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal>   -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow" />
    <field-select-modal ref="fieldSelectModal" />
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'PurchaseStandardEdit',
    mixins: [DetailMixin],
    components: {
        flowViewModal,
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'order',
            showRemote: false,
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_ItRH_40bee5ab`, '订单明细'),
                        groupCode: 'orderDetails',
                        type: 'grid',
                        custom: {
                            ref: 'orderDetails',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), width: 120 },
                                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n__RdXRL_8e11f650`, '供应商名称'), width: 120 },
                                { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 120, 
                                    fieldType: 'link',
                                    extend: {
                                        linkConfig: {
                                            primaryKey: 'orderNumber',
                                            actionPath: '/srm/order/purchase/PurchaseOrderHeadList',
                                            bindKey: 'orderNumber',
                                            otherQuery: {linkFilter: true}
                                        }
                                    }
                                },
                                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                                { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lineStatus`, '行状态'), width: 120 },
                                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 120 },
                                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 120 },
                                { field: 'receiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantity`, '收货数量'), width: 120 },
                                { field: 'onWayQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_onWayQuantity`, '在途数量'), width: 120 },
                                { field: 'notDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`, '未交货数量'), width: 120 },
                                { field: 'orderCreateBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreator`, '订单创建人'), width: 120 },
                                { field: 'orderCreateTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreatTime`, '订单创建时间'), width: 120 }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_vendorPushList`, '供应商推送列表'),
                        groupCode: 'ecnSupplierListList',
                        type: 'grid',
                        custom: {
                            ref: 'ecnSupplierListList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), width: 120 },
                                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 120 },
                                { field: 'viewStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_hasItBeenRead`, '是否已读'), width: 120 }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_internalRemindeLlist`, '内部提醒列表'),
                        groupCode: 'ecnBuyerListList',
                        type: 'grid',
                        custom: {
                            ref: 'ecnBuyerListList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 120 },
                                { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), width: 120 },
                                { field: 'department', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_department`, '部门'), width: 120 },
                                { field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), width: 200 }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'attachments',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                                { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                            ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    // { title: '未完结订单', type: 'primary', click: this.viewOrderEvent},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'), type: 'primary', click: this.cancelAudit, id: 'cancelAudit', showCondition: this.showcCncelConditionBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow, id: 'showFlow', showCondition: this.showFlowConditionBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/ecn/purchaseEcn/queryById',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${account}/purchase_ecn_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, { id: this.$route.query.id }).then(res => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    mounted () {},
    methods: {
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        init () {
            if (this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id, this.getItemNumberOptions)
            }
        },
        showcCncelConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus == '1') {
                return true
            } else {
                return false
            }
        },
        showFlowConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let needApprove = params.needApprove
            if ((needApprove == '1' )) {
                return true
            } else {
                return false
            }
        },
        cancelAudit () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.auditPostData(that.url.cancelAudit)
                }
            })
        },
        auditPostData (invokeUrl) {
            const _this = this
            this.$refs.detailPage.confirmLoading = true
            let formData = this.$refs.detailPage.form
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'ecn'
            param['auditSubject'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ECNDocNo`, 'ECN单据号：') + formData.ecnNumber
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param, 'post')
                .then(res => {
                    if (res.success) {
                        this.$message.success(res.message)
                        _this.$parent.cancelCallBack(formData)
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.init()
                    this.$refs.detailPage.confirmLoading = false
                })
        },
        showFlow () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            this.flowId = params.flowId
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },

        fieldSelectOk (data) {
            if (this.selectType == 'order') {
                console.log(data)
            }
        }
        // ,
        // viewOrderEvent (){
        //     const form = this.$refs.detailPage.getPageData()
        //     if(!form.materialNumber){
        //         this.$message.warning('先选择物料编码')
        //         return
        //     }
        //     this.selectType = 'order'
        //     let url = '/order/purchaseOrderItem/queryUnfinishedItems?materialNumber='+form.materialNumber
        //     let columns = [
        //         {field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 150},
        //         {field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 100},
        //         {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150},
        //         {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDescription`, '物料描述'), width: 150},
        //         {field: 'notDeliveryQuantity', title:this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`,'未交货数量'), width: 150}
        //     ]
        //     let params = {}
        //     this.$refs.fieldSelectModal.open(url, params, columns, 'multiple')
        // }
    }
}
</script>
