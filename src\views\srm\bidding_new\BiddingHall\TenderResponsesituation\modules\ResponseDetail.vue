<template>
  <div style="height: 100%">
    <a-spin :spinning="confirmLoading">
      <setp-lay-out 
        v-if="show"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :pageHeaderButtons="pageHeaderButtons"
        :sourceGroups="sourceGroups"
        pageStatus="detail"
        :fromSourceData="fromSourceData"
      >
        <template #attachmentInfoList="{ slotProps }">
          <responseFileMsg
            ref="responseFileMsg"
            pageStatus="detail"
            :slotProps="slotProps"
            :propOfCheckType="propOfCheckType"
            :fromSourceData="fromSourceData"
          ></responseFileMsg>
        </template>
        <template #saleTenderBidLetterList="{ slotProps }">
          <responseTenderBidLetter
            ref="responseTenderBidLetter"
            pageStatus="detail"
            :slotProps="slotProps"
            :saleTenderBidLetterList="fromSourceData.saleTenderBidLetterList"
          ></responseTenderBidLetter>
        </template>
      </setp-lay-out>
    </a-spin>

  </div>
</template>

<script>
import { postAction, getAction} from '@/api/manage'
import responseFileMsg from './ResponseFileMsg'
import responseTenderBidLetter from './ResponseTenderBidLetter'
import setpLayOut from '../../components/setpLayOut'

export default {
    name: 'ResponseDetail',
    components: {
        setpLayOut,
        responseTenderBidLetter,
        responseFileMsg
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        propOfCheckType: {
            default: () => {
                return ''
            },
            type: [String, Number]
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            show: false,
            rejectForm: {
                node: '',
                reject: ''
            },
            sourceGroups: [
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QIVH_2f594f3d`, '文件信息'),
                    groupNameI18nKey: '',
                    groupCode: 'attachmentInfoList',
                    groupType: 'item',
                    show: true,
                    sortOrder: '1'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxVH_9dbaaa19`, '投标函信息'),
                    groupNameI18nKey: '',
                    groupCode: 'saleTenderBidLetterList',
                    groupType: 'head',
                    show: true,
                    sortOrder: '2'
                }
            ],
            // currentEditRow: {},
            businessRefName: 'businessRef',
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack',
                    click: this.handleBack
                }
            ],
            currentGroupCode: {},
            status: '',
            confirmLoading: false,
            url: {
                detail: '/tender/purchase/supplierTenderProjectMasterInfo/queryInfoById'
            }
        }
    },
    methods: {
        handleBack (){
            this.$emit('hide')
        },
        queryDetail () {
            console.log('this.currentEditRow.id', this.currentEditRow)
            let params = {
                // subpackageId: this.subId,
                // propOfCheckType: '1'
                id: this.currentEditRow.id
            }
            this.confirmLoading = true
            this.show = false
            getAction(this.url.detail, params, {headers: {xNodeId: `${this.propOfCheckType}_0`}})
                .then(res => {
                    if(res.success) {
                        this.fromSourceData = res.result || {}
                        this.show = true
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        
        getAllValidate (refNames) {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))

            let promiseValidateArr = refNames.map(ref => 
                this.$refs[ref][0].getValidatePromise()
            ).filter(promise => promise)

            return new Promise((resolve, reject) => {
                Promise.all(handlePromise(promiseValidateArr)).then(result => {
                    let errItem = result.filter(item => {
                        return item.status == 'error'
                    })
                    if (errItem.length > 0) {
                        let resolveData = { validStatus: false, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiKmWVGv_95d29236`, '验证失败，请处理')}
                        resolve(resolveData)
                    } else {
                        let resolveData = { validStatus: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiLR_48599c04`, '验证成功')}
                        resolve(resolveData)
                    }
                }).catch(err => {
                    reject(err)
                })
            })
        }
    },
    created () {
        this.queryDetail()
        // this.confirmLoading = true
        // let url = '/tender/purchase/supplierTenderProjectMasterInfo/queryInfoById'
        // getAction(url+'?id='+this.currentEditRow.id)
        //         .then(res => {
        //             if(res.success) {
        //                 console.log("res:::::::",res)
        //             } else {
        //                 this.$message.error(res.message)
        //             }
        //         })
        //         .finally(() => {
        //             this.confirmLoading = false
        //         })
    }
}
</script>
