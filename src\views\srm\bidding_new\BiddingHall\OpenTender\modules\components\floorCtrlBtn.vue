<template>
  <div class="clearfix floorCtrlBtn">
    <div class="fl">
      <slot name="left"></slot>
    </div>
    <div class="fr btnbox">
      <a-button
        class="ant-btn"
        v-for="(btn, index) in floorBtns"
        :key="index"
        :disabled="btn.disabled"
        :type="btn.type"
        v-show="btn.show ? btn.show() : true"
        @click="() => {btn.click ? btn.click() : ''}">{{ btn.title }}</a-button>
    </div>
  </div>
</template>
<script>
export default {
    props: {
        formData: {
            default: () => {
                return {}
            },
            type: Object
        },
        floorBtns: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    data () {
        return {
            
        }
    }
}
</script>
<style lang="less" scoped>
.fl{
    float: left;
}
.fr{
    float: right;
}
.clearfix {
    clear: both;
}
.ml-10{
    margin-left: 10px;
}
.floorCtrlBtn{
    height: 72px;
    background: #FFFFFF;
    /* 中性色/40 */
    border: 1px solid rgba(69, 79, 89, 0.4);
    padding: 16px 0px;
    margin: 0 10px;
}
.btnbox{
    margin-right: 20px;
}
.ant-btn {
        & + .ant-btn {
            margin-left: 10px;
        }
    }
</style>