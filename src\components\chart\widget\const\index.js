/*
 * @Author: fzb
 * @Date: 2022-02-25 10:34:04
 * @LastEditTime: 2022-03-02 10:20:10
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\components\designer\const\index.js
 */
// 数据服务类型
export const STATIC_SERVICE_TYPE = '0'
export const API_SERVICE_TYPE = '1'
export const SQL_SERVICE_TYPE = '2'
export const WEBSOCKET_SERVICE_TYPE = '3'
export const HTTP_STATIC_SERVICE_TYPE = '4' // 在外传入接口返回来的数据

// 图表类型key
export const CHARTS_KEY = 'charts'
export const ASSIST_KEY = 'assist'

// 图表类型(组件名称)
export const CHARTS_BAR = 'charts-bar'
export const CHARTS_PIE = 'charts-pie'
export const CHARTS_GAUGE = 'charts-gauge'
export const CHARTS_RADAR = 'charts-radar'
export const CHARTS_LINE_CHART = 'charts-line-chart'
export const CHARTS_SPLASHES = 'charts-splashes'
export const CHARTS_K_LINE = 'charts-k-line'

// 辅助图表类型(组件名称)
export const ASSIST_CUSTOM_TEXT = 'assist-custom-text'
export const ASSIST_DECORATION_ONE = 'assist-decoration-one'
export const ASSIST_DECORATION_TWO = 'assist-decoration-two'
export const ASSIST_DECORATION_THREE = 'assist-decoration-three'
export const ASSIST_DECORATION_TWO_REVERSE = 'assist-decoration-two-reverse'
export const ASSIST_DECORATION_FOUR = 'assist-decoration-four'
export const ASSIST_DECORATION_FOUR_REVERSE = 'assist-decoration-four-reverse'
export const ASSIST_DECORATION_FIVE = 'assist-decoration-five'
export const ASSIST_DECORATION_SIX = 'assist-decoration-six'
export const ASSIST_DECORATION_SEVEN = 'assist-decoration-seven'
export const ASSIST_DECORATION_EIGHT = 'assist-decoration-eight'
export const ASSIST_DECORATION_EIGHT_REVERSE = 'assist-decoration-eight-reverse'
export const ASSIST_DECORATION_NINE = 'assist-decoration-nine'
export const ASSIST_DECORATION_ZERO = 'assist-decoration-zero'
export const ASSIST_DECORATION_ELEVEN = 'assist-decoration-eleven'
export const ASSIST_DECORATION_TWELVE = 'assist-decoration-twelve'
export const ASSIST_BORDER_ONE = 'assist-border-one'
export const ASSIST_BORDER_TWO = 'assist-border-two'
export const ASSIST_BORDER_THREE = 'assist-border-three'
export const ASSIST_BORDER_FOUR = 'assist-border-four'
export const ASSIST_BORDER_FOUR_REVERSE = 'assist-border-four-reverse'
export const ASSIST_BORDER_FIVE = 'assist-border-five'
export const ASSIST_BORDER_FIVE_REVERSE = 'assist-border-five-reverse'
export const ASSIST_BORDER_SIX = 'assist-border-six'
export const ASSIST_BORDER_SEVEN = 'assist-border-seven'
export const ASSIST_BORDER_EIGHT = 'assist-border-eight'
export const ASSIST_BORDER_EIGHT_REVERSE = 'assist-border-eight-reverse'
export const ASSIST_BORDER_NINE = 'assist-border-nine'
export const ASSIST_BORDER_ZERO = 'assist-border-zero'
export const ASSIST_BORDER_ELEVEN = 'assist-border-eleven'
export const ASSIST_BORDER_TWELVE = 'assist-border-twelve'
export const ASSIST_BORDER_THIRTEEN = 'assist-border-thirteen'

// 业务类型(组件名称)
export const BUSINESS_BY_TABLE = 'business-by-table'
export const BUSINESS_LEAGUE_TABLE = 'business-league-table'
export const BUSINESS_DIGITAL_FLOP = 'business-digital-flop'