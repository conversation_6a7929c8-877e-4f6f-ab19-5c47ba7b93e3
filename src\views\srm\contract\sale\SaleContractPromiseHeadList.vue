<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <edit
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <detail
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <record-modal
      v-model="recordShowVisible"
      :currentEditRow="currentEditRow"
    />
  </div>
</template>
<script>
import SaleContractPromiseModal from './modules/SaleContractPromiseModal'
import ViewSaleContractPromiseModal from './modules/ViewSaleContractPromiseModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import RecordModal from '@comp/recordModal'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        'edit': SaleContractPromiseModal,
        'detail': ViewSaleContractPromiseModal,
        'record-modal': RecordModal
    },
    data () {
        return {
            showEditPage: false,
            recordShowVisible: false,
            pageData: {
                businessType: 'contract',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: true, clickFn: this.settingColumns}
                ],
                form: {
                    keyWord: ''
                },
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'contractPromise#saleContractPromise:view', clickFn: this.handleView},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'contractPromise#saleContractPromise:edit', clickFn: this.handleEdit, allow: this.allowEdit},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'contractPromise#saleContractPromise:delete', clickFn: this.handleDelete, allow: this.allowDelete},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/contract/saleContractPromise/list',
                add: '/contract/saleContractPromise/add',
                delete: '/contract/saleContractPromise/delete',
                exportXlsUrl: 'contract/saleContractPromise/exportXls',
                columns: 'saleContractPromiseList'
            }
        }
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.promiseNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SaleContractPromise', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if(row.promiseStatus != '0') {
                return false
            }else {
                return true
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_salesContractHeader`, '销售合同头'))
        },
        hideDetailPage (){
            this.showDetailPage = false
        },
        allowEdit (row) {
            if (row.promiseStatus == '0'||row.promiseStatus == '4'||(row.promiseStatus == '1'&&row.createAccount!= this.$ls.get(USER_ELS_ACCOUNT))) {
                return false
            } else {
                return true
            }
        },
        allowDelete (row) {
            if (row.promiseStatus == '0'&&row.createAccount== this.$ls.get(USER_ELS_ACCOUNT)) {
                return false
            } else {
                return true
            }
        }
    }
}
</script>