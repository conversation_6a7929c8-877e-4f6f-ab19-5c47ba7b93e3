import router from '../../router'
import { getAction, postAction } from '@api/manage'
import { ctxMenu } from './contextmenu'
import { init as groupSortInit } from './groupSort'
import { imUrl } from './tools'
let { BASE_URL, wsUrl } = imUrl()
import { srmI18n, getLangAccount } from '@/utils/util'

let defaultAvatar = require('../../assets/img/common/default_avatar.png')
console.log('defaultAvatar :>> ', defaultAvatar)

const im = {
    BASE_URL,
    prefix: '/apis',
    token: '',
    currentUserId: '',
    socket: null,
    currentChatId: null,
    currentVideoCallId: null, // 视频连接id
    currentVideoCallStatus: 0, // 视频连接状态
    LAYIM: null,
    lockReconnect: false,
    baseSet () {
        const urlQuery = router.app.$route.query
        // 测试环境
        this.token = localStorage.getItem('t_token') || urlQuery.token
        if (!window.layui) { // 刷新路由bug
            window.setTimeout(() => {
                this.baseSet()
            }, 500)
            return
        }
        const _this = this
        window.layui.use(['layim'], function (layim) {
            _this.LAYIM = layim
            //  基础配置
            const opts = configOption(_this)
            layim.config(opts)

            // layim 事件监听
            imEventBind(layim)

            // 其他处理方法这里
            ctxMenu(_this)

            expandEvents(layim)
        })
    },
    socketInit () {
        const url = `${wsUrl}?token=${this.token}`
        try {
            if ('WebSocket' in window) {
                this.socket = new WebSocket(url)
            }
            this.socketListener()
        } catch (e) {
            this.reconnect(url)
        }
    },
    reconnect (url) {
        if (this.lockReconnect) return
        this.lockReconnect = true
        setTimeout(() => {     //没连接上会一直重连，设置延迟避免请求过多
            this.socketInit(url)
            this.lockReconnect = false
        }, 2000)
    },
    // 获取离线消息
    getUnreadMessage () {
        const sendData = {
            code: 1, // 0 心跳检测，1 链接就绪，2 消息
            message: {
            }
        }
        this.socket.send(JSON.stringify(sendData))
    },
    socketListener () {
        let _this = this
        if (this.socket) {
            this.socket.onerror = function () {
                console.log(srmI18n(`${getLangAccount()}#i18n_field_ehKyKm_d983f030`, '通讯连接失败!'))
            }
            // 连接成功建立的回调方法
            this.socket.onopen = function () {
                heartCheck.reset().start()  //心跳检测重置
                // 发送code=1 就绪
                _this.getUnreadMessage()
                console.log(srmI18n(`${getLangAccount()}#i18n_field_ehKyLRW_57144cd6`, '通讯连接成功!'))
            }
            // 接收到消息的回调方法
            this.socket.onmessage = function (res) {
                heartCheck.reset().start() //拿到任何消息都说明当前连接是正常的
                // 结构统一后，集中处理
                _this.handleMessage(res.data)
            }
            // 连接关闭的回调方法
            this.socket.onclose = function () {
                console.log(srmI18n(`${getLangAccount()}#i18n_field_KyRlWxUBSAOvKyWiKHKVVKyW_fb4edee3`, '连接关闭，你与服务器断开连接，正在尝试重新连接！'))
            }
        }
    },
    handleMessage (data) {
        const mydata = eval('(' + data + ')')
        console.log('mydata :>> ', mydata)
        const layim = this.LAYIM
        const type = mydata['msg_type']
        if (type && layim && mydata) {
            messageProcessor[type] && messageProcessor[type]({ mydata, layim })
        }
    },
    searchBillWord: ''

}
// 配置选项
const configOption = (_this) => {
    let opt = {
        defaultAvatar,
        // 初始化
        init: {
            url: `${_this.BASE_URL}/user/init?X-Access-Token=${_this.token}`,
            data: {}
        },

        // 查看群员接口
        members: {
            url: `${_this.BASE_URL}/user/getMembers`,
            data: {
                'X-Access-Token': _this.token
            }
        },

        // 上传图片接口
        uploadImage: {
            url: `${_this.BASE_URL}/user/uploadFiles?X-Access-Token=${_this.token}`,
            type: '', // 默认post
            callback: function () { }
        },

        // 上传文件接口
        uploadFile: {
            url: `${_this.BASE_URL}/user/uploadFiles?X-Access-Token=${_this.token}`,
            type: '' // 默认post
        },
        isAudio: false, // 开启聊天工具栏音频
        isVideo: false, // 开启聊天工具栏视频
        title: ' ', // 自定义主面板最小化时的标题
        initSkin: '3.jpg', // 1-5 设置初始背景
        min: true, // 是否始终最小化主面板，默认false
        notice: true, // 是否开启桌面消息提醒，默认false

        msgbox: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/msgbox.html`, // 消息盒子页面地址
        find: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/find.html`, // 发现页面地址
        chatLog: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/chatlog.html`, // 聊天记录页面地址
        selectMember: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/selectMember.html`,
        inviteFriends: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/inviteFriends.html`,
        deleteFriends: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/deleteFriends.html`, // 删除好友
        videoChat: `${location.origin}/im/videoChat/index.html`,
        setMinWinOffset: function () {  // 设置入口模块位置
            let right = document.querySelector('.nav-right-part')?.offsetWidth || 0
            let winW = document.body.offsetWidth || 0
            let imW = 140 // im 入口宽度
            let x = '0'
            let y = winW + 5000 + 'px'
            return [x, y]
        },
        // 处理列主界面列表点击回调
        handleChatListCallback: function (elem, data) {
            if (data.type == 'group') {
                if (data.newMsgCount && data.newMsgCount > 0) {
                    // 群离线消息清除
                    removeGroupOfflineMsg(elem, data)
                }
            }
        },
        tool: [{
            alias: 'audioOnline'
            , title: srmI18n(`${getLangAccount()}#i18n_title_voiceMessage`, '语音消息')
            , icon: '&#xe688;'
        },
        {
            alias: 'audioTransform'
            , title: srmI18n(`${getLangAccount()}#i18n_field_UWnQJ_fb0857b6`, '语音转文字')
            , icon: '&#xe6dc;'
        },
        {
            alias: 'videoChat'
            , title: srmI18n(`${getLangAccount()}#i18n_field_KNeE_40ef8fce`, '视频通话')
            , icon: '&#xe6ed;'
        },
        {
            alias: 'dingVideoChat'
            , title: srmI18n(`${getLangAccount()}#i18n_field_IIKNeE_e51490ee`, '钉钉视频通话')
            , icon: '&#xe609;'
        }
        ]
    }
    return opt
}
// 消息处理 映射
const messageProcessor = {
    deleteChatGroup ({ mydata, layim }) { // 群组解散
        layim.removeList({
            type: 'group',
            id: mydata.data.id //好友或者群组ID
        })
        // 历史会话列表移除
        layim.removeHistoryList({
            type: 'group',
            id: mydata.data.id //好友或者群组ID
        })
        if (im.currentChatId == mydata.data.id) { // 刚好打开当前的弹窗
            // 关闭弹窗
            im.LAYIM.closeCurrentChat(null, 1)
        }
    },
    msg_ping () {
        // console.log('心跳')
    },
    msg_video_request ({ mydata, layim }) {
        const message = mydata.data
        const type = message.type
        const currentChatId = im.currentChatId
        im.currentVideoCallId = message.fromid
        im.currentVideoCallStatus = 1
        if (currentChatId == message.fromid && type == 'video_request') {
            openVideoCallModal(message)
        }
        if (currentChatId == message.id && type == 'group_video_request') {
            openMultipleVideoCallModal(message)
        }
    },
    msg_video_accept ({ mydata, layim }) {
        // 找到对应弹窗并打开全屏 媒体查询控制呼叫提醒
        window.setTimeout(() => {
            let body = window.layer.getChildFrame('body', eventProcessor.videoChatIndex)
            body && body.find('div.video-contact').hide()
            window.layer.full(eventProcessor.videoChatIndex)
        }, 100)
        // window.layer.close(eventProcessor.videoChatIndex)
    },
    msg_video_reject ({ mydata, layim }) {
        window.layer.msg(srmI18n(`${getLangAccount()}#i18n_field_yjFKKyW_880add01`, '好友拒绝连接！'))
        window.setTimeout(() => {
            window.layer.close(eventProcessor.videoChatIndex)
        }, 2000)
    },
    msg_message ({ mydata, layim }) {
        const message = mydata.data
        const cache = layim.cache()
        if ((message.type == 'friend' || message.type == 'group') && cache.mine.id == message.fromid) { // 当前接收信息是群组，剔除自己的信息
            return
        }
        message.id = message.type == 'friend' ? message.fromid : message.id
        layim.getMessage(message)
        let roomId = message.type == 'friend' ? message.id : Number(message.id.replace('_', ''))
        const loginInfo = {
            userId: cache.mine.username || cache.mine.id,
            roomId
        }
        // 群聊可再次进入
        layim.expandEvents.videoChatFn = (self, e) => {
            videoChatModal(loginInfo)
        }
    },
    //添加 用户
    addUser ({ mydata, layim }) {
        let cache = layim.cache()
        if (!cache.srmParams.frendsRequest) {
            cache.srmParams.frendsRequest = []
        }
        let userData = mydata.data || []
        userData.forEach(n => {
            let flag = cache.srmParams.frendsRequest.some(sub => sub.id === n.id)
            if (!flag) {
                // 加入到消息缓存中
                cache.srmParams.frendsRequest.push(mydata.data)
            }
        })
        // 弹消息盒子
        layim.msgbox(1)
    },
    //同意 用户
    agree ({ mydata, layim }) {
        // 弹消息盒子
        let obj = {
            type: 'friend',
            avatar: mydata.data.avatar || '', //好友头像
            username: mydata.data.username, //好友昵称
            groupid: mydata.data.status, //所在的分组id
            id: mydata.data.id, //好友ID
            sign: '' //好友签名
        }
        layim.addList(obj)
    },
    //删除 用户
    delUser ({ mydata, layim }) {
        layim.removeList({
            type: 'friend',
            id: mydata.data.id //好友或者群组ID
        })
    },
    //离线消息
    unread ({ mydata, layim }) {
        if (mydata.data && mydata.data.length == 0) { return }
        if (mydata?.type == 'group') { // 后续处理
            console.log(mydata.data)
        } else { // 原来单聊离线数据
            mydata.data.forEach(rs => {
                window.setTimeout(function () { layim.getMessage(rs) }, 1000)
            })
        }
    },
    // 添加 分组信息
    addGroup ({ mydata, layim }) {
        layim.addList(mydata.data)
    },
    //todo
    addChatGroup ({ mydata, layim }) {
        const message = mydata.data
        let obj = {
            type: 'group',
            avatar: message.avatar || '/im/layim-v3.9.6/dist/css/modules/layim/skin/group.jpg',
            groupname: message.groupname,
            id: message.id
        }
        layim.addList(obj)
    },
    removeChatGroup ({ mydata, layim }) {
        // 列表移除
        const message = mydata.data
        layim.removeList({
            type: 'group',
            id: message.id //好友或者群组ID
        })
        // 历史会话列表移除
        layim.removeHistoryList({
            type: 'group',
            id: message.id //好友或者群组ID
        })
    },
    // 删除组
    delGroup ({ mydata, layim }) {
        layim.removeList({
            type: 'group',
            id: mydata.data.id //好友或者群组ID
        })
    },
    // 检测聊天数据
    chatMessage ({ mydata, layim }) {
        layim.getMessage(mydata.data)
    },
    // 用户登录 更新用户列表
    online ({ mydata, layim }) {
        layim.setFriendStatus(mydata.id, 'online')
    },
    // 用户退出 更新用户列表
    logout ({ mydata, layim }) {
        layim.setFriendStatus(mydata.id, 'offline')
    },
    // 消息撤回失败
    'undo_message_reply_reject' ({ mydata, layim }) {
        window.layer.msg(mydata.data.content)
    },
    // 消息撤回成功
    'undo_message_reply_accept' ({ mydata, layim }) {
        layim.undoMessageCallback(mydata)
    },
    // 获取websocket 消息 cid
    'send_message_id_return' ({ mydata, layim }) {
        layim.setMessageIdReturn(mydata)
    }
}

// layim 事件监听器
const eventProcessor = {
    // 监听layim建立就绪 init直接赋值mine、friend的情况下（只有设置了url才会执行 ready 事件）
    ready (res) {
        im.currentUserId = res.mine.id
        sessionStorage.setItem('currentUserId', im.currentUserId)
        // 组移动
        groupSortInit(im.LAYIM)
        // websocket 初始化
        im.socketInit()
        // 打开群离线列表
        // openGroupOfflineList(res.group)
    },
    // 监听发送消息
    sendMessage (data) {
        const mine = data.mine
        const to = data.to
        const sendData = {
            code: 2, // 0 心跳检测，1 链接就绪，2 消息
            message: {
                avatar: mine.avatar,
                id: im.currentChatId,
                unSaveMessageId: mine.cid,
                fromid: mine.id,
                type: to.type,
                username: mine.username,
                content: mine.content
            }
        }
        console.log(JSON.stringify(sendData))
        im.socket.send(JSON.stringify(sendData))
    },
    // 消息撤回
    unDoMsg (data) {
        const mine = data.mine
        // const to = data.to
        const sendData = {
            code: 101, // 101 撤回消息连接
            message: {
                avatar: mine.avatar,
                // id: im.currentChatId,
                undoMessageId: mine.cid,
                fromid: mine.id,
                // type: to.type,
                type: 'undo_message_request',
                username: mine.username,
                content: mine.content
            }
        }
        console.log(JSON.stringify(sendData))
        im.socket.send(JSON.stringify(sendData))
    },
    'fliter-list' (data) {
        // 过滤群组成员
        // const cache = im.LAYIM.cache()
        // const localAccount = cache.mine.elsAccount
        // var localData = data.filter(item => {
        //     return item.elsAccount == localAccount
        // })
        // if (!localData[0].isMaster) {
        //     // 供应商保留自己和群主
        //     for (var i = 0; i < data.length; i++) {
        //         if (data[i].elsAccount !== localAccount && !data[i].isMaster) {
        //             data.splice(i, 1)
        //             i--
        //         }
        //     }
        // }
    },
    'jump-detail' () {
        getAction(`/im/chatGroupLink/configLink/${im.currentChatId || ''}`, {}).then(res => {
            if (res.code == 200 && res.result) {
                let extendLink = JSON.parse(res.result.linkConfig)
                if (extendLink.handleBefore && typeof extendLink.handleBefore === 'function') {
                    let callbackObj = extendLink.handleBefore(row, column, linkConfig, this) || {}
                    extendLink = { ...extendLink, ...callbackObj }
                }
                let query = {
                    actionPath: res.result.actionPath,
                    searchNumber: res.result.recordNumber,
                    t: new Date().getTime(),
                    ...extendLink,
                    ...extendLink.linkConfig.otherQuery
                }
                router.push({ path: res.result.actionPath.trim(), query: { ...query } })
            }
        }).catch((e) => {
            window.layer.msg(e)
        })
    },

    // 监听聊天窗口的切换
    chatChange (res) {
        const cache = im.LAYIM.cache()
        let $ = window.layui.$
        const message = res.data
        im.currentChatId = message.id
        let roomId = message.type == 'friend' ? message.id : Number(message.id.replace('_', ''))
        const loginInfo = {
            userId: cache.mine.username || cache.mine.id,
            roomId
        }
        im.LAYIM.expandEvents.videoChatFn = (self, e) => {
            videoChatModal(loginInfo)
        }
        if (message.type == 'group') {
            // 多人视频
            openMultipleVideoCallModal(message)
            if (message.newMsgCount && message.newMsgCount > 0) {
                // 群离线消息清除
                removeGroupOfflineMsg(null, message)
            }
        } else {
            // 更新公司名
            let str = `<span>${message.enterpriseName || ''}</span>`
            im.LAYIM.setChatStatus(str)
            openVideoCallModal(message)
        }
    },
    'tool(audioOnline)' (insert, send, obj) {
        console.log(obj)
        var layerIndex = window.layui.layer.open({
            type: 2,
            shade: 0.1,
            shadeClose: true,
            title: srmI18n(`${getLangAccount()}#i18n_title_voiceMessage`, '语音消息'),
            btnAlign: 'c',
            btn: [srmI18n(`${getLangAccount()}#i18n_field_CtlE_3482e64b`, '点击说话'), srmI18n(`${getLangAccount()}#i18n_field_MLGhd_3810a8c2`, '完成并发送')],
            yes: function (index, layero) {
                const iframeWin = window[layero.find('iframe')[0]['name']]
                iframeWin && iframeWin.recStart()
            }
            , btn2: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']]
                iframeWin && iframeWin.recStop(function (data) {
                    if (data.error && data.error == 1) {
                        window.layer.msg(data.text)
                    } else {
                        // insert(im.LAYIM.thisChat.textarea[0], 'audio' + '[' + src + ']')
                        // insert('audio' + '[' +'https://v5sit-micro.51qqt.com/' +data.src + ']')
                        insert('audio' + '[' + data.src + ']')
                        send() //自动发送
                        window.layer.close(layerIndex)
                    }
                })
                return false
            },
            success: function (layero, index) {
                layero.find('.layui-layer-btn0').css({
                    'pointer-events': 'none',
                    opacity: 0.3
                })
                var iframeWin = window[layero.find('iframe')[0]['name']]
                iframeWin && iframeWin.recOpen(function () {
                    layero.find('.layui-layer-btn0').css({
                        'pointer-events': 'auto',
                        opacity: 1
                    })
                })
            },
            area: ['350px', '230px'],
            closeBtn: 1,
            resize: false,
            content: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/record.html` //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
        })
    },
    'tool(audioTransform)' (insert, send, obj) {
        console.log(obj)
        var layerIndex = window.layui.layer.open({
            type: 2,
            shade: 0.1,
            shadeClose: true,
            title: srmI18n(`${getLangAccount()}#i18n_field_UWnQJ_fb0857b6`, '语音转文字'),
            btn: [srmI18n(`${getLangAccount()}#i18n_field_CtHW_347d8b20`, '点击录音'), srmI18n(`${getLangAccount()}#i18n_field_ML_b7804`, '完成')],
            btnAlign: 'c',
            yes: function (index, layero) {
                const iframeWin = window[layero.find('iframe')[0]['name']]
                iframeWin && iframeWin.recStart()
            }
            , btn2: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']]
                iframeWin && iframeWin.recStop(function (data) {
                    if (data?.error && data.error == 1) {
                        window.layer.msg(data.text)
                    } else {
                        const { result } = data
                        if (result.optType && result.optType == 'error') {
                            window.layer.msg(result.optObject)
                        } else {
                            let txt = result.Result || ''
                            insert(txt) //将内容插入到编辑器
                            window.layer.close(layerIndex)
                        }
                    }
                })
                return false
            },
            success: function (layero, index) {
                layero.find('.layui-layer-btn0').css({
                    'pointer-events': 'none',
                    opacity: 0.3
                })
                var iframeWin = window[layero.find('iframe')[0]['name']]
                iframeWin && iframeWin.recOpen(function () {
                    layero.find('.layui-layer-btn0').css({
                        'pointer-events': 'auto',
                        opacity: 1
                    })
                })
            },
            area: ['350px', '230px'],
            closeBtn: 1,
            resize: false,
            content: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/voiceMorphing.html` //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
        })
    },
    'tool(videoChat)' (insert, send, toInfo) {
        console.log(toInfo)
        let type = toInfo.data.type
        let roomId = toInfo.data.id && toInfo.data.id.substring(0, 5) || ''
        const layim = im.LAYIM
        const cache = layim.cache()
        const mine = cache.mine
        const loginInfo = {
            userId: mine.username || mine.id,
            roomId: type == 'friend' ? roomId : Number(toInfo.data.id.replace('_', ''))
            // roomId
        }
        videoChatModal(loginInfo, insert, send, toInfo)
    },
    async 'tool(dingVideoChat)' (insert, send, toInfo) {
        try {
            const layim = im.LAYIM
            const cache = layim && layim.cache()
            let params = {}
            const chatData = toInfo.data
            const api = 'base/dingtalk/creatMeeting'
            if (chatData.type == 'group') {
                const members = await getMembers(chatData.id)
                const list = members.data.list || []
                const inviteUserIdList = list.map(rs => rs.id)
                params = {
                    confTitle: chatData.groupname || chatData.name,
                    createUserId: cache.mine.id,
                    inviteUserIdList
                }
            } else {
                params = {
                    confTitle: chatData.name,
                    createUserId: cache.mine.id,
                    inviteUserIdList: [chatData.id]
                }
            }
            postAction(api, params).then(rs => {
                if (rs.code == 200) {
                    window.open(rs.result.externalLinkUrl)
                    insert(rs.result.meetingMsg)
                    send()
                } else {

                    window.layer.msg(srmI18n(`${getLangAccount()}i18n_alert_MIcILeyLKWWWHeyIltyW_369d981e`, '会议创建人账号未在SRM系统绑定手机号码！'))
                    //window.layer.msg(rs.message)
                }
            })
        } catch (e) {
            window.layer.msg(e)
        }
    }
}
const imEventBind = (layim) => {
    Object.keys(eventProcessor).forEach(evt => {
        console.log(evt)
        layim.on(evt, eventProcessor[evt])
    })
}
const expandEvents = (layim) => {
    let cache = layim.cache()
    layim.expandEvents = {
        ungroup () {
            var titleBtnI18n = srmI18n(getLangAccount() + '#i18n_title_tip', '提示')
            var confirmBtnI18n = srmI18n(getLangAccount() + '#i18n_title_confirm', '确认')
            var cancelBtnI18n = srmI18n(getLangAccount() + '#i18n_title_cancle', '取消')
            var isSureI18n = srmI18n(getLangAccount() + '#i18n_title_isSure', '是否确认')
            var delGroupI18n = srmI18n(getLangAccount() + '#i18n_alert_yIaV_403a9380', '解散群组')
            var tip = [isSureI18n, delGroupI18n].join('')

            var callback = function () {
                let api = `${BASE_URL}/userGroup/deleteChatGroup/${im.currentChatId}`
                postAction(api, {}).then(res => {
                    if (res.success) {
                        // 列表移除
                        im.LAYIM.removeList({
                            type: 'group',
                            id: im.currentChatId //好友或者群组ID
                        })
                        // 历史会话列表移除
                        im.LAYIM.removeHistoryList({
                            type: 'group',
                            id: im.currentChatId //好友或者群组ID
                        })
                        // 关闭弹窗
                        im.LAYIM.closeCurrentChat(null, 1)
                        window.layer.msg('群解散成功！')
                    } else {
                        window.layer.msg(res.message)
                    }
                }).catch((e) => {
                    window.layer.msg(e)
                })
            }

            window.layer.confirm(tip, {
                title: titleBtnI18n,
                btn: [confirmBtnI18n, cancelBtnI18n] //按钮
            }, function () {
                callback && callback()
            }, function () { })
        },
        inviteFriends (othis, e) { //邀请好友
            let config = {
                url: `${BASE_URL}/user/loadUserList/${im.currentChatId}`,
                params: {}
            }
            loadUserList(config, function (res) {
                if (res.result.imUserList && res.result.imUserList.length > 0 && res.result.groupChat) {
                    cache.srmParams.inviteFriends = {
                        id: im.currentChatId,
                        data: res.result
                    }
                    // 打开弹窗
                    cache.srmParams.inviteFriends.index = window.layer.open({
                        type: 2,
                        title: srmI18n(`${getLangAccount()}#i18n_field_PVyjuN_7294312a`, '邀请好友加入'),
                        shade: false,
                        maxmin: true,
                        area: ['600px', '526px'],
                        skin: 'layui-box layui-layer-border layui-selectMember',
                        resize: false,
                        content: cache.base.inviteFriends
                    })
                } else {
                    window.layer.msg('没有可邀请的好友了')
                }
            })
        },
        deleteFriends (othis, e) { //删除好友
            let config = {
                url: `${BASE_URL}/user/getMembers`,
                params: {
                    id: im.currentChatId
                }
            }
            loadUserList(config, function (res) {
                // 剔除自己
                let data = res.data.list && res.data.list.filter(rs => rs.id != im.currentUserId)
                cache.srmParams.deleteFriends = {
                    id: im.currentChatId,
                    data
                }
                if (data.length > 0) {
                    cache.srmParams.deleteFriends.index = window.layer.open({
                        type: 2,
                        title: srmI18n(`${getLangAccount()}#i18n_field_QGyj_2794b132`, '删除好友'),
                        shade: false,
                        maxmin: true,
                        area: ['600px', '526px'],
                        skin: 'layui-box layui-layer-border layui-selectMember',
                        resize: false,
                        content: cache.base.deleteFriends
                    })
                } else {
                    window.layer.msg('没有可删除的好友了')
                }
            })
        }
    }
}
//心跳检测
const heartCheck = {
    timeout: 30000,        //30s发一次心跳
    timeoutObj: null,
    serverTimeoutObj: null,
    reset: function () {
        clearTimeout(this.timeoutObj)

        clearTimeout(this.serverTimeoutObj)
        return this
    },
    start: function () {
        var self = this
        this.timeoutObj = setTimeout(function () {
            //这里发送一个心跳，后端收到后，返回一个心跳消息，
            //onmessage拿到返回的心跳就说明连接正常
            const sendData = {
                code: 0, // 0 心跳检测，1 链接就绪，2 消息
                message: {
                }
            }
            im.socket.send(JSON.stringify(sendData))
            self.serverTimeoutObj = setTimeout(function () {//如果超过一定时间还没重置，说明后端主动断开了
                im.socke.close()  //如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
            }, self.timeout)
        }, this.timeout)
    }
}
const loadUserList = (config, cb) => {
    getAction(config.url, config.params).then(res => {
        if (res.code == 200 || res.code == '0') {
            cb && cb(res)
        }
        else if(res.code == 500){
            window.layer.msg(res.message)
        }
    }).catch((e) => {
        window.layer.msg(e)
    })
}
// 单聊
const createChat = (obj) => {
    let api = BASE_URL + '/user/getRecordPerson'
    if (!obj.type) {
        alert(srmI18n(`${getLangAccount()}#i18n_field_VWNCQWWWW_453e1139`, '请输入模块type'))
        return false
    }
    if (!obj.id) { // 订单id不存在
        alert(srmI18n(`${getLangAccount()}#i18n_field_WWxMK_7257ea2`, 'id不存在'))
        return
    }
    let params = {
        type: obj.type,
        id: obj.id
    }
    getAction(api, params).then(res => {
        if (res.success && !res.result.groupChat && res.result.imUserList.length > 0) {
            let data = res.result.imUserList[0]
            let layim = im.LAYIM
            layim.chat({
                name: data.username,
                type: 'friend', //群组类型
                avatar: data.avatar || '/im/layim-v3.9.6/dist/css/modules/layim/skin/default.png',
                id: data.id
            })
        } else {
            window.layer.msg(res.message)
        }
    }).catch((e) => {
        window.layer.msg(e)
    })
}

const selectMember = async (obj) => {
    let layim = im.LAYIM
    let cache = layim.cache()
    cache.srmParams.selectMemberObj = obj
    if (!cache.base.selectMember) {
        return window.layer.msg(srmI18n(`${getLangAccount()}#i18n_field_SuLjIrBjcI_cab85c92`, '添加成员模板没有创建'))
    }
    const members = await getRecordPerson(obj)
    if(!cache.base.directCreate){
        // 如果类型不是群组则返回单聊
        if (members?.result?.groupChat === false) {
            createChat(obj)
            return false
        }
        if (members.result && members.result.imUserList.length == 1) { // 一个好友则自动关联上，发起群聊
            const membersList = members.result.imUserList
            createChatGroup(obj, cache, membersList)
            return false
        }
        if (members.result && members.result.imUserList.length == 0) {
            window.layer.msg(srmI18n(`${getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据'))
            return false
        }
    }
    window.layer.close(im.index)
    return (cache.srmParams.winIndex = im.index = window.layer.open({
        type: 2,
        title: srmI18n(`${getLangAccount()}#i18n_field_cILS_26c71e9e`, '创建聊天'),
        shade: false,
        maxmin: true,
        area: ['600px', '564px'],
        skin: 'layui-box layui-layer-border layui-selectMember',
        resize: false,
        content: cache.base.selectMember,
        end: function () {
            // 创建聊天窗口关闭的监听
            cache.base.directCreate = false
        }
    })
    )
}
const createChatGroup = (info, cache, members) => {
    const membersId = members.map(rs => rs.id).join(',')
    const url = '/im/user/createChatGroupById'
    const groupId = `${info.id}_${cache.mine.id}`
    let param = {
        id: groupId, // 列表订单id拼装
        // token:token,
        name: '', // 名称
        members: membersId, // 群组成员
        remark: info.url.list || '', // 记录订单详情路径
        recordNumber: info.recordNumber,
        recordType: info.type
    }
    const layim = im.LAYIM
    postAction(url, param).then((rs) => {
        if (rs.code == 200) {
            const groupname = rs.message || ''
            let info = {
                type: 'group'
                , avatar: '/im/layim-v3.9.6/dist/css/modules/layim/skin/group.jpg'
                , groupname
                , id: groupId
            }
            layim.addList(info)
            layim.chat({
                name: groupname
                , type: 'group' //群组类型
                , brief: '1' //群组类型
                , avatar: '/im/layim-v3.9.6/dist/css/modules/layim/skin/group.jpg'
                , id: groupId //定义唯一的id方便你处理信息
                , members: members.length || 1   //成员数，不好获取的话，可以设置为0
            })
        }
    })
}
const getRecordPerson = (info) => {
    let url = ''
    let param ={}
    const directCreate = im.LAYIM.cache().base.directCreate || false
    if(directCreate){
        // 手动创建群聊
        url = `${BASE_URL}/user/listAllFriends`
    }else{
        url = `${BASE_URL}/user/getRecordPerson`
        param = {
            type: info.type,
            id: info.id
        }
    }
    return getAction(url, param)
}
//todo
// /im/layim-v3.9.6/dist/css/modules/layim/skin/group.jpg
const creatGruopChat = (obj) => {
    let layim = im.LAYIM
    let cache = layim && layim.cache()
    if (!obj.type && !cache.base.directCreate) {
        alert(srmI18n(`${getLangAccount()}#i18n_field_VWNCQWWWW_453e1139`, '请输入模块type'))
        return false
    }
    if (!obj.id && !cache.base.directCreate) { // 订单id不存在
        alert(srmI18n(`${getLangAccount()}#i18n_field_WWxMK_7257ea2`, 'id不存在'))
        return
    }
    console.log('[creatGruopChat]')
    console.log(im.LAYIM)
    let groupId = obj.id + '_' + cache.mine.id
    let curChat = cache.group.find(rs => rs.id == groupId)
    im.searchBillWord = obj.recordNumber
    // 存窗口id
    if (curChat) { // 群聊已经存在
        // 打开群聊
        layim.chat({
            name: curChat.groupname,
            type: 'group', //群组类型
            brief: '1', //群组类型
            avatar: curChat.avatar || 'http://tp2.sinaimg.cn/**********/50/**********/1',
            id: curChat.id, //定义唯一的id方便你处理信息
            members: 0  //成员数，不好获取的话，可以设置为0
        })
        return
    }
    selectMember(obj)
}
const openGruopChat = (chatInfo) => {
    const layim = im.LAYIM
    console.log('[openGruopChat]')
    // 打开群聊
    layim.chat({
        name: chatInfo.groupname,
        type: 'group', //群组类型
        brief: '1', //群组类型
        avatar: chatInfo.avatar || 'http://tp2.sinaimg.cn/**********/50/**********/1',
        id: chatInfo.id, //定义唯一的id方便你处理信息
        members: 0
    })
}
const videoChatModal = (loginInfo, insert, send, toInfo) => {
    let layim = im.LAYIM
    let cache = layim.cache()
    let thatChat = layim.thisChat()
    let type = toInfo && toInfo.data.type
    if (eventProcessor.videoChatIndex) {
        window.layer.close(eventProcessor.videoChatIndex)
    }
    eventProcessor.videoChatIndex = window.layui.layer.open({
        type: 2,
        maxmin: true,
        title: srmI18n(`${getLangAccount()}#i18n_field_KNeE_40ef8fce`, '视频通话'),
        area: ['450px', '520px'],
        shade: false,
        offset: 'r',
        skin: 'layui-box',
        anim: 2,
        id: 'layui-layim-chatlog',
        content: `${cache.base.videoChat}?type=${type == 'friend' ? 'single' : 'multiple'}`,
        success: function (layero, index) {
            if (insert && send && toInfo) {
                const mine = cache.mine
                const sendCallData = {
                    code: 3, // 0 心跳检测，1 链接就绪，2 消息 3 视频连接
                    message: {
                        avatar: mine.avatar,
                        id: type == 'friend' ? im.currentChatId : toInfo.data.id,
                        fromid: mine.id,
                        type: type == 'friend' ? 'video_request' : 'group_video_request',
                        username: mine.username,
                        content: ''
                    }
                }
                insert(`[${srmI18n(`${getLangAccount()}#i18n_field_hAKNeE_114ef094`, '发起视频通话')}]`)
                send()
                im.socket.send(JSON.stringify(sendCallData))
            }
            var iframeWin = window[layero.find('iframe')[0]['name']]
            iframeWin && iframeWin.login(loginInfo)
            // 群聊暂取消提示
            if (type && type == 'group') {
                let body = window.layer.getChildFrame('body', index)
                body && body.find('div.video-contact').hide()
                // 群聊可再次进入
                layim.expandEvents.videoChatFn = (self, e) => {
                    videoChatModal(loginInfo)
                }
                insert(`[div class=layui-btn layim-event=videoChatFn]${srmI18n(`${getLangAccount()}#i18n_field_HNCI_42a2a5ff`, '进入房间')}[/div]`)
                send()

                // 全屏
                window.setTimeout(() => {
                    let body = window.layer.getChildFrame('body', index)
                    body && body.find('div.video-contact').hide()
                    // window.layer.full(index)
                }, 100)
            }
        }
    })
}
const openVideoCallModal = (message) => {
    if (im.currentChatId != im.currentVideoCallId || im.currentVideoCallStatus == 0) { // 打开过
        return true
    }
    let layim = im.LAYIM
    const cache = layim.cache()
    let videoIndex = messageProcessor.videoOnlineIndex
    if (videoIndex) {
        window.layer.close(videoIndex)
    }
    videoIndex = window.layui.layer.open({
        type: 1,
        maxmin: false,
        title: srmI18n(`${getLangAccount()}#i18n_field_KNLS_40ed72ea`, '视频聊天'),
        shade: false,
        offset: 'auto',
        area: '300px',
        skin: 'layui-box',
        btn: [srmI18n(`${getLangAccount()}#i18n_title_accept`, '接受'), srmI18n(`${getLangAccount()}#i18n_field_FK_c764b`, '拒绝')],
        anim: 2,
        id: 'layui-layim-chatlog2',
        content: `<div style='padding:10px'>[${message.username}]${srmI18n(`${getLangAccount()}#i18n_field_dnLKNKyWW_e85b5021`, '想和您视频连接..')}</div>`,
        success: function () {
            // 默认只打开一次
            im.currentVideoCallStatus = 0
        },
        yes: function (index, layero) {
            let roomId = message.id && message.id.substring(0, 5)
            const mine = cache.mine
            const loginInfo = {
                userId: mine.username || mine.id,
                roomId
            }
            videoChatModal(loginInfo)
            // 发送socket
            const sendCallData = {
                code: 3, // 0 心跳检测，1 链接就绪，2 消息 3 视频连接
                message: {
                    avatar: mine.avatar,
                    id: im.currentChatId,
                    fromid: mine.id,
                    type: 'video_accept',
                    username: mine.username,
                    content: ''
                }
            }
            im.socket.send(JSON.stringify(sendCallData))
            window.layer.close(index)
            // 全屏当前窗口
            window.setTimeout(() => {
                let body = window.layer.getChildFrame('body', eventProcessor.videoChatIndex)
                body && body.find('div.video-contact').hide()
                window.layer.full(eventProcessor.videoChatIndex)
            }, 100)
        },
        btn2: function (index, layero) {
            const mine = cache.mine
            const sendCallData = {
                code: 3, // 0 心跳检测，1 链接就绪，2 消息 3 视频连接
                message: {
                    avatar: mine.avatar,
                    id: im.currentChatId,
                    fromid: mine.id,
                    type: 'video_reject',
                    username: mine.username,
                    content: ''
                }
            }
            im.socket.send(JSON.stringify(sendCallData))
        }
    })
}
const openMultipleVideoCallModal = (message) => {
    let layim = im.LAYIM
    const cache = layim.cache()
    const mine = cache.mine
    if (im.currentVideoCallId == mine.id || im.currentVideoCallStatus == 0) { // 打开过
        return true
    }
    let videoIndex = messageProcessor.videoOnlineIndex
    if (videoIndex) {
        window.layer.close(videoIndex)
    }
    videoIndex = window.layui.layer.open({
        type: 1,
        maxmin: false,
        title: srmI18n(`${getLangAccount()}#i18n_field_KNLS_40ed72ea`, '视频聊天'),
        shade: false,
        offset: 'auto',
        area: '300px',
        skin: 'layui-box',
        btn: [srmI18n(`${getLangAccount()}#i18n_title_accept`, '接受'), srmI18n(`${getLangAccount()}#i18n_field_FK_c764b`, '拒绝')],
        anim: 2,
        id: 'layui-layim-chatlog2',
        content: `<div style='padding:10px'>${message.groupname ? `群[${message.groupname}]` : message.username}]${srmI18n(`${getLangAccount()}#i18n_field_PxIAKNeEWW_f0e41325`, '邀你一起视频通话..')}</div>`,
        success: function () {
            // 默认只打开一次
            im.currentVideoCallStatus = 0
        },
        yes: function (index, layero) {
            let roomId = Number(message.id.replace('_', ''))
            const loginInfo = {
                userId: mine.username || mine.id,
                roomId
            }
            videoChatModal(loginInfo)
            window.layer.close(index)
            // 全屏当前窗口
            window.setTimeout(() => {
                let body = window.layer.getChildFrame('body', eventProcessor.videoChatIndex)
                body && body.find('div.video-contact').hide()
                window.layer.full(eventProcessor.videoChatIndex)
            }, 100)
        },
        btn2: function (index, layero) {
        }
    })
}
const removeGroupOfflineMsg = (elem, message) => {
    let url = `${BASE_URL}/user/removeGroupChatMessage/${message.id}`
    postAction(url, {}).then(res => {
        if (res.code == 200 || res.code == '0') {
            if (elem) {
                elem.find('.layui-badge').remove()
            }
        }
    }).catch((e) => {
    })
}
const openGroupOfflineList = (group) => {
    let newMsg = group.find(rs => rs.newMsgCount > 0)
    if (newMsg) { // 有群组新消息就打开
        let main = im.LAYIM.getLayimMain()
        let tab = im.LAYIM.mainTabDeaultOpen
        main.show()
        // 打开群分组
        tab(1)
    }
}
const getMembers = (groupId) => {
    const api = 'im/user/getMembers'
    let params = {
        id: groupId
    }
    return getAction(api, params)
}
const init = () => {
    im.baseSet()
}
export default { init, createChat, selectMember, im, creatGruopChat, openGruopChat }
