<template>
  <div class="control-list">
    <a-spin
      tip="Loading..."
      :spinning="false">
      <div
        class="group-item"
        v-for="(group, index) in controlList"
        :key="index.toString()">
        <h3>{{ group.title }}</h3>
        <draggable
          :list="group.list"
          class="controls-list"
          :group="{ name: 'form', pull: 'clone', put: false }"
          :animation="0"
          :clone="cloneCB"
          :move="moveCB"
          :sort="false"
          :options="{forceFallback: true}"
        >
          <control
            v-for="(element, ind) in group.list"
            :key="ind.toString()"
            :data="element"
          />
        </draggable>
      </div>
    </a-spin>

  </div>
</template>

<script>
import draggable from 'vuedraggable'
import control from './Control'
import { getControlList } from '../../utils/config'
import { formatViewData } from '../../utils/core'
import { mapActions } from 'vuex'

export default {
    name: 'ControlList',
    components: {
        draggable,
        control
    },
    data () {
        return {
            controlList: getControlList()
        }
    },
    methods: {
        ...mapActions([ 'questionDataInit' ]),
        // 数据初始化
        init () {
            // this.questionDataInit().then(rs => {

            // }).catch((err) => {
            
            // })
        },
        cloneCB (e) {
            return formatViewData(e)
        },
        moveCB (e, o) {
            console.log('moveCb', e, o)
            if (e.draggedContext.element.type === 'grid' && e.to.className.indexOf('grid-control-wrap') >= 0) {
                return false
            }
            if (e.to.className.indexOf('grid-control-wrap') >= 0 && e.to.children.length > 0) { // 禁止向gird组件添加多列
                return false
            }
        }
    }
}
</script>

<style lang="less" scoped>
.control-list{
  .loading{
    padding: 50px;
  }
  h3{
    font-size: 14px;
  }
  >div:not(:first-child){
    margin-top: 10px;
  }
}
  .ghost {
    background: #aefbe6;
    padding: 0;
    height: 3px;
    width: 100%;
    border: 0;
    overflow: hidden;
    margin: 0;
  }
</style>
