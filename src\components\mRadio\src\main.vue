<script lang="jsx">
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { ajaxFindDictItems } from '@/api/api'
export default {
    name: 'Radio',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        options: {
            type: Array,
            default () {
                return []
            }
        },
        value: {
            type: String,
            default: ''
        },
        dictCode: {
            type: String,
            default: ''
        },
        isLayout: {
            type: Boolean,
            default: false
        },
        colSpan: {
            type: [String, Number],
            default: 6
        }
    },
    data () {
        let realOptions = this.options.map(n => {
            let label =  this.$srmI18n(`${this.$getLangAccount()}#${n.textI18nKey}`, n.title)
            return { label, value: n.value }
        })
        return {
            selectValue: [],
            realOptions: realOptions
        }
    },
    watch: {
        value: {
            immediate: true,
            handler (val) {
                this.selectValue = val || ''
            }
        },
        dictCode: {
            immediate: true,
            handler () {
                this.initDictData()
            }
        }
    },
    methods: {
        initDictData () {
            if (this.dictCode) {
                //根据字典Code, 初始化字典数组
                let postData = {
                    busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: this.dictCode
                }             
                ajaxFindDictItems(postData).then((res) => {
                    const result = res.result || []
                    this.realOptions = result.map(n => {
                        let label =  this.$srmI18n(`${this.$getLangAccount()}#${n.textI18nKey}`, n.title)
                        return { label, value: n.value }
                    })
                })
            }
        },
        handleChange (e) {
            this.$emit('change', e.target.value)
        }
    },
    render () {
        const radioGroup = this.isLayout
            ? (
                <a-radio-group
                    vModel={ this.selectValue }
                    onChange={ this.handleChange }
                >
                    <a-row>
                        {
                            this.realOptions.map(n => {
                                return (
                                    <a-col span={ this.colSpan }>
                                        <div class="boxWrap">
                                            <a-radio value={ n.value }>
                                                { n.label }
                                            </a-radio>
                                        </div>
                                    </a-col>
                                )
                            })
                        }
                    </a-row>
                </a-radio-group>
            )
            : (
                <a-radio-group
                    vModel={ this.selectValue }
                    options={ this.realOptions }
                    onChange={ this.handleChange }
                />
            )
        return (
            this.realOptions && this.realOptions.length ? radioGroup : null
        )
    }
}
</script>

<style lang="less" scoped>
.boxWrap {
    display: flex;
    justify-content: flex-start;
    
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
