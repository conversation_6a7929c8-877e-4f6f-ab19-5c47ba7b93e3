<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction } from '@/api/manage'
// import REGEXP from '@/utils/regexp'
export default {
    name: 'SubaccountCertificationAdd',
    mixins: [EditMixin],
    data () {
        return {
            selectType: 'esignEnterpriseCertification',
            pageData: {
                form: {
                    loadingCompany: '',
                    companyCode: '',
                    companyName: '',
                    subAccount: '',
                    accountId: '',
                    idNumber: '',
                    idType: '',
                    orgLegalIdNumber: '',
                    orgLegalName: '',
                    authType: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_64dEMQeAfsqvfsQ4`, '企业认证信息维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_loadingCompany`, '是否加载公司列表'),
                                    fieldName: 'loadingCompany',
                                    dictCode: 'yn',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '0')
                                            setDisabledByProp('companyCode', flag)
                                            setDisabledByProp('companyName', !flag)
                                        }else{
                                            setDisabledByProp('companyCode', true)
                                            setDisabledByProp('companyName', true)
                                        }
                                    },
                                    required: '1'

                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码'),
                                    fieldName: 'companyCode',
                                    dictCode: 'purchase_organization_info,org_name,org_code,org_category_code="companyCode"',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称'),
                                    fieldName: 'companyName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_userAccount`, '用户账号'),
                                    fieldName: 'subAccount',
                                    bindFunction: function (Vue, data){    
                                        Vue.form.subAccount = data[0].subAccount,    
                                        Vue.form.name = data[0].realname,    
                                        Vue.form.accountId = data[0].accountId
                                    }, extend: { 
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150}, 
                                            {field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_xat0lPoJ`, 'e签宝账户'), with: 150}, 
                                            {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150}
                                        ], modalUrl: '/esign/elsSubaccountCertificationInfo/list', modalParams: {orgCreateFlag: '1'}
                                    },
                                    required: '1'
                                },
                                {
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_fekrL2LC7bftWONd`, '用户e签宝id'),
                                    fieldType: 'input',
                                    fieldName: 'accountId',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_61w5j37BPo3g0ZZ5`, '机构证件类型'),
                                    fieldName: 'idType',
                                    dictCode: 'srmCompanyEsignIdType',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    fieldName: 'idNumber',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalIdNumber`, '法定代表人证件号'),
                                    fieldName: 'orgLegalIdNumber'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalName`, '法定代表人名称'),
                                    fieldName: 'orgLegalName'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型'),
                                    fieldName: 'authType',
                                    dictCode: 'srmCompanyCertificationType'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_XQzTTQLipsGMe3Mt`, 'e签宝个人机构账户'),
                                    fieldName: 'orgId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cbN3tX16RHkSMJqd`, '是否认证完成'),
                                    fieldName: 'certificationStatus',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_shortLink`, '实名认证短链接'),
                                    fieldName: 'shortLink',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_longLink`, '实名认证长链接'),
                                    disabled: true,
                                    fieldName: 'longLink'
                                }
                            ],
                            validateRules: {
                                loadingCompany: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BFQiKPrmHbGTqCS8`, '是否加载公司列表不能为空')}],
                                subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Rj3r30Q2x80Ltran`, '用户账号不能为空')}],
                                accountId: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_5BfpnlBpz9oPzzAx`, '用户账号对应的e签宝账号不能为空')}],
                                idType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_f6XyqCu3syIh31zT`, '证件类型不能为空')}],
                                idNumber: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_wfOnIW5LtvkPwjKg`, '证件号不能为空')}]
                            }
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), type: 'primary', click: this.submitCertificationEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/esign/elsEnterpriseCertificationInfo/add',
                auth: '/esign/elsEnterpriseCertificationInfo/submitCertification'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
        },
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    if(params.loadingCompany==='0' && !params.companyName){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCRLxOLV_3f6f291f`, '公司名称不能为空'))
                        return
                    }
                    if(params.loadingCompany==='1' && !params.companyCode){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCooxOLV_5449119a`, '公司代码不能为空'))
                        return
                    }
                    let url = this.url.add
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        this.goBack()
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        submitCertificationEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    if(params.loadingCompany==='0' && !params.companyName){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCRLxOLV_3f6f291f`, '公司名称不能为空'))
                        return
                    }
                    if(params.loadingCompany==='1' && !params.companyCode){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCooxOLV_5449119a`, '公司代码不能为空'))
                        return
                    }
                    let url = this.url.auth
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        this.goBack()
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>