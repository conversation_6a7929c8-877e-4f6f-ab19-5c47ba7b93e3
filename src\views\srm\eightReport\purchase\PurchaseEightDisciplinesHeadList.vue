<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <purchase-eight-disciplines-head-edit
      v-if="showEditPage"
      ref="eightDisciplines"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <purchase-eight-disciplines-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction } from '@/api/manage'
import { ListMixin } from '@comp/template/list/ListMixin'
import purchaseEightDisciplinesHeadEdit from './modules/PurchaseEightDisciplinesHeadEdit'
import purchaseEightDisciplinesHeadDetail from './modules/PurchaseEightDisciplinesHeadDetail'
import layIM from '@/utils/im/layIM.js'
import { postAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        purchaseEightDisciplinesHeadEdit,
        purchaseEightDisciplinesHeadDetail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'eightDisciplines',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNWWty_874ca137`, '请输入8D单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                        fieldName: 'eightDisciplinesStatus',
                        dictCode: 'srm8DStatus'
                    }
                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'eightDisciplines#purchaseEightDisciplines:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
                        icon: 'download',
                        folded: false,
                        clickFn: this.handleExportXls,
                        authorityCode: 'eightDisciplines#purchaseEightDisciplines:export'
                    },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF }
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'eightDisciplines#purchaseEightDisciplines:query' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEditSingle, allow: this.allowEdit, authorityCode: 'eightDisciplines#purchaseEightDisciplines:edit' },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDeleteSingle, allow: this.allowDel, authorityCode: 'eightDisciplines#purchaseEightDisciplines:delete' },
                    { type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopy, authorityCode: 'eightDisciplines#purchaseEightDisciplines:copy' },
                    { type: 'close', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_Rl_a72da`, '关闭'), clickFn: this.handleCloseSingle, allow: this.allowClose, authorityCode: 'eightDisciplines#purchaseEightDisciplines:close' },
                    { type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat, authorityCode: 'eightDisciplines#purchaseEightDisciplines:chat' },
                    { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord, authorityCode: 'eightDisciplines#purchaseEightDisciplines:record' }
                ],
                optColumnWidth: 270
            },
            url: {
                list: '/eightReport/purchaseEightDisciplines/list',
                add: '/eightReport/purchaseEightDisciplines/add',
                delete: '/eightReport/purchaseEightDisciplines/delete',
                close: '/eightReport/purchaseEightDisciplines/close',
                deleteBatch: '/eightReport/purchaseEightDisciplines/deleteBatch',
                importExcelUrl: '/eightReport/purchaseEightDisciplines/importExcel',
                columns: 'PurchaseEightDisciplinesHead',
                exportXlsUrl: '/eightReport/purchaseEightDisciplines/exportXls',
                copy: '/eightReport/purchaseEightDisciplines/copy'
            }
        }
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.eightDisciplinesNumber || id
            // 创建
            layIM.creatGruopChat({ id, type: 'PurchaseEightDisciplinesHead', url: this.url || '', recordNumber })
        },
        allowChat (row) {
            if (row.eightDisciplinesStatus != 'D0' && row.toElsAccount) {
                return false
            } else {
                return true
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WWrH_2745ce`, '8D改进'))
        },
        cancelAuditCallBack (row) {
            this.currentEditRow = row
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        allowDel (row) {
            return row.eightDisciplinesStatus != 'D0'
        },
        allowEdit (row) {
            return row.eightDisciplinesStatus == 'D9' || row.auditStatus == '1' || row.auditStatus == '2' || row.eightDisciplinesStatus == 'D10'
        },
        allowClose (row) {
            let showStatus = ['D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7', 'D8']
            if (showStatus.indexOf(row.eightDisciplinesStatus) == -1) {
                return true
            }
            return false
        },
        handleEditSingle (row) {
            this.handleEdit(row)
        },
        handleDeleteSingle (row) {
            this.handleDelete(row)
        },
        handleCloseSingle (row) {
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmClose`, '确认关闭'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_closeSelecteData`, '是否关闭选中数据?'),
                onOk: function () {
                    that.loading = true
                    getAction(that.url.close, { id: row.id })
                        .then((res) => {
                            if (res.success) {
                                that.$message.success(res.message)
                                that.$refs.listPage.loadData() // 刷新页面
                            } else {
                                that.$message.warning(res.message)
                            }
                        })
                        .finally(() => {
                            that.loading = false
                        })
                }
            })
        },
        handleCopy (row) {
            let that = this
            const url = this.url.copy + '/' + row.id
            postAction(url, null).then((res) => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BRBmBrWFLRW_e4c909f0`, '复制数据成功！'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        }
    }
}
</script>
