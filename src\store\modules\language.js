/*
 * @Author: fzb
 * @Date: 2021-07-15 11:47:32
 * @LastEditTime: 2021-07-22 18:27:51
 * @LastEditors: Please set LastEditors
 * @FilePath: \srm-frontend_v4.5\src\store\modules\language.js
 */
import Vue from 'vue'
import { getSysListLang, getCompanyListLang, getI18nByBusAccountLang } from '@api/api'
import { srmI18n, getLangAccount } from '@/utils/util'
import i18n from '@/i18n'
import {
    DEFAULT_LANG,
    REFRESH_LANG_DATA
} from '@/store/mutation-types'
const switchLanguage = {
    state: {
        defaultLang: null,
        currentLangData: {}
    },
    mutations: {
        TOGGLE_LANG: (state, lang) => {
            // lang: zh_CN
            Vue.ls.set(DEFAULT_LANG, lang)
            state.defaultLang = lang
        },
        REFRESH_LANG_DATA: (state, payload)=> {
            let { data } = payload
            i18n.setLocaleMessage(i18n.locale, Object.assign({}, i18n.messages[i18n.locale], data))
            state.currentLangData = i18n.messages[i18n.locale]
            document.title = srmI18n(`${getLangAccount()}#i18n_dict_AAeDKTyKCp_15e1ccb4`, '广州酒家供应商协同平台')
        }
    },
    actions: {
        modifySysListLangData: ({commit}) => {
            // let lang = Vue.ls.get(DEFAULT_LANG) === 'en' ? 'en' : 'zh'
            let lang = Vue.ls.get(DEFAULT_LANG) || 'zh'
            return getSysListLang(lang).then((res)=>{
                if (res) {
                    commit(REFRESH_LANG_DATA, { data: res })
                }
            })
        },
        modifyCompanyListLangData: ({commit}) => {
            // let lang = Vue.ls.get(DEFAULT_LANG)==='en'? 'en':'zh'
            let lang = Vue.ls.get(DEFAULT_LANG) || 'zh'
            return getCompanyListLang(lang).then((res)=>{
                if (res) {
                    commit(REFRESH_LANG_DATA, { data: res })
                }
            })
        },
        modifyBusAccountLangData: ({commit}, payload) => {
            let { busAccount } = payload
            // let lang = Vue.ls.get(DEFAULT_LANG) === 'en' ? 'en' : 'zh'
            let lang = Vue.ls.get(DEFAULT_LANG) || 'zh'
            return getI18nByBusAccountLang(lang, busAccount).then((res)=>{
                if (res) {
                    commit(REFRESH_LANG_DATA, { data: res })
                }
            })
        }
    }
}

export default switchLanguage