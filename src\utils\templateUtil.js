

const TemplateUtil = {
    // 是否是设备部模板 - 供应商准入
    isDeviceTemplate_supplierAccessMgmt: function (currentEditRow) {
        if (!currentEditRow || !currentEditRow.templateNumber || !currentEditRow.templateName) {
            return false
        }
        if (currentEditRow.templateNumber == 'TC2024103101' && currentEditRow.templateName == '供应商准入(设备部)') {
            return true
        }
        return false
    },

    // 是否设备部模板 - 询价
    isDeviceTemplate_enquiry: function (form) {
        if (!form) {
            return false
        }
        if (form.templateNumber == 'TC2024080201' && form.templateName == '询价单(设备部)') {
            return true
        }
        return false
    }
}

export {
    TemplateUtil
}