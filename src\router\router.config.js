import HallRouter from './hall'
import ProjectHallRouter from './projectHall'
import { BiddingHallRouter, TenderHallRouter } from './biddingHall'
import ApplyRouter from './hall/apply'
import supplierPictureRouter from './supplierPicture.js'
import helpCenterRouter from './helpCenter.js'
import Mall from './mall'
const { MallRouter, poolMallRouter } = Mall
import { getLangAccount, srmI18n } from '@/utils/util'

import { UserLayout, TabLayout } from '@/components/layouts'
// import {RouteView, PageView} from '@/components/layouts'
/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export const asyncRouterMap = [

    {
        path: '/',
        name: 'dashboard-srm',
        component: TabLayout,
        meta: { title: '首页' },
        redirect: '/dashboard/workplace'
    },
    {
        path: '*', redirect: '/404', hidden: true
    }
]

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
    {
        path: '/user',
        component: UserLayout,
        redirect: '/user/login',
        hidden: true,
        children: [
            {
                path: 'login',
                name: 'login',
                component: () => import(/* webpackChunkName: "userLogin" */ '@/views/user/LoginNew')
            },
            {
                path: 'supplierRegister',
                name: 'supplierRegister',
                component: () => import(/* webpackChunkName: "userSupplierRegister" */ '@/views/srm/supplier/SupplierRegister')
            },
            {
                path: 'register',
                name: 'register',
                component: () => import(/* webpackChunkName: "userRegister" */ '@/views/user/Register')
            },
            {
                path: 'register-result',
                name: 'registerResult',
                component: () => import(/* webpackChunkName: "userRegisterResult" */ '@/views/user/RegisterResult')
            },
            {
                path: 'alteration',
                name: 'alteration',
                component: () => import(/* webpackChunkName: "userAlteration" */ '@/views/user/Alteration')
            },
            {
                path: 'noticeList',
                name: 'noticeList',
                component: () => import(/* webpackChunkName: "userNoticeList" */ '@/views/user/noticeList')
            },
            {
                path: 'noticeDetail',
                name: 'noticeDetail',
                component: () => import(/* webpackChunkName: "userNoticeDetail" */ '@/views/user/noticeDetail')
            },
            {
                path: 'service',
                name: 'service',
                component: () => import(/* webpackChunkName: "userService" */ '@/views/user/service')
            },
            {
                path: 'suggestion',
                name: 'suggestion',
                component: () => import(/* webpackChunkName: "userSuggestion" */ '@/views/user/suggestion')
            },
            {
                path: 'noticeDetailTemplate',
                name: 'noticeDetailTemplate',
                component: () => import('@/views/user/NoticeDetailTemplate')
            },
            {
                path: 'qclogin',
                name: 'qclogin',
                component: () => import('@/views/user/QcCodeLogin')
            },
            {
                path: 'QcCodeBinding',
                name: 'QcCodeBinding',
                component: () => import('@/views/user/QcCodeBinding')
            },
            {
                path: 'addedApplicationRecharge',
                name: 'addedApplicationRecharge',
                component: () => import('@/views/user/addApplicationSeviceRecharge')
            },
            {
                path: 'biddingInformationDetail',
                name: 'biddingInformationDetail',
                component: () => import('@/views/srm/bidding_new/BiddingInformation/BiddingInformationDetail')
            }

        ]
    },
    // 首页平台公告
    {
        path: '/user/homePageNotice',
        name: 'homePageNotice',
        component: () => import(/* webpackChunkName: "homePageNotice" */ '@/views/dashboard/modules/homePageNotice')
    },
    // OA跳转登录
    {
      path: '/user/oaLogin',
      name: 'oaLogin',
      component: () => import('@/views/user/OaLogin')
    },
    // 忘记密码
    {
        path: '/user/forgetPassword',
        name: 'forgetPassword',
        component: () => import('@/views/user/forgetPassword')
    },
    // 调查问卷展示页面
    {
        path: '/questionnaire/observe',
        name: 'observe',
        meta: {
            keepAlive: false,
            title: srmI18n(`${getLangAccount()}#i18n_btn_QUDm_4572294b`, '问卷调查', true)
        },
        component: () => import('@/views/questionnaire/view')
    },
    // 调查问卷展示页面
    {
        path: '/questionnaire/result',
        name: 'questionnaireResult',
        meta: {
            keepAlive: false,
            title: srmI18n(`${getLangAccount()}#i18n_btn_QUDm_4572294b`, '问卷调查', true)
        },
        component: () => import('@/views/questionnaire/result')
    },
    //测试路径2

    {
        path: '/enquiry/purchaseHall',
        name: 'purchaseHall',
        meta: {
            keepAlive: false,
            title: '询价大厅',
            titleI18nKey: ''
        },
        component: () => import('@/views/srm/enquiry/purchase/modules/extend/PurchaseEnquiryHall')
    },
    {
        path: '/enquiry/purchaseHall2',
        name: 'purchaseHall2',
        meta: {
            keepAlive: false,
            title: '询价大厅',
            titleI18nKey: ''
        },
        component: () => import('@/views/srm/enquiry/purchase/modules/PurchaseEnquiryHall')
    },
    // {
    //     path: '/enquiry/purchaseHall3',
    //     name: 'purchaseHall3',
    //     meta: {
    //         keepAlive: false,
    //         title: '询价大厅',
    //         titleI18nKey: ''
    //     },
    //     component: () => import('@/views/srm/enquiry/purchase/modules/extend/PurchaseEnquiryHall')
    // },
    {
        path: '/ebidding/buyLobbyNew',
        
        meta: {
            keepAlive: false,
            title: '竞价大厅',
            titleI18nKey: ''
        },
        component: () => import('@/views/srm/ebidding/modules/BuyBidLobbyNew') 
    },
    
    {
        path: '/ebidding/buyLobbyNewJap',
        
        meta: {
            keepAlive: false,
            title: '竞价大厅',
            titleI18nKey: ''
        },
        component: () => import('@/views/srm/ebidding/modules/BuyBidLobbyNewJap') 
    },
    // 采购荷式竞价大厅
    {
        path: '/ebidding/buyLobbyNewDutch',
      
        meta: {
            keepAlive: false,
            title: '竞价大厅',
            titleI18nKey: ''
        },
        component: () => import('@/views/srm/ebidding/modules/BuyBidLobbyNewDutch') 
    },
        
    {
        path: '/ebidding/saleLobbyNewJap',
        
        meta: {
            keepAlive: false,
            title: '竞价大厅',
            titleI18nKey: ''
        },
        component: () => import('@/views/srm/ebidding/sale/modules/SaleBidLobbyNewJap') 
    },
    // 销售-荷式竞价大厅
    {
        path: '/ebidding/saleLobbyNewDutch',
      
        meta: {
            keepAlive: false,
            title: '竞价大厅',
            titleI18nKey: ''
        },
        component: () => import('@/views/srm/ebidding/sale/modules/SaleBidLobbyNewDutch') 
    },
            
    {
        path: '/ebidding/saleLobbyNew',
        
        meta: {
            keepAlive: false,
            title: '竞价大厅',
            titleI18nKey: ''
        },
        component: () => import('@/views/srm/ebidding/sale/modules/SaleBidLobbyNew') 
    },
    {
        path: '/oauth2/authorize',
        name: 'authorize',
        component: () => import('@/views/user/thirdPartyLogin')
    },
    {
        path: '/personalSettingsIndex',
        component: TabLayout,
        hidden: true,
        children: [
            {
                path: '/personalSettingsIndex',
                name: 'personalSettingsIndex',
                meta: {
                    keepAlive: false,
                    title: '个人设置',
                    titleI18nKey: 'i18n_title_personalSettings'
                },
                component: () => import('@/views/sys/personalSettings/personalSettingsIndex')
            }
        ]
    },
    {
        path: '/registrationGuidePage',
        name: 'registrationGuidePage',
        component: () => import('@/views/sys/registrationGuide/registrationGuidePage')
    },
    {
        path: '/calendar',
        component: TabLayout,
        children: [
            {
                path: 'set',
                name: 'calendarSet',
                meta: {
                    keepAlive: false,
                    title: '设置日历',
                    titleI18nKey: 'i18n_btn_GRBv_4170f9b1'
                },
                component: () => import('@/views/calendar/set')
            },
            {
                path: 'overview',
                name: 'overview',
                meta: {
                    keepAlive: false,
                    title: '年总览图',
                    titleI18nKey: 'i18n_title_annualOverview'
                },
                component: () => import('@/views/calendar/overview')
            }
        ]
    },
    {
        path: '/questionnaire',
        component: TabLayout,
        children: [
            {
                path: 'designer',
                name: 'designer',
                meta: {
                    keepAlive: false,
                    title: '问卷设计',
                    titleI18nKey: 'i18n_title_questionnaireDesign'
                },
                component: () => import('@/views/questionnaire/index')
            }
        ]
    },
    {
        path: '/thirdPartyEmail',
        name: 'thirdPartyEmail',
        component: () => import('@/views/user/thirdPartyEmail')
    },
    {
        path: '/pageNotice',
        name: 'pageNotice',
        component: TabLayout,
        children: [
            {
                path: 'list',
                name: 'pageNoticeList',
                meta: {
                    keepAlive: false,
                    title: '公告列表',
                    titleI18nKey: 'i18n_field_RxAB_264a0a0f'
                },
                component: () => import(/* webpackChunkName: "pageNoticeList" */ '@/views/pageNotice/List')
            },
            {
                path: 'detail',
                name: 'pageNoticeDetail',
                meta: {
                    keepAlive: false,
                    title: '公告详情',
                    titleI18nKey: 'i18n_field_RxdV_2650e27d'
                },
                component: () => import(/* webpackChunkName: "pageNoticeDetail" */ '@/views/pageNotice/Detail')
            }
        ]
    },
    {
        path: '/404',
        component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
    },
    {
        path: '/print',
        name: 'print',
        component: TabLayout,
        children: [
            {
                path: 'preview',
                name: 'PrintPreview',
                meta: {
                    url: '/els/ureport/preview?_t=1,9&_i=1',
                    title: '预览打印',
                    titleI18nKey: 'i18n_field_UBfW_4765dc41'
                },
                component: () => import('@/components/layouts/IframePageView')
            }
        ]
    },
    {
        path: '/enquiry',
        name: 'enquiry',
        component: TabLayout,
        children: [
            /*{
                path: 'purchaseHall',
                name: 'purchaseHall',
                meta: {
                    keepAlive: false,
                    title: '询价大厅',
                    titleI18nKey: 'i18n_title_inquiryHall'
                },
                component: () => import('@/views/srm/enquiry/purchase/modules/extend/PurchaseEnquiryHall')
            },*/
            {
                path: 'purchaseEnquiryHisList',
                name: 'purchaseEnquiryHisList',
                meta: {
                    keepAlive: false,
                    title: '询价报价历史',
                    titleI18nKey: 'i18n_field_husuvK_3a11ca73'
                },
                component: () => import('@/views/srm/enquiry/purchase/PurchaseEnquiryHisList')
            }
        ]
    },
    {
        path: '/ebidding',
        name: 'ebidding',
        component: TabLayout,
        children: [
            {
                path: 'purchaseEbiddingHisList',
                name: 'PurchaseEbiddingHisList',
                meta: {
                    keepAlive: false,
                    title: '竞价报价历史',
                    titleI18nKey: 'i18n_field_OusuvK_30dbd8f7'
                },
                component: () => import('@/views/srm/ebidding/PurchaseEbiddingHisList')
            },
            {
                path: 'buyLobbyNew',
                name: 'BuyBidLobbyNew',
                meta: {
                    keepAlive: false,
                    title: '竞价大厅', // 采购 - 英式
                    titleI18nKey: 'i18n_btn_OufY_390dbcd7'
                },
                component: () => import('@/views/srm/ebidding/modules/BuyBidLobbyNew')
            },
            {
                path: 'buyLobbyNewJap',
                name: 'BuyBidLobbyNewJap',
                meta: {
                    keepAlive: false,
                    title: '竞价大厅', // 采购 - 日式
                    titleI18nKey: 'i18n_btn_OufY_390dbcd7'
                },
                component: () => import('@/views/srm/ebidding/modules/BuyBidLobbyNewJap')
            },
            {
                path: 'saleLobbyNew',
                name: 'SaleBidLobbyNew',
                meta: {
                    keepAlive: false,
                    title: '竞价大厅', // 供应商 - 英式
                    titleI18nKey: 'i18n_btn_OufY_390dbcd7'
                },
                component: () => import('@/views/srm/ebidding/sale/modules/SaleBidLobbyNew')
            },
            {
                path: 'saleLobbyNewJap',
                name: 'SaleBidLobbyNewJap',
                meta: {
                    keepAlive: false,
                    title: '竞价大厅', // 供应商 - 日式
                    titleI18nKey: 'i18n_btn_OufY_390dbcd7'
                },
                component: () => import('@/views/srm/ebidding/sale/modules/SaleBidLobbyNewJap')
            },
            {
                path: 'saleLobbyBatch',
                name: 'SaleBidLobbyBatch',
                meta: {
                    keepAlive: false,
                    title: '竞价大厅', // 批量 - 竞价大厅
                    titleI18nKey: 'i18n_btn_OufY_390dbcd7'
                },
                component: () => import('@/views/srm/ebidding/sale/modules/SaleBidLobbyBatch')
            }
        ]
    },

    { // 测试路径
        path: '/bidder',
        name: 'bidder',
        component: TabLayout,
        children: [
            // {
            //     path: 'SignUpManagerList',
            //     name: 'SignUpManagerList',
            //     meta: {
            //         keepAlive: false,
            //         title: '报名管理',
            //         titleI18nKey: ''
            //     },
            //     component: () => import('@/views/srm/bidding_new/SaleSignUpManager/SignUpManagerList')
            // },
            // {
            //     path: 'PurchaseBidManagerList',
            //     name: 'PurchaseBidManagerList',
            //     meta: {
            //         keepAlive: false,
            //         title: '购标管理',
            //         titleI18nKey: ''
            //     },
            //     component: () => import('@/views/srm/bidding_new/SalePurchaseBidManager/PurchaseBidManagerList')
            // },
            // // 测试路径配置 src\views\srm\bidding_new\BiddingManager\BiddingManagerList.vue
            // {
            //     path: 'BiddingManagerList',
            //     name: 'BiddingManagerList',
            //     meta: {
            //         keepAlive: false,
            //         title: '投标管理',
            //         titleI18nKey: ''
            //     },
            //     component: () => import('@/views/srm/bidding_new/BiddingManager/BiddingManagerList')
            // },
            // {
            //     path: 'BidEvaluationManagerList',
            //     name: 'BidEvaluationManagerList',
            //     meta: {
            //         keepAlive: false,
            //         title: '评标管理',
            //         titleI18nKey: ''
            //     },
            //     component: () => import('@/views/srm/bidding_new/BidEvaluationManager/BidEvaluationManagerList')
            // },
            {
                //评标澄清
                path: '/BidEvaluationClarify/BidEvaluationClarifyList',
                name: 'BidEvaluationClarifyList',
                meta: {
                    keepAlive: false,
                    title: '评标澄清',
                    titleI18nKey: 'i18n_field_UBLV_411d1f04'
                },
                component: () => import('@/views/srm/bidding_new/BidEvaluationClarify/BidEvaluationClarifyList')
            },
            // {
            //     path: 'TemplateLibraryList',
            //     name: 'TemplateLibraryList',
            //     meta: {
            //         keepAlive: false,
            //         title: '招投标模板库',
            //         titleI18nKey: ''
            //     },
            //     component: () => import('@/views/srm/bidding_new/TemplateLibrary/TemplateLibraryList')
            // },
            {
                path: 'NewPurchaseTenderProject',
                name: 'NewPurchaseTenderProject',
                meta: {
                    keepAlive: false,
                    title: '招标项目（按分包）',
                    titleI18nKey: ''
                },
                component: () => import('@/views/srm/bidding_new/PurchaseTenderProject_new/PurchaseTenderProjectList')
            },
            // 项目执行情况列表页
            {
                path: '/ProjectSituation/ProjectSituationList',
                name: 'TemplateLibraryList',
                meta: {
                    keepAlive: false,
                    title: '项目执行情况',
                    titleI18nKey: 'i18n_field_dIREVc_e9cefa2a'
                },
                component: () => import('@/views/srm/bidding_new/ProjectSituation/ProjectSituationList')
            }
        ]
    },
    
    {
        path: '/TemplateLibrary/TemplateLibraryList',
        name: 'TemplateLibraryList',
        meta: {
            keepAlive: false,
            title: '招投标模板库',
            titleI18nKey: ''
        },
        component: () => import('@/views/srm/bidding_new/TemplateLibrary/TemplateLibraryList')
    },
    {// 项目公告详情
        path: '/BiddingInformationDetail',
        name: 'BiddingInformationDetail',
        component: () => import('@/views/srm/bidding_new/BiddingInformation/BiddingInformationDetail')
    },
    {// 中标公示
        path: '/BiddingCandidateDetail',
        name: 'BiddingCandidateDetail',
        component: () => import('@/views/srm/bidding_new/BiddingInformation/BiddingCandidateDetail')
    },
    {// 中标公告
        path: '/BiddingWinBulletinDetail',
        name: 'BiddingWinBulletinDetail',
        component: () => import('@/views/srm/bidding_new/BiddingInformation/BiddingWinBulletinDetail')
    },
    {
        path: '/vmi',
        name: 'vmi',
        component: TabLayout,
        children: [
            {
                path: 'VMIWaterLineHistoryList',
                name: 'VMIWaterLineHistoryList',
                meta: {
                    keepAlive: false,
                    title: 'vmi历史水位线',
                    titleI18nKey: 'i18n_field_WWWvKfLW_dbc6fa48'
                },
                component: () => import('@/views/srm/vmi/VMIWaterLineHistoryList')
            }
        ]
    },
    {
        path: '/demand',
        name: 'demand',
        component: TabLayout,
        children: [
            {
                path: 'ElsBusinessTransferHisList',
                name: 'ElsBusinessTransferHisList',
                meta: {
                    keepAlive: false,
                    title: '操作历史',
                    titleI18nKey: 'i18n_field_tkvK_2f06a59b'
                },
                component: () => import('@views/srm/demand/ElsBusinessTransferHisList')
            }
        ]
    },
    {
        path: '/srm/base/',
        component: TabLayout,
        children: [
            {
                path: 'SupplierVenture',
                name: 'SupplierVenture',
                meta: {
                    keepAlive: false,
                    title: '供应商风险',
                    titleI18nKey: 'i18n_title_suppliersRisk'
                },
                component: () => import('@/views/srm/base/venture/SupplierVenture')
            }
        ]
    },
    {
        path: '/sys/third/',
        component: TabLayout,
        children: [
            {
                path: 'ConnectorConfigFieldList',
                name: 'ConnectorConfigFieldList',
                meta: {
                    keepAlive: false,
                    title: '平台级连接器请求参数管理',
                    titleI18nKey: ''
                },
                component: () => import('@/views/sys/third/ConnectorConfigFieldList')
            }
        ]
    },
    {
        path: '/sys/third/',
        component: TabLayout,
        children: [
            {
                path: 'ConnectorConnectorConfigFieldList',
                name: 'ConnectorConnectorConfigFieldList',
                meta: {
                    keepAlive: false,
                    title: '企业级连接器请求参数管理',
                    titleI18nKey: ''
                },
                component: () => import('@/views/sys/third/ConnectorConnectorConfigFieldList')
            }
        ]
    },
    {
        path: '/print/ebiddingPrint',
        name: 'EbiddingPrint',
        meta: {
            keepAlive: false,
            title: '竞价打印'
        },
        component: () => import('@/views/srm/ebidding/modules/EbiddingPrintPage')
    },
    {
        path: '/print/enquiryPrint',
        name: 'EnquiryPrint',
        meta: {
            keepAlive: false,
            title: '询价打印'
        },
        component: () => import('@/views/srm/enquiry/purchase/modules/EnquiryPrintPage')
    },
    
    HallRouter,
    ProjectHallRouter,
    BiddingHallRouter,
    TenderHallRouter,
    ApplyRouter,
    supplierPictureRouter,
    helpCenterRouter,
    MallRouter,
    poolMallRouter
]
