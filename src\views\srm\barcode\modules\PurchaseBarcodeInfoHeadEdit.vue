<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        refresh
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
      <field-select-modal
        :isEmit="true"
        @ok="checkSupplierSelectOk"
        ref="SupplierFieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction, postAction } from '@/api/manage'

export default {
    name: 'PurchaseBarcodeInfoHeadEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            refresh: true,
            fieldSelectType: '',
            requestData: {
                detail: {
                    url: '/base/barcode/purchaseBarcodeInfoHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                purchaseBarcodeInfoItemList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.itemGridAddPopup
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete',
                    click: this.businessGridDelete
                }],
                sysBarcodeList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bL_e90d1`, '生成'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.generateBarcode
                }],
                customBarcodeList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.customBarcode
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete'
                }],
                barcodeSupplierListList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.supplierGridAddPopup
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete'
                }]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/base/barcode/purchaseBarcodeInfoHead/edit'
                    },
                    key: 'save',
                    showMessage: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                    args: {
                        url: '/base/barcode/purchaseBarcodeInfoHead/publish'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish',
                    handleBefore: this.handlePublishBefore,
                    handleAfter: this.handlePublishAfter,
                    show: this.handleShowFn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                    args: {
                        url: '/a1bpmn/audit/api/submit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'submit',
                    handleBefore: this.handleSubmitBefore,
                    show: this.handleShowFn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/purchaseBarcodeInfoHead/edit',
                generateBarcode: '/base/barcode/purchaseBarcodeInfoHead/generateBarcode'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_barcodeInfo_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_LFToRH_cc18358d`, '规则条码明细'),
                        groupNameI18nKey: '',
                        groupCode: 'sysBarcodeList',
                        groupType: 'item',
                        sortOrder: '5'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_JIIToRH_2e146811`, '自定义条码明细'),
                        groupNameI18nKey: '',
                        groupCode: 'customBarcodeList',
                        groupType: 'item',
                        sortOrder: '10'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_hxIdRH_5f96d352`, '发布对象明细'),
                        groupNameI18nKey: '',
                        groupCode: 'barcodeSupplierListList',
                        groupType: 'item',
                        sortOrder: '15'
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_To_cfcc0`, '条码'),
                        fieldLabelI18nKey: '',
                        field: 'barcode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_jVH_143f48d`, '原信息'),
                        fieldLabelI18nKey: '',
                        field: 'originalBarcode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormd2b_printNumber`, '允许打印份数'),
                        fieldLabelI18nKey: '',
                        field: 'printNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'customBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_To_cfcc0`, '条码'),
                        fieldLabelI18nKey: '',
                        field: 'barcode',
                        fieldType: 'input',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'customBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormd2b_printNumber`, '允许打印份数'),
                        fieldLabelI18nKey: '',
                        field: 'printNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'barcodeSupplierListList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                        fieldLabelI18nKey: '',
                        field: 'toElsAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'barcodeSupplierListList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
                        fieldLabelI18nKey: '',
                        field: 'supplierCode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'barcodeSupplierListList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                        fieldLabelI18nKey: '',
                        field: 'supplierName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            resultData['roleType'] = 'purchase'
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        // 处理发布回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!args.allData.purchaseBarcodeInfoItemList || !args.allData.purchaseBarcodeInfoItemList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyName`, '条码属性内容不能为空！'))
                    reject(args)
                } if (!args.allData.sysBarcodeList || !args.allData.sysBarcodeList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LFToRHxOLVW_ec14b784`, '规则条码明细不能为空！'))
                    reject(args)
                }else {
                    resolve(args)
                }
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        // 提交审批之前处理数据
        handleSubmitBefore (args) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            if(allData.publishAudit != '1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noNeedSubmitApproval`, '无需提交审批！'))
                return
            }
            return new Promise((resolve) => {
                let pageData = args.allData
                let params = {
                    businessId: pageData.id,
                    businessType: 'barcodeInfoAudit',
                    auditSubject: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_TotUzWtyW_1dc23b59`, '条码单审批，单号：')  + `${pageData.infoNumber}`,
                    params: JSON.stringify(pageData)
                }
                args = Object.assign({}, args, params)
                args.allData = params
                resolve(args)
            })

        },
        handleShowFn () {
            let rs = true
            if (this.currentEditRow && this.currentEditRow.id) {
                rs = true
            } else {
                rs = false
            }
            return rs
        },
        goBack () {
            this.$parent.hideEditPage()
        },
        //新增弹窗
        itemGridAddPopup ({ Vue, pageConfig, btn, groupCode }) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            this.curGroupCode = groupCode
            this.fieldSelectType = 'material'
            let url = '/base/barcode/elsBarcodeRuleHead/itemList'
            let columns = [
                { field: 'ruleNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_elsBarcodeRuleItemListf28_ruleNumber`, '单据编码') },
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseBarcodeInfoItemList951d_itemNumber`, '单据行号') },
                { field: 'businessType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm132_busDocType`, '业务单据类型') },
                { field: 'businessField', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_EStFJO_fa24b7fe`, '业务单据字段') },
                { field: 'businessFieldName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_tFJORL_d790c17a`, '单据字段名称') },
                { field: 'attributeType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WcAc_2b753bb9`, '属性类型') },
                { field: 'usedCode_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQKjIo_263b3421`, '是否使用简码') },
                { field: 'codeType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseBarcodeInfoItemListe32c_codeType`, '简码类型') }
            ]
            this.$refs.fieldSelectModal.open(url, {'headId': allData.ruleId}, columns, 'multiple')
        },
        checkItemSelectOk (data) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            let itemGrid = this.getItemGridRef('purchaseBarcodeInfoItemList')
            data.forEach(item => {
                item['id'] = null
                item['infoNumber'] = allData.infoNumber
            })
            itemGrid.insertAt(data, -1)
            let sysBarcodeListGrid = this.getItemGridRef('sysBarcodeList')
            sysBarcodeListGrid.remove()
        },
        generateBarcode ({ Vue, pageConfig, btn, groupCode }) {
            let itemGrid = this.getItemGridRef(groupCode)
            const printNumber = this.getBusinessExtendData(this.businessRefName).allData.printNumber
            const purchaseBarcodeInfoItemList = this.getBusinessExtendData(this.businessRefName).allData.purchaseBarcodeInfoItemList
            if (purchaseBarcodeInfoItemList.length == 0){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyName`, '条码属性内容不能为空！'))
                return
            }
            this.confirmLoading = true
            postAction(this.url.generateBarcode, this.getBusinessExtendData(this.businessRefName).allData).then((res) => {
                if (res.success) {
                    //插入行数据
                    itemGrid.remove()
                    itemGrid.insertAt(res.result, -1)
                    let gridRef = this.getItemGridRef('purchaseBarcodeInfoItemList')
                    let {fullData} = gridRef.getTableData()
                    // 已经生成就禁用属性内容，利用fbk1拓展字段
                    fullData.map(item => {
                        item.fbk1 = '1'
                    })
                    gridRef.loadData(fullData)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 表格通用删除
        businessGridDelete ({ groupCode = '' }) {
            if (!groupCode) {
                return
            }
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            itemGrid.removeCheckboxRow()
            // 删除过滤已经删除生成的条码
            let sysBarcodeListGrid = this.getItemGridRef('sysBarcodeList')
            sysBarcodeListGrid.remove()
        },
        customBarcode ({ Vue, pageConfig, btn, groupCode }) {
            let itemGrid = this.getItemGridRef(groupCode)
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)

            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {
                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            row.printNumber = this.getBusinessExtendData(this.businessRefName).allData.printNumber
            itemGrid.insertAt([row], -1)
        },
        supplierGridAddPopup ({ Vue, pageConfig, btn, groupCode }) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            this.curGroupCode = groupCode
            this.fieldSelectType = 'material'
            let url = '/supplier/supplierMaster/list'
            let columns = [
                {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150},
                {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 200},
                {field: 'supplierStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'), width: 200},
                {field: 'cateName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'), width: 200}
            ]
            this.$refs.SupplierFieldSelectModal.open(url, {}, columns, 'multiple')
        },
        checkSupplierSelectOk (data) {
            let itemGrid = this.getItemGridRef('barcodeSupplierListList')
            data.forEach(item => {
                item['id'] = null
            })
            itemGrid.insertAt(data, -1)
        }

    }
}
</script>