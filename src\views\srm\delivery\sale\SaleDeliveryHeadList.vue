<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"/>
    <!-- 表单区域 -->
    <!--<purchaseRequestHead-modal
          v-if="showEditPage"
          :current-edit-row="currentEditRow"
          @hide="hideEditPage" />-->

    <SaleDeliveryHeadModal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
    <ViewSaleDeliveryModal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetailPage"
    />
    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="printVisible"
      @ok="selectedPrintTemplate">
      <template slot="footer">
        <a-button
          key="back"
          @click="handlePrintCancel">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedPrintTemplate">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="printNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"/>
    </a-modal>
  </div>
</template>

<script>
import SaleDeliveryHeadModal from './modules/SaleDeliveryHeadModal'
import ViewSaleDeliveryModal from './modules/ViewSaleDeliveryModal'
import {getAction, httpAction, postAction} from '@/api/manage'
import {ListMixin} from '@comp/template/list/ListMixin'
import {REPORT_ADDRESS} from '@/utils/const.js'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        SaleDeliveryHeadModal,
        ViewSaleDeliveryModal
    },
    data () {
        return {
            showEditPage: false,
            showCreateOrderPage: false,
            showEditEnquiryPage: false,
            currentEditRow: {},
            printVisible: false,
            submitLoading: false,
            printStyle: 'single',
            printRow: {},
            printNumber: undefined,
            templateOpts: [],
            nextOpt: true,
            pageData: {
                businessType: 'delivery',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNtFMWShSty_dbff2dcb`, '请输入单据描述或发货单号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_zRfW_2ef2f473`, '批量打印'),
                        icon: 'download',
                        folded: false,
                        clickFn: this.printBatchs
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'order#saleDeliveryHead:queryById'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.showEditCondition,
                        authorityCode: 'order#saleDeliveryHead:edit'
                    },
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印'),
                        clickFn: this.serachTemplate,
                        allow: this.showPrintCondition,
                        authorityCode: 'order#saleDeliveryHead:print'
                    },
                    {
                        type: 'close-circle',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                        clickFn: this.cancel,
                        allow: this.showCancelCondition,
                        authorityCode: 'order#saleDeliveryHead:cancel'
                    },
                    {
                        type: 'close',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_close`, '关闭'),
                        clickFn: this.close,
                        allow: this.showCloseCondition,
                        authorityCode: 'order#saleDeliveryHead:close'
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.showDeleteCondition,
                        authorityCode: 'order#saleDeliveryHead:delete'
                    },
                    {
                        type: 'chat',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'),
                        clickFn: this.handleChat,
                        allow: this.allowChat
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ],
                optColumnWidth: 250
            },
            url: {
                list: '/delivery/saleDeliveryHead/list',
                add: '/delivery/saleDeliveryHead/add',
                delete: '/delivery/saleDeliveryHead/delete',
                printBatchs: '/report/print/elsUReportPrint/downLoadPdfprint',
                printBatchsJimu: '/report/elsJmReportPrint/batchPrint',
                cancel: '/delivery/saleDeliveryHead/cancel',
                close: '/delivery/saleDeliveryHead/close',
                columns: 'SaleDeliveryHeadList'
            }
        }
    },
    mounted () {
        let queryData = this.$route.query
        // 进入编辑状态
        if (queryData && queryData.status && queryData.status === 'edit' && queryData.id) {
            this.currentEditRow.id = queryData.id
            this.showEditPage = true
            this.showDetailPage = false
        }
    },
    methods: {
        handleChat (row) {
            let {id} = row
            let recordNumber = row.deliveryNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'SaleDeliveryHead', url: this.url || '', recordNumber})
        },
        printBatchs () {
            this.currentPrintRow = {}
            let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            if (selectedRows.length <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPfWjc_3a1d557d`, '请选择需要打印的行'))
                return
            } else {
                this.nextOpt = true
                selectedRows.forEach((item, i) => {
                    if (parseFloat(item.deliveryStatus) == '0' || parseFloat(item.deliveryStatus) == '-1') {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_czELuIRL_4a848d`, '行状态为非已确认'))
                        this.nextOpt = false
                        return
                    }
                })
                this.currentPrintRow = selectedRows
                if (this.nextOpt) {
                    this.openBatchModal(selectedRows[0].busAccount)
                }
            }
        },
        openBatchModal (busAccount) {
            this.queryBatchPrintTemList(busAccount).then(res => {
                if (res.success) {
                    if (res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.id,
                                printId: item.printId,
                                printName: item.printName,
                                title: item.templateName,
                                printType: item.printType,
                                param: item.param
                            }
                        })
                        this.printNumber = ''
                        this.templateOpts = options
                        this.printStyle = 'batch'
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length === 1) {
                            this.printNumber = this.templateOpts[0].value
                            this.selectedPrintTemplate()
                        } else {
                            // 有多个模板先选择在新建
                            this.printVisible = true
                        }
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        queryBatchPrintTemList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: this.pageData.businessType}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        allowChat (row) {
            if ((row.deliveryStatus == '0')) {
                return true
            } else {
                return false
            }
        },
        showEditCondition (row) {
            if (row.hide == 1) {
                return true;
            } else if ((row.deliveryStatus == '0')) {
                return false
            } else {
                return true
            }
        },
        showCancelCondition (row) {
            if (row.hide == 1) {
                return true;
            } else if (row.deliveryStatus == '1') {
                return false
            } else {
                return true
            }
        },
        showDeleteCondition (row) {
            if (row.hide == 1) {
                return true;
            } else if ((row.deliveryStatus == '0')) {
                return false
            } else {
                return true
            }
        },
        showPrintCondition (row) {
            if ((row.deliveryStatus != '0' && row.deliveryStatus != '-1')) {
                return false
            } else {
                return true
            }
        },
        handlePrintCancel () {
            this.printVisible = false
        },
        serachTemplate (row) {
            this.printRow = row
            this.printStyle = 'single'
            this.openModal(row)
        },
        openModal (row) {
            this.queryPrintTemList(row.busAccount).then(res => {
                if (res.success) {
                    if (res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.id,
                                printId: item.printId,
                                printName: item.printName,
                                title: item.templateName,
                                printType: item.printType,
                                param: item.param
                            }
                        })
                        this.printNumber = ''
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length === 1) {
                            this.printNumber = this.templateOpts[0].value
                            this.selectedPrintTemplate()
                        } else {
                            // 有多个模板先选择在新建
                            this.printVisible = true
                        }
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        queryPrintTemList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: this.pageData.businessType}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        selectedPrintTemplate () {
            if (this.printStyle == 'batch') {
                if (this.printNumber) {
                    let that = this
                    that.submitLoading = true
                    let template = this.templateOpts.filter(item => {
                        return item.value == that.printNumber
                    })
                    let param = {
                        objectList: that.$refs.listPage.$refs.listGrid.getCheckboxRecords(),
                        parameter: template[0].param,
                        ureportName: template[0].printName
                    }
                    let urlPrint = this.url.printBatchs
                    if (template[0].printType == 'jimu') {
                        param = {
                            objectList: that.$refs.listPage.$refs.listGrid.getCheckboxRecords(),
                            parameter: template[0].param,
                            excelConfigId: template[0].printId
                        }
                        urlPrint = this.url.printBatchsJimu
                    }
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_zRfW_2ef2f473`, '批量打印'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLPmistFuGWWWfW_a8c51d60`, '确认将此选中单据导出PDF打印'),
                        onOk: function () {
                            //that.currentRow.tacticsObject = 'person'
                            that.downLoadPdfprint(urlPrint, param)
                        }
                    })
                }
            } else {
                if (this.printNumber) {
                    const that = this
                    this.submitLoading = true
                    let template = this.templateOpts.filter(item => {
                        return item.value == that.printNumber
                    })
                    let params = {
                        templateNumber: this.printNumber,
                        printId: template[0].printId,
                        printName: template[0].printName,
                        printType: template[0].printType,
                        param: template[0].param
                    }
                    that.printVisible = false
                    that.submitLoading = false
                    let rowItem = this.printRow
                    this.printRow = {}
                    let urlParam = ''
                    if (params.param) {
                        let json = JSON.parse(params.param)
                        Object.keys(json).forEach((key, i) => {
                            urlParam += '&' + key + '=' + rowItem[json[key]]
                        })
                    }
                    console.log(urlParam)
                    if (params.printType == 'ureport') {
                        const token = this.$ls.get('Access-Token')
                        //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
                        const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:' + params.printName + '&token=' + token + urlParam
                        window.open(url, '_blank')
                    }
                    if (params.printType == 'jimu') {
                        const token = this.$ls.get('Access-Token')
                        const url = REPORT_ADDRESS + '/els/report/jmreport/view/' + params.printId + '?token=' + token + urlParam
                        //const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.title+'&token=' + token+'&id='+rowItem.id
                        window.open(url, '_blank')
                    }
                }
            }
        },
        hideDetailPage () {
            this.showDetailPage = false
        },
        download (row) {
            const token = this.$ls.get('Access-Token')
            //const url = REPORT_ADDRESS + '/els/report/jmreport/view/557839028264095744?id='+row.id + '&token=' + token
            const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:delivery_println.ureport.xml&id=' + row.id + '&token=' + token
            window.open(url, '_blank')
        },
        showCloseCondition (row) {
            if (row.hide == 1) {
                return true;
            } else if (row.deliveryStatus == '2' || row.deliveryStatus == '4') {
                return false
            } else {
                return true
            }
        },
        close (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_close`, '关闭'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherClose`, '确认是否关闭?'),
                onOk: function () {
                    that.postUpdateData(that.url.close, row)
                }
            })
        },
        cancel (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        downLoadPdfprint (url, row) {
            this.$refs.listPage.confirmLoading = true
            postAction(url, row, {
                responseType: 'arraybuffer'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('target', '_blank')
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
                // this.searchEvent()
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        postUpdateData (url, row) {
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        }
    }
}
</script>
