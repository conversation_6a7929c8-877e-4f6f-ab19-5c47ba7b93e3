<template>
  <div>
    <vxe-grid
      v-bind="gridConfig"
      :height="250"
      ref="table"
      :data="tableData"
      :edit-rules="editRules"
      :columns="tableColumns"
      show-overflow="title" />
  </div>
</template>
<script lang="jsx">
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
export default {
    props: {
        tenderEvaluationTemplateRegulationInfoList: {
            type: Array,
            default: () => {
                return []
            }
        },
        pageStatus: {
            type: String,
            default: 'edit'
        }
    },
    mixins: [tableMixins],
    computed: {
        tableColumns () {
            let columns =
                this.pageStatus == 'edit'
                    ? [
                        {
                            type: 'seq',
                            width: 50,
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TARL_303d20d9`, '条列名称'),
                            field: 'regulationName',
                            editRender: { enabled: true, name: '$input' }
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TvMW_30358fcb`, '条例描述'),
                            field: 'regulationDesc',
                            editRender: { enabled: true, name: '$input' }
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQqRd_c79d6ae2`, '是否客观项'),
                            field: 'objective',
                            width: 250,
                            editRender: {
                                enabled: true,
                                name: '$select',
                                options: [
                                    { value: '0', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Q_5426`, '否') },
                                    { value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_K_662f`, '是') }
                                ]
                            }
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                            field: 'title',
                            width: 100,
                            slots: {
                                default: ({ row }) => {
                                    return [<a-button onClick={() => this.handleDelete(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除')}</a-button>]
                                }
                            }
                        }
                    ]
                    : [
                        {
                            type: 'seq',
                            width: 50,
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TARL_303d20d9`, '条列名称'),
                            field: 'regulationName'
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TvMW_30358fcb`, '条例描述'),
                            field: 'regulationDesc'
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQqRd_c79d6ae2`, '是否客观项'),
                            field: 'objective_dictText',
                            width: 250
                        }
                    ]
            return columns
        }
    },
    data () {
        return {
            tableData: [],
            editRules: {
                regulationName: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TARL_303d20d9`, '请输入条列名称') },
                    { max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')}
                ],
                regulationDesc: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TvMW_30358fcb`, '请输入条例描述') },
                    { max: 1000, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow1000`, '内容长度不能超过1000个字符')}
                ],
                objective: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQqRd_c79d6ae2`, '请输入是否客观项') }]
            }
        }
    },
    watch: {
        tenderEvaluationTemplateRegulationInfoList (data) {
            this.tableData = [...data]
        }
    },
    methods: {
        addItem (data) {
            this.$refs.table.insertAt(data, -1)
        },
        handleDelete (row) {
            this.$refs.table.remove(row)
        },
        getValidatePromise () {
            return this.$refs.table.validate(true)
        },
        getAllData () {
            let { fullData } = this.$refs.table.getTableData()
            return fullData
        }
    },
    mounted () {
        this.$refs.table.loadData(this.tenderEvaluationTemplateRegulationInfoList)
    }
}
</script>
