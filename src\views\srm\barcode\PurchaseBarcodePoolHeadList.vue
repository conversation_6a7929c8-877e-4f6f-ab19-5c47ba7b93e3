<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 详情界面 -->
    <purchase-barcode-pool-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>
import PurchaseBarcodePoolHeadDetail from './modules/PurchaseBarcodePoolHeadDetail'
import { ListMixin } from '@comp/template/list/ListMixin'
import { postAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseBarcodePoolHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeInfo',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNToAy_b7f8464a`, '请输入条码编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
                        icon: 'download',
                        folded: true, 
                        clickFn: this.handleExportXls
                    },
                    // {
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt_base`, '基本信息导入'),
                    //     icon: 'import',
                    //     folded: true,
                    //     clickFn: this.importHeadExcel
                    // },
                    // {
                    //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt_barcode_attr`, '条码属性内容导入'),
                    //     icon: 'import',
                    //     folded: true,
                    //     clickFn: this.importItemExcel
                    // },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'barcode#pool:detail'
                    },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enable`, '启用'), clickFn: this.handleEnabled, allow: this.showEnabled, authorityCode: 'barcode#pool:enable' },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_disable`, '禁用'), clickFn: this.handleDisabled, allow: this.showDisabled, authorityCode: 'barcode#pool:disabled' }
                ]
            },
            url: {
                list: '/base/barcode/purchaseBarcodePoolHead/list',
                add: '/base/barcode/purchaseBarcodePoolHead/add',
                delete: '/base/barcode/purchaseBarcodePoolHead/delete',
                changeEnabled: '/base/barcode/purchaseBarcodePoolHead/changeEnabled',
                changeDisabled: '/base/barcode/purchaseBarcodePoolHead/changeDisabled',
                invalid: '/base/barcode/purchaseBarcodePoolHead/invalid',
                deleteBatch: '/base/barcode/purchaseBarcodePoolHead/deleteBatch',
                importExcelUrl: '/base/barcode/purchaseBarcodePoolHead/importExcel',
                columns: 'purchaseBarcodePoolHeadList',
                excelCode: 'BarcodePoolHeadExcelHandle',
                excelHeadCode: 'BarcodePoolHeadExcelHandle',
                excelItemCode: 'BarcodePoolItemExcelHandle',
                exportXlsUrl: '/base/barcode/purchaseBarcodePoolHead/exportXls'
            }
        }
    },
    methods: {

        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_nRToGeB_5768ae41`, '8D改进'))
        },
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        showEnabled (row) {
            return row.status == 'disabled' ? false : true
        },
        showDisabled (row) {
            return row.status == 'enabled' ? false : true
        },
        handleEnabled (row) {
            let that = this
            const params = { id: row.id, statusType: '1' }
            postAction(this.url.changeEnabled, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_AjLR_28088728`, '启用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        handleDisabled (row) {
            debugger
            let that = this
            const params = { id: row.id, statusType: '2' }
            postAction(this.url.changeDisabled, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_HjLR_38ff8896`, '禁用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        importHeadExcel (){
            this.url.excelCode = this.url.excelHeadCode
            this.importExcel(this.url.excelHeadCode)
        },
        importItemExcel (){
            this.url.excelCode = this.url.excelItemCode
            this.importExcel(this.url.excelItemCode)
        }
    }
}
</script>