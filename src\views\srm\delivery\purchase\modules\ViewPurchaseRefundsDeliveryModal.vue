<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url"
      @loadSuccess="handleLoadSuccess" />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <RelationGraphModal
      v-if="modalVisibleDocket && currentEditRow.documentId"
      :modalVisibleDocket="modalVisibleDocket"
      :id="currentEditRow.documentId"
      :rootId="currentEditRow.id"
      @closeModalDocket="closeModalDocket"
      @onNodeClick="clickNode"
    />
  </div>
</template>
<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {getAction} from '@/api/manage'
import RelationGraphModal from '@comp/RelationGraphModal'
import {cloneDeep} from 'lodash'

export default {
    name: 'ViewPurchaseRefundsDeliveryModal',
    mixins: [DetailMixin],
    components: {
        RelationGraphModal
    },
    data () {
        return {
            showRemote: false,
            modalVisibleDocket: false,
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnLineInfo`, '退货行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseRefundsDeliveryItemList',
                        columns: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tFKm_2766a28a`, '单据联查'), showCondition: this.showDocket, click: this.viewDocket },
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }

                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/delivery/purchaseRefundsDeliveryHead/queryById'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_refundsDelivery_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        }else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    mounted() {
        if(this.allowEdit) {
            let publicBtn = cloneDeep(this.pageData.publicBtn)
            publicBtn.unshift({ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), click: this.toEdit });
            this.$set(this.pageData, "publicBtn", publicBtn);
        }
    },
    methods: {
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        clickNode (){
            this.$store.dispatch('SetTabConfirm', false)
            this.modalVisibleDocket = false
        },
        closeModalDocket (){
            this.modalVisibleDocket = false
        },
        // 是否显示单据联查
        showDocket () {
            return this.currentEditRow.documentId
        },
        // 单据联查
        viewDocket () {
            this.modalVisibleDocket = true
        }
    }
}
</script>
