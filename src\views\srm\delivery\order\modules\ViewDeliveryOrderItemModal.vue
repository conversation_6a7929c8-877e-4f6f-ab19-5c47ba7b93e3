<template>
    <div class="els-page-comtainer">
        <tile-edit-page
            ref="editPage"
            :pageData="pageData"
            :url="url"
            @goBack="goBack"
        />
    </div>
</template>

<script>
import {tileEditPageMixin} from '@comp/template/tileStyle/tileEditPageMixin'
import {httpAction} from '@/api/manage'
import {formatDate} from '@/utils/util.js'

export default {
    name: 'ViewDeliveryOrderItemModal',
    mixins: [tileEditPageMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voucher`, '凭证'),
            confirmLoading: false,
            pageData: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderReceiptDetail`, '订单行收货详情'),
                form: {
                    orderNumber: '',
                    itemNumber: '',
                    factory: '',
                    factoryName: '',
                    storageLocation: '',
                    productionDate: '',
                    expiryDate: '',
                    batchNumber: '',
                    materialNumber: '',
                    materialDesc: '',
                    materialSpec: '',
                    materialGroup: '',
                    materialGroupName: '',
                    cateCode: '',
                    cateName: '',
                    requireDate: '',
                    deliveryDate: '',
                    quantity: '',
                    canDeliveryQuantity: '',
                    receiveQuantityNow: '',
                    orderBatch: '',
                    purchaseUnit: '',
                    remark: ''
                },
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentReceiptInfo`, '本次收货信息'),
                        content: {
                            ref: 'baseForm',
                            colSpan: '8',
                            type: 'form',
                            list: [
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号码'),
                                    fieldName: 'orderNumber',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                    fieldName: 'itemNumber',
                                    disabled: true
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factoryCode`, '库存组织'),
                                    fieldName: 'factory',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"',
                                    disabled: false
                                },
                                {
                                    type: 'selectModal',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageLocation`, '库存地点'),
                                    fieldName: 'storageLocation',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="location" && status="1"',
                                    disabled: false,
                                    extend: {
                                        modalColumns: [
                                            {field: "superBusinessCodeAndName", title: "业务组织", with: 250},
                                            {field: "orgCode", title: "库存地点编码", with: 150},
                                            {field: "orgName", title: "库存地点名称", with: 250}
                                        ],
                                        modalUrl: "/org/purchaseOrganizationInfo/queryLocationPage",
                                        modalParams: function (Vue, form) {
                                            console.log(form)
                                            return {
                                                busAccount: Vue.$ls.get('Login_elsAccount')
                                            };
                                        }
                                    },
                                    bindFunction: (that, data) => {
                                        that.pageData.form.storageLocation = data[0].orgCode
                                        that.pageData.form.storageLocationName = data[0].orgName
                                    }
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_productionDate`, '生产日期'),
                                    fieldName: 'productionDate',
                                    disabled: false
                                },
                                // {
                                //     type: 'date',
                                //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expiryDate`, '失效日期'),
                                //     fieldName: 'expiryDate',
                                //     disabled: false
                                // },
                                // {
                                //     type: 'input',
                                //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_batchNumber`, '批次号'),
                                //     fieldName: 'batchNumber',
                                //     disabled: false
                                // },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    fieldName: 'materialNumber',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
                                    fieldName: 'materialName',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                    fieldName: 'materialDesc',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),
                                    fieldName: 'materialSpec',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroup`, '物料组'),
                                    fieldName: 'materialGroup',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroupName`, '物料组名称'),
                                    fieldName: 'materialGroupName',
                                    disabled: true
                                },

                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SLzA_3589d785`, '物料分类'),
                                    fieldName: 'cateCode',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
                                    fieldName: 'cateName',
                                    disabled: true
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_requiredDeliveryDate`, '要求交期'),
                                    fieldName: 'requireDate',
                                    disabled: true
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveDate`, '收货日期'),
                                    fieldName: 'deliveryDate',
                                    default: new Date(),
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterReceiptDate`, '请输入收货日期'),
                                    disabled: this.currentEditRow.editType == 'detail'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_order_quantity`, '订单数量'),
                                    fieldName: 'quantity',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_onWayQuantity`, '在途数量'),
                                    fieldName: 'onWayQuantity',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivableQuantity`, '可收货数量'),
                                    fieldName: 'canDeliveryQuantity',
                                    disabled: true
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantityNow`, '本次收货数量'),
                                    fieldName: 'receiveQuantityNow',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterReceivableQuantityNow`, '请输入本次收货数量'),
                                    disabled: this.currentEditRow.editType == 'detail'
                                },
                                // {
                                //     type: 'input',
                                //     label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderBatchNum`, '订单批次号'),
                                //     fieldName: 'orderBatch',
                                //     placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterOrderBatchNum`, '请输入订单批次'),
                                //     disabled: this.currentEditRow.editType == 'detail'
                                // },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivingRemarks`, '收货备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterReceivingRemarks`, '请输入收货备注'),
                                    disabled: this.currentEditRow.editType == 'detail'
                                },
                                {
                                    type: 'input',
                                    label: '车牌号',
                                    fieldName: 'carNumber',
                                    placeholder: '请输入车牌号',
                                    disabled: false
                                }

                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IlStH_5c014508`, '已收货记录'),
                        content: {
                            type: 'table',
                            ref: 'purchaseDeliveryOrderItemList',
                            columns: [
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                                    field: 'orderNumber',
                                    width: 130
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                    field: 'itemNumber',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialNumber',
                                    width: 120
                                },

                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
                                    field: 'materialName',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                    field: 'materialDesc',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),
                                    field: 'materialSpec',
                                    width: 150
                                },

                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroup`, '物料组'),
                                    field: 'materialGroup',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroupName`, '物料组名称'),
                                    field: 'materialGroupName',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SLzA_3589d785`, '物料分类'),
                                    field: 'cateCode',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
                                    field: 'cateName',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_order_quantity`, '订单数量'),
                                    field: 'quantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderBatch`, '订单批次'),
                                    field: 'orderBatch',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveDate`, '收货日期'),
                                    field: 'deliveryDate',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantity`, '收货数量'),
                                    field: 'receiveQuantityNow',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'),
                                    field: 'purchaseUnit_dictText',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivingRemarks`, '收货备注'),
                                    field: 'remark',
                                    width: 150
                                }
                            ],
                            toolbarButton: []
                        }
                    }
                ],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receive`, '收货'),
                        type: 'primary',
                        clickFn: this.mySaveEvent,
                        showCondition: this.showButton
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack}
                ]
            },
            url: {
                edit: '/delivery/purchaseDeliveryOrderItem/add',
                detail: '/delivery/purchaseDeliveryOrderItem/queryOrderItem',
                deliveryItemList: '/delivery/purchaseVoucherHead/receiveList'
            },
        }
    },
    mounted() {

    },
    created() {
        this.pageData.form.receiveQuantityNow = this.currentEditRow.notDeliveryQuantity
        this.pageData.form.canDeliveryQuantity = this.currentEditRow.notDeliveryQuantity
        this.pageData.form.deliveryDate = formatDate(new Date().getTime(), 'yyyy-MM-dd')
    },
    methods: {
        goBack() {
            this.$emit('hide')
        },
        showButton() {
            if (parseFloat(this.pageData.form.canDeliveryQuantity) <= 0 || this.currentEditRow.editType == 'detail') {
                return false
            } else {
                return true
            }
        },
        mySaveEvent() {
            if (!this.pageData.form.factory || this.pageData.form.factory === '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_factory`, '工厂编码不能为空!'))
                return
            }
            if (!this.pageData.form.storageLocation || this.pageData.form.storageLocation === '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_storageLocation`, '库存地点不能为空!'))
                return
            }
            if (!this.pageData.form.productionDate || this.pageData.form.productionDate === '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_productionDate`, '生产日期不能为空!'))
                return
            }
            if (!this.pageData.form.carNumber || this.pageData.form.carNumber === '') {
                this.$message.warning('车牌号不能为空!')
                return
            }
            // if(!this.pageData.form.expiryDate || this.pageData.form.expiryDate ===''){
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expiryDate`, '失效日期不能为空!'))
            //     return
            // }
            // if(!this.pageData.form.batchNumber || this.pageData.form.batchNumber ===''){
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_batchNumber`, '批次号不能为空!'))
            //     return
            // }
            if (!this.pageData.form.receiveQuantityNow || this.pageData.form.receiveQuantityNow === '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentReceiptQuantityCannotBlank`, '本次收货数量不能为空!'))
                return
            }
            if (parseFloat(this.pageData.form.receiveQuantityNow) <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityReceivedThisTimeCannotLessThanEqualZero`, '本次收货数量不能小于等于0'))
                return
            }
            if (!this.pageData.form.deliveryDate || this.pageData.form.deliveryDate === '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receiptDateCannotBlank`, '收货日期不能为空!'))
                return
            }
            let receiveQuantityNow = this.pageData.form.receiveQuantityNow + ''
            if (!receiveQuantityNow.match(/^[0-9]+([.]{1}[0-9]+){0,1}$/)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterNumericValue`, '请输入数字值!'))
                return
            }
            this.pageData.form.receiveQuantityNow = ((this.pageData.form.receiveQuantityNow - 0)).toFixed(4)

            if (parseFloat(this.pageData.form.receiveQuantityNow) > parseFloat(this.pageData.form.canDeliveryQuantity)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currentReceiptQuantityCannoGreaterThanReceivableQuantity`, '当前收货数量不可大于可收货数量!'))
                return
            }
            this.postUpdateData(this.url.edit, this.$refs.editPage.getParamsData())
        },
        requestAfter(data) {
            this.pageData.form.receiveQuantityNow = data.notDeliveryQuantity
            this.pageData.form.canDeliveryQuantity = data.notDeliveryQuantity
            this.pageData.form.deliveryDate = formatDate(new Date().getTime(), 'yyyy-MM-dd')
            this.showButton()
        },
        postUpdateData(url, params) {
            let that = this
            let orderItemId = params.id
            this.$refs.editPage.confirmLoading = true
            httpAction(url, params, 'post').then((res) => {
                if (res.success) {
                    that.$message.success(res.message)
                    that.$parent.searchEvent()
                    that.$children[0].requestTabsData(orderItemId)
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
                that.$refs.editPage.confirmLoading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
</style>