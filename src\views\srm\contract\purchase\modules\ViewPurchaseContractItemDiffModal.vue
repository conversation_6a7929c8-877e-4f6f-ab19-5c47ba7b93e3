<template>
  <a-modal
    v-drag    
    v-model="diffModal"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_differenceComparison`, '差异对比')"
    :footer="null"
    :width="1300">
    <div class="code-diff">
      <code-diff
        :old-string="oldStr"
        :new-string="newStr"
        outputFormat="side-by-side" />
    </div>
  </a-modal>
</template>
<script>
import {CodeDiff} from 'v-code-diff'
export default {
    name: 'ViewItemDiff',
    components: {CodeDiff},
    data () {
        return {
            diffModal: false,
            oldStr: '',
            newStr: ''
        }
    },
    methods: {
        open (row) {
            this.diffModal = true
            // this.oldStr = row.originalContent.replace(/<[^>]+>/g, '')
            // this.newStr = row.itemContent.replace(/<[^>]+>/g, '')
            this.oldStr = row.originalContent
            this.newStr = row.itemContent
        }
    }
}
</script>