<template>
  <div>
    <a-modal
      v-drag
      centered
      :title="modalTitle"
      :width="960"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      :footer="operations ? undefined : null"
      @ok="selectedOk"
      @cancel="close">
      <a-input-search
        v-if="!needSearch"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')"
        style="margin-bottom:8px"
        v-model="form.keyWord"
        @search="onSearch"
        enterButton />
      <vxe-grid
        border
        resizable
        show-overflow
        highlight-hover-row
        max-height="350"
        row-id="id"
        size="small"
        :loading="loading"
        ref="selectGrid"
        :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
        :data="tableData"
        :pager-config="(isTree || !needPager) ? null : tablePage"
        :radio-config="selectModel === 'single' ? checkedConfig : undefined"
        :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
        :tree-config="isTree ? treeConfig : null"
        :columns="columns"
        @cell-dblclick="selectedOk"
        @page-change="handlePageChange">
      </vxe-grid>
    </a-modal>
  </div>
</template>

<script>
import { httpRequest, getAction } from '@/api/manage'
export default {
    name: 'FieldSelectModal',
    props: {
        isEmit: {
            type: Boolean,
            default: false
        },
        requestMethod: {
            type: String,
            default: 'get'
        },
        isTree: {
            type: Boolean,
            default: false
        },
        treeConfig: {
            type: Object,
            default () {
                return {}
            }
        },
        pageConfigData: {
            type: Object,
            default () {
                return {}
            }
        },
        modalTitle: {
            type: String,
            default () {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SelectData`, '选择数据')
            }
        },
        handleListData: {
            type: Function,
            default: null
        },
        operations: {
            type: Boolean,
            default () {
                return true
            }
        },
        needSearch: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            visible: false,
            loading: false,
            confirmLoading: false,
            columns: [],
            url: '',
            selectModel: 'single',
            checkedConfig: {highlight: true, reserve: true, trigger: 'row'},
            queryParams: {},
            tableData: [],
            form: {
                keyWord: ''
            },
            needPager: true,
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                align: 'left',
                pageSizes: [10, 20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            }
        }
    },
    methods: {
        loadData (params, extend) {
            this.loading = true
            httpRequest(this.url, params, this.requestMethod).then((res) => {
                this.loading = false
                if (!res.success) {
                    this.$message.error(res.message)
                    return
                }
                if (this.handleListData) { // 单独处理list数据
                    return this.handleListData(this, res)
                }
                const flag = this.isTree
                    ? res.success && res.result && res.result.length
                    : res.success && res.result && res.result.records && res.result.records.length
                if (flag) {
                    let result = this.isTree ?  res.result : res.result.records
                    result = result || []
                    this.tableData = result
                    this.tablePage.total = res.result.total
                    if (extend && extend.action === 'init') {
                        // 初始化清空选项 包括跨页勾选的
                        if (this.selectModel === 'single') {
                            this.$refs.selectGrid && this.$refs.selectGrid.clearRadioReserve()
                        }else {
                            this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxReserve()
                        }
                    }
                } else {
                    this.tableData = []
                    this.tablePage.total = 0
                    this.tablePage.currentPage = 1
                }
            })
        },
        open (url, params, columns, selectModel, checkedConfig) {
            // 打开清空keyWord
            this.form.keyWord = ''
            this.tablePage.currentPage = 1
            let tableColumns = columns ? [...columns] : []
            this.queryParams = {pageSize: this.tablePage.pageSize, pageNo: this.tablePage.currentPage}
            checkedConfig ? this.checkedConfig = {...this.checkedConfig, ...checkedConfig} : ''
            this.url = url
            if (selectModel) {
                this.selectModel = selectModel
            }
            tableColumns.unshift({ type: 'seq',  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 })
            if (this.selectModel === 'single') {
                tableColumns.unshift({ type: 'radio', width: 40 })
            }else if (this.selectModel === 'multiple') {
                tableColumns.unshift({ type: 'checkbox', width: 40 })
            }
            tableColumns.forEach(col=>{ // 国际化处理
                if(col.fieldLabelI18nKey){
                    col.title = this.$srmI18n(`${this.$getLangAccount()}#${col.fieldLabelI18nKey}`, col.title)
                }
            })
            this.columns = tableColumns
            if (params) {
                this.queryParams = Object.assign({}, this.queryParams, params)
            }
            this.loadData(this.queryParams, {action: 'init'})
            this.visible = true
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            let selectedData = this.$refs.selectGrid.getCheckboxRecords() // 当前页已选择数据
            if (this.selectModel === 'single') {
                selectedData = this.$refs.selectGrid.getRadioRecord() ? [this.$refs.selectGrid.getRadioRecord()] : []
            } else {
                const otherPageCheckData = this.$refs.selectGrid.getCheckboxReserveRecords() || [] // 不包含当前页已选中数据
                selectedData = [...otherPageCheckData, ...selectedData ]
            }
            if (selectedData.length) {
                this.visible = false
                if (this.pageConfigData.itemColumns) { // 表行
                    selectedData.forEach(item => {
                        this.pageConfigData.itemColumns.forEach(el => {
                            if (el.defaultValue && (item[el.field] == '' || item[el.field] == null)) { // 模板有默认值且当前表单返回没有值
                                item[el.field] = el.defaultValue
                            }   
                        })
                    })
                }
                if (this.isEmit) {
                    this.$emit('ok', selectedData)
                } else {
                    this.$parent.fieldSelectOk(selectedData)
                }
            }else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
            }
        },
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            let params = Object.assign({}, this.queryParams, {pageSize: pageSize, pageNo: currentPage})
            this.loadData(params)
        },
        onSearch (keyWord) {
            this.form.keyWord = keyWord
            this.queryParams = Object.assign({}, this.queryParams, {keyWord: this.form.keyWord })
            let params = Object.assign({}, this.queryParams, { pageNo: 1})
            this.loadData(params)
        }
    }
}
</script>