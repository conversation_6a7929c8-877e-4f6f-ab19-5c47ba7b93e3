<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction } from '@/api/manage'
import REGEXP from '@/utils/regexp'
console.log('REGEXP', REGEXP)
export default {
    name: 'SubaccountCertificationEdit',
    mixins: [EditMixin],
    data () {
        return {
            selectType: 'esignPersonCertification',
            credentialsNo: '',
            credentialsType: '',
            pageData: {
                form: {
                    subAccount: '',
                    name: '',
                    mobile: '',
                    email: '',
                    idType: '',
                    idNumber: '',
                    subAccountId: '',
                    certification: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_UzSQNJOHrA0MPEQA`, '个人认证信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    fieldName: 'subAccount',
                                    required: '1',
                                    bindFunction: function (Vue, data){
                                        Vue.form.subAccount = data[0].subAccount,
                                        Vue.form.name = data[0].realname,
                                        Vue.form.mobile = data[0].phone,
                                        Vue.form.email = data[0].email,
                                        Vue.form.subAccountId = data[0].id
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150},
                                            {field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150},
                                            {field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_phone`, '手机号'), with: 150},
                                            {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'), with: 150}
                                        ], modalUrl: '/account/elsSubAccount/list', modalParams: {status: 1}
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'),
                                    fieldName: 'name',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                                    fieldName: 'idType',
                                    dictCode: 'srmEsignIdType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    fieldName: 'idNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'),
                                    fieldName: 'mobile',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'),
                                    fieldName: 'email',
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型'),
                                    fieldName: 'certificationType',
                                    dictCode: 'srmCertificationType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationType`, '认证类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankCardNo`, '个人银行卡(银联卡)'),
                                    fieldName: 'bankCardNo'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_wRmEgGGAjjZKOiUf`, 'e签宝个人账户id'),
                                    fieldName: 'accountId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationStatus`, '认证状态'),
                                    fieldName: 'certificationStatus',
                                    disabled: true,
                                    dictCode: 'srmEsignCertificationStatus',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificationStatus`, '认证状态')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_shortLink`, '实名认证短链接'),
                                    fieldName: 'shortLink',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_longLink`, '实名认证长链接'),
                                    disabled: true,
                                    fieldName: 'longLink'
                                }
                            ],
                            validateRules: {
                                subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_verifiedAccount`, '认证账号')}],
                                name: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BzLzRXfGjIHmIJZn`, '名称不能为空')}],
                                idType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_f6XyqCu3syIh31zT`, '证件类型不能为空')}],
                                idNumber: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_wfOnIW5LtvkPwjKg`, '证件号不能为空')}],
                                mobile: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_mobilePhoneNumberCannotEmpty`, '手机号不能为空')}, {
                                    pattern: REGEXP.mobile,
                                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_zo0FYPrFVHS0n4uC`, '手机号有误')
                                }],
                                email: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_emailCannotBeEmpty`, '邮箱不能为空')}, {
                                    pattern: REGEXP.email,
                                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_twzFrPtBtCTBqUrx`, '邮箱格式有误')
                                }],
                                bankCardNo: [{pattern: REGEXP.unionPayCard, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_DF9JQudm1zffj6fv`, '卡号有误')}]
                            }
                        }

                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_YIpUJ3gPutMJ2ZCU`, '修改认证信息'), type: 'primary', click: this.modifyCertificationEvent, showCondition: this.modifyInfoBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), type: 'primary', click: this.submitCertificationEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                edit: '/esign/elsSubaccountCertificationInfo/edit',
                detail: '/esign/elsSubaccountCertificationInfo/queryById',
                auth: '/esign/elsSubaccountCertificationInfo/submintCertification',
                modifyAuthInfo: '/esign/elsSubaccountCertificationInfo/modifyAuthInfo'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
                if(this.currentEditRow.accountId){
                    this.credentialsNo = this.currentEditRow.idNumber
                    this.credentialsType = this.currentEditRow.idType
                }
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        modifyInfoBtn (){
            if(this.currentEditRow.accountId){
                return true
            }
            return false
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    let url = this.url.edit
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        this.goBack()
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        submitCertificationEvent () {
            const _this = this
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    let url = this.url.auth
                    let longLink = params.longLink
                    if (longLink && longLink.length>0) {
                        const that = this
                        this.$confirm({
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationSystems`, '确认认证'),
                            content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_rVHIDJRLiWKmDJMAbujWKQtTDJW_96191780`, '该信息已提交过认证，再次提交会产生费用，是否继续提交？'),
                            onOk: function () {
                                postAction(url, params).then(res => {
                                    const type = res.success ? 'success' : 'error'
                                    that.$message[type](res.message)
                                    if(type){
                                        _this.$parent.submitCallBack(params)
                                    }
                                }).finally(() => {
                                    that.confirmLoading = false
                                })
                            }
                        })
                    } else {
                        postAction(url, params).then(res => {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            if(type){
                                _this.$parent.submitCallBack(params)
                            }
                        }).finally(() => {
                            this.confirmLoading = false
                        })
                    }
                }
            }).catch(err => {
                console.log(err)
            })
        },
        modifyCertificationEvent (){
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    if(this.credentialsNo !== params.idNumber || this.credentialsType !== params.idType){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IDJLixOcriIyniIAcWNTcrWtkQGKVVDJLi_f21199ce`, '已提交认证不能修改证件号和证件类型，如需修改先操作删除再重新提交认证'))
                        return
                    }
                    let url = this.url.modifyAuthInfo
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.init()
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>