import { srmI18n, getLangAccount } from '@/utils/util'

/**
|--------------------------------------------------
| http-server
| 本地 js 配置文件设置
|--------------------------------------------------
*/

export const LOCALSERIVCEURL = 'http://127.0.0.1:8081'

/**
|--------------------------------------------------
| 模板ref 名称
|--------------------------------------------------
*/
export const BUSINESSREFNAME = 'businessRef'

/**
|--------------------------------------------------
| websocket 心跳检测 时间配置
|--------------------------------------------------
*/
export const HEART_BEAT_CONFIG = {
    time: 30 * 1000, // time：心跳时间间隔
    timeout: 3 * 1000, // timeout：心跳超时间隔
    reconnect: 10 * 1000 // reconnect：断线重连时
}

/**
|--------------------------------------------------
| websocket 心跳检测 语句配置
|--------------------------------------------------
*/
export const HEART_CHECK_CONDE = {
    HEART_CHECK: 'heartCheck', //心跳
    SERVER_HEART_CHECK: 'heartcheck' //心跳
}

/**
|--------------------------------------------------
| bindFunction 配置函数字段
|--------------------------------------------------
*/
export const BINDFUNCTION = 'bindFunction' // PC端专用绑定函数字段

/**
|--------------------------------------------------
| bindFunction 私有函数名
| 用于扩展原 bindFunction 入参方式
| 传参格式对象 function __bindFunction(Vue,  { _pageData, _form, _row, _value, _cacheAllData, _data }, customFuncion)
|--------------------------------------------------
*/
export const PRIVATE_BIND_FUNCTION = '__bindFunction'

/**
|--------------------------------------------------
| extend 扩展字段
|--------------------------------------------------
*/
export const EXTEND = 'extend'

/**
|--------------------------------------------------
| extend 内省市区级联专用属性 key
|--------------------------------------------------
*/
export const EXTEND_KEY_CASCADER = 'cascader'

/**
|--------------------------------------------------
| total 合计字段
|--------------------------------------------------
*/
export const TOTAL = 'total'
/**
|--------------------------------------------------
| accept 上传附件扩展名
|--------------------------------------------------
*/
export const ACCEPT = '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf, .txt'

/**
|--------------------------------------------------
| beforeCheckedCallBack select-modal 模态框选择前回调
|--------------------------------------------------
*/
export const BEFORE_CHECK_CALLBACK = 'beforeCheckedCallBack'

/**
|--------------------------------------------------
| beforeCheckedCallBack 私有函数名
| 用于扩展原 beforeCheckedCallBack 入参方式
| 传参格式对象 function beforeCheckedCallBack(Vue,  { _pageData, _form, _row, _cacheAllData }, customFuncion)
|--------------------------------------------------
*/
export const PRIVATE_BEFORE_CHECK_CALLBACK = '__beforeCheckedCallBack'

/**
|--------------------------------------------------
| afterClearCallBack select-modal 模态框清除后回调
|--------------------------------------------------
*/
export const AFTER_CLEAR_CALLBACK = 'afterClearCallBack'

/**
|--------------------------------------------------
| afterClearCallBack 私有函数名
| 用于扩展原 afterClearCallBack 入参方式
| 传参格式对象 function __afterClearCallBack(Vue,  { _pageData, _form, _row, _cacheAllData }, customFuncion)
|--------------------------------------------------
*/
export const PRIVATE_AFTER_CLEAR_CALLBACK = '__afterClearCallBack'

/**
|--------------------------------------------------
| modalParams MODAL_PARAMS 自定义传参
|--------------------------------------------------
*/
export const MODAL_PARAMS = 'modalParams'

/**
|--------------------------------------------------
| modalParams selectModel 自定义传参
|--------------------------------------------------
*/
export const SELECT_MODEL = 'selectModel'
/**
|--------------------------------------------------
| modalParams modalUrl 自定义传参
|--------------------------------------------------
*/
export const MODAL_URL = 'modalUrl'
/**
|--------------------------------------------------
| modalParams modalColumns 自定义传参
|--------------------------------------------------
*/
export const MODAL_COLUMNS = 'modalColumns'
/**
|--------------------------------------------------
| modalParamsFunc 私有函数名
| 用于扩展原 modalParamsFunc 入参方式
| 传参格式对象 function __modalParamsFunc(Vue,  { _pageData, _form, _row, _cacheAllData }, customFuncion)
|--------------------------------------------------
*/
export const PRIVATE_MODAL_PARAMS_FUNC = '__modalParamsFunc'

/**
|--------------------------------------------------
| validateMethod
|--------------------------------------------------
*/
export const VALIDATEMETHOD = 'validateMethod'

/**
|--------------------------------------------------
| toolBar 按钮组
|--------------------------------------------------
*/

/**
 * 表格行工具按钮
 * 新增一行
 */
export const TOOLBAR_BUTTON_ADD = {
    title: srmI18n(`${getLangAccount()}#i18n_title_add`, '新增'),
    key: 'gridAdd',
    attrs: {
        type: 'primary'
    }
}

/**
 * 表格行工具按钮
 * 新增一行
 */
export const TOOLBAR_BUTTON_DELETE = {
    title: srmI18n(`${getLangAccount()}#i18n_title_delete`, '删除'),
    key: 'gridDelete'
}

/**
 * 表格行工具按钮
 * tab 页签 popup 按钮
 */
export const TOOLBAR_BUTTON_UPLOAD = {
    title: srmI18n(`${getLangAccount()}#i18n_title_UpAtachments`, '附件上传'),
    key: 'upload',
    attrs: {
        class: 'cu-btn block line-cyan round margin-xs'
    },
    icon: {
        name: 'plus-circle-fill',
        color: '#1cbbb4'
    },
    args: {
        modalVisible: false, // 必传
        businessType: 'ebidding', // 必传,
        // headId: '',
        refName: 'purchaseEbiddingItemList' // 指定 itemInfo 的ref 名称
        // single: false, // 只否只允许上传1个附件
        // disabledItemNumber: false, // 手动置灰行选择
        // requiredFileType: false, // 手动置灰行选择
        // property: 'materialDesc', // 下拉列表项label
        // isGridUpload: false,
        // itemInfo: [],
        // action: '',
        // itemNumberKey: 'itemNumber',
        // itemNumbeValueProp: '',
        // itemNumberLabel: srmI18n(`${getLangAccount()}#i18n_title_lineItem`, '行项目'),
    }
}

/**
|--------------------------------------------------
| 编辑页底部按钮组
|--------------------------------------------------
*/

/**
 * 模板按钮
 * 保存
 */
export const BUTTON_SAVE = {
    title: srmI18n(`${getLangAccount()}#i18n_title_save`, '保存'),
    key: 'save',
    args: {
        url: ''
    },
    attrs: {
        type: 'primary'
    }
}

/**
 * 模板按钮
 * 发布
 */
export const BUTTON_PUBLISH = {
    title: srmI18n(`${getLangAccount()}#i18n_title_publish`, '发布'),
    key: 'publish',
    args: {
        url: ''
    },
    attrs: {
        type: 'primary'
    }
}

/**
 * 模板按钮
 * 提交审批
 */
export const BUTTON_SUBMIT = {
    title: srmI18n(`${getLangAccount()}#i18n_title_submit`, '提交审批'),
    key: 'submit',
    args: {
        url: ''
    },
    attrs: {
        type: 'primary'
    }
}
/**
 * 模板按钮
 * 审批撤销
 */
export const BUTTON_AUDIT_CANCEL = {
    title: srmI18n(`${getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
    key: 'auditCancel',
    args: {
        url: ''
    },
    attrs: {
        type: 'danger'
    }
}

/**
 * 模板按钮
 * 返回
 */
export const BUTTON_BACK = {
    title: srmI18n(`${getLangAccount()}#i18n_title_back`, '返回'),
    key: 'goBack',
    args: {
        url: ''
    },
    attrs: {}
}

/**
 * 模板按钮
 * 查看流程
 */
export const BUTTON_ChECKPROCESS = {
    title: srmI18n(`${getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
    key: 'checkProcess',
    args: {
        url: ''
    },
    attrs: {}
}

/**
|--------------------------------------------------
| 模板编辑
| 表单添加 DEMO
| 表行添加 ROWDEMO
| 供应商信息 SUPPLIER_INFO_COLUMNS
| 附件 ATTACHMENT_COLUMNS
| 附件需求清单 ATTACHMENT_DEMAND_COLUMNS
|--------------------------------------------------
*/
export const DEMO = {
    groupCode: '',
    sortOrder: '',
    fieldType: '',
    fieldLabel: '',
    fieldLabelI18nKey: '',
    fieldName: '',
    dictCode: '',
    defaultValue: '',
    dataFormat: '',
    helpText: '',
    alertMsg: '',
    required: '',
    mobile: 1,
    placeholder: ''
}

export const ROWDEMO = {
    groupCode: '',
    title: '',
    fieldLabelI18nKey: '',
    field: '',
    fieldType: '',
    align: '',
    headerAlign: '',
    defaultValue: '',
    width: '',
    dictCode: '',
    alertMsg: '',
    mobile: 1,
    helpText: ''
}

export const SUPPLIER_INFO_COLUMNS = ({ groupCode = '' }) => ([
    { ...ROWDEMO, groupCode, field: 'toElsAccount', title: srmI18n(`${getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150 },
    { ...ROWDEMO, groupCode, field: 'supplierCode', title: srmI18n(`${getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150 },
    { ...ROWDEMO, groupCode, field: 'supplierName', title: srmI18n(`${getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200 },
    { ...ROWDEMO, groupCode, field: 'needCoordination', title: srmI18n(`${getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), width: 120, dictCode: 'srmSupplierCoordinationWay', slots: { default: 'renderDictLabel' } }
])

export const ATTACHMENT_COLUMNS = ({ groupCode = '' }) => ([
    { ...ROWDEMO, groupCode, field: 'fileType_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
    { ...ROWDEMO, groupCode, field: 'fileName', title: srmI18n(`${getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
    { ...ROWDEMO, groupCode, field: 'uploadTime', title: srmI18n(`${getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 200 },
    { ...ROWDEMO, groupCode, field: 'uploadElsAccount', title: srmI18n(`${getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
    { ...ROWDEMO, groupCode, field: 'uploadElsAccount_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_XVCbL_713646eb`, '上传方全称'), width: 120 },
    { ...ROWDEMO, groupCode, field: 'uploadSubAccount_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
    { ...ROWDEMO, groupCode, field: 'itemNumber', title: srmI18n(`${getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
    { ...ROWDEMO, groupCode, field: 'grid_opration', title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
])

export const ATTACHMENT_COLUMNS_WITHOUT_OPRATION = ({ groupCode = '', hiddenField = [] }) => {
    let columns = [
        { ...ROWDEMO, groupCode, field: 'fileType_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
        { ...ROWDEMO, groupCode, field: 'fileName', title: srmI18n(`${getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
        { ...ROWDEMO, groupCode, field: 'uploadTime', title: srmI18n(`${getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 200 },
        { ...ROWDEMO, groupCode, field: 'uploadElsAccount', title: srmI18n(`${getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
        { ...ROWDEMO, groupCode, field: 'uploadElsAccount_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_XVCbL_713646eb`, '上传方全称'), width: 120 },
        { ...ROWDEMO, groupCode, field: 'uploadSubAccount_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
        { ...ROWDEMO, groupCode, field: 'itemNumber', title: srmI18n(`${getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 }
    ]
    // 过滤不需要的字段
    return columns.filter(rs => !hiddenField.includes(rs.field))
}

export const ATTACHMENT_DEMAND_COLUMNS = ({ groupCode = '' }) => ([
    { ...ROWDEMO, groupCode, field: 'fileType', title: srmI18n(`${getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 150, dictCode: 'srmFileType', editRender: { name: '$select', options: [] } },
    { ...ROWDEMO, groupCode, field: 'stageType', title: srmI18n(`${getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 150, dictCode: 'srmEnquiryStageType', editRender: { name: '$select', options: [] } },
    { ...ROWDEMO, groupCode, field: 'required', title: srmI18n(`${getLangAccount()}#i18n_field_must`, '是否必填'), width: 120, cellRender: { name: '$switch', props: { openValue: '1', closeValue: '0' } } },
    { ...ROWDEMO, groupCode, field: 'remark', title: srmI18n(`${getLangAccount()}#i18n_title_remark`, '备注'), width: 220, editRender: { name: '$input' } }
])

export const SEQ_COLUMN = { type: 'seq', width: 60, fixed: 'left', title: srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号') }

export const CHECKBOX_COLUMN = { type: 'checkbox', width: 40, fixed: 'left' }

export const ATTACHMENT_GROUP = {
    groupName: '附件',
    groupNameI18nKey: 'i18n_title_attachment',
    groupCode: 'purchaseAttachmentList',
    groupType: 'item',
    sortOrder: '9'
}

export const ATTACHED_DEMAND_LIST_GROUP = {
    groupName: '附件需求清单',
    groupNameI18nKey: 'i18n_title_attachedDemandList',
    groupCode: 'purchaseAttachmentDemandList',
    groupType: 'item',
    sortOrder: '8'
}

export const GRID_OPTION_ROW = {
    field: 'grid_opration',
    title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
    width: 120,
    align: 'center',
    slots: { default: 'grid_opration' }
}

export const SUPPLIER_INFO_CHANG_AUDIT = new Map([
    [
        '1', 
        {
            businessType: 'supplierInfoChangAudit',
            auditSubject: '供应商信息变更审批：'
        }
    ],
    [
        '2', 
        {
            businessType: 'supplierInfoExpandChangAudit',
            auditSubject: '供应商拓展信息变更审批：'
        }
    ],
    [
        '3', 
        {
            businessType: 'supplierInfoContactsChangAudit',
            auditSubject: '供应商联系人信息变更审批：'
        }
    ],
    [
        '4', 
        {
            businessType: 'supplierInfoAddressChangAudit',
            auditSubject: '供应商地址信息变更审批：'
        }
    ],
    [
        '5', 
        {
            businessType: 'supplierInfoBankChangAudit',
            auditSubject: '供应商银行信息变更审批：'
        }
    ],
    [
        '6', 
        {
            businessType: 'supplierInfoOtherChangAudit',
            auditSubject: '供应商其他信息变更审批：'
        }
    ]
])

