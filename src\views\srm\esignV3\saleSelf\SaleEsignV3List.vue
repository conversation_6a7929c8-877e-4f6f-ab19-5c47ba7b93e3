<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showSingerEditPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleEsignV3Edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleEsignV3Detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <!-- 签章人维护 -->
    <SaleEsignV3SingerEdit
      v-if="showSingerEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleEsignV3Edit from './modules/SaleEsignV3Edit'
import SaleEsignV3Detail from './modules/SaleEsignV3Detail'
import SaleEsignV3SingerEdit from './modules/SaleEsignV3SingerEdit'
import { getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        SaleEsignV3Edit,
        SaleEsignV3Detail,
        SaleEsignV3SingerEdit
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            showSingerEditPage: false,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCELSeynRCRLQIdD_7f9f584`, '采方ELS账号/采购方名称/文件主体')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esignv3#elsEsignV3Flow:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LDPeL_2bb593a8`, '维护签章人'), clickFn: this.handleSingerEdit, allow: this.allowEdit, authorityCode: 'esignv3#elsEsignV3Flow:add'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQILD_ee657e73`, '签署文件维护'), clickFn: this.handleEdit, allow: this.allowSignUpload, authorityCode: 'esignv3#elsEsignV3Flow:add'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentDownload`, '流程文档下载'), clickFn: this.flowFileDownload, allow: this.showFlowFileDownload, authorityCode: 'esign#elsEsignV3Flow:signFileDownload'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/esignv3/elsEsignV3Flow/saleList',
                flowFileDownload: '/esignv3/elsEsignV3Flow/signFileDownload',
                columns: 'SaleElsEsignV3List'
            }
        }
    },
    methods: {
        allowEdit (row){
            //线上，签章人未维护或者供方签章人未维护
            if(row.onlineSealed==='1' && (row.signerVindicateStatus==='2' || row.signerVindicateStatus==='0') && row.sendBack!=='1'){
                return false
            }
            return true
        },
        allowSignUpload (row){
            //线下，文件未上传
            if(row.onlineSealed!=='1' && row.signFileUploaded!=='1' && row.sendBack!=='1'){
                return false
            }
            return true
        },
        showFlowFileDownload (row){
            //流程已经发起
            if(row.launch==='1' && row.archiving==='1'){
                return false
            }
            return true
        },
        flowFileDownload (row){
            getAction(this.url.flowFileDownload, {id: row.id}).then(res => {
                if(res.success){
                    window.open(res.result[0].fileUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showSingerEditPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleSingerEdit (row){
            this.showSingerEditPage = true
            this.currentEditRow = row
        }
    }
}
</script>