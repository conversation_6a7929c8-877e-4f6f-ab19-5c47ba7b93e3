<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <purchase-barcode-level-head-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <purchase-barcode-level-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import PurchaseBarcodeLevelHeadEdit from './modules/PurchaseBarcodeLevelHeadEdit'
import PurchaseBarcodeLevelHeadDetail from './modules/PurchaseBarcodeLevelHeadDetail'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseBarcodeLevelHeadEdit,
        PurchaseBarcodeLevelHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeLevel',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNtFAy_ae8e2a03`, '请输入单据编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary',
                        authorityCode: 'barcode#level:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'barcode#level:detail'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.showEdit,
                        authorityCode: 'barcode#level:edit'
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.showEdit,
                        authorityCode: 'barcode#level:delete'
                    },
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/base/barcode/purchaseBarcodeLevelHead/list',
                add: '/base/barcode/purchaseBarcodeLevelHead/add',
                delete: '/base/barcode/purchaseBarcodeLevelHead/delete',
                changeStatus: '/base/barcode/purchaseBarcodeLevelHead/changeStatus',
                invalid: '/base/barcode/purchaseBarcodeLevelHead/invalid',
                deleteBatch: '/base/barcode/purchaseBarcodeLevelHead/deleteBatch',
                columns: 'purchaseBarcodeLevelHeadList'
            }
        }
    },
    methods: {
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        showEdit (row) {
            return row.levelStatus == 'final' ? true : false
        }
    }
}
</script>