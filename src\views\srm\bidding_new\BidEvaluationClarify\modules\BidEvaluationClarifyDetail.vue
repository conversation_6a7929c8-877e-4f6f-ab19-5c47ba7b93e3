<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-content">
          <titleCrtl>
            <div style="padding: 6px 6px 6px 6px; line-height: 17px">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBLV_411d1f04`, '评标澄清') }}</div>
            <template slot="right">
              <a-button
                class="margin-r-3"
                @click="handleChangeRecord">{{ $srmI18n(`${$getLangAccount()}#i18n_field_AHtHmA_fe90a047`, '变更记录查看') }}</a-button>
              <a-button
                @click="
                  () => {
                    this.$emit('hide')
                  }
                "
              >{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button
              >
            </template>
          </titleCrtl>

          <hr />
          <div class="title">
            <span
              class="margin-r-3"
              style="font-size: 16px">{{ $srmI18n(`${$getLangAccount()}#i18n_field_QDW_2464bf0`, '问题：') }}</span>
            <span
              class="margin-r-8"
              style="font-size: 16px">{{ allData.clarificationTitle }}</span>
            <span
              class="margin-r-3"
              style="font-size: 13px; color: grey">{{ $srmI18n(`${$getLangAccount()}#i18n_field_LVyRKI_87cc05b7`, '澄清截止时间') }}</span>
            <span style="font-size: 13px; color: grey">{{ allData.fileClarificationEndTime }}</span>
          </div>
          <div class="record">
            <div style="padding: 6px 6px 6px 6px">{{ $srmI18n(`${$getLangAccount()}#i18n_field_LVCc_34586d95`, '澄清内容') }}</div>
            <div
              class="record-replyContent"
              :style="{ height: pageContentHeight }">
              <a-steps
                progress-dot
                :current="nodeData.length"
                direction="vertical">
                <!-- 采购方初始提出的问题内容渲染 -->
                <a-step>
                  <template #title="{ slotProps }">
                    <p style="font-size: 14px; font-weight: bold">{{ getSupplierTitle(allData) }}</p>
                  </template>
                  <!-- 回复框 -->
                  <template #description="{ slotProps }">
                    <div class="replyContent-item">
                      <!-- 回复内容 -->
                      <p style="padding: 6px 15px; margin-bottom: 0px; overflow-wrap: break-word">{{ allData.content }}</p>
                      <!-- 文件列表 -->
                      <div class="fileList">
                        <div
                          class="fileItem"
                          v-for="(file, index) in allData.attachmentList"
                          :key="index">
                          <a-button
                            type="link"
                            @click="downloadEvent(file)">{{ file.fileName }}</a-button>
                          <a
                            style="margin-right: 8px"
                            @click="preViewEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                          <a @click="downloadEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-step>
                <!-- 供应商回复内容 -->
                <a-step
                  v-for="item in nodeData"
                  :key="item.id">
                  <template #title="{ slotProps }">
                    <p style="font-size: 14px; font-weight: bold">{{ getTitle(item) }}</p>
                  </template>
                  <!-- 回复框 -->
                  <template #description="{ slotProps }">
                    <div class="replyContent-item">
                      <!-- 回复内容 -->
                      <p style="padding: 6px 15px; margin-bottom: 0px; overflow-wrap: break-word">{{ item.replyContent }}</p>
                      <!-- 文件列表 -->

                      <div class="fileList">
                        <div
                          class="fileItem"
                          v-for="(file, index) in item.attachmentList"
                          :key="index">
                          <a-button
                            type="link"
                            @click="downloadEvent(file)">{{ file.fileName }}</a-button>
                          <a
                            style="margin-right: 8px"
                            @click="preViewEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                          <a @click="downloadEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-step>
              </a-steps>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
    <a-modal
      v-drag    
      :visible="previewVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_changeRecord`, '变更记录')"
      :footer="null"
      @cancel="handleCancel"
      :width="800">
      <list-table
        ref="purchaseTenderProjectAttachmentInfoList"
        :statictableColumns="statictableColumns"
        setGridHeight="250"
        :fromSourceData="purchaseTenderProjectAttachmentInfoList"
        :showTablePage="false"> </list-table>
    </a-modal>
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import listTable from '../../BiddingHall/components/listTable'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        check: {
            type: Boolean,
            default: () => {
                return {}
            }
        }
    },
    components: {
        titleCrtl,
        listTable
    },
    data () {
        return {
            previewVisible: false,
            confirmLoading: false,
            labelCol: { span: 2 },
            wrapperCol: { span: 15 },
            allData: {},
            nodeData: [],
            formData: { saleAttachmentList: [] },
            rules: {},
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/saleAttachment/upload`,
            requestData: {
                detail: {
                    url: '/tender/clarification/purchaseTenderEvaClarificationHead/queryById',
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            url: {
                changeRecord: '/tender/clarification/purchaseTenderEvaClarificationHead/queryRecordByMainId'
            }
        }
    },
    computed: {
        pageContentHeight () {
            let height = document.body.clientHeight - 300
            return height + 'px'
        },
        statictableColumns () {
            let columns = [
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHL_147841e`, '变更人'),
                    fieldLabelI18nKey: '',
                    field: 'createBy',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHLVyRKI_56628ed3`, '变更澄清截止时间'),
                    fieldLabelI18nKey: '',
                    field: 'fileClarificationEndTime',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeReason`, '变更原因'),
                    fieldLabelI18nKey: '',
                    field: 'remark',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                }
            ]
            return columns
        }
    },
    methods: {
        handleCancel () {
            this.previewVisible = false
        },
        getSupplierTitle (item) {
            return `${item.createTime} ${item.realName ?? ''}`
        },
        getTitle (item) {
            return `${item.createTime}  ${item.supplierName ?? item.purchaseEnterpriseName}  ${item.realName ?? ''}`
        },
        handleChangeRecord () {
            this.confirmLoading = true
            getAction(this.url.changeRecord, { mainId: this.currentEditRow.id })
                .then((res) => {
                    if (res.success) {
                        console.log('res.result', res.result)
                        this.purchaseTenderProjectAttachmentInfoList = res.result
                        this.previewVisible = true
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        async queryDetail () {
            let url = this.requestData.detail.url
            let args = this.requestData.detail.args(this)
            console.log(args)
            // this.confirmLoading = true
            let query = await getAction(url, args)
            // this.confirmLoading = false
            console.log(query)

            if (query && query.success) {
                // querybyid接口返回的值
                this.allData = Object.assign({}, query.result)
                // 回复内容数组
                this.nodeData = this.allData.purchaseTenderEvaClarificationItemList
                console.log(213123123)
            } else {
                this.$message.error(query.message)
            }
        },
        async downloadEvent (file) {
            console.log(file, this.currentEditRow,  '======')
            const fileName = file.fileName
            file.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(file)
            this.confirmLoading=true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                this.confirmLoading=false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            this.$previewFile.open({ params: row })
        }
    },
    mounted () {
        this.queryDetail()
    }
}
</script>
<style lang="less" scoped>
.title {
    padding: 8px;
    background: #f2f3f5;
}

.margin-r-8 {
    margin-right: 8px;
    font-weight: 700;
}
.margin-r-3 {
    margin-right: 3px;
}
.record-replyContent,
.reply-Content {
    padding: 0 6px;
    overflow: auto;
    border: 1px solid #f2f3f5;
}
.replyContent-item {
    border: 1px solid #f2f3f5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
:deep(.ant-steps-dot .ant-steps-item-content ){
    width: 90%;
}
:deep(.ant-form-item ){
    margin-bottom: 0px;
}
.fileList {
    display: flex;
    flex-wrap: wrap;
}
.fileItem {
    flex-basis: 33%;
}
</style>
