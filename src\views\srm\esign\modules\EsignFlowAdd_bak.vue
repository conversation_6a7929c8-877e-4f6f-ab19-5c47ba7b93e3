<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction } from '@/api/manage'
export default {
    name: 'EsignFlowAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        // const _self = this
        return {
            selectType: 'esignFlow',
            pageData: {
                form: {
                    busType: null,
                    busNumber: null,
                    businessScene: null,
                    relationId: null,
                    filesName: null,
                    filesId: null,
                    uploaded: null,
                    autoArchiving: null,
                    autoInitiate: null,
                    remark: null,
                    cutOffTime: null,
                    contractRemind: null,
                    noticeType: null,
                    effectiveTime: null
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'busType',
                                    dictCode: 'electronicSignatureBusType',
                                    disabled: false,
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    defaultValue: 'contract'
                                    // ,
                                    // bindFunction: function (parentRef, pageData, groupData, realValue){     
                                    //     _self.updateBusinessNumberExtendModel(realValue)
                                    // }
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderNo`, '业务单号'),
                                    fieldName: 'busNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderNo`, '业务单号')
                                    // ,
                                    // extend: null,
                                    // bindFunction: function (Vue, data){     
                                    //     // 竞价        
                                    //     if(Vue.form.businessType==='1'){
                                    //         Vue.form.businessNumber = data[0].ebiddingNumber
                                    //         Vue.form.businessName = data[0].ebiddingDesc
                                    //         Vue.form.businessId = data[0].id
                                    //     }     
                                    //     //询价                           
                                    //     if(Vue.form.businessType==='2'){
                                    //         Vue.form.businessNumber = data[0].enquiryNumber
                                    //         Vue.form.businessName = data[0].enquiryDesc
                                    //         Vue.form.businessId = data[0].id
                                    //     }  
                                    //     //招标
                                    //     if(Vue.form.businessType==='3'){
                                    //         Vue.form.businessNumber = data[0].biddingNumber
                                    //         Vue.form.businessName = data[0].biddingDesc
                                    //         Vue.form.businessId = data[0].id
                                    //     }  
                                    // }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessAssociationID`, '业务关联id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                                    fieldName: 'filesName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'),
                                    fieldName: 'filesId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkisSuccessfullyUploaded`, '文件是否上传成功'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_autoArchiving`, '是否自动归档'),
                                    fieldName: 'autoArchiving',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheProcessIsAutomaticallyStarted`, '流程是否自动开启'),
                                    fieldName: 'autoInitiate',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationDateSigning`, '签署有效截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: '文件到期前多少时间提醒(小时)',
                                    fieldName: 'contractRemind'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationdateDocument`, '文件有效截止时间'),
                                    fieldName: 'effectiveTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                busType: [{required: true, message: '业务类型不能为空'}],
                                busNumber: [{required: true, message: '业务单号不能为空', trigger: 'change'}],
                                businessScene: [{required: true, message: '文件主题不能为空'}]
                            }
                        }
                        
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/bidding/elsEsign/add'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        // 更新业务单据号
        updateBusinessNumberExtendModel (type) {
            let selectIndex = type?parseInt(type)-1: 0
            this.pageData.groups[0].custom.formFields[2].extend= Object.assign({}, this.pageData.groups[0].custom.formFields[2], this.businessNumberExtendArray[selectIndex])
        },
        init () {},
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.add
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.goBack()
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>