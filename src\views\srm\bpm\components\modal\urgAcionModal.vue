<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n__HrKdBD_5b6abfb1`, '催办事项标题')"
          required
          prop="urgMatterTitle">
          <a-input
            v-model="form.urgMatterTitle"
          />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n__HrKdCc_5b67c914`, '催办事项内容')"
          required
          prop="urgMatter">
          <a-input
            v-model="form.urgMatter"
          />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { addUrg } from '../../api/analy.js'
export default {
    mixins: [modalMixins],
    data () {
        return {
            rules: {
                urgMatterTitle: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n__HrKdBD_5b6abfb1`, '催办事项标题'), trigger: 'change' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                urgMatter: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n__HrKdCc_5b67c914`, '催办事项内容'), trigger: 'change' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ]
            },
            singleUserData: {},
            labelCol: { span: 6 }
        }
    },
    methods: {
        handleConfirm () {
            this.cheackValidate().then(() => {
                this.loading = true
                let parmas = {
                    ...this.form,
                    processInstanceId: this.processInstanceId
                }
                addUrg(parmas).then(res => {
                    this.loading = false
                    if (res.code == 0) {
                        this.$message.success(res.message)
                        this.$emit('success')
                    } else {
                        this.$message.error(res.msg)
                    }
                }).finally(() => {
                    this.loading = false
                    this.$emit('closeLoading')
                })
            })
        }
    },
    created () {}
}
</script>
