<template>
  <div class="SignUpManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="showLayOut"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :queryData="queryData"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction, getAction} from '@/api/manage'
// import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    name: 'SignUpManagerEdit',
    props: {
        currentEditRow: {
            default: () => {
                return {}
            },
            type: Object
        },
        propOfCheckType: {
            default: () => {
                return ''
            },
            type: [String, Number]
        }
    },
    components: {
        BusinessLayout,
        EditGridLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    data () {
        return {
            showLayOut: false,
            pageStatus: 'edit',
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            requestData: {
                detail: { 
                    url: '/tender/purchase/supplierTenderProjectSignUp/queryById',
                    args: (that) => {
                        return { 
                            id: that.currentEditRow.id
                        }
                    },
                    config: () => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {xNodeId: `${this.propOfCheckType || ''}__`}
                        }
                    }
                }
            },
            externalToolBar: {
                attachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            disabledItemNumber: true,
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: 'SupplierTenderProjectSignUp', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/purchase/supplierTenderProjectSignUp/edit'
                    },
                    config: () => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {xNodeId: `${this.propOfCheckType || ''}__`}
                        }
                    },
                    // show: this.syncShow, // 同步校验显示方法
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save',
                    showMessage: true,
                    // click: this.handleSave,
                    handleBefore: this.handleSubmitBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/tender/purchase/supplierTenderProjectSignUp/submit'
                    },
                    config: () => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {xNodeId: `${this.propOfCheckType || ''}__`}
                        }
                    },
                    // show: this.syncShow, // 同步校验显示方法
                    attrs: {
                        type: 'primary'
                    },
                    // key: 'submit',
                    click: this.submitEvent,
                    showMessage: true,
                    handleBefore: this.handleSubmitBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            attachmentListData: {},
            url: {

            },
            userInfo: {},
            projectObj: {},
            queryData: {},
            remoteJsFilePath: ''
        }
    },
    computed: {
        // remoteJsFilePath () {
        //     console.log('editrow', this.currentEditRow)
        //     let templateNumber = this.currentEditRow.templateNumber
        //     let templateVersion = this.currentEditRow.templateVersion
        //     let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
        //     return `${account}/sale_SupplierTenderProjectSignUp_${templateNumber}_${templateVersion}`
        // },
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    created () {
        this.queryData = this.$route.query
        // if tenderType = 0（邀请） noticeType = 0
        // else  if checkType = 1 noticeType = 2
        // else noticeType = 1
        let noticeType = ''
        let { checkType, tenderType } = this.subPackageRow
        if(tenderType == '0') {
            noticeType = '0'
        }else if(checkType == '1') {
            noticeType = '2'
        }else{
            noticeType = '1'
        }

        if (JSON.stringify(this.queryData) != '{}') {
            this.userInfo = this.$ls.get(USER_INFO)
        }
        getAction('/tender/purchaseTenderNoticeHead/queryBySubpackageId', {subpackageId: this.subId, noticeType}, {headers: {xNodeId: `${this.propOfCheckType || ''}__`}}).then(res => {
            if(res.success) {
                this.consortiumBidding=res.result.consortiumBidding
                this.noticeId=res.result.id
                // this.showLayOut = true
                console.log('msg:', res.result)
            }
        })
        this.confirmLoading = true
        getAction('/tender/purchaseTenderProjectHead/queryProjectInfo', {subpackageId: this.subId}, {headers: {xNodeId: `${this.propOfCheckType || ''}__`}}).then(res => {
            if(res.success) {
                this.showLayOut = true
                //拿disable的默认数据
                this.tenderProjectNumber=res.result.tenderProjectNumber
                this.tenderProjectName=res.result.tenderProjectName
                this.subpackageName=res.result.subpackageName
                console.log('resres', res.result)
            }else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFuKNSVXVEL_5fafea62`, '数据加载错误，请刷新页面'))
            }
            this.confirmLoading = false
        })
        console.log('this.currentEditRow', this.currentEditRow.id)
        
    },
    mounted () {
        if (!this.currentEditRow.id) {
            this.getBusinessTemplate()
        } else {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            this.remoteJsFilePath =  `${account}/sale_SupplierTenderProjectSignUp_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        // handleSave (){
        //     let url = '/purchase/supplierTenderProjectSignUp/add'
        //     postAction(url+'?id='+this.subId).then(res => {
        //         if (res.success) {
        //             this.$message.success(res.message)
        //             // this.$emit('hide')
        //         }
        //     }).finally(() => {
        //         this.confirmLoading = false
        //     })
        // },
        // 获取业务模板信息
        attrHandle () {
            return {
                'sourceNumber': this.tenderCurrentRow.tenderProjectNumber|| this.tenderCurrentRow.id || '',
                'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'
            }
        },
        getBusinessTemplate () {
            let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'SupplierTenderProjectSignUp'}
            this.confirmLoading = true
            var that = this
            getAction('/template/templateHead/getListByType', params, {headers: {xNodeId: `${that.propOfCheckType || ''}__`}}).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                templateNumber: item.templateNumber,
                                templateName: item.templateName,
                                templateVersion: item.templateVersion,
                                templateAccount: item.elsAccount
                            }
                        })
                        that.currentEditRow = options[0]
                        that.remoteJsFilePath = `${that.currentEditRow['templateAccount']}/purchase_SupplierTenderProjectSignUp_${that.currentEditRow['templateNumber']}_${that.currentEditRow['templateVersion']}`
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
                this.confirmLoading = false
            })
        },
        submitEvent () {
            let allData = this.getAllData()
            allData = Object.assign(this.currentEditRow, allData)
            if (Object.keys(this.attachmentListData).length == 0){
                allData.subpackageId=this.subId
                allData.tenderProjectName=this.tenderCurrentRow.tenderProjectName
                allData.tenderProjectNumber=this.tenderCurrentRow.tenderProjectNumber
                allData.noticeId=this.noticeId
            }
            if(!allData.id){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }
            let flag = false
            // 文件类型必填
            if(allData.attachmentList && allData.attachmentList.length != 0){
                allData.attachmentList.forEach(item=>{
                    if(!item.fileType){
                        flag = true
                    }
                })
                if(flag) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_chooseFileType`, '请选择文件类型'))
                    this.confirmLoading = false
                    return 
                }
            }
            this.confirmLoading = true
            postAction('/tender/purchase/supplierTenderProjectSignUp/submit', allData).then(res=>{
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.$emit('hide')
                }
            }).finally(()=>{
                this.confirmLoading=false
            })
            console.log(allData)
        },
        handleSubmitBefore (args) {
            console.log('args', args)
            var that = this
            return new Promise((resolve) => {
                let allData = args.allData
                allData = Object.assign(this.currentEditRow, allData)
                if(!allData.supplierName){
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMeBtLRL_9963d032`, '请填写投标单位名称'))
                    this.confirmLoading = false
                    return
                }
                if (Object.keys(that.attachmentListData).length == 0){
                    allData.subpackageId=that.subId
                    allData.tenderProjectName=this.tenderCurrentRow.tenderProjectName
                    allData.tenderProjectNumber=this.tenderCurrentRow.tenderProjectNumber
                    allData.noticeId=that.noticeId
                }
                let flag = false
                // 文件类型必填
                if(allData.attachmentList && allData.attachmentList.length != 0){
                    allData.attachmentList.forEach(item=>{
                        if(!item.fileType){
                            flag = true
                        }
                    })
                    if(flag) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_chooseFileType`, '请选择文件类型'))
                        this.confirmLoading = false
                        return 
                    }
                }

                let params = {
                    allData: allData
                }
                args = Object.assign({}, args, params)
                resolve(args)
            })
        },
        handleSubmitAfter ( args ){
            console.log(args)
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        async handleAfterDealSource (pageConfig, resultData) {
            console.log('pageConfig', pageConfig)
            //拿disable的默认数据
            this.currentEditRow.tenderProjectNumber=this.tenderProjectNumber
            this.currentEditRow.tenderProjectName=this.tenderProjectName
            this.currentEditRow.subpackageName=this.subpackageName
            console.log('this.currentEditRow', this.currentEditRow)
            if(this.consortiumBidding == '1'){
                pageConfig.groups[0].formModel.combination='1'
            }else{
                pageConfig.groups[0].formModel.combination='0'
                pageConfig.groups[0].formModel.combinationName=''
            }
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }

            let flag = (this.consortiumBidding !== '1')
            let nameFlag = (pageConfig.groups[0].formModel.combination=='0')
            setDisabledByProp('combination', flag)
            setDisabledByProp('combinationName', nameFlag)

            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }

                rule[prop] = [{
                    required: flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            //此处写根据分包信息作为判断展示位
            let validateFlag = (this.consortiumBidding == '1')
            let nameValidateFlag = (pageConfig.groups[0].formModel.combination == '0')
            setValidateRuleByProp('combination', validateFlag)
            setValidateRuleByProp('combinationName', nameValidateFlag)


            pageConfig.groups[1]['extend'] = {
                optColumnList: [
                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                    { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                ]
            }
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
                if (key == 'supplierName') {
                    formModel[key] = resultData[key] || this.$lt.get(USER_COMPANYSET).companyName
                }
                if (key == 'toElsAccount') {
                    formModel[key] = resultData[key] || this.userInfo.elsAccount
                }
            }

            // 判断是否有 单位名称，没有就取用户信息里面的
            // pageConfig.groups[0].formModel.supplierName = pageConfig.groups[0].formModel.supplierName || this.userInfo.enterpriseName

            const purchaseAttachmentLists = ['attachmentList']
            let that = this
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))

            purchaseAttachmentLists.forEach(rs => {
                if (that.externalToolBar[rs].length) {
                    that.externalToolBar[rs][0].args.headId = resultData.id || this.queryData.subpackageId || ''
                    that.externalToolBar[rs][0].args.itemInfo = itemInfo
                }
            })

            // pageConfig.groups.forEach((rs) => {
            //     if (rs.groupCode == 'attachmentList') {
            //         rs.show = false
            //         rs.verify = true
            //         this.attachmentListData = rs
            //     }
            // })
            // console.log(this.attachmentListData)
        },
        checkItemSelectOk (data) {
        },
        preViewEvent (Vue, row){
            row.subpackageId = this.subId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading=true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                this.confirmLoading=false
            })
        },
        uploadCallBack (result, ref) {
            let fileGrid = this.getItemGridRef(ref)
            console.log(result)
            // 避免上传附件后回显filetype=5 与其dicttext对应不上
            result.forEach(item=>{
                item.fileType = ''
            })
            fileGrid.insertAt(result, -1)
        }
        // uploadCallBack (result, ref) {
        //     let gridRef = `${ref}grid`
        //     let fileGrid = this.$refs[gridRef].$refs[ref]
        //     fileGrid.insertAt(result, -1)
        // },
        // getItemGridRef (groupCode) {
        //     let gridRef = `${groupCode}grid`
        //     return this.$refs[gridRef].$refs[groupCode]
        // }
    }
}
</script>

<style lang="less" scoped>
.registration-title{
    background-color: #eee;
    padding: 5px 10px;
    margin-bottom: 10px;
}
:deep(.page-container .edit-page .page-content){
    flex: auto;
}
</style>


