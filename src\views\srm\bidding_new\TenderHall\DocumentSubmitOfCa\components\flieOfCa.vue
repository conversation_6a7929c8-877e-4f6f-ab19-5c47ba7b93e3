<template>
  <div>
    <BaseForm
      :fromSourceData="fromSourceData"
      :pageStatus="pageStatus" />
    <EncryptDocuments
      ref="EncryptDocuments"
      :fromSourceData="fromSourceData"
      :pageStatus="pageStatus" />
  </div>
</template>
<script>
import BaseForm from './modules/baseForm'
import EncryptDocuments from './modules/encryptDocuments'
export default {
    props: {
        fromSourceData: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: 'edit',
            type: String
        }
    },
    data () {
        return {}
    },
    components: {
        BaseForm,
        EncryptDocuments
    },
    methods: {
        getEncryptDocumentsData () {
            let p = this.$refs.EncryptDocuments.getParamsData()
            return p
        },
        getValidatePromise () {
            return this.$refs.EncryptDocuments.getValidate()
        }
    }
}
</script>
