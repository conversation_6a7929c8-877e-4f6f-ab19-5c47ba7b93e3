<template>
  <div
    class="page"
    style="margin-top: 4px;">
    <div class="table-page-search-wrapper">
      <a-spin :spinning="confirmLoading">
        <a-page-header
          class="fixed-page-header"
          :title="title"
        >
          <template slot="extra">
            <a-button
              @click="handleOk"
              type="primary"
              key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
            <a-button
              @click="goBack"
              key="3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </template>
        </a-page-header>
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            key="1"
            :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
          >
            <a-form
              layout="inline"
              :form="form"
            >
              <a-row>
                <a-col :span="6">
                  <a-form-item
                    :label-col="labelCol"
                    label="模板编号">
                    <a-input
                      style="width: 220px"
                      v-decorator="['templateNumber', validatorRules.templateNumber]"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item
                    :label-col="labelCol"
                    label="物料编码">
                    <a-input
                      style="width: 220px"
                      @click="selectMaterialMaster"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_clickSelectMaterialCode`, '请点击选择物料编码')"
                      v-decorator="['materialNumber', validatorRules.materialNumber]"/>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item
                    :label-col="labelCol"
                    label="物料描述">
                    <a-input
                      :disabled="true"
                      style="width: 220px"
                      v-decorator="['materialDesc']"/>
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item
                    :label-col="labelCol"
                    label="物料分类">
                    <a-input
                      :disabled="true"
                      style="width: 220px"
                      v-decorator="['cateCode']"/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :span="12">
                  <a-form-item
                    :label-col="{
                      xs: { span: 6 },
                      sm: { span: 4 }
                    }"
                    label="备注">
                    <a-input
                      v-decorator="['remark', validatorRules.remark]"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-collapse-panel>

          <!-- table区域-begin -->
          <a-collapse-panel
            key="2"
            header="行信息"
          >
            <div class="els-table-box">
              <div class="els-alert-info">
                <!-- 操作按钮区域 -->
                <div class="table-operator">
                  <a-button
                    @click="handleAdd"
                    type="primary"
                    icon="plus"
                  >
                    {{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}
                  </a-button>
                </div>
              </div>
              <a-table
                :columns="columns"
                size="small"
                :pagination="false"
                :data-source="dataSource"
                :loading="loading"
              >
                <!-- :row-selection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" -->
                <span
                  slot="action"
                  slot-scope="text, record"
                >
                  <a @click="handleEdit(record)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑') }}</a>
                  <a-divider type="vertical" />
                  <a
                    href="javascript:;"
                    @click="handleAddSub(record)"
                  >{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '添加') }}</a>

                  <a-divider type="vertical" />
                  <a-popconfirm
                    :title="$srmI18n(`${$getLangAccount()}#i18n_title_deleteTips`, '确定删除吗?')"
                    @confirm="() => handleDelete(record)"
                  >
                    <a>{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a>
                  </a-popconfirm>
                </span>
                <!-- 字符串超长截取省略号显示 -->
                <span
                  slot="url"
                  slot-scope="text"
                >
                  <j-ellipsis
                    :value="text"
                    :length="25"
                  />
                </span>
                <!-- 字符串超长截取省略号显示-->
                <span
                  slot="component"
                  slot-scope="text"
                >
                  <j-ellipsis :value="text" />
                </span>
              </a-table>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </div>
    <!-- table区域-end -->
    <!-- 添加行信息 -->
    <cost-item-modal
      ref="modalForm"
      @ok="modalFormOk"
    />
    <!-- 物料弹窗 -->
    <select-modal
      ref="MaterialMasterList"
      :url="url.queryMaterialMasterList"
      :columns="selectMaterialMasterColumns"
      :title="selectMaterialMasterTitle"
      @ok="selectMaterialMasterOk"/>
  </div>
</template>

<script>
import CostItemModal from './CostItemModal'
import selectModal from '@comp/selectModal/selectModal'
import JEllipsis from '@/components/els/JEllipsis'
import { srmI18n, getLangAccount } from '@/utils/util'
import pick from 'lodash.pick'
import { getAction, postAction } from '@/api/manage'

const columns = [
    {
        title: '成本编码',
        dataIndex: 'costCode',
        key: 'costCode'
    },
    {
        title: '成本名称',
        dataIndex: 'costName',
        key: 'costName'
    },
    {
        title: '父级编码',
        dataIndex: 'parentCode',
        key: 'parentCode'
    },
    {
        title: '父级名称',
        dataIndex: 'parentName',
        key: 'parentName',
        scopedSlots: { customRender: 'parentName' }
    },
    {
        title: srmI18n(`${getLangAccount()}#i18n_title_sort`, '排序'),
        dataIndex: 'sort',
        key: 'sort'
    },
    {
        title: '计算公式',
        dataIndex: 'formula',
        key: 'formula'
    },
    {
        title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
        dataIndex: 'action',
        scopedSlots: { customRender: 'action' },
        align: 'center',
        width: 150
    }
]

export default {
    name: 'CostHeadEdit',
    mixins: [],
    components: {
        CostItemModal,
        selectModal,
        JEllipsis
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            title: '成本报价模板',
            confirmLoading: false,
            activeKey: ['1', '2'],
            description: srmI18n(`${getLangAccount()}#i18n_title_isMenuManagementPage`, '这是菜单管理页面'),
            form: this.$form.createForm(this),
            dataSource: [], // 树形结构数据
            purchaseCostItemList: [], // 平铺
            model: {},
            // 表头
            columns: columns,
            loading: false,
            validatorRules: {
                templateNumber: {rules: [{ required: true, message: '请输入成本编码' }]},
                materialNumber: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectMaterialTips`, '请选择物料!') }]}
            },
            selectMaterialMasterTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectMaterialTitle`, '选择物料'),
            selectMaterialMasterColumns: [
                { type: 'checkbox', width: 40, align: 'center'},
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),  align: 'center'},
                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), align: 'center'},
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), align: 'center'},
                { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),  align: 'center'}           
            ],
            url: {
                queryMaterialMasterList: '/material/purchaseMaterialHead/list',
                add: '/cost/purchaseCostHead/add',
                edit: '/cost/purchaseCostHead/edit',
                detail: '/cost/purchaseCostHead/queryById',
                delete: '/account/permission/delete',
                deleteBatch: '/account/permission/deleteBatch'
            }
        }
    },
    watch: {
        currentEditRow: {
            deep: true,
            immediate: true,
            handler (newVal) {
                if (newVal.id) this.loadData()
            }
        }
    },
    created () {
    },
    methods: {
        handleDelete (record) {
            const index = this.purchaseCostItemList.findIndex(i => i.costCode === record.costCode)
            this.purchaseCostItemList.splice(index, 1)
            // 删除子元素
            this.deleteChildren(record)

            this.dataSource = this.translateDataToTree(JSON.parse(JSON.stringify(this.purchaseCostItemList)))
        },
        deleteChildren (record) {
            const childIndex = this.purchaseCostItemList.findIndex(i => i.parentCode === record.costCode)
            if (childIndex === -1) return
            if (childIndex !== -1) {
                this.purchaseCostItemList.splice(childIndex, 1)
                this.deleteChildren(record)
            }
        },

        loadData () {
            getAction(this.url.detail, {id: this.currentEditRow.id}).then((res) => {
                if (res && res.success) {
                    this.form.resetFields()
                    const { templateNumber, materialNumber, remark, materialDesc, cateCode } = res.result
                    const form = { templateNumber, materialNumber, remark, materialDesc, cateCode }
                    this.form.setFieldsValue(form)
                    this.model = res.result
                    this.dataSource = this.translateDataToTree(JSON.parse(JSON.stringify(res.result.purchaseCostItemList)))
                    this.purchaseCostItemList = JSON.parse(JSON.stringify(res.result.purchaseCostItemList))
                }
            })
        },
        translateDataToTree (data) {
            // 没有父节点的数据
            let parents = data.filter(value => value.parentCode == 'undefined' || value.parentCode == null)
          
            // 有父节点的数据
            let children = data.filter(value => value.parentCode !== 'undefined' && value.parentCode != null)
          
            // 定义转换方法的具体实现
            let translator = (parents, children) => {
                //遍历父节点数据
                parents.forEach((parent) => {
                // 遍历子节点数据
                    children.forEach((current, index) => {
                        // 此时找到父节点对应的一个子节点
                        if (current.parentCode === parent.costCode) {
                            // 对子节点数据进行深复制，这里只支持部分类型的数据深复制，对深复制不了解的童靴可以先去了解下深复制
                            let temp = JSON.parse(JSON.stringify(children))
                            // 让当前子节点从temp中移除，temp作为新的子节点数据，这里是为了让递归时，子节点的遍历次数更少，如果父子关系的层级越多，越有利
                            temp.splice(index, 1)
                            // 让当前子节点作为唯一的父节点，去递归查找其对应的子节点
                            translator([current], temp)
                            // 把找到子节点放入父节点的children属性中
                            typeof parent.children !== 'undefined' ? parent.children.push(current) : parent.children = [current]
                        }
                    })
                })
            }
            // 调用转换方法
            translator(parents, children)
            return parents
        },

        modalFormOk (formData, type) {
            if (type === 'new') {
                const index = this.purchaseCostItemList.findIndex(i => i.costCode === formData.costCode)
                if (index !== -1) {
                    this.$message.warning('成本编码重复')
                    return
                }
                this.purchaseCostItemList.push(formData)
            } else if (type === 'edit') {
                const data = JSON.parse(JSON.stringify(this.purchaseCostItemList))
                data.forEach((i, index) => {
                    if (i.costCode === formData.costCode) {
                        data[index].costCode =  formData.costCode
                        data[index].costName =  formData.costName
                        data[index].parentCode =  formData.parentCode
                        data[index].parentName =  formData.parentName
                        data[index].sort =  formData.sort
                        data[index].formula =  formData.formula
                    }
                })
                this.purchaseCostItemList = JSON.parse(JSON.stringify(data))
            }
            const data = JSON.parse(JSON.stringify(this.purchaseCostItemList))
            this.dataSource = this.translateDataToTree(data)
            this.$refs.modalForm.close()
        },
        handleAddSub (record) {
            this.$refs.modalForm.title = '添加子成本'
            this.$refs.modalForm.edit({parentCode: record.costCode, parentName: record.costName}, 'new')
        },
        handleAdd (record) {
            this.$refs.modalForm.title = '新增成本'
            this.$refs.modalForm.add(record, 'new')
        },
        handleEdit (record) {
            this.$refs.modalForm.title = '编辑成本'
            const currentRecord = {
                costCode: record.costCode,
                costName: record.costName,
                parentCode: record.parentCode,
                parentName: record.parentName,
                sort: record.sort,
                formula: record.formula
            }
            this.$refs.modalForm.edit(currentRecord, 'edit')
        },
        selectMaterialMasterOk (data){
            let selectResult =  {'materialNumber': data[0].materialNumber,
                'materialDesc': data[0].materialDesc, 
                'materialName': data[0].materialName,
                'materialGroup': data[0].materialGroupCode,
                'cateCode': data[0].cateCode
            }
            this.model = { ...this.model, ...selectResult }
            this.form.setFieldsValue(pick(selectResult, 'materialNumber', 'materialDesc', 'cateCode'))
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    this.confirmLoading = true
                    const params = { ...this.currentEditRow, ...values, ...this.model }
                    params.purchaseCostItemList = this.purchaseCostItemList
                    const url = this.currentEditRow.id ? that.url.edit : that.url.add
                    postAction(url, params).then(res => {
                        if (res.success) {
                            this.$message.success(res.message)
                            this.$emit('hide')
                        } else {
                            this.$message.error(res.message)
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            })
        },
        selectMaterialMaster (){
            this.$refs.MaterialMasterList.open()
        },
        goBack () {
            this.$emit('hide')
        },
        showHelpBtn (row) {
            const types = [0, 1]
            if (!types.includes(row.menuType) ) return false
            if (row.menuType !== 1 && row.children && row.children.length) return false
            return true
        },
        toHelpCenter ({ companyMeunId, title }) {
            this.$router.replace({
                name: 'HelpCenter',
                query: {
                    id: companyMeunId,
                    title,
                    isCompany: true
                }
            })
        }
    }
}
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>