<template>
  <span>
    <!-- <router-link
      target="_blank"
      @click.stop="$router.push({path: '/questionnaire/observe'})"
      style="margin-right: 20px;">预览</router-link> -->
    <a
      @click.stop="previewHandle"
      style="margin-right: 20px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
    <a-modal
    v-drag    
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
      :visible="visible"
      @ok="onView"
      @cancel="visible = false"
      :centered="true"
      :width="750"
      :footer="null"
      destroyOnClose
    >
      <my-view
        :formHd="formHd"
        :initData="initData"
        :isPre="isPre"></my-view>

    </a-modal> 
  </span>
</template>

<script>
import FormRender from '../FormRender'
import { mapState, mapActions } from 'vuex'
import { deepClone } from '../../utils'
import { formRenderData } from '../../utils/core'
import myView from '../../view.vue'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    name: 'AddPatternRule',
    props: ['formHd'],
    inject: ['initData'],
    components: {
        FormRender,
        myView
    },
    data () {
        return {
            form: this.$form.createForm(this),
            visible: false,
            resultData: null,
            isPre: 1,
            myPatterns: {
                uppercase: {
                    regexp: /^[A-Z]+$/,
                    message: srmI18n(`${getLangAccount()}#i18n_title_fillInNonCapitalLetters`, '填入内容非大写字母！')
                },
                abc: {
                    regexp: /abc/,
                    message: srmI18n(`${getLangAccount()}#i18n_title_fillInNonCapitalLetters`, '填入内容非大写字母！')
                }
            }
        }
    },
    computed: mapState({
        renderData: state => state.formDesigner.renderData,
        formData: state => state.formDesigner.formData,
        patterns: state => state.formDesigner.patterns
    }),
    watch: {
        visible (val) {
            if (val) {
                let formData = deepClone(this.formData)
                formData = formRenderData(formData)
                this.resultData = formData
                console.log(this.resultData)
            }
        }
    },
    methods: {
        previewHandle () {
            this.visible = true
        },
        onView () {
            console.log('view')
        },
        getClick (error, values, index) {
            console.log(values)
        },
        ...mapActions({
            updateRenderData: 'updateRenderData'
        })
    }
}
</script>

<style lang="less" scoped>

</style>
