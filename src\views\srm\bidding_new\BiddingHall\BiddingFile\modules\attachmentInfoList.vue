<template>
  <div>
    <list-table
      ref="purchaseTenderProjectAttachmentInfoList"
      :groupCode="groupCode"
      :externalToolBar="externalToolBar"
      :statictableColumns="statictableColumns"
      :pageData="pageData"
      setGridHeight="500"
      :fromSourceData="purchaseTenderProjectAttachmentInfoList"
      :showTablePage="false"
    >
    </list-table>
  </div>
</template>
<script>
import listTable from '../../components/listTable'
import {getAction} from '@/api/manage'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    mixins: [baseMixins],
    props: {
        groupCode: {
            default: '',
            type: String
        },
        fromSourceData: {
            default: () => {},
            type: Object
        },
        pageStatus: {
            default: 'edit',
            type: String 
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subPackageRow () {
            return this.currentSubPackage()
        },
        statictableColumns () {
            let attachmentFileType = this.checkType == '0' ? 'preAttachmentFileType' : 'attachmentFileType'
            let columns = [
                { type: 'checkbox', width: 40, fixed: 'left' }, 
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型')),
                    fieldLabelI18nKey: '',
                    field: 'fileType',
                    fieldType: 'select',
                    dictCode: attachmentFileType,
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称')),
                    fieldLabelI18nKey: '',
                    field: 'fileName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称'),
                    fieldLabelI18nKey: '',
                    field: 'subpackageName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QIlu_2f59e218`, '文件售价')),
                    fieldLabelI18nKey: '',
                    fieldType: 'number',
                    field: 'saleAmount',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    fixed: 'right',
                    groupCode: 'purchaseTenderProjectAttachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    field: 'title',
                    width: 100,
                    slots: { default: 'grid_opration' }
                }
            ]
            return columns
        } 
        
    },
    components: {
        listTable
    },
    data () {
        return {
            purchaseTenderProjectAttachmentInfoList: [],
            externalToolBar: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                    key: 'upload',
                    args: {
                        property: 'label', // 可省略
                        itemInfo: [], // 必传
                        disabledItemNumber: true,
                        action: '/attachment/purchaseAttachment/upload', // 必传
                        businessType: 'biddingPlatform', // 必传,
                        headId: '', // 必传
                        modalVisible: false // 必传
                    },
                    attr: this.attrHandle,
                    callBack: this.uploadCallBack
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete',
                    click: this.businessGridDelete
                }
            ],
            pageData: {
                optColumnList: [
                    {
                        key: 'download',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                        clickFn: this.downloadEvent
                    },
                    {
                        key: 'preView',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        clickFn: this.preViewEvent
                    }
                ]
            }
        }
    },
    methods: {
        attrHandle () {
            return {
                'sourceNumber': this.tenderCurrentRow.tenderProjectNumber || this.tenderCurrentRow.id || '',
                'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'
            }
        },
        preViewEvent (row) {
            row.subpackageId = this.subPackageRow.id
            let preViewFile = {}
            if (row && typeof row == 'string' ) {
                preViewFile = JSON.parse(row) || ''
            } else {
                preViewFile = row
            }
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (row, config) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.subPackageRow.id
            let {message: url} = await getAttachmentUrl(row)
            this.$refs.purchaseTenderProjectAttachmentInfoList.loading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.$refs.purchaseTenderProjectAttachmentInfoList.loading = false
            })
        },
        uploadCallBack (result) {
            // 插入分包ID分包名称项目ID项目名称
            result.map(item => {
                item['fileType'] = '2'
                item['subpackageId'] = this.subPackageRow.id
                item['subpackageName'] = this.subPackageRow.subpackageName
                item['tenderProjectId'] = this.tenderCurrentRow.id
                item['tenderProjectName'] = this.tenderCurrentRow.tenderProjectName
                item['saleAmount'] = 0
            })
            this.$refs['purchaseTenderProjectAttachmentInfoList'].insertAt(result, -1)
        },
        businessGridDelete () {
            this.$refs['purchaseTenderProjectAttachmentInfoList'].businessGridDelete()
        },
        externalAllData () {
            let {fullData} = this.$refs['purchaseTenderProjectAttachmentInfoList'].getTableData()
            return fullData
        },
        getValidatePromise () {
            return new Promise(async (resolve, reject) => {
                let flag = await this.$refs['purchaseTenderProjectAttachmentInfoList'].getValidate() || true
                let {fullData} = this.$refs['purchaseTenderProjectAttachmentInfoList'].getTableData()
                let fileTypeFlag = fullData.filter(item => (item.fileType == '0' || item.fileType == '1' )).length != 1
                if (fileTypeFlag) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QIAcjFRjImYBQI_a554578e`, '文件类型有且只有一个招标文件'))
                }
                if (!flag || fileTypeFlag) {
                    reject({
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMMi_ddcaceed`, '请填写完整'),
                        validateStatus: 'error',
                        currentStep: 3
                    }) 
                }
                resolve(true)
            })
        },
        init (data) {
            this.externalToolBar[0].args.headId = data? data.id : ''
            this.purchaseTenderProjectAttachmentInfoList = data.purchaseTenderProjectAttachmentInfoList || []
            if (['1', '2'].includes(data.status) || this.pageStatus == 'detail') this.externalToolBar = []
        }
    },
    watch: {
        fromSourceData: {
            immediate: true,
            handler (val) {
                this.externalToolBar[0].args.headId = val? val.id : ''
            }
        }
    },
    mounted () {
        this.purchaseTenderProjectAttachmentInfoList = this.fromSourceData.purchaseTenderProjectAttachmentInfoList || []
        if (['1', '2'].includes(this.fromSourceData.status) || this.pageStatus == 'detail') this.externalToolBar = []
    }
}
</script>
<style lang="less" scoped>

</style>