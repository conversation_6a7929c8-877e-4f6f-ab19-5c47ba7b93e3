<template>
  <div class="steps-content-warp step3">

    <div class="formWrap">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        v-bind="formItemLayout">
        <a-row>
          <a-col :span="12">
            <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_notAnnouncementLetter`, '是否发布未中标通知书')">
              <m-switch v-model="form.bidFailNotification" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item
          :wrapper-col="{ span: 24 }"
          prop="bidFailNotificationContent">
          <div class="editor">
            <j-editor
              ref="editor"
              v-model="form.bidFailNotificationContent" />
          </div>
        </a-form-model-item>
        <a-row>
          <a-col :span="12">
            <a-form-model-item
              :label-col="{ span: 0 }">
              <custom-upload
                :disabledItemNumber="true"
                :visible.sync="upload.modalVisible"
                :title="upload.title"
                :action="upload.action"
                :accept="upload.accept"
                :headers="upload.tokenHeader"
                :data="extraData"
                @change="handleUploadChange"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>

      <div class="attachment">
        <ul>
          <template v-for="file in form.attachment__$$__3">
            <li
              :key="file.id"
              class="file">
              <a
                href="javascript:void(0)"
                @click="handleDownload(file)"
              >
                {{ file.fileName }}
              </a>
              <a-icon
                @click="handleDel(file)"
                type="delete"
                class="icon del"
              />
            </li>
          </template>
        </ul>
      </div>
    </div>
    
  </div>
</template>

<script>
import JEditor from '@/components/els/JEditor'
import CustomUpload from '@comp/template/CustomUpload'
import { getAction } from '@/api/manage'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        JEditor,
        CustomUpload
    },
    props: {
        parentData: {
            type: Object,
            default () {
                return {}
            }
        },
        templateContent: {
            type: String,
            default: ''
        },
        current: {
            type: Number,
            default: 0
        }
    },
    data () {
        return {
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 14 }
            },
            form: {
                bidFailNotification: '0', // 是否发布未中标通知书，0：否、1：是
                bidFailNotificationContent: '' // 未中标通知书内容
            },
            formItemLayout: {
                labelCol: { span: 10 },
                wrapperCol: { span: 14 }
            },
            rules: {
                bidFailNotificationContent: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterNotAnnouncementLetter`, '请输入未中标通知书内容'), trigger: 'change' }
                ]
            },
            upload: {
                modalVisible: false,
                action: '/attachment/purchaseAttachment/upload',
                tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
                accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf'
                // extraData: {
                //     businessType: 'failBidNotification',
                //     headId: this.vuex_currentEditRow.id
                // }
            }
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        extraData () {
            return {
                businessType: 'failBidNotification',
                headId: this.vuex_currentEditRow.id || ''
            }
        }
    },
    watch: {
        parentData: {
            deep: true,
            immediate: true,
            handler (obj) {
                if (!obj || Object.keys(obj).length === 0) return
                const { bidFailNotification, bidFailNotificationContent, attachment__$$__3  } = JSON.parse(JSON.stringify(obj)) || {}
                this.form = Object.assign({}, this.form, {
                    bidFailNotification: bidFailNotification || '0',
                    bidFailNotificationContent: bidFailNotificationContent || '',
                    attachment__$$__3: attachment__$$__3 || []
                })
            }
        },
        templateContent (str) {
            if (!str) return
            const props = [ 'bidWinContent', 'bidWinNotificationContent', 'bidFailNotificationContent' ]
            const property = props[this.current]
            if (!this.form.hasOwnProperty(property)) return
            this.form[property] = str
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        updateParent () {
            let parentData = JSON.parse(JSON.stringify(this.parentData))
            parentData = Object.assign({}, parentData, this.form)
            this.$emit('update:parentData', parentData)
        },
        handleUploadChange (info) {
            this.updateParent()
            this.$emit('updateFile', {
                _property: 'attachment__$$__3',
                info
            })
        },
        handleDel (file) {
            this.$emit('deleteFile', {
                _property: 'attachment__$$__3',
                ...file
            })
        },
        // 文件下载
        handleDownload ({ id, fileName }) {
            const params = {
                id
            }
            let downloadUrl = '/attachment/purchaseAttachment/download'
            getAction(downloadUrl, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }
    }
}
</script>
