<template>
  <div
    class="els-page-comtainer"
    ref="els_page_comtainer"
    style="background-color: white; margin-top: 4px;position:relative">
    <a-spin :spinning="confirmLoading">
      <a-page-header
        :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
        :title="title"
      >
        <template slot="extra">
          <a-button
            v-for="(item, key) in pageData.publicBtn"
            v-show="item.showCondition ? item.showCondition() : true"
            :type="item.type || 'default'"
            @click="item.clickFn"
            :key="'public_btn_' + key">{{ item.title }}
          </a-button>
        </template>
      </a-page-header>
      <div
        class="table-page-search-wrapper"
        style="padding:0px">

        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            v-for="(panel, index) in pageData.panels"
            :key="'collapse_panel_'+index"
            :header="panel.title"
          >
            <!-- //预警配置通知对象 -->
            <div style="display:flex; justify-content:space-around">
              <div
                v-for="(item, value) in panel.content.plainOptions"
                :key="value">
                <div
                  v-if="item.style"
                  style="width:5px;height:100%;background-color: #000000;border: 1px solid #000000;border-radius:10px;"></div>
                <div
                  style="display:flex;align-content:space-between; height:100%;"
                  v-if="panel.content.ref=='timerForm'&&!item.style" >
                  <div style="margin-right:50px"><b
                    v-if="item.ordata"
                    style="font-size:16px;margin-right:7px">{{ item.ordata }}</b>{{ item.purchasetitle }}</div>
                  <div style="display: grid;margin: 0 auto;">
                    <div
                      style="margin-left: 2px;margin-top:6px;display:flex;justify-content:space-between;"
                      v-for="(item, value) in item.list"
                      :key="value">
                      <div>
                        <span style="padding-right:0px;" >{{ item.title }}</span>
                        <a-tooltip
                          v-if="item.helpText"
                          :title="item.helpText">
                          <a-icon type="question-circle-o" />
                        </a-tooltip>
                      </div>
                      <a-form-model-item
                        style="display:flex;margin:-7px 0px 5px 5px;width:400px;"
                        :prop="item.fieldName"
                        v-if="item.type==='select'">
                        <m-select
                          style="width:400px;"
                          :getPopupContainer="triggerNode=>{ return $refs.els_page_comtainer || document.body}"
                          :returnTitle="true"
                          :mode="item.mode"
                          :disabled="item.disabled"
                          v-model="pageData.form[item.fieldName]"
                          :placeholder="item.placeholder"
                          :show-opt-value="item.showOptValue"
                          :options="item.options"
                          :source-url="item.sourceUrl"
                          :source-map="item.sourceMap"
                          :value-map="item.valueMap"
                          :title-map="item.titleMap"
                          :configData="pageData"
                          :dict-code="item.dictCode"
                          :noEmptyOpt="item.noEmptyOpt"
                          :fieldList="panel.content.list"
                          :formModel="pageData.form"
                          :relationData="item.relationData"
                          @relation="relationFieldChange"
                          @change="item.changeEvent"/>
                      </a-form-model-item>
                      <a-form-model-item
                        style="display:flex;margin:-7px 0px 5px 5px;width:400px;"
                        v-else-if="item.type=='selectModal'"
                        :prop="item.fieldName">
                        <!-- <a-tooltip
                                slot="label"
                                v-if="item.label"
                                :title="item.label">
                                {{ item.label }}
                                </a-tooltip> -->
                        <m-select-modal
                          isFromTileEditPage
                          v-model="pageData.form[item.fieldName]"
                          :disabled="item.disabled || false"
                          :config="item"
                          style="width:400px"
                          :pageData="pageData"
                          :form="pageData.form"
                          :currentStep="index"
                          @afterClearCallBack="warnhandleSelectModalAfterClear"
                          @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                        />
                      </a-form-model-item>
                      <a-form-model-item
                        style="width:400px;margin:-7px 0px 5px 5px;"
                        v-if="item.type == 'codeEditorModel'"
                        :prop="item.fieldName">
                        <code-editor-model
                          :defaultValue="item.defaultValue"
                          :disabled="item.disabled"
                          style="width:100%"
                          v-bind="item.extend || {}"
                          v-model="pageData.form[item.fieldName]"
                          @handleSureClick="(content)=> {pageData.form[item.fieldName] = content}"></code-editor-model>
                      </a-form-model-item>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <a-form-model
              v-if="panel.content.type == 'form'"
              :ref="panel.content.ref"
              :model="pageData.form"
              class="ant-advanced-search-form"
              style="padding-top:0"
              :label-col="labelCol"
              :rules="pageData.validRules"
              :wrapper-col="wrapperCol"
              layout="inline">
              <a-row
                :gutter="24">
                <a-col                  
                  v-for="(item, i) in panel.content.list"
                  :class="{'required-field': item.required==='1'}"
                  :key="'field_'+i"
                  :span="panel.content.colSpan || 8">
                  <!-- input -->
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-if="item.type==='input'"

                  >
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-input
                      :disabled="item.disabled || false"
                      v-model="pageData.form[item.fieldName]"
                      :placeholder="item.placeholder"/>
                  </a-form-model-item>
                  <!-- radio -->
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-if="item.type==='radioGroup'"
                  >
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }} 
                    </a-tooltip>
                    <a-radio-group 
                      v-model="pageData.form[item.fieldName]" 
                      @change="onChangeRadioResult"
                      :defaultValue="item.defaultValue"
                      :options="item.options">
                    </a-radio-group>
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-if="item.type==='number'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-input-number
                      style="width:100%"
                      :disabled="item.disabled || false"
                      v-model="pageData.form[item.fieldName]"
                      @blur="() => inputNumberBlurMethod({value: pageData.form[item.fieldName], fieldLabel:item.label, type: 'form', fieldType: item.type, form:pageData.form, item})"
                      :placeholder="item.placeholder"/>
                  </a-form-model-item>
                  <a-form-model-item
                    v-else-if="item.type=='selectModal'"
                    :prop="item.fieldName">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <m-select-modal
                      isFromTileEditPage
                      v-model="pageData.form[item.fieldName]"
                      :disabled="item.disabled || false"
                      :config="item"
                      :pageData="pageData"
                      :form="pageData.form"
                      :currentStep="index"
                      @afterClearCallBack="handleSelectModalAfterClear"
                      @ok="(rows) => handleSelectModalAfterSelect(item, rows)"
                    />
                  </a-form-model-item>

                  <!-- <a-form-model-item
                    :prop="item.fieldName"
                    v-if="item.type==='selectModal'"
                    :label="item.label">
                    <a-input
                      readOnly
                      :disabled="item.disabled || false"
                      v-model="pageData.form[item.fieldName]"
                      :placeholder="item.placeholder"
                      @click="e => openSelectModal(e, item)">
                      <a-icon
                        slot="suffix"
                        type="close-circle"
                        @click="() => clearInputValue(item)"></a-icon>
                    </a-input>
                  </a-form-model-item> -->
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-if="item.type==='serach'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-input
                      readOnly
                      :disabled="item.disabled || false"
                      v-model="pageData.form[item.fieldName]"
                      :placeholder="item.placeholder"
                      @click="item.clickFn">
                      <a-icon
                        slot="suffix"
                        type="close-circle"
                        @click="() => clearInputValue(item)"></a-icon>
                    </a-input>
                  </a-form-model-item>
                  <a-form-model-item
                    v-if="item.type==='numberGroup'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-input-number
                      v-for="(field,fieldIndex) in item.fieldName"
                      :style="{width: (1/item.fieldName.length*100) + '%'}"
                      :key="'number_group_'+fieldIndex"
                      :disabled="item.disabled || false"
                      v-model="pageData.form[field]"/>
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-if="item.type==='select'">
                    <span slot="label">
                      <a-tooltip
                        slot="label"
                        v-if="item.label"
                        :title="item.label">
                        {{ item.label }} 
                      </a-tooltip>
                      <a-tooltip
                        v-if="item.helpText"
                        :title="item.helpText">
                        <a-icon type="question-circle-o"/>
                      </a-tooltip>
                    </span>
                    <m-select
                      :getPopupContainer="triggerNode=>{ return $refs.els_page_comtainer || document.body}"
                      :returnTitle="true"
                      :mode="item.mode"
                      :disabled="item.disabled"
                      v-model="pageData.form[item.fieldName]"
                      :placeholder="item.placeholder"
                      :show-opt-value="item.showOptValue"
                      :options="item.options"
                      :source-url="item.sourceUrl"
                      :source-map="item.sourceMap"
                      :value-map="item.valueMap"
                      :title-map="item.titleMap"
                      :configData="pageData"
                      :dict-code="item.dictCode"
                      :noEmptyOpt="item.noEmptyOpt"
                      :fieldList="panel.content.list"
                      :formModel="pageData.form"
                      :relationData="item.relationData"
                      @relation="relationFieldChange"
                      @change="item.changeEvent"/>
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-else-if="item.type==='dateRange'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-range-picker
                      :show-time="item.showTime"
                      @change="(dates) => changeDateRangePicker(dates, index, item)"
                      :disabled="item.disabled || false"
                      :value="pageData.form[item.fieldName]"
                      :valueFormat="item.valueFormat || 'YYYY-MM-DD'"/>
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-else-if="item.type==='date'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-date-picker
                      style="width: 100%;"
                      :disabled="item.disabled || false"
                      :show-time="item.showTime"
                      :valueFormat="item.valueFormat || 'YYYY-MM-DD'"
                      @change="handleChangeTime"
                      v-model="pageData.form[item.fieldName]"/>
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-else-if="item.type==='autoComplete'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-auto-complete
                      :disabled="item.disabled || false"
                      :placeholder="item.placeholder"
                      @search="(value) => handleSearch(value, item.dictCode)"
                      v-model="pageData.form[item.fieldName]"
                      :data-source="dataSource"
                      @focus="handleFocus"
                    />
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-else-if="item.type==='addressCascader'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-cascader
                      :options="orgType == 'thirdPartyStore' ? options : areas"
                      :placeholder="item.placeholder"
                      :value="pageData.form[item.fieldName]"
                      @change="(val,selectedOptions) => onChangeArea(val,selectedOptions, item)"
                    />
                  </a-form-model-item>
                  <a-form-model-item
                    v-else-if="item.type==='switch'"
                    :prop="item.fieldName"
                  >
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <m-switch
                      :disabled="item.disabled"
                      :close-value="item.closeValue"
                      :open-value="item.openValue"
                      :checked-children="item.checkedChildren"
                      :un-checked-children="item.unCheckedChildren"
                      v-model="pageData.form[item.fieldName]"
                    />
                  </a-form-model-item>
                  <a-form-model-item
                    class="textarea-form-item"
                    :prop="item.fieldName"
                    v-else-if="item.type==='textarea'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-textarea
                      :disabled="item.disabled || false"
                      v-model="pageData.form[item.fieldName]"
                      :placeholder="item.placeholder"/>
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-else-if="item.type==='timePicker'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <a-time-picker
                      style="width:100%"
                      valueFormat="HH:mm:ss"
                      :disabled="item.disabled || false"
                      v-model="pageData.form[item.fieldName]"/>
                  </a-form-model-item>
                  <a-form-model-item
                    :prop="item.fieldName"
                    v-else-if="item.type==='treeSelect'">
                    <a-tooltip
                      slot="label"
                      v-if="item.label"
                      :title="item.label">
                      {{ item.label }}
                    </a-tooltip>
                    <m-tree-select
                      allowClear
                      v-model="pageData.form[item.fieldName]"
                      :sourceUrl="item.sourceUrl"
                      :sourceMap="item.sourceMap"
                      :titleMap="item.titleMap"
                      :valueMap="item.valueMap"
                      :multiple="item.multiple"
                      :showEmptyNode="item.showEmptyNode"
                      :placeholder="item.placeholder"/>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
            <vxe-grid
              v-else-if="panel.content.type == 'table'"
              v-bind="gridConfig"
              border
              show-overflow
              highlight-hover-row
              :columns="panel.content.columns"
              :edit-rules="panel.content.validRules || null"
              :ref="panel.content.ref"
              :data="panel.content.dataSource"
              :height="panel.content.height || '400'"
              align="center"
              v-on="initGridEvents(panel.content)"
              @cell-click="cellClickEvent"
              @edit-actived="editActivedEvent"
              :edit-config="{trigger: 'click', mode: 'cell'}"
              :mouse-config="{}">
              <template slot="empty">
                <a-empty/>
              </template>
              <template #toolbar_buttons>
                <span
                  v-for="(btn, index3) in panel.content.toolbarButton"
                  class="tools-btn"
                  :key="'btn_' + index3">
                  <a-button
                    v-if="btn.type != 'upload' && btn.type !== 'check'&&btn.type !== 'tool-fill'"
                    :type="btn.type || 'default'"
                    v-show="btn.showCondition ? btn.showCondition() : true"
                    @click="() => handleToolbarClick(btn, panel)">
                    {{ btn.title }}
                  </a-button>
                  <a-button
                    v-else-if="btn.type == 'check'"
                    :type="btn.type || 'default'"
                    v-show="btn.showCondition ? btn.showCondition() : true"
                    @click="checkedGridSelect(btn, panel.content.ref, btn.beforeCheckedCallBack)">
                    {{ btn.title }}
                  </a-button>
                <!-- 向下填充 -->
                <a-button
                    v-else-if="btn.type === 'tool-fill'"
                    :type="btn.type || 'default'"
                    :disabled="(btn.disabled && btn.disabled(btn)) || false"
                    v-show="btn.showCondition ? btn.showCondition() : true"
                    @click="toolButtonHandle(btn, panel, btn.beforeCheckedCallBack)"
                >
                  {{ btn.title }}
                </a-button>
                  <custom-upload
                    v-else
                    :single="btn.single"
                    :disabledItemNumber="btn.disabledItemNumber"
                    :property="btn.property"
                    :visible.sync="btn.modalVisible"
                    :title="btn.title"
                    :itemInfo="itemInfo"
                    :action="url.upload"
                    :multiple="btn.multiple || true"
                    :accept="accept"
                    :dictCode="btn.dictCode || ''"
                    :headers="tokenHeader"
                    :currentEditRow="currentEditRow"
                    :data="returnData(btn)"
                    @change="(info) => handleUploadChange(info, btn, panel.content.ref)"
                  >
                    <a-button
                      v-if="btn.beforeChecked"
                      type="primary"
                      icon="cloud-upload"
                      @click="checkedGridSelect(btn, panel.content.ref, btn.beforeCheckedCallBack)">
                      {{ btn.title }}
                    </a-button>
                  </custom-upload>
                </span>


              </template>
              <template #cell_edit_modal="{ row, column }">
                <a
                  @click="openCellEditModal(row, column)"
                  v-html="row[column.property]"></a>
              </template>
              <!-- 阶梯价格 -->
              <template #ladder_price_json_render="{ row, column}">
                <a-tooltip
                  placement="top"
                  v-if="row[column.property]"
                  overlayClassName="tip-overlay-class">
                  <template slot="title">
                    <vxe-table
                      auto-resize
                      border
                      size="mini"
                      :data="initRowLadderJson(row[column.property])">
                      <vxe-table-column
                        type="seq"
                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_details`, '序号')}`"
                        width="80"></vxe-table-column>
                      <vxe-table-column
                        field="ladderQuantity"
                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_ladderQuantity`, '阶梯数量')}`"
                        width="140"></vxe-table-column>
                      <vxe-table-column
                        field="price"
                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_price`, '含税价')}`"
                        width="140"></vxe-table-column>
                      <vxe-table-column
                        field="netPrice"
                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_netPrice`, '不含税价')}`"
                        width="140"></vxe-table-column>
                    </vxe-table>
                  </template>
                  <div class="json-box"><a href="javascript: void(0)">{{
                    defaultRowLadderJson(row[column.property])
                  }}</a></div>
                </a-tooltip>
              </template>
            </vxe-grid>
            <upload-list
              :ref="panel.content.ref"
              v-else-if="panel.content.type == 'upload'"></upload-list>
          <json-viewer :ref="panel.content.ref" v-else-if="panel.content.type == 'jsonView'"
                       style="width: 100%; height: 100%"
              :value="pageData.form[panel.content.jsonField]" :expand-depth=1 copyable boxed/>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </a-spin>
    <field-select-modal
      ref="fieldSelectModal">
    </field-select-modal>
  </div>
</template>

<script lang="jsx">
import {EditConfig} from '@/plugins/table/gridConfig'
import {ajaxFindDictItems} from '@/api/api'
import {getAction, postAction} from '@/api/manage'
import fieldSelectModal from '../fieldSelectModal'
import {Empty, TimePicker} from 'ant-design-vue'
import uploadList from '@comp/uploadList/uploadList'
import MTreeSelect from '@comp/treeSelect/mTreeSelect'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import CustomUpload from '@comp/template/CustomUpload'
import {areas} from '@/store/area'
import { inputNumberBlurMethod } from '@/utils/util.js'
import {mapActions, mapState} from "vuex";

export default {
    name: 'TileEditPage',
    props: {
        pageData: {
            type: Object,
            default: null
        },
        url: {  //后台接口
            type: Object,
            default: null
        },
        orgType: {
            type: String
        },
        options: {
            type: Array
        },
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        extraDetailConfig: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    components: {
        AEmpty: Empty,
        fieldSelectModal,
        uploadList,
        MTreeSelect,
        ATimePicker: TimePicker,
        CustomUpload
    },
    data () {
        return {
            //附件上传配置
            tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            accept2: '.png, .jpg, .jpeg, .gif',
            fixPageHeader: false,
            title: this.pageData.title,
            confirmLoading: false,
            activeKey: ['collapse_panel_0'],
            voucherId: '',
            areas: areas,
            gridRef: '',
            labelCol: {
                xs: {span: 24},
                sm: {span: 6}
            },
            wrapperCol: {
                xs: {span: 24},
                sm: {span: 18}
            },
            currentSelectModal: {},
            organizationType: ''
        }
    },
    computed: {
        ...mapState({
            cache_vuex_editActivedInfo: (state) => state.app.cache_vuex_editActivedInfo
        }),
        itemInfo () {
            let itemInfo = []
            // const groups = this.pageData.groups || []
            // const group = groups.find(n =    > n.groupCode === 'itemInfo')
            // if (group) {
            //     const refName = group.custom.ref
            //     itemInfo = this.$refs[refName][0].getTableData().fullData || []
            // }
            return itemInfo
        },
        gridConfig () {
            let config = { ...EditConfig, ...this.extraDetailConfig}
            config.toolbarConfig.className = 'grid_toolbar_buttons'
            return config
        }
    },
    created () {
        this.computedKey()
        this.initDictData()
    },
    methods: {
        ...mapActions(['setEditActivedInfo']),
        inputNumberBlurMethod,
        returnData (btn) {
            // 判断是否有附件新参传入
            let obj = {
                businessType: btn.businessType, 
                headId: this.voucherId
            }
            btn.attr && (()=> {
                const bj = btn.attr()
                obj = Object.assign({}, obj, bj)
            })()
            return obj
        },
        checkedGridSelect (btn, refName, cb) {
            let selectData = null
            let that = this
            if (this.$refs[refName]) {
                if (this.$refs[refName][0]) {
                    selectData = this.$refs[refName][0].getCheckboxRecords() || this.$refs[refName][0].getRadioRecord()
                }
            }
            if (selectData && selectData.length) {
                if (cb && typeof cb === 'function') {
                    if (btn.key !== 'batchDownload') {
                        cb(selectData, that).then(res => {
                            if (res) {
                                btn.modalVisible = true
                            }
                        })
                    } else {
                        cb(selectData, that)
                    }
                } else {
                    this.modalVisible = true
                }

            } else {
                console.log(btn)
                if (btn.msgType === 'batchDownload') {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRiTIKBIcW_10289077`, '请勾选需下载附件行！'))
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterChoose`, '请先选择'))
                }
            }
        },
        // 关联field
        relationFieldChange ({realValue, fields, relationData, formModel}) {
            let getFieldArr = fields.filter((item) => {
                return item.fieldName === relationData.fieldName
            })
            if (getFieldArr && getFieldArr.length) {
                let relationField = getFieldArr[0]
                // 清空关联项
                formModel[relationField.fieldName] = ''
                if (realValue) {
                    relationField.dictCode = 'message_' + realValue
                } else {
                    relationField.dictCode = relationField.sourceDictCode
                }
            }
        },
        onChangeRadioResult (ev) {
            console.log(ev)
        },
        // 阶梯报价json数据组装
        initRowLadderJson (jsonData) {
            let arr = []
            if (jsonData) {
                arr = JSON.parse(jsonData)
            }
            return arr
        },
        // 阶梯报价默认显示
        defaultRowLadderJson (jsonData) {
            let arrString = ''
            if (jsonData) {
                let arr = JSON.parse(jsonData)
                arr.forEach((item, index) => {
                    let ladderQuantity = item.ladderQuantity
                    let price = item.price
                    let netPrice = item.netPrice
                    let str = `${ladderQuantity} ${price} ${netPrice} `
                    let separator = index === arr.length - 1 ? '' : ','
                    arrString += str + separator
                })
            }
            return arrString
        },
        handleChangeTime () {
            this.$forceUpdate()
        },
        warnhandleSelectModalAfterClear (cb) {
            if(this.pageData.form.noticeUser_dictText){
                this.pageData.form.noticeUser_dictText=''
            }
            if(this.pageData.form.purchaseAssignee_dictText){
                this.pageData.form.purchaseAssignee_dictText=''
            }
            cb && cb(this.form, this.pageData)
        },
        handleSelectModalAfterClear (cb) {
            cb && cb(this.form, this.pageData)
        },
        handleSelectModalAfterSelect (item, rows) {
            item.bindFunction && item.bindFunction.call(null, this, rows)
        },
        // 初始化綁定事件
        initGridEvents (row) {
            // 可以绑定所有事件统一处理 events写在columns同级
            let evs = {}
            if (row.columns[0].type == 'radio') {
                evs = {
                    'radio-change': (row.events && row.events.radioChange) || this.radioChange
                }
            } else if (row.columns[0].type == 'checkbox') {
                evs = {
                    'checkbox-change': (row.events && row.events.checkboxChange) || this.checkboxChange
                }
            }
            return evs
        },
        // 只对 type=radio 有效，当手动勾选并且值发生改变时触发的事件
        radioChange (row) {
            console.log(row)
        },
        // 只对 type=checkbox 有效，当手动勾选并且值发生改变时触发的事件
        checkboxChange (row) {
            console.log(row)
        },
        //附件上传
        handleUploadChange (info, btn, refName) {
            btn.callBack && btn.callBack(info, refName)
        },
        // 文件下载
        handleDownload ({id, fileName}) {
            const params = {
                id
            }
            getAction('/attachment/saleAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        computedKey () {
            let keyList = []
            for (let i = 0; i < this.pageData.panels.length; i++) {
                keyList.push('collapse_panel_' + i)
            }
            this.activeKey = keyList
        },
        goBack () {
            this.$emit('goBack')
        },
        initDictData () {
            let that = this
            //根据字典Code, 初始化字典数组
            this.pageData.panels.forEach(item => {
                if (item.content.type === 'table') {
                    item.content.columns.forEach(element => { 
                        if (element.editRender && element.editRender.dictCode) {                    
                            let postData = {
                                busAccount: that.$ls.get(USER_ELS_ACCOUNT),
                                dictCode: element.editRender.dictCode
                            }
                            ajaxFindDictItems(postData).then((res) => {
                                let options = res.result
                                if (res.success) {
                                    element.editRender.options = options.map(option => {
                                        return {label: option.title, value: option.value}
                                    })
                                    that.$forceUpdate()
                                }
                            })
                        } else if (element.editRender && element.editRender.dictUrl) {
                            // 增加一个通过url请求
                            getAction(element.editRender.dictUrl).then((res)=> {
                                let options = res.result
                                if (res.success) {
                                    element.editRender.options = options.map(option => {
                                        return {label: option.itemText, value: option.itemValue}
                                    })
                                    that.$forceUpdate()
                                }
                            })
                        }
                        if (element.fieldType && element.fieldType == 'link') {
                            element.slots = this.linkModalSlots(element)
                        }
                    })
                }
            })
        },
        //超链接跳转方法
        getNewRouter (col, row, column, linkConfig){
            console.log('[col, row, column, linkConfig]', col, row, column, linkConfig)
            if (col?.extend?.handleBefore && typeof col?.extend?.handleBefore === 'function' ){
                let callbackObj = col?.extend?.handleBefore(row, column, linkConfig, this, { pageConfig: this.pageConfig }) || {}
                linkConfig = { ...linkConfig, ...callbackObj }
            }
            console.log('[col,row,column,linkConfig]', col, row, column, linkConfig)
            if(row[column.property] && linkConfig.actionPath && linkConfig.bindKey){
                let query = {
                    [linkConfig.primaryKey]: row[linkConfig.bindKey],
                    ...linkConfig.otherQuery,
                    t: new Date().getTime()
                }
                this.$router.push({ path: linkConfig.actionPath.trim(), query: { ...query } })
            }
        },
        //超链接slots
        linkModalSlots (col) {
            console.log('[slots执行]')
            const that = this
            let linkConfig = {
                title: '认证链接',
                titleI18nKey: 'i18n_title_authenticationLink',
                primaryKey: 'id', //查询用的主键名
                bindKey: 'fbk1', //绑定的主键key
                actionPath: '', //目标路由
                otherQuery: { open: true } //其余参数
            }
            let exLink = false
            if (col.extend && col.extend.linkConfig) linkConfig = { ...linkConfig, ...col.extend.linkConfig }
            return Object.assign({}, col.slots, {
                default: ({ row, rowIndex, column, columnIndex }) => {
                    if(exLink){
                        return [
                            <a
                                href= {row[column.property]}
                                target="_blank"
                            >
                                <span>{ that.$srmI18n(`${that.$getLangAccount()}#${linkConfig.titleI18nKey}`, linkConfig.title) }</span>
                            </a>
                        ]
                    }else{
                        return [
                            <a
                                onClick={() => {
                                    that.getNewRouter(col, row, column, linkConfig)
                                }}
                            >
                                {row[column.property]}
                            </a>
                        ]
                    }
                }
            })
        },
        setPromise () {
            let that = this
            let promise = this.pageData.panels.map(panel => {
                let type = panel.content.type
                if(type == 'table') {
                    return that.$refs[panel.content.ref][0].validate(true)
                }else {
                    return that.$refs[panel.content.ref][0].validate()
                }
            })
            return promise
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.currentStep = i
                        return
                    }
                }
                if (flag) this.postData()
            }).catch(err => {
                console.log(err)
            })
        },
        postData () {
            let params = this.getParamsData()
            let url = this.url.add
            if (this.voucherId) {
                url = this.url.edit
            }
            this.confirmLoading = true
            postAction(url, params).then(async (res) => {
                this.confirmLoading = false
                if (res.success) {
                    this.$message.success(res.message)
                    this.afterPostData()
                    this.$parent.refreshList()
                    if (res.result && res.result.id) {
                        await this.requestTabsData(res.result.id)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        afterPostData () {
            if (this.$parent.afterPostData) {
                this.$parent.afterPostData()
            }
        },
        clearInputValue (item) {
            this.pageData.form[item.fieldName] = ''
            if (item.cb && typeof item.cb === 'function') item.cb(this.form, this.pageData)
            this.$forceUpdate()
        },
        //打开选择弹窗
        // openSelectModal (e, item) {
        //     this.currentSelectModal = item
        //     let params = item.params
        //     if(typeof(params) == 'function') {
        //         params = item.params()
        //     }
        //     this.$refs.fieldSelectModal.open(item.sourceUrl, params, item.columns, item.selectModel)
        // },
        //设置单个属性方法
        setFormFieldValue (key, value) {
            let addKey = {}
            if (key && value) {
                addKey[key] = value
            }
            Object.assign(this.pageData.form, addKey)
            this.$forceUpdate()
        },
        //批量设置form属性方法
        setFormFieldsValue (obj) {
            let params = {...obj}
            Object.assign(this.pageData.form, params)
            this.$forceUpdate()
        },
        //设置表单禁用
        setFormFieldDisabled () {
            this.pageData.panels.forEach(item => {
                if (item.content.type === 'form') {
                    item.content.list.forEach(li => {
                        li.disabled = true
                    })
                }
            })
            this.$forceUpdate()
        },
        //修改弹窗传参数据
        setSelectModalParams (fieldName, params) {
            this.pageData.panels.forEach(panel => {
                if (panel.content.type == 'form') {
                    panel.content.list.forEach(li => {
                        if (li.type == 'selectModal' && fieldName == li.fieldName) {
                            li.params = params
                        }
                    })
                }
            })
            this.$forceUpdate()
        },
        //表单弹窗回调
        fieldSelectOk (data) {
            this.currentSelectModal.selectCallBack(data)
        },
        //获取
        async requestTabsData (voucherId) {
            let that = this
            let url = this.url.detail
            this.voucherId = voucherId
            this.confirmLoading = true
            return getAction(url, {id: voucherId}).then(res => {
                if (res.success) {
                    let data = res.result
                    //预警配置处理数据
                    if(that.$parent.requestTabsDataAfter){
                        data=that.$parent.requestTabsDataAfter(data)
                    }
                    Object.assign(this.pageData.form, data)
                    console.log('data', data)
                    this.organizationType = data.organizationType
                    this.$emit('getOrgType', this.organizationType)
                    if (data.accountValidityDate) {
                        let time = data.accountValidityDate
                        let dateTime = new Date(time)
                        let date = dateTime.toLocaleDateString().replace(/\//g, '-')
                        this.$set(this.pageData.form, 'accountValidityDate', date)
                    }
                    this.pageData.panels.forEach(panel => {
                        if (panel.content.type == 'table') {
                            that.$refs[panel.content.ref][0].loadData(data[panel.content.ref])
                        } else if (panel.content.type == 'upload') {
                            panel.content.relatedId = data[panel.content.relatedIdMap]
                        }
                    })
                    this.handleData()
                    this.getUploadListData()
                    this.confirmLoading = false
                    this.requestAfter(data, this.pageData.form)
                }
            })
        },
        requestAfter (data, formModel) {
            this.$parent.requestAfter(data, formModel)
        },
        queryTemplateList (elsAccount) {
            let params = {
                pageSize: 100,
                elsAccount: elsAccount,
                templateStatus: '1',
                businessType: this.pageData.businessType,
                pageNo: 1
            }
            return getAction('/template/templateHead/getListByType', params)
        },
        selectedTemplate () {
            if (this.templateNumber) {
                const that = this
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version
                }
                postAction(this.url.add, params).then(res => {
                    if (res.success) {
                        that.$parent.selectedTemplate(res.result)
                    } else {
                        that.$message.warning(res.message)
                    }
                    that.visible = false
                    that.submitLoading = false
                })
            }
        },
        // 设置数据
        handleData () {
            let that = this
            this.pageData.panels.forEach(panel => {
                if (panel.content.type == 'form') {
                    panel.content.list.forEach(form => {
                        if (form.type == 'addressCascader') {
                            if (that.pageData.form[form.state]) {
                                let city = that.pageData.form[form.city] || ''
                                let area = that.pageData.form[form.area] || ''
                                that.pageData.form[form.fieldName] = [that.pageData.form[form.state], city, area]
                            } else {
                                that.pageData.form[form.fieldName] = []
                            }
                        } else if (form.type == 'dateRange') {
                            if (that.pageData.form[form.start] && that.pageData.form[form.end]) {
                                that.pageData.form[form.fieldName] = [that.pageData.form[form.start], that.pageData.form[form.end]]
                            } else {
                                that.pageData.form[form.fieldName] = []
                            }
                        }
                    })
                }
            })
        },
        onChangeArea (val, selectedOptions, item) {
            let state = ''
            let city = ''
            let area = ''
            if (val) {
                state = val[0] ? val[0] : ''
                city = val[1] ? val[1] : ''
                area = val[2] ? val[2] : ''
            }
            this.pageData.form[item.state] = state
            this.pageData.form[item.city] = city
            this.pageData.form[item.area] = area
            this.pageData.form[item.stateName] = selectedOptions ? (selectedOptions[0]?.label ? selectedOptions[0].label : '') : ''
            this.pageData.form[item.cityName] = selectedOptions ? (selectedOptions[1]?.label ? selectedOptions[1].label : '') : ''
            this.pageData.form[item.areaName] = selectedOptions ? (selectedOptions[2]?.label ? selectedOptions[2].label : '') : ''
            this.pageData.form[item.fieldName] = val ? [state, city, area] : []
            this.$forceUpdate()
        },
        changeDateRangePicker (dates, index, item) {
            this.pageData.form[item.start] = dates[0]
            this.pageData.form[item.end] = dates[1]
            this.pageData.form[item.fieldName] = [dates[0], dates[1]]
            this.$forceUpdate()
        },
        // 获取界面数据
        getParamsData () {
            let that = this
            let params = {...this.pageData.form}
            this.pageData.panels.forEach(panel => {
                if (panel.content.type == 'table') {
                    params[panel.content.ref] = that.$refs[panel.content.ref][0].getTableData().fullData
                }
            })
            return params
        },
        //处理toolbar按钮事件
        handleToolbarClick (item, panel) {
            this.gridRef = panel.content.ref
            item.clickFn()
        },
        toolButtonHandle(btn, panel, cb) {
            let group = {
                custom:panel.content,
                title:panel.title,
            }
            // .custom.ref
            const otherParams = {
                buttonInfo: btn,
                flag: 'tileEditPage',
                self: this,
                tplRootRef: this,
                group:group
            }
            cb(this.cache_vuex_editActivedInfo, otherParams)
        },
        cellClickEvent(info) {
            const { row, rowIndex, column, columnIndex } = info
            // 非同一个表格置空
            if (this.cache_vuex_editActivedInfo?.column?.property !== column.property) {
                this.setEditActivedInfo({})
            }
            this.$emit('cell-click', { row, rowIndex, column, columnIndex })
        },
        editActivedEvent(info) {
            const { row, rowIndex, column, columnIndex } = info
            this.setEditActivedInfo(info)
            this.$emit('edit-actived', { row, rowIndex, column, columnIndex })
        },
        //新增表格行
        addRow (rows) {
            if (rows) {
                this.$refs[this.gridRef][0].insertAt(rows, -1)
            } else {
                this.$refs[this.gridRef][0].insertAt([{}], -1)
            }
        },
        // 删除选中行
        deleteRow () {
            let grid = this.$refs[this.gridRef][0]
            let selectedData = grid.getCheckboxRecords()
            if (!selectedData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDeleteRowTips`, '请选择删除行'))
            }
            grid.removeCheckboxRow()
        },
        deleteRadioRow () {
            let grid = this.$refs[this.gridRef][0]
            let selectedData = grid.getRadioRecord()
            if (!selectedData) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDeleteRowTips`, '请选择删除行'))
            }
            grid.removeRadioRow()
        },
        getUploadListData () {
            let that = this
            let uploadPanel = this.pageData.panels.filter(panel => {
                return panel.content.type == 'upload'
            })
            if (uploadPanel.length) {
                uploadPanel.forEach(item => {
                    that.$refs[item.content.ref][0].getFileList(item.content.relatedId, item.content.relatedType, item.content.roleName)
                })
            }
        },
        //打开行明细弹窗
        openCellEditModal (row, column) {
            this.$parent['edit_' + column.property](row)
        },
        // 重置界面
        resetPage () {
            let that = this
            this.pageData.form = {}
            this.voucherId = ''
            this.pageData.panels.forEach((panel) => {
                if (panel.content.type == 'table') {
                    that.$refs[panel.content.ref][0].loadData([])
                } else if (panel.content.type == 'form') {
                    that.$refs[panel.content.ref][0].resetFields()
                }
            })
        }
    }
}
</script>
<style lang="less" scoped>
.required-field {
    :deep(.ant-form-item-control){
        >input, .ant-select-selection {
            background-color: #fff9f7;
            border: 1px solid #fdaf96;
            border-radius: 4px;
        }
        
    }
}
.json-box {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
:deep(.grid_toolbar_buttons .tools-btn){
  >button.ant-btn{
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
:deep(.ant-checkbox + span) {
    padding-right: 0px;
}
    .ant-input-number, .ant-calendar-picker {
   
        font-size: 13px;
    }
    .ant-input{
      font-size: 13px;
    }
   :deep(.ant-input){
      font-size: 13px;
    }
    :deep(.ant-select){
       font-size: 13px;
    }
     :deep(.ant-calendar-picker-input.ant-input){
           font-size: 13px;
    }

           :deep(.vxe-table--render-default.size--mini){
           font-size: 13px;
    }
    

</style>
<style lang="less">
.ant-form-item-label > label::after {
  content: ':';
  position: relative;
  top: -0.5px;
  margin: 0 8px 0 0px;

}

.table-page-search-wrapper .ant-form-inline .textarea-form-item .ant-form-item-control {
  height: auto
}

.ant-page-header {
  padding: 8px 16px;
}

.tools-btn {
  margin-left: 10px;
  & + .tools-btn {
    margin-left: 10px;
  }
}

 
</style>
