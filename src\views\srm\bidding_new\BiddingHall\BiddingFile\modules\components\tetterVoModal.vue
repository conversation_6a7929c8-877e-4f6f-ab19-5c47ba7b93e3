<template>
  <div>
    <a-modal
      centered
      v-model="showNode"
      @ok="confirm"
      :title="`${$srmI18n(`${$getLangAccount()}#i18n_field_eBxVH_9dbaaa19`, '投标函信息')}`">
      <Dataform
        ref="dataform"
        :formData="formData"
        :solSpan="24"
        :labelCol="{span:8}"
        :wrapperCol="{span:16}"
        :fields="fields" />
    </a-modal>
  </div>
</template>
<script>
import Dataform from '@views/srm/bidding_new/BiddingHall/components/Dataform.vue'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
export default {
    mixins: [baseMixins],
    props: {
        tetterVoData: {
            type: Array,
            default: ()=> {
                return []
            }
        },
        baseFormData: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    inject: ['currentSubPackage'],
    computed: {
        tenderQuotationsType () {
            if (this.checkType == '0') {
                return 'preTenderFormatType'
            } else {
                if (this.processType == '1' || (this.$ls.get('changeBidFile') && this.currentSubPackage().processType == '1')) {
                    return 'resultTenderFormatType '
                }
                return 'tenderFormatType'
            }
        },
        fields () {
            let btn = [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxRL_9dbb44ee`, '投标函名称'),
                    fieldLabelI18nKey: '',
                    field: 'name',
                    fieldType: 'input',
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBxAc_9dc007db`, '投标函类型'),
                    fieldLabelI18nKey: '',
                    field: 'formatType',
                    fieldType: 'select',
                    dictCode: this.tenderQuotationsType,
                    required: '1',
                    bindFunction: (v) => {
                        if (v == '9') this.formData.quoteBidLetter = ''
                    },
                    defaultValue: '0'
                }
            ]
            if (this.baseFormData.quoteType == '1' && (['0', '1', '2'].includes(this.formData.formatType))) {
                btn.push({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRKSLc_c1a17ba4`, '是否关联物料行'),
                    fieldLabelI18nKey: '',
                    field: 'quoteBidLetter',
                    fieldType: 'select',
                    dictCode: 'yn',
                    required: '1',
                    defaultValue: '0'
                })
            }
            return btn
        }
    },
    components: {
        Dataform
    },
    data () {
        return {
            showNode: false,
            formData: {},
            oldFormData: {}
        }
    },
    methods: {
        open (row = null) {
            this.formData = {}
            this.oldFormData = {}
            if (row) {
                this.formData = Object.assign({}, row)
                this.oldFormData = Object.assign({}, row)
            }
            this.showNode = true
        },
        confirm () {
            this.$refs.dataform.getValidatePromise().then((valid) => {
                if (valid) {
                    let params = Object.assign({}, this.formData)
                    if (this.tetterVoData && this.tetterVoData.length > 0) {
                        let names = this.tetterVoData.map(item => item.name)
                        if (this.oldFormData.name != params.name) {
                            if (names.includes(params.name)) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MKeReBxRL_1da4db3d`, '存在同名投标函名称'))
                        }
                        if (['0', '1', '2'].includes(params.formatType) && this.oldFormData.formatType != params.formatType) {
                            let arr = this.tetterVoData.filter(item => params.formatType == item.formatType) || []
                            if (arr.length > 0) return this.$message.error('存在相同投标函类型')
                        }
                        if (this.oldFormData.quoteBidLetter != params.quoteBidLetter && params.quoteBidLetter == '1') {
                            let quoteBidLetter = this.tetterVoData.filter(item => item.quoteBidLetter == '1')
                            if (quoteBidLetter.length > 0) return this.$message.error('已经存在关联物料的开标一览表')
                        }
                        if (this.oldFormData.formatType && (this.oldFormData.formatType != params.formatType)) {
                            this.$confirm({
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQTPFSeBxAc_6e361ae8`, '是否需要切换投标函类型'),
                                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WLFSeBxAcTPsReBxWF_43770a01`, '因为切换投标函类型，需要重置投标函数据'),
                                onOk: () => {
                                    this.paramsInit(params)
                                    // 重置投标函数据
                                    this.$emit('resetCurrentRow', params)
                                    this.showNode = false
                                },
                                onCancel: () => {
                                }
                            })
                            return false
                        }
                    }
                    this.paramsInit(params)
                    this.showNode = false
                } else {
                    console.log('error submit!!')
                    return false
                }
            })
        },
        paramsInit (params) {
            params['customizeFieldData'] = []
            params['customizeFieldModel'] = []
            params['quoteColumnList'] = []
            if (params.formatType != '9') {
                // 总项报价默认值
                if (this.baseFormData.quoteType == '0') {
                    params['customizeFieldData'] = [{}]
                    params['customizeFieldModel'] = [{
                        editRender: { name: '$input', enabled: false },
                        width: 120,
                        field: 'supplierName',
                        inputOrg: '0',
                        canDeleteColumn: true,
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称')
                    }]
                } else if (this.baseFormData.quoteType == '1') {
                    // 分项报价默认值
                    params['customizeFieldData'] = [{}]
                    params['customizeFieldModel'] = [
                        {
                            editRender: { name: '$input', enabled: false },
                            width: 120,
                            field: 'supplierName',
                            inputOrg: '0',
                            canDeleteColumn: true,
                            must: true,
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称')
                        }
                    ]
                    // 是否需要关联物料
                    if (params.quoteBidLetter == '1') {
                        params['customizeFieldData'] = []
                        params['customizeFieldModel'].push(
                            ...[
                                {
                                    width: 120,
                                    field: 'materialName',
                                    canDeleteColumn: true,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称')
                                },
                                {
                                    width: 100,
                                    field: 'quotedPrice',
                                    inputOrg: '0',
                                    fieldCategory: '1',
                                    canDeleteColumn: true,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价')
                                }
                            ]
                        )
                    }
                    this.$emit('resetCurrentRow', params)
                }
            }
            this.$emit('confirm', params)
        }
    }
}
</script>