<template>
  <div class="els-page-comtainer">
    
    <a-page-header
      :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
      :title="title"
    >
      <template slot="extra">
        <a-button
          @click="handleOk"
          type="primary"
          key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <a-button
          @click="goBack"
          key="3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-page-header>
    <div class="table-page-search-wrapper">
      <a-spin :spinning="confirmLoading">
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            key="1"
            :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
          >
            <a-form :form="form">
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialGroupName`, '物料组名称')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterMaterialGroupName`, '请输入物料组名称')"
                  v-decorator="['materialGroupName', validatorRules.materialGroupName]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialGroupLevel`, '分组级别')">
                <j-dict-select-tag
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectMaterialGroupLevelTips`, '请选择分组级别')"
                  v-decorator="['materialGroupLevel', validatorRules.materialGroupLevel]"
                  :trigger-change="true"
                  dict-code="isrmMaterialGroupLevel"
                  @change="changeEvent"
                />              
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialGroupCode`, '物料组编码')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterMaterialGroupCode`, '请输入物料组编码')"
                  :disabled="canWrite"
                  v-decorator="['materialGroupCode', validatorRules.materialGroupCode]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_parentGroup`, '上级分组')"> 
                <j-dict-select-tag
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterParentGroup`, '请输入上级分组')" 
                  :trigger-change="true"
                  v-decorator="['parentId', validatorRules.parentId]"                      
                  :dict-code="condition"
                />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterRemark`, '请输入备注')"
                  rows="5"
                  type="textarea"
                  v-decorator="['remark', validatorRules.remark]" />
              </a-form-item>
            </a-form>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </div>
  </div>
  <!-- </a-modal> -->
</template>

<script>
import { httpAction } from '@/api/manage'
import pick from 'lodash.pick'
import { duplicateCheck } from '@/api/api'

export default {
    name: 'MaterialGroupModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            activeKey: ['1'],
            fixPageHeader: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
            visible: false,
            canWrite: false,
            pre_condition: 'isrm_base_material_group,material_group_name,id,is_deleted=0',
            condition: 'isrm_base_material_group,material_group_name,id,is_deleted=0 and 1!=1',
            model: {},
            labelCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            wrapperCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            confirmLoading: false,
            form: this.$form.createForm(this),
            validatorRules: {
                materialGroupCode: {rules: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterMaterialGroup`, '请输入物料组编码!') }, {max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}]},
                materialGroupName: {rules: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNameCantBeEmpty`, '物料组名称不能为空')}, {max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符' )}]},
                materialGroupLevel: {rules: []},
                parentId: {rules: []},
                remark: {rules: [{max: 200, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow200`, '内容长度不能超过200个字符' )}]}
             
            },
            url: {
                add: '/base/materialGroup/add',
                edit: '/base/materialGroup/edit'
            }
        }
    },
    mounted () {
        this.init()
        window.addEventListener('scroll', this.handleScroll)
    },
    methods: {
        init () {
            if(this.currentEditRow) {
                this.edit(this.currentEditRow)
            }else {
                this.add()
            }
        },
        add () {
            this.edit({})
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addMaterialGroup`, '新增物料组')
        },
        edit (record) {
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editMaterialGroup`, '编辑物料组')
            this.form.resetFields()  
            this.changeEvent(record.materialGroupLevel)   
            this.model = Object.assign({}, record)
            this.visible = true
            this.$nextTick(() => {
                this.form.setFieldsValue(pick(this.model, 'materialGroupCode', 'materialGroupName', 'materialGroupLevel', 'parentId', 'sourceType', 'remark', 'fbk1', 'fbk2', 'fbk3', 'fbk4', 'fbk5', 'fbk6', 'fbk7', 'fbk8', 'fbk9', 'fbk10'))
                //时间格式化
            })

        },
        validateCode (rule, value, callback) {
        // 重复校验
            var params = {
                tableName: 'isrm_base_material_group',
                fieldName: 'material_group_code',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then((res) => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    that.confirmLoading = true
                    let httpurl = ''
                    let method = ''
                    if(!this.model.id){
                        httpurl+=this.url.add
                        method = 'post'
                    }else{
                        httpurl+=this.url.edit
                        method = 'post'
                    }
                    let formData = Object.assign(this.model, values)
                    //时间格式化
            
                    console.log(formData)
                    httpAction(httpurl, formData, method).then((res)=>{
                        if(res.success){
                            that.$message.success(res.message)
                            that.$emit('ok')
                            that.goBack()
                        }else{
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        handleScroll () {
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 50) {
                this.fixPageHeader = true
            }else {
                this.fixPageHeader = false
            }
        },
        changeEvent (materialGroupLevel){
            if(materialGroupLevel=='1'){
                this.condition =this.pre_condition+' and 1!=1'
                this.canWrite = false
                this.validatorRules.materialGroupCode.rules=[{ validator: this.validateCode }, {required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroupCodeCantBeEmpty`, '物料组编码不能为空')}]
                this.validatorRules.parentId.rules=[]
            }else if(materialGroupLevel=='2'){
                this.condition =this.pre_condition+' and material_group_level=\'1\''
                this.canWrite = true
                this.form.setFieldsValue({materialGroupCode: ''}) 
                this.validatorRules.materialGroupCode.rules=[]
                this.validatorRules.parentId.rules=[{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_parentGroupCantBeEmpty`, '上级分组不能为空')}]
            }else if((materialGroupLevel=='3')){
                this.condition =this.pre_condition+' and material_group_level=\'2\''
                this.canWrite = true
                this.form.setFieldsValue({materialGroupCode: ''}) 
                this.validatorRules.materialGroupCode.rules=[]
                this.validatorRules.parentId.rules=[{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_parentGroupCantBeEmpty`, '上级分组不能为空')}]
            } 
            
           
        }
    }
}
</script>

<style lang="less" scoped>

</style>