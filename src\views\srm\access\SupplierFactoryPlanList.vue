<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <SupplierFactoryPlanModal
      v-if="showEditPage"
      ref="modalForm"
      :current-edit-row="currentEditRow"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import SupplierFactoryPlanModal from './modules/SupplierFactoryPlanModal'
import {listPageMixin} from '@comp/template/listPageMixin'

export default {
    mixins: [listPageMixin],
    inject: ['routeReload'],
    components: {
        SupplierFactoryPlanModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, primary: true},  
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterCodeName`, '请输入编码或名称')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: `/config/purBussFormHead/list?formType=${this.$route.name.split('-')[0]}`,
                delete: '/config/purBussFormHead/delete',
                deleteBatch: '/config/purBussFormHead/deleteBatch',
                columns: `${this.$route.name}`
            }
        }
    },
    watch: {
        $route () {
            this.routeReload()
        }
    },
    methods: {
        
    }
}
</script>