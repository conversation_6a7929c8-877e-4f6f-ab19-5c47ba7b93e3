<template>
  <div class="PurchaseBarcodeInfoHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="detail"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
      <field-select-modal
        :isEmit="true"
        @ok="checkSupplierSelectOk"
        ref="SupplierFieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'

export default {
    name: 'PurchaseBarcodeInfoHeadDetail',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            requestData: {
                detail: {
                    url: '/base/barcode/saleBarcodeInfoHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/saleBarcodeInfoHead/edit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber || this.$route.query.templateNumber
            let templateVersion = this.currentEditRow.templateVersion || this.$route.query.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount || this.$route.query.templateAccount
            return `${account}/sale_barcodeInfo_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_LFToRH_cc18358d`, '规则条码明细'),
                        groupNameI18nKey: '',
                        groupCode: 'sysBarcodeList',
                        groupType: 'item',
                        sortOrder: '5'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_JIIToRH_2e146811`, '自定义条码明细'),
                        groupNameI18nKey: '',
                        groupCode: 'customBarcodeList',
                        groupType: 'item',
                        sortOrder: '10'
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_To_cfcc0`, '条码'),
                        fieldLabelI18nKey: '',
                        field: 'barcode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormd2b_printNumber`, '允许打印份数'),
                        fieldLabelI18nKey: '',
                        field: 'printNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'customBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_To_cfcc0`, '条码'),
                        fieldLabelI18nKey: '',
                        field: 'barcode',
                        fieldType: 'input',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'customBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormd2b_printNumber`, '允许打印份数'),
                        fieldLabelI18nKey: '',
                        field: 'printNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        // 处理发布回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            const { elsBarcodeRuleItemList } = args.allData || {}
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!elsBarcodeRuleItemList || !elsBarcodeRuleItemList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cVHxOLV_c6f5290a`, '行信息不能为空！'))
                    reject(args)
                } else {
                    resolve(args)
                }
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            console.log('handleSaveAfter', args)
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        // 处理保存回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handleSubmitBefore (args) {
            return new Promise(resolve => {
                // 在这里处理逻辑
                args.allData.buttonType = '2'
                return resolve(args)
            })
        },
        goBack () {
            this.$parent.hideEditPage()
        },
        //新增弹窗
        itemGridAddPopup ({ Vue, pageConfig, btn, groupCode }) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            this.curGroupCode = groupCode
            this.fieldSelectType = 'material'
            let url = '/base/barcode/elsBarcodeRuleHead/itemList'
            let columns = [
                { field: 'ruleNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_elsBarcodeRuleItemListf28_ruleNumber`, '单据编码') },
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseBarcodeInfoItemList951d_itemNumber`, '单据行号')},
                { field: 'businessType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm132_busDocType`, '业务单据类型') },
                { field: 'businessField', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_EStFJO_fa24b7fe`, '业务单据字段') },
                { field: 'businessFieldName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_tFJORL_d790c17a`, '单据字段名称') },
                { field: 'attributeType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WcAc_2b753bb9`, '属性类型') },
                { field: 'usedCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQKjIo_263b3421`, '是否使用简码') }
            ]
            this.$refs.fieldSelectModal.open(url, { 'headId': allData.ruleId }, columns, 'multiple')
        },
        checkItemSelectOk (data) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            let itemGrid = this.getItemGridRef('purchaseBarcodeInfoItemList')
            data.forEach(item => {
                item['id'] = null
                item['infoNumber'] = allData.infoNumber
            })
            itemGrid.insertAt(data, -1)
        },
        generateBarcode ({ Vue, pageConfig, btn, groupCode }) {
            let itemGrid = this.getItemGridRef(groupCode)
            const printNumber = this.getBusinessExtendData(this.businessRefName).allData.printNumber
            const purchaseBarcodeInfoItemList = this.getBusinessExtendData(this.businessRefName).allData.purchaseBarcodeInfoItemList
            if (purchaseBarcodeInfoItemList.length == 0) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyName`, '条码属性内容不能为空！'))
                return
            }
            const fn = (oldAttrList, index) => {
                let newList = new Array()
                oldAttrList.forEach(oldAttr => {
                    let attrArr = purchaseBarcodeInfoItemList[index].attribute.split(',')
                    attrArr.forEach(attr => {
                        let newAttr = oldAttr + attr
                        newList.push(newAttr)
                    })
                })
                return newList
            }

            // 获取第一条的数据
            let resultArr = new Array()
            let attrArr = purchaseBarcodeInfoItemList[0].attribute.split(',')
            attrArr.forEach(attr => {
                let attrList = new Array()
                attrList.push(attr)
                for (var i = 1; i < purchaseBarcodeInfoItemList.length; i++) {
                    attrList = fn(attrList, i)
                }
                attrList.forEach(a => {
                    let barcodeObj = {
                        barcode: a,
                        printNumber: printNumber
                    }
                    resultArr.push(barcodeObj)
                })
            })
            //插入行数据
            itemGrid.remove()
            itemGrid.insertAt(resultArr, -1)
        },
        customBarcode ({ Vue, pageConfig, btn, groupCode }) {
            let itemGrid = this.getItemGridRef(groupCode)
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)

            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {
                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            row.printNumber = this.getBusinessExtendData(this.businessRefName).allData.printNumber
            itemGrid.insertAt([row], -1)
        },
        supplierGridAddPopup ({ Vue, pageConfig, btn, groupCode }) {
            const allData = this.getBusinessExtendData(this.businessRefName).allData
            this.curGroupCode = groupCode
            this.fieldSelectType = 'material'
            let url = '/supplier/supplierMaster/list'
            let columns = [
                {
                    field: 'toElsAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                    width: 150
                },
                {
                    field: 'supplierCode',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
                    width: 150
                },
                {
                    field: 'supplierName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                    width: 200
                },
                {
                    field: 'supplierStatus_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'),
                    width: 200
                },
                { field: 'cateName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'), width: 200 }
            ]
            this.$refs.SupplierFieldSelectModal.open(url, {}, columns, 'multiple')
        },
        checkSupplierSelectOk (data) {
            let itemGrid = this.getItemGridRef('barcodeSupplierListList')
            data.forEach(item => {
                item['id'] = null
            })
            itemGrid.insertAt(data, -1)
        }

    }
}
</script>