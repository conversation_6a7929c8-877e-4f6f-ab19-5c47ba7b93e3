// import { EditConfig } from '@/plugins/table/gridConfig'

export const tableMixins = {
    data () {
        return {
            //默认表格配置
            gridConfig: {
                border: true,
                resizable: true,
                autoResize: true,
                keepSource: true,
                height: 'auto',
                showOverflow: true,
                showHeaderOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                data: [],
                mouseConfig: {
                    area: true, // 是否开启单元格区域选取
                    extension: true // 是否开启右下角延伸按钮
                },
                clipConfig: {
                    isCut: false,
                    isPaste: false
                },
                keyboardConfig: {
                    isClip: true, // 是否开启复制粘贴功能
                    isEdit: true, // 是否开启单元格选择编辑
                    isTab: true, // 是否开启TAB键左右移动功能
                    isArrow: true, // 是否开启非编辑状态下，上下左右移动功能
                    isEnter: true, // 是否开启回车移动上下行移动
                    isDel: true, // 是否开启删除键功能
                    isMerge: true, // 是否开合并和取消合并功能
                    isFNR: true, // 是否开启查找和替换功能
                    isChecked: true, // 是否开启空格键切换复选框和单选框状态
                    enterToTab: false // 是否将回车键行为改成 Tab 键行为
                },
                checkboxConfig: {
                    highlight: true,
                    reserve: true,
                    trigger: 'cell'
                },
                editConfig: {
                    trigger: 'click',
                    mode: 'cell',
                    showStatus: true
                }
            },
            editRules: {}
        }
    }
}
