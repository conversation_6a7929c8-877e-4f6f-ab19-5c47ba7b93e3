<template>
  <a-drawer
    :title="companyName+title"
    placement="right"
    :closable="false"
    :visible="visible"
    :width="860"
    @close="onClose"
  >
    <vxe-grid
      ref="employManagementGridRef"
      :edit-rules="validRules"
      :edit-config="{trigger: 'manual', mode: 'row'}"
      :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
      v-bind="gridOptions">
      <template #toolbar_buttons>
        <a-button
          size="small"
          type="primary"
          @click="add">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_VajR_2f90b9fb`, '新增员工') }}
        </a-button>
      </template>
      <template #grid_opration="{ row }">
        <template v-if="$refs.employManagementGridRef.isActiveByRow(row)">
          <a-button
            style="margin-right: 10px;"
            size="small"
            type="danger"
            @click="canCelRowEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
          </a-button>
          <a-button
            size="small"
            type="primary"
            @click="save(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}
          </a-button>
        </template>
        <template v-else>
          <a-button
            v-if="(!row.role || !row.role.includes('1')) "
            size="small"
            type="primary"
            @click="editRowEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑') }}
          </a-button>
          <a-button
            v-if="row.id && (!row.role || !row.role.includes('1')) "
            size="small"
            type="danger"
            @click="del(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_IG_f5bc9`, '移除') }}
          </a-button>
        </template>
      </template>
      <template
        #psnName_edit="{ row }">
        <a-auto-complete
          allowClear
          :notFoundContent="$srmI18n(`${$getLangAccount()}#i18n_field_VWumLLiSumL_e99989a1`,'请先到个人认证添加个人')"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_ddjRcRGiF_bf295669`, '搜索员工姓名并选择')"
          v-model="row.psnName"
          @select="applyUserNameOnSelect(row,row.psnName)"
          @search="applyUserNameOnSearch"
        >
          <template slot="dataSource">
            <a-select-option
              v-for="item in dataSource"
              :key="item"
              :title="item">
              <span>{{ item.split(",")[0] }}</span>
              <span
                class="ml-16 fs-14"
                style="color: #A0A2AA;">{{ item.split(",")[1] }}</span>
            </a-select-option>
          </template>
        </a-auto-complete>
      </template>
      <template #role_default="{ row }">
        <span>{{ row.role_dictText || formatRoleType(row.role) }}</span>
      </template>
      <template #role_edit="{ row }">
        <a-select
          mode="multiple"
          v-model="row.role">
          <a-select-option
            v-for="item in roleTypeOpt"
            :key="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <template #pager>
        <vxe-pager
          :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
          :current-page.sync="tablePage.currentPage"
          :page-size.sync="tablePage.pageSize"
          :total="tablePage.total"
          @page-change="handlePageChange">
        </vxe-pager>
      </template>
    </vxe-grid>
  </a-drawer>
</template>
<script>
import {getAction, postAction} from '@/api/manage'
import {isEmail, isMobile} from '@/utils/validate.js'
import {UserOutlined} from '@ant-design/icons'

export default {
    props: {
        companyName: {
            type: String,
            default: ''
        },
        row: {
            type: Object,
            default: () => {
                return {id: ''}
            }
        },
        visible: {
            type: Boolean,
            default: false
        }
    },
    data () {
        let phoneValidTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WNjltmKxiRWVVVWN_b59e3a24`, '输入的手机格式不正确，请重新输入')
        let emailValidTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_emailError`, '邮箱格式不正确')
        const applyContactValid = (data) => {
            let {cellValue, row} = data
            return new Promise((resolve, reject) => {
                if (row.applyContactType && row.applyContactType === 'MOBILE') {
                    if (cellValue && !isMobile(cellValue)) {
                        reject(new Error(phoneValidTip))
                    }
                } else if (row.applyContactType && row.applyContactType === 'EMAIL') {
                    if (cellValue && !isEmail(cellValue)) {
                        reject(new Error(emailValidTip))
                    }
                } else {
                    resolve()
                }
            })
        }
        return {
            editRow: {},
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jRRv_27c711d2`, '员工管理'),
            dataSource: [],
            dataSourceData: [],
            gridOptions: {
                border: false,
                size: 'mini',
                resizable: true,
                showOverflow: true,
                align: 'center',
                toolbarConfig: {
                    slots: {
                        // 自定义工具栏模板
                        buttons: 'toolbar_buttons'
                    }
                },
                editConfig: {
                    trigger: 'click',
                    mode: 'cell',
                    showStatus: true,
                    activeMethod: this.activeRowMethod
                },
                columns: [
                    {type: 'seq', width: 50},
                    {type: 'checkbox', width: 50},
                    {
                        field: 'psnName',
                        width: 150,
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'),
                        showOverflow: true,
                        editRender: {enabled: true},
                        slots: {
                            edit: 'psnName_edit',
                            default: ({ row }) => {
                                return [(<span>{ row.psnName }</span>)]
                            }
                        }
                    },
                    {
                        field: 'subAccount',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRWWWey_f8d6ad72`, '员工SRM账号'),
                        showOverflow: true
                    },
                    {
                        field: 'psnAccount',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPsjDeyBK_24d27a63`, 'E签宝用户账号标识'),
                        showOverflow: true
                    },
                    {
                        field: 'role',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRKy_27c8d82d`, '员工角色'),
                        editRender: {enabled: true},
                        slots: {default: 'role_default', edit: 'role_edit'}
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fixed: 'right',
                        slots: {default: 'grid_opration'}
                    }
                ],
                data: []
            },
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 20
            },
            validRules: {
                psnName: [
                    {required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lTiFIKRjR_966a33d8`, '必须选择已实名员工')}
                ]
            },
            roleTypeOpt: [
                {value: '2', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_WesRj_d0df4e04`, '印章保管员')},
                {value: '3', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_LjRvj_8e167f2b`, '成员管理员')}
            ]
        }
    },
    mounted () {
        this.getData()
        this.applyUserNameOnSearch('')
    },
    methods: {
        canCelRowEvent (row) {
            this.editRow = null
            this.$refs.employManagementGridRef.clearActived()
            if(row._state !== 'edit'){
                this.$refs.employManagementGridRef.remove(row)
                this.gridOptions.data = this.gridOptions.data.filter(x=>x._X_ROW_KEY!=row._X_ROW_KEY)
            }
        },
        editRowEvent (row) {
            this.editRow = row
            this.editRow._state = 'edit'
            this.$refs.employManagementGridRef.setActiveRow(row)
        },
        onClose () {
            this.$emit('close', false)
        },
        activeRowMethod ({row, rowIndex}) {
            if (row.id) {
                return false
            }
            return true
        },
        formatRoleType (val) {
            console.log('va:', val)
            if (!val) {
                return
            }
            let label = []
            this.roleTypeOpt.forEach((item) => {
                val.forEach((v) => {
                    if (item.value == v) {
                        label.push(item.label)
                    }
                })

            })
            return label.join(',')
        },
        // 员工搜索
        applyUserNameOnSearch (val) {
            this.dataSource = []
            this.dataSourceData = []
            let param = {name: val}
            this.dataSource = []
            getAction('/esignv3/purchaseEsignV3Personal/queryList', param).then((res) => {
                if (res && res.success) {
                    res.result.forEach((item) => {
                        if (item.psnName) {
                            this.dataSource.push(item.psnName + ',' + item.psnAccount)
                            this.dataSourceData.push(item)
                        }
                    })
                }
            })
        },
        // 员工选择
        applyUserNameOnSelect (row, psn) {
            let account = psn.split(',')[1]
            const item = this.dataSourceData.filter(item => {
                return item.psnAccount == account
            })
            if (item) {
                row.psnName = item[0].psnName
                row.psnAccount = item[0].psnAccount
                row.subAccount = item[0].subAccount
                row.psnId = item[0].id
                row.psnCode = item[0].psnId
                row.orgId = this.row.id
                row.orgCode = this.row.orgId
            }
        },
        getData () {
            let param = {orgId: this.row.id}
            getAction('/esignv3/purchaseEsignV3OrgPsn/list', param).then((res) => {
                if (res && res.success) {
                    this.gridOptions.data = res.result.records
                    this.tablePage.total = res.result.total
                } else {
                    this.$message.error(res.message)
                }
            })
        },
        handlePageChange ({currentPage, pageSize}) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            this.getData()
        },
        add () {
            let row = {}
            this.gridOptions.data.push(row)
            this.editRow = row
            this.$refs.employManagementGridRef.setActiveRow(row)
        },
        del (row) {
            if (row && row.id) {
                let that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQPmjRNtRsIG_1517b63f`, '是否将此员工从机构中移除？'),
                    onOk: function () {
                        postAction('/esignv3/purchaseEsignV3OrgPsn/removeStaff', row).then((res) => {
                            if (res && res.success) {
                                that.$message.success(res.message)
                                that.getData()
                            } else {
                                that.$message.error(res.message)
                            }
                        })                    }
                })

            } else {
                this.$refs.employManagementGridRef.remove(row)
            }
        },
        async save (row) {
            if(!row.psnId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWumLLiSujRjmLVHKKmGSujR_4cc8d295`,
                    '请先到个人认证添加员工的个人信息，再在此处添加员工'))
                return
            }
            // 插入一条数据并触发校验
            const errMap = await this.$refs.employManagementGridRef.validate(row).catch(errMap => errMap)
            if (!errMap) {
                let req = {...row}
                if(req.role){
                    req.role = req.role.join(',')
                }
                postAction('/esignv3/purchaseEsignV3OrgPsn/addStaff', req).then((res) => {
                    if (res && res.success) {
                        this.$message.success(res.message)
                        this.getData()
                    } else {
                        if (res.message == '网络异常，请检查网络连接') {
                            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAQLAEsLHcAZdROlbWVWeRSMlbKyyVHcROlbKHcROQLtk_54c76816`, '发起流程企业还未进行契约锁功能授权，请先通过获取授权链接接口进行功能授权在进行功能流程操作'))
                        } else {
                            this.$message.error(res.message)
                        }
                    }
                })
            }
        }
    }
}
</script>