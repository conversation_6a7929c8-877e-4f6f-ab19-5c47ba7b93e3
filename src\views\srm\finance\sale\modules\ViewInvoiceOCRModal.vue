<template>
  <div class="ViewInvoiceOCRModal">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="isView"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :current-edit-row="currentEditRow"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :collapseHeadCode="['baseForm','busRule','personFrom']"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
      <a-modal
    v-drag    
        centered
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
        :width="360"
        v-model="templateVisible"
        @ok="selectedTemplateAfter">
        <template slot="footer">
          <a-button
            key="back"
            @click="handleTempCancel">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
          </a-button>
          <a-button
            key="submit"
            type="primary"
            :loading="submitLoading"
            @click="selectedTemplateAfter">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
          </a-button>
        </template>
        <m-select
          v-model="templateNumber"
          :options="templateOpts"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"/>
      </a-modal>
    </a-spin>
  </div>
</template>

<script>

import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, httpAction, postAction} from '@/api/manage'
import {axios} from '@/utils/request'

export default {
    name: 'ViewInvoiceOCRModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            isView: false,
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            templateVisible: false,
            refresh: true,
            submitLoading: false,
            nextOpt: true,
            currentRow: {},
            templateNumber: undefined,
            templateOpts: [],
            businessType: 'order',
            showHelpTip: false,
            editRowModal: false,
            previewModal: false,
            orderAllow: true,
            notShowTableSeq: true,
            editItemRow: {},
            currentItemContent: '',
            previewContent: '',
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/reconciliation/invoiceOcrData/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                invoiceOcrDataOneList: [

                ],
                invoiceProductDataList: [

                ],
                invoiceOcrDataTwoList: [

                ],
                invoiceOcrDataThreeList: [

                ],
                invoiceOcrDataFourList: [

                ],
                invoiceOcrDataFiveList: [

                ],
                invoiceOcrDataSixList: [

                ],
                invoiceOcrDataSevenList: [

                ],
                invoiceOcrDataEightList: [

                ],
                invoiceOcrDataNineList: [

                ],
                invoiceOcrDataTenList: [

                ],
                invoiceOcrDataElevenList: [

                ],
                invoiceOcrDataTwelveList: [

                ],
                invoiceOcrDataThirteenList: [

                ],
                invoiceOcrDataFourteenList: [

                ]
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.preview
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                detail: '/contract/purchaseContractHead/queryById'
            }
        }
    },

    computed: {
        remoteJsFilePath () {
            let templateNumber = 'TC2021121401'
            let templateVersion = '1'
            let account = '100000'
            return `${account}/purchase_invoiceOcr_${templateNumber}_${templateVersion}`
        }
    },
    mounted () {
        const  that = this
        getAction('/reconciliation/invoiceOcrData/queryById', {id: this.currentEditRow.id}).then(res=>{
            if(res.success){
                if (res.result) {
                    that.isView = true
                } else {
                    this.$message.error('查询失败')
                }
            }
        })
    },
    methods: {
        confirmEdit () {},
        handleBeforeRemoteConfigData (){
        },
        handleAfterDealSource (pageConfig, resultData){
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
            }
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            //this.externalToolBar['purchaseAttachmentList'][0].args.itemInfo = itemInfo
            if (resultData.invoiceOcrDataOneList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataOneList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataOneList', false)
            }
            if (resultData.invoiceProductDataList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceProductDataList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceProductDataList', false)
            }
            if (resultData.invoiceOcrDataTwoList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataTwoList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataTwoList', false)
            }
            if (resultData.invoiceOcrDataThreeList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataThreeList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataThreeList', false)
            }
            if (resultData.invoiceOcrDataFourList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataFourList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataFourList', false)
            }
            if (resultData.invoiceOcrDataFiveList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataFiveList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataFiveList', false)
            }
            if (resultData.invoiceOcrDataSixList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataSixList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataSixList', false)
            }
            if (resultData.invoiceOcrDataSevenList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataSevenList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataSevenList', false)
            }
            if (resultData.invoiceOcrDataEightList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataEightList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataEightList', false)
            }
            if (resultData.invoiceOcrDataNineList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataNineList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataNineList', false)
            }
            if (resultData.invoiceOcrDataTenList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataTenList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataTenList', false)
            }
            if (resultData.invoiceOcrDataElevenList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataElevenList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataElevenList', false)
            }
            if (resultData.invoiceOcrDataTwelveList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataTwelveList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataTwelveList', false)
            }
            if (resultData.invoiceOcrDataThirteenList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataThirteenList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataThirteenList', false)
            }
            if (resultData.invoiceOcrDataFourteenList == null) {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataFourteenList', true)
            }else {
                this.hideSingleGroup(this.businessRefName, 'invoiceOcrDataFourteenList', false)
            }
        },
        preview () {
            let parm = this.getAllData()
            this.$previewFile.open({path: parm.filePath })
        },
        createOrder () {
            this.nextOpt= true
            this.serachTemplate('order')
        },
        serachTemplate (businessType) {
            this.currentRow = {}
            debugger
            if (businessType == 'order') {
                let selectedRows = this.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
                if (selectedRows.length <= 0) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
                    return
                } else {
                    selectedRows.forEach((item, i) => {
                        if (item.sourceType == 'order') {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行来源为订单行')
                            this.nextOpt = false
                            return
                        }
                    })
                    if (this.nextOpt) {
                        this.businessType = businessType
                        this.openModal()
                    }
                }
            }
        },
        queryTemplateList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: this.businessType}
            return getAction('/template/templateHead/getListByType', params)
        },
        openModal () {
            this.queryTemplateList(this.$ls.get('Login_elsAccount')).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.templateNumber,
                                title: item.templateName,
                                version: item.templateVersion,
                                account: item.elsAccount
                            }
                        })
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length===1) {
                            this.templateNumber = this.templateOpts[0].value
                            this.selectedTemplateAfter()
                        } else {
                            // 有多个模板先选择在新建
                            this.templateVisible = true
                        }
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        handleTempCancel () {
            this.templateVisible = false
        },
        selectedTemplateAfter () {
            if(this.templateNumber) {
                const that = this
                let parm = that.getAllData()
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version,
                    templateAccount: template[0].account,
                    purchaseContractItemList: [],
                    contractItemCustom1List: []
                }
                if (this.businessType=='order') {
                    this.url.temp = this.url.createOrderByItems
                }
                if (this.businessType=='contractPromise'){
                    this.url.temp = this.url.createPromiseByItems
                }
                if (parm.showItem == '1') {
                    params.purchaseContractItemList =that.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
                }
                if (parm.showCustom1 == '1') {
                    params.contractItemCustom1List =that.getItemGridRef('contractItemCustom1List').getCheckboxRecords()
                }
                that.templateVisible = false
                that.submitLoading = false
                if (this.url.temp==''){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
                    return
                }
                that.postUpdateData(this.url.temp, params)
            }
        },
        postUpdateData (url, row) {
            this.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.init()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    }
}
</script>