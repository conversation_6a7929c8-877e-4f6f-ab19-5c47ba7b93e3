<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      refresh
      :currentEditRow="currentEditRow"
      :url="url" />
    <field-select-modal ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { uniq, uniqBy } from 'lodash'
import { PURCHASEATTACHMENTDOWNLOADAPI } from '@/utils/const'

import { formatDate } from '@/filters'

export default {
    name: 'PurchaseEcnEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            selectType: 'material',
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_affectedOrders`, '受影响的订单'),
                        groupCode: 'orderDetails',
                        type: 'grid',
                        custom: {
                            ref: 'orderDetails',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), width: 120 },
                                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 120 },
                                { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 120 },
                                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                                { field: 'itemStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lineStatus`, '行状态'), width: 120, visible: false },
                                { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lineStatus`, '行状态'), width: 120 },
                                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 120 },
                                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 120 },
                                { field: 'receiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantity`, '收货数量'), width: 120 },
                                { field: 'onWayQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_onWayQuantity`, '在途数量'), width: 120 },
                                { field: 'notDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`, '未交货数量'), width: 120 },
                                { field: 'orderCreateBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreator`, '订单创建人'), width: 120 },
                                { field: 'orderCreateTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderCreatTime`, '订单创建时间'), width: 120 }
                            ],
                            buttons: [
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addOrderDetailItem },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteOrderDetailItem }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_vendorPushList`, '供应商推送列表'),
                        groupCode: 'ecnSupplierListList',
                        type: 'grid',
                        custom: {
                            ref: 'ecnSupplierListList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), width: 120 },
                                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 120 },
                                { field: 'relevanceOrderVender', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQLItRKRdX_1e48fa76`, '是否为订单关联供应商'), width: 200, visible: false },
                                { field: 'relevanceOrderVender_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQLItRKRdX_1e48fa76`, '是否为订单关联供应商'), width: 200 }
                            ],
                            buttons: [{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addSupplierItem }],
                            rules: {
                                subAccount: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_XHDcLxOLV_49b3c181`, '消息提醒人不能为空') }]
                            },
                            showOptColumn: true,
                            optColumnList: [{ type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteSupplierEvent, allow: this.allowSupplierShow }]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_internalRemindeLlist`, '内部提醒列表'),
                        groupCode: 'ecnBuyerListList',
                        type: 'grid',
                        custom: {
                            ref: 'ecnBuyerListList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 120 },
                                { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), width: 120 },
                                { field: 'department', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_department`, '部门'), width: 120 },
                                { field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), width: 200 }
                            ],
                            buttons: [
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addBuyerItem },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteBuyerItemEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'attachments',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                                { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                            ],
                            buttons: [
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                    type: 'upload', businessType: 'ecn',
                                    attr: this.attrHandle,
                                    callBack: this.uploadCallBack },
                                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch}
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                            ]
                        }
                    }
                ],
                formFields: [],
                footerButtons: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'), type: 'primary', click: this.submitAuditEvent, authorityCode: 'ecn#purchaseEcnHead:submit'},
                    // { title: '未完结订单', type: 'primary', click: this.viewOrderEvent},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/ecn/purchaseEcn/add',
                edit: '/ecn/purchaseEcn/edit',
                detail: '/ecn/purchaseEcn/queryById',
                submitAudit: '/elsUflo/audit/submit',
                upload: '/attachment/purchaseAttachment/upload',
                download: PURCHASEATTACHMENTDOWNLOADAPI
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${account}/purchase_ecn_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    methods: {
        beforeHandleData (config) {
            if (this.currentEditRow.id) {
                return
            }
            const formFields = config.formFields || ''
            formFields.forEach(n => {
                if (n.fieldName === 'changeApplyTime') {
                    let dataFormat = n.dataFormat || 'yyyy-MM-dd'
                    n.defaultValue = formatDate(this.currentEditRow._timestamp, dataFormat) || ''
                }
            })
        },
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.ecnNumber || '',
                actionRoutePath: '/srm/ecn/purchase/PurchaseEcnList,/srm/ecn/sale/SaleEcnList'
            }
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        init () {
            if (this.currentEditRow) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id, this.getItemNumberOptions)
            }
        },
        allowSupplierShow (row) {
            if (row.relevanceOrderVender === '1') {
                return true
            }
            return false
        },
        deleteSupplierEvent (row) {
            if (row.relevanceOrderVender === '1') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_thisSupplierSupplierAffectedRrderCannotDeleted`, '该供应商是[受影响的订单]中的供应商，不能删除'))
                return
            }
            let itemGrid = this.$refs.editPage.$refs.ecnSupplierListList[0]
            itemGrid.remove(row)
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.attachments[0]
            fileGrid.insertAt(result, -1)
        },
        deleteBatch () {
            const fileGrid = this.$refs.editPage.$refs.attachments[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => n.id).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.attachments[0]
            const id = row.id
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/delete', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        addBuyerItem () {
            this.selectType = 'buyer'
            let url = '/account/elsSubAccount/getSubAccounts'
            let columns = [
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 150 },
                { field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), width: 200 },
                { field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), width: 200 },
                { field: 'orgCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_department`, '部门'), width: 200 }
            ]
            let params = {}
            this.$refs.fieldSelectModal.open(url, params, columns, 'multiple')
        },
        deleteBuyerItemEvent () {
            let itemGrid = this.$refs.editPage.$refs.ecnBuyerListList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        deleteOrderDetailItem () {
            let itemGrid = this.$refs.editPage.$refs.orderDetails[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            //获取删除行供应商列表
            let removeOrderSupplier = []
            for (let item of checkboxRecords) {
                removeOrderSupplier.push(item.toElsAccount)
            }
            itemGrid.removeCheckboxRow()
            removeOrderSupplier = uniq(removeOrderSupplier)
            console.log('removeOrderSupplier', removeOrderSupplier)
            const formData = this.$refs.editPage.getPageData()
            //获取剩余行供应商列表
            let orderSupplier = []
            for (let item of formData.orderDetails) {
                orderSupplier.push(item.toElsAccount)
            }
            orderSupplier = uniq(orderSupplier)
            console.log('orderSupplier', orderSupplier)
            //removeOrderSupplier不存在orderSupplier中的供应商
            let notExitSupplier = []
            for (let string of removeOrderSupplier) {
                if (!orderSupplier.includes(string)) {
                    notExitSupplier.push(string)
                }
            }
            console.log('notExitSupplier', notExitSupplier)
            for (let item of formData.ecnSupplierListList) {
                if (notExitSupplier.includes(item.toElsAccount)) {
                    item.relevanceOrderVender = '0'
                    item.relevanceOrderVender_dictText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否')
                }
            }
        },
        addSupplierItem () {
            this.selectType = 'supplier'
            // const form = this.$refs.editPage.getPageData()
            let url = '/enterprise/elsEnterpriseInfo/queryAccessSupplier'
            let columns = [
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), width: 150 },
                { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 200 },
                { field: 'fbk1', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'), width: 150 }
            ]
            let params = {}
            this.$refs.fieldSelectModal.open(url, params, columns, 'multiple')
        },
        fieldSelectOk (data) {
            if (this.selectType == 'supplier') {
                let arr = data.map(({ elsAccount, name, fbk1 }) => ({ toElsAccount: elsAccount, supplierName: name, supplierCode: fbk1 }))
                let itemGrid = this.$refs.editPage.$refs.ecnSupplierListList[0]
                let { fullData } = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.toElsAccount
                })
                let insertData = arr.filter(item => {
                    return !regulations.includes(item.toElsAccount)
                })
                itemGrid.insertAt(insertData)
            } else if (this.selectType == 'buyer') {
                let arr = data.map(({ subAccount, realname, email, orgCode }) => ({ subAccount: subAccount, name: realname, email: email, department: orgCode }))
                let itemGrid = this.$refs.editPage.$refs.ecnBuyerListList[0]
                let { fullData } = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.subAccount
                })
                let insertData = arr.filter(item => {
                    return !regulations.includes(item.subAccount)
                })
                itemGrid.insertAt(insertData)
            } else if (this.selectType == 'order') {
                let arr = data.map(({ createBy, createTime, ...others }) => ({ orderCreateBy: createBy, orderCreateTime: createTime, ...others }))
                let itemGrid = this.$refs.editPage.$refs.orderDetails[0]
                let { fullData } = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.toElsAccount + '_' + item.orderNumber + '_' + item.itemNumber
                })
                let insertData = arr.filter(item => {
                    return !regulations.includes(item.toElsAccount + '_' + item.orderNumber + '_' + item.itemNumber)
                })
                itemGrid.insertAt(insertData)

                let suppliers = data.map(({ toElsAccount, supplierName }) => ({ toElsAccount: toElsAccount, supplierName: supplierName, relevanceOrderVender: '1', relevanceOrderVender_dictText: '是' }))
                let supplierGrid = this.$refs.editPage.$refs.ecnSupplierListList[0]
                let supplierData = supplierGrid.getTableData()
                let mixArr = [...suppliers, ...supplierData.fullData]
                console.log('mixArr', mixArr)
                mixArr = uniqBy(mixArr, 'toElsAccount')

                console.log('mixArrUniqBy', mixArr)

                supplierGrid.remove()
                supplierGrid.insertAt(mixArr)
            }
        },
        addOrderDetailItem () {
            const form = this.$refs.editPage.getPageData()
            let materialNumber = ''
            if (form.relevanceOrder === '1') {
                if (!form.materialNumber) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectMaterialCodeFirst`, '先选择物料编码'))
                    return
                } else {
                    materialNumber = form.materialNumber
                }
            }
            this.selectType = 'order'
            let url = '/order/purchaseOrderItem/queryUnfinishedItems'
            let columns = [
                { field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), width: 150 },
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 150 },
                { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 150 },
                // {field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 150},
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 100 },
                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150 },
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                { field: 'notDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`, '未交货数量'), width: 150 }
            ]
            let params = { materialNumber: materialNumber }
            this.$refs.fieldSelectModal.open(url, params, columns, 'multiple')
        },
        saveEvent () {
            const formData = this.$refs.editPage.getPageData()
            let url = this.currentEditRow.id ? this.url.edit : this.url.add
            this.$refs.editPage.confirmLoading = true
            postAction(url, formData)
                .then(res => {
                    if (res.success) {
                        if (!this.currentEditRow.id) {
                            this.currentEditRow = res.result
                        }
                        this.$message.success(res.message)
                        this.init()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.$refs.editPage.confirmLoading = false
                })
        },
        submitAuditEvent () {
            const formData = this.$refs.editPage.getPageData()
            if (!formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }
            //如果供应商列表为空则提示请选择供应商
            if (!formData.ecnSupplierListList.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFRdX_f2ffa076`, '请选择供应商'))
                return
            }
            const handlePromise = (list = []) =>
                list.map(promise =>
                    promise.then(
                        res => ({
                            status: 'success',
                            res
                        }),
                        err => ({
                            status: 'error',
                            err
                        })
                    )
                )
            let promise = this.$refs.editPage.setPromise()
            const _this = this
            Promise.all(handlePromise(promise))
                .then(result => {
                    let flag = false
                    for (let i = 0; i < result.length; i++) {
                        if (result[i].status === 'success') {
                            flag = true
                        } else {
                            this.$refs.editPage.currentStep = i
                            return
                        }
                    }
                    if (flag) {
                        let orderDetail = formData.orderDetails
                        if (formData.relevanceOrder === '1' && orderDetail.length < 1) {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_thisDocumentNeedsAssociatedWithOrderDetails`, '该单据需要关联订单明细'))
                            return
                        }
                        //先保存再提交审批
                        // this.$refs.editPage.confirmLoading = true
                        postAction(this.url.edit, formData).then(res => {
                            if (res.success) {
                                let param = {}
                                param['businessId'] = formData.id
                                param['rootProcessInstanceId'] = formData.flowId
                                param['businessType'] = 'ecn'
                                param['auditSubject'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ECNDocNo`, 'ECN单据号：') + formData.ecnNumber
                                param['params'] = JSON.stringify(formData)
                                if (formData.needApprove == '1') {
                                    this.$confirm({
                                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLDJ_38d7204a`, '确认提交'),
                                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApproval`, '确认提交审批'),
                                        onOk: function () {
                                            _this.loading = true
                                            postAction(_this.url.submitAudit, param)
                                                .then(res => {
                                                    if (res.success) {
                                                        _this.$message.success(_this.$srmI18n(`${_this.$getLangAccount()}#i18n_field_DJUzLR_d20aaa5b`, '提交审批成功'))
                                                        _this.$parent.submitCallBack(formData)
                                                        _this.goBack()
                                                    } else {
                                                        _this.$message.warning(res.message)
                                                    }
                                                })
                                                .finally(() => {
                                                    _this.loading = false
                                                })
                                        }
                                    })
                                } else {
                                    const formData = this.$refs.editPage.getPageData()
                                    let url = this.currentEditRow.id ? this.url.edit : this.url.add
                                    formData.auditStatus = '4'
                                    formData.sendStatus = '0'
                                    formData.ecnStatus = '1'
                                    this.$confirm({
                                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLDJ_392810a1`, '是否确认提交'),
                                        onOk: function () {
                                            _this.loading = true
                                            postAction(url, formData)
                                                .then(res => {
                                                    if (res.success) {
                                                        _this.$message.success(_this.$srmI18n(`${_this.$getLangAccount()}#i18n_field_DJLR_2e92b263`, '提交成功'))
                                                        _this.goBack()
                                                    } else {
                                                        _this.$message.warning(res.message)
                                                    }
                                                })
                                                .finally(() => {
                                                    _this.loading = false
                                                })
                                        }
                                    })
                                }
                            } else {
                                this.$message.warning(res.message)
                                this.$refs.editPage.confirmLoading = false
                            }
                        })
                    }
                })
                .catch(err => {
                    console.log(err)
                })
        }
    }
}
</script>
