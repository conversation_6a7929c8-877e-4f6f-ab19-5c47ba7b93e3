<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-content">
          <div class="title">
            <span class="margin-r-20">{{ allData.title }}</span>
            <span class="margin-r-20">{{ allData.questionerName }}</span>
            <span>{{ allData.createTime }}</span>
          </div>
          <div class="record">
            <div style="padding: 6px 6px 6px 6px">{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_fItH_39da7322`, '答疑记录')) }}</div>
            <div
              class="record-replyContent"
              :style="{height: pageContentHeight}">
              <!-- <a-steps
                progress-dot
                :current="nodeData.length"
                direction="vertical">
                <a-step
                  v-for="item in nodeData"
                  :key="item.id">
                  <template #title="{slotProps}">
                    <p style="font-size: 14px;font-weight: bold;">{{ getTitle(item) }}</p>
                  </template>
                  <template #description="{slotProps}">
                    <div class="replyContent-item">
                      <p style="padding: 6px 15px;margin-bottom: 0px">{{ item.replyContent }}</p>
                      <div class="fileList">
                        <a-button
                          type="link"
                          v-for="(file,index) in item.purchaseAttachmentList"
                          :key="index"
                          @click="download(file)">{{ file.fileName }}</a-button>
                      </div>
                    </div>
                  </template>
                </a-step>
              </a-steps> -->
              <div
                v-for="item in nodeData"
                :key="item.id">
                <card
                  :nodeData="item"></card>
              </div>
            </div>
          </div>
          <div
            class="reply"
            v-if="check!=true">
            <div style="padding: 6px 6px 6px 6px">{{ $srmI18n(`${$getLangAccount()}#i18n_field_MBQD_28ddde99`, '回复问题') }}</div>
            <div class="reply-Content">
              <a-form-model
                ref="form"
                :label-col="labelCol"
                :wrapper-col="wrapperCol"
                :model="formData"
              >
                <a-row>
                  <a-col :span="24">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_MBCc_28d55903`, '回复内容')"
                    >
                      <a-textarea v-model="formData.replyContent"></a-textarea>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="24">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_title_attachment`, '附件')"
                    >
                      <a-upload
                        name="file"
                        :multiple="true"
                        :showUploadList="false"
                        :action="uploadUrl"
                        :headers="uploadHeader"
                        :accept="accept"
                        :data="{headId: currentEditRow.id, businessType: 'biddingPlatform', 'sourceNumber': tenderCurrentRow.tenderProjectNumber || tenderCurrentRow.id || '', 'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'}"
                        @change="handleUploadChange"
                      >
                        <a-button type="primary"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                      </a-upload>
                      <div
                        v-for="(fileItem, index) in formData.purchaseAttachmentList"
                        :key="fileItem.id">
                        <span style="color: blue; cursor:pointer; margin-right:8px" >{{ fileItem.fileName }}</span>
                        <a-icon
                          type="delete"
                          @click="handleDeleteFile(index)"/>
                        <a
                          style="margin-right:8px"
                          @click="preViewEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                        <a
                          @click="downloadEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>    
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </a-form-model>
            </div>
          </div>
        </div>
        <div class="page-footer">
          <a-button @click="() => {this.$emit('hide')}">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          <a-button
            type="primary"
            @click="handleSave"
            v-if="check!=true">{{ $srmI18n(`${$getLangAccount()}#i18n_title_reply`, '回复') }}</a-button>
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
import {USER_INFO} from '@/store/mutation-types'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
import card from '../components/card'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    mixins: [baseMixins],
    inject: [
        'subpackageId',
        'tenderCurrentRow'
    ],
    components: {
        card
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        check: {
            type: Boolean,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            labelCol: { span: 2 },
            wrapperCol: { span: 15 },
            allData: {},
            nodeData: [],
            formData: {
                purchaseAttachmentList: []
            },
            rules: { },
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            requestData: {
                detail: { url: '/tender/purchaseTenderMentoringHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            url: {
                reply: '/tender/purchaseTenderMentoringHead/reply'
            }
        }
    },
    computed: {
        pageContentHeight () {
            let height = document.body.clientHeight - 300
            return height + 'px'
        }
    },
    methods: {
        getTitle (item) {
            return `${item.createTime}  ${item.responderName}  ${item.responderType == '1'? this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_fBC_1da24a0`, '答复方') : this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_DGC_18102cf`, '提出方')}`
        },
        /**
         * 获取当前用户信息
         */
        getUserInfo () {
            return this.$ls.get(USER_INFO)
        },
        handleSave () {
            if (!this.formData.replyContent) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNMBCc_affd1aec`, '请输入回复内容'))
            let {elsAccount, realname} = this.getUserInfo()
            let {subpackageId, headId, tenderProjectId} = this.allData
            headId = this.allData.purchaseTenderMentoringItemList[0].headId
            let params = Object.assign({
                subpackageId, 
                headId, 
                tenderProjectId,
                responder: elsAccount,
                responderName: realname,
                responderType: '1'
            }, this.formData)
            console.log('params', this.allData)
            valiStringLength(params, [
                {field: 'replyContent', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MBCc_28d55903`, '回复内容'), maxLength: 1000}
            ])
            this.confirmLoading = true
            postAction(this.url.reply, params).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    if (!this.formData.id) {
                        this.$set(this.formData, 'id', res.result.id)
                    }
                    this.formData = {}
                }
            }).finally(() => {
                this.confirmLoading = false
                this.queryDetail()
            })
        },
        async queryDetail () {
            let url = this.requestData.detail.url
            let args = this.requestData.detail.args(this)
            this.confirmLoading = true
            let query = await getAction(url, args)
            this.confirmLoading = false
            if (query && query.success) {
                this.allData = Object.assign({}, query.result)
                this.nodeData = this.allData.purchaseTenderMentoringItemList
            } else {
                this.$message.error(query.message)
            }
        },
        async download (file) {
            const [fileName] = file.fileName
            file.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(file)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleDeleteFile (index) {
            this.formData.purchaseAttachmentList.splice(index, 1)
        },
        preViewEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            this.$previewFile.open({params: row })
        },
        async downloadEvent (row) {
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    let fileListData = file.response.result
                    fileListData.uploadElsAccount = this.$ls.get(USER_INFO).elsAccount
                    fileListData.uploadSubAccount = this.$ls.get(USER_INFO).subAccount
                    fileListData.uid = file.response.result.id
                    fileListData.name = file.response.result.fileName
                    fileListData.url = file.response.result.filePath
                    if (!this.formData.purchaseAttachmentList) this.formData.purchaseAttachmentList = []
                    this.formData.purchaseAttachmentList.push(fileListData)
                    console.log(this.formData.purchaseAttachmentList)
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        }
    },
    mounted () {
        this.queryDetail()
    }
}
</script>
<style lang="less" scoped>

.title{
  padding: 8px;
  background: #F2F3F5;
}
.margin-r-20{
  margin-right: 20px
}
.record-replyContent, .reply-Content{
  padding: 0 6px;
  overflow: auto;
  border: 1px solid #F2F3F5;
}
.replyContent-item {
  border: 1px solid #F2F3F5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)
}
:deep(.ant-steps-dot .ant-steps-item-content){
  width: 90%;
}
:deep(.ant-form-item){
  margin-bottom: 0px;
}
</style>


