<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin 
        :spinning="confirmLoading">
        <div class="page-header">
          <div class="page-content-title"><span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_OVsu_2aa78586`, '多轮报价') }}</span></div>
        </div>
        <div class="page-content">
          <div v-if="!showpriceOpeningsList">
            <titleCrtl>
              <span> {{ $srmI18n(`${$getLangAccount()}#i18n_field_RdXVH_8e115b7b`, '供应商信息') }}</span>
              <template
                slot="right"
                v-if="isEdit">
                <a-button
                  type="primary"
                  style="margin-right: 10px"
                  @click="init">{{ $srmI18n(`${$getLangAccount()}#i18n_title_refresh`, '刷新') }}</a-button>
                <a-button
                  v-if="canMultipleQuotes"
                  type="primary"
                  @click="handleAddQuotes">{{ $srmI18n(`${$getLangAccount()}#i18n_field_hAVVmsu_17543f4f`, '发起新轮次报价') }}</a-button>
              </template>
            </titleCrtl>
            <listTable
              ref="listTable"
              :fromSourceData="fromSourceData"
              :setGridHeight="'auto'"
              :showTablePage="false"
              :checkedConfig="checkedConfig"
              :statictableColumns="statictableColumns">
            </listTable>
            <quotesItem
              @closeQuotes="closeQuotes"
              :pageStatus="pageStatus"
              @showPriceList="showPriceList"
              :quotesList="quotesList"/>
          </div>
          <tenderBidLetterVoList
            ref="tenderBidLetterVoList"
            v-else
            :importBaseMixins="false"
            :fromSourceData="tenderBidLetterVoList"
            :pageStatus="'detail'"></tenderBidLetterVoList>
        </div>
        <div class="page-footer">
          <a-button 
            @click="() => {this.showpriceOpeningsList = false}" 
            v-if="showpriceOpeningsList">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          <a-button 
            @click="goBack" 
            v-else>{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
        </div>
        <a-modal
          v-drag    
          :width="440"
          :title="$srmI18n(`${$getLangAccount()}#i18n_field_OVsu_2aa78586`, '多轮报价')"
          :visible="visible"
          :confirm-loading="loading"
          @ok="handleOk"
          @cancel="handleCancel">
          <span style="margin-right: 10px">{{ $srmI18n(`${$getLangAccount()}#i18n_field_suyRKIW_9441ecf2`, '报价截止时间：') }}</span>
          <a-date-picker
            v-model="quotedPriceEndTime"
            show-time
            format="YYYY-MM-DD HH:mm:ss"/>
        </a-modal>
      </a-spin>
    </div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { USER_INFO } from '@/store/mutation-types'
import titleCrtl from '../../BiddingHall/components/title-crtl'
import listTable from '../../BiddingHall/components/listTable'
import quotesItem from './components/quotesItem'
import tenderBidLetterVoList from '@views/srm/bidding_new/TenderHall/DocumentSubmitOfCa/components/tenderBidLetterVoList'
export default {
    computed: {
        headParams () {
            let {checkType, currentStep, processType } = this.currentEditRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return {xNodeId}
        },
        isEdit () {
            return this.pageStatus == 'edit' ? true : false
        }
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        show: {
            type: Boolean,
            default () {
                return {}
            }
        },
        pageStatus: {
            type: String,
            default: 'edit'
        },
        canMultipleQuotes: {
            type: Boolean,
            default () {
                return {}
            }
        }
    },
    components: {
        titleCrtl,
        listTable,
        quotesItem,
        tenderBidLetterVoList
    },
    watch: {
        show (val) {
            if (val) {
                this.init()
            }
        }
    },
    data () {
        return {
            visible: false,
            confirmLoading: false,
            loading: false,
            fromSourceData: [],
            statictableColumns: [
                {
                    'type': 'checkbox',
                    'width': 50
                },
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n__RdXRL_8e11f650`, '供应商名称'),
                    'field': 'supplierName'
                },
                {
                    'width': 120,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQKnD_c8206068`, '是否联合体'),
                    'field': 'combination_dictText'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KnDRL_37c77362`, '联合体名称'),
                    'field': 'combinationName'
                },
                {
                    'width': 120,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_recordStatus`, '状态'),
                    'field': 'inviteQuotedPriceStatus_dictText'
                }
            ],
            url: {
                getDecryptSupplierUrl: '/tender/purchase/supplierTenderProjectMasterInfo/getDecryptSupplier',
                getStagelistUrl: '/tender/evaluation/tenderEvaQuotedPriceHead/list',
                add: '/tender/evaluation/tenderEvaQuotedPriceHead/add'
            },
            checkedConfig: {
                checkMethod: ({row}) => {
                    // 当前分包不能勾选
                    if (row.inviteQuotedPriceStatus == '0') {
                        return true
                    }
                    return false
                }
            },
            quotesList: [],
            selectedData: [],
            quotedPriceEndTime: '',
            showpriceOpeningsList: false,
            tenderBidLetterVoList: []
        }
    },
    methods: {
        handleAddQuotes () {
            this.selectedData = this.$refs.listTable.getCheckboxRecords().map(item => {
                return {
                    supplierAccount: item.supplierAccount,
                    supplierName: item.supplierName
                }
            })
            if (this.selectedData.length < 1) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFRdX_f2ffa076`, '请选择供应商'))
            this.visible = true
            this.quotedPriceEndTime = ''
        },
        handleOk () {
            let {subpackageId, evaInfoId, tenderProjectId} = this.currentEditRow
            if (!this.quotedPriceEndTime) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFsuyRKI_aa2d701f`, '请选择报价截止时间'))
            let params = {
                subpackageId,
                evaInfoId,
                tenderProjectId: tenderProjectId,
                tenderEvaQuotedPriceItemList: this.selectedData,
                quotedPriceEndTime: new Date(this.quotedPriceEndTime).getTime(),
                quotedType: '0' //多轮报价
            }
            this.loading = true
            this.confirmLoading = true
            postAction(this.url.add, params).then(res => {
                let type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.init()
                    this.visible = false
                }
            }).finally(() => {
                this.confirmLoading = false
                this.loading = false
            })
        },
        handleCancel () {
            this.visible = false
            this.loading = false
        },
        getDecryptSupplier () {
            let params = {
                subpackageId: this.currentEditRow.subpackageId
            }
            this.confirmLoading = true
            getAction(this.url.getDecryptSupplierUrl, params, {headers: this.headParams}).then(res => {
                if (res.success) {
                    this.fromSourceData = res.result || []
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 获取轮次数据
        getStagelist () {
            let params = {
                subpackageId: this.currentEditRow.subpackageId,
                evaInfoId: this.currentEditRow.evaInfoId,
                quotedType: '0' //多轮报价
            }
            this.confirmLoading = true
            getAction(this.url.getStagelistUrl, params).then(res => {
                if (res.success) {
                    this.quotesList = res.result || []
                    // 头状态传递给行里面用来控制是否能查看行详情
                    this.quotesList.map(item => {
                        item.tenderEvaQuotedPriceItemList.map(row => {
                            this.$set(row, 'headStatus', item.status)
                        })
                    })
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        closeQuotes () {
            this.init()
        },
        showPriceList (data) {
            let d = JSON.parse(JSON.stringify(data))
            let processType = '0'
            d.map(item => {
                if (item.processType == '1') processType = '1'
                if (item.priceOpeningsList) {
                    item.priceOpeningsList[0]['customizeFieldData'] = JSON.parse(item.priceOpeningsList[0]['customizeFieldData'])
                    item.priceOpeningsList[0]['customizeFieldModel'] = JSON.parse(item.priceOpeningsList[0]['customizeFieldModel'])
                    item.priceOpeningsList[0]['customizeFieldModel'].forEach(item=>{
                        item.must = item.must == '1' ? true : false
                    })
                }
            })
            this.tenderBidLetterVoList = {
                tenderBidLetterVoList: d,
                processType
            }
            this.showpriceOpeningsList = true
        },
        // 返回
        goBack () {
            this.$parent.showQuotes = false
        },
        init () {
            this.getDecryptSupplier()
            this.getStagelist()
        }
    },
    mounted () {
        this.init()
        console.log('this.currentEditRow', this.currentEditRow)
    }
}
</script>

<style lang="less" scoped>
</style>