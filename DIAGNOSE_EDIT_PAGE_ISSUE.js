// 🔍 编辑页面问题根因诊断脚本
// 请在编辑页面的浏览器控制台中执行此脚本

console.log('🔍 开始编辑页面问题根因诊断...');
console.log('='.repeat(60));

// 1. 基础环境检测
console.log('📋 1. 基础环境检测');
const basicInfo = {
    currentURL: window.location.href,
    userAgent: navigator.userAgent,
    windowSize: `${window.innerWidth} × ${window.innerHeight}`,
    documentSize: `${document.documentElement.clientWidth} × ${document.documentElement.clientHeight}`,
    timestamp: new Date().toLocaleString()
};

Object.keys(basicInfo).forEach(key => {
    console.log(`- ${key}: ${basicInfo[key]}`);
});

// 2. 页面类型识别
console.log('\n📋 2. 页面类型识别');
const pageTypeChecks = {
    'URL包含edit': window.location.href.includes('edit'),
    'URL包含detail': window.location.href.includes('detail'),
    'URL包含supplier': window.location.href.includes('supplier'),
    'URL包含contact': window.location.href.includes('contact'),
    '存在edit-layout类': !!document.querySelector('.edit-layout'),
    '存在detail-layout类': !!document.querySelector('.detail-layout'),
    '存在edit-page类': !!document.querySelector('.edit-page'),
    '存在EditLayout组件': !!document.querySelector('[class*="edit"]'),
    '存在DetailLayout组件': !!document.querySelector('[class*="detail"]')
};

Object.keys(pageTypeChecks).forEach(key => {
    const result = pageTypeChecks[key];
    console.log(`- ${key}: ${result ? '✅ 是' : '❌ 否'}`);
});

// 3. DOM结构分析
console.log('\n📋 3. DOM结构分析');

// 查找所有可能的容器
const containers = {
    'ant-tabs容器': document.querySelector('.ant-tabs'),
    'ant-tabs-content-holder': document.querySelector('.ant-tabs-content-holder'),
    'ant-tabs-tabpane': document.querySelector('.ant-tabs-tabpane'),
    'business-container': document.querySelector('.business-container'),
    'page-container': document.querySelector('.page-container'),
    'edit-page容器': document.querySelector('.edit-page'),
    'detail-page容器': document.querySelector('.detail-page')
};

console.log('容器元素查找结果:');
Object.keys(containers).forEach(key => {
    const element = containers[key];
    if (element) {
        console.log(`✅ ${key}: ${element.offsetWidth} × ${element.offsetHeight}`);
    } else {
        console.log(`❌ ${key}: 未找到`);
    }
});

// 4. 联系人信息表格查找
console.log('\n📋 4. 联系人信息表格查找');

const contactGridSelectors = [
    '[data-group-code="supplierContactsInfoList"]',
    '[data-group-code="contactsInfo"]',
    '[data-group-code*="contact"]',
    '[ref*="supplierContactsInfoList"]',
    '[ref*="contactsInfo"]',
    '[ref*="contact"]',
    '.lineWrap[ref*="contact"]',
    '.lineWrap[ref*="supplier"]'
];

const foundGrids = [];
contactGridSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        elements.forEach((el, index) => {
            foundGrids.push({
                selector,
                index,
                element: el,
                size: `${el.offsetWidth} × ${el.offsetHeight}`,
                ref: el.getAttribute('ref'),
                dataGroupCode: el.getAttribute('data-group-code'),
                className: el.className
            });
        });
    }
});

if (foundGrids.length > 0) {
    console.log('✅ 找到联系人信息表格:');
    foundGrids.forEach(grid => {
        console.log(`- 选择器: ${grid.selector}`);
        console.log(`  尺寸: ${grid.size}`);
        console.log(`  ref: ${grid.ref || '无'}`);
        console.log(`  data-group-code: ${grid.dataGroupCode || '无'}`);
        console.log(`  className: ${grid.className}`);
        console.log('---');
    });
} else {
    console.log('❌ 未找到联系人信息表格');
    
    // 尝试查找所有表格
    const allTables = document.querySelectorAll('.vxe-table, .ant-table, table');
    console.log(`🔍 页面中共找到 ${allTables.length} 个表格元素:`);
    allTables.forEach((table, index) => {
        const parent = table.closest('[ref], [data-group-code]');
        console.log(`- 表格${index + 1}: ${table.offsetWidth} × ${table.offsetHeight}`);
        if (parent) {
            console.log(`  父容器ref: ${parent.getAttribute('ref') || '无'}`);
            console.log(`  父容器data-group-code: ${parent.getAttribute('data-group-code') || '无'}`);
        }
    });
}

// 5. 表格组件详细分析
console.log('\n📋 5. 表格组件详细分析');
const mainGrid = foundGrids[0]?.element;
if (mainGrid) {
    const gridAnalysis = {
        '表格容器': mainGrid,
        'VXE表格': mainGrid.querySelector('.vxe-table'),
        '表格头部': mainGrid.querySelector('.vxe-table--header-wrapper'),
        '表格主体': mainGrid.querySelector('.vxe-table--body-wrapper'),
        '表格工具栏': mainGrid.querySelector('.vxe-toolbar'),
        '分页器': mainGrid.querySelector('.vxe-pager')
    };
    
    Object.keys(gridAnalysis).forEach(key => {
        const element = gridAnalysis[key];
        if (element) {
            const styles = window.getComputedStyle(element);
            console.log(`✅ ${key}:`);
            console.log(`  尺寸: ${element.offsetWidth} × ${element.offsetHeight}`);
            console.log(`  height: ${styles.height}`);
            console.log(`  min-height: ${styles.minHeight}`);
            console.log(`  max-height: ${styles.maxHeight}`);
        } else {
            console.log(`❌ ${key}: 未找到`);
        }
    });
} else {
    console.log('❌ 无法进行详细分析，未找到主表格');
}

// 6. CSS样式冲突检测
console.log('\n📋 6. CSS样式冲突检测');
if (mainGrid) {
    const computedStyles = window.getComputedStyle(mainGrid);
    const importantStyles = {
        'height': computedStyles.height,
        'min-height': computedStyles.minHeight,
        'max-height': computedStyles.maxHeight,
        'overflow': computedStyles.overflow,
        'position': computedStyles.position,
        'display': computedStyles.display
    };
    
    console.log('主表格容器的计算样式:');
    Object.keys(importantStyles).forEach(key => {
        console.log(`- ${key}: ${importantStyles[key]}`);
    });
    
    // 检查是否有我们的优化样式
    const hasOptimizedStyles = computedStyles.height.includes('calc') || 
                              computedStyles.minHeight.includes('calc') ||
                              computedStyles.height.includes('vh');
    
    console.log(`🎨 是否应用了我们的优化样式: ${hasOptimizedStyles ? '✅ 是' : '❌ 否'}`);
}

// 7. JavaScript配置检测
console.log('\n📋 7. JavaScript配置检测');

// 检查控制台是否有我们的调试信息
console.log('请查看控制台中是否有以下调试信息:');
console.log('- "🎯 编辑页面 - 联系人信息表格极致空间优化"');
console.log('- "🎯 编辑页面 - 联系人信息表格在tab模式下的最终高度"');

// 8. 当前页签检测
console.log('\n📋 8. 当前页签检测');
const tabs = document.querySelectorAll('.ant-tabs-tab');
const activeTab = document.querySelector('.ant-tabs-tab-active');

console.log(`页面中共有 ${tabs.length} 个页签`);
if (activeTab) {
    console.log(`当前活动页签: "${activeTab.textContent.trim()}"`);
    const isContactTab = activeTab.textContent.includes('联系人') || 
                        activeTab.textContent.includes('联系人信息') ||
                        activeTab.textContent.includes('contact');
    console.log(`是否为联系人信息页签: ${isContactTab ? '✅ 是' : '❌ 否'}`);
} else {
    console.log('❌ 未找到活动页签');
}

// 9. 问题诊断结论
console.log('\n📋 9. 问题诊断结论');
console.log('='.repeat(60));

const issues = [];

if (foundGrids.length === 0) {
    issues.push('❌ 关键问题：未找到联系人信息表格元素');
}

if (!containers['ant-tabs-content-holder']) {
    issues.push('❌ 布局问题：未找到ant-tabs-content-holder容器');
}

if (mainGrid && !window.getComputedStyle(mainGrid).height.includes('calc')) {
    issues.push('❌ 样式问题：优化样式未生效');
}

if (!pageTypeChecks['URL包含edit'] && !pageTypeChecks['存在edit-layout类']) {
    issues.push('⚠️ 页面类型：可能不是编辑页面');
}

if (issues.length > 0) {
    console.log('🚨 发现的问题:');
    issues.forEach(issue => console.log(issue));
} else {
    console.log('✅ 未发现明显问题，可能需要进一步调试');
}

// 10. 建议的解决方案
console.log('\n📋 10. 建议的解决方案');
console.log('='.repeat(60));

if (foundGrids.length === 0) {
    console.log('🔧 建议1: 查找表格元素');
    console.log('请尝试手动查找联系人信息表格:');
    console.log('- 右键检查元素，查找包含联系人数据的表格');
    console.log('- 查看该表格的父容器的ref或data-group-code属性');
    console.log('- 将信息反馈给开发人员');
}

if (mainGrid && !window.getComputedStyle(mainGrid).height.includes('calc')) {
    console.log('🔧 建议2: 强制应用样式');
    const idealHeight = document.documentElement.clientHeight - 120;
    console.log(`请在控制台执行以下代码强制设置高度:`);
    console.log(`
const grid = document.querySelector('${foundGrids[0]?.selector}');
if (grid) {
    grid.style.height = '${idealHeight}px';
    grid.style.minHeight = '${idealHeight}px';
    console.log('✅ 已强制设置表格高度');
}
    `);
}

console.log('🔧 建议3: 刷新页面后重新检测');
console.log('有时候样式需要页面刷新后才能生效');

console.log('\n🎉 诊断完成！请将以上信息提供给开发人员进行进一步分析。');
