<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleEsignV3PersonalEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleEsignV3PersonalDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <SaleEsignV3PersonalAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <a-modal
      v-drag
      centered
      :width="400"
      :visible="authVisible"
      :maskClosable="false"
      @ok="selectedOk"
      @cancel="authVisible = false"
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iFlbd_1c30671e`, '选择授权项')"
    >
      <a-checkbox-group v-model="authData">
        <template v-for="(el, idx) of authSelectData">
          <a-checkbox
            :value="el.value"
            :key="idx">
            {{ el.label }}
          </a-checkbox>
        </template>
      </a-checkbox-group>
    </a-modal>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleEsignV3PersonalAdd from './modules/SaleEsignV3PersonalAdd'
import SaleEsignV3PersonalDetail from './modules/SaleEsignV3PersonalDetail'
import SaleEsignV3PersonalEdit from './modules/SaleEsignV3PersonalEdit'
import {getAction, postAction} from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        SaleEsignV3PersonalAdd, SaleEsignV3PersonalDetail, SaleEsignV3PersonalEdit
    },
    data () {
        return {
            authData: [],
            authSelectData: [
                {
                    value: 'get_psn_identity_info',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiTSMmLjDjeytvVH_d39834c5`, '授权允许获取个人用户的账号基本信息')
                },
                {
                    value: 'psn_initiate_sign',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiToBmLjDhAnePW_b6d2aa54`, '授权允许代表个人用户发起合同签署')
                },
                {
                    value: 'manage_psn_resource',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lbiTSMmLjDjWeEJjjRvbW_ab6e005`, '授权允许获取个人用户的印章等资源的管理权限')
                }
            ],
            authVisible: false,
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                businessType: 'esignV3Personal',
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary',  authorityCode: 'esignv3#saleEsignV3Personal:add'},

                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esignv3#saleEsignV3Personal:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esignv3#saleEsignV3Personal:add'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), clickFn: this.handleCertification, allow: this.allowCertification, authorityCode: 'esignv3#saleEsignV3Personal:submitCertification'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLizE_25c88b41`, '刷新认证状态'), clickFn: this.handleRefreshStatus, allow: this.allowRefreshStatus, authorityCode: 'esignv3#saleEsignV3Personal:refresh'},
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_SMlbKy_8f5b4fe1`, '获取授权链接'),
                        clickFn: this.getAuth,
                        allow: this.allowEmployManagement,
                        authorityCode: 'esignv3#saleEsignV3Personal:getAuth'
                    },
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'esignv3#saleEsignV3Personal:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/esignv3/saleEsignV3Personal/add',
                list: '/esignv3/saleEsignV3Personal/list',
                delete: '/esignv3/saleEsignV3Personal/delete',
                columns: 'SaleEsignV3Personal',
                auth: '/esignv3/saleEsignV3Personal/submitCertification',
                refresh: '/esignv3/saleEsignV3Personal/refreshRealNameStatus'
            }
        }
    },
    methods: {
        selectedOk () {
            console.log(this.authData)
            if (!this.authData && this.authData.length <= 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFlbd_f34bbd87`, '请选择授权项'))
                return
            }
            let params = {id: this.companyRow.id, authorizedScopes: this.authData.join(',')}
            postAction('/esignv3/saleEsignV3Personal/getAuth', params).then(res => {
                if(res.success){
                    this.authVisible = false
                    window.open(res.message)
                }else {
                    this.$message.error(res.message)
                }
            })
        },
        getAuth (row) {
            this.companyRow = row
            this.authVisible = true
        },
        allowEmployManagement (row) {
            if (row.realnameStatus === '1') {
                return false
            }
            return true
        },
        //已认证不能被编辑
        allowEdit (row){
            if(row.realnameStatus==='1'){
                return true
            }
            return false
        },
        allowCertification (row){
            if(row.realnameStatus=='1'){
                return true
            }
            return false
        },
        allowRefreshStatus (row) {
            if(row.realnameStatus == '1'){
                return true
            }
            return false
        },
        //已经创建e签宝的账号的不能删除
        allowDelete (row){
            if(row.realnameStatus==='1'){
                return true
            }
            return false
        },
        handleAdd () {
            this.showAddPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        handleCertification (row) {
            let authUrl = row.authUrl
            let contentText = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLDJLiW_5f843f01`, '是否确认提交认证?')
            if (authUrl && authUrl.length>0) {
                contentText = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_rVHIDJRLiWKmDJMAbujWKQtTDJW_96191780`, '该信息已提交过认证，再次提交会产生费用，是否继续提交？')
            }
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationSystems`, '确认认证'),
                content: contentText,
                onOk: function () {
                    postAction(that.url.auth+'?needSave=0', {id: row.id}).then(res => {
                        if(res.success){
                            if(res.result.realnameStatus != '1'){
                                window.open(res.result.authUrl)
                            }else {
                                that.$refs.listPage.loadData()
                            }
                        }else{
                            if(res.message =='参数格式错误：mobileNo'){
                                that.$message.error( that.$srmI18n(`${that.$getLangAccount()}#i18n_field_mLltyIH_c48d492e`, '个人手机号异常'))
                            }else {
                                that.$message.error(res.message)
                            }
                        }

                    })
                }
            })
        },
        handleRefreshStatus (row) {
            getAction(this.url.refresh, {id: row.id}).then(res => {
                if(res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                }else {
                    this.$message.warning(res.message)
                }
                // 刷新列表
                this.$refs.listPage.loadData()
            })
        }
    }
}
</script>