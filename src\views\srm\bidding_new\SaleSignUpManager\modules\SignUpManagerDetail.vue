<template>
  <div class="SignUpManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="ifshow"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :queryData="queryData"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
      <a-modal
        v-drag     
        v-model="visible" 
        :width="800"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_sRUJtH_20284311`, '报名审查记录')"
        :cancel-text="$srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭')">
        <template slot="footer">
          <a-button @click="() => {this.visible=false}">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
        </template>
        <vxe-grid
          v-bind="gridOptions"></vxe-grid>
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {getAction} from '@/api/manage'
import { ajaxFindDictItems } from '@/api/api'
import { USER_INFO, USER_COMPANYSET, USER_ELS_ACCOUNT } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    name: 'SignUpManagerEdit',
    components: {
        BusinessLayout,
        EditGridLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        queryData: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            console.log('editrow', this.currentEditRow)
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_SupplierTenderProjectSignUp_${templateNumber}_${templateVersion}`
            // return '100000/sale_SupplierTenderProjectSignUp_TC2022042101_1'
        }
    },
    data () {
        return {
            ifshow: false,
            visible: false,
            gridOptions: {
                border: true,
                resizable: true,
                showOverflow: true,
                height: 300,
                editConfig: {
                    trigger: 'click',
                    mode: 'cell'
                },
                align: 'center',
                toolbarConfig: {
                    enabled: false // 禁用自定义工具
                },
                columns: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), type: 'seq', width: 50},
                    {field: 'createBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJL_164dad6`, '审查人'), width: 150},
                    {field: 'createTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJKI_2b39e622`, '审查时间'), width: 150},
                    {field: 'status_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_reviewStatus`, '审查状态'), width: 140},
                    {field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJII_2b3941f6`, '审查意见')}
                ],
                data: []
            },
            pageStatus: 'detail',
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            requestData: {
                detail: {
                    url: '/tender/sale/supplierTenderProjectSignUp/queryById',
                    args: (that) => {
                        return { 
                            id: that.currentEditRow.id || ''
                        }
                    }
                }
            },
            externalToolBar: {},
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJtH_2b3e4109`, '审查记录'),
                    click: () => {this.visible=true},
                    key: 'record'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack',
                    click: this.goBack.bind(this)
                }
            ],
            attachmentListData: {},
            url: {},
            userInfo: {},
            projectObj: {},
            formatType: {}
        }
    },
    created () {
        if (JSON.stringify(this.queryData) != '{}') {
            this.userInfo = this.$ls.get(USER_INFO)
            console.log(this.userInfo)
        }
        this.getQueryFileType()
    },
    methods: {
        // 获取文件类型数据字典
        getQueryFileType () {
            // 获取表格下拉字典
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'supplierSignUpFileType'
            }
            ajaxFindDictItems(postData).then(res => {
                if (res.success) {
                    const RESULTDATA = res.result || []
                    let fileType = {}
                    RESULTDATA.forEach(data => {
                        fileType[data.value] = data.title
                    })
                    this.formatType = fileType
                    this.ifshow = true
                }else{
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFJCVVKmVsK_fef58c50`, '数据字典请求失败，请重试！'))
                }
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log(pageConfig, resultData)
            // -- start -- sb xiefa
            pageConfig.groups.forEach(group => {
                if (group.groupCode == 'attachmentList') {
                    group.loadData.forEach(load => {
                        load['fileType_dictText'] = this.formatType[load.fileType] || ''
                        load['fileType'] = this.formatType[load.fileType] || ''
                    })
                }
            })
            console.log('pageConfig', pageConfig)

            // -- end ---
            this.gridOptions.data = resultData.tenderProjectSignUpRejectList || []
            
            pageConfig.groups[1]['extend'] = {
                optColumnList: [
                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                    { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                ]
            }
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
                if (key == 'purchaseEnterpriseName') {
                    formModel[key] = resultData[key] || this.$ls.get(USER_COMPANYSET).companyName
                }
            }
            
        },
        checkItemSelectOk (data) {
            console.log(data)
        },
        preViewEvent (Vue, row){
            row.subpackageId = this.currentEditRow.subpackageId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.currentEditRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        goBack () {
            this.$parent.showDetailPage = false
        }
    }
}
</script>

<style lang="less" scoped>
.registration-title{
    background-color: #eee;
    padding: 5px 10px;
    margin-bottom: 10px;
}
</style>


