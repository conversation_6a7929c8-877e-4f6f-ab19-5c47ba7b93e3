<template>
  <div class="EvaluationgDetail">
    <div class="page-container">
      <detail-layout
        ref="detailPage"
        useLocalModelLayout
        modelLayout="unCollapse"
        :showHeader="false"
        :page-data="pageData"
        :current-edit-row="vuex_currentEditRow"
        :url="url"
      />
    </div>

    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"
    />
  </div>
</template>

<script>
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { PURCHASEATTACHMENTDOWNLOADAPI } from '@/utils/const'
import { mapState, mapMutations } from 'vuex'
import { getAction } from '@/api/manage'

import {
    SEQ_COLUMN,
    CHECKBOX_COLUMN,
    ATTACHMENT_COLUMNS
} from '@/utils/constant.js'

const fileInfoAchmentList = ATTACHMENT_COLUMNS('fileInfo')

export default {
    name: 'BiddingDetail',
    mixins: [DetailMixin],
    data () {
        return {
            btns: [],
            showHeader: false,
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_UBTv_411c044d`, ' 评标条例'),  
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'biddingEvaluationTemplateItemList',
                            columns: []
                        }
                    },

                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_regulationsAnnex`, '条例附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'attachments',
                            columns: [
                                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                CHECKBOX_COLUMN,
                                ...fileInfoAchmentList  
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                            ]
                        }
                    }
                ]
            },
            url: {
                detail: '/bidding/biddingEvaluationTemplateHead/queryById'
            }
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        fileSrc () {
            const {
                templateNumber = '',
                templateVersion = '',
                templateAccount = '',
                busAccount = ''
            } = this.vuex_currentEditRow.biddingEvaluationTemplateHeadVO || {}

            const configFiles = this.$variateConfig['configFiles']
            const time = +new Date()
            const url = `${configFiles}/${templateAccount || busAccount}/purchase_biddingEvaluationTemplate_${templateNumber}_${templateVersion}.js?t=`+time
            return url
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        init () {
            if (this.vuex_currentEditRow.projectId) {
                this.$refs.detailPage.queryDetail(this.vuex_currentEditRow.biddingEvaluationTemplateHeadVO.id)
            }
        },
        downloadEvent(row) {
            const { id, fileName } = row
            let downloadUrl = PURCHASEATTACHMENTDOWNLOADAPI
            getAction(downloadUrl, { id }, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        preViewEvent(preViewFile) {
            this.$previewFile.open({ params: preViewFile, path: preViewFile?.path || '' })
        }
    }
}
</script>

<style lang="less" scoped>
.EvaluationgDetail {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
}
</style>
