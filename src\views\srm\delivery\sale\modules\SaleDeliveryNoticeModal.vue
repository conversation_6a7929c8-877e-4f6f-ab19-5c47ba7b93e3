<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url"
      @loadSuccess="handleLoadSuccess" />
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {postAction} from '@/api/manage'
export default {
    mixins: [DetailMixin],
    data () {
        return {
            showRemote: false,
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saleOrderItemInfo`, '订单行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'saleOrderItemList',
                        columns: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryPlanLine`, '交货计划行'), groupCode: 'deliveryPlanitemInfo', type: 'grid', custom: {
                        ref: 'saleOrderDeliveryPlanList',
                        expandColumnsMethod: this.expandColumnsMethod,
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'orderItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                            { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_requiredDeliveryDate`, '要求交期'), width: 180},
                            { field: 'planDeliveryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_plainDeliveryDate`, '计划交期'), width: 120},
                            { field: 'planDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_PlanneDeliveryQuantity`, '计划交货数量'), width: 120},
                            { field: 'originalPlanDeliveryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_firstCommittedDeliveryDate`, '首次承诺交货日期'), width: 120},
                            { field: 'originalPlanDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_firstCommittedDeliveryCout`, '首次承诺交货数量'), width: 120},
                            { field: 'performanceBaseDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_performanceBenchmarkdate`, '绩效基准日期'), width: 120},
                            { field: 'publishStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'), width: 120},
                            { field: 'confirmStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sureStatus`, '确认状态'), width: 120},
                            { field: 'close_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colseMarsk`, '关闭标识'), width: 120}

                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'fileSize', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bigSmall`, '大小'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                type: 'upload', businessType: 'order',
                                attr: this.attrHandle,
                                callBack: this.uploadCallBack },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFilesEvent }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                publicBtn: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_allOrderSure`, '整单确认'), type: 'primary', click: this.confirmOrder, showCondition: this.showRowConfrimConditionBtn},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accLineSure`, '按行确认'), type: 'primary', click: this.confirmOrderRow, showCondition: this.showRowConfrimConditionBtn},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accLinerefuer`, '按行拒绝'), click: this.rejectOrderRow, showCondition: this.showRowRefuseConditionBtn},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                detail: '/order/saleOrderHead/queryById',
                comfirmOrderRow: '/order/saleOrderHead/comfirmOrderRow',
                comfirmOrder: '/order/saleOrderHead/confirmOrder',
                rejectOrderRow: '/order/saleOrderHead/rejectOrderRow'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let busAccount = this.currentEditRow.templateAccount|| this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${busAccount}/sale_order_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.noticeNumber,
                actionRoutePath: '/srm/delivery/PurchaseDeliveryNoticeList,/srm/delivery/sale/SaleDeliveryNoticeList'
            }
        },
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        goBack () {
            this.$emit('hide')
        }

    }
}
</script>