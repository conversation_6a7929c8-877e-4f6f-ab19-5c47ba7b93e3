<template>
  <div>
    <vxe-grid
      border
      auto-resize 
      resizable
      column-key
      highlight-hover-row
      show-overflow
      :ref="'grid_'+relatedId"
      size="small"
      height="350"
      :toolbar="{slots: {buttons: 'toolbar_buttons'}}"
      :checkbox-config="{highlight: true}"
      :columns="tableColumns"
      :data="tableData">
      <template #toolbar_buttons>
        <div style="margin-top:-16px;text-align:right">
          <a-upload
            name="file"
            :show-upload-list="false"
            :multiple="multiple"
            :headers="tokenHeader"
            :accept="accept"
            :beforeUpload="beforeUpload"
            :data="{headId: relatedId, businessType: relatedType}"
            :action="url.upload"
            @change="handleImportExcel"
          >
            <a-button
              type="primary"> {{$srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件')}} </a-button>
          </a-upload>
          <a-button
            @click="deleteFile"
            style="margin-left:12px">删除</a-button>
        </div>
      </template>
      <template #grid_opration="{ row }">
        <a
          title="下载"
          type="download"
          style="margin-right:8px"
          @click="downloadFile(row)"><a-icon type="download"></a-icon></a>
      </template>
      <template slot="empty">
        <a-empty />
      </template>
    </vxe-grid>
  </div>
</template>
<script lang="jsx">
import { getAction, deleteAction } from '@/api/manage'
import { axios } from '@/utils/request'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import {Empty} from 'ant-design-vue'
export default {
    name: 'UploadList',
    components: {
        AEmpty: Empty
    },
    props: {
        multiple: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            relatedId: '',
            relatedType: '',
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            roleName: '',
            openFileDialogOnClick: false,
            tableColumns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
            ],
            tableData: [],
            url: {
                list: '/attachment/purchaseAttachment/list',
                upload: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
                download: '/attachment/purchaseAttachment/download',
                delete: '/attachment/purchaseAttachment/delete',
                list_sale: '/attachment/saleAttachment/list',
                upload_sale: `${this.$variateConfig['domainURL']}/attachment/saleAttachment/upload`,
                download_sale: '/attachment/saleAttachment/download',
                delete_sale: '/attachment/saleAttachment/delete'
            },
            tokenHeader: {'X-Access-Token': this.$ls.get(ACCESS_TOKEN)}
        }
    },
    methods: {
        getFileList (relatedId, relatedType, roleName) {
            this.relatedId = relatedId
            this.relatedType = relatedType
            this.roleName = roleName
            if(relatedId && relatedType) {
                let params = {'headId': this.relatedId, 'businessType': this.relatedType}
                let url = this.url.list
                if(this.roleName == 'sale'){
                    url = this.url.list_sale
                }
                getAction(url, params).then(res => {
                    this.tableData = res.result.records
                })
            }
        },
        downloadFile (row) {
            let params = {id: row.id}
            axios({
                url: this.url.download,
                responseType: 'blob',
                params: params
            }).then(res => {
                console.log(res)
                const blob = new Blob([res])
                const blobUrl = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.style.display = 'none'
                a.href = blobUrl
                a.download = row.fileName
                a.click()
            })
        },
        deleteFile () {
            let fileList = this.$refs['grid_'+this.relatedId].getCheckboxRecords()
            if(!fileList.length) {
                this.$message.warning('请选择一条记录!')
                return
            }
            let sendFileList = fileList.filter(item => {
                return item.status == '1'
            })
            if(sendFileList.length) {
                this.$message.warning('已发送文件不能删除!')
                return
            }
            let idsList = fileList.map(item => {
                return item.id
            })
            let ids = idsList.join(',')
            let params = {ids: ids, relatedId: this.relatedId, relatedType: this.relatedType}
            deleteAction(this.url.delete, params).then(res => {
                if(res.success) {
                    this.getFileList(this.relatedId, this.relatedType)
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功'))
                }
            })
        },
        handleImportExcel (info) {
            if (info.file.status === 'done') {
                if (info.file.response.success) {
                    if (info.file.response.code === 201) {
                        let { message, result: { msg, fileUrl, fileName } } = info.file.response
                        let href = this.$variateConfig['domainURL'] + fileUrl
                        this.$warning({
                            title: message,
                            content: (
                                <div>
                                    <span>{msg}</span><br/>
                                    <span>具体详情请 <a href={href} target="_blank" download={fileName}>点击下载</a> </span>
                                </div>
                            )
                        })
                    } else {
                        this.$message.success(info.file.response.message || `${info.file.name} 文件上传成功`)
                    }
                    this.getFileList(this.relatedId, this.relatedType, this.roleName)
                } else {
                    this.$message.error(`${info.file.name} ${info.file.response.message}.`)
                }
            } else if (info.file.status === 'error') {
                this.$message.error(`文件上传失败: ${info.file.msg} `)
            }
        },
        clearUploadGrid () {
            this.$refs['grid_'+this.relatedId].loadData([])
        },
        // clickUploadEvent () {
        //     if(!this.relatedId) {
        //         this.$message.warning('请先保存单据！')
        //         this.openFileDialogOnClick = false
        //     }else {
        //         this.openFileDialogOnClick = true
        //     }
        // },
        beforeUpload (file) {
            if(!this.relatedId) {
                this.$message.warning('请先保存单据后再上传！')
                return false
            }
            if(file.size > 100 * 1024 * 1024) {
                this.$message.warning('文件大小限制为100M！')
                return false
            }
        }
    }
}
</script>
<style lang="less">
    .vxe-toolbar.size--small {
        height: 32px
    }
</style>