<template>
  <div class="business-container">
    <business-layout
      :ref="businessRefName"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      modelLayout="tab"
      pageStatus="detail"
      v-on="businessHandler"
    >
    </business-layout>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { BUTTON_BACK } from '@/utils/constant.js'
export default {
    name: 'PurchaseFinanceEnterpriseOutinvoiceDetail',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            requestData: {
                detail: {
                    url: '/finance/financeEnterpriseOutinvoice/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            }
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_basicData`, '基础信息'),
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {   groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserElsAccountNumber`, '采购商ELS账号'),
                        fieldName: 'elsAccount',
                        disabled: true,
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserElsAccountNumber`, '采购商ELS账号')
                    },
                    {   groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseName`, '采购商名称'),
                        fieldName: 'enterpriseName',
                        disabled: true,
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseName`, '采购商名称')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码'),
                        fieldName: 'companyCode',
                        dictCode: 'purchase_organization_info,org_name,org_code,org_category_code="companyCode" && status="1"',
                        disabled: false,
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingOrganization`, '采购组织'),
                        fieldName: 'purchaseOrg',
                        disabled: false,
                        dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="purchaseOrganization" && status="1"',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingOrganization`, '采购组织')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseGroup`, '采购组'),
                        fieldName: 'purchaseGroup',
                        dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="purchaseGroup" && status="1"',
                        disabled: false,
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseGroup`, '采购组')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxNumber`, '纳税人识别号'),
                        fieldName: 'taxpayerRegNumber',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxNumber`, '纳税人识别号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regLocation`, '注册地址'),
                        fieldName: 'registerAddress',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regLocation`, '注册地址')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_diCE_32c265ec`, '注册电话'),
                        fieldName: 'registerTelephone',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_diCE_32c265ec`, '注册电话'),
                        disabled: false
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vDWE_2cb2838d`, '开户银行'),
                        fieldName: 'depositBank',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vDWE_2cb2838d`, '开户银行')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankAccount`, '银行账号'),
                        fieldName: 'bankAccount',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankAccount`, '银行账号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRCBFL_b8135c43`, '采购方负责人'),
                        fieldName: 'purchasePrincipal',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRCBFL_b8135c43`, '采购方负责人')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YdCK_2f765f8f`, '推送方式'),
                        fieldName: 'pushMode',
                        dictCode: 'outinvoicePushMode',
                        defaultValue: '1',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YdCK_2f765f8f`, '推送方式')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ydlt_2f75fd68`, '推送手机'),
                        fieldName: 'phone',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ydlt_2f75fd68`, '推送手机')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ydjd_2f7bafdc`, '推送邮箱'),
                        fieldName: 'email',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ydjd_2f7bafdc`, '推送邮箱')
                    }
                ],
                itemColumns: [
                ]
            }
        }
    }
}
</script>