<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      :reloadData="handleReloadData"
      @loadSuccess="handleLoadSuccess">
      <template #itemInfoTab="{pageData, resultData}">
        <vxe-grid :toolbar="{slots: {buttons: 'toolbar_buttons'}}"
          border
          resizable
          align="center"
          show-overflow
          highlight-hover-row
          :height="gridHeight"
          row-id="id"
          size="mini"
          ref="purchaseEnquiryItemList"
          :loading="purchaseEnquiryItemLoading"
          :data="purchaseEnquiryItemData"
          @page-change="purchaseEnquiryItemHandlePageChange"
          :checkbox-config="checkboxConfig"
          :pager-config="purchaseEnquiryItemTablePage"
          :columns="pageData.groups[1].custom.columns">
          <template #renderDictLabel="{ row, column }">
            <span>
              {{ getDictLabel(row, column) }}
            </span>
          </template>
            <template v-slot:toolbar_buttons>
                <div style="margin-top:-6px;text-align:right;">
                  <a-button style="margin-left:8px" type="primary" icon="download" @click="itemExportExcel" > 导出 </a-button>
                </div>
            </template>
        </vxe-grid>
      </template>
    </detail-layout>
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />

    <purchase-edit-cost
      ref="costform"
      :current-edit-row="costEditRow"
    />
    <view-ladder-price-modal ref="ladderPage"></view-ladder-price-modal>
    <!-- <a-modal
    v-drag    
          centered
          :width="960"
          :maskClosable="false"
          :visible="flowView"
          @ok="closeFlowView"
          @cancel="closeFlowView">
          <iframe
            style="width:100%;height:560px"
            title=""
            :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
            frameborder="0"></iframe>
        </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
  </div>
</template>
<script lang="jsx">
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {postAction, getAction, postDownFile} from '@/api/manage'
import ViewLadderPriceModal from '../../modules/ViewLadderPriceModal'
import PurchaseEditCost from './PurchaseEditCost'
import flowViewModal from '@comp/flowView/flowView'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import {cloneDeep} from 'lodash'

export default {
    name: 'PurchaseEnquiryDetail',
    mixins: [DetailMixin],
    components: {
        ViewLadderPriceModal,
        PurchaseEditCost,
        flowViewModal
    },
    data () {
        return {
            checkboxConfig: {
                highlight: true,
                reserve: true,
                trigger: 'cell'
            },
            purchaseEnquiryItemLoading: false,
            purchaseEnquiryItemData: [],
            purchaseEnquiryItemTablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 500,
                align: 'left',
                pageSizes: [20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            purchaseEnquiryItemTableColumns: [],
            stageTypeData: [],
            showRemote: false,
            flowView: false,
            flowId: '',
            currentBasePath: this.$variateConfig['domainURL'],
            costEditRow: {},
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseEnquiryItemList',
                        columns: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'sourceNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号'), width: 130 },
                            { field: 'businessType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#`, '业务类型'), width: 130 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120},
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadFile},
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ],
                        buttons: [
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'),
                                msgType: 'batchDownload',
                                key: 'batchDownload',
                                type: 'check',
                                beforeCheckedCallBack: rowList => {
                                    this.batchDownload2(rowList)
                                }
                            }
                        ]
                    } }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocationApproval`, '审批撤销'), type: '', click: this.auditCancel, showCondition: this.auditCancelConditionBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', click: this.showFlow, showCondition: this.showFlowConditionBtn},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/enquiry/purchaseEnquiryHead/queryById',
                cancel: '/a1bpmn/audit/api/cancel'
            },
            ladderSlots: {
                default: ({row, column}) =>{
                    const detailTpl = (
                        <div style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                            <a-tooltip placement="topLeft" overlayClassName="tip-overlay-class">
                                <template slot="title">
                                    <div>
                                        <vxe-table auto-resize border row-id="id" size="mini" data={this.initRowLadderJson(row[column.property])}>
                                            <vxe-table-column type="seq" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号')} width="80"></vxe-table-column>
                                            <vxe-table-column field="ladder" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')} width="140"></vxe-table-column>
                                            <vxe-table-column field="price" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价')} width="140"></vxe-table-column>
                                            <vxe-table-column field="netPrice" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价')} width="140"></vxe-table-column>
                                        </vxe-table>
                                    </div>
                                </template>
                                {this.defaultRowLadderJson(row[column.property])}
                            </a-tooltip>
                        </div>)
                    if(row && row.quotePriceWay == 1){
                        // let label = row['ladderPriceJson'] ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看') : ''
                        // return [
                        //     <a onClick={() => this.setLadder(row)}>{label}</a>
                        // ]
                        return detailTpl
                    }else{
                        return ''
                    }
                }
            },
            costSlots: {
                default: ({row}) => {
                    if(row && row.quotePriceWay == 2){
                        let costJson = row.costFormJson ? JSON.parse(row.costFormJson) : {}
                        let label = costJson['templateName'] ? costJson['templateName'] : ''
                        return [<a onClick={() => this.openCost(row)}>{label}</a>]
                    }else{
                        return ''
                    }
                }
            }
        }
    },
    computed: {
        fileSrc () {
            this.getStageTypeData()
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_enquiry_${templateNumber}_${templateVersion}.js?t=`+time
        },
        gridHeight () {
            // 计算当前页面最大高度
            const clientHeight = document.documentElement.clientHeight
            let contextHeight = clientHeight - 246
            let oneThird = contextHeight / 3
            let MIN = 334
            if (contextHeight < MIN) contextHeight = MIN
            if (oneThird < MIN) oneThird = MIN

            let height = contextHeight
            return height
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        }else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
        this.getPurchaseEnquiryItemList()
    },
    mounted() {
        console.log(this.allowEdit)
        if(this.allowEdit) {
            let publicBtn = cloneDeep(this.pageData.publicBtn)
            publicBtn.splice(2, 0, { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), click: this.toEdit });
            this.$set(this.pageData, "publicBtn", publicBtn);
            console.log(this.pageData)
        }
    },
    methods: {
        // 請求接口後格式化列表數據
        async formatTableData(data) {
            this.setColumnData();
            console.log('請求接口後格式化列表數據', data)
            return new Promise((resolve, reject) => {
                data = data.map((item) => {
                    if (item.price === 0 || Number(item.price) > 0) {
                        item.price = Number(item.price).toFixed(6)
                    }
                    if (item.netPrice === 0 || Number(item.netPrice) > 0) {
                        item.netPrice = Number(item.netPrice).toFixed(6)
                    }
                    if (item.quotaTaxAmount === 0 || Number(item.quotaTaxAmount) > 0) {
                        item.quotaTaxAmount = Number(item.quotaTaxAmount).toFixed(2)
                    }
                    if (item.quotaNetAmount === 0 || Number(item.quotaNetAmount) > 0) {
                        item.quotaNetAmount = Number(item.quotaNetAmount).toFixed(2)
                    }
                    return item
                })
                resolve(data)
            })
        },
        itemExportExcel() {
            postDownFile("/enquiry/purchaseEnquiryHead/exportItemExcel/" + this.currentEditRow.id, null, null).then((data) => {
                // responseType为blob的请求，统一获取错误信息
                if (data.type === 'application/json') {
                    const fileReader = new FileReader()
                    fileReader.onloadend = () => {
                        const jsonData = JSON.parse(fileReader.result)
                        this.$message.error(jsonData.message)
                    }
                    fileReader.readAsText(data)
                    return
                }
                if (!data) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), '导出询价行.xls')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '导出询价行.xls')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            })
        },
        batchDownload2 (rowList){
            const fileGrid = this.$refs.detailPage.$refs.purchaseAttachmentList[0]
            let checkboxRecords = fileGrid.getCheckboxRecords()
            if(checkboxRecords.length<=0){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#`, '请选择要下载的附件'))
                return
            }
            let elsAccount = this.$ls.get('Login_elsAccount')
            // 判断
            let flag = false
            let fromData = this.currentEditRow
            for(let i=0;i<checkboxRecords.length;i++){
                let row = checkboxRecords[i]
                if(fromData.enquiryStatus === '1' && row.uploadElsAccount !=elsAccount) {
                    this.$message.warning('所选行中存在开标前不允许查看的文件！')
                    flag = true
                    return
                }
            }
            if (flag) {
                return
            }
            new BatchDownloadBtn().batchDownloadZip(checkboxRecords)
        },
        
        // 通过value显示label
        getDictLabel (row, column) {
            // 如果没有配置数据字典则走返回的field key值
            return row[column.property + '_dictText'] || row[column.property]
        },
        purchaseEnquiryItemHandlePageChange ({currentPage, pageSize}) {
            this.purchaseEnquiryItemTablePage.currentPage = currentPage
            this.purchaseEnquiryItemTablePage.pageSize = pageSize
            this.getPurchaseEnquiryItemList()
        },
        getPurchaseEnquiryItemList () {
            let url = '/enquiry/purchaseEnquiryHead/itemList'
            const params = {
                headId: this.currentEditRow.id,
                pageSize: this.purchaseEnquiryItemTablePage.pageSize,
                pageNo: this.purchaseEnquiryItemTablePage.currentPage,
                column: 'id',
                order: 'asc'
            }
            getAction(url, params).then(async rs => {
                if (rs.success) {
                    let purchaseEnquiryItemData = rs.result?.records || [];
                    purchaseEnquiryItemData = await this.formatTableData(purchaseEnquiryItemData);
                    this.purchaseEnquiryItemData = purchaseEnquiryItemData
                    this.purchaseEnquiryItemTablePage.total = rs.result.total
                }
            })
        },
        // 阶梯报价json数据组装
        initRowLadderJson (jsonData) {
            let arr = []
            if (jsonData) {
                arr = JSON.parse(jsonData)
            }
            return arr
        },
        // 阶梯报价默认显示
        defaultRowLadderJson (jsonData) {
            console.log(jsonData)
            let arrString = ''
            if (jsonData) {
                let arr = JSON.parse(jsonData)
                if (Array.isArray(arr)) {
                    arr.forEach((item, index)=> {
                        let ladder = item.ladder
                        let price = item.price || ''
                        let str = `${ladder},${price}`
                        let separator = index===arr.length-1? '': ';'
                        arrString +=str+ separator
                    })
                }
            }
            return arrString
        },
        handleAfterDealSource (result, pageData) {
            const enquiryStatus = ['1'] // “报价中”、“已报价”或“重报价”
            const group = pageData.groups.find(rs => rs.groupCode == 'fileInfo')
            if (group && enquiryStatus.includes(result.enquiryStatus)) {
                if ( result.seePrice == '0') {
                    group.custom.optColumnList.forEach(rs => rs.allow = function () {return true})
                } else {
                    group.custom.optColumnList.forEach(rs => rs.allow = function () {return false})
                }
            }
        },
        afterHandleData (pageData) {
            if (this.currentEditRow?.detectionRequire != '1') {
                pageData.groups = pageData.groups.filter(v=>v.groupCode !== 'probeResultList')
            }
        },
        async getStageTypeData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEnquiryStageType'
            }
            const res = await ajaxFindDictItems(postData)
            if (res.success) {
                this.stageTypeData = res.result
            }
        },
        handleReloadData (res) {
            res.result.ebiddingAmount = res.result.ebiddingAmount ? res.result.ebiddingAmount.toFixed(2) : ''
            res.result.savingAmount = res.result.savingAmount ? res.result.savingAmount.toFixed(2) : ''
            res.result.savingRate = res.result.savingRate ? res.result.savingRate.toFixed(2) : ''
            if (this.stageTypeData && this.stageTypeData.length > 0) {
                res.result.purchaseAttachmentDemandList = res.result.purchaseAttachmentDemandList.map(i => {
                    i.stageType_dictText = this.stageTypeData.find(item => item.value === i.stageType).text
                    return i
                })
            }
            return res
        },
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            let enquiryFoldList = []
            this.currentEditRow.purchaseAttachmentList.map(item=>{
                if(item.businessType!="purchaseRequest"){
                    enquiryFoldList.push(item)
                }
            })
            this.currentEditRow.purchaseAttachmentList = enquiryFoldList
            this.showRemote = true
        },

        downloadFile (row){
            if(this.checkFileAuth(row) == false) {
                return
            }
            this.$refs.detailPage.handleDownload(row)
        },
        preViewEvent (row){
            if(this.checkFileAuth(row) == false) {
                return
            }
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        checkFileAuth (row){
            if(this.currentEditRow.enquiryStatus != '1' && // 报价中
                this.currentEditRow.enquiryStatus != '7' && // 议价中
                this.currentEditRow.enquiryStatus != '9' && // 已定价
                row.uploadElsAccount != this.$ls.get('Login_elsAccount')) {
                this.$message.warning('不允许查看文件')
                return false
            }
            return true
        },
        auditCancelConditionBtn (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let auditStatus = params.auditStatus
            return auditStatus == '1'
        },
        showFlowConditionBtn (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let auditStatus = params.auditStatus
            return auditStatus =='1' || auditStatus =='2' || auditStatus =='3'
        },
        beforeHandleData (data) {
            data.itemColumns.forEach(item => {
                if(item.field == 'ladderPriceJson'){
                    item.slots = this.ladderSlots
                }
                if(item.field == 'costFormJson'){
                    item.slots = this.costSlots
                }
                //if(item.field == 'materialNumber') {
                //    item.sortable = true
                //}
            })
        },
        showFlow (){
            let params = this.$refs.detailPage && this.$refs.detailPage.form || {}
            this.flowId = params.flowId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        auditCancel (){
            let form = this.$refs.detailPage && this.$refs.detailPage.form || {}
            let param = {}
            param['businessType'] = 'publishEnquiry'
            param['businessId'] = form.id
            param['rootProcessInstanceId'] = form.flowId
            this.confirmLoading = true
            postAction(this.url.cancel, param).then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    if (res?.result?.auditStatus == '0') {
                        this.$parent.cancelAuditCallBack(form)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        setLadder (row) {
            this.$refs.ladderPage.open(row)
        },
        openCost (row){
            debugger
            let costJson = row.costFormJson ? JSON.parse(row.costFormJson) : {}
            this.costEditRow = costJson
            let data = costJson['data'] || {}
            this.$refs.costform.open(data, 'detail')
        },

        // 设置列显示
        setColumnData() {
            let st = setTimeout(() => {
                const form = this.$refs.detailPage.form
                // 0 含税价   1 不含税价
                let itemGrid = this.$refs.purchaseEnquiryItemList
                itemGrid.resetColumn(true);
                let columnsList = itemGrid.getColumns();
                columnsList = columnsList.map((column) => {
                    if (column.field == 'taxCode' || column.field == 'taxRate') {
                        column.visible = form.quoteType == 1 ? false : true
                    }
                    return column
                })
                itemGrid.loadColumn(columnsList);
            }, 100)
        },
    }
}
</script>
