<template>
  <div class="PurchaseEightDisciplinesHeadEdit">
    <a-spin
      :spinning="confirmLoading"
      v-if="isView">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :collapseHeadCode="['baseForm','busRule','personFrom']"
        modelLayout="unCollapse"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleAfterRemoteConfigData="handleAfterRemoteConfigData"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
      <!-- <a-modal
                v-drag
                    centered
                    :width="960"
                    :maskClosable="false"
                    :visible="flowView"
                    @ok="closeFlowView"
                    @cancel="closeFlowView">
                    <iframe
                      style="width:100%;height:560px"
                      title=""
                      :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
                      frameborder="0"></iframe>
                  </a-modal> -->
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"/>
      <a-modal
        v-drag
        forceRender
        :visible="editRowModal"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')"
        :width="800"
        @ok="confirmEdit"
        @cancel="closeEditModal">
        <j-editor
          v-if="editRowModal"
          v-model="currentItemContent"></j-editor>
      </a-modal>
      <a-modal
        v-drag
        v-model="previewModal"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
        :footer="null"
        :width="1000">
        <div
          style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
          v-html="previewContent"></div>
      </a-modal>
      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk"/>
      <view-item-diff-modal ref="viewDiffModal"/>
      <His-Contract-Item-Modal ref="hisContractItemModal"/>
      <select-Data-Modal
        ref="selectDataModal"
        @ok="selectDataOk"/>
      <a-modal
        v-drag
        v-model="auditVisible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
        :okText="okText"
        @ok="handleOk">
        <a-textarea
          v-model="opinion"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-modal>
    </a-spin>
  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import selectDataModal from './selectDataModal'
import ViewItemDiffModal from './ViewItemDiffModal'
import HisContractItemModal from './HisContractItemModal'
import {getAction, httpAction} from '@/api/manage'
import JEditor from '@comp/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import {axios} from '@/utils/request'

export default {
    name: 'PurchaseContractHeadModal',
    mixins: [businessUtilMixin],
    components: {
        flowViewModal,
        BusinessLayout,
        fieldSelectModal,
        selectDataModal,
        ViewItemDiffModal,
        HisContractItemModal,
        JEditor
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: {span: 4},
            wrapperCol: {span: 15},
            auditVisible: false,
            opinion: '',
            isView: false,
            showRemote: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            currentRow: {},
            currentUrl: '',
            cancelAuditShow: false,
            refresh: true,
            showHelpTip: false,
            editRowModal: false,
            previewModal: false,
            notShowTableSeq: true,
            editItemRow: {},
            currentItemContent: '',
            previewContent: '',
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            requestData: {
                detail: {
                    url: '/contract/purchaseContractHead/queryById', args: (that) => {
                        return {id: that.currentEditRow.id}
                    }
                }
            },
            externalToolBar: {},
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                    attrs: {
                        type: 'primary'
                    },
                    key: 'alias',
                    authorityCode: 'contract#purchaseContractHead:getPreviewData',
                    click: this.previewPdf
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditPass,
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditReject,
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                download: '/attachment/purchaseAttachment/download',
                detail: '/contract/purchaseContractHead/queryById'
            },
            remoteJsFilePath: ''
        }
    },
    // computed: {
    //     remoteJsFilePath () {
    //         let templateNumber = this.currentEditRow.templateNumber
    //         let templateVersion = this.currentEditRow.templateVersion
    //         let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
    //         let time = new Date().getTime()
    //         if (this.currentEditRow.contractType === '3') {
    //             return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_aduit_contractSimple_${templateNumber}_${templateVersion}.js?t=`+time
    //         } else {
    //             return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_aduit_contract_${templateNumber}_${templateVersion}.js?t=`+time
    //         }
    //     }
    // },
    mounted () {
        const that = this
        getAction('/contract/purchaseContractHead/queryById', {id: this.currentEditRow.businessId}).then(res => {
            if (res.success) {
                if (res.result) {
                    console.log(res.result)
                    that.currentEditRow.templateNumber = res.result.templateNumber
                    that.currentEditRow.templateVersion = res.result.templateVersion
                    that.currentEditRow.templateAccount = res.result.templateAccount
                    that.currentEditRow.rootProcessInstanceId = res.result.flowId
                    that.currentEditRow.contractType = res.result.contractType
                    that.remoteJsFilePath = that.remoteJsFilePathFn()
                    that.isView = true
                } else {
                    that.$message.error('查询失败')
                }
            }
        })
    },
    methods: {
        handleAfterRemoteConfigData (configData) {
            console.log(configData)
            configData.itemColumns.forEach(column => {
                if (column.field == 'sourceNumber') {
                    column['extend'] = {
                        linkConfig: {
                            primaryKey: 'sourceNumber',
                            actionPath: '',
                            bindKey: 'sourceNumber',
                            otherQuery: {linkFilter: true}
                        },
                        handleBefore: function (that, form, linkConfig) {
                            let {sourceType} = form._row
debugger
                            switch(sourceType) {
                            case 'material': // 物料
                                linkConfig.actionPath = '/srm/material/PurchaseMaterialHeadList'
                                linkConfig.primaryKey = 'materialNumber'
                                break
                            case 'purchaseRequest': // 采购申请
                                linkConfig.actionPath = '/srm/demand/PurchaseRequestHeadList'
                                linkConfig.primaryKey = 'requestNumber'
                                break
                            case 'order': // 采购订单
                                linkConfig.actionPath = '/srm/order/purchase/PurchaseOrderHeadList'
                                linkConfig.primaryKey = 'orderNumber'
                                break
                            case 'bidding': // bidding
                                linkConfig.actionPath = '/srm/bidding/purchase/PurchaseBiddingHeadList'
                                linkConfig.primaryKey = 'biddingNumber'
                                break
                            case 'ebidding': // 在线竞价
                                linkConfig.actionPath = '/srm/ebidding/EbiddingBuyHeadList'
                                linkConfig.primaryKey = 'ebiddingNumber'
                                break
                            case 'enquiry': // 询报价
                                linkConfig.actionPath = '/srm/enquiry/purchase/PurchaseEnquiryList'
                                linkConfig.primaryKey = 'enquiryNumber'
                                break
                            default:
                                linkConfig.actionPath = ''
                                linkConfig.primaryKey = ''
                                that.$message.warning('未设置该来源类型跳转')
                            }
                        }
                    }
                }

                //合同审批详情 - 采购合同行信息字段排序
                // let needSortKeyList = ["物料编码","订单数量","主单位","辅数量","辅单位","含税价","含税金额","未税金额"];
                let needSortKeyList = ["materialNumber","quantity","quantityUnit","secondaryQuantity","purchaseUnit","price","taxAmount","netAmount"]
                if (needSortKeyList.includes(column.field)) {
                    column.sortable = true;
                }

            })
        },
        remoteJsFilePathFn () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            if (this.currentEditRow.contractType === '3') {
                return `${elsAccount}/purchase_aduit_contractSimple_${templateNumber}_${templateVersion}.js?t=` + time
            } else {
                return `${elsAccount}/purchase_aduit_contract_${templateNumber}_${templateVersion}.js?t=` + time
            }
        },
        fieldSelectOk () {
        },
        selectDataOk () {
        },
        syncShow ({pageData}) {
            console.log('pageData', (pageData))
            return (pageData.auditStatus === '1')
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                {
                                    key: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    click: this.downloadEvent
                                },
                                {
                                    key: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    click: this.preViewEvent
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLibrary`, '合同条款库'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseContractContentItemList',
                        groupType: 'item',
                        sortOrder: '4',
                        show: true,
                        extend: {
                            optColumnList: []
                        }
                    },
                    {
                        groupName: '关联订单',
                        groupNameI18nKey: '',
                        groupCode: 'orderItemList',
                        groupType: 'item',
                        sortOrder: '5',
                        show: true,
                        extend: {
                            optColumnList: []
                        }
                    }
                ],
                itemColumns: [
                    {
                        title: '订单号',
                        fieldLabelI18nKey: 'i18n_field_orderNumber',
                        groupCode: 'orderItemList',
                        field: 'orderNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '订单行号',
                        fieldLabelI18nKey: 'i18n_field_orderItemNumber',
                        groupCode: 'orderItemList',
                        field: 'itemNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '行类型',
                        fieldLabelI18nKey: 'i18n_field_cAc_20f0fbc',
                        field: 'itemType_dictText',
                        groupCode: 'orderItemList',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '订单行状态',
                        fieldLabelI18nKey: 'i18n_field_ItczE_d79d4484',
                        field: 'itemStatus_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '工厂',
                        fieldLabelI18nKey: 'i18n_field_factory',
                        groupCode: 'orderItemList',
                        field: 'factoryCode_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '库存地点代码',
                        fieldLabelI18nKey: 'i18n_field_GMnCoo_92e8fb6c',
                        groupCode: 'orderItemList',
                        field: 'storageLocationCode_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '物料编码',
                        fieldLabelI18nKey: 'i18n_title_enterMaterialCode',
                        groupCode: 'orderItemList',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '物料描述',
                        fieldLabelI18nKey: '',
                        groupCode: 'orderItemList',
                        field: 'materialDesc',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '物料规格',
                        fieldLabelI18nKey: 'i18n_title_materialSpec',
                        groupCode: 'orderItemList',
                        field: 'materialSpec',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '物料组',
                        fieldLabelI18nKey: 'i18n_title_materialGroup',
                        groupCode: 'orderItemList',
                        field: 'materialGroupCode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '物料组名称',
                        fieldLabelI18nKey: 'i18n_title_materialGroupName',
                        groupCode: 'orderItemList',
                        field: 'materialGroupName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '采购周期',
                        fieldLabelI18nKey: 'i18n_title_requiredDeliveryDate',
                        groupCode: 'orderItemList',
                        field: 'purchaseCycle',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '采购类型',
                        fieldLabelI18nKey: 'i18n_title_purchaseType',
                        groupCode: 'orderItemList',
                        field: 'purchaseType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '要求交期',
                        fieldLabelI18nKey: 'i18n_title_requiredDeliveryDate',
                        groupCode: 'orderItemList',
                        field: 'requireDate',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '订单数量',
                        fieldLabelI18nKey: 'i18n_title_orderQuantity',
                        groupCode: 'orderItemList',
                        field: 'quantity',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '数量单位',
                        fieldLabelI18nKey: 'i18n_title_quantityUnit',
                        groupCode: 'orderItemList',
                        field: 'quantityUnit',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: '来源合同行号',
                        fieldLabelI18nKey: 'i18n_field_wjneEy_ad073b7a',
                        groupCode: 'orderItemList',
                        field: 'sourceItemNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '项目编号',
                        fieldLabelI18nKey: 'i18n_title_projectNumber',
                        field: 'itemId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        dictCode: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '项目名称',
                        fieldLabelI18nKey: 'i18n_field_dIRL_471883d8',
                        field: 'itemName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '项目类型',
                        fieldLabelI18nKey: 'i18n_field_projectType',
                        field: 'itemType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '项目版本',
                        fieldLabelI18nKey: 'i18n_field_itemVersion',
                        field: 'itemVersion',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '变更标识',
                        fieldLabelI18nKey: 'i18n_title_changeIdentification',
                        field: 'changeFlag',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        cellRender: {
                            name: '$switch',
                            type: 'visible',
                            props: {closeValue: '0', openValue: '1', disabled: true}
                        },
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '来源类型',
                        fieldLabelI18nKey: 'i18n_title_sourceType',
                        field: 'sourceType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemList',
                        title: '操作',
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: 'sourceType_dictText',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                    onClick={() => this.viewDetail(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}</a>)
                                if (row.changeFlag == '1') {
                                    resultArray.push(<a title={this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Il_b8efb`, '比对')} style="margin-left:8px"
                                        onClick={() => this.viewDiff(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Il_b8efb`, '比对')}</a>)
                                }
                                return resultArray
                            }
                        }
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: {default: 'grid_opration'}
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
            let itemInfo = pageConfig.groups
                .map(n => ({label: n.groupName, value: n.groupCode}))
            if (resultData.showCustom1 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', false)
            }
            if (resultData.showCustom2 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', false)
            }
            if (resultData.showCustom3 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', false)
            }
            if (resultData.showCustom4 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', false)
            }
            if (resultData.showCustom5 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', false)
            }
            if (resultData.showItem == '1') {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', false)
            } else {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', true)
            }
            if ((resultData.contractStatus == '3' || resultData.contractStatus == '8' || resultData.contractStatus == '6') && (resultData.promiseType == 'promisePurchase' || resultData.promiseType == 'promiseSale')) {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', false)
            } else {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', true)
            }

            if (resultData.promisePurchase == 'order' && (resultData.contractStatus == '3' || resultData.contractStatus == '8' || resultData.contractStatus == '6')) {
                this.hideSingleGroup(this.businessRefName, 'orderItemList', false)
                this.orderAllow = false
            } else {
                this.hideSingleGroup(this.businessRefName, 'orderItemList', true)
                this.orderAllow = true
            }
        },

        confirmEdit () {
            this.editItemRow.itemContent = this.currentItemContent
            let changeFlag = '0'
            if (this.editItemRow.itemContent != this.editItemRow.originalContent) {
                changeFlag = '1'
            }
            this.editItemRow.changeFlag = changeFlag
            this.editRowModal = false
        },
        closeEditModal () {
            this.editRowModal = false
        },
        preview () {
            let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            this.confirmLoading = true
            getAction('/contract/purchaseContractHead/getPreviewData', {id: this.currentEditRow.id}).then((res) => {
                if (res.success) {
                    this.previewModal = true
                    this.previewContent = res.result
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        previewPdf () {
            let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.confirmLoading = true
            axios({
                url: '/contract/purchaseContractHead/download',
                responseType: 'blob',
                params: {id: this.currentEditRow.id}
            }).then((res) => {
                if (res) {
                    debugger
                    let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                    window.open(url)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        },
        goBack () {
            this.$emit('hide')
        },
        handleLoadSuccess (res) {
            this.currentRow = res.res.result
            this.showRemote = true
            this.flowId = this.currentRow.flowId
            this.currentEditRow.rootProcessInstanceId = this.currentRow.flowId
        },
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass () {
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject () {
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk () {
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBackAudit()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {

            })
        },
        downloadEvent (Vue, row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction(this.url.download, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        preViewEvent (Vue, row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        }
    }
}
</script>