<template>
  <div class="nlp-delivery">
    <div class="upBtn">
      <a-upload
        name="fileName"
        :multiple="false"
        :action="url"
        :headers="headers"
        :file-list="fileList"
        @change="handleChange"
        :before-upload="beforeUpload"
      >
        <a-button 
          type="primary" 
          icon="cloud-upload">{{ $srmI18n(`${$getLangAccount()}#i18n_title_pleaseUploadImg`, '上传图片') }}</a-button>
      </a-upload>
    </div>
    <div class="tabContent">
      <template v-if="descripData && descripData.length > 0">
        <a-descriptions
          bordered
          layout="horizontal"
          size="middle"
          :column="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }"
        >
          <a-descriptions-item 
            v-for="(item, index) in descripData" 
            :label="item.labelName" 
            :key="index">{{ item.value }}</a-descriptions-item>
        </a-descriptions>
      </template>
      <template v-else>
        <a-empty />
      </template>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'

export default {
    data () {
        return {
            headers: {
                // 'Content-Type': 'application/json;charset=UTF-8',
                'X-Access-Token': ''
            },
            url: '/els/attachment/nlp/upload',
            fileList: [],
            descripData: [],
            fileStatus: true
        }
    },
    created (){
        const token = Vue.ls.get(ACCESS_TOKEN)
        this.headers['X-Access-Token'] = token
    },
    methods: {
        beforeUpload (file) { // 上传文件前调用该方法
            let fileObj = file || {}
            let file_name = fileObj.name
            let type = file_name.indexOf('.') && file_name.slice(file_name.lastIndexOf('.') + 1)
            const types = ['pdf', 'jpg', 'jpeg', 'svg', 'pig', 'png']
            if (types.indexOf(type) == -1 ) { // 判断文件格式是否正确
                this.fileStatus = false
                this.$message.warning('请上传图片格式文件！')
            } else {
                this.fileStatus = true
            }
            return types.indexOf(type) != -1 
        },
        handleChange (info) { // 文件状态改变调用该方法
            console.log(info)
            if (!this.fileStatus) return
            let fileList = info.fileList.slice(-1) || [] // 保证只留一个文件
            this.fileList = [...fileList]
            this.descripData = []
            if (info.file.status == 'done') {
                let response = info.file.response
                if (response.code === 200) {
                    let result = response.result || {}
                    for (const key in result) {
                        let obj = {}
                        if (Object.hasOwnProperty.call(result, key)) {
                            obj['labelName'] = key
                            obj['value'] = result[key]
                        }
                        this.descripData.push(obj)
                    }
                    this.$message.info(response.message)
                } else {
                    this.$message.error(response.message)
                }
            }
            
        }
    }
}
</script>

<style lang="less" scoped>
.upBtn {
  padding: 15px 12px;
}
.tabContent {
  padding: 0 12px;

  :deep(.ant-descriptions-item-label){
    background: #F5F6F7;
  }
  :deep(.ant-descriptions-item-content){
    background: #ffffff;
  }
  :deep(.ant-descriptions-bordered .ant-descriptions-row){
    border-bottom: 1px solid #dddddd;
  }
}
.ant-empty {
  margin-top: 10%;
}
</style>
