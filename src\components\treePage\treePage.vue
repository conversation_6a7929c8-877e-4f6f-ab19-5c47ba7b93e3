<template>
  <div class="tree-page-contanier">
    <div class="tree-menu">
      <a-tree
        blockNode
        :autoExpandParent="autoExpandParent"
        :expanded-keys="expandedKeys"
        :tree-data="treeData"
        :selectedKeys="selectedKeys"
        @expand="onExpand"
        @select="onSelect"
      >
        <template
          style="position:relative"
          slot="custom"
          slot-scope="item" >
          <span>{{ item.title }}</span>
          <div class="tree-item-tools">
            <a-icon
              title="新增节点"
              type="plus"
              @click.stop="(e) => addTreeNode(item)"></a-icon>
            <a-icon
              title="删除节点"
              type="delete"
              v-if="item.isLeaf"
              @click.stop="(e) => deleteTreeNode(item)"></a-icon>
          </div>
        </template>
      </a-tree>
    </div>
    <div class="tree-content">
      <slot name="nodeContent" />
    </div>
  </div>
</template>
<script>
import {Tree} from 'ant-design-vue'
import { getAction, deleteAction } from '@/api/manage'
export default {
    components: {
        ATree: Tree
    },
    props: {
        url: {
            type: Object,
            default: null
        }
    },
    data () {
        return {
            autoExpandParent: true,
            expandedKeys: [],
            treeData: [],
            selectedKeys: []
        }
    },
    mounted () {
        this.loadTreeMenu()
    },
    methods: {
        loadTreeMenu () {
            getAction (this.url.list, {}).then(res => {
                if(res.success) {
                    this.treeData = this.handleTreeData(res.result)
                    if(!this.selectedKeys.length) {
                        this.selectedKeys = [this.treeData[0].key]
                        this.onSelect(this.selectedKeys, this.treeData[0])
                    }
                }
            })
        },
        handleTreeData (list) {
            let that = this
            list.forEach(item => {
                if(item.children && item.children.length) {
                    that.expandedKeys.push(item.key)
                    that.handleTreeData(item.children)
                }
                item.scopedSlots = { title: 'custom' }
            })
            return list
        },
        onExpand (expandedKeys) {
            this.expandedKeys = expandedKeys
            this.autoExpandParent = false
        },
        onSelect (selectedKeys, obj) {
            let item = obj.node ? obj.node.dataRef : obj
            this.selectedKeys = selectedKeys
            this.$parent.edit(item)
        },
        addTreeNode (item) {
            this.$parent.add(item)
        },
        deleteTreeNode (item) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmDelete`, '确认删除'),
                content: '是否删除【'+item.title+'】节点?',
                onOk: function () {
                    deleteAction(that.url.delete, {id: item.id}).then((res) => {
                        if(res.success) {
                            if(that.selectedKeys.length && item.key == that.selectedKeys[0]) {
                                that.selectedKeys = []
                            }
                            that.loadTreeMenu()
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        }
    }
}
</script>
<style lang="less">
    .tree-page-contanier {
        display: flex;
        flex-flow: row;
        height: 100%;
        .tree-menu {
            background-color: #fff;
            margin-right: 12px;
            width: 280px;
            min-height: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
        }
        .tree-content {
            background-color: #fff;
            flex: 1;
            .ant-collapse > .ant-collapse-item > .ant-collapse-header {
                padding: 4px 16px
            }
            .ant-page-header {
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            border-bottom: none;
            }
            .ant-page-header-ghost {
                background-color: #fff;
            }
        }
        .tree-item-tools {
            float: right;
            font-size: 12px;
            color: #1890ff;
            .anticon {
                margin-right: 8px
            }
        }
    }
</style>