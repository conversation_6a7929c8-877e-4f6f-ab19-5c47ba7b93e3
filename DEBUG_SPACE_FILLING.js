// 🚀 联系人信息页签空间填充调试脚本
// 在浏览器控制台执行此脚本来验证和调试空间填充效果

console.log('🔍 开始检查联系人信息页签空间填充效果...');

// 1. 检查当前窗口尺寸
const windowInfo = {
    clientHeight: document.documentElement.clientHeight,
    clientWidth: document.documentElement.clientWidth,
    innerHeight: window.innerHeight,
    innerWidth: window.innerWidth
};
console.log('📐 窗口尺寸信息:', windowInfo);

// 2. 查找联系人信息表格元素
const contactsGrid = document.querySelector('[ref*="supplierContactsInfoList"]');
const contactsGridByDataCode = document.querySelector('[data-group-code="supplierContactsInfoList"]');
const tabContent = document.querySelector('.ant-tabs-content-holder .ant-tabs-tabpane');

console.log('🎯 表格元素查找结果:');
console.log('- 通过ref查找:', contactsGrid ? '✅ 找到' : '❌ 未找到');
console.log('- 通过data-group-code查找:', contactsGridByDataCode ? '✅ 找到' : '❌ 未找到');
console.log('- Tab内容区域:', tabContent ? '✅ 找到' : '❌ 未找到');

// 3. 检查表格当前样式
if (contactsGrid) {
    const gridStyles = window.getComputedStyle(contactsGrid);
    const tableElement = contactsGrid.querySelector('.vxe-table');
    const bodyWrapper = contactsGrid.querySelector('.vxe-table--body-wrapper');
    
    console.log('📊 联系人信息表格当前样式:');
    console.log('- 容器高度:', gridStyles.height);
    console.log('- 容器最小高度:', gridStyles.minHeight);
    console.log('- 容器最大高度:', gridStyles.maxHeight);
    
    if (tableElement) {
        const tableStyles = window.getComputedStyle(tableElement);
        console.log('- 表格高度:', tableStyles.height);
        console.log('- 表格最小高度:', tableStyles.minHeight);
    }
    
    if (bodyWrapper) {
        const bodyStyles = window.getComputedStyle(bodyWrapper);
        console.log('- 表格体高度:', bodyStyles.height);
        console.log('- 表格体最大高度:', bodyStyles.maxHeight);
        console.log('- 表格体最小高度:', bodyStyles.minHeight);
    }
}

// 4. 计算理想高度
const idealHeight = windowInfo.clientHeight - 180;
const currentHeight = contactsGrid ? contactsGrid.offsetHeight : 0;
const spaceUtilization = currentHeight / windowInfo.clientHeight * 100;

console.log('📈 空间利用分析:');
console.log(`- 理想高度: ${idealHeight}px`);
console.log(`- 当前高度: ${currentHeight}px`);
console.log(`- 空间利用率: ${spaceUtilization.toFixed(1)}%`);
console.log(`- 高度差异: ${idealHeight - currentHeight}px`);

// 5. 检查是否有空白区域
const tabPane = document.querySelector('.ant-tabs-tabpane');
if (tabPane) {
    const tabPaneHeight = tabPane.offsetHeight;
    const tabPaneStyles = window.getComputedStyle(tabPane);
    console.log('📋 Tab页签内容区域:');
    console.log(`- Tab页签高度: ${tabPaneHeight}px`);
    console.log(`- Tab页签最小高度: ${tabPaneStyles.minHeight}`);
    
    // 计算空白区域
    const usedSpace = currentHeight;
    const availableSpace = tabPaneHeight;
    const whiteSpace = availableSpace - usedSpace;
    
    console.log('🎨 空白区域分析:');
    console.log(`- 可用空间: ${availableSpace}px`);
    console.log(`- 已用空间: ${usedSpace}px`);
    console.log(`- 空白区域: ${whiteSpace}px`);
    
    if (whiteSpace > 100) {
        console.warn('⚠️ 检测到较大空白区域，建议进一步优化');
    } else {
        console.log('✅ 空间利用良好');
    }
}

// 6. 提供临时调整方案
console.log('🔧 临时调整方案:');
console.log('如果空白区域仍然较大，可以在控制台执行以下代码进行临时调整:');

console.log(`
// 方案1: 强制设置表格高度
if (contactsGrid) {
    contactsGrid.style.height = '${idealHeight}px';
    contactsGrid.style.minHeight = '${idealHeight}px';
    console.log('✅ 已临时调整表格容器高度');
}

// 方案2: 强制设置表格体高度
const bodyWrapper = document.querySelector('[ref*="supplierContactsInfoList"] .vxe-table--body-wrapper');
if (bodyWrapper) {
    bodyWrapper.style.maxHeight = '${idealHeight - 50}px';
    bodyWrapper.style.minHeight = '${idealHeight - 100}px';
    console.log('✅ 已临时调整表格体高度');
}

// 方案3: 强制设置整个表格高度
const table = document.querySelector('[ref*="supplierContactsInfoList"] .vxe-table');
if (table) {
    table.style.height = '${idealHeight}px';
    table.style.minHeight = '${idealHeight}px';
    console.log('✅ 已临时调整整个表格高度');
}
`);

// 7. 监听窗口大小变化
let resizeTimer;
window.addEventListener('resize', () => {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(() => {
        console.log('📏 窗口大小已变化，重新计算空间利用率...');
        // 重新执行检查逻辑
        const newHeight = document.documentElement.clientHeight;
        const newIdealHeight = newHeight - 180;
        const newCurrentHeight = contactsGrid ? contactsGrid.offsetHeight : 0;
        const newUtilization = newCurrentHeight / newHeight * 100;
        
        console.log(`新的空间利用率: ${newUtilization.toFixed(1)}%`);
        console.log(`新的理想高度: ${newIdealHeight}px`);
    }, 500);
});

console.log('🎉 空间填充检查完成！请查看上述分析结果。');
console.log('💡 如果仍有空白区域，请尝试上面提供的临时调整方案。');
