<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction } from '@/api/manage'
export default {
    name: 'ElsSealsAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        const _self = this
        return {
            selectType: 'seals',
            pageData: {
                form: {
                    relationId: '',
                    companyName: '',
                    alias: '',
                    height: '',
                    width: '',
                    transparentFlag: '',
                    filePath: '',
                    sealId: '',
                    subAccount: '',
                    orgId: '',
                    accountId: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WeLD_27cabba0`, '印章维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                                    fieldName: 'elsAccount',
                                    bindFunction: function (Vue, data){
                                        _self.$set(Vue.form, 'elsAccount', data[0].toElsAccount)
                                        Vue.form.companyName = null,
                                        Vue.form.subAccount = null,
                                        Vue.form.name = null,
                                        Vue.form.mobile = null,
                                        Vue.form.email = null,
                                        Vue.form.subAccountId = null
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'toElsAccount', title: '账号', fieldLabelI18nKey: 'i18n_title_account', with: 150},
                                            {field: 'supplierName', title: '名称', fieldLabelI18nKey: 'i18n_field_qcItemName', with: 150}
                                        ], modalUrl: 'supplier/supplierMaster/list', modalParams: {}
                                    }
                                },
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '选择机构'),
                                    fieldName: 'companyName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '选择机构'),
                                    bindFunction: function (Vue, data){
                                        Vue.form.companyName = data[0].companyName,
                                        Vue.form.subAccount = data[0].subAccount,
                                        Vue.form.orgId = data[0].orgId,
                                        Vue.form.accountId = data[0].accountId,
                                        Vue.form.relationId = data[0].id
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), with: 150},
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '绑定子账号'), with: 150},
                                            {field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationID`, '机构id'), with: 150},
                                            {field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_subAccountID`, '子账号id'), with: 150}
                                        ],
                                        modalUrl: '/esign/elsEnterpriseCertificationInfo/replaceList',
                                        modalParams: function (Vue, form) {
                                            return {
                                                elsAccount: form.elsAccount
                                            }
                                        },
                                        beforeCheckedCallBack: function (Vue, pageData, groupData, form) {
                                            return new Promise((resolve, reject) => {
                                                let elsAccount = form.elsAccount || ''
                                                return elsAccount !== '' ? resolve('success') : reject('请先选择供应商！')
                                            })
                                        }
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_sMURgjgo`, '印章别名'),
                                    fieldName: 'alias',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_OQj0KZ8V`, '关联机构id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_yVy933rH`, '机构id'),
                                    fieldName: 'orgId',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                                    fieldName: 'subAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_HS1PSf1z`, '子账号id'),
                                    fieldName: 'accountId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'number',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeVzpx_371db172`, '印章宽度(px)'),
                                    fieldName: 'width'
                                },
                                {
                                    fieldType: 'number',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Wexzpx_e67fab37`, '印章高度(px)'),
                                    fieldName: 'height'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_transparentFlag`, '是否对图片进行透明化处理'),
                                    fieldName: 'transparentFlag',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sealId`, '印章id'),
                                    fieldName: 'sealId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'image',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_qBGuFJsS`, '图片路径'),
                                    fieldName: 'filePath',
                                    required: '1',
                                    extend: {multiple: false, limit: 1, businessType: 'esign', actionRoutePath: '/srm/esign/ReplaceElsSealsList'}
                                }
                            ],
                            validateRules: {
                                alias: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeqRxOLV_4fade7f4`, '签章别名不能为空')}],
                                companyName: [{required: true, message: '机构不能为空'}],
                                orgId: [{required: true, message: '机构id不能为空'}],
                                filePath: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WexOLV_4a4b6180`, '印章不能为空')}]
                            }
                        }

                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/esign/elsSeals/replaceAdd'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {},
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.add
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.goBack()
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>