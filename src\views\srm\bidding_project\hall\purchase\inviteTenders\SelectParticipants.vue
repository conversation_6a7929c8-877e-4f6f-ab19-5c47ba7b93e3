<template>
  <div class="select-participants">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      @content-header-specialist="handleAddItem('specialist')"
      @content-header-member="handleAddItem('member')"
      @content-header-save="handleSave"
    />

    <div
      class="container"
      :style="style">

      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_title_evaluationPersonnel`, '评标人员') }}</div>
          <div class="table">
            <vxe-grid
              ref="purchaseBiddingSpecialistList"
              v-bind="defaultGridOption"
              :columns="specialistColumns">
              <template slot="empty">
                <a-empty />
              </template>
              <template #memberType_default="{ row, column }">
                <a-select
                  v-model="row[column.property]"
                  disabled>
                  <a-select-option
                    v-for="el in memberType"
                    :key="el.value"
                    :value="el.value"
                  >
                    {{ el.title }}
                  </a-select-option>
                </a-select>
              </template>
              <template #grid_opration="{ row, column }">
                <template v-if="row.projectMemberType != '1'">
                  <a
                    v-for="(item, i) in optColumnList"
                    :key="i"
                    :title="item.title"
                    style="margin:0 4px"
                    @click="item.clickFn(row, column, 'purchaseBiddingSpecialistList')">{{ item.title }}</a>
                </template>
              </template>
            </vxe-grid>
          </div>
        </div>

        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_title_membersBidTeam`, '招标小组成员') }}</div>
          <div class="table">
            <vxe-grid
              ref="member"
              v-bind="defaultGridOption"
              :columns="memberColumns">
              <template slot="empty">
                <a-empty />
              </template>
              <template #memberType_default="{ row, column }">
                <a-select
                  v-model="row[column.property]"
                  disabled>
                  <a-select-option
                    v-for="el in memberType"
                    :key="el.value"
                    :value="el.value"
                  >
                    {{ el.title }}
                  </a-select-option>
                </a-select>
              </template>
              <template #memberRole_default="{ row, column }">
                <a-select
                  v-model="row[column.property]"
                  allowClear>
                  <a-select-option
                    v-for="el in memberRole"
                    :key="el.value"
                    :value="el.value"
                  >
                    {{ el.title }}
                  </a-select-option>
                </a-select>
              </template>
              <template #grid_opration="{ row, column }">
                <template v-if="row.projectMemberType != '1'">
                  <a
                    v-for="(item, i) in optColumnList"
                    :key="i"
                    :title="item.title"
                    style="margin:0 4px"
                    @click="item.clickFn(row, column, 'member')">{{ item.title }}</a>
                </template>
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>

    <field-select-modal ref="fieldSelectModal" />

  </div>
</template>

<script>
import ContentHeader from '@/views/srm/bidding_project/hall/components/content-header'
import { getAction, postAction } from '@/api/manage'
import { ajaxFindDictItems } from '@/api/api'
import { USER_INFO } from '@/store/mutation-types'
import fieldSelectModal from '@comp/template/fieldSelectModal'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        'content-header': ContentHeader,
        'field-select-modal': fieldSelectModal
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'getBiddingOptions'
    ],
    data () {
        return {
            confirmLoading: false,
            memberType: [],
            memberRole: [],
            selectType: '',
            userInfo: {},
            form: {},
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addEvaluationPersonnel`, '添加评标人员'), event: 'specialist', authorityCode: 'bidding#purchaseBiddingHead:addEvaluationMember' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addMembersBidTeam`, '添加招标小组成员'), event: 'member', authorityCode: 'bidding#purchaseBiddingHead:addTeamMember' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', event: 'save', authorityCode: 'bidding#purchaseBiddingHead:save' }
            ],
            showHeader: true,
            height: 0,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                columns: [],
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'dblclick', mode: 'cell' }
            },
            specialistColumns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), field: 'subAccount', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistName`, '专家姓名'), field: 'name', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistType`, '专家类型'), field: 'specialistClasses_dictText', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberType`, '成员类型'), field: 'memberType', width: 150, slots: { default: 'memberType_default' } },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'), field: 'mobileTelephone', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), field: 'email', width: 150 },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
            ],
            optColumnList: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteItemEvent }
            ],
            memberColumns: [
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), field: 'subAccount', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), field: 'name', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberType`, '成员类型'), field: 'memberType', width: 150, slots: { default: 'memberType_default' } },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_memberRole`, '成员角色'), field: 'memberRole', width: 150, slots: { default: 'memberRole_default' } },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'), field: 'mobileTelephone', width: 150 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), field: 'email', width: 150 },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
            ]
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        }
    },

    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleAddItem (type) {
            this.selectType = type
            if (type === 'specialist') {
                let url = '/specialist/specialistInfo/list'
                let columns = [
                    {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expertName`, '专家姓名'), width: 150},
                    {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expertAccount`, '专家账号'), width: 150},
                    {field: 'specialistClasses_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expertType`, '专家类型'), width: 200},
                    {field: 'mobileTelephone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expertPhone`, '专家电话'), width: 200},
                    {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), width: 200}
                ]
                this.$refs.fieldSelectModal.open(url, {}, columns, 'multiple')
            }
            this.selectType = type
            if (type === 'member') {
                let url = '/account/elsSubAccount/list'
                let columns = [
                    {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), with: 150},
                    {field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), with: 150},
                    {field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话'), with: 150},
                    {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'), with: 150}
                ]
                this.$refs.fieldSelectModal.open(url, {}, columns, 'multiple')
            }
        },
        fieldSelectOk (data) {
            if (this.selectType === 'specialist'){
                let gridRef = this.$refs.purchaseBiddingSpecialistList
                const { fullData = [] } = gridRef.getTableData() || {}
                let list = fullData.map(item => {
                    return item.subAccount
                })
                // 过滤已有数据
                let insertData = data
                    .filter(item => {
                        return !list.includes(item.subAccount)
                    })
                    .map(({
                        subAccount,
                        name,
                        mobileTelephone,
                        email,
                        specialistClasses,
                        specialistClasses_dictText
                    }) => ({
                        subAccount,
                        name,
                        mobileTelephone,
                        email,
                        specialistClasses,
                        specialistClasses_dictText,
                        memberType: '1'
                    }))

                gridRef.insertAt(insertData, -1)
            }
            if (this.selectType === 'member'){
                let gridRef = this.$refs.member
                const { fullData = [] } = gridRef.getTableData() || {}
                let list = fullData.map(item => {
                    return item.subAccount
                })
                // 过滤已有数据
                let insertData = data
                    .filter(item => {
                        return !list.includes(item.subAccount)
                    })
                    .map(({
                        subAccount,
                        realname,
                        phone,
                        email
                    }) => ({
                        subAccount,
                        name: realname,
                        mobileTelephone: phone,
                        email,
                        memberType: '2'
                    }))
                gridRef.insertAt(insertData, -1)
            }
        },
        getUserInfo () {
            this.userInfo = this.$ls.get(USER_INFO)
        },
        getDict (prop) {
            const params = {
                busAccount: this.userInfo.elsAccount,
                dictCode: prop
            }
            ajaxFindDictItems(params).then(res => {
                if (res.success) {
                    this[prop] = res.result || []
                }
            })
        },
        deleteItemEvent (row, column, ref) {
            const grid = this.$refs[ref]
            grid.remove(row)
        },
        getData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingHead/queryById'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { purchaseBiddingSpecialistList = [], ...others } = res.result || {}
                    this.form = { ...others }
                    const specialistArr = purchaseBiddingSpecialistList.filter(n => n.memberType === '1')
                    const memberArr = purchaseBiddingSpecialistList.filter(n => n.memberType === '2')
                    this.$refs.purchaseBiddingSpecialistList.loadData(specialistArr)
                    this.$refs.member.loadData(memberArr)
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleSave () {
            const callback = () => {
                const specialistArr = this.$refs.purchaseBiddingSpecialistList.getTableData().fullData
                const memberArr = this.$refs.member.getTableData().fullData

                const params = {
                    id: this.form.id,
                    purchaseBiddingSpecialistList: specialistArr.concat(memberArr)
                }

                const url = '/bidding/purchaseBiddingHead/saveMember'
                this.confirmLoading = true
                postAction(url, params)
                    .then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.getBiddingOptions()
                        }
                    })
                    .finally(() => {
                        this.confirmLoading = false
                    })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_evaluationPersonnel`, '评标人员'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmEvaluationPersonnel`, '是否确认当前所选评标人员？'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        init () {
            this.height = document.documentElement.clientHeight
            let dictArr = ['memberType', 'memberRole']
            dictArr.forEach(n => this.getDict(n))
            this.getData()
            this.getUserInfo()
        }
    },
    // 使用 beforeRouteUpdate 导航守卫监听路由变化
    // 对路由销毁重建
    beforeRouteUpdate (to, from, next) {
        this.routerRefresh() //路由销毁重建方法
        next()
    },
    created () {
        this.init()
    }
}
</script>

<style lang="less" scoped>
.select-participants {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
}
</style>
