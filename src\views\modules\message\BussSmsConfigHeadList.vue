<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <bussSmsConfigHead-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="modalForm"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import BussSmsConfigHeadModal from './modules/BussSmsConfigHeadModal'
import {listPageMixin} from '@comp/template/listPageMixin'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    mixins: [listPageMixin],
    components: {
        BussSmsConfigHeadModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: srmI18n(`${getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/message/bussSmsConfigHead/list',
                delete: '/message/bussSmsConfigHead/delete',
                deleteBatch: '/message/bussSmsConfigHead/deleteBatch',
                exportXlsUrl: '/message/bussSmsConfigHead/exportXls',
                importExcelUrl: '/message/bussSmsConfigHead/importExcel',
                columns: 'bussSmsConfigHeadList'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(srmI18n(`${getLangAccount()}#i18n_title_businessModuleMsgConfig`, '业务模块消息配置'))
        }
    }
}
</script>