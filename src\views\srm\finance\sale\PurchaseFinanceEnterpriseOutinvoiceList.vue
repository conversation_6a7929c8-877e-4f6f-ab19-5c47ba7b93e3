<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url" />

    <!-- 编辑页面 -->
    <PurchaseFinanceEnterpriseOutinvoiceEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hidePage"/>

    <!-- 详情界面 -->
    <PurchaseFinanceEnterpriseOutinvoiceDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />

    <!-- 供应商主数据列表弹出框 start -->
    <a-modal
    v-drag    
      :title="$srmI18n(`${$getLangAccount()}#`, '采购方列表信息')"
      :visible="visible1"
      :width="890"
      @cancel="purchaseListCancel"
    >
      <template slot="footer">
        <a-button
          key="submit"
          type="primary"
          :loading="purchaseSubmitLoading"
          @click="this.purchaseListSubmit">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
        </a-button>
      </template>
      <div>
        <vxe-grid
          border
          resizable
          align="center"
          show-overflow
          highlight-hover-row
          max-height="350"
          row-id="id"
          size="small"
          ref="selectPurchaseGrid"
          :loading="purchaseTableLoading"
          :data="purchaseTableData"
          :pager-config="purchaseTablePage"
          :columns="purchaseTableColumns"
          :radio-config="checkedConfig"
          @page-change="purchaseListHandlePageChange">
        </vxe-grid>
      </div>
    </a-modal>
    <!-- 供应商主数据列表弹出框 end -->
    />
  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import PurchaseFinanceEnterpriseOutinvoiceEdit from './modules/PurchaseFinanceEnterpriseOutinvoiceEdit'
import PurchaseFinanceEnterpriseOutinvoiceDetail from './modules/PurchaseFinanceEnterpriseOutinvoiceDetail'
import { srmI18n, getLangAccount } from '@/utils/util'
import {getAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseFinanceEnterpriseOutinvoiceEdit,
        PurchaseFinanceEnterpriseOutinvoiceDetail
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pfLKqy_11eaf4b3`, '纳税人识别号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    // {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handlePurchaseList, type: 'primary'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'finance#financeEnterpriseOutinvoice:view'}
                ]
            },
            // 供应商主数据列表参数 start
            visible1: false,
            purchaseSubmitLoading: false,
            checkedConfig: {highlight: true, reserve: true, trigger: 'row'},
            purchaseTableData: [],
            queryParams: {},
            userTableRoleId: '',
            purchaseTableLoading: false,
            purchaseListKeyWord: '',
            purchaseTablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 500,
                align: 'left',
                pageSizes: [20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            purchaseTableColumns: [
                { type: 'radio', width: 40 },
                { type: 'seq', title: srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                { field: 'elsAccount', title: srmI18n(`${getLangAccount()}#`, '采购商ELS账号'), width: 370},
                { field: 'name', title: srmI18n(`${getLangAccount()}#`, '采购商名称'), width: 370 }
            ],
            // 供应商主数据列表参数 end
            url: {
                list: '/finance/financeEnterpriseOutinvoice/saleToPurchaseByList',
                delete: '/finance/financeEnterpriseOutinvoice/delete',
                deleteBatch: '/finance/financeEnterpriseOutinvoice/deleteBatch',
                exportXlsUrl: 'finance/financeEnterpriseOutinvoice/exportXls',
                importExcelUrl: 'finance/financeEnterpriseOutinvoice/importExcel',
                columns: 'purchaseFinanceEnterpriseOutinvoiceList'
            }
        }
    },
    methods: {
        // 新增、编辑、详情页面隐藏
        hidePage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        // 供应商主数据列表-弹出框事件
        handlePurchaseList (){
            // 列表数据data
            this.purchaseTableData = []
            // 查询内容
            this.purchaseListKeyWord = ''
            this.queryParams = {}
            // 弹出框选中的记录
            this.currentEditRow = null
            getAction('/enterprise/elsEnterpriseInfo/getPurchaseAccount', {pageNo: 1, pageSize: 20, column: 'create_time', order: 'desc', toElsAccount: this.$ls.get('Login_elsAccount')}).then(res => {
                if (res.success) {
                    let list = res.result.records || []
                    this.purchaseTableData = list
                    this.purchaseTablePage.total = res.result.total
                    this.visible1 = true
                    // 清除上一次勾选中的数据
                    this.$refs.selectPurchaseGrid && this.$refs.selectPurchaseGrid.clearRadioReserve()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        // 供应商主数据列表-分页事件
        purchaseListHandlePageChange ({ currentPage, pageSize }) {
            this.purchaseTablePage.currentPage = currentPage
            this.purchaseTablePage.pageSize = pageSize
            let params = Object.assign({column: 'create_time', order: 'desc'}, this.queryParams, {pageSize: pageSize, pageNo: currentPage})
            this.purchaseListLoadData(params)
        },
        // 供应商主数据列表-查询、分页后台请求事件
        purchaseListLoadData (params) {
            this.purchaseTableLoading = true
            getAction('/enterprise/elsEnterpriseInfo/getPurchaseAccount', params).then((res) => {
                if (res.success) {
                    let result = res.result.records
                    result = result || []
                    this.purchaseTableData = result
                    this.purchaseTablePage.total = res.result.total
                    // 清除上一次勾选中的数据
                    this.$refs.selectPurchaseGrid && this.$refs.selectPurchaseGrid.clearRadioReserve()
                }
            }).finally(() => {
                // 关闭笼罩层
                this.purchaseTableLoading = false
            })
        },
        // 供应商主数据列表-确定事件
        purchaseListSubmit (){
            // 获取选中的数据
            let itemList = this.$refs.selectPurchaseGrid.getRadioRecord()

            // 只有选中数据才能操作
            if (itemList) {
                // 确定按钮 loading 开启
                this.purchaseSubmitLoading = true
                this.currentEditRow = {elsAccount: itemList.elsAccount, purchaseName: itemList.name, busAccount: itemList.elsAccount}
                this.showEditPage = true
                this.visible1 = false
                this.purchaseSubmitLoading = false
            } else {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
            }
        },
        // 供应商主数据列表-取消事件
        purchaseListCancel (){
            this.visible1 = false
            this.purchaseSubmitLoading = false
        }
    }
}
</script>