<template>
  <vxe-grid
    :height="isDynamics()? '100%' : '334px'"
    class="test"
    :ref="group.groupCode"
    row-id="_X_ID"
    v-bind="currentGridConfig"
    :loading="gridLoading"
    @cell-click="cellClickEvent"
    @edit-actived="editActivedEvent"
    :edit-config="group.editConfig"
    :edit-rules="group.editRules"
    :columns="group.columns"
    :sort-config="group.sortConfig || {}"
    :menuConfig="menuConfig"
    @menu-click="menuClickEvent"
    @cell-menu="cellMenuEvent"
    :show-footer="showGridFooter"
    :footer-method="
      ({ columns, data }) => {
        return footerMethod({ group, columns, data })
      }
    "
    @checkbox-change="checkboxChange"
  >
    <!-- 表格代码编辑器 -->
    <template #code_editor_col_render="{ row, column }">
      <code-editor-model
        :value="row[column.property]"
        @handleSureClick="
          (content) => {
            row[column.property] = content
          }
        "
      ></code-editor-model>
    </template>
    <!-- 表格富文本编辑器 -->
    <template #rich_editor_col_render="{ row, column, _columnIndex }">
      <rich-editor-model
        :value="row[column.property]"
        :disabled="group.columns[_columnIndex].disabled"
        @handleSureClick="richEditorSureClick($event, row, column)"
      ></rich-editor-model>
    </template>
    <!-- 数据字典渲染 -->
    <template #renderDictLabel="{ row, column }">
      <span>
        {{ getDictLabel(row[column.property], column, group.columns) }}
      </span>
    </template>
    <!-- 货币千分位 -->
    <template #renderCurrency="{ row, column }">
      <span>{{ currencyFormat(row[column.property], column, group.columns) }}</span>
    </template>
    <!-- toolbar导航按钮 -->
    <template #toolbar_buttons>
      <business-button
        :buttons="group.externalToolBar"
        :groupCode="group.groupCode"
        :pageConfig="pageConfig"
        :currentEditRow="currentEditRow"
        isToolbarButtons
        v-bind="$attrs"
        v-on="$listeners"
      />
    </template>
    <template #grid_opration="{ row, column }">
      <div v-if="group.extend && group.extend.optColumnList">
        <span
          v-for="(opt, optIndex) in group.extend.optColumnList"
          :key="'opt_' + row.id + '_' + optIndex"
        >
          <a
            :title="opt.title"
            style="margin: 0 4px"
            :disabled="typeof opt.disabled === 'function' ? opt.disabled(row) : opt.disabled"
            v-show="!opt.hide"
            @click="
              () => {
                optColumnFuntion(opt, row, column, group.columns)
              }
            "
            >{{ opt.title }}</a
          >
        </span>
      </div>
    </template>
    <!--使用 bottom 插槽-->
    <template #bottom>
      <div
        class="summary-message"
        v-if="group.total.totalValue"
      >
        <span class="summary-message-content">
          {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }} {{ $srmI18n(`${$getLangAccount()}#${group.i18n_title_generalSummary}`, '总汇总') }}：<span class="total-num">{{ group.total.totalValue }}</span></span
        >
      </div>
    </template>

    <template #treeSelect="{ row, column, _columnIndex }">
      <!-- {{ group.columns[_columnIndex+1] }} -->
      <!-- <span slot="label">
        {{ $srmI18n(`${busAccount}#${group.columns[_columnIndex+1].fieldLabelI18nKey}`, row[column.property]) }}
        <a-tooltip
          v-if="group.columns[_columnIndex+1].helpText"
          :title="group.columns[_columnIndex+1].helpText">
          <a-icon type="question-circle-o" />
        </a-tooltip>
      </span> -->
      <m-tree-select
        v-model="row[column.property]"
        allowClear
        :disabled="group.columns[_columnIndex + 1].disabled"
        :multiple="(group.columns[_columnIndex + 1].extend && group.columns[_columnIndex + 1].extend.multiple) || false"
        :maxTagCount="(group.columns[_columnIndex + 1].extend && group.columns[_columnIndex + 1].extend.maxTagCount) || 1"
        :sourceUrl="group.columns[_columnIndex + 1].dictCode"
        :sourceMap="group.columns[_columnIndex + 1].sourceMap"
        :valueMap="group.columns[_columnIndex + 1].valueMap"
        :titleMap="group.columns[_columnIndex + 1].titleMap"
        :showEmptyNode="group.columns[_columnIndex + 1].showEmptyNode"
        :placeholder="group.columns[_columnIndex + 1].placeholder"
        :configData="group.columns[_columnIndex + 1]"
        @change="changeSelectValue"
      />
    </template>
    <template #empty>
      <m-empty
        :displayModel="displayModel"
        :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
      />
    </template>
  </vxe-grid>
</template>

<script lang="jsx">
import BusinessButton from './components/BusinessButton'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { downFile } from '@/api/manage'
import { mapActions, mapState } from 'vuex'
import RichEditorModel from '@comp/richEditorModel/RichEditorModel'
import { getObjType } from '@/utils/util'
import Sortable from 'sortablejs'
import { currency } from '@/filters'
import { bindfunctionMiddleware } from '@/utils/util'
import MTreeSelect from '@comp/treeSelect/mTreeSelect'
import { PRIVATE_BIND_FUNCTION } from '@/utils/constant.js'
export default {
  name: 'EditGridLayout',
  inject: ['tplRootRef'],
  components: {
    RichEditorModel,
    BusinessButton,
    MTreeSelect
  },
  props: {
    // 传入归属方busAccount
    busAccount: {
      required: true,
      type: String,
      default: null
    },
    currentStep: {
      type: [String, Number]
    },
    currentEditRow: {
      type: Object,
      default() {
        return {}
      }
    },
    gridLoading: {
      type: Boolean,
      default: false
    },
    group: {
      type: Object,
      required: true,
      default() {
        return {}
      }
    },
    pageConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    loadData: {
      type: Array,
      default() {
        return []
      }
    },
    gridConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    displayModel: {
      type: String,
      default: 'tab'
    },
    gridFooterMethod: {
      type: Function,
      default: null
    },
    showGridFooter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tokenHeader: { 'X-Access-Token': this.$ls.get(ACCESS_TOKEN) },
      gridData: this.loadData || [],
      menuConfig: this.gridConfig.menuConfig,
      groupSortable: null
    }
  },
  computed: {
    ...mapState({
      cache_vuex_editActivedInfo: (state) => state.app.cache_vuex_editActivedInfo
    }),
    columnsCompute() {
      return (columns) => columns.filter((rs) => rs.fold != 1)
    },
    currentGridConfig() {
      return this.gridConfig
    }
  },
  watch: {
    loadData: {
      immediate: true,
      handler: function (val) {
        this.gridData = val
        this.loadGridData()
      }
    }
  },
  created() {
    if (!!this.group.extend && this.group.extend.dragAndDrop) {
      this.groupRowDrop()
    }
  },
  methods: {
    isDynamics() {
      if(
        this.group.groupCode === 'purchaseBiddingHeadList' || 
        this.group.groupCode === 'purchaseBiddingItemList' || 
        this.group.groupCode === 'recAcceptReturnList' || 
        this.group.groupCode === 'recAdditionalChargesList' || 
        this.group.groupCode === 'recChargeList' || 
        this.group.groupCode === 'prePaymentWriteOffList' || 
        this.group.groupCode === 'invoiceList'
      ) {
        return true;
      }
      return false;
    },
    // select 改变事件
    changeSelectValue(realValue, opt, oldVal, configData) {
      let parentRef = null
      let groupData = null
      if (this.pageConfig.groups[this.currentStep]) {
        let parentRefName = this.pageConfig.groups[this.currentStep].groupCode
        parentRef = this.$refs[parentRefName]
        groupData = this.pageConfig.groups[this.currentStep]
      }
      if (configData && configData.bindFunction && typeof configData.bindFunction === 'function') {
        if (configData.bindFunction.name === PRIVATE_BIND_FUNCTION) {
          let params = {
            _pageData: this.pageConfig, // 页面所有数据
            _form: this.group.formModel,
            _cacheAllData: this.tplRootRef.getAllData(),
            _value: realValue
          }
          bindfunctionMiddleware(this.tplRootRef, configData.bindFunction, params)
        } else {
          configData.bindFunction(this.$parent.$parent.$parent, parentRef, this.pageConfig, groupData, realValue, opt, oldVal, this.group.formModel, this.tplRootRef)
        }
      }
    },
    ...mapActions(['setEditActivedInfo']),
    footerMethod({ group, columns, data }) {
      let footerData = []
      if (columns) {
        footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '合计'
            }
            return null
          })
        ]
      }
      if (this.gridFooterMethod && getObjType(this.gridFooterMethod) === 'function') {
        return this.gridFooterMethod({ group, columns, data })
      } else {
        return footerData
      }
    },
    cellMenuEvent({ column }) {
      const setVisibleByCode = (code, flag) => {
        if (this.menuConfig?.body?.options.length) {
          this.menuConfig.body.options[0].forEach((rs) => {
            if (rs.code === code) {
              rs.visible = flag
            }
          })
        }
      }
      if (column.editRender) {
        // 关闭右键菜单 向下填充
        setVisibleByCode('FILLDOWN_CELL', false)
      } else {
        setVisibleByCode('FILLDOWN_CELL', false)
      }
    },
    menuClickEvent(info) {
      const { $grid, column, row, menu } = info
      const { fullData } = $grid.getTableData()
      if (menu.code === 'FILLDOWN_CELL') {
        // 向下填充
        // 虚拟行index
        const rowIndex = $grid.getVMRowIndex(row)
        fullData.forEach((item, index) => {
          if (index > rowIndex) {
            // 当前选中的值
            const currentSelectVal = fullData[rowIndex][column.property]
            item[column.property] = currentSelectVal
          }
        })
      }
    },
    cellClickEvent(info) {
      const { row, rowIndex, column, columnIndex } = info
      // 非同一个表格置空
      if (this.cache_vuex_editActivedInfo?.column?.property !== column.property) {
        this.setEditActivedInfo({})
      }
      this.$emit('cell-click', { row, rowIndex, column, columnIndex })
    },
    editActivedEvent(info) {
      const { row, rowIndex, column, columnIndex } = info
      this.setEditActivedInfo(info)
      this.$emit('edit-actived', { row, rowIndex, column, columnIndex })
    },
    richEditorSureClick(content, row, column) {
      row[column.property] = content
      this.$root.$emit('syncRow', { content: content, property: column.property, groupCode: this.group.groupCode, id: row._X_ID })
    },
    //拖拽分组行
    groupRowDrop() {
      this.$nextTick(() => {
        let groupGrid = this.$refs[this.group.groupCode]
        this.groupSortable = Sortable.create(groupGrid.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
          handle: '.drag-btn',
          onEnd: ({ newIndex, oldIndex }) => {
            let { fullData } = groupGrid.getTableData()
            let tableData = [...fullData]
            let currRow = tableData.splice(oldIndex, 1)[0]
            tableData.splice(newIndex, 0, currRow)
            tableData = tableData.map((item, index) => {
              item.sortOrder = index + 1
              return item
            })
            groupGrid.loadData(tableData)
            groupGrid.syncData()
          }
        })
      })
    },
    // 操作列方法
    optColumnFuntion(opt, row, col, columns) {
      opt.click && opt.click(this, row, col, this.tplRootRef, { columns })
    },
    loadGridData() {
      let that = this
      this.$nextTick(() => {
        if (that.$refs[that.group.groupCode]) {
          that.$refs[that.group.groupCode].loadData(that.gridData)
        }
      })
    },
    // 获取列数据字典对应label
    getDictLabel(value, column, columns = []) {
      let txt = ''
      for (const { field, options = [] } of columns) {
        if (field === column.property) {
          let { label = '' } = options.find((sub) => sub.value == value) || {}
          txt = label
          break
        }
      }
      return txt || value
    },
    // 货币千分位
    currencyFormat(value, info, columns = []) {
      if (!value) {
        return ''
      }
      let extend = {}
      for (let item of columns) {
        if (info.property === item.field) {
          extend = item.extend || {}
          break
        }
      }

      console.log('extend :>> ', extend)

      let symbol = (extend && extend.symbol) || ''
      let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
      return currency(value, symbol, decimals)
    },
    gridBtnMixin(group, groupCode, btn) {
      if (btn.click && typeof btn.click === 'function') {
        let itemGrid = this.$refs[groupCode]
        btn.click(this, group, itemGrid)
      } else {
        if (btn.key === 'gridAdd') {
          this.addItemMixin(groupCode)
        } else if (btn.key === 'gridDelete') {
          this.deleteItemMixin(groupCode)
        } else if (btn.key === 'gridExport') {
          this.handleExport(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_files`, '文件'), group)
        }
      }
    },
    //附件上传
    handleUploadChange(info, btn, refName) {
      btn.callBack && btn.callBack(info, refName)
    },
    // 检查表格是否有选中
    checkedGridSelect(btn, refName, cb) {
      let selectData = null
      let that = this
      if (this.$refs[refName]) {
        if (this.$refs[refName][0]) {
          selectData = this.$refs[refName][0].getCheckboxRecords() || this.$refs[refName][0].getRadioRecord()
        }
      }
      if (selectData && selectData.length) {
        if (cb && typeof cb === 'function') {
          if (btn.key !== 'batchDownload') {
            cb(selectData, that).then((res) => {
              if (res) {
                btn.modalVisible = true
              }
            })
          } else {
            cb(selectData, that)
          }
        } else {
          this.modalVisible = true
        }
      } else {
        console.log(btn)
        if (btn.msgType === 'batchDownload') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRiTIKBIcW_10289077`, '请勾选需下载附件行！'))
        } else {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterChoose`, '请先选择'))
        }
      }
    },
    // 默认表行增加
    addItemMixin(refName) {
      if (refName) {
        let itemGrid = this.$refs[refName]
        let itemData = {}
        itemGrid.insert([itemData])
      }
    },
    // 默认表行删除
    deleteItemMixin(refName) {
      if (refName) {
        let itemGrid = this.$refs[refName]
        let checkboxRecords = itemGrid.getCheckboxRecords()
        if (!checkboxRecords.length) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
          return
        }
        itemGrid.removeCheckboxRow()
      }
    },
    // 导出
    handleExport(fileName, group) {
      this.gridLoading = true
      fileName = fileName || this.$srmI18n(`${this.$getLangAccount()}#i18n_title_exportFile`, '导出文件')
      if (this.requestData.export) {
        // 传多ref给外部处理
        let gridRef = this.$refs[group.groupCode]
        group.gridRef = gridRef
        let data = this.requestData.export.args ? this.requestData.export.args(group) : {}
        downFile(this.requestData.export.url, data)
          .then((data) => {
            if (!data) {
              this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
              return
            }
            if (typeof window.navigator.msSaveBlob !== 'undefined') {
              window.navigator.msSaveBlob(new Blob([data]), fileName + '.xlsx')
            } else {
              let url = window.URL.createObjectURL(new Blob([data]))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', fileName + '.xlsx')
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) //下载完成移除元素
              window.URL.revokeObjectURL(url) //释放掉blob对象
            }
          })
          .finally(() => {
            this.gridLoading = false
          })
      } else {
        this.$message.warn('请先配置导出的参数')
        this.gridLoading = false
      }
    },
    // 导入方法
    importMethod(data) {
      let name = 'get'
      if (data && data.method) {
        name = data.method.toLowerCase() === 'post' ? 'post' : 'get'
      }
      return name
    },
    // 导入的参数
    importArgs(data, group) {
      let args = {}
      if (data && data.args) {
        // 传多ref给外部处理
        let gridRef = this.$refs[group.groupCode]
        group.gridRef = gridRef
        args = data.args(group)
      }
      return args
    },
    /* 导入 */
    handleImport(info, group) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList)
      }
      if (info.file.status === 'done') {
        if (info.file.response.success) {
          if (info.file.response.code === 201) {
            let {
              message,
              result: { msg, fileUrl, fileName }
            } = info.file.response
            let href = this.$variateConfig['domainURL'] + fileUrl
            this.$warning({
              title: message,
              content: (
                <div>
                  <span>{msg}</span>
                  <br />
                  <span>
                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_detailContent`, '具体详情请')}{' '}
                    <a
                      href={href}
                      target='_blank'
                      download={fileName}
                    >
                      点击下载
                    </a>{' '}
                  </span>
                </div>
              )
            })
          } else {
            this.$message.success(info.file.response.message || `${info.file.name} 文件上传成功`)
            this.$emit('handleImportSuccess', { response: info.file.response, group: group, ref: this.$refs[group.groupCode] })
          }
        } else {
          this.$message.error(`${info.file.name} ${info.file.response.message}.`)
        }
      } else if (info.file.status === 'error') {
        this.$message.error(`文件上传失败: ${info.file.msg} `)
      }
    },
    // 多选触发事件
    checkboxChange({ checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event }) {
      // 用这个最笨的方法处理
      this.tplRootRef.checkboxChange && this.tplRootRef.checkboxChange({ checked, row, rowIndex, $rowIndex, column, columnIndex, $columnIndex, $event })
    }
  },
  beforeDestroy() {
    this.groupSortable && this.groupSortable.destroy()
  }
}
</script>
<style lang="less" scoped>
.drag-btn {
  cursor: move;
}
.btn-opt {
  margin-left: 6px;
  margin-right: 6px;
}
.summary-message {
  height: 14px;
  display: flex;
  align-items: center;
  margin: 10px 0;
}
.summary-message-content {
  flex-grow: 1;
  font-weight: bolder;
  .total-num {
    font-size: 16px;
    color: red;
  }
}
:deep(.vxe-body--column.required-col) {
  background-color: #f6ebe8;
  background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
}
:deep(.vxe-header--column.required-col) {
  background-color: #f6ebe8;
  background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
}
:deep(.field-color-box) {
  position: relative;
  font-weight: 700;
  .color-input {
    width: 80%;
  }
  .color-modal {
    position: absolute;
    width: 20%;
    padding: 0;
    border: 0;
    cursor: pointer;
  }
}
:deep(.color-value-box) {
  line-height: 32px;
  font-weight: 700;
}
</style>
