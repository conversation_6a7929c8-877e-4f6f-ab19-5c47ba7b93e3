<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <a-modal
    v-drag
      v-model="previewModal"
      title="预览"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
    <a-modal
    v-drag
      v-model="visible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_PeIL_39fb9595`, '签章定位')"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :label-col="layout.labelCol"
        :wrapper-col="layout.wrapperCol">
        <a-form-model-item label="关键字">
          <a-input
            v-model="form.keyword" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { SALEATTACHMENTDOWNLOADAPI } from '@/utils/const'
import { axios } from '@/utils/request'
import {REPORT_ADDRESS} from '@/utils/const'
export default {
    name: 'SaleEsignFlowEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            printRow: {},
            printVisible: false,
            rowIndex: -1,
            sealAeraIndex: -1,
            selectType: 'esignFlow',
            previewModal: false,
            previewContent: '',
            visible: false,
            form: {
                keyword: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'busType',
                                    dictCode: 'electronicSignatureBusType',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserAccountNumber`, '采购方账号'),
                                    fieldName: 'elsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserAccountName`, '采购方名称'),
                                    fieldName: 'purchaseName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: '合同编号',
                                    fieldName: 'busNumber',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessAssociationID`, '业务关联id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                                    fieldName: 'filesName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'),
                                    fieldName: 'filesId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkisSuccessfullyUploaded`, '文件是否上传成功'),
                                    fieldName: 'uploaded',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    dictCode: 'srmSendStatus',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSignatoryMaintainsTheState`, '签署人维护状态'),
                                    fieldName: 'signerVindicateStatus',
                                    dictCode: 'srmSignerVindicateStatus',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_autoArchiving`, '是否自动归档'),
                                    fieldName: 'autoArchiving',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheProcessIsAutomaticallyStarted`, '流程是否自动开启'),
                                    fieldName: 'autoInitiate',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationDateSigning`, '签署有效截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD',
                                    disabled: true
                                },
                                {
                                    fieldType: 'number',
                                    fieldLabel: '文件到期前多少时间提醒(小时)',
                                    fieldName: 'contractRemind',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType',
                                    disabled: true
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationdateDocument`, '文件有效截止时间'),
                                    fieldName: 'effectiveTime',
                                    dataFormat: 'YYYY-MM-DD',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    disabled: true
                                }
                            ]
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCPWL_36613b74`, '采方签署人'), groupCode: 'purchaseSignersList', type: 'grid', custom: {
                        ref: 'purchaseSignersList',
                        columns: [
                            { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                            { field: 'signWord', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'), width: 120 },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'enterpriseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 180 },
                            { field: 'loadingCompany', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), visible: false},
                            { field: 'loadingCompany_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), width: 120, visible: false},
                            { field: 'companyCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'), width: 120, visible: false},
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureCompany`, '签署公司'), width: 200 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureAccount`, '签署用户E签宝账号'), width: 120, visible: false},
                            { field: 'idType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170, visible: false },
                            { field: 'idType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170 },
                            { field: 'idNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'), width: 170 },
                            { field: 'orgLegalIdNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateNumber`, '法人证件号'), width: 170 },
                            { field: 'orgLegalName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_corporateName`, '法人名称'), width: 120 },
                            { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_companySignatureAccount`, '公司E签宝账号'), width: 120, visible: false},
                            { field: 'filesId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'), width: 120, visible: false},
                            { field: 'filesName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'), width: 120 },
                            { field: 'certificationStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), visible: false},
                            { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120},
                            // { field: 'autoSign', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isAutomaticSignature`, '是否自动签署'), visible: false},
                            // { field: 'autoSign_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isAutomaticSignature`, '是否自动签署'), width: 120  },
                            { field: 'signType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), visible: false},
                            { field: 'signType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), width: 120 }
                        ]
                    } },
                    { groupName: '签章文件', groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'saleAttachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                type: 'upload', businessType: 'esign',
                                attr: this.attrHandle,
                                callBack: this.uploadCallBack},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.previewEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFile}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: '下载', clickFn: this.downloadEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sMGhd_905e2e4b`, '保存并发送'), type: 'primary', click: this.saveEvent },
                    { title: '签署文件预览', type: 'primary', click: this.preview },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_by6FtppjfaHaxK4d`, '签署文件下载'), type: 'primary', click: this.download },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                edit: '/esign/elsEsign/edit',
                detail: '/esign/elsEsign/queryById',
                keyWordToAera: '/esign/elsEsign/keyWordToAera',
                downloadFile: '/contract/purchaseContractHead/download',
                upload: '/attachment/saleAttachment/upload',
                download: SALEATTACHMENTDOWNLOADAPI,
                viewEsignFile: '/esign/elsEsign/viewEsignFile'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.busNumber,
                actionRoutePath: '/srm/esign/EsignFlow,/srm/esign/sale/SaleEsignFlow'
            }
        },
        previewEvent () {
            const fileGrid = this.$refs.editPage.$refs.saleAttachments[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if (checkboxRecords.length > 1) {
                this.$message.warning('只能选择一条数据！')
                return
            }
            let preViewFile = checkboxRecords[0]
            this.$previewFile.open({params: preViewFile })
        },
        showUploadBtn (){
            if(this.currentEditRow.onlineSealed==='1'){
                return false
            }
            return true
        },
        showdeleteFileBtn (){
            if(this.currentEditRow.onlineSealed==='1'){
                return false
            }
            return true
        },
        // 删除方法
        deleteFile () {
            const fileGrid = this.$refs.editPage.$refs.saleAttachments[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        uploadCallBack (result) {
            const saleAttachments = this.$refs.editPage.getPageData().saleAttachments
            let itemGrid = this.$refs.editPage.$refs.saleAttachments[0]
            if(saleAttachments.length>0){
                const ids = saleAttachments.map(n => (n.id)).join(',')
                const params = {
                    ids
                }
                getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                    if (res.success) {
                        itemGrid.remove()
                        itemGrid.insertAt(result, -1)
                    }
                })
            }else{
                itemGrid.insertAt(result, -1)
            }
        },
        // getSignArea (row, column, $rowIndex){
        getSignArea (row){
            if(row.signType==='0'){
                this.$message.warning('当【签署类型】为‘不限’时，不需要设置签署区域')
                return
            }
            this.selectType = 'keyWord'
            this.sealAeraIndex = 0
            this.visible = true
        },
        handleOk () {
            if(!this.form.keyword){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIJxOLV_594d82ac`, '关键字不能为空'))
                return
            }
            const form = this.$refs.editPage.getPageData()
            form.saleSignersList[this.sealAeraIndex].signWord = this.form.keyword
            let param = {elsAccount: form.elsAccount, signWord: this.form.keyword, filesId: form.filesId}
            this.confirmLoading = true
            let columns = [
                { field: 'pageNo', title: '页码', width: 200 },
                { field: 'posx', title: '横轴(X)', width: 180 },
                { field: 'posy', title: '纵轴(Y)', width: 180 }
            ]
            this.$refs.fieldSelectModal.open(this.url.keyWordToAera, param, columns, 'single')
            // postAction(this.url.keyWordToAera, param).then(res => {
            //     this.confirmLoading = false
            //     if(res.success) {
            //         this.$message.success('定位成功')
            //         param.signArea = res.message
            //     }else {
            //         this.$message.warning(res.message)
            //     }
            // })
            this.visible = false
        },
        // getSeal (row, column, $rowIndex){
        getSeal (row){
            const form = this.$refs.editPage.getPageData()
            this.selectType = 'addSigner'
            this.rowIndex = 0
            let url = '/esign/elsSeals/list'
            let columns = [
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 200 },
                { field: 'sealId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeWW_39f03bbd`, '签章id'), width: 180 },
                { field: 'alias', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'), width: 180 }
            ]
            let params = {companyName: row.companyName, createdAccount: form.elsAccount}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        showAddPurchaseSignEvent (){
            if(this.currentEditRow.uploaded==='1'){
                return true
            }
            return false
        },
        showDeleteSaleSignEvent (){
            if(this.currentEditRow.uploaded==='1'){
                return true
            }
            return false
        },
        addPurchaseSignEvent () {
            this.selectType = 'saleEsign'
            const form = this.$refs.editPage.getPageData()
            if(form.uploaded!=='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeQIsLXVShILreMU_88640b6a`, '签章文件还未上传，无法定位盖章区域'))
                return
            }
            let url = '/esign/elsEsign/getSignerlist'
            let columns = [
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 200 },
                { field: 'enterpriseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 180 },
                { field: 'loadingCompany', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), width: 120 },
                { field: 'companyCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'), width: 120 },
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称'), width: 120 },
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                { field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureAccount`, '签署用户E签宝账号'), width: 120 },
                { field: 'idType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 120 },
                { field: 'idNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'), width: 120 },
                { field: 'orgLegalIdNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateNumber`, '法人证件号'), width: 120 },
                { field: 'orgLegalName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_corporateName`, '法人名称'), width: 120 },
                { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_companySignatureAccount`, '公司E签宝账号'), width: 120 },
                { field: 'certificationStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120, editRender: {name: '$select', options: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LLi_194b947`, '未认证'), value: '0'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ILi_1721e0f`, '已认证'), value: '1'}
                ], disabled: true} },
                { field: 'personType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'), width: 120 },
                { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                { field: 'signType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), width: 120, editRender: {name: '$select', options: [
                    {label: '不限', value: '0'},
                    {label: '单页签', value: '1'},
                    {label: '骑缝签', value: '2'}
                ], disabled: true} },
                { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                { field: 'autoSign', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isAutomaticSignature`, '是否自动签署'), width: 120, editRender: {name: '$select', options: [
                    {label: '否', value: '0'},
                    {label: '是', value: '1'}
                ], disabled: true} }
            ]
            let params = {id: form.relationId, signer: 'sale', createdAccount: form.elsAccount}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        fieldSelectOk (data) {
            if(this.selectType == 'saleEsign'){
                let arr = data.map(({ id, ...others }) => ({ relationId: id, signType: '1', ...others}))
                let itemGrid = this.$refs.editPage.$refs.saleSignersList[0]
                itemGrid.remove()
                let { fullData } = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.relationId
                })
                let insertData = arr.filter(item => {
                    return !regulations.includes(item.relationId)
                })
                itemGrid.insertAt(insertData)
            }else if(this.selectType === 'addSigner'){
                let ids = data.map(n => n.sealId).join(',')
                const form = this.$refs.editPage.getPageData()
                form.saleSignersList[this.rowIndex].sealIds = ids
            }else if(this.selectType === 'keyWord'){
                const { pageNo = '', posx = '', posy = '' } = data[0] || {}
                let result = `${pageNo}_${posx}_${posy}`
                const form = this.$refs.editPage.getPageData()
                let param = form.saleSignersList[this.sealAeraIndex]
                param.signArea = result
            }
        },
        deleteSaleSignEvent (){
            let itemGrid = this.$refs.editPage.$refs.saleSignersList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        selectedPrintTemplate (type) {
            if(this.printNumber) {
                const that = this
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.printNumber
                })
                let params = {
                    templateNumber: this.printNumber,
                    printId: template[0].printId,
                    printName: template[0].printName,
                    printType: template[0].printType,
                    param: template[0].param
                }
                that.printVisible = false
                that.submitLoading = false
                let rowItem = this.printRow
                this.printRow = {}
                let urlParam = ''
                if (params.param) {
                    let json = JSON.parse(params.param)
                    Object.keys(json).forEach((key, i) => {
                        urlParam += '&'+key+'='+rowItem[json[key]]
                    })
                }
                if (params.printType=='ureport') {
                    const token = this.$ls.get('Access-Token')
                    const url = REPORT_ADDRESS + '/els/report/ureport/'+type+'?_u=mysql:'+params.printName+'&token=' + token+ urlParam
                    window.open(url, '_blank')
                }
                if (params.printType=='jimu') {
                    const token = this.$ls.get('Access-Token')
                    const url = REPORT_ADDRESS + '/els/report/jmreport/view/'+params.printId+'?token=' + token+urlParam
                    //const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.title+'&token=' + token+'&id='+rowItem.id
                    window.open(url, '_blank')
                }
            }
        },
        openModal (row, type) {
            this.printRow = row
            this.queryPrintTemList(row.busAccount).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.id,
                                printId: item.printId,
                                printName: item.printName,
                                title: item.templateName,
                                printType: item.printType,
                                param: item.param
                            }
                        })
                        this.printNumber = ''
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length===1) {
                            this.printNumber = this.templateOpts[0].value
                            this.selectedPrintTemplate(type)
                        } else {
                            // 有多个模板先选择在新建
                            this.printVisible = true
                        }
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        queryPrintTemList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: 'orderSale'}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        preview () {
            let params= this.$refs.editPage.getPageData()
            if(params.busType == 'order'){
                this.$refs.editPage.confirmLoading = true
                getAction('/order/saleOrderHead/list', {orderNumber: params.busNumber}).then(res => {
                    if(res.success){
                        this.openModal(res.result.records[0], 'preview')
                        this.$refs.editPage.confirmLoading = false
                    }else{
                        this.$message.warning(res.message)
                        this.$refs.editPage.confirmLoading = false
                    }
                })
            } else if(params.busType == 'reconciliationConfirmation') {
                this.$refs.editPage.confirmLoading = true
                getAction('/reconciliation/saleReconciliationConfirmation/list', {preparedByNumber: params.busNumber}).then(res => {
                    if(res.success){
                        this.openModal(res.result.records[0], 'preview')
                        this.$refs.editPage.confirmLoading = false
                    }else{
                        this.$message.warning(res.message)
                        this.$refs.editPage.confirmLoading = false
                    }
                })
            } else {
                getAction('/contract/saleContractHead/getPreviewDataByRelationId', {id: params.relationId}).then((res) => {
                    if (res.success) {
                        this.previewModal = true
                        this.previewContent = res.result
                    }
                })
            }
        },
        download (){
            let params= this.$refs.editPage.getPageData()
            this.$refs.editPage.confirmLoading = true

            if(params.busType == 'order'){
                this.$refs.editPage.confirmLoading = true
                getAction('/order/saleOrderHead/list', {orderNumber: params.busNumber}).then(res => {
                    if(res.success){
                        this.openModal(res.result.records[0], 'pdf')
                        this.$refs.editPage.confirmLoading = false
                    }else{
                        this.$message.warning(res.message)
                        this.$refs.editPage.confirmLoading = false
                    }
                })
            }else {
                axios({
                    url: this.url.downloadFile,
                    responseType: 'blob',
                    params: {id: params.relationId}
                }).then(res => {
                    this.$refs.editPage.confirmLoading = false
                    let fieldName = params.busNumber+'_'+params.filesName+'.pdf'
                    const blob = new Blob([res])
                    const blobUrl = window.URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.style.display = 'none'
                    a.href = blobUrl
                    a.download = fieldName
                    a.click()
                })
            }


        },
        saveEvent () {
            const params = this.$refs.editPage.getPageData()
            //线上签署
            if(params.onlineSealed==='1'){
                if(params.saleSignersList.length<1){
                    this.$message.warning('供方签署人不能为空')
                    return
                }
                if(!params.saleSignersList[0].sealIds){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WexOLV_4a4b6180`, '印章不能为空'))
                    return
                }
                const signer = params.saleSignersList
                if(signer && signer.length>0 && signer[0].signType!=='0'&& !signer[0].signArea){
                    this.$message.warning('签署类型为(单页签署/骑缝签署)时需要设置签署区域')
                    return
                }
            }else{
                //线下签署，上传文件
                const files = params.saleAttachments
                if(!files || files.length<1){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQIxOLV_d540bb93`, '签署文件不能为空'))
                    return
                }
                //前述状态“已签署”
                params.signFileUploaded='1'
            }
            let url = this.url.edit
            //供方
            params.modifyPerson='1'
            //是否发送信息发布
            params.operateType = 'supplierEsignPublish'
            postAction(url, params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.goBack()
                }
            })

        }
    }
}
</script>