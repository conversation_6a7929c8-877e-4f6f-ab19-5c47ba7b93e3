<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      refresh
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <ItemImportExcel
      ref="itemImportExcel"
      @importCallBack="importCallBack"/>
  </div>
</template>

<script lang='jsx'>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {downFile, getAction, postAction} from '@/api/manage'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { ajaxFindDictItems } from '@api/api'
import { handlePromise } from '@/utils/util.js'

export default {
    name: 'PurchaseBiddingEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal,
        ItemImportExcel
    },
    data () {
        return {
            selectType: 'material',
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseBiddingItemList',
                        columns: [],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addBiddingItem, authorityCode: 'bidding#purchaseBiddingHead:add'},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteItemEvent, authorityCode: 'bidding#purchaseBiddingHead:delete'},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem},
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
                                authorityCode: 'bidding#purchaseBiddingHead:importExcel',
                                params: this.importParams,
                                click: this.importExcel
                            }
                        ]
                    } },
                    { 
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), 
                        groupCode: 'supplierInfo', 
                        type: 'grid', 
                        needDynamicsHeight: true,
                        custom: {
                        ref: 'biddingSupplierList',
                        columns: [
                            {
                                type: 'checkbox', width: 40
                            },
                            {
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                                field: 'toElsAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
                                field: 'supplierCode',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'),
                                field: 'supplierName',
                                width: 200
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addSupplierEvent, authorityCode: 'bidding#purchaseBiddingHead:add'},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteSupplierEvent, authorityCode: 'bidding#purchaseBiddingHead:delete'}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_evaluationExpert`, '评标专家'), groupCode: 'specialistInfo', type: 'grid', custom: {
                        ref: 'purchaseBiddingSpecialistList',
                        columns: [
                            {
                                type: 'checkbox', width: 40
                            },
                            {
                                type: 'seq', width: 50,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistName`, '专家姓名'),
                                field: 'name',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistAccount`, '专家账号'),
                                field: 'subAccount',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistType`, '专家类型'),
                                field: 'specialistClasses_dictText',
                                width: 150
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expertPhone`, '专家电话'),
                                field: 'mobileTelephone',
                                width: 150
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addSpecialistEvent, authorityCode: 'bidding#purchaseBiddingHead:add'},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.delSpecialistEvent, authorityCode: 'bidding#purchaseBiddingHead:delete'}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120, dictCode: 'srmFileType', fieldType: 'select', editRender: {name: '$select', options: []} },
                            { field: 'stageType',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'),
                                width: 120,
                                dictCode: 'biddingStageType',
                                editRender: {},
                                slots: {
                                    default: ({row}) => {
                                        let dictcode = 'biddingStageType'
                                        return [
                                            row['stageType'] ? this.getDictLabel(row['stageType'], dictcode) : ''
                                        ]
                                    },
                                    edit: ({row}) => {
                                        const form = this.$refs.editPage.getPageData()
                                        // 0 邀请竞价
                                        let dictcode = form.biddingType === '0' ? 'bidding2StageType' : 'biddingStageType'
                                        return [
                                            <m-select  configData={row} getPopupContainer={triggerNode => {
                                                return triggerNode.parentNode || document.body
                                            }}
                                            v-model={row.stageType}
                                            placeholder={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')}
                                            dict-code={dictcode} />
                                        ]
                                    }
                                }
                            },
                            { field: 'required', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120, cellRender: {name: '$switch', props: {openValue: '1', closeValue: '0'}} },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220, editRender: {name: '$input'} }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addFileDemand, authorityCode: 'bidding#purchaseBiddingHead:add'},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFileDemand, authorityCode: 'bidding#purchaseBiddingHead:delete'}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload',
                                dictCode: 'srmFileType', businessType: 'bidding',
                                attr: this.attrHandle,
                                callBack: this.uploadCallBack, authorityCode: 'bidding#purchaseBiddingHead:fileUpload'},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch, authorityCode: 'bidding#purchaseBiddingHead:batchDelete' }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent, authorityCode: 'bidding#purchaseBiddingHead:download' },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent, authorityCode: 'bidding#purchaseBiddingHead:preview' },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent, authorityCode: 'bidding#purchaseBiddingHead:attachmentDelete' }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent, authorityCode: 'bidding#purchaseBiddingHead:save' },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publishEvent, showCondition: this.showPublishConditionBtn, authorityCode: 'bidding#purchaseBiddingHead:publish'},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'), type: 'primary', click: this.submit, showCondition: this.showAuditConditionBtn, authorityCode: 'bidding#purchaseBiddingHead:submit'},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/bidding/purchaseBiddingHead/add',
                edit: '/bidding/purchaseBiddingHead/edit',
                detail: '/bidding/purchaseBiddingHead/queryById',
                public: '/bidding/purchaseBiddingHead/publish',
                isExistFrozenSource: 'supplier/supplierMaster/isExistFrozenSource',
                submit: '/a1bpmn/audit/api/submit',
                upload: '/attachment/purchaseAttachment/upload',
                materialBidding: '/inquiry/searchSource/materialBidding',
                import: '/els/base/excelByConfig/importExcel',
                downloadTemplate: '/base/excelByConfig/downloadTemplate',
                checkEnquirySameMaterial: '/bidding/purchaseBiddingHead/checkEnquirySameMaterial'
            },
            optionsMap: []
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount ? this.currentEditRow.templateAccount :  this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${account}/purchase_bidding_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        this.getDictData()
    },
    methods: {
        formatPageData(item) {
            console.log('請求接口後格式化页面數據', data)
            if (item.securityCost === 0 || Math.abs(item.securityCost) > 0) {
                item.securityCost = Number(item.securityCost).toFixed(2)
            }
            return item;
        },
        formatTableData(data) {
            console.log('請求接口後格式化列表數據', data)
            data = data.map((item) => {
                if (item.price === 0 || Math.abs(item.price) > 0) {
                    item.price = Number(item.price).toFixed(6)
                }
                if (item.netPrice === 0 || Math.abs(item.netPrice) > 0) {
                    item.netPrice = Number(item.netPrice).toFixed(6)
                }
                if (item.targetPrice === 0 || Math.abs(item.targetPrice) > 0) {
                    item.targetPrice = Number(item.targetPrice).toFixed(6)
                }

                
                if (item.quotaTaxAmount === 0 || Math.abs(item.quotaTaxAmount) > 0) {
                    item.quotaTaxAmount = Number(item.quotaTaxAmount).toFixed(2)
                }
                if (item.quotaNetAmount === 0 || Math.abs(item.quotaNetAmount) > 0) {
                    item.quotaNetAmount = Number(item.quotaNetAmount).toFixed(2)
                }
                
                return item
            })
            return data
        },
        init () {
            // queryDetail方法已经处理了id,可以直接调用
            if (this.currentEditRow) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id, (data) => {
                    this.editFormData = data
                    let attachmentList = this.editFormData.purchaseAttachmentList||[]
                    let purchaseRequestItemList = this.editFormData.purchaseBiddingItemList || []
                    let materialMap = purchaseRequestItemList.reduce((acc, obj) => {
                        acc[obj.itemNumber] = obj.materialNumber+'_'+ obj.materialName
                        return acc
                    }, {})
                    attachmentList.forEach(item => {
                        let number = item.itemNumber
                        if (number && materialMap[number] && !item.materialName) {
                            item.materialNumber = materialMap[number].split('_')[0]
                            item.materialName = materialMap[number].split('_')[1]
                        }
                        
                        item.quantityUnit = item.baseUnit;
                        if (!!item.requireQuantity) {
                            item.secondaryQuantity = item.requireQuantity / (item.conversionRate || 1)
                        } else {
                            item.secondaryQuantity = 0
                        }
                        item.secondaryQuantity = item.secondaryQuantity.toFixed(6)
                    })
                })
            }
        },

        getDictData () {
            let dictCodeArr = [
                {code: 'biddingStageType', dict: 'biddingStageType'}
            ]
            dictCodeArr.map(item => {
                let postData = {
                    busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: item.dict
                }
                ajaxFindDictItems(postData).then(res => {
                    if(res.success) {
                        let options = res.result.map(item2 => {
                            return {
                                value: item2.value,
                                label: item2.text,
                                title: item2.title
                            }
                        })
                        this.optionsMap[item.code] = options
                    }
                })
            })
        },
        // 通过value显示label
        getDictLabel (value, dict) {
            let currentValueArr = value.split(',') || []
            if (dict) {
                let dictItem = this.optionsMap[dict].filter((opt) => {
                    return currentValueArr.includes(opt.value)
                }).map(item => item.label)
                return dictItem.length ? dictItem.join('；'): currentValueArr[0]
            } else {
                return value
            }
        },
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.biddingNumber,
                actionRoutePath: '/srm/bidding/purchase/PurchaseBiddingHeadList,/srm/bidding/sale/SaleBiddingHeadList'
            }
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        downloadTemplate () {
            const form = this.$refs.editPage.getPageData()
            let params = {'id': form.id, 'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'handlerName': 'purchaseBiddingItemExcelRpcServiceImpl', 'roleCode': 'purchase', 'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息')}
            if (!params.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsMWFWKIKIr_de818fdb`, '请先保存数据，再下载模板！'))
                return
            } else {
                downFile(this.url.downloadTemplate, params).then((data) => {
                    if (!data) {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                        return
                    }
                    if (typeof window.navigator.msSaveBlob !== 'undefined') {
                        window.navigator.msSaveBlob(new Blob([data]), 'template.xlsx')
                    } else {
                        let url = window.URL.createObjectURL(new Blob([data]))
                        let link = document.createElement('a')
                        link.style.display = 'none'
                        link.href = url
                        link.setAttribute('download', this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_hucuNIr_20a4b03e`, '询价行导入模板') + '.xlsx')
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link) //下载完成移除元素
                        window.URL.revokeObjectURL(url) //释放掉blob对象
                    }
                }).finally(() => {
                    this.gridLoading = false
                })
            }
        },
        importParams () {
            const form = this.$refs.editPage.getPageData()
            return {'id': form.id, 'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'handlerName': 'purchaseBiddingItemExcelRpcServiceImpl', 'roleCode': 'purchase',
                'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息')}
        },
        importExcel () {
            const form = this.$refs.editPage.getPageData()
            let params = {'id': form.id, 'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'handlerName': 'purchaseBiddingItemExcelRpcServiceImpl', 'roleCode': 'purchase', 'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息')}
            this.$refs.itemImportExcel.open(params, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'), 'purchaseBiddingItemList')
        },
        importCallBack (result) {
            if (result.file.status === 'done') {
                let response = result.file.response
                debugger
                if (response.success) {
                    let itemGrid = this.$refs.editPage.$refs.purchaseBiddingItemList[0]
                    let insertData = response.result.dataList
                    this.pageConfig.itemColumns.forEach(item => {
                        if (item.defaultValue) {
                            insertData.forEach(insert => {
                                if (!insert[item.field]) {
                                    insert[item.field] = item.defaultValue
                                }
                            })
                        }
                    })
                    itemGrid.insertAt(insertData, -1)
                } else {
                    this.$message.warning(response.message)
                }
            }
        },
        addBiddingItem () {
            this.selectType = 'material'
            const form = this.$refs.editPage.getPageData()
            const { mustMaterialNumber = '1' } = form
            if(mustMaterialNumber == '1'){
                this.$refs.fieldSelectModal.requestMethod = 'get'
                let url = '/material/purchaseMaterialHead/list'
                let columns = [
                    {
                        field: 'cateCode',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
                        width: 150
                    },
                    {
                        field: 'cateName',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
                        width: 150
                    },
                    {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150},
                    {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150},
                    {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200},
                    {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200}
                ]
                this.$refs.fieldSelectModal.open(url, {blocDel: '0', freeze: '0'}, columns, 'multiple')
            }else{
                let itemGrid = this.$refs.editPage.$refs.purchaseBiddingItemList[0]
                let itemData = {}
                this.pageConfig.itemColumns.forEach(item => {
                    if(item.defaultValue) {
                        itemData[item.field] = item.defaultValue
                    }
                })
                itemGrid.insertAt([itemData], -1)
            }
        },
        showPublishConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if ((params.id)) {
                return true
            }else{
                return false
            }
        },
        deleteItemEvent () {
            let itemGrid = this.$refs.editPage.$refs.purchaseBiddingItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        fieldSelectOk (data) {
            if(this.selectType == 'material'){
                let itemGrid = this.$refs.editPage.$refs.purchaseBiddingItemList[0]
                let { fullData } = itemGrid.getTableData()
                let materialList = fullData.map(item => {
                    return item.materialNumber
                })
                //过滤已有数据
                let insertData = data//data.filter(item => {return !materialList.includes(item.materialNumber)})
                this.pageConfig.itemColumns.forEach(item => {
                    if(item.defaultValue) {
                        insertData.forEach(insert => {
                            if(!insert[item.field]){
                                insert[item.field] = item.defaultValue
                            }
                        })
                    }
                })
                insertData.forEach(insert => {
                    insert['materialId'] = insert['id']
                    insert.quantityUnit = insert.baseUnit; // 计量单位
                    if(!!insert.requireQuantity) {
                        insert.secondaryQuantity = insert.requireQuantity / (insert.conversionRate || 1)
                    }else{
                        insert.secondaryQuantity = 0
                    }
                    insert.secondaryQuantity = insert.secondaryQuantity.toFixed(6)
                })
                let param = {
                    'purOrgCode': this.$refs.editPage.getPageData().purchaseOrg,
                    'materialDataVos': data
                }
                postAction(this.url.materialBidding, param).then(res => {
                    if(res.success){
                        itemGrid.insertAt(insertData, -1)
                    } else {
                        this.$confirm({
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLSu_38d85f7b`, '确认添加'),
                            content: res.message,
                            onOk: function () {
                                itemGrid.insertAt(insertData, -1)
                            }
                        })
                    }
                })
                //itemGrid.insertAt(insertData, -1)
            }else if(this.selectType == 'supplier'){
                let supplierGrid = this.$refs.editPage.$refs.biddingSupplierList[0]
                let { fullData } = supplierGrid.getTableData()
                let supplierList = fullData.map(item => {
                    return item.toElsAccount
                })
                // 过滤已有数据
                let insertData = data.filter(item => {
                    return !supplierList.includes(item.toElsAccount)
                })
                insertData = insertData.map(item => {
                    return {
                        toElsAccount: item.toElsAccount,
                        supplierCode: item.supplierCode,
                        supplierName: item.supplierName,
                        supplierStatus_dictText: item.supplierStatus_dictText
                    }
                })
                supplierGrid.insertAt(insertData, -1)
            }else if(this.selectType == 'specialist'){
                let specialistGrid = this.$refs.editPage.$refs.purchaseBiddingSpecialistList[0]
                let { fullData } = specialistGrid.getTableData()
                let specialistList = fullData.map(item => {
                    return item.subAccount
                })
                // 过滤已有数据
                let insertData = data.filter(item => {
                    return !specialistList.includes(item.subAccount)
                })
                insertData = insertData.map(item => {
                    return {
                        subAccount: item.subAccount,
                        name: item.name,
                        mobileTelephone: item.mobileTelephone,
                        specialistClasses_dictText: item.specialistClasses_dictText,
                        specialistClasses: item.specialistClasses
                    }
                })
                specialistGrid.insertAt(insertData, -1)
            }
        },
        addSupplierEvent () {
            this.selectType = 'supplier'
            this.$refs.fieldSelectModal.requestMethod = 'post'
            let url = '/supplier/supplierMaster/list'
            let columns = [
                {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150},
                {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'), width: 200},
                {field: 'supplierStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'), width: 200},
                {field: 'supplierClassify', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'), width: 200}
            ]
            // 获取供应商范围参数
            const form = this.$refs.editPage.getPageData()
            if (form.mustMaterialNumber == '0') form.accessCategoryFilter = '0'
            if (form.mustMaterialNumber == '1' && form.accessCategoryFilter == '1' && (!form.purchaseOrg || form.purchaseOrg == '' || form.purchaseOrg == null)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectPurchaseOrgFirst`, '请先选择采购组织'))
                return
            }
            // 供应商冻结功能检查
            if (form.purchaseBiddingItemList && form.purchaseBiddingItemList.length>0) {
                // 设置供应商信息和询价行信息
                const array = new Array()
                const cateCodeArray = new Array()
                form.purchaseBiddingItemList.forEach( a =>{
                    let item = a.factory + ':' + a.cateCode
                    if (!array.includes(item)) {
                        array.push(item)
                    }
                    const cateCode = a.cateCode == null || a.cateCode == '' ? 'all' : a.cateCode
                    if (cateCodeArray.indexOf(cateCode) == -1) cateCodeArray.push(cateCode)
                })
                form.purchaseOrgItemList = array.join(',')
                form.accessCategoryList = cateCodeArray.join(',')
            }else {
                form.purchaseOrgItemList = ''
                form.accessCategoryList = ''
            }
            const { supplierScope = '', purchaseOrgItemList = '', accessCategoryFilter = '0',  accessCategoryList = '' } = form
            this.$refs.fieldSelectModal.open(url, {
                supplierStatus: supplierScope,
                frozenFunctionValue: '2',
                purchaseOrgItemList: purchaseOrgItemList,
                accessCategoryFilter: accessCategoryFilter,
                purchaseOrganization: form.purchaseOrg,
                accessCategoryList: accessCategoryList
            }, columns, 'multiple')
        },
        deleteSupplierEvent () {
            let supplierGrid = this.$refs.editPage.$refs.biddingSupplierList[0]
            let checkboxRecords = supplierGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            supplierGrid.removeCheckboxRow()
        },
        addSpecialistEvent (){
            this.selectType = 'specialist'
            let url = '/specialist/specialistInfo/list'
            let columns = [
                {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistName`, '专家姓名'), width: 150},
                {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistAccount`, '专家账号'), width: 150},
                {field: 'specialistClasses_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_specialistType`, '专家类型'), width: 200},
                {field: 'mobileTelephone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expertPhone`, '专家电话'), width: 200}
            ]
            // 获取供应商范围参数
            //const form = this.$refs.editPage.getPageData()
            //const { supplierScope = '' } = form
            this.$refs.fieldSelectModal.requestMethod = 'get'
            this.$refs.fieldSelectModal.open(url, { }, columns, 'multiple')
        },
        delSpecialistEvent () {
            let specialistGrid = this.$refs.editPage.$refs.purchaseBiddingSpecialistList[0]
            let checkboxRecords = specialistGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            specialistGrid.removeCheckboxRow()
        },
        addFileDemand () {
            let demandGrid = this.$refs.editPage.$refs.purchaseAttachmentDemandList[0]
            demandGrid.insertAt({ required: '0' }, -1)
        },
        deleteFileDemand () {
            let demandGrid = this.$refs.editPage.$refs.purchaseAttachmentDemandList[0]
            let checkboxRecords = demandGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            demandGrid.removeCheckboxRow()
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            let pageData = this.$refs.editPage.getPageData()
            let purchaseRequestItemList = pageData.purchaseBiddingItemList || []
            let materialMap = purchaseRequestItemList.reduce((acc, obj) => {
                acc[obj.itemNumber] = obj.materialNumber+'_'+ obj.materialName
                return acc
            }, {})
            result.forEach(item => {
                let number = item.itemNumber
                if (number && materialMap[number] && !item.materialName) {
                    item.materialNumber = materialMap[number].split('_')[0]
                    item.materialName = materialMap[number].split('_')[1]
                }
            })
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            //如果删除的数据有和登录人账号不一致的
            if('purchaseRequest' !== row.businessType && (user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount)){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
                return
            }

            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        // 批量删除
        deleteBatch () {
            let arr = []
            let delArr = []
            //大B
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if(checkboxRecords && checkboxRecords.length>0){
                checkboxRecords.forEach(row=> {
                    //当前登录人等账号于查询的账号 大B
                    if (user === row.uploadElsAccount) {
                        if( subAccount === row.uploadSubAccount || 'purchaseRequest' === row.businessType){
                            delArr.push(row)
                        }else{
                            arr.push(row.fileName)
                        }
                    }else{
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if(arr && arr.length>0){
                    let str = arr.join(',')
                    this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') +str+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        saveEvent () {
            this.$refs.editPage.postData()
        },
        publishEvent () {
            const form = this.$refs.editPage.getPageData()
            if(form.publishAudit == '1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitForApprovalTips`, '不可直接发布，请提交审批！'))
                return
            }
            let rows = this.$refs.editPage.getPageData().purchaseBiddingItemList || []
            // 如果基本信息中 供应商税率 0 不可以修改， 发布 招标行信息税码不能为空
            let taxCodeArr = (form.supplierTaxRate && form.supplierTaxRate == '0' && rows.find(rs => rs.taxCode)) ? rows.map(rs => rs.taxCode || '') : null
            if (taxCodeArr && taxCodeArr.includes('')) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierTaxRateTips`, '供应商税率不能为空'))
                return
            }

            let supperSize1 = this.$refs.editPage.$refs.biddingSupplierList[0].getTableData().tableData.length
            // 参与数量验证
            if (form.biddingType == '0' && form.participateQuantity && form.participateQuantity>supperSize1 ){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cantBeLessOfParticipants`, '邀请供应商数量不能小于参与数量！'))
                return
            }

            //预算金额校验
            // if(Number(form.budgetAmount)<=0){
            //     this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_UdHfTPfUQ_1f573b0f`, '预算金额需要大于零'))
            //     return
            // }

            // 供应商冻结功能检查
            if ((form.purchaseBiddingItemList && form.purchaseBiddingItemList.length>0) && (form.biddingSupplierList && form.biddingSupplierList.length>0)) {
                // 设置供应商信息和询价行信息
                form['purchaseOrgItemList'] = form.purchaseBiddingItemList
                form['supplierItemList'] = form.biddingSupplierList
                postAction(this.url.isExistFrozenSource, form).then(rest =>  {
                    if (rest.success) {
                        // 发布
                        let promise = this.$refs.editPage.setPromise()
                        Promise.all(handlePromise(promise)).then(result => {
                            let flag = false
                            for (let i = 0; i < result.length; i++) {
                                if (result[i].status === 'success') {
                                    flag = true
                                } else {
                                    this.$refs.editPage.currentStep = i
                                    return
                                }
                            }
                            if (flag) {
                                this.$refs.editPage.postData('save', () => {
                                    // 发布
                                    this.$refs.editPage.handleSend()
                                })
                            }
                        }).catch(err => {
                            console.error(err)
                        })
                        
                    } else {
                        this.$message.warning(rest.message)
                    }
                })
            } else {
                let promise = this.$refs.editPage.setPromise()
                Promise.all(handlePromise(promise)).then(result => {
                    let flag = false
                    for (let i = 0; i < result.length; i++) {
                        if (result[i].status === 'success') {
                            flag = true
                        } else {
                            this.$refs.editPage.currentStep = i
                            return
                        }
                    }
                    if (flag) {
                        this.$refs.editPage.postData('save', () => {
                            // 发布
                            this.$refs.editPage.handleSend()
                        })
                    }
                }).catch(err => {
                    console.error(err)
                })
                
            }
        },
        showAuditConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let auditStatus = params.auditStatus
            let audit = params.publishAudit
            if((audit=='1')&&(!auditStatus||auditStatus=='0'||auditStatus=='3')){
                return true
            }else{
                return false
            }
        },
        async submit (){
            const form = this.$refs.editPage.getPageData()
            if ((!form.id)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }
            if(form.publishAudit != '1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noNeedSubmitApproval`, '无需提交审批！'))
                return
            }

            //预算金额校验
            // if(Number(form.budgetAmount)<=0){
            //     this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_UdHfTPfUQ_1f573b0f`, '预算金额需要大于零'))
            //     return
            // }

            const _this = this
            const fn = (url, param, vm) => {
                console.log('vm :>> ', vm) // 编辑模板组件实例
                _this.$refs.editPage.confirmLoading = true
                postAction(url, param ).then(res =>  {
                    const type = res.success ? 'success' : 'error'
                    _this.$message[type](res.message)
                    if(res.success){
                        //_this.goBack()
                        this.$parent.submitCallBack(form)
                    }
                }).finally(() => {
                    _this.$refs.editPage.confirmLoading = false
                })
            }

            let auditSubject = await _this.$FlowUtil.convertFlowTitle(form,
                `招标单发布审批，单号：${form.biddingNumber} ${form.projectName||''}`
            )

            const param = {
                businessId: form.id,
                businessType: 'publishBidding',
                auditSubject: auditSubject,
                params: JSON.stringify(form)
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmSubmitApprovalTips`, '是否确认提交审批'),
                onOk () {
                    // 供应商冻结功能检查
                    if ((form.purchaseBiddingItemList && form.purchaseBiddingItemList.length>0) && (form.biddingSupplierList && form.biddingSupplierList.length>0)) {
                        // 设置供应商信息和询价行信息
                        form['purchaseOrgItemList'] = form.purchaseBiddingItemList
                        form['supplierItemList'] = form.biddingSupplierList
                        postAction(_this.url.isExistFrozenSource, form).then(rest =>  {
                            if (rest.success) {
                                postAction(_this.url.checkEnquirySameMaterial, form.purchaseBiddingItemList).then(rest =>  {
                                    if (rest.success) {
                                        // 提交审批
                                        _this.$refs.editPage.handValidate(_this.url.submit, param, fn)
                                    }else {
                                        _this.$message.warning(rest.message)
                                    }
                                })
                            } else {
                                _this.$message.warning(rest.message)
                            }
                        })
                    } else {
                        postAction(_this.url.checkEnquirySameMaterial, form.purchaseBiddingItemList).then(rest =>  {
                            if (rest.success) {
                                // 提交审批
                                _this.$refs.editPage.handValidate(_this.url.submit, param, fn)
                            }else {
                                _this.$message.warning(rest.message)
                            }
                        })
                    }
                }
            })
        }
    }
}
</script>
