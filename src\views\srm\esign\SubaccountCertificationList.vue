<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SubaccountCertificationEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SubaccountCertificationDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <SubaccountCertificationAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SubaccountCertificationAdd from './modules/SubaccountCertificationAdd'
import SubaccountCertificationEdit from './modules/SubaccountCertificationEdit'
import SubaccountCertificationDetail from './modules/SubaccountCertificationDetail'
import { postAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        SubaccountCertificationEdit,
        SubaccountCertificationDetail,
        SubaccountCertificationAdd
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                businessType: 'esignPersonCertification',
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', showCondition: this.showAddBtn, authorityCode: 'esign#subaccountCertification:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                    // ,
                    // {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, clickFn: this.showHelpText},
                    // {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_userName`, '用户名')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esign#subaccountCertification:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esign#subaccountCertification:edit'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), clickFn: this.handleCertification, allow: this.allowCertification, authorityCode: 'esign#subaccountCertification:submitCertification'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLizE_25c88b41`, '刷新认证状态'), clickFn: this.handleRefreshStatus, allow: this.allowRefreshStatus, authorityCode: 'esign#subaccountCertification:refreshStatus'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OClb_472d3b9a`, '设置静默授权'), clickFn: this.silentAuth, allow: this.allowSilentAuthStatus, authorityCode: 'esign#subaccountCertification:setAuth'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'esign#subaccountCertification:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/esign/elsSubaccountCertificationInfo/add',
                list: '/esign/elsSubaccountCertificationInfo/list',
                delete: '/esign/elsSubaccountCertificationInfo/delete',
                columns: 'subaccountCertificationInfoList',
                auth: '/esign/elsSubaccountCertificationInfo/submintCertification',
                refresh: '/esign/elsSubaccountCertificationInfo/getCertificationInfo',
                silentAuth: '/esign/elsSubaccountCertificationInfo/silentAuth'
            }
        }
    },
    methods: {
        //已认证不能被编辑
        allowEdit (row){
            if(row.createdAccount){
                return true
            }
            if(row.certificationStatus==='1'){
                return true
            }
            return false
        },
        allowSilentAuthStatus (row) {
            if(row.silentAuthStatus =='1'){
                return true
            }
            let longLink = row.longLink
            if(!longLink || !longLink.length>0){
                return true
            }
            return false
        },
        silentAuth (row) {
            let contentText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLOClb_a80d2827`, '是否确认静默授权?')
            let that = this
            this.$confirm({
                title: that.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLlb_38d73031`, '确认授权'),
                content: contentText,
                onOk: function () {
                    postAction(that.url.silentAuth, row).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                        }else {
                            that.$message.warning(res.message)
                        }
                        // 刷新列表
                        that.$refs.listPage.loadData()
                    })
                }
            })
        },
        allowCertification (row){
            if(row.createdAccount){
                return true
            }
            if(row.certificationStatus==='1'){
                return true
            }
            return false
        },
        allowRefreshStatus (row) {
            let certificationStatus = row.certificationStatus
            let longLink = row.longLink
            if (certificationStatus && certificationStatus==='0' && longLink && longLink.length>0) {
                return false
            }
            return true
        },
        //已经创建e签宝的账号的不能删除
        allowDelete (row){
            if(row.certificationStatus==='1'){
                return true
            }
            return false
        },
        handleAdd () {
            this.showAddPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        handleCertification (row) {
            let longLink = row.longLink
            let contentText = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLDJLiW_5f843f01`, '是否确认提交认证?')
            if (longLink && longLink.length>0) {
                contentText = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_rVHIDJRLiWKmDJMAbujWKQtTDJW_96191780`, '该信息已提交过认证，再次提交会产生费用，是否继续提交？')
            }
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationSystems`, '确认认证'),
                content: contentText,
                onOk: function () {
                    postAction(that.url.auth, row).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.submitCallBack (row)
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        handleRefreshStatus (row) {
            postAction(this.url.refresh, {id: row.id}).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                }else {
                    this.$message.warning(res.message)
                }
                // 刷新列表
                this.$refs.listPage.loadData()
            })
        }
    }
}
</script>