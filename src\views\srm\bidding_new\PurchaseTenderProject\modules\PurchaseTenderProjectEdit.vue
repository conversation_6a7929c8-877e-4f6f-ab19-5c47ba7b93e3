<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handlePublishBeforeRemoteConfigData="handlePublishBeforeRemoteConfigData"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"

        v-on="businessHandler"
      >
      </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        @ok="fieldSelectOk"
        isEmit
      />
      <ItemImportExcel
        ref="itemImportExcel"
        @importCallBack="importCallBack"/>
      <a-modal
        v-drag    
        v-model="showNode"
        :footer="null"
        width="1200px"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_QLIrUB_58cb0bac`, '流程模板预览')" >
        <IotStep
          :stepList="periodTypeInfoArray"
          :allNodeMap="allNodeMap">
        </IotStep>
      </a-modal>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import IotStep from '../../TenderProcessModelHead/modules/components/IotStep'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
import { Base64 } from 'js-base64'

import {
    BUTTON_PUBLISH,
    BUTTON_SUBMIT
} from '@/utils/constant.js'

export default {
    name: 'PurchaseTenderProjectHead',
    components: {
        BusinessLayout,
        fieldSelectModal,
        IotStep,
        ItemImportExcel
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            businessRefName: 'businessRefName',
            submit: '/a1bpmn/audit/api/submit',
            params: {},
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            showTenderProcess: false,
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                detail: { url: '/tender/purchaseTenderProjectHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                subpackageInfoVOList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'),
                        key: 'gridDelete',
                        click: this.copyRow
                    }
                ],
                purchaseAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            disabledItemNumber: true,
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'biddingPlatform', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: this.currentEditRow.id, // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ],
                projectItemList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                        attrs: {
                            type: 'primary'
                        },
                        click: this.addBiddingItem, authorityCode: 'tender#purchaseTenderProjectHead:add'},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteItemEvent, authorityCode: 'tender#purchaseTenderProjectHead:delete'},
                    // {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                    //     key: 'fillDown',
                    //     type: 'tool-fill',
                    //     beforeCheckedCallBack: this.fillDownGridItem
                    // },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
                        authorityCode: 'tender#purchaseTenderProjectHead:importExcel',
                        params: this.importParams,
                        click: this.importExcel
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/purchaseTenderProjectHead/edit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save',
                    click: this.handleSave,
                    showMessage: true
                },
                {
                    ...BUTTON_PUBLISH,
                    args: {
                        url: '/tender/purchaseTenderProjectHead/publish'
                    },
                    show: false
                },
                {
                    ...BUTTON_SUBMIT,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/a1bpmn/audit/api/submit'
                    },
                    handleBefore: this.handleBeforeFooterSubmit,
                    click: this.handleSubmit
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            allNodeMap: {},
            periodTypeInfoArray: {},
            showNode: false,
            url: {
                save: '/tender/purchaseTenderProjectHead/edit',
                publish: '/tender/purchaseTenderProjectHead/publish',
                submit: '/a1bpmn/audit/api/submit',
                queryByGruop: '/tender/tenderProcessModelHead/queryNodeByGruop'
                // uploadAction: '/a1bpmn/audit/api/submit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_biddingPlatform_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        activeRowMethod (that, row, rowIndex, column, columnIndex) {
            console.log(that, row, rowIndex, column, columnIndex)
            // 有物料编码且物料名称的行，不允许再编辑物料名称
            if(column.field == 'materialName' && row.materialName && row.materialNumber){
                // 行物料名称为空时才可编辑
                return false
            }else{
                return true
            }
        },
        handleBeforeRemoteConfigData (remoteData) {
            console.log('remoteData', remoteData)
            remoteData.groups.forEach(item=>{
                if(item.groupCode == 'projectItemList'){
                    item.extend={
                        editConfig: {
                            trigger: 'click', mode: 'cell', activeMethod: this.activeRowMethod
                        }
                    }
                }
                
            })
            remoteData.itemColumns.forEach(item2=>{
                if(item2.field == 'materialName'){
                    item2.editRender= {name: '$input'}
                    item2.required = '1'
                }
            })
        },
        // 拿到项目立项弹窗选中的立项id
        async __bindFunction (data, pageConfig){
            let approvalId = data[0].id
            let url = '/tender/tenderProjectApprovalHead/queryById'
            await getAction (url, {id: approvalId}).then(res =>{
                if(res.success){
                    console.log('@@', res, res.result.approvalItemList, pageConfig, this.$refs.businessRefName)
                    pageConfig.groups.forEach(item =>{
                        if(item.groupCode == 'projectItemList'){
                            item.loadData=res.result.approvalItemList
                            item.loadData.forEach(item2=>{
                                if(item2.sourceType == 'new'){
                                    item2.sourceType = 'approval' 
                                }
                            })
                        }
                    })
                    this.$refs.businessRefName.$refs.projectItemListgrid[0].loadData=res.result.approvalItemList
                    console.log('pageConfig', pageConfig)
                    // this.$refs.businessRefName.$refs.projectItemListgrid[0] = res.result.approvalItemList
                }
            })
            console.log(data[0])
        },
        importCallBack (result) {
            if (result.file.status === 'done') {
                let response = result.file.response
                if (response.success) {
                    let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
                    let insertData = response.result.dataList
                    let pageConfig = itemGrid.pageConfig.groups[1]
                    pageConfig.columns.forEach(item => {
                        if (item.defaultValue) {
                            insertData.forEach(insert => {
                                if (!insert[item.field]) {
                                    insert[item.field] = item.defaultValue
                                }
                            })
                        }
                    })
                    itemGrid.$refs.projectItemList.insertAt(insertData, -1)
                } else {
                    this.$message.warning(response.message)
                }
            }
        },
        addBiddingItem () {
            this.selectType = 'material'
            console.log(this.$refs.businessRefName.$refs.projectItemListgrid[0])
            const form = this.$refs.businessRefName.$refs.projectItemListgrid[0].gridData
            const { mustMaterialNumber = '1' } = form
            if(mustMaterialNumber == '1'){
                let url = '/material/purchaseMaterialHead/list'
                let columns = [
                    {
                        field: 'cateCode',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
                        width: 150
                    },
                    {
                        field: 'cateName',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
                        width: 150
                    },
                    {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150},
                    {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150},
                    {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200},
                    {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200}
                ]
                this.$refs.fieldSelectModal.open(url, {blocDel: '0', freeze: '0'}, columns, 'multiple')
            }else{
                console.log('进入了else')
                let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
                let itemData = {}
                this.pageConfig.itemColumns.forEach(item => {
                    if(item.defaultValue) {
                        itemData[item.field] = item.defaultValue
                    }
                })
                itemGrid.insertAt([itemData], -1)
            }
        },
        fieldSelectOk (data) {
            console.log('jinlaile')
            let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
            console.log(this.$refs.businessRefName)
            let {tableData} = itemGrid.$refs.projectItemList.getTableData()
            let materialList = tableData.map(item => {
                return item.materialId
            })
            //过滤已有数据
            let insertData = data.filter(item => {
                if(materialList.includes(item.id)){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSVBxVSL_321b5369`, '请勿重复补充物料！'))
                    return false
                }
                return true
                // return !materialList.includes(item.materialNumber)
            })
            itemGrid.pageConfig.groups[1].columns.forEach(item => {
                if(item.defaultValue) {
                    insertData.forEach(insert => {
                        if(!insert[item.field]){
                            insert[item.field] = item.defaultValue
                        }
                    })
                }
            })
            insertData.forEach(insert => {
                insert['materialId'] = insert['id']
                // insert.sourceType = this.currentEditRow.sourceType == 'new' ? 'approval' : 'new'
                
                insert.sourceType = 'new'
                
                delete insert.id

                insert.quantityUnit = insert.baseUnit
                if (!!insert.requireQuantity) {
                    insert.secondaryQuantity = insert.requireQuantity / (insert.conversionRate || 1)
                } else {
                    insert.secondaryQuantity = 0
                }
                insert.secondaryQuantity = insert.secondaryQuantity.toFixed(6)
            })
            // let param = {
            //     'purOrgCode': this.$refs.businessRefName.$refs.projectItemListgrid[0].gridData.purchaseOrg,
            //     'materialDataVos': data
            // }
            // postAction(this.url.materialBidding, param).then(res => {
            //     if(res.success){
            //         itemGrid.insertAt(insertData, -1)
            //     } else {
            //         this.$confirm({
            //             title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLSu_38d85f7b`, '确认添加'),
            //             content: res.message,
            //             onOk: function () {
            //                 itemGrid.insertAt(insertData, -1)
            //             }
            //         })
            //     }
            // })
            itemGrid.$refs.projectItemList.insertAt(insertData, -1)
            console.log()

        },
        deleteItemEvent () {
            let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
            let checkboxRecords = itemGrid.$refs.projectItemList.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            checkboxRecords.forEach(item=>{
                console.log(item)

                if(item.sourceType == 'request' || item.sourceType == 'approval'){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xiTQG_e9fdf359`, '不允许删除！'))
                }else{
                    itemGrid.$refs.projectItemList.remove(item)
                }
            })
            itemGrid.$refs.projectItemList.removeCheckboxRow()
        },
        importParams () {

            const form = this.$refs.businessRefName.$refs.projectItemListgrid[0]
            return {'id': this.currentEditRow.id, 'templateAccount': form.currentEditRow.templateAccount, 'templateNumber': form.currentEditRow.templateNumber, 'templateVersion': form.currentEditRow.templateVersion,
                'handlerName': 'purchaseProjectItemExcelRpcServiceImpl', 'roleCode': 'purchase',
                'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息')}
        },
        importExcel () {
            if(!this.currentEditRow.id){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
                return false
            }
            const form = this.$refs.businessRefName.$refs.projectItemListgrid[0]
            let params = {'id': this.currentEditRow.id, 'templateAccount': form.currentEditRow.templateAccount, 'templateNumber': form.currentEditRow.templateNumber, 'templateVersion': form.currentEditRow.templateVersion,
                'handlerName': 'purchaseProjectItemExcelRpcServiceImpl', 'roleCode': 'purchase', 'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息')}
            this.$refs.itemImportExcel.open(params, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'), 'approvalItemListgrid')
        },
        attrHandle (){
            return {
                sourceNumber: this.currentEditRow.tenderProjectNumber|| this.currentEditRow.id || '',
                actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'
            }
        },
        handlePublishBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                {
                                    key: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    click: this.preViewEvent
                                }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'subpackageInfoVOList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: {
                            default: ((row) => {
                                return [
                                    <a-button type="link" onClick={() => {this.handleShowNode(row)}}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IrUB_31d5dbc2`, '模板预览')}</a-button>
                                ]
                            })
                        },
                        showOptColumn: true
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }
                ]
            }
        },
        async handleAfterDealSource (pageConfig, resultData) {
            pageConfig.groups[0].formModel.elsRealname=this.$ls.get(USER_COMPANYSET).companyName
            pageConfig.groups[0].formModel.purchaseEnterpriseName=this.$ls.get(USER_COMPANYSET).companyName
        },
        // showPublish ({ pageConfig }){
        //     console.log('pageconfig', pageConfig)
        //     if(!pageConfig.groups[0].formModel.id){
        //         return false
        //     }
        //     return true
        // },
        businessGridAdd ({ pageConfig, groupCode }) {
            let useCa = pageConfig.groups[0].formModel.useCa || '0'
            let openBidEncrypt = pageConfig.groups[0].formModel.openBidEncrypt || '0'
            // const { allData = {} } = this.getBusinessExtendData(this.businessRefName)
            // let {tenderType, checkType, processType, signUp, bidOpenTpye, evaluationType} = allData.info
            let itemGrid = this.getItemGridRef(groupCode)
            itemGrid.insertAt([{useCa, openBidEncrypt}], -1)
        },
        copyRow (data) {
            let itemGrid = this.getItemGridRef(data.groupCode)
            let rows = itemGrid.getCheckboxRecords()
            if (rows.length > 0) {
                let arr = rows.map(item => {
                    let { subpackageNumber, id, _X_ID, ...others} = item
                    return {
                        ...others
                    }
                })
                itemGrid.insertAt(arr, -1)
            } else {
                return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRiIc_dc3c48ce`, '请勾选一行'))
            }

        },
        getNodeGruop (allData) {
            this.confirmLoading = true
            return postAction(this.url.queryByGruop, allData).then(res => {
                if (res.success) {
                    this.nodeData = res.result
                    return res.result
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleBeforeFooterSubmit (args) {
            const { allData = {} } = args || {}
            let pageData = allData
            delete pageData.purchaseAttachmentList
            return new Promise((resolve, reject) => {
                let formatData = {
                    businessId: allData.id,
                    businessType: 'biddingPlatform',
                    auditSubject: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_YBdIUzWtyW_d542fae5`, '招标项目,项目编号:')  + `${allData.tenderProjectNumber}`+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dIRL_d4397eb6`, '，项目名称：')  + `${allData.tenderProjectName}`,
                    params: JSON.stringify(pageData)
                }
                resolve({
                    ...args,
                    allData: formatData
                })
            })
        },
        // 保存
        handleSave (params){
            const allData = this.getAllData()
            if(allData.projectItemList.length >0){
                allData.projectItemList.forEach(item=>{
                    delete item.id
                })
            }
            this.composeBusinessSave(params)
            // composeBusinessSave
        },
        // 提交审批
        handleSubmit (params){
            const allData = this.getAllData()
            let hasMaterialName = true
            let noNameIndexArr=[]
            let noNameIndex
            if(allData.projectItemList.length >0){
                allData.projectItemList.forEach((item, index)=>{
                    delete item.id
                    if(!item.materialName){
                        noNameIndexArr.push(index+1)
                        hasMaterialName = false
                    }
                })
                noNameIndex = noNameIndexArr.join(',')
            }
            if(!hasMaterialName) {
                this.$message.warning(`第${noNameIndex}行物料名称不能为空`)
                return
            }
            // 分包行校验
            if (!allData.subpackageInfoVOList || allData.subpackageInfoVOList.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RRPjImzscW_399cd384`, '至少要有一个分包行！'))
                return
            }
            if (allData.tenderStatus && allData.tenderStatus !== '0'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n__xiTVBDJW_c57ee4e9`, '不允许重复提交！'))
                return
            }
            // 审批状态
            if (allData.auditStatus === '1' || allData.auditStatus === '2'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n__xiTVBDJW_c57ee4e9`, '不允许重复提交！'))
                return
            }

            let currency = allData.currency || ''
            if(allData.projectItemList && currency){
                allData.projectItemList.forEach(item=>{
                    item.currency = currency
                })
                params.projectItemList = allData.projectItemList
            }
            // params.pageConfig.groups.forEach(group=>{
            //     if(group.groupCode == 'baseForm'){
            //         group.formModel.openBidPassword = (group.formModel.openBidPassword ?? '') != '' ? Base64.encode(group.formModel.openBidPassword) : ''
            //         console.log(Base64.encode(group.formModel.openBidPassword), group.formModel.openBidPassword)
            //     }
            // })
            if (allData.audit === '1') {
                this.composeBusinessSubmit(params)
            } else {
               
                this.composeBusinessPublish(params)
            }

        },
        async handleShowNode ({row}) {
            let {tenderProcessModelId, tenderProcessModelName} = row
            if (!tenderProcessModelId) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板'))
            let allNode = await this.getNodeGruop({tenderProcessModelId, tenderProcessModelName })
            let {mustNode, periodTypeInfo, notMustNode} = allNode
            this.allNodeMap = {}
            this.periodTypeInfoArray = periodTypeInfo
            Object.values(mustNode).map(el => {
                el.map(item => {
                    if (!this.allNodeMap[item.periodType]) this.allNodeMap[item.periodType] = []
                    this.allNodeMap[item.periodType].push(item)
                })
            })
            Object.values(notMustNode).map(el => {
                el.map(item => {
                    if (!this.allNodeMap[item.periodType]) this.allNodeMap[item.periodType] = []
                    this.allNodeMap[item.periodType].push(item)
                })
            })
            this.showNode = true
        }
    },
    created (){
        console.log('@#@##@#', this.$ls.get(USER_INFO))
    }

}
</script>