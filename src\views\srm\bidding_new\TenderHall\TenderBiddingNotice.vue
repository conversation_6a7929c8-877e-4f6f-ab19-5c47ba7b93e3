<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="show"
        :ref="businessRefName"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :currentEditRow="{}"
        :pageFooterButtons="pageFooterButtons"
        modelLayout="masterSlave"
        :handleAfterDealSource="handleAfterDealSource"
        :fromSourceData="fromSourceData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
        <template v-slot:noticeInfo="">
          <div class="ueditorDetail">
            <div v-html="noticeContent"></div>
          </div>
        </template>
      </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import JEditor from '@/components/els/JEditor'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins.js'
import { BUTTON_PUBLISH } from '@/utils/constant.js'
import { merge } from 'lodash'
export default {
    name: 'TenderNoticeHead',
    components: {
        BusinessLayout,
        JEditor,
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId'],
    mixins: [businessUtilMixin, baseMixins],
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            show: false,
            externalToolBar: {},
            pageFooterButtons: [],
            currentGroupCode: {},
            noticeStatus: '',
            fromSourceData: {},
            noticeContent: '',
            url: {
                detail: '/tender/purchaseTenderNoticeHead/queryBySubpackageId',
                add: '/tender/purchaseTenderNoticeHead/add',
                edit: '/tender/purchaseTenderNoticeHead/edit',
                publish: '/tender/purchaseTenderNoticeHead/publish'
            },
            remoteJsFilePath: ''
        }
    },
    computed: {
        // remoteJsFilePath () {
        //     // let templateNumber = this.currentEditRow.templateNumber
        //     // let templateVersion = this.currentEditRow.templateVersion
        //     // let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
        //     let templateNumber = 'TC2022051401'
        //     let templateVersion = 1
        //     let account = 100000
        //     return `${account}/purchase_purchaseTenderNotice_${templateNumber}_${templateVersion}`
        // },
        pageStatus () {
            return 'detail'
        },
        subId () {
            return this.subpackageId()
        }
    },
    methods: {
        ccc () {
            console.log(3333)
        },
        // 获取业务模板信息
        async getTemplateInfo () {
            const currentEditRow = await this.getBusinessTemplate('purchaseTenderNotice')
            if (!currentEditRow) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                return
            }
            this.currentEditRow = Object.assign(this.currentEditRow, currentEditRow)
            this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/purchase_purchaseTenderNotice_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
        
            // let templateNumber = 'TC2022051401'
            // let templateVersion = 1
            // let account = 100000
            // this.remoteJsFilePath = `${account}/purchase_purchaseTenderNotice_${templateNumber}_${templateVersion}`
            // let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'purchaseTenderNotice'}
            
            // getAction('/template/templateHead/getListByType', params).then(res => {
            //     if(res.success) {
            //         if(res.result.length > 0) {
            //             let options = res.result.map(item => {
            //                 return {
            //                     templateNumber: item.templateNumber,
            //                     templateName: item.templateName,
            //                     templateVersion: item.templateVersion,
            //                     templateAccount: item.elsAccount
            //                 }
            //             })
            //             this.currentEditRow = options[0]
            //             this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/purchase_purchaseTenderNotice_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
            //         } else {
            //             this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
            //         }
            //     }else {
            //         this.$message.warning(res.message)
            //     }
            // })
        },
        init () {
            // noticeType值不写死，从节点信息中获取
            return getAction(this.url.detail, { subpackageId: this.subId, noticeType: this.currentNode().extend.noticeType })
                .then(res => {
                    if (res.success) {
                        if (res.result) {
                            this.fromSourceData = res.result || {}
                            this.noticeStatus = res.result.noticeStatus
                            this.noticeContent = res.result.noticeContent
                            if (this.noticeStatus != '0') this.externalToolBar = {}
                        } else {
                            this.fromSourceData = { z: 1_1}
                            this.noticeStatus = '0'
                        }
                    }
                })
        },
        handleAfterDealSource (pageConfig, resultData) {
            
            let signUpFlag = !resultData.signUpType
            let biddingFlag = !resultData.biddingType
            // let none = (signUpFlag && biddingFlag)
            // 隐藏数组内的字段
            let arr = []
            // 非报名情况
            if (signUpFlag) {
                arr = arr.concat('signUpBeginTime', 'signUpEndTime', 'signUpType')
            }
            //非购标情况
            if (biddingFlag) {
                arr = arr.concat('biddingBeginTime', 'biddingEndTime', 'biddingType', 'offlineSaleAccount')
            }
            // 不报名不购标情况
            // if(none){
            //     arr=arr.concat('fileSubmitEndTime')
            // }
            let formFields = []
            console.log('pageConfig', pageConfig)
            console.log('resultData', resultData)
            console.log(resultData.signUp, resultData.bidding)
            pageConfig.groups[1].formFields.forEach((item) => {
                if (arr.indexOf(item.fieldName) == -1) {
                    formFields.push(item)
                }
            })
            pageConfig.groups[1].formFields = formFields

            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }

            // let biddingFlag = (this.bidding == '1');
            setDisabledByProp('signUpBeginTime', signUpFlag)
            setDisabledByProp('signUpEndTime', signUpFlag)
            setDisabledByProp('signUpType', signUpFlag)
            // setDisabledByProp('biddingBeginTime', biddingFlag);
            // setDisabledByProp('biddingEndTime', biddingFlag);

            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }

                rule[prop] = [{
                    required: flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            //此处写根据分包信息作为判断展示位
            let signUpValidateFlag = (this.signUp == '1')
            // let biddingValidateFlag = (this.bidding !== '1');
            setValidateRuleByProp('signUpBeginTime', signUpValidateFlag)
            setValidateRuleByProp('signUpEndTime', signUpValidateFlag)
            setValidateRuleByProp('signUpType', signUpValidateFlag)

            // setValidateRuleByProp('biddingBeginTime', biddingValidateFlag);
            // setValidateRuleByProp('biddingEndTime', biddingValidateFlag);
        },
        fieldSelectOk () {
        }
    },
    mounted () {
        this.confirmLoading = true
        Promise.all([this.getTemplateInfo(), this.init()]).then(res => {
            this.confirmLoading = false
            this.show = true
        }).catch(error => {
            console.log(error)
            this.confirmLoading = false
        })
    }
}
</script>

<style scoped>
.ueditorDetail{
    margin-top: 10px;
    border: 1px solid #e8e8e8;
    padding: 6px;
}
</style>
