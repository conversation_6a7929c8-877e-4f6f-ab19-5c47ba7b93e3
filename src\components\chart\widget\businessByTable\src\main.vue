
<template>
  <dv-scroll-board
    :config="{
      data: data.data,
      header: data.header,
      index: widget.option.index,
      align: widget.option.align,
    }"
    :style="style"
  />
</template>
<script>
import { chartsMixins } from '@comp/chart/widget/mixins/chartsMixins'
export default {
    name: 'BusinessByTable',
    mixins: [chartsMixins],
    props: {
        widget: {
            type: [Object],
            default: () => { }
        }
    },
    computed: {
        option () {
            return {
                index: this.widget.option.index
            }
        }
    },
    watch: {
        option: {
            handler (val) {
                this.widget.option.index = val.index
            },
            deep: true
        }
    },
    methods: {
        refreshWidgetData (data) {
            this.widget.data.header = data.header
            this.widget.data.data = data.data
        }
    }
}
</script>