<template>
  <div class="announcement">

    <div
      v-if="vuex_currentEditRow.id"
      class="container"
      :style="style">
      
      <component
        :is="curComp"
        :current-edit-row="vuex_currentEditRow"
        @routerRefreshEvent="handleRouterRefresh" />
        
    </div>
  </div>
</template>

<script>
import PurchaseBiddingDetail from './components/PurchaseBiddingDetail'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    name: 'Announcement',
    components: {
        'purchase-bidding-detail': PurchaseBiddingDetail
    },
    data () {
        return {}
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        curComp () {
            // return this.isEdit ? 'purchase-bidding-edit' : 'purchase-bidding-detail'
            return 'purchase-bidding-detail'
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleRouterRefresh () {}
    },
    created () {
        this.height = document.documentElement.clientHeight
    }
}
</script>

<style lang="less" scoped>
.announcement {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    :deep(.page-container ){
        padding: 0;
    }
}
</style>
