import { cloneDeep } from 'lodash'
import { ListConfig } from '@/plugins/table/gridConfig'
import { getAction } from '@/api/manage'
let cloneListConfig = cloneDeep(ListConfig)
if (cloneListConfig.toolbarConfig) {
    delete cloneListConfig.toolbarConfig
}
export default {
    name: 'contactList',
    data() {
        return {
            visible: false,
            spinning: false,
            tableData: [],
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contactInfo`, '联系人信息'),
            tableColumns: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                    field: 'elsAccount',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_toElsAccount`, '采购维护账号'),
                    field: 'toElsAccount',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                    field: 'subAccount',
                    width: 150
                },
                {
                    required: '0',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_function`, '职能'),
                    field: 'functionName',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_position`, '职位'),
                    field: 'position',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                    field: 'name',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话号码'),
                    field: 'telphone',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'),
                    field: 'email',
                    width: 150
                },
                {
                    title: 'QQ',
                    field: 'qqNo',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_weChat`, '微信'),
                    field: 'wxNo',
                    width: 150
                }
            ],
            pageConfig: {
                total: 0,
                currentPage: 1,
                pageSize: 500,
                align: 'right',
                pageSizes: [20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            }
        }
    },
    methods: {
        open(row) {
            this.tableData = []
            this.visible = true
            this.getTableData(row)
        },
        getTableData({ elsAccount, toElsAccount }) {
            this.spinning = true
            const { pageSize = 20, currentPage = 1 } = this.pageConfig || {}
            const params = {
                pageSize,
                pageNo: currentPage,
                elsAccount: toElsAccount,
                toElsAccount: elsAccount
            }
            getAction('/supplier/supplierMaster/getContractInfo', params)
                .then((res) => {
                    if (res.code == 200) {
                        this.tableData = res.result.records || []
                        this.pageConfig.total = res.result.total || 0
                    }
                })
                .finally(() => {
                    this.spinning = false
                })
        },
        handleCancel() {
            this.visible = false
        },
        handlePageChange({ currentPage, pageSize }) {
            this.pageConfig.currentPage = currentPage
            this.pageConfig.pageSize = pageSize
            this.getTableData()
        }
    },
    render() {
        const props = {
            visible: this.visible,
            title: this.title,
            width: 900
        }
        const on = {
            cancel: this.handleCancel,
            ok: this.handleCancel
        }
        const spinProps = {
            spinning: this.spinning,
            delayTime: 300
        }
        const minHeight = 400

        const scopedSlots = {
            empty: () => (<a-empty />)
        }
        return (
            <div class="approvalHistoryModal">
                <a-modal {...{ props, on }}>
                    <div class="content">
                        <a-spin {...{ props: spinProps }} >
                            <div style={{ minHeight: `${minHeight}px` }}>
                                <vxe-grid data={this.tableData}
                                    columns={this.tableColumns}
                                    height={minHeight}
                                    page-change={this.handlePageChange}
                                    pagerConfig={this.pageConfig}
                                    {...cloneListConfig} scopedSlots={scopedSlots} />
                            </div>
                        </a-spin>
                    </div>
                </a-modal>
            </div>
        )
    }
}