<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <EnterpriseCertificationEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <EnterpriseCertificationDetail 
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
    <EnterpriseCertificationAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>    
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import EnterpriseCertificationAdd from './modules/EnterpriseCertificationAdd'
import EnterpriseCertificationEdit from './modules/EnterpriseCertificationEdit'
import EnterpriseCertificationDetail from './modules/EnterpriseCertificationDetail'
import { postAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        EnterpriseCertificationEdit,
        EnterpriseCertificationDetail,
        EnterpriseCertificationAdd
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                businessType: 'esignEnterpriseCertification',
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    // {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                    // ,
                    // {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, clickFn: this.showHelpText},
                    // {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: '(公司名称)'
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_view`, '查看'), clickFn: this.handleView, authorityCode: 'esign#saleEnterpriseCertification:detail'}
                ]
            },
            url: {
                add: '/esign/elsEnterpriseCertificationInfo/add',
                list: '/esign/elsEnterpriseCertificationInfo/list',
                delete: '/esign/elsEnterpriseCertificationInfo/delete',
                columns: 'enterpriseCertificationInfoList',
                auth: '/esign/elsEnterpriseCertificationInfo/submitCertification'
            }
        }
    },
    methods: {
        //已认证不能被编辑
        allowEdit (row){
            if(row.createdAccount){
                return true
            }
            if(row.certificationStatus==='1'){
                return true
            }
            return false
        },
        allowCertification (row){
            if(row.createdAccount){
                return true
            }
            if(row.certificationStatus==='1'){
                return true
            }
            return false
        },
        //已经创建e签宝的账号的不能删除
        allowDelete (row){
            if(row.orgId){
                return true
            }
            return false
        },
        handleAdd () {
            this.showAddPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
        },
        handleCertification (row) {
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationSystems`, '确认认证'),
                content: '是否确认提交认证?',
                onOk: function () {
                    postAction(that.url.auth, row).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })         
        }
    }
}
</script>