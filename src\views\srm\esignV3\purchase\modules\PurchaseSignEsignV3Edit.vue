<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url"/>
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal"/>
    <a-modal
      v-drag
      centered
      :width="400"
      :visible="selectSale"
      :maskClosable="false"
      @ok="selectedOk"
      @cancel="selectSale = false"
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierFirst`, '请先选择供应商！')"
    >
      <a-select
        style="width: 100%"
        v-model="saleElsAccount">
        <a-select-option
          v-for="item in saleElsAccountSelect"
          :value="item.value"
          :key="item.value">
          {{ item.text }}
        </a-select-option>
      </a-select>
    </a-modal>
    <a-modal
      v-drag
      v-model="visible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_PeIL_39fb9595`, '签章定位')"
      @ok="handleKeyOk">
      <a-form-model
        :model="form"
        :layout="layout">
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_keyword`, '关键字')">
          <a-input
            v-model="form.keyword"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

  </div>
</template>

<script>
import {EditMixin} from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'

export default {
    name: 'EsignFlowAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            isSaleEdit: false,
            saleElsAccountSelect: [],
            saleElsAccount: null,
            selectSale: false,
            rowIndex: -1,
            rowSignerIndex: -1,
            selectType: 'esignFlow',
            labelCol: {span: 4},
            wrapperCol: {span: 14},
            visible: false,
            peopleNumber: 1,
            form: {
                keyword: ''
            },
            layout: {
                labelCol: {span: 4},
                wrapperCol: {span: 15}
            },
            pageData: {
                form: {
                    toElsAccount: '',
                    supplierName: '',
                    esignNumber: null,
                    businessScene: null,
                    filesName: null,
                    filesId: null,
                    uploaded: null,
                    autoArchiving: '1',
                    autoInitiate: '1',
                    remark: null,
                    cutOffTime: null,
                    contractRemind: null,
                    noticeType: '1',
                    effectiveTime: null
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAy_24c6a9a8`, '业务编号'),
                                    fieldName: 'busNumber',
                                    disabled: true
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount',
                                    bindFunction: function (Vue, data) {
                                        let els = []
                                        let name = []
                                        data.forEach(v => {
                                            if (!els.includes(v.elsAccount)) {
                                                els.push(v.elsAccount)
                                                name.push(v.orgName)
                                            }
                                        })
                                        Vue.$set(Vue.form, 'toElsAccount', els.join(','))
                                        Vue.$set(Vue.form, 'supplierName', name.join(','))
                                    },
                                    extend: {
                                        modalColumns: [
                                            {
                                                field: 'elsAccount',
                                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_to_els_account`, '供应商ELS账号'),
                                                fieldLabelI18nKey: 'i18n_field_toCompanyCode',
                                                with: 150
                                            },
                                            {
                                                field: 'orgName',
                                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPsLiAERL_b00d127d`, 'E签宝认证企业名称'),
                                                fieldLabelI18nKey: 'i18n_massProdHeade95_supplierName',
                                                with: 150
                                            },
                                            {
                                                field: 'realnameStatus_dictText',
                                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIKR_c7a03e6a`, '是否已实名'),
                                                with: 150
                                            }
                                        ],
                                        selectModel: 'multiple',
                                        modalUrl: '/esignv3/saleEsignV3Org/list',
                                        modalParams: {}
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene',
                                    required: '1'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationDateSigning`, '签署截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                businessScene: [{required: true, message: '文件主题不能为空'}],
                                noticeType: [{required: true, message: '提醒方式不能为空'}],
                                autoInitiate: [{required: true, message: '流程是否自动开启不能为空'}],
                                autoArchiving: [{required: true, message: '是否自动归档不能为空'}]
                            }
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCPWL_36613b74`, '采方签署人'),
                        groupCode: 'purchaseSignersList',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseSignersList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 250,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                },
                                {
                                    field: 'signerType',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型'),
                                    width: 120,
                                    editRender: {
                                        name: '$select', options: [
                                            {
                                                label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'),
                                                value: '0'
                                            },
                                            {
                                                label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'),
                                                value: '1'
                                            },
                                            {
                                                label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPerson`, '法定代表人'),
                                                value: '2'
                                            }
                                        ]
                                    }
                                },
                                {
                                    field: 'signFieldStyle',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeMVK_55b1010`, '签章区样式'),
                                    width: 120,
                                    editRender: {
                                        name: '$select', options: [
                                            {label: '单页签署', value: '1'},
                                            {label: '骑缝签署', value: '2'}
                                        ]
                                    }
                                },
                                {
                                    field: 'signArea',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'),
                                    width: 120
                                },
                                {
                                    field: 'signWord',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'),
                                    width: 120
                                },
                                {
                                    field: 'sealIds',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'),
                                    width: 120
                                },
                                {
                                    field: 'elsAccount',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'),
                                    width: 100
                                },
                                {
                                    field: 'orgName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                                    width: 180
                                },
                                {
                                    field: 'subAccount',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'),
                                    width: 120
                                },
                                {
                                    field: 'psnAccount',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDEPsey_25baae92`, '签署用户E签宝账号'),
                                    width: 120
                                },
                                {
                                    field: 'psnName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDcR_f5c3f89d`, '签署用户姓名'),
                                    width: 120
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuPWRC_72eda045`, '添加签署公司'),
                                    type: 'primary',
                                    click: this.addPurchaseSignEvent,
                                    showCondition: this.showAddPurchaseSignEvent
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QGPWRC_a5504d04`, '删除签署公司'),
                                    click: this.deletePurchaseSignEvent,
                                    showCondition: this.showDeleteSaleSignEvent
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'add',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWMU_b399f609`, '设置签署区域'),
                                    clickFn: this.getSignArea
                                },
                                {
                                    type: 'add',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRWe_416ee4e0`, '设置印章'),
                                    clickFn: this.getSeal
                                },
                                {
                                    type: 'add',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWL_ed04f016`, '设置签署人'),
                                    clickFn: this.addPurchaseSignerEvent
                                }
                            ],
                            rules: {
                                subAccount: [{
                                    required: true,
                                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWWWeyWxOLV_18bfbcf9`, '[签署人SRM账号]不能为空')
                                }],
                                psnAccount: [{
                                    required: true,
                                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWPseyWxOLV_9b256723`, '[签署人E签宝账号]不能为空')
                                }],
                                signerType: [{
                                    required: true,
                                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_[PWCAc]xOLV_d16c9bf3`, '[签署方类型]不能为空')
                                }]
                                // ,
                                // signArea: [{required: true, message: '签署区域不能为空'}]
                            }
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RCPWL_91e5ef48`, '供方签署人'),
                        groupCode: 'saleSignersList',
                        type: 'grid',
                        custom: {
                            ref: 'saleSignersList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 230,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                },
                                {
                                    field: 'signerType',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型'),
                                    width: 120,
                                    editRender: {
                                        name: '$select', options: [
                                            {
                                                label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'),
                                                value: '0'
                                            },
                                            {
                                                label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'),
                                                value: '1'
                                            },
                                            {
                                                label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPerson`, '法定代表人'),
                                                value: '2'
                                            }
                                        ]
                                    }
                                },
                                {
                                    field: 'signFieldStyle',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeMVK_55b1010`, '签章区样式'),
                                    width: 120,
                                    editRender: {
                                        name: '$select', options: [
                                            {label: '单页签署', value: '1'},
                                            {label: '骑缝签署', value: '2'}
                                        ]
                                    }
                                },
                                {
                                    field: 'signArea',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'),
                                    width: 120
                                },
                                {
                                    field: 'signWord',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'),
                                    width: 120
                                },
                                {
                                    field: 'sealIds',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'),
                                    width: 120
                                },
                                {
                                    field: 'elsAccount',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'),
                                    width: 100
                                },
                                {
                                    field: 'orgName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                                    width: 180
                                },
                                {
                                    field: 'subAccount',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'),
                                    width: 120
                                },
                                {
                                    field: 'psnAccount',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDEPsey_25baae92`, '签署用户E签宝账号'),
                                    width: 120
                                },
                                {
                                    field: 'psnName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDcR_f5c3f89d`, '签署用户姓名'),
                                    width: 120
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuPWRC_72eda045`, '添加签署公司'),
                                    type: 'primary',
                                    click: this.addSaleSignEvent,
                                    showCondition: this.showAddPurchaseSignEvent
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QGPWRC_a5504d04`, '删除签署公司'),
                                    click: this.deleteSaleSignEvent,
                                    showCondition: this.showDeleteSaleSignEvent
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'add',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWMU_b399f609`, '设置签署区域'),
                                    clickFn: this.getSaleSignArea
                                },
                                {
                                    type: 'add',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRWe_416ee4e0`, '设置印章'),
                                    clickFn: this.getSaleSeal
                                },
                                {
                                    type: 'add',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWL_ed04f016`, '设置签署人'),
                                    clickFn: this.addSaleSignerEvent
                                }
                            ],
                            rules: {
                                subAccount: [{
                                    required: true,
                                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWWWeyWxOLV_18bfbcf9`, '[签署人SRM账号]不能为空')
                                }],
                                accountId: [{required: true, message: '[签署人E签宝账号]不能为空'}],
                                signerType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_[PWCAc]xOLV_d16c9bf3`, '[签署方类型]不能为空')}]
                            }
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachments',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 200
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 180
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'createBy_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                                    width: 120
                                },
                                {
                                    field: 'signType',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'),
                                    width: 120,
                                    editRender: {
                                        name: '$select', options: [
                                            {
                                                label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Attachment`, '附件'),
                                                value: '0'
                                            },
                                            {
                                                label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dD_9c878`, '主体'),
                                                value: '1'
                                            }
                                        ]
                                    }
                                },
                                {
                                    field: 'uploaded',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LaUawliDMFVcRPYm`, '是否上传e签宝'),
                                    width: 180,
                                    visible: false
                                },
                                {
                                    field: 'uploaded_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LaUawliDMFVcRPYm`, '是否上传e签宝'),
                                    width: 120
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 220,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                    type: 'upload', businessType: 'esign',
                                    attr: this.attrHandle,
                                    callBack: this.uploadCallBack
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                                    click: this.deleteBatch
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    clickFn: this.downloadEvent
                                },
                                {
                                    type: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preViewEvent
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    clickFn: this.deleteFilesEvent
                                }
                            ]
                        }
                    }
                ],
                formFields: [],
                footerButtons: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        type: 'primary',
                        click: this.saveEvent
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QIXVWPs_e850e7df`, '文件上传E签宝'),
                        type: 'primary',
                        click: this.fileUpload
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_hAIxPW_f576ec9f`, '发起一步签署'),
                        type: 'primary',
                        click: this.launchOneStepEsignEvent
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                save: '/esignv3/elsEsignV3Flow/multipleSave',
                detail: '/esignv3/elsEsignV3Flow/queryById',
                uploadFile: '/esignv3/elsEsignV3Flow/fileCreate',
                upload: '/attachment/purchaseAttachment/upload',
                uploadLogUrl: '/attachment/purchaseAttachment/uploadLog',
                download: '/attachment/purchaseAttachment/download',
                launchOneStepEsign: '/esignv3/elsEsignV3Flow/createFlowOneStep',
                keyWordToAera: '/esignv3/elsEsignV3Flow/keyWordToAera'
            }
        }
    },
    mounted () {
        this.init()
    },
    methods: {
        getSaleSignArea (row, column, $rowIndex) {
            this.isSaleEdit = true
            const form = this.$refs.editPage.getPageData()
            if (form.uploaded !== '1') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dDsLXVePs_e85ff264`, '主体还未上传e签宝'))
                return
            }
            this.sealAeraIndex = $rowIndex
            this.rowSignerIndex = $rowIndex
            this.confirmLoading = true
            let columns = [
                {field: 'fileName', title: '文件名称', width: 200},
                {field: 'fileId', title: '上传文件id', width: 180}
            ]
            this.selectType = 'keyWordCheck'
            this.$refs.fieldSelectModal.open('/esignv3/elsEsignV3Attachment/list', {
                headId: form.id,
                signType: '1'
            }, columns, 'single')
        },
        getSignArea (row, column, $rowIndex) {
            this.isSaleEdit = false
            const form = this.$refs.editPage.getPageData()
            if (form.uploaded !== '1') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dDsLXVePs_e85ff264`, '主体还未上传e签宝'))
                return
            }
            this.sealAeraIndex = $rowIndex
            this.rowSignerIndex = $rowIndex
            this.confirmLoading = true
            let columns = [
                {field: 'fileName', title: '文件名称', width: 200},
                {field: 'fileId', title: '上传文件id', width: 180}
            ]
            this.selectType = 'keyWordCheck'
            this.$refs.fieldSelectModal.open('/esignv3/elsEsignV3Attachment/list', {
                headId: form.id,
                signType: '1'
            }, columns, 'single')
        },
        addSaleSignerEvent (row, column, $rowIndex) {
            this.selectType = 'saleEsigner'
            this.rowSignerIndex = $rowIndex
            let url = '/esignv3/saleEsignV3OrgPsn/list'
            let columns = [
                {
                    field: 'subAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                    width: 100
                },
                {
                    field: 'psnAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPseDy_a4573ac2`, 'e签宝账号'),
                    width: 180
                },
                {field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'), width: 180},
                {field: 'psnId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPseyID_49891550`, 'E签宝账号ID'), width: 180}
            ]
            let params = {orgId: row.orgId}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        getSaleSeal (row) {
            if (!row.signerType) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFPWCAcW_752c471d`, '请先选择签署方类型！'))
                return
            }
            if (!row.psnId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWGRPWL_eab9ad1c`, '请先设置签署人！'))
                return
            }
            this.selectType = 'addSaleSigner'
            this.rowIndex = 0
            let url = '/esignv3/saleEsignV3Seals/getSignSeal'
            let columns = [
                {
                    field: 'orgName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                    width: 200
                },
                {
                    field: 'sealId',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeWW_39f03bbd`, '签章id'),
                    width: 180
                },
                {
                    field: 'sealName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'),
                    width: 180
                },
                {
                    field: 'sealBizType_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeESAc_49a8b9e7`, '印章业务类型'),
                    width: 100
                },
                {
                    field: 'orgAuth_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIHAElb_744f8381`, '是否已跨企业授权'),
                    width: 100
                }
            ]
            let params = {orgId: row.orgId, sealType: row.signerType, psnId: row.psnId}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        handleKeyOk () {
            if (!this.form.keyword) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIJxOLV_594d82ac`, '关键字不能为空'))
                return
            }
            const form = this.$refs.editPage.getPageData()
            let filesId
            if (this.isSaleEdit) {
                form.saleSignersList[this.sealAeraIndex].signWord = this.form.keyword
                filesId = form.saleSignersList[this.sealAeraIndex].filesId
            } else {
                form.purchaseSignersList[this.sealAeraIndex].signWord = this.form.keyword
                filesId = form.purchaseSignersList[this.sealAeraIndex].filesId
            }
            let param = {
                signWord: this.form.keyword,
                filesId: filesId
            }
            this.confirmLoading = true
            let columns = [
                {field: 'page', title: '页码', width: 200},
                {field: 'positionX', title: '横轴(X)', width: 180},
                {field: 'positionY', title: '纵轴(Y)', width: 180}
            ]
            this.selectType = 'keyWord'
            this.$refs.fieldSelectModal.tablePage.pageSize = 100
            this.$refs.fieldSelectModal.open(this.url.keyWordToAera, param, columns, 'single')

            this.visible = false
        },
        selectedOk () {
            this.selectType = 'saleEsign'
            let url = '/esignv3/saleEsignV3Org/list'
            let columns = [
                {
                    field: 'elsAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'),
                    width: 100
                },
                {
                    field: 'orgName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                    width: 180
                },
                {
                    field: 'orgId',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_yVy933rH`, '机构ID'),
                    width: 120
                },
                {field: 'legalRepName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLcR_b362e8a`, '法定代表人姓名'), width: 120}
            ]
            let params = {realnameStatus: 1, elsAccount: this.saleElsAccount}
            this.selectSale = false
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.esignNumber,
                actionRoutePath: '/srm/esign/PurchaseEsignList,/srm/esign/sale/SaleEsignList'
            }
        },
        preViewEvent (row) {
            console.log('row', row)
            if(row.absoluteFilePath){
                this.$previewFile.open({path: row.absoluteFilePath})
            }else {
                let preViewFile = row
                this.$previewFile.open({params: preViewFile})
            }
        },
        init () {
            if (this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            } else {
                this.$refs.editPage.queryDetail('')
            }
        },
        deleteBatch () {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachments[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            for(let r of checkboxRecords){
                if(r.uploaded == '1'){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IXVEPsQIxOQG_cf8860bf`, '已上传E签宝文件不能删除'))
                    return
                }
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/esignv3/elsEsignV3Attachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        deleteFilesEvent (row) {
            if(row.uploaded == '1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IXVEPsQIxOQG_cf8860bf`, '已上传E签宝文件不能删除'))
                return
            }
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachments[0]
            getAction('/esignv3/elsEsignV3Attachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        addSaleSignEvent () {
            const form = this.$refs.editPage.getPageData()
            console.log('form:', form)
            if (!form.toElsAccount) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierFirst`, '请先选择供应商！'))
                return
            }
            let e = form.toElsAccount.split(',')
            let s = form.supplierName.split(',')
            this.saleElsAccountSelect = []
            let i = 0
            e.forEach(v => {
                this.saleElsAccountSelect.push({value: v, text: s[i]})
                i++
            })
            this.selectSale = true
        },
        addPurchaseSignEvent () {
            this.selectType = 'purchaseEsign'
            let url = '/esignv3/purchaseEsignV3Org/list'
            let columns = [
                {
                    field: 'elsAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'),
                    width: 100
                },
                {
                    field: 'orgName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                    width: 180
                },
                {
                    field: 'orgId',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_yVy933rH`, '机构ID'),
                    width: 120
                },
                {field: 'legalRepName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLcR_b362e8a`, '法定代表人姓名'), width: 120}
            ]
            let params = {realnameStatus: 1}

            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        handleOk () {
            this.peopleNumber = this.form.number
            this.selectType = 'purchaseEsign'
            const form = this.$refs.editPage.getPageData()
            let url = '/esign/elsEnterpriseCertificationInfo/list'
            let columns = [
                {
                    field: 'elsAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'),
                    width: 100
                },
                {
                    field: 'enterpriseName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'),
                    width: 180
                },
                {
                    field: 'companyName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称'),
                    width: 180
                },
                {
                    field: 'orgId',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_companySignatureAccount`, '公司E签宝账号'),
                    width: 120
                },
                {
                    field: 'certificationStatus',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'),
                    width: 120,
                    editRender: {
                        name: '$select', options: [
                            {label: '未认证', value: '0'},
                            {label: '已认证', value: '1'}
                        ], disabled: true
                    }
                }
            ]
            let params = {elsAccount: form.elsAccount}
            this.visible = false
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        getSeal (row, column, $rowIndex) {
            this.selectType = 'addSigner'
            this.rowIndex = $rowIndex
            console.log('row:', row)
            if (!row.signerType) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFPWCAcW_752c471d`, '请先选择签署方类型！'))
                return
            }
            if (!row.psnId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWGRPWL_eab9ad1c`, '请先设置签署人！'))
                return
            }
            let url = '/esignv3/purchaseEsignV3Seals/getSignSeal'
            let columns = [
                {
                    field: 'orgName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                    width: 200
                },
                {
                    field: 'sealId',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeWW_39f03bbd`, '签章id'),
                    width: 180
                },
                {
                    field: 'sealName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'),
                    width: 180
                },
                {
                    field: 'sealBizType_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeESAc_49a8b9e7`, '印章业务类型'),
                    width: 100
                },
                {
                    field: 'orgAuth_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIHAElb_744f8381`, '是否已跨企业授权'),
                    width: 100
                }

            ]
            let params = {orgId: row.orgId, sealType: row.signerType, psnId: row.psnId}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addPurchaseSignerEvent (row, column, $rowIndex) {
            this.selectType = 'purchaseEsigner'
            this.rowSignerIndex = $rowIndex
            let url = '/esignv3/purchaseEsignV3OrgPsn/list'
            let columns = [
                {
                    field: 'subAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                    width: 100
                },
                {
                    field: 'psnAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPseDy_a4573ac2`, 'e签宝账号'),
                    width: 180
                },
                {field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'), width: 180},
                {field: 'psnId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPseyID_49891550`, 'E签宝账号ID'), width: 180}
            ]
            let params = {orgId: row.orgId}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        fieldSelectOk (data) {
            const form = this.$refs.editPage.getPageData()
            if (this.selectType == 'purchaseEsign') {
                let arr = data.map(({id, ...others}) => ({
                    relationId: id,
                    autoSign: '0', ...others,
                    subAccount: '',
                    psnAccount: '',
                    psnId: '',
                    psnName: ''
                }))
                let itemGrid = this.$refs.editPage.$refs.purchaseSignersList[0]
                itemGrid.remove()
                let {fullData} = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.relationId
                })
                let insertData = arr.filter(item => {
                    return !regulations.includes(item.relationId)
                })
                itemGrid.insertAt(insertData)
            } else if (this.selectType === 'addSigner') {
                let ids = data.map(n => n.sealId).join(',')
                form.purchaseSignersList[this.rowIndex].sealIds = ids
                form.purchaseSignersList[this.rowIndex].orgAuth = data[0].orgAuth
            } else if (this.selectType === 'purchaseEsigner') {
                form.purchaseSignersList[this.rowSignerIndex].subAccount = data[0].subAccount
                form.purchaseSignersList[this.rowSignerIndex].psnId = data[0].psnCode
                form.purchaseSignersList[this.rowSignerIndex].psnAccount = data[0].psnAccount
                form.purchaseSignersList[this.rowSignerIndex].psnName = data[0].psnName
                form.purchaseSignersList[this.rowSignerIndex].sealIds = ''
            } else if (this.selectType == 'saleEsign') {
                let arr = data.map(({id, ...others}) => ({
                    relationId: id,
                    signType: '1',
                    autoSign: '0', ...others,
                    subAccount: '',
                    psnId: '',
                    psnName: '',
                    psnAccount: ''
                }))
                let itemGrid = this.$refs.editPage.$refs.saleSignersList[0]
                itemGrid.remove()
                let {fullData} = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.relationId
                })
                let insertData = arr.filter(item => {
                    return !regulations.includes(item.relationId)
                })
                itemGrid.insertAt(insertData)
            } else if (this.selectType === 'addSaleSigner') {
                let ids = data.map(n => n.sealId).join(',')
                // const form = this.$refs.editPage.getPageData()
                form.saleSignersList[this.rowIndex].sealIds = ids
                form.saleSignersList[this.rowIndex].orgAuth = data[0].orgAuth
            } else if (this.selectType === 'saleEsigner') {
                // const form = this.$refs.editPage.getPageData()
                form.saleSignersList[this.rowSignerIndex].subAccount = data[0].subAccount
                form.saleSignersList[this.rowSignerIndex].psnId = data[0].psnCode
                form.saleSignersList[this.rowSignerIndex].psnName = data[0].psnName
                form.saleSignersList[this.rowSignerIndex].psnAccount = data[0].psnAccount
                form.saleSignersList[this.rowSignerIndex].sealIds = ''
            } else if (this.selectType === 'keyWordCheck') {
                if (this.isSaleEdit) {
                    form.saleSignersList[this.rowSignerIndex].filesId = data[0].fileId
                } else {
                    form.purchaseSignersList[this.rowSignerIndex].filesId = data[0].fileId
                }
                this.visible = true
            } else if (this.selectType === 'keyWord') {
                if (data && data.length > 0) {
                    const {page = '', positionX = '', positionY = ''} = data[0] || {}
                    let result = `${page}_${positionX}_${positionY}`
                    // const form = this.$refs.editPage.getPageData()
                    let param = this.isSaleEdit? form.saleSignersList[this.sealAeraIndex]: form.purchaseSignersList[this.sealAeraIndex]
                    param.signArea = result
                }

                this.visible = false
            }
        },
        uploadCallBack (result) {
            let itemGrid = this.$refs.editPage.$refs.purchaseAttachments[0]
            itemGrid.insertAt(result, -1)
        },
        deletePurchaseSignEvent () {
            let itemGrid = this.$refs.editPage.$refs.purchaseSignersList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        deleteSaleSignEvent () {
            let itemGrid = this.$refs.editPage.$refs.saleSignersList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        saveEvent () {
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    postAction(this.url.save, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.currentEditRow = res.result
                            this.init()
                        }
                    }).finally(() => {
                        this.$refs.editPage.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        launchOneStepEsignEvent () {
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            if (params.uploaded !== '1') {
                this.$refs.editPage.confirmLoading = false
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dDsLXVePs_e85ff264`, '主体还未上传e签宝'))
                return
            }
            if (!params.purchaseSignersList || params.purchaseSignersList.length < 1) {
                this.$refs.editPage.confirmLoading = false
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWLxOLV_9c982296`, '签署人不能为空'))
                return
            }
            for (let row of params.purchaseSignersList) {
                if (row.sealIds || row.signArea) {
                    if (!row.sealIds) {
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrPWMUlTGRWe_a65494a5`, '设置了签署区域必须设置印章'))
                        return
                    }
                    if (!row.signArea) {
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrWelTGRPWMU_913de1e5`, '设置了印章必须设置签署区域'))
                        return
                    }
                    if (!row.signFieldStyle) {
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrWeSPWMUlTiFPeMVK_42827fb1`, '设置了印章或签署区域必须选择签章区样式'))
                        return
                    }
                }
            }
            for (let row of params.saleSignersList) {
                if (row.sealIds || row.signArea) {
                    if (!row.sealIds) {
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrPWMUlTGRWe_a65494a5`, '设置了签署区域必须设置印章'))
                        return
                    }
                    if (!row.signArea) {
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrWelTGRPWMU_913de1e5`, '设置了印章必须设置签署区域'))
                        return
                    }
                    if (!row.signFieldStyle) {
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrWeSPWMUlTiFPeMVK_42827fb1`, '设置了印章或签署区域必须选择签章区样式'))
                        return
                    }
                }
            }
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    let url = this.url.launchOneStepEsign
                    postAction(url, params).then(res => {
                        this.$refs.editPage.confirmLoading = false
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.goBack()
                        }
                    })
                }
            })
        },
        fileUpload () {
            const params = this.$refs.editPage.getPageData()
            this.$refs.editPage.confirmLoading = true
            if (!params.purchaseAttachments || params.purchaseAttachments.length < 1) {
                this.$refs.editPage.confirmLoading = false
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVQIxOLV_117a08b5`, '上传文件不能为空'))
                return
            }
            let flag = true
            for (let file of params.purchaseAttachments) {
                if (file.signType == '1') {
                    flag = false
                    break
                }
            }
            if (flag) {
                this.$refs.editPage.confirmLoading = false
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQIsSdDAcQI_19db864d`, '签署文件中无主体类型文件'))
                return
            }
            let url = this.url.uploadFile
            postAction(url, params).then(res => {
                this.$refs.editPage.confirmLoading = false
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.init()
                }
            })

        },
        esignFileDown () {
            const params = this.$refs.editPage.getPageData()
            getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                if (res.success) {
                    window.open(res.result.downloadUrl)
                } else {
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>