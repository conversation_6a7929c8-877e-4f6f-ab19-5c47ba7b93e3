<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-05-18 16:28:17
 * @LastEditors: LOK
 * @LastEditTime: 2022-08-22 15:12:06
 * @Description: 销售协同/寻源协同/报价管理-详情
-->
<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
<!--      :reloadData="handleReloadData"-->
<!--       @cell-click="cellClickEvent"-->

<!--    <field-select-modal ref="fieldSelectModal" />-->
    <set-ladder-price-modal
      :current-edit-row="currentEditRow"
      ref="ladderPage"
    />
    <sale-edit-cost
      ref="costform"
      :current-edit-row="costEditRow"
    ></sale-edit-cost>
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <ItemImportExcel
      ref="itemImportExcel"
      @importCallBack="importCallBack"/>
  </div>
</template>
<script lang="jsx">
import {EditMixin} from '@comp/template/edit/EditMixin'
//import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction, getAction, downFile } from '@/api/manage'
import SetLadderPriceModal from './SetLadderPriceModal'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
import SaleEditCost from './SaleEditCost'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { formatDate } from '@/utils/util.js'
import moment from 'moment'
import { Modal } from 'ant-design-vue'

export default {
    name: 'SaleEnquiryDetail',
    mixins: [EditMixin],
    components: {
        //fieldSelectModal,
        SetLadderPriceModal,
        SaleEditCost,
        ItemImportExcel
    },
    data () {
        return {
            stageTypeData: [],
            showRemote: false,
            costEditRow: {},
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'saleEnquiryItemList',
                        columns: [],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
                                params: this.importParams, click: this.importExcel, showCondition: this.showExcelButton},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_eportExcel`, '导出Excel'), click: this.exportExcel,
                                showCondition: this.showExcelButton}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'sourceNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号'), width: 130 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rowIitem`, '行项目'), width: 120 },
                            {
                                field: 'materialName',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'),
                                width: 120
                            },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {   title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                dictCode: 'srmFileType',
                                type: 'upload',
                                businessType: 'enquiry',
                                callBack: this.uploadCallBack,
                                attr: this.attrHandle,
                                // showCondition: this.showUploadButton
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), 
                                click: this.deleteBatch
                            }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_RLsu_38d6fc68`, '确认报价'), type: 'primary', click: this.confirm, showCondition: this.showConfirmButton },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save, showCondition: this.showButton },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_offer`, '报价'), type: 'primary', click: this.quote, showCondition: this.showButton },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/enquiry/saleEnquiryHead/queryById',
                confirm: '/enquiry/saleEnquiryHead/confirm',
                save: '/enquiry/saleEnquiryHead/save',
                public: '/enquiry/saleEnquiryHead/quote',
                download: '/attachment/saleAttachment/download',
                upload: '/attachment/saleAttachment/upload',
                import: '/els/base/excelByConfig/importExcel',
                export: '/base/excelByConfig/exportExcel'
            },
            ladderSlots: {
                default: ({row, column}) =>{
                    const tpl = (
                        <div style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                            <a-tooltip placement="topLeft" overlayClassName="tip-overlay-class">
                                <template slot="title">
                                    <div>
                                        <vxe-table auto-resize border row-id="id" size="mini" data={this.initRowLadderJson(row[column.property])}>
                                            <vxe-table-column type="seq" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号')} width="80"></vxe-table-column>
                                            <vxe-table-column field="ladder" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')} width="140"></vxe-table-column>
                                            <vxe-table-column field="price" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价')} width="140"></vxe-table-column>
                                            <vxe-table-column field="netPrice" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价')} width="140"></vxe-table-column>
                                        </vxe-table>
                                    </div>
                                </template>
                                {this.defaultRowLadderJson(row[column.property])}
                            </a-tooltip>
                            <a style="position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 10;">
                                <a-icon
                                    type="stock"
                                    onClick={() => {
                                        this.setLadder && this.setLadder(row)
                                    }}
                                />
                            </a>
                        </div>
                    )
                    if(row && row.quotePriceWay == 1){
                        return tpl
                        // let label=this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noQuotationPrice`, '未报价')
                        // if(row.ladderPriceJson){
                        //     let itemList = JSON.parse(row.ladderPriceJson)
                        //     if(itemList[0].price){
                        //         label = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationPrice`, '已报价')
                        //     }
                        // }
                        // return [
                        //     <a onClick={() => this.setLadder(row)}>{label}</a>
                        // ]
                    }else{
                        return ''
                    }
                }
            },
            costSlots: {
                default: ({row}) => {
                    if(row && row.quotePriceWay == 2){
                        let label = row.price || row.netPrice ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationPrice`, '已报价') :this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noQuotationPrice`, '未报价')
                        return [
                            <a onClick={() => this.openCost(row)}>{label}</a>
                        ]
                    }else{
                        return ''
                    }
                }
            },
            currentRow: {}
        }
    },
    computed: {
        fileSrc () {
            this.getStageTypeData()
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let busAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${busAccount}/sale_enquiry_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        let row = this
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            console.log('[!getDetail!]')
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    row.currentEditRow.templateNumber = res.result.templateNumber
                    row.currentEditRow.templateVersion = res.result.templateVersion
                    row.currentEditRow.templateAccount = res.result.templateAccount
                    row.showRemote = true
                }
            })
        }else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
        async formatTableData(data) { 
            this.setColumnData()
            return new Promise((resolve, reject) => {
                resolve(data)
            })
        },

        // 设置列显示
        setColumnData() {
            let st = setTimeout(() => {
                const form = this.$refs.editPage.getPageData()
                // 0 含税价   1 不含税价
                let itemGrid = this.$refs.editPage.$refs.saleEnquiryItemList[0]
                itemGrid.resetColumn(true)
                let columnsList = itemGrid.getColumns()
                columnsList = columnsList.map((column) => {
                    if (column.field == 'taxCode' || column.field == 'taxRate') {
                        column.visible = form.quoteType == 1 ? false : true
                    }
                    if (column.field == 'netPrice' && form.quoteType == 1) {
                        column.editRender = {
                            enabled: false
                        }
                    }
                    return column;
                })
                itemGrid.loadColumn(columnsList)
            }, 100)
        },

        // 阶梯报价json数据组装
        initRowLadderJson (jsonData) {
            let arr = []
            if (jsonData) {
                arr = JSON.parse(jsonData)
            }
            return arr
        },
        // 阶梯报价默认显示
        defaultRowLadderJson (jsonData) {
            console.log(jsonData)
            let arrString = ''
            if (jsonData) {
                let arr = JSON.parse(jsonData)
                if (Array.isArray(arr)) {
                    arr.forEach((item, index)=> {
                        let ladder = item.ladder
                        let price = item.price || ''
                        let str = `${ladder},${price}`
                        let separator = index===arr.length-1? '': ';'
                        arrString +=str+ separator
                    })
                }
            }
            return arrString
        },
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.enquiryNumber,
                actionRoutePath: '/srm/enquiry/purchase/PurchaseEnquiryList,/srm/enquiry/sale/SaleEnquiryList'
            }
        },
        async getStageTypeData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEnquiryStageType'
            }
            const res = await ajaxFindDictItems(postData)
            if (res.success) {
                this.stageTypeData = res.result
            }
        },
        // checkValidate (form){
        //     if(form.effectiveDate && form.expiryDate && !moment(form.effectiveDate).isBefore(form.expiryDate)) {
        //         this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_umKXKITfUumbXKIW_83d0a788`, '价格失效时间需大于价格生效时间！'))
        //         return false
        //     }
        //     return true
        // },
        checkTableValidate (){
            const currentDate= moment().format('YYYY-MM-DD')
            const params = this.$refs.editPage.getPageData()
            let saleEnquiryItemList = params.saleEnquiryItemList
            if( saleEnquiryItemList?.length>0){
                let i = 1
                for(let item of saleEnquiryItemList){
                    if(item.expiryDate){
                        if(!(moment(item.expiryDate).isSame(currentDate) || moment(item.expiryDate).isAfter(currentDate))) {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, `询价行信息中第 ${i} 行价格失效日期需大于等于当前日期！`))
                            return false
                        }
                    } 
                    if(item.expiryDate && item.effectiveDate){
                        if(!moment(item.effectiveDate).isBefore(item.expiryDate)){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, `询价行信息中第 ${i} 行价格失效日期需大于价格生效日期！`))
                            return false
                        }
                    }
                    i++
                }
            }
            return true
        },
        handleReloadData (res) {
            res.result.ebiddingAmount = res.result.ebiddingAmount ? res.result.ebiddingAmount.toFixed(2) : ''
            res.result.savingAmount = res.result.savingAmount ? res.result.savingAmount.toFixed(2) : ''
            res.result.savingRate = res.result.savingRate ? res.result.savingRate.toFixed(2) : ''
            if (this.stageTypeData && this.stageTypeData.length > 0) {
                res.result.saleAttachmentDemandList = res.result.saleAttachmentDemandList.map(i => {
                    i.stageType_dictText = this.stageTypeData.find(item => item.value === i.stageType).text
                    return i
                })
            }
            return res
        },
        preViewEvent (preViewFile){
            this.$previewFile.open({params: preViewFile })
        },
        beforeHandleData (data) {
            console.log(data,'测试当前数据集')
            data.itemColumns.forEach(item => {
                if(item.field === 'ladderPriceJson'){
                    item.slots = this.ladderSlots
                }
                if(item.field === 'costFormJson'){
                    item.slots = this.costSlots
                }
                //供应商税率为是时,税码可弹框
                if(item.field === 'taxCode' && this.currentEditRow['supplierTaxRate'] == '1'){
                    item.fieldType = 'selectModal'
                    item.extend.modalParams['elsAccount'] = this.currentEditRow['busAccount']
                }
                if(item.field === 'taxCode' && this.currentEditRow['supplierTaxRate'] != '1'){
                    item.fieldType = ''
                }
                if (item.field!==''){
                    item.sortable = true;
                }
            })
            let enquiryStatus = this.currentEditRow.enquiryStatus
            if(enquiryStatus=='2'){
                this.pageData.groups[2].custom.buttons[0].disabled = true
            }
        },
        //控制行信息税码弹框不可编辑
        disableHandle(row){
            //议价中或已悔标
            if(this.currentEditRow['enquiryStatus'] === '7' || this.currentEditRow['enquiryStatus'] === '11'){
                //不是重报价 && 不是报价中
                if(row.itemStatus !== '8' && row.itemStatus !== '1'){
                   return true
                }
            }
            //是否报价为否
            return row.quotePrice=='0'
        },
        importParams (){
            const form = this.$refs.editPage.getPageData()
            return {'id': form.id, 'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'handlerName': 'saleEnquiryItemExcelRpcServiceImpl', 'roleCode': 'sale',
                'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_XlsucVH_eeec005a`, '销售报价行信息')}
        },
        importExcel () {
            const form = this.$refs.editPage.getPageData()
            let params = {'id': form.id, 'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'handlerName': 'saleEnquiryItemExcelRpcServiceImpl', 'roleCode': 'sale',
                'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_XlsucVH_eeec005a`, '销售报价行信息')
            }
            this.$refs.itemImportExcel.open(params, this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_XlsucVH_eeec005a`, '销售报价行信息'), 'saleEnquiryItemList', '')
        },
        importCallBack (result) {
            let response = result.file.response
            let itemGrid = this.$refs.editPage.$refs.saleEnquiryItemList[0]
            let excelData = response.result.dataList

            for (let excelItem of excelData) {
                let itemNumber = excelItem.itemNumber
                for (let item of itemGrid.getTableData().fullData) {
                    if (item.itemNumber == itemNumber) {
                        for (let filed in excelItem) {
                            let value = excelItem[filed]
                            if (value) {
                                item[filed] = value
                            }
                        }
                    }
                }
                //不需要的
                //itemGrid.insertAt(excelData, -1)
            }
            let head = this.currentEditRow || {}
            
            //计算不含税价，不含税总额，含税总额
            if(head.quoteType == '0'){
                for (let item of itemGrid.getTableData().fullData) {
                    // if(item.quotePriceWay == '1'){
                        item['price'] = parseFloat(item.price).toFixed(6)
                        //不含税价
                        item['netPrice'] = (item.price / (1 + parseFloat(item.taxRate)/100)).toFixed(6)
                        //含税总额
                        item['taxAmount'] = (item.price * parseFloat(item.requireQuantity)).toFixed(6)
                        //不含税总额
                        item['netAmount'] = (item.netPrice * parseFloat(item.requireQuantity)).toFixed(6)
                    // }
                }
            }else{
                for (let item of itemGrid.getTableData().fullData) {
                    // if(item.quotePriceWay == '1'){
                        item['netPrice'] = parseFloat(item.netPrice).toFixed(6)
                        item['price'] = (item.netPrice * (1 + parseFloat(item.taxRate)/100)).toFixed(6)
                        item['taxAmount'] = (item.price * parseFloat(item.requireQuantity)).toFixed(6)
                        item['netAmount'] = (item.netPrice * parseFloat(item.requireQuantity)).toFixed(6)
                    // }
                }
            }
        },
        importCallBackOld (result){
            let response = result.file.response
            let itemGrid = this.$refs.editPage.$refs.saleEnquiryItemList[0]
            let excelData = response.result.dataList
            for(let index in excelData){
                for(let filed in excelData[index]){
                    let vaule = excelData[index][filed]
                    if(vaule){
                        let item = itemGrid.getTableData().fullData[index]
                        item[filed] = vaule
                    }
                }
            }
            //不需要的
            //itemGrid.insertAt(excelData, -1)
        },
        exportExcel (){
            const form = this.$refs.editPage.getPageData()
            let params = {'id': form.id, 'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'handlerName': 'saleEnquiryItemExcelRpcServiceImpl', 'roleCode': 'sale', 'busAccount': form.busAccount}
            downFile(this.url.export, params).then((data) => {
                if (!data) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败') )
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), '报价Excel.xlsx')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '报价Excel.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            }).finally(() => {
                this.gridLoading = false
            })
        },
        async confirm (){
            const params = this.$refs.editPage.getPageData()
            
            // 先调用保存接口再进行确认
            this.$refs.editPage.confirmLoading = true;
            try {
                await postAction(this.url.save, params);
            } catch (error) {
                
            }
            
            postAction(this.url.confirm, params).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$refs.editPage.queryDetail(params.id)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        save () {
            const params = this.$refs.editPage.getPageData()
            if(!this.checkTableValidate()) return
            this.$refs.editPage.confirmLoading = true
            postAction(this.url.save, params).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$refs.editPage.queryDetail(params.id)
                } else {
                    this.$message.warning(res.message)
                    this.$refs.editPage.confirmLoading = false
                }
            }).catch(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        quote () {
            // this.$refs.editPage.handleSend()
            this.$refs.editPage.confirmLoading = true
            if(!this.checkTableValidate()) {
                this.$refs.editPage.confirmLoading = false
                return
            }
            this.handleSend()
        },
        handleSend (callback) {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.currentStep = i
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                }
                if (flag) this.postData(callback)
                else this.$refs.editPage.confirmLoading = false
            }).catch(err => {
                console.log(err)
            })
        },
        setPromise () {
            let that = this
            let promise = this.pageData.groups.map(group => {
                if (group.groupCode === 'itemInfo') {
                    return new Promise((resolve, reject) => {
                        const ruleItem = Object.keys(that.$refs.editPage.$refs[group.custom.ref][0].editRules)
                        const params = this.$refs.editPage.getPageData()
                        if (ruleItem && ruleItem.length > 0) {
                            const nullIndex = []
                            const saleEnquiryItemList = params.saleEnquiryItemList
                            const reQuoteList = saleEnquiryItemList.filter(i => i.itemStatus === '8')
                            const reQuoteFlag = reQuoteList.length > 0
                            saleEnquiryItemList.forEach((i, index) => {
                                const nullItems = ruleItem.filter(r => !i[r])
                                // 存在重报价行情况，判断重报价行
                                if (reQuoteFlag && i.itemStatus === '8' && nullItems.length > 0 && i.quotePrice === '1') nullIndex.push(index + 1)
                                // 普通情况，仅判断是否报价
                                else if (!reQuoteFlag && nullItems.length > 0 && i.quotePrice === '1') nullIndex.push(index + 1)
                            })
                            if (nullIndex && nullIndex.length > 0) {
                                if (nullIndex.length > 10)
                                    that.$message.warning(`询价行信息：请完善第 ${nullIndex.slice(0, 10).toString()} ... 行必填项`)
                                else
                                    that.$message.warning(`询价行信息：请完善第 ${nullIndex.slice(0, 10).toString()} 行必填项`)
                                // that.$message.warning(`询价行信息：请完善第 ${nullIndex.toString()} 行必填项`)
                                // const arr = ['询价行信息：请完善第', nullIndex.toString(), '行必填项']
                                // const str = arr.join(' ')
                                // Modal.warning({
                                //     title: '提示',
                                //     okText: '确认',
                                //     content: str
                                // })
                                reject()
                            }
                        }
                        resolve()
                    })
                } else {
                    if (group.type == 'grid') {
                        return that.$refs.editPage.$refs[group.custom.ref][0].validate(true)
                    } else {
                        return that.$refs.editPage.$refs[group.groupCode][0].validate()
                    }
                }
            })
            return promise
        },
        getPageData () {
            const that = this
            let params = {...this.form}
            // 没有模板时，赋值默认参数
            if (!this.voucherId) {
                params = Object.assign(params, this.currentEditRow)
            }
            this.pageData.groups.forEach(group => {
                if(group.type == 'grid') {
                    let ref = group.custom.ref
                    params[ref] = that.$refs.editPage.$refs[ref] && that.$refs.editPage.$refs[ref][0] && that.$refs.editPage.$refs[ref][0].getTableData().fullData
                }
            })
            return params
        },
        postData (callback) {
            let params = this.getPageData()
            let url =  this.url.public
            this.$refs.editPage.confirmLoading = true
            postAction(url, params).then(res => {
                const msgType = res.success ? 'success' : 'error'
                let resMsg = res.message;
                if(!!resMsg && resMsg.indexOf("\n") >= 0) {
                    const h = this.$createElement;
                    let strList = resMsg.split("\n");
                    strList = strList.map((str, strIndex) => {
                        return h(strIndex === 0? 'span' : 'div',null,str);
                    })
                    resMsg = h('span', null, strList)
                }
                this.$message[msgType](resMsg)
                if (res.success && this.refresh) {
                    this.queryDetail()
                }
                if (res.success) {
                    this.goBack()
                } else {
                    return callback && callback(params, this)
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            let pageData = this.$refs.editPage.getPageData()
            let saleEnquiryItemList = pageData.saleEnquiryItemList || []
            let materialMap = saleEnquiryItemList.reduce((acc, obj) => {
                acc[obj.itemNumber] = obj.materialNumber+'_'+ obj.materialName
                return acc
            }, {})
            result.forEach(item => {
                let number = item.itemNumber
                if (number && materialMap[number] && !item.materialName) {
                    item.materialNumber = materialMap[number].split('_')[0]
                    item.materialName = materialMap[number].split('_')[1]
                }
            })
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            //如果删除的数据有和登录人账号不一致的
            if(user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
                return
            }

            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            getAction('/attachment/saleAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        // 批量删除
        deleteBatch () {
            let arr = []
            let delArr = []
            //大B
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if(checkboxRecords && checkboxRecords.length>0){
                checkboxRecords.forEach(row=> {
                    //当前登录人等账号于查询的账号 大B
                    if (user == row.uploadElsAccount) {
                        if( subAccount==row.uploadSubAccount){
                            delArr.push(row)
                        }else{
                            arr.push(row.fileName)
                        }
                    }else{
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if(arr && arr.length>0){
                    let str = arr.join(',')
                    this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') +str+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        setLadder (row){
            if(!row.taxCode){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterTaxCode`, '请先选择税码！') )
                return
            }
            this.currentRow = row
            let head = this.$refs.editPage.getPageData()
            this.$refs.ladderPage.open(row, head)
        },
        setLadderCallBack (itemList){
            this.currentRow['ladderPriceJson'] = JSON.stringify(itemList)
            const current = this.getQuantityPrice(this.currentRow)
            const { price, netPrice } = current
            let requireQuantity = this.currentRow.requireQuantity || 1
            this.currentRow['price'] = price // 含税价
            this.currentRow['netPrice'] = netPrice // 未税价
            this.currentRow['taxAmount'] = (price * requireQuantity).toFixed(6)
            this.currentRow['netAmount'] = (netPrice * requireQuantity).toFixed(6)
        },
        // 根据当前询价行 需求数量获取阶梯报价应得报价行
        getQuantityPrice (row) {
            const json = JSON.parse(row.ladderPriceJson)
            const quantityList = json.map((item) => {
                return Number(item.ladderQuantity)
            })
            const requireQuantity = row.requireQuantity || quantityList[quantityList.length - 1]
            quantityList.push(requireQuantity)
            quantityList.sort(function (a, b) {
                return a - b
            })
            const indexs = []
            quantityList.forEach((i, index) => {
                if (i === Number(requireQuantity)) {
                    indexs.push(index)
                }
            })
            const index = indexs.pop()
            const current = json[index === 0 ? index : index - 1]

            return current
        },
        openCost (row){
            if(!row.taxCode){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterTaxCode`, '请先选择税码！') )
                return
            }
            this.currentRow = row
            let costJson = JSON.parse(row.costFormJson)
            costJson['itemId'] = row.id
            this.costEditRow = costJson
            let data = costJson['data'] || {}
            let endTimeQue = row.quoteEndTime > formatDate(new Date().getTime(), 'yyyy-MM-dd hh:mm:ss')
            // 单 1 报价中、2 已报价、8 重报价 且 未到报价截止日期
            endTimeQue = endTimeQue && ['1', '2', '8'].includes(this.currentEditRow.enquiryStatus)
            // 1 报价中、8 重报价、未到报价截止日期
            let containerStatus = row.itemStatus == '1' || row.itemStatus == '8' || endTimeQue ? 'edit' : 'detail'
            this.$refs.costform.open(data, containerStatus)
        },
        costCallBack (extendAllData){
            const params = this.$refs.editPage.getPageData()
            //报价项 1:不含税价;0:含税价
            let quoteType = params.quoteType == '1' ? 'netPrice' : 'price'
            let costJson = JSON.parse(this.currentRow['costFormJson'])
            const allData = extendAllData.allData || []
            costJson['data'] = allData
            //分组信息
            let costGroups = []
            let costPrice = 0
            extendAllData.pageConfig.groups.forEach(group => {
                let costGroup = {}
                costGroup['groupCode'] = group['groupCode']
                costGroup['groupName'] = group['groupName']
                costGroup['groupType'] = group['groupType']
                costGroup['totalValue'] = group['total']['totalValue'] || 0
                costGroup[quoteType] = costGroup['totalValue']
                if (params.quoteType == '1') {
                    let price = (costGroup[quoteType] * (1 + parseFloat(this.currentRow.taxRate)/100)).toFixed(6)
                    costGroup['price'] = price
                } else {
                    let netPrice = (costGroup[quoteType] / (1 + parseFloat(this.currentRow.taxRate)/100)).toFixed(6)
                    costGroup['netPrice'] = netPrice
                }
                costGroups.push(costGroup)
                costPrice += costGroup[quoteType]
            })
            costJson['groups'] = costGroups
            this.currentRow['costFormJson']=JSON.stringify(costJson)
            this.currentRow[quoteType] = costPrice

            // 分别计算行内含税或未税单价，以及含税、未税总额
            if (params.quoteType == '1') {
                let price = (this.currentRow[quoteType] * (1 + parseFloat(this.currentRow.taxRate)/100)).toFixed(6)
                this.currentRow['price'] = price
            } else {
                let netPrice = (this.currentRow[quoteType] / (1 + parseFloat(this.currentRow.taxRate)/100)).toFixed(6)
                this.currentRow['netPrice'] = netPrice
            }
            this.currentRow['taxAmount'] = (this.currentRow.price * this.currentRow.requireQuantity).toFixed(6)
            this.currentRow['netAmount'] = (this.currentRow.netPrice * this.currentRow.requireQuantity).toFixed(6)
        },
        showConfirmButton (){
            let editPage = this.$refs.editPage
            if(editPage){
                const { quoteConfirmFlag } = editPage.getPageData() || {}
                return quoteConfirmFlag === '0'
            }
        },
        showButton (){
            let editPage = this.$refs.editPage
            if(editPage){
                const { enquiryStatus } = editPage.getPageData() || {}
                //报价中 || 未报价 || 议价中 || 已悔标
                return enquiryStatus == '1' || enquiryStatus === '3' || enquiryStatus == '7' || enquiryStatus == '11'
            }
        },
        showExcelButton () {
            let editPage = this.$refs.editPage
            if (editPage) {
                const { enquiryStatus } = editPage.getPageData() || {}
                return enquiryStatus != '2'
            }
            return false
        },
        showUploadButton () {
            let editPage = this.$refs.editPage
            if (editPage) {
                const { enquiryStatus } = editPage.getPageData() || {}
                //报价中 || 未报价 || 议价中 || 已悔标
                return enquiryStatus == '1' || enquiryStatus === '3' || enquiryStatus == '7' || enquiryStatus == '11'
            }
            return false
        },
        // fieldSelectOk(data){
        //     if (!!this.selectedType && this.selectedType.includes('tax')) {
        //         let _X_ROW_KEY = this.selectedType.split('-')[1]
        //         let saleItemGrid = this.$refs.editPage.$refs.saleEnquiryItemList[0]
        //         const { fullData } = saleItemGrid.getTableData()
        //         fullData = fullData.map((item) => {
        //             if (item._X_ROW_KEY === _X_ROW_KEY) {
        //                 item.taxCode = data[0].taxCode || ''
        //                 item.taxRate = data[0].taxCode=='CN007'? 0 : (data[0].taxRate || '')
        //                 function add(a,b){var c,d,e;try{c=a.toString().split(".")[1].length}catch(f){c=0}try{d=b.toString().split(".")[1].length}catch(f){d=0}e=Math.pow(10,Math.max(c,d));return(mul(a,e)+mul(b,e))/e}function sub(a,b){var c,d,e;try{c=a.toString().split(".")[1].length}catch(f){c=0}try{d=b.toString().split(".")[1].length}catch(f){d=0}e=Math.pow(10,Math.max(c,d));return(mul(a,e)-mul(b,e))/e}function mul(a,b){var c=0;var d=a.toString();var e=b.toString();try{c+=d.split(".")[1].length}catch(f){}try{c+=e.split(".")[1].length}catch(f){}return((Number(d.replace(".",""))*Number(e.replace(".","")))/Math.pow(10,c))}function div(a,b){var c=0;var d=0;var e=0;var f=0;try{e=a.toString().split(".")[1].length}catch(g){}try{f=b.toString().split(".")[1].length}catch(g){}c=Number(a.toString().replace(".",""));d=Number(b.toString().replace(".",""));return mul(c/d,Math.pow(10,f-e))};
        //
        //                 if (item.price) {
        //                     let price = item.price;
        //                     let taxRate = item.taxRate;
        //                     let tax = add(1, div(taxRate, 100));
        //                     // console.log('tax', tax)
        //                     let netPrice = div(price, tax);
        //                     netPrice = netPrice.toFixed(6);
        //                     item.netPrice = netPrice;
        //
        //                     if (item.requireQuantity) {
        //                         let quantity = item.requireQuantity;
        //                         let netAmount = mul(netPrice, quantity);
        //                         netAmount = netAmount.toFixed(2);
        //                         item.netAmount = netAmount;
        //
        //                         let taxAmount = mul(price, quantity);
        //                         taxAmount = taxAmount.toFixed(2);
        //                         item.taxAmount = taxAmount;
        //                     }
        //                 }
        //             }
        //             return item
        //         })
        //         saleCostGrid.reloadData(fullData)
        //     }
        // },
        // cellClickEvent({ row, rowIndex, column, columnIndex }){
        //     if(column.field=='taxCode'){ //税码
        //         let itemGrid = this.$refs.editPage.$refs.saleEnquiryItemList[0]
        //         console.log(itemGrid,'taxCode是否编辑状态', itemGrid.isEditByRow(row))
        //         if (!itemGrid.isEditByRow(row)) return
        //
        //         itemGrid.clearCheckboxRow()
        //         itemGrid.setCheckboxRow([row], true)
        //         this.selectedType = `tax-${row._X_ROW_KEY}`
        //         let url = '/base/tax/queryTaxForTemplate'
        //         let busAccount = this.currentEditRow['busAccount']
        //         let param = {
        //             purchaseAccount:busAccount
        //         }
        //         let columns = [
        //             {field: 'taxCode', title: '税码', with: 150},
        //             {field: 'taxRate', title: '税率（%）', with: 150},
        //             {field: 'taxName', title: '税码名称', with: 150},
        //             {field: 'remark', title: '备注', with: 150}
        //         ]
        //         this.$refs.fieldSelectModal.open(url, param, columns, 'single')
        //     }else{
        //         return;
        //     }
        // }
    }
}
</script>
