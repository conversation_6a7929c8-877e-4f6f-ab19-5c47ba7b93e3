<template>
  <!-- 保证金管理抵扣记录 -->
  <div class="dunning-records-view">
    <ContentHeaderNew :btns="btns"></ContentHeaderNew>
    <div v-if="!showEditPage">
      <a-row style="padding: 10px 15px;">
        <a-col :span="12">
          <label>{{ $srmI18n(`${$getLangAccount()}#i18n_field_RIJW_274265fe`, '关键字：') }}</label>
          <a-input
            style="width: 220px;"
            v-model="tenderName"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNeBLRL_fb7f81a2`, '请输入投标人名称')"></a-input>
        </a-col>
        <a-col
          :span="12"
          style="text-align: right;">
          <a-button
            type="primary"
            icon="search"
            style="margin-right: 10px;"
            @click="searchFun">{{ $srmI18n(`${$getLangAccount()}#i18n_title_search`, '搜索') }}</a-button>
          <a-button
            icon="reload"
            @click="searchFun('0')">{{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}</a-button>
        </a-col>
      </a-row>
      <div class="grid">
        <ListTable 
          ref="listTable"
          :pageData="pageData"
          :url="url"
          :defaultParams="defaultParams"
          :statictableColumns="tableColumns"
          :showTablePage="false">
        </ListTable>
      </div>
    </div>
  </div>
</template>
  
<script>
import ListTable from '../components/listTable'
import ContentHeaderNew from '../components/content-header-new'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
  
export default {
    components: {
        ListTable,
        ContentHeaderNew
    },
    inject: ['tenderCurrentRow', 'subpackageId'],
    data () {
        return {
            showEditPage: false,
            tenderName: '',
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBLRL_9daf066b`, '投标人名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nVAc_2e5f653e`, '抵扣类型'),
                    'field': 'deductType_dictText',
                    enabled: false
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nVHf_2e623bba`, '抵扣金额'),
                    'field': 'deductAmount'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cIL_140065b`, '创建人'),
                    'field': 'createBy'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cIKI_26c42b3d`, '创建时间'),
                    'field': 'createTime'
                }
            ],
            btns: [{title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: () => {this.$router.go(-1)}}],
            url: {
                list: '/tender/supplier/purchaseTenderProjectMarginHead/queryDeductBysubpackageId'
            },
            pageData: {
                form: {
                    keyWord: ''
                }
            }
            
        }
    },
    computed: {
        defaultParams () {
            console.log(this.tenderName)
            return {subpackageId: this.subId}
        },
        subId () {
            return this.subpackageId()
            // return '1532190061912612866'
        }
    },
    methods: {
        searchFun (type) {
            this.tenderName = type == '0' ? '' : this.tenderName
            this.pageData.form.keyWord = this.tenderName
            this.$nextTick(() => {
                this.$refs.listTable.loadData && this.$refs.listTable.loadData()
            })
              
        }
    }
    // mounted (){
    //     this.pageData.form = {subpackageId: this.subId()}
    // }
}
</script>
  
  <style lang="less" scoped>
  </style>
  
  