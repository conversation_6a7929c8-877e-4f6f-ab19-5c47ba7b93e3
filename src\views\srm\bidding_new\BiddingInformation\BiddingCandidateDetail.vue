<template>
  <div class="BiddingCandidateDetail">
    <a-spin :spinning="confirmLoading">
      <div class="head">
        <span>{{ dataObj.noticeTitle }}</span>
      </div>
      <div class="titlp">
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_creator`, '创建者') }}：{{ dataObj.createBy }}</span>|<span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_publishTime`, '发布时间') }}：{{ dataObj.updateTime }}</span>
      </div>
      <div class="content">
        <p v-html="dataObj.noticeContent"></p>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
    data () {
        return {
            confirmLoading: false,
            dataObj: {
                noticeTitle: '',
                updateTime: '',
                noticeContent: '',
                noticeType: '',
                createBy: ''
            },
            params: {}
        }
    },
    created () {
        const queryData = this.$route.query
        this.params = queryData
    },
    mounted () {
        this.getInfo()
    },
    methods: {
        async getInfo () { // 获取招标信息详情
            if (!this.params && !this.params.id) {
                return false
            }
            const url = `/inquiry/public/noToken/queryById/${this.params.businessId}/${this.params.businessType}`
            this.confirmLoading = true
            const res = await getAction(url)
            if (res.code == 200) {
                const {result} = res
                result && ((result) => {
                    console.log(result)
                    const {publicityTitle, publicityBeginTime, publicityContent, createBy} = result
                    this.dataObj = {
                        noticeTitle: publicityTitle,
                        updateTime: publicityBeginTime,
                        noticeContent: publicityContent,
                        createBy: createBy
                    }
                })(result)
            } else {
                this.$message.error(res.message)
            }
            this.confirmLoading = false
        }
    }
}
</script>

<style lang="less" scoped>
.BiddingCandidateDetail{
  height: 100%;

  .head {
    text-align: center;
    height: 60px;
    line-height: 60px;
    font-size: 18px;
    font-weight: 600;
  }
  .titlp {
    color: blue;
    text-align: center;
    margin-bottom: 20px;
    font-size: 14px;

    span:nth-child(1) {
      margin-right: 20px;
    }
    span:last-child {
      margin-left: 20px;
    }
  }
  .content {
    height: calc(100% - 102px);
    overflow-y: auto;
  }
}
</style>