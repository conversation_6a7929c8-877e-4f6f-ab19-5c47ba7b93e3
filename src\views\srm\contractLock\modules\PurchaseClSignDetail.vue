<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
    <!-- 加载配置文件 -->
    <field-select-modal 
      ref="fieldSelectModal" />
    <a-modal
      v-drag    
      v-model="visible"
      title="驳回"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item label="驳回原因">
          <a-input
            v-model="form.reason"
            type="reason" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
export default {
    name: 'EsignFlowAdd',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            labelCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            wrapperCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            visible: false,
            form: {
                reason: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAy_24c6a9a8`, '业务编号'),
                                    fieldName: 'busNumber',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'subject',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QIKQXVAZd_18c96370`, '文件是否上传契约锁'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjXKI_ef65ec11`, '签署有效时间'),
                                    fieldName: 'expireTime',
                                    dataFormat: 'YYYY-MM-DD',
                                    disabled: true
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWsRKI_fa482f8c`, '签署终止时间'),
                                    fieldName: 'endTime',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRCTPMVQI_6b397fa7`, '是否供方需要回传文件'),
                                    fieldName: 'needCheck',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_gwarjw3QgSxzQUiQ`, '签署完成是否自动发送'),
                                    fieldName: 'autoSend',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isOpen`, '是否已开启'),
                                    fieldName: 'initiate',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isFile`, '是否已归档'),
                                    fieldName: 'archiving',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                                    fieldName: 'fileType',
                                    dictCode: 'contractLockFileType',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    disabled: true
                                }
                            ]
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'), groupCode: 'purchaseSignersList', type: 'grid', custom: {
                        ref: 'purchaseSigners',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'tenantTypeArr', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`,
                                this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型')),
                            width: 120, editRender: {
                                name: '$select',
                                props: { multiple: true, clearable: true },
                                options: [
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RC_a300c`, '公司'), value: 'COMPANY'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: 'PERSONAL'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPersonName`, '法人'), value: 'LP'}
                                ]
                            }
                            },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'signatoryName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWLcR_75c3ca0`, '签署人姓名'), width: 120 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'signTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWKI_3a0e4572`, '签署时间'), width: 120 }

                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'uploaded', title: '是否上传契约锁', width: 180, visible: false },
                            { field: 'uploaded_dictText', title: '是否上传契约锁', width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 220, align: 'center', slots: { default: 'grid_opration' } }

                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载'), clickFn: this.downloadEvent },
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_by6FtppjfaHaxK4d`, '签署文件下载'), clickFn: this.flowFileDownload, allow: this.allowDownload },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_nKZbyZGNSutnhk7s`, '供应商回传文件'), groupCode: 'signRetrunAttachments', type: 'grid', custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            { ...new BatchDownloadBtn().btnConfig }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'), type: 'primary', click: this.confirmEvent, showCondition: this.showBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'), type: 'primary', click: this.rejectEvent, showCondition: this.showBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/contractLock/elsClContract/queryById',
                //原文件下载链接
                download: '/attachment/purchaseAttachment/download',
                //签署文件下载链接
                esignFIledownload: '/esign/purchaseEsignAttachment/esignFiledownload',
                //回传文件下载链接
                returnFileDownload: '/esign/purchaseSignReturnAttachment/download',
                //确认
                confirm: '/contractLock/elsClContract/confirm',
                //驳回
                reject: '/contractLock/elsClContract/reject',
                getSignature: '/attachment/purchaseAttachment/getSignature'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        },
        showBtn (){
            let data = this.currentEditRow
            if(data.returnSignedFile==='1' && data.reject!=='1' && data.returnFileConfirm!=='1'){
                return true
            }
            return false
        },
        allowDownload (row){
            if(this.currentEditRow.contractStatus == 'COMPLETE'){
                return false
            }
            if(row.signFilePath){
                return false
            }
            return true
        },
        downloadEvent (row) {
            let str = row.absoluteFilePath
            console.log(str)
            const fileName = str.substr(str.lastIndexOf('/')+1, str.length)
            getAction(str, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        flowFileDownload (row){
            console.log(row)
            let str = row.signFilePath
            if(str){
                let fileName = row.fileName
                let arr =  fileName.split('.')
                fileName = arr[0] + '.pdf'
                getAction(str, {}, {
                    responseType: 'blob'
                }).then(res => {
                    let url = window.URL.createObjectURL(new Blob([res]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', fileName)
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                })
            }else {
                let fileName = row.fileName
                let arr =  fileName.split('.')
                fileName = arr[0] + '.pdf'
                console.log(arr)
                getAction('/contractLock/elsClContract/downSignFileByDocumentId', {
                    'fileName': fileName,
                    'id': this.currentEditRow.id,
                    'documentId': row.fileId
                }, {
                    responseType: 'blob'
                }).then(res => {
                    let url = window.URL.createObjectURL(new Blob([res]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', fileName)
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                })
            }


        },
        preViewEvent (row){
            let preViewFile = row
            console.log('row:', row)
            postAction(this.url.getSignature, {id: preViewFile.id}).then(res => {
                if(res.success){
                    this.$previewFile.open({params: preViewFile, path: res.message})
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        downloadReturnFileEvent (row){
            this.downloadFile(row, this.url.returnFileDownload, row.fileName)
        },
        downloadFile (data, url, fileName){
            let id = data.id
            const params = {
                id
            }
            getAction(url, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        rejectEvent (){
            this.visible = true
        },
        handleOk (){
            const params = {id: this.currentEditRow.id, rejectReason: this.form.reason}
            getAction('/contractLock/elsClContract/reject', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.goBack()
                }
            })
        },
        confirmEvent (){
            const params = this.currentEditRow
            getAction('/contractLock/elsClContract/confirm', {id: params.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.goBack()
                }
            })
        }
    }
}
</script>