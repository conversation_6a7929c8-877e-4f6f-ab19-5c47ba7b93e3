<template>
  <div v-if="ifshow">
    <titleCrtl>
      {{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息')) }}
    </titleCrtl>
    <list-table
      ref="bidEvaSupplierRecordList"
      :statictableColumns="statictableColumns"
      setGridHeight="500"
      :fromSourceData="formData.bidEvaSupplierRecordList"
      :showTablePage="false"> </list-table>
  </div>
</template>
<script lang="jsx">
import listTable from '../../components/listTable'
import titleCrtl from '../../components/title-crtl'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'

export default {
    mixins: [baseMixins],
    props: {
        formData: {
            default: () => {},
            type: Object
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    components: {
        listTable,
        titleCrtl
    },
    data () {
        return {
            forbiddenCandidate: false,
            statictableColumns: [
                // let attachmentFileType = this.checkType == '0' ? 'preAttachmentFileType' : 'attachmentFileType'

                // { type: 'checkbox', width: 40, fixed: 'left' },
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                    groupCode: 'bidEvaSupplierRecordList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                    fieldLabelI18nKey: '',
                    field: 'supplierName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'bidEvaSupplierRecordList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQuB_2fbaf75f`, '是否废标'),
                    fieldLabelI18nKey: '',
                    field: 'invalid',
                    fieldType: 'select',
                    dictCode: 'yn',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    bindFunction: (row) => {
                        console.log('row', row)
                        row.forbiddenCandidate = row.invalid == '1' ? true : false
                            
                        
                        this.$set(this.formData, 'stageQuoteOperator', '')
                    }
                },
                // 此处放插槽，放置文件

                {
                    groupCode: 'bidEvaSupplierRecordList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                    fieldLabelI18nKey: '',
                    field: 'quote',
                    align: 'center',
                    fieldType: 'input',
                    headerAlign: 'center',
                    defaultValue: '',
                    required: 1
                },
                {
                    groupCode: 'bidEvaSupplierRecordList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBu_2199294`, ' 评标价'),
                    fieldLabelI18nKey: '',
                    field: 'evaPrice',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    required: 1
                },
                {
                    groupCode: 'bidEvaSupplierRecordList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Mksz_32ad7d23`, '汇总得分'),
                    fieldLabelI18nKey: '',
                    field: 'totalScore',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    required: 1
                },
                {
                    groupCode: 'bidEvaSupplierRecordList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    fieldLabelI18nKey: '',
                    field: 'orderBy',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'bidEvaSupplierRecordList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQYI_2fbbaebf`, '是否推荐'),
                    field: 'candidate',
                    width: '120',
                    slots: {
                        default: ({ row, column }) => {
                            return [
                                // <a-checkbox disabled={this.pageStatus == 'detail'}v-model={row[column.property]} ></a-checkbox>
                                <vxe-checkbox disabled={this.formData.evaStatus == '1' || row.forbiddenCandidate} v-model={row[column.property]} checked-value="1" unchecked-value="0"></vxe-checkbox>
                            ]
                        }
                    }
                }
            ],
            ifshow: false
        }
    },
    methods: {},
    mounted () {
        this.formData.bidEvaSupplierRecordList ||= []
        // console.log(this.currentNode().extend.checkType)
        if (this.currentNode().extend.checkType == '0') {
            this.statictableColumns = this.statictableColumns.filter((item) => {
                console.log(item.field != 'quote' || item.field != 'evaPrice')

                return !(item.field == 'quote' || item.field == 'evaPrice')
            })
        }
        this.ifshow = true
    }
}
</script>
<style lang="less" scoped></style>
