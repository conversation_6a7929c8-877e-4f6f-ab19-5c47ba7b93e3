<template>
  <div class="jc-component__range">
    <div 
      class="jc-range"
      :class="rangeStatus?'success':''" >
      <i 
        @mousedown="rangeMove"
        @touchstart="mobileRangeMove"
        :class="['iconfont', rangeStatus ? successIcon : startIcon]"></i>
      {{ rangeStatus?successText:startText }}
    </div>
  </div>
</template>
<script>
import { srmI18n, getLangAccount } from '@/utils/util'
export default {
    props: {
        // 成功之后的函数
        successFun: {
            type: Function
        },
        //成功图标
        successIcon: {
            type: String,
            default: 'icon-select'
        },
        //成功文字
        successText: {
            type: String,
            default: srmI18n(`${getLangAccount()}#i18n_alert_OiLR_48599c04`, '验证成功')
        },
        //开始的图标
        startIcon: {
            type: String,
            default: 'icon-arrow-double-right'
        },
        //开始的文字
        startText: {
            type: String,
            default: srmI18n(`${getLangAccount()}#i18n_btn_VdjEOEQ_53090696`, '请向右滑动滑块')
        },
        //失败之后的函数
        errorFun: {
            type: Function
        },
        //或者用值来进行监听
        status: {
            type: String
        }
    },
    data (){
        return {
            disX: 0,
            rangeStatus: false
        }
    },
    methods: {
        //滑块移动
        rangeMove (e){
            let ele = e.target
            // if (ele && ele.localName != 'i') {
            //     ele = ele.
            // }
            let startX = e.clientX
            let eleWidth = ele.offsetWidth
            let parentWidth =  ele.parentElement.offsetWidth
            let MaxX = parentWidth - eleWidth
            if(this.rangeStatus){//不运行
                return false
            }
            document.onmousemove = (e) => {
                let endX = e.clientX
                this.disX = endX - startX
                if(this.disX<=0){
                    this.disX = 0
                }
                if(this.disX>=MaxX-eleWidth){//减去滑块的宽度,体验效果更好
                    this.disX = MaxX
                }
                ele.style.transition = '.1s all'
                ele.style.transform = 'translateX('+this.disX+'px)'
                e.preventDefault()
            }
            document.onmouseup = ()=> {
                if(this.disX !== MaxX){
                    ele.style.transition = '.5s all'
                    ele.style.transform = 'translateX(0)'
                    //执行成功的函数
                    this.errorFun && this.errorFun()
                }else{
                    this.rangeStatus = true
                    if(this.status){
                        this.$parent[this.status] = true
                    }
                    //执行成功的函数
                    this.successFun && this.successFun()
                }
                document.onmousemove = null
                document.onmouseup = null
            }
        },
        mobileRangeMove (e){
            let ele = e.target
            let startX = e.touches[0].pageX
            let eleWidth = ele.offsetWidth
            let parentWidth =  ele.parentElement.offsetWidth
            let MaxX = parentWidth - eleWidth
            if(this.rangeStatus){//不运行
                return false
            }
            document.ontouchmove = (e) => {
                let endX = e.touches[0].pageX
                this.disX = endX - startX
                if(this.disX<=0){
                    this.disX = 0
                }
                if(this.disX>=MaxX-eleWidth){//减去滑块的宽度,体验效果更好
                    this.disX = MaxX
                }
                ele.style.transition = '.1s all'
                ele.style.transform = 'translateX('+this.disX+'px)'
                e.preventDefault()
            }
            document.ontouchend = ()=> {
                if(this.disX !== MaxX){
                    ele.style.transition = '.5s all'
                    ele.style.transform = 'translateX(0)'
                    //执行成功的函数
                    this.errorFun && this.errorFun()
                }else{
                    this.rangeStatus = true
                    if(this.status){
                        this.$parent[this.status] = true
                    }
                    //执行成功的函数
                    this.successFun && this.successFun()
                }
                document.ontouchmove = null
                document.ontouchend = null
            }
        }
    }
}
</script>
<style lang="less" scoped>
@import '~@/assets/less/font.less';

@mixin jc-flex{
    display: flex;
    justify-content:center;
    align-items: center
}
.jc-component__range{
    .jc-range{
        background-color: #e9e9e9;
        position: relative;
        transition: 1s all;
        user-select: none;
        color: #585858;
        @include jc-flex;
        height: 42px/*no*/;
        text-align: center;
        &.success{
            background-color: #3bc923;
            color: #fff;
            i{
                color: #3bc923;
            }
        }
        i{
            position: absolute;
            left: 0;
            width: 42px/*no*/;
            height: 100%;
            color: #C7CBD2;
            background-color: #fff;
            border: 1px solid #d8d8d8;
            cursor: pointer;
            font-size: 24px;
            @include jc-flex;
        }
    }
}
</style>