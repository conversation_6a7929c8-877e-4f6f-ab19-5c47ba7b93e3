<template>
  <a-modal
    v-drag
    forceRender
    :visible="madalVisible"
    :title="modalTitle"
    :width="1146"
    :current-edit-row="currentEditRow"
    @cancel="cancelEvent"
    @ok="editOk">
    <a-spin :spinning="confirmLoading">
      <a-form-model
        :model="form"
        layout="inline">
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_projectType`, '项目类型')">
          <m-select
            style="width:200px"
            v-model="form.itemType"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectItemType`, '请选择项目类型')"
            dict-code="srmItemType"/>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_SaleMassProdHeadList_projectName`, '项目名称')">
          <a-input
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterProjectName`,'请输入项目名称')"
            v-model="form.itemName"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button
            type="primary"
            @click="showParam">{{ $srmI18n(`${$getLangAccount()}#i18n_title_viewParams`, '查看参数') }}
          </a-button>
        </a-form-model-item>
      </a-form-model>
      <a-form-model
        :model="form">
        <a-form-model-item
          label="">
          <j-editor
            ref="jEditor"
            v-if="madalVisible"
            v-model="form.itemContent"
            v-bind="{plugins,coustomInit}"
          ></j-editor>
        </a-form-model-item>
      </a-form-model>
      <Query-Param-Modal
        @ok="okHandle"
        ref="queryParamModal"/>
    </a-spin>
  </a-modal>
</template>
<script>
import JEditor from '@comp/els/JEditor'
import {httpAction} from '@/api/manage'
import QueryParamModal from './QueryPurchaseContractParamModal'

export default {
    name: 'SetLadderModal',
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    components: {
        JEditor,
        QueryParamModal
    },
    data () {
        return {
            modalTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_newProject`, '新建项目'),
            confirmLoading: false,
            madalVisible: false,
            form: {
                itemContent: '', id: '', itemName: '', itemType: '3', itemVersion: ''
            },
            plugins: 'lists image media table wordcount fullscreen code powerpaste',
            coustomInit: {}
        }
    },
    mounted () {
    },
    methods: {
        okHandle (row) {
            const content = row.paramContent || ''
            this.$refs.jEditor.insertContent(content)
        },
        open (row) {
            this.madalVisible = true
            if (row) {
                this.modalTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editProject`, '编辑项目')
                this.form = row
            } else {
                this.modalTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_newProject`, '新建项目')
                this.form = {itemContent: '', id: '', itemName: '', itemType: '3', itemVersion: ''}
            }
        },
        goBack () {
            this.$emit('hide')
        },
        showParam () {
            this.$refs.queryParamModal.open()
        },
        editOk () {
            let that = this
            if (that.form.itemContent == '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CcxOLVW_60c5e69d`, '内容不能为空'))
                return false
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmtoSave`, '确认保存'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmtoSave`, '是否确认保存?'),
                onOk: function () {
                    that.postData()
                }
            })
        },
        postData () {
            let that = this
            let url = '/contract/purchaseContractLibrary/add'
            if (that.form.id && that.form.id != '') {
                url = '/contract/purchaseContractLibrary/edit'
            }
            that.confirmLoading = true
            httpAction(url, that.form, 'post').then((res) => {
                if (res.success) {
                    that.$message.success(res.message)
                    that.madalVisible = false
                    that.$emit('ok')
                    that.goBack()
                    that.$parent.$refs.listPage.handleQuery()
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
                that.confirmLoading = false
            })
        },
        cancelEvent () {
            this.madalVisible = false
        }
    }
}
</script>
