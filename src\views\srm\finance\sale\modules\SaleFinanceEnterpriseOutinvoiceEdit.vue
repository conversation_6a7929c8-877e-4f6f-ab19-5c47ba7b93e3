<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :pageFooterButtons="pageFooterButtons"
        :externalToolBar="externalToolBar"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { composePromise } from '@/utils/util.js'
import { getAction, postAction } from '@/api/manage'
import {
    BUTTON_SAVE,
    BUTTON_BACK,
    BUTTON_PUBLISH
} from '@/utils/constant.js'
export default {
    name: 'SaleFinanceEnterpriseOutinvoiceEdit',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            externalToolBar: {
            },
            pageFooterButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/finance/financeEnterpriseOutinvoice/saleToEdit'
                    },
                    click: this.saveEvent
                },
                BUTTON_BACK
            ],
            requestData: {
                detail: {
                    url: '/finance/financeEnterpriseOutinvoice/saleQueryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            url: {
                add: '/finance/financeEnterpriseOutinvoice/saleToAdd',
                edit: '/finance/financeEnterpriseOutinvoice/saleToEdit',
                detail: '/finance/financeEnterpriseOutinvoice/saleQueryById'
            }
        }
    },
    methods: {
        saveEvent (args) {
            if(!this.currentEditRow.id) args.btn.args.url='/finance/financeEnterpriseOutinvoice/saleToAdd'
            console.log(args, 'args====')
            const allData = this.getAllData()
            this.stepValidate(args).then(()=>{
                // //判断组织中是否存在相同值（判断条件：组织编码+准入类别）
                // let orgList = supplierOrgInfoList
                // if(orgList && orgList.length>0){
                //     let map = new Map()
                //     for(let sub of orgList){
                //         if(map.get(sub.orgCode) && map.get(sub.orgCode)===sub.accessCategory){
                //             this.$message.warning('组织信息中存在同一组织的同一准入品类：'+sub.orgCode+'---'+sub.accessCategory+' ,不能进行保存')
                //             return
                //         }
                //         map.set(sub.orgCode, sub.accessCategory)
                //     }
                //
                // }
                this.composeBusinessSave(args, allData)
            })
        },
        // 重写保存方法
        composeBusinessSave (args, allData) {
            let hasId = !!allData.id
            let flag = !hasId && this.$refs[this.businessRefName].isNeedJudge
            let steps = flag ? [this.stepBusinessAdd] : [this.stepBusinessSave]
            const handleCompose = composePromise(...steps)

            this.confirmLoading = true
            handleCompose({ ...args, allData })
                .then(res => {
                    console.log('all save success', res)
                }, err => {
                    console.log('all save error', err)
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.$refs[this.businessRefName] && this.$refs[this.businessRefName].queryDetail()
                })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_basicData`, '基础信息'),
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'),
                        fieldName: 'elsAccount',
                        disabled: true,
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'),
                        fieldName: 'enterpriseName',
                        disabled: true,
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxNumber`, '纳税人识别号'),
                        fieldName: 'taxpayerRegNumber',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxNumber`, '纳税人识别号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regLocation`, '注册地址'),
                        fieldName: 'registerAddress',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regLocation`, '注册地址')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_diCE_32c265ec`, '注册电话'),
                        fieldName: 'registerTelephone',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_diCE_32c265ec`, '注册电话'),
                        disabled: false
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vDWE_2cb2838d`, '开户银行'),
                        fieldName: 'depositBank',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vDWE_2cb2838d`, '开户银行')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankAccount`, '银行账号'),
                        fieldName: 'bankAccount',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankAccount`, '银行账号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BnL_15b374f`, '复核人'),
                        fieldName: 'reviewer',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BnL_15b374f`, '复核人')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lVL_1893af2`, '收款人'),
                        fieldName: 'payee',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lVL_1893af2`, '收款人')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vPj_173a6f0`, '开票员'),
                        fieldName: 'drawer',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vPj_173a6f0`, '开票员')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'),
                        fieldName: 'email',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱')
                    }
                ],
                itemColumns: [
                ]
            }
        },

        // 返回操作
        goBack () {
            this.$emit('hide')
        }
    }
}
</script>