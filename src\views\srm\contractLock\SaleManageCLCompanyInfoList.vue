<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <SaleManageClCompanyInfoEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleManageClCompanyInfoDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <SaleManageClCompanyInfoAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <SaleEmployeeManagement
      v-if="employeeManagementVisible"
      :companyName="currentCompanyName"
      :row="companyRow"
      :visible="employeeManagementVisible"
      @close="employeeManagementVisible=false">
    </SaleEmployeeManagement>
    <a-modal
      v-drag
      centered
      title="默认印章创建/授权"
      :width="400"
      :visible="sealVisible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="sealVisible = false">
      <a-form
        :form="form">
        <a-form-item label="选择印章使用者">
          <a-checkbox-group
            v-model="form.userIds"
          >
            <a-checkbox
              v-for="(item,index) in staff"
              :key="index"
              :value="item.id">
              {{ item.applyUserName }}<span style="color: #4B5869">{{ item.applyContact }}</span>
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>

      </a-form>

    </a-modal>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleManageClCompanyInfoAdd from './modules/SaleManageClCompanyInfoAdd'
import SaleManageClCompanyInfoDetail from './modules/SaleManageClCompanyInfoDetail'
import SaleManageClCompanyInfoEdit from './modules/SaleManageClCompanyInfoEdit'
import { postAction } from '@/api/manage'
import { getAction } from '@/api/manage'
import SaleEmployeeManagement from './modules/SaleEmployeeManagement'

export default {
    mixins: [ListMixin],
    components: {
        SaleManageClCompanyInfoAdd,
        SaleManageClCompanyInfoDetail,
        SaleManageClCompanyInfoEdit,
        SaleEmployeeManagement
    },
    data () {
        return {
            confirmLoading: false,
            staff: [],
            sealVisible: false,
            form: {userIds: []},
            currentCompanyName: '',
            companyRow: {},
            employeeManagementVisible: false,
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                businessType: 'contractLock',
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'contractLock#sale2CompanyInfo:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractLock#sale2CompanyInfo:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'contractLock#sale2CompanyInfo:edit'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), clickFn: this.handleCertification, allow: this.allowCertification, authorityCode: 'contractLock#sale2CompanyInfo:submitCertification'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLizE_25c88b41`, '刷新认证状态'), clickFn: this.handleRefreshStatus, allow: this.allowRefreshStatus, authorityCode: 'contractLock#sale2CompanyInfo:refreshStatus'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_impower`, '授权'), clickFn: this.getAuth, allow: this.allowAuthStatus, authorityCode: 'contractLock#sale2CompanyInfo:auth'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CLWeRv_e5dd67c1`, '默认印章管理'), clickFn: this.generateDefaultSeal, allow: this.allowAuthStatus, authorityCode: 'contractLock#saleCompanyInfo:defaultSealAdmin'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_jRRv_27c711d2`, '员工管理'), clickFn: this.handleEmployeeManagement, allow: this.allowEmployManagement, authorityCode: 'contractLock#sale2CompanyInfo:staffManage' },
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'contractLock#sale2CompanyInfo:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/contractLock/saleCLCompanyInfo/list',
                delete: '/contractLock/saleCLCompanyInfo/delete',
                deleteBatch: '/contractLock/saleCLCompanyInfo/deleteBatch',
                exportXlsUrl: 'contractLock/saleCLCompanyInfo/exportXls',
                importExcelUrl: 'contractLock/saleCLCompanyInfo/importExcel',
                columns: 'SaleCLCompanyInfo',
                auth: '/contractLock/saleCLCompanyInfo/submitCertification',
                refresh: '/contractLock/saleCLCompanyInfo/getCertificationInfo',
                getAuthPage: '/contractLock/saleCLCompanyInfo/getAuthPage'
            }
        }
    },
    methods: {
        selectedOk (){
            this.confirmLoading = true
            if(!this.form || !this.form.userIds || this.form.userIds.length == 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWeKjN_746f5c55`, '请选择印章使用者'))
                this.confirmLoading = false
                return
            }
            postAction('/contractLock/saleCLCompanyInfo/generateDefaultSeal', this.form).then(res=>{
                if(res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                }else {
                    this.$message.warning(res.message)
                }
            }).finally(
                this.confirmLoading = false
            )
        },
        generateDefaultSeal (row){
            this.sealVisible = true
            let param= {id: row.id}
            this.staff = []
            this.form.userIds = []
            getAction('/contractLock/saleCLCompanyInfo/defaultSeal', param).then((res)=> {
                if (res && res.success) {
                    console.log(res)
                    res.result.salePsns.forEach(r=>{
                        this.staff.push(r)
                    })
                    if(res.result.userIds && res.result.userIds.length >0){
                        res.result.userIds.forEach(r=>{
                            this.form.userIds.push(r)
                        })
                    }
                    this.form.id = row.id
                } else {
                    this.$message.error(res.message)
                }
            })

        },
        //已认证不能被编辑
        allowEdit (row){
            if(row.certificationStatus == '2'){
                return true
            }
            return false
        },
        allowEmployManagement (row){
            if(!row.companyId){
                return true
            }
            return false
        },
        allowCertification (row){
            if(row.certificationStatus == '2'){
                return true
            }
            return false
        },
        allowRefreshStatus (row) {
            if(row.certificationStatus == '2'){
                return true
            }
            return false
        },
        allowAuthStatus (row) {
            if(row.certificationStatus == '2'){
                return false
            }
            return true
        },
        //已经创建e签宝的账号的不能删除
        allowDelete (row){
            if(row.companyId){
                return true
            }
            return false
        },
        handleAdd () {
            this.showAddPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleEmployeeManagement (row) {
            this.currentCompanyName = row.companyName
            this.companyRow = row
            this.employeeManagementVisible = true
        },
        handleCertification (row) {
            let url = this.url.auth
            postAction(url, row).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                window.open(res.result.certificationPageUrl)
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        handleRefreshStatus (row) {
            getAction(this.url.refresh, {id: row.id}).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                }else {
                    this.$message.warning(res.message)
                }
                // 刷新列表
                this.$refs.listPage.loadData()
            })
        },
        getAuth (row) {
            getAction(this.url.getAuthPage, {id: row.id}).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    window.open(res.result.authPageUrl)
                }else {
                    this.$message.warning(res.message)
                }
                // 刷新列表
                this.$refs.listPage.loadData()
            })
        }
    }
}
</script>