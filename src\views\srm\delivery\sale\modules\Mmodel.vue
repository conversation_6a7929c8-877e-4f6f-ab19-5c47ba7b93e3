<template>
  <a-modal
    v-drag    
    v-model="madalVisible"
    centered
    :width="1000"
    title="拒绝列表数据"
    @ok="setLadderOk">
    <a-spin :spinning="confirmLoading">
      <div class="compare-page-container">
        <div class="page-content-right">
          <div style="margin-bottom: 12px">
            <vxe-grid
              border
              ref="headerGrid"
              row-id="row_id"
              auto-resize
              size="small"
              height="350"
              :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderInfo`, '发送列表数据')"
              :columns="tableHeaderColumn"
              :data="tableHeaderData"
              :edit-config="{trigger: 'click', mode: 'cell'}"
            >  
              <template
                slot="operate"
                slot-scope="text">      
                <p
                  class="editStyle"
                  :class="{deleteBtn:text.row.id == ''}" 
                  @click="splitRowEvent(text.row,text.rowIndex+1)">拆分</p>
                <p 
                  class="editStyle"
                  :class="{deleteBtn:text.row.id !== ''}" 
                  @click="removeRowEvent(text.row,text.rowIndex+1)">删除</p>
              </template>
              <template #sex_edit="{ row }">
                <a-date-picker
                  @change="onChangesafdsa"
                  v-model="row.replyDate"/>
              </template>
              <template #responsibleParty_edit="{ row }">
                <a-select 
                  v-model="row.responsibleParty"
                  placeholder="请选择"
                  :dropdownMatchSelectWidth="false"
                >
                  <a-select-option 
                    v-for="item in responsiblePartyList" 
                    :key="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </template>
              <template #srmRejectReason_edit="{ row }">
                <a-select 
                  v-model="row.responsibleReason"
                  placeholder="请选择"
                  :dropdownMatchSelectWidth="false"
                >
                  <a-select-option 
                    v-for="item in responsibleReasonList" 
                    :key="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </template>
            </vxe-grid>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import {List} from 'ant-design-vue'
import {  getAction, postAction } from '@/api/manage'
export default {
    name: 'SetLadderModal',
    props: {
        tableHeaderData: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            confirmLoading: false,
            listData: [],
            madalVisible: false,
            tableDatas: [],
            tableHeaderColumn: [ ],
            responsiblePartyList: [],
            responsibleReasonList: []
        }
    },
    mounted () {
        this.getListHeader()
        this.getFindDictItems()
    },
    methods: {
        // 拆分
        splitRowEvent (rows, i){
            let rowEdit = JSON.parse(JSON.stringify(rows))
            rowEdit.id =''
            rowEdit.splitSourceId =rows.id
            this.tableHeaderData.splice(i, 0, rowEdit)
        },
        // 移除
        removeRowEvent (rows, i){
            // delete this.tableHeaderData[i-1]
            this.tableHeaderData.splice(i-1, 1)
            console.log(this.tableHeaderData)
        },
        open () {
            this.madalVisible = true
        },
        onChangesafdsa (){

        },
        // 获取表头信息
        getListHeader (){
            let api = '/base/userColumnDefine/queryCurrentUserColumnDefineJson/saleDeliveryNoticeByOrderList'
            getAction(api).then(res=>{
                if(res.code == 200){
                    // res.result.push({ type: 'seq', width: 60 })
                    res.result.push({title: '操作', width: 60, fixed: 'left', align: 'center', slots: { default: 'operate' }} )
                    for( let vals of res.result){
                        vals['field'] = vals.dataIndex
                        if(vals.title == '回复数量'  ||  vals.title == '供方备注'){
                            vals.fixed= 'left'
                            vals.width= '120'
                            vals['editRender'] =  { name: '$input'}
                        }
                        if(vals.title == '责任方' ){
                            vals.fixed= 'left'
                            vals.width= '100'
                            vals['slots']={ default: 'responsibleParty_edit', edit: 'responsibleParty_edit' } 
                        }
                        if(vals.title == '责任方原因' ){
                            vals.fixed= 'left'
                            vals.width= '100'
                            vals['slots']={ default: 'srmRejectReason_edit', edit: 'srmRejectReason_edit' } 
                        }
                        if(vals.title == '回复日期'){
                            vals.fixed= 'left'
                            vals.width= '150'
                            console.log(vals)
                            vals['slots']={ default: 'sex_edit', edit: 'sex_edit' } 
                        }
                    }
                    console.log(res.result)
                   
                    this.tableHeaderColumn = res.result
                }
            })
        },
        // 获取下拉框 信息
        getFindDictItems (){
            // eslint-disable-next-line no-undef
            postAction('/base/dict/findDictItems', {busAccount: `${this.$getLangAccount()}`, dictCode: 'srmOrderDeliveryResponsible'}).then(res => {
                if(res.success === true){
                    res.result.forEach(item => {
                        let dictData = {}
                        dictData['value'] = item.value
                        dictData['label'] = item.text
                        this.responsiblePartyList.push(dictData)
                    })
                }
            }).finally(() => {
            })
            postAction('/base/dict/findDictItems', {busAccount: `${this.$getLangAccount()}`, dictCode: 'srmRejectReason'}).then(res => {
                if(res.success === true){
                    res.result.forEach(item => {
                        let dictData = {}
                        dictData['value'] = item.value
                        dictData['label'] = item.text
                        this.responsibleReasonList.push(dictData)
                    })
                }
            }).finally(() => {
            })
        },
        getCurrentStyle (current, today) {
            const style = {}
            if (current.date() === 1) {
                style.border = '1px solid #1890ff'
                style.borderRadius = '50%'
            }
            return style
        },
        goBack () {
            this.$emit('hide')
        },
        setLadderOk (){
            let rows = this.$refs.headerGrid.data
            console.log(rows)
            for(let i=0;i<rows.length;i++){
                let item = rows[i]
                if(!item.replyQuantity) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdSeRtVSMMBWRW_4d094986`, '行送货通知单请填写回复数量!'))
                    return
                }
                if(!item.replyDate) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdSeRtVSMMBBA_a7a55976`, '行送货通知单请填写回复日期!'))
                    return
                }
                if(parseFloat(item.replyQuantity) >parseFloat(item.requireQuantity)){
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+ '行送货通知单的回复数量不能大于需求数量!')
                    return
                }
                if(item.id ==''){
                    let num=0
                    // debugger
                    let rowsContent = rows.filter(res=> res.splitSourceId == item.splitSourceId )
                    let rowsContentOld = rows.filter(res=> res.id == rowsContent[0].splitSourceId )
                    rowsContent.push(rowsContentOld[0])
                    rowsContent.map(res => num+= parseFloat(res.replyQuantity))
                    if(parseFloat(num).toFixed(4) !== parseFloat(item.requireQuantity).toFixed(4)){
                        this.$message.error('从第'+(i)+'到'+(i+rowsContent.length-1)+'行的回复数量之和必须要等于需求数量')
                        return
                    }
                }
                if(item.id !=''){
                    let rowsId= rows.filter(res => item.id == res.splitSourceId).length
                    if(rowsId == 0 && parseFloat(item.replyQuantity) !==parseFloat(item.requireQuantity)){
                        this.$message.error('第'+(i+1)+'行的回复数量必须要等于需求数量') 
                        return
                    }
                }
            }
            this.$emit('ok', rows)
            this.madalVisible = false
        }
    }
}
</script>
<style lang="less" scoped>
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 900px;
            background-color: #fff
        }
         .editStyle{
           color:#1890ff;
           cursor: pointer;
        }
        .deleteBtn{
            color: rgba(0, 0, 0, 0.35);
            cursor:no-drop ;
            pointer-events:none
        }
    }
</style>