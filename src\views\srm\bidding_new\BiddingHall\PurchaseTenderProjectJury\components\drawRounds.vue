<template>
  <div>
    <div
      v-for="(item, index) in extractionList"
      :key="index">
      <titleTrtl>
        <span>抽取结果第{{ index+1 }}轮次</span>
        <a-tooltip placement="bottom">
          <template #title>
            <p v-if="item[0].search.sourceLibrary_dictText">{{ $srmI18n(`${$getLangAccount()}#i18n_field_wjsuG_aa6a6475`, '来源专家库：') }}{{ item[0].search.sourceLibrary_dictText }}</p>
            <p v-if="item[0].search.specialistLevel_dictText">{{ $srmI18n(`${$getLangAccount()}#i18n_field_sutq_77cbf053`, '专家级别：') }}{{ item[0].search.specialistLevel_dictText }}</p>
            <p v-if="item[0].search.specialistType_dictText">{{ $srmI18n(`${$getLangAccount()}#i18n_field_suAc_77c46fc7`, '专家类型：') }}{{ item[0].search.specialistType_dictText }}</p>
            <p v-if="item[0].search.specialistClasses_dictText">{{ $srmI18n(`${$getLangAccount()}#i18n_field_suAq_77c3c927`, '专家类别：') }}{{ item[0].search.specialistClasses_dictText }}</p>
            <p v-if="item[0].search.specializedName">{{ $srmI18n(`${$getLangAccount()}#i18n_field_susE_771516b0`, '专家专业：') }}{{ item[0].search.specializedName }}</p>
            <p v-if="item[0].search.addressName">{{ $srmI18n(`${$getLangAccount()}#i18n_field_sudWnU_268bc94a`, '专家所属地域：') }}{{ item[0].search.addressName }}</p>
            <p v-if="item[0].search.cateCode">{{ $srmI18n(`${$getLangAccount()}#i18n_field_SLzA_7bb21835`, '物料分类：') }}{{ item[0].search.cateCode }}</p>
          </template>
          
          <a
            href="#"
            style="margin-left: 15px;">{{ $srmI18n(`${$getLangAccount()}#i18n_field_mAVMTI_59f8af4`, '查看抽取条件') }}</a>
        </a-tooltip>
      </titleTrtl>
      <a-spin :spinning="loading">
        <listTable
          ref="listTable"
          :pageStatus="pageStatus"
          setGridHeight="auto"
          :fromSourceData="item"
          :statictableColumns="statictableColumns"
          :showTablePage="false" />
      </a-spin>
    </div>
    <a-modal
      v-drag    
      :visible="visible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_VRLtk_ed32f81c`, '请确认操作')"
      @ok="handleOk"
      @cancel="handleCancel">
      <p>{{ $srmI18n(`${$getLangAccount()}#i18n_field_KQRLtk_ebdecea3`, '是否确认操作？') }}</p>
    </a-modal>
  </div>
</template>
<script>
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import {getAction, postAction} from '@api/manage'

export default {
    mixins: [baseMixins],
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        currentRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        juryMemberRecordList: {
            type: Array,
            default: () => {
                return []
            }
        },
        juryConditionList: {
            type: Array,
            default: () => {
                return []
            }
        },
        root: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    computed: {
    },
    components: {
        listTable,
        titleTrtl
    },
    watch: {
      
    },
    data () {
        
        return {
            currentTableRow: {},
            visible: false,
            loading: false,
            extractionList: [],
            statictableColumns: [
                // {
                //     type: 'checkbox',
                //     width: 50
                // },
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                // {
                //     title: '抽取条件',
                //     field: 'xx1',
                //     headerAlign: 'center',
                //     width: 150,
                //     fieldType: 'input',
                //     required: '0'
                // },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n__WWWey_401851d`, '主账号'),
                    field: 'elsAccount',
                    headerAlign: 'center',
                    required: '1',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                    field: 'elsSubAccount',
                    headerAlign: 'center',
                    required: '1',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                    field: 'elsRealname',
                    headerAlign: 'center',
                    enabled: false,
                    required: '1',
                    width: 150,
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'),
                    field: 'phone',
                    enabled: true,
                    required: '1',
                    headerAlign: 'center',
                    width: 150,
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                    field: 'certificateType',
                    enabled: true,
                    required: '1',
                    headerAlign: 'center',
                    dictCode: 'srmCertificateType',
                    width: 150,
                    fieldType: 'select'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                    field: 'certificateNumber',
                    enabled: true,
                    required: '1',
                    headerAlign: 'center',
                    width: 150,
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_workUnit`, '工作单位'),
                    field: 'workUnit',
                    enabled: false,
                    headerAlign: 'center',
                    width: 150,
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    field: 'confirmStatus',
                    headerAlign: 'center',
                    width: 150,
                    fieldType: 'select',
                    dictCode: 'tenderConfirmStatus', 
                    bindFunction: (row, rowIndex, column, columnIndex)=>{
                        console.log('this.root.$refs.baseForm', this.root.$refs.baseForm)
                        if(row.confirmStatus == '1'){
                            this.visible = true
                            this.currentTableRow = row
                        }else if(row.confirmStatus == '2'){
                            this.visible = true
                            this.currentTableRow = row
                        } 
                    }
                        
                    
                }
            ],
            show: false,
            url: {
                reject: '/tender/jury/purchaseTenderProjectJuryHead/extract/specialist/reject',
                confirmed: '/tender/jury/purchaseTenderProjectJuryHead/extract/specialist/confirmed'
            }
        }
    },
    methods: {
        handleCancel () {
            this.currentTableRow.confirmStatus = '0'
            this.visible = false
        },
        handleOk (){
            let url = ''
            let msg = ''
            if(this.currentTableRow.confirmStatus == '1') {
                url = this.url.confirmed
                msg = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IRLsu_3178a83b`, '已确认参加！')
            }else if(this.currentTableRow.confirmStatus == '2') {
                url = this.url.reject
                msg = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IFKsu_fb25d6c6`, '已拒绝参加！')
            }
            // 对当前行进行必填校验
            if(this.currentTableRow.confirmStatus == '1' && (!this.currentTableRow.phone || !this.currentTableRow.certificateType|| !this.currentTableRow.certificateNumber)){
                this.visible = false
                this.currentTableRow.confirmStatus = ''
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VPlSdxVMi_3bc4658c`, '请将必填项补充完整'))
                return 
            }
            let {evaExpertTotal='', juryMemberRecordList=[]} = this.root.$refs.baseForm.fromSourceData
            let evaList = []
            if(juryMemberRecordList.length != 0){
                // 总参与人数列表中留下评标的人员
                evaList = juryMemberRecordList.filter(item=>{
                    return item.confirmStatus == '1'
                })
            }
            console.log(evaList)
            if(this.currentTableRow.confirmStatus == '1' && evaList.length > evaExpertTotal){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sujsuLWBGUBsuLW_396494d1`, '参加的专家人数超出评标专家人数！'))
                return 
            }
            let params = this.currentTableRow
            this.loading = true
            postAction(url, params).then(res => {
                if(res.success) {
                    this.$message.success(msg)
                    // 成功后刷新页面并回调
                    // this.root.queryDetail(()=>{
                    //     setTimeout(() => {
                    //         this.root.setCurrentStep(1)
                    //     }, 500)
                    // })
                }else{
                    this.$message.error(res.message)
                    this.currentTableRow.confirmStatus = ''
                }
            }).finally(()=>{
                this.loading = false
                this.visible = false
            })
        },
        getValidate () {
            return this.$refs['listTable'].getValidate()
        },
        getTableData () {
            return this.$refs['listTable'].getTableData()
        },
        sortClass (sortData){
            const groupBy = (array, f) => {
                let groups = {}
                array.forEach((o) => {
                    let group = JSON.stringify(f(o))
                    groups[group] = groups[group] || []
                    groups[group].push(o)
                })
                return Object.keys(groups).map((group) => {
                    return groups[group]
                })
            }
            const sorted = groupBy(sortData, (item) => {
                // 先将recordlist数据组装上对应conditionlist的数据
                this.juryConditionList.forEach((conditionItem)=>{
                    if(conditionItem.id == item.searchId){
                        item.search = {
                            sourceLibrary_dictText: conditionItem.sourceLibrary_dictText, 
                            specialistLevel_dictText: conditionItem.specialistLevel_dictText, 
                            specialistType_dictText: conditionItem.specialistType_dictText, 
                            specialistClasses_dictText: conditionItem.specialistClasses_dictText, 
                            cateCode: conditionItem.cateCode,
                            specializedName: conditionItem.specializedName,
                            addressName: conditionItem.addressName
                        }
                    }
                })
                return item.rounds
                // search: {
                //     sourceLibrary_dictText, specialistLevel_dictText, specialistType_dictText, specialistClasses_dictText, cateName
                // }
                // 返回需要分组的对象
            })
            return sorted
        }

    },
    created () {
        let prop = this.juryMemberRecordList
        this.extractionList = this.sortClass(prop)
        console.log('this.extractionList', this.extractionList)
        console.log('this.juryConditionList', this.juryConditionList)
    }
}
</script>
