import elsListPage from '@comp/template/elsListPage'
export const listPageMixin = {
    components: {
        elsListPage
    },
    data () {
        return {
            pageData: {
                publicBtn: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Query`, '查询'), type: 'primary', icon: 'search', clickFn: this.searchEvent},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reset`, '重置'), icon: 'reload', clickFn: this.resetEvent}
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, primary: true},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), icon: 'delete', clickFn: this.handleDel},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', folded: true, clickFn: this.handleExportXls},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt`, '导入'), icon: 'import', folded: true, type: 'upload'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: true, clickFn: this.settingColumns}
                ],
                table: {
                    tableData: [],
                    tableColumn: []
                },
                showOptColumn: true,
                optColumnList: [
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEditInner},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDeleteSingle}
                ],
                optColumnWidth: 100,
                optColumnAlign: 'center' 
            },
            //当前点击的行数据
            currentEditRow: null
        }
    },
    methods: {
        modalFormOk () {
            // 新增/修改 成功时，重载列表
            this.$refs.listPage.loadData()
        },
        handleAdd () {
            this.currentEditRow = {}
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleEditInner (row) {
            this.currentEditRow = row
            this.$refs.listPage.handleEdit(row)
            this.showEditPage = true
        },
        handleDeleteSingle (row) {
            this.$refs.listPage.handleDelete(row.id)
        },
        handleDel () {
            this.$refs.listPage.removeRows()
        },
        hideEditPage () {
            this.showEditPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        confirmHideEditPage () {
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmToReturn`, '是否确认返回？'),
                onOk: () => {
                    that.hideEditPage()
                }
            })
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_files`, '文件'))
        },
        settingColumns () {
            this.$refs.listPage.settingColumnConfig()
        },
        searchEvent () {
            this.$refs.listPage.searchQuery()
        },
        resetEvent () {
            this.$refs.listPage.searchReset()
        }
    }
}