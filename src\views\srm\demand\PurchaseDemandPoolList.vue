<template>
  <!-- 需求池管理 -->
  <div style="height: 100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showRequestToOrderPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @beforeHandleData="beforeHandleData"
      @afterChangeTab="handleAfterChangeTab"
    />
    <!-- 表单区域 -->
    <Mapping-Price
      v-if="showCreateOrderPage"
      :rows="currentEditRow"
      @hide="hideCreateOrderPage"
    />
    <!-- 转订单 -->
    <demand-to-order-modal
      ref="modalForm"
      v-if="showRequestToOrderPage"
      :currentEditRow="{}"
      :responseData="responseData"
      @hide="hideDemandToOrderModel"
      @ok="modalFormOk"
    />
    <field-select-modal ref="fieldSelectModal" />
    <a-modal
      v-drag
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_returnComments`, '退回意见')"
      :okText="okText"
      @ok="handleOk"
    >
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterReturnComments`, '请输入退回意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <a-modal
      v-drag
      v-model="auditVisible2"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_returnComments`, '退回意见')"
      :okText="okText"
      @ok="handleOk"
    >
      <a-input-number
        v-if="type == 'partReturnItem'"
        v-model="returnNumber"
        style="margin-bottom: 20px; width: 200px"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterReturnComments`, '请输入退回数量')"
      />
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterReturnComments`, '请输入退回意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="demandVisible"
    >
      <template slot="footer">
        <a-button
          key="back"
          @click="handleDemandCancel"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedDemandPoolTemplate"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"
      />
    </a-modal>
    <a-modal
      v-drag
      v-model="downloadVisible"
      title="请选择文件下载"
      footer=""
    >
      <div
        class="down-flie-list"
        v-for="(item, index) in downloadList"
        :key="index"
      >
        <p class="fontwith">
          <a-tooltip
            :title="`${item.fileName}`"
            placement="topLeft"
          >
            {{ item.fileName }}
          </a-tooltip>
        </p>
        <a-button
          type="primary"
          size="small"
          @click="downloadEvent(item)"
          >下载
        </a-button>
      </div>
    </a-modal>
    <record-modal
      v-model="recordShowVisible"
      :currentEditRow="currentEditRow"
    />

    <RelationGraphModal
      v-if="modalVisibleDocket"
      :modalVisibleDocket="modalVisibleDocket"
      :id="documentId"
      :rootId="id"
      @closeModalDocket="closeModalDocket"
      @onNodeClick="clickNode"
    >
    </RelationGraphModal>
  </div>
</template>

<script>
import MappingPrice from './modules/MappingPrice'
import { ListMixin } from '@comp/template/list/ListMixin'
import { getAction, httpAction, postAction } from '@/api/manage'
import RelationGraphModal from '@comp/RelationGraphModal'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import DemandToOrderModal from './modules/DemandToOrderModal'
import RecordModal from '@comp/recordModal'

export default {
  mixins: [ListMixin],
  components: {
    RelationGraphModal,
    DemandToOrderModal,
    MappingPrice,
    fieldSelectModal,
    RecordModal
  },
  data() {
    return {
      id: '',
      documentId: '',
      modalVisibleDocket: false,
      showEditPage: false,
      showCreateOrderPage: false,
      showEditEnquiryPage: false,
      showRequestToOrderPage: false,
      recordShowVisible: false,
      auditVisible: false,
      auditVisible2: false,
      okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_drawBack`, '退回'),
      opinion: '',
      returnNumber: null,
      submitLoading: false,
      demandVisible: false,
      downloadVisible: false,
      nextOpt: true,
      currentEditRow: {},
      responseData: {}, // 转订单接口返回数据
      currentRow: {},
      selectRow: {},
      templateNumber: undefined,
      templateOpts: [],
      downloadList: [],
      type: '',
      btnLoading: false,
      pageData: {
        businessType: 'purchaseRequest',
        button: [
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_forwardInquiry`, '转询价'),
            authorityCode: 'enquiry#purchaseEnquiryHead:requestToEnquiry',
            icon: 'pay-circle',
            clickFn: this.createEnquiry,
            primary: true
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transferBidding`, '转竞价'),
            authorityCode: 'ebidding#purchaseEbiddingHead:requestToEbidding',
            icon: 'pay-circle',
            clickFn: this.createEbidding,
            primary: true
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sYBWtsW_cbfdaba9`, '转招标（单包）'),
            authorityCode: 'bidding#purchaseBiddingHead:requestToBidding',
            icon: 'pay-circle',
            clickFn: this.createBidding,
            primary: true
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sYBWOsW_cc13542e`, '转招标（多包）'),
            authorityCode: 'bidding#purchaseBiddingHead:requestToBiddingProject',
            icon: 'pay-circle',
            clickFn: this.createBiddingProject,
            primary: true
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_sYBWdIvdW_a00868f6`, '转招标（项目立项）'),
            authorityCode: 'tender#tenderProjectApprovalHead:tenderProjectApproval',
            icon: 'pay-circle',
            clickFn: this.createTenderProjectApproval,
            primary: true
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transferOrder`, '转订单'),
            authorityCode: 'demandPool#purchaseDemandPool:requestToOrder',
            icon: 'pay-circle',
            clickFn: this.createOrder,
            primary: true
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_zRsr_2ef869a8`, '批量转办'),
            authorityCode: '',
            icon: 'pay-circle',
            clickFn: this.batchTransfer,
            primary: true
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_zRYM_2ef87fd4`, '批量退回'),
            authorityCode: '',
            icon: 'pay-circle',
            clickFn: this.batchReturn,
            primary: true
          },
          {
            label: '批量部分退回',
            authorityCode: '',
            icon: 'pay-circle',
            clickFn: this.batchPartReturn,
            primary: true
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduXL_bf0b657f`, '推送到商城'),
            authorityCode: 'demandPool#purchaseDemandPool:requestToMall',
            icon: 'arrow-up',
            clickFn: this.pushDataToMall
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
            allow: () => {
              return this.btnInvalidAuth('demandPool#purchaseDemandPool:export')
            },
            icon: 'download',
            clickFn: this.handleExportXls
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operaRecord`, '操作记录'),
            icon: 'snippets',
            clickFn: this.showTransferHis
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
            icon: 'setting',
            clickFn: this.settingColumns
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
            icon: 'file-text',
            folded: true,
            isDocument: true,
            clickFn: this.showHelpText
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
            icon: 'file-pdf',
            folded: true,
            isDocument: true,
            clickFn: this.showHelpPDF
          }
        ],
        formField: [
          {
            type: 'input',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
            fieldName: 'materialName',
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterMaterialName`, '请输入物料名称')
          },
          {
            type: 'input',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
            fieldName: 'materialNumber',
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterMaterialCode`, '请输入物料编码')
          },
          {
            type: 'input',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_requestNO`, '申请单号'),
            fieldName: 'requestNumber',
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterPurchaseRequisitionNo`, '请输入申请单号')
          }
        ],
        isOrder: {
          column: '',
          order: ''
        },
        form: {
          keyWord: ''
        },
        optColumnList: [
          {
            type: '',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办'),
            authorityCode: 'demandPool#purchaseDemandPool:edit',
            clickFn: this.updateItem,
            allow: this.showItemCondition
          },
          {
            type: 'view',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_drawBack`, '退回'),
            authorityCode: 'demandPool#purchaseDemandPool:returnItem',
            clickFn: this.returnItem,
            allow: this.showReturnCondition
          },
          {
            type: 'view',
            title: '部分退回',
            authorityCode: 'demandPool#purchaseDemandPool:returnItem',
            clickFn: this.partReturnItem,
            allow: this.showPartReturnCondition
          },
          {
            type: 'view',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_tFKm_2766a28a`, '单据联查'),
            clickFn: this.viewDocket
          },
          {
            type: 'record',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
            clickFn: this.handleShowRecord
          },
          {
            type: 'download',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
            clickFn: this.showFileListEvent,
            authorityCode: 'demandPool#purchaseDemandPool:download'
          }
        ]
      },
      tabsList: [],
      url: {
        list: '/demand/purchaseDemandPool/list',
        edit: '/demand/purchaseDemandPool/edit',
        batchEdit: '/demand/purchaseDemandPool/batchEdit',
        temp: '',
        createEnquiry: '/enquiry/purchaseEnquiryHead/requestToEnquiry',
        createEbidding: '/ebidding/purchaseEbiddingHead/requestToEbidding',
        createBidding: '/bidding/purchaseBiddingHead/requestToBidding',
        createBiddingProject: '/bidding/purchaseBiddingProjectHead/requestToBiddingProject',
        createTenderProjectApproval: '/tender/tenderProjectApprovalHead/requestToTenderProjectApproval',
        requestSampleToOrder: '/demand/purchaseDemandPool/requestSampleToOrder',
        cancel: '/demand/purchaseDemandPool/cancel',
        returnItem: '/demand/purchaseDemandPool/returnItem',
        partReturnItem: '/demand/purchaseDemandPool/partReturnItem',
        batchReturn: '/demand/purchaseDemandPool/batchReturn',
        batchPartReturn: '/demand/purchaseDemandPool/batchPartReturn',
        queryPermissionById: '/demand/purchaseDemandPool/queryPermissionById',
        queryPermissionByIds: '/demand/purchaseDemandPool/queryPermissionByIds',
        demandMatchPrice: '/demand/purchaseRequestHead/requestMatchInfomationRecord',
        exportXlsUrl: '/demand/purchaseDemandPool/exportXls',
        pushDataToMallUrl: '/mall/pushPurchaseRequestItemDataToMall',
        columns: 'purchaseDemandPoolList'
      },
      isOrder: {
        column: 'update_time',
        order: 'desc'
      }
    }
  },
  mounted() {
    // this.serachTabs('srmPurchaseRequestItemStatus', 'itemStatus')
    this.serachCountTabs('/demand/purchaseDemandPool/counts')
  },
  methods: {
    clickNode() {
      this.$store.dispatch('SetTabConfirm', false)
      this.modalVisibleDocket = false
    },
    closeModalDocket() {
      this.modalVisibleDocket = false
    },
    // 是否显示单据联查
    showDocket(row) {
      return row.headId
    },
    // 单据联查
    viewDocket(row) {
      this.id = row.headId
      this.documentId = row.documentId
      console.log('id:' + this.documentId)
      console.log('rootId:' + this.id)
      this.modalVisibleDocket = true
    },
    beforeHandleData(columns) {
      // columns.forEach(item => {
      //     if (item.dataIndex == 'updateBy') {
      //         item.scopedSlots = {
      //             default: ({row, rowIndex, column, columnIndex}, h) => {
      //                 return [
      //                     (<span>{row[column.property]}</span>)
      //                 ]
      //             },
      //             edit: ({row, rowIndex, column, columnIndex}, h) => {
      //                 return [(<a-input vModel={row[column.property]}/>)]
      //             }
      //         }
      //     }
      // })
    },
    hideDemandToOrderModel() {
      this.showEditPage = false
      this.showRequestToOrderPage = false
      this.searchEvent(false)
    },
    handleExportXls() {
      this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_demandPool`, '需求池'))
    },
    showItemCondition(row) {
      if (row.itemStatus == '1' || row.itemStatus == '2') {
        return false
      } else {
        return true
      }
    },
    showReturnCondition(row) {
      if (row.itemStatus == '1' || row.sourceType == 'sample' || row.itemStatus == '2') {
        return false
      } else {
        return true
      }
    },
    showPartReturnCondition(row) {
      if (row.itemStatus == '1' || row.itemStatus == '2' || row.itemStatus == '7' || row.itemStatus == '13') {
        // 可操作
        return false
      } else {
        return true
      }
    },
    showCancelCondition(row) {
      if (row.itemStatus == '1' || row.itemStatus == '-1') {
        return false
      } else {
        return true
      }
    },
    handleOk() {
      this.auditVisible = false
      this.auditVisible2 = false
      let that = this
      if (this.type == 'returnItem') {
        this.currentRow.backRemark = this.opinion
        let elsBusinessTransferHis = {
          businessType: 'demandPool',
          businessTransferNumber: that.currentRow.requestNumber,
          businessTransferName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请号'),
          transferObject: '',
          transferType: 'refund',
          transferEntity: that.currentRow.tacticsEntity ? that.currentRow.tacticsEntity + '->' : ''
        }
        // that.$set(that.currentRow, 'elsBusinessTransferHis', elsBusinessTransferHis)
        let params = {
          id: that.currentRow.id,
          backRemark: this.opinion,
          elsBusinessTransferHis: elsBusinessTransferHis
        }
        that.postUpdateData(that.url.returnItem, params)
      } else if (this.type === 'batchReturn') {
        this.selectRow.forEach((item, i) => {
          let elsBusinessTransferHis = {
            businessType: 'demandPool',
            businessTransferNumber: item.requestNumber,
            businessTransferName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请号'),
            transferObject: '',
            transferType: 'refund',
            transferEntity: item.tacticsEntity ? item.tacticsEntity + '->' : ''
          }
          that.$set(item, 'elsBusinessTransferHis', elsBusinessTransferHis)
          item.backRemark = that.opinion
        })
        that.postUpdateData(that.url.batchReturn, this.selectRow)
      } else if (this.type === 'partReturnItem') {
        let elsBusinessTransferHis = {
          businessType: 'demandPool',
          businessTransferNumber: that.currentRow.requestNumber,
          businessTransferName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请号'),
          transferObject: '',
          transferType: 'refund',
          transferEntity: that.currentRow.tacticsEntity ? that.currentRow.tacticsEntity + '->' : ''
        }
        // that.$set(that.currentRow, 'elsBusinessTransferHis', elsBusinessTransferHis)
        let params = {
          id: that.currentRow.id,
          backRemark: this.opinion,
          partReturnQuantity: this.returnNumber,
          elsBusinessTransferHis: elsBusinessTransferHis
        }
        that.postUpdateData(that.url.partReturnItem, params)
      } else if (this.type === 'batchPartReturn') {
        this.selectRow.forEach((item, i) => {
          let elsBusinessTransferHis = {
            businessType: 'demandPool',
            businessTransferNumber: item.requestNumber,
            businessTransferName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请号'),
            transferObject: '',
            transferType: 'refund',
            transferEntity: item.tacticsEntity ? item.tacticsEntity + '->' : ''
          }
          that.$set(item, 'elsBusinessTransferHis', elsBusinessTransferHis)
          item.backRemark = that.opinion
        //   item.partReturnQuantity = that.returnNumber
        })
        that.postUpdateData(that.url.batchPartReturn, this.selectRow)
      }
    },
    returnItem(row) {
      // eslint-disable-next-line no-irregular-whitespace
      this.type = 'returnItem'
      let subAccount = this.$ls.get('Login_subAccount')
      if (!(subAccount == '1001' || (row.tacticsEntity && row.tacticsEntity.split('_')[0] == subAccount))) {
        // if ((row.tacticsEntity == null||row.tacticsEntity ==''||row.tacticsObject == null||row.tacticsObject =='')) {
        //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zEKDLVWVKHRvjMdYMW_e06201e5`, '分配实体为空，请联系管理员协助退回。'))
        //     return
        // }
        getAction(this.url.queryPermissionById, { id: row.id }, 'get')
          .then((res) => {
            if (res.success && res.result) {
              this.currentRow = row
              this.auditVisible = true
            } else if (res.success && !res.result) {
              this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APeyStkbW_91a73231`, '当前账号无操作权限'))
            } else {
              this.$message.warning(res.message)
            }
          })
          .finally(() => {
            this.$refs.listPage.confirmLoading = false
          })
      } else {
        this.currentRow = row
        this.auditVisible = true
      }
    },
    // 部分回退
    partReturnItem(row) {
      // eslint-disable-next-line no-irregular-whitespace
      this.type = 'partReturnItem'
      this.opinion = null
      this.returnNumber = null
      let subAccount = this.$ls.get('Login_subAccount')
      if (!(subAccount == '1001' || (row.tacticsEntity && row.tacticsEntity.split('_')[0] == subAccount))) {
        // if ((row.tacticsEntity == null||row.tacticsEntity ==''||row.tacticsObject == null||row.tacticsObject =='')) {
        //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zEKDLVWVKHRvjMdYMW_e06201e5`, '分配实体为空，请联系管理员协助退回。'))
        //     return
        // }
        getAction(this.url.queryPermissionById, { id: row.id }, 'get')
          .then((res) => {
            if (res.success && res.result) {
              this.currentRow = row
              this.auditVisible2 = true
            } else if (res.success && !res.result) {
              this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APeyStkbW_91a73231`, '当前账号无操作权限'))
            } else {
              this.$message.warning(res.message)
            }
          })
          .finally(() => {
            this.$refs.listPage.confirmLoading = false
          })
      } else {
        this.currentRow = row
        this.auditVisible2 = true
      }
    },
    batchReturn() {
      this.type = 'batchReturn'
      let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
      if (selectedRows.length <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPYMjc_4eed8c9e`, '请选择需要退回的行'))
        return
        // eslint-disable-next-line no-empty
      } else {
        this.selectRow = selectedRows
        let subAccount = this.$ls.get('Login_subAccount')
        let ids = ''
        let permissionFlag = false
        selectedRows.forEach((item, i) => {
          if (!(subAccount == '1001' || (item.tacticsEntity && item.tacticsEntity.split('_')[0] == subAccount))) {
            //记录需要校验转办全选的ID
            ids = ids + item.id
            ids = ids + ','
          }
          if (!(item.itemStatus == '1' || item.itemStatus == '2')) {
            permissionFlag = true
          }
        })
        if (permissionFlag) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '只有行状态为待订单或者待寻源的需求才能退回'))
          return
        }
        if (ids != '') {
          this.$refs.listPage.confirmLoading = true
          let that = this
          getAction(this.url.queryPermissionByIds, { id: ids }, 'get')
            .then((res) => {
              if (res.success && res.result) {
                that.auditVisible = true
              } else if (res.success && !res.result) {
                this.$message.warning(res.message)
              } else {
                this.$message.warning(res.message)
              }
            })
            .finally(() => {
              this.$refs.listPage.confirmLoading = false
            })
        } else {
          this.auditVisible = true
        }
      }
    },
    batchPartReturn() {
      this.type = 'batchPartReturn'
      let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
      if (selectedRows.length <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPYMjc_4eed8c9e`, '请选择需要退回的行'))
        return
        // eslint-disable-next-line no-empty
      } else {
        this.selectRow = selectedRows
        let subAccount = this.$ls.get('Login_subAccount')
        let ids = ''
        let permissionFlag = false
        selectedRows.forEach((item, i) => {
          if (!(subAccount == '1001' || (item.tacticsEntity && item.tacticsEntity.split('_')[0] == subAccount))) {
            //记录需要校验转办全选的ID
            ids = ids + item.id
            ids = ids + ','
          }
          if (this.showPartReturnCondition(item)) {
            permissionFlag = true
          }
        })
        if (permissionFlag) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '只有行状态为待订单或者待寻源的需求才能退回'))
          return
        }
        this.opinion = null
        this.returnNumber = null
        if (ids != '') {
          this.$refs.listPage.confirmLoading = true
          let that = this
          getAction(this.url.queryPermissionByIds, { id: ids }, 'get')
            .then((res) => {
              if (res.success && res.result) {
                that.auditVisible2 = true
              } else if (res.success && !res.result) {
                this.$message.warning(res.message)
              } else {
                this.$message.warning(res.message)
              }
            })
            .finally(() => {
              this.$refs.listPage.confirmLoading = false
            })
        } else {
          this.auditVisible2 = true
        }
      }
    },
    // 跳转到询价管理, path: /srm/enquiry/purchase/PurchaseEnquiryList
    goPage(path, query) {
      let meta = { ...query }
      this.$router.push({ path: path, query: meta })
    },
    jumpTargetPage(data) {
      const { businessType, result } = data
      const jumpArr = [
        { type: 'enquiry', url: '/srm/enquiry/purchase/PurchaseEnquiryList' },
        { type: 'ebidding', url: '/srm/ebidding/EbiddingBuyHeadList' },
        { type: 'bidding', url: '/srm/bidding/purchase/PurchaseBiddingHeadList' },
        { type: 'biddingProject', url: '/srm/bidding/purchase/PurchaseBiddingProjectHeadList' },
        { type: 'biddingProject', url: '/srm/bidding/purchase/PurchaseBiddingProjectHeadList' },
        { type: 'projectApproval', url: '/srm/ebidding/ProjectBuildingList' },
        { type: 'order', url: '/srm/order/purchase/PurchaseOrderHeadList' }
      ]
      const query = {
        source: 'demand-pool',
        result
      }
      const targetModule = jumpArr.find((rs) => rs.type == businessType)
      this.$router.push({ path: targetModule?.url || '', query })
    },
    postUpdateData(url, row, businessType) {
      this.$refs.listPage.loading = true
      httpAction(url, row, 'post')
        .then((res) => {
          if (res.success) {
            if (businessType) {
              if (res?.result?.length == 1) {
                this.jumpTargetPage({ url, row, businessType, result: res.result })
              } else {
                this.$message.success(res.message)
              }
            } else {
              this.$message.success(res.message)
            }
            this.searchEvent()
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.listPage.loading = false
        })
    },
    batchTransfer() {
      this.type = 'batchTransfer'
      let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
      if (selectedRows.length <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFTPsrjc_4e9a5172`, '请选择需要转办的行'))
        return
      } else {
        let subAccount = this.$ls.get('Login_subAccount')
        let ids = ''
        let permissionFlag = false
        selectedRows.forEach((item, i) => {
          if (!(subAccount == '1001' || (item.tacticsEntity && item.tacticsEntity.split('_')[0] == subAccount))) {
            //记录需要校验转办全选的ID
            ids = ids + item.id
            ids = ids + ','
          }
          if (!(item.itemStatus == '1' || item.itemStatus == '2')) {
            permissionFlag = true
          }
        })
        if (permissionFlag) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, '只有行状态为待订单或者待寻源的需求才能转办'))
          return
        }
        if (ids != '') {
          this.$refs.listPage.confirmLoading = true
          let that = this
          getAction(this.url.queryPermissionByIds, { id: ids }, 'get')
            .then((res) => {
              if (res.success && res.result) {
                let item = {
                  sourceUrl: '/account/elsSubAccount/list',
                  params: {},
                  columns: [
                    {
                      field: 'subAccount',
                      title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                      with: 150
                    },
                    {
                      field: 'realname',
                      title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'),
                      with: 150
                    }
                  ]
                }
                that.selectRow = selectedRows
                that.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
              } else if (res.success && !res.result) {
                this.$message.warning(res.message)
              } else {
                this.$message.warning(res.message)
              }
            })
            .finally(() => {
              this.$refs.listPage.confirmLoading = false
            })
        } else {
          let item = {
            sourceUrl: '/account/elsSubAccount/list',
            params: {},
            columns: [
              {
                field: 'subAccount',
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                with: 150
              },
              {
                field: 'realname',
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'),
                with: 150
              }
            ]
          }
          this.selectRow = selectedRows
          this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        }
      }
    },
    updateItem(row) {
      this.type = 'transfer'
      // eslint-disable-next-line no-irregular-whitespace
      let subAccount = this.$ls.get('Login_subAccount')
      if (!(subAccount == '1001' || (row.tacticsEntity && row.tacticsEntity.split('_')[0] == subAccount))) {
        // if ((row.tacticsEntity == null||row.tacticsEntity ==''||row.tacticsObject == null||row.tacticsObject =='')) {
        //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_zEKDLVWVKHRvjMdsrW_e05f5291`, '分配实体为空，请联系管理员协助转办。'))
        //     return
        // }
        getAction(this.url.queryPermissionById, { id: row.id }, 'get')
          .then((res) => {
            if (res.success && res.result) {
              let item = {
                sourceUrl: '/account/elsSubAccount/list',
                params: {},
                columns: [
                  {
                    field: 'subAccount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                    with: 150
                  },
                  {
                    field: 'realname',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'),
                    with: 150
                  }
                ]
              }
              this.currentRow = row
              this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
            } else if (res.success && !res.result) {
              this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APeyStkbW_91a73231`, '当前账号无操作权限'))
            } else {
              this.$message.warning(res.message)
            }
          })
          .finally(() => {
            this.$refs.listPage.confirmLoading = false
          })
      } else {
        let item = {
          sourceUrl: '/account/elsSubAccount/list',
          params: {},
          columns: [
            {
              field: 'subAccount',
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
              with: 150
            },
            {
              field: 'realname',
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'),
              with: 150
            }
          ]
        }
        this.currentRow = row
        this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
      }
    },
    fieldSelectOk(data) {
      let that = this
      //批量转办
      if (that.type == 'batchTransfer') {
        this.selectRow.forEach((item, i) => {
          let elsBusinessTransferHis = {
            businessType: 'demandPool',
            businessTransferNumber: item.requestNumber,
            businessTransferName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请号'),
            transferObject: 'person',
            transferType: 'transfer',
            transferEntity: (item.tacticsEntity ? item.tacticsEntity : '') + '->' + data[0].subAccount + '_' + data[0].realname
          }
          that.$set(item, 'elsBusinessTransferHis', elsBusinessTransferHis)
          item.tacticsObject = 'person'
          item.tacticsEntity = data[0].subAccount + '_' + data[0].realname
        })
        this.$confirm({
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办'),
          content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmTransferDocumentassignor`, '确认将此单据分配人转办为') + ':' + data[0].subAccount + '_' + data[0].realname,
          onOk: function () {
            that.postUpdateData(that.url.batchEdit, that.selectRow)
          }
        })
      } else if (that.type == 'transfer') {
        let elsBusinessTransferHis = {
          businessType: 'demandPool',
          businessTransferNumber: that.currentRow.requestNumber,
          businessTransferName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请号'),
          transferObject: 'person',
          transferType: 'transfer',
          transferEntity: (that.currentRow.tacticsEntity ? that.currentRow.tacticsEntity : '') + '->' + data[0].subAccount + '_' + data[0].realname
        }
        this.$confirm({
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办'),
          content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmTransferDocumentassignor`, '确认将此单据分配人转办为') + ':' + data[0].subAccount + '_' + data[0].realname,
          onOk: function () {
            that.currentRow.tacticsObject = 'person'
            that.currentRow.tacticsEntity = data[0].subAccount + '_' + data[0].realname
            that.$set(that.currentRow, 'elsBusinessTransferHis', elsBusinessTransferHis)
            that.postUpdateData(that.url.edit, that.currentRow)
          }
        })
      }
    },
    createEnquiry() {
      if (this.$refs.listPage.loading) return
      this.$refs.listPage.loading = true
      this.nextOpt = true
      this.serachTemplate('enquiry')
    },
    createEbidding() {
      if (this.$refs.listPage.loading) return
      this.$refs.listPage.loading = true
      this.nextOpt = true
      this.serachTemplate('ebidding')
    },
    createBidding() {
      if (this.$refs.listPage.loading) return
      this.$refs.listPage.loading = true
      this.nextOpt = true
      this.serachTemplate('bidding')
    },
    createBiddingProject() {
      if (this.$refs.listPage.loading) return
      this.$refs.listPage.loading = true
      this.nextOpt = true
      this.serachTemplate('biddingProject')
    },
    createTenderProjectApproval() {
      if (this.$refs.listPage.loading) return
      this.$refs.listPage.loading = true
      this.nextOpt = true
      this.serachTemplate('projectApproval')
    },
    createOrder() {
      let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
      this.nextOpt = true
      if (selectedRows.length <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
        return
      } else {
        let countSourceType = 0
        selectedRows.forEach((item, i) => {
          if (!(item.itemStatus == '1' || item.itemStatus == '2' || item.itemStatus == '7' || item.itemStatus == '8' || item.itemStatus == '9' || item.itemStatus == '10' || item.itemStatus == '13' || item.itemStatus == '14')) {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_chjLML_d8c1b1cd`, '行寻源未完成'))
            this.nextOpt = false
            return
          }
          if (item.sourceType == 'sample') {
            countSourceType++
          }
          if (item.sourceType == 'mall') {
            countSourceType++
          }
        })
        if (countSourceType > 0 && countSourceType != selectedRows.length) {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isEjwjxIR_b87bce9a`, '选中行的来源不一致'))
          this.nextOpt = false
          return
        } else if (countSourceType === 0 && selectedRows[0].sourceType !== 'sample' && selectedRows[0].sourceType !== 'mall') {
          if (this.nextOpt) {
            // 此处可以传参多行数据
            // 测试单号:
            // PR202204070029
            this.getToOrderData(selectedRows)
          }
        } else {
          this.nextOpt = true
          this.serachTemplate('order')
        }
      }
    },
    // 获取转订单数据
    getToOrderData(rows) {
      let url = '/demand/purchaseRequestHead/requestMatchInfomationRecord'
      postAction(url, rows).then((res) => {
        if (!res.success) {
          this.$message.warning(res.message)
          return
        }
        this.responseData = {
          purchaseDeliveryNoticeToDeliveryItemVOList: res.result || []
        }
        this.showRequestToOrderPage = true
      })
    },
    serachTemplate(businessType) {
      this.currentEditRow = {}
      let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
      if (selectedRows.length <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
        this.$refs.listPage.loading = false
        return
      } else {
        selectedRows.forEach((item, i) => {
          if (item.itemStatus == '14') {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cIbxsIt_12ae8c79`, '行已全部转订单'))
            this.nextOpt = false
            this.$refs.listPage.loading = false
            return
          }
        })
        if (this.nextOpt) {
          this.pageData.businessType = businessType
          this.openModal()
        }
      }
    },
    openModal() {
      this.$refs.listPage.queryTemplateList(this.$ls.get('Login_elsAccount')).then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            let options = res.result.map((item) => {
              return {
                value: item.templateNumber,
                title: item.templateName,
                version: item.templateVersion,
                account: item.elsAccount
              }
            })
            this.templateNumber = ''
            this.templateOpts = options
            // 只有单个模板直接新建
            if (this.templateOpts && this.templateOpts.length === 1) {
              this.templateNumber = this.templateOpts[0].value
              this.selectedDemandPoolTemplate()
            } else {
              // 有多个模板先选择在新建
              this.demandVisible = true
              this.$refs.listPage.loading = false
            }
          } else {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
            this.$refs.listPage.loading = false
          }
        } else {
          this.$message.warning(res.message)
          this.$refs.listPage.loading = false
        }
      })
    },
    handleDemandCancel() {
      this.demandVisible = false
    },
    handleDownloadVisibleCancel() {
      this.downloadVisible = false
    },
    selectedDemandPoolTemplate() {
      if (this.templateNumber) {
        const that = this
        this.submitLoading = true
        let template = this.templateOpts.filter((item) => {
          return item.value == that.templateNumber
        })
        let params = {
          templateNumber: this.templateNumber,
          templateName: template[0].title,
          templateVersion: template[0].version,
          templateAccount: template[0].account,
          purchaseRequestItemList: []
        }
        if (this.pageData.businessType == 'enquiry') {
          this.url.temp = this.url.createEnquiry
        } else if (this.pageData.businessType == 'ebidding') {
          this.url.temp = this.url.createEbidding
        } else if (this.pageData.businessType == 'bidding') {
          this.url.temp = this.url.createBidding
        } else if (this.pageData.businessType == 'biddingProject') {
          this.url.temp = this.url.createBiddingProject
        } else if (this.pageData.businessType == 'projectApproval') {
          this.url.temp = this.url.createTenderProjectApproval
        } else if (this.pageData.businessType == 'order') {
          this.url.temp = this.url.requestSampleToOrder
        }
        that.demandVisible = false
        that.submitLoading = false
        if (this.url.temp == '') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
          this.$refs.listPage.loading = false
          return
        }
        params.purchaseRequestItemList = that.$refs.listPage.$refs.listGrid.getCheckboxRecords()
        that.postUpdateData(this.url.temp, params, this.pageData.businessType)
      } else {
        this.$refs.listPage.loading = false
      }
    },
    showTransferHis() {
      this.$store.dispatch('SetTabConfirm', false)
      this.$router.push({
        path: '/demand/ElsBusinessTransferHisList'
      })
    },
    showFileListEvent(row) {
      const params = { id: row.headId, itemNumber: row.itemNumber }
      if (row.sourceType == 'sample') {
        params.id = row.sourceId
      }
      getAction('/demand/purchaseDemandPool/download', params, {}).then((res) => {
        if (res.success && res.result) {
          if (res.result.length == 0) {
            alert(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_rcBWF_f2f789f8`, '该行没数据'))
            return
          }
          //todo  获取文件列表
          this.downloadList = res.result
          //todo   打开弹窗
          this.downloadVisible = true
        } else {
          alert(res.message)
        }
      })
    },
    downloadEvent(row) {
      console.log(row)
      const params = { id: row.id }
      getAction('/attachment/purchaseAttachment/download', params, {
        responseType: 'blob'
      }).then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', row.fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    },
    handleShowRecord(row) {
      this.currentEditRow = row
      this.$refs.listPage.showRecordModal()
    },
    pushDataToMall() {
      let grid = this.$refs.listPage.$refs.listGrid
      let checkRow = grid.getCheckboxRecords() || []
      if (!checkRow.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRRiFIcWF_e9c4fe3f`, '请至少选择一行'))
        return
      }
      let ids = checkRow.map((row) => row.id).join(',')
      this.$refs.listPage.loading = true
      getAction(this.url.pushDataToMallUrl, { ids: ids })
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功'))
            this.searchEvent()
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.listPage.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.down-flie-list {
  display: flex;
  justify-content: space-between;
  flex-flow: row;
  align-items: center;
}
.fontwith {
  width: 90%;
  margin-top: 10px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
