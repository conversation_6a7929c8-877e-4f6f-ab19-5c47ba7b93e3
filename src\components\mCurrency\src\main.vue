<template>
  <div class="MCurrency">
    <span>
      <a-input
        :readOnly="!disabled"
        :disabled="disabled"
        v-show="!show"
        :value="realValue | currency(symbol, decimals)"
        @click="focusFn">
      </a-input>
      <a-input-number
        style="width:100%"
        v-show="show"
        v-bind="configData.extend || {}"
        ref="number"
        @blur="() => (show = false)"
        :value="realValue"
        @change="changeInputValue"
        :placeholder="placeholder"
      ></a-input-number>
    </span>
  </div>
</template>

<script>
import { currency } from '@/filters'
import { debounce } from 'lodash'

export default {
    name: 'MCurrency',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        placeholder: {
            type: String,
            default: ''
        },
        configData: {
            type: Object,
            default () {
                return {}
            }
        },
        disabled: {
            type: Boolean,
            default: false
        },
        value: {
            type: [Number, String],
            default: ''
        }
    },
    filters: {
        currency
    },
    data () {
        return {
            show: false,
            realValue: this.value
        }
    },
    computed: {
        symbol () {
            return this.configData.symbol || ''
        },
        decimals () {
            return this.configData.decimals ? Number(this.configData.decimals) : 2
        }
    },
    watch: {
        value (val) {
            this.realValue = val
        }
    },
    methods: {
        focusFn () {
            // 置灰状态不切换
            if (this.disabled) {
                return
            }
            this.show = true
            this.$nextTick(() => {
                this.$refs.number.focus()
            })
        },
        changeInputValue: debounce(function (val) {
            this.$emit('change', val)
        }, 200)
    }
}
</script>
