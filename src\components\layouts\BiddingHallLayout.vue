<template>
  <div class="hall-layout">
    <a-spin :spinning="spinning">
      <a-layout>
        <a-layout-header class="header">
          <div class="logo fl">
            <img
              class="logoP"
              src="../../assets/img/ebidding/logo.png"
              alt="">
            <span>{{ title }}</span></div>
          <div
            class="fl width180"
            v-if="canChangeSubPage">
            <a-select
              v-model="subpackageId"
              option-filter-prop="children"
              show-search
              :filter-option="filterOption"
              style="width: 100%"
              placeholder="Tags Mode"
              :options="subPackageArr"
              @change="handleChangeSubPackage"> </a-select>
          </div>
          <div
            class="fl"
            style="font-weight: 400; font-size: 20px; color: #fff"
            v-else>
            <span>{{ tenderCurrentRow.subpackageName }}</span>
          </div>
          <div class="fl openBidStatus">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_zszE_268f120a`, '分包状态') }}：<span class="blue">{{ currentSubPackage.statusDesc }}</span></span>
          </div>
          <div class="fr currentTender">
            <div class="fl ">{{ $srmI18n(`${$getLangAccount()}#i18n_massProdHead140_projectName`, '项目名称') }}：<span class="blue">{{ tenderCurrentRow.tenderProjectName }}</span></div>
            <div class="fl currentTenderItem">{{ $srmI18n(`${$getLangAccount()}#i18n_title_projectNumber`, '项目编号') }}：<span class="blue">{{ tenderCurrentRow.tenderProjectNumber }}</span></div>
          </div>
        </a-layout-header>
        <a-layout
          class="siderLayout"
          style="min-height: calc(100vh - 50px)">
          <a-layout-sider width="200">
            <s-menu
              ref="menu"
              :collapsed="false"
              :menu="menus"
              :theme="navTheme"
              @setCurrentMenuTender="setCurrentMenuTender"
              @select="onSelect"
              @getSessionStorageOfCurrentNode="getSessionStorageOfCurrentNode"
              :style="style"
              :userIconFont="true" />
          </a-layout-sider>
          <a-layout style="padding: 8px">
            <a-layout-content :style="{ 'position': 'relative', 'background': '#fff' }">
              <!-- <route-view v-if="subpackageId" /> -->
              <div
                class="main"
                v-if="routerAlive && !isHome">
                <router-view
                  @resetCurrentSubPackage="resetCurrentSubPackage"
                  @getSubpackage="getSubpackage"
                  :key="currentNode.nodeId"
                  v-if="subpackageId && menus.length > 0" />
              </div>
              <div
                v-else
                class="bgImage"
                :style="{height: `${bgImageHeight}px`}"><img
                  src="../../assets/img/ebidding/bgImage.jpg"
                  alt=""></div>
            </a-layout-content>
          </a-layout>
        </a-layout>
      </a-layout>
    </a-spin>
  </div>
</template>

<script>
// import RouteView from '@/components/layouts/RouteView'
import SMenu from '@/views/srm/bidding_new/BiddingHall/components/menu/index.jsx'
import { mapState } from 'vuex'
import layIM from '@/utils/im/layIM.js'
import { postAction, getAction } from '@/api/manage'
import {isString} from 'lodash'
export default {
    name: 'BiddingHallLayout',
    components: {
        SMenu
        // RouteView
    },
    computed: {
        ...mapState({
            navTheme: (state) => state.app.theme
        }),
        style () {
            return {
                padding: 0,
                height: '100%',
                overflowX: 'hidden',
                overflowY: 'auto'
            }
        },
        // 是否首页
        isHome () {
            return ['tenderHall', 'biddingHall'].includes(this.$route.name)
        }
    },
    data () {
        return {
            routerAlive: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingHall`, '招标大厅'),
            menus: [],
            collapsed: false,
            currentRow: {},
            subPackageArr: [],
            tenderCurrentRow: {},
            currentSubPackage: {},
            subpackageList: [],
            spinning: false,
            subpackageId: null,
            projectId: '',
            currentHallName: '',
            canChangeSubPage: true,
            url: {
                getMenuUrl: '/tender/tenderProcessInstance/process/node',
                getSubpackageUrl: '/tender/purchaseTenderProjectHead/subpackage',
                nodeAduitUrl: '/tender/tenderProcessInstance/process/nodeAduit',
                querySubpackageInfoBySubpackageId: '/tender/purchaseTenderProjectHead/querySubpackageInfoBySubpackageId',
                getExecutorAuthorityUrl: '/tender/purchaseTenderProjectHead/getExecutorAuthority',
                getProjectUrl: '/tender/purchaseTenderProjectHead/queryById'
            },
            pathMap: {
                1: 'purchaseLink',
                2: 'saleLink',
                0: 'purchaseLink'
            },
            bgImageHeight: '',
            currentNode: {}
        }
    },
    provide () {
        return {
            tenderCurrentRow: this.$ls.get('SET_TENDERCURRENTROW'),
            currentSubPackage: () => this.currentSubPackage,
            subpackageId: () => this.subpackageId,
            currentNode: () => this.currentNode,
            resetCurrentSubPackage: this.resetCurrentSubPackage,
            handleLoading: this.handleLoading,
            stopLoading: this.stopLoading
        }
    },
    watch: {
        $route: {
            immediate: true,
            handler ({ path, query }) {
                this.$nextTick(() => {
                    this.routerAlive = true
                })
                this.title = path.indexOf('/tenderHall') !== -1 ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supBiddingHall`, '投标大厅') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingHall`, '招标大厅')
            }
        }
    },
    methods: {
        // 根据URL参数获取当前项目信息
        getProjectAction (id) {
            return getAction(this.url.getProjectUrl, {id}).then(res => {
                return res.result
            })
        },
        // 获取当前项目行信息
        async getCurrentRow () {
            // 信息公告跳转 判断url上是否存在项目ID
            if (this.$route.query?.tenderProjectId) {
                let {tenderProjectId, subpackageId} = this.$route.query
                // 根据URL项目ID异步获取当前项目信息
                this.tenderCurrentRow = await this.getProjectAction(tenderProjectId)
                // 获取的项目信息上没有当前分包ID需要赋值
                this.tenderCurrentRow['subpackageId'] = subpackageId
                // 兼容旧代码，供应商是tenderProjectId
                if (this.$route.path.indexOf('/biddingHall') == -1) {
                    this.tenderCurrentRow.tenderProjectId = this.tenderCurrentRow.id
                }
                // 获取完数据缓存起来
                this.$ls.set('SET_TENDERCURRENTROW', this.tenderCurrentRow)
            } else {
                // 通过点击项目行
                this.tenderCurrentRow = this.$ls.get('SET_TENDERCURRENTROW')
                console.log('tenderCurrentRow', this.tenderCurrentRow)
            }
        },
        handleChangeSubPackage (value) {
            // 请求获取当前分包最新信息
            this.getCurrentSubPackageInfo(value)
            this.$router.push({
                name: this.currentHallName
            })
            // 重新获取菜单
            this.getMenu(value)
        },
        // 接口请求分包数据
        getSubpackage () {
            // 区分招标大厅和投标大厅的分包获取字段
            let id = ''
            if (this.$route.path.indexOf('/tenderHall') !== -1) {
                id = this.tenderCurrentRow.tenderProjectId
                this.currentHallName = 'tenderHall'
            } else {
                id = this.tenderCurrentRow.id
                this.currentHallName = 'biddingHall'
            }
            this.spinning = true
            getAction(this.url.getSubpackageUrl, { id })
                .then(async (res) => {
                    if (res.success) {
                        this.subPackageArr = res.result.map((item) => {
                            return {
                                value: item.id,
                                label: item.subpackageName
                            }
                        })
                        this.subpackageList = res.result
                        // 角色处理
                        await this.applyRoleMapMethods()
                        let id = ''
                        // 供应商端获取行的分包ID
                        if (this.$route.path.indexOf('/tenderHall') !== -1) {
                            id = this.tenderCurrentRow.subpackageId
                        } else {
                            // 缓存id
                            id = this.subpackageId || res.result[0].id
                        }
                        this.getMenu(id)
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.spinning = false
                })
        },
        getExecutorAuthority (tenderCurrentRow) {
            return getAction('/tender/purchaseTenderProjectHead/getExecutorAuthority', {id: tenderCurrentRow.id}).then(res=>{
                return res.result
            })
        },
        // 获取用户角色
        async applyRoleMapMethods () {
            let {applyRole} = this.tenderCurrentRow
            // 信息公告跳转下，不带有用户角色信息
            if (!applyRole && applyRole != 0) {
                // 根据当前路由判断用户角色，如果是采购方再根据接口返回是否是执行人
                // applyRole = this.$route.path.indexOf('/tenderHall') !== -1 ? '2' : await this.getExecutorAuthority(this.tenderCurrentRow)
                if (this.$route.path.indexOf('/tenderHall') !== -1) {
                    applyRole = '2'
                } else {
                    let code = await this.getExecutorAuthority(this.tenderCurrentRow)
                    applyRole = code > 1 ? 0 : code
                }
                this.tenderCurrentRow['applyRole'] = applyRole
            }
            let map = {
                '0': () => {
                    // 获取缓存:刷新页面定位到之前分包
                    let currentSubPackage = this.getSessionStorageOfCurrentSubPackage()
                    let currentId = (this.tenderCurrentRow.subpackageId ?? '' != '') ? this.tenderCurrentRow.subpackageId : this.subpackageList[0].id
                    this.subpackageId = this.$route.query?.subpackageId ? (this.$route.query?.subpackageId) : (currentSubPackage ? currentSubPackage.id : currentId)
                    // 重新更新当前分包信息
                    this.getCurrentSubPackageInfo(this.subpackageId)
                    this.canChangeSubPage = true
                },
                '1': () => {
                    // 获取缓存:刷新页面定位到之前分包
                    let currentSubPackage = this.getSessionStorageOfCurrentSubPackage()
                    let currentId = (this.tenderCurrentRow.subpackageId ?? '' != '') ? this.tenderCurrentRow.subpackageId : this.subpackageList[0].id
                    this.subpackageId = this.$route.query?.subpackageId ? (this.$route.query?.subpackageId) : (currentSubPackage ? currentSubPackage.id : currentId)
                    // 重新更新当前分包信息
                    this.getCurrentSubPackageInfo(this.subpackageId)
                    this.canChangeSubPage = true
                },
                // 供应商
                '2': () => {
                    this.canChangeSubPage = false
                    let { subpackageId } = this.tenderCurrentRow
                    this.currentSubPackage = this.subpackageList.filter((item) => {
                        return item.id == subpackageId
                    })[0]
                    this.subpackageId = this.currentSubPackage.id
                }
            }
            map[applyRole]()
        },
        getMenu (subpackageId) {
            this.spinning = true
            getAction(this.url.getMenuUrl, { subpackageId, applyRole: this.tenderCurrentRow.applyRole })
                .then((res) => {
                    if (res.success) {
                        this.menus = []
                        if (!res.result || res.result.length == 0) return this.$message.error('当前分包没流程节点')
                        // 这里只写了两层，后面会重写
                        let pathCode = this.pathMap[this.tenderCurrentRow.applyRole]
                        let menus = res.result.map((item) => {
                            let children = item.nodeList.map((children) => {
                                return {
                                    ...children,
                                    meta: {
                                        title: children.nodeName,
                                        keepAlive: false
                                    },
                                    state: children.state,
                                    path: children[pathCode],
                                    route: '0',
                                    hightLightNode: children.hightLightNode
                                }
                            })
                            return {
                                // name: item.value,
                                meta: {
                                    type: 'purchase',
                                    title: item.name,
                                    icon: 'icon-111-02',
                                    keepAlive: false
                                },
                                key: `${item.name}_${item.value}`,
                                children: children
                            }
                        })
                        this.menus = menus
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.spinning = false
                })
        },
        onSelect (obj) {
            // console.log('obj', obj)
        },
        // 缓存当前节点
        setSessionStorageOfCurrentNode () {
            sessionStorage.setItem('tender_currentNode', JSON.stringify(this.currentNode))
        },
        // 获取缓存当前节点
        getSessionStorageOfCurrentNode () {
            this.currentNode = sessionStorage.getItem('tender_currentNode') ? JSON.parse(sessionStorage.getItem('tender_currentNode')) : {}
        },
        // 缓存当前分包
        setSessionStorageOfCurrentSubPackage () {
            let id = this.tenderCurrentRow.id
            let params = {}
            params[id] = this.currentSubPackage
            sessionStorage.setItem('tender_subpackage', JSON.stringify(params))
        },
        // 获取缓存:刷新页面定位到之前分包
        getSessionStorageOfCurrentSubPackage () {
            let cunrrtenPackageJson = sessionStorage.getItem('tender_subpackage') || ''
            let cunrrtenPackage = null
            if (cunrrtenPackageJson) {
                let id = this.tenderCurrentRow.id
                cunrrtenPackage = JSON.parse(cunrrtenPackageJson)[id] || null
            }
            return cunrrtenPackage
        },
        // 点击当前菜单节点
        setCurrentMenuTender (data) {
            let params = {
                subpackageId: this.subpackageId,
                nodeId: data.nodeId,
                applyRole: this.tenderCurrentRow.applyRole
            }
            console.log('@@@', params.applyRole)
            this.spinning = true
            getAction(this.url.nodeAduitUrl, params)
                .then((res) => {
                    if (res.success) {
                        if (res.result.nodeAuditStatus == '1') {
                            // 设置节点信息
                            this.currentNode = Object.assign({}, data)
                            if (this.currentNode.extend) {
                                if (isString(this.currentNode.extend)) this.currentNode.extend = JSON.parse(this.currentNode.extend)
                            } else {
                                this.currentNode.extend = { checkType: null, processType: null }
                            }
                            // 缓存当前节点信息
                            this.setSessionStorageOfCurrentNode()
                            this.$router.push({ path: data.path })
                        } else if (res.result.nodeAuditStatus == '2'){
                            this.$message.error(res.result.subpackageStatusDesc)
                        } else {
                            this.$refs.menu.selectedKeys = [this.$route.path]
                            this.$message.error(`当前分包状态:${res.result.subpackageStatusDesc}`)
                            // this.$message.error(`当前分包状态:${res.result.subpackageStatusDesc},不允许操作【${data.nodeName}】`)
                        }
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.spinning = false
                })
                .catch((error) => {
                    this.$message.error(error.message)
                })
        },
        // 请求获取当前分包最新信息
        getCurrentSubPackageInfo (id) {
            getAction(this.url.querySubpackageInfoBySubpackageId, { subpackageId: id }).then((res) => {
                if (res.success) {
                    this.currentSubPackage = res.result
                    // 如果状态为终止招标，则页面都为详情页（效果同非当前执行人applyrole=0）
                    if(res.result.status == 6210){
                        this.tenderCurrentRow['applyRole'] = 0
                        this.$ls.set('SET_TENDERCURRENTROW', this.tenderCurrentRow)
                    }
                    // 缓存当前分包
                    this.setSessionStorageOfCurrentSubPackage()
                } else {
                    // 请求失败时候，拿取本地
                    this.currentSubPackage = this.subpackageList.filter((item) => {
                        return item.id == id
                    })[0]
                }
            })
        },
        // 更新当前分包信息
        resetCurrentSubPackage () {
            let { id } = this.currentSubPackage
            this.getCurrentSubPackageInfo(id)
            console.log('jinlaile')
        },
        handleLoading () {
            this.spinning = true
        },
        stopLoading () {
            this.spinning = false
        },
        filterOption (input, option) {
            return (
                option.componentOptions.children[0].text.indexOf(input) >= 0
            )
        },
        async init () {
            await this.getCurrentRow()
            this.getSessionStorageOfCurrentNode()
            this.getSubpackage()
            // this.getMenu()
        }
    },
    beforeRouteUpdate (to, from, next) {
        this.routerAlive = false
        next()
    },
    created () {
        this.bgImageHeight = document.documentElement.clientHeight - 50
        this.init()
        layIM.init()
    }
}
</script>

<style lang="less" scoped>
:deep(.page-container){
    background-color: #fff;
}
.fl {
    float: left;
}
.fr {
    float: right;
}
.inline_block {
    display: inline-block;
}
.blue{
   color: #6AFFFF
}
.before{
    position: absolute;
    left: -11px;
    top: 19px;
    width: 1px;
    height: 12px;
    background-color: #fff;
    content: "";
}
.currentTender {
    color: #fff;
}
.currentTenderItem {
    margin-left: 20px;
    position: relative;
    &:before{
        .before
    }
}
.width180 {
    width: 180px;
}

.openBidStatus {
    font-weight: 400;
    color: #fff;
    margin-left: 20px;
    position: relative;
    &:before{
        .before
    }
}
.hall-layout {
    .header {
        // display: flex;
        // justify-content: space-between;
        padding: 0 16px 0 0;
        height: 50px;
        background: #4e85ff;
        line-height: 50px;
        .logo {
            width: 200px;
            text-align: center;
            font-weight: 400;
            font-size: 25px;
            color: #fff;
        }
        .logoP{
            height: 30px;
            vertical-align: sub;
            margin-right:10px; 
        }
    }
    // 菜单高度修改(因为SMenu是一个js组件)
    :deep(.ant-menu-inline > .ant-menu-item){
        height: 28px;
        line-height: 28px;
    }
    :deep(.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title){
        height: 28px;
        line-height: 28px;
    }
    :deep(.ant-menu-sub.ant-menu-inline > .ant-menu-item),
    .ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
        height: 28px;
        line-height: 28px;
    }
    .bgImage{
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
