<template>
  <vue-echarts
    :style="style"
    :option="widget.option"
    :update-options="{ notMerge: true }"
    autoresize
  />
</template>
<script>
import { chartsMixins } from '@comp/chart/widget/mixins/chartsMixins'
import _ from 'lodash'
export default {
    name: 'ChartsLineChart',
    mixins: [chartsMixins],
    computed: {
        option () {
            return {
                smooth: this.widget.option.series[0].smooth,
                itemStyle: this.widget.option.series[0].itemStyle
            }
        }
    },
    watch: {
        option: {
            handler (val) {
                for (let i = 1; i < this.widget.option.series.length; i++) {
                    this.widget.option.series[i].smooth = val.smooth
                    this.widget.option.series[i].itemStyle = _.cloneDeep(val.itemStyle)
                }
            },
            deep: true
        }
    },
    mounted () {
        this.refreshWidgetInfo()
    },
    methods: {
        // refreshWidgetData (data) {     
        //     const option = this.widget.option
        //     option.xAxis.data = data.xAxisData
        //     option.series[0].data=data.seriesData      
        // },
        refreshWidgetData (data) {
            debugger
            const option = this.widget.option
            option.xAxis.data = data.xAxisData
            // 每次刷新都只保留一个Series元素，其他的删掉
            option.series.splice(1, option.series.length - 1)
            for (let i = 0; i < data.seriesData.length; i++) {
                let chartData = data.seriesData[i]
                let item = option.series[i]
                if (!item) {
                    // 如果数据长度大于配置长度，则深拷贝一个配置出来
                    option.series.push(_.cloneDeep(option.series[0]))
                    item = option.series[i]
                }
                item.name = chartData.name
                item.data = chartData.data
            }
        }
    }
}
</script>