<template>
  <div class="SaleFadadaPersonal business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="edit"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {postAction } from '@/api/manage'
import REGEXP from '@/utils/regexp'
import {BUTTON_SAVE, BUTTON_BACK } from '@/utils/constant.js'

export default {
    name: 'EditSaleFadadaPersonalModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/saleFadadaPersonal/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/electronsign/fadada/saleFadadaPersonal/edit'
                    },
                    authorityCode: 'fadada#saleFadadaPersonal:edit'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LilbDJ_29aa74ec`, '认证授权提交'),
                    key: 'publish',
                    attrs: {
                        type: 'primary'
                    },
                    args: {
                        url: '/electronsign/fadada/saleFadadaPersonal/auth'
                    },
                    click: this.handleCustomPublish,
                    showCondition: this.showButton,
                    authorityCode: 'fadada#saleFadadaPersonal:auth'
                },
                BUTTON_BACK
            ],
            url: {
                save: '/electronsign/fadada/saleFadadaPersonal/edit',
                auth: '/electronsign/fadada/saleFadadaPersonal/auth',
                detail: '/electronsign/fadada/saleFadadaPersonal/queryById'
            }
        }
    },
    methods: {
        showButton () {
            const params = this.getAllData()
            //未保存单据，按钮隐藏
            if(!params.id || params.id ==''){
                return false
            }
            return true
        },            
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEsNo`, '采购ELS号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'busAccount',
                        required: '1',
                        initFunction: 
                        function initFunction (Vue, { _pageData, _form, _row, _value, _cacheAllData, _data }) {
                            let { busAccount = '', companyName = ''} = _data[0] || {}
                            _form.busAccount = busAccount
                            _form.companyName = companyName
                        },
                        bindFunction: function bindFunction (Vue, data) {
                            if (Vue.pageConfig) {
                                let formModel = Vue.pageConfig.groups[0].formModel
                                formModel.busAccount = data[0].elsAccount
                                formModel.companyName = data[0].name
                            }
                        },
                        extend: {
                            modalColumns: [
                                {field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEsNo`, '采购ELS号'), with: 150},
                                {field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRRL_4460e249`, '采购名称'), with: 150}
                            ], modalUrl: '/supplier/supplierMaster/querySupplierData', modalParams: {}
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRRL_4460e249`, '采购名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'companyName',
                        required: '1',
                        disabled: true
                    },
                    // {
                    //     groupCode: 'baseForm',
                    //     fieldType: 'input',
                    //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccountId`, '用户id'),
                    //     fieldLabelI18nKey: '',
                    //     fieldName: 'clientUserId',
                    //     required: '1',
                    //     disabled: true
                    // },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_userAccount`, '用户账号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'subAccount',
                        required: '1',
                        initFunction: 
                        function initFunction (Vue, { _pageData, _form, _row, _value, _cacheAllData, _data }) {
                            let {  subAccount = '', userName = '', mobile = '', email = '' } = _data[0] || {}
                            _form.subAccount = subAccount
                            _form.userName = userName
                            _form.mobile = mobile
                            _form.email = email
                        },
                        bindFunction: function bindFunction (Vue, data) {
                            if (Vue.pageConfig) {
                                let formModel = Vue.pageConfig.groups[0].formModel
                                formModel.subAccount = data[0].subAccount
                                formModel.userName = data[0].realname
                                formModel.mobile = data[0].phone
                                formModel.email = data[0].email
                            }
                        },
                        extend: {
                            modalColumns: [
                                {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150},
                                {field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150},
                                {field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_phone`, '手机号'), with: 150},
                                {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'), with: 150}
                            ], modalUrl: '/account/elsSubAccount/list', modalParams: {status: 1}
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '用户名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userName',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jDdieyAc_c344b054`, '用户注册账号类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'accountType',
                        dictCode: 'fadadaAccountType',
                        defaultValue: 'mobile',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hffjDey_658023d5`, '法大大用户账号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'accountName',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userIdentType',
                        dictCode: 'fadadaUserIdentType',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userIdentNo',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_phone`, '手机号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'mobile',
                        regex: REGEXP.mobile,
                        alertMsg: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ltymKxiR_71211a7d`, '手机号格式不正确'),
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'),
                        fieldLabelI18nKey: '',
                        fieldName: 'email',
                        regex: REGEXP.email,
                        alertMsg: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_jdmKxiR_71317622`, '邮箱格式不正确'),
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_WEmy_45c0d14c`, '银行卡号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'bankAccountNo',
                        regex: REGEXP.unionPayCard,
                        alertMsg: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WEmymKxiR_620c9339`, '银行卡号格式不正确'),
                        required: ''
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'multiple',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jDLiCK_94dc6462`, '用户认证方式'),
                        fieldLabelI18nKey: '',
                        fieldName: 'identMethod',
                        dictCode: 'fadadaUserIdentMethod',
                        required: ''
                    },
                    // {
                    //     groupCode: 'baseForm',
                    //     fieldType: 'multiple',
                    //     fieldLabel: '认证时页面不可编辑信息',
                    //     fieldLabelI18nKey: '',
                    //     fieldName: 'nonEditableInfo',
                    //     dictCode: 'fadadaUserNonEditableInfo',
                    //     required: ''
                    // },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'multiple',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbvL_2ed2664c`, '授权范围'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authScopes',
                        dictCode: 'fadadaUserAuthScopes',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'identProcessStatus',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbyR_2ed1f524`, '授权结果'),
                        fieldLabelI18nKey: '',
                        fieldName: 'authResult',
                        disabled: true
                    }
                ]
            }
        },
        handleCustomPublish (args){
            // 获取页面所有数据
            const allData = this.getAllData() || {}
            // 这里可以添加自定义校验逻辑
            if (!allData.id || allData.id=='') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WsM_13b2663`, '先保存'))
                return
            }

            // 默认保存方法不会校验当前业务模板必填字段
            // 此处手动触发校验方法
            this.stepValidate(args).then(() => {
                postAction(this.url.auth, allData).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.$emit('handleChidCallback', allData)
                    }
                })

            })
        }
    }
}
</script>
