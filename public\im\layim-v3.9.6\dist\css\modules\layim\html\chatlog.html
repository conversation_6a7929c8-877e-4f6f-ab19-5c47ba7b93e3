<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <title>聊天记录</title>

    <link rel="stylesheet" href="/im/layim-v3.9.6/dist/css/layui.css" />
    <style>
      body .layim-chat-main {
        height: auto;
        padding-bottom: 60px;
      }

      body .layim-chat-main >ul >li.layim-chat-undo {
        line-height: 68px;
        text-align: center;
        font-size: 12px;
        color: #393D49;
        padding: 0;
        margin: 0;
      }

      .lay-chat-page {
        box-shadow: 1px 1px 6px black;
        position: fixed;
        bottom: 0;
        width: 100%;
        box-sizing: border-box;
        padding: 0 10px;
        padding-top: 5px;
        background: white;
      }
    </style>
  </head>
  <body>
    <div class="layim-chat-main">
      <ul id="LAY_view"></ul>
    </div>
    <div id="LAY_page" class="lay-chat-page"></div>


    <textarea title="消息模版" id="LAY_tpl" style="display: none">
      {{# layui.each(d.data, function(index, item){
        if(item.undoStatus == 1){ }}
        <li class="layim-chat-undo">{{ layui.layim.content(item.content) }}</li>
        {{# } else if(item.id == parent.layui.layim.cache().mine.id){ }}
          <li class="layim-chat-mine"><div class="layim-chat-user"><img src="{{ item.avatar }}"><cite><i>{{ layui.data.date(item.timestamp) }}</i>{{ item.username }}</cite></div><div class="layim-chat-text">{{ layui.layim.content(item.content) }}</div></li>
        {{# } else { }}
          <li><div class="layim-chat-user"><img src="{{ item.avatar }}"><cite>{{ item.username }}<i>{{ layui.data.date(item.timestamp) }}</i></cite></div><div class="chatlog layim-chat-text">{{ layui.layim.content(item.content) }}</div></li>
        {{# }
      }); }}
    </textarea>

    <!-- 
上述模版采用了 laytpl 语法，不了解的同学可以去看下文档：http://www.layui.com/doc/modules/laytpl.html

-->

    <script src="/im/layim-v3.9.6/dist/layui.js"></script>
    <script>
      layui.use(['layim', 'laypage'], function () {
        var layim = layui.layim,
          layer = layui.layer,
          laytpl = layui.laytpl,
          $ = layui.jquery,
          laypage = layui.laypage;

        var token = localStorage.getItem('t_token');
        var fromId = sessionStorage.getItem('currentUserId');
        var pageSize = 50
        var pageNo = 1
        var count = 1

        //聊天记录的分页此处不做演示，你可以采用laypage，不了解的同学见文档：http://www.layui.com/doc/modules/laypage.html

        //开始请求聊天记录
        const api = '/message/list';
        var BASE_URL = localStorage.getItem('IM_BASE_URL') || '//v5sit.51qqt.com/els/im'
        // if (location.hostname.includes('localhost')) {
        //   BASE_URL = 'http://localhost:11888/els/im'
        // }

        var param = location.search; //获得URL参数。该窗口url会携带会话id和type，他们是你请求聊天记录的重要凭据
        param = param.replace('?id=', '?chatId=');
        param = param.replace('&type=', '&chatType=');
        var url = BASE_URL + api + param + "&fromId=" + fromId + "&pageNo=" + pageNo + "&pageSize=" + pageSize
        getListInfo(url, 'first')
        // 分项change事件
        function jumpCallBack (obj, first) {
          pageSize = obj.limit
          pageNo = obj.curr
          if (!first) {
            var lastUrl = BASE_URL + api + param + "&fromId=" + fromId + "&pageNo=" + pageNo + "&pageSize=" + pageSize
            getListInfo(lastUrl)
          }
        }
        
        // 获取聊天数据
        function getListInfo (url, first) {
          $.ajax({
            headers: {
              'X-Access-Token': token
            },
            url,
            type: 'get',
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            success: function (res) {
              var messageList = res.messageList || []
              var mainId = parent.layui.layim.cache().mine.id
              var thisChat = parent.layui.layim.thisChat() || {}
              var name = thisChat.data.name || ''
              for (var i = 0, len = messageList.length; i < len; i++) {
                var item = messageList[i] || {}
                if (item.undoStatus == 1) {
                  var content = '撤回了一条消息'
                  var pre = mainId == item.fromid ? '您' : name
                  if (pre && pre.length > 25) {
                    pre = pre.slice(0, 25) + '...';
                  }
                  content = pre + content
                  item.content = content
                  console.log('item :>> ', item);
                }
              }
              
              var html = laytpl(LAY_tpl.value).render({
                data: messageList
              });
              if (first) {
                laypage.render({
                  elem: 'LAY_page',
                  limit: pageSize,
                  groups: 1,
                  layout: ['prev', 'page', 'next', 'limit'],
                  jump: jumpCallBack,
                  limits: [50, 100, 200, 1000],
                  count: res.count //数据总数，从服务端得到
                });
              }
              $('#LAY_view').html(html);
              window.scrollTo(0,0)
            },
            error: function () {
              console.log('ajax error');
            }
          });
        }
      });
    </script>
  </body>
</html>
