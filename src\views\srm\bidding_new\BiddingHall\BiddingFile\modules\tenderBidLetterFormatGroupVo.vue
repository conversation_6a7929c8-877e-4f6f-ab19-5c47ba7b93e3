<template>
  <div>
    <div
      class="bassForm"
      v-if="this.checkType !== '0'">
      <titleTrtl class="margin-b-10">
        <span>{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_suVH_2e09d920`, '报价信息')) }}</span>
      </titleTrtl>
      <Dataform
        ref="dataform"
        :pageStatus="pageStatus"
        :formData="formData"
        :fields="fields" />
    </div>
    <div class="tenderBidTetterVoBox">
      <div
        class="tenderBidTetterVoList" 
      >
        <titleTrtl class="margin-b-10">
          <span>{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_eBxVH_9dbaaa19`, '投标函信息')) }}</span>
          <template
            slot="right"
            v-if="isEdit">
            <a-button
              type="primary"
              size="small"
              v-if="formData.quoteType"
              @click="handleAddTetterVoItem">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VaeBx_c2bd847d`, '新增投标函') }}</a-button>
          </template>
        </titleTrtl>
        <div
          class="tenderBidTetterBox"
          :style="{height: `${tenderBidTetterVoListHeight}px`}"
        >
          <div
            class="tetterVoItem"
            :class="tetterVoModalIndex == i ? 'activeBox': 'normalBox'"
            v-for="(item, i) in tetterVoData"
            :key="i"
            @click="radioChange(item, i)">
            <div class="tetterVoItemLine margin-b-10">
              <span class="flex3 colorName">{{ item.name }}</span>
              <a-icon
                v-if="isEdit"
                class="flex1"
                type="edit"
                theme="twoTone"
                @click="(e) => {handleEditTetterVoItem(item, i, e)}"/>
            </div>
            <div class="tetterVoItemLine">
              <span
                class="flex3 "
                :class="item.formatType == '9' ? 'colorFormatTypeNormal': 'colorFormatType'">{{ item.formatType ? formatTypeOptions[item.formatType] : "" }}</span>
              <a-icon
                v-if="isEdit"
                class="flex1"
                type="delete"
                @click="(e) => {handleDelTetterVoItem(item, i, e)}"
                theme="twoTone" />
            </div>
          </div>
        </div>
        <tetterVoModal
          :tetterVoData="tetterVoData"
          :baseFormData="formData"
          @confirm="confirmTetterVoItem"
          @resetCurrentRow="resetCurrentRow"
          ref="tetterVoModal" />
      </div>
      <div class="infoRightBox">
        <listInformation
          v-show="isTetterListTable"
          key="listInformation"
          @handleDeleteColumn="handleDeleteColumn"
          @handleAddTableColumn="handleAddTableColumn"
          @maintenanceMaterial="maintenanceMaterial"
          @handleTableAdd="handleTableAdd"
          @handleTableDel="handleTableDel"
          ref="listInformation"
          :formData="formData"
          :pageStatus="pageStatus"
          :currentRow="currentRow"
        />
        <materialList 
          v-if="!isTetterListTable"
          key="materialList"
          ref="materialList"
          @back="showTetterListTable"
          @addMaterialList="addMaterialList"
          @handleDelectMaterial="handleDelectMaterial"
          :formData="formData"
          :pageStatus="pageStatus"
          :currentRow="currentRow"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import Dataform from '@views/srm/bidding_new/BiddingHall/components/Dataform.vue'
import listInformation from './components/listInformation'
import tetterVoModal from './components/tetterVoModal'
import materialList from './components/materialList'
import titleTrtl from '../../components/title-crtl'
import { cloneDeep } from 'lodash'
export default {
    name: 'TenderBidLetterFormatGroupVo',
    mixins: [tableMixins, baseMixins],
    props: {
        tenderBidLetterFormatGroupVo: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: '',
            type: String
        }
    },
    inject: ['currentSubPackage'],
    components: {
        listInformation,
        titleTrtl,
        tetterVoModal,
        Dataform,
        materialList
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        tenderQuotationsType () {
            if (this.checkType == '0') {
                return 'preTenderFormatType'
            } else {
                if (this.processType == '1' || (this.$ls.get('changeBidFile') && this.currentSubPackage().processType == '1')) {
                    return 'resultTenderFormatType '
                }
                return 'tenderFormatType'
            }
        }
    },
    data () {
        return {
            fields: [
                {
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suAc_2e0f36e2`, '报价类型')),
                    fieldLabelI18nKey: '',
                    field: 'quoteType',
                    fieldType: 'select',
                    dictCode: 'tenderQuoteType',
                    bindFunction: this.handleChangeQuoteType,
                    disabledValueList: [],
                    required: '1'
                },
                {
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suCK_2e0c7ce8`, '报价方式')),
                    fieldLabelI18nKey: '',
                    field: 'quotations',
                    fieldType: 'select',
                    dictCode: 'tenderQuotations',
                    required: '1'
                },
                {
                    title: this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_suAWFwj_aefa8d2e`, '报价列数据来源')),
                    fieldLabelI18nKey: '',
                    field: 'quoteColumnSource',
                    fieldType: 'select',
                    dictCode: 'tenderQuoteColumnSource',
                    required: '1'
                }
            ],
            currentRow: null,
            formData: {},
            formatTypeOptions: {},
            tetterVoData: [],
            tetterVoModalType: 'add',
            tenderBidTetterVoListHeight: '200',
            tetterVoModalIndex: null,
            isTetterListTable: true,
            quoteColumnList: []
        }
    },
    methods: {
        // 获取表格下拉字典
        queryDictData (dictCode) {
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: dictCode
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    res.result.map(({value, text, title}) => {
                        this.formatTypeOptions[value] = title
                        return {
                            value,
                            title,
                            label: text
                        }
                    })
                    this.$forceUpdate()
                }
            })
        },
        // 校验
        getValidatePromise () {
            return new Promise((resolve, reject) => {
                let arr = this.checkType == '1' ? [this.$refs.dataform.getValidatePromise(), this.checkTenderList()]: [this.checkTenderList()]
                let PromiseAll = arr.filter((promise) => promise)
                Promise.all(PromiseAll)
                    .then((res) => {
                        resolve(res)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        },
        checkTenderList () {
            let that = this
            return new Promise((resolve, reject) => {
                if (!this.tetterVoData || this.tetterVoData.length == 0) {
                    that.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSueBx_bfc746f`, '请添加投标函'))
                    reject('请添加投标函')
                    return
                }
                // 分项报价
                if (this.formData.quoteType == '1') {
                    let tableItem = null
                    let num = 0
                    let num1 = 0
                    this.tetterVoData.map((item) => {
                        // 其他类型不需要校验
                        if (item.formatType !== '9') {
                            num++
                            // 是否需要关联物料行信息
                            if (item.quoteBidLetter == '1') {
                                num1++
                            }
                            if (!item.customizeFieldData || item.customizeFieldData.length == 0) tableItem = item
                        }
                    })
                    if (num == 0) {
                        that.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSuvBIBB_cc39227d`, '请添加开标一览表'))
                        reject('请添加开标一览表')
                        return
                    }
                    if (num1 == 0) {
                        that.$message.error('投标函中未关联物料行信息')
                        reject('投标函中未关联物料行信息')
                        return
                    }
                    if (tableItem) {
                        that.$message.error(`投标函[${tableItem.name}]:请维护物料行信息`)
                        reject(`投标函[${tableItem.name}]:请维护物料行信息`)
                        return
                    }
                } else {
                    // 总项报价
                    let tableItem = null
                    let num = 0
                    for (let item of this.tetterVoData) {
                        // 其他类型不需要校验
                        if (item.formatType == '9') continue
                        num++
                        // 后审才需要校验物料行
                        if (this.checkType =='1') {
                            if (tableItem) continue
                            let quotations = item.customizeFieldModel && item.customizeFieldModel.filter((item) => {
                                // 从自定义列过滤报价列
                                return item.fieldCategory == '1'
                            }) || []
                            // 报价列 => 数据源：手动输入不需要校验 => 物料行
                            if(quotations.length > 0 && this.formData.quoteColumnSource != '0') {
                                if (!item.quoteColumnList || item.quoteColumnList?.length == 0) {
                                    that.$message.error(`报价列数据来源为含税总价/单价之和，投标函[${item.name}]请维护物料行信息`)
                                    reject(`报价列数据来源为含税总价/单价之和，投标函[${item.name}]请维护物料行信息`)
                                    tableItem = item
                                    break
                                } else {
                                    for (let quoteColumnItem of item.quoteColumnList) {
                                        if (quoteColumnItem.materialDataList.length == 0) {
                                            tableItem = item
                                            that.$message.error(`报价列数据来源为含税总价/单价之和，投标函[${item.name}]的报价列[${quoteColumnItem.title}]:需维护物料信息`)
                                            reject(`报价列数据来源为含税总价/单价之和，投标函[${item.name}]的报价列[${quoteColumnItem.title}]:需维护物料信息`)
                                            break
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (num == 0) {
                        that.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSuvBIBB_cc39227d`, '请添加开标一览表'))
                        reject('请添加开标一览表')
                    }
                            
                }
                resolve()
            })
        },
        // 投标函数据
        externalTableData () {
            let p = cloneDeep(this.tetterVoData)
            p.map((item) => {
                item['customizeFieldData'] = item['customizeFieldData'] ? JSON.stringify(item['customizeFieldData']) : '[]'
                // 过滤掉排序和操作
                let customizeFieldModel =
                    item['customizeFieldModel'] &&
                    item['customizeFieldModel']
                        .filter((custom) => {
                            return !['seq', 'grid_opration'].includes(custom.key)
                        })
                        .map((item) => {
                            // 删除插槽
                            delete item.slots
                            return item
                        })
                item['customizeFieldModel'] = JSON.stringify(customizeFieldModel)
            })
            return p
        },
        // 向外抛数据
        externalAllData () {
            let { quoteType, quotations, quoteColumnSource } = Object.assign({}, this.formData)
            let tenderBidTetterVoList = this.externalTableData()
            let params = {
                quoteType,
                quotations,
                quoteColumnSource,
                tenderBidTetterVoList
            }
            return params
        },
        // 投标函列表选择切换
        radioChange (row, i) {
            this.tetterVoModalIndex = i
            this.currentRow = row
            this.$refs.listInformation.initColumns(this.currentRow)
            this.isTetterListTable = true
        },
        // 投标函行提交
        handleTableAdd () {
            this.currentRow.customizeFieldData.push({})
            this.$refs.listInformation.reloadData()
        },
        // 投标函行删除
        handleTableDel (index) {
            this.currentRow.customizeFieldData.splice(index, 1)
            this.$refs.listInformation.reloadData()
        },
        // 报价类型切换清除价格开标一览表信息
        handleChangeQuoteType (v, item) {
            // 报价列数据来源禁用选项
            // this.fields[2].disabledValueList = v == '1' ? ['0', '1', '2'] : ['3', '4']
            this.fields[2].disabledValueList = v == '1' ? ['0', '1', '2', '4'] : ['3', '4']
            // 报价列数据来源默认值
            this.formData.quoteColumnSource = v == '0' ? '0' : '3'
            if (this.tetterVoData.length == 0) return 
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQTPFSsuAc_e2340e35`, '是否需要切换报价类型'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WLsuAcxIVeBxVHCcxIRWmMGKEWF_f4f63b81`, '因为报价类型不一样投标函信息内容不一致，因此会初始化数据！'),
                onOk: () => {
                    // 清空数据
                    this.currentRow = null
                    this.isTetterListTable = true
                    this.tetterVoModalIndex = null
                    // 投标函数据清空
                    this.tetterVoData = []
                },
                onCancel: () => {
                    this.formData.quoteType = v == '0' ? '1' : '0'
                    this.fields[2].disabledValueList = v == '0' ? ['0', '1', '2', '4'] : ['3', '4']
                }
            })
        },
        init ({ tenderBidLetterFormatGroupVo }) {
            this.formData = tenderBidLetterFormatGroupVo || {quoteType: '0', quoteColumnSource: '0', quotations: '0'}
            if (this.formData.tenderBidTetterVoList) {
                this.tetterVoData = this.formData.tenderBidTetterVoList.map((item) => {
                    item.customizeFieldData = JSON.parse(item.customizeFieldData)
                    item.customizeFieldModel = JSON.parse(item.customizeFieldModel)
                    return item
                })
                this.radioChange(this.tetterVoData[0], 0)
                if (this.$refs.listInformation) {
                    this.$refs.listInformation.initColumns(this.currentRow)
                }
            }
            this.fields[2].disabledValueList = this.formData.quoteType == '1' ? ['0', '1', '2', '4'] : ['3', '4']
        },
        // 新增确认投标函信息
        confirmTetterVoItem (data) {
            if (this.tetterVoModalType == 'add') {
                this.tetterVoData.push(data)
            } else {
                this.$set(this.tetterVoData, this.tetterVoModalIndex, data)
                this.radioChange(this.tetterVoData[this.tetterVoModalIndex], this.tetterVoModalIndex)
            }
        },
        // 新增投标函信息
        handleAddTetterVoItem () {
            this.tetterVoModalType = 'add'
            this.$refs.tetterVoModal.open()
        },
        // 编辑投标函信息
        handleEditTetterVoItem (row, i, e) {
            this.tetterVoModalType = 'edit'
            this.tetterVoModalIndex = i
            this.$refs.tetterVoModal.open(row)
        },
        // 删除投标函信息
        handleDelTetterVoItem (row, i, e) {
            e.stopPropagation()
            this.tetterVoModalIndex = null
            this.currentRow = null
            this.tetterVoData.splice(i, 1)
        },
        maintenanceMaterial () {
            if (!this.currentRow) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFYBx_dbe46160`, '请先选择招标函'))
            let quoteColumnList = cloneDeep(this.currentRow.customizeFieldModel).filter((item) => {
                // 从自定义列过滤报价列
                return item.fieldCategory == '1'
            })
            // 校验物料列
            if (quoteColumnList.length == 0) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSusuA_bf9a7a9`, '请添加报价列'))
            
            quoteColumnList.map(quoteItem => {
                if (!quoteItem.materialDataList) {
                    quoteItem.materialDataList = []
                    // 如果存在物料行
                    if (this.currentRow.quoteColumnList && this.currentRow.quoteColumnList.length > 0) {
                        // 每个物料列映射对应物料行
                        this.currentRow.quoteColumnList.map(materitItem => {
                            if (materitItem.field == quoteItem.field && materitItem?.materialDataList) quoteItem.materialDataList.push(...materitItem.materialDataList)
                        })
                    }
                }
            })
            this.currentRow.quoteColumnList = quoteColumnList
            this.isTetterListTable = false
        },
        showTetterListTable () {
            this.isTetterListTable = true
        },
        resetCurrentRow (data) {
            this.isTetterListTable = true
            if (this.tetterVoModalType != 'add') {
                this.$set(this.currentRow, 'customizeFieldData', data.customizeFieldData)
                this.$set(this.currentRow, 'quoteColumnList', [])
                this.$set(this.currentRow, 'customizeFieldModel', data.customizeFieldModel)
                this.$refs.listInformation.initColumns(this.currentRow)
            }
        },
        handleDeleteColumn ({i, col}) {
            this.currentRow.customizeFieldData && this.currentRow.customizeFieldData.map((row) => {
                delete row[col.property]
            })
            this.currentRow.customizeFieldModel.splice(i, 1)
            this.$set(this.currentRow, 'customizeFieldModel', this.currentRow.customizeFieldModel)
            if (this.currentRow.quoteColumnList) {
                this.currentRow.quoteColumnList = this.currentRow.quoteColumnList.filter(item => {
                    return item.field != col.property
                })
            }
        },
        handleAddTableColumn (columns) {
            let index = this.currentRow.formatType == '9' ? this.currentRow.customizeFieldModel.length - 1 : this.currentRow.customizeFieldModel.length
            this.currentRow.customizeFieldModel.splice(index, 0, ...columns)
        },
        addMaterialList ({activeKey, data}) {
            if (!this.currentRow.quoteColumnList[activeKey].materialDataList) this.currentRow.quoteColumnList[activeKey].materialDataList = []
            this.currentRow.quoteColumnList[activeKey].materialDataList.push(...data)
            this.$refs.materialList.reloadData()
            // 如果报价类型为分项报价，需要把物料行同步到投标函
            if (this.formData.quoteType == '1') {
                let arr = data.map(item => {
                    return {
                        materialName: item.materialName,
                        materialNumber: item.materialNumber,
                        materialId: item.materialId
                    }
                })
                this.currentRow.customizeFieldData.push(...arr)
                this.$refs.listInformation.reloadData()
                this.$set(this.currentRow, 'customizeFieldData', this.currentRow.customizeFieldData)
            }
        },
        handleDelectMaterial ({activeKey, data, checkboxRecords}) {
            this.currentRow.quoteColumnList[activeKey].materialDataList = data
            this.$refs.materialList.reloadData()
            // 如果报价类型为分项报价，需要把物料行同步到投标函
            if (this.formData.quoteType == '1') {
                checkboxRecords.map(item => {
                    this.currentRow.customizeFieldData = this.currentRow.customizeFieldData.filter(row => {
                        return row.materialId !== item.materialId
                    })
                })
                this.$refs.listInformation.reloadData()
            }
        }
    },
    created () {
        this.queryDictData(this.tenderQuotationsType)
        this.tenderBidTetterVoListHeight = this.checkType == '1' ? document.documentElement.clientHeight - 400 : document.documentElement.clientHeight - 300
    },
    mounted () {
        this.init({tenderBidLetterFormatGroupVo: this.tenderBidLetterFormatGroupVo})
        console.log(this.currentSubPackage())
    }
}
</script>
<style lang="less" scoped>
.fl{
    float: left;
}
.fr{
    float: right;
}
.clearFix{
    clear: both;
}
.margin-b-10{
    margin-bottom: 10px;
}
.tenderBidTetterVoBox{
    display: flex;
}
.tenderBidTetterVoList{
    .tenderBidTetterBox{
        overflow: auto;
    }
    min-width: 200px;
    padding: 5px;
    border: 1px solid #e8e8e8;
    flex: 17%;
    margin-right: 10px;
}
.infoRightBox{
    flex: 78%;
    padding: 5px;
    border: 1px solid #e8e8e8;
    min-width: 400px;
}
.tetterVoItem{
    padding: 5px 10px;
    cursor: pointer;
    margin-bottom: 10px;
    .tetterVoItemLine{
        display: flex;
    }
    .flex1{
        flex: 1;
        height: 21px;
        :deep(svg){
            height: 21px;
        }
    }
    .flex3{
        flex: 5;
        overflow: hidden;
    }
}
.normalBox{
    border: 1px solid #ccc;
}
.activeBox {
    border: 1px solid #1890ff;
}
.colorName{
    color: #1890ff;
}
.colorFormatType{
    color: #e68824;
}
.colorFormatTypeNormal{
    color: #ccc;
}
</style>
