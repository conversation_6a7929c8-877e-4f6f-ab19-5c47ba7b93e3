<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :remoteJsFilePath="remoteJsFilePath"
        :pageFooterButtons="pageFooterButtons"
        :externalToolBar="externalToolBar"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
    <customSelectModal
      ref="customSelectModal"
      @ok="selectDataOk"
    />
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import {getLangAccount, srmI18n} from '@/utils/util.js'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import REGEXP from '@/utils/regexp'
import {
    composePromise
} from '@/utils/util.js'
import {
    BUTTON_SAVE,
    BUTTON_BACK
} from '@/utils/constant.js'
export default {
    name: 'SupplierInfoChangeHeadModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout,
        customSelectModal: () =>import('../components/frozenFunctionModal.vue')
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            pageFooterButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/supplier/supplierInfoChangeHead/edit'
                    },
                    click: this.saveEvent
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_savePublish`, '保存并发布'),
                    key: 'submit',
                    args: {
                        url: '/supplier/supplierInfoChangeHead/releaseByIds'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    click: this.submitEvent
                },
                BUTTON_BACK
            ],
            externalToolBar: {
                supplierContactsInfoList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addContactsItem,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteContactsEvent
                    }
                ],
                supplierAddressInfoList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addAddressItem,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteAddressEvent
                    }
                ],
                supplierBankInfoList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addBankItem,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteBankEvent
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        beforeChecked: true,
                        beforeCheckedCallBack: this.bankFileUploadCallBack,
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'supplierMasterData', // 必传,
                            multiple: false,
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        callBack: this.uploadCallBack
                    },
                    {...new BatchDownloadBtn({pageCode: 'supplierBankInfoList'}).btnConfig}
                ],

                supplierMasterCustom1List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],
                supplierMasterCustom2List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],
                supplierMasterCustom3List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],
                supplierMasterCustom4List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],
                supplierMasterCustom5List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],
                supplierMasterCustom6List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],
                supplierMasterCustom7List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],
                supplierMasterCustom8List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],
                supplierMasterCustom9List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],
                supplierMasterCustom10List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addItemMixin,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.deleteItemMixin
                    }
                ],

                supplierInfoChangeAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'supplierMasterData', // 必传,
                            multiple: false,
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        callBack: this.uploadInfoChangCallBack
                    },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch},
                    {...new BatchDownloadBtn({pageCode: 'supplierInfoChangeAttachmentList'}).btnConfig}
                ]
            },
            requestData: {
                detail: {
                    url: this.currentEditRow.changeNumber && this.currentEditRow.initiatorElsAccount?
                        '/supplier/supplierInfoChangeHead/queryAfterInfoById':'/supplier/supplierMaster/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            flowView: false,
            flowId: 0,
            url: {
                edit: '/supplier/supplierInfoChangeHead/edit',
                detail: '/supplier/supplierMaster/queryById',
                submit: '/supplier/supplierInfoChangeHead/submit',
                release: '/supplier/supplierInfoChangeHead/releaseByIds',
                submitAudit: '/a1bpmn/audit/api/submit',
                upload: '/attachment/purchaseAttachment/upload',
                download: '/attachment/purchaseAttachment/download'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_supplierMasterData_${templateNumber}_${templateVersion}`
        },
        detailUrl () {
            if (this.currentEditRow.changeNumber && this.currentEditRow.initiatorElsAccount) {
                return '/supplier/supplierInfoChangeHead/queryAfterInfoById'
            }
            return '/supplier/supplierMaster/queryById'
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            let baseList = pageConfig.groups.filter(item => item.groupCode == 'enterpriseForm')
            baseList[0].formFields.map(v=>{
                if(v.fieldName!='toElsAccount'&&v.fieldName!='updateBy'){
                    v.disabled = false
                }
            })

            this.externalToolBar['supplierBankInfoList'][2].args.headId = resultData.id || ''
            this.externalToolBar['supplierInfoChangeAttachmentList'][0].args.headId = resultData.id || ''

            // 银行页签 file 下载
            let index0 = pageConfig.groups.findIndex(item => item.groupCode == 'supplierBankInfoList')
            if (pageConfig.groups[index0].extend && pageConfig.groups[index0].extend.editConfig) {
                pageConfig.groups[index0].extend.optColumnList = [{key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.bankFileDownloadEvent}]
            }
        },
        handleBeforeRemoteConfigData (data) {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEVHAHBI_2f93ce75`, '企业信息变更附件'),
                        groupCode: 'supplierInfoChangeAttachmentList',
                        groupType: 'item',
                        sortOrder: data.groups.length+'',
                        extend: {
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFilesEvent }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'supplierBankInfoList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '100',
                        slots: {default: 'grid_opration'}
                    },

                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                    { groupCode: 'supplierInfoChangeAttachmentList', field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                ]
            }
        },
        // 点击弹出自定义弹窗
        customSelect () {
            const {enterpriseForm = {}} =  this.getAllData()
            console.log(enterpriseForm, 'enterpriseForm===')
            const {supplierMasterFrozenHistoryList = [], frozenFunction = ''} = enterpriseForm
            let params = {
                supplierMasterFrozenHistoryList: supplierMasterFrozenHistoryList,
                frozenFunction: frozenFunction ? JSON.parse(frozenFunction) : []
            }
            this.$refs.customSelectModal.open(params)
        },
        selectDataOk (data) {
            console.log(this.$refs[this.businessRefName].pageConfig)
            this.$refs[this.businessRefName].pageConfig.groups.forEach(v=>{
                if(v.groupCode === 'expandForm'){
                    v.formModel.frozenFunction = JSON.stringify(data)
                }
            })
            this.$forceUpdate()
        },
        bankFileDownloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            let param = {
                id: row.fileName.split('-')[0],
                fileName: row.fileName.split('-')[1],
                filePath: row.filePath
            }
            this.attachmentDownloadEvent(param)
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            this.attachmentDownloadEvent(row)
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        deleteFilesEvent (Vue, row) {
            const fileGrid = this.getItemGridRef('supplierInfoChangeAttachmentList')
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        bankFileUploadCallBack (data){
            return new Promise((resolve)=> {
                // 验证的规则
                if (data && data.length) {
                    if (data.length===1) {
                        if(data[0].elsAccount && !data[0].toElsAccount){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_rWFLRdXLDjRRVHWxOHccr_cae59b`, '改数据为供应商维护的公共信息，不能进行修改'))
                            resolve(false)
                        }
                        resolve(true)
                    } else{
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_onlySelectOne`, '只能选择一条'))
                        resolve(false)
                    }
                }else{
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDataoPerated`, '请选择需要操作的数据'))
                    resolve(false)
                }

            })
        },
        uploadCallBack (result, refName) {
            console.log(result)
            const { id = '', fileName = '', filePath = '' } = result[0] || {}
            const fileGrid = this.getItemGridRef(refName).getCheckboxRecords()
            fileGrid[0].filePath = `${filePath}`
            fileGrid[0].fileName = `${id}-${fileName}`
        },
        uploadInfoChangCallBack (result, refName) {
            console.log(result)
            let fileGrid = this.getItemGridRef('supplierInfoChangeAttachmentList')
            fileGrid.insertAt(result, -1)
        },
        addContactsItem ({pageConfig, groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let itemData = {}
            pageConfig.groups.forEach(item => {
                if(item.groupCode === groupCode){
                    item.columns.forEach(col=>{
                        if(col.defaultValue) {
                            itemData[item.field] = col.defaultValue
                        }
                    })
                }

            })
            const {enterpriseForm} =  this.getAllData()
            itemData['elsAccount'] = enterpriseForm.toElsAccount
            itemData['toElsAccount'] = enterpriseForm.elsAccount
            itemGrid.insert([itemData])
        },
        deleteContactsEvent ({groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }

            let orderText=''
            for(let item of checkboxRecords){
                if(!item.toElsAccount){
                    let order = itemGrid.getRowSeq(item)
                    orderText = orderText + '[' + order + ']'
                }
            }
            if (orderText!=='') {
                const {enterpriseForm} =  this.getAllData()
                // let msg = 序号[3][4]的数据是供应商维护的公共数据，不是企业[********]私有的，您不能在此删除。如需删除，请到企业基本信息菜单中做删除，删除后将影响所有采购商。
                let msg = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号') +
                        orderText +
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_jWFKRdXLDjRRWFWxKAE_85c4c950`, '的数据是供应商维护的公共数据，不是企业') +
                        '[' + enterpriseForm.elsAccount +']' +
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CjjWLxOKmQGWNTQGWVuAEtvVHntskQGWQGSPdddjnRX_9394ca1f`, '私有的，您不能在此删除。如需删除，请到企业基本信息菜单中做删除，删除后将影响所有采购商')

                this.$message.warning(msg)
                return
            }

            itemGrid.removeCheckboxRow()
        },
        // 默认表行增加
        addItemMixin ({pageConfig, groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let itemData = {}
            const {enterpriseForm} =  this.getAllData()
            itemData['elsAccount'] = enterpriseForm.toElsAccount
            itemData['toElsAccount'] = enterpriseForm.elsAccount
            itemGrid.insert([itemData])
        },
        // 默认删除行数据
        deleteItemMixin ({groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }

            let orderText=''
            for(let item of checkboxRecords){
                if(!item.toElsAccount){
                    let order = itemGrid.getRowSeq(item)
                    orderText = orderText + '[' + order + ']'
                }
            }
            if (orderText!=='') {
                const {enterpriseForm} =  this.getAllData()
                // let msg = 序号[3][4]的数据是供应商维护的公共数据，不是企业[********]私有的，您不能在此删除。如需删除，请到企业基本信息菜单中做删除，删除后将影响所有采购商。
                let msg = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号') +
                        orderText +
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_jWFKRdXLDjRRWFWxKAE_85c4c950`, '的数据是供应商维护的公共数据，不是企业') +
                        '[' + enterpriseForm.elsAccount +']' +
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CjjWLxOKmQGWNTQGWVuAEtvVHntskQGWQGSPdddjnRX_9394ca1f`, '私有的，您不能在此删除。如需删除，请到企业基本信息菜单中做删除，删除后将影响所有采购商')

                this.$message.warning(msg)
                return
            }

            itemGrid.removeCheckboxRow()
        },
        addAddressItem ({pageConfig, groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let itemData = {}
            pageConfig.groups.forEach(item => {
                if(item.groupCode === groupCode){
                    item.columns.forEach(col=>{
                        if(col.defaultValue) {
                            itemData[item.field] = col.defaultValue
                        }
                    })
                }

            })
            const {enterpriseForm} =  this.getAllData()
            itemData['elsAccount'] = enterpriseForm.toElsAccount
            itemData['toElsAccount'] = enterpriseForm.elsAccount
            itemGrid.insert([itemData])
        },
        deleteAddressEvent ({groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }

            let orderText=''
            for(let item of checkboxRecords){
                if(!item.toElsAccount){
                    let order = itemGrid.getRowSeq(item)
                    orderText = orderText + '[' + order + ']'
                }
            }
            if (orderText!=='') {
                const {enterpriseForm} =  this.getAllData()
                // let msg = 序号[3][4]的数据是供应商维护的公共数据，不是企业[********]私有的，您不能在此删除。如需删除，请到企业基本信息菜单中做删除，删除后将影响所有采购商。
                let msg = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号') +
                        orderText +
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_jWFKRdXLDjRRWFWxKAE_85c4c950`, '的数据是供应商维护的公共数据，不是企业') +
                        '[' + enterpriseForm.elsAccount +']' +
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CjjWLxOKmQGWNTQGWVuAEtvVHntskQGWQGSPdddjnRX_9394ca1f`, '私有的，您不能在此删除。如需删除，请到企业基本信息菜单中做删除，删除后将影响所有采购商')

                this.$message.warning(msg)
                return
            }

            itemGrid.removeCheckboxRow()
        },
        addBankItem ({pageConfig, groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let itemData = {}
            pageConfig.groups.forEach(item => {
                if(item.groupCode === groupCode){
                    item.columns.forEach(col=>{
                        if(col.defaultValue) {
                            itemData[item.field] = col.defaultValue
                        }
                    })
                }

            })
            const {enterpriseForm} =  this.getAllData()
            itemData['elsAccount'] = enterpriseForm.toElsAccount
            itemData['toElsAccount'] = enterpriseForm.elsAccount
            itemGrid.insert([itemData])
        },
        deleteBankEvent ({groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }

            let orderText=''
            for(let item of checkboxRecords){
                if(!item.toElsAccount){
                    let order = itemGrid.getRowSeq(item)
                    orderText = orderText + '[' + order + ']'
                }
            }
            if (orderText!=='') {
                const {enterpriseForm} =  this.getAllData()
                // let msg = 序号[3][4]的数据是供应商维护的公共数据，不是企业[********]私有的，您不能在此删除。如需删除，请到企业基本信息菜单中做删除，删除后将影响所有采购商。
                let msg = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号') +
                        orderText +
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_jWFKRdXLDjRRWFWxKAE_85c4c950`, '的数据是供应商维护的公共数据，不是企业') +
                        '[' + enterpriseForm.elsAccount +']' +
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CjjWLxOKmQGWNTQGWVuAEtvVHntskQGWQGSPdddjnRX_9394ca1f`, '私有的，您不能在此删除。如需删除，请到企业基本信息菜单中做删除，删除后将影响所有采购商')

                this.$message.warning(msg)
                return
            }

            itemGrid.removeCheckboxRow()
        },
        addOrgItem ({pageConfig, groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let itemData = {}
            pageConfig.groups.forEach(item => {
                if(item.groupCode === groupCode){
                    item.columns.forEach(col=>{
                        if(col.defaultValue) {
                            itemData[item.field] = col.defaultValue
                        }
                    })
                }

            })
            const {enterpriseForm} =  this.getAllData()
            itemData['elsAccount'] = enterpriseForm.toElsAccount
            itemData['toElsAccount'] = enterpriseForm.elsAccount
            itemData['orgCategoryId'] = 'purchaseOrganization'
            itemData['orgCategoryDesc'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeadc98_purchaseOrgCode`, '采购组织')
            itemData['regulationType'] = '0'
            itemData['frozenFlag'] = '0'
            itemData['frozenFlag_dictText'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_normal`, '正常')
            itemData['regulationType_dictText'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lRrN_2e1926d9`, '手工准入')
            itemGrid.insert([itemData])
        },
        deleteOrgEvent ({groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }else{
                let i = 1
                for(let item of checkboxRecords){
                    if(item.accessed && item.accessed=='1'){
                        this.$message.warning('第'+i+'行是通过准入流程添加的组织信息,不能删除')
                        return
                    }
                    i++
                }
            }
            itemGrid.removeCheckboxRow()
        },
        // 发布提交
        submitEvent (args) {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {},
                supplierAddressInfoList= [],
                supplierBankInfoList = [],
                supplierContactsInfoList = [],
                supplierOrgInfoList = []
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm,
                ...allData,
                id: enterpriseForm.id
            }

            if (formatData.enterpriseForm && formatData.enterpriseForm != null) {
                Object.keys(formatData.enterpriseForm).forEach(key => {
                    formatData[key] = formatData.enterpriseForm[key]
                })
            }

            //delete formatData.enterpriseForm
            delete formatData.expandForm
            this.stepValidate(args).then(()=>{

                let url = this.url.edit
                const that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                    onOk: function () {
                        that.$refs[that.businessRefName].confirmLoading = true

                        postAction(url, formatData).then(res => {
                            if(res.success) {
                                if (!that.currentEditRow.id) {
                                    that.currentEditRow.id = res.result.id
                                }
                                that.$refs[that.businessRefName].pageConfig.groups.forEach(v=>{
                                    if(v.groupCode === 'enterpriseForm'){
                                        v.formModel = res.result
                                    }
                                })
                                const result = res.result
                                const supplierInfoChangeId = result.supplierInfoChangeId

                                // 发布
                                getAction(that.url.release, {id: supplierInfoChangeId}).then(res => {
                                    that.$refs[that.businessRefName].confirmLoading = false
                                    if(res.success) {
                                        if (that.currentEditRow.changeNumber && that.currentEditRow.initiatorElsAccount) {
                                            that.$message.success(that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_tkLRAEVHAHtIHVGhx_e7a85afe`, '操作成功，企业信息变更单已更新并发布'))
                                        } else {
                                            that.$message.success(that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_tkLRAEVHAHtIcIGhx_bed3651b`, '操作成功，企业信息变更单已创建并发布'))
                                        }
                                        that.businessHide()
                                        that.$parent.showEditPage = false
                                    }else {
                                        that.$message.warning(res.message)
                                        that.$refs[that.businessRefName].confirmLoading = false
                                    }
                                })

                            }else {
                                that.$message.warning(res.message)
                                that.$refs[that.businessRefName].confirmLoading = false
                            }
                        })
                    }
                })
            })

        },
        // 保存操作
        saveEvent (args) {
            const allData = this.getAllData()
            const {
                enterpriseForm= {},
                expandForm = {},
                supplierAddressInfoList= [],
                supplierBankInfoList = [],
                supplierContactsInfoList = [],
                supplierOrgInfoList = []
            } = allData
            let formatData = {
                ...enterpriseForm,
                ...expandForm,
                ...allData,
                id: enterpriseForm.id
            }

            if (formatData.enterpriseForm && formatData.enterpriseForm != null) {
                Object.keys(formatData.enterpriseForm).forEach(key => {
                    formatData[key] = formatData.enterpriseForm[key]
                })
            }

            // delete formatData.enterpriseForm
            delete formatData.expandForm
            this.$refs[this.businessRefName].confirmLoading = true
            const url = this.url.edit
            postAction(url, formatData).then(res => {
                if (res.success) {
                    if (this.currentEditRow.changeNumber && this.currentEditRow.initiatorElsAccount) {
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tkLRAEVHAHtIHV_4a968e0a`, '操作成功，企业信息变更单已更新'))
                    } else {
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_sMLRIcIAEVHAHtVImGhxvnRX_e7bbd71c`, '保存成功，已创建企业信息变更单，请检查并发布给采购商'))
                    }
                    this.$refs[this.businessRefName].confirmLoading = false
                    const id = res.result.supplierInfoChangeId
                    this.queryDetail(id)
                } else {
                    this.$message.warning(res.message)
                    this.$refs[this.businessRefName].confirmLoading = false
                }
            })
        },
        // 查详情
        queryDetail (id) {
            this.$refs[this.businessRefName].confirmLoading = true
            const url = '/supplier/supplierInfoChangeHead/queryAfterInfoById'
            getAction(url, {id: id}).then(res => {
                if (res.success) {
                    this.$refs[this.businessRefName].editFormData = res.result
                    this.$refs[this.businessRefName].dealSource(res.result)
                    this.$refs[this.businessRefName].confirmLoading = false
                } else {
                    this.$message.warning(res.message)
                    this.$refs[this.businessRefName].confirmLoading = false
                }
            })
        },
        // 批量删除
        deleteBatch ({ pageConfig, groupCode }) {
            const fileGrid = this.getItemGridRef(groupCode)
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        }
    }
}
</script>