<template>
  <div>
    <div
      class="tenderBidTetterVoList"
      v-if="ifShow"
    >
      <titleTrtl class="margin-b-10">
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBxAB_9dbb169c`, '投标函列表') }}</span>
      </titleTrtl>
      <vxe-grid
        height="200"
        v-bind="gridConfig"
        ref="table"
        :data="tableData"
        :columns="tableColumns"
        @radio-change="radioChange"
        show-overflow="title"
      >
        <template #default_formatType="{row, cloumn}">
          <span>{{ returnFormatText(row, cloumn) }}</span>
        </template>
      </vxe-grid>
      <div
        class="listInformation">
        <titleTrtl class="margin-b-10">
          <span>{{ rowTitel }}：{{ $srmI18n(`${$getLangAccount()}#i18n_field_umvBIBBVHWVWRiYBxW_4abc3a9a`, '价格开标一览表信息(请先勾选招标函)') }}</span>
        </titleTrtl>
        <listInformation
          ref="listInformation"
          :pageStatus="pageStatus"
          :currentRow="currentRow"></listInformation>
      </div>
    </div>
    <div
      v-else
      style="text-align:center">
      <h3>{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBtLLywWxiTmAeBxVH_e57927d8`, '投标单位未解密，不允许查看投标函信息') }}</h3>
    </div>
    
  </div>
</template>
<script>
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import listInformation from './components/listInformation'
import titleTrtl from '../../components/title-crtl'
export default {
    name: 'SaleTenderBidLetterList',
    mixins: [tableMixins],
    props: {
        saleTenderBidLetterList: {
            default: () => {
                return []
            },
            type: Array
        },
        pageStatus: {
            default: '',
            type: String
        }
    },
    components: {
        listInformation,
        titleTrtl
    },
    computed: {
        isEdit () {
            return false
        },
        rowTitel () {
            if(this.currentRow) return this.currentRow.name
            return '某投标函'
        } 
    },
    data () {
        return {
            ifShow: false,
            labelCol: {
                span: 9
            },
            wrapperCol: {
                span: 15
            },
            tableColumns: [
                {
                    type: 'radio',
                    width: 50
                },
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBxRL_9dbb44ee`, '投标函名称'),
                    field: 'name',
                    editRender: { enabled: false, name: '$input' }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBxAc_9dc007db`, '投标函类型'),
                    field: 'formatType',
                    slots: {default: 'default_formatType'}
                }
            ],
            optColumnList: [],
            currentRow: null,
            formData: {},
            tableData: [],
            preTenderFormatType: [],
            resultTenderFormatType: [],
            tenderFormatType: []
        }
    },
    methods: {
        returnFormatText (row) {
            let formatTypeText = ''
            if (row.checkType == '0') { // 预审
                this.preTenderFormatType.forEach(forma => {
                    if (forma.value == row.formatType) {
                        formatTypeText = forma.text
                    }
                })
            } else {
                if (row.processType == '1') { // 后审二步法
                    this.resultTenderFormatType.forEach(forma => {
                        if (forma.value == row.formatType) {
                            formatTypeText = forma.text
                        }
                    })
                } else { // 后审一步法
                    this.tenderFormatType.forEach(forma => {
                        if (forma.value == row.formatType) {
                            formatTypeText = forma.text
                        }
                    })
                }
            }
            return formatTypeText
        },
        // 操作列方法
        optColumnFuntion (opt, row, col) {
            opt.click && opt.click(this, row, col)
        },
        radioChange ({ row }) {
            this.currentRow = row
            this.currentRow.customizeFieldData = this.currentRow.customizeFieldData || []
        }
    },
    mounted () {
        this.formData = this.saleTenderBidLetterList || []
        console.log('this.formDatathis.formDatathis.formData', this.formData)
        this.tableData = this.formData && this.formData.map(item => {
            if(item.priceOpeningsList && item.priceOpeningsList.length != 0){
                item.customizeFieldData=JSON.parse(item.priceOpeningsList[0].customizeFieldData)
                item.customizeFieldModel=JSON.parse(item.priceOpeningsList[0].customizeFieldModel)
            }
            return item
        })
    },
    created (){
        // 获取表格下拉字典
        // 预审
        let postData = {
            busAccount: this.$ls.get(USER_ELS_ACCOUNT),
            dictCode: 'preTenderFormatType'
        }
        ajaxFindDictItems(postData).then(res => {
            if (res.success) {
                this.preTenderFormatType = res.result || []
            }
        })
        // 后审二步法
        let postData2 = {
            busAccount: this.$ls.get(USER_ELS_ACCOUNT),
            dictCode: 'resultTenderFormatType'
        }
        ajaxFindDictItems(postData2).then(res => {
            if (res.success) {
                this.resultTenderFormatType = res.result || []
            }
        })
        // 后审一步法
        let postData3 = {
            busAccount: this.$ls.get(USER_ELS_ACCOUNT),
            dictCode: 'tenderFormatType'
        }
        ajaxFindDictItems(postData3).then(res => {
            if (res.success) {
                this.tenderFormatType = res.result || []
            }
        })


        if(this.saleTenderBidLetterList.length > 0){
            this.ifShow = true
        }
    }
}
</script>
<style lang="less" scoped>
 h3{
     color: red;
 }
</style>
