<template>
  <a-form-item
    :label="index +'.' + field.label"
    validate-status="error"
    :help="field.validateOption.message"
    :required="!!field.rules && field.rules.length > 0"
  >
    <a-checkbox-group
      v-decorator="[field.id, {
        initialValue: field.initialValue
      }]">
      <template v-for="(el, idx) of options">
        <a-checkbox
          :style="radioStyle"
          :value="el.value"
          :key="idx">
          {{ String.fromCharCode((65 + idx)) }}.{{ el.label }}
        </a-checkbox>
      </template>
    </a-checkbox-group>
  </a-form-item>
</template>

<script>
export default {
    name: 'CheckboxCom',
    props: {
        field: {
            type: Object,
            require: true
        }
    },
    inject: ['index'],
    data () {
        return {
            options: [],
            radioStyle: {
                display: 'block',
                height: '30px',
                lineHeight: '30px',
                marginLeft: 0
            }
        }
    },
    created () {
        this.options = this.field.options
    }
}
</script>

<style scoped>

</style>
