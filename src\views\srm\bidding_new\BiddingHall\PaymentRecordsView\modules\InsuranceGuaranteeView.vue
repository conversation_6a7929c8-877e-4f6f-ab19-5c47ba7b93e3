<template>
  <!-- 保险保函缴纳 -->
  <div class="insurance-guarantee-view">
    <div v-if="!showEditPage">
      <a-row style="padding: 10px 15px;">
        <a-col :span="12">
          <label>{{ $srmI18n(`${$getLangAccount()}#i18n_field_RIJW_274265fe`, '关键字：') }}</label>
          <a-input
            style="width: 220px;"
            v-model="queryParams.supplierName"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNeBLRL_fb7f81a2`, '请输入投标人名称')"></a-input>
        </a-col>
        <a-col
          :span="12"
          style="text-align: right;">
          <a-button
            type="primary"
            icon="search"
            @click="searchFun"
            style="margin-right: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_search`, '搜索') }}</a-button>
          <a-button
            icon="reload"
            @click="reload">{{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}</a-button>
        </a-col>
      </a-row>
      <div class="add">
        <a-button
          v-if="tenderCurrentRow.applyRole == '1'"
          type="primary"
          @click="addRecords">{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}</a-button>
      </div>
      <div class="grid">
        <ListTable 
          ref="listTable"
          :url="url"
          :defaultParams="queryParams"
          :statictableColumns="tableColumns"
          :pageData="pageData"
          :showTablePage="false">
        </ListTable>
      </div>
    </div>
    <!-- <InsuranceGuaranteeEdit
      v-if="showEditPage" /> -->
  </div>
</template>

<script lang="jsx">
import ListTable from '../../components/listTable'
import {getAction, postAction} from '@/api/manage'

export default {
    components: {
        ListTable
    },
    inject: ['tenderCurrentRow', 'subpackageId'],
    data () {
        return {
            showEditPage: false,
            queryParams: {
                subpackageId: this.subpackageId(), 
                marginCollectionType: '2', 
                supplierName: ''
            },
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBLRL_9daf066b`, '投标人名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tsL_17cc002`, '担保人'),
                    'field': 'guarantor'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                    'field': 'updateTime'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'),
                    'field': 'updateBy'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sxHfWjW_ef47d502`, '保函金额（元）'),
                    'field': 'amount'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sxBI_25940ef2`, '保函附件'),
                    'field': '',
                    slots: {
                        default ({row, column}) {
                            const {attachmentDTOList} = row
                            const span = attachmentDTOList.map(item => {
                                return <span>{item.fileName}</span>
                            })
                            return span
                        }
                    }
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 120,
                    slots: { default: 'grid_opration' }
                }
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleDetail},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showEdit}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            url: {
                list: '/tender/supplier/purchaseTenderProjectMarginHead/queryItem'
            }
        }
    },
    computed: {
        // subId () {
        //     return this.subpackageId()
        //     // return '1532190061912612866'
        // }
    },
    methods: {
        showEdit ({status}) {
            return status == 0 ? false : true
        },
        handleEdit (row) {
            this.$emit('operationPage', '2', row, 'edit')
        },
        handleDetail (row) {
            this.$emit('operationPage', '2', row, 'detail')
        },
        addRecords () { // 新增记录
            this.$emit('changePageStatus', '2')
        },
        searchFun () {
            this.$refs.listTable.loadData()
        },
        reload () {
            this.$set(this.queryParams, 'supplierName', '')
            this.$refs.listTable.loadData()
        }
    }
}
</script>

<style lang="less" scoped>
.add {
  padding: 10px 15px;
}
</style>

