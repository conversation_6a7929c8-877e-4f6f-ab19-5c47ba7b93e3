import inviteTendersRouter from './purchase/inviteTenders'
import bidEvaluationRouter from './purchase/bidEvaluation'
import openTenderRouter from './purchase/openTender'
import calibrationRouter from './purchase/calibration'
import offlineRouter from './purchase/bidOffline'
import { ProjectHallLayout } from '@/components/layouts'

const HallRouter = {
    path: '/projectHall',
    redirect: () => {
        return { name: 'project_announcement' }
    },
    name: 'projectHall',
    meta: {
        title: '自主招标大厅',
        titleI18nKey: 'i18n_title_biddingHall',
        icon: 'sliders',
        keepAlive: false
    },
    component: ProjectHallLayout,
    children: [
        inviteTendersRouter,
        openTenderRouter,
        bidEvaluationRouter,
        offlineRouter,
        calibrationRouter
    ]
}

export default HallRouter