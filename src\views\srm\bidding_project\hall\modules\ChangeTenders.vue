<template>
  <div class="change-tenders">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      @content-header-new="handleNew"
      @content-header-showFlow="showFlow"
      @content-header-cancelAudit="cancelAudit"
    />
    <div
      class="container"
      :style="style">
      <a-spin :spinning="confirmLoading">
        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_title_changeList`, '变更列表') }}</div>
          <div class="table">
            <vxe-grid
              ref="changeTendersList"
              v-bind="defaultGridOption"
              :columns="changeTendersColumns"
              :data="changeTendersList"
              @cell-click="handleCellClick">
              <template slot="empty">
                <a-empty />
              </template>
              <template #grid_opration="{ row, column }">
                <span v-if="row.auditStatus === '0' || row.auditStatus === '3'">
                  <a
                    v-for="(item, i) in optColumnList"
                    :key="i"
                    :title="item.title"
                    style="margin:0 4px"
                    @click="item.clickFn(row, column)">{{ item.title }}</a></span>
              </template>
            </vxe-grid>
          </div>
        </div>
        <div class="itemBox">
          <div class="title dark">{{ $srmI18n(`${$getLangAccount()}#i18n_title_changeDetail`, '变更明细') }}</div>
          <div class="table">
            <vxe-grid
              ref="subTender"
              v-bind="defaultGridOption"
              :columns="subTenderColumns"
              :data="subTenderList">
              <template slot="empty">
                <a-empty />
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="vuex_currentEditRow"/>
  </div>
</template>
<script>
import ContentHeader from '@/views/srm/bidding_project/hall/components/content-header'
import { getAction,  httpAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        flowViewModal,
        'content-header': ContentHeader
    },
    data () {
        return {
            flowView: false,
            flowId: '',
            currentBasePath: this.$variateConfig['domainURL'],
            confirmLoading: false,
            form: {},
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_newBuild`, '新建'), type: 'primary', event: 'new' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'), type: '', event: 'cancelAudit', showCondition: this.showRevokeApprovalBtn },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', event: 'showFlow', showCondition: this.showViewProcessBtn}
            ],
            showHeader: true,
            height: 0,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                columns: [],
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'dblclick', mode: 'cell' }
            },
            changeTendersColumns: [
                { type: 'radio', width: 40 },
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeOrderNo`, '变更单号'), field: 'changeNumber', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm65d_auditStatus`, '审批状态'), field: 'auditStatus_dictText', width: 120 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_effectiveTime`, '生效时间'), field: 'effectivetime', width: 140 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeReason`, '变更原因'), field: 'changeReason', width: 300 },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), field: 'remark'},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), field: 'grid_opration', width: 120, fixed: 'right', slots: { default: 'grid_opration' }}
            ],
            optColumnList: [
                { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.editTender },
                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteTender }
            ],
            changeTendersList: [],
            curRow: {},
            subTenderColumns: [
                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                { field: 'fieldCode_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fieldName`, '字段名') },
                { field: 'fieldOriginalValue', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_originalValue`, '原始值') },
                { field: 'fieldValue', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeValue`, '变更值')}
            ],
            subTenderList: []
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        showFlow (){
            let row = this.$refs.changeTendersList.getRadioRecord()
            if(!row){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFPmAQLjtyW_bad4de55`, '请选择要查看流程的单号！'))
                return 
            }
            this.flowId = row.flowId
            const { auditStatus } = row
            if(auditStatus === '0' || auditStatus === ''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        showRevokeApprovalBtn (){
            let rows = this.changeTendersList
            if (rows && rows.length) {
                return true
            }
            return false
        },
        showViewProcessBtn () {
            let rows = this.changeTendersList
            if (rows && rows.length) {
                return true
            }
            return false
        },
        closeFlowView (){
            this.flowView = false
        },
        cancelAudit (){
            let form = this.$refs.changeTendersList.getRadioRecord()
            if(!form){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFPqXUzjtyW_c9efdf9`, '请选择要撤销审批的单号！'))
                return 
            }
            const { auditStatus } = form
            if(auditStatus === '0' || auditStatus === ''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LUzzExOqXW_fcbc2808`, '未审批状态不能撤销！'))
                return
            }
            let param = {}
            param['businessType'] = 'changeBidding'
            param['businessId'] = form.id
            param['rootProcessInstanceId'] = form.flowId
            this.confirmLoading = true
            httpAction('/a1bpmn/audit/api/cancel', param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.getTenderData()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleCellClick ({ row, column }) {
            const unSelected = [ 'bidCheck', 'bidQuote' ]
            if (unSelected.includes(column.property)) {
                return
            }
            this.supSetRadioRow(row)
        },
        getTenderData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingChange/list'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { relationId: id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { records = [] } = res.result || {}
                    this.changeTendersList = records
                    this.$nextTick(() => {
                        if (!records || !records.length) {
                            return
                        }
                        this.supSetRadioRow(this.changeTendersList[0])
                    })
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleNew () {
            this.$emit('create-tender')
        },
        supSetRadioRow (row) {
            if (row.id === this.curRow.id) {
                return
            }
            this.curRow = row
            const changeTendersListRef = this.$refs.changeTendersList
            if (!changeTendersListRef) {
                return
            }
            changeTendersListRef.setRadioRow(row).then(() => {
                this.getTenderDetailData(row)
            })
        },
        // 查询明细
        getTenderDetailData (row) {
            const { id = '' } = row || {}
            const params = {
                id: id
            }
            const url = '/bidding/purchaseBiddingChange/queryPurchaseBiddingChangeItemByMainId'
            this.confirmLoading = true
            this.subTenderList = []
            getAction(url, params).then(res => {
                if (!res.success) {
                    this.$message.error(res.message)
                    return
                }
                if (res.result && res.result.length) {
                    res.result.forEach((data)=> {
                        let item = {
                            fieldCode_dictText: data.fieldCode_dictText,
                            fieldCode: data.fieldCode,
                            fieldType: data.fieldType,
                            fieldOriginalValue: data.fieldOriginalValue,
                            fieldValue: data.fieldValue
                        }
                        this.subTenderList.push(item)
                    })
                } 
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 删除
        deleteTender (row) {
            const { id = '' } = row || {}
            const params = {
                id: id
            }
            const url = '/bidding/purchaseBiddingChange/delete'
            this.confirmLoading = true
            this.subTenderList = []
            getAction(url, params).then(res => {
                if (!res.success) {
                    this.$message.error(res.message)
                    return
                }
                this.$message.success(res.message)
                this.getTenderData()
            })
        },
        // 编辑
        editTender (row) {
            this.$emit('edit-tender', {row: row})
        }
    },
    created () {
        this.height = document.documentElement.clientHeight
        this.getTenderData()
    }
}
</script>

<style lang="less" scoped>
.change-tenders {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
}
</style>
