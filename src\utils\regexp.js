/**
 * 验证电子邮箱格式
 */
const email = /[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+/

/**
 * 验证手机格式
 */
const mobile = /^1[3|4|5|6|7|8|9]\d{9}$/

/**
 * 验证URL格式
 */
const url = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-.\/?%&=]*)?/

/**
 * 验证ISO类型的日期格式
 */
const dateISO = /^\d{4}[/-](0?[1-9]|1[012])[/-](0?[1-9]|[12][0-9]|3[01])$/

// 验证时间 HH:mm
const timeHourAndMinute = /^(2[0-3]|0[0-9]|1[0-9]):[0-5][0-9]$/

/**
 * 验证正整数 + 正小数
 */
const interger = /^[1-9]+\d*(\.\d*)?$|^0?\.\d*[1-9]\d*|^0+[1-9]\d*(\.\d*)?$/
// /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/


/**
 * 验证正整数(包括0)
 */
const positiveInterger = /^[0]{0,1}(\d+)$/


/**
 * 验证十进制数字
 */
const number = /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/

/**
 * 验证整数
 */
const digits = /^\d+$/

/**
 * 验证身份证号码
 */
const idCard = /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/

/**
 * 是否车牌号
 */
// 新能源车牌
const carNo = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/
// 旧车牌
const carNo2 = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/

/**
 * 金额,只允许4位小数
 */
const amount = /^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,4}$/

const amount_2 = /^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/

/**
 * 金额,限制位数为10位数
 */
const money = /^[1-9]+?\d{0,9}/

/**
 * 中文
 */
const chinese = /^[\u4e00-\u9fa5]+$/gi

/**
 * 只能输入字母
 */
const letter = /^[a-zA-Z]*$/

/**
 * 只能是字母或者数字
 */
const enOrNum = /^[0-9a-zA-Z]*$/g

/**
 * 是否固定电话
 */
const landline = /^\d{3,4}-\d{7,8}(-\d{3,4})?$/

/**
 * 是否手机号码或者固定电话
 */
const mobileOrLandline = /^1\d{10}$|^(0\d{2,3}-?|)?[1-9]\d{4,7}(-\d{1,8})?$/

/**
 * 银联卡
 */
const unionPayCard = /^62\d{11,17}$/

/**
  * ip地址
  */
const ip = /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/

export default {
  email,
  mobile,
  url,
  dateISO,
  interger,
  positiveInterger,
  number,
  digits,
  idCard,
  carNo,
  carNo2,
  amount,
  money,
  chinese,
  letter,
  enOrNum,
  landline,
  unionPayCard,
  ip
}

export const regExpOptions = [
  { label: '电子邮箱', value: email.toString(), msg: '请输入电子邮箱' },
  { label: '手机号码', value: mobile.toString(), msg: '请输入手机号码' },
  { label: 'URL', value: url.toString(), msg: '请输入URL' },
  { label: '日期格式(ISO类型)', value: dateISO.toString(), msg: '请输入日期' },
  { label: '时间格式(HH:mm)', value: timeHourAndMinute.toString(), msg: '请输入时间(HH:mm)' },
  { label: '正整数+正小数', value: interger.toString(), msg: '只允许输入正整数或正小数' },
  { label: '正整数', value: positiveInterger.toString(), msg: '只允许输入正整数' },
  { label: '数字(十进制)', value: number.toString(), msg: '只允许输入数字' },
  { label: '整数(十进制)', value: digits.toString(), msg: '只允许输入整数' },
  { label: '身份证', value: idCard.toString(), msg: '请输入身份证' },
  { label: '车牌(新能源)', value: carNo.toString(), msg: '请输入新能源车牌号码' },
  { label: '车牌', value: carNo2.toString(), msg: '请输入车牌号码' },
  { label: '金额(4位小数)', value: amount.toString(), msg: '请输入4位小数金额' },
  { label: '金额(2位小数)', value: amount_2.toString(), msg: '请输入2位小数金额' },
  { label: '金额(10位数)', value: money.toString(), msg: '请输入小于10位数的金额' },
  { label: '汉字', value: chinese.toString(), msg: '请输入汉字' },
  { label: '字母', value: letter.toString(), msg: '请输入字母' },
  { label: '字母或数字', value: enOrNum.toString(), msg: '请输入字母或数字' },
  { label: '固定电话', value: landline.toString(), msg: '请输入固定电话' },
  { label: '手机号码或者固定电话', value: mobileOrLandline.toString(), msg: '请输入手机号码或者固定电话' },
  { label: '银联卡', value: unionPayCard.toString(), msg: '请输入银联卡' },
  { label: 'ip地址', value: ip.toString(), msg: '请输入ip地址' }
]
