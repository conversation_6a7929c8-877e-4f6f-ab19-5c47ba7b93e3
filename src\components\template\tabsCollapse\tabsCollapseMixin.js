/*
 * @Author: your name
 * @Date: 2021-03-29 14:02:20
 * @LastEditTime: 2021-05-31 15:14:32
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\components\template\tabsCollapse\tabsCollapseMixin.js
 */
import tabsEditPage from '@comp/template/tabsCollapse/tabsCollapsePage'
export const editPageMixin = {
    components: {
        tabsEditPage
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            pageData: {
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', clickFn: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack }
                ]
            }
        }
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.setParamsData(this.currentEditRow.id)
            }
        },
        goBack () {
            this.$emit('hide')
        },
        addRow () {
            this.$refs.editPage.addRow()
        },
        //删除复选框选定行
        deleteRow () {
            this.$refs.editPage.deleteRow()
        },
        refreshList () {
            this.$emit('ok')
        },
        saveEvent () {
            this.$refs.editPage.saveEvent()
        },
        requestAfter (data) {
            console.log(data)
        }
    }
}