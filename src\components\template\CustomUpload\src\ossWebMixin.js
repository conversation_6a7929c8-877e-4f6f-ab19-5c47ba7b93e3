/**
 * OSS web直传
 * public/aliyun-oss-appserver-js-master/upload.js
 */
export const ossWebMixin = {
    data () {
        return {
            uploader: {}, // 上传对象
            uploadData: [], // 已上传数据缓存
            accessid: '',
            accesskey: '',
            host: '',
            policyBase64: '',
            signature: '',
            callbackbody: '',
            filename: '',
            key: '',
            expire: 0,
            g_object_name: '',
            suffix: '',
            // local_name 上传文件名字保持本地文件名字, random_name 上传文件名字是随机文件名, 后缀保留;
            g_object_name_type: 'local_name',
            now: Date.parse(new Date()) / 1000,
            timestamp: Date.parse(new Date()) / 1000,
            serverUrl: '/attachment/store/policySign',
            ossUrl: 'http://oss.aliyuncs.com'
        }
    },
    methods: {
        initUploader () {
            this.uploader = new window.plupload.Uploader({
                runtimes: 'html5,flash,silverlight,html4',
                browse_button: 'selectfiles',
                //multi_selection: false,
                // container: document.getElementById('container'),
                flash_swf_url: 'lib/plupload-2.1.2/js/Moxie.swf',
                silverlight_xap_url: 'lib/plupload-2.1.2/js/Moxie.xap',
                url: this.ossUrl,
                filters: {
                    mime_types: [
                        //只允许上传图片和zip文件
                        { title: 'Image files', extensions: 'jpg,jpeg,gif,png,bmp' },
                        { title: 'Zip files', extensions: 'zip,rar' }
                    ],
                    max_file_size: '20mb', //最大只能上传10mb的文件
                    prevent_duplicates: true //不允许选取重复文件
                },
                init: {
                    PostInit: function () {
                        // document.getElementById('ossfile').innerHTML = ''
                        // document.getElementById('postfiles').onclick = function () {
                        //     this.set_upload_param(this.uploader, '', false)
                        //     return false
                        // }
                    },

                    FilesAdded: function (up, files) {
                        window.plupload.each(files, function (file) {
                            document.getElementById('ossfile').innerHTML +=
                                '<div id="' +
                                file.id +
                                '">' +
                                file.name +
                                ' (' +
                                window.plupload.formatSize(file.size) +
                                ')<b></b>' +
                                '<div class="progress"><div class="progress-bar" style="width: 0%"></div></div>' +
                                '</div>'
                        })
                    },

                    BeforeUpload: function (up, file) {
                        // this.check_object_radio()
                        this.set_upload_param(up, file.name, true)
                    },

                    UploadProgress: function (up, file) {
                        var d = document.getElementById(file.id)
                        d.getElementsByTagName('b')[0].innerHTML = '<span>' + file.percent + '%</span>'
                        var prog = d.getElementsByTagName('div')[0]
                        var progBar = prog.getElementsByTagName('div')[0]
                        progBar.style.width = 2 * file.percent + 'px'
                        progBar.setAttribute('aria-valuenow', file.percent)
                    },

                    FileUploaded: function (up, file, info) {
                        if (info.status == 200) {
                            document.getElementById(file.id).getElementsByTagName('b')[0].innerHTML =
                                'upload to oss success, object name:' +
                                this.get_uploaded_object_name(file.name) +
                                ' 回调服务器返回的内容是:' +
                                info.response
                        } else if (info.status == 203) {
                            document.getElementById(file.id).getElementsByTagName('b')[0].innerHTML =
                                '上传到OSS成功，但是oss访问用户设置的上传回调服务器失败，失败原因是:' + info.response
                        } else {
                            document.getElementById(file.id).getElementsByTagName('b')[0].innerHTML = info.response
                        }
                    },

                    Error: function (up, err) {
                        if (err.code == -600) {
                            document
                                .getElementById('console')
                                .appendChild(
                                    document.createTextNode(
                                        '\n选择的文件太大了,可以根据应用情况，在upload.js 设置一下上传的最大大小'
                                    )
                                )
                        } else if (err.code == -601) {
                            document
                                .getElementById('console')
                                .appendChild(
                                    document.createTextNode(
                                        '\n选择的文件后缀不对,可以根据应用情况，在upload.js进行设置可允许的上传文件类型'
                                    )
                                )
                        } else if (err.code == -602) {
                            document
                                .getElementById('console')
                                .appendChild(document.createTextNode('\n这个文件已经上传过一遍了'))
                        } else {
                            document
                                .getElementById('console')
                                .appendChild(document.createTextNode('\nError xml:' + err.response))
                        }
                    }
                }
            })
        },
        send_request () {
            var xmlhttp = null
            if (window.XMLHttpRequest) {
                xmlhttp = new XMLHttpRequest()
            } else if (window.ActiveXObject) {
                xmlhttp = new window.ActiveXObject('Microsoft.XMLHTTP')
            }

            if (xmlhttp != null) {
                // serverUrl是 用户获取 '签名和Policy' 等信息的应用服务器的URL，请将下面的IP和Port配置为您自己的真实信息。
                // serverUrl = 'http://127.0.0.1:8080/els/attachment/store/noToken/policySign'

                let token = this.$store.getters.token

                xmlhttp.open('GET', this.serverUrl, false)
                xmlhttp.setRequestHeader('x-access-token', token)
                xmlhttp.send(null)
                return xmlhttp.responseText
            } else {
                alert('Your browser does not support XMLHTTP.')
            }
        },

        check_object_radio () {
            var tt = document.getElementsByName('myradio')
            for (var i = 0; i < tt.length; i++) {
                if (tt[i].checked) {
                    this.g_object_name_type = tt[i].value
                    break
                }
            }
        },
        get_signature () {
            // 可以判断当前 expire 是否超过了当前时间， 如果超过了当前时间， 就重新取一下，3s 作为缓冲。
            this.now = this.timestamp = Date.parse(new Date()) / 1000
            console.log('this.expire', this.expire)
            console.log('this.now', this.now)
            if (this.expire < this.now + 3) {
                let body = this.send_request()
                var obj = eval('(' + body + ')')
                this.host = obj['host']
                this.policyBase64 = obj['policy']
                this.accessid = obj['accessid']
                this.signature = obj['signature']
                this.expire = parseInt(obj['expire'])
                this.callbackbody = obj['callback']
                this.key = obj['dir']
                return true
            }
            return false
        },
        getOssPolicySign () {
            this.host = this.policySignData['host']
            this.policyBase64 = this.policySignData['policy']
            this.accessid = this.policySignData['accessid']
            this.signature = this.policySignData['signature']
            this.expire = parseInt(this.policySignData['expire'])
            this.callbackbody = this.policySignData['callback']
            this.key = this.policySignData['dir']
        },
        random_string (len) {
            len = len || 32
            var chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
            var maxPos = chars.length
            var pwd = ''
            for (let i = 0; i < len; i++) {
                pwd += chars.charAt(Math.floor(Math.random() * maxPos))
            }
            return pwd
        },
        get_suffix (filename) {
            let pos = filename.lastIndexOf('.')
            let suffix = ''
            if (pos != -1) {
                suffix = filename.substring(pos)
            }
            return suffix
        },
        calculate_object_name (filename) {
            this.suffix = this.get_suffix(filename)
            this.g_object_name = ''

            if (this.g_object_name_type == 'local_name') {
                let _t = +new Date()
                let name = filename.replace(this.suffix, '')
                this.g_object_name += `${this.key}${name}_${_t}${this.suffix}`
            }
            else if (this.g_object_name_type == 'random_name') {
                this.suffix = this.get_suffix(filename)
                this.g_object_name = this.key + this.random_string(10) + this.suffix
            }
            return ''
        },

        get_uploaded_object_name (filename) {
            if (this.g_object_name_type == 'local_name') {
                let tmp_name = this.g_object_name
                tmp_name = tmp_name.replace('${filename}', filename)
                return tmp_name
            } else if (this.g_object_name_type == 'random_name') {
                return this.g_object_name
            }
        },
        set_upload_param (up, filename, ret) {
            if (ret == false) {
                ret = this.get_signature()
            }
            this.g_object_name = this.key
            if (filename != '') {
                this.suffix = this.get_suffix(filename)
                this.calculate_object_name(filename)
            }
            let new_multipart_params = {
                'key': this.g_object_name,
                'policy': this.policyBase64,
                'OSSAccessKeyId': this.accessid,
                'success_action_status': '200', //让服务端返回200,不然，默认会返回204
                'callback': this.callbackbody,
                'signature': this.signature
            }
            up.setOption({
                'url': this.host,
                'multipart_params': new_multipart_params
            })

            up.start()
        }
    },
    created () {
        // this.initUploader()
    }
}







