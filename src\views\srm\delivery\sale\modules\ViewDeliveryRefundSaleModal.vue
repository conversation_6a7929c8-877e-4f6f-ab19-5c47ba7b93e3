<template>
  <detail-page
    ref="detailPage"
    :current-edit-row="currentEditRow"
    :pageData="pageData"></detail-page>
</template>

<script>
import detailPage from '@comp/template/detailPage'
import { httpAction } from '@/api/manage'
export default {
    name: 'ViewDeliveryBuyModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        detailPage
    },
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnDoc`, '退货单'),
            confirmLoading: false,
            pageData: {
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            form: {
                                deliveryNumber: '',
                                supplierId: '',
                                supplierCode: '',
                                supplierName: '',
                                companyCode: '',
                                companyName: '',
                                factoryCode: '',
                                factoryName: '',
                                deliveryType: '0',
                                deliveryStatus: '0',
                                auditStatus: '0',
                                deliveryDesc: '',
                                deliveryTime: '',
                                planArriveDate: '',
                                receiveTime: '',
                                storageLocationCode: '',
                                storageLocationName: '',
                                deliveryWay: '0',
                                logisticsCompany: '',
                                trackingNumber: '',
                                carNumber: '',
                                driverName: '',
                                driverIdNumber: '',
                                driverPhone: '',
                                deliveryAddress: '',
                                receiveContact: '',
                                receivePhone: '',
                                purchasePrincipal: '',
                                supplierPrincipal: '',
                                purchaseRemark: '',
                                supplierRemark: ''
                            },
                            list: [
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_returnDocNo`, '退货单号'),
                                    fieldName: 'deliveryNumber', 
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_systemGeneration`, '系统生成'),
                                    disabled: true
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                                    fieldName: 'deliveryStatus_dictText'
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm65d_auditStatus`, '审批状态'),
                                    fieldName: 'auditStatus_dictText'
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryTime`, '退货日期'),
                                    fieldName: 'deliveryTime'
                                },
                                {
                                    type: 'selectModal',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                                    fieldName: 'supplierName'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_soaDesc`, '单据描述'),
                                    fieldName: 'deliveryDesc'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司'),
                                    fieldName: 'companyCode'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                                    fieldName: 'factoryCode'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_InventoryLocation`, '库存地点'),
                                    fieldName: 'storageLocationCode'
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_distributionMode`, '配送方式'),
                                    fieldName: 'deliveryWay_dictText',
                                    dictCode: 'isrmDeliveryRefundWay'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_logisticsCompany`, '物流公司'),
                                    fieldName: 'logisticsCompany'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trackingNumber`, '物流单号'),
                                    fieldName: 'trackingNumber'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_carNumber`, '车牌号'),
                                    fieldName: 'carNumber'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverName`, '司机姓名'),
                                    fieldName: 'driverName'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverIdNumber`, '司机身份证号'),
                                    fieldName: 'driverIdNumber'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_driverPhone`, '司机电话'),
                                    fieldName: 'driverPhone'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'purchaseRemark'
                                }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                        content: {
                            type: 'table',
                            ref: 'deliveryRefundSaleItemList',
                            columns: [
                                { 
                                    type: 'seq', width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                                    field: 'orderNumber',
                                    width: 130
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                                    field: 'orderItemNumber',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialCode',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                    field: 'materialDesc',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
                                    field: 'materialSpec',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                                    field: 'materialGroupCode',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTaxCode`, '税码'),
                                    field: 'taxCode',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'),
                                    field: 'taxRate',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '净价'),
                                    field: 'netPrice',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'),
                                    field: 'price',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityUnit`, '数量单位'),
                                    field: 'quantityUnit',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryQuantity`, '退货数量'),
                                    field: 'deliveryQuantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_batchNumber`, '批次号'),
                                    field: 'batchNumber',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRemark`, '退货原因'),
                                    field: 'purchaseRemark',
                                    width: 220
                                }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        content:
                        {
                            type: 'upload',
                            ref: 'fileList',
                            relatedIdMap: 'relationId',
                            relatedType: 'deliveryRefund',
                            roleName: 'SU'
                        }
                    }
                ],
                url: {
                    detail: '/delivery/deliveryRefundSaleHead/queryDetailById',
                    confirm: '/delivery/deliveryRefundSaleHead/confirm'
                },
                publicBtn: [
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'), clickFn: this.confirm},
                    {type: 'rollBack', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack}
                ]
            }
            
        }
    },
    mounted () {
       
    },
    created (){
      
    },
    methods: {
        init (row) {
            let params = {id: row.id}
            this.$refs.detailPage.getPageDetail(params)
        },
        goBack () {
            this.$emit('hide')
        },
        confirm (){
            let data = this.$refs.detailPage.pageDetail
            if(data.deliveryStatus != '1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderHasBeenConfirmedAndThereNeedconfirmAgain`, '该单已经确认，无需重复确认！'))
                return 
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmReturn`, '确认退货'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areYouSureHaveReceivedReturn`, '是否确认已经收到退货?'),
                onOk: function () {
                    that.postData()
                }
            })
        },
        postData (){
            let param = this.$refs.detailPage.pageDetail
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.pageData.url.confirm, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.goBack()
                    this.$parent.$refs.listPage.searchQuery()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        }
    }
}
</script>
