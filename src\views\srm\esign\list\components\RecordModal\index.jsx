import { getAction } from '@/api/manage'
import AuditTable from './AuditTable'
import OperaTable from './OperaTable'
import { srmI18n, getLangAccount } from '@/utils/util'
import {
    tabs,
    operaPagerConfig,
    auditPagerConfig,
    operaColumns,
    auditColumns,
    operaUrl,
    auditUrl
} from './mock.js'

export default {
    name: 'RecordModal',
    components: {
        'opera-table': OperaTable,
        'audit-table': AuditTable
    },
    model: {
        prop: 'visible',
        event: 'change'
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: srmI18n(`${getLangAccount()}#i18n_title_numberOfHistory`, '历史记录') 
        },
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            tabs,
            operaPagerConfig,
            auditPagerConfig,
            operaColumns,
            auditColumns,
            operaUrl,
            auditUrl,
            spinning: false,
            activeKey: '0',
            operaTableData: [],
            auditTableData: []
        }
    },
    watch: {
        visible: {
            immediate: true,
            handler (val) {
                if (!val) return
                this.getOperaTableData()
                this.getAuditTableData()
            }
        }
    },
    methods: {
        getOperaTableData () {
            const { pageSize = 20, currentPage = 1 } = this.operaPagerConfig || {}
            const params = {
                pageSize,
                pageNo: currentPage,
                businessId: this.currentEditRow.id || ''
            }
            this.spinning = true
            getAction(this.operaUrl, params)
                .then((res) => {
                    this.operaTableData = res.result.records || []
                    this.operaPagerConfig.total = res.result.total
                })
                .finally(() => {
                    this.spinning = false
                })
        },
        getAuditTableData () {
            const { pageSize = 20, currentPage = 1 } = this.auditPagerConfig || {}
            const params = {
                pageSize,
                pageNo: currentPage,
                businessId: this.currentEditRow.id || ''
            }
            this.spinning = true
            getAction(this.auditUrl, params)
                .then((res) => {
                    this.auditTableData = res.result.records || []
                    this.auditPagerConfig.total = res.result.total
                })
                .finally(() => {
                    this.spinning = false
                })
        },
        handleOperaPageChange ({ currentPage, pageSize }) {
            this.operaPagerConfig.currentPage = currentPage
            this.operaPagerConfig.pageSize = pageSize
            this.getOperaTableData()
        },
        handleAuditPageChange ({ currentPage, pageSize }) {
            this.auditPagerConfig.currentPage = currentPage
            this.auditPagerConfig.pageSize = pageSize
            this.getAuditTableData()
        },
        handleCancel () {
            this.resetData()
            this.$emit('change', false)
        },
        resetData () {
            Object.assign(this.$data, this.$options.data.call(this))
        }
    },
    render (h) {

        const props = {
            visible: this.visible,
            title: this.title,
            width: 900,
            keyboard: false,
            maskClosable: false,
            destroyOnClose: true
        }

        const on = {
            cancel: this.handleCancel,
            ok: this.handleCancel
        }
        
        const spinProps = {
            spinning: this.spinning,
            delayTime: 300
        }

        return (
            <div class="RecordModal">
                <a-modal { ...{ props, on } }>
                    <div class="tabs">
                        <a-tabs vModel={ this.activeKey }>
                            { tabs.map((n, i) => (<a-tab-pane key={ String(i) } tab={ n } />)) }
                        </a-tabs>
                    </div>
                    <div class="content">
                        <a-spin { ...{ props: spinProps } } >
                            <div class="table">
                                {
                                    this.activeKey === '0'
                                        ? (
                                            <opera-table
                                                tableData={ this.operaTableData }
                                                columns={ this.operaColumns }
                                                pagerConfig={ operaPagerConfig }
                                                vOn:page-change={ this.handleOperaPageChange }
                                            />
                                        )
                                        : (
                                            <audit-table
                                                tableData={ this.auditTableData }
                                                columns={ this.auditColumns }
                                                pagerConfig={ auditPagerConfig }
                                                vOn:page-change={ this.handleAuditPageChange }
                                            />
                                        )
                                }
                            </div>
                        </a-spin>
                    </div>
                </a-modal>
            </div>
        )
    }
}
