<template>
  <a-modal
    centered
    v-drag
    :mask-closable="false"
    :title="title"
    :visible="visible"
    :width="580"
    @cancel="visible = false"
    :confirmLoading="loading"
    @ok="handleOk">
    <a-select
      style="width: 100%"
      v-model="form.regretFlag"
      :options="options"/>
  </a-modal>
</template>

<script>
import {postAction} from '@api/manage'

export default {
    data (){
        return{
            form: {},
            options: [
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noMoreFulfillment`, '悔标，不再寻源'), value: '0'},
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_replaceSourcing`, '悔标，重新寻源'), value: '1'},
                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparison`, '重新比价/定标'), value: '2'}
            ],
            title: this.$srmI18n(`${this.$getLangAccount()}#`, '悔标方式'),
            visible: false,
            loading: false
        }
    },
    methods: {
        handleOk (){
            this.visible = false
            this.loading = true
            this.$parent.spinning = true
            postAction('/enquiry/purchaseEnquiryHead/regret', this.form).then(res => {
                if(res.success){
                    this.$notification.success({description: res.message, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tkLRW_b20577c3`, '操作成功')})
                    this.$emit('success')
                }else{
                    this.$notification.warning({description: res.message, message: '警告'})
                    this.$parent.spinning = false
                }
            }).finally(() => {
                this.loading = false
            })
        },
        openMaterial ({headId, purchaseEnquiryItemList}){
            this.$set(this.form, 'id', headId)
            this.$set(this.form, 'regretFlag', '0')
            this.$set(this.form, 'regretWay', 'material')
            this.$set(this.form, 'purchaseEnquiryItemList', purchaseEnquiryItemList)
            this.visible = true
        },
        openWhole ({headId}){
            this.$set(this.form, 'id', headId)
            this.$set(this.form, 'regretFlag', '0')
            this.$set(this.form, 'regretWay', 'whole')
            this.$set(this.form, 'purchaseEnquiryItemList', null)
            this.visible = true
        }
    },
    name: 'EnquiryRegret'
}
</script>
