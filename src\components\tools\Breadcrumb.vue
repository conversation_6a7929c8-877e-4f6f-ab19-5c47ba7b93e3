<template>
  <a-breadcrumb class="breadcrumb">
    <a-breadcrumb-item
      v-for="(item, index) in breadList"
      :key="index"
    >
      <router-link
        v-if="item.name != name"
        :to="{ path: item.path }"
      >
        {{ handleTitle(item.meta) }}
      </router-link>
      <span v-else>{{ handleTitle(item.meta) }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script>
export default {
    data () {
        return {
            name: '',
            breadList: []
        }
    },
    created () {
        this.getBreadcrumb()
    },
    methods: {
        getBreadcrumb () {
            this.breadList = []

            this.name = this.$route.name
            this.$route.matched.forEach((item) => {
                this.breadList.push(item)
            })
        },
        handleTitle (meta) {
            let t = meta.title
            if (meta.titleI18nKey) {
                t = this.$srmI18n(`${this.$getLangAccount()}#${meta.titleI18nKey}`, meta.title)
            }
            return t
        }
    },
    watch: {
        $route () {
            this.getBreadcrumb()
        }
    }
}
</script>

<style scoped>

</style>