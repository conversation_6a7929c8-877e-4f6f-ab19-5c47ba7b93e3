import { RouteView } from '@/components/layouts'

const openTenderRouter = {
    path: '/projectHall/openTender',
    name: 'project_openTender',
    meta: {
        title: '开标',
        titleI18nKey: 'i18n_dict_vB_be907',
        icon: 'icon-111-05',
        keepAlive: false
    },
    component: RouteView,
    children: [
        {
            path: '/projectHall/openTender/open',
            name: 'project_open',
            meta: {
                title: '开启',
                titleI18nKey: 'i18n_menu_vA_bd52f',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'open' */ '@/views/srm/bidding_project/hall/purchase/openTender/Open.vue')
        }
    ]
}

export default openTenderRouter