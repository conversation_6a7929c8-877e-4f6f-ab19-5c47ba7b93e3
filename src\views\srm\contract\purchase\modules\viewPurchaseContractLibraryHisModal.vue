<template>
  <a-modal
    v-drag
    v-model="madalVisible"
    :title="modalTitle"
    :width="1100"
    @cancel="cancelEvent"
    @ok="editOk">
    <a-spin :spinning="confirmLoading">
      <div style="max-height: 420px; overflow-y: auto;">
        <a-form-model
          :model="form"
          layout="inline">
          <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_field_projectType`, '项目类型')">
            {{ form.itemType_dictText }}
          </a-form-model-item>
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_SaleMassProdHeadList_projectName`, '项目名称')">
            {{ form.itemName }}
          </a-form-model-item>
        </a-form-model>
        <a-form-model
          :model="form">
          <a-form-model-item
            label="">
            <p v-html="form.itemContent"></p>
          </a-form-model-item>
        </a-form-model>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>

export default {
    name: 'ViewPurchaseContractLibraryHisModal',
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    components: {},
    data () {
        return {
            modalTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
            confirmLoading: false,
            madalVisible: false,
            labelCol: {
                xs: {span: 24},
                sm: {span: 5}
            },
            wrapperCol: {
                xs: {span: 24},
                sm: {span: 16}
            },
            form: {
                itemContent: '', id: '', itemName: '', itemType: '3', itemVersion: ''
            }
        }
    },
    mounted () {
    },
    methods: {
        open (row) {
            this.madalVisible = true
            this.form = row
        },
        goBack () {
            this.$emit('hide')
            this.madalVisible = false
        },
        editOk () {
            this.madalVisible = false
            this.$emit('hide')
        },
        cancelEvent () {
            this.madalVisible = false
        }
    }
}
</script>
