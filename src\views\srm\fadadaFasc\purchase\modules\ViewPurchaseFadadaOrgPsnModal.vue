<template>
  <div class="PurchaseFadadaOrgPsn business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="detail"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { BUTTON_BACK } from '@/utils/constant.js'

export default {
    name: 'ViewPurchaseFadadaOrgPsnModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/purchaseFadadaOrgPsn/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            url: {
                detail: '/contract/purchaseContractHead/queryById'
            }
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberName'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRBK_27c4c9ec`, '员工标识'),
                        fieldLabelI18nKey: '',
                        fieldName: 'internalIdentifier',
                        disabled: true                        
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRjd_27c9a610`, '员工邮箱'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberEmail',
                        disabled: true      
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRCEyo_4fc2b43f`, '员工电话号码'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberMobile',
                        disabled: true      
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'),
                        fieldLabelI18nKey: '',
                        fieldName: 'orgName'
                    },
                    // {
                    //     groupCode: 'baseForm',
                    //     fieldType: 'input',
                    //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_srmtReyID_7d49fa64`, 'srm机构账号ID'),
                    //     fieldLabelI18nKey: '',
                    //     fieldName: 'orgId',
                    //     disabled: true       
                    // },
                    // {
                    //     groupCode: 'baseForm',
                    //     fieldType: 'input',
                    //     fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SRMjRID_de875936`, 'SRM员工ID'),
                    //     fieldLabelI18nKey: '',
                    //     fieldName: 'psnId',
                    //     disabled: true       
                    // },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRzE_27c5e958`, '员工状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberStatus_dictText'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRKy_27c8d82d`, '员工角色'),
                        fieldLabelI18nKey: '',
                        fieldName: 'roleType_dictText'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQeRjdtS_43c5cc82`, '是否通过邮箱激活'),
                        fieldLabelI18nKey: '',
                        fieldName: 'notifyActiveByEmail'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LjtSKy_2f41c16a`, '成员激活链接'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberActiveUrl',
                        extend: {
                            linkConfig: {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LjtSKy_2f41c16a`, '成员激活链接'),
                                titleI18nKey: ''
                            },
                            exLink: true
                        }
                    }
                ]
            }
        }
    }
}
</script>
