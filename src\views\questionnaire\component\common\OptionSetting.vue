<template>
  <div class="option-setting">
    <div class="option-title">
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_setOption`, '设置选项') }}:
    </div>
    <div>
      <a-table
        ref="setOption"
        :columns="columns"
        :rowKey="record => record.key"
        :dataSource="options"
        :pagination="false"
      >
        <template
          slot="label"
          slot-scope="text, record, $index">
          {{ `${String.fromCharCode((65 + $index))}.` }}
          <a-input
            :value="text"
            style="width: 80px;"
            @change="(e) => {changeLabel(e, record)}"/>
        </template>
        <template
          slot="rightAnswer"
          slot-scope="text, record, $index">
          <a-checkbox
            :checked="record.rightAnswer == 1 ? true : false"
            @change="rightChange($event, record)"
          >
          </a-checkbox>
        </template>
        <template
          slot="action"
          slot-scope="text, record">
          <a-popconfirm
            :title="$srmI18n(`${$getLangAccount()}#i18n_title_deleteTips`, '确定删除吗？') "
            :okText="$srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确定')"
            :cancelText="$srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消')"
            @confirm="delOptions(record)">
            <a href="javascript:;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a>
          </a-popconfirm>
          <a-divider type="vertical" />
        </template>
      </a-table>
      <div style="text-align: center;margin-top: 10px;">
        <a-button
          @click="addOptions"
          icon="plus"
          type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_title_addSelect`, '添加选项') }}</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { findIndexWithKey } from '../../utils/core'
import { deepClone, generateUnitId } from '../../utils'
import Sortable from 'sortablejs'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    name: 'OptionSetting',
    data () {
        return {
            visible: false,
            activeType: '',
            activeArr: [],
            valueType: 1, // value类型 0 数字 1 字符串
            optionData: [
                { id: 10001, name: 'Test1', nickname: 'T1', role: '1', age: 28, address: 'Shenzhen', date12: '', date13: '' },
                { id: 10002, name: 'Test2', nickname: 'T2', role: '2', age: 22, address: 'Guangzhou', date12: '', date13: '2020-08-20' }
            ],
            roleList: [
                {
                    label: srmI18n(`${getLangAccount()}#i18n_title_yes`, '是'),
                    value: '0',
                    disabled: false
                },
                {
                    label: srmI18n(`${getLangAccount()}#i18n_title_no`, '否'),
                    value: '1',
                    disabled: false
                }
            ],
            columns: [
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_optionContent`, '选项内容'),
                    dataIndex: 'label',
                    scopedSlots: { customRender: 'label' }
                },
                {
                    title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
                    dataIndex: 'action',
                    scopedSlots: { customRender: 'action' }
                }
            ],
            options: []
        }
    },
    computed: {
    // options () {
    //   const activeArr = findIndexWithKey(this.formData, this.activeKey)
    //   if (activeArr.length === 1) {
    //     return this.formData[activeArr[0]].attr.options
    //   } else if (activeArr.length === 3) {
    //     return this.formData[activeArr[0]].columns[activeArr[1]].children[activeArr[2]].attr.options
    //   } else {
    //     return []
    //   }
    // },
        ...mapState({
            formData: state => state.formDesigner.formData,
            questionType: state => state.formDesigner.questionType,
            activeKey: state => state.formDesigner.activeKey
        }),
        list: {
            get () {
                return this.formData
            },
            set (value) {
                this.updateFormData(value)
            }
        }
    },
    created () {
        if (this.questionType == 1) {
            let rightAs = {
                title: srmI18n(`${getLangAccount()}#i18n_title_rightKey`, '正确答案'),
                dataIndex: 'rightAnswer',
                scopedSlots: { customRender: 'rightAnswer' }
            }
            this.columns.splice(1, 0, rightAs)
            // this.$set(this.columns, 1, rightAs)
        }
        const activeArr = findIndexWithKey(this.formData, this.activeKey)
        let options = []
        if (activeArr.length === 1) {
            options = this.formData[activeArr[0]].attr.options
        }
        if (activeArr.length === 3) {
            options = this.formData[activeArr[0]].columns[activeArr[1]].children[activeArr[2]].attr.options
        }
        // if (typeof options[0].value === 'number') { // 判断value类型
        //     this.valueType = 0
        // } else {
        //     this.valueType = 1
        // }
        this.options = this.transformOptions(options)
        this.activeArr = activeArr
        this.rowDrop()
    },
    methods: {
        rowDrop () {
            this.$nextTick(() => {
                let dom = this.$refs.setOption
                this.sortable = Sortable.create(dom.$el.querySelector('.ant-table-tbody'), {
                    handle: '.ant-table-row',
                    onEnd: ({ newIndex, oldIndex }) => {
                        const tableData = deepClone(this.options)
                        let currRow = tableData.splice(oldIndex, 1)[0]
                        tableData.splice(newIndex, 0, currRow)
                        this.options = tableData
                        this.viewUpdate()
                    }
                })
            })
        },
        onValueType (value) {
            this.valueType = value
        },
        addOptions () {
            const options = deepClone(this.options)
            options.push({
                label: '',
                value: String(this.options.length),
                key: this.options.length + '-' + generateUnitId()
            })
            this.options = options
        },
        delOptions (record) {
            const index = this.findIndexWithKey(record.key)
            const options = deepClone(this.options)
            options.splice(index, 1)
            this.options = options
            this.viewUpdate()
        },
        changeLabel (e, record) {
            const index = this.findIndexWithKey(record.key)
            this.options = this.updateOptions('label', e.target.value, index)
            this.viewUpdate()
        },
        rightChange (e, record) {
            const index = this.findIndexWithKey(record.key)
            let val = e.target.checked ? '1' : '0'
            this.options = this.updateOptions('rightAnswer', val, index)
            this.viewUpdate()
        },
        changeValue (val, record) {
            const index = this.findIndexWithKey(record.key)
            this.options = this.updateOptions('value', val, index)
        },
        transformOptions (options) {
            const opt = deepClone(options)
            return opt.map((item, index) => {
                if (!item.key) {
                    item.key = index + '-' + generateUnitId()
                }
                return item
            })
        },
        findIndexWithKey (key) {
            let index = -1
            for (let i = 0; i < this.options.length; i++) {
                if (this.options[i].key === key) {
                    index = i
                    break
                }
            }
            return index
        },
        updateOptions (key, value, index) {
            const options = deepClone(this.options)
            options[index][key] = value
            return options
        },
        // 同步视图
        viewUpdate () {
            const options = this.options
            const activeArr = this.activeArr
            const formData = deepClone(this.formData)
            if (activeArr.length === 1) {
                formData[activeArr[0]].attr.options = options
            }
            if (activeArr.length === 3) {
                formData[activeArr[0]].columns[activeArr[1]].children[activeArr[2]].attr.options = options
            }
            this.updateFormData(formData)
            this.$emit('update', this.activeKey)
        },
        // 选项保存
        settingOptions (e) {
            let check = true
            let qType= 1
            for (let i = 0; i < this.options.length; i++) {
                const keys = Object.keys(this.options[i])
                for (let n = 0; n < keys.length; n++) {
                    if (this.options[i][keys[n]] === null || this.options[i][keys[n]] === '') {
                        let msg = qType == 1 ? srmI18n(`${getLangAccount()}#i18n_title_optionContentMsg`, '选项内容不能为空') : srmI18n(`${getLangAccount()}#i18n_title_optionContentOrAnswerMsg`, '选项内容或正确答案不能为空')
                        this.$message.error(msg) 
                        return false
                    }
                }
            }
            return check
        },
        ...mapMutations({
            updateFormData: 'setFormData'
        })
    }
}
</script>

<style lang="less" scoped>
.option-title{
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    margin-bottom: 8px;
}
.option-setting :deep(.ant-table-row ){
    cursor: move;
}
</style>
