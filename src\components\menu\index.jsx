import Menu from 'ant-design-vue/es/menu'
import Icon from 'ant-design-vue/es/icon'

const { Item, SubMenu } = Menu
import { REPORT_ADDRESS, REPORT_JMREPORT_ADDRESS_SUFFIX } from '@/utils/const.js'
import { mapGetters } from 'vuex'
import { once } from 'lodash'
export default {
    name: 'SMenu',
    props: {
        menu: {
            type: Array,
            required: true
        },
        theme: {
            type: String,
            default: 'dark'
        },
        mode: {
            type: String,
            default: 'inline'
        },
        collapsed: {
            type: Boolean,
            default: false
        },
        userIconFont: {
            type: Boolean,
            default: false
        },
        searchOpenKeys: {
            type: Array,
            default: () => []
        }
    },
    data () {
        return {
            openKeys: [
                '/purchase'
            ],
            selectedKeys: [],
            cachedOpenKeys: []
        }
    },
    computed: {
        ...mapGetters([
            'setPath',
            'openKeyPath',
            'permissionList'
        ]),
        rootSubmenuKeys: vm => {
            const keys = []
            vm.menu.forEach(item => keys.push(item.path))
            return keys
        }
    },
    mounted () {
        this.updateMenu()
    },
    watch: {
        collapsed (val) {
            if (val) {
                this.cachedOpenKeys = this.openKeys.concat()
                this.openKeys = []
            } else {
                this.openKeys = this.cachedOpenKeys
            }
        },
        $route: function () {
            this.updateMenu()
        },
        searchOpenKeys: {
            handler (val) {
                if (val && val.length > 0) {
                    this.openKeys = this.searchOpenKeys
                }
            },
            deep: true
        }
    },
    methods: {
        // select menu item
        onOpenChange (openKeys) {
            // console.log(this.mode, ' this.mode===')
            // 在水平模式下时执行，并且不再执行后续
            if (this.mode === 'horizontal') {
                this.openKeys = openKeys
                return
            }
            // 非水平模式时
            const latestOpenKey = openKeys.find(key => !this.openKeys.includes(key))
            if (!this.rootSubmenuKeys.includes(latestOpenKey)) {
                this.openKeys = openKeys
            } else {
                this.openKeys = latestOpenKey ? [latestOpenKey] : []
            }
        },
        onceSetOpenKeys: once(function (arr = []) {
            if (!this.setPath) return
            if (this.openKeyPath) {
                arr.push(this.openKeyPath)
                return arr
            }
        }),
        updateMenu () {
            const routes = this.$route.matched.concat()
            const { hidden } = this.$route.meta
            if (routes.length >= 3 && hidden) {
                routes.pop()
                this.selectedKeys = [routes[routes.length - 1].path]
            } else {
                this.selectedKeys = [routes.pop().path]
            }
            let openKeys = []

            this.onceSetOpenKeys(openKeys)

            if (this.mode === 'inline') {
                routes.forEach(item => {
                    openKeys.push(item.path)
                })
            }

            if (!this.selectedKeys || this.selectedKeys[0].indexOf(':') < 0) {
                this.collapsed ? (this.cachedOpenKeys = openKeys) : (this.openKeys = openKeys)
            }

        },

        // render
        renderItem (menu) {
            if (!menu.hidden) {
                return menu.children && !menu.alwaysShow ? this.renderSubMenu(menu) : this.renderMenuItem(menu)
            }
            return null
        },
        handleTitle (meta) {
            let t = meta.title
            if (meta.titleI18nKey) {
                t = this.$srmI18n(`${this.$getLangAccount()}#${meta.titleI18nKey}`, meta.title)
            }
            return t
        },
        renderMenuItem (menu) {
            const target = menu.meta.target || null
            const tag = target && 'a' || 'router-link'
            let props = { to: { name: menu.name } }
            if (menu.route && menu.route === '0') {
                props = { to: { path: menu.path } }
            }
            // 外部系统
            if (REPORT_JMREPORT_ADDRESS_SUFFIX.indexOf(menu.path) !== -1) {
                menu.path = REPORT_ADDRESS + menu.path
            }

            const attrs = { href: menu.path, target: menu.meta.target }

            if (menu.children && menu.alwaysShow) {
                // 把有子菜单的 并且 父菜单是要隐藏子菜单的
                // 都给子菜单增加一个 hidden 属性
                // 用来给刷新页面时， selectedKeys 做控制用
                menu.children.forEach(item => {
                    item.meta = Object.assign(item.meta, { hidden: true })
                })
            }

            return (
                <Item {...{ key: menu.path }}>
                    <tag style="overflow: hidden;text-overflow: ellipsis" title={this.handleTitle(menu.meta)} {...{ props, attrs }}>
                        {this.renderIcon(menu.meta.icon)}
                        <span>{this.handleTitle(menu.meta)}</span>
                    </tag>
                </Item>
            )
        },
        renderSubMenu (menu) {
            const itemArr = []
            if (!menu.alwaysShow) {
                menu.children.forEach(item => itemArr.push(this.renderItem(item)))
            }
            return (
                <SubMenu {...{ key: menu.path }}>
                    <span slot="title">
                        {this.renderIcon(menu.meta.icon)}
                        <span title={this.handleTitle(menu.meta)} >{this.handleTitle(menu.meta)}</span>
                    </span>
                    {itemArr}
                </SubMenu>
            )
        },
        renderIcon (icon) {
            const { userIconFont } = this
            if (icon === 'none' || icon === undefined) {
                return null
            }
            const props = {}
            typeof (icon) === 'object' ? props.component = icon : props.type = icon
            return (
                userIconFont
                    ? <icon-font {...{ props }} />
                    : <Icon {...{ props }} />
            )
        }
    },
    render () {
        const { mode, theme, menu } = this
        const props = {
            mode: mode,
            theme: theme,
            openKeys: this.openKeys
        }
        const on = {
            select: obj => {
                this.selectedKeys = obj.selectedKeys
                this.$emit('select', obj)
            },
            openChange: this.onOpenChange
        }

        const menuTree = menu.map(item => {
            if (item.hidden) {
                return null
            }
            return this.renderItem(item)
        })
        // {...{ props, on: on }}
        return (
            <Menu vModel={this.selectedKeys} {...{ props, on: on }}>
                {menuTree}
            </Menu>
        )
    }
}
