<template>
  <div>
    <a-switch
      v-bind="$attrs"
      :auto-focus="autoFocus"
      :disabled="disabled"
      :size="size"
      :loading="loading"
      :checked="checked === openValue ? true : false"
      :default-checked="defaultChecked"
      :checked-children="checkedChildren"
      :un-checked-children="unCheckedChildren"
      @change="changeEvent" />
  </div>
</template>
<script>
export default {
    name: 'MSwitch',
    model: {
        prop: 'checked',
        event: 'change'
    },
    props: {
        autoFocus: {
            type: Boolean,
            default: false
        },
        checked: [Boolean, Number, String],
        closeValue: {
            type: [Boolean, Number, String],
            default: '0'
        },
        openValue: {
            type: [Boolean, Number, String],
            default: '1'
        },
        checkedChildren: {
            type: String,
            defautl: ''
        },
        unCheckedChildren: {
            type: String,
            default: ''
        },
        defaultChecked: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        },
        loading: {
            type: Boolean,
            default: false
        },
        size: {
            type: String,
            default: 'default'
        }
    },
    methods: {
        changeEvent (val, event) {
            if(val) {
                this.$emit('change', this.openValue, event, '', this.$attrs.configData)
            } else {
                this.$emit('change',  this.closeValue, event, '', this.$attrs.configData)
            }
        }
    }
}
</script>