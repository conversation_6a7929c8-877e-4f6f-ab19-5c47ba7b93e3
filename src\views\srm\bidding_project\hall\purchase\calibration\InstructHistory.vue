<template>

  <div class="Instruct">
    <content-header
      v-if="showHeader"
      class="posA"
    />

    <div
      class="container"
      :style="style">

      <a-spin :spinning="confirmLoading">
        <div
          class="itemBox"
          v-if="historyData.length">
          <a-tabs
            v-model="current"
            @change="handleTabChange">
            <a-tab-pane
              v-for="(el, idx) in historyData"
              :key="idx"
              :tab="getTabName(idx)" />
          </a-tabs>
        </div>

        <div class="itemBox">
          <div class="table">
            <vxe-grid
              ref="biddingSupplierList"
              v-bind="defaultGridOption"
              :columns="columns">
              <template slot="empty">
                <a-empty />
              </template>
            </vxe-grid>
          </div>
        </div>
      </a-spin>
    </div>

    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="vuex_currentEditRow" />

  </div>
</template>

<script lang="jsx">
import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import { getAction, postAction, httpAction, downFile } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
import { SEQ_COLUMN, ROWDEMO } from '@/utils/constant.js'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

import { once } from 'lodash'

export default {
    components: {
        flowViewModal,
        'content-header': ContentHeader
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh'
    ],
    data () {
        return {
            current: 0,
            historyData: [],
            flowView: false,
            flowId: '',
            confirmLoading: false,
            detailPromise: {},
            purchaseBiddingItemList: [],
            form: {},
            showHeader: true,
            height: 0,
            //默认表格配置
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: true,
                columnKey: true,
                highlightHoverRow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                height: 400,
                data: [],
                radioConfig: { highlight: false, trigger: 'row' },
                checkboxConfig: { highlight: false, trigger: 'row' },
                editConfig: { trigger: 'click', mode: 'cell' }
            },
            columns: [
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                    ...ROWDEMO,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                    fieldLabelI18nKey: 'i18n_title_supplierELSAccount',
                    field: 'toElsAccount',
                    width: 150
                },
                {
                    ...ROWDEMO,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'),
                    fieldLabelI18nKey: 'i18n_massProdHead88b_supplierErpCode',
                    field: 'supplierCode',
                    width: 150
                },
                {
                    ...ROWDEMO,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                    fieldLabelI18nKey: 'i18n_massProdHeade95_supplierName',
                    field: 'supplierName',
                    width: 150
                },
                // {
                //     ...ROWDEMO,
                //     title: '否决投标',
                //     fieldLabelI18nKey: 'i18n_field_QKeB_277f7b1f',
                //     field: 'bidEva_dictText',
                //     width: 150,
                // },
                {
                    ...ROWDEMO,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQLULYIL_88b87b1f`, '供应商是否为评委推荐人名称'),
                    fieldLabelI18nKey: 'i18n_field_KQLULYIL_88b87b1f',
                    slots: {
                        default: ({ row }) => {
                            let txt = row.recommend === '1' ? `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是')}` : `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否')}`
                            return [
                                (<span>{ txt }</span>)
                            ]
                        }
                    },
                    width: 150
                },
                {
                    ...ROWDEMO,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Mksz_32ad7d23`, '汇总得分'),
                    fieldLabelI18nKey: 'i18n_field_Mksz_32ad7d23',
                    field: 'totalScore',
                    width: 120
                }
            ],
            options: [
                {
                    value: '2',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notWonTheBid`, '未中标')
                },
                {
                    value: '3',
                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_wonTheBid`, '已中标')
                }
            ]
        }
    },
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        // 判断是否为拆分方式
        isQuotaWay () {
            return this.vuex_currentEditRow.quotaWay === '1'
        },
        btns () {
            let arr = [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), type: 'primary', event: 'exportRecord'},
                // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatPriceRecord`, '生成价格记录'), type: 'primary', event: 'record' },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'), type: '', event: 'cancelAudit', showCondition: this.showRevokeApprovalBtn },
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: '', event: 'showFlow', showCondition: this.showViewProcessBtn }
            ]
            let other = this.vuex_currentEditRow.biddingStatus === '5'
                ? [{ title: '变更定标结果', type: 'primary', event: 'update' }]
                : [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', event: 'save' },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'), type: 'primary', event: 'submit' }
                ]

            return arr.concat(other)
        }
    },

    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleTabChange () {
            const result = this.historyData[this.current] || {}
            const { biddingSupplierList = [] } = result
            this.defaultGridOption.data = biddingSupplierList
            if (!biddingSupplierList.length) {
                return
            }
            const { purchaseBiddingItemList = [] } = biddingSupplierList[0] || {}
            if (!purchaseBiddingItemList.length) {
                return
            }
            this.purchaseBiddingItemList = purchaseBiddingItemList
        },
        getTabName (idx) {
            let arr = [
                this.$srmI18n(`${this.$getLangAccount()}#i18n_field_n_7b2c`, '第'),
                ` ${idx + 1} `,
                this.$srmI18n(`${this.$getLangAccount()}#i18n_title_count`, '次'),
                this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bided`, '定标')
            ]
            return arr.join('')
        },
        getDetailPromise () {
            const { id = '' } = this.vuex_currentEditRow || {}
            const url = '/bidding/purchaseBiddingHead/queryById'
            return getAction(url, { id })
        },
        showFlow () {
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        showRevokeApprovalBtn () {
            const { resultAuditStatus, resultAudit } = this.detailPromise || {}
            if ((resultAuditStatus == '1' || resultAuditStatus == '3') && resultAudit === '1') {
                return true
            }
            return false
        },
        showViewProcessBtn () {
            const {resultAuditStatus, resultAudit } = this.detailPromise || {}
            if (resultAuditStatus == '1'  && resultAudit === '1') {
                return true
            }
            return false
        },
        closeFlowView () {
            this.flowView = false
        },
        cancelAudit () {
            let param = {}
            param['businessType'] = 'resultBidding'
            param['businessId'] = this.vuex_currentEditRow.id
            param['rootProcessInstanceId'] = this.flowId
            this.confirmLoading = true
            httpAction('/a1bpmn/audit/api/cancel', param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.getTenderData()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        //导出
        exportRecord () {
            const exportUrl='/bidding/purchaseBiddingHead/exportConfirmBidXls'
            const params = { id: this.vuex_currentEditRow.id, quotaWay: this.vuex_currentEditRow.quotaWay }
            downFile(exportUrl, params).then((data) => {
                if (data.type=='application/json') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), '定标请示.xlsx')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '定标请示.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            }).finally(() => {
                this.gridLoading = false
            })
        },
        getHistoryData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingHead/queryConfirmBidHisById'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    this.historyData = res.result || []
                    this.handleTabChange()
                    this.setColumns()
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        setColumns () {
            const itemColumns = this.purchaseBiddingItemList.map((item, idx) => {
                return {
                    title: item.materialDesc,
                    children: [
                        {
                            title: '报价',
                            fieldLabelI18nKey: 'i18n_title_offer',
                            field: `price_${idx}`,
                            slots: {
                                default: ({ row }, h) => {
                                    return [
                                        (<span>{ row.purchaseBiddingItemList[idx].price || '' }</span>)
                                    ]
                                }
                            },
                            width: 150
                        },
                        {
                            title: '授标',
                            fieldLabelI18nKey: 'i18n_title_contractAward',
                            field: `itemStatus_${idx}`,
                            // editRender: {
                            //     enabled: true,
                            // },
                            slots: {
                                default: ({ row }, h) => {
                                    let label = row.purchaseBiddingItemList[idx].itemStatus === '3' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_wonTheBid`, '已中标') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notWonTheBid`, '未中标')

                                    return [
                                        (<span>{ label }</span>)
                                    ]
                                },
                                edit: (({row}, h) => {
                                    const opts = this.options.map(n => {
                                        return (<vxe-option value={ n.value } label={n.label}></vxe-option>)
                                    })

                                    return [
                                        (<vxe-select vModel={ row.purchaseBiddingItemList[idx].itemStatus }>
                                            { opts }
                                        </vxe-select>)
                                    ]
                                })
                            },
                            width: 150
                        },
                        {
                            title: '拆分比例(%)',
                            fieldLabelI18nKey: 'i18n_title_splitRatio',
                            field: `quota_${idx}`,
                            visible: this.isQuotaWay,
                            // editRender: {
                            //     enabled: true
                            // },
                            slots: {
                                default: ({ row }, h) => {
                                    return [
                                        (<span>{ row.purchaseBiddingItemList[idx].quota || '' }</span>)
                                    ]
                                },
                                edit: (({row}) => {
                                    const props = {
                                        type: 'number',
                                        min: '0'
                                    }
                                    return [
                                        (<vxe-input vModel={row.purchaseBiddingItemList[idx].quota} {...{ props }} />)
                                    ]
                                })
                            }
                        }
                    ]
                }
            })
            console.log('itemColumns :>> ', itemColumns)
            this.columns = this.columns.concat(itemColumns)
        },
        handleSave (handleSubmit) {
            let biddingSupplierList = this.defaultGridOption.data || []
            if (this.isQuotaWay) {
                // 完成拆分百分比之和不能超过 100 的校验
                for (let i = 0; i < this.purchaseBiddingItemList.length; i++) {
                    let materialDesc = this.purchaseBiddingItemList[i].materialDesc
                    // 拆分比例
                    let count = 0
                    biddingSupplierList.forEach(n => {
                        let quota = n.purchaseBiddingItemList[i].quota || 0
                        count += Number(quota)
                    })

                    if (count !== 100) {
                        this.$message.error(`${materialDesc}, 物料拆分比例合计须等于100%`)
                        return
                    }
                }
            }

            const callback = () => {
                const params = {
                    id: this.vuex_currentEditRow.id,
                    biddingSupplierList
                }

                const url = '/bidding/purchaseBiddingHead/saveConfirmBid'

                this.confirmLoading = true
                postAction(url, params)
                    .then(res => {
                        if (!handleSubmit){
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                        }else {
                            handleSubmit()
                        }
                    })
                    .finally(() => {
                        this.confirmLoading = false
                    })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_calibration`, '定标'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmCalibration`, '是否确认当前定标结果?'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        handleUpdate () {
            const callback = () => {
                const params = {
                    id: this.vuex_currentEditRow.id
                }
                const url = '/bidding/purchaseBiddingHead/updateBidComfirmResult'

                this.confirmLoading = true
                getAction(url, params)
                    .then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        this.routerRefresh()
                    })
                    .finally(() => {
                        this.confirmLoading = false
                    })
            }
            let contentTip = [
                this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isSure`, '是否确认'),
                '变更定标结果'
            ]
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                content: contentTip.join(''),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })

        },
        getHeadDataRow () {
            this.getDetailPromise()
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { timestamp = '', result = {} } = res || {}
                    this.timestamp = timestamp
                    this.detailPromise = result
                    this.flowId = result.resultFlowId
                })
        },
        // 提交审批
        handleSubmit () {
            this.getDetailPromise()
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { timestamp = '', result = {} } = res || {}
                    this.timestamp = timestamp
                    const { id, biddingNumber, resultAuditStatus, resultAudit, biddingStatus } = result || {}
                    if (resultAudit === '0') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentNotNeedSubmit`, '该单据无需提交审批'))
                        return
                    }
                    if (resultAuditStatus === '1' || resultAuditStatus ==='2') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unapprovedSubmit`, '未审批的单据才可提交审批'))
                        return
                    }
                    if (biddingStatus !== '5') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingSubmit`, '已定标的单据才可提交审批'))
                        return
                    }
                    const callback = () => {
                        let handleSubmit = () => {
                            const params = {
                                businessId: id,
                                businessType: 'resultBidding',
                                auditSubject: `招标结果审批, 单号: ${biddingNumber}`,
                                params: JSON.stringify(this.vuex_currentEditRow)
                            }
                            const url = '/a1bpmn/audit/api/submit'

                            this.confirmLoading = true
                            postAction(url, params)
                                .then(res => {
                                    const type = res.success ? 'success' : 'error'
                                    this.$message[type](res.message)
                                })
                                .finally(() => {
                                    this.confirmLoading = false
                                })
                        }
                        this.handleSave(handleSubmit)
                    }
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmApproveTips`, '是否确认提交审批?'),
                        onOk () {
                            callback && callback()
                        },
                        onCancel () {
                            console.log('Cancel')
                        }
                    })
                })
        },
        // 生成价格记录
        handleRecord () {
            this.getDetailPromise()
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { timestamp = '', result = {} } = res || {}
                    this.timestamp = timestamp
                    const { id, biddingStatus, priceCreateWay } = result || {}
                    if (priceCreateWay == '1') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cIumCKLJOcIWxqlObL_4e50da54`, '创建价格方式为自动创建、不可手动生成'))
                        return
                    }
                    if (biddingStatus !== '5') {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IIBntFnqbLumdWF_33b87883`, '已定标的单据才可生成价格主数据'))
                        return
                    }
                    const callback = () => {
                        const params = { id }
                        const url = '/bidding/purchaseBiddingHead/manualCreatePrce'

                        this.confirmLoading = true
                        getAction(url, params)
                            .then(res => {
                                const type = res.success ? 'success' : 'error'
                                this.$message[type](res.message)
                            })
                            .finally(() => {
                                this.confirmLoading = false
                            })
                    }
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatPriceInfoTips`, '是否确认生成价格信息记录?'),
                        onOk () {
                            callback && callback()
                        },
                        onCancel () {
                            console.log('Cancel')
                        }
                    })
                })
        },
        init () {
            this.height = document.documentElement.clientHeight
            let half = this.height / 2
            this.defaultGridOption.height = Math.max(half, 400)
            this.getHistoryData()
            this.getHeadDataRow()
        }
    },
    // 使用 beforeRouteUpdate 导航守卫监听路由变化
    // 对路由销毁重建
    beforeRouteUpdate (to, from, next) {
        this.routerRefresh() //路由销毁重建方法
        next()
    },
    created () {
        this.init()
    }
}
</script>

<style lang="less" scoped>
.Instruct {
    .posA {
        & +.container {
            margin-top: 44px;
        }
    }
    .container {
        background: #fff;
        padding: 12px;
    }
    .itemBox {
        + .itemBox {
        margin-top: 12px;
        }
    }
	.title {
		padding: 0 7px;
		border: 1px solid #ededed;
		height: 34px;
		line-height: 34px;
		&.dark {
			background: #f2f2f2;
		}
	}
	.table,
	.description {
		margin-top: 10px;
	}
}
</style>

