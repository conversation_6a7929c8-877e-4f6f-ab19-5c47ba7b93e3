<!--
 * @Author: fzb
 * @Date: 2022-02-21 16:28:52
 * @LastEditTime: 2022-03-01 17:14:50
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\components\designer\widget\chartsBar\src\main.vue
-->
<template>
  <div>
    <vue-echarts
      :style="style"
      :option="widget.option"
      :update-options="{ notMerge: true }"
      autoresize
    />

  </div>

</template>
<script>
import { chartsMixins } from '@comp/chart/widget/mixins/chartsMixins'
import _ from 'lodash'
export default {
    name: 'ChartsBar',
    mixins: [chartsMixins],
    computed: {
        option () {
            return {
                barWidth: this.widget.option.series[0].barWidth,
                itemStyle: this.widget.option.series[0].itemStyle
            }
        }
    },
    watch: {
        option: {
            handler (val) {
                for (let i = 1; i < this.widget.option.series.length; i++) {
                    this.widget.option.series[i].barWidth = val.barWidth
                    this.widget.option.series[i].itemStyle = _.cloneDeep(val.itemStyle)
                }
            },
            deep: true
        }
    },
    mounted () {
        this.refreshWidgetInfo()
    },
    methods: {
        refreshWidgetData (data) {
            const option = this.widget.option
            option.xAxis.data = data.categories
            // 每次刷新都只保留一个Series元素，其他的删掉
            option.series.splice(1, option.series.length - 1)
            for (let i = 0; i < data.series.length; i++) {
                let chartData = data.series[i]
                let item = option.series[i]
                if (!item) {
                    // 如果数据长度大于配置长度，则深拷贝一个配置出来
                    option.series.push(_.cloneDeep(option.series[0]))
                    item = option.series[i]
                }
                item.name = chartData.name
                item.data = chartData.data
            }
        }
    }
}
</script>