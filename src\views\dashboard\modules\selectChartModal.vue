<template>
  <div>
    <a-modal
    v-drag    
      centered
      :title="title"
      :width="800"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="close">
      <a-transfer
        :list-style="{
          width: '350px',
          height: '300px',
        }"
        :data-source="sourceData"
        :target-keys="targetKeys"
        :render="item => `${item.chartCode}__${item.chartName}`"
        @change="handleChange"
      >
      </a-transfer>
    </a-modal>
  </div>
</template>
<script>
import { getAction } from '@/api/manage'
import { Transfer } from 'ant-design-vue'
export default {
    components: {
        ATransfer: Transfer
    },
    props: {
        selectType: {
            type: String,
            default: ''
        }
    },
    data () {
        return {
            targetKeys: [],
            selectedPanel: [],
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_chartSelect`, '图表选择'),
            visible: false,
            loading: false,
            confirmLoading: false,
            urlList: {
                item: '/report/dashboard/chartConfig/list'
            },
            sourceData: []
        }
    },
    created () {
        this.loadData({pageSize: 9999, pageNo: 1})
    },
    
    methods: {
        loadData (params) {
            this.loading = true
            getAction(this.urlList.item, params).then((res) => {
                let list = res.result.records || []
                list.forEach(item => {
                    item.key = item.id
                    item.title = item.chartName
                })
                this.sourceData = list
                this.loading = false
            })
        },
        open (list) {
            let targetKeys = list.map(item => {
                return item.chartId
            })
            this.selectedPanel = targetKeys
            this.targetKeys = targetKeys
            this.visible = true
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            if(this.targetKeys.length) {
                this.visible = false
                let selectedList = []
                // 获取选中数据
                this.sourceData.forEach(item => {
                    if(this.targetKeys.includes(item.id) && !this.selectedPanel.includes(item.id)) {
                        selectedList.push(item)
                    }
                })
                //删除取消选中的数据
                this.selectedPanel.forEach((panel) => {
                    if(!this.targetKeys.includes(panel)) {
                        this.$parent.deletePanel(panel)
                    }
                })

                this.$emit('ok', selectedList)
            }else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
            }
        },
        handleChange (targetKeys) {
            this.targetKeys = targetKeys
        }
    }
}
</script>