<template>
  <div>
    <a-modal
    v-drag    
      centered
      :title="title"
      :width="960"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="close"
    >
      <a-form-model layout="inline">
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_sourceType`, '来源类型')">
          <m-select
            style="width:200px"
            v-model="suorceType"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectSourceType`, '请选择来源类型')"
            @change="suorceTypeChange"
            dict-code="srmAddCostItemSourceType" />
        </a-form-model-item>
        <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_keyword`, '关键字')">
          <a-input
            @keyup.enter.native="onSearch"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')"
            v-model="keyWord"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button
            type="primary"
            @click="onSearch">{{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}</a-button>
        </a-form-model-item>
      </a-form-model>
      <vxe-grid
        border
        resizable
        max-height="350"
        row-id="id"
        size="mini"
        :loading="loading"
        ref="selectGrid"
        :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
        :data="tableData"
        :pager-config="tablePage"
        :radio-config="selectModel === 'single' ? checkedConfig : undefined"
        :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
        :columns="currentColumns"
        @page-change="handlePageChange"
      >
      </vxe-grid>
    </a-modal>
  </div>
</template>
<script>
import {getAction} from '@/api/manage'
import {getLangAccount, srmI18n} from '@/utils/util'

export default {
    props: {
        title: {
            type: String,
            default: srmI18n(`${getLangAccount()}#i18n_title_selectDataMsg`, '选择数据')
        },
        columns: {
            type: Array,
            default: () => [
                { type: 'checkbox', width: 40 },
                { type: 'seq', title: srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号'), width: 60 }
            ]
        },
        selectModel: {
            type: String,
            default: 'multiple'
        }
    },
    data () {
        return {
            suorceType: 'order',
            suorceTypeName: this.$srmI18n(`${this.$getLangAccount()}#`, '采购订单'),
            keyWord: '',
            visible: false,
            loading: false,
            confirmLoading: false,
            currentUrl: '/order/purchaseOrderItem/financeQueryOrderItem',
            urlList: {

                order: '/order/purchaseOrderItem/financeQueryOrderItem',
                delivery: '/delivery/purchaseDeliveryHead/financeQueryDelivery',
                refundsDelivery: '/delivery/purchaseRefundsDeliveryHead/financeQueryDeliveryItem'
            },
            currentColumns: [
                { type: 'checkbox', width: 40 },
                { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 120 },
                { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 120 },
                { field: 'purchaseOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                { field: 'orderDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderDate`, '订单日期'), width: 120 },
                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 100 },
                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), width: 120 },
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialSpec`, '物料规格'), width: 120 },
                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '数量'), width: 100 },
                { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 100 },
                { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税单价'), width: 120 },
                { field: 'taxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), width: 120 }
            ],
            columnList: {
                order: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 120 },
                    { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 120 },
                    { field: 'purchaseOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                    { field: 'orderDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderDate`, '订单日期'), width: 120 },
                    { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 100 },
                    { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                    { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), width: 120 },
                    { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                    { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '数量'), width: 100 },
                    { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 100 },
                    { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税单价'), width: 120 },
                    { field: 'taxAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'), width: 120 }
                ],
                delivery: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'deliveryNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shipmentNo`, '发货单号'), width: 120 },
                    { field: 'deliveryTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliveryTime`, '发货时间'), width: 120 },
                    { field: 'planArriveDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_planArriveDate`, '计划到货日期'), width: 120 },
                    { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 120 },
                    { field: 'orderItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                    { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 120 },
                    { field: 'purchaseOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                    { field: 'factory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'), width: 120 },
                    { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                    { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), width: 120 },
                    { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                    { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireDate`, '需求日期'), width: 120 },
                    { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 120 },
                    { field: 'deliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hSWR_28388855`, '发货数量'), width: 120 }
                ],
                refundsDelivery: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 },
                    { field: 'refundsDeliveryNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_refundsDeliveryNumber`, '退货单号'), width: 120 },
                    { field: 'refundsDeliveryWay_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_refundsDeliveryWay`, '退货方式'), width: 120 },
                    { field: 'refundsDeliveryTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_refundsDeliveryTime`, '退货时间'), width: 120 },
                    { field: 'planReturnDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_planArriveDate`, '计划到货日期'), width: 120 },
                    { field: 'orderNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'), width: 120 },
                    { field: 'orderItemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                    { field: 'company_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '公司'), width: 120 },
                    { field: 'purchaseOrg_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseOrg`, '采购组织'), width: 120 },
                    { field: 'factory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'), width: 120 },
                    { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120 },
                    { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialName`, '物料名称'), width: 120 },
                    { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150 },
                    { field: 'receiveQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveQuantity`, '收货数量'), width: 120 },
                    { field: 'refundsQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_refundsQuantity`, '退货数量'), width: 120 }
                ]
            },
            labelCol: {
                md: { span: 8 },
                lg: { span: 8 },
                xxl: { span: 6 }
            },
            wrapperCol: {
                md: { span: 16 },
                lg: { span: 16 },
                xxl: { span: 18 }
            },
            checkedConfig: {highlight: true, reserve: true, trigger: 'row'},
            tableData: [],
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                pageNo: 1,
                align: 'left',
                pageSizes: [10, 20, 50, 100, 200, 500],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            propParams: {}
        }
    },
    methods: {
        suorceTypeChange (newValue, opt) {
            if (newValue != '') {
                this.suorceType = newValue
                this.suorceTypeName = opt.componentOptions.propsData.title
                this.currentUrl = this.urlList[newValue]
                this.currentColumns = this.columnList[newValue]
                this.$refs.selectGrid.reloadColumn(this.currentColumns)
                var p = Object.assign({}, this.propParams, {keyWord: this.keyWord})

                if (newValue == 'order') {
                    p = Object.assign(p, {itemStatus: '1'})
                }
                this.loadData(p)
                this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxRow()
            }
        },
        loadData (params) {
            this.loading = true
            getAction(this.currentUrl, params).then(res => {
                if (res.success) {
                    let list = res.result.records || []
                    this.tableData = list
                    this.tablePage.total = res.result.total
                }
                this.loading = false
            })
        },
        open (params) {
            // 重置第一个
            this.suorceType = 'order'
            let queryParams = {pageSize: this.tablePage.pageSize, pageNo: '1'}
            if (params) {
                queryParams = Object.assign({}, queryParams, params)
            }
            this.propParams = {...queryParams}
            this.loadData(queryParams)
            this.visible = true
            this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxRow()
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            debugger
            let itemGrid = this.$parent.getItemGridRef('addCostItemList')
            // let checkboxRecords = itemGrid.getCheckboxRecords()[0]
            let selectedData = this.$refs.selectGrid.getCheckboxRecords()
            if (!(selectedData?.length)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            let resultList = []
            for (let i in selectedData) {
                let data = selectedData[i]
                let item = {}
                if (this.suorceType === 'order') {
                    data.relationNumber = data['orderNumber']
                    data.relationItemNumber = data['itemNumber']
                } else if (this.suorceType === 'delivery') {
                    data.relationNumber = data['deliveryNumber']
                } else if (this.suorceType === 'refundsDelivery') {
                    data.relationNumber = data['refundsDeliveryNumber']
                }
                data.relationType = this.suorceType
                resultList.push(item)
            }
            itemGrid.insert(selectedData)
            this.visible = false
            this.$emit('ok', resultList)
        },
        handlePageChange ({currentPage, pageSize}) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            let pageNo = currentPage
            let queryParams = Object.assign({}, this.propParams, {pageNo, pageSize, keyWord: this.keyWord})


            /*
        根据不同的url增加入参
        this.suorceType
         */
            let p = queryParams

            if (this.suorceType == 'order') {
                p = Object.assign(p, {itemStatus: '1'})
            }
            this.loadData(p)
        },
        onSearch () {
            console.log(this.suorceType)
            // 提交重置
            this.tablePage.currentPage = 1
            this.tablePage.pageSize = this.propParams.pageSize
            if (this.suorceType == '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectSourceType`, '请选择来源类型'))
            } else {
                let p = Object.assign({}, this.propParams, {keyWord: this.keyWord})
                /*
        根据不同的url增加入参  采购类型的入参有多个，直接在后端写死了
         */
                if (this.suorceType == 'order') {
                    p = Object.assign(p, {itemStatus: '1'})
                }
                this.loadData(p)
            }
        }
    }
}
</script>