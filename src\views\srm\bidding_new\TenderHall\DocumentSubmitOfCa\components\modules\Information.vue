<template>
  <div>
    <div v-show="isTetterListTable">
      <titleTrtl class="margin-b-10">
        <span>{{ currentRow.name }}</span>
        <template
          slot="right"
          v-if="currentRow"
        >
          <a-button
            type="primary"
            size="small"
            v-if="showMaintenanceMaterial"
            @click="maintenanceMaterial">{{ $srmI18n(`${$getLangAccount()}#i18n_field_LDSLc_2b9121cc`, '维护物料行') }}</a-button>
        </template>
      </titleTrtl>
      <listTable
        ref="listTable"
        v-if="show && !isEncryption"
        :pageStatus="pageStatus"
        setGridHeight="auto"
        :mergeCells="mergeCells"
        :fromSourceData="priceOpeningsList.customizeFieldData"
        :statictableColumns="statictableColumns"
        :showTablePage="false" />
      <div v-if="show && isEncryption">
        {{ $srmI18n(`${$getLangAccount()}#i18n_field_eBxCcIuwWNTmAVCtWyweBxWpS_915bf74`, '投标函内容已加密，如需查看请点击“解密投标函”按钮') }}
      </div>
    </div>
    <materialList
      v-show="!isTetterListTable"
      :show="!isTetterListTable"
      ref="materialList"
      @back="showTetterListTable"
      @setQuotePrice="setQuotePrice"
      :pageStatus="pageStatus"
      :currentRow="currentRow"
    />
  </div>
</template>
<script>
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import materialList from './materialList.vue'
export default {
    mixins: [baseMixins],
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        currentRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        priceOpeningsList: {
            type: Object,
            default: () => {
                return {}
            }
        },
        fromSourceData: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    computed: {
        // 是否加密
        isEncryption () {
            if (this.priceOpeningsList) {
                let flag = false
                switch (this.priceOpeningsList.encryption) {
                case '0':
                    flag = false
                    break
                case '1':
                    flag = true
                    break
                case '2':
                    flag = false
                    break
                default:
                    flag = false
                }
                return flag
            } else {
                return false
            }
        },
        mergeCells () {
            if (this.currentRow.formatType != '9') {
                let rowspan = this.priceOpeningsList.customizeFieldData.length
                return [
                // row: 行，col: 列，
                    { row: 0, col: 0, rowspan, colspan: 1 }
                ]
            }
            return []
        },
        showMaintenanceMaterial () {
            let checkType = this.checkType||this.priceOpeningsList.checkType
            if (this.priceOpeningsList.encryption !== '1' && checkType =='1' && this.currentRow?.formatType != '9') {
                if (this.currentRow.saleQuoteMaterialDataList && this.currentRow.saleQuoteMaterialDataList.length == 0){
                    return false
                }
                return true
            }
            return false
        }
    },
    components: {
        listTable,
        titleTrtl,
        materialList
    },
    watch: {
        priceOpeningsList (value) {
            // 初始化列表
            this.initColumns(value)
        }
    },
    data () {
        return {
            statictableColumns: [],
            show: false,
            isTetterListTable: true
        }
    },
    methods: {
        initColumns (data) {
            if (!data) return
            this.show = false
            if (!this.isEncryption) {
                this.statictableColumns = JSON.parse(JSON.stringify(data.customizeFieldModel))
                this.statictableColumns.map(item => {
                    if (item.inputOrg && item.inputOrg == '0' && this.pageStatus == 'edit') {
                        // 报价列类型不是0手动输入的都不能编辑填写
                        if (item.editRender && this.currentRow.quoteColumnSource !== '0' && (item.field == 'quotedPrice' || item.fieldCategory == '1')) {
                            item['editRender']['enabled'] = false
                            item.fieldType = 'text'
                        } else {
                            if (item['editRender']) {
                                item['editRender']['enabled'] = true
                                if (item.field == 'supplierName') {
                                    item.required = true
                                    item['editRender']['events'] = {
                                        change: (currentRow, currentValue) => {
                                            currentRow.items.map(item => {
                                                item.supplierName = currentValue.value
                                            })
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        if (item.editRender) {
                            item['editRender']['enabled'] = false
                        }
                    }
                })
            }
            setTimeout(() => {
                this.show = true
            }, 50)
        },
        getValidate () {
            return this.$refs['listTable'].getValidate()
        },
        maintenanceMaterial () {
            this.isTetterListTable = false
        },
        showTetterListTable () {
            this.isTetterListTable = true
        },
        setQuotePrice (data) {
            let {price, quoteColumnSource, field, materialId} = data
            if ([1, 2].includes(quoteColumnSource)) {
                // 总项报价只有一行需要对应自定义列数据
                this.$set(this.priceOpeningsList.customizeFieldData[0], field, price)
            } else if ([3, 4].includes(quoteColumnSource)) {
                // 分项报价需要对应行数据
                this.priceOpeningsList.customizeFieldData.map(item => {
                    if (item.materialId == materialId) {
                        this.$set(item, 'quotedPrice', price)
                    }
                })
            }
        }
    },
    created () {
        this.initColumns(this.priceOpeningsList)
    }
}
</script>
