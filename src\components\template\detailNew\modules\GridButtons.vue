<template>
  <span>
    <span
      v-for="(btn, i) in getAuthCodeBtns(buttons)"
      class="tools-btn"
      :key="'btn_' + i">
      <a-button
        v-if="btn.type != 'upload' && btn.type !== 'import' && btn.type !== 'check'"
        :type="btn.type"
        v-show="btn.showCondition ? btn.showCondition() : true"
        @click="btn.click">
        {{ btn.title }}
      </a-button>
      <a-button
        v-else-if="btn.type == 'check'"
        :type="btn.type"
        v-show="btn.showCondition ? btn.showCondition() : true"
        @click="checkedGridSelect(btn, tab.custom.ref, btn.beforeCheckedCallBack)">
        {{ btn.title }}
      </a-button>
      <a-upload
        v-else-if="btn.type === 'import'"
        :show-upload-list="false"
        :multiple="false"
        :headers="tokenHeader"
        :data="btn.params"
        :action="url.import"
        @change="(info) => handleUploadChange(info, btn, tab.custom.ref)"
      >
        <a-button type="primary">{{ btn.title }}</a-button>
      </a-upload>
      <custom-upload
        v-else
        v-show="btn.showCondition ? btn.showCondition() : true"
        :single="btn.single"
        :disabledItemNumber="btn.disabledItemNumber"
        :disabled="uploadDisableFn(btn)"
        :requiredFileType="btn.requiredFileType"
        :property="btn.property"
        :visible.sync="btn.modalVisible"
        :title="btn.title"
        :itemInfo="itemInfo"
        :action="url.upload"
        :multiple="btn.multiple || true"
        :accept="accept"
        :dictCode="btn.dictCode || ''"
        :headers="tokenHeader"
        :data="{businessType: btn.businessType, headId: form.id}"
        @change="(info) => handleUploadChange(info, btn, tab.custom.ref)"
      >
        <a-button
          v-if="btn.beforeChecked"
          type="primary"
          icon="cloud-upload"
          @click="checkedGridSelect(btn, tab.custom.ref, btn.beforeCheckedCallBack)">{{ btn.title }}</a-button>
      </custom-upload>
    </span>
  </span>
    
</template>

<script>
import CustomUpload from '@comp/template/CustomUpload'

export default {
    components: {
        CustomUpload
    },
    props: {
        buttons: {
            type: Array,
            default: () => []
        },
        pageData: {
            type: Object,
            default: () => {}
        },
        form: {
            type: Object,
            default: () => {}
        },
        url: {
            type: Object,
            default: () => {}
        },
        tab: {
            type: Object,
            default: () => {}
        }
    },
    data () {
        return {
            tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf'
        }
    },
    methods: {
        // 获取非权限码按钮组
        getAuthCodeBtns (btns) {
            let authBtns = []
            if (btns && btns.length) {
                btns.forEach((item)=> {
                    // 配置authorityCode做权限控制
                    if (item && item.authorityCode) {
                        // 有权限
                        if (this.$hasOptAuth(item.authorityCode)) {
                            authBtns.push(item)
                        }
                    } else {
                        // 不配置authorityCode就不做权限控制
                        authBtns.push(item)
                    }
                })
            }
            return authBtns
        },
        // 检查表格是否有被选中, cb返回promise,true打开上传，false不打开上传，如果没有cb,直接打开上传
        checkedGridSelect (btn, refName, cb) {
            this.$emit('checkedGridSelect', ...arguments)
        },
        itemInfo () {
            let itemInfo = []
            const groups = this.pageData.groups || []
            const group = groups.find(n => n.groupCode === 'itemInfo')
            if (group) {
                const refName = group.custom.ref
                itemInfo = this.$refs[refName][0].getTableData().fullData || []
            }
            return itemInfo
        },
        //附件上传
        handleUploadChange (info, btn, refName) {
            btn.callBack && btn.callBack(info, refName)
        },
        uploadDisableFn (btn) {
            this.$emit('uploadDisableFn', ...arguments)
        }
    }
}
</script>