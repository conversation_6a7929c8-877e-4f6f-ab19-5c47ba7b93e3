<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
    <!-- 加载配置文件 -->
    <field-select-modal 
      ref="fieldSelectModal" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction } from '@/api/manage'
export default {
    name: 'EsignFlowAdd',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'esignFlow',
            visible: false,
            signerBtn: false,
            form: {
                keyword: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: '业务编号',
                                    fieldName: 'busNumber'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'elsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'subject'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjXKI_ef65ec11`, '签署有效时间'),
                                    fieldName: 'expireTime',
                                    dataFormat: 'YYYY-MM-DD',
                                    disabled: true
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWsRKI_fa482f8c`, '签署终止时间'),
                                    fieldName: 'endTime',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: '是否回传签署文档',
                                    fieldName: 'returnSignedFile',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: '回传文件是否确认',
                                    fieldName: 'returnFileConfirm',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: '是否被驳回',
                                    fieldName: 'reject',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: '驳回原因',
                                    fieldName: 'rejectReason'
                                }
                            ]
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'uploaded', title: '是否上传契约锁', width: 180, visible: false },
                            { field: 'uploaded_dictText', title: '是否上传契约锁', width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 220, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载'), clickFn: this.downloadEvent },
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_by6FtppjfaHaxK4d`, '签署文件下载'), clickFn: this.flowFileDownload, allow: this.allowDownload },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } },
                    { groupName: '回传文件', groupCode: 'signRetrunAttachments', type: 'grid', custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '下载'), clickFn: this.downloadReturnFileEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/contractLock/elsClContract/queryById'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        flowFileDownload (row){
            console.log(row)
            let str = row.signFilePath
            const fileName = str.substr(str.lastIndexOf('/')+1, str.length)
            getAction(str, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })

        },
        allowDownload (row){
            if(row.signFilePath){
                return false
            }
            return true
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        },
        esignFIledownloadEvent (row){
            this.downloadFile(row, this.url.esignFIledownload, row.uploadFileName)
        },
        downloadReturnFileEvent (row){
            this.downloadFile(row, row.absoluteFilePath , row.fileName)
        },
        downloadFile (data, url, fileName){
            let id = data.id
            const params = {
                id
            }
            getAction(url, params, {
                responseType: 'blob'
            }).then(res => {
                debugger
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }
    }
}
</script>