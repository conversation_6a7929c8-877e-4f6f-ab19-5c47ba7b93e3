<template>
  <div class="page-container">
    <div class="edit-page">
      <a-spin
        :spinning="confirmLoading">
        <content-header
          v-if="showHeader"
          :btns="btns"
        />
        <div
          class="container"
          :style="style">
          <div>
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBLVH_7e1ecb2e`, '中标人信息') }}</span>
            </titleTrtl>
          </div>
          <div
            v-if="show">
            <finalQuoteList 
              :pageStatus="'edit'"
              :canfinalQuoteList="pageStatus == 'edit'"
              determineType="sub"
              @back="back"
              :show="showfinalQuoteList"
              :currentEditRow="formData"
              v-if="showfinalQuoteList" />
            <div
              v-else
              :is="awardName"
              :pageStatus="pageStatus"
              ref="editLayout"
              :resultData="formData">
            </div>
          </div>
        </div>
        <flowViewModal
          v-model="flowView"
          :flowId="flowId"/>
      </a-spin>
    </div>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '@views/srm/bidding_new/BiddingHall/components/content-header'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import flowViewModal from '@comp/flowView/flowView'
import normalAward from './modules/normalAward'
import materialAward from './modules/materialAward'
import supplierAward from './modules/supplierAward'
import finalQuoteList from './finalQuoteList'
import { add } from '@/utils/mathFloat.js'

export default {
    name: 'DetermineTheWinner',
    components: {
        titleTrtl,
        ContentHeader,
        flowViewModal,
        normalAward,
        supplierAward,
        materialAward,
        finalQuoteList
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            show: false,
            tableData: [],
            formData: {},
            showHeader: true,
            showfinalQuoteList: false,
            height: 0,
            active: 0,
            awardName: 'normalAward',
            awardNameBtn: {
                'normalAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pRdXlB_8d34dee3`, '按供应商授标'), type: 'primary', click: () => {this.changeAwardName('supplierAward')} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pSLRHlB_e8a46290`, '按物料明细授标'), type: 'primary', click: () => {this.changeAwardName('materialAward')} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refresh`, '刷新'), type: 'primary', click: this.reset },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish }
                ],
                'materialAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ],
                'supplierAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ]
            },
            awardNameBtnView: {
                'normalAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pRdXlB_8d34dee3`, '按供应商授标'), type: 'primary', click: () => {this.changeAwardName('supplierAward')} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pSLRHlB_e8a46290`, '按物料明细授标'), type: 'primary', click: () => {this.changeAwardName('materialAward')} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow }
                ],
                'materialAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ],
                'supplierAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ]
            },
            url: {
                queryById: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningFinalCandidate',
                add: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/add',
                edit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/edit',
                submit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryInfo: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningConfirmInfoBySubpackage'
            }
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'currentSubPackage',
        'resetCurrentSubPackage'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        btns () {
            let btn = []
            // 刚进去或者保存过后为新建状态0，展示保存发布按钮
            if (this.formData.status == '0' || !this.formData.status) {
                btn = this.awardNameBtn[this.awardName].map(item => {
                    return item
                })
                //评标方式 0-全部、1-线上、2-线下*/
                let { evaluationType} = this.subPackageRow
                if (evaluationType == '1') {
                    btn.unshift(
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAessu_119c780`, '发起最终报价'), type: 'primary', click: this.finalQuote }
                    )
                }
            }
            //点击发布后已发布状态status == 1以及审批中状态aduitStatus == 1，
            if(this.formData.status == '1' && this.formData.auditStatus == '1'){
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_withdraw`, '撤回'), type: 'primary', click: this.withdraw }
                ]
            }
            if(this.formData.audit == '1' && this.formData.status != '0' && this.formData.flowId != null){
                btn = this.awardNameBtnView[this.awardName].map(item => {
                    return item
                })
            }
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') btn = []
            return btn
        },
        pageStatus () {
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                return 'detail'
            } else {
                if (this.formData.status == '0' || !this.formData.status) {
                    return 'edit'
                } else {
                    return 'detail'
                }
            }
        }
    },
    methods: {
        init (data) {
            this.show = false
            this.confirmLoading = true
            this.formData = data
            setTimeout(() => {
                this.confirmLoading = false
                this.show = true
            }, 100)
        },
        reset () {
            this.$emit('reset')
        },
        getParams () {
            let params = Object.assign({}, this.formData)
            params['winningAffirmMaterialList'] = this.$refs.editLayout.externalAllData()
            let materialMap = {}
            for (let item of params['winningAffirmMaterialList']) {
                if (item.award == '0') continue
                if (!materialMap[item.materialId]) {
                    materialMap[item.materialId] = item.quotaScale
                } else {
                    materialMap[item.materialId] = add(materialMap[item.materialId], item.quotaScale)
                }
                if (materialMap[item.materialId] > 100) {
                    this.$message.warning(`物料：【${item.materialName}】的拆分比例合计不能超过100%`)
                    return false
                }
            }
            return params
        },
        async save () {
            // 拼接数据
            let params = this.getParams()
            if (!params) return
            let url = params.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            postAction(url, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.$parent.$parent.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
                // this.$emit('resetCurrentSubPackage')
                this.resetCurrentSubPackage()
            })
        },
        publish () {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                onOk: () => {
                    let params = this.getParams()
                    if (!params) return
                    this.confirmLoading = true
                    postAction(this.url.submit, params).then(res => {
                        let type = res.success ? 'success': 'error'
                        this.$message[type](res.message)
                        if (res.success) {
                            this.$parent.$parent.getData()
                            this.resetCurrentSubPackage()
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            })
        },
        withdraw (){
            let that = this
            let params = this.formData
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData('/a1bpmn/audit/api/cancel', params)
                }
            })
        },
        showFlow (){
            this.flowId = this.formData.flowId
            this.flowView = true
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['rootProcessInstanceId'] = formData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.$parent.getData()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        changeAwardName (name) {
            this.confirmLoading = true
            this.showfinalQuoteList = false
            // 给个切换loading，假装一下加载
            setTimeout(() => {
                this.awardName = name
                this.confirmLoading = false
            }, 100)
        },
        back () {
            this.showfinalQuoteList = false
        },
        finalQuote () {
            this.showfinalQuoteList = true
        }
    }
}
</script>
<style lang="less" scoped>
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
:deep(.vxe-table--render-wrapper) {
    max-height: 550px;
}
</style>




