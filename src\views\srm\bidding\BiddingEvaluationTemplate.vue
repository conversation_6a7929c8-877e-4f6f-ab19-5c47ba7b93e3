<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showDetailPage && ! showEditPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <BiddingEvaluationTemplateEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <BiddingEvaluationTemplateDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import BiddingEvaluationTemplateDetail from './modules/BiddingEvaluationTemplateDetail'
import BiddingEvaluationTemplateEdit from './modules/BiddingEvaluationTemplateEdit'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, postAction} from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        BiddingEvaluationTemplateDetail,
        BiddingEvaluationTemplateEdit
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'biddingEvaluationTemplate',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_evaluationNoOrName`, '(评标编号/名称)')
                    }
                ],
                form: {
                    keyWord: '',
                    name: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'bidding#biddingEvaluationTemplateHead:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnWidth: 200,
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'bidding#biddingEvaluationTemplateHead:queryById'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'bidding#biddingEvaluationTemplateHead:edit'},
                    {type: 'loseEfficacy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_invalid`, '失效'), clickFn: this.handleLoseEfficacy, allow: this.allowLoseEfficacy, authorityCode: 'bidding#biddingEvaluationTemplateHead:loseEfficacy'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'bidding#biddingEvaluationTemplateHead:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord},
                    {type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopy, authorityCode: 'bidding#biddingEvaluationTemplateHead:copyData'}
                ]
            },
            url: {
                add: '/bidding/biddingEvaluationTemplateHead/add',
                list: '/bidding/biddingEvaluationTemplateHead/list',
                delete: '/bidding/biddingEvaluationTemplateHead/delete',
                loseEfficacy: '/bidding/biddingEvaluationTemplateHead/loseEfficacy',
                columns: 'biddingEvaluationTemplateHead',
                copyData: '/bidding/biddingEvaluationTemplateHead/copyData'

            }
        }
    },
    methods: {
        allowEdit (row){
            //失效，不能编辑
            if(row.templateStatus==='2'){
                return true
            }
            //生效，不能编辑
            if(row.templateStatus==='1'){
                return true
            }
            //未生效，审批中，不能编辑
            if(row.templateStatus==='0' && row.auditStatus==='1'){
                return true
            }
            return false
        },
        allowDelete (row){
            //审批中，不可删除
            if (row.auditStatus==='1') {
                return true
            }
            //失效，可删除
            if(row.templateStatus==='2'){
                return false
            }
            //未生效可删除
            if(!row.templateStatus || row.templateStatus==='' || row.templateStatus==='0'){
                return false
            }
            // if(row.used==='1'){
            //     return true
            // }
            return true
        },
        allowLoseEfficacy (row){
            // if(row.templateStatus==='2'){
            //     return true
            // }
            //生效，可失效
            if(row.templateStatus==='1'){
                return false
            }
            return true
        },
        handleLoseEfficacy (row, column, rowIndex, columnIndex, tableData){
            const that = this
            this.$confirm({
                title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_confirmFailure`, '确认失效'),
                content: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_confirmFailureTips`, '确认将该条模板失效吗?'),
                onOk: function () {
                    that.loading = true
                    getAction(that.url.loseEfficacy, {id: row.id}).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData()
                        }else {
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.loading = false
                    })
                }
            })
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelCallBack (){
            this.showEditPage = true
            this.showDetailPage = false
            this.searchEvent()
        },
        handleCopy (row){
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(row)
                }
            })
        },
        copyData (row){
            let headId = row.id
            this.confirmLoading = true
            postAction(this.url.copyData, {id: headId}).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    this.$refs.listPage.loadData()
                }else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    }
}
</script>