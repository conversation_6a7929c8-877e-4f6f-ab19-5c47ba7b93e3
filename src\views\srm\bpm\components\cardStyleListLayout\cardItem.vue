<template>
  <div class="card">
    <a-card
      :bordered="false">
      <template
        #title
        v-if="item[titleConfig[dataIndex]]">
        <a-tooltip :title="item[titleConfig[dataIndex]]">
          <div class="title">
            {{ item[titleConfig[dataIndex]] }}
          </div>
        </a-tooltip>
      </template>
      <div class="content">
        <div class="info">
          <ul>
            <li
              v-for="sub in localColumns"
              :key="sub.id">
              <div class="item">
                <span class="tit">{{ sub[title] }}</span>
                <span
                  :class="['txt', sub.extendLink ? 'pointer blue' : '' ]"
                  @click="() => handleExtendLink(sub, item)"
                >{{ item[sub[dataIndex]] }}</span>
              </div>
            </li>
          </ul>
        </div>
        <div class="extra">
          <div
            v-for="sub in extraColumns"
            :key="sub.id"
            class="panel">
            <span
              class="val"
              :class="getPriorityClass(sub)">{{ item[sub[dataIndex]] }}</span>
            <span class="title">{{ sub[title] }}</span>
          </div>
        </div>
      </div>
      <div class="footer">
        <a-button
          v-for="(btn, idx) in localButtons"
          :key="idx"
          v-bind="btn.attrs || {}"
          :disabled="btn.allow? btn.allow(item): false"
          @click="() => handleClick(btn, item)">
          {{ btn.title }}
        </a-button>
      </div>
    </a-card>
  </div>
</template>

<script>
const TITLEPROP = 'taskTitle'
// 抽出 "任务优先级" + "本节点处理人"
const EXTRA_PROPS = ['assigneeName', 'priority_dictText']

export default {
    name: 'CardItem',
    props: {
        item: {
            type: Object,
            default () {
                return {}
            }
        },
        title: {
            type: String,
            default: 'title'
        },
        titleProp: {
            type: String,
            default: TITLEPROP
        },
        dataIndex: {
            type: String,
            default: 'dataIndex'
        },
        columns: {
            type: Array,
            default () {
                return []
            }
        },
        buttons: {
            type: Array,
            default () {
                return []
            }
        }
    },
    computed: {
        localButtons () {
            return this.getAuthCodeBtns(this.buttons)
        },
        // 过滤在 listLayou 中塞入 “序号” “操作”  等列
        formatColumns () {
            return this.columns.filter(n => {
                // 隐藏字段不显示
                if (n.hidden === '1') {
                    return false
                }
                if (n.extendLink) {
                    return true
                }
                if (n.type && n.type === 'seq') {
                    return false
                }
                if (n.type && n.type === 'checkbox') {
                    return false
                }
                if (n.slots && !n.fieldColors) { // 因为保存过的字段就会生成fieldColors，listLayout就会生成一个slots
                    return false
                }
                return true
            })
        },
        titleConfig () {
            let prop = this.dataIndex || 'dataIndex'
            return this.columns.find(n => n[prop] === this.titleProp) || {}
        },
        extraColumns () {
            let prop = this.dataIndex || 'dataIndex'
            return this.formatColumns.filter(n => EXTRA_PROPS.includes(n[prop])) || []
        },
        localColumns () {
            let prop = this.dataIndex || 'dataIndex'
            return this.formatColumns.filter(n => {
                if (n[prop] === this.titleProp) {
                    return false
                }
                if (EXTRA_PROPS.includes(n[prop])) {
                    return false
                }
                return true
            })
        }
    },
    methods: {
        getPriorityClass (sub) {
            let prop = this.dataIndex || 'dataIndex'
            let Map = {
                '50': 'qing',
                '80': 'blue',
                '100': 'red'
            }
            if (sub[prop] === 'priority_dictText') {
                let priority = sub.priority || 50
                return Map[priority]
            } else {
                return ''
            }
        },
        // 获取非权限码按钮组
        getAuthCodeBtns (btns) {
            let authBtns = []
            if (btns && btns.length) {
                btns.forEach((item)=> {
                    // 配置authorityCode做权限控制
                    if (item && item.authorityCode) {
                        // 有权限
                        if (this.$hasOptAuth(item.authorityCode)) {
                            authBtns.push(item)
                        }
                    } else {
                        // 不配置authorityCode就不做权限控制
                        authBtns.push(item)
                    }
                })
            }
            return authBtns
        },
        handleClick (btn, row) {
            let event = btn.event || ''
            if (event) {
                let eventName = `card-${event}`
                this.$emit(eventName, { btn, row })
            }
        },
        handleExtendLink (col, column) {
            this.$emit('card-link', { col, column })
        }
    }
}
</script>

<style lang="less" scoped>
.ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.qing {
  color: #3BDBCF;
}
.blue {
  color: #3B87F7;
}
.red {
  color: #F93C00;
}

.card {
  margin-bottom: 8px;
  :deep(.ant-card) {
    border-radius: 8px;
    &:hover {
      box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.16);
    }
  }
  :deep(.ant-card-head) {
    min-height: 42px;
  }
  :deep(.ant-card-head-title) {
    padding: 10px 0;
    width: 0;
  }
  :deep(.ant-card-body) {
    padding: 12px 24px 12px;
  }

  .ant-btn {
    & + .ant-btn {
      margin-left: 8px;
    }
  }
}
.title {
  color: #3c557a;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.pointer {
  cursor: pointer;
}
.content {
  display: flex;
  overflow: hidden;
  .info {
    flex: 1;
    width: 0;
  }
}
.item {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 13px;
  line-height: 2.2;
  white-space: nowrap;
  .tit {
    color: #454f59;
    &::after {
      margin: 0 4px;
      content: ":";
    }
  }
  .txt {
    color: rgba(rgba(69, 79, 89, .4));
    .ellipsis;
    &.blue {
      color: #3B87F7;
    }
  }
}
.extra {
  display: flex;
  flex-direction: column;
  .panel {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4px 8px;
    background: rgb(249 249 249);
    position: relative;
    &:not(:last-child) {
      &:after {
        position: absolute;
        background-color: #cddcee;
        content: "";
        left: 50%;
        bottom: 0;
        width: 66%;
        height: 1px;
        transform: translateX(-50%);
      }
      &:first-child {
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
      }
      &:last-child {
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
      }
    }
    .val {
      font-size: 13px;
    }
    .title {
      font-size: 12px;
      color: rgba(69, 79, 89, .6);
    }
  }
}
</style>
