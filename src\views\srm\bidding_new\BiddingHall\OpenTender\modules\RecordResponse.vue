<template>
  <div v-if="ifshow">
    <titleCrtl>
      {{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_ddRt_27f9e68f`, '响应名单')) }}

      <template slot="right">
        <a-button
          v-if="this.$ls.get('SET_TENDERCURRENTROW').applyRole == 1"

          @click="addInviteItem"
          type="primary">{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_SuRdX_3498bfe8`, '添加供应商')) }}</a-button>
      </template>
    </titleCrtl>
    <list-table
      ref="tenderOpenBidRecordSupplierList"
      :statictableColumns="statictableColumns"
      :pageData="pageData"
      setGridHeight="500"
      :fromSourceData="formData.tenderOpenBidRecordSupplierList"
      :showTablePage="false"
    >
    </list-table>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>
<script lang='jsx'>
import listTable from '../../components/listTable'
import titleCrtl from '../../components/title-crtl'
import {getAction} from '@/api/manage'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
  
export default {
    mixins: [baseMixins],
    props: {
        // groupCode: {
        //     default: '',
        //     type: String
        // },
        formData: {
            default: () => {},
            type: Object
        }
        // pageStatus: {
        //     default: 'edit',
        //     type: String 
        // }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subPackageRow () {
            return this.currentSubPackage()
        },
        statictableColumns () {
            // let attachmentFileType = this.checkType == '0' ? 'preAttachmentFileType' : 'attachmentFileType'
            let columns = [
                { type: 'checkbox', width: 40, fixed: 'left' }, 
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                    fieldLabelI18nKey: '',
                    field: 'supplierName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KnDRL_37c77362`, '联合体名称'),
                    fieldLabelI18nKey: '',
                    field: 'combinationName',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                // 此处放插槽，放置文件
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KnDRL_37c77362`, '投标文件'),
                    fieldLabelI18nKey: '',
                    width: 180,
                    field: 'attachmentDTOList',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    slots: {
                        default: ({ row, column }) => {
                            console.log(row, column)
                            if(row.attachmentDTOList.length != 0){
                                return [
                                // <span >{row.purchaseAttachmentDemandDTOList[0]}</span>
                                // row['purchaseAttachmentDemandDTOList'].length> 0 ? <div ><span style='color: blue' onClick={() => this.preViewEvent(row)}>{row['purchaseAttachmentDemandDTOList'][0]['fileName']} </span><a-icon type="delete" onClick={() => row.purchaseAttachmentDemandDTOList = []}/> </div>: ''
                                    <div>
                                        <a-upload
                                            name={this.file}
                                            multiple={true}
                                            showUploadList={false}
                                            action={this.uploadUrl}
                                            headers={this.uploadHeader}
                                            accept={this.accept}
                                            beforeUpload={this.beforeUpload}

                                            data={ {headId: this.formData.id, businessType: 'biddingPlatform', 'sourceNumber': this.formData.tenderProjectNumber || this.formData.id || '', 'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开' }}
                                        
                                            onChange={() => this.handleUploadChange}
                                        >
                                            <a-button onClick={() => this.handleRow(row)}>  { this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件')) }</a-button>
                                        </a-upload>

                                        <div>
                                            <span style='color: blue'>{row.fileName}</span>
                                            <a style="margin-right: 8px"
                                                onClick={() => this.preViewEvent(row)}>{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览') }</a>
                                            <a onClick={() => this.downloadEvent(row)}>{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载') }</a>
                                        </div>
                                    </div>
                                ]
                            }else{
                                return [
                                    <div>
                                        <a-upload
                                            name={this.file}
                                            multiple={true}
                                            showUploadList={false}
                                            action={this.uploadUrl}
                                            headers={this.uploadHeader}
                                            accept={this.accept}
                                            beforeUpload={this.beforeUpload}

                                            data={ {headId: this.formData.id, businessType: 'biddingPlatform', 'sourceNumber': this.formData.tenderProjectNumber || this.formData.id || '', 'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开' }}
                                        
                                            onChange={() => this.handleUploadChange}
                                        >
                                            <a-button onClick={() => this.handleRow(row)}>  { this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件')) }</a-button>
                                        </a-upload>
                                    </div>
                                ]
                            }
                            
                        }
                    }
                },
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                    fieldLabelI18nKey: '',
                    field: 'quote',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contact`, ' 联系人'),
                    fieldLabelI18nKey: '',
                    field: 'contacts',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
                    fieldLabelI18nKey: '',
                    field: 'contactsPhone',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    fixed: 'right',
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    field: 'title',
                    width: 100,
                    slots: { default: 'grid_opration' }
                }
            ]
            return columns
        } 
          
    },
    components: {
        listTable,
        titleCrtl,
        fieldSelectModal
    },
    data () {
        return {
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            ifshow: false,
            tenderOpenBidRecordSupplierList: [],
            // externalToolBar: [
            //     {
            //         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
            //         key: 'upload',
            //         args: {
            //             property: 'label', // 可省略
            //             itemInfo: [], // 必传
            //             disabledItemNumber: true,
            //             action: '/attachment/purchaseAttachment/upload', // 必传
            //             businessType: 'biddingPlatform', // 必传,
            //             headId: '', // 必传
            //             modalVisible: false // 必传
            //         },
            //         attr: this.attrHandle,
            //         callBack: this.uploadCallBack
            //     }
                
            // ],
            pageData: {
                optColumnList: [
                    // {
                    //     key: 'download',
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                    //     clickFn: this.downloadEvent
                    // },
                    // {
                    //     key: 'preView',
                    //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                    //     clickFn: this.preViewEvent
                    // }
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        clickFn: this.handleDeleteFile
                    }
                ]
            }
        }
    },
    methods: {
        handleRow (row){
            this.row=row
        },
        // 添加供应商弹窗
        addInviteItem (){
            // let param = encodeURI('[{"logicSymbol":"in","fieldCode":"supplierStatus","fieldType":"dict","dictCode":"srmSupplierStatus","fieldValue":"1,2","joiner":"AND"}]')
            // let url = '/supplier/supplierMaster/list?superQueryParams='+param
            // let columns = [
            //     { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '供应商名称') },
            //     { field: 'legalPersonName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEhL_251c717e`, '企业法人') },
            //     { field: 'phoneNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AEKHCK_595d2496`, '企业联系方式') }
                
            // ]
            // this.$refs.fieldSelectModal.open(url, { headId: this.tenderCurrentRow.id}, columns, 'multiple')
        },
        fieldSelectOk (data) {
            // let itemGrid = this.getItemGridRef('supplierInviteReceiptList')
            // var subpackageList = {}
            // let param = []
            // var subpackage = {}
            // let {supplierAccount, supplierName, tenderProjectName, subpackageName} = this.subpackage
            // let subpackageMsg = {supplierAccount, supplierName, tenderProjectName, subpackageName}
            // subpackageMsg.tenderProjectId = this.subpackage.headId
            // subpackageMsg.subpackageId = this.subId
            // data.forEach(item => {
            //     subpackage['supplierName'] = item.supplierName
            //     subpackage['contacts'] = item.legalPersonName
            //     subpackage['contactsPhone'] = item.phoneNumber
            //     subpackage['supplierAccount'] = item.toElsAccount
            //     subpackageList={...subpackageMsg, ...subpackage}
            //     param.push({...subpackageList})

            // })
            // // itemGrid.insertAt([...subpackageList], -1)
            // postAction(this.url.add, param).then(res => {
            //     if (res.success) {
            //         this.$message.success(res.message)
            //         this.$refs.supplierInviteReceiptList.insertAt(data, -1)
            //         this.$emit('refresh')
            //     } else {
            //         this.$message.error(res.message)
            //     }
            // }).finally(() => {
            // })
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            console.log('123')
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if(this.formData.id){
                if (file.status === 'done') {
                    if (file.response.success) {
                        let {fileName, filePath, fileSize, id, headId} = file.response.result
                        let fileListData = {
                            uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                            uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName, filePath, fileSize, id, headId
                        }
                        // if (!this.formData.purchaseAttachmentList) this.$set(this.formData, 'purchaseAttachmentList', [])
                        this.formData.tenderOpenBidRecordSupplierList.forEach(item=>{
                            // if(item.)
                            console.log(item, this.row)

                        })
                        // attachmentDTOList.push(fileListData)
                        
                        // 仅对表格数据进行操作，再在父组件获取子组件table表格数据进行保存发布
                        // this.$refs.tenderOpenBidRecordSupplierList.insertAt(fileListData, -1)
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                }
            }
        },
        handleDeleteFile (deleteItem) {
            let list = this.formData.tenderOpenBidRecordSupplierList
            let targetIndex
            
            list.forEach((item, index)=>{
                if(item.id == deleteItem.id) targetIndex = index
            })
            this.formData.tenderOpenBidRecordSupplierList.splice(targetIndex, 1)
            // 仅对表格数据进行操作，再在父组件获取子组件table表格数据进行保存发布
            // this.$refs.tenderOpenBidRecordSupplierList.fromSourceData.splice(index, 1)
        },
        async downloadEvent (row) {
            let {message: url} = await getAttachmentUrl(row)
            this.$refs.tenderOpenBidRecordSupplierList.loading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.$refs.tenderOpenBidRecordSupplierList.loading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.subpackageId()
            this.$previewFile.open({params: row })
        }
        // init (data) {
        //     this.tenderOpenBidRecordSupplierList = data.tenderOpenBidRecordSupplierList || []
        //     // this.tenderOpenBidRecordSupplierList = this.responseList
        //     // this.ifshow = true
        //     console.log(data, this.tenderOpenBidRecordSupplierList)
        // }
    },
    // watch: {
    //     fromSourceData: {
    //         immediate: true,
    //         handler (val) {
    //             // this.externalToolBar[0].args.headId = val? val.id : ''
    //         }
    //     }
    // },
    mounted () {
        // this.tenderOpenBidRecordSupplierList = this.formData.tenderOpenBidRecordSupplierList
        this.ifshow = true
        // console.log(this.tenderOpenBidRecordSupplierList)
        // this.tenderOpenBidRecordSupplierList = this.fromSourceData.tenderOpenBidRecordSupplierList || []
        // if (['1', '2'].includes(this.fromSourceData.status) || this.pageStatus == 'detail') this.externalToolBar = []
    }
}
</script>
  <style lang="less" scoped>
:deep(.vxe-table--render-default .vxe-body--column.col--ellipsis>.vxe-cell){
    max-height: none;
}
:deep(.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis>.vxe-cell){
max-height: none;
}
  </style>