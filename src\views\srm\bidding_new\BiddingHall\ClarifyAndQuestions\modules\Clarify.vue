<template>
  <div class="clarify">
    <a-spin :spinning="confirmLoading">

      <titleTrtl class="margin-b-10">
        <span >{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_QILV_2f5d3190`, '文件澄清')) }}</span>
        <template slot="right">
          <a-button
            type="primary"
            v-if="applyRoleCanEdit"
            @click="handleAddClarify">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_hGLV_275b790a`, '发出澄清') }}</a-button>
        </template>
      </titleTrtl>

      <div>
        <listTable
          ref="listTable"
          :pageData="pageData"
          :url="url"
          :defaultParams="defaultParams"
          :statictableColumns="tableColumns"
          :showTablePage="true"
        />
      </div>
    </a-spin>
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import titleTrtl from '../../components/title-crtl'
import listTable from '../../components/listTable'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
export default {
    mixins: [baseMixins],
    components: {
        titleTrtl,
        listTable
    },
    inject: ['subpackageId', 'tenderCurrentRow'],
    computed: {
        subId (){
            return this.subpackageId()
        },
        applyRoleCanEdit () {
            return this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'
        }
    },
    data () {
        return {
            confirmLoading: false,
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LVBD_345b6432`, '澄清标题'),
                    'field': 'title'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hGKI_275a78c7`, '发出时间'),
                    'field': 'publishTime',
                    'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                    'field': 'status_dictText',
                    'width': 80
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLVcWIRLWkWW_88581feb`, '确认情况（已确认/总数）'),
                    'field': 'confirmNumber&total',
                    'width': 200,
                    slots: {
                        default: ({row}) => {
                            return [
                                `${row.confirmNumber}/${row.total}`
                            ]
                        }
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ],
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit},
                    {type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'), clickFn: this.handlePublish, allow: this.allowPublish},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            defaultParams: {},
            url: {
                list: '/tender/purchaseTenderClarificationInfo/list',
                publish: '/tender/purchaseTenderClarificationInfo/publish',
                delete: '/tender/purchaseTenderClarificationInfo/delete',
                detail: '/tender/purchaseTenderClarificationInfo/queryById'
            }
        }
    },
    methods: {
        handleAddClarify () {
            this.confirmLoading = true
            getAction('/tender/purchaseTenderClarificationInfo/queryById?id=&subpackageId='+this.subId).then(res=>{
                if(res.success){
                    this.$emit('handClarifyEditPage', res.result || {})
                }else{
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleEdit (row) {
            // this.confirmLoading = true
            // getAction('/tender/purchaseTenderClarificationInfo/queryById?id='+row.id+'&subpackageId='+this.subId).then(res=>{
            //     if(res.success){
            //         this.$emit('handClarifyEditPage', res.result || {})
            //     }else{
            //         this.$message.error(res.message)
            //     }
            // }).finally(() => {
            //     this.confirmLoading = false
            // })
            this.$emit('handClarifyEditPage', row)

        },
        handleView (row) {
            this.$emit('handleClarifyViewPage', row)
        },
        handlePublish (row) {
            var that=this
            this.confirmLoading = true
            postAction(that.url.publish+'?id='+row.id).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$refs.listTable.loadData()
                    // this.$emit('hide')
                }else{
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })

        },
        handleDelete (row){
            this.confirmLoading = true
            getAction(this.url.delete, {id: row.id}).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$refs.listTable.loadData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // status：保存0，待发出1，已发送2
        allowEdit ({status}){
            // 单据状态为“新建0”、或“未发布1” ，则允许编辑
            if ((status==='0')) {
                if (!this.applyRoleCanEdit) return true
                return false
            } else {
                return true
            }
        },
        allowPublish ({status}){
            // 单据状态为“新建0”、或“未发布1” ，则允许发布
            if ((status==='1')) {
                if (!this.applyRoleCanEdit) return true
                return false
            } else {
                return true
            }
        },
        allowDelete ({status}){
            // 单据状态为“新建0”、或“未发布1” ，则允许删除
            if ((status==='0' || status==='1')) {
                if (!this.applyRoleCanEdit) return true
                return false
            } else {
                return true
            }
        }
    },
    created () {
        this.defaultParams= { subpackageId: this.subId }
        console.log('defaultParams', this.defaultParams)
    }
}
</script>
<style lang="less" scoped>
.clarify{
  margin-top: 20px;
}
.margin-b-10{
  margin-bottom: 5px;
}
</style>


