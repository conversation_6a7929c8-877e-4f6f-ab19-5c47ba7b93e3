<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      refresh
      :url="url" 
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <purchaseDeductCost-edit
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <!-- 详情页面 -->
    <purchaseDeductCost-detail
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import PurchaseDeductCostEdit from './modules/PurchaseDeductCostEdit'
import PurchaseDeductCostDetail from './modules/PurchaseDeductCostDetail'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, postAction} from '@/api/manage'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseDeductCostEdit,
        PurchaseDeductCostDetail
    },
    data () {
        return {
            pageData: {
                businessType: 'deductCost',
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),  icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'deductCost#purchaseDeductCost:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'deductCost#purchaseDeductCost:view'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopyData, authorityCode: 'deductCost#purchaseDeductCost:copy'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showEditCondition, authorityCode: 'deductCost#purchaseDeductCost:edit'},
                    {type: 'invalid', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.handleInvalid, allow: this.showInvalidCondition, authorityCode: 'deductCost#purchaseDeductCost:invalid'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.showDeleteCondition, authorityCode: 'deductCost#purchaseDeductCost:delete'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 200,
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNVVty_b57e1fa6`, '请输入扣款单号')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/finance/purchaseDeductCost/list',
                add: '/finance/purchaseDeductCost/add',
                copyData: '/finance/purchaseDeductCost/copyData',
                invalid: '/finance/purchaseDeductCost/invalid',
                delete: '/finance/purchaseDeductCost/delete',
                deleteBatch: '/finance/purchaseDeductCost/deleteBatch',
                exportXlsUrl: 'finance/purchaseDeductCost/exportXls',
                importExcelUrl: 'finance/purchaseDeductCost/importExcel',
                columns: 'purchaseDeductCostList'
            },
            tabsList: []
        }
    },
    mounted () {
        this.serachCountTabs('/finance/purchaseDeductCost/counts')
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.deductNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'PurchaseDeductCost', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if((row.confirmStatus == '0')) {
                return true
            }else {
                return false
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseExpenseManag `, '采购扣款费用管理'))
        },
        showEditCondition (row) {
            if(this.btnInvalidAuth('deductCost#purchaseDeductCost:edit')){
                return true
            }
            let confirmStatus = row.confirmStatus
            let auditStatus = row.auditStatus
            //审批中不能编辑
            if ('1' == auditStatus) {
                return true
            }
            if ('0' == confirmStatus && '0' == auditStatus) {
                return false
            } else if ('3' == auditStatus) {
                return false
            } else if ('3' == confirmStatus) {
                return false
            }  else {
                // 不可操作
                return true
            }
        },
        handleCopyData (row) {
            let that = this
            getAction(this.url.copyData, { id: row.id })
                .then(res => {
                    if (res.success) {
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BRLR_29bbbf18`, '复制成功'))
                        that.$refs.listPage.handleQuery()
                    } else {
                        this.$message.error(res.message)
                    }
                })
        },
        showDeleteCondition (row) {
            if(this.btnInvalidAuth('deductCost#purchaseDeductCost:delete')){
                return true
            }
            let confirmStatus = row.confirmStatus
            let auditStatus = row.auditStatus
            if (('0' == confirmStatus) && '0' == auditStatus) {
                return false
            } else if ('0' == confirmStatus && '3' == auditStatus) {
                return false
            } else {
                // 不可操作
                return true
            }
        },
        handleInvalid (row) {
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoid`, '确认作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voidSelectedData`, '是否作废选中数据?'),
                onOk: function () {
                    that.loading = true
                    postAction(that.url.invalid, row).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.searchEvent()
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        showInvalidCondition (row) {
            if(this.btnInvalidAuth('deductCost#purchaseDeductCost:invalid')){
                return true
            }
            if ('1' == row.deductStatus) {
                // 不可操作
                return true
            } else {
                return false
            }
        },
        cancelCallBack () {
            this.showEditPage = true
            this.showDetailPage = false
            this.searchEvent()
        }
    }
}
</script>