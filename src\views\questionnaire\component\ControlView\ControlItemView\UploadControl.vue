<template>
  <a-form-item
    :label-col="{ span: attr.layout }"
    :wrapper-col="{ span: attr.layout === 24 ? 24 : 24 - attr.layout }"
    :required="attr.rules.length > 0"
  >
    <span slot="label" >
      <!-- {{ formData }} -->
      {{ attr.label }} 
      <a-button
        v-if="showDownloadButton"
        @click="downloadEvent(attr)">
        {{ $srmI18n(`${$getLangAccount()}#i18n_field_IKBI_25a71784`, '下载附件') }}
      </a-button>
    </span>
    <a-upload-dragger
      :value="attr.initialValue"
      :name="attr.name"
      :multiple="true"
      :action="attr.action"
      :accept="attr.accept"
      :before-upload="beforeUpload"
      @change="handleUploadChange" 
    >
      <!-- @change="handleChange"
      @drop="handleDrop" -->
      <p class="ant-upload-drag-icon">
        <a-icon type="inbox" />
      </p>
      <p class="ant-upload-text">
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_clickOrDragToUploadAttachment`, '单击或拖动文件到此区域上传') }}
      </p>
      <p class="ant-upload-hint">
        {{ $srmI18n(`${$getLangAccount()}#i18n_field_Rutm_2f7d019e`, '支持单次') }}
      </p>
    </a-upload-dragger>
    <div class="custom-upload-max-limit">{{ $srmI18n(`${$getLangAccount()}#i18n_dict_dWiTXVjBIfXefL_58ea22b4`, '注:允许上传的附件大小最大为') }}100M</div>
  </a-form-item> 
</template>

<script>
import { mapState } from 'vuex'
import {getAction} from '@/api/manage'
export default {
    name: 'InputControl',
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    computed: {
        showDownloadButton () {
            let rs = false
            if (this.attr?.sampleAttach?.length) {
                if (this.attr.sampleAttach[0]?.response?.success){
                    rs = true
                }
            }
            return rs
        },
        attr () {
            return this.data.attr
        },
        ...mapState({
            formData: state => state.formDesigner.formData
        })
    },
    methods: {
        handleUploadChange ({ file, fileList, event }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    this.$message.success(file.response.message || `${file.name} 文件上传成功`)
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        },
        beforeUpload (file) {
            let Size=100
            if (this.upload&&this.upload.length >= 1) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RiTXVImBI_8522573`, '只允许上传一个附件'))
                return window.Promise.reject(false)
            }
            //查看时，不予许上传附件
            if(this.isPre == 1 || this.isPre == 2){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xiTXVBI_52cd156c`, '不允许上传附件'))
                return window.Promise.reject(false)
            }
            if (Size) {
                const limitSize = file.size / 1024 / 1024 > Size
                if (limitSize) {
                    const tips = `${this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_iTXVjBIfXefL_d2cdd62`, '允许上传的附件大小最大为')}${Size}M`
                    this.$message.error(tips)
                    return window.Promise.reject(false)
                }
            }
        },
        downloadEvent (attr) {
            const params = {id: attr.sampleAttach[0]?.result.id}
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }
    }
}
</script>

<style lang="less" scoped>

</style>
