<template>
  <div class="custom-upload-form">
    <a-spin
      :spinning="spinning"
      :delay="delayTime">
      <a-modal
        v-drag
        :visible="visible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_BIXV_4580bf08`, '附件上传')"
        :keyboard="false"
        :maskClosable="false"
        :confirm-loading="spinning"
        @cancel="handleCancel"
        @ok="handleConfirm"
      >
        <a-form
          class="collectionForm"
          ref="collectionForm"
          :form="form"
          v-bind="formItemLayout"
        >
          <template v-if="isGridUpload">
            <slot></slot>
          </template>
          <template v-else-if="isList">
            <!-- 物料编码 -->
            <a-form-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialNumber`, '物料编码')"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }">
              <!-- <a-input v-decorator="['materialNumber']" /> -->

              <a-auto-complete
                v-if="visible"
                class="global-search"
                style="width: 100%"
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterMaterialCode`, '请输入或选择物料编码')"
                optionLabelProp="title"
                @select="onSelect"
                @search="handleSearch"
              >
                <template slot="dataSource">
                  <a-select-option
                    v-for="item in dataSource"
                    :key="item.materialNumber"
                    :title="item.materialNumber">
                    <a href="javascript: void(0)">
                      {{ item.materialNumber }}
                    </a>
                    <span className="global-search-item-count">-{{ item.materialName }}</span>
                  </a-select-option>
                </template>
                <a-input v-decorator="['materialNumber']">
                  <!-- <a-icon
                    slot="suffix"
                    type="file-search"
                    class="certain-category-icon" /> -->
                </a-input>
              </a-auto-complete>
            </a-form-item>

            <!-- 物料名称 -->
            <a-form-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_materialName`, '物料名称')"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }">
              <a-input v-decorator="['materialName']" />
            </a-form-item>

            <a-form-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_versionN`, '版本号')"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }">
              <a-input v-decorator="['attachmentVersion']" />
            </a-form-item>

            <a-form-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_field_factory`, '工厂')"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }">
              <a-select v-decorator="[ 'factory' ]">
                <a-select-option
                  v-for="el in factoryOptions"
                  :key="el.value"
                  :value="el.value">{{ el.title }}</a-select-option>
              </a-select>
            </a-form-item>

            <!-- 生效日期 -->
            <a-form-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_effectiveDate`, '生效日期')"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }">
              <a-date-picker
                style="width:100%"
                valueFormat="YYYY-MM-DD"
                v-decorator="['effectiveDate']"
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_effectiveDate`, '生效日期')"
              />
            </a-form-item>

            <!-- 失效日期 -->
            <a-form-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_field_unableDate`, '失效日期')"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }">
              <a-date-picker
                style="width:100%"
                valueFormat="YYYY-MM-DD"
                v-decorator="['expiryDate']"
                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_unableDate`, '失效日期')"
              />
            </a-form-item>
          </template>
          <template v-else>
            <a-form-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_title_fileBelong`, '文件所属')"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }">
              <a-radio-group
                v-decorator="['fileBelong', { initialValue: this.attrCheck }]"
                @change="handleChange">
                <a-radio
                  value="headId"
                  :disabled="disabledHead">{{ $srmI18n(`${$getLangAccount()}#i18n_title_head`, '头') }}</a-radio>
                <a-tooltip
                  v-if="!itemInfo.length"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_canChooseAfterAddItemContent`, '添加行信息表格内容后可选择')">
                  <a-radio
                    value="itemNumber"
                    disabled>{{ $srmI18n(`${$getLangAccount()}#i18n_title_row`, '行') }}</a-radio>
                </a-tooltip>
                <a-radio
                  v-else
                  :disabled="disabledItemNumber"
                  value="itemNumber">{{ $srmI18n(`${$getLangAccount()}#i18n_title_row`, '行') }}</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item
              v-show="form.getFieldValue('fileBelong') === 'itemNumber'"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }"
              :label="itemNumberLabel">

              <a-select v-decorator="[ 'itemNumber', { initialValue: itemNumberDefaultValue, rules: [{ required: check, message: `${$srmI18n(`${$getLangAccount()}#i18n_title_chooseRowItem`, '请选择行项目')}` }] } ]">
                <template v-for="(el, i) in itemInfo">
      
                  <a-select-option
                    :key="i"
                    :value="itemNumbeValueProp ? el[itemNumbeValueProp] : `${i + 1}`">{{ property === 'seq' ? i + 1 : `${ itemNumberSelectIndex ? el['itemNumber'] : i+1 }-${el[property]?el[property]:''}` }}</a-select-option>
                </template>
              </a-select>
            </a-form-item>
            <a-form-item
              :label="$srmI18n(`${$getLangAccount()}#i18n_field_fileType`, '文件类型')"
              v-if="srmFileType.length"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 14 }">
              <a-select v-decorator="[ 'fileType', { rules: [{ required: requiredFileType, message: `${$srmI18n(`${$getLangAccount()}#i18n_title_chooseFileType`, '请选择文件类型')}` }] } ]">
                <a-select-option
                  v-for="el in srmFileType"
                  :key="el.value"
                  :value="el.value">{{ el.title }}</a-select-option>
              </a-select>
            </a-form-item>
          </template>

          <a-form-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_title_attachment`, '附件')"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 14 }">
            <div class="dropbox">
              <a-upload-dragger
                v-decorator="[
                  'fileList',
                  {
                    valuePropName: 'fileList',
                    getValueFromEvent: normFile,
                    rules: [{ required: true, message: `${$srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '请上传附件')}` }]
                  },
                ]"
                v-bind="attrs.uploadAttrs"
                :before-upload="beforeUpload"
                @change="handleUploadChange">
                <p class="ant-upload-drag-icon">
                  <a-icon type="inbox" />
                </p>
                <p class="ant-upload-text">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_clickOrDragToUploadAttachment`, '单击或拖动文件到此区域上传') }}
                </p>
                <p class="ant-upload-hint">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_oneOrMoreUpload`, '支持单次或批量上传') }}
                </p>
              </a-upload-dragger>
              <div class="custom-upload-max-limit">{{ $srmI18n(`${$getLangAccount()}#i18n_dict_dWiTXVjBIfXefL_58ea22b4`, '注:允许上传的附件大小最大为') }}{{ limitSize }}M</div>
              <div class="custom-upload-max-limit custom-upload-accept">{{ $srmI18n(`${$getLangAccount()}#i18n_dict_RuXVBIVXR_5bfcfb87`, '支持上传附件扩展名：') }} {{ $attrs.accept }}</div>
            </div>
          </a-form-item>
        </a-form>
      </a-modal>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import { USER_INFO } from '@/store/mutation-types'
import { getAction, postAction } from '@api/manage'
import {srmI18n, getLangAccount, handlePromise} from '@/utils/util.js'
import REGEXP from '@/utils/regexp'
import { debounce } from 'lodash'
import { ossWebMixin } from './ossWebMixin.js'

let i18nItemNumberLabel = srmI18n(`${getLangAccount()}#i18n_title_lineItem`, '行项目')

const POLICYSIGNURL = '/attachment/store/policySign'

export default {
    inheritAttrs: false,
    inject: {
        tplRootRef: {
            default: () => {}
        }
    },
    mixins: [ ossWebMixin ],
    props: {
        isList: {
            type: Boolean,
            default: false
        },
        isGridUpload: {
            type: Boolean,
            default: false
        },
        // visible: {
        //     type: Boolean,
        //     default: false
        // },
        single: {
            type: Boolean,
            default: false
        },
        requiredFileType: {
            type: Boolean,
            default: false
        },
        disabledItemNumber: {
            type: Boolean,
            default: false
        },
        disabledHead: {
            type: Boolean,
            default: false
        },
        srmFileType: {
            type: Array,
            default: () => []
        },
        factoryOptions: {
            type: Array,
            default: () => []
        },
        property: {
            type: String,
            default: 'materialName'
        },
        // 行 select 序号是否显示itemNumber
        itemNumberSelectIndex: {
            type: Boolean,
            default: false
        },
        // 行项目 key
        itemNumberKey: {
            type: String,
            default: 'itemNumber'
        },
        itemNumbeValueProp: {
            type: String,
            default: ''
        },
        // 行项目 label
        itemNumberLabel: {
            type: String,
            default: i18nItemNumberLabel
        },
        // 设置iteminfo 下拉默认值
        itemNumberDefaultValue: {
            type: String,
            default: ''
        },
        attrCheck: {
            type: String,
            default: 'headId'
        },
        limitSize: {
            type: Number,
            default: 100
        },
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            cacheAllData: {},
            dataSource: [],
            spinning: false,
            delayTime: 300,
            userInfo: {},
            visible: false,
            check: false,
            formItemLayout: {
                labelCol: { span: 6 },
                wrapperCol: { span: 14 }
            },
            policySignData: {},
            // uploadUrl: '/attachment/purchaseAttachment/upload'
            uploadLogUrl: '/attachment/purchaseAttachment/uploadLog',
            saleUploadLogUrl: '/attachment/saleAttachment/uploadLog'
        }
    },
    computed: {
        // 父级传参
        attrs () {
            const { itemInfo = [], ...others } = this.$attrs || {}
            return {
                itemInfo,
                uploadAttrs: { ...others }
            }
        },
        // 获取物料行下拉选项
        itemInfo () {
            let arr = []
            if (this.$attrs.itemInfo && this.$attrs.itemInfo.length) {
                arr = this.attrs.itemInfo
            }
            if (this.$attrs.refName) {
                let refName = this.$attrs.refName
                arr = this.cacheAllData[refName] || []
            }
            return arr
        },
        isOss () {
            // if (!this.policySignData.host) {
            //     return false
            // }
            // let reg = /aliyuncs/g
            //return reg.test(this.policySignData.host)
            return false
        },
        isLocal () {
            return !this.isOss
        }
    },
    beforeCreate () {
        this.form = this.$form.createForm(this, { name: 'customUploadForm' })
    },
    created () {
        this.getUserInfo()
        // this.getPolicySignData()
    },
    mounted () {
        this.$nextTick(() => {
            this.form.setFieldsValue({
                fileBelong: this.attrCheck
            })
            this.handleChange(this.attrCheck)
        })
    },
    methods: {
        getAllData () {
            this.cacheAllData = (this.tplRootRef && this.tplRootRef.getAllData) ? this.tplRootRef.getAllData() : {}
            //兼容旧模板
            // if (this?.tplRootRef?.refreshTimeStamp) this.tplRootRef.refreshTimeStamp = new Date()
        },
        getPolicySignData () {
            // let token = this.$store.getters.token || ''
            getAction(POLICYSIGNURL).then(res => {
                if (!res.success) {
                    return
                }
                this.policySignData = res.result || {}
            })
        },
        open () {
            this.visible = true
            this.getAllData()
        },
        onSelect (value) {
            const { materialName } = this.dataSource.find(n => n.materialNumber === value) || {}
            this.form.setFieldsValue({
                materialNumber: value,
                materialName: materialName
            })
        },

        handleSearch: debounce(function (value) {
            this.searchResult(value)
        }, 200),
        searchResult (query) {
            const url = '/material/purchaseMaterialHead/list'
            const params = {
                pageNo: 1,
                pageSize: 20
            }
            if (REGEXP.enOrNum.test(query)) {
                params.materialNumber = query
            } else {
                params.materialName = query
            }
            return getAction(url, params).then(res => {
                const records = res.result.records || []
                this.dataSource = records
            })
        },
        getUserInfo () {
            this.userInfo = this.$ls.get(USER_INFO)
        },
        beforeUpload (file) {
            let fileList = this.form.getFieldValue('fileList') || []
            if (this.single && fileList.length >= 1) {
                this.$message.error('只允许上传一个附件')
                return window.Promise.reject(false)
            }
            
            if (this.limitSize) {
                const limitSize = file.size / 1024 / 1024 > this.limitSize
                if (limitSize) {
                    const tips = `${this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_iTXVjBIfXefL_d2cdd62`, '允许上传的附件大小最大为')}${this.limitSize}M`
                    this.$message.error(tips)
                    return window.Promise.reject(false)
                }
            }
            if ( this.$attrs.accept) { // 前端校验 检查拓展名是否符合
                const fileType = file.name.replace(/.+\./, '')
                let patt=new RegExp(fileType, 'i') 
                const isAccept =  patt.test(this.$attrs.accept)
                if (!isAccept) {
                    const tips = `${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVKmWXVQImKPxRu_715fcbd7`, '上传失败，上传文件格式暂不支持')}`
                    this.$message.error(tips)
                    return window.Promise.reject(false)
                }
            }
            fileList = [ ...fileList, file ]
            this.form.setFieldsValue({
                fileList
            })
            return window.Promise.reject(false)
        },
        handleRemove (file) {
            let fileList = this.form.getFieldValue('fileList')
            const index = fileList.indexOf(file)
            const newFileList = fileList.slice()
            newFileList.splice(index, 1)
            this.form.setFieldsValue({
                fileList: newFileList
            })
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    if (file.response.code === 201) {
                        let { message, result: { msg, fileUrl, fileName } } = file.response
                        let href = this.$variateConfig['domainURL'] + fileUrl
                        this.$warning({
                            title: message,
                            content: (
                                <div>
                                    <span>{msg}</span><br/>
                                    <span>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_detailContent`, '具体详情请')} <a href={href} target="_blank" download={fileName}>点击下载</a> </span>
                                </div>
                            )
                        })
                    } else {
                        this.$message.success(file.response.message || `${file.name} 文件上传成功`)
                    }
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        },
        handleChange (e) {
            this.check = e.target ? e.target.value === 'itemNumber' : e === 'itemNumber'
            this.$nextTick(() => {
                this.form.validateFields(['itemNumber'], { force: true })
            })
        },
        resetData () {
            this.form.resetFields()
            Object.assign(this.$data, this.$options.data.call(this))
        },
        handleCancel () {
            this.resetData()
            this.$emit('custom_upload_cancel')
        },
        handleConfirm () {

            this.form.validateFields((err, values) => {
                if (err) {
                    return
                }

                let { effectiveDate = null, expiryDate = null } = values || {}


                // 物料文档列表页附件判断
                if (this.isList) {
                    let now = new Date().setHours(8, 0, 0, 0)// 统一设置为早上 8 点
                    if (effectiveDate) {
                        let effectiveDate_2 = new Date(effectiveDate).setHours(8, 0, 0, 0)
                        if (now > effectiveDate_2) {
                            this.$message.error('生效日期须大于等于当天日期')
                            return
                        }
                    }
                    if (expiryDate) {
                        let expiryDate_2 = new Date(expiryDate).setHours(8, 0, 0, 0)
                        if (now > expiryDate_2) {
                            this.$message.error('失效日期须大于等于当天日期')
                            return
                        }
                        if (effectiveDate) {
                            let effectiveDate_2 = new Date(effectiveDate).setHours(8, 0, 0, 0)
                            if (effectiveDate_2 >= expiryDate_2) {
                                this.$message.error('失效日期须大于生效日期')
                                return
                            }
                        }
                    }
                }
                if(!values.attachmentVersion) {
                    values.attachmentVersion = ''
                }
                if(!values.effectiveDate) {
                    values.effectiveDate = ''
                }
                if(!values.expiryDate) {
                    values.expiryDate = ''
                }
                if(!values.materialNumber) {
                    values.materialNumber = ''
                }
                if(!values.materialName) {
                    values.materialName = ''
                }
                if(!values.factory) {
                    values.factory = ''
                }
                // 本地上传
                if (this.isLocal) {
                    this.uploadToLocalServer(values)
                }
                // 阿里云 OSS 文件上传
                if (this.isOss) {
                    this.uploadToOssServer(values)
                }
            })
        },
        uploadToLocalServer (values) {
            // 获取上传方信息
            const { elsAccount, subAccount, realname } = this.userInfo
            const uploadElsAccount = [ elsAccount, subAccount, realname ].join('_')

            const { attachmentVersion = null, materialNumber = null, materialName = null, factory = null, fileBelong = null, itemNumber = null, fileType = '', fileList = [], effectiveDate = null, expiryDate = null } = values || {}

            const promises = fileList.map(file => {
                const formData = new FormData()
                let header = { ...this.$attrs.data, uploadElsAccount }
                if (this.isList) {
                    header = Object.assign({}, header, {
                        attachmentVersion,
                        effectiveDate,
                        expiryDate,
                        materialNumber,
                        materialName,
                        factory
                    })
                } else {
                    header = Object.assign({}, header, {
                        fileType
                    })
                    if (fileBelong === 'itemNumber') {
                        header[this.itemNumberKey] = itemNumber
                    }
                }
                // 获取 headId 与 businessType
                Object.keys(header).forEach(key => {
                    formData.append(key, header[key])
                })
                formData.append('file', file)
                return postAction(this.$attrs.action, formData, {
                    headers: {
                        'bus-account': this.currentEditRow.busAccount || ''
                    }
                })
            })
            this.spinning = true
            Promise.all(handlePromise(promises))
                .then(res => {
                    // 附件上传接口成功code为200
                    const result = res
                        .filter(n => n.status === 'success' && n.res.code === 200)
                        .map(n => n.res.result)
                    const hasErrFile = res.find(n => n.res.code !== 200)
                    if (hasErrFile) { // 上传失败的文件
                        this.$message.error(hasErrFile.res.message)
                    }
                    // 只返回上传成功的值
                    this.$emit('custom_upload_create', result)
                    this.visible = false
                    this.check = false
                })
                .finally((err) => {
                    console.log('err', err)
                    this.spinning = false
                })
        },
        uploadToOssServer (values) {
            // 获取上传方信息
            const { elsAccount, subAccount, realname } = this.userInfo
            const uploadElsAccount = [ elsAccount, subAccount, realname ].join('_')

            const { attachmentVersion = null, materialNumber = null, materialName = null, fileBelong = null, itemNumber = null, fileType = '', fileList = [], effectiveDate = null, expiryDate = null } = values || {}

            this.getOssPolicySign()

            const promises = fileList.map(file => {
                const formData = new FormData()


                let fileName = file.name || ''
                if (fileName) {
                    this.suffix = this.get_suffix(fileName)
                    this.calculate_object_name(fileName)
                }

                let new_multipart_params = {
                    'key': this.g_object_name,
                    'policy': this.policyBase64,
                    'OSSAccessKeyId': this.accessid, 
                    'success_action_status': '200', // 让服务端返回200,不然，默认会返回204
                    'callback': this.callbackbody,
                    'signature': this.signature
                }

                let cacheObj = { ...this.$attrs.data, uploadElsAccount }
                if (this.isList) {
                    cacheObj = Object.assign({}, cacheObj, {
                        attachmentVersion,
                        effectiveDate,
                        expiryDate,
                        materialNumber,
                        materialName
                    })
                } else {
                    cacheObj = Object.assign({}, cacheObj, {
                        fileType
                    })
                    if (fileBelong === 'itemNumber') {
                        cacheObj[this.itemNumberKey] = itemNumber
                    }
                }

                // 带上根目录
                let filePath = this.g_object_name[0] !== '/' ? `/${this.g_object_name}` : this.g_object_name

                // 缓存上传数据，供回调使用
                this.uploadData.push(Object.assign({}, {
                    ...cacheObj,
                    // fileName: new_multipart_params.key,
                    fileName: fileName,
                    filePath: filePath,
                    fileSize: file.size
                }))

                Object.keys(new_multipart_params).forEach(key => {
                    formData.append(key, new_multipart_params[key])
                })
                
                formData.append('file', file)
                return postAction(this.host, formData, {
                    headers: {
                        // 'Access-Control-Allow-Origin': '*',
                        // 'Access-Control-Allow-Methods': 'GET, POST',
                        // 默认写死: 公有读取权限, 2022.05.12
                        'x-oss-object-acl': 'public-read'
                    }
                })
            })
            this.spinning = true
            Promise.all(handlePromise(promises))
                .then(() => {
                    this.handleAfterOssUpload()
                })
                .finally((err) => {
                    console.log('err', err)
                    this.spinning = false
                })
        },
        handleAfterOssUpload () {
            
            let action = this.$attrs.action
            let reg = /saleAttachment/
            let uploadLogUrl = reg.test(action) ? this.saleUploadLogUrl : this.uploadLogUrl
            
            postAction(uploadLogUrl, this.uploadData, {
                headers: {
                    'bus-account': this.currentEditRow.busAccount || ''
                }
            })
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    let result = res.result || []
                    this.$emit('custom_upload_create', result)
                    this.visible = false
                    this.check = false
                })
                .finally(() => {
                    this.uploadData = []
                })
        },
        normFile (e) {
            console.log('Upload event:', e)
            if (Array.isArray(e)) {
                return e
            }
            return e && e.fileList
        }
    }
}
</script>

<style lang="less" scoped>
.custom-upload-max-limit{
  color: rgba(0, 0, 0, 0.45);
  font-size: 13px;
}
.custom-upload-accept{
    line-height: 125%;
    word-break:break-all;
}
.custom-upload-form {
  .collectionForm {
    .dropbox {
      height: 180px;
      line-height: 1.5;
    }
  }
}
.global-search {
  width: 100%;
}

.global-search.ant-select-auto-complete .ant-select-selection--single {
  margin-right: -46px;
}

.global-search.ant-select-auto-complete .ant-input-affix-wrapper .ant-input:not(:last-child) {
  padding-right: 62px;
}

.global-search.ant-select-auto-complete .ant-input-affix-wrapper .ant-input-suffix button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.global-search-item {
  display: flex;
}

.global-search-item-desc {
  flex: auto;
  text-overflow: ellipsis;
  overflow: hidden;
}

.global-search-item-count {
  flex: none;
}
</style>