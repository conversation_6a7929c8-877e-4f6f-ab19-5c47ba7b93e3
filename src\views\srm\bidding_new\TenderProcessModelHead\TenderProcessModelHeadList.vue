<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      :tabsList="tabsList"
      v-show="!showEditPage && !showDetailPage "
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <Tender-Process-Model-Head-edit
      v-if="showEditPage"
      ref="editlPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
    <Tender-Process-Model-Head-Detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import TenderProcessModelHeadEdit from './modules/TenderProcessModelHeadEdit'
import TenderProcessModelHeadDetail from './modules/TenderProcessModelHeadDetail'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
import { postAction, getAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        TenderProcessModelHeadEdit,
        TenderProcessModelHeadDetail
    },
    data () {
        return {
            tabsList: [],
            showEditPage: false,
            pageData: {
                businessType: 'tenderProcessModel',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n__VWNQLRL_baa68bd6`, '请输入流程名称')
                    }

                ],
                form: {
                    supplierName: '',
                    orderDesc: '',
                    orderNumber: '',
                    orderStatus: ''
                },
                button: [
                    {
                        allow: ()=> {
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'tender#tenderProcessModelHead:add',
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'tender#tenderProject:queryById', clickFn: this.handleView },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), authorityCode: 'tender#tenderProcessModelHead:edit', clickFn: this.handleEdit, allow: this.allowEdit },
                    { type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bX_e9409`, '生效'), authorityCode: 'tender#tenderProcessModelHead:publish', clickFn: this.handlePublish, allow: this.allowPublish },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), authorityCode: 'tender#tenderProcessModelHead:delete', clickFn: this.handleDelete}
                    // { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 270
            },
            url: {
                list: '/tender/tenderProcessModelHead/list',
                delete: '/tender/tenderProcessModelHead/delete',
                publish: '/tender/tenderProcessModelHead/publish',
                columns: 'tenderProcessModelHead'
            }
        }
    },
    methods: {
        allowEdit (row){
            return false
            // return (row.status == 1)
        },
        handleEdit (row) {
            this.currentEditRow = row.templateNumber ? row : {
                templateNumber: 'TC2022033001',
                templateName: 'purchase_tenderProcessModel',
                templateVersion: '1',
                templateAccount: '100000',
                elsAccount: this.$ls.get(USER_ELS_ACCOUNT)  
            }
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleView (row) {
            this.currentEditRow = row.templateNumber ? row : {
                templateNumber: 'TC2022033001',
                templateName: 'purchase_tenderProcessModel',
                templateVersion: '1',
                templateAccount: '100000',
                elsAccount: this.$ls.get(USER_ELS_ACCOUNT)  
            }
            this.showDetailPage = true
        },
        handlePublish (row) {
            getAction(this.url.publish, {id: row.id}).then(res=>{
                if(res.success){
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_IbX_16f3d7b`, '已生效'))
                    this.$refs.listPage.loadData()
                }else{
                    this.$message.error(res.message)
                }
            })
        },
        allowPublish (row) {
            return (row.status == 1)
        }
    },
    mounted () {
        // this.serachTabs('srmReconciliationStatus', 'reconciliationStatus')
        this.serachCountTabs('/tender/tenderProcessModelHead/counts')
    }
}
</script>