<template>
  <div style="height:100%">
    <list-layout
      v-show="!showAddPage && !showEditPage && !showDetailPage && !showHisPage"
      ref="listPage"
      :pageData="pageData"
      :url="url" />
    <!-- 新增页面 -->
    <BpExchangeRateAdd
      v-if="showAddPage"
      :current-edit-row="currentEditRow"
      @hide="handListPage" />
    <!-- 编辑页面 -->
    <BpExchangeRateEdit
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="handListPage" />
    <!-- 查看页面 -->
    <BpExchangeRateDetail
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="handListPage" />

    <a-modal
      v-drag    
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_historicalVersion`, '历史版本')"
      :visible="visible1"
      :width="700"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div>
        <vxe-table
          align="center"
          border
          resizable
          autoResize
          showOverflow
          showHeaderOverflow
          headerAlign="center"
          row-id="id"
          height="300"
          ref="listGrid"
          size="mini"
          :data="hisTableData" >
          <vxe-table-column
            field="exchangeSource_dictText"
            :title="$srmI18n(`${$getLangAccount()}#i18n_field_MIwj_32f6fbeb`, '汇率来源')"></vxe-table-column>
          <vxe-table-column
            field="originalCurrency_dictText"
            :title="$srmI18n(`${$getLangAccount()}#i18n_field_trSl_29425bc6`, '基准货币')"></vxe-table-column>
          <vxe-table-column
            field="standardUnit_dictText"
            :title="$srmI18n(`${$getLangAccount()}#i18n_field_trtL_293b4ca4`, '基准单位')"></vxe-table-column>
          <vxe-table-column
            field="targetCurrency_dictText"
            :title="$srmI18n(`${$getLangAccount()}#i18n_title_targetCurrency`, '目标货币')"></vxe-table-column>
          <vxe-table-column
            field="exchange"
            :title="$srmI18n(`${$getLangAccount()}#i18n_title_exchange`, '汇率')"></vxe-table-column>
          <vxe-table-column
            field="exchangeYear"
            :title="$srmI18n(`${$getLangAccount()}#i18n_field_MIMz_32f5c729`, '汇率年份')"></vxe-table-column>
          <vxe-table-column
            field="effectiveTime"
            :title="$srmI18n(`${$getLangAccount()}#i18n_field_effectiveTime`, '生效时间')"></vxe-table-column>
          <vxe-table-column
            field="expireTime"
            :title="$srmI18n(`${$getLangAccount()}#i18n_field_expiryTime`, '失效时间')"></vxe-table-column>
          <vxe-table-column
            field="updateBy"
            :title="$srmI18n(`${$getLangAccount()}#i18n_PurchaseMassProdHeadList_updateBy`, '修改人')"></vxe-table-column>
          <vxe-table-column
            field="updateTime"
            :title="$srmI18n(`${$getLangAccount()}#i18n_field_updateTime`, '修改时间')"></vxe-table-column>
        </vxe-table>
      </div>
    </a-modal>
    
  </div>
</template>
<script>
import BpExchangeRateAdd from './modules/BpExchangeRateAdd'
import BpExchangeRateDetail from './modules/BpExchangeRateDetail'
import BpExchangeRateEdit from './modules/BpExchangeRateEdit'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        BpExchangeRateAdd,
        BpExchangeRateEdit,
        BpExchangeRateDetail
    },
    data () {
        return {
            visible1: false,
            showAddPage: false,
            showEditPage: false,
            showDetailPage: false,
            showHisPage: false,
            hisTableData: [],
            pageData: {
                businessType: 'rule',
                button: [
                    {authorityCode: 'srmExchangeRate#BpExchangeRate:add', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    {authorityCode: 'srmExchangeRate#BpExchangeRate:getDataByErp', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_SMWWWWF_b44eb4e2`, '获取ERP数据'), icon: 'arrow-down', clickFn: this.getDataByErp},
                    {authorityCode: 'srmExchangeRate#BpExchangeRate:pushDataToErp', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'), icon: 'arrow-up', clickFn: this.pushDataToERP},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                   
                ],
                optColumnList: [
                    {
                        type: 'detail',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleDetail,
                        authorityCode: 'srmExchangeRate#BpExchangeRate:queryById'
                    },
                    {authorityCode: 'srmExchangeRate#BpExchangeRate:edit', type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showEditCondition},
                    {authorityCode: 'srmExchangeRate#BpExchangeRate:delete', type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete},
                    {authorityCode: 'exchange#bpExchangeRateHis:historyList', type: 'history', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_numberOfHistory`, '历史记录'), clickFn: this.handleHis, allow: this.showHisCondition}
                ],
                optColumnWidth: 200,
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_MIMzWMI_4781bcc6`, '汇率年份/汇率')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/exchange/bpExchangeRate/list',
                delete: '/exchange/bpExchangeRate/delete',
                histRecordList: '/exchange/bpExchangeRateHis/historyList',
                columns: 'PurchaseBpExchangeRate',
                getDataByErpUrl: '/exchange/bpExchangeRate/getDataByErp',
                pushDataToERPUrl: '/exchange/bpExchangeRate/pushDataToErp'
            }
        }
    },
    methods: {
        handleAdd (row) {
            this.currentEditRow = {}
            this.showAddPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleEdit (row) {
            this.currentEditRow = row
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleDetail (row) {
            this.currentEditRow = row
            this.showDetailPage = true
        },
        changeVersion (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_versionUpdating `, '版本更新'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterTheVersionIsUpdated`, '版本更新后,可以在历史查看旧版'),
                onOk: function () {
                    that.postData({'id': row.id})
                }
            })
        },
        postData (param){
            this.$refs.listPage.confirmLoading = true
            getAction(this.url.changeVersion, param).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        handleHis (row){
            this.currentEditRow = null
            this.loading = true
            console.log(row.id) 
            getAction(this.url.histRecordList, {id: row.id}).then(res => {
                console.log(res)
                if (res.success) {
                    let list = res.result || []
                    this.hisTableData = list
                    this.visible1 = true
                }
                this.loading = false
            })
          
        },
        handleOk (){
            this.visible1 = false
        },
        handleCancel (){
            this.visible1 = false
        },
        handListPage (){
            this.showAddPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.showHisPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        }

    }
}
</script>