<template>
  <div class="PurchaseBarcodeInfoHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        ref="businessRef"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="tab"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction, postAction } from '@/api/manage'

export default {
    name: 'SaleBarcodePoolHeadDetail',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            requestData: {
                detail: {
                    url: '/base/barcode/saleBarcodePoolHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/purchaseBarcodeInfoHead/edit'
            }
        }
    },
    // computed: {
    //     remoteJsFilePath () {
    //         let templateNumber = this.currentEditRow.templateNumber
    //         let templateVersion = this.currentEditRow.templateVersion
    //         let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
    //         return `${account}/purchase_barcodeInfo_${templateNumber}_${templateVersion}`
    //     }
    // },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: '',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_ToWcCc_2f30a8dd`, '条码属性内容'),
                        groupNameI18nKey: '',
                        groupCode: 'saleBarcodePoolItemList',
                        groupType: 'item',
                        sortOrder: '2'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ToKjtH_29acbdae`, '条码使用记录'),
                        groupNameI18nKey: '',
                        groupCode: 'saleBarcodePoolRecordList',
                        groupType: 'item',
                        sortOrder: '3'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        sortOrder: '6',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_ToLF_30d1cd95`, '条码规则'),
                        fieldLabelI18nKey: '',
                        fieldName: 'ruleCode',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        disabled: true,
                        placeholder: ''
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '6',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_To_cfcc0`, '条码'),
                        fieldLabelI18nKey: '',
                        fieldName: 'barcode',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        disabled: true,
                        placeholder: ''
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '9',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'),
                        fieldLabelI18nKey: '',
                        fieldName: 'status',
                        dictCode: 'barcodeInfoStatus',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        disabled: true,
                        placeholder: ''
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '13',
                        fieldType: 'number',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormd2b_printNumber`, '允许打印份数'),
                        fieldLabelI18nKey: '',
                        fieldName: 'printNumber',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        required: '1',
                        placeholder: ''
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '11',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseBarcodePoolHeadList_printdNumber`, '已打印份数'),
                        fieldLabelI18nKey: '',
                        fieldName: 'printedNumber',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '14',
                        fieldType: 'switch',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQKj_2fb92fa0`, '是否使用'),
                        fieldLabelI18nKey: '',
                        fieldName: 'used',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        required: '0',
                        placeholder: ''
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'saleBarcodePoolItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_EStFAc_fa28a730`, '业务单据类型'),
                        fieldLabelI18nKey: '',
                        field: 'businessType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        helpText: ''
                    },
                    {
                        groupCode: 'saleBarcodePoolItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_EStFJO_fa24b7fe`, '业务单据字段'),
                        fieldLabelI18nKey: '',
                        field: 'businessField',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        helpText: ''
                    },
                    {
                        groupCode: 'saleBarcodePoolItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_tFJORL_d790c17a`, '单据字段名称'),
                        fieldLabelI18nKey: '',
                        field: 'businessFieldName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        helpText: ''
                    },
                    {
                        groupCode: 'saleBarcodePoolItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WcAc_2b753bb9`, '属性类型'),
                        fieldLabelI18nKey: '',
                        field: 'attributeType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        helpText: ''
                    },
                    {
                        groupCode: 'saleBarcodePoolItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQKjIo_263b3421`, '是否使用简码'),
                        fieldLabelI18nKey: '',
                        field: 'usedCode_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        fieldType: 'switch',
                        required: '0',
                        helpText: ''
                    },
                    {
                        groupCode: 'saleBarcodePoolItemList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WcCc_2b700c1d`, '属性内容'),
                        fieldLabelI18nKey: '',
                        field: 'attribute',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        alertMsg: '',
                        alertMsgI18nKey: '',
                        fieldType: 'input',
                        required: '0',
                        helpText: ''
                    },
                    {
                        groupCode: 'saleBarcodePoolRecordList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ok_a51b4`, '动作'),
                        fieldLabelI18nKey: '',
                        field: 'operation',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleBarcodePoolRecordList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operatorPerson`, '操作人'),
                        fieldLabelI18nKey: '',
                        field: 'operator',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleBarcodePoolRecordList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationTime`, '操作时间'),
                        fieldLabelI18nKey: '',
                        field: 'operateTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        goBack () {
            this.$parent.hideEditPage()
        },
        showFlow ({ Vue, pageConfig, btn, groupCode }) {
            this.flowId = pageConfig.groups[0].formModel.flowId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_APUziNxOmAQLW_25fa4a00`, '当前审批策略不能查看流程！'))
                return
            }
            this.flowView = true
        },
        cancelAudit ({ Vue, pageConfig, btn, groupCode }) {
            this.auditStatus = pageConfig.groups[0].formModel.auditStatus
            if(this.auditStatus != '1'){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APxOqXUz_5fba973e`, '当前不能撤销审批'))
                return
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit, pageConfig.groups[0].formModel)
                }
            })
        },
        closeFlowView () {
            this.flowView = false
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'barcodeInfoAudit'
            param['auditSubject'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_TotUzWtyW_1dc23b59`, '条码单审批，单号：') + formData.trialNumber
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.submitCallBack(formData)
                } else {
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>