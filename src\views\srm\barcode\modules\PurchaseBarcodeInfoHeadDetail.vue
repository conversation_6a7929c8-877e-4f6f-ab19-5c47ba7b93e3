<template>
  <div class="PurchaseBarcodeInfoHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        ref="businessRef"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="tab"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>

      <!-- <a-modal
    v-drag    
        centered
        :width="960"
        :maskClosable="false"
        :visible="flowView"
        @ok="closeFlowView"
        @cancel="closeFlowView">
        <iframe
          style="width:100%;height:560px"
          title=""
          :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
          frameborder="0"></iframe>
      </a-modal> -->
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"/>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction, postAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
export default {
    name: 'PurchaseBarcodeInfoHeadDetail',
    components: {
        BusinessLayout,
        flowViewModal,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            requestData: {
                detail: {
                    url: '/base/barcode/purchaseBarcodeInfoHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.cancelAudit,
                    show: this.auditButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.auditDetailButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/purchaseBarcodeInfoHead/edit',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_barcodeInfo_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_LFToRH_cc18358d`, '规则条码明细'),
                        groupNameI18nKey: '',
                        groupCode: 'sysBarcodeList',
                        groupType: 'item',
                        sortOrder: '5'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_JIIToRH_2e146811`, '自定义条码明细'),
                        groupNameI18nKey: '',
                        groupCode: 'customBarcodeList',
                        groupType: 'item',
                        sortOrder: '10'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_hxIdRH_5f96d352`, '发布对象明细'),
                        groupNameI18nKey: '',
                        groupCode: 'barcodeSupplierListList',
                        groupType: 'item',
                        sortOrder: '15'
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_To_cfcc0`, '条码'),
                        fieldLabelI18nKey: '',
                        field: 'barcode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_jVH_143f48d`, '原信息'),
                        fieldLabelI18nKey: '',
                        field: 'originalBarcode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'sysBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormd2b_printNumber`, '允许打印份数'),
                        fieldLabelI18nKey: '',
                        field: 'printNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'customBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_To_cfcc0`, '条码'),
                        fieldLabelI18nKey: '',
                        field: 'barcode',
                        fieldType: 'input',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '250'
                    },
                    {
                        groupCode: 'customBarcodeList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseFormd2b_printNumber`, '允许打印份数'),
                        fieldLabelI18nKey: '',
                        field: 'printNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'barcodeSupplierListList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'),
                        fieldLabelI18nKey: '',
                        field: 'toElsAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'barcodeSupplierListList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'),
                        fieldLabelI18nKey: '',
                        field: 'supplierCode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'barcodeSupplierListList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                        fieldLabelI18nKey: '',
                        field: 'supplierName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }
                ]
            }
        },
        auditDetailButtonShow  ({ pageData }) {
            if(pageData.auditStatus == '0'){
                return false
            }
            return (pageData.publishAudit === '1')
        },
        auditButtonShow  ({ pageData }) {
            console.log(pageData)
            if(pageData.auditStatus !== '1'){
                return false
            }
            return (pageData.publishAudit === '1')
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }

        },
        goBack () {
            this.$parent.hideEditPage()
        },
        showFlow ({ Vue, pageConfig, btn, groupCode }) {
            this.flowId = pageConfig.groups[0].formModel.flowId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_APUziNxOmAQLW_25fa4a00`, '当前审批策略不能查看流程！'))
                return
            }
            this.flowView = true
        },
        cancelAudit ({ Vue, pageConfig, btn, groupCode }) {
            this.auditStatus = pageConfig.groups[0].formModel.auditStatus
            if(this.auditStatus != '1'){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APxOqXUz_5fba973e`, '当前不能撤销审批'))
                return
            }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit, pageConfig.groups[0].formModel)
                }
            })
        },
        closeFlowView () {
            this.flowView = false
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'barcodeInfoAudit'
            param['auditSubject'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_TotUzWtyW_1dc23b59`, '条码单审批，单号：') + formData.trialNumber
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.submitCallBack(formData)
                } else {
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>