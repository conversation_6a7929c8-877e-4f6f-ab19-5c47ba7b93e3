<template>
  <a-drawer
    :title="title"
    :mask-closable="true"
    width="650"
    placement="right"
    :closable="true"
    @close="close"
    :visible="visible"
    style="height: calc(100% - 55px);overflow: auto;padding-bottom: 53px;"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_newsTitle`, '消息标题')"
        >
          <a-input
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_newsTitleMsg`, '请输入消息标题')"
            v-decorator="['esTitle', {}]"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_sendContent`, '发送内容')"
        >
          <a-input
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_sendContentMsg`, '请输入发送内容')"
            v-decorator="['esContent', {}]"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_sendNeedParams`, '发送所需参数')"
        >
          <a-input
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_sendNeedParamsMsg`, '请输入发送所需参数Json格式')"
            v-decorator="['esParam', {}]"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_receiver`, '接收人')"
        >
          <a-input
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterReceiver`, '请输入接收人')"
            v-decorator="['esReceiver', {}]"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_sendType`, '发送方式')"
        >
          <j-dict-select-tag
            :trigger-change="true"
            dict-code="msgType"
            v-decorator="['esType', {}]"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectSendType`, '请选择发送方式')"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_sendTime`, '发送时间')"
        >
          <a-date-picker
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            v-decorator="['esSendTime', {}]" />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_sendStatus`, '发送状态')">
          <j-dict-select-tag
            :trigger-change="true"
            dict-code="msgSendStatus"
            v-decorator="['esSendStatus', {}]"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectSendStatus`, '请选择发送状态')"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_sendFrequency`, '发送次数')">
          <a-input-number v-decorator="['esSendNum', {}]" />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_sendFailReason`, '发送失败原因')">
          <a-input v-decorator="['esResult', {}]" />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')">
          <a-input v-decorator="['remark', {}]" />
        </a-form-item>
      </a-form>
    </a-spin>
    <div v-show="!disableSubmit">
      <a-button
        style="margin-right: .8rem"
        @confirm="handleCancel">
        
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
      </a-button>
      <a-button
        @click="handleOk"
        type="primary"
        :loading="confirmLoading">
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
import { postAction } from '@/api/manage'
import pick from 'lodash.pick'
import moment from 'moment'

export default {
    name: 'SysMessageModal',
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
            visible: false,
            model: {},
            labelCol: {
                xs: { span: 24 },
                sm: { span: 5 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            },

            confirmLoading: false,
            form: this.$form.createForm(this),
            validatorRules: {},
            disableSubmit: true,
            url: {
                add: '/message/sysMessage/add',
                edit: '/message/sysMessage/edit'
            }
        }
    },
    created () {},
    methods: {
        add () {
            this.edit({})
        },
        edit (record) {
            this.form.resetFields()
            this.model = Object.assign({}, record)
            this.visible = true
            this.$nextTick(() => {
                this.form.setFieldsValue(
                    pick(
                        this.model,
                        'esContent',
                        'esParam',
                        'esReceiver',
                        'esResult',
                        'esSendNum',
                        'esSendStatus',
                        'esTitle',
                        'esType',
                        'remark'
                    )
                )
                //时间格式化
                this.form.setFieldsValue({ esSendTime: this.model.esSendTime ? moment(this.model.esSendTime) : null })
            })
        },
        close () {
            this.$emit('close')
            this.visible = false
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    that.confirmLoading = true
                    let httpurl = ''
                    if (!this.model.id) {
                        httpurl += this.url.add
                    } else {
                        httpurl += this.url.edit
                    }
                    let formData = Object.assign(this.model, values)
                    //时间格式化
                    formData.esSendTime = formData.esSendTime ? formData.esSendTime.format('YYYY-MM-DD HH:mm:ss') : null

                    console.log(formData)
                    postAction(httpurl, formData)
                        .then(res => {
                            if (res.success) {
                                that.$message.success(res.message)
                                that.$emit('ok')
                            } else {
                                that.$message.warning(res.message)
                            }
                        })
                        .finally(() => {
                            that.confirmLoading = false
                            that.close()
                        })
                }
            })
        },
        handleCancel () {
            this.close()
        }
    }
}
</script>

<style scoped></style>
