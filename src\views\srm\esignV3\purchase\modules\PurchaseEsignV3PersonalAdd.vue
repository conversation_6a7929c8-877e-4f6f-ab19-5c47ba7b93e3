<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
// import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction } from '@/api/manage'
import REGEXP from '@/utils/regexp'
export default {
    name: 'PurchaseEsignV3PersonalAdd',
    mixins: [EditMixin],
    data () {
        return {
            pageData: {
                form: {
                    subAccount: '',
                    psnAccount: '',
                    psnName: '',
                    subAccountId: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_UzSQNJOHrA0MPEQA`, '个人认证信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_srmey_69b65ff`, 'srm账号'),
                                    fieldName: 'subAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.form.subAccount = data[0].subAccount
                                        Vue.form.psnName = data[0].realname
                                        Vue.form.psnAccount = data[0].phone || data[0].email
                                        Vue.form.subAccountId = data[0].id
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150},
                                            {field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150},
                                            {field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_phone`, '手机号'), with: 150},
                                            {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'), with: 150}
                                        ], modalUrl: '/account/elsSubAccount/list', modalParams: {status: 1}
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPsmLjDeyBK_b93e3b33`, 'E签宝个人用户账号标识'),
                                    fieldName: 'psnAccount',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPsmLjDeyBK_b93e3b33`, 'E签宝个人用户账号标识'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                                    fieldName: 'psnName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iIyo_40ba5cff`, '证件号码'),
                                    fieldName: 'psnIdCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iIyo_40ba5cff`, '证件号码')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                                    fieldName: 'psnIdCardType',
                                    dictCode: 'psnIdCardType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mLlty_72c077b8`, '个人手机号'),
                                    fieldName: 'psnMobile',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_idXKREtltySWEmUQltyHjULi_b9d87b07`, '运营商实名登记手机号或银行卡预留手机号，仅用于认证')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mLWEmy_fcd32f1c`, '个人银行卡号'),
                                    fieldName: 'bankCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mLWEmy_fcd32f1c`, '个人银行卡号')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '页面中默认选择的实名认证方式'),
                                    fieldName: 'psnDefaultAuthMode',
                                    dictCode: 'psnDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '设置页面中默认选择的实名认证方式')
                                },
                                {
                                    fieldType: 'multiple',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjmLLiCKvL_5eba0f39`, '页面中可选择的个人认证方式范围'),
                                    fieldName: 'psnAvailableAuthModes',
                                    dictCode: 'psnDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjmLLiCKvL_5eba0f39`, '设置页面中可选择的个人认证方式范围')
                                },
                                {
                                    fieldType: 'multiple',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjmLVHJO_bd7b6fe6`, '页面中可编辑的个人信息字段'),
                                    fieldName: 'psnEditableFields',
                                    dictCode: 'psnEditableFields',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjmLVHJO_bd7b6fe6`, '设置页面中可编辑的个人信息字段')
                                },
                                {
                                    fieldType: 'multiple',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjlbvL_72298d1`, '页面中可选择的授权范围'),
                                    fieldName: 'authorizedScopes',
                                    dictCode: 'authorizedScopes',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjlbvL_72298d1`, '页面中可选择的授权范围')
                                }
                            ],
                            validateRules: {
                                subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LieyxOLV_542509fe`, '认证账号不能为空')}],
                                psnAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mLjDeyBKxOLV_850950df`, '个人用户账号标识不能为空')}],
                                psnName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cRxOLV_d345baa`, '姓名不能为空')}],
                                authorizedScopes: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbvLxOLV_3cc7645c`, '授权范围不能为空')}]
                            }
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), type: 'primary', click: this.submitCertificationEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/esignv3/purchaseEsignV3Personal/add',
                auth: '/esignv3/purchaseEsignV3Personal/submitCertification'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
        },
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            this.$refs.addPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.confirmLoading = false
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.add
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$refs.addPage.confirmLoading = false
                        this.$message[type](res.message)
                        this.goBack()
                    }).finally(() => {
                        this.$refs.addPage.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        submitCertificationEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.auth
                    postAction(url, params).then(res => {
                        this.confirmLoading = false
                        if(res.success){
                            this.goBack()
                            if(res.result.realnameStatus !== '1'){
                                window.open(res.result.authUrl)
                            }
                        }else{
                            this.$message.error(res.message)
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>