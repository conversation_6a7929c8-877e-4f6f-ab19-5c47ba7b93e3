<template>
  <div
    style="height:100%"
    class="SaleCLSealsList">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      modelLayout="seal"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleManageCLSealsEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleManageCLSealsDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <SaleManageCLSealsAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>

</template>
<script>
// 新列表模板
import { ListMixin } from '@comp/template/list/ListMixin'
import {getAction, postAction} from '@/api/manage'
import SaleManageCLSealsAdd from './modules/SaleManageCLSealsAdd'
import SaleManageCLSealsEdit from './modules/SaleManageCLSealsEdit'
import SaleManageCLSealsDetail from './modules/SaleManageCLSealsDetail'
export default {
    mixins: [ListMixin],
    components: {
        SaleManageCLSealsEdit,
        SaleManageCLSealsDetail,
        SaleManageCLSealsAdd
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRRLSNWeqR_85f28ae`, '(机构名称/印章别名)')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractLock#saleSeals:detail'}
                ],
                showGridLayoutBtn: false
            },
            url: {
                add: '/contractLock/purchaseClSeals/add',
                edit: '/contractLock/purchaseClSeals/edit',
                list: '/contractLock/saleClSeals/saleList',
                delete: '/contractLock/purchaseClSeals/delete',
                columns: 'ElsSealsList'
            }
        }
    },
    methods: {
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        // 新增
        handleAdd () {
            this.showAddPage = false
            this.$nextTick(() => {
                this.showDetailPage = false
                this.showEditPage = false
                this.showAddPage = true
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        // 查看
        handleView (row) {
            this.showDetailPage = false
            this.$nextTick(() => {
                this.currentEditRow = row
                this.showDetailPage = true
                this.showEditPage = false
                this.showAddPage = false
            })
        },
        // 编辑
        handleEdit (row){
            this.showEditPage = false
            this.$nextTick(() => {
                this.currentEditRow = row
                this.showDetailPage = false
                this.showEditPage = true
                this.showAddPage = false
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        // 启用
        enableItem (row) {
            row.operateStatus = 'ENABLE'
            postAction(this.url.edit, row).then((res)=>{
                // 接口调用完查询
                if(res && res.success) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_AjLR_28088728`, '启用成功'))
                    this.searchEvent()
                } else{
                    this.$message.warning(res.message)
                }
            })

        },
        // 禁用
        disabledItem (row) {
            row.operateStatus = 'DISABLE'
            postAction(this.url.edit, row).then((res)=>{
                // 接口调用完查询
                if(res && res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_HjLR_38ff8896`, '禁用成功'))
                    this.searchEvent()
                } else{
                    this.$message.warning(res.message)
                }
            })
        },
        pushItem (row) {
            getAction('/contractLock/purchaseClSeals/push', {id: row.id}).then((res)=>{
                // 接口调用完查询
                if(res && res.success){
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_YdLR_2f75e1a8`, '推送成功'))
                    this.searchEvent()
                }else{
                    this.$message.warning(res.message)
                }
            })
            this.searchEvent()
        },
        // 删除
        deleteItem (row) {
            getAction(this.url.delete, {id: row.id}).then((res)=>{
                // 接口调用完查询
                if(res && res.success){
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_deleteSuccess`, '删除成功'))
                    this.searchEvent()
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        allowEdit (row){
            if(row.sealId || row.uploaded == '1'){
                return true
            }
            return false
        },
        enableEdit (row){
            if(row.sealId && row.operateStatus == 'DISABLE'){
                return false
            }
            return true
        },
        disabledEdit (row){
            if(row.sealId && row.operateStatus !== 'ENABLE'){
                return false
            }
            return true
        },
        addCallBack (row){
            this.currentEditRow = row
            this.showAddPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        allowDelete (row){
            if(row.sealId || row.uploaded == '1'){
                return true
            }
            return false
        }
    }
}
</script>

<style lang="less" scoped>
.SaleCLSealsList {
    .page-container {
        :deep(.grid-box){
            overflow: auto;
            overflow-x: hidden;
            overflow-y: auto;
        }
    }
}
</style>
