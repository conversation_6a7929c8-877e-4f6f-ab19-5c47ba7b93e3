<!--
    此文件原存放路径不正确,现已调整 如出现404问题 需更新菜单对应路径
-->
<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage "
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction } from '@/api/manage'
import { ListMixin } from '@comp/template/list/ListMixin'
import { REPORT_ADDRESS } from '@/utils/const.js'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            showEditPage: false,
            printRow: {},
            templateNumber: undefined,
            templateOpts: [],
            pageData: {
                businessType: 'deliverywater',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNtFy_e4990d55`, '请输入单号')
                    }

                ],
                form: {
                    keyWord: '',
                    name: ''
                },
                button: [
                    {
                        allow: ()=> {
                            return this.btnInvalidAuth('reconciliation#purchaseDeliveryWater:exportXls')
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
                        type: 'primary',
                        icon: 'download', 
                        folded: false, 
                        clickFn: this.handleExportXls
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.JumpDetail, authorityCode: 'reconciliation#purchaseDeliveryWater:view'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 270
            }, 
            url: {
                list: '/reconciliation/purchaseDeliveryWater/list',
                exportXlsUrl: 'reconciliation/purchaseDeliveryWater/exportXls',
                columns: 'PurchaseFinanceDeliveryWater'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_nRJBQf_aca7c00d`, '采购交互流水'))
        },
        //根据来源单号跳转到对应的详情页
        JumpDetail (row){ 
            let Path = ''
            if(row.documentCategory=='0'){
                Path='/srm/delivery/voucher/VoucherHeadList' 
            }
            if(row.documentCategory=='1'){
                Path='/srm/finance/purchase/PurchaseDeductCostList' 
            }
            if(row.documentCategory=='2'){
                Path='/srm/finance/purchase/PurchaseAddCostList' 
            }
            if(row.documentCategory=='3'){
                Path='/srm/finance/purchase/PurchasePaymentApplyList' 
            }
            if(row.documentCategory=='4'){
                Path='/srm/contract/purchase/PurchaseContractPromiseList' 
            }
            if(row.documentCategory=='5'){
                Path='/srm/contract/purchase/PurchaseContractAcceptanceList' 
            }
            this.$router.push({
                path: Path,
                query: {
                    searchNumber: row.documentNumber,
                    linkRouteType: 'im'
                }
            })
        }
    }
}
</script>