<template>
  <div class="PurchaseFadadaSealPsn business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="edit"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction } from '@/api/manage'
import {  BUTTON_SAVE, BUTTON_BACK} from '@/utils/constant.js'

export default {
    name: 'EditPurchaseFadadaSealPsnModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            requestData: {
                detail: { url: '/electronsign/fadada/purchaseFadadaSealPsn/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/electronsign/fadada/purchaseFadadaSealPsn/edit'
                    },
                    authorityCode: 'fadada#purchaseFadadaSealPsn:edit'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SMWelbvLjKy_4079f79e`, '获取印章授权给成员链接'),
                    key: 'edit',
                    attrs: {
                        type: 'primary'
                    },
                    args: {
                        url: '/electronsign/fadada/purchaseFadadaSealPsn/sealGrantUrl'
                    },
                    click: this.handleCustomSave,
                    showCondition: this.showButton,
                    authorityCode: 'fadada#purchaseFadadaSealPsn:sealGrantUrl'
                },
                BUTTON_BACK
            ],
            url: {
                save: '/electronsign/fadada/purchaseFadadaSealPsn/edit',
                sealGrantUrl: '/electronsign/fadada/purchaseFadadaSealPsn/sealGrantUrl',
                detail: '/electronsign/fadada/purchaseFadadaSealPsn/queryById'
            }
        }
    },
    methods: {    
        showButton () {
            const params = this.getAllData()
            //未保存单据，按钮隐藏
            if(!params.id || params.id ==''){
                return false
            }
            return true
        },    
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'),
                        fieldLabelI18nKey: '',
                        fieldName: 'corpName',
                        required: '1',
                        initFunction: 
                        function initFunction (Vue, { _pageData, _form, _row, _value, _cacheAllData, _data }) {
                            let { corpName = '', openCorpId = ''} = _data[0] || {}
                            _form.corpName = corpName
                            _form.openCorpId = openCorpId
                        },
                        bindFunction: function bindFunction (Vue, data) {
                            if (Vue.pageConfig) {
                                let formModel = Vue.pageConfig.groups[0].formModel
                                formModel.corpName = data[0].corpName
                                formModel.openCorpId = data[0].openCorpId
                                formModel.sealName = ''
                                formModel.categoryType = ''
                                formModel.memberName = ''
                                formModel.memberId = ''
                                formModel.sealId = ''
                                formModel.picFileUrl = ''
                            }
                        },
                        extend: {
                            modalColumns: [
                                {field: 'orgCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRoo_307b3288`, '机构代码'), with: 150},
                                {field: 'corpName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), with: 150}
                            ], modalUrl: '/electronsign/fadada/purchaseFadadaOrg/listOrg', modalParams: {}
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hfftRID_5f2da41a`, '法大大机构ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'openCorpId',
                        disabled: true    
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeRL_27c5a0f3`, '印章名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealName',
                        required: '1',
                        initFunction: 
                        function initFunction (Vue, { _pageData, _form, _row, _value, _cacheAllData, _data }) {
                            let { sealName = '', categoryType = '', sealId = '', picFileUrl = ''} = _data[0] || {}
                            _form.sealName = sealName
                            _form.categoryType = categoryType
                            _form.sealId = sealId
                            _form.picFileUrl = picFileUrl
                        },
                        bindFunction: function bindFunction (Vue, data) {
                            if (Vue.pageConfig) {
                                let formModel = Vue.pageConfig.groups[0].formModel
                                formModel.sealName = data[0].sealName
                                formModel.categoryType = data[0].categoryType
                                formModel.sealId = data[0].sealId
                                formModel.picFileUrl = data[0].picFileUrl
                            }
                        },
                        extend: {
                            modalColumns: [
                                {field: 'sealName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeRL_27c5a0f3`, '印章名称'), with: 150},
                                {field: 'categoryType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeAc_27ca63e0`, '印章类型'), with: 150}
                            ], 
                            modalUrl: '/electronsign/fadada/purchaseFadadaSeal/listAuthSeal', 
                            modalParams (Vue, form, row) {
                                return { openCorpId: form.openCorpId }
                            },
                            beforeCheckedCallBack (parentRef, pageData, groupData, form) {
                                debugger
                                return new Promise((resolve, reject) => {
                                    !form.openCorpId ? reject('先选择机构') : resolve('success')
                                })
                            }
                        }    
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeAc_27ca63e0`, '印章类型'),
                        fieldLabelI18nKey: '',
                        dictCode: 'fadadaCategoryType',
                        fieldName: 'categoryType',
                        disabled: true     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sealId`, '印章ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealId',
                        disabled: true     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberName',
                        required: '1',
                        initFunction: 
                        function initFunction (Vue, { _pageData, _form, _row, _value, _cacheAllData, _data }) {
                            let { memberName = '', memberId = ''} = _data[0] || {}
                            _form.memberName = memberName
                            _form.memberId = memberId
                        },
                        bindFunction: function bindFunction (Vue, data) {
                            if (Vue.pageConfig) {
                                let formModel = Vue.pageConfig.groups[0].formModel
                                formModel.memberName = data[0].memberName
                                formModel.memberId = data[0].memberId
                            }
                        },
                        extend: {
                            modalColumns: [
                                {field: 'memberName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRcR_27c2d9e7`, '员工姓名'), with: 150},
                                {field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), with: 150}
                            ], modalUrl: '/electronsign/fadada/purchaseFadadaOrgPsn/listEnableMember', 
                            modalParams (Vue, form, row) {
                                return { openCorpId: form.openCorpId }
                            },
                            beforeCheckedCallBack (parentRef, pageData, groupData, form) {
                                debugger
                                return new Promise((resolve, reject) => {
                                    !form.openCorpId ? reject('先选择机构') : resolve('success')
                                })
                            }
                        }   
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jRID_27b7ae68`, '员工ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'memberId',
                        disabled: true   
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbvKKI_b38ae204`, '授权开始时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'grantStartTime',
                        required: '1'       
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lbyWKI_c23545e5`, '授权结束时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'grantEndTime',
                        required: '1'     
                    }
                ]
            }
        },
        // 自定义保存操作
        handleCustomSave (args) {
            // 获取页面所有数据
            const allData = this.getAllData() || {}
            // 这里可以添加自定义校验逻辑
            if (!allData.id || allData.id=='') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WsM_13b2663`, '先保存'))
                return
            }

            // 默认保存方法不会校验当前业务模板必填字段
            // 此处手动触发校验方法
            this.stepValidate(args).then(() => {
                postAction(this.url.sealGrantUrl, allData).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.$emit('handleChidCallback', allData)
                    }
                })

            })
        }
    }
}
</script>
