<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showTargetList"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"/>
    <FileList
      v-if="showTargetList"
      ref="supplierCapacity"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import FileList from './modules/FileList'
import { postAction, getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        FileList
    },
    data () {
        return {
            showTargetList: false,
            pageData: {
                businessType: 'tender',
                form: {
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNYBdIAySYBdIRL_4b01476f`, '请输入招标项目编号或招标项目名称')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                        fieldName: 'status',
                        dictCode: 'tenderArchiveStatus'
                    }
                ],
                superQueryShow: false,
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'transfer', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IJ_f1409`, '移交'), clickFn: this.handleTransfer, allow: this.allowTransfer},
                    { type: 'archive', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LA_bf351`, '归档'), clickFn: this.handleArchive, allow: this.allowArchive},
                    { type: 'manage', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Rv_f6c85`, '管理'), clickFn: this.handleManage}
                ],
                optColumnWidth: 270,
                tabsList: []
            }, 
            url: {
                // 列表数据展示
                list: '/tender/archive/tenderProjectArchiveAttachmentHead/list',
                columns: 'PurchaseTenderProjectArchiveAttachmentHeadList',
                transfer: '/tender/archive/tenderProjectArchiveAttachmentHead/transfer',
                archive: '/tender/archive/tenderProjectArchiveAttachmentHead/archive'
            }
        }
    },
    methods: {
        // 返回按钮，隐藏页面，返回list列表
        hideEditPage (){
            this.showTargetList = false
            this.$store.dispatch('SetTabConfirm', false)
        },
        // 管理
        handleManage (row){
            console.log(row)
            this.currentEditRow = row
            this.showTargetList = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        // 归档
        handleArchive (row){
            postAction(this.url.archive, {id: row.id}).then(res => {
                if(res.success) {
                    this.$refs.listPage.loadData()
                    this.$message.success(res.message)
                }else{
                    this.$message.error(res.message)
                }
            }).finally(()=>{
            })
        },  
        // 移交
        handleTransfer (row){
            console.log(row)
            postAction(this.url.transfer, {id: row.id}).then(res => {
                if(res.success) {
                    this.$refs.listPage.loadData()
                    this.$message.success(res.message)
                }else{
                    this.$message.error(res.message)
                }
            }).finally(()=>{
            })
        },
        allowTransfer (row){
            // 新建才可移交
            return row.status !== '0'
        },
        allowArchive (row){
            // 移交才可归档
            return row.status !== '1'
        }
        
    }
    
}
</script>