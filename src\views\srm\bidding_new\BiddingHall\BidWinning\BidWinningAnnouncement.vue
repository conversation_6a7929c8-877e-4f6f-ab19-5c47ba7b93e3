<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <content-header
        v-if="showHeader"
        :btns="btns" />
      <div
        class="container"
        :style="style">
        <div v-if="ifshow">
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBLRt_7e1f3f68`, '中标人名单') }}</span>
          </titleTrtl>
          <listTable
            ref="listTable"
            :setGridHeight="250"
            :pageStatus="pageStatus"
            :fromSourceData="tableData"
            :showTablePage="false"
            :statictableColumns="tableColumns"
          ></listTable>
        </div>
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_basicInfo`, '基本信息') }}</span>
          </titleTrtl>
          <div>
            <Dataform
              ref="dataform"
              :formData="formData"
              :validateRules="validateRules"
              :pageStatus="pageStatus"
              :fields="fields" />
          </div>
          <div>
            <j-editor
              ref="ueditor"
              :disabled="pageStatus == 'detail'"
              v-model="formData.noticeContent" />
          </div>
        </div>
      </div>
      <flowViewModal
        v-model="flowView"
        :flowId="flowId" />
    </a-spin>
  </div>
</template>
<script>
import ContentHeader from '../components/content-header'
import Dataform from '../components/Dataform'
import listTable from '../components/listTable'
import titleTrtl from '../components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import JEditor from '@/components/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import { valiStringLength, valitNumberLength } from '@views/srm/bidding_new/utils/index'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
export default {
    name: 'BidWinningAnnouncement',
    components: {
        JEditor,
        Dataform,
        listTable,
        titleTrtl,
        ContentHeader,
        flowViewModal
    },
    mixins: [tableMixins, baseMixins],
    data () {
        return {
            ifshow: false,
            alreadyCancel: false,
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            tableColumns: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    field: 'supplierName'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHf_252229e6`, '中标金额'),
                    field: 'winnerAmount',
                    fieldType: 'number',
                    width: '100'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    field: 'scopeSort',
                    width: '80'
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            fields: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRx_2fb94a75`, '是否公告'),
                    fieldLabelI18nKey: '',
                    field: 'notice',
                    defaultValue: '0',
                    fieldType: 'select',
                    dictCode: 'yn'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxvKKI_c12c1427`, '公告开始时间'),
                    fieldLabelI18nKey: '',
                    field: 'noticeStartTime',
                    dataFormat: 'YYYY-MM-DD HH:mm:ss',
                    defaultValue: '',
                    fieldType: 'dateTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxyRKI_c2de5094`, '公告截止时间'),
                    fieldLabelI18nKey: '',
                    field: 'noticeEndTime',
                    defaultValue: '',
                    dataFormat: 'YYYY-MM-DD HH:mm:ss',
                    fieldType: 'dateTime'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxBD_264cc24f`, '公告标题'),
                    fieldLabelI18nKey: '',
                    field: 'noticeTitle',
                    defaultValue: '',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFRxvL_6da70fe6`, '请选择公告范围'),
                    fieldLabelI18nKey: '',
                    field: 'noticeRange',
                    defaultValue: '',
                    filterSelectList: [this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RIRdX_9e53ebba`, '指定供应商')],
                    dictCode: 'srmNoticeScope',
                    fieldType: 'select'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQUz_2fba950f`, '是否审批'),
                    fieldLabelI18nKey: '',
                    field: 'audit',
                    fieldType: 'select',
                    dictCode: 'yn'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板'),
                    field: 'templateLibraryName',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 150,
                    fieldType: 'selectModal',
                    special: true,
                    // dictCode: 'srmCateStatus',
                    required: '0',
                    curConfig: {
                        modalUrl: '/tender/template/purchaseTenderTemplateLibrary/queryTemplateLibrary',
                        selectModel: 'single',
                        modalColumns: [
                            {field: 'templateTitle', title: '模板标题', with: 150},
                            {field: 'remark', title: '备注', with: 150}
                        ],
                        modalParams: {'businessType': 'tender', 'templateType': 'bidWinningNotice'},
                        bindFunction: (Vue, data) => {
                            const {id = '', templateTitle = '', templateContent = ''} = data[0]
                            Vue.$set(Vue.formData, 'templateLibraryName', templateTitle)
                            Vue.$set(Vue.formData, 'noticeContent', templateContent)
                            Vue.$set(Vue.formData, 'templateLibraryId', id)
                        },
                        afterClearCallBack: (Vue, data) => {
                            Vue.$set(Vue.formData, 'templateLibraryName', '')
                            Vue.$set(Vue.formData, 'templateLibraryId', '')
                        }
                    }
                }

            ],
            validateRules: {},

            url: {
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryById: '/tender/purchaseTenderProjectBidWinningAffirmNotice/queryBySubpackageId',
                add: '/tender/purchaseTenderProjectBidWinningAffirmNotice/add',
                edit: '/tender/purchaseTenderProjectBidWinningAffirmNotice/edit',
                submit: '/tender/purchaseTenderProjectBidWinningAffirmNotice/publish',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                generateTemplate: '/tender/purchaseTenderProjectBidWinningAffirmNotice/generator'
            }
        }
    },
    watch: {
        'formData.notice': {
            handler (newV, oldV) {
                if (newV == '1') {
                    let obj = {
                        notice: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRxxOLV_e9de0c05`, '是否公告不能为空') }],
                        noticeStartTime: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxvKKIxOLV_8c90acb7`, '公告开始时间不能为空') }],
                        noticeEndTime: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxyRKIxOLV_b8c4eaa4`, '公告截止时间不能为空') }],
                        noticeTitle: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxBDxOLV_f7de06df`, '公告标题不能为空') }],
                        noticeRange: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxvLxOLV_66e499bf`, '公告范围不能为空') }],
                        audit: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQUzxOLV_1ca4799f`, '是否审批不能为空') }]

                    }
                    this.$set(this, 'validateRules', obj)
                } else {
                    let obj = {
                        notice: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRxxOLV_e9de0c05`, '是否公告不能为空') }],
                        audit: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQUzxOLV_1ca4799f`, '是否审批不能为空') }]
                    }
                    this.$set(this, 'validateRules', obj)
                }
            },
            deep: true
        }
    },
    computed: {
        subPackageRow () {
            return this.currentSubPackage()
        },
        subId () {
            return this.subpackageId()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        pageStatus () {
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                return 'detail'
            } else {
                if (this.formData.status == '0' || !this.formData.status) {
                    return 'edit'
                } else {
                    return 'detail'
                }
            }
        },
        btns () {
            let btn = []
            if (this.formData.status == '0' || !this.formData.status || this.alreadyCancel == true) {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bL_e90d1`, '生成'), click: this.generateTemplateEvent }
                ]
            }
            if (this.formData.status == '1' && this.formData.auditStatus == '1') {
                btn = [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                        type: 'primary',
                        click: this.cancelAudit
                        // show: this.cancelAuditShow
                    }
                ]
            }
            console.log(this.formData.audit, this.formData.status, ['1', '2'].includes(this.formData.auditStatus), this.formData.flowId)
            if (this.formData.audit == '1' && this.formData.status != '0' && ['1', '2'].includes(this.formData.auditStatus) && this.formData.flowId) {
                btn.push({ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow })
            }
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') btn = []
            return btn
        }
    },
    methods: {
        async save (callback = null) {
            let params = await this.$refs.dataform.externalAllData()
            console.log('this.$refs.dataform.externalAllData()', this.$refs.dataform.externalAllData())
            console.log('this.$refs.dataform.formData', this.$refs.dataform.formData)
            params['bidWinningAffirmList'] = this.tableData
            params['subpackageId'] = this.subId
            params['audit'] = params['audit'] ? params['audit'] : '0'
            // 拼接数据
            let url = params.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            let saveFlag = '0'
            valiStringLength(params, [
                {field: 'noticeTitle', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxBD_264cc24f`, '公告标题'), maxLength: 100}
                // {field: 'noticeContent', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKCc_26d474a2`, '公示内容'), maxLength: 1000}
            ])
            for( let item of this.tableData) {
                if (!valitNumberLength(item.winnerAmount, 8, 6)){
                    return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHfHzxOBR_1ffa065f`, '中标金额长度不能超过') + '8')
                }
            }
            postAction(url, params)
                .then((res) => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        saveFlag = '1'
                        this.getData()
                        if (!params.id) {
                            this.id = res.result.id
                        }
                    } else {
                        this.updataVersion()
                    }
                }).catch(() => {
                    this.getData()
                }).finally(() => {
                    this.confirmLoading = false
                    // this.$emit('resetCurrentSubPackage')
                    this.resetCurrentSubPackage()
                    if (saveFlag == '1' && callback) {
                        callback()
                    }
                })
        },
        generateTemplateEvent () {
            let params = this.formData
            // params.id = this.id ? this.id : ''
            // params.bidding = this.bidding
            // params.signUp = this.signUp
            if(!params.templateLibraryId){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFRxIr_987441cd`, '请先选择公告模板'))
                return 
            }
            params['bidWinningAffirmList'] = this.tableData
            params['subpackageId'] = this.subId
            this.confirmLoading = true
            postAction(this.url.generateTemplate, params)
                .then((res) => {
                    // 刷新
                    if (res.success) {
                        this.$message.success(res.message)
                        // this.noticeContent = res.result.content
                        this.init()
                    }else{
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
        },
        publish () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                onOk: function () {
                    that.submit()
                }
            })
        },
        async submit () {
            let params = await this.$refs.dataform.externalAllData()
            params['bidWinningAffirmList'] = this.tableData
            params['subpackageId'] = this.subId
            params['audit'] = params['audit'] ? params['audit'] : '0'

            valiStringLength(params, [
                {field: 'noticeTitle', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RxBD_264cc24f`, '公告标题'), maxLength: 100}
                // {field: 'noticeContent', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKCc_26d474a2`, '公示内容'), maxLength: 1000}
            ])
            for( let item of this.tableData) {
                if (!valitNumberLength(item.winnerAmount, 8, 6)){
                    return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHfHzxOBR_1ffa065f`, '中标金额长度不能超过') + '8')
                }
            }
            this.confirmLoading = true
            postAction(this.url.submit, params).then((res) => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.getData()
                } else {
                    this.updataVersion()
                }
            }).catch((err) => {
                this.$message.error(err)
            }).finally(() => {
                this.confirmLoading = false
                // this.$emit('resetCurrentSubPackage')
                this.resetCurrentSubPackage()
            })
        },
        updataVersion () {
            let params = {
                subpackageId: this.subId
            }
            getAction(this.url.queryById, params).then(res => {
                if(res.success) {
                    let {dataVersion} = res.result || {}
                    this.formData['dataVersion'] = dataVersion
                }
            })
        },
        async getData () {
            let params = {
                subpackageId: this.subId
            }
            try {
                this.confirmLoading = true

                let res2 = await getAction(this.url.queryById, params)
                if (res2.code == 200 && res2.result) {
                    let {bidWinningAffirmList = [], ...others} = res2.result || {}
                    this.formData = others
                    bidWinningAffirmList && bidWinningAffirmList.forEach(item => {
                        item.saleQuoteColumnVOS.forEach(vos => {
                            item[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                            item.affirm = item.affirm == '1' ? true : false
                        })
                    })
                    this.tableData = bidWinningAffirmList
                    console.log(this.tableData)
                }


                const res = await getAction(this.url.queryPrice, params)
                if (res.code == 200 && res.result) {
                    // 分项报价且线上评标的时候，去掉排名
                    if ((res2.result.quoteType == '1' && res2.result.evaluationType =='1') || (res2.result.quoteType == null)){
                        this.tableColumns = this.tableColumns.filter(item=>{
                            return item['field'] != 'scopeSort'
                        })
                    } else {
                        // this.tableColumns.splice(1, 0, {
                        //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                        //     field: 'evaPrice'
                        // })
                        // 否则，其他情况就附带上排名以及各个投标函的投标报价列信息
                        const resultData = res.result
                        let columns = []
                        resultData.forEach(data => {
                            let obj = {
                                title: data.title,
                                children: []
                            }
                            let columnChildren = []
                            data.quoteColumnList.forEach(column => {
                                column['field'] = `${column['field']}_${data['id']}`
                                columnChildren.push(column)
                            })
                            obj.children = columnChildren
                            columns.push(obj)
                        })
                        console.log('columns', columns)
                        // 如果是线下的情况，则替换投标报价字段field
                        if(res2.result.evaluationType == '2'){
                            console.log('jinlaile')
                            columns.forEach(item=>{
                                item.children.forEach(item2=>{
                                    console.log(item2)
                                    item2.field = 'evaPrice'
                                })
                            })
                        }
                        console.log('columns', columns)
                        this.tableColumns = this.tableColumns.filter(column => {
                            if (!column.hasOwnProperty('children')) {
                                return column
                            }
                        })
                        this.tableColumns.splice(3, 0, ...columns)
                    }
                }

            } catch (error) {
                console.log(error)
            }
            this.confirmLoading = false
            // this.$emit('resetCurrentSubPackage')
            this.resetCurrentSubPackage()
        },
        async init () {
            this.height = document.documentElement.clientHeight
            await this.getData()
            this.ifshow = true
        },
        cancelAudit () {
            let params = this.formData
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData('/a1bpmn/audit/api/cancel', params)
                    // that.$refs.businessRefName.loadData()
                }
            })
        },
        showFlow () {
            this.flowId = this.formData.flowId
            this.flowView = true
        },
        postAuditData (invokeUrl, formData) {
            this.showHeader = false
            let param = {}
            param['rootProcessInstanceId'] = formData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param)
                .then((res) => {
                    if (res.success) {
                        this.alreadyCancel = true
                        this.getData()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.showHeader = true
                    this.confirmLoading = false
                })
        }
    },
    mounted () {
        this.init()
    }
}
</script>
<style lang="less" scoped>
.container {
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
</style>
