<template>
  <div class="PurchaseFadadaSeal business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="edit"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { BUTTON_SAVE, BUTTON_BACK} from '@/utils/constant.js'
import { postAction } from '@/api/manage'


export default {
    name: 'EditPurchaseFadadaSealModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/purchaseFadadaSeal/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/electronsign/fadada/purchaseFadadaSeal/edit'
                    },
                    authorityCode: 'fadada#purchaseFadadaOrg:edit'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_SMWecIKy_a544d415`, '获取印章创建链接'),
                    key: 'publish',
                    attrs: {
                        type: 'primary'
                    },
                    args: {
                        url: '/electronsign/fadada/purchaseFadadaSeal/getCreateUrl'
                    },
                    click: this.handleCustomPublish,
                    showCondition: this.showButton,
                    authorityCode: 'fadada#purchaseFadadaSeal:create'
                },
                BUTTON_BACK
            ],
            url: {
                save: '/electronsign/fadada/purchaseFadadaSeal/edit',
                detail: '/electronsign/fadada/purchaseFadadaSeal/queryById',
                getCreateUrl: '/electronsign/fadada/purchaseFadadaSeal/getCreateUrl'
            }
        }
    },
    methods: {
        showButton () {
            const params = this.getAllData()
            //未保存单据，按钮隐藏
            if(!params.id || params.id ==''){
                return false
            }
            return true
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'),
                        fieldLabelI18nKey: '',
                        fieldName: 'orgName',
                        required: '1',
                        initFunction: 
                        function initFunction (Vue, { _pageData, _form, _row, _value, _cacheAllData, _data }) {
                            let { orgName = '', orgId = ''} = _data[0] || {}
                            _form.orgName = orgName
                            _form.orgId = orgId
                        },
                        bindFunction: function bindFunction (Vue, data) {
                            if (Vue.pageConfig) {
                                let formModel = Vue.pageConfig.groups[0].formModel
                                formModel.orgName = data[0].corpName
                                formModel.openCorpId = data[0].openCorpId
                            }
                        },
                        extend: {
                            modalColumns: [
                                {field: 'orgCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRoo_307b3288`, '机构代码'), with: 150},
                                {field: 'corpName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), with: 150}
                            ], modalUrl: '/electronsign/fadada/purchaseFadadaOrg/listOrg', modalParams: {}
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeAc_27ca63e0`, '印章类型'),
                        fieldLabelI18nKey: '',
                        dictCode: 'fadadaCategoryType',
                        fieldName: 'categoryType',
                        required: '1'     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeRL_27c5a0f3`, '印章名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealName',
                        required: '1'     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeBP_27c80dc7`, '印章标签'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealTag'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hfftRID_5f2da41a`, '法大大机构ID'),
                        fieldLabelI18nKey: '',
                        fieldName: 'openCorpId',
                        disabled: true
                    }
                ]
            }
        },
        handleCustomPublish (args){
            // 获取页面所有数据
            const allData = this.getAllData() || {}
            // 这里可以添加自定义校验逻辑
            if (!allData.id || allData.id=='') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WsM_13b2663`, '先保存'))
                return
            }

            // 默认保存方法不会校验当前业务模板必填字段
            // 此处手动触发校验方法
            this.stepValidate(args).then(() => {
                postAction(this.url.getCreateUrl, allData).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.$emit('handleChidCallback', allData)
                    }
                })

            })
        }
    }
}
</script>
