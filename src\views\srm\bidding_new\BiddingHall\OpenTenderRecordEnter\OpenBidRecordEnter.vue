<template>
  <div v-if="ifshow">
    
    <a-spin :spinning="confirmLoading">
      <content-header
        :btns="btns"
      />
      <RecordResponse
        ref="response"
        :formData="formData"></RecordResponse>
      <RecordFile
        ref="file"
        :formData="formData"></RecordFile>
    </a-spin>
  </div>
</template>
<script>
import RecordFile from './modules/RecordFile'
import RecordResponse from './modules/RecordResponse'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import ContentHeader from '../components/content-header'
  
export default {
    mixins: [baseMixins],
    components: {
        RecordFile,
        RecordResponse,
        ContentHeader
    },
    props: {
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subPackageRow () {
            return this.currentSubPackage()
        },
        currentRow () {
            return this.tenderCurrentRow()
        },  
        btns () {
            let btn = []
            if(this.formData.status != '1' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'){
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish }
                ]
            }
            
            return btn
        }
        
          
    },
    
    data () {
        return {
            confirmLoading: false,
            ifshow: false,
            formData: [],
            purchaseTenderProjectAttachmentInfoList: [],
            pageData: {
                optColumnList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            }
        }
    },
    methods: {
        save () {
            this.confirmLoading = true
            this.$refs.response.$refs.tenderOpenBidRecordSupplierList.getValidate().then(res=>{
                console.log(this.formData)
                let params = this.formData
                if(params.tenderOpenBidRecordAttachmentList){
                    params.tenderOpenBidRecordAttachmentList.forEach(item=>{
                        item.attachmentId = item.id
                        delete item.id
                    })
                }
                let url = params.id ? '/tender/openbid/tenderOpenBidRecordHead/edit' : '/tender/openbid/tenderOpenBidRecordHead/add'
                let {checkType} = this.currentNode().extend
                params = {
                    ...params,
                    checkType
                }
                postAction(url, params).then(res=>{
                    if(res.success){
                        this.$message.success(res.message)
                        this.getData()
                    }else{
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            }).catch(err=>{
                this.confirmLoading = false
                this.$message.error('保存失败！请检查')
            })
            
        },
        publish (){
            this.confirmLoading = true
            this.$refs.response.$refs.tenderOpenBidRecordSupplierList.getValidate().then(res=>{
                let url = '/tender/openbid/tenderOpenBidRecordHead/publish'
                let params = this.formData
                if(params.tenderOpenBidRecordAttachmentList){
                    params.tenderOpenBidRecordAttachmentList.forEach(item=>{
                        item.attachmentId = item.id
                        delete item.id
                    })
                }
                let {checkType} = this.currentNode().extend
                params = {
                    ...params,
                    checkType
                }
                postAction(url, params).then(res=>{
                    if(res.success){
                        this.$message.success(res.message)
                        this.getData()
                    }else{
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            }).catch(err=>{
                this.confirmLoading = false
                this.$message.error('发布失败！请检查')
            })
            
        },
        async getData () {
            console.log(this.subPackageRow)
            let params = {
                // subpackageId: this.subPackageRow.subpackageId,
                // 先写死分包id，拿默认数据
                subpackageId: this.subPackageRow.id
            }
            await getAction('/tender/openbid/tenderOpenBidRecordHead/queryBySubpackageId', params, {headers: {xNodeId: this.getNodeParams().nodeId}}).then(res=>{
                if(res.success) {
                    this.formData = res.result
                    this.formData.subpackageId = this.subPackageRow.id
                    this.formData.subpackageName = this.subPackageRow.subpackageName
                    this.formData.tenderProjectId = this.subPackageRow.headId
                    this.formData.tenderProjectName = this.subPackageRow.tenderProjectName
                    // this.$refs.response.init(res.result)
                    // this.$refs.file.init(res.result)
                    this.ifshow = true
                    console.log(this.formData)
                    console.log(this.subPackageRow)
                }else{
                    this.$message.error(res.message)
                }
            })
        }
    },
    async created () {
        await this.getData()
        // if (['1', '2'].includes(this.fromSourceData.status) || this.pageStatus == 'detail') this.externalToolBar = []
    }
}
</script>
  <style lang="less" scoped>
  
  </style>