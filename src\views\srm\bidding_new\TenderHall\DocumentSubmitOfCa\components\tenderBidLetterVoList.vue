<template>
  <div>
    <a-spin :spinning="spinning">
      <div class="tenderBidTetterVoBox">
        <div
          class="tenderBidTetterVoList" 
        >
          <titleTrtl class="margin-b-10">
            <span>{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_eBxVH_9dbaaa19`, '投标函信息')) }}</span>
            <template
              slot="right"
              v-if="subpackage.useCa == '1'">
              <a-button
                size="small"
                @click="encodeFile"
                v-if="currentRow && isEdit && (!priceOpeningsList.encryption || priceOpeningsList.encryption == '0')">{{ $srmI18n(`${$getLangAccount()}#i18n_field_uweBx_b78cc545`, '加密投标函') }}</a-button>
              <a-button
                @click="toDecodeFile"
                size="small"
                v-if="currentRow && isEdit && priceOpeningsList.encryption == '1'">{{ $srmI18n(`${$getLangAccount()}#i18n_field_yweBx_c249a308`, '解密投标函') }}</a-button>
            </template>
          </titleTrtl>
          <div
            class="tenderBidTetterBox"
            :style="{height: `${tenderBidTetterVoListHeight}px`}"
          >
            <div
              class="tetterVoItem"
              :class="tetterVoModalIndex == i ? 'activeBox': 'normalBox'"
              v-for="(item, i) in tetterVoData"
              :key="i"
              @click="radioChange(item, i)">
              <div class="tetterVoItemLine margin-b-10">
                <span class="flex3 colorName">{{ item.name }}</span>
              </div>
              <div class="tetterVoItemLine">
                <span
                  class="flex3"
                  :class="item.formatType == '9' ? 'colorFormatTypeNormal': 'colorFormatType'">{{ item.formatType ? formatTypeOptions[item.formatType] : "" }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="infoRightBox">
          <Information
            ref="Information"
            :currentRow="currentRow"
            :pageStatus="pageStatus"
            :fromSourceData="fromSourceData"
            :priceOpeningsList="priceOpeningsList"
            v-if="priceOpeningsList"/>
        </div>
        <a-modal
          centered
          :title="$srmI18n(`${$getLangAccount()}#i18n_field_VWNCAVQ_8751a228`, '请输入CA口令')"
          :visible="showDecodeFileKey"
          :width="300"
          :confirmLoading="confirmLoading"
          @ok="decodeFile"
          @cancel="() => {this.showDecodeFileKey = false}">
          <a-input v-model="decodeFileKey"></a-input>
        </a-modal>
      </div>
    </a-spin>
  </div>
</template>
<script>
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import Information from './modules/Information'
import { postAction } from '../api/request'
export default {
    mixins: [baseMixins],
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        fromSourceData: {
            default: () => {
                return {}
            },
            type: Object
        },
        subpackage: {
            default: () => {
                return {}
            },
            type: Object
        },
        importBaseMixins: {
            type: Boolean,
            default: true
        }
    },
    components: {
        listTable,
        titleTrtl,
        Information
    },
    computed: {
        tenderQuotationsType () {
            if (this.checkType == '0') {
                return 'preTenderFormatType'
            } else {
                if (this.processType == '1' || this.fromSourceData.processType == '1') {
                    return 'resultTenderFormatType '
                }
                return 'tenderFormatType'
            }
        },
        isEdit () {
            return this.pageStatus == 'edit'
        },
        encryptionStatus () {
            if (this.priceOpeningsList) {
                let s = ''
                switch (this.priceOpeningsList.encryption) {
                case '0':
                    s = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Luw_18da1d0`, '未加密')
                    break
                case '1':
                    s = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Iuw_16b0698`, '已加密')
                    break
                case '2':
                    s = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Iyw_171b7b5`, '已解密')
                    break
                default: 
                    s = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Luw_18da1d0`, '未加密')
                }
                return s
            } else {
                return ''
            }
        }
    },
    watch: {
        pageStatus () {
            this.currentRow = null
            this.priceOpeningsList = null
        }
    },
    data () {
        return {
            currentRow: null,
            priceOpeningsList: null,
            rowTitel: '某投标函',
            uKeyInfo: null,
            certInfo: null,
            confirmLoading: false,
            showDecodeFileKey: false,
            decodeFileKey: '',
            spinning: false,
            formData: {},
            formatTypeOptions: {},
            tetterVoData: [],
            tetterVoModalType: 'add',
            tenderBidTetterVoListHeight: '200',
            tetterVoModalIndex: null,
            quoteColumnList: []

        }
    },
    methods: {
        getParamsData () {
            return JSON.parse(JSON.stringify(this.tetterVoData))
        },
        // 获取表格下拉字典
        queryDictData (dictCode) {
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: dictCode
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    res.result.map(({value, text, title}) => {
                        this.formatTypeOptions[value] = title
                        return {
                            value,
                            title,
                            label: text
                        }
                    })
                    this.$forceUpdate()
                }
            })
        },
        // 投标函列表选择切换
        radioChange (row, i) {
            this.tetterVoModalIndex = i
            this.currentRow = row
            this.priceOpeningsList = this.currentRow.priceOpeningsList ? this.currentRow.priceOpeningsList[0] : {}
            if (this.$refs.Information) this.$refs.Information.showTetterListTable()
        },
        init ({ tenderBidLetterVoList }) {
            if (tenderBidLetterVoList && tenderBidLetterVoList.length > 0) {
                this.tetterVoData = tenderBidLetterVoList
                this.radioChange(this.tetterVoData[0], 0)
            }
        },

        // 加密&解密
        getUkeyInfo () { // 获取Uk
            return postAction('/uk/action', {'type': 100}).then(res => {
                this.uKeyInfo = JSON.parse(res.data)[0]
            })
        },
        // 获取加密信息
        async getCertInfo () {
            if (!this.uKeyInfo) {
                await this.getUkeyInfo()
            }
            let p = {
                'type': 200,
                'keySN': this.uKeyInfo.keySN,
                'asymid': 1,
                'usage': 1
            }
            return postAction('/uk/action', p).then(res => {
                this.certInfo = res.data
            })
        },
        toDecodeFile () { // 解密弹出
            this.showDecodeFileKey = true
            this.decodeFileKey = ''
        },
        // base64解密
        decode (str) {
            return decodeURIComponent(window.atob(str).split('').map(function (c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
            }).join(''))
        },
        // 解密
        async decodeFile () {
            if (!this.decodeFileKey) return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNCAVQ_8751a228`, '请输入CA口令' ))
            if (this.spinning) return 
            this.spinning = true
            try {
                if (!this.uKeyInfo) {
                    await this.getUkeyInfo()
                }
                let raw1 = this.priceOpeningsList.customizeFieldData
                let params1 = {
                    'type': 303, 
                    'keySN': this.uKeyInfo.keySN,
                    'pin': this.decodeFileKey,
                    'raw': raw1
                }
                let raw2 = this.priceOpeningsList.quoteColumnEncryption
                let params2 = {
                    'type': 303, 
                    'keySN': this.uKeyInfo.keySN,
                    'pin': this.decodeFileKey,
                    'raw': raw2
                }
                let p1 = new Promise((resolve, reject) => {
                    postAction('/uk/action', params1).then(res => {
                        console.log(res)
                        if (res.code == '0') {
                            let data = JSON.parse(this.decode(res.data))
                            resolve(data)
                        } else {
                            reject(res)
                        }
                    }, (err) => {
                        reject(err)
                    })
                })
                let p2 = new Promise((resolve, reject) => {
                    postAction('/uk/action', params2).then(res => {
                        if (res.code == '0') {
                            let data = JSON.parse(this.decode(res.data))
                            resolve(data)
                        } else {
                            reject(res)
                        }
                    }, (err) => {
                        reject(err)
                    })
                })
                Promise.all([p1, p2]).then(res => {
                    this.$set(this.priceOpeningsList, 'customizeFieldData', res[0])
                    this.$set(this.priceOpeningsList, 'saleQuoteMaterialDataList', res[1])
                    this.$set(this.priceOpeningsList, 'encryption', '0')
                    this.$refs.Information.showTetterListTable()
                    this.showDecodeFileKey = false
                    this.spinning = false
                }, (err) => {
                    this.spinning = false
                })
            } catch {
                this.spinning = false

            }
        },
        encodeFile () { // 加密投标函
            if (this.spinning) return 
            // 加密前要校验数据是否完整填写
            this.$refs['Information'].getValidate().then(async (res) => {
                if (this.priceOpeningsList.processType !== '0') {
                    let falg = false
                    // 物料报价列遍历
                    console.log(this.currentRow.saleQuoteMaterialDataList)
                    for (let item of this.currentRow.saleQuoteMaterialDataList) {
                        // 物料行遍历
                        for (let material of item.materialDataList) {
                            if (!material.price) {
                                falg = true
                                this.$message.warning(`投标函[${this.currentRow.name}]报价列[${item.title}]物料号[${material.materialName}]未填写报价`)
                                break
                            }
                        }
                        if (falg) break
                    }
                    if (falg) return false
                }
                this.spinning = true
                try {
                    if (!this.uKeyInfo) {
                        await this.getUkeyInfo()
                    }
                    await this.getCertInfo()
                    let raw1 = JSON.stringify(this.priceOpeningsList.customizeFieldData)
                    let params1 = {
                        'type': 302,
                        'asymid': 1,
                        'cert': this.certInfo,
                        'raw': raw1,
                        'wrapperType': 0
                    }
                    let raw2 = JSON.stringify(this.currentRow.saleQuoteMaterialDataList)
                    let params2 = {
                        'type': 302,
                        'asymid': 1,
                        'cert': this.certInfo,
                        'raw': raw2,
                        'wrapperType': 0
                    }
                    let p1 = new Promise((resolve, reject) => {
                        postAction('/uk/action', params1).then(res => {
                            if (res.code == '0') {
                                resolve(res)
                            } else {
                                reject(res)
                            }
                        }, (err) => {
                            reject(err)
                        })
                    })
                    let p2 = new Promise((resolve, reject) => {
                        postAction('/uk/action', params2).then(res => {
                            if (res.code == '0') {
                                resolve(res)
                            } else {
                                reject(res)
                            }
                        }, (err) => {
                            reject(err)
                        })
                    })
                    Promise.all([p1, p2]).then(res => {
                        this.priceOpeningsList.customizeFieldData = res[0].data
                        this.priceOpeningsList.quoteColumnEncryption = res[1].data
                        this.$set(this.priceOpeningsList, 'encryption', '1')
                        this.$refs.Information.showTetterListTable()
                        this.spinning = false
                    }, (err) => {
                        this.spinning = false
                    })
                } catch {
                    this.spinning = false

                }
            }, err => {
                console.log(err)
            })
            
        }
        
    },
    created () {
        this.queryDictData(this.tenderQuotationsType)
        this.tenderBidTetterVoListHeight = document.documentElement.clientHeight - 300
    },
    mounted () {
        this.init({tenderBidLetterVoList: this.fromSourceData.tenderBidLetterVoList})
    }
}
</script>
<style lang="less" scoped>
.fl{
    float: left;
}
.fr{
    float: right;
}
.clearFix{
    clear: both;
}
.margin-b-10{
    margin-bottom: 10px;
}
.tenderBidTetterVoBox{
    display: flex;
}
.tenderBidTetterVoList{
    .tenderBidTetterBox{
        overflow: auto;
    }
    min-width: 200px;
    padding: 5px;
    border: 1px solid #e8e8e8;
    flex: 17%;
    margin-right: 10px;
}
.infoRightBox{
    flex: 78%;
    padding: 5px;
    border: 1px solid #e8e8e8;
    min-width: 400px;
}
.tetterVoItem{
    padding: 5px 10px;
    cursor: pointer;
    margin-bottom: 10px;
    .tetterVoItemLine{
        display: flex;
    }
    .flex1{
        flex: 1;
        height: 21px;
        :deep(svg){
            height: 21px;
        }
    }
    .flex3{
        flex: 5;
        overflow: hidden;
    }
}
.normalBox{
    border: 1px solid #ccc;
}
.activeBox {
    border: 1px solid #1890ff;
}
.colorName{
    color: #1890ff;
}
.colorFormatType{
    color: #e68824;
}
.colorFormatTypeNormal{
    color: #ccc;
}
</style>
