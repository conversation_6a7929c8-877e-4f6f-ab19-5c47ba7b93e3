<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="show"
        :ref="businessRefName"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :currentEditRow="currentEditRow"
        :pageFooterButtons="pageFooterButtons"
        modelLayout="masterSlave"
        :handleAfterDealSource="handleAfterDealSource"
        :fromSourceData="fromSourceData"
        pageStatus="edit"
        v-on="businessHandler"
      >
        <template #noticeInfo="{ slotProps }">
          <j-editor
            ref="ueditor"
            v-model="noticeContent" />
        </template>
      </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk"
        isTree />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import JEditor from '@/components/els/JEditor'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { baseMixins } from '../../../plugins/baseMixins.js'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { BUTTON_PUBLISH } from '@/utils/constant.js'
export default {
    name: 'ChangeTenderNoticeEdit',
    components: {
        BusinessLayout,
        JEditor,
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage', 'currentNode', 'resetCurrentSubPackage'],
    mixins: [businessUtilMixin, baseMixins],
    data () {
        return {
            businessRefName: 'businessRef',
            show: false,
            id: '',
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            externalToolBar: {
                purchaseTenderNoticeItemList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    type: 'primary',
                    click: this.saveEvent
                },
                {
                    ...BUTTON_PUBLISH,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                    args: {
                        url: '/tender/purchaseTenderNoticeHead/publish'
                    },
                    click: this.publishEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bL_e90d1`, '生成'),
                    click: this.generateTemplateEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            currentGroupCode: {},
            noticeStatus: '',
            fromSourceData: { z: 1_1},
            noticeContent: '',
            url: {
                detail: '/tender/purchaseTenderNoticeHead/queryById',
                add: '/tender/purchaseTenderNoticeHead/add',
                edit: '/tender/purchaseTenderNoticeHead/edit',
                publish: '/tender/purchaseTenderNoticeHead/publish',
                generateTemplate: '/tender/purchaseTenderNoticeHead/generator'
            },
            remoteJsFilePath: ''
        }
    },
    computed: {
        // remoteJsFilePath () {
        //     // let templateNumber = this.currentEditRow.templateNumber
        //     // let templateVersion = this.currentEditRow.templateVersion
        //     // let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
        //     let templateNumber = 'TC2022051401'
        //     let templateVersion = 1
        //     let account = 100000
        //     return `${account}/purchase_purchaseTenderNotice_${templateNumber}_${templateVersion}`
        // },
        subId () {
            return this.subpackageId()
        },
        subpackage (){
            return this.currentSubPackage()
        }
    },
    methods: {
        generateTemplateEvent () {
            // let cb = () => {
            //     let params = this.getParamsData()
            //     let templateLibraryId
            //     this.$refs.businessRef.$refs.purchaseTenderNoticeItemListgrid[0].pageConfig.groups.forEach(group=>{
            //         if(group.groupCode == 'noticeInfo'){
            //             templateLibraryId = group.formModel.templateLibraryId || ''
            //         }
            //     })
            //     postAction(this.url.generateTemplate, {'businessType': 'tender', templateLibraryId, 'tenderNoticeId': this.id, 'businessId': params.subpackageId})
            //         .then((res) => {
            //             // 刷新
            //             if (res.success) {
            //                 this.noticeContent = res.result.content
            //             }else{
            //                 this.$message.error(res.message)
            //             }
            //         }).finally(() => {
            //             this.confirmLoading = false
            //         })
            // }
            // if (this.id) {
            //     // cb()
            // } else {
            //     // 未保存前先保存
            //     this.saveEvent(cb)
            // }

            let params = this.getParamsData()
            params.id = this.id ? this.id : ''
            params.bidding = this.bidding
            params.signUp = this.signUp
            if(!params.templateLibraryId){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFRxIr_987441cd`, '请先选择公告模板'))
                return
            }
            this.confirmLoading = true
            // let templateLibraryId
            // this.$refs.businessRef.$refs.purchaseTenderNoticeItemListgrid[0].pageConfig.groups.forEach(group=>{
            //     if(group.groupCode == 'noticeInfo'){
            //         templateLibraryId = group.formModel.templateLibraryId || ''
            //     }
            // })
            postAction(this.url.generateTemplate, params)
                .then((res) => {
                    // 刷新
                    if (res.success) {
                        this.$message.success(res.message)
                        //this.noticeContent = res.result.content
                        this.currentEditRow.id = res.result.id
                        this.init()
                    }else{
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
        },
        publishEvent () {
            let params = this.getParamsData()
            try{
                //购标开始时间必须小于购标结束时间
                if(params.biddingEndTime && new Date(params.biddingBeginTime).getTime()>=new Date(params.biddingEndTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBvKKIlTXUlByWKI_bdc4b7e6`, '购标开始时间必须小于购标结束时间'))
                    return
                }
                //报名开始时间必须小于报名结束时间
                if(params.signUpEndTime && new Date(params.signUpBeginTime).getTime()>=new Date(params.signUpEndTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sRvKKIlTXUsRyWKI_1366c8b8`, '报名开始时间必须小于报名结束时间'))
                    return
                }
                //文件澄清截止时间必须小于等于开标时间
                if(new Date(params.fileClarificationEndTime).getTime()>new Date(params.openBiddingTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QILVyRKIlTXUEUvBKI_1889c9a5`, '文件澄清截止时间必须小于等于开标时间'))
                    return
                }
                //递交截止时间必须大于等于售标结束时间
                if(params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime()<new Date(params.biddingEndTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTfUEUlByWKI_803238d`, '递交截止时间必须大于等于售标结束时间'))
                    return
                }
                //递交截止时间必须小于等于开标时间
                if(params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime()>new Date(params.openBiddingTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTXUEUvBKI_59297d67`, '递交截止时间必须小于等于开标时间'))
                    return
                }
                //公告内容不能为空
                if (this.noticeContent == '') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOKmRxCcxOLV_c478a5c4`, '校验失败！公告内容不能为空'))
                    return
                }
                // // 发布招标公告时必填项通知标题校验
                // if (!params.noticeTitle) {
                //     this.$message.warning('请选择公告标题')
                //     return
                // }
                // // 发布招标公告时必填项通知范围校验
                // if (!params.noticeScope) {
                //     this.$message.warning('请选择通知范围')
                //     return
                // }
                // this.stepValidate(this.$refs[this.businessRefName])
            }catch(err) {
                console.log(err)
            }
            // params.templateNumber='TC2022051401'
            // params.templateVersion= 1
            // params.account= 100000
            params.templateNumber = this.currentEditRow.templateNumber || ''
            params.templateVersion = this.currentEditRow.templateVersion || ''
            params.account = this.currentEditRow.templateAccount || ''
            if (this.currentEditRow.id) {
                params.id=this.currentEditRow.id
            }
            else{
                delete params.id
                delete params.purchaseTenderNoticeItemList.forEach(v=>{
                    delete v.id
                })
                // params.auditStatus = params.audit == '0' ? null : params.auditStatus
                // params.flowId = params.audit == '0' ? null : params.flowId
            }
            console.log('this.currentEditRow.id', params)
            params.bidding = this.subpackage.bidding
            params.signUp = this.subpackage.signUp
            this.confirmLoading = true
            postAction(this.url.publish, params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(type == 'success'){
                    // this.$parent.showNoticeDetailPage = true
                    // this.$parent.showNoticeEditPage = false
                    this.$emit('hide')
                    // this.$emit('resetCurrentSubPackage')
                    this.resetCurrentSubPackage()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        saveEvent () {
            let params = this.getParamsData()
            var that = this
            let flag = false
            params.purchaseTenderNoticeItemList.forEach((v, index)=>{
                // 选的保证金收取方式不为 不收取 同时保证金为空
                if( v.marginCollectionType !== '4' && (v.margin ?? '') === ''){
                    console.log('v', v)
                    that.$message.warning(`第${index+1}行的保证金不能为空`)
                }
            })
            if (flag) {
                return
            }
            try{
                //购标开始时间必须小于购标结束时间
                if(params.biddingEndTime && new Date(params.biddingBeginTime).getTime()>=new Date(params.biddingEndTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBvKKIlTXUlByWKI_bdc4b7e6`, '购标开始时间必须小于购标结束时间'))
                    return
                }
                //报名开始时间必须小于报名结束时间
                if(params.signUpEndTime && new Date(params.signUpBeginTime).getTime()>=new Date(params.signUpEndTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sRvKKIlTXUsRyWKI_1366c8b8`, '报名开始时间必须小于报名结束时间'))
                    return
                }
                //文件澄清截止时间必须小于等于开标时间
                if(new Date(params.fileClarificationEndTime).getTime()>new Date(params.openBiddingTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QILVyRKIlTXUEUvBKI_1889c9a5`, '文件澄清截止时间必须小于等于开标时间'))
                    return
                }
                //递交截止时间必须大于等于售标结束时间
                if(params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime()<new Date(params.biddingEndTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTfUEUlByWKI_803238d`, '递交截止时间必须大于等于售标结束时间'))
                    return
                }
                //递交截止时间必须小于等于开标时间
                if(params.fileSubmitEndTime && new Date(params.fileSubmitEndTime).getTime()>new Date(params.openBiddingTime).getTime()){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJyRKIlTXUEUvBKI_59297d67`, '递交截止时间必须小于等于开标时间'))
                    return
                }



            }catch(err) {
                console.log(err)
            }
            //如果保存过 params.id就有值；或者从list的编辑按钮进来，有currentEditRow.id值 就进行edit操作
            let url = this.currentEditRow.id ? this.url.edit : this.url.add
            // 编辑页面进来的，params.id赋值为当前行id
            if(url == this.url.edit){
                params.id = this.currentEditRow.id
            }else if(url == this.url.add){
                // 否则，走的add接口，params中不应该有id的存在，因此delete掉
                delete params.id
                delete params.purchaseTenderNoticeItemList.forEach(v=>{
                    delete v.id
                })
            }
            params.bidding = this.subpackage.bidding
            params.signUp = this.subpackage.signUp
            params.templateNumber = params.templateNumber || this.currentEditRow.templateNumber || ''
            params.templateVersion = params.templateVersion || this.currentEditRow.templateVersion || ''
            params.templateAccount = params.templateAccount || this.currentEditRow.templateAccount || ''
            params.templateName = params.templateName || this.currentEditRow.templateName || ''
            // let templateLibraryId, templateTitle
            // this.$refs.businessRef.$refs.purchaseTenderNoticeItemListgrid[0].pageConfig.groups.forEach(group=>{
            //     if(group.groupCode == 'noticeInfo'){
            //         templateLibraryId = group.formModel.templateLibraryId || ''
            //         templateTitle = group.formModel.templateTitle || ''
            //     }
            // })
            // params.templateLibraryId = templateLibraryId ||  params.templateLibraryId
            // params.templateTitle = templateTitle ||  params.templateTitle

            this.confirmLoading = true
            postAction(url, params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    // 走的新增保存接口，将返回的res.result.id赋值到this.id
                    if(url==this.url.add){
                        this.currentEditRow.id=res.result.id
                    }
                    this.init()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        getParamsData () {
            let {
                purchaseTenderSupplierInvitation,
                purchaseTenderNoticeItemList,
                fileSubmit,
                getFile,
                openTenderInfo,
                noticeInfo,
                ...baseForm
            } = this.$refs[this.businessRefName].extendAllData().allData
            console.log('this.$refs[this.businessRefName].extendAllData().allData', this.$refs[this.businessRefName].extendAllData().allData)
            // 保存时，预审变更公告 noticeType：4，后审变更公告 noticeType：3，
            let {nodeId, extend: {checkType, processType, currentStep, noticeType}} = this.currentNode()
            // const noticeType = checkType == '0' ? '4' : '3'
            let params = baseForm
            // 文件获取
            params['signUpType'] = getFile.signUpType
            params['signUpBeginTime'] = getFile.signUpBeginTime
            params['signUpEndTime'] = getFile.signUpEndTime
            params['biddingType'] = getFile.biddingType
            params['bidding'] = getFile.bidding
            params['biddingBeginTime'] = getFile.biddingBeginTime
            params['biddingEndTime'] = getFile.biddingEndTime
            params['contactsPhone'] = getFile.contactsPhone
            params['fileTenderObtainDesc'] = getFile.fileTenderObtainDesc
            params['offlineSaleAccount'] = getFile.offlineSaleAccount
            params['fileClarificationEndTime'] = getFile.fileClarificationEndTime
            params['receipt'] = getFile.receipt || ''
            // 文件递交
            params['fileSubmitEndTime'] = fileSubmit.fileSubmitEndTime
            params['fileSubmitAddress'] = fileSubmit.fileSubmitAddress
            // 开标信息
            params['openBiddingTime'] = openTenderInfo.openBiddingTime
            params['openBiddingAddress'] = openTenderInfo.openBiddingAddress
            // 公告信息
            params['noticeTitle'] = noticeInfo.noticeTitle
            params['noticeScope'] = noticeInfo.noticeScope
            params['noticeContent'] = noticeInfo.noticeContent
            params['templateName'] = noticeInfo.templateName
            params['templateLibraryId'] = noticeInfo.templateLibraryId || ''
            params['templateTitle'] = noticeInfo.templateTitle || ''
            // 邀请名单 -- 投标邀请变更
            if (this.noticeType == '5') {
                params['purchaseTenderSupplierInvitation'] = purchaseTenderSupplierInvitation
            }
            // 关联分包
            params['purchaseTenderNoticeItemList'] = purchaseTenderNoticeItemList
            params['subpackageId'] = this.subId
            params['tenderProjectId'] = this.tenderCurrentRow.id
            params['noticeType'] = noticeType
            params['noticeContent'] = this.noticeContent
            params['tenderTaskId'] = this.$ls.get('SET_TENDERCURRENTROW').tenderTaskId
            // params.templateLibraryId = params.templateLibraryId || this.tenderCurrentRow.templateLibraryId || ''
            // params.templateTitle = params.templateTitle || this.tenderCurrentRow.templateTitle || ''
            return params
        },
        businessGridAdd ({ pageConfig, groupCode }) {
            this.currentGroupCode = groupCode
            console.log('groupCode', groupCode)
            let url = '/tender/purchaseTenderProjectHead/subpackage/listAll'
            let columns = [
                { field: 'subpackageName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称') },
                { field: 'subpackageNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseBarcodeInfoItemList951d_itemNumber`, '单据行号') }
            ]
            let {fullData} = this.getItemGridRef(this.currentGroupCode).getTableData()
            let ids =  fullData.map(item => item.subpackageId)
            // 已选的不能在勾选
            let checkedConfig = {
                visibleMethod: ({row}) => {
                    let flag = true
                    if (ids.includes(row.id)) flag = false
                    return flag
                }
            }
            //let superQueryParams = [{'logicSymbol': 'eq', 'fieldCode': 'status', 'fieldType': '', 'dictCode': '', 'fieldValue': '3150', 'joiner': 'AND'}]
            //superQueryParams=encodeURI(JSON.stringify(superQueryParams))
            this.$refs.fieldSelectModal.open(url, { headId: this.tenderCurrentRow.id, signUp: this.subpackage.signUp, bidding: this.subpackage.bidding, tenderType: this.subpackage.tenderType, status: 3150}, columns, 'multiple', checkedConfig)
        },
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef(this.currentGroupCode)
            console.log('this.currentGroupCode', this.currentGroupCode)

            let ids = itemGrid.getTableData().fullData.map(item => item.subpackageId)
            var subpackageList = new Array()

            // 已选的不能在勾选
            data.map(({id, subpackageName}) => {
                if (!ids.includes(id)) {
                    subpackageList.push({
                        subpackageId: id,
                        subpackageName
                    })
                }
            })
            itemGrid.insertAt([...subpackageList], -1)
        },
        handleAfterDealSource (pageConfig, resultData) {
            if (this.noticeType != '5') {
                // 不是邀请公告类型的公告需要进行下拉选项的过滤操作
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'noticeInfo') {
                        group.formFields.forEach(field => {
                            if (field.fieldName == 'noticeScope') {
                                field['filterSelectList'] = [this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RIRdX_9e53ebba`, '指定供应商')]
                            }
                        })
                    }
                })
            }
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }

            let signUpFlag = (this.subpackage.signUp !== '1')
            let biddingFlag = (this.subpackage.bidding !== '1')
            // let none = (signUpFlag && biddingFlag)
            // 分包预审
            const checkType = this.subpackage.checkType
            // 隐藏数组内的字段
            let arr = []
            // 非报名情况, 投标邀请变更的预审环节
            if(signUpFlag || (this.noticeType && this.noticeType == '5' && checkType == '0')){
                arr=arr.concat('signUpBeginTime', 'signUpEndTime', 'signUpType')
            }
            //非购标情况
            if(biddingFlag){
                arr=arr.concat('biddingBeginTime', 'biddingEndTime', 'biddingType', 'offlineSaleAccount')
            }
            // 不报名不购标情况
            // if(none){
            //     arr=arr.concat('fileSubmitEndTime')
            // }
            let formFields = []
            pageConfig.groups[1].formFields.forEach(item => {
                if (arr.indexOf(item.fieldName) == -1) {
                    formFields.push(item)
                }
            })
            pageConfig.groups[1].formFields = formFields

            setDisabledByProp('signUpBeginTime', signUpFlag)
            setDisabledByProp('signUpEndTime', signUpFlag)
            setDisabledByProp('biddingBeginTime', biddingFlag)
            setDisabledByProp('biddingEndTime', biddingFlag)
            // setDisabledByProp('fileSubmitEndTime', none)
            // 无论什么情况，变更公告这里的报名方式和购标方式都不能操作，只能查看
            setDisabledByProp('biddingType', true)
            setDisabledByProp('signUpType', true)

            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }

                rule[prop] = [{
                    required: flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            //此处写根据分包信息作为判断展示位
            let signUpValidateFlag = (this.subpackage.signUp == '1')
            let biddingValidateFlag = (this.subpackage.bidding == '1')
            // let Or = (signUpValidateFlag || biddingValidateFlag)

            setValidateRuleByProp('signUpBeginTime', signUpValidateFlag)
            setValidateRuleByProp('signUpEndTime', signUpValidateFlag)
            setValidateRuleByProp('signUpType', signUpValidateFlag)
            setValidateRuleByProp('biddingBeginTime', biddingValidateFlag)
            setValidateRuleByProp('biddingEndTime', biddingValidateFlag)
            // setValidateRuleByProp('fileSubmitEndTime', Or)

        },
        init () {
            // 有id，则是从list列表的编辑按钮进入的，有当前行id，可查数据
            if(this.currentEditRow.id){
                this.confirmLoading = true
                this.show = false
                return getAction(this.url.detail, {id: this.currentEditRow.id}).then(res => {
                    if (res.success) {
                        if (res.result) {
                            this.fromSourceData = res.result || {}
                            // this.noticeStatus = res.result.noticeStatus
                            this.noticeContent = res.result.noticeContent
                            // if (this.noticeStatus != '0') this.externalToolBar = {}
                        }
                    }
                })
                    .finally(() => {
                        this.confirmLoading = false
                        this.show = true
                    })
            } else {
                let {nodeId, extend: {checkType, processType, currentStep, noticeType}} = this.currentNode()
                // 变更时拿对应公告的原始类型
                // 5-0  4-1  3-2
                let selnoticeType = noticeType
                if (noticeType == '4') {
                    selnoticeType = '1'
                }
                if (noticeType == '5') {
                    selnoticeType = '0'
                }
                if (noticeType == '3') {
                    selnoticeType = '2'
                }
                let getGridDataUrl = '/tender/purchaseTenderNoticeHead/queryBySubpackageId'
                return getAction(getGridDataUrl, {subpackageId: this.subId, noticeType: selnoticeType})
                    .then(res => {
                        if (res.success) {
                            console.log('this.subpackage.tenderType', this.subpackage.tenderType)
                            // 公开招标状态，则从分包信息中查到招标公告信息，回显到页面
                            // if(this.subpackage.tenderType == '1'){
                            this.fromSourceData = res.result || {}
                            this.noticeContent = res.result.noticeContent
                            // }
                            this.show = true
                            this.purchaseTenderNoticeItemList = res.result.purchaseTenderNoticeItemList
                        }
                    })
            }
        }
    },
    async created () {
        await this.init()

        const currentEditRow = this.fromSourceData.templateNumber && this.fromSourceData.templateAccount ? {
            templateNumber: this.fromSourceData.templateNumber,
            templateName: this.fromSourceData.templateName,
            templateVersion: this.fromSourceData.templateVersion,
            templateAccount: this.fromSourceData.templateAccount
        } : await this.getBusinessTemplate('purchaseTenderNotice')
        if (!currentEditRow) {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
            return
        }
        this.currentEditRow = Object.assign(this.currentEditRow, currentEditRow)
        this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/purchase_purchaseTenderNotice_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`

    }
}
</script>