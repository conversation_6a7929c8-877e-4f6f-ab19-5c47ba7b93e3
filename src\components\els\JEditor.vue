<template>
  <div class="tinymce-editor">
    <editor
      v-if="showEditor"
      :id="id"
      v-model="myValue"
      :init="init"
      :disabled="disabled"
      :key="tinymceFlag"
      @onClick="onClick" />
  </div>
</template>

<script>
import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver/theme'
import 'tinymce/plugins/image'
import 'tinymce/plugins/media'
import 'tinymce/plugins/table'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/code'
// import 'tinymce/plugins/contextmenu'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/colorpicker'
// import 'tinymce/plugins/textcolor'
import 'tinymce/plugins/fullscreen'
import 'tinymce/icons/default/icons'
import 'tinymce/plugins/paste'
import { DEFAULT_LANG, USER_INFO } from '@/store/mutation-types'
export default {
    components: {
        Editor
    },
    props: {
        id: {
            type: String,
            default: 'myEditor'
        },
        value: {
            type: String,
            required: false
        },
        triggerChange: {
            type: Boolean,
            default: false,
            required: false
        },
        disabled: {
            type: Boolean,
            default: false
        },
        plugins: {
            type: [String, Array],
            default: 'lists image media table wordcount fullscreen code powerpaste paste'
        },
        toolbar: {
            type: [String, Array],
            default: 'undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | subscript superscript  | table | alignleft aligncenter alignright alignjustify | outdent indent blockquote |  numlist bullist checklist | forecolor backcolor casechange permanentpen formatpainter removeformat | pagebreak | charmap emoticons | fullscreen  preview save print | insertfile image media pageembed template link anchor codesample | a11ycheck ltr rtl | showcomments addcomment | wordcount | code '
        },
        coustomInit: {
            //自定义配置
            type: Object,
            default: () => {}
        }
    },
    data () {
        return {
            showEditor: false,
            //初始化配置
            init: {
                skin_url: '/tinymce/skins/lightgray',
                height: 380,
                // inline: true,
                plugins: this.plugins,
                toolbar: this.toolbar,
                branding: false,
                menubar: false,
                toolbar_drawer: false,
                auto_reset_designmode: true,
                content_css: '',
                paste_retain_style_properties: 'color font-size text-align line-height width height font-weight',
                images_upload_handler: (blobInfo, success) => {
                    const img = 'data:image/jpeg;base64,' + blobInfo.base64()
                    success(img)
                },
                font_formats: 'Arial=arial,helvetica,sans-serif; Courier New=courier new,courier,monospace; AkrutiKndPadmini=Akpdmi-n;宋体=宋体;黑体=黑体;仿宋=仿宋;微软雅黑=微软雅黑;楷体-GB2312=楷体-GB2312;',
                paste_merge_formats: false, // 合并相同的文本格式元素以减少生成的 HTML 元素的数量
                // powerpaste_word_import: 'clean', // 参数可以是propmt, merge, clean
                // powerpaste_html_import: 'clean', // propmt, merge, clean
                // powerpaste_allow_local_images: true //允许带图片
                init_instance_callback: function (editor) {
                    // _this.loadFont(editor.id)
                    // loadFont()
                    // console.log('document.fonts', document.fonts, editor)
                    document.fonts.forEach((v)=>{
                        
                        document.getElementById(editor.id+'_ifr').contentWindow.document.fonts.add(v)
                    })
                   
                }
            },
            languageMap: {
                zh: {
                    language_url: '/tinymce/langs/zh_CN.js',
                    language: 'zh_CN'
                },
                jp: {
                    language_url: '/tinymce/langs/ja.js',
                    language: 'ja'
                }
            },
            myValue: this.value,
            tinymceFlag: 1
        }
    },
    created () {
        //合并自定义的配置
        // 处理多语言 暂时支持中文/英语/日语，其他需要自行下载添加
        this.setCustomFont()
        this.setLang()
        this.init = { ...this.init, ...this.coustomInit }
    },
    mounted () {
        let clientHeight = document.documentElement.clientHeight
        this.init.height = Math.ceil(clientHeight / 2)
        console.log('this.init.height :>> ', this.init.height)
        this.showEditor = true
        this.initEditor()
    },
    destroyed () {
        this.removeEditor()
    },
    methods: {
        // async loadFont () {
        //     const font = new FontFace('YCTEST', `url(${window.origin}/opt/upFiles/files/307000/20220921/simkai_1663747773254.ttf)`)
        //     await font.load()
        //     document.getElementById('myEditor_ifr').contentWindow.document.fonts.add(font)
        // },

        //加入自定义字体
        setCustomFont (){
            let fonts=document.fonts
            fonts.forEach((v)=>{
             
                this.init.font_formats+=`${v.family}=${v.family};`
                

            }) 
        },
        setLang () {
            const defaultLang = this.$ls.get(DEFAULT_LANG) || 'zh'
            if (this.languageMap[defaultLang]) {
                this.init = { ...this.init, ...this.languageMap[defaultLang] }
            }
            // 处理默认en
            if (defaultLang == 'en') {
                delete this.init.language_url
                delete this.init.language
            }
        },
        // 插入内容
        insertContent (content) {
            tinymce.editors[this.id].insertContent(`${content}`)
        },
        initEditor () {
            tinymce.init({})
        },
        // 销毁实例
        removeEditor () {
            tinymce.execCommand('mceRemoveControl', true, this.id)
        },
        onClick (e) {
            this.$emit('onClick', e, tinymce)
        },
        //可以添加一些自己的自定义事件，如清空内容
        clear () {
            this.myValue = ''
        }
    },
    watch: {
        value (newValue) {
            this.myValue = newValue == null ? '' : newValue
        },
        myValue (newValue) {
            if (this.triggerChange) {
                this.$emit('change', newValue)
            } else {
                this.$emit('input', newValue)
            }
        }
    }
}
</script>
<style scoped></style>
