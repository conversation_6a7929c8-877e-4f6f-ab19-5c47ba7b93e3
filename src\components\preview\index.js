import Vue from 'vue'
import Preview from './src/main.vue'

const PreviewBox = Vue.extend(Preview)
let instance
export default {
    open (options = {}) {
        let {params, path } = options
        if (!instance) {
            instance = new PreviewBox({
                el: document.createElement('div')
            })
        }
        if (!instance.visible) {
            document.body.appendChild(instance.$el)
            Vue.nextTick(() => {
                instance.getUrl(params, path)
            })
        }
    }
    // close() {
    //   if (instance) {
    //     instance.visible = false
    //   }
    // }
}