<template>
  <div
    class="editFormLayout"
    :style="minHeightStyle">
    <a-form-model
      :ref="group.groupCode"
      :model="group.formModel" 
      :rules="group.validateRules"
      class="ant-advanced-rule-form"
      :layout="layout"
      v-bind="formModelConfig">
      <!-- 过滤隐藏域展示 -->
      <template v-for="(field, fieldIndex) in group.formFields">
        <a-form-model-item
          style="min-height: 300px;"
          :key="'col_' + group.groupCode + '_' + fieldIndex"
          v-if="group.formFields.length===1 && (field.fieldType === 'input' || field.fieldType === 'password' || field.fieldType === 'textArea')"
          :prop="field.fieldName">
          <span slot="label">
            {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
            <a-tooltip
              v-if="field.helpText"
              :title="field.helpText">
              <a-icon type="question-circle-o" />
            </a-tooltip>
          </span>
          <a-input
            v-bind="field.extend"
            :disabled="field.disabled"
            :type="field.fieldType === 'input'?'text': field.fieldType === 'password'?'password':'textarea'"
            @change="changeInputValue(group.formModel[field.fieldName], field)"
            v-model="group.formModel[field.fieldName]"
            :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`" />
        </a-form-model-item>
      </template>
      <a-row
        :getterr="12"
        v-if="group.formFields.length>1">
        <a-col
          v-for="(field, fieldIndex) in group.formFields"
          v-show="!field.hide"
          :key="'col_' + group.groupCode + '_' + fieldIndex"
          :class="{ textAreaClass: onlyRowFieldTypes.includes(field.fieldType), 'required-field': field.required === '1' }"
          :span="setColSpan(field)">
          <a-form-model-item
            v-if="field.fieldType === 'input' || field.fieldType === 'password' || field.fieldType === 'textArea'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <a-input
              v-bind="field.extend"
              :disabled="field.disabled"
              :title="field.disabled && field.fieldType == 'input'?group.formModel[field.fieldName]:''"
              :type="field.fieldType === 'input'?'text': field.fieldType === 'password'?'password':'textarea'"
              @change="changeInputValue(group.formModel[field.fieldName], field)"
              v-model="group.formModel[field.fieldName]"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`" />
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType == 'select'"
            :prop="field.fieldName">
            <span slot="label">
              <a-tooltip
                :title="$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)">
                {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              </a-tooltip>  
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-select
              :disabled="field.disabled"
              :configData="field"
              :getPopupContainer="triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
              @change="changeSelectValue"
              :options="field.options || []"
              v-model="group.formModel[field.fieldName]"
              :current-edit-row="currentEditRow"
              :filterSelectList="field.filterSelectList"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`"
              :dict-code="field.dictCode" />
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType == 'multiple'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <template v-if="field.disabled">
              <a-tooltip
                :title="group.formModel[field.fieldName + '_dictText'] || group.formModel[field.fieldName]">
                <span class="ant-form-item-children">
                  <span
                    class="ant-input ant-input-disabled"
                    style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                    {{ group.formModel[field.fieldName + '_dictText'] || group.formModel[field.fieldName] }}
                  </span>
                </span>
              </a-tooltip>
            </template>
            <template v-else>
              <m-select
                mode="multiple"
                :configData="field"
                :maxTagCount="field.extend && field.extend.maxTagCount || 1"
                v-model="group.formModel[field.fieldName]"
                :current-edit-row="currentEditRow"
                :options="field.options"
                :filterSelectList="field.filterSelectList"
                :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`"
                :dict-code="field.dictCode"
                @change="changeSelectValue"
              />
            </template>
          </a-form-model-item>
          <a-form-model-item
            v-if="field.fieldType == 'cascader'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-cascader
              change-on-select
              v-model="group.formModel[field.fieldName]"
              :mode="field.dictCode"
              :disabled="field.disabled"
              :options="field.options"
              @change="changeCascaderValue(arguments, field)"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`"
            />
          </a-form-model-item>
          <a-form-model-item
            v-if="field.fieldType == 'number'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <a-input-number
              style="width:100%"
              :disabled="field.disabled"
              v-bind="field.extend || {}"
              @change="changeInputValue(group.formModel[field.fieldName], field)"
              v-model="group.formModel[field.fieldName]"
              @blur="() => inputNumberBlurMethod({value: group.formModel[field.fieldName], fieldLabel:field.fieldLabel, type: 'form', fieldType: field.fieldType, form: group.formModel, item: field})"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`" />
          </a-form-model-item>
          <a-form-model-item
            v-if="field.fieldType == 'currency'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-currency
              v-model="group.formModel[field.fieldName]"
              :configData="field"
              :disabled="field.disabled"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`"
              @change="changeInputValue(group.formModel[field.fieldName], field)"
            />
          </a-form-model-item>
          <a-form-model-item
            v-if="field.fieldType == 'float'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-float
              v-model="group.formModel[field.fieldName]"
              :configData="field"
              :disabled="field.disabled"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`"
              @change="changeInputValue(group.formModel[field.fieldName], field)"
            />
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType == 'date'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <a-date-picker
              style="width:100%"
              :disabled="field.disabled"
              @change="changeInputValue(group.formModel[field.fieldName], field)"
              :show-time="field.dataFormat && field.dataFormat.length > 10 ? true : false"
              :valueFormat="(field.dataFormat || 'YYYY-MM-DD')"
              v-model="group.formModel[field.fieldName]"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`" />
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType == 'switch'"
            :prop="field.fieldName">
            <span slot="label">
              <a-tooltip
                :title="$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)">
                {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              </a-tooltip>
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-switch
              :disabled="field.disabled"
              :configData="field"
              @change="changeSelectValue"
              v-model="group.formModel[field.fieldName]" />
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType == 'treeSelect'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-tree-select 
              v-model="group.formModel[field.fieldName]"
              allowClear
              :disabled="field.disabled"
              :multiple="field.extend && field.extend.multiple || false"
              :maxTagCount="field.extend && field.extend.maxTagCount || 1"
              :sourceUrl="field.dictCode"
              :sourceMap="field.sourceMap"
              :valueMap="field.valueMap"
              :titleMap="field.titleMap"
              :showEmptyNode="field.showEmptyNode"
              :placeholder="field.placeholder"
              :configData="field"
              :getPopupContainer="(node) => node.parentNode || document.body"
              @afterClearCallBack="handleTreeSelectAfterClear"
              @change="changeSelectValue"
            />
          </a-form-model-item>
          <a-form-model-item
            v-if="field.type==='datepicker'"
            :prop="field.fieldName"
            :label="field.label">
            <a-date-picker
              v-model="pageData.form[field.fieldName]"
              :placeholder="field.placeholder"
              :valueFormat="(field.dataFormat || 'YYYY-MM-DD')"
              @change="changeInputValue(pageData.form[field.fieldName], field)">
            </a-date-picker>
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType=='remoteSelect'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-remote-select
              v-model="group.formModel[field.fieldName]"
              :config="field"
              :disabled="field.disabled"
              :pageData="pageConfig"
              :form="group.formModel"
              :currentStep="currentStep"   
              :getPopupContainer="triggerNode => {
                return triggerNode.parentNode || document.body;
              }"
              @afterClearCallBack="(cb)=> handleSelectModalAfterClear(group.formModel, pageConfig, field?.extend?.afterClearCallBack)"
              @ok="(rows) => handleSelectModalAfterSelect(field, rows)"
            />
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType=='selectModal'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-select-modal
              v-model="group.formModel[field.fieldName]"
              :config="field"
              :pageData="pageConfig"
              :form="group.formModel"
              :currentStep="currentStep"
              @afterClearCallBack="(cb)=> handleSelectModalAfterClear(group.formModel, pageConfig, cb)"
              @ok="(rows) => handleSelectModalAfterSelect(field, rows)"
            />
          </a-form-model-item>
          <!-- 自定义弹窗选择，抛出事件在自己的业务下编写自己业务需要的弹窗 -->
          <a-form-model-item
            v-else-if="field.fieldType=='customSelectModal'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <div @click="customSelectModel(field)">
              <a-input 
                v-model="group.formModel[field.fieldName]"
                :disabled="field.disabled"
                readOnly
                allowClear />
            </div>
            
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType === 'richEditorModel'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <rich-editor-model
              :value="group.formModel[field.fieldName]"
              :disabled="field.disabled"
              @handleSureClick="(content)=> {group.formModel[field.fieldName] = content}"></rich-editor-model>
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType === 'image'"
            class="image-class"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <m-upload
              :value.sync="group.formModel[field.fieldName]"
              :accept="accept"
              :multiple="field.extend && field.extend.multiple"
              :limit="field.extend && field.extend.limit"
              :disabled="field.disabled"
              :headers="tokenHeader"
              :data="{ businessType: field.dictCode, headId: group.formModel.id}"
            > 
            </m-upload>
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType == 'codeEditorModel'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <code-editor-model
              :disabled="field.disabled"
              style="width:100%"
              v-bind="field.extend || {}"
              v-model="group.formModel[field.fieldName]"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`"
              @handleSureClick="(content)=> {group.formModel[field.fieldName] = content}"></code-editor-model>
          </a-form-model-item>
          <a-form-model-item
            v-if="field.fieldType == 'hiddenField'"
            :prop="field.fieldName">
            <a-input
              v-bind="field.extend"
              :disabled="field.disabled"
              type="hidden"
              @change="changeInputValue(group.formModel[field.fieldName], field)"
              v-model="group.formModel[field.fieldName]"
              :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`" />
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType == 'ladderPrice'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <ladder-price
              v-model="group.formModel[field.fieldName]"
              :config="field"
              :pageData="pageConfig"
              :form="group.formModel"
              @afterClearCallBack="(cb)=> { handleLadderPriceAfterClear(group.formModel, pageConfig, cb)}"
              @ok="(rows) => handleLadderPriceAfterSelect(field, rows)"
            />
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType === 'color'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <div class="field-color-box">
              <a-input
                class="color-input"
                :disabled="field.disabled"
                :style="{color: group.formModel[field.fieldName]}"
                :type="'input'"
                @change="changeInputValue(group.formModel[field.fieldName], field)"
                v-model="group.formModel[field.fieldName]"
                :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel)}`" />
              <a-input
                type="color"
                class="color-modal"
                :disabled="field.disabled"
                @change="changeInputValue(group.formModel[field.fieldName], field)"
                v-model="group.formModel[field.fieldName]" />
            </div>
          </a-form-model-item>
          <a-form-model-item
            v-else-if="field.fieldType === 'icon'"
            :prop="field.fieldName">
            <span slot="label">
              {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
              <a-tooltip
                v-if="field.helpText"
                :title="field.helpText">
                <a-icon type="question-circle-o" />
              </a-tooltip>
            </span>
            <IconModel
              v-model="group.formModel[field.fieldName]"
              :disabled="field.disabled"></IconModel>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row
        :getterr="12"
        v-if="group.total.totalValue">
        <div class="summary-message">
          <span class="summary-message-content">
            {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }} {{ $srmI18n(`${$getLangAccount()}#${group.i18n_title_generalSummary}`, '总汇总') }}：<span class="total-num">{{ group.total.totalValue }}</span></span>
        </div>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
import RichEditorModel from '@comp/richEditorModel/RichEditorModel'
import MUpload from '@comp/mUpload'
import MTreeSelect from '@comp/treeSelect/mTreeSelect'
import {bindfunctionMiddleware} from '@/utils/util'
import { AFTER_CLEAR_CALLBACK, PRIVATE_BIND_FUNCTION, PRIVATE_AFTER_CLEAR_CALLBACK } from '@/utils/constant.js'
import { inputNumberBlurMethod } from '@/utils/util.js'
import LadderPrice from '@comp/LadderPrice'
import IconModel from '@comp/IconModel'
export default {
    name: 'EditFormLayout',
    components: {
        MUpload,
        RichEditorModel,
        MTreeSelect,
        LadderPrice,
        IconModel
    },
    inject: ['tplRootRef'],
    props: {
        // 传入归属方busAccount
        busAccount: {
            required: true,
            type: String,
            default: ''
        },
        currentStep: {
            type: [String, Number]
        },
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        group: {
            type: Object,
            required: true,
            default () {
                return {}
            }
        },
        pageConfig: {
            type: Object,
            default () {
                return {}
            }
        },
        gridConfig: {
            type: Object,
            default () {
                return {}
            }
        },
        displayModel: {
            type: String,
            default: 'tab'
        }
    },
    data () {
        return {
            accept: '.png, .jpg, .jpeg, .gif',
            onlyRowFieldTypes: ['textArea', 'image'], // 表头独占一行
            //附件上传配置
            tokenHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            iconChooseVisible: false,
            currentFieldName: ''
        }
    },
    computed: {
        // form 表单布局
        layout () {
            if (this.group.formFields.length && this.group.formFields.length > 1) {
                return 'inline'
            } else {
                return 'vertical'
            }
        },
        // form表单默认配置
        formModelConfig () {
            if (this.group.formFields.length && this.group.formFields.length > 1) {
                return {
                    labelCol: { span: 9 },
                    wrapperCol: { span: 15 }
                }
            } else {
                return {
                    labelCol: { span: 3 },
                    wrapperCol: { span: 21 }
                }
            }
        },
        minHeightStyle () {
            if (this.displayModel === 'tab') {
                return { minHeight: `${this.gridConfig.height}px` }
            }
            return {}
        }
    },
    methods: {
        inputNumberBlurMethod,
        //自定义seleclModel
        customSelectModel (field){
            field.extend && field.extend.modalColumns.forEach(col=>{
                if(col.fieldLabelI18nKey){
                    col.title = this.$srmI18n(`${this.$getLangAccount()}#${col.fieldLabelI18nKey}`, col.title)
                }
            })
            this.$emit('customSelect', field)
        },
        setColSpan (item) {
            return this.onlyRowFieldTypes.includes(item.fieldType) ? 24 : 8
        },
        // selectModal 清除
        handleSelectModalAfterClear (formModel, pageConfig, cb) {
            if (cb && typeof cb === 'function') {
                if (cb.name === PRIVATE_AFTER_CLEAR_CALLBACK) {
                    let params = {
                        _pageData: pageConfig, // 页面所有数据
                        _cacheAllData: this.tplRootRef.getAllData(),
                        _form: formModel
                    }
                    bindfunctionMiddleware(this.tplRootRef, cb, params)
                } else {
                    cb(this, formModel, pageConfig, this.tplRootRef)
                }
            }
        },
        // selectModal 确认时
        handleSelectModalAfterSelect (item, rows) {
            if (item.bindFunction.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
                let params = {
                    _pageData: this.pageConfig, // 页面所有数据
                    _form: this.group.formModel,
                    _cacheAllData: this.tplRootRef.getAllData(),
                    _data: rows
                }
                bindfunctionMiddleware(this.tplRootRef, item.bindFunction, params)
            } else {
                item.bindFunction && item.bindFunction.call(null, this, rows, this.tplRootRef)
            }
        },
        // 树形结构 清除
        handleTreeSelectAfterClear (cb){
            cb && cb(this.group.formModel, this.pageConfig)
        },
        // 阶梯价格 清除
        handleLadderPriceAfterClear (formModel, pageConfig, cb) {
            if (cb && typeof cb === 'function') {
                if (cb.name.indexOf(PRIVATE_AFTER_CLEAR_CALLBACK) !== -1) {
                    let params = {
                        _pageData: pageConfig, // 页面所有数据
                        _cacheAllData: this.tplRootRef.getAllData(),
                        _form: formModel
                    }
                    bindfunctionMiddleware(this.tplRootRef, cb, params)
                } else {
                    cb(this, formModel, pageConfig, this.tplRootRef)
                }
            }
        },
        // 阶梯价格 确认时
        handleLadderPriceAfterSelect (item, rows) {
            if (item.bindFunction.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
                let params = {
                    _pageData: this.pageConfig, // 页面所有数据
                    _form: this.group.formModel,
                    _cacheAllData: this.tplRootRef.getAllData(),
                    _data: rows
                }
                bindfunctionMiddleware(this.tplRootRef, item.bindFunction, params)
            } else {
                item.bindFunction && item.bindFunction.call(null, this, rows, this.tplRootRef)
            }
        },
        // input 改变事件,value-当前值
        changeInputValue (value, item) {
            let parentRef = null
            let groupData = null
            if (this.pageConfig.groups[this.currentStep]) {
                let parentRefName = this.pageConfig.groups[this.currentStep].groupCode
                parentRef = this.$refs[parentRefName]
                groupData = this.pageConfig.groups[this.currentStep]
            }
            // let test = value?true: false
            // groupData.custom.validateRules= Object.assign({}, groupData.custom.validateRules, {enquiryType: {required: test, message: '123455'}})
            if (item && item.bindFunction &&  typeof item.bindFunction === 'function') {
                if (item.bindFunction.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
                    let params = {
                        _pageData: this.pageConfig, // 页面所有数据
                        _form: this.group.formModel,
                        _cacheAllData: this.tplRootRef.getAllData(),
                        _value: value
                    }
                    bindfunctionMiddleware(this.tplRootRef, item.bindFunction, params)
                } else {
                    item.bindFunction(this.$parent.$parent.$parent, parentRef, this.pageConfig, groupData, value, item, this.group.formModel, '', this.tplRootRef)
                }
            }
        },
        // 级联选择事件
        changeCascaderValue (args, item) {
            const value = args[0]
            const selectedOptions = args[1]
            let parentRef = null
            let groupData = null
            if (this.pageConfig.groups[this.currentStep]) {
                let parentRefName = this.pageConfig.groups[this.currentStep].groupCode
                parentRef = this.$refs[parentRefName]
                groupData = this.pageConfig.groups[this.currentStep]
            }
            if (item && item.bindFunction &&  typeof item.bindFunction === 'function') {
                let params = {
                    _pageData: this.pageConfig, // 页面所有数据
                    _form: this.group.formModel,
                    _cacheAllData: this.tplRootRef.getAllData(),
                    _value: value,
                    _selectedOptions: selectedOptions
                }
                bindfunctionMiddleware(this.tplRootRef, item.bindFunction, params)
            }
        },

        // select 改变事件
        changeSelectValue (realValue, opt, oldVal, configData) {
            let parentRef = null
            let groupData = null
            if (this.pageConfig.groups[this.currentStep]) {
                let parentRefName = this.pageConfig.groups[this.currentStep].groupCode
                parentRef = this.$refs[parentRefName]
                groupData = this.pageConfig.groups[this.currentStep]
            }
            if (configData && configData.bindFunction && typeof configData.bindFunction==='function') {
                if (configData.bindFunction.name.indexOf(PRIVATE_BIND_FUNCTION) !== -1) {
                    let params = {
                        _pageData: this.pageConfig, // 页面所有数据
                        _form: this.group.formModel,
                        _cacheAllData: this.tplRootRef.getAllData(),
                        _value: realValue
                    }
                    bindfunctionMiddleware(this.tplRootRef, configData.bindFunction, params)
                } else {
                    configData.bindFunction(this.$parent.$parent.$parent, parentRef, this.pageConfig, groupData, realValue, opt, oldVal, this.group.formModel, this.tplRootRef)
                }
            }
        },
        // 超链接 跳转
        getNewRouter (value, item){
            let params = {
                _pageData: this.pageConfig, // 页面所有数据
                _cacheAllData: this.tplRootRef.getAllData(),
                _form: this.group.formModel,
                _value: value,
                _formField: item
            }
            let linkConfig = item?.extend?.linkConfig || {}
            if (item && item.bindFunction &&  typeof item.bindFunction === 'function') {
                bindfunctionMiddleware(this.tplRootRef, item.bindFunction, params)
            }
            if (item?.extend?.handleBefore && typeof item.extend.handleBefore === 'function') {
                let callbackObj = item.extend.handleBefore(this.tplRootRef, params, linkConfig) || {}
                linkConfig = { ...linkConfig, ...callbackObj }
            }
            if (value && linkConfig.actionPath && linkConfig.bindKey) {
                let query = {
                    [linkConfig.primaryKey]: this.group.formModel[linkConfig.bindKey],
                    ...linkConfig.otherQuery,
                    t: new Date().getTime()
                }
                this.$router.push({ path: linkConfig.actionPath.trim(), query: { ...query } })
            }
        }
    }
}
</script>
<style lang="less" scoped>
.summary-message {
  height: 14px;
  display: flex;
  align-items: center;
  margin: 10px 0;
}
.summary-message-content {
  flex-grow: 1;
  font-weight: bolder;
  .total-num {
    font-size: 16px;
    color: red;
  }
}
:deep(.description .ant-descriptions-bordered .ant-descriptions-item-label){
  background: #F5F6F7;
}
:deep(.description .ant-descriptions-bordered .ant-descriptions-item-label){
  width: 20%;
	max-width: 20%;
}
:deep(.description .ant-descriptions-bordered .ant-descriptions-item-content){
	width: 30%;
	max-width: 30%;
}
:deep(.textAreaClass){
  .ant-form-item-label{
    width: 12%;
  }
  .ant-form-item-control-wrapper{
    width: 88%;
  }
  .image-class{
    height: 112px !important; // 权重不够，使用!important提高下权重
    line-height: 112px !important; 
}
}
.required-field {
    :deep(.ant-form-item-control){
        >input, .ant-select-selection {
            background-color: #fff9f7;
            border: 1px solid #fdaf96;
            border-radius: 4px;
        }
        
    }
}
.field-color-box {
    position: relative;
    font-weight: 700;
    .color-input {
        width: 86%;
    }
    .color-modal {
        position: absolute;
        width: 14%;
        padding: 0;
        height: 36px;
        margin: 2px;        
        border: 0;
        cursor:pointer;
    }
}
.color-value-box {
    line-height: 32px;
    font-weight: 700;
}
</style>
