<template>
  <vue-echarts
    :style="style"
    :option="widget.option"
    :update-options="{ notMerge: true }"
    autoresize
  />
</template>
<script>
import { chartsMixins } from "@comp/chart/widget/mixins/chartsMixins";
import _ from "lodash";
export default {
  name: "chartsKLine",
  mixins: [chartsMixins],
  computed: {
    option() {
      return {
        barWidth: this.widget.option.series[0].barWidth,
        itemStyle: this.widget.option.series[0].itemStyle,
      };
    },
  },
  watch: {
    option: {
      handler(val) {
        for (let i = 1; i < this.widget.option.series.length; i++) {
          this.widget.option.series[i].barWidth = val.barWidth;
          this.widget.option.series[i].itemStyle = _.cloneDeep(val.itemStyle);
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.refreshWidgetInfo();
  },
  methods: {},
};
</script>