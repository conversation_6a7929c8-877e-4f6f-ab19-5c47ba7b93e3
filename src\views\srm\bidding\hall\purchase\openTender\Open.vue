<template>
  <div class="open">
    <div
      class="container"
      :style="style">

      <a-spin :spinning="confirmLoading">
        <div class="boxWrap">
          <h3 class="h3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidCountdown`, '开标倒计时') }}</h3>

          <div class="count">
            <div class="countdown">
              <countdown 
                :time="deadline"
                :style="valueStyle"
                @end="handleFinish"
              >
                <template slot-scope="props">{{ props.days }} {{ $srmI18n(`${$getLangAccount()}#i18n_title_day`, '天') }} {{ props.hours }} {{ $srmI18n(`${$getLangAccount()}#i18n_title_time`, '时') }} {{ props.minutes }} {{ $srmI18n(`${$getLangAccount()}#i18n_title_branch`, '分') }} {{ props.seconds }} {{ $srmI18n(`${$getLangAccount()}#i18n_title_second`, '秒') }}</template>
              </countdown>
            </div>
            
          </div>
          <!-- <div class="time">
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_day`, '天') }}</span>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_time`, '时') }}</span>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_branch`, '分') }}</span>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_second`, '秒') }}</span>
          </div> -->
          <div class="info">
            <div class="row">
              <span class="tit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidOpenStatus`, '开标状态') }}</span>
              <strong>{{ biddingStatus_dictText }}</strong>
            </div>
            <!--            <div class="row">-->
            <!--              <span class="tit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_supervisor`, '监督人员') }}</span>-->
            <!--            </div>-->
            <div class="row">
              <span class="tit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidOpenTime`, '开标时间') }}</span>
              {{ planOpenBidTime }}
            </div>
            <div class="row">
              <span class="tit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidOpenPersonnel`, '开标人员') }}</span>
              <a-tooltip
                :title="`${names}`"
                placement="topLeft"
              >
                {{ names | ellipsis(8) }}
              </a-tooltip>
            </div>
          </div>
          <div class="btns">
            <a-button
              type="primary"
              v-if="$hasOptAuth('bidding#purchaseBiddingHead:openBiddingById')"
              :disabled="disabled"
              @click="handleOpen">
              {{ $srmI18n(`${$getLangAccount()}#i18n_title_bidOpenEnter`, '进入开标') }}
            </a-button>
          </div>
        </div>
      </a-spin>

    </div>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import countdown from '@/components/countdown'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    components: {
        countdown
    },
    data () {
        return {
            confirmLoading: false,
            valueStyle: {
                width: '100%',
                height: '78px',
                fontSize: '46px',
                fontWeight: 700,
                color: '#727272',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                letterSpacing: '5px'
            },
            showHeader: false,
            height: 0,
            planOpenBidTime: '',
            planOpenBidTime_DateMaps: '',
            names: '',
            biddingStatus_dictText: '',
            biddingStatus: '',
            timeStamp: '',
            deadline: null
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        disabled () {
            return this.biddingStatus !== '1'
        }
    },
    filters: {
        ellipsis (value, count) {
            if (!value) return ''
            if (value.length > count) {
                return value.slice(0, count) + '...'
            }
            return value
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        handleOpen () {
            this.confirmLoading = true
            this.getDetailPromise()
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    let timestamp = res.timestamp
                    let deadline = this.planOpenBidTime_DateMaps
                    this.timestamp = timestamp
                    if (timestamp <= deadline) {
                        let txt = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_LufvBKI_a5419e3d`, '未到达开标时间')
                        this.$message.error(`${txt} ${this.planOpenBidTime}`)
                        return
                    }

                    const callback = () => {
                        const { id = '' } = this.vuex_currentEditRow || {}
                        const params = {
                            id
                        }
                        const url = '/bidding/purchaseBiddingHead/openBiddingById'
                        getAction(url, params)
                            .then(res => {
                                const type = res.success ? 'success' : 'error'
                                this.$message[type](res.message)
                                if (res.success) {
                                    this.updateVuexCurrentEditRow()
                                }
                                this.init()
                            })
                    }
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_vB_be907`, '开标'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_openBidStatus`, '是否开标'),
                        onOk () {
                            callback && callback()
                        },
                        onCancel () {
                            console.log('Cancel')
                        }
                    })

                }).finally(() => {
                    this.confirmLoading = false
                })
        },
        handleFinish () {
            console.log('finish')
            // this.init()
        },
        getDetailPromise () {
            const { id = '' } = this.vuex_currentEditRow || {}
            const url = '/bidding/purchaseBiddingHead/queryById'
            return getAction(url, { id })
        },
        getMemberData () {
            this.confirmLoading = true
            const url = '/bidding/purchaseBiddingHead/queryById'
            const { id = '' } = this.vuex_currentEditRow || {}
            const params = { id }
            getAction(url, params)
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { purchaseBiddingSpecialistList = [] } = res.result || {}
                    const memberArr = purchaseBiddingSpecialistList.filter(n => n.memberType === '2')
                    this.names = memberArr.map(n => n.name).join(',')
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        init () {
            this.confirmLoading = true
            this.getDetailPromise()
                .then(res => {
                    if (!res.success) {
                        this.$message.error(res.message)
                        return
                    }
                    const { timestamp = '', result = {} } = res || {}
                    this.timestamp = timestamp
                    this.biddingStatus = result.biddingStatus
                    this.planOpenBidTime = result.planOpenBidTime
                    this.biddingStatus_dictText = result.biddingStatus_dictText
                    this.planOpenBidTime_DateMaps = result.planOpenBidTime_DateMaps
                    let planOpenBidTime = this.planOpenBidTime ? this.planOpenBidTime_DateMaps : Date.now()
                    if (this.timestamp < planOpenBidTime) {
                        this.deadline = planOpenBidTime - this.timestamp
                    }
                    console.log('deadline', this.deadline)
                    this.getMemberData()
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        }
    },
    created () {
        this.height = document.documentElement.clientHeight
        this.init()
    }
}
</script>

<style lang="less" scoped>
.open {
	.posA {
		& + .container {
			margin-top: 44px;
		}
	}
	.container {
		margin-left: -8px;
		margin-top: -8px;
		padding: 12px;
		background: #e9e9e9;
		text-align: center;
		font-weight: 400;
		font-size: 14px;
		.boxWrap {
			margin-top: 80px;
		}
	}
	.h3 {
		font-size: 24px;
		color: #3d3d3d;
	}
	// .count,
	// .time {
	// 	width: 454px;
	// }
	.count {
        display: inline-block;
        padding: 0 10px;
		margin: 0 auto;
		border: 1px solid #000;
		border-radius: 8px;
		height: 78px;
	}
	.time {
		display: flex;
		justify-content: space-between;
		margin: 14px auto;
		padding: 0 36px;
	}
	.info {
		line-height: 2.4;
		.tit {
			&::after {
				margin-right: 8px;
				content: ":";
			}
		}
	}
	.btns {
		margin-top: 14px;
	}
}
</style>
