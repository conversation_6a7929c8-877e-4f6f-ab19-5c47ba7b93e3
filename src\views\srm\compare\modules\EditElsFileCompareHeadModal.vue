<template>
  <div class="ElsFileCompareHead business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="edit"
      modelLayout="collapse"
      :currentEditRow="currentEditRow"
      :remoteJsFilePath="remoteJsFilePath"
      :requestData="requestData"
      :externalToolBar="externalToolBar"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler"/>
  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {BUTTON_BACK, BUTTON_SAVE} from '@/utils/constant.js'
import {postAction} from '@/api/manage'

export default {
    name: 'EditElsFileCompareHeadModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            requestData: {
                detail: {
                    url: '/compare/elsFileCompareHead/queryById', args: (that) => {
                        return {id: that.currentEditRow.id}
                    }
                }
            },
            externalToolBar: {},
            pageHeaderButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/compare/elsFileCompareHead/edit'
                    },
                    authorityCode: 'compare#elsFileCompareHead:edit',
                    handleBefore: this.handleSaveBefore,
                    isFlattenHeadTypeForm: true // 是否展开 groupType 为 head 的分组合并至提交接口中
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#`, 'A文件上传'),
                    key: 'upload',
                    args: {
                        property: 'label', // 可省略
                        itemInfo: [], // 必传
                        action: '/attachment/purchaseAttachment/upload', // 必传
                        businessType: 'fileCompare', // 必传,
                        itemNumbeValueProp: 'value',
                        itemNumberLabel: '关联tab',
                        fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                        headId: '1', // 必传
                        accept: '.pdf,.doc,.docx',
                        useLocalAccept: true,
                        modalVisible: false // 必传
                    },
                    single: true,
                    attr: this.attrHandle,
                    callBack: this.uploadCallBackA
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#`, 'B文件上传'),
                    key: 'upload',
                    args: {
                        property: 'label', // 可省略
                        itemInfo: [], // 必传
                        action: '/attachment/purchaseAttachment/upload', // 必传
                        businessType: 'fileCompare', // 必传,
                        itemNumbeValueProp: 'value',
                        itemNumberLabel: '关联tab',
                        fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                        headId: '1', // 必传
                        accept: '.pdf,.doc,.docx',
                        useLocalAccept: true,
                        modalVisible: false // 必传
                    },
                    single: true,
                    attr: this.attrHandle,
                    callBack: this.uploadCallBackB
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '预览A文件'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.previewFileA
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '预览B文件'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.previewFileB
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '文档对比'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.fileCompare
                },
                BUTTON_BACK
            ],
            url: {
                save: '/compare/elsFileCompareHead/edit',
                detail: '/compare/elsFileCompareHead/queryById'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_fileCompare_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        preViewEvent (row) {
            this.$previewFile.open({params: {}, path: row.filePath})
        },
        preDowloadEvent (row) {
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = row.filePath
            link.setAttribute('download', row.fileName)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(row.filePath) //释放掉blob对象
        },
        fileCompare () {
            let pageData = this.getAllData() || {}
            if (!pageData.fileA.fileASourceId || !pageData.fileB.fileBSourceId) return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseUploadFile`, '请上传文件'))
            if (!pageData.id) return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
            this.$refs.businessRef.confirmLoading = true
            postAction('/compare/elsFileCompareHead/fileCompare', pageData).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.fileCompareCallBack(pageData)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.businessRef.confirmLoading = false
            })
        },
        previewFileA () {
            debugger
            let pageData = this.getAllData() || {}
            let preViewFile = {id: pageData.fileASourceId, filePath: pageData.fileAPath}
            this.$previewFile.open({params: preViewFile})
        },
        previewFileB () {
            let pageData = this.getAllData() || {}
            let preViewFile = {id: pageData.fileBSourceId, filePath: pageData.fileBPath}
            this.$previewFile.open({params: preViewFile})
        },
        uploadCallBackA (result) {
            debugger
            this.getAllData().fileA.fileASourceId = result[0].id,
            this.getAllData().fileA.fileABusinessType = result[0].businessType,
            this.getAllData().fileA.fileAType = result[0].fileType,
            this.getAllData().fileA.fileAUploadElsAccount = result[0].uploadElsAccount,
            this.getAllData().fileA.fileAUploadSubAccount = result[0].uploadSubAccount,
            this.getAllData().fileA.fileAUploadTime = result[0].uploadTime,
            this.getAllData().fileA.fileAName = result[0].fileName,
            this.getAllData().fileA.fileAPath = result[0].filePath,
            this.getAllData().fileA.fileASize = result[0].fileSize

        },
        uploadCallBackB (result) {
            this.getAllData().fileB.fileBSourceId = result[0].id,
            this.getAllData().fileB.fileBBusinessType = result[0].businessType,
            this.getAllData().fileB.fileBType = result[0].fileType,
            this.getAllData().fileB.fileBUploadElsAccount = result[0].uploadElsAccount,
            this.getAllData().fileB.fileBUploadSubAccount = result[0].uploadSubAccount,
            this.getAllData().fileB.fileBUploadTime = result[0].uploadTime,
            this.getAllData().fileB.fileBName = result[0].fileName,
            this.getAllData().fileB.fileBPath = result[0].filePath,
            this.getAllData().fileB.ileBSize = result[0].fileSize
        },
        handleBeforeRemoteConfigData () {
            return {
                itemColumns: [
                    {
                        groupCode: 'elsFileCompareResultList',
                        title: '操作',
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: 'sourceType_dictText',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '75',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#`, '预览')}
                                    onClick={() => this.preViewEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#`, '预览')}</a>)
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}
                                    onClick={() => this.preDowloadEvent(row)}> {this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}</a>)
                                return resultArray
                            }
                        }
                    }
                ]
            }
        }
    }
}
</script>
