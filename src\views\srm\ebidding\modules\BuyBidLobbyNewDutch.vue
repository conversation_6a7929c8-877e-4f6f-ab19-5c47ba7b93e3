<template>
  <!-- 2022-03-08 新版本采购竞价大厅 日荷《QQT SRM V5-PRD-竞价管理优化方案-v2.1-********》 -->
  <div class="buyBid">
    <a-spin :spinning="spinning">
      <div class="container">
        <div class="top">
          <div class="breadcrumb">
          </div>
          <div class="menu">
            <a-button
              type="primary"
              v-if="showStartBid"
              :disabled="!activeStartBid"
              @click="startOrEndBid(true)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_openBiding`, '开启竞价') }}</a-button>
            <a-button
              type="primary"
              v-if="showEndBid"
              :disabled="startEndDisabled"
              @click="startOrEndBid(false)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_overBiding`, '结束竞价') }}</a-button>
            <a-button
              type="primary"
              @click="refresh(true)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_refresh`, '刷新') }}</a-button>
            <a-button
              type="primary"
              @click="windowOpenClose">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
          </div>
        </div>
        <div class="content">
          <div class="gutter">
            <div class="item price">
              <div class="info">
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_field_OuyO_391539d8`, '竞价阶段') }}</b>
                  <b class="value">{{ getCurrentTitle }}</b>
                </span>
                <span class="inline">
                  <b class="value">{{ getCurrentMaterialStatus }}</b>
                  <!-- form.ebiddingStatus_dictText -->
                </span>
                 
                <span
                  v-if="form.ebiddingStatus === '1'||form.ebiddingStatus === '3' || form.ebiddingStatus === '4'"
                  class="inline"
                  style="float: right;display: flex;align-items: center;justify-content: center;">
                  <b class="label">{{ countdownText }}</b>
                  <b class="value red">
                    <span
                      class="icon">
                      <a-icon
                        type="clock-circle"
                        theme="outlined" 
                        :style="{ fontSize: '22px', color: '#f41616', marginRight: '8px' }"
                      />
                    </span>
                    <countdown
                      :time="deadline"
                      v-if="deadline"
                      :style="valueStyle"
                      @end="handleFinish"
                    >
                      <template slot-scope="props">{{ props.days ? `${props.days} ${$srmI18n(`${$getLangAccount()}#i18n_dict_S_5929`, '天')} ` : '' }}{{ props.hours }} : {{ props.minutes }} : {{ props.seconds }}</template>
                    </countdown>
                    <span v-else>00:00:00</span>
                  </b>
                </span>
                <span
                  class="inline"
                  style="float: right;"><b class="remaining">实时剩余数量: <b>{{ currentRemainingQuantity }}</b>{{ remainUnit }}</b></span>
              </div>
            </div>
          </div>
          <div class="gutter">
            <div class="item price ">
              <div class="info">
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号') }}</b>
                  <b class="value m-l-24">{{ form.ebiddingNumber }}</b>
                  <b
                    class="value inline-flot"
                    @click="changeToggle">{{ getToggleStatu }} <a-icon :type="resizeHeight==0?'down':'up'" /></b>
                </span>
                  
                <a-descriptions
                  bordered
                  v-if="resizeHeight==0"
                  class="m-t-12"
                  id="description"
                  size="small">
                  <!-- :column="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }" -->
                  <a-descriptions-item
                    v-for="item in baseInfo"
                    :key="item.key"
                    :label="item.label"
                    :span="item.span || 1">
                    {{ form[item.key] }}
                  </a-descriptions-item>
                </a-descriptions>
              </div>
            </div>
          </div>
          <div class="gutter">
            <multipane
              class="custom-resizer"
              layout="vertical"
              @paneResizeStop="reSizeChart"
            >
              <div class="item material">
                <div class="info">
                  <a-card
                    :title="$srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息')"
                    :bordered="false"
                    :headStyle="headStyle"
                    :bodyStyle="bodyStyle_2"
                  >
                    <div class="cardContent">
                      <div class="table">
                        <vxe-grid
                          ref="materialGrid"
                          :height="430+resizeHeight"
                          show-overflow
                          v-bind="materialGridOptions"
                          :row-class-name="handleRowClass"
                          @cell-click="cellMaterialClickEvent"
                        >
                          <template slot="empty">
                            <a-empty />
                          </template>
                        </vxe-grid>
                      </div>
                    </div>
                  </a-card>
                </div>
              </div>
              <multipane-resizer></multipane-resizer>
              <div class="item supplier">
                <div class="info">
                  <a-card
                    :title="$srmI18n(`${$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息')"
                    :bordered="false"
                    :headStyle="headStyle"
                    :bodyStyle="bodyStyle_2"
                  >
                    <vxe-grid
                      ref="rankGrid"
                      :loading="loading"
                      :height="412+resizeHeight"
                      v-bind="rankGridOptions"
                      :row-class-name="handleRowSupplierClass"
                      @cell-click="cellSupplierClickEvent"
                    >
                      <template slot="empty">
                        <a-empty />
                      </template>
                      <template #online="{ row }">
                        <span>{{ suppliers.includes(row.toElsAccount) ? '在线' : '离线' }}</span>
                      </template>
                      <template #quoteRank="{ row }">
                        <a-tooltip
                          placement="topLeft"
                          v-if="row.quoteRank&&row.quoteRank>1">
                          <template slot="title">
                            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_import_RdXylmumttkKIlAR1RS_b87a1f23`, '供应商接受此价格，但操作时间比排名1滞后') }}</span>
                          </template>
                          <span>{{ row.quoteRank }}</span>
                        </a-tooltip>
                        <span v-else>{{ row.quoteRank }}</span>
                      </template>
                    </vxe-grid>
                    <!-- <a-tab-pane 
                      key="2"
                      :tab="$srmI18n(`${$getLangAccount()}#i18n_title_trendChartQuotation`, '报价趋势图')">
                      <div class="cardContent">
                        <div class="chartInfo">
                          <a-select
                            v-model="xasDeaultVal"
                            style="width: 120px"
                            @change="handleXasChange">
                            <a-select-option value="length">
                              {{ $srmI18n(`${$getLangAccount()}#i18n_field_quoteCount`, '报价次数') }}
                            </a-select-option>
                            <a-select-option value="quoteTime">
                              {{ $srmI18n(`${$getLangAccount()}#i18n_field_suKI_2e0cbb30`, '报价时间') }}
                            </a-select-option>
                                   
                          </a-select>
                          <div
                            class="m-t-12"
                            :class="['echart', isShowEchart ? '' : 'unShow']">
                            <line-chart
                              v-if="isShowEchart"
                              ref="echart"
                              :chartData="chartData">
                            </line-chart>
                            <div
                              v-if="!isShowEchart"
                              class="red">{{ $srmI18n(`${$getLangAccount()}#i18n_title_doesDownNotAllow`, '当前配置不允许查看供方报价信息') }}</div>
                          </div>
                        </div>
                      </div>
                    </a-tab-pane> -->
                  </a-card>
                </div>
              </div>
              <multipane-resizer></multipane-resizer>
              <div class="item quota">
                <div class="info">
                  <a-card
                    :title="$srmI18n(`${$getLangAccount()}#i18n_field_suGR_2e1137e2`, '报价设置')"
                    :bordered="false"
                    :headStyle="headStyle2"
                    :bodyStyle="bodyStyle_2"
                  >
                    <div class="cardContent">
                      <div class="table">
                        <!-- 打包 -->
                        <vxe-grid
                          v-if="form.ebiddingWay === '0'"
                          ref="quotaGrid"
                          :height="437+resizeHeight"
                          show-overflow
                          v-bind="quotaPackGridOptions"
                        >
                          <!-- v-on="quotaGridEvents" -->
                          <template slot="empty">
                            <a-empty />
                          </template>
                          <template #toolbar_buttons>
                            <vxe-button
                              v-if="currentSupplierAccount
                                && currentSupplierAccount !== -1
                                && form.purchaseAutoQuote != '1'"
                              @click="handleAddQuota"
                            >{{ $srmI18n(`${$getLangAccount()}#i18n_btn_exVa_27d46f87`, '同步新增') }}</vxe-button>
                          </template>
                          <template #price_header="{ column }">
                            <i class="vxe-icon--edit-outline"></i>
                            <span>{{ column.title }}</span>
                            <span class="red">*</span>
                          </template>
          
                          <template #totalAmount_default="{ row, rowIndex }">
                            <div>
                              <vxe-input
                                v-if="setQuoteInput(row)"
                                v-model="row.totalAmount"
                                type="number"
                                :step="priceStep(row.totalAmount)"
                                min="0"
                                clearable
                                :disabled="isPriceDisabled"
                                @change="handlePackBlur({ row, rowIndex })"
                              >
                              </vxe-input>
                              <span v-else>{{ row.totalAmount }}</span>
                            </div>
                          </template>
                          <template #bidNumber_default="{ row }">
                            <div>
                              <vxe-input
                                v-if="setQuoteInput(row)"
                                v-model="row.bidNumber"
                                :placeholder="投标数量"
                                :disabled="true"
                                type="number"
                              >
                              </vxe-input>
                              <span v-else>{{ row.bidNumber }}</span>
                            </div>
                          </template>
                          <template #netTotalAmount_default="{ row, rowIndex }">
                            <div>
                              <vxe-input
                                v-if="setQuoteInput(row)"
                                v-model="row.netTotalAmount"
                                type="number"
                                min="0"
                                clearable
                                :disabled="!isPriceDisabled"
                                @change="handleNetPackBlur({ row, rowIndex })"
                              >
                              </vxe-input>
                              <span v-else>{{ row.netTotalAmount }}</span>
                            </div>
                          </template>
                          <template #accept_default="{ row }">
                            <span v-if="row.accept">
                              {{ rePortPriceAccpet(row) }}
                            </span>
                          </template>
                          <template #operation="{ row, rowIndex }">
                            <a-button
                              v-if="!row.lastQuote"
                              type="primary"
                              size="small"
                              :loading="loading"
                              @click="submitQuota({ row, rowIndex })"
                            >
                              {{ $srmI18n(`${$getLangAccount()}#i18n_title_release`, '发布') }}
                            </a-button>
                          </template>
                        </vxe-grid>
                        <!-- 逐条 -->
                        <vxe-grid
                          v-else
                          ref="quotaGrid"
                          height="437"
                          show-overflow
                          v-bind="quotaGridOptions"
                        >
                          <!-- v-on="quotaGridEvents" -->
                          <template slot="empty">
                            <a-empty />
                          </template>
                          <template #toolbar_buttons>
                            <!-- 当前已选供应商 currentSupplierAccount -->
                            <!-- 当前行报价currentItemNumber 或 允许各自报价（物料）respectiveQuotations -->
                            <!-- 采购自动报价 purchaseAutoQuote - 否 0 -->
                            <!-- v-if="currentSupplierAccount
                                        && currentSupplierAccount !== -1
                                        && (currentItemNumber === currentMaterialItemNumber || form.respectiveQuotations === '1')
                                        && form.purchaseAutoQuote === '0'" -->
                            <vxe-button
                              v-if="form.purchaseAutoQuote!='1'"
                              :loading="addLoading"
                              @click="handleAddQuota"
                            >{{ $srmI18n(`${$getLangAccount()}#i18n_btn_exVa_27d46f87`, '同步新增') }}</vxe-button>
                          </template>
                          <template #price_header="{ column }">
                            <i class="vxe-icon--edit-outline"></i>
                            <span>{{ column.title }}</span>
                            <span class="red">*</span>
                          </template>
          
                          <template #price_default="{ row, rowIndex }">
                            <div>
                              <vxe-input
                                v-if="setQuoteInput(row)"
                                v-model="row.price"
                                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价')"
                                type="number"
                                :step="priceStep(row.price)"
                                min="0"
                                clearable
                                :disabled="isPriceDisabled"
                                @change="handlePriceBlur({ row, rowIndex })"
                              >
                              </vxe-input>
                              <span v-else>{{ row.price }}</span>
                            </div>
                          </template>
          
                          <template #netPrice_default="{ row, rowIndex }">
                            <div>
                              <vxe-input
                                v-if="setQuoteInput(row)"
                                v-model="row.netPrice"
                                :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价')"
                                type="number"
                                :step="priceStep(row.netPrice)"
                                min="0"
                                clearable
                                :disabled="!isPriceDisabled"
                                @change="handleNetPriceBlur({ row, rowIndex })"
                              >
                              </vxe-input>
                              <span v-else>{{ row.netPrice }}</span>
                            </div>
                          </template>
                          <template #bidNumber_default="{ row }">
                            <div>
                              <vxe-input
                                v-if="setQuoteInput(row)"
                                v-model="row.bidNumber"
                                :placeholder="投标数量"
                                type="number"
                                :disabled="isEnableRemin"
                               
                              >
                              </vxe-input>
                              <span v-else>{{ row.bidNumber }}</span>
                            </div>
                          </template>
                          <template #accept_default="{ row }">
                            <span v-if="row.accept">
                              {{ rePortPriceAccpet(row) }}
                            </span>
                          </template>
                          <template #operation="{ row, rowIndex }">
                            <a-button
                              v-if="row.itemNumber === currentMaterialItemNumber && !row.lastQuote"
                              type="primary"
                              size="small"
                              :loading="loading"
                              @click="submitQuota({ row, rowIndex })"
                            >
                              {{ $srmI18n(`${$getLangAccount()}#i18n_title_release`, '发布') }}
                            </a-button>
                          </template>
                        </vxe-grid>
                      </div>
                    </div>
                  </a-card>
                </div>
              </div>
            </multipane>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>
          
<script lang='jsx'>
const resizeChartMethod = '$__resizeMethod'
          
import { groupBy, debounce} from 'lodash'
import {
    apiQueryBidLobbyDetail,
    apiQueryBidLobbyQuote,
    apiHisQueryBidLobbyDetail,
    apiManualStartBid,
    apiManualEndBid,
    apiQueryQuoteSite,
    apiQueryAddQuoteSite,
    apiQuote, apiQueryOnlineAccount
} from '@/api/apiBidding.js'
import { currency } from '@/filters'
import { materialGridOptions, rankDucGridOptions, quotaGridOptions, quotaPackGridOptions } from '../gridConfig/purchase/indexDutch'
import { getAction } from '@/api/manage'
import LineChart from '../components/LineChart'
import { USER_INFO } from '@/store/mutation-types'
import { isDecimal } from '@/utils/validate.js'
import { add, mul, div } from '@/utils/mathFloat.js' // 加 减 乘 除
import { mapActions, mapGetters } from 'vuex'
import countdown from '@/components/countdown/index.js'
import { Multipane, MultipaneResizer } from 'vue-multipane'
import { closeWS, createSocket } from '../websocket.js'
import {nominalEdgePullWhiteBlack } from '@/utils/util.js'
          
const INTERVAL = 1000 * 30 // 30秒刷新页面
const ONEBYONE = '1' // 逐条

export default {
    inject: [
        'closeCurrent'
    ],
    components: {
        LineChart,
        countdown,
        Multipane, 
        MultipaneResizer
        // Breadcrumb
    },
    filters: {
        currency
    },
    data () {
        return {
            xasDeaultVal: 'length',
            timer: null,
            activeKey: '1',
            materialGridOptions, // 物料信息列表
            // chartGridOptions, // 报价趋势图
            ebiddingDetailData: {},
            rankGridOptions: rankDucGridOptions, // 供应商信息列表
            quotaGridOptions, // 逐条报价设置列表
            quotaPackGridOptions, // 打包报价设置列表
            currentMaterialItemNumber: -1, // 点击物料行当前行号
            currentMaterialNumName: null, // 点击物料行当前行物料名称
            currentMaterialStatus: null, // 点击物料行当前行竞价状态
            currentTaxRate: 0,
            currentItemNumber: -1, // 头信息当前行号
            currentSupplierAccount: -1, // 当前选中供应商ELS账号
            currentSupplierStatus: -1, // 当前选中供应商 行状态 判断是否可新增（竞价中 4 可新增）
            currentSupplierItemId: '',
            deadline: 0,
            countdownText: '',
            gt1900: false,
            spinning: false,
            delayTime: 300,
            headStyle: {
                borderBottom: 'none',
                padding: '0 6px',
                minHeight: '34px',
                lineHeight: '34px',
                height: '40px'
            },
            headStyle2: {
                borderBottom: 'none',
                padding: '0 6px',
                minHeight: '30px',
                lineHeight: '30px',
                height: '30px'
            },
            valueStyle: {
                fontSize: '24px',
                color: '#f41616'
            },
            bodyStyle: {
                padding: '0 24px 24px'
            },
            bodyStyle_2: {
                padding: '0 6px 24px 6px'
            },
            progress: {
                percent: 78,
                status: 'active',
                strokeColor: {
                    from: '#4892ff',
                    to: '#596fe1'
                }
            },
            // endTime: 0,
            minPrice: 1200,
            form: {
                ebiddingNumber: '', // 竞价单号
                currentItemNumber: '',
                ebiddingType: '', // 竞价方式
                changeRange: '', // 价格调整幅度
                rangeUnit: '', // 幅度单位
                beginTime: '', // 竞价开始时间  
                endTime: '', // 竞价结束时间
                keepMinute: '', // 每轮持续时间
                intervalMinute: '', // 每轮间隔时间
                currentRound: '',
                ebiddingWay: '',
                id: '',
                itemNumber: '',
                quoteFlag: null //null 默认， 0报价下一轮，1报价中 是否开始报价
            },
            // chartData: {},
            visible: false,
            wsOnlineUrl: '',
            hasNewQuota: false,
            addLoading: false,
            httpOnlineSuppliers: '',
            suppliers: '',
            loading: false,
            resizeHeight: 0,
            chartXAxisData: [],
            isIntervalCountdown: false, //处理间隔倒计时，开启竞价按钮不能点击
            currentEndMaterialItemNumber: 1
            // currentRemainingQuantity: null
            // endEbidd: true
            
        }
    },
    computed: {
        ...mapGetters(['getOnlineSuppliers', 'getOnlineID', 'getPageRefreshTime']),
        showStartBid () {
            return this.$hasOptAuth('ebidding#purchaseEbiddingHead:manualStartBid')
        },
        isEnableRemin (){
            return this.form.allowModifyQuantity=='0'
        },
        remainUnit (){
            let {ebiddingWay}= this.form  
            let labelUnit=''
            if(ebiddingWay=='0'){
                labelUnit='标包'
            }else{
                //  currentMaterialItemNumber
                if(this.materialGridOptions.data.length>0){
                    console.log(this.materialGridOptions.data)
                    labelUnit=this.materialGridOptions.data[this.currentMaterialItemNumber-1].purchaseUnit_dictText

                }

            }
            return labelUnit
        },
        currentRemainingQuantity (){
            let quantity=0
            if(this.materialGridOptions.data.length>0){
                // console.log(this.materialGridOptions.data)
                quantity=this.materialGridOptions.data[this.currentMaterialItemNumber-1].remainingQuantity

            }
            return quantity
        },
        activeStartBid () {
            let flag = false
            // "1", "待应标", "3", "待竞价"
            let status = this.form.ebiddingStatus
            console.log('this.form.ebiddingStatus', this.form.ebiddingStatus)
            if (status === '1') {
                flag = true
            }
            if(status === '3'&&!this.isIntervalCountdown){
                flag = true
            }
            return flag
        },
        // 竞价结束、已授标、已流标、已建新轮次、已作废、已悔标  时，竞价大厅中，开始竞价、结束竞价按钮置灰不可操作
        startEndDisabled () {
            return ['5', '6', '7', '10', '11', '12'].includes(this.form.ebiddingStatus)
        },
        showEndBid () {
            return this.$hasOptAuth('ebidding#purchaseEbiddingHead:manualEndBid')
        },
        showPrice () { // 是否显示价格
            return this.form.purchaseShow.includes('1')
        },
        showRank () { // 是否显示排名
            return this.form.purchaseShow.includes('0')
        },
        // 含税单价是否可输入
        isPriceDisabled () {
            return this.form.quoteType === '1'
        },
        getCurrentTitle () {
            if (this.form.ebiddingWay === '1' && ['3', '4'].includes(this.form.ebiddingStatus)) { // 逐条报价、竞价中
                return this.currentMaterialNumName ? `【 ${ this.currentMaterialNumName } 】` : ''
            } else {
                return ''
            }
        },
        getToggleStatu (){
            let lable=this.resizeHeight==0?this.$srmI18n(`${this.$getLangAccount()}#i18n_title_putAway`, '收起'):this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expand`, '展开')
            return lable
        },
        getCurrentMaterialStatus () {
            if (this.form.ebiddingWay === '1' && ['3', '4'].includes(this.form.ebiddingStatus)) { // 逐条报价、竞价中
                return this.currentMaterialStatus ? `${ this.currentMaterialStatus }` : ''
            } else {
                return this.form.ebiddingStatus_dictText
                
            }
        },
        baseInfo () {
            const { ebiddingWay = '', rangeUnit_dictText} = this.form || {}
            if (ebiddingWay === '0') {
                return [
                    // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingNumber`, '竞价单号'), key: 'ebiddingNumber', show: true, span: '4' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'), key: 'ebiddingStatus_dictText', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingType`, '竞价类型'), key: 'ebiddingType_dictText', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quotePriceWay`, '报价方式'), key: 'ebiddingWay_dictText', show: true },
                    { label: (this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingRange`, '竞价幅度'))+`(${rangeUnit_dictText})`, key: 'changeRange', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AOKI_2787c0d7`, '启动时间'), key: 'startTime', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_endTime`, '结束时间'), key: 'endTime', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIzs_63db992`, '持续时间(分钟)'), key: 'keepMinute' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ImKIzs_cd425da6`, '间隔时间(分钟)'), key: 'intervalMinute' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packageStartingUnitPrice`, '打包起拍价'), key: 'startTotalAmount' } // 打包起拍价 - 打包竞价显示
                ]
            }
            return [
                // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingNumber`, '竞价单号'), key: 'ebiddingNumber', show: true, span: '4' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'), key: 'ebiddingStatus_dictText', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingType`, '竞价类型'), key: 'ebiddingType_dictText', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quotePriceWay`, '报价方式'), key: 'ebiddingWay_dictText', show: true },
                { label: (this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingRange`, '竞价幅度'))+`(${rangeUnit_dictText})`, key: 'changeRange', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AOKI_2787c0d7`, '启动时间'), key: 'startTime', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_endTime`, '结束时间'), key: 'endTime', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIzs_63db992`, '持续时间(分钟)'), key: 'keepMinute' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ImKIzs_cd425da6`, '间隔时间(分钟)'), key: 'intervalMinute' }
            ]
        },
        isShowEchart () {
            return true
            // 需方可见, 0: 排名, 1: 价格, 2: 供方名称
            // let { serviceTime, endTime, purchaseShow = '' } = this.form
            // endTime = endTime && new Date(endTime).getTime()
            // const now = serviceTime
            //     ? new Date(serviceTime).getTime()
            //     : +new Date()
            // if (now > endTime) {
            //     return true
            // }
            // let arr = purchaseShow.split(',')
            // return arr.includes('0') && arr.includes('1') && arr.includes('2')
        },
        // 反否反向竞价
        isReverse () {
            const { ebiddingType = '' } = this.form || {}
            return ebiddingType === '1'
        },
        // 是否打包状态
        isPack () {
            const { ebiddingWay = '' } = this.form || {}
            return ebiddingWay === '0'
        }
    },
    methods: {
        ...mapActions(['SetOnlineWS', 'CloseOnlineWS']),
        //关闭页面
        windowOpenClose (){
            window.close()
        },
        rePortPriceAccpet (row){
         
            let acceptObj={
                '1': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accept`, '接受'),
                '0': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'),
                '3': this.$srmI18n(`${this.$getLangAccount()}#i18n_import_Ltk_18fc8d9`, '未操作')
            }
  
            let label=acceptObj[row.accept]||''
            return label
        },       
        //拖动的时候重置图表
        reSizeChart: debounce(function (){
            this.$refs.echart.resizeChart()
        }, 500),
        //报价趋势X切换
        // handleXasChange (e){
                     
        //     console.log(e)
        //     let xAxis=[]
        //     this.xasDeaultVal=e
        //     let axisLabel={ rotate: 0, interval: 0}
        //     if(e!='length'){
                  
        //         let result =  this.chartXAxisData.sort((a, b) => {
        //             const timeA = new Date(a.quoteTime).getTime()
        //             const timeB = new Date(b.quoteTime).getTime()
        //             return timeA - timeB
        //         })

        //         xAxis=result.map(val=> {
        //             let time=new Date(val[e]).getHours()<=9?'0'+new Date(val[e]).getHours():new Date(val[e]).getHours()=='24'?'00':new Date(val[e]).getHours()
        //             let mins=new Date(val[e]).getMinutes()<=9?'0'+new Date(val[e]).getMinutes():new Date(val[e]).getMinutes()
        //             let seconds=new Date(val[e]).getSeconds()<=9?'0'+new Date(val[e]).getSeconds():new Date(val[e]).getSeconds()
        //             console.log('timetimetime', typeof time) 
        //             return `${time}:${mins}:${seconds}`
        //         } 
        //         )
        //         xAxis=[...new Set(xAxis)]
        //     } else{
        //         for(let [i] of this.chartXAxisData.entries()){
        //             xAxis.push(i+1)
        //         }
        //     }
        //     if(e=='quoteTime'){
        //         axisLabel={
        //             rotate: 50, interval: 0
        //         }
        //     }
        //     this.chartData.xAxis.data=xAxis
        //     this.chartData.xAxis.axisLabel=axisLabel
        // },
        //收取和展开逻辑
        changeToggle (){
            if(document.getElementById('description')){
                let domHeight=document.getElementById('description').clientHeight
                this.resizeHeight=domHeight
                // debugger
                          
            }else{
                this.resizeHeight=0
            }
          
        },
        priceStep (price) {
            const rangeUnit = this.form.rangeUnit // 幅度单位 0 金额，1 百分比
            const changeRange = this.form.changeRange // 幅度
            if (rangeUnit === '0') return changeRange
            return Math.ceil(changeRange / 100 * price*100)/100
        },
        setApiQueryOnlineAccount (){
            this.setOnlineAccountWesocketPush && clearInterval(this.setOnlineAccountWesocketPush)
            this.setOnlineAccountWesocketPush = setInterval(() => {
                this.setApiQueryOnlineAccount()
                apiQueryOnlineAccount({ headId: this.$route.query.id }).then(res => {
                    console.log('res', res)
                    this.$nextTick(() => {
                        this.httpOnlineSuppliers = res.message
                        this.suppliers = res.message
                    })
                })

            }, 3000)
 
        },
        sendRefresh (time = INTERVAL) {
            console.log('this.startEndDisabled :>> ', this.startEndDisabled)
            // if (this.startEndDisabled) {
            //     this.setIntervalWesocketPush && clearInterval(this.setIntervalWesocketPush)
            //     return
            // }
             
            this.setIntervalWesocketPush && clearInterval(this.setIntervalWesocketPush)
            this.setIntervalWesocketPush = setInterval(() => {
                this.sendRefresh()
                // apiQueryOnlineAccount({ headId: this.$route.query.id }).then(res => {
                //     console.log('res', res)
                //     this.$nextTick(() => {
                //         this.httpOnlineSuppliers = res.message
                //         this.suppliers = res.message
                //         if (!this.startEndDisabled) { // 仅竞价中可刷新
                //             // 不刷新getData接口，防止供应商信息重新加载数据去掉选中行
                //             // this.refresh(false)
                //             this.checkTime()
                //         }
                //     })
                // })
                if (!this.startEndDisabled) { // 仅竞价中可刷新
                    this.refresh(false)
                }
            }, time)
            // clearTimeout(this.timer)
            // this.timer = setTimeout(() => {
            //     this.sendRefresh()
            //     apiQueryOnlineAccount({ headId: this.$route.query.id }).then(res => {
            //         console.log('res', res)
            //         this.$nextTick(() => {
            //             this.httpOnlineSuppliers = res.message
            //             this.suppliers = res.message
            //             this.refresh()
            //         })
            //     })
            // }, time)
        },
        getSocketData (newVal) {
            console.log('getSocketData newVal.detail', newVal.detail.data)
            const message = newVal.detail.data
          
            // 报价更新
            console.log('message === this.form.id', message === this.form.id)
         
            if (message === this.form.id) {
                this.refresh(false)
                return
            }
        },
        getOnlineWebsocketUrl () {
            let { serivceUrl = '', elsAccount = '' } = this.$ls.get(USER_INFO) || {}
            let url = serivceUrl.replace('https://', 'wss://').replace('http://', 'ws://')
            this.wsOnlineUrl = `${url}/els/websocket/online/${this.form.id}/${elsAccount}`
            createSocket(this.wsOnlineUrl)
        },
        async submitQuota (data) {
            if (this.loading) return
            this.addLoading = true
            this.loading = true
            this.hasNewQuota = true
            const res = await apiQuote(data.row)
            if (res.success) {
                this.$message.success(res.message)
                this.currentSupplierAccount = data.row.toElsAccount
                await this.getQuotaData(true)
            } else {
                this.loading = false
                this.addLoading = false
                this.$message.error(res.message)
            }
            this.checkTime()
        },
        // 打包：
        // 打包未税金额 netTotalAmount, 实时计算: 打包含税金额 totalAmount
        // 不含税价 = 含税价 / (1 + 税率 / 100)
        handleNetPackBlur (data) {
            const { netTotalAmount } = data.row
          
            const rowIndex = data.rowIndex
            if (!netTotalAmount || !isDecimal(netTotalAmount)) return
                      
            let { taxRate = 0 } = this.quotaPackGridOptions.data[rowIndex] || {}
            if (!taxRate || !isDecimal(taxRate)) {
                taxRate = 0
            }
            let tax = add(1, div(taxRate, 100))
            let totalAmount = mul(netTotalAmount, tax)
            totalAmount = totalAmount.toFixed(2)
            this.quotaPackGridOptions.data[rowIndex].totalAmount = totalAmount
        },
        // 打包：
        // 打包含税金额 totalAmount, 实时计算: 打包未税金额 netTotalAmount
        // 不含税价 = 含税价 / (1 + 税率 / 100)
        handlePackBlur (data) {
            const { totalAmount } = data.row
            const rowIndex = data.rowIndex
            if (!totalAmount || !isDecimal(totalAmount)) return
            if (!this.currentTaxRate || !isDecimal(this.currentTaxRate)) {
                this.currentTaxRate = 0
            }
            let tax = add(1, div(this.currentTaxRate, 100))
            let netTotalAmount = div(totalAmount, tax)
            // netTotalAmount = netTotalAmount.toFixed(2)
            this.quotaPackGridOptions.data[rowIndex].netTotalAmount = netTotalAmount
        },
        // 含税单价 price, 实时计算: 不含税价 netPrice
        // 不含税价 = 含税价 / (1 + 税率 / 100)
        handlePriceBlur (data) {
            const { price } = data.row
            const rowIndex = data.rowIndex
            if (!price || !isDecimal(price)) return
            if (!this.currentTaxRate || !isDecimal(this.currentTaxRate)) {
                this.currentTaxRate = 0
            }
            let tax = add(1, div(this.currentTaxRate, 100))
            let netPrice = div(price, tax)
            // netPrice = netPrice.toFixed(2)
            this.quotaGridOptions.data[rowIndex].netPrice = netPrice
        },
        // 未税单价 netPrice, 实时计算: 含税价 price
        // 含税价 = 不含税价 * (1 + 税率 / 100)
        handleNetPriceBlur (data) {
            const { netPrice } = data.row
            const rowIndex = data.rowIndex
            if (!netPrice || !isDecimal(netPrice)) return
            if (!this.currentTaxRate || !isDecimal(this.currentTaxRate)) {
                this.currentTaxRate = 0
            }
            let tax = add(1, div(this.currentTaxRate, 100))
            let price = mul(netPrice, tax)
            // price = price.toFixed(2)
            this.quotaGridOptions.data[rowIndex].price = price
        },
        setQuoteInput (row) {
            return this.form.ebiddingStatus === '4' && this.currentMaterialItemNumber === row.itemNumber && !row.lastQuote
        },
        handleAddQuota () {
            this.addLoading = true
            if (this.hasNewQuota) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VhxAPsuSVa_4fedb387`, '请发布当前报价后新增'))
                this.addLoading = false
                return
            }
            // if (this.currentSupplierItemId === '' || this.currentSupplierAccount === -1) {
          
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFRdXSVa_2da3546`, '请选择供应商后新增'))
            //     this.addLoading = false
            //     return
            // }
            this.hasNewQuota = true
            if(!this.currentSupplierItemId) return
            apiQueryAddQuoteSite({ headId: this.form.id, itemId: this.currentSupplierItemId }).then(res => {
                if (res.success) {
                    if (this.form.ebiddingWay === '0') { // 打包
                        this.quotaPackGridOptions.data.splice(0, 0, res.result)
                    } else { // 逐条、批量  if (this.form.ebiddingWay === '1')
                        this.quotaGridOptions.data.splice(0, 0, res.result)
                    }
                } else {
                    this.$message.error(res.message)
                    this.hasNewQuota = false
                }
                this.addLoading = false
            }).catch(err => {
                console.log('getUserMedia err', err.name, err.message)
                this.$message.error(err.message)
                this.addLoading = false
                this.hasNewQuota = false
            })
        },
        cellSupplierClickEvent ({ row }) {
            const { toElsAccount= '', id, itemStatus } = row || {}
            if (toElsAccount === this.currentSupplierAccount) return
            this.currentSupplierAccount = toElsAccount
            this.currentSupplierStatus = itemStatus
            this.currentSupplierItemId = id

            this.hasNewQuota = true
            this.addLoading = true
            this.getQuotaData(true)
        },
        cellMaterialClickEvent ({ row }) {
            this.currentSupplierAccount = -1
            this.currentSupplierStatus = -1
            this.currentSupplierItemId = ''
            this.$nextTick(()=>{
                this.xasDeaultVal='length'
            })
            if (this.isPack) { // 打包
                return
            }
            const { itemNumber= '', taxRate, materialNumber, materialName, itemStatus_dictText } = row || {}
            this.currentTaxRate = taxRate
            if (itemNumber === this.currentMaterialItemNumber) return
            this.currentMaterialItemNumber = itemNumber
            this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
            this.clearQuotaData()
            this.getMaterialData()
        },
        setCurrentMaterialInfo (materialNumber = null, materialName = null, itemStatus_dictText = null) {
            this.currentMaterialNumName = `${materialNumber} - ${materialName}`
            this.currentMaterialStatus = itemStatus_dictText
        },
        goBack () {
            closeWS()
            // this.CloseOnlineWS()
            this.closeCurrent()
            this.timer && clearTimeout(this.timer)
        },
        startOrEndBid (flag = false) {
            // 参与数量 供应商信息
            const { participateQuantity, saleHeadList = [] } = this.ebiddingDetailData
            // 待竞价的供应商
            let saleEbiddingNumbers = saleHeadList.filter(item => item.ebiddingStatus == '3')
            if(flag) {
                if(saleEbiddingNumbers.length == 0) return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n__BjqsUOujRdX_dcf49d55`, '没有可参与竞价的供应商'))
            }
          
            const title = flag ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openBiding`, '开启竞价') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overBiding`, '结束竞价')
          
            const content = flag ? `${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eRsUWRL_7e095f3e`, '最少参与数量为')}${participateQuantity}，${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WqsUOujRdXWRL_f5dc0f6e`, '现可参与竞价的供应商数量为')}${saleEbiddingNumbers.length}，${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRLKQvAOuW_98f84593`, '请确认是否开启竞价？')}` : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLKQyWOu_50fee892`, '确认是否结束竞价')
            const callback = () => {
                const params = {
                    id: this.form.id
                }
                if (!flag && this.form.ebiddingWay === '1' && this.form.respectiveQuotations === '1') {
                    const currentMaterial = this.materialGridOptions.data.find(i => i.itemNumber === this.currentMaterialItemNumber)
                    params.purchaseEbiddingItemList = [currentMaterial]
                }
                const request = flag ? apiManualStartBid : apiManualEndBid
                request(params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(flag){
                        //开启竞价之后重新设置供应商信息选中状态
                        this.setSupplierRow()
          
                    }
                    this.getData()
                })
            }
            this.$confirm({
                title,
                content,
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        refresh: debounce( function (spinning = true) {
            if (spinning) {
                this.hasNewQuota = false
                this.setApiQueryOnlineAccount()
            }
              
            // this.clearQuotaData() // FIX 150082（有一段时间被清空，并无用处）
            this.getData(spinning)
            this.$nextTick(()=>{
                this.xasDeaultVal='length'
            })
        }, 500),
        handleFinish () {
            this.$nextTick(()=>{
                this.refresh(true)
            })
        
        },
        //点击物料切换的时候重新获取值
        getMaterialData (spinning = true) {
            if (spinning) this.spinning = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: res.success ?  'success' : 'error', res }), err => ({ status: 'error', err })))
            const { id } = this.form
            const params = {
                id,
                itemNumber: this.currentMaterialItemNumber !== -1 ? this.currentMaterialItemNumber : this.currentItemNumber
            }
            const promiseList = [
                apiQueryBidLobbyDetail(params)
            ]
            Promise.all(handlePromise(promiseList)).then(res => {
                const [infoRes] = res || []
                if (infoRes && infoRes.status === 'success') {
                    this.fixInfoData(infoRes.res)
                }
                this.getItemData()
            })
        },
        getData (spinning = true) {
            this.getEbiddingDetailData().then(res=>{
                this.currentItemNumber=res.currentItemNumber||'1'
                // if (this.currentMaterialItemNumber === -1) {
                this.currentMaterialItemNumber = this.currentItemNumber
             
                // }
                this.form.currentItemNumber=this.currentItemNumber
                if(res.ebiddingStatus==='5'&&!this.endEbidd){
                  
                    this.endEbidd=true
                    this.$notification.open({
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                        description: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingOver`, '竞价结束')}`,
                        icon: <a-icon type="sound" style="color: #108ee9" />,
                        duration: 10
                    })
                    this.getItemData()
                }else{
                    if(this.currentEndMaterialItemNumber!=this.currentItemNumber){
                        this.currentSupplierAccount = -1
                        this.currentSupplierStatus = -1
                        this.currentSupplierItemId = ''
                        if(this.currentMaterialNumName&&this.currentItemNumber!='1'){
                            let materNumber=this.materialGridOptions.data.find(val=> this.currentEndMaterialItemNumber==val.itemNumber  )
                            
                            this.$notification.open({
                                message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                                description: `${materNumber.materialNumber}-${materNumber.materialDesc}${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingOver`, '竞价结束')},切换到下一个物料`,
                                icon: <a-icon type="sound" style="color: #108ee9" />,
                                duration: 10
                            })
                        }
                        this.currentEndMaterialItemNumber=this.currentItemNumber
                    }
                }
                //  debugger
                if (spinning) this.spinning = true
                const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: res.success ?  'success' : 'error', res }), err => ({ status: 'error', err })))
                const { id } = this.form
                const params = {
                    id,
                    itemNumber: this.currentMaterialItemNumber !== -1 ? this.currentMaterialItemNumber : this.currentItemNumber
                }
                const promiseList = [
                    apiQueryBidLobbyDetail(params)
                ]
                Promise.all(handlePromise(promiseList)).then(res => {
                    const [infoRes] = res || []
                    if (infoRes && infoRes.status === 'success') {
                        this.fixInfoData(infoRes.res)
                    }
                    this.getItemData()
                })
            })
        },
        // 查竞价详情接口 根据开启方式以及待竞价供应商数量判断是否自动开启竞价
        getEbiddingDetailData () {
          
            return new Promise((resolve, reject)=>{
              
                getAction('/ebidding/purchaseEbiddingHead/queryById', {id: this.$route.query.id}).then(res => {
                    if(res.success) {
                        this.ebiddingDetailData = res.result
                        // 供应商信息 启动方式
                        const { saleHeadList = [], startWay = '0' } = this.ebiddingDetailData
                        // 自动开启竞价
                        // 自动开启竞价
                        if(startWay == '1') {
                        // 过滤出待应标 || 不参与 的数据
                            let result = saleHeadList.filter(item => item.ebiddingStatus == 1 || item.ebiddingStatus == 2)
                            // 与参与供应商数组长度相同 即没有供应商为待竞价状态
                            if(result.length == saleHeadList.length) {
                                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n__BjqsUOujRdXWVRLdBRdX_8d298784`, '没有可参与竞价的供应商，请确认应标供应商'))
                            }
                        }
                        resolve(res.result)
                    }
                })
            })

        },
        getItemData () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: res.success ?  'success' : 'error', res }), err => ({ status: 'error', err })))
            const { id } = this.form
            const itemNumber = this.currentMaterialItemNumber !== -1 ? this.currentMaterialItemNumber : this.currentItemNumber
            const params = {
                id,
                itemNumber
            }
            const promiseList = [
                apiQueryBidLobbyQuote(params),
                apiHisQueryBidLobbyDetail(params)
            ]
            Promise.all(handlePromise(promiseList)).then(res => {
                this.spinning = false
                const [itemRes, hisRes] = res || []
                if (itemRes && itemRes.status === 'success') {
                    this.fixItemData(itemRes.res)
                }
                if (hisRes && hisRes.status === 'success') {
                    this.fixHisPriceData(hisRes.res)
                }
            })
        },
        async getQuotaData (isResetList = false, isFixItemData = false) {
            const params = {
                id: this.form.id,
                itemNumber: this.currentMaterialItemNumber !== -1 ? this.currentMaterialItemNumber : this.currentItemNumber,
                toElsAccount: this.currentSupplierAccount
            }
            const res = await apiQueryQuoteSite(params)
            if (res.success) {
                // if (this.form.ebiddingWay === '0') { // 打包
                //     this.quotaPackGridOptions.data =res.result
                // } else if (this.form.ebiddingWay === '1') { // 逐条
                //     this.quotaGridOptions.data = res.result
                // }
                if (this.form.ebiddingWay === '0') { // 打包
                    this.quotaPackGridOptions.data =this.quotaPackGridOptions.data.length>res.result.length&&!isResetList?this.quotaPackGridOptions.data:res.result
                } else if (this.form.ebiddingWay === '1') { // 逐条
                    this.quotaGridOptions.data =this.quotaGridOptions.data.length> res.result.length&&!isResetList?this.quotaGridOptions.data:res.result
                }
            }
            if (isFixItemData) return
            this.loading = false
            this.addLoading = false
            this.hasNewQuota = false
        },
        refreshSupplierData () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: res.success ?  'success' : 'error', res }), err => ({ status: 'error', err })))
            const { id } = this.form
            const itemNumber = this.currentMaterialItemNumber !== -1 ? this.currentMaterialItemNumber : this.currentItemNumber
            const params = {
                id,
                itemNumber
            }
            const promiseList = [
                apiQueryBidLobbyQuote(params)
            ]
            Promise.all(handlePromise(promiseList)).then(res => {
                const [itemRes] = res || []
                if (itemRes && itemRes.status === 'success') {
                    this.rankGridOptions.data = itemRes.res.result.purchaseEbiddingItemList || []
                }
            })
        },
        // 倒计时计算
        checkTime () {
            let { beginTime, beginTime_DateMaps,  endTime, endTime_DateMaps, ebiddingStatus, quoteFlag } = this.form
            const { serviceTime } = this.form
            const now = serviceTime || Date.now()
            beginTime = beginTime ? beginTime_DateMaps : Date.now()
            endTime = endTime ? endTime_DateMaps : Date.now()
            console.log('间隔', ebiddingStatus, quoteFlag, now < endTime, this.currentItemNumber)
            if(ebiddingStatus === '3'||ebiddingStatus === '1'){
                if (quoteFlag === '0'||(quoteFlag==null&&(this.currentItemNumber!='1'))) {
                    if (now < endTime) {
                        this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ImKIutK_41a04769`, '间隔时间倒计时')
                        this.deadline = endTime - now
                        this.isIntervalCountdown=true
                    }
                } else {
                    // 待竞价状态
                    this.isIntervalCountdown=false
                    if (now < beginTime) {
                        this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OuvKutK_bc29df23`, '竞价开始倒计时')
                        this.deadline = beginTime - now
                    }else{
                        this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EovAOu_cf2bb4a4`, '等待开启竞价')
                        this.deadline = 0
                    }
                }
                    
            } else if(ebiddingStatus === '4'){
                if (quoteFlag === '1') {
                    // 竞价中状态
                    if (now < beginTime) {
                        this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ImKIutK_41a04769`, '间隔时间倒计时')
                        this.deadline = beginTime - now
                    } else if(now < endTime) {
                        this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIutK_b713bdfd`, '持续时间倒计时')//this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yWutK_2b6a279b`, '结束倒计时')
                        this.deadline = endTime - now
                    }else{
                        this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIutK_b713bdfd`, '持续时间倒计时') // '等待结束竞价' // this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EolOyWOu_3e47d35e`, '等待手动结束竞价')
                        this.deadline = 0
                    }
                } else {
                    this.countdownText =this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EonRCsu_fb71e689`, '等待采购方报价') 
                    this.deadline = 0
                }            
            } else {
                this.countdownText =  this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SutK_2f8d3027`, '无倒计时')
                this.deadline = 0
            }
        },
        fixInfoData (res) {
            const { purchaseEbiddingItemList = [], ebiddingWay, ...others } = res.result || {}
            let { currentItemNumber, taxRate } = res.result || {}
                      
            this.materialGridOptions.data = purchaseEbiddingItemList
            this.currentItemNumber = currentItemNumber || '1'
        
            // let curNumber=Number( this.currentItemNumber )
            // if(purchaseEbiddingItemList.length>0&&purchaseEbiddingItemList[curNumber-1]&&purchaseEbiddingItemList[curNumber-1].remainingQuantity) 
            // {
            //     this.currentRemainingQuantity=purchaseEbiddingItemList[curNumber-1].remainingQuantity
            // }
            const currentMaterial = purchaseEbiddingItemList.find(i => i.itemNumber === (this.currentMaterialItemNumber || this.currentItemNumber))
            if (currentMaterial) {
                const { materialNumber, materialName, itemStatus_dictText } = currentMaterial
                this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
            }
            this.currentTaxRate = taxRate
            // 初始时，高亮竞价物料表格第一行
            if (this.currentMaterialItemNumber === -1) {
                this.currentMaterialItemNumber = this.currentItemNumber
            }
            this.form = {
                ebiddingWay,
                currentItemNumber: this.currentItemNumber,
                ...others,
                serviceTime: res.timestamp
            }
            this.sendRefresh()
            // 逐条，获取起拍价格
            if (ebiddingWay === '1') {
                let i = this.currentItemNumber - 1
                this.form.startPrice = purchaseEbiddingItemList[i].startPrice
            }
            this.checkTime()
        },
        //处理页面进来选中供应商信息第一条数据
        setSupplierRow (){
            this.$nextTick(()=>{
                this.$refs.rankGrid.clearCurrentRow()
                let supplierArry=this.$refs.rankGrid.getTableData().fullData
                if(supplierArry.length==0) return
                this.$refs.rankGrid.setCurrentRow(supplierArry[0])
                let curCheckRow=this.$refs.rankGrid.getCurrentRecord()
                let _this=this
                setTimeout (()=>{
                    _this.cellSupplierClickEvent({row: curCheckRow})
                }, 500)
            })
        },
        fixItemData ({ result }) {
            let _this=this
            const { purchaseEbiddingItemList = [] } = result || {}
            // this.refreshSupplierData()
            this.rankGridOptions.data = purchaseEbiddingItemList
            // this.cellSupplierClickEvent({row:purchaseEbiddingItemList[0]})
            //  setTimeout(()=>{
            _this.getQuotaData(false, true)
            _this.setSupplierRow()
            //  },2000)

        },
        fixHisPriceData () {
            // this.chartGridOptions.data = result
            this.setRankTableColumn()
            // this.resetChart()
            // this.getEchartData(result)
        },
        setRankTableColumn () {
            this.rankGridOptions.columns.forEach(item => {
                if (item.field === 'price') {
                    item.visible = !this.isPack
                }
            })
        },
        // getEchartData (arr) {
        //     let legend = {
        //         data: []
        //     }
        //     let xAxis = {
        //         type: 'category',
        //         boundaryGap: false,
        //         data: []
        //     }
        //     let result = arr.sort((a, b) => {
        //         const timeA = new Date(a.quoteTime).getTime()
        //         const timeB = new Date(b.quoteTime).getTime()
        //         return timeA - timeB
        //     })
        //     const group = groupBy(result, 'toElsAccount')
        //     legend.data = Object.keys(group).map(key => {
        //         const arr = group[key]
        //         const { toElsAccount, supplierName } = arr[0] || {}
        //         return `${toElsAccount}_${supplierName}`
        //     })
        //     let length = 0
        //     const series =  Object.keys(group).map(key => {
        //         const prop = this.isPack ? 'totalAmount' : 'price'
        //         const arr = group[key]
        //         const data = arr.map(n => n[prop])
        //         // quoteTime
        //         const { toElsAccount, supplierName } = arr[0] || {}
        //         const name = `${toElsAccount}_${supplierName}`
                
        //         length = arr.length > length
        //             ? arr.length
        //             : length
        //         this.chartXAxisData=this.chartXAxisData.length> arr.length?this.chartXAxisData:arr
        //         // this.chartXAxisData=arr
        //         return {
        //             name,
        //             data,
        //             type: 'line'
        //         }
        //     })
        //     for (let i = 0; i < length; i++) {
        //         xAxis.data.push(i + 1)
        //     }
        //     // xAxis.axisLabel={
        //     //     rotate: 50, interval: 0
        //     // }
        //     this.chartData = {
        //         legend,
        //         xAxis,
        //         series
        //     }
        // },
        getScreenWidth () {
            const clientWidth = document.documentElement.clientWidth
            this.gt1900 = (clientWidth >= 1900)
        },
        getQueryData () {
            this.activeKey = '1' // 初始供应商信息、报价趋势图 tab 为 1
            const { ebiddingNumber, currentItemNumber, id } = this.$route.query || {}
            // this.currentItemNumber = currentItemNumber || '1'
            // if (this.currentMaterialItemNumber === -1) {
            //     this.currentMaterialItemNumber = this.currentItemNumber
            // }

            this.form = Object.assign({}, this.form, {
                ebiddingNumber,
                // currentItemNumber,
                id
            })
        },
        handleRowClass ({ row }) {
            if (this.isPack) { // 打包
                return ''
            }
            const { itemNumber = '' } = row || {}
            if (itemNumber === this.currentMaterialItemNumber) {
                return 'row--current'
            }
        },
        handleRowSupplierClass ({ row }) {
            const { toElsAccount = '' } = row || {}
            if (toElsAccount === this.currentSupplierAccount) {
                return 'row--current'
            }
        },
        resetChart () {
            this.$refs.echart && this.$refs.echart.chart.clear()
        },
        getOnline () {
            apiQueryOnlineAccount({ headId: this.$route.query.id }).then(res => {
                console.log('res', res)
                this.httpOnlineSuppliers = res.message
                this.suppliers = res.message
            })
        },
        // 清空报价行
        clearQuotaData () {
            this.quotaGridOptions.data = []
            this.quotaPackGridOptions.data = []
        },
        // 通过lodash的防抖函数来控制resize的频率
        [resizeChartMethod]: debounce(function () {
            this.getScreenWidth()
        }, 100)
    },
    watch: {
        '$route': {
            handler ({ path }) {
                if (path === '/ebidding/buyLobbyNewDutch') {
                    this.clearQuotaData()
                    this.getQueryData()
                    // this.getEbiddingDetailData().then(res=>{
                    this.setApiQueryOnlineAccount()
                    this.getData()
                    // })
                }
            },
            immediate: true
        },
        getOnlineID: {
            handler (newVal, oldVal) {
                console.log('getOnlineID newVal', newVal)
                if (newVal && oldVal && newVal.id === this.$route.query.id && oldVal.time !== newVal.time) {
                    console.log('getOnlineID')
                    this.refresh()
                }
            },
            immediate: true,
            deep: true
        },
        getPageRefreshTime: {
            handler (newVal) {
                console.log('getPageRefreshTime newVal', newVal)
                if (newVal && newVal.id === this.$route.query.id) {
                    // this.refresh()
                    this.refreshSupplierData()
                    this.getQuotaData(false, true)
                }
                this.getOnline()
            },
            immediate: true,
            deep: true
        }
    },
    created () {
        window.addEventListener('resize', this[resizeChartMethod])
        this.getScreenWidth()
        this.getOnlineWebsocketUrl()
        // this.getData()
        // this.getEbiddingDetailData()
        nominalEdgePullWhiteBlack()
        this.$nextTick(()=>{
            this.changeToggle()
        })
    },
    // activated () {
    //     this.SetOnlineWS({ wsOnlineUrl: this.wsOnlineUrl, id: this.$route.query.id }) // 是否在线WS
    // },
    // beforeDestroy () {
    //     window.removeEventListener('reisze', this[resizeChartMethod])
    // }
    activated () {
        // this.SetOnlineWS({ wsOnlineUrl: this.wsOnlineUrl, id: this.form.id }) // 是否在线WS
        console.log('activated ------------------------- activated')
        this.getOnlineWebsocketUrl()
        this.sendRefresh()
        window.addEventListener('resize', this[resizeChartMethod])
    },
    mounted () {
        console.log('mounted ------------------------- mounted')
        window.addEventListener('onmessageWS', this.getSocketData)
    },
    beforeDestroy () {
        console.log('beforeDestroy ------------------------- beforeDestroy')
        window.removeEventListener('onmessageWS', this.getSocketData)
        window.removeEventListener('reisze', this[resizeChartMethod])
    }
}
</script>
          
          <style lang="less" scoped>
          @red: #f41616;
          @blue: #1690ff;
          html{
            overflow: hidden;
          }
          .m-l-24{
            margin-left:36px!important;
          }
          .m-t-12{
            margin-top:12px
          }
          .inline-flot{
            display: inline-block;
            float: right;
            font-size: 14px;
          }
          :deep(.bg-color-1){
            background-color: #f6ebe8!important;;  
            // background-image: red;
          }
          .buyBid {
              background-color: #eaeaea;
              ul {
                list-style: none;
                margin: 0;
                padding: 0;
              }
              .redTxt {
                color: @red;
              }
              .blue {
                color: #1890ff;
              }
              .top {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 6px;
                  background: #fff;
                  position: fixed;
                  width: 100%;
                  position: absolute;
                  height: 44px;
                  z-index: 99;
                  .menu {
                      text-align: right;
                      .ant-btn {
                          & +.ant-btn {
                              margin-left: 10px;
                          }
                      }
          
                  }
              }
              .content {
                  padding: 8px;
                  padding-top: 52px;
                  .gutter {
                      display: flex;
                      & + .gutter {
                          margin-top: 8px;
                      }
                      .price {
                          max-width: 100%;
                          flex: 1;
                      }
                      .history,
                      .compare {
                          overflow-y: auto;
                          flex: 1;
                          max-width: 100%;
                          .ant-btn {
                              & + .ant-btn {
                                  margin-left: 10px;
                              }
                          }
                      }
                      .material {
          
                          width: 30%;
                      }
                      .supplier {
                          // flex: 1;
                      width: 35%;
                      }
                      .quota {
                          flex: 1;
                      width: 40%;
                      }
                  }
                  .item {
                      padding: 12px;
                      background: #fff;
                border-radius: 4px;
                      & + .item {
                          margin-left: 10px;
                      }
                  }
                  .info {
                      font-size: 20px;
                      color: #000;
                      .inline {
                  .tit {
                    margin-right: 8px;
                    &::after {
                      content: ':'
                    }
                  }
                          &:first-of-type {
                              border-left: 4px solid @blue;
                          }
                          & + .inline {
                              margin-left: 24px;
                          }
                          .label {
                              margin-left: 12px;
                              &::after {
                                  content: "";
                              }
                          }
                          .value {
                              margin-left: 12px;
                              color: @blue;
                          }
                          .red {
                              color: @red;
                          }
                      }
                  }
                  .currentTrendEchart {
                      width: 100%;
                      height: 400px;
                  }
                  .hisPriceEchart {
                      width: 100%;
                      height: 446px;
                  }
                  .table {
                      height: 420px;
                  }
              .material-table {
                  height: 480px;
              }
              }
            .chartInfo {
              display: flex;
              margin-top: 10px;
          
            flex-direction: column;
              .echart {
                flex: 1;
                min-height: 320px;
              }
              .echart.unShow {
                  // background-size: 100px 100px;
                  position: relative;
                  background-color: #fff;
                  background-image: url(~@/assets/img/ebidding/x1.png);
                  background-repeat: no-repeat;
                  background-position: center;
              }
              .chartTable {
                flex: 0 0 260px;
                margin-left: 20px;
              }
              .red {
                color: @red;
                position: absolute;
                left: 50%;
                bottom: 20px;
                transform: translateX(-50%);
              }
            }
          }
          :deep(.ant-descriptions-item-content) {
            width: 20%;
          }
          :deep(.ant-card-head-title) {
            padding: 0;
          }
          .custom-resizer{
              width: 100vw;
              overflow: hidden;
               > .multipane-resizer {
              margin: 0;
              left: 0;
              // margin-top: 20%;
              position: relative;
              &:before {
                display: block;
                content: "";
                width: 3px;
                height: 40px;
                position: absolute;
                top: 50%;
                left: 50%;
                margin-top: -20px;
                margin-left: -4px;
                border-left: 1px solid #ccc;
                border-right: 1px solid #ccc;
              }
              &:hover {
                &:before {
                  border-color: #999;
                }
              }
            }
          }
          .remaining{
            font-weight: bold;
            font-size: 15px;
            b{
                display: inline-block;
                margin: 0 5px;
            }
          }
          
          </style>
          