<template>
  <div class="PurchaseBiddingDetailView">

    <div class="page-container">
      <detail-layout
        ref="detailPage"
        useLocalModelLayout
        modelLayout="unCollapse"
        :page-data="pageData"
        :current-edit-row="vuex_currentEditRow"
        :url="url"
      />
    </div>

    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />

  </div>
</template>

<script>
import { DetailMixin } from '@comp/template/detailNew/DetailMixin'
import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    mixins: [
        DetailMixin
    ],
    data () {
        return {
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_subjectInfo`, '标的信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseBiddingItemList',
                            columns: []
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingDocuments`, '招标文件'),
                        groupCode: 'fileDemandInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachmentDemandList',
                            columns: [{
                                type: 'checkbox',
                                width: 40
                            },
                            {
                                type: 'seq',
                                width: 60,
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                            },
                            {
                                field: 'fileType_dictText',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                                width: 120
                            },
                            {
                                field: 'required_dictText',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'),
                                width: 120
                            },
                            {
                                field: 'remark',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                width: 220
                            }
                            ]
                        }
                    }
                ]
            },
            url: {
                detail: '/bidding/purchaseBiddingHead/queryById',
                submit: '/a1bpmn/audit/api/submit'
            }
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        fileSrc () {
            const {
                templateNumber = '',
                templateVersion = '',
                templateAccount = '',
                busAccount = ''
            } = this.vuex_currentEditRow || {}

            const configFiles = this.$variateConfig['configFiles']
            const time = +new Date()
            const url = `${configFiles}/${templateAccount || busAccount}/purchase_bidding_${templateNumber}_${templateVersion}.js?t=`+time
            return url
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        })
    }
}
</script>
