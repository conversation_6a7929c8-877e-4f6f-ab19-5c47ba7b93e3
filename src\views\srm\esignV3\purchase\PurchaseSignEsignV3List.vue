<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
        <PurchaseSignEsignV3Edit
          v-if="showEditPage"
          ref="editPage"
          :current-edit-row="currentEditRow"
          @hide="hideEditPage"/>
        <!-- 详情界面 -->
        <PurchaseSignEsignV3Detail
          v-if="showDetailPage"
          ref="detailPage"
          :current-edit-row="currentEditRow"
          @hide="hideEditPage" />
  </div>
</template>
<script>
import PurchaseSignEsignV3Edit from './modules/PurchaseSignEsignV3Edit'
import PurchaseSignEsignV3Detail from './modules/PurchaseSignEsignV3Detail'
import {ListMixin} from '@comp/template/list/ListMixin'
import { getAction, postAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        PurchaseSignEsignV3Edit,
        PurchaseSignEsignV3Detail
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'esign',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QfyRdXElSey_14f6f17b`, '流水号/供应商ElS账号')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'esignv3#elsEsignV3Flow:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                form: {
                    keyWord: ''
                },
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esignv3#elsEsignV3Flow:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'esignv3#elsEsignV3Flow:add'},
                    {type: 'query', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QLzEmh_5c8fbdd2`, '流程状态查询'), clickFn: this.queryFlow, allow: this.allowQueryFlow, authorityCode: 'esign#elsEsignV3Flow:flowQuery'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'esignv3#elsEsignV3Flow:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/esignv3/elsEsignV3Flow/purchaseSignList',
                add: '/esignv3/elsEsignV3Flow/add',
                delete: '/esignv3/elsEsignV3Flow/delete',
                send: '/esignv3/elsEsignV3Flow/send',
                queryFlow: '/esignv3/elsEsignV3Flow/flowQuery',
                columns: 'PurchaseSignEsignV3List'
            }
        }
    },
    methods: {
        handleAdd (){
            this.showEditPage = true
            this.currentEditRow = {}
        },
        allowEdit (row){
            if(row.launch==='1'){
                return true
            }
            return false
        },
        allowDelete (row){
            if(row.launch==='1'){
                return true
            }
        },
        allowSend (row){
            //已上传未发送，签署完成
            if(row.uploaded==='1' && row.sendStatus!=='1' && row.esignStatus==='2'){
                return false
            }
            return true
        },
        allowConfirm (row){
            //已回传，未驳回，未确认
            if(row.returnSignedFile==='1' && row.reject!=='1' && row.returnFileConfirm!=='1'){
                return false
            }
            return true
        },
        handleSend (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'),
                content: '是否确认发送',
                onOk: function () {
                    getAction(that.url.send, {id: row.id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.sendStatus_dictText = '是'
                            row.sendStatus = '1'
                        }
                    })
                }
            })
        },
        handleConfirm (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },

        allowQueryFlow (row){
            if(row.launch==='1'){
                return false
            }
            return true
        },
        queryFlow (row){
            getAction(this.url.queryFlow, {id: row.id}).then(res => {
                if(res.success){
                    this.$message.info(res.message)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>