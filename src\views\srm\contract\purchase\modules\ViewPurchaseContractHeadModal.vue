<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :page-data="pageData"
      :url="url"
      @loadSuccess="handleLoadSuccess"/>
    <a-modal
      v-drag
      :width="1000"
      :height="500"
      v-model="fileCompareVisible"
      title="对比结果"
      @ok="fileCompareVisible= false">
      <vxe-grid
        :height="300"
        v-bind="recordGridOptions">
      </vxe-grid>
    </a-modal>
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
      v-drag
      v-model="previewModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="checkEditContractTemplate(previewContent)"></div>
    </a-modal>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk"/>
    <view-item-diff-modal ref="viewDiffModal"/>
    <His-Contract-Item-Modal ref="hisContractItemModal"/>
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
  </div>
</template>
<script lang="jsx">
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import ViewItemDiffModal from './ViewItemDiffModal'
import HisContractItemModal from './HisContractItemModal'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import JEditor from '@comp/els/JEditor'
import {getAction, postAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
import {axios} from '@/utils/request'
import {checkEditContractTemplate} from '@/utils/util'

export default {
    mixins: [DetailMixin],
    components: {
        flowViewModal,
        fieldSelectModal,
        ViewItemDiffModal,
        HisContractItemModal,
        JEditor
    },
    data () {
        return {
            recordGridOptions: {
                columns: [],
                data: []
            },
            fileCompareVisible: false,
            checkEditContractTemplate: checkEditContractTemplate,
            showRemote: false,
            showHelpTip: false,
            previewModal: false,
            editItemRow: {},
            currentItemContent: '',
            previewContent: '',
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            pageData: {
                groups: [
                    /*{ groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLineInfo`, '采购合同行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseContractItemList',
                        columns: [],
                        notShowTableSeq: true
                    } },*/
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLibrary`, '采购合同条款库'),
                        groupCode: 'itemContentInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseContractContentItemList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'id',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectNumber`, '项目编号'),
                                    width: 150
                                },
                                {
                                    field: 'itemName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_SaleMassProdHeadList_projectName`, '项目名称'),
                                    width: 120
                                },
                                {
                                    field: 'itemType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                                    width: 120,
                                    dictCode: 'srmItemType'
                                },
                                {
                                    field: 'itemVersion',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_itemVersion`, '项目版本'),
                                    width: 120
                                },
                                {
                                    field: 'changeFlag',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeIdentification`, '变更标识'),
                                    width: 120,
                                    slots: {
                                        default: ({row}) => {
                                            return [
                                                <span>{row.changeFlag == '1' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_yes`, '是') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_no`, '否')}</span>
                                            ]
                                        }
                                    }
                                    // editRender: {
                                    //     name: '$switch',
                                    //     type: 'visible',
                                    //     props: {closeValue: '0', openValue: '1', disabled: true}
                                    // }
                                },
                                {
                                    field: 'sourceType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'),
                                    width: 120,
                                    dictCode: 'srmContractContentSourceType'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 150,
                                    align: 'left',
                                    slots: {
                                        default: ({row}) => {
                                            let resultArray = []
                                            resultArray.push(<a
                                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                                onClick={() => this.viewDetail(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}</a>)
                                            if (row.changeFlag == '1') {
                                                resultArray.push(<a title="比对" style="margin-left:8px"
                                                    onClick={() => this.viewDiff(row)}>比对</a>)
                                            }
                                            return resultArray
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachmentList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 120
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'uploadSubAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                                    width: 120
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    clickFn: this.downloadEvent
                                },
                                {
                                    type: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preViewEvent
                                },
                                {
                                    key: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '比对'),
                                    clickFn: this.fileCompare,
                                    authorityCode: 'compare#elsFileCompareHead:edit'
                                },
                                {
                                    key: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '比对结果'),
                                    clickFn: this.fileCompareResult,
                                    authorityCode: 'compare#elsFileCompareHead:list'
                                }
                            ]
                        }
                    }
                ],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_examineApprove`, '审批'),
                        type: 'primary',
                        click: this.submitAudit,
                        showCondition: this.showcAuditConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                        type: 'primary',
                        click: this.cancelAudit,
                        id: 'cancelAudit',
                        showCondition: this.showcCncelConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                        type: '',
                        click: this.showFlow,
                        id: 'showFlow',
                        showCondition: this.showFlowConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                        authorityCode: 'contract#purchaseContractHead:publish',
                        type: 'primary',
                        click: this.publishEvent,
                        showCondition: this.showPublishConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_versionChange`, '版本变更'),
                        authorityCode: 'contract#purchaseContractHead:upgradeVersion',
                        type: 'primary',
                        click: this.upgradeVersionEvent,
                        showCondition: this.showVersionConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        authorityCode: 'contract#purchaseContractHead:getPreviewData',
                        type: 'primary',
                        click: this.previewPdf
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                        authorityCode: 'contract#purchaseContractHead:download',
                        type: 'primary',
                        click: this.downloadFile
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IKWord_12f184fc`, '下载Word'),
                        attrs: {
                            type: 'primary'
                        },
                        authorityCode: 'contract#purchaseContractHead:download',
                        click: this.downloadWordFile
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                public: '/contract/purchaseContractHead/publish',
                detail: '/contract/purchaseContractHead/queryById',
                upgradeVersion: '/contract/purchaseContractHead/upgradeVersion',
                submitAudit: '/a1bpmn/audit/api/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                downloads: '/contract/purchaseContractHead/download',
                downloadDoc: '/contract/purchaseContractHead/downloadDoc',
                download: '/attachment/purchaseAttachment/download'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            if (this.currentEditRow.contractType === '3') {
                return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_contractSimple_${templateNumber}_${templateVersion}.js?t=` + time
            }
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_contract_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.currentEditRow.contractType = res.result.contractType
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
        formatPageData(item) {
            if (item.totalTaxAmount === 0 || Math.abs(item.totalTaxAmount) > 0) {
                item.totalTaxAmount = Number(item.totalTaxAmount).toFixed(2)
            }
            return item;
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        },
        handleLoadSuccess (res) {
            this.currentEditRow.contractType = res.result.contractType
            if (this.currentEditRow.contractType === '3') {
                this.pageData.groups.splice(0, 1)
            }
        },
        preview () {
            let contentGrid = this.$refs.detailPage.$refs.purchaseContractContentItemList[0]
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.$refs.detailPage.confirmLoading = true
            getAction('/contract/purchaseContractHead/getPreviewData', {id: this.currentEditRow.id}).then((res) => {
                if (res.success) {
                    this.previewModal = true
                    this.previewContent = res.result
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        },
        fileCompareResult (row) {
            this.confirmLoading = true
            getAction('/compare/elsFileCompareHead/getResultById', {id: row.id}).then((res) => {
                if (res.success) {
                    if (res.result.elsFileCompareResultList && res.result.elsFileCompareResultList.length == 1) {
                        let filePath = res.result.elsFileCompareResultList[0].filePath
                        this.$previewFile.open({params: {}, path: filePath})
                    } else {
                        this.recordGridOptions.columns = [
                            {
                                title: '比对时间',
                                fieldLabelI18nKey: 'i18n_field_lIKI_326a4423',
                                field: 'createTime',
                                helpText: ''
                            },
                            {
                                title: '文件类型',
                                fieldLabelI18nKey: 'i18n_field_fileType',
                                field: 'fbk2_dictText',
                                helpText: ''
                            },
                            {
                                title: '文件名称',
                                fieldLabelI18nKey: 'i18n_title_fileName',
                                field: 'fileName',
                                helpText: ''
                            },
                            {
                                title: '文件路径',
                                fieldLabelI18nKey: 'i18n_field_filePath',
                                field: 'filePath',
                                helpText: ''
                            },
                            {
                                title: '操作',
                                fieldLabelI18nKey: 'i18n_title_operation',
                                field: 'sourceType_dictText',
                                headerAlign: 'center',
                                slots: {
                                    default: ({row}) => {
                                        let resultArray = []
                                        resultArray.push(<a
                                            title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '预览')}
                                            onClick={() => this.resultView(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '预览')}</a>)
                                        resultArray.push(<a
                                            title={this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}
                                            style="margin-left:8px"
                                            onClick={() => this.resultDownload(row)}> {this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}</a>)
                                        return resultArray
                                    }
                                }
                            }
                        ]
                        this.recordGridOptions.data = res.result.elsFileCompareResultList
                        this.fileCompareVisible = true
                    }
                }else {
                    this.$message.warning(res.message)
                }
            }
            ).finally(() => {
                this.confirmLoading = false
            })
            /*this.$router.push({
                path: '/srm/compare/ElsFileCompareHeadList',
                query: {sourceId: row.id}
            })*/
        },
        resultView (row) {
            this.$previewFile.open({params: {}, path: row.filePath})
        },
        resultDownload (row) {
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = row.filePath
            link.setAttribute('download', row.fileName)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(row.filePath) //释放掉blob对象
        },
        fileCompare (row) {
            this.fileRow = row
            let item = {
                selectModel: 'single',
                sourceUrl: '/attachment/purchaseAttachment/listFileCompare',
                params: {
                    businessType: 'contractSimple'
                },
                columns: [
                    {
                        field: 'businessType_dictText',
                        fieldLabelI18nKey: 'i18n_field_businessType',
                        title: '业务类型',
                        with: 150
                    },
                    {
                        field: 'fileType_dictText',
                        fieldLabelI18nKey: 'i18n_field_fileType',
                        title: '文件类型',
                        with: 150
                    },
                    {
                        field: 'uploadElsAccount',
                        fieldLabelI18nKey: 'i18n_field_uploadElsAccount',
                        title: '文件上传方账号',
                        with: 150
                    },
                    {
                        field: 'uploadSubAccount',
                        fieldLabelI18nKey: 'i18n_title_fileUploadPartySubAccount',
                        title: '文件上传方子账号',
                        with: 150
                    },
                    {
                        field: 'uploadTime',
                        fieldLabelI18nKey: 'i18n_title_uploadTime',
                        title: '上传时间',
                        with: 150
                    },
                    {
                        field: 'fileName',
                        fieldLabelI18nKey: 'i18n_title_fileName',
                        title: '文件名称',
                        with: 150
                    }, {
                        field: 'filePath',
                        fieldLabelI18nKey: 'i18n_field_filePath',
                        title: '文件路径',
                        with: 150
                    }, {
                        field: 'fileSize',
                        fieldLabelI18nKey: 'i18n_field_fileSize',
                        title: '文件大小',
                        with: 150
                    }
                ]
            }
            this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        },
        compareFileTypes (file1, file2) {
            const getFileType = (filename) => {
                const fileExtension = filename.split('.').pop()
                return fileExtension
            }
            const type1 = getFileType(file1)
            const type2 = getFileType(file2)
            if (type1 == 'doc' && type2 == 'docx' || type1 == 'docx' && type2 == 'doc') return true
            if (type1 == 'xlsx' && type2 == 'xls' || type1 == 'xls' && type2 == 'xlsx') return true
            return type1 === type2
        },
        fieldSelectOk (data) {
            let flag = this.compareFileTypes(this.fileRow.fileName, data[0].fileName)
            if (!flag) return this.$message.warning('不同类型文件不做对比')
            this.$refs.detailPage.confirmLoading = true
            let fileCompareRow = {
                fileASourceId: this.fileRow.id,
                fileABusinessType: this.fileRow.businessType,
                fileAType: this.fileRow.fileType,
                fileAUploadElsAccount: this.fileRow.uploadElsAccount,
                fileAUploadSubAccount: this.fileRow.uploadSubAccount,
                fileAUploadTime: this.fileRow.uploadTime,
                fileAName: this.fileRow.fileName,
                fileAPath: this.fileRow.filePath,
                fileASize: this.fileRow.fileSize,
                fileBSourceId: data[0].id,
                fileBBusinessType: data[0].businessType,
                fileBType: data[0].fileType,
                fileBUploadElsAccount: data[0].uploadElsAccount,
                fileBUploadSubAccount: data[0].uploadSubAccount,
                fileBUploadTime: data[0].uploadTime,
                fileBName: data[0].fileName,
                fileBPath: data[0].filePath,
                fileBSize: data[0].fileSize
            }
            postAction('/compare/elsFileCompareHead/fileCompare', fileCompareRow).then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })

        },
        previewPdf () {
            let contentGrid = this.$refs.detailPage.$refs.purchaseContractContentItemList[0]
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.confirmLoading = true
            axios({
                url: '/contract/purchaseContractHead/download',
                responseType: 'blob',
                params: {id: this.currentEditRow.id}
            }).then((res) => {
                if (res) {
                    debugger
                    let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                    window.open(url)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        },
        showPublishConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            let contractStatus = params.contractStatus
            if ((auditStatus == '2') && (contractStatus == '1' || contractStatus == '5')) {
                return true
            } else {
                return false
            }
        },
        showVersionConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let contractStatus = params.contractStatus
            if (contractStatus == '4') {
                return true
            } else {
                return false
            }
        },
        showcAuditConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus == '3' || auditStatus == '0') {
                return true
            } else {
                return false
            }
        },
        publishEvent () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_releaseContract`, '发布合同'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sendContractTheSupplierAreYouSureSend`, '将合同发送给供应商，是否确认发送?'),
                onOk: function () {
                    getAction(that.url.public, {id: params.id}).then(res => {
                        if (res.success) {
                            that.form = res.result
                            that.$message.success(res.message)
                            that.init()
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })

        },
        upgradeVersionEvent () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractVersionChange`, '合同版本变更'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_upgradeVersionContractAreYouSureUpdateVersion`, '将合同进行版本升级，是否确认更新版本?'),
                onOk: function () {
                    getAction(that.url.upgradeVersion, {id: params.id}).then(res => {
                        if (res.success) {
                            that.form = res.result
                            that.$message.success(res.message)
                            that.init()
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        submitAudit () {
            let formData = this.$refs.detailPage.form
            if (formData.purchaseContractContentItemList.length == 0) {
                this.$message.warning('需配置合同条款库信息！')
                return
            }
            const fn = (data, vm) => {
                console.log('data :>> ', data)
                console.log('vm :>> ', vm) // 编辑模板组件实例
                if (data.status === 'error') {
                    this.$message.warning('请完善必填项')
                    return
                }

                let that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterSubmittingApprovalItcannotIsurSubmitApproval?`, '提交审批后将不能修改，是否确认提交审批?'),
                    onOk: function () {
                        that.auditPostData(that.url.submitAudit, 'audit')
                    }
                })
            }

            this.$refs.detailPage.handValidate('', null, fn)
        },
        auditPostData (invokeUrl, type) {
            this.$refs.detailPage.confirmLoading = true
            let formData = this.$refs.detailPage.form
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'contract'
            param['auditSubject'] = '合同编号：' + formData.contractNumber + ' ' + formData.contractName || ''
            // param['auditSubject'] = '合同编号：' + formData.contractNumber
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    if (type == 'audit') {
                        this.$parent.submitCallBack(formData)
                    } else if (res?.result?.auditStatus == '0') {
                        this.$parent.cancelAuditCallBack(formData)
                    }
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.init()
                this.$refs.detailPage.confirmLoading = false
            })
        },
        cancelAudit () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.auditPostData(that.url.cancelAudit, 'cancel')

                }
            })/*.finally(() => {
                that.init()
            })*/
        },
        showcCncelConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus != '1') {
                return false
            } else {
                return true
            }
        },
        showFlowConditionBtn () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus == '0' && auditStatus != '') {
                return false
            } else {
                return true
            }
        },
        showFlow () {
            let params = this.$refs.detailPage ? this.$refs.detailPage.form : {}
            this.flowId = params.flowId
            if (params.flowId) {
                this.flowView = true
            }
        },
        closeFlowView () {
            this.flowView = false
        },
        downloadWordFile () {
            let params = {id: this.$refs.detailPage.form.id}
            this.$refs.detailPage.confirmLoading = true
            axios({
                url: this.url.downloadDoc,
                responseType: 'blob',
                params: params
            }).then(res => {
                this.$refs.detailPage.confirmLoading = false
                let fieldName = this.currentEditRow.contractNumber + '_' + this.currentEditRow.contractVersion + '.doc'
                //console.log(res)
                const blob = new Blob([res])
                const blobUrl = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.style.display = 'none'
                a.href = blobUrl
                a.download = fieldName
                a.click()
            })
        },
        downloadFile () {
            let params = {id: this.$refs.detailPage.form.id}
            this.$refs.detailPage.confirmLoading = true
            axios({
                url: this.url.downloads,
                responseType: 'blob',
                params: params
            }).then(res => {
                this.$refs.detailPage.confirmLoading = false
                let fieldName = this.currentEditRow.contractNumber + '_' + this.currentEditRow.contractVersion + '.pdf'
                //console.log(res)
                const blob = new Blob([res])
                const blobUrl = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.style.display = 'none'
                a.href = blobUrl
                a.download = fieldName
                a.click()
            })
        }
    }
}
</script>