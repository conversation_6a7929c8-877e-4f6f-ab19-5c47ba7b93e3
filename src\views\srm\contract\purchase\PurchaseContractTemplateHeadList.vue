<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage && !showHisPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"/>
    <!-- 表单区域 -->
    <editPurchaseContractTemplate-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="modalForm"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
    <viewPurchaseContractTemplate-modal
      ref="detailPage"
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetail"
    />
    <purchaseContractTemplateHisList-modal
      v-if="showHisPage"
      :current-edit-row="currentEditRow"
      @hide="hideHisPage"
    />

  </div>
</template>
<script>
import EditPurchaseContractTemplateModal from './modules/EditPurchaseContractTemplateModal'
import ViewPurchaseContractTemplateModal from './modules/ViewPurchaseContractTemplateModal'
import PurchaseContractTemplateHisListModal from './PurchaseContractTemplateHisListModal'

import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, httpAction, postAction} from '@api/manage'

export default {
    mixins: [ListMixin],
    components: {
        'editPurchaseContractTemplate-modal': EditPurchaseContractTemplateModal,
        'viewPurchaseContractTemplate-modal': ViewPurchaseContractTemplateModal,
        'purchaseContractTemplateHisList-modal': PurchaseContractTemplateHisListModal
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            showHisPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTemplateCodeOrName`, '请输入模板编号或模板名称')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_templateStatus`, '模板状态'),
                        fieldName: 'templateStatus',
                        dictCode: 'srmTemplateStatus',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectTemplateStatus`, '请选择模板状态')
                    }

                ],
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus',
                        authorityCode: 'contractTemplate#purchaseContractTemplateHead:add',
                        clickFn: this.handleAddTemplate,
                        type: 'primary'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        folded: false,
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                form: {
                    keyWord: '',
                    templateStatus: ''

                },
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        authorityCode: 'contractTemplate#purchaseContractTemplateHead:view',
                        clickFn: this.showDetail
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        authorityCode: 'contractTemplate#purchaseContractTemplateHead:edit',
                        clickFn: this.handleEditInner,
                        allow: this.showEditCondition
                    },
                    {
                        type: 'history',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_numberOfHistory`, '历史记录'),
                        clickFn: this.handleHis,
                        allow: this.showHisCondition
                    },
                    {
                        type: 'copy',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_upgradeVersion`, '升级版本'),
                        authorityCode: 'contractTemplate#purchaseContractTemplateHead:upgradeVersion',
                        clickFn: this.changeVersion,
                        allow: this.showChangeVersionCondition
                    },
                    {
                        type: 'copy',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'),
                        authorityCode: 'contractTemplate#purchaseContractTemplateHead:copy',
                        clickFn: this.handleCopy
                    },
                    {
                        type: 'cancel',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                        clickFn: this.handleCancel,
                        allow: this.showCancelCondition,
                        authorityCode: 'contractTemplate#purchaseContractTemplateHead:cancel'
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        authorityCode: 'contractTemplate#purchaseContractTemplateHead:delete',
                        clickFn: this.handleDelete,
                        allow: this.showDeleteCondition
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ],
                // eslint-disable-next-line no-dupe-keys
                optColumnWidth: 300
            },
            url: {
                list: '/contract/purchaseContractTemplateHead/list',
                delete: '/contract/purchaseContractTemplateHead/delete',
                copy: '/contract/purchaseContractTemplateHead/copy',
                cancel: '/contract/purchaseContractTemplateHead/cancel',
                exportXlsUrl: 'contract/purchaseContractTemplateHead/exportXls',
                changeVersion: 'contract/purchaseContractTemplateHead/upgradeVersion',
                columns: 'purchaseContractTemplateHeadList'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTemplate`, '合同模板'))
        },
        handleAddTemplate () {
            this.currentEditRow = null
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        showEditCondition (row) {
            if (row.templateStatus == '1' && row.auditStatus == '0' || row.auditStatus == '3') {
                return false
            } else {
                return true
            }
        },
        showCancelCondition (row) {
            if (row.templateStatus == '1' && row.auditStatus == '2' || row.auditStatus == '4') {
                return false
            } else {
                return true
            }
        },
        // 复制功能按钮
        handleCopy (row) {
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
                onOk: () => {
                    that.copyData(row)
                }
            })
        },
        handleCancel (row) {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoid`, '确认作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voidSelectedData`, '是否作废选中数据?'),
                onOk: () => {
                    this.loading = true
                    //postAction,
                    postAction(this.url.cancel, {id: row.id}).then(res => {
                        if (res.success) {
                            this.$message.success(res.message)
                            this.searchEvent()
                        } else {
                            this.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        // 复制数据请求接口
        copyData (row) {
            this.confirmLoading = true
            let param = {id: row.id}
            getAction(this.url.copy, param).then(res => {
                if (res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功！'))
                    this.searchEvent()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        showChangeVersionCondition (row) {
            if (row.auditStatus == '2') {
                return false
            } else {
                return true
            }
        },
        handleEditInner (row) {
            this.currentEditRow = row
            //this.$refs.modalForm.handleEdit(row)
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        showHisCondition (row) {
            if (row.contractTemplateVersion > 1) {
                return false
            } else {
                return true
            }
        },
        showDeleteCondition (row) {
            if (row.contractTemplateVersion == 1 && (row.auditStatus == '0' || row.auditStatus == '3')) {
                return false
            } else {
                return true
            }
        },
        handleHis (row) {
            this.currentEditRow = row
            this.showHisPage = true
        },
        hideHisPage () {
            this.showHisPage = false
        },
        showDetail (row) {
            this.currentEditRow = row
            this.showDetailPage = true
        },
        hideDetail () {
            this.showDetailPage = false
        },
        submitCallBack (row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        init () {
            this.searchEvent()
        },
        cancelAuditCallBack () {
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        changeVersion (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_versionUpgrade`, '版本升级'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterVersionUpgradedItNeedsToApprovedConfirmWhetherUpgrade`, '版本升级后需重新审批，确认是否升级?'),
                onOk: function () {
                    that.postData(row)
                }
            })
        },
        postData (param) {
            this.$refs.listPage.confirmLoading = true
            httpAction(this.url.changeVersion, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        }

    }
}
</script>