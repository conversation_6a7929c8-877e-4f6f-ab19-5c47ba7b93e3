<template>
  <div class="TemplateLibrary">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :url="url"
      @afterChangeTab="handleAfterChangeTabOpt" />
    <TemplateLibraryEdit
      v-if="showEditPage"
      @copyRow="handleCopy"
      @hidden="hidden"
      :currentEditRow="currentEditRow" />
    <TemplateLibraryDetail
      v-if="showDetailPage"
      @copyRow="handleCopy"
      @hidden="hidden"
      :currentEditRow="currentEditRow" />
  </div>
</template>

<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import TemplateLibraryEdit from './modules/TemplateLibraryEdit'
import TemplateLibraryDetail from './modules/TemplateLibraryDetail'
import { getAction} from '@/api/manage'
import { srmI18n, getLangAccount } from '@/utils/util'
export default {
    mixins: [ListMixin],
    components: {
        TemplateLibraryEdit,
        TemplateLibraryDetail
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            pageData: {
                businessType: 'tenderTemplateLibrary',
                formField: [
                    {
                        type: 'input',
                        label: srmI18n(`${getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseEnterCodeName`, '请输入编码或名称')
                    }
                ],
                form: {
                },
                button: [
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns }

                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, key: 'edit'},
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, key: 'delete'},
                    { type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopy, allow: this.allowCopy, key: 'copy'}
                ]
            },
            tabsList: [],
            url: {
                list: '/tender/template/purchaseTenderTemplateLibrary/list',
                delete: '/tender/template/purchaseTenderTemplateLibrary/delete',
                copyTemplate: '/tender/template/purchaseTenderTemplateLibrary/copyTemplate',
                columns: 'PurchaseTenderTemplateLibraryList'
            },
            currentEditRow: {}
        }
    },
    methods: {
        allowCopy (row) {
            if (row.status == '0') return true
            return false
        },
        handleAdd () {
            this.currentEditRow = {}
            this.showEditPage = true
        },
        handleCopy (row) {
            let {status, id} = row
            if (status != '1') return this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROBRIhxIr_4dbaeb66`, '只能复制已发布模板'))
            getAction(this.url.copyTemplate, {id}).then(res => {
                let type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.$refs.listPage.loadData()
                }
            })
        },
        hidden () {
            this.showEditPage = false
            this.showDetailPage = false
            this.$refs.listPage.loadData()
        }
    }
}
</script>
<style lang="less" scoped>
.TemplateLibrary {
    height: 100%;
}
</style>

