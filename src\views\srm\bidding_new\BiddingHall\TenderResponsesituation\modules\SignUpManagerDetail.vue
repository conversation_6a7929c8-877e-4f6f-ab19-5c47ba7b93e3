<template>
  <div class="SignUpManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="flag"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :queryData="queryData"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >

      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
      <a-modal
        v-drag    
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_VWNUJII_b26103df`, '请输入审查意见')"
        :visible="prompt.visible"
        :confirm-loading="ifLoading"
        @cancel="prompt.visible=false"
        @ok="handlePromptOk"
      >
        <a-textarea
          :style="{width:'100%',height: '200px'}"
          v-model="prompt.value"/>
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction, getAction} from '@/api/manage'
import { ajaxFindDictItems } from '@/api/api'
// import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { USER_COMPANYSET, USER_ELS_ACCOUNT } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    name: 'SignUpManagerEdit',
    components: {
        BusinessLayout,
        EditGridLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        queryData: {
            type: Object,
            default () {
                return {}
            }
        },
        propOfCheckType: {
            default: () => {
                return ''
            },
            type: [String, Number]
        }
    },
    inject: ['subpackageId'],
    computed: {
        remoteJsFilePath () {
            console.log('editrow', this.currentEditRow)
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_SupplierTenderProjectSignUp_${templateNumber}_${templateVersion}`
            // return '100000/sale_SupplierTenderProjectSignUp_TC2022042101_1'
        },
        subId () {
            return this.subpackageId()
        }
    },
    data () {
        return {
            ifLoading: false,
            prompt: {
                visible: false,
                value: ''
            },
            flag: false,
            flowId: 0,
            auditVisible: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            opinion: '',
            flowView: false,
            pageStatus: 'detail',
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            requestData: {
                detail: {
                    url: '/tender/purchase/supplierTenderProjectSignUp/queryById',
                    args: (that) => {
                        return { 
                            id: that.currentEditRow.id
                        }
                    },
                    config: () => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {xNodeId: `${this.propOfCheckType || ''}_0`}
                        }
                    }
                }
            },
            externalToolBar: {
                
            },
            pageHeaderButtons: [],
            detailHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                    // click: this.goBack.bind(this)
                }
            ],
            auditHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_passed`, '通过'),
                    key: 'pass',
                    attrs: {
                        type: 'primary'
                    },
                    click: this.pass
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_FK_c764b`, '拒绝'),
                    key: 'reject',
                    attrs: {
                        type: 'primary'
                    },
                    click: this.reject
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                    // click: this.goBack.bind(this)
                }
            ],
            attachmentListData: {},
            url: {},
            userCompanyset: {},
            projectObj: {},
            formatType: {}
        }
    },
    async created () {
        console.log('row', this.currentEditRow)
        
        if (JSON.stringify(this.queryData) != '{}') {
            this.userCompanyset = this.$ls.get(USER_COMPANYSET)
        }
        if( this.currentEditRow.status == '1'){
            this.pageHeaderButtons=this.auditHeaderButtons
        }else{
            this.pageHeaderButtons=this.detailHeaderButtons
        }
        await this.getQueryFileType()
        this.flag = true
    },
    methods: {
        // 获取文件类型数据字典
        async getQueryFileType () {
            // 获取表格下拉字典
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'supplierSignUpFileType'
            }
            await ajaxFindDictItems(postData).then(res => {
                if (res.success) {
                    const RESULTDATA = res.result || []
                    let fileType = {}
                    RESULTDATA.forEach(data => {
                        fileType[data.value] = data.title
                    })
                    this.formatType = fileType
                }
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log(pageConfig, resultData)
            // -- start -- sb xiefa
            pageConfig.groups.forEach(group => {
                if (group.groupCode == 'attachmentList') {
                    console.log('formatType', this.formatType)
                    group.loadData.forEach(load => {
                        load['fileType_dictText'] = this.formatType[load.fileType] || ''
                        load['fileType'] = this.formatType[load.fileType] || ''
                    })
                }
            })
            // -- end ---
            pageConfig.groups[1]['extend'] = {
                optColumnList: [
                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                    { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                ]
            }
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
                if (key == 'purchaseEnterpriseName') {
                    formModel[key] = resultData[key] || this.userCompanyset.companyName
                }
            }
            
        },
        checkItemSelectOk (data) {
            console.log(data)
        },
        preViewEvent (Vue, row){
            row.subpackageId = this.subId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading=true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                this.confirmLoading=false
            })
        },
        handlePromptOk (){
            let { value } = this.prompt
            if(!value){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_FKCcxOLV_b34106ef`, '拒绝内容不能为空'))
                return
            }
            let url = '/tender/purchase/supplierTenderProjectSignUp/reject'
            // let params = {'id': this.currentEditRow.id, 'remark': value}
            this.ifLoading = true
            postAction(url, {id: this.currentEditRow.id, remark: value}, {headers: {xNodeId: `${this.propOfCheckType || ''}_0`}}).then((res) => {
                if(res.success){
                    this.pageHeaderButtons=this.detailHeaderButtons
                    this.$message.success(res.message)
                    this.prompt.visible=false
                }else{
                    this.$message.warning(res.message)
                }
            }).finally(()=>{
                this.ifLoading = false
            })
        },
        pass (){
            let url = '/tender/purchase/supplierTenderProjectSignUp/approved'
            this.confirmLoading = true
            getAction(url, {id: this.currentEditRow.id}, {headers: {xNodeId: `${this.propOfCheckType || ''}_0`}}).then((res) => {
                this.confirmLoading = false
                if(res.success){
                    this.pageHeaderButtons=this.detailHeaderButtons
                    this.$message.success(res.message)
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        reject (){
            this.prompt.visible=true
        }
    }
      
}
</script>
<style lang="less" scoped>
.registration-title{
    background-color: #eee;
    padding: 5px 10px;
    margin-bottom: 10px;
}
</style>


