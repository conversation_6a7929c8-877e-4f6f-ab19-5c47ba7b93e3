<!--
 * @Author: fzb
 * @Date: 2022-02-18 14:50:31
 * @LastEditTime: 2022-03-09 11:39:50
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\components\widgets\src\main.vue
-->

<template>
  <!-- <VueDragResize
    contentClass="drag-box-shaddow"
    :w="widget.location.width"
    :h="widget.location.height"
    :x="widget.location.left"
    :y="widget.location.top"
    :z="index"
    :isActive="widget.active"
    ref="draggable"
    @dragging="onDraggableEnd"
    @resizing="onDraggableEnd"
  >
    <template v-if="widget.active">
      <div class="line-left"></div>
      <div class="line-top"></div>
      <div class="line-label">
        {{ widget.location.left }},{{ widget.location.top }}
      </div>
    </template>
    <component :is="widget.entity" :widget="widget"></component>
  </VueDragResize> -->
</template>
<script>
// import ChartsBar from "../chartsBar";
// import ChartsGauge from "../chartsGauge";
// import ChartsPie from "../chartsPie";
// import ChartsRadar from "../chartsRadar";
// import VueDragResize from "vue-drag-resize";
// import ChartsLineChart from "../chartsLineChart";
// import ChartsSplashes from "../chartsSplashes";
// import ChartsKLine from "../chartsKLine";
// import BusinessByTable from "../businessByTable";
// import BusinessLeagueTable from "../businessLeagueTable";
// import BusinessDigitalFlop from "../businessDigitalFlop";

// export default {
//   name: "Widget",
//   components: {
//     VueDragResize,
//     ChartsBar,
//     ChartsGauge,
//     ChartsPie,
//     ChartsRadar,
//     AssistCustomText,
//     AssistDecorationOne,
//     AssistDecorationTwo,
//     AssistDecorationThree,
//     AssistDecorationTwoReverse,
//     AssistDecorationFour,
//     AssistDecorationFourReverse,
//     AssistDecorationFive,
//     AssistDecorationSix,
//     AssistDecorationSeven,
//     AssistDecorationEight,
//     AssistDecorationEightReverse,
//     AssistDecorationNine,
//     AssistDecorationZero,
//     AssistDecorationEleven,
//     AssistDecorationTwelve,
//     AssistBorderOne,
//     AssistBorderTwo,
//     AssistBorderThree,
//     AssistBorderFour,
//     AssistBorderFourReverse,
//     AssistBorderFive,
//     AssistBorderFiveReverse,
//     AssistBorderSix,
//     AssistBorderSeven,
//     AssistBorderEight,
//     AssistBorderEightReverse,
//     AssistBorderNine,
//     AssistBorderZero,
//     AssistBorderEleven,
//     AssistBorderTwelve,
//     AssistBorderThirteen,
//     ChartsLineChart,
//     ChartsSplashes,
//     ChartsKLine,
//     BusinessByTable,
//     BusinessLeagueTable,
//     BusinessDigitalFlop,   
//   },
//   props: {
//     index: {
//       type: Number,
//       default: () => {
//         return 1;
//       },
//     },
//     widget: {
//       type: [Object],
//       default: () => { },
//     },
//   },
//   data () {
//     return {
//     };
//   },
//   mounted () {
//   },
//   methods: {
//     onDraggableEnd ({ left, top, width, height }) {
//       this.widget.location.left = left;
//       this.widget.location.top = top;
//       this.widget.location.width = width;
//       this.widget.location.height = height;
//       this.$emit("refreshWidgetActive", this.widget.id);
//     },
//   },
// };
</script>

<style lang="less" scoped>
.drag-box-shaddow.active {
  background-color: rgba(115, 170, 229, 0.5);
}
.line-top {
  border-width: 1px;
  position: absolute;
  border-left: 1px dashed #fff;
  width: 0;
  height: 10000px;
  left: 0;
  transform: translateY(-100%);
}
.line-left {
  border-width: 1px;
  position: absolute;
  border-top: 1px dashed #fff;
  width: 10000px;
  height: 0;
  top: 0;
  transform: translateX(-100%);
}
.line-label {
  font-size: 18px;
  top: -5px;
  left: -8px;
  position: absolute;
  padding: 5px;
  transform: translate(-100%, -100%);
  color: #fff;
  font-size: 18px;
  white-space: nowrap;
  cursor: move;
}
</style>
