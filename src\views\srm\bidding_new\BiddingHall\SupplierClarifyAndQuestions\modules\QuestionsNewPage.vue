<template>
  <div class="page-container">
    <div
      class="edit-page"
      v-if="ifShow">
      <a-spin :spinning="confirmLoading">
        <div
          class="page-content">
          <a-form-model
            ref="baseForm"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            :rules="rules"
            :model="formData">
            <div class="deliveryTime">
              <div class="deliveryTime-title">
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_baseForm`, '基本信息') }}
              </div>
              <div class="deliveryTime-content">
                <a-row >
                  <a-col :span="8">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_fIty_39d394bf`, '答疑单号')"
                      prop="number">
                      <a-input
                        v-model="formData.mentoringNumber"
                        :disabled="true"
                      ></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_projectName`, '项目名称')"
                      prop="tenderProjectName">
                      <a-input
                        v-model="formData.tenderProjectName"
                        :disabled="true"></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称')"
                      prop="subpackageName">
                      <a-input
                        v-model="formData.subpackageName"
                        :disabled="true"></a-input>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_tLRL_2715c81b`, '单位名称')"
                      prop="questionerName">
                      <a-input
                        v-model="formData.questionerName"
                        :disabled="true"></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_QfBD_4603a317`, '问题标题')"
                      required
                      prop="title">
                      <a-input v-model="formData.title"></a-input>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="$srmI18n(`${$getLangAccount()}#i18n_field_QDCc_466e88be`, '问题内容')"
                      required
                      prop="replyContent">
                      <a-input v-model="formData.replyContent" ></a-input>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-form-model>

          <list-table
            ref="attachmentList"
            :pageData="pageData"
            :groupCode="groupCode"
            :externalToolBar="externalToolBar"
            :statictableColumns="statictableColumns"
            setGridHeight="500"
            :fromSourceData="formData.attachmentList"
            :showTablePage="false"
          >
          </list-table>

        </div>
        
        <div class="page-footer" >
          <a-button @click="() => {this.$emit('hide')}">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          <a-button
            @click="handleSave"
            type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
          <a-button
            @click="publish"
            type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_title_release`, '发布') }}</a-button>
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import listTable from '../../components/listTable'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    props: {
        inCurrentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        groupCode: {
            default: '',
            type: String
        },
        isEdit: {
            type: Boolean
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'currentSubPackage',
        'resetCurrentSubPackage'
    ],
    computed: {
        subId (){
            return this.subpackageId
        },
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    components: {
        listTable
    },
    data () {
        return {
            ifShow: false,
            tableData: [],
            labelCol: { span: 8 },
            wrapperCol: { span: 15 },
            confirmLoading: false,
            formData: {},
            rules: {
                title: [
                    {  required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QDBD_46717f5b`, '问题标题！') },
                    { max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符')}
                ],
                replyContent: [
                    { required: true,  message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_QDCcxOLVW_6d56bc13`, '问题内容不能为空!') },
                    { max: 1000, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '1000'}
                ]
            },
            headId: '',
            attachmentList: [],
            externalToolBar: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                    key: 'upload',
                    args: {
                        property: 'label', // 可省略
                        itemInfo: [], // 必传
                        disabledItemNumber: true,
                        action: '/attachment/saleAttachment/upload', // 必传
                        businessType: 'biddingPlatform', // 必传,
                        headId: '', // 必传
                        modalVisible: false // 必传
                    },
                    attr: this.attrHandle,
                    callBack: this.uploadCallBack
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete',
                    click: this.businessGridDelete
                }
            ],
            statictableColumns: [
                { type: 'checkbox', width: 40, fixed: 'left' }, 
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                {
                    groupCode: 'attachmentList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                    fieldLabelI18nKey: '',
                    field: 'fileName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '250'
                },
                {
                    groupCode: 'attachmentList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                    fieldLabelI18nKey: '',
                    field: 'uploadTime',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '250'
                },
                {
                    groupCode: 'attachmentList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人'),
                    fieldLabelI18nKey: '',
                    field: 'uploadElsAccount_dictText',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '250'
                },
                {
                    groupCode: 'attachmentList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLJey_ab46593d`, '上传人子账号'),
                    fieldLabelI18nKey: '',
                    field: 'uploadSubAccount_dictText',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '250'
                },
                {
                    groupCode: 'attachmentList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    fieldLabelI18nKey: '',
                    // fieldType: 'number',
                    // field: 'saleAmount',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '130',
                    slots: { default: 'grid_opration' }
                }
            ],
            pageData: {
                optColumnList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                        clickFn: this.downloadEvent
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        clickFn: this.preViewEvent
                    }
                ]
            },
            requestData: {
                detail: { url: '/tender/saleTenderMentoringHead/queryById', args: (that) => { return {id: that.inCurrentEditRow.id}}}
            },
            url: {
                add: '/tender/saleTenderMentoringHead/add',
                edit: '/tender/saleTenderMentoringHead/edit',
                // // fxw：你这里用错了接口，给你从发布换成了提交接口
                publish: '/tender/saleTenderMentoringHead/publish',
                templatePreview: '/base/barcode/purchaseBarcodeTemplateHead/templatePreview'

            }
        }
    },
    methods: {
        attrHandle (){
            return {
                sourceNumber: this.tenderCurrentRow.tenderProjectNumber || this.tenderCurrentRow.id || '',
                actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'
            }
        },
        // 预览
        preViewEvent (row){
            row.subpackageId = this.subPackageRow.id
            this.$previewFile.open({params: row })
        },
        // 下载
        async downloadEvent (row){
            console.log('jinlaile')
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const {fileName} = row
            row.subpackageId = this.subPackageRow.id
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        uploadCallBack (result) {
            // 插入分包ID分包名称项目ID项目名称
            result.map(item => {
                item['subpackageId'] = this.subPackageRow.id
                item['subpackageName'] = this.subPackageRow.subpackageName
                item['tenderProjectId'] = this.tenderCurrentRow.id
            })
            this.$refs['attachmentList'].insertAt(result, -1)
        },
        businessGridDelete () {
            this.$refs['attachmentList'].businessGridDelete()
        },
        externalAllData () {
            let {fullData} = this.$refs['attachmentList'].getTableData()
            return fullData
        },
        getValidatePromise () {
            return this.$refs['attachmentList'].getValidate()
        },
        async queryDetail () {
            if (this.requestData.detail) {
                let url = this.requestData.detail.url
                let args = this.requestData.detail.args(this)
                // 有id 才能请求,新建时是没有id的，不用请求查询接口
                if (args && args.id) {
                    this.confirmLoading = true
                    let query = await getAction(url, args)
                    this.confirmLoading = false
                    if (query && query.success) {
                        console.log(query.result)
                        this.formData = Object.assign({}, query.result)
                        this.$set(this.formData, 'replyContent', this.formData.saleTenderMentoringItemList[0].replyContent)
                        this.ifShow = true
                        // this.nodeListData = this.formData.attachmentList
                    } else {
                        this.$message.error(query.message)
                    }
                }
            }
        },
        handleSave (callBack = null){
            // 没id的新增情况
            let url = this.formData.id ? this.url.edit : this.url.add
            let obj = Object.assign({}, this.formData)
            let params = {
                ...obj,
                // id: this.headId,
                tenderProjectId: this.headId||this.tenderCurrentRow.id,
                subpackageId: this.subId()
            }
            // params.id= this.headId
            // 第二次编辑问题内容，进行修改的情况
            if(params.saleTenderMentoringItemList){
                params.saleTenderMentoringItemList[0].replyContent=params.replyContent || ''
            }
            params.attachmentList = this.externalAllData() || []
            params.saleTenderMentoringItemList=[obj]
            valiStringLength(params, [
                {field: 'title', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BD_d3171`, '标题')},
                {field: 'replyContent', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MBCc_28d55903`, '回复内容'), maxLength: 100}
            ])
            // console.log('this.subpackageId', this.subId())
            this.confirmLoading = true
            let result = 0
            console.log('parms', params)

            postAction(url, params).then(res => {
                if (res.success) {
                    this.formData = res.result
                    if (res.result.saleTenderMentoringItemList.length > 0) {
                        this.formData.replyContent = res.result.saleTenderMentoringItemList[0].replyContent
                    }
                    this.inCurrentEditRow = this.formData
                    result = 1
                    this.$message.success(res.message)
                    this.queryDetail()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
                if (callBack != null && result == 1) {
                    callBack()
                }
            })
        },
        publish (){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布?'),
                onOk: function () {
                    that.$refs.baseForm.validate(valid => {
                        if (valid) {
                            that.handleSave(()=>{
                                that.handlePublish()
                            })
                        }
                    })
                }
            })
        },
        handlePublish (){
            let that = this
            that.confirmLoading = true
            postAction(that.url.publish+'?id='+this.inCurrentEditRow.id).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    // this.$emit('resetCurrentSubPackage') || ''
                    this.resetCurrentSubPackage()
                    this.$emit('hide')
                }
            }).finally(() => {
                that.confirmLoading = false
            })
        }
    },
    watch: {
        formData: {
            immediate: true,
            handler (val) {
                this.externalToolBar[0].args.headId = val? val.id : ''
            }
        }
    },
    beforeMount () {
        let projectMsgUrl= '/tender/purchaseTenderProjectHead/querySubpackageInfoBySubpackageId'
        getAction(projectMsgUrl+'?subpackageId='+this.subId(), {}).then((res) => {
            if (res.success) {
                console.log('res', res)
                this.headId=res.result.headId
                this.formData.tenderProjectName=res.result.tenderProjectName
                this.formData.subpackageName=res.result.subpackageName
                // 单位名称
                this.formData.questionerName=this.$ls.get(USER_COMPANYSET).companyName
                this.queryDetail()
                this.$forceUpdate()
            }
        }).finally(() => {
            // 若新增提出问题界面，无数据不走querydetail接口，则页面不展示，需手动展示页面
            if(!this.ifShow){
                this.ifShow = true
            }
        })
    },
    mounted (){
        this.attachmentList = this.formData.attachmentList || []
    }
}
</script>
<style lang="less" scoped>
.deliveryTime-title, .clarification-title{
  padding: 8px;
  background: #F2F3F5;
}
</style>


