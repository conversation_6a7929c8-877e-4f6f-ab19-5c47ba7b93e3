<script lang="jsx">
export default {
    name: 'MenuPart',
    props: {
        menus: {
            type: Array,
            default () {
                return []
            }
        },
        mainSupplierInfo: {//主体供应商信息
            type: Object,
            default: ()=>{
                return {
                    enterpriseLogo: '',
                    name: ''
                }
            }
        }
    },
    data (){
        return {
            curMenuIndex: 0//当前menus
        }
    },
    components: {
    },
    watch: {
    },
    methods: {
        //导航刷新
        handleResetClick (e, n){
            this.$emit(`content-reset-header-${e.type}`, n)
        },
        // 导航点击
        handleClick (e, n, index) {
            this.curMenuIndex=index
            this.$emit(`content-header-${e.type}`, n)
        }
    },
    render () {
        let { menus } = this
        const menusEl = menus.map((n, index)=> (
            <a-col
                span={6}
                onClick={ (event) => this.handleClick(event, n, index) }>
                <a-card bordered={false}  class={this.curMenuIndex==index?'menus-active':''}>
                    <div class="content">

                        <div class="content-icon">
                            <img src= {require(`./../img/venture-${index+1}.png`)} />
                        </div>

                        <div class="content-txt">
                            <div class="title"><p class="title-p"> 
                         
                                <a-tooltip placement="topLeft">
                                    <span slot='title'> {n.title}</span>
                                    <span > {n.title}</span>
                                </a-tooltip>
                                       
                            </p>  <a-button size='small'  vOn:click_stop={ (event) => this.handleResetClick(event, n, index) }>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refresh`, '刷新')} </a-button></div>
                            <div class="desc m-t-8">
                                <span class='txt title-p'>
                                 
                                  
                                    <a-tooltip placement="topLeft">
                                        <span slot='title'> {n.desc}</span>
                                        <span > {n.desc}</span>
                                    </a-tooltip>
                                       
                                </span>
                                {true|| <span class="red" > <b>{n.number}</b>条</span>} 
                            </div>

                           
                        </div>

                    </div>
                </a-card>
            </a-col>
        ))

        const returnNameFrist=(val)=>{

            return val?val.charAt(0):''
        }

        return (


            <div class="menu-part">
                <div class="top-wrap">
                    <div class="warp-company">
                        <a-card bordered={false}>
                            <div class="content">
                                <div class="content-icon">
                                    {this.mainSupplierInfo.enterpriseLogo?<img src= {this.mainSupplierInfo.enterpriseLogo} /> : <span class="logo-name">{ returnNameFrist(this.mainSupplierInfo.name)}</span> }
                                </div>

                                <div class="content-txt">
                                    <div class="title-one" title={Object.keys(this.mainSupplierInfo).length>0?this.mainSupplierInfo.name:''}>{Object.keys(this.mainSupplierInfo).length>0?this.mainSupplierInfo.name:''}</div>

                                </div>

                            </div>
                        </a-card>
                    </div>
                    <div  class="warp-supplist">
                        <a-row gutter={16}>
                            {menusEl}
                        </a-row>
                    </div>
                </div>
            </div>
        )
    }
}
</script>

<style lang="less" scoped>
.menu-part{
    :deep(.ant-card-body){
        padding: 12px;
    }
    .m-t-8{
        margin-top: 8px;
    }
    .logo-name{
        background-color: #1890ff;
        display: inline-block;
        width: 55px;
        height: 55px;
        border-radius: 5px;
        text-align:center;
        font-size: 20px;
        color: #fff;
        line-height: 55px;
    }
    .menus-active{
        box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.16);
        transition: all 1s;
    }
    .top-wrap{
        display: flex;
        flex-direction: row;
        .warp-company{
            width: 23%;
        }
        .warp-supplist{
            flex: 1;
            margin-left: 12px;
            :deep(.ant-col-6){
              padding: 0px 4px!important;
            }
        }
       :deep(.ant-col){
           cursor: pointer;
       }
              .content{
        display: flex;
        align-items: center;
        .content-icon{
            width: 55px;
            height: 55px;
            img{
                width: 100%;
            }
        }
        .content-txt{
            flex: 1;
            margin-left: 12px;

            .title{
                display: flex;
                font-size: 16px;
                font-weight: 600;
                color: #606060;
                overflow: hidden;
                text-overflow: ellipsis;

                white-space: nowrap;
                    justify-content: space-between;

                
            }
            .title-one{
              font-size: 16px;
              font-weight: 600;
              color: #606060;
              margin-bottom: 8px;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 185px;
              white-space: nowrap
            }
              .title-p{
                    width: 95px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                     margin-bottom: 0px;
                }
            .desc{
                display: flex;
                justify-content: space-between;
                color: #798087;
                font-size: 12px;
                .red{
                   b{color: #F71515;
                }}
            }
        }
    }
    }

}

</style>