<template>
  <div class="TreeComponent">
    <a-input-search 
      style="margin-bottom: 8px" 
      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_keyword`, '关键字')"
      @change="onChange" />
    <a-tree
      class="tree"
      :style="{height: minHeight+'px'}"
      :expanded-keys="expandedKeys"
      :auto-expand-parent="autoExpandParent"
      :tree-data="treeData"
      @expand="onExpand"
      @select="onSelect">
      <template 
        slot="title" 
        slot-scope="{title, children, showParamName, paramContent}">
        <span v-if="children">{{ title }}</span>
        <span v-else>
          <span v-if="showParamName.indexOf(searchValue) > -1">
            {{ showParamName.substr(0, title.indexOf(searchValue)) }}
            <span style="color: blue;">{{ searchValue }}</span>
            {{ showParamName.substr(showParamName.indexOf(searchValue) + searchValue.length) }}
          </span>
          <span v-else>{{ showParamName }}</span>
          <a-tooltip placement="top">
            <template slot="title">
              <span>{{ paramContent }}</span>
            </template>
            <a-icon type="question-circle" />
          </a-tooltip>
        </span>
      </template>
    </a-tree>
  </div>
</template>

<script>
import {Tree} from 'ant-design-vue'
import { getAction} from '@/api/manage'

const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.children) {
            if (node.children.some(item => item.key === key)) {
                parentKey = node.key
            } else if (getParentKey(key, node.children)) {
                parentKey = getParentKey(key, node.children)
            }
        }
    }
    return parentKey
}

export default {
    components: {
        ATree: Tree
    },
    data () {
        return {
            expandedKeys: [], // 指定展开的树节点
            searchValue: '',
            autoExpandParent: true, // 是否自动展开父节点
            treeData: [],
            dataList: [],
            url: {
                getList: '/tender/template/purchaseTenderVariableLibrary/queryList'
            },
            minHeight: 100
        }
    },
    created () {
        this.minHeight = document.documentElement.clientHeight - 500
        this.getData()
    },
    methods: {
        getData () {
            getAction(this.url.getList).then(res => {
                if (res.success) {
                    this.treeData = res.result.map(item => {
                        // item['disabled'] = true
                        item.children.map(c => {
                            c['key'] = c.id
                            c['title'] = c.showParamName || c.paramName
                        })
                        return item
                    })
                    this.generateList(this.treeData)

                }
            })
        },
        generateList (data) {
            for (let i = 0; i < data.length; i++) {
                const node = data[i]
                this.dataList.push({ ...node })
                if (node.children) {
                    this.generateList(node.children)
                }
            }
        },
        onSelect (key, event) {
            this.dataList.forEach(item => {
                if (item.id && item.key == key) {
                    this.$emit('onClick', item.paramName)
                }
            })
        },
        onExpand (expandedKeys) {
            this.expandedKeys = expandedKeys
            this.autoExpandParent = false
        },
        onChange (e) {
            const value = e.target.value
            const expandedKeys = this.dataList.map(item => {
                if (item.title.indexOf(value) > -1) {
                    return getParentKey(item.key, this.treeData)
                }
                return null
            }).filter((item, i, self) => item && self.indexOf(item) === i)
            Object.assign(this, {
                expandedKeys,
                searchValue: value,
                autoExpandParent: true
            })
        }
    }
}
</script>

<style lang="less" scoped>
.tree{
    overflow: auto;
}
</style>