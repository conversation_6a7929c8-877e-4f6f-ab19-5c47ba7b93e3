<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="edit"
  
        v-on="businessHandler"
      >
      </business-layout>
  
      <field-select-modal
        ref="fieldSelectModal"
        @ok="fieldSelectOk"
        isEmit
      />
      <ItemImportExcel
        ref="itemImportExcel"
        @importCallBack="importCallBack"/>
      <a-modal
        v-drag    
        v-model="showNode"
        :footer="null"
        width="1200px"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_QLIrUB_58cb0bac`, '流程模板预览')" >
        <IotStep
          :stepList="periodTypeInfoArray"
          :allNodeMap="allNodeMap">
        </IotStep>
      </a-modal>
    </a-spin>
  </div>
</template>
  
<script lang="jsx">
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import IotStep from '../../TenderProcessModelHead/modules/components/IotStep'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
  
export default {
    name: 'PurchaseTenderProjectHead',
    components: {
        BusinessLayout,
        fieldSelectModal,
        IotStep,
        ItemImportExcel
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            businessRefName: 'businessRefName',
            submit: '/a1bpmn/audit/api/submit',
            params: {},
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            showTenderProcess: false,
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                detail: { url: '/tender/purchaseTenderProjectHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                projectItemList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VaSSLyWF_5f640073`, '新增无物料号数据'),
                        attrs: {
                            type: 'primary'
                        },
                        click: this.addNoMaterialItem, authorityCode: 'tender#purchaseTenderProjectHead:noMaterialAdd'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SLGzRiF_fb0edbb9`, '物料库批量选择'),
                        attrs: {
                            type: 'primary'
                        },
                        click: this.addBiddingItem, authorityCode: 'tender#purchaseTenderProjectHead:add'
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteItemEvent, authorityCode: 'tender#purchaseTenderProjectHead:delete'},
                    // {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'),
                    //     key: 'fillDown',
                    //     type: 'tool-fill',
                    //     beforeCheckedCallBack: this.fillDownGridItem
                    // },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
                        authorityCode: 'tender#purchaseTenderProjectHead:importExcel',
                        params: this.importParams,
                        click: this.importExcel
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/purchaseTenderProjectHead/edit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    click: this.handleSave,
                    showMessage: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    click: this.handleBack
                }
            ],
            allNodeMap: {},
            periodTypeInfoArray: {},
            showNode: false,
            url: {
                save: '/tender/purchaseTenderProjectHead/edit',
                publish: '/tender/purchaseTenderProjectHead/publish',
                submit: '/a1bpmn/audit/api/submit',
                queryByGruop: '/tender/tenderProcessModelHead/queryNodeByGruop'
                // uploadAction: '/a1bpmn/audit/api/submit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            console.log(templateNumber, templateVersion, account)
            return `${account}/purchase_biddingPlatform_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleBack (){
            this.$emit('hide')
        },
        importCallBack (result) {
            if (result.file.status === 'done') {
                let response = result.file.response
                if (response.success) {
                    let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
                    let insertData = response.result.dataList
                    let pageConfig = itemGrid.pageConfig.groups[1]
                    pageConfig.columns.forEach(item => {
                        if (item.defaultValue) {
                            insertData.forEach(insert => {
                                if (!insert[item.field]) {
                                    insert[item.field] = item.defaultValue
                                }
                            })
                        }
                    })
                    itemGrid.$refs.projectItemList.insertAt(insertData, -1)
                } else {
                    this.$message.warning(response.message)
                }
            }
        },
        importParams () {

            const form = this.$refs.businessRefName.$refs.projectItemListgrid[0]
            return {'id': this.currentEditRow.id, 'templateAccount': form.currentEditRow.templateAccount, 'templateNumber': form.currentEditRow.templateNumber, 'templateVersion': form.currentEditRow.templateVersion,
                'handlerName': 'purchaseProjectItemExcelRpcServiceImpl', 'roleCode': 'purchase',
                'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息')}
        },
        importExcel () {
            if(!this.currentEditRow.id){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return false
            }
            const form = this.$refs.businessRefName.$refs.projectItemListgrid[0]
            let params = {'id': this.currentEditRow.id, 'templateAccount': form.currentEditRow.templateAccount, 'templateNumber': form.currentEditRow.templateNumber, 'templateVersion': form.currentEditRow.templateVersion,
                'handlerName': 'purchaseProjectItemExcelRpcServiceImpl', 'roleCode': 'purchase', 'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息')}
            this.$refs.itemImportExcel.open(params, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingBankInfo`, '招标行信息'), 'approvalItemListgrid')
        },
        deleteItemEvent () {
            let ifValid = true
            let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
            console.log(this.$refs.businessRefName)
            let checkboxRecords = itemGrid.$refs.projectItemList.getCheckboxRecords()
            if(checkboxRecords.length != 0){
                checkboxRecords.forEach(item=>{
                    if(item.id){
                        ifValid = false
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xiTQGIsMRjSLc_a90424c7`, '不允许删除已保存过的物料行！'))
                        throw Error('不允许删除已保存过的物料行！')
                    }
                })
            }
            // if(!checkboxRecords.length) {
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
            //     return
            // }
            if(ifValid){
                itemGrid.$refs.projectItemList.removeCheckboxRow()
            }
        },
        // 补充物料
        addBiddingItem () {
            this.selectType = 'material'
            console.log(this.$refs.businessRefName.$refs.projectItemListgrid[0])
            const form = this.$refs.businessRefName.$refs.projectItemListgrid[0].gridData
            const { mustMaterialNumber = '1' } = form
            if(mustMaterialNumber == '1'){
                let url = '/material/purchaseMaterialHead/list'
                let columns = [
                    {
                        field: 'cateCode',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
                        width: 150
                    },
                    {
                        field: 'cateName',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
                        width: 150
                    },
                    {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150},
                    {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150},
                    {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200},
                    {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200}
                ]
                this.$refs.fieldSelectModal.open(url, {blocDel: '0', freeze: '0'}, columns, 'multiple')
            }else{
                console.log('进入了else')
                let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
                let itemData = {}
                this.pageConfig.itemColumns.forEach(item => {
                    if(item.defaultValue) {
                        itemData[item.field] = item.defaultValue
                    }
                })
                itemGrid.insertAt([itemData], -1)
            }
        },
        addNoMaterialItem () {
            let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0].$refs.projectItemList
            let itemData = {}
            itemGrid.insertAt([itemData], -1)
        },
        fieldSelectOk (data) {
            let itemGrid = this.$refs.businessRefName.$refs.projectItemListgrid[0]
            let {tableData} = itemGrid.$refs.projectItemList.getTableData()
            let materialList = tableData.map(item => {
                return item.materialId
            })
            //过滤已有数据
            let insertData = data.filter(item => {
                if(materialList.includes(item.id)){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSVBxVSL_321b5369`, '请勿重复补充物料！'))
                    return false
                }
                return true
                // return !materialList.includes(item.materialNumber)
            })
            itemGrid.pageConfig.groups[2].columns.forEach(item => {
                if(item.defaultValue) {
                    insertData.forEach(insert => {
                        if(!insert[item.field]){
                            insert[item.field] = item.defaultValue
                        }
                    })
                }
            })
            insertData.forEach(insert => {
                insert['materialId'] = insert['id']
                delete insert.id
            })
            // let param = {
            //     'purOrgCode': this.$refs.businessRefName.$refs.projectItemListgrid[0].gridData.purchaseOrg,
            //     'materialDataVos': data
            // }
            // postAction(this.url.materialBidding, param).then(res => {
            //     if(res.success){
            //         itemGrid.insertAt(insertData, -1)
            //     } else {
            //         this.$confirm({
            //             title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLSu_38d85f7b`, '确认添加'),
            //             content: res.message,
            //             onOk: function () {
            //                 itemGrid.insertAt(insertData, -1)
            //             }
            //         })
            //     }
            // })
            console.log(insertData)
            // 当补充的物料在表行中不存在时，则保存不重复的物料
            if(insertData.length >0){
                itemGrid.$refs.projectItemList.insertAt(insertData, -1)
                
            }
        },
        handleBeforeRemoteConfigData (remoteData) {
            console.log('remoteData', remoteData)
            remoteData.groups.forEach(item=>{
                if(item.groupCode == 'projectItemList'){
                    item.show = true
                    item.extend={
                        editConfig: {
                            trigger: 'click', mode: 'cell', activeMethod: this.activeRowMethod
                        }
                    }
                }else{
                    item.show = false
                }
                
            })
            remoteData.itemColumns.forEach(item2=>{
                if(item2.field == 'materialNumber'){
                    item2.fieldType = 'selectModal'
                    item2.bindFunction = function (row, data, Vue) {
                        row.cateCode = data[0].cateCode
                        row.cateName = data[0].cateName
                        row.materialName = data[0].materialName
                        row.materialDesc = data[0].materialDesc
                        row.materialSpec = data[0].materialSpec
                        row.materialNumber = data[0].materialNumber
                        row.materialId = data[0].id
                        if(row.id){
                            Vue.confirmLoading = true
                            postAction('/tender/purchaseTenderProjectHead/replenishMaterialNumber', row).then(res => {
                                if(res.success) {
                                    row.materialModel = res.result.materialModel||''
                                    row.materialName = res.result.materialName||''
                                    row.materialDesc = res.result.materialDesc||''
                                    row.materialSpec = res.result.materialSpec||''
                                    row.materialNumber = res.result.materialNumber||''
                                    row.materialId = res.result.materialId||''
                                    Vue.queryDetail()
                                    Vue.confirmLoading = false
                                }else{
                                    Vue.$message.error(Vue.$srmI18n(`${Vue.$getLangAccount()}#i18n_field_VsK_21f9ddf`, '请重试'))
                                    Vue.confirmLoading = false
                                    return false
                                }
                            })
                        }
                    },
                    item2.extend={
                        // requestMethod: 'POST',
                        modalColumns: [
                            {
                                field: 'cateCode',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
                                width: 150
                            },
                            {
                                field: 'cateName',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
                                width: 150
                            },
                            {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编号'), width: 150},
                            {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150},
                            {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200},
                            {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200}
                        ],
                        modalUrl: '/material/purchaseMaterialHead/list',
                        modalParams: function (Vue, form, row){
                            return {row}
                        }
                        // beforeCheckedCallBack: function (Vue, row, groupData, form) {
                        //     console.log(Vue, row, groupData, form)
                        //     return new Promise((resolve, reject) => {
                        //         let id = row.id || ''
                        //         return id != '' ? resolve('success') : reject('请先保存单据！')
                        //     })
                        // }
                        // afterClearCallBack: function (form, pageData) {
                        //     pageData.materialNumber = ''
                        // }
                    }
                }
                if(item2.field == 'materialName'){
                    item2.editRender= {name: '$input'}
                    item2.required = '1'
                }
            })
        },
        activeRowMethod (that, row, rowIndex, column, columnIndex) {
            console.log(that, row, rowIndex, column, columnIndex)
            // 有物料编码且物料名称的行，不允许再编辑物料名称
            if(column.field == 'materialName' && row.materialName && row.materialNumber){
                // 行物料名称为空时才可编辑
                return false
            }else{
                return true
            }
        },
        // 保存
        handleSave (args){
            this.stepValidate(args).then(()=>{
                let url = '/tender/purchaseTenderProjectHead/replenish'
                let {fullData} = this.$refs.businessRefName.$refs.projectItemListgrid[0].$refs.projectItemList.getTableData()
                let params = {
                    ...this.currentEditRow,
                    projectItemList: fullData
                }
                this.confirmLoading = true
                postAction(url, params).then(res => {
                    if(res.success) {
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_savaSuccess`, '保存成功'))
                        this.$refs.businessRefName.queryDetail()
                        this.confirmLoading = false
                    }else{
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VsK_21f9ddf`, '请重试'))
                        this.confirmLoading = false
                        return false
                    }
                })
            })
            
        }
    },
    created (){
        console.log('@#@##@#', this.currentEditRow)
    }
  
}
</script>