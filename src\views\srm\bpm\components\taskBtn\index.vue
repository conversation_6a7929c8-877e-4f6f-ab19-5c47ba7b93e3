<template>
  <div
    class="taskBtn noprint"
    v-if="taskInfo.taskId">
    <template v-if="multiInstanceTaskBtns && multiInstanceTaskBtns.length > 0">
      <a-button
        type="primary"
        v-for="(item, index) in taskSequence"
        :key="item.alias + index"
        @click="handleClick(item, item.alias)">
        {{ item.btnName || item.title }}
      </a-button>
      <a-button
        type="primary"
        v-for="(item, index) in multiInstanceTaskBtns"
        :key="index"
        @click="handleClick(item)">
        {{ item.btnName || item.title }}
      </a-button>
    </template>

    <template v-if="taskBtnList.length > 0">
      <a-button
        type="primary"
        v-for="(item, index) in taskSequence"
        :key="'sequence' + index"
        @click="item.click ? item.click() : handleClick(item, 'sequence')">
        {{ item.btnName || item.title }}
      </a-button>
      <a-button
        type="primary"
        v-for="(item, index) in normalBtn"
        :key="item.btnName + index"
        @click="item.click ? item.click(that) : handleClick(item)">
        {{ item.btnName || item.title }}
      </a-button>
      <a-dropdown v-if="dropdownBtn.length > 0">
        <a-menu slot="overlay">
          <a-menu-item
            @click="(e) => { handleMenuClick(e, item) }"
            v-for="item in dropdownBtn"
            :key="item.alias ? item.alias : item.key">{{ item.btnName || item.title }}
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> {{ $srmI18n(`${$getLangAccount()}#i18n_title_more`, '更多') }} <a-icon
          type="down" /> </a-button>
      </a-dropdown>
    </template>
    <a-button
      v-if="btnBack"
      @click="btnBack.click">
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}
    </a-button>
    <a-button
      v-print="printObj"
      ref="printBtn"
      v-show="false"></a-button>
    <a-modal :width="diaLogWidth" :dialogStyle="{top: '2px', left: diaLogLeftLocation}"
      v-drag
      class="actionModal"
      v-bind="{ ...modalForm }"
      v-if="modalForm.visible"
      :maskClosable="false"
      @ok="modalForm.handleOk"
      :confirm-loading="confirmLoading"
      @cancel="modalForm.handleCancel">
      <div class="modalCtn">
        <agreeModal
          ref="agreeModal"
          :task="task"
          :taskInfo="taskInfo"
          @success="modalFormSuccess"
          :resultData="resultData"
          :currentEditRow="currentEditRow"
          @closeLoading="closeLoading"
          v-if="['agree', 'pass', 'agreeAdHocSubProcess'].includes(alias)"
          :taskId="taskInfo.taskId" />
        <unPassModal
          ref="unPassModal"
          :task="task"
          :taskInfo="taskInfo"
          :resultData="resultData"
          :currentEditRow="currentEditRow"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          v-if="alias == 'unPass'"
          :taskId="taskInfo.taskId" />
        <informedModal
          ref="informedModal"
          :task="task"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          v-if="alias == 'informed'"
          :taskId="taskInfo.taskId" />
        <transferTaskModal
          ref="transferTaskModal"
          :task="task"
          :taskId="taskInfo.taskId"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          v-if="alias == 'transferTask'" />
        <backToNodeModal
          ref="backToNodeModal"
          :task="task"
          :taskId="taskInfo.taskId"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          v-if="alias == 'backToNode'" />
        <backAddPreNodeModal
          ref="backAddPreNodeModal"
          :task="task"
          :taskId="taskInfo.taskId"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          v-if="alias == 'backAddPre'" />
        <approvalHistoryModal
          ref="approvalHistoryModal"
          :task="task"
          :taskId="taskInfo.taskId"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          :processInstanceId="processInstanceId"
          :visible="modalForm.visible"
          v-if="alias == 'approvalHistory'" />
        <!-- 点击添加临时任务按钮后右侧展示流程图，左侧展示临时任务 -->
        <div
          v-if="alias == 'addNode'"
          style="display: flex;">


          <div
            style="flex:2"
            ref="flowImageModal">
            <iframe
              scrolling="no"
              id="flowImageframe"
              width="100%"
              style="min-height: 400px"
              height="99.9%"
              frameborder="0"
              align="center"
              allowfullscreen="true"
              allow="autoplay"
              :src="flowImage.src" />
          </div>
          <div style="flex: 1;padding-top: 25px">
            <addNodeModal
              ref="addNodeModal"
              :task="task"
              :taskId="taskInfo.taskId"
              @success="modalFormSuccess"
              @closeLoading="closeLoading" />
          </div>
        </div>
        <!-- <addNodeModal
          ref="addNodeModal"
          :task="task"
          :taskId="taskInfo.taskId"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          v-if="alias == 'addNode'"
        /> -->
        <!-- 点击添加会签按钮后右侧展示流程图，左侧展示临时节点 -->
        <div
          v-if="alias == 'addSignNode'"
          style="display: flex;justify-content: space-between;">
          <div
            style="flex:2"
            ref="flowImageModal">
            <iframe
              scrolling="no"
              id="flowImageframe"
              width="100%"
              style="min-height: 400px"
              height="99.9%"
              frameborder="0"
              align="center"
              allowfullscreen="true"
              allow="autoplay"
              :src="flowImage.src" />
          </div>
          <div style="flex:1;padding-top: 25px;">
            <addSignNodeModal
              ref="addSignNodeModal"
              :task="task"
              :taskId="taskInfo.taskId"
              @success="modalFormSuccess"
              @closeLoading="closeLoading" />
          </div>

        </div>
        <!-- 前加签按钮 -->
        <div
          v-if="alias == 'addSign'"
          style="display: flex;justify-content: space-between;">
          <div
            style="flex:2"
            ref="flowImageModal">
            <iframe
              scrolling="no"
              id="flowImageframe"
              width="100%"
              style="min-height: 400px"
              height="99.9%"
              frameborder="0"
              align="center"
              allowfullscreen="true"
              allow="autoplay"
              :src="flowImage.src" />
          </div>
          <div style="flex:1;padding-top: 25px;">
            <addSign
              ref="addSign"
              :task="task"
              :taskId="taskInfo.taskId"
              @success="modalFormSuccess"
              @closeLoading="closeLoading" />
          </div>

        </div>
        <!-- <addSignNodeModal
          ref="addSignNodeModal"
          :task="task"
          :taskId="taskInfo.taskId"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          v-if="alias == 'addSignNode'"
        /> -->

        <!-- 新增退回节点按钮弹窗 -->
        <div
          v-if="alias == 'fallBack'"
          style="display: flex;">
          <div style="width: 46%;">
            <fallBack
              ref="fallBack"
              :task="task"
              :taskInfo="taskInfo"
              @changeFlowImage="handleChangeFlowImage"
              @success="modalFormSuccess"
              @closeLoading="closeLoading" />
          </div>

          <div
            style="flex:1;margin: -24px 0 0 0;"
            ref="flowImageModal">
            <iframe
              scrolling="no"
              id="flowImageframe"
              width="100%"
              style="min-height: 400px"
              height="99.9%"
              frameborder="0"
              align="center"
              allowfullscreen="true"
              allow="autoplay"
              :src="flowImage.src" />
          </div>

        </div>

        <transactionUrgeModal
          ref="transactionUrgeModal"
          :task="task"
          :taskId="taskInfo.taskId"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          v-if="alias == 'transactionUrge'" />
        <div
          ref="flowImageModal"
          v-if="['headmap', 'flowImage'].includes(alias)">
          <iframe
            scrolling="no"
            id="flowImageframe"
            width="100%"
            style="min-height: 400px"
            height="99.9%"
            frameborder="0"
            align="center"
            allowfullscreen="true"
            allow="autoplay"
            :src="flowImage.src" />
        </div>
        <urgAcionModal
          ref="urgAcionModal"
          :task="task"
          :taskId="taskInfo.taskId"
          @success="modalFormSuccess"
          @closeLoading="closeLoading"
          v-if="alias == 'urg'" />
      </div>
    </a-modal>
  </div>
</template>
<script lang="jsx">
// import { mapGetters } from 'vuex'
import agreeModal from '../modal/agreeModal.vue'
import unPassModal from '../modal/unPassModal.vue'
import informedModal from '../modal/informedModal.vue'
import transferTaskModal from '../modal/transferTaskModal.vue'
import addNodeModal from '../modal/addNodeModal.vue'
import addSign from '../modal/addSign.vue'
import backToNodeModal from '../modal/backToNodeModal.vue'
import backAddPreNodeModal from '../modal/backAddPreNodeModal.vue'
import fallBack from '../modal/fallBack.vue'
import approvalHistoryModal from '../modal/approvalHistoryModal'
import addSignNodeModal from '../modal/addSignNodeModal'
import transactionUrgeModal from '../modal/transactionUrgeModal'
import urgAcionModal from '../modal/urgAcionModal'
import { getTaskBtn } from '../../api/analy'
import actionMixins from './actionMixins.js'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { mapGetters } from 'vuex'
import layIM from '@/utils/im/layIM.js'
import { cloneDeep } from 'lodash'
export default {
    props: {
        // taskId: {
        //     type: [String, Number],
        //     default: 'ElsAuditList'
        // },
        pageHeaderButtons: {
            type: Array,
            default: () => {
                return []
            }
        },
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        },
        resultData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    provide () {
        return {
            currentEditRow: this.currentEditRow,
            resultData: this.resultData
        }
    },
    mixins: [actionMixins],
    components: {
        agreeModal,
        unPassModal,
        informedModal,
        transferTaskModal,
        backToNodeModal,
        backAddPreNodeModal,
        fallBack,
        approvalHistoryModal,
        addNodeModal,
        addSign,
        addSignNodeModal,
        transactionUrgeModal,
        urgAcionModal
    },
    watch: {

        changeFlowId: {
            handler (val) {
                if (val) {
                    this.flowImageAction()
                }
            }
        }
    },
    computed: {
        ...mapGetters([
            'taskBtn',
            'taskInfo'
        ]),
        taskBtnsDemo () {
            let btnList = []
            if (this.taskBtn.length > 0) {
                btnList = this.taskBtn
            } else {
                btnList = [
                    { alias: 'chat', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通') },
                    { alias: 'agree', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过') },
                    // { alias: 'unPass', btnName: '拒绝' },
                    { alias: 'informed', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VZ_a2ae5`, '传阅') },
                    { alias: 'transferTask', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办') },
                    { alias: 'backToNode', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_drawBack`, '退回') },
                    { alias: 'invalid', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_ku_9fac3`, '作废') },
                    { alias: 'flowImage', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLP_1a93f54`, '流程图') },
                    { alias: 'approvalHistory', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvalComments`, '审批意见') },
                    // { alias: 'headmap', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xvP_1b24170`, '热力图') },
                    { alias: 'addSignNode', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JKMP_25156566`, '临时会签') },
                    { alias: 'addSign', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PuP_13f6feb`, '前加签') },
                    { alias: 'addNode', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SuP_146056c`, '后加签') },
                    { alias: 'transactionUrge', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zKzr_353e55f7`, '特事特办') },
                    { alias: 'print', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印') },
                    { alias: 'fallBack', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JKyCSuvK_7b93f7aa`, '临时节点添加历史') }
                    // 审批通过后 如果下个节点没有发布可以撤销审批
                    // { alias: 'withdrawalTask', btnName: this.$srmI18n(`${this.$getLangAccount()}#`, '撤回')}
                ]
            }
            // 自定义按钮必须key为alias才当做新按钮
            let aliasBtnList = []
            // handleBeforeClick绑定
            let buttons = cloneDeep(this.pageHeaderButtons)
            buttons.map(item => {
                for (let btn of btnList) {
                    if (item?.alias == btn.alias) {
                        btn['handleBeforeClick'] = item?.handleBeforeClick || null
                        continue
                    }
                }
                if (item.key == 'alias') aliasBtnList.push(item)
            })
            btnList.push(...aliasBtnList)
            return btnList
        },
        btnBack () {
            let btnBack = {}
            let buttons = cloneDeep(this.pageHeaderButtons)
            buttons.map(item => {
                if (['goBack', 'back', 'rollBack'].includes(item.type) || ['goBack', 'back'].includes(item.key)) {
                    btnBack = item
                }
            })
            if (!btnBack.click) {
                btnBack.click = () => {
                    console.error('请在详情配置返回按钮type:back')
                    this.$message.warning('请在详情配置返回按钮type:back')
                }
                if (btnBack.key == 'goBack') {
                    btnBack.click = () => {
                        this.$emit('handleGoBack')
                    }
                }
            }
            return btnBack
        },
        normalBtn () {
            if (this.taskBtnList.length > this.normalBtnMaxLength) {
                return this.taskBtnList.slice(0, this.normalBtnMaxLength)
            }
            return this.taskBtnList
        },
        dropdownBtn () {
            if (this.taskBtnList.length > this.normalBtnMaxLength) {
                return this.taskBtnList.slice(this.normalBtnMaxLength)
            }
            return []
        }
    },
    data () {
        return {
            diaLogLeftLocation: null,
            diaLogWidth: 600,
            that: this,
            changeFlowId: '',
            task: {
                action: ''
            },
            confirmLoading: false,
            multiInstanceTaskBtns: [],
            taskBtnList: [],
            taskSequence: [],
            taskData: {},
            handlerModel: false,
            normalBtnMaxLength: 4,
            alias: '',
            processDefinitionId: '',
            tokenHeader: this.$ls.get(ACCESS_TOKEN),
            flowImage: {
                show: false,
                src: ''
            },
            agreeAdHocSubProcess: {},
            viewImageModelerTmpSrc: null,
            modalFormDemo: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                confirmLoading: false,
                visible: false,
                handleOk: () => {
                    this.confirmLoading = true
                    this.$refs[this.moadlName].handleConfirm()
                    this.changeFlowId = ''
                },
                handleCancel: () => {
                    this.modalForm.visible = false
                    this.task.action = ''
                    this.changeFlowId = ''
                }
            },
            moadlName: '',
            modalForm: {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                confirmLoading: false,
                visible: false,
                handleOk: () => {
                    this.confirmLoading = true
                    this.$refs[this.moadlName].handleConfirm()
                },
                handleCancel: () => {
                    this.modalForm.visible = false
                    this.task.action = ''
                }
            },
            printObj: {
                id: 'auditDetail',
                popTitle: ''
            }
        }
    },
    methods: {
        resultPrintBtn (){
            if(this.taskInfo.businessType == 'resultEnquiry'){
                let url = this.$router.resolve({
                    path: '/print/enquiryPrint',
                    query: { id: this.taskInfo.id }
                })
                window.open(url.href, '_blank')
            }else{
                this.$refs['printBtn'].$el.click()
            }
        },
        handleChangeFlowImage (changeFlowId) {
            this.changeFlowId = changeFlowId
        },
        back () {
            // this.$emit('back')
            this.btnBack.click()
        },
        handleClick (item, type) {
            if (item.handleBeforeClick) {
                if (typeof action === 'object') {
                    this.alias = item.alias
                } else {
                    this.alias = item
                }
                item.handleBeforeClick(this)
            } else {
                this.action(item, type)
            }
        },
        handleAfterClick () {
            this.action(this.alias)
        },
        action (action, type) {
            if (typeof action === 'object') {
                this.alias = action.alias
            } else {
                this.alias = action
            }
            if (type) {
                this.alias = 'sequence'
                const obj = JSON.parse(JSON.stringify(this.nextNode))
                this.taskSequenceForm.taskTitle = this.task.taskTitle

                var sequenceNode = []
                obj.forEach((item, index, array) => {
                    //执行代码
                    if (item.taskId == action.targetRef) {
                        sequenceNode.push(item)
                    }
                })
                this.sequenceNode = sequenceNode

                this.handlerSequenceModel = true
            } else {
                console.log(this.alias)
                switch (this.alias) {
                case 'agree': // 普通任务正常办理
                    this.agreeAction()
                    break
                case 'pass': // 多实例通过
                    this.passAction()
                    break
                case 'unPass': // 多实例不通过
                    this.unPassAction()
                    break
                case 'informed':
                    this.informedAction()
                    break
                case 'agreeAdHocSubProcess':
                    this.agreeAdHocSubProcessAction()
                    break
                case 'transferTask':
                    this.transferTaskAction()
                    break
                case 'invalid':
                    this.invalidTaskAction()
                    break
                case 'headmap':
                    this.headMapAction()
                    break
                case 'flowImage':
                    this.flowImageAction()
                    break
                case 'approvalHistory':
                    this.approvalHistoryAciton()
                    break
                case 'transactionUrge':
                    this.transactionUrgeAction()
                    break
                case 'addNode':
                    this.flowImageAction()
                    this.addNodeAcion()
                    break
                case 'addSignNode':
                    this.flowImageAction()
                    this.addSignNodeAcion()
                    break
                case 'saveDraft':
                    // this.saveAction()
                    break
                case 'backToNode':
                    this.backToNodeAction()
                    break
                case 'backAddPre': //审批后撤回
                    this.withdrawalAddPreNodeAction()
                    break
                case 'print':
                    // window.print()
                    //this.$refs['printBtn'].$el.click()
                    this.resultPrintBtn()
                    break
                case 'addUser':
                    this.addUserTask.model = true
                    break
                case 'revisit':
                    var url =
                            process.env.VUE_APP_BASE_BPM_API +
                            'revisit/viewer.html' +
                            '?t=' +
                            new Date().getTime() +
                            '&taskId=' +
                            this.taskInfo.taskId
                    this.revisit.modelerSrc = url
                    this.revisit.model = true
                    break
                case 'flw_fawen_add': //发文下发任务
                    this.flw_fawen_addAction()
                    break
                case 'flw_fawen_agree': //发文下办理
                    this.completeActivityAction()
                    break
                case 'flw_fawen_target_only': //发文下办理
                    this.flw_fawen_target_onlyAction()
                    break
                case 'flw_fawen_target_sign_only': //发文下办理
                    this.flw_fawen_target_sign_onlyAction()
                    break
                case 'revoke': //撤回
                    this.revokeAcion()
                    break
                case 'fallBack': //回退
                    this.flowImageAction()
                    this.fallBackAcion()
                    break
                case 'addSign':
                    this.flowImageAction()
                    this.addSignAction()
                    break
                case 'urg': //催办
                    this.urgAcion()
                    break
                case 'withdrawalTask': //审批后撤回
                    this.withdrawalTaskAcion()
                    break
                case 'chat': //沟通
                    layIM.creatGruopChat({ id: this.currentEditRow.taskId, type: 'auditProcessAllAuditUserApply', url: this.url || '', recordNumber: this.currentEditRow.taskId })
                    break
                default:
                    this.agreeAction()
                }
            }
        },
        renderBpmTaskAttachment (processInstanceId) {
            var url = this.flowImage.base + processInstanceId + '&t=' + new Date().getTime()
            this.viewImageModelerTmpSrc = url
        },
        getTaskBtn (id) {
            if (this.taskBtn.length > 0) {
                // this.processInstanceId = this.task.processInstanceId
                this.taskBtnList = this.taskBtnsDemo
                return false
            }
            try {
                getTaskBtn(id).then(response => {
                    let {
                        data,
                        processDefinitionId,
                        multiInstance,
                        sequence,
                        processInstanceId,
                        code,
                        taskTitle
                    } = response
                    if (code == 0) {
                        this.taskBtnList = data.length > 0 ? data : this.taskBtnsDemo
                        if (this.taskBtnList.length != this.taskBtnsDemo.length) {
                            this.taskBtnList.push(
                                { alias: 'print', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印') },
                                { alias: 'fallBack', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JKyCSuvK_7b93f7aa`, '临时节点添加历史') }
                            )
                        }
                        this.multiInstanceTaskBtns = multiInstance
                        this.taskSequence = sequence
                        // 热力图ID
                        if (processInstanceId) {
                            this.renderBpmTaskAttachment(processInstanceId)
                            this.processDefinitionId = processDefinitionId
                        }
                        this.processInstanceId = processInstanceId
                        this.task.taskTitle = taskTitle || ''
                    } else if (code == 500) {
                        // 任务不存在就只显示流程图和审批意见,后台大佬的要求
                        this.taskBtnList = [
                            { alias: 'flowImage', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLP_1a93f54`, '流程图') },
                            { alias: 'approvalHistory', btnName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvalComments`, '审批意见') }
                        ]
                    } else {
                        this.taskBtnList = this.taskBtnsDemo
                    }
                })
            } catch (e) {
                this.back()
            }
        },
        modalFormSuccess (isback) {
            this.modalForm.visible = false
            this.confirmLoading = false
            this.task.action = ''
            if (isback != 'back') {
                this.back()
            }
        },
        closeLoading () {
            this.confirmLoading = false
        },
        handleMenuClick (e, item) {
            if (item.click) {
                item.click(this)
            } else {
                this.handleClick(e.key)
            }
        }
    },
    created () {
        console.log(this.taskInfo,'当前任务task信息')
        this.diaLogWidth = document.documentElement.clientWidth / 3 * 2
        if(this.diaLogWidth <= 630) {
          this.diaLogWidth = document.documentElement.clientWidth
        }
        this.diaLogLeftLocation = (document.documentElement.clientWidth - this.diaLogWidth) /2 + 'px'

        // this.taskId = this.taskInfo.taskId || ''
        this.processInstanceId = this.taskInfo.processInstanceId || ''
        this.getTaskBtn(this.taskInfo.taskId)
    },
    destroyed () {
        // 重置任务按钮
        let { taskId } = this.$route.query
        if (!taskId) {
            this.$store.commit('SET_TASKBTN', [])
            this.$store.commit('SET_TASK', {})
        }
    }
}
</script>
<style lang="less">
.taskBtn {
    display: inline-block;

    .ant-btn {
        margin-left: 8px;

        &:first-child {
            margin-left: 0;
        }
    }

    .actionModal {
        .modalCtn {
            overflow: auto;
        }
    }
}

@media print {
    .noprint {
        display: none
    }

    .page-container .detail-page #page-content.page-content {
        height: 100% !important;
    }
}
</style>
