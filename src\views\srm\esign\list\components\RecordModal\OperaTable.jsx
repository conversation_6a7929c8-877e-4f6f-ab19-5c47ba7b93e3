import { cloneDeep } from 'lodash'
import { ListConfig } from '@/plugins/table/gridConfig'
let cloneListConfig = cloneDeep(ListConfig)
if (cloneListConfig.toolbarConfig) {
    delete cloneListConfig.toolbarConfig
}

export default {
    name: 'OperaTable',
    functional: true,
    render (h, context) {
        const { tableData = [], columns = [], pagerConfig = {}, minHeight = 300 } = context.props
        let props = {
            ...cloneListConfig,
            columns: columns,
            pagerConfig: pagerConfig,
            data: tableData
        }
        

        const scopedSlots = {
            empty: () => (<a-empty />)
        }
        
        return (
            <div class="OperaTable" style={ { minHeight: `${minHeight}px`, maxHeight: `${minHeight}px` } }>
                <vxe-grid { ...{ props, on: context.listeners } } scopedSlots={ scopedSlots }  />
            </div>
        )
    }
}