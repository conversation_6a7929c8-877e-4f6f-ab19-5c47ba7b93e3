<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="show"
        :subpackageTitle="subpackageTitle"
        :ref="businessRefName"
        :remoteJsFilePath="remoteJsFilePath"
        :currentEditRow="currentEditRow"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        :fromSourceData="fromSourceData"
        :handleAfterDealSource="handleAfterDealSource"
        pageStatus="detail"
        v-on="businessHandler"
      >
        <template v-slot:noticeInfo="{ slotProps }">
          <j-editor
            ref="ueditor"
            v-model="noticeContent" />
        </template>
      </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit />
    </a-spin>
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
    v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import JEditor from '@/components/els/JEditor'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { templatePublic } from '../../plugins/templatePublic.js'
import { postAction, getAction, httpAction } from '@/api/manage'
import { BUTTON_PUBLISH } from '@/utils/constant.js'
import { merge } from 'lodash'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'InvitaionBid',
    components: {
        BusinessLayout,
        JEditor,
        fieldSelectModal,
        flowViewModal
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    mixins: [businessUtilMixin, templatePublic],
    data () {
        return {
            subpackageTitle: '',
            flowId: 0,
            auditVisible: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            opinion: '',
            flowView: false,
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            show: false,
            externalToolBar: {
                
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.showFlowConditionBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditPass,
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditReject,
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack',
                    click: this.goBackAudit
                }
            ],
            currentGroupCode: {},
            noticeStatus: '',
            fromSourceData: {},
            noticeContent: '',
            url: {
                detail: '/tender/purchaseTenderNoticeHead/queryById',
                add: '/tender/purchaseTenderNoticeHead/add',
                edit: '/tender/purchaseTenderNoticeHead/edit',
                publish: '/tender/purchaseTenderNoticeHead/publish'
            },
            remoteJsFilePath: ''
        }
    },
    computed: {
        // remoteJsFilePath () {
        //     // let templateNumber = this.currentEditRow.templateNumber
        //     // let templateVersion = this.currentEditRow.templateVersion
        //     // let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
        //     let templateNumber = 'TC2022041301'
        //     let templateVersion = 1
        //     let account = 100000
        //     return `${account}/purchase_purchaseTenderNotice_${templateNumber}_${templateVersion}`
        // }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            var that = this
            // 如果分包状态是预审环节需要隐藏一些字段
            const checkType = that.checkType
            if (checkType == '0') {
                let formFields = [],
                    arr = ['signUpType', 'signUpBeginTime', 'signUpEndTime']
                let formFieldsOld = []
                pageConfig.groups.forEach((group) => {
                    if (group.groupCode == 'getFile') {
                        formFieldsOld = group.formFields
                    }
                })
                formFieldsOld.forEach((form) => {
                    if (arr.indexOf(form.fieldName) == -1) {
                        formFields.push(form)
                    }
                })
                pageConfig.groups.forEach((group) => {
                    if (group.groupCode == 'getFile') {
                        group.formFields = formFields
                    }
                    if (group.groupCode == 'noticeInfo') {
                        // 投标邀请公告-范围给死
                        group.formModel.noticeScope = '4'
                    }
                })
            }

            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }

            let signUpFlag = this.signUp !== '1'
            let biddingFlag = this.bidding !== '1'
            // let none = (signUpFlag && biddingFlag)
            // 隐藏数组内的字段
            let arr = []
            // 非报名情况
            if (signUpFlag) {
                arr = arr.concat('signUpBeginTime', 'signUpEndTime', 'signUpType')
            }
            //非购标情况
            if (biddingFlag) {
                arr = arr.concat('biddingBeginTime', 'biddingEndTime', 'biddingType', 'offlineSaleAccount')
            }
            // 不报名不购标情况
            // if(none){
            //     arr=arr.concat('fileSubmitEndTime')
            // }
            let formFields = []
            let formFieldsOld = []
            pageConfig.groups.forEach((group) => {
                if (group.groupCode == 'getFile') {
                    formFieldsOld = group.formFields
                }
            })
            formFieldsOld.forEach((form) => {
                if (arr.indexOf(form.fieldName) == -1) {
                    formFields.push(form)
                }
            })
            pageConfig.groups.forEach((group) => {
                if (group.groupCode == 'getFile') {
                    group.formFields = formFields
                }
                if (group.groupCode == 'noticeInfo') {
                    // 投标邀请公告-范围给死
                    group.formModel.noticeScope = '4'
                }
            })
            setDisabledByProp('signUpBeginTime', signUpFlag)
            setDisabledByProp('signUpEndTime', signUpFlag)
            setDisabledByProp('signUpType', signUpFlag)
            setDisabledByProp('biddingBeginTime', biddingFlag)
            setDisabledByProp('biddingEndTime', biddingFlag)
            // setDisabledByProp('fileSubmitEndTime', none)

            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }

                rule[prop] = [
                    {
                        required: flag,
                        message: `${fieldLabel}必填`
                    }
                ]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            //此处写根据分包信息作为判断展示位
            let signUpValidateFlag = this.signUp == '1'
            let biddingValidateFlag = this.bidding == '1'
            // let Or = (signUpValidateFlag || biddingValidateFlag)
            setValidateRuleByProp('signUpBeginTime', signUpValidateFlag)
            setValidateRuleByProp('signUpEndTime', signUpValidateFlag)
            setValidateRuleByProp('signUpType', signUpValidateFlag)

            setValidateRuleByProp('biddingBeginTime', biddingValidateFlag)
            setValidateRuleByProp('biddingEndTime', biddingValidateFlag)
        },
        async init () {
            this.confirmLoading = true
            this.show = false
            await getAction(this.url.detail+'?id='+this.currentEditRow.id)
                .then(res => {
                    if (res.success) {
                        if (res.result) {
                            this.fromSourceData = res.result || {}
                            // this.noticeStatus = res.result.noticeStatus
                            this.noticeContent = res.result.noticeContent
                            this.noticeType = res.result.noticeType
                            // if (this.noticeStatus != '0') this.externalToolBar = {}
                        } else {
                            // this.noticeStatus = '0'
                        }
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.show = true
                })

            let url = '/tender/purchaseTenderProjectHead/querySubpackageInfoBySubpackageId'
            await getAction(url, { subpackageId: this.fromSourceData.subpackageId }).then((res) => {
                if (res.success) {
                    console.log('res.result', res.result)
                    this.signUp=res.result.signUp
                    this.bidding=res.result.bidding
                    this.checkType = res.result.checkType
                }
            })
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBackAudit()

                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        showFlowConditionBtn ({pageData}) {
            console.log('pageData', pageData)
            if (pageData.auditStatus == '1') {
                return true
            } else {
                return false
            }
        },
        showFlow (){
            let params = this.paramIntegrate()
            this.flowId = params.flowId
            this.flowView = true
            console.log('this.flowView', this.flowView)

        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        syncShow ({ pageData }) {
            console.log('pageData', pageData)
            return (pageData.auditStatus === '1')
        },
        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        goBack () {
            this.$emit('hide')
        },
        goBackAudit () {
            this.$parent.hideController()
        }
    },
    async created () {
        this.subpackageTitle = this.currentEditRow.subject||''
        // this.noticeStatus = '0'
        await this.init()
        console.log(this.noticeType)
        console.log(this.fromSourceData)
        const currentEditRow = this.fromSourceData.templateNumber && this.fromSourceData.templateAccount ? {
            templateNumber: this.fromSourceData.templateNumber,
            templateName: this.fromSourceData.templateName,
            templateVersion: this.fromSourceData.templateVersion,
            templateAccount: this.fromSourceData.templateAccount
        } : this.getBusinessTemplate('purchaseTenderNotice', this.noticeType)
        console.log(currentEditRow)
        if (!currentEditRow) {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
            return
        }
        this.currentEditRow = Object.assign(this.currentEditRow, currentEditRow)
        this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/purchase_purchaseTenderNotice_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
    
    }
}
</script>
