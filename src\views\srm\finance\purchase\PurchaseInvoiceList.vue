<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showDetailPage && !showEditPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"/>
    <ViewInvoiceDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction, httpAction, postAction} from '@/api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import ViewInvoiceDetail from './modules/ViewInvoiceDetail'

export default {
    mixins: [ListMixin],
    components: {
        ViewInvoiceDetail
    },
    data () {
        let batchDownloadBtn = new BatchDownloadBtn({pageCode: 'paymentApply'})
        batchDownloadBtn.baseConfig.url = '/attachment/saleAttachment/downloadZip'
        return {
            showEditPage: false,
            pageData: {
                businessType: 'paymentApply',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNhPty_af13bb22`, '请输入发票单号')
                    }
                ],
                form: {
                    keyWord: '',
                    name: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_YduWWW_211799ec`, '推送到ERP'),
                        icon: 'arrow-up',
                        clickFn: this.pushDataToERP,
                        authorityCode: 'finance#purchaseInvoice:pushInvoiceData'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    },
                    {...batchDownloadBtn.btnConfig}
                ],
                showOptColumn: true,
                optColumnList: [
                    //{type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    {
                        type: 'preView',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        clickFn: this.preViewEvent,
                        authorityCode: 'finance#purchaseInvoice:preview'
                    },
                    {
                        type: 'preView',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_view`, '查看'),
                        clickFn: this.invoiceHandleView,
                        authorityCode: 'finance#purchaseInvoiceOCRData:view'
                    },
                    {
                        type: 'download',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                        clickFn: this.downloadFile,
                        authorityCode: 'finance#purchaseInvoice:down'
                    },
                    {
                        type: 'record',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
                        clickFn: this.handleRecord
                    }
                ],
                optColumnWidth: 80
            },
            url: {
                list: '/reconciliation/purchaseInvoice/list',
                pushDataToERPUrl: '/reconciliation/purchaseInvoice/pushInvoiceData',
                columns: 'PurchaseInvoiceHead',
                download: '/attachment/saleAttachment/download'
            },
            tabsList: []
        }
    },
    mounted () {
        // this.serachTabs('srmAddCostConfirmStatus', 'confirmStatus')
        this.serachCountTabs('/reconciliation/purchaseInvoice/counts')
    },
    methods: {
        invoiceHandleView (row) {
            let preViewFile = {}
            if (row.invoiceImg != '' && row.invoiceImg != null) {
                const id = row.invoiceImg.split('-')[0]
                const fileName = row.invoiceImg.split('-')[1]
                preViewFile = {id: id, fileName: fileName}
            }
            localStorage.setItem('preViewFile', JSON.stringify(preViewFile))
            //打开详情页
            this.showDetailPage = false
            this.$nextTick(() => {
                this.currentEditRow = row
                this.showDetailPage = true
            })
        },
        hideDetailPage () {
            this.showDetailPage = false
        },
        preViewEvent (row) {
            if (row.invoiceImg != '' && row.invoiceImg != null) {
                const id = row.invoiceImg.split('-')[0]
                const fileName = row.invoiceImg.split('-')[1]
                let preViewFile = {id: id, fileName: fileName}
                this.$previewFile.open({params: preViewFile})
            } else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_hPBjPO_e8a4f3c8`, '发票没有图片'))
            }
        },
        async downloadFile (row) {
            if (!row.invoiceImg) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noAttachmentsDownloadLine`, '该行没有可下载的附件'))
                return
            }
            const id = row.invoiceImg.split('-')[0]
            const str = row.invoiceImg.split('-', -1)
            let fileNames = str[1]
            for (let i = 1; i < str.length; i++) {
                if (i + 1 < str.length) {
                    fileNames = fileNames + '-' + str[i + 1]
                } else {
                    break
                }
            }
            // 没有格式，通过预览获取格式
            if (fileNames.split('.').length == 1){
                const getFullfilename = (url) => {
                    const parsedUrl = new URL(url)
                    const params = new URLSearchParams(parsedUrl.search)
                    return params.get('fullfilename')
                }
                await postAction('/attachment/purchaseAttachment/getSignature', {id}).then((res) => {
                    console.log('res', res)
                    fileNames = getFullfilename(res.message)
                })
            }
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileNames)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        showEditCondition (row) {
            if ((row.auditStatus == '0')) {
                return false
            } else {
                return true
            }
        },
        showCancelCondition (row) {
            if (row.auditStatus == '2') {
                return false
            } else {
                return true
            }
        },
        showSynchrCondition (row) {
            if (row.sendStatus == '1') {
                return true
            } else {
                return false
            }
        },
        showDeleteCondition (row) {
            if ((row.auditStatus == '0')) {
                return false
            } else {
                return true
            }
        },
        handleCancel (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voidSelectedData`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        handleSynchr (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_synchronization`, '同步'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherSynchronize`, '确认是否将单据同步至供应商?'),
                onOk: function () {
                    that.postUpdateData(that.url.synchr, row)
                }
            })
        },
        postUpdateData (url, row) {
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        }
    }
}
</script>