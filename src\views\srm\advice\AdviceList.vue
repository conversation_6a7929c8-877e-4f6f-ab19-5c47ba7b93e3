<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAbnormalPage"
      ref="listPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url" 
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 表单区域 -->
    <EditAdviceModal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="editPage"
      @hide="hideEditPage"
    />
    <!-- 查看 -->
    <ViewAdviceModal
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @handleUpdate="handleOrderUpdate"
      @hide="hideEditPage" />
    <a-modal
    v-drag    
      :width="660"
      v-model="confirmVisible"
      :title="confirmTitle"
      :okText="$srmI18n(`${$getLangAccount()}#`, '确认')"
      @ok="confirmHandleOk">
      <a-form-model
        :model="confirmForm"
        :layout="formLayout"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(``, '备注')"
          prop="content">
          <a-textarea
            :auto-size="{ minRows: 6, maxRows: 10 }"
            v-model="confirmForm.content" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    <a-modal
    v-drag    
      :width="960"
      v-model="orderRecordVisible"
      :title="orderRecordTitle"
      @ok="orderRecordVisible= false">
      <vxe-grid v-bind="orderRecordGridOptions">
        <template #pager>
          <vxe-pager
            :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
            :current-page.sync="orderRecordTablePage.currentPage"
            :page-size.sync="orderRecordTablePage.pageSize"
            :total="orderRecordTablePage.total"
            @page-change="handleOrderRecordTablePageChange">
          </vxe-pager>
        </template>
      </vxe-grid>
    </a-modal>
    <a-modal
    v-drag    
      :width="800"
      v-model="recordVisible"
      title="记录"
      @ok="recordVisible= false">
      <vxe-grid v-bind="recordGridOptions">
        <template #pager>
          <vxe-pager
            :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
            :current-page.sync="recordTablePage.currentPage"
            :page-size.sync="recordTablePage.pageSize"
            :total="recordTablePage.total"
            @page-change="handleRecordTablePageChange">
          </vxe-pager>
        </template>
      </vxe-grid>
    </a-modal>
  </div>
</template>
<script>
import EditAdviceModal from './modules/EditAdviceModal'
import ViewAdviceModal from './modules/ViewAdviceModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import { getAction, postAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EditAdviceModal,
        ViewAdviceModal
    },
    data () {
        return {
            showEditPage: false,
            deliverVisible: false,
            confirmVisible: false,
            confirmTitle: '',
            optType: '',
            subAccountList: [],
            deliverForm: { content: '', recipient: '' },
            confirmForm: { content: ''},
            verificationForm: { checkResult: '1', content: ''},
            verificationformRules: {
                checkResult: [ { required: true, message: '请选择', trigger: 'blur' }],
                content: [ { required: true, message: '请输入', trigger: 'blur' }]
            },
            formLayout: 'vertical',
            formRules: {
                recipient: [ { required: true, message: '请选择接收人', trigger: 'blur' }]
            },
            labelCol: {},
            wrapperCol: {},
            recordVisible: false,
            orderRecordTitle: '工单记录',
            recordGridOptions: {
                size: 'small',
                border: true,
                showHeaderOverflow: true,
                showOverflow: true,
                columns: [
                    {
                        type: 'seq',
                        width: 50,
                        title: '序号'
                    },
                    {
                        title: '操作人',
                        field: 'username',
                        width: 200
                    },
                    {
                        title: '操作时间',
                        field: 'createTime',
                        width: 150
                    },
                    {
                        title: '动作',
                        field: 'operateName',
                        width: 200
                    },
                    {
                        title: '耗时（毫秒）',
                        field: 'costTime'
                    }
                ],
                data: []
            },
            recordTablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
            orderRecordVisible: false,
            orderRecordGridOptions: {
                size: 'small',
                border: true,
                showHeaderOverflow: true,
                showOverflow: true,
                columns: [
                    {
                        type: 'seq',
                        width: 50,
                        title: '序号'
                    },
                    {
                        title: '操作类型',
                        field: 'operationType_dictText',
                        width: 100
                    },
                    {
                        title: '操作人',
                        field: 'operationPerson',
                        width: 120
                    },
                    {
                        title: '操作时间',
                        field: 'createTime',
                        width: 140
                    },
                    {
                        title: '接收人',
                        field: 'recipient',
                        width: 120
                    },
                    {
                        title: '备注',
                        field: 'remark'
                    }
                ],
                data: []
            },
            orderRecordTablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: '公司名称',
                        fieldName: 'companyName',
                        placeholder: '请输入公司名称'
                    },
                    {
                        type: 'input', 
                        label: '关键字',
                        fieldName: 'keyWord', 
                        placeholder: '请输入单号/标题'
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.add, type: 'primary',authorityCode: 'other#complaintAdviceHead:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, allow: this.allowShow,authorityCode: 'other#complaintAdviceHead:query'},
                    //{ type: 'edit', title: '指派', clickFn: this.deliverTo, allow: this.allowOpt},
                    { type: 'edit', title: '关闭', clickFn: this.close, allow: this.allowCloseOpt,authorityCode: 'other#complaintAdviceHead:close'},
                    //{ type: 'record', title: '操作记录', clickFn: this.orderRecord },
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit,authorityCode: 'other#complaintAdviceHead:add'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDel,authorityCode: 'other#complaintAdviceHead:delete'}
                    //{type: 'record', title: '记录', clickFn: this.hisRecord }
                ],
                form: {
                    businessType: '',
                    operateType: ''
                }
            },
            showAbnormalPage: false,
            url: {
                list: '/other/complaintAdviceHead/list',
                delete: '/other/complaintAdviceHead/delete',
                columns: 'complaint_advice_head'
            },
            countTabsUrl: '/other/complaintAdviceHead/count'
        }
    },
    mounted () {
        this.serachCountTabs(this.countTabsUrl)
    },
    methods: {
        // 更新详情单据状态
        handleOrderUpdate (data) {
            this.currentEditRow.adviceStatus = data.adviceStatus
            this.currentEditRow.score = data.score
        },
        
        // 接收人过滤
        filterOption (input, option) {
            return (
                option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
            )
        },

        allowShow (row){
            if(row.adviceStatus ==='0'){
                return true
            }else{
                return false    
            }
        },
        allowEdit (row){
            if(row.adviceStatus ==='0'){
                return false
            }else{
                return true    
            }
        },
        allowDel (row){
            if (row.adviceStatus === '0'){
                return false
            }else{
                return true
            }
        },
        allowOpt (row){
            if(row.adviceStatus ==='1'){
                return false
            }
            return true
        },
        allowCloseOpt (row){
            if(row.adviceStatus ==='1'){
                return false
            }
            return true
        },
        //关闭工单
        close (row){
            this.confirmTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RtRl_2bf1e0ea`, '工单关闭')
            this.confirmForm = { content: '' }
            this.currentEditRow = row
            this.confirmVisible = true
            this.optType = 'close'
        },
        confirmHandleOk (){
            const that = this
            let param = Object.assign({}, this.currentEditRow)
            param['content']=this.confirmForm.content 
               
            let url = ''
            if(this.optType === 'close'){
                url = '/other/complaintAdviceHead/close'
            }
            postAction(url, param).then((res)=>{
                if(res && res.success){
                    that.$message.success(res.message)
                    that.confirmVisible = false
                    that.searchEvent()
                } else{
                    that.$message.warning(res.message)
                }
            })          
        },
        // 工单记录
        orderRecord (row){
            this.currentEditRow = row
            this.orderRecordTitle = '单据操作记录  '+row.adviceNumber+'  '+row.title
            this.getOrderRecordTablePageData()
            this.orderRecordVisible = true
        },
        // 记录
        hisRecord (row) {
            this.currentEditRow = row
            this.getRecordTablePageData()
            this.recordVisible = true
            
        },
        getRecordTablePageData () {
            const { pageSize = 10, currentPage = 1 } = this.recordTablePage || {}
            const params = {
                pageSize,
                pageNo: currentPage,
                businessId: this.currentEditRow.id || ''
            }
            getAction('/log/queryOptHisList', params).then((res) => {
                if (res && res.success) {
                    this.recordGridOptions.data = res.result.records || []
                    this.recordTablePage.total = res.result.total
                }                
            })

        },
        handleRecordTablePageChange ({ currentPage, pageSize }) {
            this.recordTablePage.currentPage = currentPage
            this.recordTablePage.pageSize = pageSize
            this.getRecordTablePageData()
        },
        getOrderRecordTablePageData () {
            const { pageSize = 10, currentPage = 1 } = this.orderRecordTablePage || {}
            const params = {
                pageSize,
                pageNo: currentPage,
                adviceId: this.currentEditRow.id || '',
                column: 'id',
                order: 'desc'
            }
            getAction('/advice/complaintAdviceOperateRecord/list', params).then((res) => {
                if (res && res.success) {
                    this.orderRecordGridOptions.data = res.result.records || []
                    this.orderRecordTablePage.total = res.result.total
                }                
            })

        },
        handleOrderRecordTablePageChange ({ currentPage, pageSize }) {
            this.orderRecordTablePage.currentPage = currentPage
            this.orderRecordTablePage.pageSize = pageSize
            this.getOrderRecordTablePageData()
        },
        add (){
            this.currentEditRow = {}
            this.showEditPage = true
            this.showDetailPage = false
        },
        hidePage () {
            this.showAbnormalPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
        },
        handleThrowAdvice (row) {
            this.currentEditRow = row
            this.showAbnormalPage = true
        }
    }
}
</script>