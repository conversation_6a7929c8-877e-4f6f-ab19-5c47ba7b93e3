<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      refresh
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'PurchaseClarificationInfoAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        const _self = this
        return {
            selectType: 'clarification',
            pageData: {
                form: {
                    clarificationNumber: null,
                    businessType: null,
                    businessNumber: null,
                    businessName: null,
                    businessId: null,
                    matterTitel: null,
                    clarificationMatter: null,
                    sendStatus: null,
                    remark: null
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_basicData`, '基础信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_clarificationNo`, '澄清编号'),
                                    fieldName: 'clarificationNumber',
                                    disabled: true,
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_clarificationNo`, '澄清编号')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'businessType',
                                    dictCode: 'srmClarificationBusinessType',
                                    disabled: false,
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    required: '1',
                                    bindFunction: function (parentRef, pageData, groupData, realValue){     
                                        _self.updateBusinessNumberExtendModel(realValue)
                                    }
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderNo`, '业务单号'),
                                    fieldName: 'businessNumber',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderNo`, '业务单号'),
                                    required: '1',
                                    extend: {
                                        beforeCheckedCallBack: function (Vue, pageData, groupData, form) {
                                            return new Promise((resolve, reject) => {
                                                let businessType = form.businessType || ''
                                                return businessType !== '' ? resolve('success') : reject('先选择业务类型')
                                            })
                                        }
                                    },
                                    bindFunction: function (Vue, data){     
                                        // 竞价        
                                        if(Vue.form.businessType==='1'){
                                            Vue.form.businessNumber = data[0].ebiddingNumber
                                            Vue.form.businessName = data[0].ebiddingDesc
                                            Vue.form.businessId = data[0].id
                                        }     
                                        //询价                           
                                        if(Vue.form.businessType==='2'){
                                            Vue.form.businessNumber = data[0].enquiryNumber
                                            Vue.form.businessName = data[0].enquiryDesc
                                            Vue.form.businessId = data[0].id
                                        }  
                                        //招标
                                        if(Vue.form.businessType==='3'){
                                            Vue.form.businessNumber = data[0].biddingNumber
                                            Vue.form.businessName = data[0].biddingDesc
                                            Vue.form.businessId = data[0].id
                                        }  
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessName`, '业务描述'),
                                    fieldName: 'businessName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessName`, '业务描述')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessId`, '业务单据id'),
                                    fieldName: 'businessId',
                                    disabled: true,
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessId`, '业务单据id')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_matterTitel`, '事项标题'),
                                    fieldName: 'matterTitel',
                                    required: '1'
                                },
                                {
                                    fieldType: 'richEditorModel',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_clarificationMatter`, '澄清事项'),
                                    fieldName: 'clarificationMatter',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_clarificationMatter`, '澄清事项'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    disabled: true,
                                    dictCode: 'srmSendStatus'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                businessType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderTypeCantEmpty`, '业务类型不能为空')}],
                                businessNumber: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderNoCantEmpty`, '业务单号不能为空'), trigger: 'change'}],
                                matterTitel: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_matterTitelCantEmpty`, '事项标题不能为空')}],
                                clarificationMatter: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_clarificationMatterCantEmpty`, '澄清事项不能为空')}]
                            }
                        }
                        
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'attachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'clarification', callBack: this.uploadCallBack},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFilesEvent}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_savePublish`, '保存并发布'), type: 'primary', click: this.publishEvent,
                        authorityCode: 'bidding#purchaseClarificationInfo:publish' },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/bidding/purchaseClarificationInfo/add',
                publish: '/bidding/purchaseClarificationInfo/publish',
                upload: '/attachment/purchaseAttachment/upload'
            },
            businessNumberExtendArray: [
                {    
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidDocument`, '竞价单'),   
                    modalColumns: [         
                        {field: 'ebiddingNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号'), with: 150},         
                        {field: 'ebiddingDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentCheck`, '单据检查'), with: 150},         
                        {field: 'ebiddingStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingStatus`, '竞价单状态'), with: 150}     
                    ],     
                    modalUrl: '/ebidding/purchaseEbiddingHead/list?superQueryParams=%5B%7B%22fieldCode%22:%22ebiddingStatus%22,%22fieldType%22:%22select%22,%22dictCode%22:%22srmEbiddingStatus%22,%22fieldValue%22:%220%22,%22logicSymbol%22:%22ne%22,%22joiner%22:%22AND%22%7D%5D&column=id&order=desc',
                    modalParams: {}
                },
                { 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquirySheet`, '询价单'),      
                    modalColumns: [         
                        {field: 'enquiryNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquirySheetNo`, '询价单号'), with: 150},         
                        {field: 'enquiryDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enquiryDesc`, '询价描述'), with: 150},         
                        {field: 'enquiryStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquirySheetStatus`, '询价单状态'), with: 150}     
                    ],     
                    modalUrl: '/enquiry/purchaseEnquiryHead/list?superQueryParams=%5B%7B%22fieldCode%22:%22enquiryStatus%22,%22fieldType%22:%22input%22,%22dictCode%22:%22srmEnquiryStatus%22,%22fieldValue%22:%220%22,%22logicSymbol%22:%22ne%22,%22joiner%22:%22AND%22%7D%5D&column=id&order=desc',
                    modalParams: {}
                },
                {   
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tenderFrom`, '招标单'),  
                    modalColumns: [         
                        {field: 'biddingNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tenderNumber`, '招标单号'), with: 150},         
                        {field: 'biddingDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_biddingDesc`, '单据简称'), with: 150}
                    ],     
                    modalUrl: '/bidding/purchaseBiddingHead/list?superQueryParams=%5B%7B%22fieldCode%22:%22biddingStatus%22,%22fieldType%22:%22input%22,%22fieldValue%22:%220%22,%22logicSymbol%22:%22ne%22,%22joiner%22:%22AND%22%7D%5D&column=id&order=desc',
                    modalParams: {}
                }
            ]
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        // 更新业务单据号
        updateBusinessNumberExtendModel (type) {
            let selectIndex = type?parseInt(type)-1: 0
            this.pageData.groups[0].custom.formFields[2].extend= Object.assign({}, this.pageData.groups[0].custom.formFields[2], this.businessNumberExtendArray[selectIndex])
        },
        init () {},
        uploadCallBack (result) {
            let fileGrid = this.$refs.addPage.$refs.attachments[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent () {
            const fileGrid = this.$refs.addPage.$refs.attachments[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.add
                    postAction(url, params).then(res => {
                        debugger
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.init()
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        publishEvent (){
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.publish
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.goBack()
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>
<style lang="less" scoped>
.page-container{
    :deep(.edit-form-box){
        width: 100%;
    }
}
    
</style>