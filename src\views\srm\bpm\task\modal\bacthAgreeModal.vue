<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_priorityLevel`, '优先级')"
          prop="priority">
          <a-radio-group
            name="radioGroup"
            defaultValue="50"
            v-model="form.priority">
            <a-radio
              v-for="(item, index) in priorityMap"
              :key="index"
              :value="item.value">{{
                item.title
              }}</a-radio>
          </a-radio-group>
        </a-form-model-item>

        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_taskName`, '任务标题')"
          prop="taskTitle">
          <a-input
            v-model="form.taskTitle"
            clearable />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见')"
          prop="opinion">
          <a-textarea
            show-word-limit
            v-model="form.opinion"
            allowClear
            type="textarea"
            autoSize />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_BdL_183905d`, '抄送人')"
          prop="usersInfo">
          <a-tag
            v-for="(users, userIndex) in form.usersInfo"
            :key="userIndex"
            size="large"
            color="blue"
            closable
            @close="delUsers(index, userIndex)"
          >{{ users.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectUsers()"></a-button>
          </div>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_HjU_1706bfd`, '常用语')"
          v-if="approval && approval.length > 0">
          <a-tag
            v-for="(item, index) in approval"
            color="primary"
            :key="index"
            type="border"
            @click.native="selectTag(item)"
          >
            {{ item.content }}
          </a-tag>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from '../../components/modal/modalMixins.js'
import {postAction} from '@/api/manage'
// import { listVariable } from '../../api/variable.js'
export default {
    mixins: [modalMixins],
    props: {
        taskInfoList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    data () {
        return {
            bpmVarList: [],
            approval: [],
            relatedId: '',
            relatedType: '',
            nodeIndex: null,
            monitor: false,
            userShow: false,
            // existData: null,
            labelCol: { span: 6 },
            single: false,
            rules: {
                taskTitle: [
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                opinion: [
                    { required: true, message: '请填写备注/意见', trigger: 'blur' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ]
                // usersInfo: [{ required: true, message: '请选择处理人' } ]
            }
        }
    },
    methods: {
        delUsers (index, userIndex) {
            this.form.usersInfo.splice(userIndex, 1)
        },
        selectUsers () {
            this.showUserSelectModal({ selectModel: 'multiple' })
        },
        fieldSelectOk (data) {
            this.$set(this.form, 'usersInfo', data)
        },
        selectTag (data) {
            this.form.opinion = data.content
        },
        handleConfirm () {
            this.cheackValidate().then(() => {
                const params = Object.assign({}, this.form)
                params['monitor'] = this.monitor
                params['action'] = 'agree'
                params['actionName'] = 'agree'
                params['taskInfoList'] = this.taskInfoList.map(item => {
                    return {
                        id: item.id,
                        taskTitle: item.taskTitle
                    }
                })
                this.loading = true
                postAction('/a1bpmn/audit/api/runtime/task/batch/complete', params).then(response => {
                    this.loading = false
                    if (response.success) {
                        this.$emit('success')
                        if (response.result && response.result.length > 0) {
                            response.result.forEach((item, index) => {
                                this.$message.error(item)
                            })
                        }  else {
                            this.$message.success(response.message)
                        }
                    } else {
                        this.$message.error(response.message)
                    }
                }).finally(() => {
                    this.loading = false
                })
            })
        }
    },
    cheackValidate () {
        return new Promise((resolve, reject) => {
            this.$refs[this.formName].validate(valid => {
                if (valid) {
                    resolve(true)
                } else {
                    console.log('error submit!!')
                    reject(false)
                }
            })
        })
    },
    beforeCreate () {
        this.form = this.$form.createForm(this, { name: 'customUploadForm' })
    },
    created () {
        this.getDictData('taskPriority', 'priorityMap')
    }
}
</script>
