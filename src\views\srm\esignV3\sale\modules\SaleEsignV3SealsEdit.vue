<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'
export default {
    name: 'SaleEsignV3SealsEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'seals',
            confirmLoading: false,
            pageData: {
                form: {
                    sealType: '1',
                    orgId: '',
                    orgName: '',
                    psnName: '',
                    psnId: '',
                    filePath: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_WeLD_27cabba0`, '印章维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商账号'),
                                    fieldName: 'elsAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.form.elsAccount = data[0].toElsAccount,
                                        Vue.form.companyName = data[0].supplierName
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150},
                                            {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150}
                                        ], modalUrl: 'supplier/supplierMaster/list', modalParams: {}
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_transferType`, '类型'),
                                    fieldName: 'sealType',
                                    dictCode: 'srmEsignSealsType',
                                    required: '1',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        console.log('prop:', prop)
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value == '1'){
                                            setDisabledByProp('orgName', false)
                                            setDisabledByProp('psnName', true)
                                        }else{
                                            setDisabledByProp('orgName', true)
                                            setDisabledByProp('psnName', false)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dWtR_2e111a28`, '所属机构'),
                                    fieldName: 'orgName',
                                    disabled: false,
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dWtR_2e111a28`, '所属机构'),
                                    bindFunction: function (Vue, data){
                                        Vue.form.orgId = data[0].orgId
                                        Vue.form.orgName = data[0].orgName
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), with: 150},
                                            {field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tReyID_1a3ee6f6`, '机构账号ID'), with: 150},
                                            {field: 'orgIdCardNum', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIy_76cf8d5`, '组织机构证件号'), with: 150}
                                        ],
                                        modalUrl: '/esignv3/saleEsignV3Org/list',
                                        modalParams: function (Vue, form, obj){
                                            return {realnameStatus: '1'}
                                        }
                                    }
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WedWL_d12084ec`, '印章所属人'),
                                    fieldName: 'psnName',
                                    disabled: true,
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WedWL_d12084ec`, '印章所属人'),
                                    bindFunction: function (Vue, data){
                                        Vue.form.psnId = data[0].psnId
                                        Vue.form.psnName = data[0].psnName
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150},
                                            {field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), with: 150},
                                            {field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPsmLjDeyBK_b93e3b33`, 'E签宝个人用户账号标识'), with: 150}
                                        ], modalUrl: '/esignv3/saleEsignV3Personal/list', modalParams: {realnameStatus: '1'}
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeRL_27c5a0f3`, '印章名称'),
                                    fieldName: 'sealName',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_width`, '印章宽度（单位：mm，上限为100mm）'),
                                    fieldName: 'sealWidth',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_width`, '印章宽度（单位：mm，上限为100mm）'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_height`, '印章高度（单位：mm，上限为100mm）'),
                                    fieldName: 'sealHeight',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_height`, '印章高度（单位：mm，上限为100mm）'),
                                    required: '1'

                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeOy_27cdf6c6`, '印章颜色'),
                                    fieldName: 'sealColor',
                                    dictCode: 'sealColor',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeOy_27cdf6c6`, '印章颜色')

                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeESAc_49a8b9e7`, '印章业务类型'),
                                    fieldName: 'sealBizType',
                                    dictCode: 'sealBizType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeESAc_49a8b9e7`, '印章业务类型')
                                },
                                {
                                    fieldType: 'image',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_qBGuFJsS`, '图片路径'),
                                    fieldName: 'filePath',
                                    required: '1',
                                    extend: {multiple: false, limit: 1, businessType: 'esign', actionRoutePath: '/srm/esignV3/sale/SaleEsignV3SealsList'}
                                }
                            ],
                            validateRules: {
                                elsAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#111`, '供应商ELS账号不能为空')}],
                                orgName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dWtRxOLV_5390ca38`, '所属机构不能为空')}],
                                sealName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeqRxOLV_4fade7f4`, '签章别名不能为空')}],
                                filePath: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WexOLV_4a4b6180`, '印章不能为空')}],
                                sealType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AcxOLV_61ddb680`, '类型不能为空')}],
                                sealBizType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeESAcxOLV_96c47277`, '印章业务类型不能为空')}],
                                sealHeight: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WexzxOLV_bd4cd4e`, '印章高度不能为空')}],
                                sealWidth: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeVzxOLV_5c72d389`, '印章宽度不能为空')}]
                            }
                        }

                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_2CYTheJuOy2DtsZC`, '图片上传E签宝'), type: 'primary', click: this.uploadSeal, showCondition: this.showUploadBtn  },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                edit: '/esignv3/saleEsignV3Seals/edit',
                detail: '/esignv3/saleEsignV3Seals/queryById',
                uploadSeal: '/esignv3/saleEsignV3Seals/uploadToEsign'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        showUploadBtn (){
            if(this.currentEditRow.uploaded == '1'){
                return false
            }
            return true
        },
        prevEvent () {
            this.$refs.editPage.prevStep()
        },
        nextEvent () {
            this.$refs.editPage.nextStep()
        },
        saveEvent () {
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    if(params.sealType == 'LP' && !params.lpLetterPath){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AWeAcLhLKWlTXVhLlbW_b4518378`, '当印章类型为法人时，必须上传法人授权书'))
                        return
                    }
                    let url = this.url.edit
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.init()
                        }
                    }).finally(() => {
                        this.$refs.editPage.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        uploadSeal () {
            this.$refs.editPage.confirmLoading = true
            const row = this.$refs.editPage.getPageData()
            getAction('/esignv3/saleEsignV3Seals/uploadToEsign', {id: row.id}).then((res)=>{
                // 接口调用完查询
                if(res && res.success){
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVLR_24aeb765`, '推送成功'))
                    this.currentEditRow.uploaded == '1'
                    this.$refs.editPage.confirmLoading = false
                    this.searchEvent()
                }else{
                    this.$message.warning(res.message)
                    this.$refs.editPage.confirmLoading = false
                }
            })
            this.searchEvent()
        }
    }
}
</script>