<template>
  <div class="BiddingCandidateDetail">
    <a-spin :spinning="confirmLoading">
      <div class="head">
        <span>{{ dataObj.noticeTitle }}</span>
      </div>
      <div class="titlp">
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_creator`, '创建者') }}：{{ dataObj.createby }}</span>|<span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_publishTime`, '发布时间') }}：{{ dataObj.updateTime }}</span>
      </div>
      <div class="content">
        <p v-html="dataObj.noticeContent"></p>
      </div>
      <!-- <div class="footer-grid">
        <vxe-grid
          v-bind="gridOptions"
          :columns="columns"
          :data="gridData">
        </vxe-grid>
      </div> -->
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
    data () {
        return {
            confirmLoading: false,
            gridOptions: {
                border: true,
                showHeaderOverflow: true,
                showOverflow: true,
                align: 'center',
                headerAlign: 'center',
                height: '200',
                size: 'mini',
                toolbarConfig: {
                    enabled: false
                }
            },
            columns: [
                {type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tLRL_2715c81b`, '单位名称')},
                {field: 'scopeSort', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialOrder`, '排序'), width: 80}
                // {field: 'quote', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价')},
                // {field: 'evaPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHf_252229e6`, '中标金额')}
            ],
            gridData: [],
            dataObj: {
                noticeTitle: '',
                updateTime: '',
                noticeContent: '',
                noticeType: '',
                createby: ''
            },
            params: {},
            url: {
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId'
            }
        }
    },
    created () {
        const queryData = this.$route.query
        this.params = queryData
    },
    mounted () {
        this.getInfo()
    },
    methods: {
        async getInfo () { // 获取招标信息详情
            if (!this.params && !this.params.id) {
                return false
            }
            try {
                const url = `/inquiry/public/noToken/queryById/${this.params.businessId}/${this.params.businessType}`
                let subId = ''
                this.confirmLoading = true
                const res = await getAction(url)
                if (res.code == 200) {
                    const {result} = res
                    result && ((result) => {
                        console.log(result)
                        const {noticeTitle, updateTime, noticeContent, noticeType, createby, subpackageId, bidWinningAffirmList} = result
                        subId = subpackageId
                        this.dataObj = {
                            noticeTitle: noticeTitle,
                            updateTime: updateTime,
                            noticeContent: noticeContent,
                            noticeType: noticeType,
                            createby: createby
                        }
                        // this.gridData = result.bidWinningAffirmList
                        bidWinningAffirmList && bidWinningAffirmList.forEach(item => {
                            item.saleQuoteColumnVOS.forEach(vos => {
                                item[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                                item.affirm = item.affirm == '1' ? true : false
                            })
                        })
                        this.gridData = bidWinningAffirmList
                    })(result)
                } else {
                    this.$message.error(res.message)
                }

                let params = {
                    subpackageId: subId
                }
                this.confirmLoading = true
                const res2 = await getAction(this.url.queryPrice, params)
                if (res.code == 200 && res2.result) {
                    const resultData = res2.result
                    let columns = []
                    resultData.forEach(data => {
                        let obj = {
                            title: data.title,
                            children: []
                        }
                        let columnChildren = []
                        data.quoteColumnList.forEach(column => {
                            column['field'] = `${column['field']}_${data['id']}`
                            column['width'] = '120'
                            columnChildren.push(column)
                        })
                        obj.children = columnChildren
                        columns.push(obj)
                    })
                    
                    this.columns = this.columns.filter(column => {
                        if (!column.hasOwnProperty('children')) {
                            return column
                        }
                    })
                    this.columns.splice(3, 0, ...columns)
                }
            } catch (err) {
                console.log(err)
            }
            this.confirmLoading = false
        }
    }
}
</script>

<style lang="less" scoped>
.BiddingCandidateDetail{
  height: 100%;

  .head {
    text-align: center;
    height: 60px;
    line-height: 60px;
    font-size: 18px;
    font-weight: 600;
  }
  .titlp {
    color: blue;
    text-align: center;
    margin-bottom: 20px;
    font-size: 14px;

    span:nth-child(1) {
      margin-right: 20px;
    }
    span:last-child {
      margin-left: 20px;
    }
  }
  .content{
    margin-top: 10px;
    height: calc(100vh - 300px);
    overflow-y: auto;
  }
  .footer-grid {
    margin-top: 10px;
    margin-left: 10px;
    margin-right: 10px;
    position: fixed;
    bottom: 0;
    width: calc(100% - 20px);
  }
}
</style>