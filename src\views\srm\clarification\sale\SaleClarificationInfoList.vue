<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab" 
      :url="url" />
    <!-- 详情界面 -->
    <SaleClarificationInfoDetail 
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" /> 
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleClarificationInfoDetail from './modules/SaleClarificationInfoDetail'
import { postAction } from '@/api/manage'
import layIM from '@/utils/im/layIM.js'
export default {
    mixins: [ListMixin],
    components: {
        SaleClarificationInfoDetail
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                businessType: 'purchase_clarification',
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_clarificationNo`, '澄清单号'),
                        fieldName: 'clarificationNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_clarificationNo`, '澄清单号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                        fieldName: 'businessType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                        dictCode: 'srmClarificationBusinessType'
                    }
                ],
                optColumnWidth: 170,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'bidding#saleClarificationInfo:queryById'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, authorityCode: 'bidding#saleClarificationInfo:creatGruopChat'}
                ]
            },
            url: {
                list: '/bidding/saleClarificationInfo/list',
                edit: '/bidding/saleClarificationInfo/edit',
                columns: 'SaleClarificationInfo',
                download: '/attachment/saleAttachment/download'
            },
            countTabsUrl: '/bidding/saleClarificationInfo/counts'

        }
    },
    mounted () {
        this.serachCountTabs(this.countTabsUrl)
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.clarificationNumber || id
            // 创建群聊
            layIM.creatGruopChat({id, type: 'SaleClarificationInfo', url: this.url || '', recordNumber})
        },
        handleView (row) {
            this.currentEditRow = row
            if(row.viewStatus==='0'){
                //触发查询按钮时，将“查看状态”置为“已阅读”
                row.viewStatus = '1'
                postAction(this.url.edit, row).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.showDetailPage = true
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            }else{
                this.showDetailPage = true
            }
            
        }
    }
}
</script>