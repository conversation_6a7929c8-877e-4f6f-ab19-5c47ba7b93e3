// 🚀 验证编辑页面联系人信息页签空间填充效果的脚本
// 请在浏览器控制台中执行此脚本

console.log('🔍 开始验证编辑页面联系人信息页签空间填充效果...');

// 1. 检查当前页面类型
const isEditPage = window.location.href.includes('edit') || 
                   document.querySelector('.edit-layout') ||
                   document.querySelector('[class*="edit"]');

const isDetailPage = window.location.href.includes('detail') || 
                     document.querySelector('.detail-layout') ||
                     document.querySelector('[class*="detail"]');

console.log('📋 页面类型检测:');
console.log(`- 编辑页面: ${isEditPage ? '✅ 是' : '❌ 否'}`);
console.log(`- 详情页面: ${isDetailPage ? '✅ 是' : '❌ 否'}`);

// 2. 检查窗口尺寸
const windowHeight = document.documentElement.clientHeight;
const windowWidth = document.documentElement.clientWidth;
console.log(`📐 窗口尺寸: ${windowWidth} × ${windowHeight}`);

// 3. 查找关键元素
const elements = {
    tabsContentHolder: document.querySelector('.ant-tabs-content-holder'),
    tabPane: document.querySelector('.ant-tabs-tabpane'),
    contactsGrid: document.querySelector('[data-group-code="supplierContactsInfoList"], [data-group-code="contactsInfo"]'),
    contactsGridByRef: document.querySelector('[ref*="supplierContactsInfoList"], [ref*="contactsInfo"]'),
    editLayout: document.querySelector('.edit-layout'),
    detailLayout: document.querySelector('.detail-layout'),
    vxeTable: null,
    vxeBodyWrapper: null
};

// 查找表格元素
const gridElement = elements.contactsGrid || elements.contactsGridByRef;
if (gridElement) {
    elements.vxeTable = gridElement.querySelector('.vxe-table');
    elements.vxeBodyWrapper = gridElement.querySelector('.vxe-table--body-wrapper');
}

console.log('🎯 关键元素查找结果:');
Object.keys(elements).forEach(key => {
    const element = elements[key];
    console.log(`- ${key}: ${element ? '✅ 找到' : '❌ 未找到'}`);
    if (element && element.offsetHeight) {
        console.log(`  尺寸: ${element.offsetWidth} × ${element.offsetHeight}`);
    }
});

// 4. 检查控制台调试信息
console.log('🔍 查找编辑页面相关调试信息...');
console.log('请查看控制台中是否有以下调试信息:');
console.log('- "🎯 编辑页面 - 联系人信息表格极致空间优化"');
console.log('- "🎯 编辑页面 - 联系人信息表格在tab模式下的最终高度"');

// 5. 计算空间利用率
if (gridElement) {
    const gridHeight = gridElement.offsetHeight;
    const utilization = (gridHeight / windowHeight * 100).toFixed(1);
    
    console.log('📊 空间利用分析:');
    console.log(`- 表格高度: ${gridHeight}px`);
    console.log(`- 窗口高度: ${windowHeight}px`);
    console.log(`- 空间利用率: ${utilization}%`);
    
    // 计算理想高度（编辑页面）
    const idealHeight = windowHeight - 120;
    const heightDiff = idealHeight - gridHeight;
    
    console.log(`- 理想高度: ${idealHeight}px`);
    console.log(`- 高度差异: ${heightDiff}px`);
    
    if (heightDiff > 50) {
        console.warn('⚠️ 编辑页面仍有较大空白区域，需要进一步优化');
        
        // 提供编辑页面专用的强制调整方案
        console.log('🔧 编辑页面强制调整方案:');
        console.log(`
// 执行以下代码强制调整编辑页面表格高度
const gridElement = document.querySelector('[data-group-code="supplierContactsInfoList"], [data-group-code="contactsInfo"]') || 
                   document.querySelector('[ref*="supplierContactsInfoList"], [ref*="contactsInfo"]');
if (gridElement) {
    // 编辑页面特殊处理
    gridElement.style.height = '${idealHeight}px';
    gridElement.style.minHeight = '${idealHeight}px';
    
    const table = gridElement.querySelector('.vxe-table');
    if (table) {
        table.style.height = '${idealHeight}px';
        table.style.minHeight = '${idealHeight}px';
    }
    
    const bodyWrapper = gridElement.querySelector('.vxe-table--body-wrapper');
    if (bodyWrapper) {
        bodyWrapper.style.maxHeight = '${idealHeight - 50}px';
        bodyWrapper.style.minHeight = '${idealHeight - 100}px';
    }
    
    // 确保tab内容容器也充分利用空间
    const tabsHolder = document.querySelector('.ant-tabs-content-holder');
    if (tabsHolder) {
        tabsHolder.style.height = '${idealHeight + 50}px';
        tabsHolder.style.minHeight = '${idealHeight + 50}px';
    }
    
    const tabPane = document.querySelector('.ant-tabs-tabpane');
    if (tabPane) {
        tabPane.style.height = '100%';
        tabPane.style.minHeight = '${idealHeight}px';
    }
    
    console.log('✅ 已强制调整编辑页面表格高度');
}
        `);
    } else {
        console.log('✅ 编辑页面空间利用良好！');
    }
} else {
    console.error('❌ 未找到联系人信息表格元素');
    
    // 提供查找帮助
    console.log('🔍 尝试查找其他可能的表格元素:');
    const allGrids = document.querySelectorAll('.vxe-table');
    console.log(`- 找到 ${allGrids.length} 个表格元素`);
    
    const allRefs = document.querySelectorAll('[ref]');
    console.log(`- 找到 ${allRefs.length} 个带ref属性的元素`);
    
    allRefs.forEach((el, index) => {
        const refValue = el.getAttribute('ref');
        if (refValue && (refValue.includes('contact') || refValue.includes('supplier'))) {
            console.log(`  - ref="${refValue}": ${el.offsetWidth} × ${el.offsetHeight}`);
        }
    });
}

// 6. 检查是否在正确的页签
const activeTab = document.querySelector('.ant-tabs-tab-active');
if (activeTab) {
    const tabText = activeTab.textContent;
    console.log(`📋 当前活动页签: ${tabText}`);
    
    if (!tabText.includes('联系人') && !tabText.includes('联系人信息')) {
        console.warn('⚠️ 当前不在联系人信息页签，请切换到联系人信息页签后重新执行此脚本');
    }
} else {
    console.warn('⚠️ 未找到活动页签');
}

// 7. 检查样式应用情况
console.log('🎨 编辑页面样式应用检查:');

if (elements.tabsContentHolder) {
    const styles = window.getComputedStyle(elements.tabsContentHolder);
    console.log('Tab内容容器样式:');
    console.log(`- height: ${styles.height}`);
    console.log(`- min-height: ${styles.minHeight}`);
}

if (elements.vxeTable) {
    const styles = window.getComputedStyle(elements.vxeTable);
    console.log('VXE表格样式:');
    console.log(`- height: ${styles.height}`);
    console.log(`- min-height: ${styles.minHeight}`);
}

// 8. 提供编辑页面专用的实时监控
console.log('🔄 启用编辑页面实时监控...');
let monitorInterval;

function startEditPageMonitoring() {
    monitorInterval = setInterval(() => {
        const currentGrid = document.querySelector('[data-group-code="supplierContactsInfoList"], [data-group-code="contactsInfo"]') || 
                           document.querySelector('[ref*="supplierContactsInfoList"], [ref*="contactsInfo"]');
        if (currentGrid) {
            const currentHeight = currentGrid.offsetHeight;
            const currentUtilization = (currentHeight / document.documentElement.clientHeight * 100).toFixed(1);
            console.log(`📊 编辑页面实时监控 - 高度: ${currentHeight}px, 利用率: ${currentUtilization}%`);
        }
    }, 5000); // 每5秒检查一次
}

function stopEditPageMonitoring() {
    if (monitorInterval) {
        clearInterval(monitorInterval);
        console.log('⏹️ 已停止编辑页面实时监控');
    }
}

// 自动启动监控
startEditPageMonitoring();

// 提供停止监控的方法
window.stopEditPageSpaceMonitoring = stopEditPageMonitoring;

console.log('🎉 编辑页面验证完成！');
console.log('💡 如需停止实时监控，请执行: stopEditPageSpaceMonitoring()');

// 9. 特殊提示
if (isEditPage) {
    console.log('📝 编辑页面特殊说明:');
    console.log('- 编辑页面的高度计算逻辑已优化');
    console.log('- 如果仍有空白，请尝试上面的强制调整方案');
    console.log('- 编辑页面可能需要刷新后才能看到效果');
} else {
    console.log('ℹ️ 当前可能不是编辑页面，如果是编辑页面但检测失败，请手动执行强制调整方案');
}
