<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-04-27 15:34:49
 * @LastEditors: LokNum
 * @LastEditTime: 2022-05-16 16:40:32
 * @Description: 采购协同/寻源协同/询价代报价
-->
<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 详情界面 -->
    <enquiry-substitute-detail
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import EnquirySubstituteDetail from './modules/EnquirySubstituteDetail'
export default {
    mixins: [ListMixin],
    components: {
        EnquirySubstituteDetail
    },
    data () {
        return {
            pageData: {
                form: {
                    enquiryDesc: '',
                    enquiryNumber: ''
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_inquirySheetNo`, '询价单号'),
                        fieldName: 'enquiryNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterinquirySheetNo`, '请输入询价单号') 
                    },
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQDescription`, '询价单描述'),
                        fieldName: 'enquiryDesc',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterRFQDescription`, '请输入询价单描述') 
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleSpecialByView},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/enquiry/enquirySubstituteHead/list',
                columns: 'enquirySubstituteList' 
            }
        }
    }
}
</script>