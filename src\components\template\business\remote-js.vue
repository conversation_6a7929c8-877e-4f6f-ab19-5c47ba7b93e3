<script>
import { mapGetters } from 'vuex'
import { LOCALSERIVCEURL } from '@/utils/constant.js'

const isDevelopment = process.env.NODE_ENV === 'development'

export default {
    name: 'RemoteJs',
    props: {
        src: {
            type: String,
            default: ''
        },
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        },
        role: {
            type: String,
            default: 'purchase'
        },
        moduleName: {
            type: [String, Function]
        },
        // 本地调试使有
        // 开启后从本地拉取代码
        isLocal: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        ...mapGetters(['userInfo']),
        fileSrc () {
            let _moduleName = ''
            if (typeof this.moduleName === 'function') {
                _moduleName = this.moduleName(this.currentEditRow) || ''
            } else {
                // 自定义
                if (this.moduleName && this.moduleName === 'custom') {
                    return
                }
                _moduleName = this.moduleName
            }
            const {
                templateNumber = '',
                templateVersion = '',
                templateAccount = '',
                busAccount = ''
            } = this.currentEditRow || {}
            const { elsAccount = '', serivceUrl = '' } = this.userInfo || {}
            // 当前账号没配置模板时，取 100000 号原始模板
            let account = templateAccount || busAccount || elsAccount
            let time = +new Date()

            let filePath = `${this.role}_${_moduleName}_${templateNumber}_${templateVersion}.js`

            console.log('this.$variateConfig.configFiles :>> ', this.$variateConfig.configFiles)

            let fileSrc = this.isLocal
                ? `${LOCALSERIVCEURL}/${filePath}?t=${time}`
                : `${this.$variateConfig.configFiles}/${account}/${filePath}?t=${time}`

            console.log('remote-js fileSrc :>> ', fileSrc)

            return fileSrc
        },
        isCustom () {
            return this.moduleName === 'custom'
        },
        absoluteIsLocal () {
            return isDevelopment && this.isLocal
        }
    },
    methods: {
        loadSuccess () {
            const pageConfig = this.isCustom ? {} : getPageConfig()
            this.$emit('remote-js-load-success', {
                pageConfig
            })
        },
        loadError (err) {
            this.$emit('remote-js-load-error', {
                err
            })
        }
    },
    created () {
        if (this.isCustom) this.loadSuccess()
    },
    render (h) {
        let src = this.absoluteIsLocal || this.moduleName ? this.fileSrc : this.src
        console.log('this.absoluteIsLocal || !this.src :>> ', this.absoluteIsLocal || !this.src)
        console.log('src :>> ', src)
        const scriptEl = h('script', {
            // defer 渲染完再执行
            // async 下载完就执行
            attrs: { type: 'text/javascript', src: src, async: true },
            on: {
                load: this.loadSuccess,
                error: this.loadError
            }
        })

        return this.isCustom ? h('view', { class: 'scrip' }) : scriptEl
    }
}
</script>
