import Vue from 'vue'
import axios from 'axios'
import store from '@/store'
import { VueAxios } from './axios'
import {notification, Modal} from 'ant-design-vue'
import { ACCESS_TOKEN, DEFAULT_LANG } from '@/store/mutation-types'
import { ROUTER_WHITE_LIST } from '@/utils/const'
import { srmI18n, getLangAccount } from '@/utils/util'
import router from '@/router'
// 创建 axios 实例
const service = axios.create({
    baseURL: '/els' // api base_url
    // timeout: 600000 // 请求超时时间
})
// 判断当前的路由地址是否在白名单内
const checkedWhileListAddress= ()=> {
    return new Promise((resolve)=> {
        let pathName = location.pathname
        if (ROUTER_WHITE_LIST.indexOf(pathName) !== -1) {
            resolve(true)
        } else {
            resolve(false)
        }
    })
}
const err = (error) => {
    if (error.response) {
        let data = error.response.data
        const token = Vue.ls.get(ACCESS_TOKEN)
        console.log('------异常响应------', token)
        console.log('------异常响应------', error.response.status)
        switch (error.response.status) {
        case 403:
            notification.error({ message: srmI18n(`${getLangAccount()}#i18n_title_systemPrompt`, '系统提示'), description: srmI18n(`${getLangAccount()}#i18n_alert_FKCQ_2ed9919a`, '拒绝访问'), duration: 4})
            break
        case 520:
            if(token){
                notification.error({ message: srmI18n(`${getLangAccount()}#i18n_title_systemPrompt`, '系统提示'), description: srmI18n(`${getLangAccount()}#i18n_alert_BsPWEHIRAWVVVEHW_e7c2aed5`, '很抱歉，登录已过期，请重新登录!'), duration: 4})
                checkedWhileListAddress().then((res)=> {
                    if (!res) {
                        store.dispatch('Logout').then(() => {
                            Vue.ls.remove(ACCESS_TOKEN)
                            window.location.reload()
                        })
                    }
                })

            }
            break
        case 530: {
            const logoutMark = store.state.app.logoutMark
            if (logoutMark) {
                return true
            }
            loginAgainByCode(error.response.status)
            break
        }
        case 404:
            notification.error({ message: srmI18n(`${getLangAccount()}#i18n_title_systemPrompt`, '系统提示'), description: srmI18n(`${getLangAccount()}#i18n_alert_BsPWJjLYuW_a056051d`, '很抱歉，资源未找到!'), duration: 4})
            break
        case 504:
            notification.error({ message: srmI18n(`${getLangAccount()}#i18n_title_systemPrompt`, '系统提示'), description: srmI18n(`${getLangAccount()}#i18n_alert_TWBK_3bcdb3fc`, '网络超时')})
            break
        case 401:
            notification.error({ message: srmI18n(`${getLangAccount()}#i18n_title_systemPrompt`, '系统提示'), description: srmI18n(`${getLangAccount()}#i18n_alert_LlbWVVVEH_ca660b8d`, '未授权，请重新登录'), duration: 4})
            if (token) {
                checkedWhileListAddress().then((res)=> {
                    if (!res) {
                        store.dispatch('Logout').then(() => {
                            setTimeout(() => {
                                window.location.reload()
                            }, 1500)
                        })
                    }
                })
            }
            break
        default:
            notification.error({
                message: srmI18n(`${getLangAccount()}#i18n_title_systemPrompt`, '系统提示'),
                description: data.message,
                duration: 4
            })
            break
        }
    }
    return Promise.reject(error)
}
const resErr = (data) => {
    const {code, message} = data
    const logoutMark = store.state.app.logoutMark
    if (logoutMark) {
        return true
    }
    if (code === 520 || code === 530) {
        loginAgainByCode(code)
    } else if (code === 510) { // 接口权限拦截
        notification.error({ message: srmI18n(`${getLangAccount()}#i18n_title_systemPrompt`, '系统提示'), description: message, duration: 4})
    } else {
        return true
    }
}

const loginAgainByCode = (code) => {
    const tips = code === 520 ? srmI18n(`${getLangAccount()}#i18n_alert_BsPWEHIRAWVVVEHW_e7c2aed5`, '很抱歉，登录已过期，请重新登录!') : srmI18n(`${getLangAccount()}#i18n_alert_APjDIqEGWVVVEHW_b4b9da18`, '当前用户已被登出，请重新登录!')
    store.commit('SET_SYS_LOGOUT', true)
    checkedWhileListAddress().then((res)=> {
        if (!res) {
            // notification.error({ message: srmI18n(`${getLangAccount()}#i18n_title_systemPrompt`, '系统提示'), description: tips, duration: 4})
            Modal.info({
                title: srmI18n(`${getLangAccount()}#i18n_title_systemPrompt`, '系统提示'),
                iconType: 'info-circle',
                content: tips,
                okText: srmI18n(`${getLangAccount()}#i18n_field_Rl_a72da`, '关闭'),
                onOk () {
                    store.dispatch('Logout').then(() => {
                        Vue.ls.remove(ACCESS_TOKEN)
                        // if (router?.app?.$route?.path == '/user/login') { // 登录页面不需重复reload
                        //     return false
                        // }
                        window.location.reload()
                    })
                }
            })
        }
        Vue.ls.remove(ACCESS_TOKEN)
    })
} 
// request interceptor
service.interceptors.request.use(config => {
    const token = Vue.ls.get(ACCESS_TOKEN)
    const language = Vue.ls.get(DEFAULT_LANG) || 'zh'
    if (token) {
        config.headers[ 'X-Access-Token' ] = token // 让每个请求携带自定义 token 请根据实际情况自行修改
        config.headers[ 'language' ] = language // 让每个请求携带自定义 token 请根据实际情况自行修改
    }
    if(config.method=='post'){
        if(config.url.indexOf('els/dict/findDictItems')<0){
            config.params = {
                _t: Date.parse(new Date())/1000,
                ...config.params
            }
        }
    }
    return config
}, (error) => {
    return Promise.reject(error)
})

// response interceptor
service.interceptors.response.use((response) => {
    let data = response.data
    resErr(data)
    return data
}, err)

const installer = {
    vm: {},
    install (Vue, router = {}) {
        Vue.use(VueAxios, router, service)
    }
}

export {
    installer as VueAxios,
    service as axios
}