import {textColor} from '@comp/chart/widget/utils/theme.js'
// K线图
export default {
    backgroundColor: 'rgba(255, 255, 255, 0)',
    title: {
        show: true,
        text: '',
        top: 10,
        left: 'center',
        textStyle: {// 主标题文字样式
            color: textColor,
            fontWeight: 'normal',
            fontSize: 18
        },
        subtext: '',
        subtextStyle: { // 副标题文字样式
            color: textColor,
            fontWeight: 'normal',
            fontSize: 16
        }
    },
    legend: { // 图例
        show: true,
        left: 'left',
        orient: 'horizontal',
        textStyle: {
            color: textColor,
            fontSize: 10
        }
    },
    grid: {// 直角坐标系轴 - 最外面的矩形 - 或者理解为坐标轴边距
        show: false,
        top: 60,
        right: 20,
        bottom: 60,
        left: 60
    },
    tooltip: { // 鼠标悬浮的提示语
        show: true,
        textStyle: {
            color: '#000000',
            fontWeight: 'normal',
            fontSize: 14
        }
    },
    color: ['#83bff6', '#23B7E5', '#61a0a8', '#d48265'],
    xAxis: {
        data: ['2017-10-24', '2017-10-25', '2017-10-26', '2017-10-27'],
        axisLine: {         // X坐标轴
            show: true,
            lineStyle: {
                color: textColor
            }
        },
        axisLabel: {
            show: true,
            color: textColor,
            fontWeight: 'normal',
            fontSize: 12
        },
        axisTick: {         // X坐标轴刻度
            show: true
        },
        splitLine: {        // X坐标的网格线
            show: false
        }
    },
    yAxis: {
        axisLine: {         // Y坐标轴
            show: true,
            lineStyle: {
                color: textColor
            }
        },
        axisLabel: {
            show: true,
            color: textColor,
            fontWeight: 'normal',
            fontSize: 12
        },
        axisTick: {         // Y坐标轴刻度
            show: true
        },
        splitLine: {        // Y坐标的网格线
            show: false
        }
    },
    series: [
        {
            type: 'candlestick',
            data: [],
            barWidth: 15,     // 柱形宽度
            itemStyle: {
                color: '#c23531', // 阳线颜色     
                color0: '#314656', // 阴线颜色   
                borderColor: '#c23531', // 阳线边框颜色   
                borderColor0: '#314656' // 阴线边框颜色   
            }
        }
    ]
}













