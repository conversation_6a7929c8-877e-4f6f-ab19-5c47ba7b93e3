<script lang="jsx">
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { ajaxFindDictItems } from '@/api/api'
import { isDef } from '@/utils/util.js'
export default {
    name: 'Checkbox',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        options: {
            type: Array,
            default () {
                return []
            }
        },
        value: {
            type: String,
            default: ''
        },
        dictCode: {
            type: String,
            default: ''
        },
        isLayout: {
            type: Boolean,
            default: false
        },
        colSpan: {
            type: [String, Number],
            default: 6
        },
        selectControlType: {
            type: String,
            default: ''
        }
    },
    data () {
        let realOptions = this.options.map(n => {
            let label =  this.$srmI18n(`${this.$getLangAccount()}#${n.textI18nKey}`, n.title)
            return { label, value: n.value }
        })
        return {
            type: '',
            selectValue: [],
            realOptions: realOptions
        }
    },
    watch: {
        value: {
            immediate: true,
            handler (val) {
                this.selectValue = val ? val.split(',') : []
            }
        },
        dictCode: {
            immediate: true,
            handler () {
                this.initDictData()
            }
        },
        selectControlType: {
            immediate: true,
            handler (val) {
                this.toggleSelectAll(val)
            }
        }
    },
    methods: {
        toggleSelectAll (type) {
            if (!isDef(type)) {
                return
            }
            if (type === 'all') {
                let checkedValues = this.realOptions.map(n => n.value)
                this.$emit('change', checkedValues.join(','))
            }
            if (type === 'none') {
                this.$emit('change', '')
            }
        },
        initDictData () {
            if (this.dictCode) {
                //根据字典Code, 初始化字典数组
                let postData = {
                    busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: this.dictCode
                }             
                ajaxFindDictItems(postData).then((res) => {
                    const result = res.result || []
                    this.realOptions = result.map(n => {
                        let label =  this.$srmI18n(`${this.$getLangAccount()}#${n.textI18nKey}`, n.title)
                        return { label, value: n.value }
                    })
                })
            }
        },
        handleChange (checkedValues) {
            this.$emit('change', checkedValues.join(','))
        }
    },
    render () {
        const checkboxGroup = this.isLayout
            ? (
                <a-checkbox-group
                    vModel={ this.selectValue }
                    onChange={ this.handleChange }
                >
                    <a-row>
                        {
                            this.realOptions.map(n => {
                                return (
                                    <a-col span={ this.colSpan }>
                                        <div class="boxWrap">
                                            <a-checkbox value={ n.value }>
                                                { n.label }
                                            </a-checkbox>
                                        </div>
                                    </a-col>
                                )
                            })
                        }
                    </a-row>
                </a-checkbox-group>
            )
            : (
                <a-checkbox-group
                    vModel={ this.selectValue }
                    options={ this.realOptions }
                    onChange={ this.handleChange }
                />
            )
        
        return (
            this.realOptions && this.realOptions.length ? checkboxGroup : null
        )
    }
}
</script>


<style lang="less" scoped>
.boxWrap {
    display: flex;
    justify-content: flex-start;
    
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
