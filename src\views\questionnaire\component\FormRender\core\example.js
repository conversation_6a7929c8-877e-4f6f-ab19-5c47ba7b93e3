import { srmI18n, getLangAccount } from '@/utils/util'

export const exampleData = {
    basic: {
        version: ''
    },
    formDesc: {
        rows: [
        ],
        footer: {
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 14, offset: 2 }
            },
            buttons: [
                {
                    text: srmI18n(`${getLangAccount()}#i18n_title_define`, '确定'),
                    type: 'primary',
                    size: 'default',
                    disabled: false,
                    ifValidateForm: true
                },
                {
                    text: srmI18n(`${getLangAccount()}#i18n_title_close`, '取消'),
                    type: 'default',
                    size: 'default',
                    disabled: false,
                    ifValidateForm: false
                }
            ]
        },
        ui: {
            style: { padding: '80px' },
            gutter: 32
            // showType: 'inline', // vertical, inline, horizontal todo 此属性与 grid组件不兼容
        },
        associate: { // 关联属性 todo 如何在ui层添加逻辑
            login: {
                value: true,
                properties: ['PW'],
                type: 'show' // 'disable'
            }
        }
    },
    api: {
        url: 'xxx',
        token: 'xxx',
        fieldName: 'data'
    }
}
export const extendPattern = {
    uppercase: {
        regexp: /^[A-Z]+$/,
        message: srmI18n(`${getLangAccount()}#i18n_title_fillInNonCapitalLetters`, '填入内容非大写字母！')
    },
    abc: {
        regexp: /abc/,
        message: srmI18n(`${getLangAccount()}#i18n_title_fillInNonCapitalLetters`, '填入内容非大写字母！')
    }
}
