<template>
  <div class="edit-page">
    <a-spin
      v-if="!reviewStatus && !scoringStatus && !reviewScoringStatus && !priceComparisonStatus && !priceScoreStatus && !showQuotes"
      :spinning="confirmLoading">
      <div class="page-header">
        <a-steps
          :current="currentStep"
          @change="changeStep"
          size="small">
          <template v-for="step in stepList">
            <a-step
              :key="step.id"
              :title="step.name" />
          </template>
        </a-steps>
      </div>
      <div class="page-content">
        <div class="page-content-title">
          <titleCrtl>

            <span>{{ titleFn }}</span>
            <template slot="right">
              <a-button
                v-if="titleFn == pingbus"
                @click="handleEvaluationClarify"
                style="float: right">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBLV_411d1f04`, '评标澄清') }}</a-button>
            </template>
          </titleCrtl>

        </div>
        <div class="page-content-grid">
          <template v-for="(grid, index) in gridList">
            <vxe-grid
              :loading="loading"
              :ref="grid.gridCode"
              :key="grid.gridCode"
              v-show="index == currentStep"
              v-bind="gridConfig"
              height="300"
              :data="grid.gridData"
              :columns="grid.columns">
              <template
                #expert_default="{ row }"
                v-if="index == 0">
                <span>{{ row.expert }}</span>
                <span
                  v-if="row.judgesGroupLeader == '1'"
                  style="color: blue; margin-left: 5px">{{ $srmI18n(`${$getLangAccount()}#i18n_field_[Ve]_2175647`, '[组长]') }}</span>
              </template>
              <template #data_default="{ row, column }">
                <span v-if="row.invalid == '1'">/</span>
                <span v-else>{{ row[column['property']] }}</span>
              </template>
              <template #checkbox_default="{ row }">
                <vxe-checkbox
                  v-if="row.invalid != '1'"
                  v-model="row.candidate"
                  checked-value="1"
                  unchecked-value="0"
                  disabled></vxe-checkbox>
              </template>
              <template
                #file_default="{ row }"
                v-if="index == 2">
                <span v-if="row.fileType == '1'">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBBm_41201cd7`, '评标表格') }}</span>
                <span v-if="row.fileType == '2'">{{ $srmI18n(`${$getLangAccount()}#i18n_field_UBsx_411b7648`, '评标报告') }}</span>
              </template>
              <template
                #cz_default="{ row }"
                v-if="index == 2">
                <a-button
                  v-if="row.fileName"
                  type="link"
                  @click="downloadEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a-button>
                <a-button
                  v-if="row.fileName"
                  type="link"
                  @click="preViewEvent(row)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a-button>
              </template>
            </vxe-grid>
          </template>
        </div>
      </div>
      <div class="page-footer">
        <a-button
          :type="customPageFooterPreBtn.type"
          v-if="currentStep"
          @click="customPageFooterPreBtn.click">{{ customPageFooterPreBtn.title }}</a-button>
        <a-button
          :type="customPageFooterNextBtn.type"
          v-if="currentStep < stepList.length - 1"
          @click="customPageFooterNextBtn.click">{{ customPageFooterNextBtn.title }}</a-button>
        <a-button
          @click="handleQuotes"
          v-if="canMultipleQuotes"
          type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_field_hAOVsu_fb06e64c`, '发起多轮报价') }}</a-button>
        <a-button @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </div>
    </a-spin>
    <!-- 评审项 -->
    <ReviewItems
      v-if="reviewStatus"
      :currentRow="currentRow" />
    <!-- 评分项 -->
    <ScoringItem
      v-if="scoringStatus"
      :currentRow="currentRow" />
    <PriceComparison
      v-if="priceComparisonStatus"
      :currentRow="currentRow"
    />
    <!-- 评审评分项 -->
    <ReviewScoringItem
      v-if="reviewScoringStatus"
      :currentRow="currentRow" />
    <!-- 价格评分项 -->
    <PriceScore
      v-if="priceScoreStatus"
      :currentRow="currentRow" />
    <multipleQuotes
      v-if="showQuotes"
      :pageStatus="'detail'"
      :show="showQuotes"
      :currentEditRow="currentEditRow" />
    <QuoteDetailsModal ref="QuoteDetailsModal" />
    <LeaderOpinionModal
      ref="LeaderOpinionModal"
      typeNum="leader"
      pageStatus="detail"/>
  </div>
</template>

<script lang="jsx">
import { getAction, postAction } from '@/api/manage'
// import { gridOptionsMixin } from '../public/gridOptionsMixin.js'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import ReviewItems from '../components/ReviewItems.vue'
import ScoringItem from '../components/ScoringItem.vue'
import ReviewScoringItem from '../components/ReviewScoringItem.vue'
import PriceScore from '../components/PriceScore.vue'
import LeaderOpinionModal from '../components/LeaderOpinionModal.vue'
import { USER_INFO } from '@/store/mutation-types'
import multipleQuotes from '../multipleQuotes/multipleQuotes.vue'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import QuoteDetailsModal from '../components/QuoteDetailsModal.vue'
import PriceComparison from '../components/PriceComparison.vue'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    mixins: [tableMixins],
    computed: {
        titleFn () {
            let title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理')
            switch (this.currentStep) {
            case 1:
                title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUAR_40ed07d8`, '评审排名')
                break
            case 2:
                title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRnL_753bc075`, '评标结果材料')
                break
            default:
                title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理')
                break
            }
            return title
        },
        headParams () {
            let { checkType, currentStep, processType } = this.currentEditRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return { xNodeId }
        },
        canMultipleQuotes () {
            // 后审才能发起多轮报价
            if (this.formData && this.formData.stageQuote == '1' && this.currentEditRow.checkType == '1') {
                // 多轮报价发起人:0-全部，1-采购方，2-评标组长
                if (this.formData.stageQuoteOperator == '1') {
                    let { elsAccount } = this.$ls.get(USER_INFO)
                    if (this.formData.purchaseExecutorAccount == elsAccount) return true
                    return false
                } else if (this.formData.stageQuoteOperator == '2') {
                    if (this.leaderStatus) return true
                    return false
                } else {
                    return true
                }
            } else {
                return false
            }
        }
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {}
        }
    },
    components: {
        ReviewItems,
        ScoringItem,
        ReviewScoringItem,
        PriceScore,
        multipleQuotes,
        titleCrtl,
        QuoteDetailsModal,
        LeaderOpinionModal,
        PriceComparison
    },
    data () {
        return {
            pingbus: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBRv_411e9c88`, '评标管理'),
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            reviewStatus: false, // 评审项组件控制状态
            scoringStatus: false, // 评分项组件控制状态
            reviewScoringStatus: false, // 评审评分项组件控制状态
            priceScoreStatus: false, // 价格评分项
            confirmLoading: false,
            showQuotes: false, // 发起多轮报价
            priceComparisonStatus: false,
            loading: false,
            currentStep: 0,
            customPageFooterPreBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_prevStep`, '上一步'), type: 'primary', belong: 'preStep', click: this.prevStep },
            customPageFooterNextBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nextStep`, '下一步'), type: 'primary', belong: 'nextStep', click: this.nextStep },
            stepList: [
                { id: 1, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_UBRv_411e9c88`, '评标管理') },
                { id: 2, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUAR_40ed07d8`, '评审排名') },
                { id: 3, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UByRnL_753bc075`, '评标结果材料') }
            ],
            gridList: [
                {
                    gridCode: 'BidEvaluation',
                    columns: [{ field: 'expert', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_evaluationExpert`, '评标专家'), slots: { default: 'expert_default' } }],
                    gridData: []
                },
                {
                    gridCode: 'ReviewRanking',
                    columns: [],
                    gridData: []
                },
                {
                    gridCode: 'BidEvaluationResults',
                    columns: [
                        { type: 'seq', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 50 },
                        { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nLAc_30836479`, '材料类型'), slots: { default: 'file_default' } },
                        { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileName`, '文件名称') },
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), slots: { default: 'cz_default' } }
                    ],
                    gridData: [
                        { fileType: '1', fileName: '', id: '' },
                        { fileType: '2', fileName: '', id: '' }
                    ]
                }
            ],
            columns1: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), type: 'seq', width: 50 },
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBtL_2e61022a`, '投标单位') }
            ],
            columns2: [
                { field: 'weightScore', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_totalScore`, '总分'), slots: { default: 'data_default' } },
                { field: 'order', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'), slots: { default: 'data_default' } },
                { field: 'candidate', type: '', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQYI_2fbbaebf`, '是否推荐'), width: 100, slots: { default: 'checkbox_default' } }
            ],
            currentRow: {},
            rankingColumns: [],
            isEvaFinish: false, // 是否全部完成评审
            formData: {}
        }
    },
    mounted () {
        console.log(this.currentEditRow)
        this.getBidEvaluationData()
        this.getIsEvaFinish()
    },
    methods: {
        // 跳转到评标澄请列表页
        handleEvaluationClarify () {
            let params = { linkFilter: true, subpackageId: this.currentEditRow.subpackageId, evaInfoId: this.currentEditRow.evaInfoId, ifToList: true, checkType: this.currentEditRow.checkType, processType: this.currentEditRow.processType, currentStep: this.currentEditRow.currentStep }
            this.$router.push({ name: 'BidEvaluationClarifyList', query: params })
        },
        checkBoxChange (row, $event) {
            const { checked } = $event
            const gridObj = this.$refs.ReviewRanking
            if (gridObj && gridObj.length > 0) {
                gridObj[0].setCheckboxRow(row, checked)
            }
        },
        // 获取是否完成评标
        getIsEvaFinish () {
            const params = {
                // subpackageId: this.currentEditRow.subpackageId || ''
                evaInfoId: this.currentEditRow.evaInfoId || ''
            }
            getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/isEvaFinish', params, { headers: this.headParams }).then((res) => {
                if (res.code == 200) {
                    let { result = false } = res
                    if (result) {
                        this.getSupplierEvaRanking()
                    }
                    this.isEvaFinish = result
                }
            })
        },
        // 获取评标管理数据
        async getBidEvaluationData () {
            const params = {
                id: this.currentEditRow.id || ''
            }
            const params2 = {
                subpackageId: this.currentEditRow.subpackageId || '',
                requestType: 1
            }
            this.confirmLoading = true
            // 获取评标专家组数据
            const res = await getAction('/tender/evaluation/purchaseTenderProjectEvaSettingHead/queryEvaGroupBySubpackageId', params2, { headers: this.headParams })
            if (res.code != 200) return
            const resultList = res.result || []
            this.rankingColumns = resultList
            let columns = []
            resultList.forEach((rl) => {
                if (rl) {
                    let titleTip = '',
                        type = rl.type || '0'
                    switch (type) {
                    case '0':
                        titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WUUdW_4326c815`, '(评审项)')
                        break
                    case '1':
                        titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n__WUzdW_4302b93a`, '(评分项)')
                        break
                    case '2':
                        titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WUUUzdW_10142117`, '(评审评分项)')
                        break
                    case '3':
                        titleTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WumUUdW_143bbba`, '(价格评审项)')
                        break
                    }
                    const column = {
                        field: rl.id,
                        checkType: rl.checkType,
                        currentStep: rl.currentStep,
                        title: `${rl.name}${titleTip}`,
                        slots: {
                            header: () => {
                                return [
                                    <span>{`${rl.name}${titleTip}`}<a-icon type="edit" theme="twoTone" onClick={() => this.showLeaderOpinion(rl)}/></span>
                                ]
                            },
                            // 使用 JSX 渲染
                            default: ({ row, column }) => {
                                if (row[column.property]) {
                                    let span = null
                                    if (row['summaryStatus']) {
                                        if (rl.type != '3'  && rl.type != '4') {
                                            span =
                                                row[column.property] == 0 ? (
                                                    <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LMk_190c17e`, '未汇总')}</span>
                                                ) : (
                                                    <span style="color: blue; cursor: pointer;" onClick={() => this.pbclick({ row: row, type: rl.type, id: rl.id, status: false, sum: true, summaryCalType: rl.summaryCalType, title: rl.name })}>
                                                        {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IMk_16e2646`, '已汇总')}
                                                    </span>
                                                )
                                        } else {
                                            span = <span></span>
                                        }
                                    } else {
                                        if (row['btnStatus']) {
                                            // 属于自己的评审项
                                            span =
                                                row[column.property] == 0 ? (
                                                    <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LUU_1948d07`, '未评审')}</span>
                                                ) : (
                                                    <div>
                                                        <span style="color: blue; cursor: pointer;" onClick={() => this.pbclick({ row: row, type: rl.type, id: rl.id, status: false, title: rl.name })}>
                                                            {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUML_40ec1d21`, '评审完成')}
                                                        </span>
                                                    </div>
                                                )
                                        } else {
                                            if (row['leaderStatus']) {
                                                // 组长可以查看其他评审完成的信息
                                                span =
                                                    row[column.property] == 0 ? (
                                                        <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LUU_1948d07`, '未评审')}</span>
                                                    ) : (
                                                        <span style="color: blue; cursor: pointer;" onClick={() => this.pbclick({ row: row, type: rl.type, id: rl.id, status: false, title: rl.name })}>
                                                            {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUML_40ec1d21`, '评审完成')}
                                                        </span>
                                                    )
                                            } else {
                                                span = row[column.property] == 0 ? <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LUU_1948d07`, '未评审')}</span> : <span style="color: #999;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUML_40ec1d21`, '评审完成')}</span>
                                            }
                                        }
                                    }
                                    return [span]
                                }
                                return []
                            }
                        }
                    }
                    columns.push(column)
                }
            })
            console.log(columns)
            // 由于返回值中processType为空，所以不作为条件过滤
            let {checkType, currentStep} = this.currentEditRow
            if(checkType == '1' && currentStep == '1'){
                columns=columns.filter(item=>{
                    if(item.checkType == '1' && item.currentStep == '1'){
                        return true
                    }
                })
            }
            this.gridList.forEach((grid) => {
                if (grid.gridCode == 'BidEvaluation') {
                    grid.columns = [grid.columns[0]] // 清空原有数据，只留第一条
                    grid.columns = [...grid.columns, ...columns] // 合并最终需要的数据
                }
            })

            // 查询专家内容
            const res2 = await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryById', params, { headers: this.headParams })
            if (res2.code != 200) return
            const resultList2 = res2.result
            this.formData = resultList2
            const expertTaskList = resultList2.expertTaskList || {}
            let gridData = []
            const { subAccount } = this.$ls.get(USER_INFO)
            // 连续替换，记录是否全部评标完成
            let record = {}
            let leaderStatus = false
            Object.keys(expertTaskList).forEach((key) => {
                let objArray = key && key.split('_')
                let obj = {
                    expert: objArray[0],
                    elsAccount: objArray[1],
                    elsSubAccount: objArray[2],
                    btnStatus: subAccount == objArray[2] ? true : false,
                    summaryStatus: false
                }

                expertTaskList[key].forEach((item, index) => {
                    if (index == 0) {
                        obj['judgesGroupLeader'] = item['judgesGroupLeader']
                        // 标识当前账号是否是组长
                        if (item['judgesGroupLeader'] == '1' && subAccount == objArray[2]) {
                            leaderStatus = true
                        }
                    }
                    obj[item.evaGroupId] = item.evaGroupStatus
                    record[item.evaGroupId] = record[item.evaGroupId] == 0 ? '0' : item.evaGroupStatus // 记录最后一次状态
                    record[`${item.evaGroupId}_ID`] = item.id
                    obj[`${item.evaGroupId}_ID`] = item.id
                })
                if (obj['judgesGroupLeader'] == '1') {
                    gridData.unshift(obj)
                } else {
                    gridData.push(obj)
                }
            })
            gridData.push(Object.assign({}, record, { expert: '汇总', summaryStatus: true }))
            gridData.forEach((grid, index, array) => {
                grid['leaderStatus'] = leaderStatus
            })
            this.gridList.forEach((grid) => {
                if (grid.gridCode == 'BidEvaluation') {
                    grid.gridData = gridData // 合并最终需要的数据
                }
            })
            this.confirmLoading = false
        },
        // 获取评审排名和评标结果材料数据
        async getSupplierEvaRanking () {
            const params = {
                evaInfoId: this.currentEditRow.evaInfoId || ''
            }
            await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/getSupplierEvaRanking', params, { headers: this.headParams }).then((res) => {
                if (res.code == 200) {
                    let { result = {} } = res
                    let supplierEvaRanking = result && result.supplierEvaRanking
                    const evaBidAttachmentInfoList = result.evaBidAttachmentInfoList
                    let oldData = {}
                    let col = []
                    supplierEvaRanking &&
                        supplierEvaRanking.forEach((item, index) => {
                            let resultItem = item.result
                            // debugger
                            if (resultItem) {
                                const resultData = Object.keys(resultItem).map((rl) => {
                                    return resultItem[rl]
                                })
                                let dataObj = {}
                                resultData.forEach((data) => {
                                    const evaGroupResult = data['evaGroupResult'] == '1' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_passed`, '通过') : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xeR_13701ba`, '不通过')
                                    dataObj[data['evaGroupId']] = data['evaGroupType'] == '0' ? evaGroupResult : data['weightScore']
                                })
                                item = Object.assign(item, dataObj)
                                if (index === 0) {
                                    // 第一次组装好完整的 表头信息
                                    // let col = []
                                    this.rankingColumns.forEach((rank) => {
                                        // 判断评审不通过时的操作
                                        oldData[rank['id']] = rank['type'] == '0' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xeR_13701ba`, '不通过') : '/'
                                    })
                                }
                            } else {
                                item = Object.assign(item, oldData)
                            }
                        })
                    // type:3价格分,4价格比较项
                    let typeflag = false
                    this.rankingColumns.forEach((rank) => {
                        if (rank.type == '4') typeflag = true
                        // 延用评标管理的表头顺序
                        col.push({
                            field: rank['id'],
                            title: rank['name'],
                            width: 100
                        })
                    })
                    let bidLetterFormatGroup = result && result.bidLetterFormatGroup
                    // 是否是分项且是否是比较项
                    if (bidLetterFormatGroup.quoteType == '1' && typeflag) {
                        this.columns2.unshift(...[
                            { field: 'summaryRegulationResultList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sudV_2e111e11`, '报价详情'), width: 100,
                                slots: {
                                    default: ({ row }) => {
                                        return [
                                            <a-button type="link" onClick={() => this.view(row)}>
                                                {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                            </a-button>
                                        ]
                                    }
                                }
                            }
                        ])
                    }
                    col = [...this.columns1, ...col, ...this.columns2]
                    this.gridList.forEach((grid) => {
                        if (grid.gridCode == 'ReviewRanking') {
                            grid.gridData = supplierEvaRanking
                            grid.columns = col // 合并最终需要的数据
                        }
                        if (grid.gridCode == 'BidEvaluationResults') {
                            grid.gridData.forEach((res) => {
                                res['tenderProjecId'] = this.currentEditRow.tenderProjectId || ''
                                res['subpackageId'] = this.currentEditRow.subpackageId || ''
                            })
                        }
                    })
                    if (evaBidAttachmentInfoList) {
                        this.gridList.forEach((grid) => {
                            if (grid.gridCode == 'BidEvaluationResults') {
                                grid.gridData = evaBidAttachmentInfoList.length > 0 ? evaBidAttachmentInfoList : grid.gridData // 合并最终需要的数据
                            }
                        })
                    }
                } else {
                    this.$message.error(res.message)
                    this.confirmLoading = false
                }
            })

            let gridObj = this.$refs.ReviewRanking[0]
            this.gridList[1].gridData &&
                this.gridList[1].gridData.forEach((grid) => {
                    if (grid.candidate == '1') {
                        gridObj.setCheckboxRow(grid, true) // 设置默认勾选项
                    }
                })
        },
        // 预览文件
        preViewEvent (row) {
            const subpackageId = row.subpackageId
            let preViewFile = {
                id: row.id,
                sourceType: row.sourceType,
                filePath: row.filePath,
                subpackageId
            }
            if(row.attachmentId){
                preViewFile.id = row.attachmentId
            }
            this.$previewFile.open({ params: preViewFile })
        },
        async downloadEvent (row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.attachmentId ? row.attachmentId : row.id
            console.log(row.attachmentId, row.id)
            const fileName = row.fileName
            const subpackageId = row.subpackageId
            let {message: url} = await getAttachmentUrl({id, subpackageId})
            this.confirmLoading=true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                this.confirmLoading=false
            })
        },
        // 点击评标触发该函数
        pbclick (rowData) {
            let { row, type, id, status, sum = false, summaryCalType = null, title } = rowData
            console.log(row, type, id, status, sum, summaryCalType, title)
            this.currentRow = {
                editStatus: status,
                summary: sum,
                summaryCalType: summaryCalType,
                title: title,
                evaGroupId: id,
                elsAccount: row.elsAccount,
                elsSubAccount: row.elsSubAccount,
                expert: row.expert,
                id: row[`${id}_ID`] || ''
            }
            let { checkType, currentStep, processType, subpackageId, evaInfoId } = this.currentEditRow
            this.currentRow = Object.assign(this.currentRow, { checkType, currentStep, processType, subpackageId, evaInfoId })

            switch (type) {
            case '0':
                this.reviewStatus = true
                break
            case '1':
                this.scoringStatus = true
                break
            case '2':
                this.reviewScoringStatus = true
                break
            case '3':
                this.priceScoreStatus = true
                break
            case '4':
                this.priceComparisonStatus = true
                break

            }
        },
        view (row) {
            let data = []
            for (let key in row.result) {
                if (row.result[key]['evaGroupType'] == '4') {
                    data = row.result[key]['summaryRegulationResultList'] && row.result[key]['summaryRegulationResultList'].filter(item => {
                        return item['supplierAccount'] == row['supplierAccount']
                    })
                }
            }
            this.$refs.QuoteDetailsModal.open(data)
        },
        changeStep (step) {
            if (!this.isEvaFinish) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MKLMLjUULSSMkLS_142afc07`, '存在未完成的评审任务或汇总任务'))
                return false
            }
            this.currentStep = step
        },
        prevStep () {
            this.currentStep > 0 && this.currentStep--
        },
        nextStep () {
            if (!this.isEvaFinish) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MKLMLjUULSSMkLS_142afc07`, '存在未完成的评审任务或汇总任务'))
                return false
            }
            if (this.currentStep < this.stepList.length - 1) {
                this.currentStep++
            }
        },
        // 返回
        goBack () {
            this.$emit('hide')
        },
        // 发起多轮报价
        handleQuotes () {
            this.showQuotes = true
        },
        // 评审意见
        showLeaderOpinion (item) {
            this.confirmLoading = true
            getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryLeaderOpinion', {evaGroupId: item.id, evaInfoId: this.formData.evaInfoId}).then(res => {
                if (res.success) {
                    let data = res.result
                    this.$refs.LeaderOpinionModal.open({data, title: item.name, leaderStatus: this.leaderStatus})
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    }
}
</script>

<style lang="less" scoped>
//编辑界面
.edit-page {
    height: 100%;
    .ant-spin-nested-loading {
        height: 100%;
        .ant-spin-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
    }
    .page-header {
        padding: 6px 14px;
        margin-bottom: 6px;
        background-color: #fff;
        .desc-col {
            text-align: left;
            line-height: 32px;
        }
        .btn-col {
            text-align: right;
        }
    }
    .page-content {
        flex: 1;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: auto;
        padding-left: 6px;
        padding-top: 6px;
        padding-right: 6px;
        background-color: #fff;
        margin-left: 6px;
        margin-right: 6px;
        .edit-form-box {
            // position: absolute;
        }
        .ant-advanced-rule-form {
            padding: 12px;
            .ant-form-item {
                display: flex;
                margin-bottom: 0;
                height: 55px;
                line-height: 55px;
                .ant-form-item-label {
                    text-overflow: ellipsis;
                }
            }
            .ant-form-item-control-wrapper {
                flex: 1;
            }
        }
        .edit-grid-box {
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            left: 0;
            overflow: auto;
            .vxe-toolbar {
                padding: 0 12px;
                .vxe-toolbar.size--mini {
                    height: 42px;
                }
                .tools-btn {
                    margin-left: 6px;
                }
            }
            .ant-input {
                height: 28px;
            }
        }
        .page-content-title {
            background-color: #eee;
            padding: 10px;
        }
        .page-content-grid {
            margin-top: 10px;
        }
    }
    .page-footer {
        border-top: 1px solid #e8eaec;
        background-color: #fff;
        margin-top: -1px;
        margin-left: 6px;
        margin-right: 6px;
        padding: 6px;
        text-align: center;
        .ant-btn:not(:first-child) {
            margin-left: 6px;
        }
    }
}
</style>
