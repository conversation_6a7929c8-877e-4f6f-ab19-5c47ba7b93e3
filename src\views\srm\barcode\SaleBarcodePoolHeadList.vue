<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 详情界面 -->
    <sale-barcode-pool-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
  </div>
</template>
<script>
import SaleBarcodePoolHeadDetail from './modules/SaleBarcodePoolHeadDetail'
import { ListMixin } from '@comp/template/list/ListMixin'
import { postAction } from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        SaleBarcodePoolHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodeInfo',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNToAy_b7f8464a`, '请输入条码编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'barcode#salePool:detail'
                    }
                ]
            },
            url: {
                list: '/base/barcode/saleBarcodePoolHead/list',
                add: '/base/barcode/saleBarcodePoolHead/add',
                delete: '/base/barcode/saleBarcodePoolHead/delete',
                changeStatus: '/base/barcode/saleBarcodePoolHead/changeStatus',
                invalid: '/base/barcode/saleBarcodePoolHead/invalid',
                deleteBatch: '/base/barcode/saleBarcodePoolHead/deleteBatch',
                exportXlsUrl: '/base/barcode/saleBarcodePoolHead/exportXls',
                importExcelUrl: '/base/barcode/saleBarcodePoolHead/importExcel',
                columns: 'saleBarcodePoolHeadList'
            }
        }
    },
    methods: {
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        showEnabled (row) {
            return row.status == 'disabled' ? false : true
        },
        showDisabled (row) {
            return row.status == 'enabled' ? false : true
        },
        handleEnabled (row) {
            let that = this
            const params = { id: row.id, statusType: '1' }
            postAction(this.url.changeStatus, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_AjLR_28088728`, '启用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        },
        handleDisabled (row) {
            let that = this
            const params = { id: row.id, statusType: '2' }
            postAction(this.url.changeStatus, params).then(res => {
                if (res.success) {
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_HjLR_38ff8896`, '禁用成功'))
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            })
        }
    }
}
</script>