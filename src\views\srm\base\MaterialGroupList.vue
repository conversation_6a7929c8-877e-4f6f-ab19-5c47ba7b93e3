<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <material-group-modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
  </div>
</template>

<script>
import MaterialGroupModal from './modules/MaterialGroupModal'
import {listPageMixin} from '@comp/template/listPageMixin'

export default {
    name: 'MaterialGroupList',
    mixins: [listPageMixin],
    components: {
        MaterialGroupModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input', 
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroupName`, '物料组名称'),
                        fieldName: 'materialGroupName', 
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterMaterialGroupName`, '请输入物料组名称')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/base/materialGroup/list',
                delete: '/base/materialGroup/delete',
                deleteBatch: '/base/materialGroup/deleteBatch',
                exportXlsUrl: '/base/materialGroup/exportXls',
                importExcelUrl: '/base/materialGroup/importExcel',
                columns: 'baseMaterialGroupList'          
            }
        }
    },
    computed: {

    },
    created () {

    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroupInfo`, '物料组信息'))
        }
    }
}
</script>
