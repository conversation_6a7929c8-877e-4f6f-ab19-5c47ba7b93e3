<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction } from '@/api/manage'
import {srmI18n, getLangAccount} from '@/utils/util.js'
export default {
    name: 'EsignFlowAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'esignFlow',
            pageData: {
                form: {
                    busType: 'contract',
                    busNumber: '',
                    businessScene: '',
                    toElsAccount: '',
                    supplierName: '',
                    relationId: '',
                    filesName: '',
                    filesId: '',
                    uploaded: '',
                    autoArchiving: '',
                    autoInitiate: '',
                    remark: '',
                    cutOffTime: '',
                    contractRemind: '',
                    noticeType: '',
                    effectiveTime: '',
                    sendStatus: '0',
                    signerVindicateStatus: '0'
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'busType',
                                    dictCode: 'electronicSignatureBusType',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: '供方ELS账号',
                                    fieldName: 'toElsAccount',
                                    placeholder: '供方ELS账号',
                                    bindFunction: function (Vue, data){
                                        Vue.form.toElsAccount = data[0].elsAccount, 
                                        Vue.form.supplierName = data[0].name
                                    }, extend: {modalColumns: [
                                        {field: 'elsAccount', title: srmI18n(`${getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), with: 150},
                                        {field: 'name', title: srmI18n(`${getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), with: 150}
                                    ],     
                                    modalUrl: '/supplier/supplierMaster/queryAccessSupplier',
                                    modalParams: {}
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: '合同',
                                    fieldName: 'busNumber',
                                    placeholder: '合同',
                                    bindFunction: function (Vue, data){    
                                        Vue.form.busNumber = data[0].contractNumber,    
                                        Vue.form.filesName = data[0].contractName,
                                        Vue.form.businessScene = data[0].contractName,    
                                        Vue.form.relationId = data[0].id 
                                    }, extend: {      
                                        modalColumns: [         
                                            {field: 'contractNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractNumber`, '合同单号'), with: 150},         
                                            {field: 'contractName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractName`, '合同名称'), with: 150}
                                        ],     
                                        modalUrl: '/contract/purchaseContractHead/list',     
                                        modalParams: function (Vue, form) {
                                            return{
                                                auditStatus: '2',
                                                toElsAccount: form.toElsAccount
                                            }
                                        }
                                    }
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessAssociationID`, '业务关联id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                                    fieldName: 'filesName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'),
                                    fieldName: 'filesId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkisSuccessfullyUploaded`, '文件是否上传成功'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    disabled: true,
                                    dictCode: 'srmSendStatus'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSignatoryMaintainsTheState`, '签署人维护状态'),
                                    fieldName: 'signerVindicateStatus',
                                    disabled: true,
                                    dictCode: 'srmSignerVindicateStatus'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_autoArchiving`, '是否自动归档'),
                                    fieldName: 'autoArchiving',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheProcessIsAutomaticallyStarted`, '流程是否自动开启'),
                                    fieldName: 'autoInitiate',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationDateSigning`, '签署有效截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'number',
                                    fieldLabel: '文件到期前多少时间提醒(小时)',
                                    fieldName: 'contractRemind'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationdateDocument`, '文件有效截止时间'),
                                    fieldName: 'effectiveTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                busType: [{required: true, message: '业务类型不能为空'}],
                                busNumber: [{required: true, message: '业务单号不能为空', trigger: 'change'}],
                                businessScene: [{required: true, message: '文件主题不能为空'}]
                            }
                        }
                        
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/esign/elsEsign/add'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {},
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    let url = this.url.add
                    const _this = this
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            _this.$parent.addCallBack(res.result)
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>