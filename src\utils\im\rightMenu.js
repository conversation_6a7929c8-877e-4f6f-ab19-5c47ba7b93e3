import store from '../../store'
import { srmI18n, getLangAccount } from '../util'

// 递归获取系统菜单id
const findSupLink = (data, supplierId) => {
    //传入
    const it = (i, n) => {
        if (i && i.length > 0) {
            for (let v of i) {
                if (v.id == n) {
                    return v
                } else {
                    if (v.children && v.children.length > 0) {
                        let re = it(v.children, n)
                        if (re) {
                            return re
                        }
                    }
                }
            }
        }
    }
    let ret = it(data, supplierId)
    return ret
}
const menuInit = () => {
    // 系统菜单
    let sysMenuList = store.getters.permissionList
    let menu = []
    // 系统菜单交互
    const sysMenu = [
        {
            id: '1284051076171304961', // 菜单id
            title: srmI18n(`${getLangAccount()}#i18n_menu_nRIt_446776b9`, '采购订单'), // 菜单标
            handleFnName: 'menuBindEvent',
            isV5Menu: true
        },
        {
            id: '1285103684332396546', // 菜单id
            title: srmI18n(`${getLangAccount()}#i18n_menu_XlIt_450c48a1`, '销售订单'), // 菜单标
            handleFnName: 'menuBindEvent',
            isV5Menu: true
        }
    ]
    // im菜单交互
    const baseMenu = [
        {
            id: 'deleteFriend', // 菜单id
            title: srmI18n(`${getLangAccount()}#i18n_field_QGyj_2794b132`, '删除好友 '), // 菜单标
            handleFnName: 'menuDeleteFriend',
            isV5Menu: false
        },
        {
            id: 'moveToGroup', // 菜单id
            title: srmI18n(`${getLangAccount()}#i18n_field_yjIu_29f77e03`, '好友移到'), // 菜单标
            handleFnName: 'moveToGroup',
            isV5Menu: false
        }
    ]
    sysMenu.forEach(el => {
        let ls = findSupLink(sysMenuList, el.id)
        if (ls) {
            el.path = ls.path
            el.component = ls.component
            el.name = ls.name
            el.route = ls.route
            menu.push(el)
        }
    })
    console.log(menu)
    menu = [...menu, ...baseMenu]
    // 得到最后的匹配菜单
    return menu
}
export { menuInit }