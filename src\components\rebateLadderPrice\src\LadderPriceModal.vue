<template>
  <a-modal
    v-drag    
    v-model="madalVisible"
    centered
    :width="800"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderSetting`, '阶梯设置')"
    @ok="setLadderOk">
    <a-spin :spinning="confirmLoading">
      <div class="compare-page-container">
        <div class="page-content-right">
          <div style="margin-bottom: 12px">
            <vxe-grid
              border
              ref="headerGrid"
              row-id="ladderQuantity"
              show-overflow
              size="small"
              height="350"
              :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderInfo`, '阶梯信息')"
              :columns="tableHeaderColumn"
              :data="tableData"
              :edit-config="{trigger: 'click', mode: 'cell'}"
              :radio-config="{highlight: true, reserve: true}"
              :toolbar="{ slots: { buttons: 'toolbar_buttons' }}"
            >
              <template #toolbar_buttons>
                <div style="margin-top:-8px;text-align:right">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_ladderCount`, '阶梯数量') }}：
                  <a-input-number
                    v-model="ladderQuantity"
                    :placeholder="$srmI18n(`${$getLangAccount()}#i18n_alert_VWNfUEUWjyDWR_48d2aac1`, '请输入大于等于0的阶梯数量')"
                    style="width:200px"/>
                  <a-button
                    @click="addLadderQuantity"
                    type="primary"
                    style="margin-left:8px"
                    slot="tabBarExtraContent">  {{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '添加') }}
                  </a-button>
                  <a-button
                    @click="deleteLadder"
                    type=""
                    style="margin-left:8px"
                    slot="tabBarExtraContent">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}
                  </a-button>
                </div>
              </template>
              <template #role_computeMode="{ row }">
                <a-select 
                  v-model="row.type"
                  defaultValue="请选择"
                  :dropdownMatchSelectWidth="false"
                  @change="selectChanges(row)"
                  allow-clear
                >
                  <a-select-option 
                    v-for="item in sexList1" 
                    :key="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </template>
            </vxe-grid>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import {List} from 'ant-design-vue'
export default {
    name: 'SetLadderModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        isEmit: {
            type: Boolean,
            default: false
        },
        form: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            tableData: [],
            headObj: {},
            ladderQuantity: '',
            confirmLoading: false,
            listData: [],
            madalVisible: false,
            currentRow: null,
            tableHeaderData: [],
            sexList1: [
                { value: 'fix', label: '固定值' },
                { value: 'rate', label: '比例值%' },
                { value: 'union', label: '每单位返' }
            ],
            tableHeaderColumn: [
                { type: 'radio', width: 40, align: 'center'},
                { field: 'ladder', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级'), width: 170},
                { field: 'ladderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderCount`, '阶梯数量'), width: 100},
                { field: 'type', title: '计算方式', width: 150, slots: {default: 'role_computeMode', edit: 'role_computeMode' } },
                { field: 'fix', title: '固定值', width: 100, 
                    editRender: { name: '$input', 
                        props: {type: 'number', disabled: false, min: 0},
                        events: {change: (row, rowIndex) => this.priceChangeEvent1(row, rowIndex, 'fix')}
                    }
                },
                { field: 'rate', title: '比例值%', 
                    editRender: { name: '$input', 
                        props: {type: 'number', disabled: false, min: 0},
                        events: {change: (row, rowIndex) => this.priceChangeEvent1(row, rowIndex, 'rate')}
                    }
                },
                { field: 'union', title: '每单位返', 
                    editRender: { name: '$input', 
                        props: {type: 'number', disabled: false, min: 0},
                        events: {change: (row, rowIndex) => this.priceChangeEvent1(row, rowIndex, 'union')}
                    }
                }
            ]
        }
    },
    mounted () {
    },
    methods: {
        open (row, curFieldValue) {
            let that = this
            this.madalVisible = true
            this.currentRow = row
            let ladderJson = curFieldValue || row.ladderPriceJson || ''
            if(ladderJson){
                const list = JSON.parse(ladderJson)
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData(list)
                })
            }else{
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData([])
                })
            }
        },
        priceChangeEvent1 (rows, rowIndex, actKey) {
            if(!rows.row.type) return
            if(rows.row.type !==actKey){
                setTimeout(()=>{
                    rows.row[actKey] =''
                })
            }
        },
        selectChanges (row){
            if(!row.type) return
            let arr= ['fix', 'rate', 'union']
            arr.forEach(element => {
                if(element != row.type){
                    row[element] = ''
                }  
            })
        },
        goBack () {
            this.$emit('hide')
        },
        addLadderQuantity (){
            if(!this.ladderQuantity && this.ladderQuantity !==0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterladderCount`, '请输入阶梯数量！'))
                return
            }
            if(this.ladderQuantity < 0){
                this.$message.warning('梯数量必须大于等于0！')
                return
            }
            let grid = this.$refs.headerGrid
            let itemList = grid.getTableData().fullData
            const {rate, fix, union, type} = this.currentRow
            itemList = grid.getTableData().fullData
            if(itemList.length >= 1){
                let currentLastIndex = itemList.length-1
                let item = itemList[currentLastIndex]
                if(parseInt(item.ladderQuantity) >= parseInt(this.ladderQuantity) ){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderMostLastLadderCount`, '阶梯数量必须大于上一阶梯数量'))
                    return
                }
                item['ladder']=item.ladderQuantity+' <= x < '+this.ladderQuantity
                item['min'] = item.ladderQuantity
                item['max'] = this.ladderQuantity
            }
            let min = this.ladderQuantity
            let max =''
            let item2 = {'ladderQuantity': this.ladderQuantity, 'ladder': this.ladderQuantity+' <= x ',  rate, fix, union, min, max, type}
            grid.insertAt(item2, -1)
            this.ladderQuantity = ''
        },
        deleteLadder (){
            let currentItem = this.$refs.headerGrid.getRadioRecord()
            if(!currentItem){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDataDelete`, '请选择需要删除的数据！'))
                return
            }
            let itemList = this.$refs.headerGrid.getTableData().fullData
            if(currentItem.ladderQuantity != itemList[itemList.length - 1].ladderQuantity){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseDeleteFollowLadderLineFirst`, '请先删除后面的阶梯行！'))
            }else{
                this.$refs.headerGrid.removeRadioRow()
            }
        },
        setLadderOk (){
            let itemList = this.$refs.headerGrid.getTableData().fullData
            if(itemList.length == 0 ){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseAddLadderInfo`, '请添加阶梯信息！'))
                itemList = null
                return
            }
            // 获取选中的值
            this.madalVisible = false
            if (this.isEmit) {
                this.$emit('ok', itemList)
            } else {
                this.$parent.fieldSelectOk(itemList)
            }
        }
    }
}
</script>
<style lang="less" scoped>
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 1000px;
            background-color: #fff
        }
    }
</style>