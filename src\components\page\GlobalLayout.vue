<template>
  <a-layout
    class="layout"
    id="Layout"
    :class="[device]"
  >
    <template v-if="layoutMode === 'sidemenu'">
      <side-menu
        :mode="sidemenuMode"
        :menus="menus"
        @menuSelect="myMenuSelect"
        :theme="navTheme"
        :collapsed="collapsed"
        :collapsible="true"
        :searchShow="!collapsed"
      />
    </template>

    <a-layout
      :class="[layoutMode, `content-width-${contentWidth}`]"
      :style="{ paddingLeft: fixSiderbar && isDesktop() ? `${sidebarOpened ? 200 : 80}px` : '0' }"
    >
      <!-- layout header -->
      <global-header
        :mode="layoutMode"
        :menus="menus"
        :theme="navTheme"
        :collapsed="collapsed"
        :device="device"
        @toggle="toggle"
      />
      <!-- layout content -->
      <a-layout-content :style="{ height: '100%', paddingTop: fixedHeader ? '59px' : '0' }">
        <slot />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script>
import SideMenu from '@/components/menu/SideMenu'
import GlobalHeader from '@/components/page/GlobalHeader'
import { triggerWindowResizeEvent } from '@/utils/util'
import { mapState, mapActions } from 'vuex'
import { mixin, mixinDevice } from '@/utils/mixin.js'
import {nominalEdgePullWhiteBlack } from '@/utils/util.js'
import { Layout } from 'ant-design-vue'

export default {
    name: 'GlobalLayout',
    components: {
        SideMenu,
        GlobalHeader,
        ALayout: Layout,
        ALayoutContent: Layout.Content
    },
    mixins: [mixin, mixinDevice],
    data () {
        return {
            collapsed: false,
            activeMenu: {},
            menus: []
        }
    },
    computed: {
        ...mapState({
        // 主路由
            mainRouters: state => state.permission.addRouters,
            // 后台菜单
            permissionMenuList: state => state.user.permissionList,
            // 菜单状态
            sidemenuMode: state => state.app.sidemenuMode
        })
    },
    watch: {
        sidebarOpened (val) {
            this.collapsed = !val
        }
    },
    created () {
        this.menus = this.permissionMenuList
        //用于判断，在edge浏览器莫名加上了touch功能，导致出现下来时候有空白回弹不回去
        //wheel 兼容在edge浏览器，触控板在双指下滑的时候会出现touch的功能
        nominalEdgePullWhiteBlack()
    },
    methods: {
        ...mapActions(['setSidebar']),
        toggle () {
            this.collapsed = !this.collapsed
            this.setSidebar(!this.collapsed)
            triggerWindowResizeEvent()
        },
        menuSelect () {
            if (!this.isDesktop()) {
                this.collapsed = false
            }
        },

        myMenuSelect (value){
        //此处触发动态路由被点击事件
            this.findMenuBykey(this.menus, value.key)
            this.$emit('dynamicRouterShow', value.key, this.activeMenu.meta.title)
        },
        findMenuBykey (menus, key){
            for(let i of menus){
                if(i.path==key){
                    this.activeMenu = {...i}
                }else if(i.children && i.children.length>0){
                    this.findMenuBykey(i.children, key)
                }
            }
        }

    }
}

</script>

<style lang="scss">
 $aside-bg-color:rgb(33, 56, 89);
 $aside-font-color:rgb(179, 192, 231);
 $aside-select-a-color:#fff;
  .htmlHidden{
    overflow-y: hidden;
  }
  body {
      // 打开滚动条固定显示
      overflow-y: auto;

      &.colorWeak {
        filter: invert(80%);
      }
    }

    .layout {
      overflow-x: hidden;
      min-height: 100vh !important;
      .ant-menu-dark, .ant-menu-dark .ant-menu-sub{
        background:  $aside-bg-color;
        color:  $aside-font-color;
        //  color: @primary-color;
      }
      .ant-menu-dark .ant-menu-inline.ant-menu-sub{
        background-color: $aside-bg-color;
        box-shadow: none;
   
      }
      .ant-menu.ant-menu-dark .ant-menu-item-selected a, .ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected a{
      color:  $aside-select-a-color;
      }
      .ant-menu-item span{
       font-size:13px ;
      }
      .ant-menu-submenu  span{
        font-size: 13px;
      }
      .ant-menu-dark .ant-menu-item-selected .anticon + span{
        color:$aside-font-color
      }
      .ant-menu-dark .ant-menu-item, .ant-menu-dark .ant-menu-item-group-title, .ant-menu-dark .ant-menu-item > a{
           color: $aside-font-color;
      }
         .ant-layout-sider{
           background-color: $aside-bg-color;
         }
      &.mobile {

        .ant-layout-content {

          .content {
            margin: 24px 0 0;
          }
        }

        /**
         * ant-table-wrapper
         * 覆盖的表格手机模式样式，如果想修改在手机上表格最低宽度，可以在这里改动
         */
        .ant-table-wrapper {
          .ant-table-content {
            overflow-y: auto;
          }
          .ant-table-body {
            min-width: 800px;
          }
        }
        .sidemenu {
          .ant-header-fixedHeader {

            &.ant-header-side-opened, &.ant-header-side-closed {
              width: 100%
            }
          }
        }

        .topmenu {
          /* 必须为 topmenu  才能启用流式布局 */
          &.content-width-Fluid {
            .header-index-wide {
              margin-left: 0;
            }
          }
        }
        .header, .top-nav-header-index {
          .user-wrapper .action {
            padding: 0 12px;
          }
        }
      }

      &.ant-layout-has-sider {
        flex-direction: row;
        // .ant-menu.ant-menu-dark .ant-menu-item-selected{
        //   width: 85%;
        //   margin: 0 auto;
        //   border-radius: 8px;
        // }
      }

      .trigger {
        /* line-height: 45px; */
        padding: 11px 18px;
        cursor: pointer;
        font-size: 22px;
        transition: color 300ms, background 300ms;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }

      .topmenu {
        .ant-header-fixedHeader {
          position: fixed;
          right: 0;
          top: 0;
          z-index: 9;
          width: 100%;
          transition: width .2s;

          &.ant-header-side-opened {
            width: 100%;
          }

          &.ant-header-side-closed {
            width: 100%;
          }
        }
        /* 必须为 topmenu  才能启用流式布局 */
        &.content-width-Fluid {
          .header-index-wide {
            margin-left: 24px;
            max-width: unset;
          }

          .page-header-index-wide {
            max-width: unset;
          }
        }

      }

      .sidemenu {
        .ant-header-fixedHeader {
          position: fixed;
          right: 0;
          top: 0;
          z-index: 9;
          width: 100%;
          transition: width .2s;

          &.ant-header-side-opened {
            width: calc(100% - 200px)
          }

          &.ant-header-side-closed {
            width: calc(100% - 80px)
          }
        }
      }

      .header {
        position: relative;
        padding: 0 12px 0 0;
        height: 64px;
        background: #fff;
        box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
      }

      .header, .top-nav-header-index {

        .user-wrapper {
          // float: right;
          height: 100%;
          display: flex;
          align-items: center;

          .action {
            display: inline-block;
            padding: 0 14px;
            height: 100%;
            cursor: pointer;
            line-height: 46px;
            transition: all .3s;
            vertical-align: top;
            &.action-full {
              height: 100%;
            }

            &:hover {
              background: rgba(255, 255, 255, 0.3);
            }

            .avatar {
              margin: 0px 10px 3px 0;
              background: hsla(0, 0%, 100%, .85);
              vertical-align: middle;
              color: #1890ff;
            }

            .icon {
              padding: 4px;
              font-size: 16px;
            }
          }
        }

        &.dark {
          .user-wrapper {

            .action {
              color: black;

              &:hover {
                background: rgba(0, 0, 0, 0.05);
              }
            }
          }
        }
      }

      &.mobile {
        .top-nav-header-index {

          .header-index-wide {

            .header-index-left {

              .trigger {
                padding: 0 12px;
                color: rgba(255, 255, 255, 0.85);
              }

              .logo.top-nav-header {
                width: 56px;
                line-height: 58px;
                text-align: center;
              }
            }
          }

          .user-wrapper .action .avatar {
            margin: 20px 0;
          }

          &.light {

            .header-index-wide {

              .header-index-left {
                .trigger {
                  color: rgba(0, 0, 0, 0.65);
                }
              }
            }
            //
          }
        }
      }

      &.tablet {
        // overflow: hidden; text-overflow:ellipsis; white-space: nowrap;
        .top-nav-header-index {

          .header-index-wide {

            .header-index-left {
              .logo > a {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }

      }

      .top-nav-header-index {
        position: relative;
        box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
        transition: background .3s, width .2s;

        .header-index-wide {
          display: flex;
          margin: auto;
          padding: 0 20px 0 0;
          width: 100%;
          height: 48px;

          .ant-menu.ant-menu-horizontal {
            border: none;
            height: 64px;
            line-height: 64px;
          }

          .header-index-left {
            display: flex;
            flex: 1 1;

            .logo.top-nav-header {
              overflow: hidden;
              position: relative;
              width: 165px;
              height: 48px;
              transition: all .3s;

              img {
                display: inline-block;
                height: 32px;
                max-width: 82px;
                vertical-align: middle;
              }

              h1 {
                display: inline-block;
                margin: 0 0 0 12px;
                vertical-align: top;
                font-weight: 400;
                font-size: 16px;
                color: #fff;
              }
            }
          }

          .header-index-right {
            overflow: hidden;
            float: right;
            height: 45px;
            // display: flex;
            // align-items: center;
            .action:hover {
              background-color: rgba(0, 0, 0, 0.05);
            }
          }
        }

        &.light {
          background-color: #fff;

          .header-index-wide {
            .header-index-left {
              .logo {
                h1 {
                  color: #002140;
                }
              }
            }
          }
        }

        &.dark {

          .user-wrapper {

            .action {
              color: white;

              &:hover {
                background: rgba(255, 255, 255, 0.3);
              }
            }
          }
          .header-index-wide .header-index-left .trigger:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }

      }

      // 内容区
      .layout-content {
        margin: 24px 24px 0px;
        padding: 0 12px 0 0;
        height: 64px;
      }

    }

    .topmenu {
      .page-header-index-wide {
        margin: 0 auto;
        width: 100%;
      }
    }

    // drawer-sider 自定义
    .ant-drawer.drawer-sider {
      .sider {
        box-shadow: none;
      }

      &.dark {
        .ant-drawer-content {
          background-color: rgb(0, 21, 41);
        }
      }
      &.light {
        box-shadow: none;
        .ant-drawer-content {
          background-color: #fff;
        }
      }

      .ant-drawer-body {
        padding: 0
      }
    }

    // 菜单样式
    .sider {
      position: relative;
      z-index: 10;
      box-shadow: 2px 116px 6px 0 rgba(0, 21, 41, .35);

      &.ant-fixed-sidemenu {
        position: absolute;
        height: 100%;
      }

      .logo {
        overflow: hidden;
        position: relative;
        /* padding-left: 24px; */
        height: 64px;
        background: #002140;
        line-height: 64px;
        transition: all .3s;

        -webkit-transition: all .3s;

        img, h1 {
          display: inline-block;
          vertical-align: middle;
        }

        img {
          height: 32px;
        }

        h1 {
          margin: 0 0 0 8px;
          font-family: "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
          font-weight: 600;
          font-size: 18px;
          color: #fff;
        }
      }

      &.light {
        background-color: #fff;
        box-shadow: 2px 116px 8px 0 rgba(29, 35, 41, 0.05);

        .logo {
          background: #fff;
          box-shadow: 1px 1px 0 0 #e8e8e8;

          h1 {
            color: unset;
          }
        }

        .ant-menu-light {
          border-right-color: transparent;
        }
      }

    }

    // 外置的样式控制
    .user-dropdown-menu-wrapper.ant-dropdown-menu {
      padding: 4px 0;

      .ant-dropdown-menu-item {
        width: 160px;
      }

      .ant-dropdown-menu-item > .anticon:first-child,
      .ant-dropdown-menu-item > a > .anticon:first-child,
      .ant-dropdown-menu-submenu-title > .anticon:first-child
      .ant-dropdown-menu-submenu-title > a > .anticon:first-child {
        margin-right: 8px;
        min-width: 12px;
      }

    }

    // 数据列表 样式
    .table-alert {
      margin-bottom: 16px;
    }

    .table-page-search-wrapper {

      .ant-form-inline {

        .ant-form-item {
          display: flex;
          margin-right: 0;
          margin-bottom: 16px;

          .ant-form-item-control-wrapper {
            display: inline-block;
            flex: 1 1;
            vertical-align: middle;
          }

          > .ant-form-item-label {
            // padding-right: 8px;
            // width: auto;
            overflow: hidden;
            line-height: 32px;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .ant-form-item-control {
            height: 32px;
            line-height: 32px;
          }
        }
      }

      .table-page-search-submitButtons {
        display: block;
        margin-bottom: 24px;
        white-space: nowrap;
      }

    }

    .content {

      .table-operator {
        margin-bottom: 18px;

        button {
          margin-right: 8px;
        }
      }
    }
    .ant-layout-has-sider .dark .action.top-icon > i{ // 覆盖bug
      color:#000;
    }
</style>
