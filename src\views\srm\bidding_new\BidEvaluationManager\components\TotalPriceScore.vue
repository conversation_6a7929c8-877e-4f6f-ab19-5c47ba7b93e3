<template>
  <!-- 价格评分 -->
  <div class="ReviewItems">
    <a-spin :spinning="confirmLoading">
      <div class="review-items-top">
        <a-row>
          <a-col :span="12">
            <span>{{ currentRow.title }}</span>
          </a-col>
          <a-col
            :span="12"
            style="text-align: right;">
            <a-button
              type="primary"
              v-if="currentRow['editStatus'] && priceRegulationInfo?.calType == 0"
              @click="calculation">{{ $srmI18n(`${$getLangAccount()}#i18n_field_td_116416`, '计算') }}</a-button>
            <a-button
              type="primary"
              @click="submitData"
              v-if="currentRow['editStatus']"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
            <a-button
              @click="goBack"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </a-col>
          <a-col :span="24">
            <template v-if="priceRegulationInfo.calType == 0 && priceRegulationInfo.pricePointsCalFormula =='operationMinPriceStrategy' ">
              <span >{{ priceRegulationInfo.pricePointsCalFormula_dictText }}</span>
            </template>
            <template v-else-if="priceRegulationInfo.calType == 0 && priceRegulationInfo.pricePointsCalFormula =='operationMinPricePositiveDeviationStrategy' ">
              <span>
                {{ priceRegulationInfo.pricePointsCalFormula_dictText }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_WsuBxUtruWWV_2ddce27d`, '：报价每高于基准价1%扣') }}{{ priceRegulationInfo.aboveScore }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_zWenVR_31baf384`, '分，最低扣至') }}{{ priceRegulationInfo.lowestScore }}分
              </span>
            </template>
            <template v-else-if="priceRegulationInfo.calType == 0 && priceRegulationInfo.pricePointsCalFormula =='operationStandardPriceStrategy' ">
              <span>
                {{ priceRegulationInfo.pricePointsCalFormula_dictText }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_WsuBxUtruWWV_2ddce27d`, '：报价每高于基准价1%扣') }}{{ priceRegulationInfo.aboveScore }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_zWBnUtruWWV_3d25724d`, '分，每低于基准价1%扣') }}{{ priceRegulationInfo.belowScore }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_zWenVR_31baf384`, '分，最低扣至') }}{{ priceRegulationInfo.lowestScore }}分
              </span>
            </template>
            <template v-else>
              <div>
                {{ priceRegulationInfo.calArtificialRulesDesc }}
              </div>
            </template>
            <span v-if="priceRegulationInfo.calType == 0">{{ $srmI18n(`${$getLangAccount()}#i18n_field_truMRLFdjjXeBsuj_67fc24a4`, '基准价取值规则：所有有效投标报价的') }}{{ priceRegulationInfo.baseCalRules_dictText }}为基准价</span>
          </a-col>
        </a-row>
      </div>
      <div class="review-items-grid">
        <div class="grid-ul">
          <ul>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位') }}</li>
            <template v-for="review in reviewList">
              <li :key="review.id">{{ review.name }}</li>
            </template>
          </ul>
          <ul
            class="ul"
            v-for="supplier in resultData.supplierList"
            :key="supplier.supplierAccount">
            <li v-if="supplierInvalidMap[supplier.supplierAccount] == '1'">{{ supplier.supplierName }}({{ $srmI18n(`${$getLangAccount()}#i18n_alert_IuB_16c86ba`, '已废标') }})</li>
            <li v-else>{{ supplier.supplierName }}</li>
            <template v-if="resultData.evaResultListMap">
              <template v-if="resultData.evaResultListMap[supplier.supplierAccount] && resultData.evaResultListMap[supplier.supplierAccount][0].invalid !== '1'">
                <template v-for="review in resultData.evaResultListMap[supplier.supplierAccount]">
                  <li
                    v-for="(field, i) in reviewList"
                    :key="review.regulationId + field.id + i">
                    <a-input
                      :addon-after="$srmI18n(`${$getLangAccount()}#i18n_field_j_5143`, '元')"
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNumciR_e72db299`, '请输入价格修正值')"
                      type="number"
                      :disabled="!currentRow['editStatus'] || review.invalid == '1'"
                      style="width: 50%;margin-top: 2px;"
                      v-if="field.code == 'priceCorrection'"
                      @change="(e) => handleChangePriceCorrection(e, review)"
                      v-model="review.priceCorrection" />
                    <a-input
                      :addon-after="$srmI18n(`${$getLangAccount()}#i18n_title_branch`, '分')"
                      type="number"
                      :disabled="!currentRow['editStatus'] || review.invalid == '1'"
                      style="width: 50%;margin-top: 2px;"
                      :min="0"
                      :max="maxScore"
                      @change="(e) => handleGetWeightScore(e, review)"
                      v-else-if="field.code == 'score' && priceRegulationInfo.calType == '1'"
                      v-model="review.score" />
                    <span v-else>{{ review[field.code] }}<span v-if="field.code == 'weight'">%</span></span>
                  </li>
                </template>
              </template>
              <template v-else>
                <li
                  v-for="(review, i) in reviewList"
                  :key="i"><span>/</span></li>
              </template>
            </template>
          </ul>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import { getAction, postAction } from '@/api/manage'
import { gridOptionsMixin } from '../public/gridOptionsMixin.js'
import {  mul, div, add } from '@/utils/mathFloat.js'

export default {
    mixins: [gridOptionsMixin],
    props: {
        currentRow: {
            type: Object,
            default () {
                return {}
            }
        },
        resultDataProp: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        headParams () {
            let {checkType, currentStep, processType } = this.currentRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return {xNodeId}
        },
        reviewList () {
            let list = [
                {id: 1, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBsu_2e62dc84`, '投标报价'), code: 'quote'},
                {id: 2, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umci_25769c1a`, '价格修正'), code: 'priceCorrection'},
                {id: 3, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UBu_2199294`, '评标价'), code: 'evaPrice'},
                {id: 4, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tru_154776b`, '基准价'), code: 'basePrice'},
                {id: 5, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_getScore`, '得分'), code: 'score'},
                {id: 6, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bs_d12ea`, '权重'), code: 'weight'},
                {id: 7, name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Ktjz_2be595f6`, '实际得分'), code: 'weightScore'}
            ]
            if (this.priceRegulationInfo.calType == '1') {
                list = list.filter(item => item.code !== 'basePrice')
            }
            return list
        }
    },
    data () {
        return {
            calculateStatus: false,
            confirmLoading: false,
            resultData: {},
            priceRegulationInfo: {},
            supplierInvalidMap: {},
            maxScore: '9999'
        }
    },
    created () {
        this.resultData = Object.assign({}, this.resultDataProp)
        this.setSupplierInvalidMap()
        this.maxScore = this.resultData.evaluationGroupVO.score
        this.priceRegulationInfo = this.resultData.evaluationGroupVO.tenderEvaluationTemplatePriceRegulationInfo
    },
    methods: {
        setSupplierInvalidMap (item = null) {
            this.resultData.supplierList.map(supplier => {
                if (item) {
                    if (supplier.supplierAccount == item.supplierAccount) {
                        this.$set(this.supplierInvalidMap, item.supplierAccount, item.invalid)
                    }
                } else {
                    this.$set(this.supplierInvalidMap, supplier.supplierAccount, supplier.invalid)
                }
            })
        },
        // 计算
        calculation (needMessage = true) {
            const {evaResultListMap={}} = this.resultData
            const keys = evaResultListMap && Object.keys(evaResultListMap)
            const regulationCalList = keys.map(item => {
                return evaResultListMap[item][0]
            })
            let params = regulationCalList
            this.confirmLoading = true
            return postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/priceScoreCalculation', params, {headers: this.headParams}).then(res => {
                this.confirmLoading = false
                if (res.code == 200) {
                    const {result = []} = res
                    result.forEach(item => {
                        if (item.invalid == '1') {
                            item.priceCorrection = ''
                        }
                        this.resultData.evaResultListMap[item.supplierAccount] = [item]
                        this.setSupplierInvalidMap(item)
                    })
                    // this.resultData.supplierList = result || []
                    this.calculateStatus = true
                    needMessage && this.$message.success(res.message)
                } else {
                    this.$message.error(res.message)
                }
                return res
            })
        },
        // 提交数据
        async submitData () {
            // 系统计算且是否已经计算完成
            if (this.priceRegulationInfo?.calType == 0){
                if (!this.calculateStatus) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWHctdW_eaf6fea9`, '请先进行计算！'))
                    return false
                }
                let {success} = await this.calculation(false)
                if (!success) return false
            }
            // 人工计算
            if (this.priceRegulationInfo?.calType == 1) {
                for(let item of Object.values(this.resultData.evaResultListMap)) {
                    if (item[0].score == 0) {
                        return this.$message.warning(`${item[0].supplierName}未填写得分`)
                    }
                }
            }
            this.resultData.evaResultListMap && (() => {
                const evaObj = this.resultData.evaResultListMap
                Object.keys(evaObj).forEach(key => {
                    evaObj[key].forEach(item => {
                        item.judgesTaskItemId = this.currentRow.id
                        item.judgesTaskHeadId = this.currentRow.judgesTaskHeadId
                    })
                })
            })()
            this.resultData.supplierList && this.resultData.supplierList.forEach(data => {
                data['judgesTaskItemId'] = this.currentRow.id || ''
            })
            this.confirmLoading = true
            postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/publishSupplierEvaGroupResult', this.resultData, {headers: this.headParams}).then(res => {
                this.confirmLoading = false
                if (res.code == 200) {
                    this.$message.success(res.message)
                    this.goBack()
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 返回
        goBack () {
            this.$emit('goBack')
        },
        // 价格修正
        handleChangePriceCorrection (e, row) {
            if (this.priceRegulationInfo.calType == '0') return
            let total = add(row.quote, e.target.value)
            this.$set(row, 'evaPrice', total)
        },
        // 最终得分计算
        handleGetWeightScore (e, row) {
            let value = e.target.value
            if (value < 0) {
                this.$set(row, 'score', 0)
                return
            }
            if (value > this.maxScore) {
                this.$set(row, 'score', this.maxScore)
                return
            }
            if (value > this.priceRegulationInfo.score) value = this.priceRegulationInfo.score
            let totalWeight = div(mul(value, row.weight), 100)
            this.$set(row, 'weightScore', totalWeight)
        }
    }
}
</script>

<style lang="less" scoped>
  .ReviewItems {
    height: 100%;
    background-color: #fff;
    padding: 15px 10px;
  }
  .review-items-top {
    line-height: 30px;

    .ant-col-12:nth-child(1) {
      font-size: 16px;
      font-weight: 600;
    }
    .ant-col-24 {
      background-color: #E6F7FF;
      padding: 5px 10px;
      margin-top: 5px;
      border-radius: 5px;
      color: #02A7F0;

      span {
        display: block;
        font-size: 12px;
        height: 20px;
        line-height: 20px;
      }
    }
  }
  .review-items-grid {
    margin-top: 15px;

    .grid-ul {
      display: flex;

      ul:nth-child(1) {
        width: 90px;
      }
      ul {
        text-align: center;

        li:nth-child(1) {
          background-color: #e4e4e4;
        }
        li {
          border: 1px solid #f0eeee;
          height: 40px;
          line-height: 40px;
        }
      }
      .ul {
        flex-grow: 1;
      }

    }
  }
</style>
