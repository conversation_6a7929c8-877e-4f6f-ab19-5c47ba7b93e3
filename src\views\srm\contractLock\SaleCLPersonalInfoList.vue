<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showAddPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleManagePersonalInfoEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleManagePersonalInfoDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <SaleManagePersonalInfoAdd
      v-if="showAddPage"
      ref="addPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleManagePersonalInfoAdd from './modules/SaleManagePersonalInfoAdd'
import SaleManagePersonalInfoDetail from './modules/SaleManagePersonalInfoDetail'
import SaleManagePersonalInfoEdit from './modules/SaleManagePersonalInfoEdit'
import {getAction, postAction} from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        SaleManagePersonalInfoAdd, SaleManagePersonalInfoDetail, SaleManagePersonalInfoEdit
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            pageData: {
                businessType: 'contract-lock',
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVNcR_b51847bb`, '申请者姓名')
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'contractLock#salePersonalInfo:detail'}
                ]
            },
            url: {
                add: '/contractLock/saleClPersonalInfo/add',
                list: '/contractLock/saleClPersonalInfo/saleList',
                delete: '/contractLock/saleClPersonalInfo/delete',
                columns: 'SaleClPersonalInfo',
                auth: '/contractLock/saleClPersonalInfo/submitCertification',
                refresh: '/contractLock/saleClPersonalInfo/getCertificationInfo'
            }
        }
    },
    methods: {
        //已认证不能被编辑
        allowEdit (row){
            if(row.realName==='1'){
                return true
            }
            return false
        },
        allowCertification (row){
            if(row.realName=='1'){
                return true
            }
            return false
        },
        allowRefreshStatus (row) {
            if(row.realName == '1'){
                return true
            }
            return false
        },
        //已经创建e签宝的账号的不能删除
        allowDelete (row){
            if(row.realName==='1'){
                return true
            }
            return false
        },
        handleAdd () {
            this.showAddPage = true
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showAddPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showEditPage = true
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        handleCertification (row) {
            let longLink = row.longLink
            let contentText = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLDJLiW_5f843f01`, '是否确认提交认证?')
            if (longLink && longLink.length>0) {
                contentText = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_rVHIDJRLiWKmDJMAbujWKQtTDJW_96191780`, '该信息已提交过认证，再次提交会产生费用，是否继续提交？')
            }
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationSystems`, '确认认证'),
                content: contentText,
                onOk: function () {
                    postAction(that.url.auth, row).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            window.open(res.result.certificationPageUrl)
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        handleRefreshStatus (row) {
            getAction(this.url.refresh, {id: row.id}).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                }else {
                    this.$message.warning(res.message)
                }
                // 刷新列表
                this.$refs.listPage.loadData()
            })
        }
    }
}
</script>