<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <notice-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="modalForm"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import NoticeModal from './modules/NoticeModal'
import {listPageMixin} from '@comp/template/listPageMixin'
import { httpAction, getAction } from '@/api/manage'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    mixins: [listPageMixin],
    components: {
        NoticeModal
    },
    data () {
        return {
            showEditPage: false,
            rows: [],
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: srmI18n(`${getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    }
                ],
                button: [
                    {label: srmI18n(`${getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary'},
                    {label: srmI18n(`${getLangAccount()}#i18n_title_delete`, '删除'), icon: 'delete', clickFn: this.delete},
                    {label: srmI18n(`${getLangAccount()}#i18n_title_void`, '作废'), icon: 'plus', clickFn: this.cancel, type: 'primary'},
                    {label: srmI18n(`${getLangAccount()}#i18n_title_release`, '发布'), icon: 'plus', clickFn: this.sendNotice, type: 'primary'},
                    {label: srmI18n(`${getLangAccount()}#i18n_title_topping`, '置顶'), icon: 'plus', clickFn: this.top, type: 'primary'},
                    {label: srmI18n(`${getLangAccount()}#i18n_title_cancelTop`, '取消置顶'), icon: 'plus', clickFn: this.cancelTop, type: 'primary'},
                    {label: srmI18n(`${getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: true, clickFn: this.settingColumns}
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/notice/notice/list',
                delete: '/notice/notice/delete',
                deleteBatch: '/notice/notice/deleteBatch',
                exportXlsUrl: '/notice/notice/exportXls',
                importExcelUrl: '/notice/notice/importExcel',
                columns: 'noticeList'
            },
            requestUrl: {
                list: '',
                batchCancel: '/notice/notice/batchCancel',
                getInfo: '/notice/notice/findById',
                findByStick: '/notice/notice/findByStick',
                batchPublish: '/notice/notice/batchPublish',
                publish: '/notice/notice/publish'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls('平台公告配置')
        },
        handleEditInner (row){
            getAction(this.requestUrl.getInfo, {id: row.id}).then(res => {
                console.log(res)
                if (res.code === 200){
                    let dataInfo = res.result
                    sessionStorage.setItem('noticeInfo', JSON.stringify(dataInfo))
                    this.showEditPage = true
                }
            })
        },
        top () {
            let rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            let ids = []
            for (let i = 0; i < rows.length; i++) {
                let row  = rows[i]
                if(row.status == null ||row.status == 0 ){
                    this.$message.warning(srmI18n(`${getLangAccount()}#i18n_title_topingFail`, '未发布单据，置顶失败!'))
                    return
                }
                ids.push(row.id)
            }
            let param = {}
            param['stick'] = 1
            param['ids'] = ids
            httpAction(this.requestUrl.findByStick, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$refs.listPage.searchQuery()
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        cancelTop (){
            let rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            let ids = []
            for (let i = 0; i < rows.length; i++) {
                ids.push(rows[i].id)
            }
            let param = {}
            param['stick'] = 0
            param['ids'] = ids
            console.log(param)
            httpAction(this.requestUrl.findByStick, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$refs.listPage.searchQuery()
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        cancel (){
            let rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            let ids = []
            for (let i = 0; i < rows.length; i++) {
                ids.push(rows[i].id)
            }
            httpAction(this.requestUrl.batchCancel, {'ids': ids}, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$refs.listPage.searchQuery()
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        sendNotice (){
            let rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            let ids = []
            for (let i = 0; i < rows.length; i++) {
                ids.push(rows[i].id)
            }
            httpAction(this.requestUrl.batchPublish, {'ids': ids}, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$refs.listPage.searchQuery()
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        delete (){
            console.log('进入delete')
        }
    }
}
</script>