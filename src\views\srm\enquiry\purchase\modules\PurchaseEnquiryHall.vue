<!--
 * @Author: LokNum
 * @Date: 2022-05-12 17:44:40
 * @LastEditors: wangxin <EMAIL>
 * @LastEditTime: 2022-11-23 23:03:54
 * @Description: 询价大厅组件
-->
<template>
  <div class="PurchaseEnquiryHall">
    <a-modal
      v-drag
      centered
      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板') }`"
      :width="360"
      v-model="visible"
      @ok="selectedTemplate">
      <template slot="footer">
        <a-button
          key="back"
          @click="handleCancel">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedTemplate">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplateType`, '请选择模板')"
      />
    </a-modal>
    <a-spin :spinning="spinning">
      <div class="container">
        <div class="top">
          <div class="breadcrumb" />
          <div class="menu">
            <a-dropdown v-if="showButton">
              <a-button>{{ $srmI18n(`${$getLangAccount()}#i18n_btn_IIlB_25b1692d`, '一键授标') }}<a-icon type="down" /></a-button>
              <a-menu slot="overlay">
                <a-menu-item key="1">
                  <div @click="oneAward('minNetPrice')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_enLftu_516dd6b4`, '最低未税单价') }} </div>
                </a-menu-item>
                <a-menu-item key="2">
                  <div @click="oneAward('minPrice')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_enxftu_48cb3e13`, '最低含税单价') }} </div>
                </a-menu-item>
                <a-menu-item key="3">
                  <div @click="oneAward('minPackagePrice')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_enfsu_d0fd2317`, '最低打包价') }} </div>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
            <a-dropdown v-if="showButton">
              <a-button
                v-if="showRequote">{{ $srmI18n(`${$getLangAccount()}#i18n_title_restatement1`, '批量操作') }}<a-icon type="down" /></a-button>
              <a-menu slot="overlay">
                <a-menu-item key="1">
                  <div @click="reQuote('material')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_iSLss_cb3a023c`, '整物料重报') }} </div>
                </a-menu-item>
                <a-menu-item key="2">
                  <div @click="reQuote('supplier')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_iRdXss_ad823c51`, '整供应商重报') }} </div>
                </a-menu-item>
                <a-menu-item key="3">
                  <div @click="reQuote('all')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_itss_2f691299`, '整单重报') }} </div>
                </a-menu-item>
                <a-menu-item key="4">
                  <div @click="patchOperate('allAccept')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_itss_allAccept`, '全部物料接受') }} </div>
                </a-menu-item>
                <a-menu-item key="5">
                  <div @click="patchOperate('allReject')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_itss_allReject`, '全部物料拒绝') }} </div>
                </a-menu-item>
                <a-menu-item key="6">
                  <div @click="patchOperate('partReject')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_itss_partReject`, '余行全部拒绝') }} </div>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
            <a-button
              type="primary"
              @click="setPricingNotice">{{ $srmI18n(`${$getLangAccount()}#i18n_field_pricingNotice`, '定价通知') }}</a-button>
            <a-button
              v-if="showPriceRecord"
              type="primary"
              @click="generatePriceRecord"
              :disabled="disabledButton">{{ $srmI18n(`${$getLangAccount()}#i18n_title_creatPriceRecord`, '生成价格记录') }}</a-button>
            <a-dropdown v-if="showButton">
              <a-button
                type="primary">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_DJIu_2e91e671`, '提交定价') }}<a-icon type="down" /></a-button>
              <a-menu slot="overlay">
                <a-menu-item key="1">
                  <div @click="submitPrice('whole')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_itDJ_2f636cf5`, '整单提交') }} </div>
                </a-menu-item>
                <a-menu-item key="2">
                  <div @click="submitPrice('row')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_pcDJ_2f10de77`, '按行提交') }} </div>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
            <a-button
              type="primary"
              @click="submitEvaluationPrice()">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_DJnu_2e936d93`, '提交核价') }}</a-button>
            <a-dropdown v-if="showButton">
              <a-button
                v-if="showRegretBtn"
                type="danger">{{ $srmI18n(`${$getLangAccount()}#i18n_title_regretBid`, '悔标') }}<a-icon type="down" /></a-button>
              <a-menu slot="overlay">
                <a-menu-item key="1">
                  <div @click="showRegret('whole')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_itMB_2f632214`, '整单悔标') }} </div>
                </a-menu-item>
                <a-menu-item key="2">
                  <div @click="showRegret('material')"> {{ $srmI18n(`${$getLangAccount()}#i18n_btn_pSLMB_a9213ccc`, '按物料悔标') }} </div>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
            <a-button
              type="primary"
              @click="refresh">{{ $srmI18n(`${$getLangAccount()}#i18n_title_refresh`, '刷新') }}</a-button>
            <!-- <a-button
              type="default"
              @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button> -->
          </div>
        </div>
        <div class="content">
          <div class="gutter">
            <div class="item price">
              <div class="info">
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_title_inquirySheetNo`, '询价单号') }}</b>
                  <b class="value">{{ form.enquiryNumber }}</b>
                </span>
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_field_enquiryStatus`, '询价单状态') }}</b>
                  <b class="value">{{ form.enquiryStatus_dictText }}</b>
                </span>
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_title_deadline`, '截止时间') }}</b>
                  <b class="value red">{{ form.quoteEndTime }}</b>
                </span>
              </div>
            </div>
          </div>
          <div class="gutter">
            <multipane
              class="custom-resizer"
              layout="vertical">
              <div class="item material">
                <div>
                  <a-button
                    v-if="showReplenishMaterial"
                    @click="replenishMaterialNumber">{{ $srmI18n(`${$getLangAccount()}#i18n_title_supplementaryMaterialCode`, '补充物料编码') }}</a-button>
                </div>
                <div class="material-table">
                  <vxe-grid
                    ref="materialGrid"
                    border
                    stripe
                    size="small"
                    :height="vxTableHeight"
                    resizable
                    auto-resize
                    show-overflow
                    highlight-hover-row
                    :columns="materialGridColumn"
                    :row-class-name="handleRowClass"
                    @cell-click="cellClickEvent"
                  >
                  </vxe-grid>
                </div>
              </div>
              <multipane-resizer></multipane-resizer>
              <div class="item compare">
                <div>
                  <a-button
                    type="primary"
                    @click="reportModalShow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表') }}</a-button>
                  <a-button
                    v-if="$hasOptAuth('enquiry#purchaseEnquirySum:exportExcel')"
                    type="primary"
                    @click="exportExcel">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_MksB_32ae1237`, '汇总报表') }}</a-button>

                  <a-button
                    @click="currentQuoteShow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_trendChartOfQuotation`, '本次报价趋势图') }}</a-button>
                  <a-button
                    @click="hisQuoteShow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_historicalPriceTrendChart`, '历史价格趋势图') }}</a-button>
                </div>
                <a-tabs
                  v-model="tabPaneKey"
                  @change="handleChange"
                  :style="{'height':vxTableHeightList+60+'px'}"
                >
                  <a-tab-pane
                    forceRender
                    :disabled="compareTabFlag"
                    key="compareNormal"
                    :tab="$srmI18n(`${$getLangAccount()}#i18n_title_conventionalPriceComparison`, '常规比价')">
                    <div class="table">
                      <vxe-grid
                        ref="compareGrid"
                        border
                        stripe
                        size="small"
                        :height="vxTableHeightList"
                        resizable
                        auto-resize
                        show-overflow
                        highlight-hover-row
                        :sort-config="gridCommonSort"
                        :edit-config="gridCustomEditConfig"
                        :edit-rules="girdEditRulesConfig"
                        :columns="compareGridColumn">
                        <template #grid_opration="{ row, column }">
                          <a
                            v-for="(item, i) in optColumnList"
                            :key="'opt_' + i"
                            :title="item.title"
                            style="margin:0 4px"
                            :disabled="item.disabled ? item.disabled(row, false) : false"
                            v-show="item.showCondition ? item.showCondition(row) : true"
                            @click="item.clickFn(row, column, false)">{{ item.title }}</a>
                        </template>
                        <template #grid_supplierName="{ row }"> <a-button
                          type="link"
                          @click="showContactList(row)">{{ row.supplierName }}</a-button>
                        </template>
                      </vxe-grid>
                    </div>
                  </a-tab-pane>
                  <a-tab-pane
                    forceRender
                    :disabled="ladderTabFlag"
                    key="compareLadder"
                    :tab="$srmI18n(`${$getLangAccount()}#i18n_field_yDlu_45de8c9c`, '阶梯比价')">
                    <div class="table">
                      <vxe-grid
                        ref="compareLadderGrid"
                        border
                        stripe
                        size="small"
                        :height="vxTableHeightList"
                        resizable
                        auto-resize
                        show-overflow
                        highlight-hover-row
                        :sort-config="gridSort"
                        :edit-config="gridCustomEditConfig"
                        :edit-rules="girdEditRulesConfig"
                        :columns="compareLadderGridColumn">
                        <template #grid_opration="{ row, column }">
                          <a
                            v-for="(item, i) in optColumnList"
                            :key="'opt_' + i"
                            :title="item.title"
                            style="margin:0 4px"
                            :disabled="item.disabled ? item.disabled(row, false) : false"
                            v-show="item.showCondition ? item.showCondition(row) : true"
                            @click="item.clickFn(row, column, false)">{{ item.title }}</a>
                        </template>
                        <template #grid_supplierName="{ row }"> <a-button
                          type="link"
                          @click="showContactList(row)">{{ row.supplierName }}</a-button>
                        </template>
                      </vxe-grid>
                    </div>
                  </a-tab-pane>
                  <a-tab-pane
                    forceRender
                    :disabled="costTabFlag"
                    key="compareCost"
                    :tab="$srmI18n(`${$getLangAccount()}#i18n_title_costPriceComparison`, '成本比价')">
                    <div class="table">
                      <vxe-grid
                        ref="compareCostGrid"
                        border
                        stripe
                        size="small"
                        :height="vxTableHeightList"
                        resizable
                        auto-resize
                        show-overflow
                        highlight-hover-row
                        :sort-config="gridSort"
                        :edit-config="gridCustomEditConfig"
                        :edit-rules="girdEditRulesConfig"
                        :columns="compareCostGridColumn">
                        <template #grid_opration="{ row, column }">
                          <a
                            v-for="(item, i) in optColumnList"
                            :key="'opt_' + i"
                            :title="item.title"
                            style="margin:0 4px"
                            :disabled="item.disabled ? item.disabled(row, false) : false"
                            v-show="item.showCondition ? item.showCondition(row) : true"
                            @click="item.clickFn(row, column, false)">{{ item.title }}</a>
                        </template>
                        <template #grid_supplierName="{ row }"> <a-button
                          type="link"
                          @click="showContactList(row)">{{ row.supplierName }}</a-button>
                        </template>
                      </vxe-grid>
                    </div>
                  </a-tab-pane>
                  <a-tab-pane
                    forceRender
                    key="comparePackage"
                    :tab="$srmI18n(`${$getLangAccount()}#i18n_title_packagePriceComparison`, '打包比价')">
                    <div class="table">
                      <vxe-grid
                        ref="comparePackagetGrid"
                        border
                        stripe
                        size="small"
                        :height="vxTableHeightList"
                        resizable
                        auto-resize
                        show-overflow
                        highlight-hover-row
                        :sort-config="gridSort"
                        :edit-config="gridCustomEditConfig"
                        :edit-rules="girdEditRulesConfig"
                        :columns="comparePackageGridColumn">
                        <template #grid_opration="{ row, column }">
                          <a
                            v-for="(item, i) in optColumnList"
                            :key="'opt_' + i"
                            :title="item.title"
                            style="margin:0 4px"
                            :disabled="item.disabled ? item.disabled(row, true) : false"
                            v-show="item.showCondition ? item.showCondition(row) : true"
                            @click="item.clickFn(row, column, true)">{{ item.title }}</a>
                        </template>
                        <template #grid_supplierName="{ row }"> <a-button
                          type="link"
                          @click="showContactList(row)">{{ row.supplierName }}</a-button>
                        </template>
                      </vxe-grid>
                    </div>
                  </a-tab-pane>
                </a-tabs>
              </div>
            </multipane>
          </div>
        </div>
      </div>
    </a-spin>
    <!-- 定价通知 -->
    <pricing-notice ref="pricingNotice" />
    <!-- 重报价 -->
    <re-quote ref="reQuote" />
    <!-- 悔标 -->
    <regret ref="regret"/>

    <submit-priced ref="submitPriced"/>
    <field-select-modal ref="fieldSelectModal" />
    <!-- 本次报价趋势图 -->
    <this-quote-chart ref="currentQuoteChart" />
    <!-- 历史价格趋势图 -->
    <his-quote-chart ref="hisQuoteChart" />
    <contactList ref="contactList" />
    <!-- 比价报表弹窗 -->
    <report-modal
      ref="reportModal"
      @exportCompare="exportCompare" />
  </div>
</template>

<script lang='jsx'>
import { getAction, postAction, downFile } from '@/api/manage'
import PricingNotice from '../../component/PricingNotice'
import ReQuote from '../../component/ReQuote'
import Regret from '../../component/Regret'
import SubmitPriced from '../../component/SubmitPriced'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import ThisQuoteChart from '../../component/ThisQuoteChart'
import HisQuoteChart from '../../component/HisQuoteChart'
import contactList from '../../component/contactList'
import reportModal from '../../component/reportModal'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import { currency } from '@/filters'
import {nominalEdgePullWhiteBlack } from '@/utils/util.js'
import { Multipane, MultipaneResizer } from 'vue-multipane'

export default {
    name: 'ComparePrice',
    components: {
        PricingNotice,
        ReQuote,
        Regret,
        SubmitPriced,
        fieldSelectModal,
        ThisQuoteChart,
        HisQuoteChart,
        contactList,
        reportModal,
        Multipane, MultipaneResizer
    },
    inject: [
        // 'closeCurrent'
    ],
    data () {
        return {
            submitLoading: false,
            templateOpts: [],
            templateNumber: undefined,
            visible: false,
            quotaWayDictText: '',
            spinning: false,
            compareTabFlag: true,
            ladderTabFlag: true,
            costTabFlag: true,
            tabPaneKey: 'compareNormal', // comparePackage
            sourceId: '',
            itemNumber: '',
            purchaseEnquiryItemList: [],
            form: {},
            url: { detail: '/enquiry/purchaseEnquiryHead/queryById' },
            materialGridColumn: [
                { type: 'checkbox', width: 36, fixed: 'left' },
                { field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 200  },
                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150 },
                { field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 250 },
                { field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 250 },
                // { field: 'factory_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'), width: 250 },
                // { field: 'storageLocation_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageLocation`, '库存地点'), width: 250 },
                { field: 'quantityUnit_dictText', title: '主单位', width: 75 },
                { field: 'purchaseUnit_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 250 },
                { field: 'requireQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireQuantity`, '需求数量'), width: 120 },
                { field: 'requireDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireDate`, '需求日期'), width: 250 }
            ],
            gridCommonSort: { trigger: 'cell', defaultSort: { field: 'supplierName', order: 'asc' }, orders: ['asc', 'desc', 'null'] },
            packageOptFrist: '',
            optColumnList: [
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accept`, '接受'), disabled: (row, isPack) => {
                    // isPack 是否打包操作 packageOptFrist 已打包操作 1
                    let _this = this
                    // （非打包比价列表 且 当前已打包操作） 或（打包比价列表 且 当前已非打包操作）
                    if ((!isPack && _this.packageOptFrist === '1') || (isPack && _this.packageOptFrist === '0')) return true
                    // 非(已报价 || 重报价 || (已悔标 && 悔标类型重比价))
                    return !(row.itemStatus === '2' || row.itemStatus === '8' || (row.itemStatus === '11' && row.regretFlag === '2'))
                },
                clickFn: this.accept,
                authorityCode: 'enquiry#purchaseEnquiryHead:accept',
                showCondition: () => { return this.$hasOptAuth('enquiry#purchaseEnquiryHead:accept') } },
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'), disabled: (row, isPack) => {
                    // isPack 是否打包操作 packageOptFrist 已打包操作 1
                    let _this = this
                    if ((!isPack && _this.packageOptFrist === '1') || (isPack && _this.packageOptFrist === '0')) return true
                    //非(已报价 || 重报价 || 不能报价 || 未报价 || (已悔标 && 悔标类型重比价))
                    return !(row.itemStatus === '2' || row.itemStatus === '8' || row.itemStatus === '6' || row.itemStatus === '3' || (row.itemStatus === '11' && row.regretFlag === '2'))
                }, clickFn: this.reject,
                authorityCode: 'enquiry#purchaseEnquiryHead:reject',
                showCondition: () => { return this.$hasOptAuth('enquiry#purchaseEnquiryHead:reject') } },
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revocation`, '撤销'), disabled: (row, isPack) => {
                    let _this = this
                    // isPack 是否打包操作 packageOptFrist 已打包操作 1
                    if ((!isPack && _this.packageOptFrist === '1') || (isPack && _this.packageOptFrist === '0')) return true
                    //核价状态核价通过后不能撤销
                    if(row.evaluationStatus== '4') return true
                    // 提交定价后不能撤销
                    if (row.pricedFlag == '1') return true
                    // 审批中 不能撤销
                    if (row.auditStatus == '1') return true
                    if (row.itemStatus === '11') return true // 行悔标 不可操作
                    if (row.itemStatus == '4' || row.itemStatus == '5') return false // 行接受或拒绝 可操作
                    let enquiryStatus = _this.form.enquiryStatus
                    // (行状态 接受 || 拒绝) && (单据状态 报价中 || 议价中 || (已悔标 && 悔标类型重比价))
                    return !(row.itemStatus == '4' || row.itemStatus == '5') && 
                    (enquiryStatus == '1' || enquiryStatus == '7' || (enquiryStatus == '11' && row.regretFlag == '2'))
                }, clickFn: this.revoke},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_restatement`, '重报'), disabled: (row, isPack) => {
                    // isPack 是否打包操作 packageOptFrist 已打包操作 1
                    let _this = this
                    // （非打包比价列表 且 当前已打包操作） 或（打包比价列表 且 当前已非打包操作）
                    if ((!isPack && _this.packageOptFrist === '1') || (isPack && _this.packageOptFrist === '0')) return true
                    let enquiryStatus = this.form.enquiryStatus
                    // enquiryStatus === '7' // 议价中
                    const data = JSON.parse(JSON.stringify(this.$refs.compareGrid.getTableData().fullData))
                    let quotaScaleCount = 0
                    // 仅计算已接受状态 百分比
                    data.filter(i => i.itemStatus == '4').forEach((i) => {
                        quotaScaleCount += i.quotaScale
                    })
                    // 当前物料拆分百分比100，且当前单状态为议价中，不可操作重报
                    if (quotaScaleCount === 100 && enquiryStatus === '7') {
                        return true
                    }
                    //已报价, 不能报价, 未报价
                    let canOptStatus = ['2', '6', '3', '8']
                    let itemStatus = row.itemStatus
                    let flag = canOptStatus.find(status => status === itemStatus)
                    if(flag) return false
                    //已悔标 && 悔标标识为重比价
                    if(itemStatus === '11' && row.regretFlag === '2') return false
                    return true
                }, clickFn: (row, column, packageOpt) => {this.reQuote('user', row, packageOpt)},
                authorityCode: 'enquiry#purchaseEnquiryHead:reQuote',
                showCondition: () => { return this.$hasOptAuth('enquiry#purchaseEnquiryHead:reQuote') } }
            ],
            gridSort: { trigger: 'cell', defaultSort: { field: 'netAmount', order: 'desc' } },
            gridCustomEditConfig: {
                trigger: 'click', // dblclick
                mode: 'cell',
                showStatus: true,
                activeMethod: this.activeRowMethod
            },
            girdEditRulesConfig: {
                quotaQuantity: [{pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/, message: '输入一个正数'}],
                quotaScale: [{pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/, message: '输入一个正数'}]
            }
        }
    },
    watch: {
        '$route': {
            handler ({ path }) {
                if (path === '/enquiry/purchaseHall2') {
                    this.sourceId = this.$route.query.id || ''
                    this.getDetailData()
                    this.getData()
                }
            },
            immediate: true
        }
    },
    created (){
        nominalEdgePullWhiteBlack()
    },
    computed: {
        showPriceRecord () {
            return this.$hasOptAuth('enquiry#purchaseEnquiryHead:generatePriceRecord')
        },
        showRegretBtn () {
            return this.$hasOptAuth('enquiry#purchaseEnquiryHead:regret')
        },
        showRequote () {
            return this.$hasOptAuth('enquiry#purchaseEnquiryHead:reQuote')
        },
        showReplenishMaterial () {
            return this.$hasOptAuth('enquiry#purchaseEnquiryHead:replenishMaterialNumber')
        },
        showButton () {
            if (this.form.enquiryStatus === '10') {
                return false
            }
            return true
        },
        disabledButton () {
            let { priceCreateWay } = this.form
            if (priceCreateWay === '1' || priceCreateWay === '3') return true // 不生成或自动生成，不可操作
            else if (priceCreateWay === '2') return false // 手动生成，可操作
            return true
        },
        compareGridColumn () {
            return [
                { type: 'checkbox', width: 36, fixed: 'left' },
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center', fixed: 'left' },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 160, align: 'center', slots: { default: 'grid_opration' } },
                { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'), width: 70 },
                { field: 'evaluationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nuzE_3096be0a`, '核价状态'), width: 75 },
                { sortable: true, field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vendorName`, '供应商'), width: 150, slots: { default: 'grid_supplierName' } },
                {
                    sortable: true,
                    field: 'netPrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                {
                    sortable: true,
                    field: 'payTermsCode_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paymentCondition`, '付款条件'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'tradeCondition_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tradeCondition`, '国贸条件'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'futurePrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IBtu_37a0e2db`, '目标单价'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'hisMinPrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vKenu_c0b47fbd`, '历史最低价'),
                    width: 110
                },
                {
                    sortable: true,
                    field: 'supplierHisMinPrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXvKenu_c51fa1d0`, '供应商历史最低价'),
                    width: 150
                },
                {
                    sortable: true,
                    field: 'supplierSumAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AtnRf_25bdff85`, '累计采购额'),
                    width: 110
                },
                {
                    sortable: true,
                    field: 'sumQuantity',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AtnRR_25bdf8b7`, '累计采购量'),
                    width: 110
                },
                {
                    sortable: true,
                    field: 'threeMonthsSumAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HImZknRf_d4911472`, '近三个月总采购额'),
                    width: 160
                },
                {
                    sortable: true,
                    field: 'price',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                { sortable: true, field: 'quotaQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitQty`, '拆分数量'), width: 130, editRender: {name: '$input', props: {type: 'number'}}},
                { sortable: true, field: 'quotaScale', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比'), width: 130, editRender: {name: '$input', props: {type: 'number'}}},
                {
                    sortable: true,
                    field: 'taxAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                {
                    sortable: true,
                    field: 'netAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netAmount`, '未税金额'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                { sortable: true, field: 'currency_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), width: 70 },
                { sortable: true, field: 'taxRate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), width: 70 },
                { sortable: true, field: 'priceUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_priceBase`, '价格基数'), width: 93 },
                { sortable: true, field: 'deliveryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliveryDate`, '交货日期'), width: 100 },
                { sortable: true, field: 'minPackQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_minPackQuantity`, '最小包装量'), width: 110 },
                { sortable: true, field: 'minOrderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_minOrderQuantity`, '最小订单量'), width: 110 },
                { sortable: true, field: 'effectiveDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceEffectiveDate`, '价格生效日期'), width: 120 },
                { sortable: true, field: 'expiryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceNotEffectiveDate`, '价格失效日期'), width: 120 },
                { sortable: true, field: 'quoteCount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteCount`, '报价次数'), width: 93 },
                { sortable: true, field: 'quoteEndTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suyRKI_db7df728`, '报价截止时间'), width: 150 },
                { sortable: true, field: 'quoteTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lastQuotationTime`, '最后报价时间'), width: 150 },
                { field: 'bargainRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bargainRemark`, '议价备注'), width: 100, editRender: {name: '$input'}}
            ]
        },
        compareLadderGridColumn () {
            return [
                { type: 'checkbox', width: 36, fixed: 'left' },
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center', fixed: 'left' },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 160, align: 'center', slots: { default: 'grid_opration' } },
                { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'), width: 70 },
                { field: 'evaluationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nuzE_3096be0a`, '核价状态'), width: 75 },
                { sortable: true, field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vendorName`, '供应商'), width: 150, slots: { default: 'grid_supplierName' }},
                {
                    sortable: true,
                    field: 'payTermsCode_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paymentCondition`, '付款条件'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'tradeCondition_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tradeCondition`, '国贸条件'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'futurePrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IBtu_37a0e2db`, '目标单价'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'hisMinPrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vKenu_c0b47fbd`, '历史最低价'),
                    width: 110
                },
                {
                    sortable: true,
                    field: 'supplierHisMinPrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXvKenu_c51fa1d0`, '供应商历史最低价'),
                    width: 150
                },
                {
                    sortable: true,
                    field: 'supplierSumAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AtnRf_25bdff85`, '累计采购额'),
                    width: 110
                },
                {
                    sortable: true,
                    field: 'sumQuantity',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AtnRR_25bdf8b7`, '累计采购量'),
                    width: 110
                },
                {
                    sortable: true,
                    field: 'threeMonthsSumAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HImZknRf_d4911472`, '近三个月总采购额'),
                    width: 160
                },
                { sortable: true, field: 'quotaQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitQty`, '拆分数量'), width: 130, editRender: {name: '$input', props: {type: 'number'}}},
                { sortable: true, field: 'quotaScale', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比'), width: 130, editRender: {name: '$input', props: {type: 'number'}}},
                {
                    sortable: true,
                    field: 'taxAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                {
                    sortable: true,
                    field: 'netAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netAmount`, '未税金额'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                { sortable: true, field: 'currency_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), width: 70 },
                { sortable: true, field: 'taxRate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), width: 70 },
                { sortable: true, field: 'priceUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_priceBase`, '价格基数'), width: 93 },
                { sortable: true, field: 'deliveryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliveryDate`, '交货日期'), width: 100 },
                { sortable: true, field: 'minPackQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_minPackQuantity`, '最小包装量'), width: 110 },
                { sortable: true, field: 'minOrderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_minOrderQuantity`, '最小订单量'), width: 110 },
                { sortable: true, field: 'effectiveDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceEffectiveDate`, '价格生效日期'), width: 120 },
                { sortable: true, field: 'expiryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceNotEffectiveDate`, '价格失效日期'), width: 120 },
                { sortable: true, field: 'quoteCount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteCount`, '报价次数'), width: 93 },
                { sortable: true, field: 'quoteEndTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suyRKI_db7df728`, '报价截止时间'), width: 150 },
                { sortable: true, field: 'quoteTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lastQuotationTime`, '最后报价时间'), width: 150 },
                { field: 'bargainRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bargainingRemarks`, '议价备注'), width: 100, editRender: {name: '$input'}}
            ]
        },
        compareCostGridColumn () {
            return [
                { type: 'checkbox', width: 36, fixed: 'left' },
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center', fixed: 'left' },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 160, align: 'center', slots: { default: 'grid_opration' } },
                { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'), width: 70 },
                { field: 'evaluationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nuzE_3096be0a`, '核价状态'), width: 75 },
                { sortable: true, field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vendorName`, '供应商'), width: 150, slots: { default: 'grid_supplierName' }},
                {
                    sortable: true,
                    field: 'payTermsCode_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paymentCondition`, '付款条件'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'tradeCondition_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tradeCondition`, '国贸条件'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'futurePrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_IBtu_37a0e2db`, '目标单价'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'hisMinPrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vKenu_c0b47fbd`, '历史最低价'),
                    width: 110
                },
                {
                    sortable: true,
                    field: 'supplierHisMinPrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXvKenu_c51fa1d0`, '供应商历史最低价'),
                    width: 150
                },
                {
                    sortable: true,
                    field: 'supplierSumAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AtnRf_25bdff85`, '累计采购额'),
                    width: 110
                },
                {
                    sortable: true,
                    field: 'sumQuantity',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AtnRR_25bdf8b7`, '累计采购量'),
                    width: 110
                },
                {
                    sortable: true,
                    field: 'threeMonthsSumAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HImZknRf_d4911472`, '近三个月总采购额'),
                    width: 160
                },
                { sortable: true, field: 'quotaQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitQty`, '拆分数量'), width: 130, editRender: {name: '$input', props: {type: 'number'}}},
                { sortable: true, field: 'quotaScale', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比'), width: 130, editRender: {name: '$input', props: {type: 'number'}}},
                {
                    sortable: true,
                    field: 'netPrice',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                {
                    sortable: true,
                    field: 'price',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                {
                    sortable: true,
                    field: 'taxAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_amountIncludTax`, '含税金额'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                {
                    sortable: true,
                    field: 'netAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netAmount`, '未税金额'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                { sortable: true, field: 'currency_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), width: 70 },
                { sortable: true, field: 'taxRate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_taxRate`, '税率'), width: 50 },
                { sortable: true, field: 'priceUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_priceBase`, '价格基数'), width: 73 },
                { sortable: true, field: 'deliveryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliveryDate`, '交货日期'), width: 100 },
                { sortable: true, field: 'minPackQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_minPackaging`, '最小包装量'), width: 90 },
                { sortable: true, field: 'minOrderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_minOrderQuantity`, '最小订单量'), width: 90 },
                { sortable: true, field: 'effectiveDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceEffectiveDate`, '价格生效日期'), width: 100 },
                { sortable: true, field: 'expiryDate', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceNotEffectiveDate`, '价格失效日期'), width: 100 },
                { sortable: true, field: 'quoteCount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteCount`, '报价次数'), width: 73 },
                { sortable: true, field: 'quoteEndTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suyRKI_db7df728`, '报价截止时间'), width: 150 },
                { sortable: true, field: 'quoteTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lastQuotationTime`, '最后报价时间'), width: 150 },
                { field: 'bargainRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bargainingRemarks`, '议价备注'), width: 100, editRender: {name: '$input'}}
            ]
        },
        comparePackageGridColumn () {
            return [
                { type: 'checkbox', width: 36, fixed: 'left' },
                { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), align: 'center', fixed: 'left' },
                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 160, align: 'center', slots: { default: 'grid_opration' } },
                { field: 'itemStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_status`, '状态'), width: 70 },
                { sortable: true, field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vendorName`, '供应商'), width: 150, slots: { default: 'grid_supplierName' } },
                {
                    sortable: true,
                    field: 'payTermsCode_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paymentCondition`, '付款条件'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'tradeCondition_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tradeCondition`, '国贸条件'),
                    width: 100
                },
                {
                    sortable: true,
                    field: 'supplierStatus_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierStatus`, '供应商状态'),
                    width: 120
                },
                { sortable: true, field: 'quotaScale', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzmzl_96f5e18c`, '拆分百分比'), width: 130, editRender: {name: '$input', props: {type: 'number'}}},
                {
                    sortable: true,
                    field: 'taxAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                },
                {
                    sortable: true,
                    field: 'netAmount',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未含税金额'),
                    width: 150,
                    slots: {
                        default ({row, column}) {
                            return [
                                <span>{currency(row[column.property], '', 6)}</span>
                            ]
                        }
                    }
                }
            ]
        },
        vxTableHeight (){
            return document.documentElement.clientHeight-180
        },
        vxTableHeightList (){
            return document.documentElement.clientHeight-240
        }
    },
    methods: {
        exportExcel () {
            this.spinning = true
            const exportUrl=`/enquiry/sumExcel/exportExcel?id=${this.form.id}`
            downFile(exportUrl).then((data) => {
                if (data.type=='application/json') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), `${this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_MksB_32ae1237`, '汇总报表')}.xlsx`)
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', `${this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_MksB_32ae1237`, '汇总报表')}.xlsx`)
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            }).finally(() => {
                this.spinning = false
            })
        },
        currency,
        async changeMaterialSave () {
            let data = []
            if (this.tabPaneKey === 'compareNormal') data = JSON.parse(JSON.stringify(this.$refs.compareGrid.getTableData().fullData))
            if (this.tabPaneKey === 'compareLadder') data = JSON.parse(JSON.stringify(this.$refs.compareLadderGrid.getTableData().fullData))
            if (this.tabPaneKey === 'compareCost') data = JSON.parse(JSON.stringify(this.$refs.compareCostGrid.getTableData().fullData))
            if (this.tabPaneKey === 'comparePackage') data = JSON.parse(JSON.stringify(this.$refs.comparePackagetGrid.getTableData().fullData))
            await postAction('/enquiry/purchaseEnquiryHead/editItemList', data)
        },
        queryTemplateList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: 'purchasePriceEvaluationManageHead'}
            return getAction('/template/templateHead/getListByType', params)
        },
        openTemplateModal () {
            this.queryTemplateList(this.$ls.get('Login_elsAccount')).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.templateNumber,
                                title: item.templateName,
                                version: item.templateVersion,
                                account: item.elsAccount
                            }
                        })
                        this.templateOpts = options
                        this.visible = true
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })

        },
        selectedTemplate () {
            if(this.templateNumber) {
                const that = this
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version,
                    templateAccount: template[0].account,
                    elsAccount: this.$ls.get(USER_ELS_ACCOUNT)
                }
                let id = this.sourceId
                params.id = id
                postAction('/enquiry/purchaseEnquiryHead/submitEvaluationPrice', params).then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.getItemData()
                        this.visible = false
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }
        },
        handleCancel () {
            this.visible = false
        },
        reportModalShow (){
            this.$refs.reportModal.open(this.sourceId, this.purchaseEnquiryItemList)
        },
        handleChange (column) {
            let _this = this
            if (column == 'comparePackage') {
                _this.$refs.materialGrid.setAllCheckboxRow(true)
            } else {
                _this.$refs.materialGrid.clearCheckboxRow()
            }
        },
        // 更新询价管理tab下的数据，不更新状态
        refreshEnguiryDetailTab () {
            let randonValue = Math.random()* 100000
            this.$store.commit('TOGGLE_REFLESHINTERFACE_DATA', randonValue)
        },
        activeRowMethod ({row, column}){
            let itemStatus = row['itemStatus']
            let regretFlag = row['regretFlag']
            let property = column.property
            //(拆分数量 || 拆分百分比) && 行状态为  已报价 || 重报价 || (已悔标 && 悔标标识为重比价)
            if((property === 'quotaScale' || property === 'quotaQuantity') && (itemStatus === '2' || itemStatus === '8' || (itemStatus === '11' && regretFlag === '2'))){
                return true
            }
            //议价备注字段 && 行状态为  已报价 || 重报价 || 不能报价 || 未报价 || (已悔标 && 悔标标识为重比价)
            if(property === 'bargainRemark' && (itemStatus === '2' || itemStatus === '8' || itemStatus === '6' || itemStatus === '3' || (itemStatus === '11' && regretFlag === '2'))){
                return true
            }
            return false
        },
        handleRowClass ({ row }) {
            const itemNumber = this.itemNumber || '1'
            if (row.itemNumber === itemNumber) {
                return 'row--current'
            }
        },
        cellClickEvent ({ row }) {
            const { itemNumber = '' } = row || {}
            if (itemNumber === this.itemNumber) return
            this.itemNumber = itemNumber
            this.getItemData('changeMaterial')
        },
        goBack () {
            this.closeCurrent()
        },
        async refresh () {
            await this.getDetailData()
            this.getData()
        },
        async getDetailData () {
            this.spinning = true
            const res = await getAction(this.url.detail, { id: this.sourceId })
            if (res && res.success) {
                this.quotaWayDictText = res.result.quotaWay_dictText // 拆分方式
                this.packageOptFrist = res.result.packageOptFrist
            }
            this.spinning = false
        },
        getData () {
            const params = {
                headId: this.sourceId
            }
            const apiQueryMaterialList = getAction('/enquiry/purchaseEnquiryHead/queryMaterialList', params)
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))
            const promiseList = [
                apiQueryMaterialList
            ]
            this.spinning = true
            Promise.all(handlePromise(promiseList)).then(res => {
                this.spinning = false
                const [infoRes] = res || []
                if (infoRes && infoRes.status === 'success') {
                    this.fixInfoData(infoRes.res)
                }
                this.getItemData()
            })
        },
        fixInfoData ({ result }) {
            const { purchaseEnquiryItemList = [], ...others } = result || {}
            this.purchaseEnquiryItemList = purchaseEnquiryItemList
            if (purchaseEnquiryItemList.length) {
                this.itemNumber = purchaseEnquiryItemList[0].itemNumber || '1'
            }
            this.$refs.materialGrid.loadData(purchaseEnquiryItemList)
            this.form = {
                ...others
            }
        },
        async getItemData (type = '', row = null) {
            // 切换物料前先保存当前物料下拆分数量、百分比
            if (type === 'changeMaterial') {
                await this.changeMaterialSave()
            }

            const itemNumber = this.itemNumber || '1'
            const { quotePriceWay = '0' } = this.purchaseEnquiryItemList.find(n => n.itemNumber === itemNumber)
            this.changeAndDisableTab(quotePriceWay)
            //查询常规比价
            const apiQueryDetailsCompare = getAction('/enquiry/purchaseEnquiryHead/queryDetailsCompare', {
                headId: this.sourceId,
                itemNumber: itemNumber
            })
            //查询阶梯比价
            const apiQueryLadderCompare = getAction('/enquiry/purchaseEnquiryHead/queryLadderCompare', {
                headId: this.sourceId,
                itemNumber: itemNumber
            })
            //查询成本比价
            const apiQueryCostCompare = getAction('/enquiry/purchaseEnquiryHead/queryCostCompare', {
                headId: this.sourceId,
                itemNumber: itemNumber
            })
            //查询打包比价
            const apiQueryPackageCompare = getAction('/enquiry/purchaseEnquiryHead/queryPackageCompare', {
                headId: this.sourceId}
            )

            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: res.success ?  'success' : 'error', res }), err => ({ status: 'error', err })))
            const promiseList = [
                apiQueryDetailsCompare,
                apiQueryLadderCompare,
                apiQueryCostCompare,
                apiQueryPackageCompare
            ]
            this.spinning = true
            Promise.all(handlePromise(promiseList)).then(res => {
                this.spinning = false
                const [compare, ladder, cost, packages] = res || []
                if (compare && compare.status === 'success') {
                    this.fixCompareData(compare.res, type, row)
                }
                if (ladder && ladder.status === 'success') {
                    this.fixLadderData(ladder.res, type, row)
                }
                if (cost && cost.status === 'success') {
                    this.fixCostData(cost.res, type, row)
                }
                if(packages && packages.status === 'success'){
                    this.fixPackageData(packages.res, type, row)
                }
            })
        },
        changeAndDisableTab (quotePriceWay) {
            if(quotePriceWay === '0'){//常规报价
                this.tabPaneKey = this.tabPaneKey === 'comparePackage' ? this.tabPaneKey : 'compareNormal'
                this.compareTabFlag = false
                this.ladderTabFlag = true
                this.costTabFlag = true
            }else if(quotePriceWay === '1'){//阶梯报价
                this.tabPaneKey = this.tabPaneKey === 'comparePackage' ? this.tabPaneKey : 'compareLadder'
                this.compareTabFlag = true
                this.ladderTabFlag = false
                this.costTabFlag = true
            }else if(quotePriceWay === '2'){//成本报价
                this.tabPaneKey = this.tabPaneKey === 'comparePackage' ? this.tabPaneKey : 'compareCost'
                this.compareTabFlag = true
                this.ladderTabFlag = true
                this.costTabFlag = false
            }
        },
        fixCompareData ({ result }, type = '', row = null) {
            if (type === 'option' && row) {
                let oldData = JSON.parse(JSON.stringify(this.$refs.compareGrid.getTableData().fullData))
                const id = row.id
                const changeItem = result.find(i => i.id === id)
                let data = oldData.map(i => {
                    if (i.id === id) return changeItem
                    else return i
                })
                this.$refs.compareGrid.loadData(data)
            } else {
                let data = JSON.parse(JSON.stringify(result))
                this.$refs.compareGrid.loadData(data)
            }
        },
        fixLadderData ({ result }, type = '', row = null){
            if (type === 'option' && row) {
                let loadLaddertData = []
                let ladderColumn = []

                let oldData = JSON.parse(JSON.stringify(this.$refs.compareLadderGrid.getTableData().fullData))
                const id = row.id
                const changeItem = result.find(i => i.id === id)
                oldData.forEach(item => {
                    if (item.id === id) {
                        let ladderData = changeItem.ladderDataMap || {}
                        for(let name in changeItem){
                            if(name != 'costColumn' && name != 'costDataMap' && name != 'ladderColumn' && name != 'ladderDataMap'){
                                ladderData[name] = changeItem[name]
                                ladderData.sortable = true
                            }
                        }
                        loadLaddertData.push(ladderData)
                        if (changeItem && changeItem.ladderColumn)
                            ladderColumn = changeItem.ladderColumn.map(i => {
                                return { ...i, sortable: true }
                            })
                        else ladderColumn = []
                    } else {
                        let ladderData = item.ladderDataMap || {}
                        for(let name in item){
                            if(name != 'costColumn' && name != 'costDataMap' && name != 'ladderColumn' && name != 'ladderDataMap'){
                                ladderData[name] = item[name]
                                ladderData.sortable = true
                            }
                        }
                        loadLaddertData.push(ladderData)

                        if (changeItem && changeItem.ladderColumn)
                            ladderColumn = changeItem.ladderColumn.map(i => {
                                return { ...i, sortable: true }
                            })
                        else ladderColumn = []
                    }
                })

                let frontArray = this.compareLadderGridColumn.slice(0, 5)
                let lastArray = this.compareLadderGridColumn.slice(5, -1)
                ladderColumn = frontArray.concat(ladderColumn).concat(lastArray)
                this.$refs.compareLadderGrid.reloadColumn(ladderColumn)
                this.$refs.compareLadderGrid.loadData(loadLaddertData)
            } else {
                let loadLaddertData = []
                let ladderColumn = []
                result.forEach(item => {
                    let ladderData = item.ladderDataMap || {}
                    for(let name in item){
                        if(name != 'costColumn' && name != 'costDataMap' && name != 'ladderColumn' && name != 'ladderDataMap'){
                            ladderData[name] = item[name]
                            ladderData.sortable = true
                        }
                    }
                    loadLaddertData.push(ladderData)
                    ladderColumn = item.ladderColumn.map(i => {
                        return { ...i, sortable: true }
                    }) || []
                })
                let frontArray = this.compareLadderGridColumn.slice(0, 5)
                let lastArray = this.compareLadderGridColumn.slice(5, -1)
                ladderColumn = frontArray.concat(ladderColumn).concat(lastArray)
                this.$refs.compareLadderGrid.reloadColumn(ladderColumn)
                this.$refs.compareLadderGrid.loadData(loadLaddertData)
            }
        },
        fixCostData ({ result }, type = '', row = null) {
            if (type === 'option' && row) {
                let oldData = JSON.parse(JSON.stringify(this.$refs.compareCostGrid.getTableData().fullData))
                const id = row.id
                const changeItem = result.find(i => i.id === id)
                let loadCostData = []
                let costColumn = []
                oldData.forEach(item => {
                    if (item.id === id) {
                        let costData = changeItem.costDataMap || {}
                        for(let name in changeItem){
                            if(name != 'costColumn' && name != 'costDataMap' && name != 'ladderColumn' && name != 'ladderDataMap'){
                                costData[name] = changeItem[name]
                            }
                        }
                        loadCostData.push(costData)
                        if (changeItem && changeItem.costColumn) {
                            costColumn = changeItem.costColumn.map(i => {
                                return { ...i, sortable: true }
                            })
                        } else {
                            costColumn = []
                        }
                    } else {
                        let costData = item.costDataMap || {}
                        for(let name in item){
                            if(name != 'costColumn' && name != 'costDataMap' && name != 'ladderColumn' && name != 'ladderDataMap'){
                                costData[name] = item[name]
                            }
                        }
                        loadCostData.push(costData)
                        if (changeItem && changeItem.costColumn) {
                            costColumn = changeItem.costColumn.map(i => {
                                return { ...i, sortable: true }
                            })
                        } else {
                            costColumn = []
                        }
                    }
                })
                let frontArray = this.compareCostGridColumn.slice(0, 5)
                let lastArray = this.compareCostGridColumn.slice(5, -1)
                costColumn = frontArray.concat(costColumn).concat(lastArray)
                this.$refs.compareCostGrid.reloadColumn(costColumn)
                this.$refs.compareCostGrid.loadData(loadCostData)
            } else {
                let loadCostData = []
                let costColumn = []
                result.forEach(item => {
                    let costData = item.costDataMap || {}
                    for(let name in item){
                        if(name != 'costColumn' && name != 'costDataMap' && name != 'ladderColumn' && name != 'ladderDataMap'){
                            costData[name] = item[name]
                        }
                    }
                    loadCostData.push(costData)
                    if (item.costColumn) {
                        costColumn = item.costColumn.map(i => {
                            return { ...i, sortable: true }
                        })
                    } else {
                        costColumn = []
                    }
                })
                let frontArray = this.compareCostGridColumn.slice(0, 5)
                let lastArray = this.compareCostGridColumn.slice(5, -1)
                costColumn = frontArray.concat(costColumn).concat(lastArray)
                this.$refs.compareCostGrid.reloadColumn(costColumn)
                this.$refs.compareCostGrid.loadData(loadCostData)
            }
        },
        fixPackageData ({ result }, type = '', row = null){
            if (type === 'option' && row) {
                let oldData = JSON.parse(JSON.stringify(this.$refs.comparePackagetGrid.getTableData().fullData))
                const id = row.id
                const changeItem = result.find(i => i.id === id)
                let data = oldData.map(i => {
                    if (i.id === id) return changeItem
                    else return i
                })
                this.$refs.comparePackagetGrid.loadData(data)
            } else {
                this.$refs.comparePackagetGrid.loadData(result)
            }
        },
        //一键授标
        oneAward (optType){
            const callback = () => {
                this.spinning = true
                getAction('/enquiry/purchaseEnquiryHead/oneAward', {'headId': this.sourceId, 'optType': optType}).then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.getItemData()
                        this.refreshEnguiryDetailTab()
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.spinning = false
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLlB_3928214c`, '是否确认授标'),
                onOk () {
                    callback && callback()
                }
            })
        },
        //接受
        accept (row, column, packageOpt){
            let records = []
            records.push(row)
            const callback = () => {
                let param = {}
                param['id'] = records[0].headId
                param['packageOpt'] = packageOpt
                param['purchaseEnquiryItemList'] = records
                this.spinning = true
                postAction('/enquiry/purchaseEnquiryHead/accept', param).then(async (res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        await this.getDetailData()
                        this.getItemData('option', row)
                        this.refreshEnguiryDetailTab()
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.spinning = false
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sureToAcceptSelectedRowData`, '是否确认接受选择的行数据'),
                onOk () {
                    callback && callback()
                }
            })
        },
        //拒绝
        reject (row, column, packageOpt) {
            let records = []
            records.push(row)
            const callback = () => {
                let param = {}
                param['id'] = records[0].headId
                param['packageOpt'] = packageOpt
                param['purchaseEnquiryItemList'] = records
                this.spinning = true
                postAction('/enquiry/purchaseEnquiryHead/reject', param).then(async (res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        await this.getDetailData()
                        this.getItemData('option', row)
                        this.refreshEnguiryDetailTab()
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.spinning = false
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sureToAcceptSelectedNotRowData`, '是否确认拒绝选择的行数据'),
                onOk () {
                    callback && callback()
                }
            })
        },
        revoke (row, column, packageOpt){
            let records = []
            records.push(row)
            const callback = () => {
                let param = {}
                param['id'] = records[0].headId
                param['packageOpt'] = packageOpt
                param['purchaseEnquiryItemList'] = records
                this.spinning = true
                postAction('/enquiry/purchaseEnquiryHead/revoke', param).then(async (res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        await this.getDetailData()
                        this.getItemData('option', row)
                        this.refreshEnguiryDetailTab()
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.spinning = false
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLqXiFjcWF_d4057e2f`, '是否确认撤销选择的行数据'),
                onOk () {
                    callback && callback()
                }
            })
        },
        // 重报价
        reQuote (reQuoteWay, row, packageOpt) {
            if(reQuoteWay === 'material'){
                this.$refs.reQuote.openMaterial(this.sourceId)
            }else if(reQuoteWay === 'supplier'){
                this.$refs.reQuote.openSupplier(this.sourceId)
            }else if(reQuoteWay === 'all'){
                this.$refs.reQuote.openAll(this.sourceId)
            }else {
                let chooseList = []
                chooseList.push(row)
                this.$refs.reQuote.openUser(this.sourceId, chooseList, packageOpt, row)
            }
        },
        patchOperate (type){
            let checkList=null
            let records = []
            let allMaterial= []
            let packageOpt = false
            if(this.tabPaneKey==='compareNormal'){
                checkList = this.$refs.compareGrid.getCheckboxRecords()
                allMaterial= this.$refs.compareGrid.getTableData().fullData
            }else if(this.tabPaneKey==='compareLadder'){
                checkList = this.$refs.compareLadderGrid.getCheckboxRecords()
                allMaterial= this.$refs.compareLadderGrid.getTableData().fullData
            }else if(this.tabPaneKey==='compareCost'){
                checkList = this.$refs.compareCostGrid.getCheckboxRecords()
                allMaterial= this.$refs.compareCostGrid.getTableData().fullData

            }else if(this.tabPaneKey==='comparePackage'){
                packageOpt = true
                checkList = this.$refs.comparePackagetGrid.getCheckboxRecords()
                allMaterial= this.$refs.comparePackagetGrid.getTableData().fullData

            }
            if((type === 'allAccept' || type === 'allReject')&& checkList.length <= 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_import_ViFRdX_f2ffa076`, '请选择供应商'))
                return
            }
            if (type==='allAccept'){
                records.push(...checkList)
                const callback = () => {
                    let param = {}
                    param['id'] = records[0].headId
                    param['packageOpt'] = packageOpt
                    param['purchaseEnquiryItemList'] = records
                    this.spinning = true
                    postAction('/enquiry/purchaseEnquiryHead/allAccept', param).then((res) => {
                        if (res.success) {
                            this.$message.success(res.message)
                            checkList.forEach(i=>{
                                this.getItemData('option', i)
                            })
                            this.refreshEnguiryDetailTab()
                        } else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.spinning = false
                    })
                }
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_KQRLdRRdXbxSLyl_c6e429a7`, '是否确认相关供应商全部物料接受'),
                    onOk () {
                        callback && callback()
                    }
                })

            }else if(type==='allReject'){
                records.push(...checkList)
                const callback = () => {
                    let param = {}
                    param['id'] = records[0].headId
                    param['packageOpt'] = packageOpt
                    param['purchaseEnquiryItemList'] = records
                    this.spinning = true
                    postAction('/enquiry/purchaseEnquiryHead/allReject', param).then((res) => {
                        if (res.success) {
                            this.$message.success(res.message)
                            checkList.forEach(i=>{
                                this.getItemData('option', i)
                            })
                            this.refreshEnguiryDetailTab()
                        } else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.spinning = false
                    })
                }
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_KQRLdRRdXbxSLFK_c6e43b20`, '是否确认相关供应商全部物料拒绝'),
                    onOk () {
                        callback && callback()
                    }
                })
            }else if(type==='partReject'){
                // let allMaterial= this.$refs.materialGrid.getTableData().fullData
                records.push(...allMaterial)
                const callback = () => {
                    let param = {}
                    param['id'] = records[0].headId
                    param['packageOpt'] = packageOpt
                    param['purchaseEnquiryItemList'] = records
                    this.spinning = true
                    postAction('/enquiry/purchaseEnquiryHead/partReject', param).then((res) => {
                        if (res.success) {
                            this.$message.success(res.message)
                            allMaterial.forEach(i=>{
                                this.getItemData('option', i)
                            })
                            this.refreshEnguiryDetailTab()
                        } else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.spinning = false
                    })
                }
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_import_KQRLAvSLcbxFK_6bdd50ed`, '是否确认其它物料行全部拒绝'),
                    onOk () {
                        callback && callback()
                    }
                })
            }
        },
        showRegret (regretWay){
            if(regretWay === 'material'){
                let regretList = this.$refs.materialGrid.getCheckboxRecords()
                if(regretList.length <= 0){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFkiTPMBnSL_7b867728`, '请选择左侧需要悔标的物料'))
                    return
                }
                let materialDesc = regretList.map(row => row.materialName).join('，')
                let message = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areYouSureYouWantCancelBid`, '是否确认悔标物料')+'：' + materialDesc
                this.$refs.regret.openMaterial(this.sourceId, regretList, message)
            }else{
                let message = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLitMB_90430ea1`, '是否确认整单悔标')
                this.$refs.regret.openWhole(this.sourceId, message)
            }
        },
        replenishMaterialNumber (){
            let records = this.$refs.compareGrid.getTableData().fullData
            let row = JSON.parse(JSON.stringify(records[0]))
            if(row.materialNumber){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_material`, '物料')+':' + row.materialName + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_codeAlreadyExistsNoNeedSupplement`, '已存在编码，无需补充！'))
                return
            }
            let columns = [
                {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 150},
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 150},
                {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200},
                {field: 'brand', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialBrand`, '物料品牌'), width: 200},
                {field: 'purchaseCycle', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), width: 200},
                {field: 'purchaseType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseType`, '采购类型'), width: 200},
                {field: 'checkWay_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkWay`, '检验方式'), width: 200},
                {field: 'purchaseUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingUnit`, '采购单位'), width: 200},
                {field: 'materialModel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialModel`, '物料型号'), width: 200},
                {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料规格'), width: 200}
            ]
            this.$refs.fieldSelectModal.open('/material/purchaseMaterialHead/list', {}, columns, 'single')
        },
        fieldSelectOk (data) {
            let records = this.$refs.compareGrid.getTableData().fullData
            let row = JSON.parse(JSON.stringify(records[0]))
            const _this = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, '是否确认将物料')}：` + row.materialName + `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_encodingUpdatedTo`, '的编码更新为')}：` + data[0].materialNumber,
                onOk () {
                    _this.spinning = true
                    row.materialId = data[0].id
                    row.materialNumber = data[0].materialNumber
                    row.materialDesc = data[0].materialDesc
                    row.materialGroup = data[0].materialGroup
                    row.materialGroupName = data[0].materialGroupName
                    row.materialModel = data[0].materialModel
                    row.materialSpec = data[0].materialSpec
                    postAction('/enquiry/purchaseEnquiryHead/replenishMaterialNumber', row).then((res) => {
                        if (res.success) {
                            _this.$message.success(res.message)
                            _this.getData()
                        } else {
                            _this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        _this.spinning = false
                    })
                }
            })
        },
        setPricingNotice (){
            this.$refs.pricingNotice.open(this.sourceId, this.form.pricingNotice)
        },
        submitPrice (type){
            // 129250 授标意见初始化
            this.$refs.submitPriced.awardOpinion = null
            let row = null

            let checkList = this.$refs.materialGrid.getCheckboxRecords()
            if(type === 'row' && checkList.length <= 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFkiTPDJIujSL_ef42042a`, '请选择左侧需要提交定价的物料'))
                return
            }
            let materialDesc = checkList.map(row => row.materialName).join('，')

            // 提交定价需求：头、行均需要选择行
            // let records = this.$refs.compareGrid.getTableData().fullData // 当前选中的物料
            const materialList = this.$refs.materialGrid.getTableData().fullData // 所有物料
            row = type === 'row' ? checkList : materialList // 按行提交 row - 选择当前选中的物料，否则所有物料

            let message = type === 'row' ? this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLDJIuSL_e7345ef7`, '确认提交定价物料：') + materialDesc : this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLitDJIu_f9bb7ac8`, '确认整单定价')
            const _this = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: message,
                onOk () {
                    _this.$refs.submitPriced.open(_this.sourceId, row, type)
                }
            })
            
        },
        submitEvaluationPrice (){
            let sourceId = this.sourceId
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_DJnu_2e936d93`, '提交核价'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLDJnuW_94074dd6`, '确认提交核价?'),
                onOk () {
                    let params = {}
                    params.id = sourceId
                    that.spinning = true
                    postAction('/enquiry/purchaseEnquiryHead/submitEvaluationPrice', params).then((res) => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.refresh()
                        } else {
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.spinning = false
                    })
                }
            })
        },
        currentQuoteShow () {
            let records = this.$refs.compareGrid.getTableData().fullData
            this.$refs.currentQuoteChart.open(this.sourceId, records)
        },
        hisQuoteShow () {
            let records = this.$refs.compareGrid.getTableData().fullData
            this.$refs.hisQuoteChart.open(records)
        },
        exportCompare () {
            let params = {'headId': this.sourceId}
            this.spinning = true
            downFile('/enquiry/purchaseEnquiryHead/exportBargain', params).then((data) => {
                if (!data) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败') )
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表')+'.xlsx')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表')+'.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            }).finally(() => {
                this.spinning = false
            })
        },
        showContactList (row) { this.$refs.contactList.open(row) },
        generatePriceRecord (){
            let regretList = this.$refs.materialGrid.getCheckboxRecords()
            if(regretList.length <= 0){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFkiTPbLumtHjSL_c424cfc0`, '请选择左侧需要生成价格记录的物料'))
                return
            }
            const filterList = regretList.filter(i => i.itemStatus === '11')
            if (filterList.length > 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IMBSLxqIbLumtH_9d663369`, '已悔标物料不可生成价格记录'))
                return
            }
            let materialName = regretList.map(row => row.materialName).join('，')
            const callback = () => {
                let param = this.form
                param['purchaseEnquiryItemList'] = regretList
                postAction('/enquiry/purchaseEnquiryHead/generatePriceRecord', param).then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.getItemData()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areSureWantMaterial`, '是否确认将物料'))+':' + materialName + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_generateInformationRecord`, '生成信息记录'),
                onOk () {
                    callback && callback()
                }
            })
        }
    },
    destroyed () {
        // 组件销毁时，还原询价管理的刷新状态
        this.$store.commit('TOGGLE_REFLESHINTERFACE_DATA', '')
    }
}
</script>

<style lang="less" scoped>
@red: #f41616;
@blue: #178aff;
.PurchaseEnquiryHall {
        background-color: #eaeaea;
	ul {
		list-style: none;
		margin: 0;
		padding: 0;
	}
	.red {
		color: @red;
	}
	.blue {
		color: #1890ff;
	}
	.top {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 6px;
		background: #fff;
        position: fixed;
        width: 100%;
        height: 44px;
        z-index: 2;
		.menu {
			text-align: right;
			.ant-btn {
				& + .ant-btn {
					margin-left: 10px;
				}
			}
		}
	}
	.content {
		padding: 52px 8px 8px;
		.gutter {
			display: flex;
			& + .gutter {
				margin-top: 8px;
			}
			.price {
		
            flex: 1;
		
			}
			.history,
			.compare {
        overflow-y: auto;
				flex: 1;
				max-width: 100%;
		
                .ant-btn {
                    & + .ant-btn {
                        margin-left: 10px;
                    }
                }
			}
			.material {
			width: 374px;
            max-width: calc(100vw - 100px);
			}
		}
		.item {
			padding: 12px;
			background: #fff;
			& + .item {
				margin-left: 8px;
			}
		}
		.info {
			font-size: 20px;
			color: #000;
			.inline {
				&:first-of-type {
					border-left: 4px solid @blue;
				}
				& + .inline {
					margin-left: 24px;
				}
				.label {
					margin-left: 12px;
					&::after {
						content: ":";
					}
				}
				.value {
					margin-left: 12px;
					color: @blue;
				}
				.red {
					color: @red;
				}
			}
		}
		.currentTrendEchart {
			width: 100%;
			height: 400px;
		}
		.hisPriceEchart {
			width: 100%;
			height: 446px;
		}
		.table {
			height: 420px;
		}
        .material-table {
            height: 480px;
        }
	}
}

// @media only screen and (min-width: 992px) and (max-width: 1600px) {
//     .PurchaseEnquiryHall {
//         .content {
//             .gutter {
//                 .price {
//                     flex: 0 0 586px;
//                 }
//             }
//         }
//     }
// }
.PurchaseEnquiryHall {
	.material {
		.table {
			.vxe-grid .vxe-body--row {
				cursor: pointer;
			}
		}
	}
    .compare {
        .table {
            .vxe-grid .vxe-header--row {
                background: #EFF7FF;
            }
        }
    }
}
.vx-table-hight{
    height: calc(100vh - 180px);
  
}
.custom-resizer{
    width: 100vw;
    overflow: hidden;
     > .multipane-resizer {
    margin: 0;
    left: 0;
    // margin-top: 20%;
    position: relative;
    &:before {
      display: block;
      content: "";
      width: 3px;
      height: 40px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -20px;
      margin-left: -1.5px;
      border-left: 1px solid #ccc;
      border-right: 1px solid #ccc;
    }
    &:hover {
      &:before {
        border-color: #999;
      }
    }
  }
}
  
</style>

