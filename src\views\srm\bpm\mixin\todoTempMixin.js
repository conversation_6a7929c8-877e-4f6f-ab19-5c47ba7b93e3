import eventBus from '@/utils/eventBus.js'

export const todoTempMixin = {
    data () {
        return {
            pageStyle: {
                height: 'calc(100vh - 104px)',
                overflowY: 'auto'
            },
            toggleCardMode: true,
            tabCurrent: 0 // 缓存当前tab索引值
        }
    },
    methods: {
        scrollTo () {
            this.$nextTick(() => {
                eventBus.emitEvent('scrollTo', [0, 0, 300])
            })
        },
        handleToggleCardMode () {
            this.toggleCardMode = !this.toggleCardMode
        },
        handleSidebarClick ({ tab, idx }, pagerConfig, loadDataFunc) {
            if (this.tabCurrent === idx) {
                return
            }
            this.tabCurrent = idx
            pagerConfig.currentPage = 1
            loadDataFunc && loadDataFunc(tab)
            this.scrollTo()
        },
        handleExtendLink (col, row, getNewRouterFunc) {
            console.log('col :>> ', col)
            console.log('row :>> ', row)
            let linkConfig = {
                primaryKey: 'id', //查询用的主键名
                bindKey: 'fbk1', //绑定的主键key
                actionPath: '', //目标路由
                otherQuery: { open: true } //其余参数
            }
            if (col.extendLink) linkConfig = { ...linkConfig, ...col.extendLink.linkConfig }

            getNewRouterFunc && getNewRouterFunc(col, row, {}, linkConfig)
        },
        handlePageChange ({ currentPage, pageSize }, pagerConfig, loadDataFunc) {
            pagerConfig.currentPage = currentPage
            pagerConfig.pageSize = pageSize
            loadDataFunc && loadDataFunc()
            this.scrollTo()
        }
    }
}
