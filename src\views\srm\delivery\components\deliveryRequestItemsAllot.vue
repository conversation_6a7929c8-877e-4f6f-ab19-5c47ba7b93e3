<!--
 * @Author: gongzhirong
 * @Date: 2022-06-20 14:22:02
 * @LastEditTime: 2022-08-09 16:22:16
 * @LastEditors: gongzhirong
 * @Description: 
-->
<template>
  <div>
    <vxe-modal
      value
      @hide="close"
      :width="`${modalWidth}%`"
      :loading="modalLoading"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据')"
    >
      <template>
        <vxe-grid
          ref="grid"
          row-id="id"
          v-bind="gridConfig"
          :data="tableData"
          @page-change="pageChange"
          :loading="tableLoading"
          :checkbox-config="{ 
            highlight: true, 
            reserve: true, 
          }"
          border="full"
        >
          <template v-slot:toolbar_buttons>
            <a-form-model
              layout="inline"
              :model="searchParams"
              @submit="search"
              @submit.native.prevent
            >
              <a-form-model-item label="物料编码">
                <a-input
                  allow-clear
                  style="width: 150px"
                  v-model="searchParams.materialNumber"
                  placeholder="请输入"
                >
                  <a-icon
                    slot="prefix"
                    type="search"
                    style="color: rgba(0, 0, 0, 0.25)"
                  />
                </a-input>
              </a-form-model-item>
              <a-form-model-item label="物料名称">
                <a-input
                  allow-clear
                  style="width: 150px"
                  v-model="searchParams.materialName"
                  placeholder="请输入"
                >
                  <a-icon
                    slot="prefix"
                    type="search"
                    style="color: rgba(0, 0, 0, 0.25)"
                  />
                </a-input>
              </a-form-model-item>
              <a-form-model-item label="供应商编码">
                <a-input
                  allow-clear
                  style="width: 150px"
                  v-model="searchParams.supplierCode"
                  placeholder="请输入"
                >
                  <a-icon
                    slot="prefix"
                    type="search"
                    style="color: rgba(0, 0, 0, 0.25)"
                  />
                </a-input>
              </a-form-model-item>
              <a-form-model-item label="供应商名称">
                <a-input
                  allow-clear
                  style="width: 150px"
                  v-model="searchParams.supplierName"
                  placeholder="请输入"
                >
                  <a-icon
                    slot="prefix"
                    type="search"
                    style="color: rgba(0, 0, 0, 0.25)"
                  />
                </a-input>
              </a-form-model-item>
              <a-form-model-item>
                <a-button
                  type="primary"
                  html-type="submit"
                >
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}
                </a-button>
                <a-button
                  style="margin-left: 10px"
                  @click="resetSearchParams"
                >
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}
                </a-button>
                <a-button
                  style="margin-left: 10px"
                  @click="settingColumns"
                >
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_listCustom`, '列自定义') }}
                </a-button>
              </a-form-model-item>
            </a-form-model>
          </template>

          <template #assigned-quantity="{ row }">
            <a-input-number
              v-model="row.assignedQuantity"
              :min="0"
              :max="row.allocatableQuantity || 0"
              :precision="2"
              @blur="assignedQuantityBlur(row)"
              @focus="assignedQuantityFocus(row)"
            />
          </template>
        </vxe-grid>

        <div class="footer">
          <a-button
            type="primary"
            @click="allotSubmit"
          >
            {{ $srmI18n(`${$getLangAccount()}#i18n_btn_zEGcIdSeRt_c0b39796`, '分配创建送货通知单') }}
          </a-button>
          <a-button @click="close">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}
          </a-button>
        </div>
      </template>
    </vxe-modal>
    <column-setting
      ref="columnSettingModal"
      :hiddenFieldColors="true"
      :customReload="true"
      @success="reloadColunm"
    ></column-setting>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
import { getAction, postAction } from '@/api/manage'
import columnSetting from '@comp/template/columnSetting'
export default {
  components: { columnSetting },
  props: {
    currentEditRows: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      modalLoading: false,
      tableLoading: false,
      searchParams: {},
      currentParams: {},
      gridConfig: {
        align: 'center',
        size: 'small',
        height: '580',
        // checkboxConfig: {
        //     trigger: 'row'
        // },
        // mouseConfig: {
        //     selected: true
        // },
        // editConfig: {
        //     mode: 'cell'
        // },
        columns: [],
        editRules: {
          assignedQuantity: [
            {
              required: true,
              validator: this.assignedQuantityValidator
            }
          ]
        },
        toolbarConfig: {
          slots: {
            buttons: 'toolbar_buttons'
          }
        },
        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 500,
          align: 'right',
          pageSizes: [20, 50, 100, 200, 500],
          layouts: ['Sizes', 'PrevPage', 'Number', 'NextPage', 'FullJump', 'Total'],
          perfect: true
        }
      },
      tableData: [],
      tableDataOrigin: [],
      tableDataAll: [],

      modalWidth: 68
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    search() {
      this.currentParams.materialNumber = this.searchParams.materialNumber
      this.currentParams.materialName = this.searchParams.materialName
      this.currentParams.supplierCode = this.searchParams.supplierCode
      this.currentParams.supplierName = this.searchParams.supplierName
      this.getData()
    },
    assignedQuantityValidator({ row }) {
      // 判断是否已勾选
      const s = this.$refs.grid.isCheckedByCheckboxRow(row)
      if (s && !(row.assignedQuantity > 0)) {
        return new Error('分配数量必填！')
      }
    },
    async getData() {
      try {
        this.tableLoading = true
        const { currentPage, pageSize } = this.gridConfig.pagerConfig
        const params = {
          pageNo: currentPage,
          pageSize: pageSize,

          ids: this.currentEditRows.map((i) => i.id).join(','),
          materialName: this.currentParams.materialName || null, // 物料名称
          materialNumber: this.currentParams.materialNumber || null, // 物料编码
          supplierName: this.currentParams.supplierName || null, // 供应商名称
          supplierCode: this.currentParams.supplierCode || null // 供应商编码
        }
        
        // 获取全量数据，用于计算每个物料剩余可分配数量（因为原数据存在分页）
        if (this.tableDataAll.length === 0) {
          getAction('/delivery/purchaseDeliveryRequestItem/extractOrderBatch', {
            ...params,
            pageNo: 1,
            pageSize: 99999
          }).then((res) => {
            this.tableDataAll = res.result.records
          })
        }

        // 备份已选数据
        let selectedList = this.$refs.grid.getCheckboxRecords()
        let otherPageCheckData = this.$refs.grid.getCheckboxReserveRecords() || [] // 不包含当前页已选中数据
        selectedList = cloneDeep([...otherPageCheckData, ...selectedList]);
        console.log(selectedList)
        
        const res = await getAction('/delivery/purchaseDeliveryRequestItem/extractOrderBatch', params)
        if (res.success && res.result) {
          let tableData = res.result.records
          // 重新赋值分配数量
          if (this.tableDataOrigin.length > 0) {
            tableData = tableData.map((item) => {
              let id = item.id
              let itemOrigin = this.tableDataOrigin.find((i) => i.id === id)
              if (!!itemOrigin) item.assignedQuantity = itemOrigin.assignedQuantity
              return item
            })
            this.tableDataOrigin = []
          }

          // 将已填写过的分配数量重新赋值
          tableData.forEach(newItem => {
            selectedList.forEach(oldItem => {
              if(newItem.id == oldItem.id && !!oldItem.assignedQuantity) {
                newItem.assignedQuantity = oldItem.assignedQuantity;
              }
            })
          })

          this.tableData = tableData
          this.gridConfig.pagerConfig.total = res.result.total
          this.gridConfig.pagerConfig.currentPage = res.result.current

          const grid = this.$refs.grid
          grid.sort([{ field: 'materialNumber' }])
        } else {
          this.$message.error(res.message)
          this.close()
          // this.gridConfig.pagerConfig.currentPage = 1
          // this.gridConfig.pagerConfig.total = 0
          // this.tableData = []
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.tableLoading = false
      }
    },
    /**
     * @description: 分配并创建送货通知单
     */
    async allotSubmit() {
      try {
        this.modalLoading = true
        const grid = this.$refs.grid
        const errorList = await grid.validate(this.tableData)
        if (errorList) return
        let selectedList = grid.getCheckboxRecords()
        let otherPageCheckData = grid.getCheckboxReserveRecords() || [] // 不包含当前页已选中数据
        selectedList = [...otherPageCheckData, ...selectedList]
        if (selectedList.length < 1) return this.$message.error('请设置需要分配的物料！')

        const { id } = this.currentEditRows
        const params = selectedList
        let { message, success, result } = await postAction(`/delivery/purchaseDeliveryRequestItem/toBatchCreateDelivery`, params)
        if (success) {
          if (result?.length >= 1) {
            let orders = result.map((rs) => rs.noticeNumber)
            orders = orders.join(',')
            message = `分配创建送货通知单成功，已转 ${result.length} 单，单号为${orders}`
          }
          this.$message.success(message)
          this.$emit('success-cb', result && result[0] ? result[0] : null)
          this.close()
        } else {
          if (!!message && message.indexOf('\n') >= 0) {
            const h = this.$createElement
            let strList = message.split('\n')
            strList = strList.map((str, strIndex) => {
              1
              return h(strIndex === 0 ? 'span' : 'div', null, str)
            })
            this.$message.error(h('span', null, strList))
            return
          }
          this.$message.error(message)
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.modalLoading = false
      }
    },
    pageChange({ currentPage, pageSize }) {
      this.gridConfig.pagerConfig.currentPage = currentPage
      this.gridConfig.pagerConfig.pageSize = pageSize
      this.getData()
    },

    // 分配数量输入框失焦时，如果数量大于0，自动勾选
    assignedQuantityBlur(row) {
      let remainingQuantity = this.assignedQuantityFocus(row, true);
      console.log("remainingQuantity", remainingQuantity)
      if(row.assignedQuantity > remainingQuantity) {
        // this.$message.error("该物料总分配数量已超过可分配数量");
        row.assignedQuantity = remainingQuantity;
      }
      if (row.assignedQuantity > 0) {
        
        const grid = this.$refs.grid
        grid.setCheckboxRow(row, true)
      }

      // 同步分配数量数据到总数据中
      let index = this.tableDataAll.findIndex((item) => {
        return item.id == row.id
      })
      if (index >= 0) this.tableDataAll[index].assignedQuantity = row.assignedQuantity || 0
    },

    // 分配数据输入框聚焦时，计算该物料剩余可分配数量，直接填入
    assignedQuantityFocus(row, getNum = false) {
      if ((row.assignedQuantity > 0 || row.assignedQuantity === 0) && !getNum) return
      let { id, materialNumber, deliveryRequestAllocatableQuantity } = row
      let quantityForAll = 0
      console.log('tableDataAll', this.tableDataAll)
      this.tableDataAll.forEach((item) => {
        if (item.materialNumber == materialNumber && item.id != id) {
          quantityForAll += Number(item.assignedQuantity || 0)
        }
      })
      let assignedQuantity = deliveryRequestAllocatableQuantity - quantityForAll
      assignedQuantity = assignedQuantity > 0 ? assignedQuantity : 0;
      console.log(123, assignedQuantity, getNum)
      if(getNum) return assignedQuantity;
      row.assignedQuantity = assignedQuantity || row.assignedQuantity
    },

    resetSearchParams() {
      this.searchParams = {}
      this.tableDataOrigin = cloneDeep(this.tableData)
      this.search()
    },

    // 列自定义
    settingColumns() {
      this.$refs.columnSettingModal.open('PurchaseDeliveryRequestAllocationList')
    },

    checkIsFullWindow() {
      const windowWidth = window.outerWidth
      const windowHeight = window.outerHeight
      const screenWidth = screen.availWidth
      const screenHeight = screen.availHeight
      console.log(windowWidth, screenWidth, windowHeight, screenHeight)

      if (windowWidth === screenWidth && windowHeight === screenHeight) {
        console.log('窗口最大化')
        this.modalWidth = 80
      } else {
        console.log('正常大小弹框显示')
        this.modalWidth = 68
      }
    },

    async getColumns() {
      try {
        let res = await getAction('/base/userColumnDefine/queryCurrentUserColumnDefine/PurchaseDeliveryRequestAllocationList')
        let columns = res.result.map((item) => {
          let newItem = {
            field: item.columnCode,
            title: this.$srmI18n(`${this.$getLangAccount()}#${item.columnNameI18nKey}`, item.columnName),
            width: item.columnWidth,
            showOverflow: true,
            visible: item.hidden != 1,
            align: item.alignType,
            resizable: true,
            fixed: (item.fixType == 'left' || item.fixType == 'right')? item.fixType : null,
          }
          if (['供应商名称', '物料编码', '订单数量', '可分配数量', '订单可分配数量'].includes(item.columnName)) {
            newItem.sortable = true
          }
          // if (['订单可分配数量', '分配数量'].includes(item.columnName)) {
          //   newItem.fixed = 'right'
          // }
          if (item.columnName == '分配数量') {
            newItem = {
              ...newItem,
              editRender: {},
              slots: { default: 'assigned-quantity' }
            }
          }
          return newItem
        })
        columns.unshift(
          ...[
            { type: 'checkbox', width: 60, fixed: 'left' },
            { type: 'seq', title: '序号', width: 50, fixed: 'left' }
          ]
        )
        this.gridConfig.columns = columns
      } catch (error) {}
    },

    reloadColunm() {
      this.$emit('reloadColunm', 'multiple')
    }
  },
  async created() {
    await this.getColumns()
    await this.getData()
  },
  mounted() {
    let that = this
    window.addEventListener('resize', function (event) {
      console.log('窗口大小改变了！')
      // 在这里可以添加你想要执行的代码，比如更新页面布局或者调整媒体查询
      that.checkIsFullWindow()
    })
    that.checkIsFullWindow()
  }
}
</script>
<style scoped lang="less">
.footer {
  margin-top: 18px;
  text-align: center;
  .ant-btn + .ant-btn {
    margin-left: 58px;
  }
}

:deep(.vxe-toolbar .vxe-buttons--wrapper) {
  justify-content: space-between;

  .numberShow {
    font-size: 20px;
    margin-right: 100px;
  }
}
</style>
