<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showNewEditPage"
      ref="listPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
      :url="url" />


    <SaleRefundsDeliveryHeadModal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />

    <SaleNewRefundsDeliveryHeadModal
        ref="modalForm"
        v-if="showNewEditPage"
        :current-edit-row="currentEditRow"
        @hide="hideAddPage"
        @ok="modalFormOk"
    />

    <ViewSaleRefundsDeliveryModal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetailPage"
    />
    <TemSelectModal
        ref="temSelectModal"
        :pageData="pageData"
        @success="handleSaleAdd">
    </TemSelectModal>
  </div>
</template>

<script>
import SaleRefundsDeliveryHeadModal from './modules/SaleRefundsDeliveryHeadModal'
import SaleNewRefundsDeliveryHeadModal from './modules/SaleNewRefundsDeliveryHeadModal'
import ViewSaleRefundsDeliveryModal from './modules/ViewSaleRefundsDeliveryModal'
import TemSelectModal from './modules/TemSelectModal'
import { getAction, httpAction } from '@/api/manage'
import {ListMixin} from '@comp/template/list/ListMixin'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        SaleRefundsDeliveryHeadModal,
        SaleNewRefundsDeliveryHeadModal,
        ViewSaleRefundsDeliveryModal,
        TemSelectModal
    },
    data () {
        return {
            showEditPage: false,
            showNewEditPage: false,
            showCreateOrderPage: false,
            showEditEnquiryPage: false,
            currentEditRow: {},
            templateOpts: [],
            pageData: {
                businessType: 'saleRefundDelivery',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterTitleOrReturnOrderNo`, '请输入标题或退货单号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'order#saleRefundsDeliveryHead:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting',  clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'order#saleRefundsDeliveryHead:queryById'},
                    {type: '', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), clickFn: this.handleEdit, allow: this.showEditCondition, authorityCode: 'order#saleRefundsDeliveryHead:edit'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 180
            },
            url: {
                add: '/delivery/saleRefundsDeliveryHead/add',
                list: '/delivery/saleRefundsDeliveryHead/list',
                cancel: '/delivery/saleRefundsDeliveryHead/cancel',
                columns: 'SaleRefundsDeliveryHead'
            }
        }
    },
    watch: {
        $route: {
            immediate: true,
            handler (to) {
                if (to.query.open) {
                    let row = {
                        id: to.query.id,
                        templateName: to.query.templateName,
                        templateNumber: to.query.templateNumber,
                        templateVersion: to.query.templateVersion,
                        templateAccount: to.query.createAccount
                    }
                    if (row.id) {
                        this.handleEdit(row)
                        this.$nextTick(()=> {
                            this.showDetailPage = false
                            this.currentEditRow = row
                            this.showEditPage = true
                            this.$store.dispatch('SetTabConfirm', true)
                        })
                    }
                }

            }
        }
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.refundsDeliveryNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SaleRefundsDeliveryHead', url: this.url || '', recordNumber})
        },
        handleAdd () {
            let data = {
                url: '/enterprise/elsEnterpriseInfo/getPurchaseAccount',
                params: {
                    toElsAccount: this.$ls.get('Login_elsAccount'),
                    // frozenFunctionValue: '1'
                }
            }
            this.$refs.temSelectModal.open(data)
        },
        handleSaleAdd (row) {
            this.currentEditRow = row
            this.showNewEditPage = true
        },
        hideAddPage() {
            this.showNewEditPage = false
        },
        showEditCondition (row) {
            if((row.refundsDeliveryStatus == '1')) {
                return false
            }else {
                return true
            }
        },
        showCancelCondition (row) {
            if(row.deliveryStatus == '1') {
                return false
            }else {
                return true
            }
        },
        hideDetailPage (){
            this.showDetailPage = false
        },
        cancel (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
    }
}
</script>
