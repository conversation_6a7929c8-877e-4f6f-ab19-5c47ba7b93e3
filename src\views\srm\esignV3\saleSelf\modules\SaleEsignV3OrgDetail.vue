<template>
    <div class="page-container">
        <detail-layout
            ref="detailPage"
            :currentEditRow="currentEditRow"
            :page-data="pageData"
            :url="url"/>
    </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'

export default {
    name: 'SaleEsignV3OrgDetail',
    mixins: [DetailMixin],
    data() {
        return {
            selectType: 'esignPersonCertification',
            pageData: {
                form: {
                    subAccount: '',
                    applyUserName: '',
                    applyContact: '',
                    applyContactType: '',
                    mode: '',
                    modifyFields: '',
                    subAccountId: '',
                    paperType: '',
                    otherModes: '',
                    idCardNo: '',
                    bankMobile: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AELiVH_5eb668c4`, '企业认证信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商账号'),
                                    fieldName: 'elsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQuKVRtRAB_8bd82932`, '是否加载组织机构列表'),
                                    fieldName: 'loadingOrg_dictText',
                                    dictCode: 'yn',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQuKVRtRAB_8bd82932`, '是否加载组织机构列表')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tRoo_307b3288`, '机构代码'),
                                    fieldName: 'orgCode',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                                    fieldName: 'orgName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'),
                                    disabled: true,
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIy_76cf8d5`, '组织机构证件号'),
                                    fieldName: 'orgIdCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIy_76cf8d5`, '组织机构证件号'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIAc_e6376152`, '组织机构证件类型'),
                                    fieldName: 'orgIdCardType_dictText',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRtRiIAc_e6376152`, '组织机构证件类型'),
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLcR_b362e8a`, '法定代表人姓名'),
                                    fieldName: 'legalRepName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLcR_b362e8a`, '法定代表人姓名')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalIdNumber`, '法定代表人证件号'),
                                    fieldName: 'legalRepIdCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orgLegalIdNumber`, '法定代表人证件号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLiIAc_2d136275`, '法定代表人证件类型'),
                                    fieldName: 'legalRepIdCardType_dictText',
                                    dictCode: 'psnIdCardType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLiIAc_2d136275`, '法定代表人证件类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIELsCLiFjtRLiCK_b76a6b88`, '指定页面中默认选择的机构认证方式'),
                                    fieldName: 'orgDefaultAuthMode_dictText',
                                    dictCode: 'orgDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIELsCLiFjtRLiCK_b76a6b88`, '指定页面中默认选择的机构认证方式')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjtRLiCK_1f7b29a2`, '设置页面中可选择的机构认证方式'),
                                    fieldName: 'orgAvailableAuthModes_dictText',
                                    dictCode: 'orgDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjtRLiCK_1f7b29a2`, '设置页面中可选择的机构认证方式')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjVH_fed36f58`, '设置页面中可编辑的信息'),
                                    fieldName: 'orgEditableFields_dictText',
                                    dictCode: 'orgEditableFields',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjVH_fed36f58`, '设置页面中可编辑的信息')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrL_1e656eb`, '经办人'),
                                    fieldName: 'psnId',
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLSRMey_946bb5d4`, '经办人SRM账号'),
                                    fieldName: 'subAccount'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLeyBK_a92cd95b`, '经办人账号标识'),
                                    fieldName: 'psnAccount',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLeyBK_a92cd95b`, '经办人账号标识'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLcR_21b77cc5`, '经办人姓名'),
                                    fieldName: 'psnName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLcR_21b77cc5`, '经办人姓名'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIy_15f34077`, '经办人证件号'),
                                    fieldName: 'psnIdCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIy_15f34077`, '经办人证件号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIAc_a87a0df0`, '经办人证件类型'),
                                    fieldName: 'psnIdCardType_dictText',
                                    dictCode: 'psnIdCardType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLiIAc_a87a0df0`, '经办人证件类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLWEmy_ad7bbab7`, '经办人银行卡号'),
                                    fieldName: 'bankCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLWEmy_ad7bbab7`, '经办人银行卡号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLlty_155a8cbd`, '经办人手机号'),
                                    fieldName: 'psnMobile',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OrLlty_155a8cbd`, '经办人手机号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '设置页面中默认选择的实名认证方式'),
                                    fieldName: 'psnDefaultAuthmode_dictText',
                                    dictCode: 'psnDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '设置页面中默认选择的实名认证方式')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjmLLiCK_fdb374e8`, '设置页面中可选择的个人认证方式'),
                                    fieldName: 'psnAvailableAuthmodes_dictText',
                                    dictCode: 'psnDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjmLLiCK_fdb374e8`, '设置页面中可选择的个人认证方式')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjmLVHJO_bd7b6fe6`, '设置页面中可编辑的个人信息字段'),
                                    fieldName: 'psnEditableFields_dictText',
                                    dictCode: 'psnEditableFields',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjmLVHJO_bd7b6fe6`, '设置页面中可编辑的个人信息字段')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsbWvL_3d54cd4e`, '设置页面中权限范围'),
                                    fieldName: 'authorizedScopes_dictText',
                                    dictCode: 'orgAuthorizedScopes',
                                    required: '1',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsbWvL_3d54cd4e`, '设置页面中权限范围')
                                }
                            ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                detail: '/esignv3/saleEsignV3Org/queryById'
            }
        }
    },
    computed: {},
    mounted() {
        this.init()
    },
    methods: {
        init() {
            if (this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            } else {
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>
