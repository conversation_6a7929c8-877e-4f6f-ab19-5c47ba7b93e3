<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="edit"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
        <template v-slot:workerOrderContent="{ }">
          <j-editor
            :id="getJEditorID()"
            v-if="isShowJEditor"
            ref="ueditor"
            v-bind="{plugins,coustomInit}"
            v-model="editContent" />
        </template>
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import JEditor from '@/components/els/JEditor'
import { postAction } from '@/api/manage'
export default {
    mixins: [businessUtilMixin],
    name: 'EditAdviceModal',
    components: {
        BusinessLayout,
        JEditor
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            isShowJEditor: true,
            editContent: this.currentEditRow.content,
            refresh: true,
            requestData: {
                detail: { url: '/other/complaintAdviceHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/other/complaintAdviceHead/edit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save',
                    click: this.handleSave,
                    showMessage: true
                },
                {
                    title: '发布',
                    args: {
                        url: '/other/complaintAdviceHead/submit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save',
                    click: this.handleSubmit,
                    showMessage: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                edit: '/other/complaintAdviceHead/edit',
                add: '/other/complaintAdviceHead/add',
                submit: '/other/complaintAdviceHead/submit'
            },
            plugins: 'lists image media table wordcount fullscreen code powerpaste',
            coustomInit: {}
        }
    },
    methods: {
        // 提交数据组装
        dealParamsData () {
            let {
                ...baseForm
            } = this.$refs[this.businessRefName].extendAllData().allData
            let params = baseForm
            params.content = this.editContent
            params.id = this.currentEditRow.id
            return params
        },
        saveEvent () {
            let params = this.dealParamsData()
            let url = this.currentEditRow.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            postAction(url, params).then(res => {
                if (res && res.success) {
                    if (this.currentEditRow && !this.currentEditRow.id) {
                        this.currentEditRow.id=res.result.id
                    }                   
                    this.$message.success(res.message)
                } else {
                    this.$message.error(res.message)
                }
                
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 保存
        handleSave (args) {
            this.stepValidate(args).then(() => {
                this.saveEvent()
            })
        
        },
        submitEvent () {
            let params = this.dealParamsData()
            this.confirmLoading = true
            postAction(this.url.submit, params).then(res => {
                if (res && res.success) {                  
                    this.$message.success(res.message)
                    this.businessHide()
                } else {
                    this.$message.error(res.message)
                }
                
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        // 发布
        handleSubmit (args) {
            this.stepValidate(args).then(() => {
                this.submitEvent()
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_field_100100',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    },
                    {
                        groupName: '单据内容',
                        groupNameI18nKey: '',
                        groupCode: 'workerOrderContent',
                        groupType: 'head',
                        sortOrder: '2'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        fieldType: 'input',
                        fieldLabel: '单据单号',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_systemAutoCreat`, '系统自动生成'),
                        fieldLabelI18nKey: '',
                        fieldName: 'adviceNumber',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '2',
                        fieldType: 'input',
                        fieldLabel: '单据标题',
                        placeholder: '请输入单据标题',
                        fieldLabelI18nKey: '',
                        fieldName: 'title',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select', 
                        fieldLabel: '单据类型',
                        fieldName: 'adviceType', 
                        placeholder: '请选择单据类型',
                        dictCode: 'adviceType',
                        defaultValue: '1',
                        required: '1'
                    },
                    {
                        groupCode: 'workerOrderContent',
                        fieldType: '',
                        fieldLabel: '',
                        fieldName: 'content'
                    }
                ]
            }
        },
        getJEditorID () {
            return `editor-box-${Math.floor(Math.random() * 10 + 1)}`
        }
    },
    activated (){
        this.isShowJEditor=true
    },
    deactivated (){
        this.isShowJEditor=false
    }
}
</script>