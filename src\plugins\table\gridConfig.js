export const ListConfig = {
    border: true,
    stripe: true,
    resizable: true,
    autoResize: true,
    showOverflow: true,
    keepSource: true,
    showHeaderOverflow: true,
    size: 'mini',
    height: 'auto',
    headerAlign: 'center',
    columnKey: true,
    // editConfig: {
    //     trigger: 'dblclick',
    //     mode: 'cell',
    //     showStatus: true
    // },
    checkboxConfig: {
        highlight: true,
        reserve: true,
        trigger: 'cell'
    },
    toolbarConfig: {
        slots: { buttons: 'toolbar_buttons' },
        print: true,
        zoom: true,
        perfect: true
    },
    sortConfig: { remote: true },
    clipConfig: {
        isCut: false,
        isPaste: false
    },
    fnrConfig: {
        isFind: true,   //是否启用查找功能
        isReplace: false
    },
    mouseConfig: {
        area: true, // 是否开启单元格区域选取
        extension: true // 是否开启右下角延伸按钮
    },
    keyboardConfig: {
        isClip: true, // 是否开启复制粘贴功能
        isTab: true, // 是否开启TAB键左右移动功能
        isArrow: true, // 是否开启非编辑状态下，上下左右移动功能
        isFNR: true, // 是否开启查找和替换功能
        enterToTab: false // 是否将回车键行为改成 Tab 键行为
    },
    menuConfig: {
        body: {
            options: [
                [
                    { code: 'COPY_CELL', name: '复制 (Ctrl+C)', prefixIcon: 'fa fa-copy' }
                ],
                // 引入 echarts 和 vxe-table-plugin-charts 之后可以直接使用，也可以自行实现
                [
                    {
                        name: '创建图表',
                        prefixIcon: 'fa fa-area-chart',
                        children: [
                            { code: 'CHART_BAR_X_AXIS', name: '横向柱状图 - 自由选择', prefixIcon: 'fa fa-bar-chart' },
                            { code: 'CHART_BAR_X_AXIS', name: '横向柱状图 - 固定类别', prefixIcon: 'fa fa-bar-chart', params: { category: 'a' } },
                            { code: 'CHART_BAR_Y_AXIS', name: '纵向柱状图 - 自由选择', prefixIcon: 'fa fa-bar-chart' },
                            { code: 'CHART_BAR_Y_AXIS', name: '纵向柱状图 - 固定类别', prefixIcon: 'fa fa-bar-chart', params: { category: 'a' } },
                            { code: 'CHART_LINE', name: '折线图 - 自由选择', prefixIcon: 'fa fa-line-chart' },
                            { code: 'CHART_LINE', name: '折线图 - 固定类别', prefixIcon: 'fa fa-line-chart', params: { category: 'a' } },
                            { code: 'CHART_PIE', name: '饼图 - 自由选择', prefixIcon: 'fa fa-pie-chart' },
                            { code: 'CHART_PIE', name: '饼图 - 固定类别', prefixIcon: 'fa fa-pie-chart', params: { category: 'a' } }
                        ]
                    }
                ]
            ]
        }
    }
}
export const EditConfig = {
    border: true,
    stripe: true,
    resizable: true,
    autoResize: true,
    keepSource: true,
    height: 'auto',
    showOverflow: true,
    showHeaderOverflow: true,
    columnKey: true,
    highlightHoverRow: true,
    size: 'mini',
    align: 'center',
    headerAlign: 'center',
    data: [],
    mouseConfig: {
        area: true, // 是否开启单元格区域选取
        extension: true // 是否开启右下角延伸按钮
    },
    clipConfig: {
        isCut: false,
        isPaste: false
    },
    keyboardConfig: {
        isClip: true, // 是否开启复制粘贴功能
        isEdit: true, // 是否开启单元格选择编辑
        isTab: true, // 是否开启TAB键左右移动功能
        isArrow: true, // 是否开启非编辑状态下，上下左右移动功能
        isEnter: true, // 是否开启回车移动上下行移动
        isDel: true, // 是否开启删除键功能
        isMerge: true, // 是否开合并和取消合并功能
        isFNR: true, // 是否开启查找和替换功能
        isChecked: true, // 是否开启空格键切换复选框和单选框状态
        enterToTab: false // 是否将回车键行为改成 Tab 键行为
    },
    checkboxConfig: {
        highlight: true,
        trigger: 'row'
    },
    editConfig: {
        trigger: 'dblclick',
        mode: 'row',
        showStatus: true
    },
    toolbarConfig: {
        slots: { buttons: 'toolbar_buttons' },
        perfect: true
    },
    menuConfig: {
        body: { 
            options: [
                [
                    { code: 'FILLDOWN_CELL', name: '向下填充', visible: false },
                    { code: 'COPY_CELL', name: '复制 (Ctrl+C)', prefixIcon: 'fa fa-copy' }
                    // { code: 'CLEAR_CELL', name: '清除内容 (Del)' },
                    // { code: 'CUT_CELL', name: '剪贴 (Ctrl+X)', prefixIcon: 'fa fa-cut' },
                    // { code: 'PASTE_CELL', name: '粘贴 (Ctrl+V)', prefixIcon: 'fa fa-paste' }
                ],
                // 引入 echarts 和 vxe-table-plugin-charts 之后可以直接使用，也可以自行实现
                [
                    {
                        name: '创建图表',
                        prefixIcon: 'fa fa-area-chart',
                        children: [
                            { code: 'CHART_BAR_X_AXIS', name: '横向柱状图 - 自由选择', prefixIcon: 'fa fa-bar-chart' },
                            { code: 'CHART_BAR_X_AXIS', name: '横向柱状图 - 固定类别', prefixIcon: 'fa fa-bar-chart', params: { category: 'a' } },
                            { code: 'CHART_BAR_Y_AXIS', name: '纵向柱状图 - 自由选择', prefixIcon: 'fa fa-bar-chart' },
                            { code: 'CHART_BAR_Y_AXIS', name: '纵向柱状图 - 固定类别', prefixIcon: 'fa fa-bar-chart', params: { category: 'a' } },
                            { code: 'CHART_LINE', name: '折线图 - 自由选择', prefixIcon: 'fa fa-line-chart' },
                            { code: 'CHART_LINE', name: '折线图 - 固定类别', prefixIcon: 'fa fa-line-chart', params: { category: 'a' } },
                            { code: 'CHART_PIE', name: '饼图 - 自由选择', prefixIcon: 'fa fa-pie-chart' },
                            { code: 'CHART_PIE', name: '饼图 - 固定类别', prefixIcon: 'fa fa-pie-chart', params: { category: 'a' } }
                        ]
                    }
                ],
                [
                    { code: 'PRINT_ALL', name: '打印', prefixIcon: 'fa fa-print', params: { columns: ['a', 'b', 'c', 'd', 'e'] } },
                    { code: 'EXPORT_ALL', name: '导出', prefixIcon: 'fa fa-download', params: { filename: '导出数据', type: 'csv' } }
                ]
            ]
        }
    }
}