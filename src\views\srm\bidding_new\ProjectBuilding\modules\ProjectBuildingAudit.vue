<template>
  <div class="purchaseSupplierCapacityHead">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="flag"
        :subpackageTitle="subpackageTitle"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        
        v-on="businessHandler"
      >
      </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit
      />
    </a-spin>
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
    v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {  httpAction } from '@/api/manage'
import {getAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'PurchaseSupplierCapacityHead',
    components: {
        BusinessLayout,
        fieldSelectModal,
        flowViewModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            subpackageTitle: '',
            flag: false,
            flowId: 0,
            auditVisible: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            opinion: '',
            flowView: false,
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                detail: { url: '/tender/tenderProjectApprovalHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {

            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.showFlowConditionBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditPass,
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditReject,
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack',
                    click: this.goBackAudit
                }
            ],
            url: {
                save: '/supplierCapacity/purchaseSupplierCapacityHead/edit',
                publish: '/supplierCapacity/purchaseSupplierCapacityHead/publis',
                detail: '/tender/tenderProjectApprovalHead/queryById'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            console.log('this.currentEditRow', this.currentEditRow)
            // this.loading = true
            
            // let templateNumber = this.currentEditRow.templateNumber
            // let templateVersion = this.currentEditRow.templateVersion
            // let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${this.currentEditRow.templateAccount}/purchase_projectApproval_${this.currentEditRow.templateNumber}_${this.currentEditRow.templateVersion}`
        }
    },
    methods: {
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBackAudit()

                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        showFlowConditionBtn ({pageData}) {
            if (pageData.auditStatus == '1') {
                return true
            } else {
                return false
            }
        },
        showFlow (){
            let params = this.paramIntegrate()
            this.flowId = params.flowId
            this.flowView = true
        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        syncShow ({ pageData }) {
            return (pageData.auditStatus === '1')
        },
        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        goBack () {
            this.$emit('hide')
        },
        goBackAudit () {
            this.$parent.hideController()
        }



    },
    created (){
        this.subpackageTitle = this.currentEditRow.subject||''
        getAction(this.url.detail, this.currentEditRow).then(res => {
            if(res.success) {
                // this.$message.success(res.message)
                // this.$refs.listPage.loadData() // 刷新页面
                this.currentEditRow.templateNumber=res.result.templateNumber
                this.currentEditRow.templateVersion=res.result.templateVersion
                this.currentEditRow.templateAccount=res.result.templateAccount
                console.log(res)
                this.flag=true
            }else {
                this.$message.warning(res.message)
            }
        }).finally(() => {
            // this.loading = false
        })
    }
}
</script>