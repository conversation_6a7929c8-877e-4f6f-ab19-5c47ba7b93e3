<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        modelLayout="masterSlave"
        pageStatus="edit"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
      <a-modal
        v-drag    
        v-model="rejectVisible"
        :title="rejectModelTitle"
        :okText="okText"
        @ok="handleOk">
        <a-form-model
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :model="rejectForm">
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_rMyC_478bc282`, '驳回节点')">
            <a-select
              style="width: 100%"
              v-model="rejectForm.node">
              <a-select-option
                v-for="(item, i) in filterNodeList"
                :key="i"
                :value="item.val"
              >
                {{ item.key }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item
            :label="$srmI18n(`${$getLangAccount()}#i18n_field_rMvj_478a05f6`, '驳回理由')"
          >
            <a-textarea
              v-model="rejectForm.reject"
              :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNrMvj_ceb1c7df`, '请输入驳回理由')"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </a-form-model-item>
        </a-form-model>
      </a-modal>

      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk"/>
    </a-spin>
  </div>
</template>

<script lang="jsx">
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import {USER_ELS_ACCOUNT, USER_INFO} from '@/store/mutation-types'

import {BUTTON_BACK, BUTTON_PUBLISH, BUTTON_SAVE} from '@/utils/constant.js'
import moment from 'moment'

export default {
    name: 'PurchaseEightDisciplinesHeadEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    created () {
        // 监听syncRow 事件，D5纠正措施字段改变，D6跟着改变
        this.$root.$on('syncRow', msg => {
            const {groupCode, property, content, id} = msg
            if (groupCode === 'eightDisciplinesFiveList') {
                let syncItemGrid = this.getItemGridRef('eightDisciplinesSixList')
                const tableData = syncItemGrid && syncItemGrid.getTableData().fullData
                tableData.forEach(syncRow => {
                    if(syncRow._X_ID){
                        if (Number(id.split('_')[1]) + 1  === Number(syncRow._X_ID.split('_')[1])) {
                            syncRow[property] = content
                        }
                    }
                })
                syncItemGrid.loadData(tableData)
            }
        })
    },
    data () {
        return {
            labelCol: {span: 4},
            wrapperCol: {span: 15},
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNrMvj_ceb1c7df`, '请输入驳回理由'),
            rejectModelTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIyC_2e6c072a`, '指定节点'),
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'),
            rejectVisible: false,
            refresh: true,
            nodeList: [
                {key: 'D0:问题提出', val: 'D0', status: 0},
                {key: 'D1:小组成立', val: 'D1', status: 1},
                {key: 'D2:问题界定', val: 'D2', status: 2},
                {key: 'D3:围堵措施', val: 'D3', status: 3},
                {key: 'D4:原因分析', val: 'D4', status: 4},
                {key: 'D5:纠正措施', val: 'D5', status: 5},
                {key: 'D6:效果验证', val: 'D6', status: 6},
                {key: 'D7:预防再发生', val: 'D7', status: 7},
                {key: 'D8:结案评价', val: 'D8', status: 8}
            ],
            filterNodeList: [],
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                detail: {
                    url: '/eightReport/purchaseEightDisciplines/queryById', args: (that) => {
                        return {id: that.currentEditRow.id}
                    }
                }
            },
            externalToolBar: {
                eightDisciplinesThreeList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        disabled: true,
                        attrs: {
                            type: 'primary'
                        },
                        show: this.tableBtnShow
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete,
                        show: this.tableBtnShow
                    }
                ],
                eightDisciplinesFiveList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        },
                        show: this.tableBtnShow
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete,
                        show: this.tableBtnShow
                    }
                ],
                eightDisciplinesSixList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        },
                        show: this.tableBtnShow
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete,
                        show: this.tableBtnShow
                    }
                ],
                purchaseAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'eightDisciplines', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联Tab'),
                            multiple: true,
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDeleteAttachment
                    }
                ]
            },
            pageFooterButtons: [
                {
                    ...BUTTON_SAVE,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/eightReport/purchaseEightDisciplines/edit'
                    }
                },
                {
                    ...BUTTON_PUBLISH,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/eightReport/purchaseEightDisciplines/publis'
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'),
                    args: {
                        url: '/eightReport/purchaseEightDisciplines/reject'
                    },
                    attrs: {
                        type: 'danger'
                    },
                    key: 'reject',
                    show: this.showReject,
                    click: this.reject
                },
                {
                    ...BUTTON_BACK,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回')
                }
                
            ],
            url: {
                save: '/eightReport/purchaseEightDisciplines/edit',
                publish: '/eightReport/purchaseEightDisciplines/publis',
                reject: '/eightReport/purchaseEightDisciplines/reject'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_eightDisciplines_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        attrHandle (){
            return {
                'sourceNumber': this.currentEditRow.eightDisciplinesNumber,
                'actionRoutePath': '/srm/eightReport/purchase/PurchaseEightDisciplinesHeadList'
            }
        },
        tableBtnShow ({pageConfig}) {
            if ('D0' == pageConfig.groups[0].formModel.eightDisciplinesStatus) {
                return false
            } else {
                let toElsAccount = pageConfig.groups[0].formModel.toElsAccount
                let loginElsAccount = this.$ls.get('Login_elsAccount')
                if (toElsAccount != '' && toElsAccount != loginElsAccount) {
                    return false
                }
                return true

            }
        },
        // 业务需要重写表格删除
        businessGridDelete ({pageConfig, groupCode}) {
            if ('D0' == pageConfig.groups[0].formModel.eightDisciplinesStatus) {
                return false
            }
            let itemGrid = this.getItemGridRef(groupCode)
            let syncItemGrid = ''
            if (groupCode === 'eightDisciplinesFiveList') {
                syncItemGrid = this.getItemGridRef('eightDisciplinesSixList')
            }
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            let delArr = []
            itemGrid.removeCheckboxRow().then(() => {
                const tableData = syncItemGrid && syncItemGrid.getTableData().fullData
                console.log(tableData, 'tableData')
                if (tableData && tableData.length > 0) {
                    checkboxRecords.forEach(row => {
                        tableData.forEach(syncRow => {
                            if (Number(row.id.split('_')[1]) + 1 === Number(syncRow.id.split('_')[1])) {
                                delArr.push(syncRow)
                            }
                        })
                    })
                }
                syncItemGrid && syncItemGrid.remove(delArr)
            })
        },


        businessGridDeleteAttachment ({pageConfig, groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let arr = []
            let delArr = []
            //大B
            let user = this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')

            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            if (checkboxRecords && checkboxRecords.length > 0) {
                checkboxRecords.forEach(row => {
                    //当前登录人等账号于查询的账号 大B
                    if (user == row.uploadElsAccount) {
                        if (subAccount == row.uploadSubAccount) {
                            delArr.push(row)
                        } else {
                            arr.push(row.fileName)
                        }
                    } else {
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if (arr && arr.length > 0) {
                    let str = arr.join(',')
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') + str + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }

            itemGrid.removeCheckboxRow().then(() => {
                const tableData = itemGrid && itemGrid.getTableData().fullData
                console.log(tableData, 'tableData')
                if (tableData && tableData.length > 0) {
                    checkboxRecords.forEach((row) => {
                        if (user == row.uploadElsAccount) {
                            delArr.push(row)
                        } else {
                            arr.push(row.id)
                        }
                    })
                }
            })
        },

        // 业务需要重写表格新增
        businessGridAdd ({pageConfig, groupCode}) {
            if ('D0' == pageConfig.groups[0].formModel.eightDisciplinesStatus) {
                return true
            }
            console.log(pageConfig)
            console.log(groupCode, '===')
            let itemGrid = this.getItemGridRef(groupCode)
            let syncItemGrid = ''
            if (groupCode === 'eightDisciplinesFiveList') {
                syncItemGrid = this.getItemGridRef('eightDisciplinesSixList')
            }
            let {columns = []} = pageConfig.groups.find(n => n.groupCode === groupCode)
            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {

                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            itemGrid.insertAt([row], -1)
            syncItemGrid && syncItemGrid.insertAt([row], -1)
        },

        showPublish ({pageConfig}) {
            if (!pageConfig.groups[0].formModel.id) {
                return false
            }
            return true
        },
        showReject ({pageConfig}) {
            return pageConfig.groups[0].formModel.eightDisciplinesStatus !== 'D0'
        },
        initTeamGridData () {
            let itemTeamGrid = this.$refs.businessRef.$refs.eightDisciplinesTeamListgrid[0].$refs.eightDisciplinesTeamList
            let itemTeamDataList = itemTeamGrid.getTableData().fullData

            if (itemTeamDataList.length == 0) {
                for (let i = 1; i <= 8; i++) {
                    let item2 = {
                        'elsAccount': this.$ls.get('Login_elsAccount'),
                        'toElsAccount': this.$ls.get('Login_elsAccount'),
                        'subAccount': this.$ls.get('Login_subAccount'),
                        'phone': this.$ls.get(USER_INFO).phone,
                        'mail': this.$ls.get(USER_INFO).email,
                        'name': this.$ls.get(USER_INFO).realname,
                        'itemNumber': '',
                        'reportFlowStep': 'D' + i,
                        'teamRole': 'member'
                    }
                    itemTeamGrid.insertAt(item2, -1)
                }

            }
        },
        // 按钮显示
        // 绑定同步方法示例
        // 可选入参 { Vue, pageConfig, btn, pageData, groupCode }
        syncShow ({pageData}) {
            console.log('pageData.createBy', (pageData.createBy))
            return (pageData.createBy === '307000')
        },
        // 按钮显示
        // 绑定同步方法示例
        // 可选入参 { Vue, pageConfig, btn, pageData, groupCode }
        asyncShow () {
            return getAction('/eightReport/purchaseEightDisciplines/queryById', {id: '*********0946163714'})
                .then(res => {
                    let flag = res.result.id !== '*********0946163714'
                    console.log('flag', flag)
                    if (flag) {
                        return window.Promise.resolve(res)
                    } else {
                        return window.Promise.reject(res)
                    }
                })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                {
                                    key: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    click: this.downloadEvent
                                },
                                {
                                    key: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    click: this.preViewEvent
                                },
                                {
                                    type: 'delete',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteFile
                                }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联Tab'),
                        fieldLabelI18nKey: '',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'select',
                        defaultValue: '',
                        dictCode: 'SRMEightAttachmentRelationTab',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: {
                            default: ({row}) => {
                                return [
                                    <span>{moment(row.uploadTime).format('YYYY-MM-DD HH:mm:ss')}</span>
                                ]
                            }
                        }
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        field: 'uploadElsAccount',
                        fieldLabelI18nKey: '',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '120'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: {default: 'grid_opration'}
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            let eightDisciplinesTeamList = resultData.eightDisciplinesTeamList
            const elsAccount = this.$ls.get(USER_ELS_ACCOUNT)


            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
            this.externalToolBar['purchaseAttachmentList'][0].args.headId = resultData.id || ''
            let itemInfo = pageConfig.groups
                .map(n => ({
                    label: this.$srmI18n(`${this.$getLangAccount()}#${n.groupNameI18nKey}`, n.groupName),
                    value: n.groupCode
                }))
            this.externalToolBar['purchaseAttachmentList'][0].args.itemInfo = itemInfo

            // console.log("pageConfigpageConfigpageConfig",pageConfig)
            this.getBusinessExtendData(this.businessRefName).pageConfig = pageConfig
            this.$nextTick(() => {
                this.initTeamGridData()
                for (let [idx, group] of pageConfig.groups.entries()) {

                    if (group.groupCode != 'baseForm' && group.groupCode != 'eightDisciplinesTeamList' && group.groupCode != 'purchaseAttachmentList') {
                        console.log('idx', idx)
                        if (elsAccount != eightDisciplinesTeamList[idx]?.toElsAccount) {
                            for (let i of group.columns) {
                                i.disabled = true
                            }

                        }

                    }
                }
            })
        },
        preViewEvent (Vue, row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        },
        // 批量删除
        deleteFile (Vue, row) {
            console.log(Vue, row)
            const fileGrid = Vue.$refs.purchaseAttachmentList
            const id = row.id
            const params = {
                id
            }
            //大B
            let user = this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            //如果删除的数据有和登录人账号不一致的
            if(user != row.uploadElsAccount || (user == row.uploadElsAccount && subAccount != row.uploadSubAccount)){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') + row.fileName + this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                return
            }
            getAction('/attachment/purchaseAttachment/delete', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        downloadEvent (Vue, row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        uploadCallBack (result, ref) {
            let fileGrid = this.getItemGridRef(ref)
            fileGrid.insertAt(result, -1)
        },
        businessGridAddInModal ({Vue, pageConfig, btn, groupCode}) {
            this.curGroupCode = groupCode
            console.log('groupCode', groupCode)
            this.fieldSelectType = 'material'
            let url = '/material/purchaseMaterialHead/list'
            let columns = [
                {
                    field: 'materialNumber',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'),
                    width: 150
                },
                {
                    field: 'materialDesc',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                    width: 150
                },
                {
                    field: 'materialName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'),
                    width: 200
                },
                {
                    field: 'materialSpec',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),
                    width: 200
                }
            ]
            this.$refs.fieldSelectModal.open(url, {}, columns, 'multiple')
        },
        fieldSelectOk (data) {
            if (this.fieldSelectType === 'material') {

                let itemGrid = this.getItemGridRef(this.curGroupCode)

                let {fullData} = itemGrid.getTableData()

                // 过滤表格已有数据
                let filterData = data.filter(item => {
                    return !fullData.some(n => n.materialNumber === item.materialNumber)
                })

                const {pageConfig = {}} = this.getBusinessExtendData(this.businessRefName)
                let {columns = []} = pageConfig.groups.find(n => n.groupCode === this.curGroupCode)
                debugger
                // 获取默认表格行行配置
                let row = columns
                    .filter(n => n.field)
                    .reduce((acc, obj) => {
                        acc[obj.field] = obj.defaultValue || ''
                        return acc
                    }, {})
                let itemDatas = filterData.map(item => {
                    let anoRow = Object.assign({}, row, {
                        itemNumber: fullData.length + 1
                    })
                    return anoRow
                })

                itemGrid.insertAt(itemDatas, -1)
            }
        },
        reject () {
            let eightReportStatus = this.getAllData().eightDisciplinesStatus
            if (eightReportStatus == 'D0') return
            let index = this.nodeList.findIndex(item => item.val == eightReportStatus)
            this.rejectForm.node = this.nodeList[index - 1].val
            this.filterNodeList = this.nodeList.filter((item, idx) => idx < index)
            this.rejectVisible = true
        },
        handleOk () {
            const that = this
            let pageAllData = this.getAllData()
            //驳回理由
            pageAllData.rejectReason = this.rejectForm.reject
            //退回到的节点
            pageAllData.fbk1 = this.rejectForm.node
            postAction(this.url.reject, pageAllData).then(res => {
                if (res.success) {
                    that.$message.success(res.message)
                } else {
                    that.$message.warning(res.message)
                }
                that.stepBusinessRefresh()
            })
            this.rejectVisible = false
        }
    }
}
</script>
