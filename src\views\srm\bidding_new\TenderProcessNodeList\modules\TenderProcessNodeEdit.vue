<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        modelLayout="masterSlave"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'

export default {
    name: 'PurchaseSupplierCapacityHead',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            showNode: false,
            requestData: {
                detail: {
                    url: '/tender/tenderProcessNode/queryById',
                    args: that => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {},
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/tenderProcessNode/edit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save',
                    showMessage: true,
                    click: this.handleSave,
                    handleBefore: this.handleSaveBefore,
                    handleAfter: this.handleSaveAfter
                },
                // {
                //     title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                //     args: {
                //         url: '/tender/tenderProcessNode/publish'
                //     },
                //     attrs: {
                //         type: 'primary'
                //     },
                //     show: this.showPublish,
                //     key: 'publish'
                // },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            nodeData: {},
            groupCode: '',
            periodTypeInfoArray: {},
            allNodeMap: {},
            url: {
                save: '/tender/tenderProcessNode/edit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_tenderProcessNode_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        showPublish ({ pageConfig }) {
            if (!pageConfig.groups[0].formModel.id) {
                return false
            }
            return true
        },
        fieldSelectOk (data) {
            console.log(data)
        }
    }
}
</script>
