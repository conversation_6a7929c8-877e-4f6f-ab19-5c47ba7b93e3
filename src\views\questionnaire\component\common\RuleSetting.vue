<template>
  <a-form-item
    :label="$srmI18n(`${$getLangAccount()}#i18n_title_verificationRules`, '校验规则')"
  >
    <a-checkbox
      :checked="required"
      @change="onRequired">
      
      {{ $srmI18n(`${$getLangAccount()}#i18n_title_required`, '必填') }}
    </a-checkbox>
  </a-form-item>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { findIndexWithKey } from '../../utils/core'
import { deepClone } from '../../utils'

function getDefaultData () {
    return {
        required: false,
        minSign: false,
        min: 0,
        maxSign: false,
        max: 10,
        lenSign: false,
        len: 10,
        requiredMsg: '',
        rangeMsg: '',
        lenMsg: ''
    }
}
export default {
    name: 'RuleSetting',
    data () {
        return {
            ...(getDefaultData()),
            visible: false,
            activeArr: [],
            cusPatterns: []
        }
    },
    computed: {
        ...mapState({
            formData: state => state.formDesigner.formData,
            activeKey: state => state.formDesigner.activeKey,
            patterns: state => state.formDesigner.patterns
        })
    },
    watch: {
        min () {
            this.$nextTick(this.updateRangeMsg)
        },
        max () {
            this.$nextTick(this.updateRangeMsg)
        },
        len () {
            this.$nextTick(this.updateLenMsg)
        },
        activeKey (val) {
            this.changeFormItem(val)
            this.updateRules()
        }
    },
    created () {
        this.changeFormItem(this.activeKey)
        this.updateRules()
    },
    methods: {
        changeFormItem (val) {
            const defaultData = getDefaultData()
            const activeArr = findIndexWithKey(this.formData, val)
            let rules = []
            if (activeArr.length === 1) {
                rules = this.formData[activeArr[0]].attr.rules
            }
            if (activeArr.length === 3) {
                rules = this.formData[activeArr[0]].columns[activeArr[1]].children[activeArr[2]].attr.rules
            }
            for (let i = 0; i < rules.length; i++) {
                const keyArr = Object.keys(rules[i])
                if (keyArr.includes('min') && keyArr.includes('max')) {
                    defaultData.maxSign = true
                    defaultData.minSign = true
                    defaultData.min = rules[i].min
                    defaultData.max = rules[i].max
                    defaultData.rangeMsg = rules[i].message
                } else if (keyArr.includes('min')) {
                    defaultData.minSign = true
                    defaultData.min = rules[i].min
                    defaultData.rangeMsg = rules[i].message
                } else if (keyArr.includes('max')) {
                    defaultData.maxSign = true
                    defaultData.max = rules[i].min
                    defaultData.rangeMsg = rules[i].message
                } else if (keyArr.includes('required') && rules[i].required) {
                    defaultData.required = true
                    defaultData.requiredMsg = rules[i].message
                } else if (keyArr.includes('len')) {
                    defaultData.lenSign = true
                    defaultData.len = rules[i].len
                    defaultData.lenMsg = rules[i].message
                }
            }
            const dataArr = Object.keys(defaultData)
            for (let i = 0; i < dataArr.length; i++) {
                this[dataArr[i]] = defaultData[dataArr[i]]
            }
            this.activeArr = activeArr
        },
        onRequired (e) {
            const checked = e.target.checked
            this.required = checked
            if (checked) {
                this.requiredMsg = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_requiredMsg`, '此项为必填项')
            } else {
                this.requiredMsg = ''
            }
            this.$nextTick(this.transformRulesData)
        },
        transformRulesData () {
            const rules = []
            if (this.required) {
                rules.push({
                    required: true,
                    message: this.requiredMsg
                })
            }
            if (this.minSign && this.maxSign) {
                rules.push({
                    min: this.min, max: this.max, message: this.rangeMsg
                })
            } else if (this.minSign) {
                rules.push({
                    min: this.min, message: this.rangeMsg
                })
            } else if (this.maxSign) {
                rules.push({
                    max: this.max, message: this.rangeMsg
                })
            }
            if (this.lenSign) {
                rules.push({
                    len: this.len, message: this.lenMsg
                })
            }
            const formData = this.setRules(rules)
            console.log(formData)
            this.updateFormData(formData)
        },
        getRules () {
            const formData = deepClone(this.formData)
            const activeArr = this.activeArr
            if (this.activeArr.length === 1) {
                return formData[activeArr[0]].attr.rules
            } else if (this.activeArr.length === 3) {
                return formData[activeArr[0]].columns[activeArr[1]].children[activeArr[2]].attr.rules
            } else {
                return []
            }
        },
        setRules (rules) {
            const formData = deepClone(this.formData)
            const activeArr = this.activeArr
            if (activeArr.length === 1) {
                formData[activeArr[0]].attr.rules = rules
            }
            if (activeArr.length === 3) {
                formData[activeArr[0]].columns[activeArr[1]].children[activeArr[2]].attr.rules = rules
            }
            return formData
        },
        updateRules () {
            const rules = this.getRules()
            const cusPatterns = this.patterns.map(item => {
                item.checked = false
                rules.forEach(it => {
                    if (it.pattern && item.name === it.pattern) {
                        item.checked = true
                    }
                })
                return item
            })
            this.cusPatterns = cusPatterns
        },
        ...mapMutations({
            updateFormData: 'setFormData'
        })
    }
}
</script>

<style lang="less" scoped>

</style>
