<template>
  <div class="payment-records-view">
    <ContentHeaderNew :btns="btns"></ContentHeaderNew>
    <div>
      <a-tabs
        default-active-key="1"
        @change="callback">
        <a-tab-pane
          key="1"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_menu_AvCK_263c5496`, '其他方式')">
          <OtherMethodsView />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script>
import ContentHeaderNew from '../components/content-header-new'
import OtherMethodsView from './modules/OtherMethodsView'

export default {
    components: {
        ContentHeaderNew,
        OtherMethodsView
    },
    data () {
        return {
            btns: [{title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: () => {this.$router.go(-1)}}]
        }
    },
    methods: {
        callback (key) {
            console.log(key)
        }
    }
}
</script>

<style lang="less" scoped>
.payment-records-view {
  background-color: #fff;
  height: 100%;
}
</style>