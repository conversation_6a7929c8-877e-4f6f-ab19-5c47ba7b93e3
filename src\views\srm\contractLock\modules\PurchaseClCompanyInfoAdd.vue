<template>
  <div class="page-container">
    <edit-layout
      ref="addPage"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction } from '@/api/manage'
export default {
    name: 'ClCompanyInfoAdd',
    mixins: [EditMixin],
    data () {
        return {
            selectType: 'esignEnterpriseCertification',
            pageData: {
                form: {
                    loadingCompany: '0',
                    companyCode: '',
                    companyName: '',
                    subAccount: '',
                    accountId: '',
                    legalPerson: '',
                    applyUserName: '',
                    applyContact: '',
                    applyContactType: '',
                    registerNo: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_64dEMQeAfsqvfsQ4`, '企业认证信息维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_loadingCompany`, '是否加载公司列表'),
                                    fieldName: 'loadingCompany',
                                    dictCode: 'yn',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_loadingCompany`, '是否加载公司列表'),
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '0')
                                            setDisabledByProp('companyCode', flag)
                                            setDisabledByProp('companyName', !flag)
                                        }else{
                                            setDisabledByProp('companyCode', true)
                                            setDisabledByProp('companyName', true)
                                        }
                                    },
                                    required: '1'

                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码'),
                                    fieldName: 'companyCode',
                                    disabled: true,
                                    dictCode: 'purchase_organization_info,org_name,org_code,org_category_code="companyCode"',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称'),
                                    fieldName: 'companyName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    fieldName: 'applyContactType',
                                    dictCode: 'contractLockContactType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    fieldName: 'subAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.form.subAccount = data[0].subAccount
                                        Vue.form.applyUserName = data[0].realname
                                        Vue.form.applyContact = data[0].phone
                                        if(Vue.form.applyContactType === 'EMAIL') {
                                            Vue.form.applyContact = data[0].email
                                        }
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150},
                                            {field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150},
                                            {field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_phone`, '手机号'), with: 150},
                                            {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'), with: 150}
                                        ], modalUrl: '/account/elsSubAccount/list', modalParams: {status: 1}
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVNcR_b51847bb`, '申请者姓名'),
                                    fieldName: 'applyUserName',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCK_2babc4f3`, '申请人联系方式'),
                                    fieldName: 'applyContact',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCK_2babc4f3`, '申请人联系方式'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCRXdiy_c6d93ea6`, '公司工商注册号'),
                                    fieldName: 'registerNo'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RChLcR_c2c6edeb`, '公司法人姓名'),
                                    fieldName: 'legalPerson'
                                }
                            ],
                            validateRules: {
                                loadingCompany: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BFQiKPrmHbGTqCS8`, '是否加载公司列表不能为空')}],
                                subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LieyxOLV_542509fe`, '认证账号不能为空')}],
                                applyContact: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCKxOLV_5af31783`, '申请人联系方式不能为空')}],
                                applyContactType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCKAcxOLV_5d30b933`, '申请人联系方式类型不能为空')}],
                                applyUserName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVNcRxOLV_8150f64b`, '申请者姓名不能为空')}]
                            }
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), type: 'primary', click: this.submitCertificationEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/contractLock/purchaseCLCompanyInfo/add',
                auth: '/contractLock/purchaseCLCompanyInfo/submitCertification'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
        },
        prevEvent () {
            this.$refs.addPage.prevStep()
        },
        nextEvent () {
            this.$refs.addPage.nextStep()
        },
        saveEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    if(params.loadingCompany==='0' && !params.companyName){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCRLxOLV_3f6f291f`, '公司名称不能为空'))
                        return
                    }
                    if(params.loadingCompany==='1' && !params.companyCode){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCooxOLV_5449119a`, '公司代码不能为空'))
                        return
                    }
                    let url = this.url.add
                    const _this = this
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            _this.$parent.addCallBack(res.result)
                        }
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        submitCertificationEvent () {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.addPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.addPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.addPage.getPageData()
                    if(params.loadingCompany==='0' && !params.companyName){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCRLxOLV_3f6f291f`, '公司名称不能为空'))
                        return
                    }
                    if(params.loadingCompany==='1' && !params.companyCode){
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCooxOLV_5449119a`, '公司代码不能为空'))
                        return
                    }
                    let url = this.url.auth
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        window.open(res.result.certificationPageUrl)
                    }).finally(() => {
                        this.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>