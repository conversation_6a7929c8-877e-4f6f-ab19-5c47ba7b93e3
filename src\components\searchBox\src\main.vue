<template>
  <div class="SearchBox">
    <div class="query">
      <div class="query-wrap">
        <a-form-model
          ref="queryForm"
          :model="form"
          @keyup.enter.native="handleQuery"
          v-bind="layout">
          <a-row
            type="flex"
            :gutter="12">
            <template v-for="(item, i) in computedFormFields">
              <a-col
                v-if="i === 2"
                :key="`field_${i}`"
                :span="8">
                <div class="search-btn-groups">
                  <a-button-group>
                    <a-button
                      type="primary"
                      icon="search"
                      @click="handleQuery">{{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}</a-button>
                    <a-button
                      @click="superQuery"
                      v-if="superQueryShow"><span>{{ $srmI18n(`${$getLangAccount()}#i18n_dict_xt_133ecf`, '高级') }}<a-icon type="double-right" /></span></a-button>
                  </a-button-group>

                  <a-button
                    type="default"
                    icon="reload"
                    style="margin-right: 8px;"
                    @click="handleReset">{{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}</a-button>
                  <a-tooltip
                    v-if="showGridLayoutBtn"
                    :title="isAggregate ? $srmI18n(`${$getLangAccount()}#i18n_dict_ULKP_2d2f6d1f`, '平铺视图') : $srmI18n(`${$getLangAccount()}#i18n_dict_FnKP_3ba4e166`, '聚合视图') ">
                    <a-button
                      class="search-btn"
                      type="default"
                      :icon="isAggregate ? 'fullscreen' : 'fullscreen-exit'"
                      @click="toggleGridLayout"
                    />
                  </a-tooltip>
                  <slot name="query_extra"></slot>
                </div>
              </a-col>
              <a-col
                v-else
                :key="`field_${i}`"
                :span="8">
                <template v-if="item.type">
                  <a-form-model-item
                    :prop="item.fieldName"
                  >
                    <template #label>
                      <a-tooltip
                        placement="bottomLeft"
                        :title="item.label">
                        {{ item.label }}
                      </a-tooltip>
                    </template>
                    <template v-if="item.type === 'select'">
                      <m-select
                        v-model="form[item.fieldName]"
                        :dict-code="item.dictCode"
                        :placeholder="item.placeholder"
                      />
                    </template>

                    <template v-else-if="item.type === 'datepicker'">
                      <a-date-picker
                        v-model="form[item.fieldName]"
                        :placeholder="item.placeholder"
                        :valueFormat="(item.dataFormat || 'YYYY-MM-DD')"
                      />
                    </template>

                    <!-- 时间范围 -->
                    <template v-else-if="item.type === 'dateRange'">
                      <a-range-picker
                        :value="dateRange"
                        :placeholder="item.placeholder"
                        :valueFormat="(item.dataFormat || 'YYYY-MM-DD')"
                        @change="(dates) => handleDateRangePickerChange(dates, item)"
                      />
                    </template>

                    <template v-else-if="item.type === 'treeSelect'">
                      <m-tree-select
                        v-model="form[item.fieldName]"
                        allowClear
                        :disabled="item.disabled"
                        :multiple="item.extend && item.extend.multiple || false"
                        :maxTagCount="item.extend && item.extend.maxTagCount || 1"
                        :sourceUrl="item.sourceUrl"
                      />
                    </template>

                    <template v-else-if="item.type === 'radio'">
                      <m-radio
                        v-model="form[item.fieldName]"
                        :dictCode="item.dictCode"
                      />
                    </template>

                    <template v-else-if="item.type === 'checkbox'">
                      <m-checkbox
                        v-model="form[item.fieldName]"
                        :options="item.options || []"
                        :dictCode="item.dictCode"
                      />
                    </template>

                    <template v-else>
                      <a-input
                        v-model="form[item.fieldName]"
                        :placeholder="item.placeholder"
                      />
                    </template>
                  </a-form-model-item>
                </template>
              </a-col>
            </template>
          </a-row>
        </a-form-model>

        <search-extend
          :fieldList="fieldList"
          ref="superQueryModal"
          :tableCode="url.columns"
          v-on="$listeners"
        ></search-extend>

      </div>
    </div>
  </div>
</template>

<script>
import {cloneDeep} from 'lodash'
import SearchExtend from '@/components/template/list/components/searchExtend.vue'

export default {
    name: 'SearchBox',
    components: {
        'search-extend': SearchExtend
    },
    props: {
        formField: {
            type: Array,
            default () {
                return []
            }
        },
        url: {
            type: Object,
            default () {
                return {}
            }
        },
        superQueryShow: {
            type: Boolean,
            default: true
        },
        showGridLayoutBtn: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        // 计算适配搜索框内容
        // 不足时填满三列
        computedFormFields () {
            let formField = this.formField || []
            let arr = cloneDeep(formField)
            let length = formField.length
            let fllArr = []
            if (length < 3) {
                fllArr = new Array(3 - length).fill({})
                arr = arr.concat(fllArr)
            } else {
                arr.splice(2, 0, {})
            }
            return arr
        }
    },
    data () {
        return {
            layout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 16 }
            },
            fieldList: [],
            isAggregate: false, // 是否聚合视图
            dateRange: [],
            form: {}
        }
    },
    methods: {
        // 初始化表单
        getProperty (arr) {
            arr.forEach(n => {
                if (n.type === 'dateRange') {
                    let beginTime = n.startProp || 'beginTime'
                    let endTime = n.endProp || 'endTime'
                    this.form[beginTime] = ''
                    this.form[endTime] = ''
                } else {
                    let prop = n.fieldName
                    if (prop) {
                        this.form[prop] = ''
                    }
                }
            })
            console.log('this.form :>> ', this.form)
        },
        toggleGridLayout () {
            this.isAggregate = !this.isAggregate
        },
        handleDateRangePickerChange (dates, item) {
            this.dateRange = dates
            let beginTime = item.startProp || 'beginTime'
            let endTime = item.endProp || 'endTime'
            this.form[beginTime] = dates[0] || ''
            this.form[endTime] = dates[1] || ''
        },
        handleQuery () {
            this.$emit('query', { form: Object.assign({}, this.form) })
        },
        handleReset () {
            Object.assign(this.$data, this.$options.data.call(this))
            this.$refs.superQueryModal.queryParamsModel= [{logicSymbol: 'like'}]
            this.$emit('reset')
        },
        superQuery () {
            let flag = !this.$refs.superQueryModal.visible
            this.$refs.superQueryModal.show(flag)
        }
    }
}
</script>

<style lang="less" scoped>
.SearchBox {
    .query {
        padding-top: 6px;
        padding-right: 6px;
        padding-left: 6px;
        padding-bottom: 6px;
        min-height: 66px;
        .query-wrap {
            position: relative;
            border: solid 1px #e8eaec;
            border-radius: 4px;
            background-color: #fff;
        }

        :deep(.ant-form ){
            min-width: 860px !important;
            padding: 6px 15px;
        }
        :deep(.ant-form-item){
            margin-bottom: 0px;
        }
        :deep(.ant-form-item-label){
            // text-align: left;
            text-overflow: ellipsis;
        }
    }
    .search-btn-groups {
        text-align: right;
        padding-top: 4px;
        // min-width: 350px;
        :deep(.ant-btn-group){
            margin-right: 8px;
            .ant-btn {
                margin-left: 0px;
            }
        }
    }
}
</style>
