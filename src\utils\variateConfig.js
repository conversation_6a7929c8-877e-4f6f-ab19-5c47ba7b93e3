
const domainURL = `${window.location.origin}/els`
let configTemplateUrl = ''
if (process.env && process.env.VUE_APP_CONFIG_TEMPALTE_URL) {
    configTemplateUrl = process.env.VUE_APP_CONFIG_TEMPALTE_URL
} else {
    configTemplateUrl = 'https://test-srm.gzr.com.cn:8789'
}
// configTemplateUrl = 'https://v5sit-micro.51qqt.com'
export const variateConfig = {
    domainURL,
    casPrefixUrl: 'http://cas.example.org:8443/cas',
    configFiles: `${configTemplateUrl}/opt/upFiles/js`,
    configUpFiles: `${configTemplateUrl}/opt/upFiles`,
    imgDomainURL: `${domainURL}/sys/common/view`,
    downloadUrl: `${domainURL}/sys/common/download`,
    pdfDomainURL: `${domainURL}/sys/common/pdf/pdfPreviewIframe`,
    report: 'http://localhost:8085'
}