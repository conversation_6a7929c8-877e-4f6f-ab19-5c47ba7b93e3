<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showDetailPage && ! showEditPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <BiddingEvaluationRegulationEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/> 
    <!-- 详情界面 -->
    <BiddingEvaluationRegulationDetail 
      v-if="showDetailPage"
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>
</template>
<script>
import BiddingEvaluationRegulationDetail from './modules/BiddingEvaluationRegulationDetail'
import BiddingEvaluationRegulationEdit from './modules/BiddingEvaluationRegulationEdit'
import {ListMixin} from '@comp/template/list/ListMixin'
export default {
    mixins: [ListMixin],
    components: {
        BiddingEvaluationRegulationDetail,
        BiddingEvaluationRegulationEdit
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'biddingRegulation',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_TvAyWRL_8e33baa7`, '条例编号/名称')
                    }
                ],
                form: {
                    keyWord: '',
                    name: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'bidding#biddingEvaluationRegulation:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'bidding#biddingEvaluationRegulation:queryById'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, authorityCode: 'bidding#biddingEvaluationRegulation:edit'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDelete, authorityCode: 'bidding#biddingEvaluationRegulation:delete'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                add: '/bidding/biddingEvaluationRegulation/add',
                list: '/bidding/biddingEvaluationRegulation/list',
                delete: '/bidding/biddingEvaluationRegulation/delete',
                columns: 'BiddingEvaluationRegulation'
            }
        }
    },
    methods: {
        allowEdit (row){
            if(row.used==='1'){
                return true
            }
            return false
        },
        allowDelete (row){
            if(row.used==='1'){
                return true
            }
            return false
        }
    }
}
</script>