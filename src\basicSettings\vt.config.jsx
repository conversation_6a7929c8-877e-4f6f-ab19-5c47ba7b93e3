/*
 * @Author: your name
 * @Date: 2021-03-29 14:02:20
 * @LastEditTime: 2022-03-21 17:19:13
 * @LastEditors: LokNum
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\vt.config.js
 */
import VXETable from 'vxe-table'
const renderer = {
    // 文本域渲染组件
    'srmTextArea': {
        // 可编辑激活模板
        renderEdit (h, renderOpts, { row, column }) {
            let props = renderOpts.props || {}
            return [
                <a-textarea disabled={ props.disabled } v-model={ row[column.property] } auto-size></a-textarea>
            ]
        },
        // 显示模板
        renderCell (h, renderOpts, { row, column }) {
            return [
                <span>{ row[column.property] }</span>
            ]
        }
    },
    // 日期渲染组件
    'mDatePicker': {
        // 可编辑激活模板
        renderEdit (h, renderOpts, { row, column }) {
            let props = renderOpts.props || {}
            let event = renderOpts.events || {}
            return [
                <a-date-picker
                    disabledDate= {props.disabledDate}
                    placeholder=""
                    valueFormat={props.valueFormat || 'YYYY-MM-DD'}
                    onChange={()=> event.change && event.change(row, row[column.property])}
                    showTime={ props.showTime || false}
                    v-model={ row[column.property] } />
            ]
        },
        // 可编辑显示模板
        renderCell (h, renderOpts, { row, column }) {
            return [
                <span>{ row[column.property] }</span>
            ]
        }
    },
    // 自定义开关组件
    'mSwitch': {
        // 可编辑激活模板
        renderEdit (h, renderOpts, { row, column }) {
            let props = renderOpts.props || {}
            let event = renderOpts.events || {}
            return [
                <m-switch 
                    disabled={ props.disabled } 
                    closeValue={ props.closeValue }
                    openValue={ props.openValue }
                    onChange={()=> event.change && event.change(row, row[column.property])}
                    checkedChildren={ props.checkedChildren }
                    unCheckedChildren={ props.unCheckedChildren }
                    v-model={ row[column.property] } />
            ]
        },
        // 可编辑显示模板
        renderCell (h, renderOpts, { row, column }) {
            let props = renderOpts.props || {}
            let event = renderOpts.events || {}
            return [
                <m-switch 
                    disabled={ props.disabled } 
                    closeValue={ props.closeValue }
                    openValue={ props.openValue }
                    onChange={()=> event.change && event.change(row, row[column.property])}
                    checkedChildren={ props.checkedChildren }
                    unCheckedChildren={ props.unCheckedChildren }
                    v-model={ row[column.property] } />
            ]
            // return [
            //     <span>{ row[column.property] }</span>
            // ]
        }
    },
    // 单选下拉组件，兼容弹窗嵌入表格下拉的问题
    'srmSelect': {
        // 可编辑激活模板
        renderEdit (h, renderOpts, { row, column }) {
            let props = renderOpts.props || {}
            let options = renderOpts.options || []
            let event = renderOpts.events || {}
            function filterOption (input, option) {
                if (props.filterOption && typeof props.filterOption == 'function') { // 自定义function
                    props.filterOption(input, option)
                } else { // 走默认过滤
                    return (
                        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    )
                }
            }
            return [
                (<a-select
                    vModel= {row[column.property]}
                    allowClear={true}
                    showSearch={props.showSearch || false}
                    filterOption={ filterOption }
                    disabled={ props.disabled }
                    onChange={()=> event.change && event.change(row, row[column.property])}
                    placeholder='请选择'
                >
                    {
                        options.map((item)=> {
                            return (
                                <a-select-option value= {item.value}>
                                    { item.label }
                                </a-select-option>
                            )
                        })
                    }
                </a-select>)
            ]
        },
        // 可编辑显示模板
        renderCell (h, renderOpts, { row, column }) {
            let options = renderOpts.options || []
            let currentText = null
            if (options && options.length) {
                let filterOpt = options.filter(opt=> {
                    return opt.value === row[column.property] 
                })
                if (filterOpt && filterOpt.length) {
                    currentText = filterOpt[0].label
                }
            } else {
                let text = column.property + '_dictText'
                currentText = row[text] ||  row[column.property]
            }
            return [
                <span>{ currentText }</span>
            ]
        }
    },
    // 自定义下拉，可以单选、多选、输入框，使用页面为 现场考察页面
    'customSelect': {
        // 可编辑激活模板
        renderEdit (h, renderOpts, { row, column }) {
            console.log(renderOpts)
            console.log(row)
            let $element = ''
            let props = renderOpts.props || {}
            let options = renderOpts.options || []
            let rowOptions = row.itemDetailList || []
            renderOpts.options = rowOptions
            let event = renderOpts.events || {}
            const curRowAndCol = {
                row,
                column
            }

            const $seletBox = (<a-select
                vModel= {row[column.property]}
                allowClear={true}
                disabled={ props.disabled }
                onChange={()=> event.change(curRowAndCol, row[column.property])}
                placeholder='请选择'
            >
                {
                    rowOptions.map((item)=> {
                        return (
                            <a-select-option value= {item.optionType}>
                                { item.optionName }
                            </a-select-option>
                        )
                    })
                }
            </a-select>)
            const $seletMultipleBox = (<a-select
                vModel= {row[column.property]}
                mode="multiple"
                allowClear={true}
                disabled={ props.disabled }
                onChange={()=> event.change(curRowAndCol, row[column.property])}
                placeholder='请选择'
            >
                {
                    rowOptions.map((item)=> {
                        return (
                            <a-select-option value= {item.optionType}>
                                { item.optionName }
                            </a-select-option>
                        )
                    })
                }
            </a-select>)
            let $inputBox = (<a-input disabled={ props.disabled } v-model={ row[column.property] } placeholder="" />)
            switch (row.scoringType) {
            case '0': // 单选
                $element = $seletBox
                break
            case '1':
                $element = $seletMultipleBox
                break
            case '2':
                $element = $inputBox
                break
            
            default:
                break
            }
           
            console.log($element)
            return [
                $element
            ]
        },
        // 可编辑显示模板
        renderCell (h, renderOpts, { row, column }) {
            let options =  row.itemDetailList || []
            let currentText = null
            console.log(options)
            if (options && options.length) {
                if (row.scoringType == '0') { // 单选
                    let filterOpt = options.filter(opt=> {
                        return opt.optionType === row[column.property] 
                    })
                    if (filterOpt && filterOpt.length) {
                        currentText = filterOpt[0].optionName
                    }
                } else if (row.scoringType == '1') { // 多选
                    let filterOpt = options.filter(opt=> {
                        return row[column.property] && row[column.property].includes(opt.optionType)
                    })
                    if (filterOpt && filterOpt.length) {
                        currentText = filterOpt.map(rs => rs.optionName).join(',')
                    }
                }
            } else {
                let text = column.property + '_dictText'
                currentText = row[text] ||  row[column.property]
            }
            return [
                <span>{ currentText }</span>
            ]
        }
    }
}

VXETable.renderer.mixin(renderer)
