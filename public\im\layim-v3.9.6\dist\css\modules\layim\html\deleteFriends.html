<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/im/layim-v3.9.6/dist/css/layui.css" />
    <title></title>
    <style>
      .select-member-box{
        padding: 20px;
      }
      .select-member-box .wrap {
        display: none;
      }
      .select-member-box .wrap .top {
        margin: 8px 0;
      }
      .select-member-box .wrap .top > div{
        display: inline-block;
      }
      .select-member-box .wrap .top > div.title{
        color: #666;
      }
      .select-member-box .wrap .top > div.number{
        float: right;
        color: #666;
      }
      .select-member-box .wrap .bot{
        margin-top: 20px;
      }
      .select-member-box .box-loading{
        text-align: center;
      }
      .select-member-box .box-loading >i {
        font-size: 50px;
      }

      input.group-name-text:placeholder-shown {
        border-color: #4E85FF;
      }

      input.group-name-text::placeholder {
        color: rgba(0, 0, 0, 0.25);
      }
      
      input.group-name-text {
        border: 1px solid #d9d9d9;
        padding: 4px 12px;
        border-radius: 4px;
      }

      .layui-btn.member-submit,
      .layui-btn.member-close {
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
      }

      .layui-btn.member-submit {
        background: #1890FF;
      }

      .text-right {
        text-align: right;
      }

      .select-member-box .layui-transfer-active .layui-btn {
        background: #4E85FF;
        border-color: #4E85FF;
      }

      .select-member-box .layui-form-checked i {
        background: #4E85FF;
        border-color: #4E85FF !important;
      }

      .select-member-box .layui-form-checkbox[lay-skin=primary]:hover i {
        border-color: #4E85FF;
      }

      .layui-btn .layui-icon {
        color: #fff;
      }
    </style>
</head>
<body>
    <div class="select-member-box">
      <!-- <div class='box-loading'>
        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
      </div> -->
      <div class='wrap'>
        <div class="top">
          <div class="title" id="ftitle">选人移出</div>
          <div class="number" id="fnumber">移出人数</div>
        </div>
        <div id="select-member"></div>
        
        <div class="bot">
          <div class="item">
            <div class="text-right">
              <button type="submit" class="layui-btn member-submit" id="fsubmit">确认</button>
              <button type="reset" class="layui-btn member-close layui-btn-primary" id="fclose">取消</button>
            </div>
          </div>
        </div>
      </div>
    </div>
</body>
<script src="/im/layim-v3.9.6/dist/layui.js"></script>
<script>
    var token = localStorage.getItem('t_token');
    const mglobalSrmI18n = window.parent.globalSrmI18n ? window.parent.globalSrmI18n : function(){}
    const mglobalGetLangAccount = window.parent.globalGetLangAccount ? window.parent.globalGetLangAccount : function(){}
    document.getElementById("ftitle").innerHTML= mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_iLIG_42b026f0", "选人移出")
    var i18nTxt = mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_IGLW_38b0a995", "移出人数")
    document.getElementById("fnumber").innerHTML= i18nTxt + '<span style="margin-left: 4px;">0</span>'
    document.getElementById("fsubmit").innerHTML= mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_RL_f20f6", "确认")
    document.getElementById("fclose").innerHTML= mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_title_cancle", "取消")
    layui.use(['layim', 'transfer', 'layer', 'util'], function(layim){
      var $ = layui.$
      ,transfer = layui.transfer
      ,layer = layui.layer
      ,util = layui.util;
      var olayui = parent.layui
      var cache = olayui.layim.cache()
      var listObj = cache.srmParams.deleteFriends
      console.log(listObj)
      var selectMemberObj = cache.srmParams.deleteFriends
      var transferId = selectMemberObj.id+'_'+cache.mine.id
      // layim.config({
      //   brief: true
      // })
      //初始化基本数据
      var members = 0
      var selected = 0
      var api = '/user/getMembers?id=' + listObj.id;
      var BASE_URL = localStorage.getItem('IM_BASE_URL') || '//v5sit-micro.51qqt.com/'
      if (location.hostname.includes('localhost')) {
        BASE_URL = 'https://v5sit-micro.51qqt.com/els/im'
      }
      $('.select-member-box .wrap').show()
      //初始右侧数据
      transfer.render({
        elem: '#select-member'
        ,showSearch: true
        ,id: transferId 
        ,parseData: function(res){
          return {
            "value": res.id //数据值
            ,"title": res.username //数据标题
          }
        }
        ,data: listObj.data
        ,title: [mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_yj_b29ee", "好友"), mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_IGN_1d45486", "移出者")]  //自定义标题
        ,width: 242 //定义宽度
        ,height: 338 //定义高度
        ,onchange: function(obj, index){
          selected = obj
          // 获取后边的数据
          members = $("#select-member .layui-transfer div[data-index=1]").find(".layui-transfer-data li").length;
          $('#fnumber span').text(members);
        }
      })
      // 取消弹窗
      $('.select-member-box .bot .member-close').click(function(){
        listObj.index && olayui.layer.close(listObj.index)
      })
      var oname = '聊天室'
      // 创建群聊天室
      $('.select-member-box .bot .member-submit').click(function(){
        var getData = transfer.getData(transferId); // 获取右侧数据
        var postData = getData.map(res => res.value)
        // postData = [...new Set(postData)].join(',')
        var params = {
          chatGroupId: selectMemberObj.id,
          userId: postData
        }
        chatSubmit(params)
      })
      function chatSubmit(params) {
        $.ajax({
          headers: {
            'X-Access-Token': token
          },
          url: BASE_URL + '/userGroup/removeChatGroupUser',
          contentType: "application/json",
          dataType: 'json',
          type: 'POST',
          data: JSON.stringify(params),
          async: false,
          success: function (res) {
            listObj.index && olayui.layer.close(listObj.index)
            parent.layui.layim.getMembersData(true)
            layer.msg(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_alert_tkLR_2f0866fe", "操作成功"));
            // $('body').click()
          },
          error: function () {
            console.log('ajax error');
          }
        });
      }
    });
</script>
</html>