<template>
  <div>
    <div
      v-for="(item, index) in tenderEvaluationTemplateItemVoList"
      class="margin-b-20"
      :key="getNodeName(item) + index + item.orderBy">
      <div class="title_ctrl clearfix">
        <div class="fl">
          <span>
            <a-tag color="#CCCCCC">
              {{ item.orderBy }}
            </a-tag>
          </span>
          <span >
            <a-tag color="cyan">
              {{ item.groupName }}
            </a-tag>
          </span>
          <span >
            <a-tag color="#2F54EB">
              {{ nodeNameMap[item.groupType] }}
            </a-tag>
          </span>
          <span
            v-if="item.score">
            <a-tag color="#F5222D"> {{ item.score }}分 </a-tag>
          </span>
          
          <m-select
            class="currentStep"
            v-if="showCurrentStep"
            v-model="item.currentStep"
            :disabled="!isEdit"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_ViFUUyO_884a8c13`, '请选择评审阶段')"
            dict-code="resultTenderEvaluationPeriod" />
        </div>
        <div
          class="fr"
        >
          <a-button
            v-if="isEdit"
            type="primary"
            size="small"
            class="mar-l-10"
            @click="handleChangeNodeItme(item, index)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_modify`, '修改') }}</a-button>
          <a-button
            v-if="isEdit"
            type="danger"
            class="mar-l-10"
            size="small"
            @click="handleDeleteNodeItme(index)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
          <a-button
            type="text"
            class="mar-l-10"
            size="small"
            @click="handleViewNodeItme(item, index)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_see`, '查看') }}</a-button>
          <span
            v-if="['0', '1', '2'].includes(item.groupType) && isEdit"
            class="mar-l-10"> 
            <a-button
              type="text"
              size="small"
              @click="handleAddRulefromLibrary(item, index)">{{ $srmI18n(`${$getLangAccount()}#i18n_field_TvGiF_d6692949`, '条例库选择') }}</a-button>
            <a-button
              type="primary"
              class="mar-l-10"
              size="small"
              @click="handleAddRule(item, index)">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VaTv_2f92f9b8`, '新增条例') }}</a-button>
          </span>
          
        </div>
      </div>
      <div
        :ref="getNodeName(item) + index"
        :is="getNodeName(item)"
        :isBiddingFileModule="isBiddingFileModule"
        :pageStatus="pageStatus"
        v-on="$listeners"
        :dealScore="item.score"
        :currentItem="item"
        :itemIndex="index"
        :tenderEvaluationTemplatePriceRegulationInfo="item.tenderEvaluationTemplatePriceRegulationInfo"
        :tenderEvaluationTemplateRegulationInfoList="item.tenderEvaluationTemplateRegulationInfoList"></div>
    </div>
    <node-modal
      @confirmChangeNode="confirmChangeNode"
      :tenderEvaluationTemplateItemVoList="tenderEvaluationTemplateItemVoList"
      ref="nodeModal" />
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import Price from './Price'
import ReviewNode from './ReviewNode'
import ScoreNode from './ScoreNode'
import NodeModal from './NodeModal'
import PriceComparison from './PriceComparison'

import { mul } from '@/utils/mathFloat.js'
export default {
    name: 'TemplateNodeBox',
    inject: {
        currentSubPackage: {
            from: 'currentSubPackage',
            default: null
        },
        currentNode: {
            from: 'currentNode',
            default: null
        }
    },
    props: {
        nodeListData: {
            type: Array,
            default: () => {
                return []
            }
        },
        pageStatus: {
            type: String,
            default: 'edit'
        },
        isBiddingFileModule: {
            type: Boolean,
            default: false
        }
    },
    components: {
        fieldSelectModal,
        Price,
        ReviewNode,
        ScoreNode,
        NodeModal,
        PriceComparison
    },
    watch: {
        nodeListData: {
            handler (data) {
                this.tenderEvaluationTemplateItemVoList = JSON.parse(JSON.stringify(data))
            },
            immediate: true,
            deep: true
        }
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        nodeList () {
            let data = this.tenderEvaluationTemplateItemVoList.sort((a, b) => {
                return a.orderBy - b.orderBy //升序
            })
            return data
        },
        showCurrentStep () {
            let { extend: { checkType = '' } = {} } = this.currentNode ? this.currentNode() : {}
            let { processType } = this.currentSubPackage ? this.currentSubPackage() : {}
            return checkType == '1' && processType == '1'
        }
    },
    data () {
        return {
            tenderEvaluationTemplateItemVoList: [],
            nodeNameMap: {
                0: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_UUd_2185bbc`, '评审项'),
                1: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_Uzd_21731f7`, '评分项'),
                2: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_UUUzd_dd4b307a`, '评审评分项'),
                3: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_umUzd_8a3b0bb2`, '价格评分项'),
                4: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umlJd_89ca99e5`, '价格比较项')
            },
            currentNodeItme: {},
            currentNodeIndex: null,
            currentNodeRef: ''
        }
    },
    methods: {
        async getAllData () {
            let params = []
            this.tenderEvaluationTemplateItemVoList.map((item, index) => {
                let refName = this.getNodeName(item) + index
                let itemData = this.$refs[refName][0].getAllData()
                let paramsCode = ['3', '4'].includes(item.groupType) ? 'tenderEvaluationTemplatePriceRegulationInfo' : 'tenderEvaluationTemplateRegulationInfoList'
                let obj = Object.assign({}, item)
                obj[paramsCode] = itemData
                params.push(obj)
            })
            return params
        },
        async getValidatePromise () {
            let refNames = this.tenderEvaluationTemplateItemVoList.map((item, index) => {
                let refName = this.getNodeName(item) + index
                return refName
            })
            let { validStatus } = await this.getAllValidate(refNames).then((res) => res)
            if (validStatus) {
                if (this.showCurrentStep) {
                    let flag = false
                    let row = null
                    this.tenderEvaluationTemplateItemVoList.map((item, i) => {
                        if (!['0', '1'].includes(item.currentStep)) {
                            flag = true
                            row = item
                        }
                    })
                    if (flag) {
                        this.$message.warning(`序号：${row.orderBy}环节名为[${row.groupName}]：请填写评审阶段`)
                        return false
                    }
                }
                return true
            } else {
                return false
            }
        },
        getAllValidate (refNames) {
            const handlePromise = (list = []) =>
                list.map((promise) =>
                    promise.then(
                        (res) => ({
                            status: 'success',
                            res
                        }),
                        (err) => ({
                            status: 'error',
                            err
                        })
                    )
                )

            let promiseValidateArr = refNames.map((ref) => this.$refs[ref][0].getValidatePromise()).filter((promise) => promise)
            return new Promise((resolve, reject) => {
                Promise.all(handlePromise(promiseValidateArr))
                    .then((result) => {
                        let errItem = result.filter((item) => {
                            return item.status == 'error'
                        })
                        if (errItem.length > 0) {
                            let resolveData = { validStatus: false, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiKmWVGv_95d29236`, '验证失败，请处理') }
                            resolve(resolveData)
                        } else {
                            let resolveData = { validStatus: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OiLR_48599c04`, '验证成功') }
                            resolve(resolveData)
                        }
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        },
        getNodeName ({ groupType = 0}) {
            if (!groupType) groupType = 0
            let map = {
                0: 'ReviewNode',
                1: 'ScoreNode',
                2: 'ReviewNode',
                3: 'Price',
                4: 'PriceComparison'
            }
            return map[groupType]
        },
        confirmChangeNode (data) {
            let params = Object.assign({}, data)
            if (this.currentNodeIndex || this.currentNodeIndex == 0) {
                this.$set(this.tenderEvaluationTemplateItemVoList, this.currentNodeIndex, params)
            } else {
                this.tenderEvaluationTemplateItemVoList.push(data)
            }
            this.$nextTick(()=> {
                this.dataHandleSort()
            })
        },
        handleChangeNodeItme (item, index) {
            this.currentNodeIndex = index
            let {veto} = this.tenderEvaluationTemplateItemVoList[index]
            let vetoToString = veto + ''
            this.$refs.nodeModal.open(Object.assign({}, this.tenderEvaluationTemplateItemVoList[index], {veto: vetoToString}), 'edit')
        },
        handleViewNodeItme (item, index) {
            this.currentNodeIndex = index
            let {veto} = this.tenderEvaluationTemplateItemVoList[index]
            let vetoToString = veto + ''
            this.$refs.nodeModal.open(Object.assign({}, this.tenderEvaluationTemplateItemVoList[index], {veto: vetoToString}), 'detail')
        },
        handleAddNode () {
            this.currentNodeIndex = null
            // 否决项为默认否
            this.$refs.nodeModal.open({ veto: '0' })
        },
        handleDeleteNodeItme (index) {
            this.tenderEvaluationTemplateItemVoList.splice(index, 1)
        },
        handleAddRulefromLibrary (item, index) {
            this.currentNodeRef = this.getNodeName(item) + index
            let url = '/tender/tenderEvaluationRegulation/list'
            let columns = [
                { field: 'regulationName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TARL_303d20d9`, '条例名称') },
                { field: 'regulationDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TvMW_30358fcb`, '条例描述') }
            ]
            this.$refs.fieldSelectModal.open(url, {status: 1}, columns, 'multiple')
        },
        fieldSelectOk (data) {
            let p = data.map(({ regulationName, regulationDesc, objective, scoreRange }) => {
                return {
                    regulationName,
                    regulationDesc,
                    scoreRange,
                    objective
                }
            })
            this.$refs[this.currentNodeRef][0].addItem(p)
        },
        handleAddRule (item, index) {
            let ref = this.getNodeName(item) + index
            this.$refs[ref][0].addItem({
                regulationName: '',
                regulationDesc: '',
                scoreRange: '',
                objective: ''
            })
        },
        dealScore (item) {
            let n = item.score
            if (item.weights) {
                let s = mul(item.score, item.weights)
                n = mul(s, 0.01)
            }
            return n
        },
        dataHandleSort () {
            // 重置数据排序
            let params = []
            this.tenderEvaluationTemplateItemVoList.map((item, index) => {
                let refName = this.getNodeName(item) + index
                let itemData = this.$refs[refName][0].getAllData()
                let paramsCode = ['3', '4'].includes(item.groupType) ? 'tenderEvaluationTemplatePriceRegulationInfo' : 'tenderEvaluationTemplateRegulationInfoList'
                let obj = Object.assign({}, item)
                obj[paramsCode] = itemData
                params.push(obj)
            })
            this.tenderEvaluationTemplateItemVoList = params.sort((a, b) => {
                return a.orderBy - b.orderBy //升序
            })
        }
    },
    created () {
    }
}
</script>
<style lang="less" scoped>
.fl {
    float: left;
}
.fr {
    float: right;
}
.clearfix {
    clear: both;
}
.title_ctrl {
    padding: 8px;
    background: #f2f3f5;
}
.mar-l-10 {
    margin-left: 10px;
}
.margin-b-20 {
    margin-bottom: 20px;
    &:last-child {
        margin-bottom: 0px;
    }
}
.currentStep {
    :deep(.ant-select-selection--single){
        height: 24px;
    }
    :deep(.ant-select-selection__rendered){
        line-height: 22px;
    }
    display: inline-block;
    width: 100px;
    .mar-l-10;
}
</style>
